class Serve::User::EntriesPolicy < ApplicationPolicy
  def create?
    return false unless record.activity&.use_permitable_by_user?(user)
    # return true unless record.is_a?(Serve::OperEntry)
    # return true if record.order_at.nil?
    # activity = record.activity
    # return false unless activity.effective?(record.order_at)
    # start_at = activity.effective_at.beginning_of_day
    # end_at = activity.invalid_at.end_of_day
    # activity.entries.where(order_at: start_at..end_at).count < 1
    true
  end
end
