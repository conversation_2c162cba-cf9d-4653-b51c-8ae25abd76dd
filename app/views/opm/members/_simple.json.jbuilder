json.partial! "opm/members/single", member: member
json.extract!(
  member,
  *member.class.try(:extra_view_attributes, "simple")
)

json.user member.user, partial: "users/single", as: :user

if @member_duties_departments.present?
  duties_departments = @member_duties_departments[member.id] || { duties: [], departments: [] }
  json.duties duties_departments[:duties]
  json.duty member.pos_job
  json.departments duties_departments[:departments]
  json.duty_ranks duties_departments[:duty_ranks]
  json.priorities duties_departments[:priorities]
end

if @org_id.present?
  json.departments_duties member.departments_duties(@org_id)
elsif defined?(org_id) && org_id.present?
  json.departments_duties member.departments_duties(org_id)
elsif @departments_duties.present?
  json.departments_duties @departments_duties
end

json.ta_statistics member.ta_statistic(@resource_stat_condition) if @resource_stat_condition.present?
