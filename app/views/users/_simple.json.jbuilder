json.partial! "users/single", user: user

json.duty_names user.duty_names
json.department_names user.department_names.reverse
json.org_names user.org_names
json.member_identity_ids user.member_identity_ids
json.member_identity_names user.member_identity_names
json.member_pos_job user.member_pos_job
json.members user.members, partial: "members/lite", as: :member
json.ref_user user.ref_user, partial: "users/single", as: :user
