json.extract!(
  duty_change,
  *duty_change.class.try(:extra_view_attributes, "single"),
  :id,
  :created_at,
  :updated_at,
  :app_id,
  :user_id,
  :department_id,
  :original_department_id,
  :duty,
  :duty_level,
  :original_duty,
  :original_duty_level,
  :change_date,
  :reason,
  :create_instance_state,
  :create_instance_timestamp
)
json.department_path_names duty_change&.department&.path_names&.reverse
json.original_department_path_names duty_change&.original_department&.path_names&.reverse
