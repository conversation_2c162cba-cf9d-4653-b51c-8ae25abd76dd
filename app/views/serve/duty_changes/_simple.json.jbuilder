json.partial! "serve/duty_changes/single", duty_change: duty_change
json.extract!(
  duty_change,
  *duty_change.class.try(:extra_view_attributes, "simple")
)

json.app duty_change.app, partial: "apps/single", as: :app
json.user duty_change.user, partial: "users/single", as: :user
json.department duty_change.department, partial: "departments/single", as: :department
json.original_department duty_change.original_department, partial: "departments/single", as: :original_department

json.ta_statistics duty_change.ta_statistic(@resource_stat_condition) if @resource_stat_condition.present?
