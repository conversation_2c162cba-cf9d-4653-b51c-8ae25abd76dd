json.partial! 'serve/permit_actions/single', permit_action: permit_action
json.extract!(
  permit_action,
  *permit_action.class.try(:extra_view_attributes, 'simple'),
)

json.app permit_action.app, partial: 'apps/single', as: :app
# json.real_user permit_action.real_user, partial: 'users/single', as: :real_user
json.target permit_action.target&.as_jbuilder_json(partial: 'single')
json.user permit_action.user&.as_jbuilder_json(partial: 'single')

json.ta_statistics permit_action.ta_statistic(@resource_stat_condition) if @resource_stat_condition.present?
