json.partial! 'serve/tags/single', tag: tag
json.extract!(
  tag,
  *tag.class.try(:extra_view_attributes, 'simple'),
  :activities_count
)

json.app tag.app, partial: 'apps/single', as: :app
json.submodule tag.submodule, partial: 'serve/submodules/single', as: :submodule

json.ta_statistics tag.ta_statistic(@resource_stat_condition) if @resource_stat_condition.present?
json.group_ids tag.group_ids
json.group_names tag.groups.pluck(:name)
