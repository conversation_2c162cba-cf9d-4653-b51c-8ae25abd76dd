json.partial! 'serve/banners/single', banner: banner
json.extract!(
  banner,
  *banner.class.try(:extra_view_attributes, 'simple'),
)

json.app banner.app, partial: 'apps/single', as: :app
json.source banner.source&.as_jbuilder_json(partial: 'single')
json.can_redirect banner.can_redirect?(@current_user)

json.ta_statistics banner.ta_statistic(@resource_stat_condition) if @resource_stat_condition.present?
