json.partial! 'serve/catalogs/simple', catalog: catalog
json.extract!(
  catalog,
  *catalog.class.try(:extra_view_attributes, 'detail'),
)

json.extract!(
  catalog,
  :model_detail,
)

json.view_serve_permit_actions catalog.view_serve_permit_actions&.map(&:as_json)
json.use_serve_permit_actions catalog.use_serve_permit_actions&.map(&:as_json)
json.manage_serve_permit_actions catalog.manage_serve_permit_actions&.map(&:as_json)