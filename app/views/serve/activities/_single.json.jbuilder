json.extract!(
  activity,
  *activity.class.try(:extra_view_attributes, 'single'),
  :id,
  :created_at,
  :updated_at,
  :app_id,
  :submodule_id,
  :source_type,
  :source_id,
  :name,
  :model_flag,
  :model_payload,
  :model_payload_summary,
  :type,
  :effective_at,
  :invalid_at,
  :views,
  :view_enable,
  :uses,
  :use_enable,
  :state,
  :cover_image,
  :position,
  :content,
  :address,
  :layout,
  :views_count,
  :attachments,
  :icon,
  :creator_id,
  :create_instance_state,
  :manages,
  :manage_enable,
  :is_hotted,
  :target_type,
  :target_id,
  :published_at,
)
