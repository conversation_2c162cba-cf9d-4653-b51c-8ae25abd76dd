json.partial! 'serve/activities/single', activity: activity
json.extract!(
  activity,
  *activity.class.try(:extra_view_attributes, 'simple'),
)

json.app activity.app, partial: 'apps/single', as: :app
json.submodule activity.submodule, partial: 'serve/submodules/single', as: :submodule
json.creator activity.creator, partial: 'users/simple', as: :user
json.create_instance_id activity.create_instance&.id
json.catalog_ids activity.catalog_ids
json.ta_statistics activity.ta_statistic(@resource_stat_condition) if @resource_stat_condition.present?
