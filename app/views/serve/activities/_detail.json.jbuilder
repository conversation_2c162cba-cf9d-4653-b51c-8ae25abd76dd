json.partial! 'serve/activities/simple', activity: activity
json.extract!(
  activity,
  *activity.class.try(:extra_view_attributes, 'detail'),
)

json.source_info activity.source_info(partial: 'detail', :@current_user => @current_user)
json.view_serve_permit_actions activity.view_serve_permit_actions&.map(&:as_json)
json.use_serve_permit_actions activity.use_serve_permit_actions&.map(&:as_json)
json.manage_serve_permit_actions activity.manage_serve_permit_actions&.map(&:as_json)
json.entry activity.entry(user: @current_user)&.as_jbuilder_json(partial: 'detail', :@current_user => @current_user)
json.can_use activity.use_permitable_by_user?(@current_user)
json.create_instance_id activity.create_instance&.id

json.extract!(
  activity,
  :model_detail,
)
