json.partial! 'serve/entries/single', entry: entry
json.extract!(
  entry,
  *entry.class.try(:extra_view_attributes, 'simple'),
)

json.app entry.app, partial: 'apps/single', as: :app
json.submodule entry.submodule, partial: 'serve/submodules/single', as: :submodule
json.activity entry.activity, partial: 'serve/activities/single', as: :activity
json.user entry.user, partial: 'users/single', as: :user

json.ta_statistics entry.ta_statistic(@resource_stat_condition) if @resource_stat_condition.present?

json.source_info entry.source_info(partial: 'simple', :@current_user => @current_user)
