json.partial! "serve/bid_projects/single", bid_project: bid_project
json.extract!(
  bid_project,
  *bid_project.class.try(:extra_view_attributes, "simple"),
  :org_name
)

# json.region_area bid_project.region_area, partial: 'region/areas/single', as: :region_area
# json.app bid_project.app, partial: 'apps/single', as: :app
# json.org bid_project.org, partial: 'orgs/single', as: :org
# json.contactor bid_project.contactor, partial: 'users/single', as: :contactor
# json.manager bid_project.manager, partial: 'users/single', as: :manager

json.ta_statistics bid_project.ta_statistic(@resource_stat_condition) if @resource_stat_condition.present?
