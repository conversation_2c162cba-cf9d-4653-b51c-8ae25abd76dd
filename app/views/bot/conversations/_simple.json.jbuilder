json.partial! 'bot/conversations/single', conversation: conversation
json.extract!(
  conversation,
  *conversation.class.try(:extra_view_attributes, 'simple'),
)

json.app conversation.app, partial: 'apps/single', as: :app
json.agent conversation.agent, partial: 'bot/agents/single', as: :agent
json.user conversation.user, partial: 'users/single', as: :user

json.ta_statistics conversation.ta_statistic(@resource_stat_condition) if @resource_stat_condition.present?
