json.partial! 'bot/review_documents/single', review_document: review_document
json.extract!(
  review_document,
  *review_document.class.try(:extra_view_attributes, 'simple'),
)

json.app review_document.app, partial: 'apps/single', as: :app
json.user review_document.user, partial: 'users/single', as: :user
json.reviewer review_document.reviewer, partial: 'bot/reviewers/single', as: :reviewer

json.ta_statistics review_document.ta_statistic(@resource_stat_condition) if @resource_stat_condition.present?
