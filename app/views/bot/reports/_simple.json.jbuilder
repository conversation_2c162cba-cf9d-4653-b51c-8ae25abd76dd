json.partial! 'bot/reports/single', report: report
json.extract!(
  report,
  *report.class.try(:extra_view_attributes, 'simple'),
)

json.app report.app, partial: 'apps/single', as: :app
json.user report.user, partial: 'users/single', as: :user
json.report_template report.report_template, partial: 'bot/report_templates/single', as: :report_template

json.ta_statistics report.ta_statistic(@resource_stat_condition) if @resource_stat_condition.present?
