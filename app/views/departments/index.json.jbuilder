json.cache! ['departments_index', @departments.map(&:id).sort, @departments.current_page, @departments.per_page] do
  json.records @departments do |department|
    json.partial! 'departments/single', department: department
  end

  json.total_count @departments.total_entries
  json.total_pages @departments.total_pages
  json.current_page @departments.current_page
  json.per_page @departments.per_page
end
