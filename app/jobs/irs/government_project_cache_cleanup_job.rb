# frozen_string_literal: true

module Irs
  # 政府项目缓存清理任务
  # 定期清理过期的缓存数据，保持数据库整洁
  class GovernmentProjectCacheCleanupJob < ApplicationJob
    queue_as :default

    # 默认保留天数
    DEFAULT_RETENTION_DAYS = 30

    # 执行缓存清理
    # @param retention_days [Integer] 保留天数，默认30天
    def perform(retention_days = DEFAULT_RETENTION_DAYS)
      Rails.logger.info "[政府项目缓存清理] 开始清理过期缓存，保留 #{retention_days} 天"

      begin
        # 清理过期缓存
        deleted_count = Irs::GovernmentProject.cleanup_expired_cache(retention_days)
        
        # 记录清理结果
        Rails.logger.info "[政府项目缓存清理] 清理完成，删除了 #{deleted_count} 条记录"
        
        # 获取清理后的统计信息
        stats = Irs::GovernmentProject.cache_statistics
        Rails.logger.info "[政府项目缓存清理] 清理后统计: #{stats}"
        
        # 返回清理结果
        {
          success: true,
          deleted_count: deleted_count,
          retention_days: retention_days,
          remaining_records: stats[:total_records],
          message: "缓存清理成功"
        }
      rescue => e
        Rails.logger.error "[政府项目缓存清理] 清理失败: #{e.message}"
        Rails.logger.error e.backtrace.join("\n")
        
        # 返回错误结果
        {
          success: false,
          error: e.message,
          message: "缓存清理失败"
        }
      end
    end

    # 立即执行清理任务
    # @param retention_days [Integer] 保留天数
    # @return [Hash] 执行结果
    def self.cleanup_now(retention_days = DEFAULT_RETENTION_DAYS)
      perform_now(retention_days)
    end

    # 异步执行清理任务
    # @param retention_days [Integer] 保留天数
    # @return [Job] 任务实例
    def self.cleanup_async(retention_days = DEFAULT_RETENTION_DAYS)
      perform_later(retention_days)
    end
  end
end
