# frozen_string_literal: true

# 微信文章抓取异步任务
class WechatCrawlJob < ApplicationJob
  queue_as :default

  # 执行微信文章抓取
  # @param url [String] 微信公众号文章URL
  # @param options [Hash] 抓取选项
  # @option options [Integer] :submodule_id 子模块ID
  # @option options [String] :state 文章状态
  # @option options [Integer] :origin_id 来源ID
  # @option options [Array<Integer>] :tag_ids 标签ID数组
  # @option options [Integer] :user_id 发起抓取的用户ID
  # @option options [String] :task_id 任务跟踪ID
  def perform(url, options = {})
    Rails.logger.info "[WechatCrawlJob] 开始异步抓取微信文章: #{url}"
    Rails.logger.info "[WechatCrawlJob] 抓取选项: #{options.inspect}"

    # 添加整体超时控制
    Timeout::timeout(240) do # 4分钟超时，留1分钟缓冲
      # 更新任务状态为进行中
      WechatCrawlTaskService.mark_processing(options[:task_id], '正在抓取微信文章...') if options[:task_id]

      begin
        # 验证URL格式
        unless url.match?(/mp\.weixin\.qq\.com/)
          raise ArgumentError, "无效的微信公众号URL"
        end

        # 调用原有的抓取逻辑（已有内部超时控制）
        activity = Serve::Activity.crawl_from_wechat_url(
          url,
          submodule_id: options[:submodule_id] || 1,
          state: options[:state] || "pending",
          origin_id: options[:origin_id],
          tag_ids: options[:tag_ids]
        )

        if activity
          Rails.logger.info "[WechatCrawlJob] 微信文章抓取成功: #{activity.name}"

          # 更新任务状态为成功
          WechatCrawlTaskService.mark_completed(
            options[:task_id],
            '微信文章抓取成功',
            {
              activity_id: activity.id,
              activity_name: activity.name,
              activity_state: activity.state,
              effective_at: activity.effective_at,
              author: activity.model_payload&.dig("author"),
              link: activity.model_payload&.dig("link")
            }
          ) if options[:task_id]

          # 发送通知给用户（如果需要）
          send_notification_to_user(options[:user_id], activity) if options[:user_id]

          activity
        else
          raise StandardError, "文章抓取失败，请检查URL或稍后重试"
        end

      rescue StandardError => e
        Rails.logger.error "[WechatCrawlJob] 微信文章抓取失败: #{e.message}"
        Rails.logger.error "[WechatCrawlJob] 错误堆栈: #{e.backtrace.first(5).join("\n")}"

        # 更新任务状态为失败
        WechatCrawlTaskService.mark_failed(
          options[:task_id],
          "抓取失败: #{e.message}",
          e.message
        ) if options[:task_id]

        # 重新抛出异常，让Sidekiq处理重试逻辑
        raise e
      end
    end

  rescue Timeout::Error
    error_msg = "抓取超时(4分钟)，请稍后重试"
    Rails.logger.error "[WechatCrawlJob] #{error_msg}: #{url}"

    # 更新任务状态为超时失败
    WechatCrawlTaskService.mark_failed(
      options[:task_id],
      error_msg,
      "抓取超时"
    ) if options[:task_id]

    raise StandardError, error_msg
  end

  private

  # 发送通知给用户
  # @param user_id [Integer] 用户ID
  # @param activity [Serve::Activity] 抓取成功的文章
  def send_notification_to_user(user_id, activity)
    return unless user_id && activity

    begin
      # 这里可以根据项目需求发送钉钉消息或其他通知
      # 暂时只记录日志
      Rails.logger.info "[WechatCrawlJob] 向用户 #{user_id} 发送抓取成功通知: #{activity.name}"

      # 如果项目中有钉钉通知功能，可以在这里调用
      # DingtalkSendMessage.perform_async(user_id, notification_data)

    rescue StandardError => e
      Rails.logger.error "[WechatCrawlJob] 发送用户通知失败: #{e.message}"
    end
  end
end
