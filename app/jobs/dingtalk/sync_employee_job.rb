# Asynchronously synchronizes employee data from DingTalk government platform
# @param employee_code [String] The employee code to sync
# @param tenant_id [String] The tenant ID for the organization
class Dingtalk::SyncEmployeeJob < ApplicationJob
  queue_as :default

  def perform(employee_code, tenant_id)
    Rails.logger.info "开始同步员工: #{employee_code}, 租户: #{tenant_id}"

    # 根据租户ID找到对应的client
    client = Dingtalk::GovClient.find_by(corp_id: tenant_id)
    return unless client

    # 根据client找到对应的组织
    org = find_org_by_client(client)
    return unless org

    # 调用现有的同步服务
    member_service = Dingtalk::MemberService.new(client)

    # 根据员工代码获取员工信息并同步
    sync_single_employee(member_service, employee_code, org)

  rescue StandardError => e
    Rails.logger.error "同步员工失败: #{employee_code}, 错误: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
  end

  private

  def find_org_by_client(client)
    # 根据client找到对应的组织
    # 这里需要根据实际的业务逻辑来实现
    client.app&.orgs&.first
  end

  def sync_single_employee(member_service, employee_code, org)
    Rails.logger.info "开始同步单个员工: #{employee_code} 在组织: #{org.name}(#{org.code})"

    # 获取员工详细信息 - 使用公共方法或重新实现
    target_member = fetch_employee_data(member_service, employee_code, org.code)

    if target_member
      Rails.logger.info "找到员工信息: #{target_member['employeeName']} (#{employee_code})"
      sync_employee_with_department(member_service, target_member, employee_code, org)
    else
      Rails.logger.warn "未找到员工: #{employee_code} 在组织 #{org.code} 中"
    end
  end

  # 获取员工数据
  def fetch_employee_data(member_service, employee_code, org_code)
    # 尝试从第一页获取员工信息
    begin
      response = member_service.send(:fetch_members_page, org_code, 1)

      if response&.data&.dig("content", "success")
        members_data = response.data.dig("content", "data") || []
        target_member = members_data.find { |m| m["employeeCode"] == employee_code }

        unless target_member
          # 如果在第一页没找到，尝试获取所有页面
          Rails.logger.info "在第一页未找到员工，尝试获取所有页面..."
          all_members = member_service.send(:fetch_all_members, org_code)
          target_member = all_members.find { |m| m["employeeCode"] == employee_code }
        end

        return target_member
      else
        Rails.logger.error "获取员工信息失败: #{response&.data}"
        return nil
      end
    rescue StandardError => e
      Rails.logger.error "获取员工数据异常: #{e.message}"
      return nil
    end
  end

  # 同步员工和部门信息
  def sync_employee_with_department(member_service, target_member, employee_code, org)
    # 预处理员工职位信息
    member_service.send(:process_member_position, target_member)

    # 获取账户信息
    account_info = member_service.send(:fetch_account_info, [target_member])

    # 找到员工所属部门
    dept_code = target_member.dig("govEmployeePosition", "organizationCode")
    department = org.departments.find_by(code: dept_code)

    if department
      sync_employee_data(member_service, target_member, account_info, org, department, employee_code)
    else
      handle_missing_department(member_service, target_member, account_info, org, dept_code, employee_code)
    end
  end

  # 同步员工数据
  def sync_employee_data(member_service, target_member, account_info, org, department, employee_code)
    Rails.logger.info "找到员工所属部门: #{department.name} (#{department.code})"

    # 同步员工数据
    account_data = member_service.send(:prepare_account_data, target_member, account_info)

    # 使用事务确保数据一致性
    ActiveRecord::Base.transaction do
      member_service.send(:sync_member, target_member, account_data, org: org, department: department)
    end

    Rails.logger.info "成功同步员工: #{employee_code}"
  end

  # 处理缺失部门的情况
  def handle_missing_department(member_service, target_member, account_info, org, dept_code, employee_code)
    Rails.logger.warn "未找到员工 #{employee_code} 对应的部门: #{dept_code}"

    # 尝试同步部门信息
    Rails.logger.info "尝试同步部门信息..."

    # 获取client - 避免使用instance_variable_get
    client = member_service.instance_variable_get(:@client)
    org_service = Dingtalk::OrgService.new(client)
    org_service.sync_departments_of_org(org)

    # 重新查找部门
    department = org.departments.find_by(code: dept_code)
    if department
      sync_employee_data(member_service, target_member, account_info, org, department, employee_code)
    else
      Rails.logger.error "同步部门后仍未找到部门: #{dept_code}"
    end
  end
end
