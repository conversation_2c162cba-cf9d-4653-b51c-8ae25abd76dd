class Res::Member::OrgUserExcel

  include TalltyImportExport::Importable

  Import.class_eval do

    def import_headers(app: nil, **args)
      [
        { key: "account", name: "账号" },
        { key: "name", name: "接收人名称" },
      ]
    end

    def import_record(line_info, associations)
      # 优先使用账号查找
      user = if line_info["account"].present?
        User.find_by(account: line_info["account"])
      else
        User.find_by(name: line_info["name"])
      end

      return unless user

      # 返回用户ID
      user.id
    end

  end

end
