module Serve::Excel::EntryExportable
  extend ActiveSupport::Concern

  included do
    def export_headers(app: App.first, **args)
      activity_id = args.dig(:q, 'activity_id_eq').presence || args[:activity_id].presence
      activity = Serve::Activity.find_by(id: activity_id)

      headers = [
        { key: 'user.name', name: '姓名' },
        { key: 'user.account', name: '账号' },
      ]

      if activity&.is_a?(Serve::OperActivity)
        headers.push({ key: 'order_at', name: '预约时间', attr_type: :datetime })
        headers.push({ key: 'state_zh', name: '状态' })
      end

      if activity&.respond_to?(:export_headers)
        headers.concat(activity.export_headers)
      end

      if activity && activity.respond_to?(:form)
        storage_attribute = activity.respond_to?(:storage_attribute) ? activity.storage_attribute : 'payload'
        activity.form['column_attributes']
          .select { |_attr| _attr['export'] && _attr['export']['on'] }
          .each do |_attr|
            headers << {
              key: _attr['dataIndex'],
              name: _attr['title'],
              chain: _attr['key'] ? [:source, _attr['key']] : [:source, storage_attribute, _attr['dataIndex']]
            }
          end
      end

      headers
    end
  end
end