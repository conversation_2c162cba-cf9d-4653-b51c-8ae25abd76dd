class Com::User::RedisResourcesController < SimpleController::BaseController

  before_action :validate_redis_key, except: [:index]
  auth_action :user

  def index
    page = params[:page] || 1
    per_page = params[:per_page] || 15

    @redis_resources = params[:redis_key].present? ?
      RedisResources.new(redis_key: params[:redis_key]) :
      RedisResources.new(resources: params[:resources_class].constantize.none)
    @pagination_resources = @redis_resources.resources.paginate(page: page, per_page: per_page)

    render json: {
      redis_key: @redis_resources.redis_key,
      total_count: @pagination_resources.total_entries,
      total_pages: @pagination_resources.total_pages,
      current_page: page.to_i,
      per_page: per_page.to_i,
      records: @pagination_resources.map { |resource| resource.as_jbuilder_json(partial: :simple) }
    }
  end

  def show
    @resource = @redis_resources.find(params[:id])
    render json: @resource.as_jbuilder_json(partial: :detail)
  end

  def create
    @redis_resources.add_ids(*params[:redis_resources][:ids])
    render json: { message: "ok" }, status: 201
  end

  def batch_destroy
    @redis_resources.remove_ids(*params[:redis_resources][:ids])
    head 204
  end

  def destroy
    @redis_resources.remove_ids(params[:id])
    head 204
  end

  def validate_redis_key
    raise Error::BaseError.new(message: "missing redis_key", status: 422) if params[:redis_key].blank?

    @redis_resources = RedisResources.new(redis_key: params[:redis_key])
  end

end
