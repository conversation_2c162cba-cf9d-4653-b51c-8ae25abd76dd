class Serve::Admin::BannersController < SimpleController::BaseController
  defaults(
    resource_class: Serve::Banner,
    collection_name: 'banners',
    instance_name: 'banner',
    view_path: 'serve/banners',
  )

  auth_action :user
  permit_action :serve_admin

  protected

  def begin_of_association_chain
    current_app
  end

  def method_for_association_chain
    :serve_banners
  end

  private

  def banner_params
    params.require(:banner).permit(
      *resource_class.try(:extra_permitted_attributes),
      :source_type,
      :source_id,
      :submodule_id,
      :model_flag,
      :effective_at,
      :invalid_at,
      :published_at,
      :is_published,
      :name,
      :position,
      model_payload: {},
      model_payload_summary: {},
      model_detail: {},
      cover_image: {},
      conf: {}
    )
  end
end
