class Serve::Admin::SubmodulesController < SimpleController::BaseController
  defaults(
    resource_class: Serve::Submodule,
    collection_name: 'submodules',
    instance_name: 'submodule',
    view_path: 'serve/submodules',
  )

  auth_action :user
  permit_action :serve_admin

  protected

  def begin_of_association_chain
    current_app
  end

  def method_for_association_chain
    :serve_submodules
  end

  private

  def submodule_params
    params.require(:submodule).permit(
      *resource_class.try(:extra_permitted_attributes),
      :model_flag,
      :manage_enable,
      :name,
      :position,
      :key,
      :state,
      model_payload: {},
      model_payload_summary: {},
      model_detail: {},
      manages: {},
      cover_image: {},
      layout: {},
      manage_serve_manage_actions_attributes: [
        :id,
        :_destroy,
        :target_type,
        :target_id,
      ]
    )
  end
end
