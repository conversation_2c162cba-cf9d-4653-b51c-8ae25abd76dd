class Serve::User::SubmodulesController < SimpleController::BaseController
  defaults(
    resource_class: Serve::Submodule,
    collection_name: 'submodules',
    instance_name: 'submodule',
    view_path: 'serve/submodules',
  )

  auth_action :user, skip_error: true

  protected

  def begin_of_association_chain
    current_app
  end

  def method_for_association_chain
    :serve_submodules
  end
end
