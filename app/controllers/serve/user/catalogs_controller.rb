class Serve::User::CatalogsController < SimpleController::BaseController
  defaults(
    resource_class: Serve::Catalog,
    collection_name: 'catalogs',
    instance_name: 'catalog',
    view_path: 'serve/catalogs',
  )

  auth_action :user, skip_error: true
  belongs_to :submodule, collection_name: :serve_submodules

  protected

  def begin_of_association_chain
    current_app
  end

  def after_association_chain association
    association.view_by_user(current_user)
  end
end
