class Serve::User::ActivitiesController < SimpleController::BaseController
  defaults(
    resource_class: Serve::Activity,
    collection_name: 'activities',
    instance_name: 'activity',
    view_path: 'serve/activities',
  )

  auth_action :user, skip_error: true
  belongs_to :submodule, collection_name: :serve_submodules

  def show
    show! do
      @activity.increment!(:views_count)
    end
  end

  protected

  def begin_of_association_chain
    current_app
  end

  def after_association_chain association
    association = association.published.visible
                    .view_by_user(current_user)
    unless params.dig('q', 's') || params[:sub_q]&.dig('sub_q', 's')
      association = association.order('serve_activities.is_hotted desc, serve_activities.position asc')
    end
    association
  end
end
