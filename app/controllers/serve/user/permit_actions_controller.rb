class Serve::User::PermitActionsController < SimpleController::BaseController
  defaults(
    resource_class: Serve::PermitAction,
    collection_name: 'permit_actions',
    instance_name: 'permit_action',
    view_path: 'serve/permit_actions',
  )

  auth_action :user

  protected

  def begin_of_association_chain
    current_user
  end

  def method_for_association_chain
    :serve_permit_actions
  end
end
