class Serve::User::EntriesController < SimpleController::BaseController
  defaults(
    resource_class: Serve::Entry,
    collection_name: 'entries',
    instance_name: 'entry',
    view_path: 'serve/entries',
  )

  auth_action :user
  belongs_to :submodule, collection_name: :serve_submodules

  protected

  def begin_of_association_chain
    current_app
  end

  def after_association_chain association
    association.where(user: current_user)
  end

  private

  def update_entry_params
    params.require(:entry).permit(
      *resource_class.try(:extra_permitted_attributes),
      :activity_id,
      # :source_type,
      # :source_id,
      :name,
      :type,
      :order_at,
      :model_flag,
      model_payload: {},
      model_payload_summary: {},
      model_detail: {},
      source_info: {},
    )
  end

  def create_entry_params
    update_entry_params[:source_info].blank? ?
      update_entry_params.merge(user: current_user, source_info: {}) :
      update_entry_params.merge(user: current_user)
  end
end
