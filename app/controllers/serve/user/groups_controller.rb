class Serve::User::GroupsController < SimpleController::BaseController
  defaults(
    resource_class: Serve::Group,
    collection_name: 'groups',
    instance_name: 'group',
    view_path: 'serve/groups',
  )

  auth_action :user, skip_error: true
  belongs_to :submodule, collection_name: :serve_submodules

  protected

  def begin_of_association_chain
    current_app
  end

  def after_association_chain association
    association.order(position: :asc)
  end
end
