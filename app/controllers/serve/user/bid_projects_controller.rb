class Serve::User::BidProjectsController < SimpleController::BaseController

  defaults(
    resource_class: Serve::BidProject,
    collection_name: "bid_projects",
    instance_name: "bid_project",
    view_path: "serve/bid_projects"
  )

  auth_action :user, skip_error: true

  protected

  def begin_of_association_chain
    current_app
  end

  def method_for_association_chain
    :serve_bid_projects
  end

end
