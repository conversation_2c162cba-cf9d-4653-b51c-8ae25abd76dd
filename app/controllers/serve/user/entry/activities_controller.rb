class Serve::User::Entry::ActivitiesController < SimpleController::BaseController
  defaults(
    resource_class: Serve::Activity,
    collection_name: 'activities',
    instance_name: 'activity',
    view_path: 'serve/activities',
  )

  auth_action :user

  protected

  def begin_of_association_chain
    current_user
  end

  def method_for_association_chain
    :serve_activities
  end

  def after_association_chain association
    association.order(position: :asc)
  end
end
