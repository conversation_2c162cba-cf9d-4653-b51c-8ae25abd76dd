class Serve::Manage::CatalogsController < SimpleController::BaseController
  defaults(
    resource_class: Serve::Catalog,
    collection_name: 'catalogs',
    instance_name: 'catalog',
    view_path: 'serve/catalogs',
  )

  auth_action :user
  permit_action :serve_admin, :serve_manage
  belongs_to :submodule, collection_name: :manage_serve_submodules

  protected

  def begin_of_association_chain
    current_user
  end

  def after_association_chain association
    current_user.has_role?(:serve_admin) ?
      association.manage_by_user(current_user) :
      association
  end

  private

  def catalog_params
    params.require(:catalog).permit(
      *resource_class.try(:extra_permitted_attributes),
      :model_flag,
      :view_enable,
      :use_enable,
      :manage_enable,
      :name,
      :position,
      :state,
      :target_type,
      :target_id,
      icon: {},
      model_payload: {},
      model_payload_summary: {},
      model_detail: {},
      views: {},
      uses: {},
      cover_image: {},
      layout: {},
      group_ids: [],
      view_serve_permit_actions_attributes: [
        :id,
        :_destroy,
        :target_type,
        :target_id,
      ],
      use_serve_permit_actions_attributes: [
        :id,
        :_destroy,
        :target_type,
        :target_id,
      ],
      manage_serve_permit_actions_attributes: [
        :id,
        :_destroy,
        :target_type,
        :target_id,
      ],
    )
  end
end
