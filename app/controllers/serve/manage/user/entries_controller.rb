class Serve::Manage::User::EntriesController < SimpleController::BaseController
  defaults(
    resource_class: Serve::Entry,
    collection_name: 'entries',
    instance_name: 'entry',
    view_path: 'serve/entries',
  )

  auth_action :user
  permit_action :serve_manage, :serve_admin

  include Reg::Controller::OriginRegisterable

  belongs_to :activity, optional: true, collection_name: :manage_serve_activities

  protected

  def begin_of_association_chain
    current_user
  end

  def method_for_association_chain
    params[:activity_id] ? :entries : :manage_serve_entries
  end

  private

  def update_entry_params
    params.require(:entry).permit(
      *resource_class.try(:extra_permitted_attributes),
      :user_id,
      :name,
      :type,
      :model_flag,
      :state,
      :order_at,
      model_payload: {},
      model_payload_summary: {},
      model_detail: {},
      source_info: {},
    )
  end

  def create_entry_params
    update_entry_params[:source_info].blank? ?
      update_entry_params.merge(source_info: {}) :
      update_entry_params
  end
end
