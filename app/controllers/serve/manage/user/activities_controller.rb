class Serve::Manage::User::ActivitiesController < SimpleController::BaseController
  defaults(
    resource_class: Serve::Activity,
    collection_name: 'activities',
    instance_name: 'activity',
    view_path: 'serve/activities',
  )

  include Reg::Controller::OriginQrcodeable
  include Reg::Controller::OriginScanable
  include Reg::Controller::OriginRegisterable

  auth_action :user
  permit_action :serve_manage, :serve_admin

  protected

  def begin_of_association_chain
    current_user
  end

  def method_for_association_chain
    :manage_serve_activities
  end

  def after_association_chain association
    association.order(position: :desc)
  end
end
