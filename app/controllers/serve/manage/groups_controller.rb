class Serve::Manage::GroupsController < SimpleController::BaseController
  defaults(
    resource_class: Serve::Group,
    collection_name: 'groups',
    instance_name: 'group',
    view_path: 'serve/groups',
  )

  auth_action :user
  permit_action :serve_admin, :serve_manage
  belongs_to :submodule, collection_name: :manage_serve_submodules

  protected

  def after_association_chain association
    association.order(position: :asc)
  end

  private

  def group_params
    params.require(:group).permit(
      *resource_class.try(:extra_permitted_attributes),
      :type,
      :model_flag,
      :name,
      :state,
      :position,
      model_payload: {},
      model_payload_summary: {},
      model_detail: {},
      tag_ids: [],
      catalog_ids: []
    )
  end
end
