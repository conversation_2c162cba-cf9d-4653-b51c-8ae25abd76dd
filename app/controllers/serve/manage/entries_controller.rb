class Serve::Manage::EntriesController < SimpleController::BaseController
  defaults(
    resource_class: Serve::Entry,
    collection_name: 'entries',
    instance_name: 'entry',
    view_path: 'serve/entries',
  )

  include Reg::Controller::OriginRegisterable

  auth_action :user
  permit_action :serve_admin, :serve_manage
  belongs_to :submodule, collection_name: :manage_serve_submodules

  protected

  def begin_of_association_chain
    current_user
  end

  def after_association_chain association
    association
  end

  private

  def update_entry_params
    params.require(:entry).permit(
      *resource_class.try(:extra_permitted_attributes),
      :activity_id,
      :source_type,
      :source_id,
      :user_id,
      :name,
      :type,
      :state,
      :order_at,
      :model_flag,
      model_payload: {},
      model_payload_summary: {},
      model_detail: {},
    )
  end

  def create_entry_params
    update_entry_params[:source_info].blank? ?
      update_entry_params.merge(source_info: {}) :
      update_entry_params
  end
end
