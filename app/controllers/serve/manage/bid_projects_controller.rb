class Serve::Manage::BidProjectsController < SimpleController::BaseController

  defaults(
    resource_class: Serve::BidProject,
    collection_name: "bid_projects",
    instance_name: "bid_project",
    view_path: "serve/bid_projects"
  )

  auth_action :user
  permit_action :serve_admin, :serve_manage

  protected

  def begin_of_association_chain
    current_app
  end

  def method_for_association_chain
    :serve_bid_projects
  end

  private

  def bid_project_params
    params.require(:bid_project).permit(
      *resource_class.try(:extra_permitted_attributes),
      :region_area_id,
      :org_id,
      :contactor_id,
      :manager_id,
      :province,
      :city,
      :district,
      :start_at,
      :end_at,
      :code,
      :name,
      :state,
      :region_code,
      :setup_at,
      :open_at,
      :manager_name,
      :contactor_name,
      :phone,
      :unit,
      :unit_code,
      :send_state,
      :amount,
      content: {},
      meta: {},
      payload: {},
      attachments: {}
    )
  end

end
