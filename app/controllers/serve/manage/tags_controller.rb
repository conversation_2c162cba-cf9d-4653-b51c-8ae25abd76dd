class Serve::Manage::TagsController < SimpleController::BaseController
  defaults(
    resource_class: Serve::Tag,
    collection_name: 'tags',
    instance_name: 'tag',
    view_path: 'serve/tags',
  )

  auth_action :user
  permit_action :serve_admin, :serve_manage

  belongs_to :submodule, collection_name: :manage_serve_submodules

  protected

  def begin_of_association_chain
    current_user
  end

  private

  def tag_params
    params.require(:tag).permit(
      *resource_class.try(:extra_permitted_attributes),
      :name,
      :color,
      :position,
      option: {},
      group_ids: []
    )
  end
end
