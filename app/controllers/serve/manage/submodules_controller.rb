class Serve::Manage::SubmodulesController < SimpleController::BaseController
  defaults(
    resource_class: Serve::Submodule,
    collection_name: 'submodules',
    instance_name: 'submodule',
    view_path: 'serve/submodules',
  )

  auth_action :user
  permit_action :serve_admin, :serve_manage

  protected

  def begin_of_association_chain
    current_user
  end

  def method_for_association_chain
    :manage_serve_submodules
  end

  private

  def submodule_params
    params.require(:submodule).permit(
      *resource_class.try(:extra_permitted_attributes),
      :model_flag,
      :ancestry,
      :depth,
      :children_count,
      :manage_enable,
      :name,
      :position,
      :key,
      model_payload: {},
      model_payload_summary: {},
      model_detail: {},
      manages: {},
      cover_image: {},
      layout: {},
    )
  end
end
