class Serve::Manage::ActivitiesController < SimpleController::BaseController
  defaults(
    resource_class: Serve::Activity,
    collection_name: 'activities',
    instance_name: 'activity',
    view_path: 'serve/activities',
  )

  include Bpm::Controller::ResourceInstance

  auth_action :user
  permit_action :serve_admin, :serve_manage
  belongs_to :submodule, collection_name: :manage_serve_submodules

  def need_to_use_resource_instance
    !current_user.has_role?(:serve_admin)
  end

  def show
    show! do
      @activity.increment!(:views_count)
    end
  end

  protected

  def begin_of_association_chain
    current_user
  end

  def after_association_chain association
    association = association.manage_by_user(current_user) unless current_user.has_role?(:serve_admin)
    association.order('serve_activities.is_hotted desc, serve_activities.position asc')
  end

  private

  def create_activity_params
    update_activity_params.merge(creator: current_user)
  end

  def update_activity_params
    params.require(:activity).permit(
      *resource_class.try(:extra_permitted_attributes),
      :source_type,
      :source_id,
      :name,
      :model_flag,
      :type,
      :effective_at,
      :invalid_at,
      :view_enable,
      :use_enable,
      :manage_enable,
      :state,
      :position,
      :address,
      :is_hotted,
      :target_type,
      :target_id,
      :published_at,
      icon: {},
      model_payload: {},
      model_payload_summary: {},
      model_detail: {},
      views: {},
      uses: {},
      cover_image: {},
      attachments: {},
      content: {},
      layout: {},
      source_info: {},
      catalog_ids: [],
      tag_ids: [],
      view_serve_permit_actions_attributes: [
        :id,
        :_destroy,
        :target_type,
        :target_id,
      ],
      use_serve_permit_actions_attributes: [
        :id,
        :_destroy,
        :target_type,
        :target_id,
      ],
      manage_serve_permit_actions_attributes: [
        :id,
        :_destroy,
        :target_type,
        :target_id,
      ],
    )
  end
end
