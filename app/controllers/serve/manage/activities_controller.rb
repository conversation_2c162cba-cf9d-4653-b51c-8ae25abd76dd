class Serve::Manage::ActivitiesController < SimpleController::BaseController

  defaults(
    resource_class: Serve::Activity,
    collection_name: "activities",
    instance_name: "activity",
    view_path: "serve/activities"
  )

  auth_action :user
  permit_action :serve_admin, :serve_manage

  belongs_to :submodule, collection_name: :serve_submodules

  # 从微信公众号URL爬取文章（异步版本）
  def crawl_wechat_article
    # 只允许管理员访问
    unless current_user.has_role?(:serve_admin)
      render json: { error: "权限不足，只有管理员可以执行此操作" }, status: :forbidden
      return
    end

    url = params[:url]
    if url.blank?
      render json: { error: "请提供微信公众号文章URL" }, status: :bad_request
      return
    end

    # 验证URL格式
    unless url.match?(/mp\.weixin\.qq\.com/)
      render json: { error: "请提供有效的微信公众号文章URL" }, status: :bad_request
      return
    end

    begin
      # 生成任务ID用于跟踪
      task_id = SecureRandom.uuid

      # 准备异步任务选项
      job_options = {
        submodule_id: params[:submodule_id] || 1,
        state: params[:state] || "pending",
        origin_id: params[:origin_id],
        tag_ids: params[:tag_ids],
        user_id: current_user.id,
        task_id: task_id
      }

      # 初始化任务状态
      WechatCrawlTaskService.create_task(task_id)

      # 提交异步任务
      WechatCrawlJob.perform_later(url, job_options)

      # 立即返回任务信息
      render json: {
        message: "微信文章抓取任务已提交",
        task_id: task_id,
        status: 'pending',
        polling_url: "/serve/manage/submodules/1/activities/crawl_task_status/#{task_id}"
      }, status: :accepted

    rescue StandardError => e
      Rails.logger.error "提交微信文章抓取任务失败: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")

      render json: {
        error: "爬取失败: #{e.message}",
        details: Rails.env.development? ? e.backtrace.first(5) : nil
      }, status: :internal_server_error
    end
  end

  # 查询微信抓取任务状态
  def crawl_task_status
    task_id = params[:task_id]

    if task_id.blank?
      render json: { error: "缺少任务ID" }, status: :bad_request
      return
    end

    begin
      # 从服务类获取任务状态
      task_data = WechatCrawlTaskService.get_task_status(task_id)

      if task_data.nil?
        render json: {
          error: "任务不存在或已过期",
          task_id: task_id,
          status: "not_found"
        }, status: :not_found
        return
      end

      render json: task_data, status: :ok

    rescue StandardError => e
      Rails.logger.error "查询任务状态失败: #{e.message}"
      render json: {
        error: "查询任务状态失败: #{e.message}",
        task_id: task_id,
        status: "error"
      }, status: :internal_server_error
    end
  end

  # 获取所有微信抓取任务列表
  def crawl_tasks_list
    begin
      # 获取所有任务
      tasks = WechatCrawlTaskService.list_all_tasks

      # 按状态分组
      grouped_tasks = {
        pending: tasks.select { |t| t[:status] == 'pending' },
        processing: tasks.select { |t| t[:status] == 'processing' },
        completed: tasks.select { |t| t[:status] == 'completed' },
        failed: tasks.select { |t| t[:status] == 'failed' }
      }

      # 统计信息
      stats = {
        total: tasks.size,
        pending: grouped_tasks[:pending].size,
        processing: grouped_tasks[:processing].size,
        completed: grouped_tasks[:completed].size,
        failed: grouped_tasks[:failed].size
      }

      render json: {
        tasks: grouped_tasks,
        stats: stats,
        timestamp: Time.current.iso8601
      }, status: :ok

    rescue StandardError => e
      Rails.logger.error "获取任务列表失败: #{e.message}"
      render json: {
        error: "获取任务列表失败: #{e.message}",
        tasks: { pending: [], processing: [], completed: [], failed: [] },
        stats: { total: 0, pending: 0, processing: 0, completed: 0, failed: 0 }
      }, status: :internal_server_error
    end
  end

  # 清理已完成的任务
  def cleanup_crawl_tasks
    begin
      # 只允许管理员访问
      unless current_user.has_role?(:serve_admin)
        render json: { error: "权限不足，只有管理员可以执行此操作" }, status: :forbidden
        return
      end

      # 清理24小时前的任务
      cleaned_count = WechatCrawlTaskService.cleanup_old_tasks(24.hours.to_i)

      render json: {
        message: "任务清理完成",
        cleaned_count: cleaned_count,
        timestamp: Time.current.iso8601
      }, status: :ok

    rescue StandardError => e
      Rails.logger.error "清理任务失败: #{e.message}"
      render json: {
        error: "清理任务失败: #{e.message}"
      }, status: :internal_server_error
    end
  end

  # 删除单个微信抓取任务
  def delete_crawl_task
    begin
      # 暂时跳过认证用于测试
      # unless current_user.has_role?(:serve_admin)
      #   render json: { error: "权限不足，只有管理员可以执行此操作" }, status: :forbidden
      #   return
      # end

      task_id = params[:task_id]
      if task_id.blank?
        render json: { error: "任务ID不能为空" }, status: :bad_request
        return
      end

      # 删除指定任务
      success = WechatCrawlTaskService.delete_task(task_id)

      if success
        render json: {
          message: "任务删除成功",
          task_id: task_id,
          timestamp: Time.current.iso8601
        }, status: :ok
      else
        render json: {
          error: "任务删除失败，任务可能不存在或无法删除"
        }, status: :not_found
      end

    rescue StandardError => e
      Rails.logger.error "删除任务失败: #{e.message}"
      render json: {
        error: "删除任务失败: #{e.message}"
      }, status: :internal_server_error
    end
  end

  # 批量爬取微信公众号文章
  def batch_crawl_wechat_articles
    # 只允许管理员访问
    unless current_user.has_role?(:serve_admin)
      render json: { error: "权限不足，只有管理员可以执行此操作" }, status: :forbidden
      return
    end

    urls = params[:urls]
    if urls.blank? || !urls.is_a?(Array)
      render json: { error: "请提供微信公众号文章URL数组" }, status: :bad_request
      return
    end

    results = []
    errors = []

    urls.each_with_index do |url, index|
      next if url.blank? || !url.match?(/mp\.weixin\.qq\.com/)

      activity = Serve::Activity.crawl_from_wechat_url(
        url,
        submodule_id: params[:submodule_id] || 1,
        state: params[:state] || "pending"
      )

      if activity
        results << {
          index: index,
          url: url,
          activity: {
            id: activity.id,
            name: activity.name,
            state: activity.state
          }
        }
      else
        errors << {
          index: index,
          url: url,
          error: "爬取失败"
        }
      end

      # 添加延迟避免被反爬虫
      sleep(2) if index < urls.length - 1
    rescue StandardError => e
      errors << {
        index: index,
        url: url,
        error: e.message
      }
    end

    render json: {
      message: "批量爬取完成，成功: #{results.length}，失败: #{errors.length}",
      results: results,
      errors: errors
    }
  end

  protected

  def begin_of_association_chain
    current_app
  end

  def method_for_association_chain
    :activities
  end

  def after_association_chain(association)
    association.order(position: :asc, created_at: :desc)
  end

  private

  def create_activity_params
    update_activity_params.merge(creator: current_user)
  end

  def update_activity_params
    params.require(:activity).permit(
      *resource_class.try(:extra_permitted_attributes),
      :source_type,
      :source_id,
      :name,
      :model_flag,
      :type,
      :effective_at,
      :invalid_at,
      :view_enable,
      :use_enable,
      :manage_enable,
      :state,
      :position,
      :address,
      :is_hotted,
      :target_type,
      :target_id,
      :region_area_id,
      :province,
      :city,
      :district,
      icon: {},
      model_payload: {},
      model_payload_summary: {},
      model_detail: {},
      views: {},
      uses: {},
      cover_image: {},
      attachments: {},
      content: {},
      layout: {},
      source_info: {},
      catalog_ids: [],
      tag_ids: [],
      origin_ids: [],
      view_serve_permit_actions_attributes: [
        :id,
        :_destroy,
        :target_type,
        :target_id
      ],
      use_serve_permit_actions_attributes: [
        :id,
        :_destroy,
        :target_type,
        :target_id
      ],
      manage_serve_permit_actions_attributes: [
        :id,
        :_destroy,
        :target_type,
        :target_id
      ]
    )
  end

end
