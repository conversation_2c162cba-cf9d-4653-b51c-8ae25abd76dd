class Dingtalk::User::PolymorphicMessagesController < SimpleController::BaseController

  defaults(
    resource_class: Dingtalk::TemplateMessage,
    collection_name: "template_messages",
    instance_name: "template_message",
    view_path: "dingtalk/template_messages"
  )
  auth_action :user

  def create
    user_ids = User.collect_user_ids_from_params(polymorphic_message_params)
    result = if async_message_send?
               enqueue_async_message_send(user_ids, polymorphic_message_params[:message_structure])
             else
               Dingtalk::TemplateMessageSender.call(user_ids, polymorphic_message_params[:message_structure])
             end
    if result[:error_messages] && result[:error_messages].empty?
      render json: result, status: :created
    elsif result[:status] == "queued"
      render json: result, status: :ok
    else
      render json: result, status: unprocessable_entity
    end
  end

  private

  def polymorphic_message_params
    params.require(:polymorphic_message).permit(
      *resource_class.try(:extra_permitted_attributes),
      :async,
      :scheduled_at,
      org_ids: [],
      duty_ids: [],
      user_ids: [],
      tag_ids: [],
      message_structure: {}
    )
  end

  def async_message_send?
    polymorphic_message_params[:async].to_s.downcase == "true"
  end

  def enqueue_async_message_send(user_ids, message_structure)
    scheduled_at = polymorphic_message_params[:scheduled_at].present? ? Time.zone.parse(polymorphic_message_params[:scheduled_at]) : nil
    Dingtalk::TemplateMessageSender.call(user_ids, message_structure, async: true, scheduled_at: scheduled_at.to_i)
  end

end
