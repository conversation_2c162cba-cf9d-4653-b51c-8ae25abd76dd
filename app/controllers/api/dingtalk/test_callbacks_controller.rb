# Test controller for DingTalk callbacks without external dependencies
class Api::Dingtalk::TestCallbacksController < ActionController::API
  def create
    # 输入验证
    return render json: { errcode: "1", errmsg: "事件标识不能为空" }, status: 400 if params[:eventTag].blank?
    return render json: { errcode: "1", errmsg: "回调内容不能为空" }, status: 400 if params[:content].blank?
    return render json: { errcode: "1", errmsg: "租户ID不能为空" }, status: 400 if params[:tenantId].blank?

    event_tag = params[:eventTag]
    content = params[:content]
    tenant_id = params[:tenantId]

    Rails.logger.info "收到浙政钉回调: #{event_tag}, 内容长度: #{content&.length}"

    case event_tag
    when 'MOZI_VDS_TENANT_CHANGE|EMPLOYEE_ADD_UPDATE'
      handle_employee_update(content, tenant_id)
    when 'MOZI_VDS_TENANT_CHANGE|ORGANIZATION_ATTACH_EMPLOYEE'
      handle_employee_attach(content, tenant_id)
    when 'MOZI_VDS_TENANT_CHANGE|ORGANIZATION_DETACH_EMPLOYEE'
      handle_employee_detach(content, tenant_id)
    else
      Rails.logger.warn "未知的事件类型: #{event_tag}"
    end

    render json: { errcode: "0", errmsg: "成功" }
  rescue StandardError => e
    Rails.logger.error "处理回调失败: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
    render json: { errcode: "1", errmsg: "处理失败" }, status: 500
  end

  private

  def handle_employee_update(content, tenant_id)
    data = parse_json_content(content)
    return unless data

    employee_codes = data['employeeCodes']&.split(',') || []
    Rails.logger.info "处理员工信息变更: #{employee_codes.join(', ')}"
    # 在测试中不实际执行Job
  end

  def handle_employee_attach(content, tenant_id)
    data = parse_json_content(content)
    return unless data

    employee_codes = data['employeeCodes']&.split(',') || []
    organization_codes = data['organizationCodes']&.split(',') || []
    Rails.logger.info "员工 #{employee_codes.join(', ')} 加入组织 #{organization_codes.join(', ')}"
  end

  def handle_employee_detach(content, tenant_id)
    data = parse_json_content(content)
    return unless data

    employee_codes = data['employeeCodes']&.split(',') || []
    organization_codes = data['organizationCodes']&.split(',') || []
    Rails.logger.info "员工 #{employee_codes.join(', ')} 离开组织 #{organization_codes.join(', ')}"
  end

  def parse_json_content(content)
    JSON.parse(content)
  rescue JSON::ParserError => e
    Rails.logger.error "JSON解析失败: #{e.message}, 内容: #{content}"
    nil
  end
end
