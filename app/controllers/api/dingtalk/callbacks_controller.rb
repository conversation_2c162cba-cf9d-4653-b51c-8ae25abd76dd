# Handles DingTalk government platform callbacks for employee position changes
class Api::Dingtalk::CallbacksController < ApplicationController

  # 禁用Rails 7.1的回调验证以避免ActionController::RailsCom的兼容性问题
  self.raise_on_missing_callback_actions = false

  # 安全验证 - 验证请求来源
  before_action :verify_callback_source, only: [:create]

  def create
    # 处理浙政钉回调地址验证请求
    if params[:eventTag].blank? && params[:content].blank? && params[:tenantId].blank?
      Rails.logger.info "收到浙政钉回调地址验证请求"

      # 尝试不同的验证响应格式
      case request.headers["User-Agent"]
      when /DingTalk/i
        # 浙政钉标准格式
        return render json: { success: true, errorCode: "200", errorMsg: "success" }
      when /curl/i
        # 测试用简单格式
        return render plain: "success"
      else
        # 通用格式
        return render json: { errcode: "0", errmsg: "验证成功" }
      end
    end

    # 输入验证
    return render json: { errcode: "1", errmsg: "事件标识不能为空" }, status: :bad_request if params[:eventTag].blank?
    return render json: { errcode: "1", errmsg: "回调内容不能为空" }, status: :bad_request if params[:content].blank?
    return render json: { errcode: "1", errmsg: "租户ID不能为空" }, status: :bad_request if params[:tenantId].blank?

    event_tag = params[:eventTag]
    content = params[:content]
    tenant_id = params[:tenantId]

    Rails.logger.info "收到浙政钉回调: #{event_tag}, 内容长度: #{content&.length}"

    case event_tag
    when "MOZI_VDS_TENANT_CHANGE|EMPLOYEE_ADD_UPDATE"
      handle_employee_update(content, tenant_id)
    when "MOZI_VDS_TENANT_CHANGE|ORGANIZATION_ATTACH_EMPLOYEE"
      handle_employee_attach(content, tenant_id)
    when "MOZI_VDS_TENANT_CHANGE|ORGANIZATION_DETACH_EMPLOYEE"
      handle_employee_detach(content, tenant_id)
    else
      Rails.logger.warn "未知的事件类型: #{event_tag}"
    end

    render json: { errcode: "0", errmsg: "成功" }
  rescue StandardError => e
    Rails.logger.error "处理回调失败: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
    render json: { errcode: "1", errmsg: "处理失败" }, status: :internal_server_error
  end

  private

  def handle_employee_update(content, tenant_id)
    data = parse_json_content(content)
    return unless data

    employee_codes = data["employeeCodes"]&.split(",") || []

    Rails.logger.info "处理员工信息变更: #{employee_codes.join(', ')}"

    employee_codes.each do |employee_code|
      # 异步处理员工同步
      Dingtalk::SyncEmployeeJob.perform_later(employee_code.strip, tenant_id)
    end
  end

  def handle_employee_attach(content, tenant_id)
    # 处理员工加入组织事件
    data = parse_json_content(content)
    return unless data

    employee_codes = data["employeeCodes"]&.split(",") || []
    organization_codes = data["organizationCodes"]&.split(",") || []

    Rails.logger.info "员工 #{employee_codes.join(', ')} 加入组织 #{organization_codes.join(', ')}"

    employee_codes.each do |employee_code|
      Dingtalk::SyncEmployeeJob.perform_later(employee_code.strip, tenant_id)
    end
  end

  def handle_employee_detach(content, tenant_id)
    # 处理员工离开组织事件
    data = parse_json_content(content)
    return unless data

    employee_codes = data["employeeCodes"]&.split(",") || []
    organization_codes = data["organizationCodes"]&.split(",") || []

    Rails.logger.info "员工 #{employee_codes.join(', ')} 离开组织 #{organization_codes.join(', ')}"

    employee_codes.each do |employee_code|
      Dingtalk::SyncEmployeeJob.perform_later(employee_code.strip, tenant_id)
    end
  end

  # 安全解析JSON内容
  def parse_json_content(content)
    JSON.parse(content)
  rescue JSON::ParserError => e
    Rails.logger.error "JSON解析失败: #{e.message}, 内容: #{content}"
    nil
  end

  # 验证回调请求来源（可选的安全措施）
  def verify_callback_source
    # 记录回调请求信息用于监控
    client_ip = request.remote_ip
    user_agent = request.headers["User-Agent"] || "Unknown"
    content_type = request.headers["Content-Type"] || "Unknown"
    request_method = request.method

    Rails.logger.info "收到浙政钉回调请求 - Method: #{request_method}, IP: #{client_ip}, User-Agent: #{user_agent}, Content-Type: #{content_type}"
    Rails.logger.info "请求参数: #{params.to_unsafe_h.except(:controller, :action)}"

    # IP白名单验证（仅在配置了白名单时启用）
    allowed_ips = ENV["DINGTALK_CALLBACK_ALLOWED_IPS"]&.split(",")
    if allowed_ips.present? && !allowed_ips.include?(client_ip)
      Rails.logger.warn "未授权的回调请求来源IP: #{client_ip}"
      render json: { errcode: "1", errmsg: "未授权的请求来源" }, status: :unauthorized
      return false
    end

    # 注意：不验证User-Agent，因为浙政钉可能使用各种User-Agent
    true
  end

end
