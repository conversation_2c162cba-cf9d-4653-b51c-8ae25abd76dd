# frozen_string_literal: true

# 测试控制器，用于测试删除任务功能
class Api::TestController < ActionController::API

  # 测试删除任务功能
  def delete_crawl_task
    task_id = params[:task_id]
    if task_id.blank?
      render json: { error: "任务ID不能为空" }, status: :bad_request
      return
    end

    # 删除指定任务
    success = WechatCrawlTaskService.delete_task(task_id)

    if success
      render json: {
        message: "任务删除成功",
        task_id: task_id,
        timestamp: Time.current.iso8601
      }, status: :ok
    else
      render json: {
        error: "任务删除失败，任务可能不存在或无法删除"
      }, status: :not_found
    end
  rescue StandardError => e
    Rails.logger.error "删除任务失败: #{e.message}"
    render json: {
      error: "删除任务失败: #{e.message}"
    }, status: :internal_server_error
  end

  # 获取任务列表
  def crawl_tasks_list
    tasks = WechatCrawlTaskService.get_all_tasks
    render json: {
      tasks: tasks,
      count: tasks.length,
      timestamp: Time.current.iso8601
    }, status: :ok
  rescue StandardError => e
    Rails.logger.error "获取任务列表失败: #{e.message}"
    render json: {
      error: "获取任务列表失败: #{e.message}"
    }, status: :internal_server_error
  end

end
