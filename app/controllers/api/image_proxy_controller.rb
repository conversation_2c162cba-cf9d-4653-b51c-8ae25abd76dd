# frozen_string_literal: true

# 图片代理控制器，用于解决微信公众号图片防盗链问题
class Api::ImageProxyController < ActionController::API

  # 图片代理接口
  def show
    url = params[:url]

    if url.blank?
      render json: { error: "缺少图片URL参数" }, status: :bad_request
      return
    end

    # 验证URL是否为微信图片域名
    unless valid_wechat_image_url?(url)
      render json: { error: "不支持的图片域名" }, status: :bad_request
      return
    end

    # 获取图片数据
    image_data = fetch_image_with_wechat_headers(url)

    if image_data
      # 设置响应头
      response.headers["Content-Type"] = detect_content_type(image_data, url)
      response.headers["Cache-Control"] = "public, max-age=86400" # 缓存1天
      response.headers["Access-Control-Allow-Origin"] = "*"

      # 返回图片数据
      render body: image_data, status: :ok
    else
      render json: { error: "图片获取失败" }, status: :not_found
    end
  rescue StandardError => e
    Rails.logger.error "图片代理失败: #{e.message}"
    render json: { error: "图片代理服务异常" }, status: :internal_server_error
  end

  private

  # 验证是否为有效的微信图片URL
  def valid_wechat_image_url?(url)
    return false unless url.is_a?(String)

    # 允许的微信图片域名
    allowed_domains = [
      "mmbiz.qpic.cn",
      "mmecoa.qpic.cn", 
      "mmbiz.qlogo.cn",
      "wx.qlogo.cn"
    ]

    begin
      uri = URI.parse(url)
      allowed_domains.any? { |domain| uri.host&.include?(domain) }
    rescue URI::InvalidURIError
      false
    end
  end

  # 使用微信兼容的请求头获取图片
  def fetch_image_with_wechat_headers(url)
    require "net/http"
    require "uri"
    require "openssl"

    uri = URI(url)
    http = Net::HTTP.new(uri.host, uri.port)

    if uri.scheme == "https"
      http.use_ssl = true
      http.verify_mode = OpenSSL::SSL::VERIFY_NONE
      http.ssl_timeout = 30
    end

    http.open_timeout = 10
    http.read_timeout = 30

    # 构造请求
    request = Net::HTTP::Get.new(uri)

    # 设置微信兼容的请求头
    request["User-Agent"] = "Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.0(0x18000029) NetType/WIFI Language/zh_CN"
    request["Referer"] = "https://mp.weixin.qq.com/"
    request["Accept"] = "image/webp,image/apng,image/*,*/*;q=0.8"
    request["Accept-Language"] = "zh-CN,zh;q=0.9,en;q=0.8"
    request["Accept-Encoding"] = "gzip, deflate, br"
    request["Connection"] = "keep-alive"

    response = http.request(request)

    case response
    when Net::HTTPSuccess
      # 处理gzip压缩
      if response["Content-Encoding"] == "gzip"
        require "zlib"
        Zlib::GzipReader.new(StringIO.new(response.body)).read
      else
        response.body
      end
    when Net::HTTPRedirection
      # 处理重定向（最多3次）
      redirect_count = 0
      location = response["Location"]

      while location && redirect_count < 3
        redirect_count += 1
        Rails.logger.info "图片重定向到: #{location}"
        return fetch_image_with_wechat_headers(location)
      end

      nil
    else
      Rails.logger.warn "图片获取失败: #{response.code} #{response.message}"
      nil
    end
  rescue StandardError => e
    Rails.logger.error "获取图片异常: #{e.message}"
    nil
  end

  # 检测图片内容类型
  def detect_content_type(image_data, url)
    # 根据文件扩展名判断
    case url.downcase
    when /\.jpe?g/
      "image/jpeg"
    when /\.png/
      "image/png"
    when /\.gif/
      "image/gif"
    when /\.webp/
      "image/webp"
    else
      # 根据文件头判断
      case image_data[0..3]
      when "\xFF\xD8\xFF"
        "image/jpeg"
      when "\x89PNG"
        "image/png"
      when "GIF8"
        "image/gif"
      when "RIFF"
        "image/webp"
      else
        "image/jpeg" # 默认
      end
    end
  end

end
