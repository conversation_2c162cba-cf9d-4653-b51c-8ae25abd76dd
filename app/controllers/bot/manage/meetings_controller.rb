class Bot::Manage::MeetingsController < SimpleController::BaseController
  defaults(
    resource_class: Bot::Meeting,
    collection_name: 'meetings',
    instance_name: 'meeting',
    view_path: 'bot/meetings',
  )
  auth_action :user
  permit_action :bpt_admin, :bot_manage

  def generate_topic_and_summary
    @meeting = resource
    @meeting.generate_topic_and_summary

    head 201
  rescue Bot::Model::Meeting::Error => e
    render json: { error: e.message }, status: :unprocessable_entity
  end

  protected

  def	begin_of_association_chain
    current_app
  end

  def	method_for_association_chain
    :bot_meetings
  end

  private

  def create_meeting_params
    update_meeting_params.merge(
      user: current_user,
    )
  end

  def update_meeting_params
    params.require(:meeting).permit(
      *resource_class.try(:extra_permitted_attributes),
      :user_id,
      :name,
      :meeting_time,
      :background,
      :topic,
      :summary,
      :participants,
      file: {},
      audio: {},
      payload: {},
      permit_res_permit_actions_attributes: [
        :id,
        :_destroy,
        :target_type,
        :target_id,
      ],
    )
  end
end
