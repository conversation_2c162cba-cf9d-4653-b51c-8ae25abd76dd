class Bot::Manage::ReportsController < SimpleController::BaseController
  defaults(
    resource_class: Bot::Report,
    collection_name: 'reports',
    instance_name: 'report',
    view_path: 'bot/reports',
  )
  auth_action :user
  permit_action :bot_admin, :bot_manage

  belongs_to :report_template, collection_name: :bot_report_templates

  protected

  def	begin_of_association_chain
    current_app
  end

  private

  def report_params
    params.require(:report).permit(
      *resource_class.try(:extra_permitted_attributes),
      :user_id,
      :name,
      :title,
      :content,
      variables: {},
    )
  end
end
