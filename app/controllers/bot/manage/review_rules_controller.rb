class Bot::Manage::ReviewRulesController < SimpleController::BaseController
  defaults(
    resource_class: Bot::ReviewRule,
    collection_name: 'review_rules',
    instance_name: 'review_rule',
    view_path: 'bot/review_rules',
  )
  auth_action :user
  permit_action :bot_admin, :bot_manage

  belongs_to :reviewer, collection_name: :bot_reviewers, optional: true
  belongs_to :report_template, collection_name: :bot_report_templates, optional: true

  protected

  def	begin_of_association_chain
    current_app
  end

  private

  def review_rule_params
    params.require(:review_rule).permit(
      *resource_class.try(:extra_permitted_attributes),
      :name,
      :content,
      :scoring_criteria,
      :active,
    )
  end
end
