class Bot::Manage::ReviewersController < SimpleController::BaseController
  defaults(
    resource_class: Bot::Reviewer,
    collection_name: 'reviewers',
    instance_name: 'reviewer',
    view_path: 'bot/reviewers',
  )
  auth_action :user
  permit_action :bot_admin, :bot_manage

  protected

  def	begin_of_association_chain
    current_app
  end

  def	method_for_association_chain
    :bot_reviewers
  end

  private

  def reviewer_params
    params.require(:reviewer).permit(
      *resource_class.try(:extra_permitted_attributes),
      :name,
      :description,
      :review_instructions,
      :model_flag,
      model_payload: {},
      model_payload_summary: {},
      model_detail: {},
      icon: {},
    )
  end
end
