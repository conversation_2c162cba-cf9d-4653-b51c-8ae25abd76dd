class Bot::Manage::ReviewDocumentsController < SimpleController::BaseController
  defaults(
    resource_class: Bot::ReviewDocument,
    collection_name: 'review_documents',
    instance_name: 'review_document',
    view_path: 'bot/review_documents',
  )
  auth_action :user
  permit_action :bot_admin, :bot_manage

  belongs_to :reviewer, collection_name: :bot_reviewers

  protected

  def	begin_of_association_chain
    current_app
  end

  private

  def review_document_params
    params.require(:review_document).permit(
      *resource_class.try(:extra_permitted_attributes),
      :user_id,
      :name,
      file: {},
    )
  end
end
