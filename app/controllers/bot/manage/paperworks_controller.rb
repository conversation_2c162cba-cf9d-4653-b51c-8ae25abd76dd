class Bot::Manage::PaperworksController < SimpleController::BaseController
  defaults(
    resource_class: Bot::Paperwork,
    collection_name: 'paperworks',
    instance_name: 'paperwork',
    view_path: 'bot/paperworks',
  )

  auth_action :user
  permit_action :bot_admin, :bot_manage

  protected

  def begion_of_association_chain
    current_app
  end

  def method_for_association_chain
    :bot_paperworks
  end

  private

  def update_paperwork_params
    params.require(:paperwork).permit(
      *resource_class.try(:extra_permitted_attributes),
      :user_id,
      :name,
      :operate_at,
      :state,
      :prompt_text,
      attachment: {},
      response: {},
    )
  end

  def create_paperwork_params
    update_paperwork_params.merge(user: current_user)
  end
end
