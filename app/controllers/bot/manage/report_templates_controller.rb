class Bot::Manage::ReportTemplatesController < SimpleController::BaseController
  defaults(
    resource_class: Bot::ReportTemplate,
    collection_name: 'report_templates',
    instance_name: 'report_template',
    view_path: 'bot/report_templates',
  )
  auth_action :user
  permit_action :bot_admin, :bot_manage

  protected

  def	begin_of_association_chain
    current_app
  end

  def	method_for_association_chain
    :bot_report_templates
  end

  private

  def report_template_params
    params.require(:report_template).permit(
      *resource_class.try(:extra_permitted_attributes),
      :name,
      :title,
      :content,
      :instructions,
      :review_instructions,
      :prompt,
      conf: {},
      icon: {},
    )
  end
end
