class Bot::Manage::AgentsController < SimpleController::BaseController
  defaults(
    resource_class: Bot::Agent,
    collection_name: 'agents',
    instance_name: 'agent',
    view_path: 'bot/agents',
  )
  auth_action :user
  permit_action :bot_manage

  protected

  def	begin_of_association_chain
    current_app
  end

  def	method_for_association_chain
    :bot_agents
  end

  private

  def agent_params
    params.require(:agent).permit(
      *resource_class.try(:extra_permitted_attributes),
      :type,
      :name,
      :description,
      :instructions,
      :llm_model_key,
      intent_ids: [],
    )
  end
end
