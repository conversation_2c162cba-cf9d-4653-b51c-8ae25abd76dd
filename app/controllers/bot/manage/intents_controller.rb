class Bot::Manage::IntentsController < SimpleController::BaseController
  defaults(
    resource_class: Bot::Intent,
    collection_name: 'intents',
    instance_name: 'intent',
    view_path: 'bot/intents',
  )
  auth_action :user
  permit_action :bot_manage

  protected

  def	begin_of_association_chain
    current_app
  end

  def	method_for_association_chain
    :bot_intents
  end

  private

  def intent_params
    params.require(:intent).permit(
      *resource_class.try(:extra_permitted_attributes),
      :type,
      :name,
      :description,
      :tool_cname,
      :llm_model_key,
      tool_conf: {},
    )
  end
end
