class Bot::User::ReviewDocumentsController < SimpleController::BaseController
  defaults(
    resource_class: Bot::ReviewDocument,
    collection_name: 'review_documents',
    instance_name: 'review_document',
    view_path: 'bot/review_documents',
  )
  auth_action :user

  def perform_review
    @review_document = resource
    @review_document.perform_review

    head 201
  rescue Bot::Reviewable::Error => e
    render json: { error: e.message }, status: :unprocessable_entity
  end

  def ask
    @review_document = resource
    answer = @review_document.ask(params[:question])

    render json: { answer: answer }
  end

  protected

  def begin_of_association_chain
    current_user
  end

  def method_for_association_chain
    :bot_review_documents
  end

  private

  def review_document_params
    params.require(:review_document).permit(
      *resource_class.try(:extra_permitted_attributes),
      :reviewer_id,
      :name,
      :state,
      file: {},
    )
  end
end
