class Bot::User::ReportTemplatesController < SimpleController::BaseController
  defaults(
    resource_class: Bot::ReportTemplate,
    collection_name: 'report_templates',
    instance_name: 'report_template',
    view_path: 'bot/report_templates',
  )
  auth_action :user

  protected

  def	begin_of_association_chain
    current_app
  end

  def	method_for_association_chain
    :bot_report_templates
  end
end
