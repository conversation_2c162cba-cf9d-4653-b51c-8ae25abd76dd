class Bot::User::PaperworksController < SimpleController::BaseController
  defaults(
    resource_class: Bot::Paperwork,
    collection_name: 'paperworks',
    instance_name: 'paperwork',
    view_path: 'bot/paperworks',
  )

  auth_action :user

  protected

  def begin_of_association_chain
    current_user
  end

  def method_for_association_chain
    :bot_paperworks
  end

  private

  def paperwork_params
    params.require(:paperwork).permit(
      *resource_class.try(:extra_permitted_attributes),
      :name,
      :operate_at,
      :state,
      :prompt_text,
      attachment: {},
      response: {},
    )
  end
end
