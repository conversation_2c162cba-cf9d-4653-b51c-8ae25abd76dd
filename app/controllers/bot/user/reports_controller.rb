class Bot::User::ReportsController < SimpleController::BaseController
  defaults(
    resource_class: Bot::Report,
    collection_name: 'reports',
    instance_name: 'report',
    view_path: 'bot/reports',
  )
  auth_action :user

  def perform_review
    @report = resource
    @report.perform_review

    head 201
  rescue Bot::Reviewable::Error => e
    render json: { error: e.message }, status: :unprocessable_entity
  end

  # 文本处理相关接口
  def process_text
    permitted_params = params.require(:report).permit(:operation, :content, :requirements)

    result = resource.process_text(
      operation: permitted_params[:operation],
      content: permitted_params[:content],
      requirements: permitted_params[:requirements],
    )
    render json: { text: result }, status: 201
  rescue ArgumentError => e
    render json: { error: e.message }, status: :unprocessable_entity
  end

  # 根据模板生成报告
  def generate_from_template
    result = resource.generate_from_template
    render json: result, status: 201
  rescue Bot::Report::Error => e
    render json: { error: e.message }, status: :unprocessable_entity
  end

  protected

  def begin_of_association_chain
    current_user
  end

  def method_for_association_chain
    :bot_reports
  end

  private

  def report_params
    params.require(:report).permit(
      *resource_class.try(:extra_permitted_attributes),
      :report_template_id,
      :name,
      :title,
      :content,
      variables: {},
    )
  end
end
