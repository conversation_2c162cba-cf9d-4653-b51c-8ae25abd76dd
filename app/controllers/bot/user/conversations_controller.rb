class Bot::User::ConversationsController < SimpleController::BaseController
  defaults(
    resource_class: Bot::Conversation,
    collection_name: 'conversations',
    instance_name: 'conversation',
    view_path: 'bot/conversations',
  )
  auth_action :user

  def chat
    Bot::Current.conversation = resource

    # 获取 agent 并进行聊天
    agent = resource.agent
    response = agent.chat(
      chat_params[:meta].to_h,
      user: current_user,
      conversation_id: resource.id
    )

    resource.generate_name_if_empty

    render json: {
      message: response
    }, status: :created
  end

  protected

  def begin_of_association_chain
    current_user
  end

  def method_for_association_chain
    :bot_conversations
  end

  private

  def conversation_params
    params.require(:conversation).permit(
      *resource_class.try(:extra_permitted_attributes),
      :name,
      :agent_id,
    )
  end

  def chat_params
    params.require(:conversation).permit(
      :agent_id,
      meta: {}
    )
  end
end
