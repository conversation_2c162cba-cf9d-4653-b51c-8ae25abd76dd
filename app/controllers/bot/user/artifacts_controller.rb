class Bot::User::ArtifactsController < SimpleController::BaseController
  defaults(
    resource_class: Bot::Artifact,
    collection_name: 'artifacts',
    instance_name: 'artifact',
    view_path: 'bot/artifacts',
    distinct_off: true,
  )
  auth_action :user

  belongs_to :conversation, collection_name: :bot_conversations

  def activate
    rlt = resource.activate

    render json: rlt, status: 201
  end

  protected

  def	begin_of_association_chain
    current_user
  end

  private

  def artifact_params
    params.require(:artifact).permit(
      *resource_class.try(:extra_permitted_attributes),
      meta: {},
      info: {},
    )
  end
end
