class Bot::User::ReviewRulesController < SimpleController::BaseController
  defaults(
    resource_class: Bot::ReviewRule,
    collection_name: 'review_rules',
    instance_name: 'review_rule',
    view_path: 'bot/review_rules',
  )
  auth_action :user

  belongs_to :review_document, collection_name: :bot_review_documents, optional: true
  belongs_to :report, collection_name: :bot_reports, optional: true

  protected

  def	begin_of_association_chain
    current_user
  end
end
