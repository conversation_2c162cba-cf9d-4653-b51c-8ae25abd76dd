class Bot::User::ReviewResultsController < SimpleController::BaseController
  defaults(
    resource_class: Bot::ReviewResult,
    collection_name: 'review_results',
    instance_name: 'review_result',
    view_path: 'bot/review_results',
  )
  auth_action :user

  belongs_to :review_document, collection_name: :bot_review_documents, optional: true
  belongs_to :report, collection_name: :bot_reports, optional: true

  protected

  def	begin_of_association_chain
    current_user
  end

  private

  def review_result_params
    params.require(:review_result).permit(
      *resource_class.try(:extra_permitted_attributes),
      :name,
      :raw,
      :score,
      :reason,
      :suggest,
      :level,
      meta: {},
    )
  end
end
