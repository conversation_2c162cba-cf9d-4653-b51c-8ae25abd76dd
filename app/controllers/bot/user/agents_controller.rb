class Bot::User::AgentsController < SimpleController::BaseController
  defaults(
    resource_class: Bot::Agent,
    collection_name: 'agents',
    instance_name: 'agent',
    view_path: 'bot/agents',
  )
  auth_action :user

  protected

  def	begin_of_association_chain
    current_app
  end

  def	method_for_association_chain
    :bot_agents
  end

  private

  def agent_params
    params.require(:agent).permit(
      *resource_class.try(:extra_permitted_attributes),
      :type,
      :name,
      :description,
      :instructions,
    )
  end
end
