class Res::Member::OrgDepartmentsController < SimpleController::BaseController
  defaults(
    resource_class: Department,
    collection_name: 'departments',
    instance_name: 'department',
    view_path: 'departments'
  )

  auth_action :member, skip_error: true

  def index
    if current_member.is_a?(Member)
      # 获取当前用户的组织
      user_orgs = current_user.orgs

      # 使用查询范围对象，避免多次查询
      @query_scope = if params[:org_id].present?
        org = user_orgs.find_by(id: params[:org_id])
        if org.present?
          # 预加载关联数据
          org.departments.includes(:org, :parent)
        else
          Department.none
        end
      else
        # 默认获取用户所有组织的部门，预加载关联数据
        Department.includes(:org, :parent).where(org_id: user_orgs.pluck(:id))
      end

      # 添加所需排序
      @query_scope = @query_scope.order(position: :asc, id: :asc)

      # 应用ransack搜索，但只选择需要的字段
      q = @query_scope.select(:id, :name, :code, :short_name, :type_name, :position, :org_id, :parent_id, :created_at, :updated_at).ransack(params[:q])

      # 设置更大的每页数量，并添加缓存
      per_page = (params[:per_page] || 15).to_i
      @departments = Rails.cache.fetch(["org_departments", user_orgs.pluck(:id).sort, params[:org_id], params[:q]&.to_s, params[:page], per_page], expires_in: 5.minutes) do
        q.result.paginate(page: params[:page], per_page: per_page).to_a
      end
    else
      @departments = Department.none.paginate(page: params[:page], per_page: params[:per_page])
    end
  end

end
