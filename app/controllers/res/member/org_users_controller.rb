class Res::Member::OrgUsersController < SimpleController::BaseController

  defaults(
    resource_class: User,
    importable_class: Res::Member::OrgUserExcel,
    collection_name: "users",
    instance_name: "user",
    view_path: "users"
  )

  auth_action :member, skip_error: true

  def import_precheck
    excel = TalltyImportExport::Excel.new(params[:uid])
    headers = params[:headers]
    result = {
      succeed: true,
      redis_key: nil,
      data: {
        dup: {},
        missing_keywords: []
      }
    }

    accept_users = []

    # 获取当前用户关联的所有组织ID（包括子组织）
    all_org_ids = current_user.orgs.flat_map(&:self_and_descendant_ids).uniq

    # 限定用户范围为当前用户关联组织内的用户
    org_scoped_users = User.joins(:memberships)
      .where(memberships: { org_id: all_org_ids })
      .where("memberships.effective_at IS NULL OR memberships.effective_at <= ?", Time.current)
      .where("memberships.invalid_at IS NULL OR memberships.invalid_at >= ?", Time.current)
      .distinct

    TalltyImportExport::Import.new(User).process_xlsx_line_info(excel, org_scoped_users,
                                                                headers: headers) do |line_info, _associations|
      # 优先使用账号匹配
      if line_info["account"].present?
        user = org_scoped_users.find_by(account: line_info["account"])

        if user
          accept_users << user
        else
          # 账号找不到，再用姓名查找
          users = org_scoped_users.where(name: line_info["name"])

          if users.count > 1
            result[:succeed] = false
            # 创建一个包含所有同名用户的RedisResources对象
            redis_resources = RedisResources.new(resources: users)
            result[:data][:dup][line_info["name"]] = redis_resources.redis_key
          elsif users.count.zero?
            result[:succeed] = false
            result[:data][:missing_keywords] << "#{line_info['name']}#{line_info['account'] ? "(账号: #{line_info['account']})" : ''}"
          else
            accept_users << users.first
          end
        end
      else
        # 没有账号信息，使用姓名查找
        users = org_scoped_users.where(name: line_info["name"])

        if users.count > 1
          result[:succeed] = false
          # 创建一个包含所有同名用户的RedisResources对象
          redis_resources = RedisResources.new(resources: users)
          result[:data][:dup][line_info["name"]] = redis_resources.redis_key
        elsif users.count.zero?
          result[:succeed] = false
          result[:data][:missing_keywords] << line_info["name"]
        else
          accept_users << users.first
        end
      end
    end

    # 总的数据 ids - 修复这里的问题
    if accept_users.any?
      # 移除重复用户
      unique_user_ids = accept_users.map(&:id).uniq

      if params[:redis_key].present?
        result[:redis_key] = params[:redis_key]
        # 覆盖原有 redis_key 数据
        final_redis_resources = RedisResources.new(redis_key: result[:redis_key])
        final_redis_resources.clear
        final_redis_resources.add_ids(*unique_user_ids)
      else
        # 使用 User.where(id: ids) 来获取 ActiveRecord::Relation
        final_redis_resources = RedisResources.new(
          resources: User.where(id: unique_user_ids)
        )
        result[:redis_key] = final_redis_resources.redis_key
      end
    end

    render json: result
  end

  def begin_of_association_chain
    current_auth&.app
  end

  def index
    @users = if current_member.is_a?(Member)
               # 1. 查出所有 org 及其子 org 的 id
               all_org_ids = current_user.orgs.flat_map(&:self_and_descendant_ids).uniq

               # 2. 先查出基础集合
               base_users = User.joins(:memberships)
                 .where(memberships: { org_id: all_org_ids })
                 .where("memberships.effective_at IS NULL OR memberships.effective_at <= ?", Time.current)
                 .where("memberships.invalid_at IS NULL OR memberships.invalid_at >= ?", Time.current)
                 .distinct

               # 3. 应用 ransack
               @q = base_users.ransack(params[:q])
               users = @q.result.paginate(page: params[:page], per_page: params[:per_page])
               users
             else
               User.none.paginate(page: params[:page], per_page: params[:per_page])
             end
  end

  def after_association_chain(association)
    if current_member.is_a?(Member)
      association
    else
      User.none.paginate(page: params[:page], per_page: params[:per_page])
    end
  end

end
