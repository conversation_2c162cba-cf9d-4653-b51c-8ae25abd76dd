class CheckToken

  include Sidekiq::Worker
  sidekiq_options retry: 2 # 设置重试次数
  TOKEN = ENV.fetch("BID_GUARD_TOKEN", "********-acc0-4d5b-8cc5-ac55bc58eaed")
  ACCOUNT = ENV.fetch("BID_GUARD_ACCOUNT", "tallty")
  NOTICE_ACCOUNTS = ENV.fetch("BID_GUARD_NOTICE_ACCOUNT", "***********").split(/,|，|、/)

  def perform
    result = $token_redis.get(TOKEN)
    return if result

    account = SoaAuth::AuthAccount.find_by(account: ACCOUNT)
    $token_redis.set TOKEN, account.as_json.merge(token: TOKEN).to_json

    # 检查环境变量是否禁用浙政钉消息发送
    return if ENV['DISABLE_ZZD_MESSAGE'].to_s.downcase == 'true'

    # 通知人员
    users = User.where(account: NOTICE_ACCOUNTS)
    users.each do |user|
      DingtalkSendMessage.perform_async(
        user.id,
        nil,
        nil,
        {
          msgtype: "action_card",
          action_card: {
            title: "免登token失效了,已尝试重置，请后续检查",
            markdown: "account：#{ACCOUNT}，\ntoken:#{TOKEN}"
          }
        }.to_json
      )
    end
  end

end
