class Serve::PackScheduler

  include Sidekiq::Worker
  sidekiq_options retry: 2 # 设置重试次数

  def perform(**_args)
    Serve::Pack.sending.find_each do |pack|
      next if 2.hours.since < pack.send_at
      next if pack.flag == "queueing"

      Rails.logger.info "=========start====queue====="
      Rails.logger.debug "=========start====queue====="
      pack.update(flag: "queueing")
      if Time.zone.now > pack.send_at
        Serve::MessageScheduler.perform_async(pack.id)
      else
        Serve::MessageScheduler.perform_at(pack.send_at, pack.id)
      end
    rescue StandardError
      next
    end
  end

end
