class Serve::BirthdayScheduler

  include Sidekiq::Worker
  sidekiq_options retry: 2 # 设置重试次数

  def perform(**_options)
    Serve::Birthday.find_and_create_record("now", "birthday",'滨江区')
    Serve::Birthday.find_and_create_record("now", "political_birthday",'滨江区')
    Serve::Birthday.find_and_create_record("next", "birthday",'滨江区')
    Serve::Birthday.find_and_create_record("next", "political_birthday",'滨江区')
  end

end
