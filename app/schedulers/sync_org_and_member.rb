class SyncOrgAndMember

  include Sidekiq::Worker
  sidekiq_options retry: 2 # 设置重试次数
  # 同步杭州市下面的组织和人员
  def perform
    client = Dingtalk::Client.find(3)
    org_service = Dingtalk::OrgService.new(client)
    member_service = Dingtalk::MemberService.new(client)

    father_org = Org.find_by(name: "杭州市")
    #  遍历子组织
    father_org.children.each do |org|
      org_service.sync_departments_of_org(org)
      member_service.sync_members_by(org)
    end
  end

end
