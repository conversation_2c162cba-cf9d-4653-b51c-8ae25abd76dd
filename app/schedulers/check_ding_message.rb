class CheckDingMessage

  include Sidekiq::Worker
  sidekiq_options retry: 2 # 设置重试次数
  # 如果招投标pack中的消息超过时间未读取的，将再次发送ding消息
  def perform
    # 检查ding消息是否已读，将其状态同步到message里
    # 查询条件说明：
    # 1. notifyable_id 不为空
    # 2. response 为 null 或者 response 中没有 sync_complete 字段，或者 sync_complete 不是 true/True
    Dingtalk::DingMessage.where.not(notifyable_id: nil)
      .where("response IS NULL OR response ->> 'sync_complete' IS NULL OR " \
            "(response ->> 'sync_complete' != 'true' AND response ->> 'sync_complete' != 'True')")
      .find_each do |ding_message|
      # 增加调试输出
      Rails.logger.debug("[CheckDingMessage] ding_message_id=\#{ding_message.id} response=\#{ding_message.response.inspect}")

      # 查询情况
      ding_message.check
      check_result = ding_message.response["check_result"]
      unless check_result
        Rails.logger.debug("[CheckDingMessage] 无check_result，跳过 ding_message_id=\#{ding_message.id}")
        next
      end

      read_users = check_result["readUsers"]
      message = ding_message.notifyable
      openid = message.dingtalk_messages&.first&.openid
      if read_users&.include?(openid.to_i)
        message.update_columns(is_read: true, read_at: Time.zone.now)
        ding_message.response["sync_complete"] = true
        ding_message.save
        Rails.logger.info("[CheckDingMessage] 已同步 is_read，ding_message_id=\#{ding_message.id}")
      end
    end
  end

end
