class OneDayLaterDingMessage

  include Sidekiq::Worker
  sidekiq_options retry: 2 # 设置重试次数
  # 廉洁教育工具箱里的一些规则，需要在未读24小时后发送ding消息
  def perform
    # 检查环境变量是否禁用浙政钉消息发送
    return if ENV['DISABLE_ZZD_MESSAGE'].to_s.downcase == 'true'

    rule_ids = ENV.fetch("ONE_DAY_LATER_DING_RULE_IDS", "386").split(",")
    rule_ids.each do |rule_id|
      rule = Serve::Rule.find(rule_id)
      next unless rule

      # 检查已经发送完消息的pack
      rule.packs.where(state: "finished").find_each do |pack|
        start_time = 2.days.ago
        end_time = 1.day.ago
        pack.messages.where.not(is_read: true)
          .where(state: "successed")
          .where(created_at: start_time..end_time)
          .order(created_at: :desc)
          .find_each do |message|
          # 如果已经发送过ding消息，则跳过
          next if Dingtalk::DingMessage.where(notifyable_type: "Serve::Message",
                                              notifyable_id: message.id).count.positive?

          # 将消息中的内容取出，合并成需要发送的话
          if message.activity_id
            activity = Serve::Activity.find(message.activity_id)
            body_html = activity.content["content"]&.first&.dig("body")
            activity_content = Nokogiri::HTML(body_html).text.strip
            msg = message.content + activity_content
          elsif message.content
            msg = message.content
          end
          # 获取其他参数
          user = message.user
          oauth_app_id = message.dingtalk_messages&.first&.oauth_app_id
          openid = user.openid(oauth_app_id)
          # 生成ding消息
          Dingtalk::DingMessage.create!(
            notifyable: message,
            user: user,
            oauth_app_id: oauth_app_id || ENV.fetch("DINGTALK_NOTIFY_APP_CODE", nil),
            dingBody: { text: msg, attachments: [] },
            source: { sourceId: openid, sourceName: user.name },
            receivers: { array: [{ accountId: openid, accountName: user.name }] }
          )
        end
      end
    end
  end

end
