class CleanDuplicateActivity

  include Sidekiq::Worker
  sidekiq_options retry: 2 # 设置重试次数
  def perform(exclude_name = "中央纪委国家监委网站")
    duplicate_names = Serve::Activity.group(:name).having("count(*) > 1").pluck(:name)
    # 遍历重复的 name，进行处理
    duplicate_names.each do |name|
      activities = Serve::Activity.where(name: name)

      # 找到 origin.name 是 '中央纪委国家监委网站' 的记录
      preferred_activity = activities.joins(:origin).where(serve_origins: { name: exclude_name }).first

      if preferred_activity
        # 保留 origin.name 是 '中央纪委国家监委网站' 的记录，删除其他记录
        activities.where.not(id: preferred_activity.id).destroy_all
      else
        # 保留最早 created_at 的记录
        earliest_activity = activities.order(:created_at).first
        activities.where.not(id: earliest_activity.id).destroy_all
      end
    end
  end

end
