class DingtalkSendMessage

  include Sidekiq::Worker
  sidekiq_options retry: 2 # 设置重试次数

  def perform(user_id, notifyable_type, notifyable_id, message, seq, oauth_app_id)
    Dingtalk::TemplateMessage.create!(
      seq: seq,
      notifyable_type: notifyable_type,
      notifyable_id: notifyable_id,
      user_id: user_id,
      oauth_app_id: oauth_app_id || ENV.fetch("DINGTALK_NOTIFY_APP_CODE", nil),
      message: JSON.parse(message)
    )
  end

end
