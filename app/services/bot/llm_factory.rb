module Bot
  class LlmFactory
    def self.create(key = nil)
      key ||= ENV['DEFAULT_LLM'] || 'openai'
      public_send(key.to_sym)
    end

    def self.openai
      Langchain::LLM::OpenAI.new(
        api_key: ENV['OPENAI_API_KEY'],
        llm_options: {
          uri_base: 'http://api.tallty.com/gpt/',
        },
        default_options: {
          chat_model: 'gpt-4-turbo',
        },
      )
    end

    def self.qwen
      Langchain::LLM::Qwen.new(
        api_key: ENV['QWEN_API_KEY'],
        llm_options: {
          uri_base: 'https://dashscope.aliyuncs.com/compatible-mode/',
        },
        default_options: {
          chat_model: 'qwen-max-latest',
          completion_model: 'qwen-max-latest',
        },
      )
    end

    def self.deepseek
      # DeepSeek 标准模式 - 使用阿里云百炼平台 (deepseek-v3)
      Langchain::LLM::Deepseek.new(
        api_key: ENV['QWEN_API_KEY'],
        llm_options: {
          uri_base: 'https://dashscope.aliyuncs.com/compatible-mode/',
        },
        default_options: {
          chat_model: 'deepseek-v3',
          completion_model: 'deepseek-v3',
          # 默认使用text格式，如果需要JSON格式，可以在调用时指定
          response_format: { type: 'text' },
        },
      )
    end

    def self.deepseek_r1
      # DeepSeek R1 深度推理模式 - 使用阿里云百炼平台 (deepseek-r1)
      Langchain::LLM::Deepseek.new(
        api_key: ENV['QWEN_API_KEY'],
        llm_options: {
          uri_base: 'https://dashscope.aliyuncs.com/compatible-mode/',
        },
        default_options: {
          chat_model: 'deepseek-r1',
          completion_model: 'deepseek-r1',
          # 默认使用text格式，如果需要JSON格式，可以在调用时指定
          response_format: { type: 'text' },
        },
      )
    end

    def self.ollama
      Langchain::LLM::Ollama.new(
        url: ENV['OLLAMA_URI_BASE'] || 'http://localhost:11434',
        default_options: {
          temperature: 0.0,
          completion_model: ENV['OLLAMA_MODEL'] || 'llama3.2',
          embedding_model: ENV['OLLAMA_MODEL'] || 'llama3.2',
          chat_model: ENV['OLLAMA_MODEL'] || 'llama3.2',
        },
      )
    end

    def self.gemini
      Langchain::LLM::GoogleGemini.new(
        api_key: ENV['GEMINI_API_KEY'],
        default_options: {
          chat_model: ENV['GEMINI_MODEL'] || 'gemini-2.0-flash-exp',
          # temperature: DEFAULTS[:temperature]
        },
      )
    end

    def self.chat_with_llm(llm, messages:, **options)
      # 使用 Langchain 的适配器
      adapter = Langchain::Assistant::LLM::Adapter.build(llm)

      # 构建参数
      params = adapter.build_chat_params(
        messages: messages.map { |msg| build_message(adapter, msg) },
        instructions: nil,
        tools: [],
        tool_choice: 'none',
        parallel_tool_calls: false,
      )

      # 调用 LLM
      llm.chat(**params)
    end

    def self.build_message(adapter, msg)
      # 角色映射
      role = case adapter
             when Langchain::Assistant::LLM::Adapters::GoogleGemini
               case msg[:role]
               when 'assistant' then 'model'
               when 'system' then 'user' # Gemini 不支持 system role
               else msg[:role]
               end
             else
               msg[:role] # 其他 LLM 保持原样
             end

      adapter.build_message(
        role: role,
        content: msg[:content],
      )
    end
  end
end
