module Bot
  class Tool
    include Toolable
    extend Langchain::ToolDefinition

    class_attribute :functions, default: {}
    attr_reader :description, :function_name, :function_description, :intent

    class << self
      def define_function(method_name, description: nil, &block)
        function_schemas.add_function(method_name:, description:, &block)

        # 初始化回调存储
        @callbacks ||= {}
        @callbacks[method_name] ||= { before: [], after: [] }
      end

      def before_execute(method_name, callback_method = nil, &block)
        @callbacks ||= {}
        @callbacks[method_name] ||= { before: [], after: [] }
        callback = block_given? ? block : callback_method
        # 避免重复添加相同的回调
        unless @callbacks[method_name][:before].include?(callback)
          @callbacks[method_name][:before] << callback
        end
      end

      def after_execute(method_name, callback_method = nil, &block)
        @callbacks ||= {}
        @callbacks[method_name] ||= { before: [], after: [] }
        callback = block_given? ? block : callback_method
        # 避免重复添加相同的回调
        unless @callbacks[method_name][:after].include?(callback)
          @callbacks[method_name][:after] << callback
        end
      end

      def callbacks
        @callbacks ||= {}
      end

      def property(name, type:, description: nil, transformer: nil, required: false, **options)
        super(name, type: type, description: description, required: required, **options.merge(transformer: transformer))
      end
    end

    def initialize(description: nil, function_description: nil, intent: nil, **options)
      @description = description || intent&.description
      @function_description = function_description
      @intent = intent
      @context = {}  # 添加 context 实例变量

      options.each do |key, value|
        instance_variable_set("@#{key}", value)
      end

      super()
    end

    def send(method_name, *args, **kwargs)
      method_name = normalize_method_name(method_name)
      if self.class.callbacks&.key?(method_name)
        # 初始化方法的上下文
        @context ||= {}
        @context[method_name] ||= {}
        @current_method = method_name  # 记录当前方法名

        # 保存原始参数到 context
        @context[method_name][:params] = kwargs

        # 验证必需参数
        validate_required_parameters!(method_name, kwargs)

        # 转换参数
        transformed_kwargs = transform_parameters(kwargs)
        @context[method_name][:transformed_params] = transformed_kwargs

        # 执行before回调
        execute_callbacks(:before, method_name, @context[method_name])

        # 执行方法
        result = super(method_name, **transformed_kwargs)

        # 执行after回调
        execute_callbacks(:after, method_name, result, @context[method_name])

        # 返回结果，包含 function 的返回值和可能的 artifact
        {
          data: result,
          artifact: @context[method_name][:artifact]
        }
      else
        super(method_name, *args, **kwargs)
      end
    ensure
      @current_method = nil  # 清理当前方法名
    end

    protected

    # 通用方法，供子类调用
    def create_artifact(params, type: nil, source: nil)
      return unless Bot::Current.conversation && @intent  # 只有有 intent 的 tool 才创建 artifact
      return unless @current_method  # 确保有当前方法名

      artifact = Bot::Artifact.create!(
        type: type,
        conversation: Bot::Current.conversation,
        intent_name: @intent.name,
        tool_cname: @intent.tool_cname,
        tool_function: @current_method,
        function_params: params,
        tool_conf: @intent.tool_conf,
        source: source
      )

      # 保存到当前上下文
      @context[@current_method][:artifact] = artifact

      artifact
    rescue => e
      Rails.logger.error("Failed to create artifact: #{e.message}")
      nil
    end

    def update_artifact(meta: nil, info: nil)
      return unless @current_method
      artifact = @context[@current_method][:artifact]
      return unless artifact

      artifact.update!(
        meta: meta,
        info: info
      )
    rescue => e
      Rails.logger.error("Failed to update artifact: #{e.message}")
    end

    private

    def normalize_method_name(name)
      name.to_s.to_sym
    end

    def execute_callbacks(type, method_name, *data)
      method_name = normalize_method_name(method_name)
      callbacks = self.class.callbacks.dig(method_name, type)
      return unless callbacks

      callbacks.each do |callback|
        case callback
        when Symbol, String
          send(normalize_method_name(callback), *data)
        when Proc
          instance_exec(*data, &callback)
        end
      end
    end

    def validate_required_parameters!(method_name, arguments)
      method_name = normalize_method_name(method_name)
      properties = self.class.function_schemas
        &.instance_variable_get(:@schemas)&.dig(method_name, :function, :parameters, :properties)
      return unless properties

      required_params = properties.select { |_, v| v['required'] }.keys
      missing_params = required_params.select { |param| arguments[param.to_sym].nil? }
      if missing_params.any?
        raise ArgumentError, "缺少必需的参数: #{missing_params.join(', ')}"
      end
    end

    def transform_parameters(params)
      params.transform_values do |value|
        if value.is_a?(Hash) && value['transformer']
          value['transformer'].transform(value)
        else
          value
        end
      end
    end
  end
end
