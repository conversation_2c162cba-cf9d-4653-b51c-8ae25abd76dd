module Bot
  module Services
    class ConversationNameGenerator
      def self.generate(conversation:)
        new(conversation).generate
      end

      def initialize(conversation)
        @conversation = conversation
        @llm = Bot::LlmFactory.create
      end

      def generate
        messages = format_messages(@conversation.messages.order(created_at: :asc).last(5))
        
        prompt = "Based on the following conversation messages, generate a concise and descriptive name (maximum 50 characters) that captures the main topic or purpose of the conversation.\nOnly return the name, without any additional explanation or formatting.\n\nMessages:\n#{messages}"

        response = @llm.chat(
          messages: [
            { role: "user", content: prompt }
          ]
        )

        extract_name_from_response(response)
      rescue => e
        Rails.logger.error("Error in ConversationNameGenerator: #{e.message}")
        nil
      end

      private

      def format_messages(messages)
        messages.map do |message|
          content = extract_message_content(message.meta)
          "#{message.role.capitalize}: #{content}"
        end.join("\n")
      end

      def extract_message_content(meta)
        return '' unless meta.is_a?(Bot::MessageContent)

        meta.messages.map do |msg|
          if msg[:content_type] == 'text'
            msg[:content]
          else
            # 对于非文本内容，可以选择忽略或用一个占位符替代
            "[Non-text content]"
          end
        end.join(" ")
      end

      def extract_name_from_response(response)
        if response.is_a?(Langchain::LLM::OpenAIResponse)
          content = response.raw_response.dig('choices', 0, 'message', 'content')
        else
          content = response.dig('choices', 0, 'message', 'content')
        end

        content.is_a?(String) ? content.strip : nil
      end
    end
  end
end
