module Bot
  class ResponseValidator
    class ValidationResult
      attr_reader :valid, :issues, :suggestions

      def initialize(valid, issues = [], suggestions = [])
        @valid = valid
        @issues = issues
        @suggestions = suggestions
      end

      def valid?
        @valid
      end

      def has_issues?
        @issues.any?
      end
    end

    def self.validate(response, preprocessing_info = {})
      new.validate(response, preprocessing_info)
    end

    def validate(response, preprocessing_info = {})
      issues = []
      suggestions = []

      # 如果是强制工具调用的查询，进行特殊验证
      if preprocessing_info[:forced_tool_call]
        validate_forced_tool_call_response(response, preprocessing_info, issues, suggestions)
      end

      # 通用验证
      validate_general_response(response, issues, suggestions)

      # 判断是否有效
      valid = issues.empty?

      ValidationResult.new(valid, issues, suggestions)
    end

    private

    def validate_forced_tool_call_response(response, preprocessing_info, issues, suggestions)
      query_type = preprocessing_info[:query_type]
      tool_result = preprocessing_info[:tool_result]

      case query_type
      when :activity_query
        validate_activity_query_response(response, tool_result, issues, suggestions)
      end
    end

    def validate_activity_query_response(response, tool_result, issues, suggestions)
      text_messages = response[:messages]&.select { |msg| msg[:content_type] == 'text' } || []
      artifact_messages = response[:messages]&.select { |msg| msg[:content_type] == 'artifact' } || []

      return if text_messages.empty?

      content = text_messages.first[:content] || ''
      tool_message = tool_result&.dig(:message) || ''

      # 检查是否包含详细分类统计
      classification_keywords = %w[文字 图片 视频 漫画]
      found_classifications = classification_keywords.select { |keyword| content.include?(keyword) }

      if found_classifications.size < 2
        issues << '响应缺少详细的分类统计信息'
        suggestions << '应该包含文字、图片、视频、漫画等分类的具体数量'
      end

      # 检查是否使用了工具返回的数据
      if tool_message.present?
        # 提取工具返回的数字
        tool_numbers = tool_message.scan(/\d+/).map(&:to_i)
        response_numbers = content.scan(/\d+/).map(&:to_i)

        # 检查是否有数字匹配
        if tool_numbers.any? && response_numbers.any?
          common_numbers = tool_numbers & response_numbers
          if common_numbers.empty?
            issues << '响应中的数字与工具返回的数据不匹配'
            suggestions << '应该直接使用工具返回的统计数据'
          end
        end
      end

      # 检查是否有Artifact（组件）
      activity_artifacts = artifact_messages.select do |msg|
        artifact = msg[:content]
        artifact['tool_cname'] == 'Bot::Tools::ActivityQueryTool'
      end

      if activity_artifacts.empty?
        issues << '缺少素材查询组件'
        suggestions << '应该显示可交互的素材查询组件'
      end

      # 检查是否过于简化
      return unless content.match?(/\d+个.*?记录/) && !content.match?(/其中.*?\d+个/)

      issues << '响应过于简化，缺少详细分类'
      suggestions << "应该显示'其中文字X个，图片Y个'等详细分类"
    end

    def validate_general_response(response, issues, suggestions)
      messages = response[:messages] || []

      if messages.empty?
        issues << '响应为空'
        return
      end

      text_messages = messages.select { |msg| msg[:content_type] == 'text' }

      if text_messages.empty?
        issues << '缺少文本响应'
        suggestions << '应该包含文本回答'
      end

      # 检查响应长度
      return unless text_messages.any?

      content = text_messages.first[:content] || ''
      return unless content.length < 10

      issues << '响应过于简短'
      suggestions << '应该提供更详细的回答'
    end
  end
end
