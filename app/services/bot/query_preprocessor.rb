module Bot
  class QueryPreprocessor
    attr_reader :agent, :user, :conversation

    def initialize(agent, user, conversation)
      @agent = agent
      @user = user
      @conversation = conversation
    end

    def self.process(query, agent:, user:, conversation:)
      new(agent, user, conversation).process(query)
    end

    def process(query)
      # 1. 分类查询
      query_type = QueryClassifier.classify(query)

      Rails.logger.info('=== Query Preprocessing ===')
      Rails.logger.info("Original query: #{query}")
      Rails.logger.info("Classified as: #{query_type&.type} (confidence: #{query_type&.confidence})")

      # 2. 如果需要强制工具调用
      if query_type&.should_force_tool_call?
        Rails.logger.info("Triggering forced tool call for #{query_type.type}")
        return process_with_forced_tool_call(query, query_type)
      else
        Rails.logger.info("Not triggering forced tool call: confidence=#{query_type&.confidence}, threshold=#{query_type&.config&.dig(:confidence_threshold)}")
      end

      # 3. 原始查询处理
      {
        processed_query: query,
        preprocessing_info: {
          forced_tool_call: false,
          query_type: query_type&.type,
          confidence: query_type&.confidence
        }
      }
    end

    private

    def process_with_forced_tool_call(query, query_type)
      Rails.logger.info("Forcing tool call for #{query_type.type}")

      # 策略改变：不直接调用工具，而是重写查询强制LLM调用工具
      processed_query = rewrite_query_to_force_tool_call(query, query_type)

      {
        processed_query: processed_query,
        preprocessing_info: {
          forced_tool_call: true,
          query_type: query_type.type,
          confidence: query_type.confidence,
          original_query: query,
          strategy: 'query_rewrite'
        }
      }
    end

    def force_call_tool(query, query_type)
      # 根据查询类型调用相应的工具
      case query_type.type
      when :activity_query
        force_call_activity_tool(query)
      else
        Rails.logger.warn("Unknown query type for forced tool call: #{query_type.type}")
        nil
      end
    end

    def force_call_activity_tool(query)
      # 直接调用ActivityQueryTool
      tool = Bot::Tools::ActivityQueryTool.new

      # 解析查询中的时间范围和关键词
      query_text = extract_query_text(query)

      Rails.logger.info("Calling ActivityQueryTool with query: #{query_text}")

      # 调用工具，传递query参数
      result = tool.query_activities(query: query_text)

      Rails.logger.info("Tool result: #{result}")
      result
    end

    def parse_activity_query_params(query_text)
      # 简单的参数解析逻辑
      params = {}

      # 解析时间范围
      if query_text.match?(/今年|本年/)
        params[:time_range] = 'this_year'
      elsif query_text.match?(/去年|上年/)
        params[:time_range] = 'last_year'
      elsif query_text.match?(/本月|这个月/)
        params[:time_range] = 'this_month'
      elsif query_text.match?(/上月|上个月/)
        params[:time_range] = 'last_month'
      elsif query_text.match?(/最近.*?(\d+).*?天/)
        days = query_text.match(/最近.*?(\d+).*?天/)[1].to_i
        params[:time_range] = "last_#{days}_days"
      elsif query_text.match?(/过去.*?(\d+).*?月|前.*?(\d+).*?月/)
        months = (query_text.match(/过去.*?(\d+).*?月/) || query_text.match(/前.*?(\d+).*?月/))[1].to_i
        params[:time_range] = "last_#{months}_months"
      elsif query_text.match?(/过去.*?(\d+).*?年|前.*?(\d+).*?年/)
        years = (query_text.match(/过去.*?(\d+).*?年/) || query_text.match(/前.*?(\d+).*?年/))[1].to_i
        params[:time_range] = "last_#{years}_years"
      end

      # 解析关键词
      if query_text.match?(/文字|文本/)
        params[:content_type] = 'text'
      elsif query_text.match?(/图片|图像/)
        params[:content_type] = 'image'
      elsif query_text.match?(/视频/)
        params[:content_type] = 'video'
      elsif query_text.match?(/漫画/)
        params[:content_type] = 'comic'
      end

      params
    end

    def rewrite_query_to_force_tool_call(original_query, query_type)
      query_text = extract_query_text(original_query)

      case query_type.type
      when :activity_query
        rewrite_activity_query_to_force_tool(query_text)
      else
        original_query
      end
    end

    def rewrite_activity_query_to_force_tool(query_text)
      # 构建强制调用工具的查询，但保持用户友好的显示
      <<~REWRITTEN_QUERY
        用户询问：#{query_text}

        请调用 query_activities 工具获取素材数据，参数为：query: "#{query_text}"
      REWRITTEN_QUERY
    end

    def extract_query_text(query)
      case query
      when String
        query
      when Hash
        if query[:messages].is_a?(Array)
          query[:messages].map { |msg| msg[:content] }.join(' ')
        else
          query.to_s
        end
      else
        query.to_s
      end
    end
  end
end
