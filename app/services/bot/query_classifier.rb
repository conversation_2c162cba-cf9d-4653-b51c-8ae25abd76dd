module Bot
  class QueryClassifier
    # 查询类型定义
    QUERY_TYPES = {
      activity_query: {
        name: '素材查询',
        keywords: %w[素材 内容 资料 数量 统计 分类 分布 情况 总数 总量 多少 几个],
        time_keywords: %w[今年 去年 本月 上月 最近 过去 前 后 天 周 月 年],
        tool_class: 'Bot::Tools::ActivityQueryTool',
        tool_method: 'query_activities',
        confidence_threshold: 0.3 # 降低阈值，提高触发率
      }
      # 可扩展其他查询类型
      # user_query: {
      #   name: '人员查询',
      #   keywords: %w[人员 用户 员工 成员 团队],
      #   tool_class: 'Bot::Tools::UserQueryTool',
      #   tool_method: 'query_users',
      #   confidence_threshold: 0.7
      # }
    }.freeze

    class QueryType
      attr_reader :type, :confidence, :matched_keywords, :config

      def initialize(type, confidence, matched_keywords, config)
        @type = type
        @confidence = confidence
        @matched_keywords = matched_keywords
        @config = config
      end

      def should_force_tool_call?
        confidence >= config[:confidence_threshold]
      end

      def tool_class
        config[:tool_class]
      end

      def tool_method
        config[:tool_method]
      end
    end

    def self.classify(query)
      new.classify(query)
    end

    def classify(query)
      return nil if query.blank?

      query_text = extract_text_from_query(query)
      best_match = nil
      highest_confidence = 0

      QUERY_TYPES.each do |type, config|
        confidence = calculate_confidence(query_text, config)

        next unless confidence > highest_confidence

        highest_confidence = confidence
        matched_keywords = find_matched_keywords(query_text, config)
        best_match = QueryType.new(type, confidence, matched_keywords, config)
      end

      best_match
    end

    private

    def extract_text_from_query(query)
      case query
      when String
        query
      when Hash
        if query[:messages].is_a?(Array)
          query[:messages].map { |msg| msg[:content] }.join(' ')
        else
          query.to_s
        end
      else
        query.to_s
      end.downcase
    end

    def calculate_confidence(query_text, config)
      keyword_matches = config[:keywords].count { |keyword| query_text.include?(keyword) }
      time_matches = config[:time_keywords]&.count { |keyword| query_text.include?(keyword) } || 0

      # 如果有关键词匹配，基础分数就很高
      if keyword_matches > 0
        base_score = 0.6 # 基础分数60%

        # 每个额外关键词增加10%
        keyword_bonus = (keyword_matches - 1) * 0.1

        # 时间关键词额外加分20%
        time_bonus = time_matches > 0 ? 0.2 : 0

        # 多个关键词组合加分
        combination_bonus = keyword_matches >= 2 ? 0.1 : 0

        [base_score + keyword_bonus + time_bonus + combination_bonus, 1.0].min
      else
        0.0
      end
    end

    def find_matched_keywords(query_text, config)
      matched = []
      matched += config[:keywords].select { |keyword| query_text.include?(keyword) }
      matched += config[:time_keywords]&.select { |keyword| query_text.include?(keyword) } || []
      matched
    end
  end
end
