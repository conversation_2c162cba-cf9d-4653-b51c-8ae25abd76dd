module Bot
  module Transformers
    class ActiveRecordLlm < Base
      SYSTEM_COLUMNS = %w[id created_at updated_at].freeze

      attr_reader :model_class, :fields, :llm

      # @param model_class [Class] ActiveRecord model class
      # @param fields [Array<String>] 用于搜索的字段列表，如果为空则使用所有字段
      # @param llm [Langchain::LLM::Base] LLM实例，默认使用OpenAI
      def initialize(model_class, fields: nil, llm: nil)
        @model_class = model_class
        @fields = normalize_fields(fields)
        @llm = llm || default_llm
      end

      # 将自然语言描述转换为查询条件
      # @param description [String] 自然语言描述
      # @return [Hash] 查询条件
      def transform(description)
        # 获取100条现有记录作为样本
        records = model_class.limit(100).map do |record|
          {
            id: record.id,
            fields: valid_fields.each_with_object({}) do |field, hash|
              hash[field] = record.send(field)
            end,
          }
        end

        prompt = <<~PROMPT
          系统：你是一个记录匹配专家。你的任务是根据用户的描述，从现有记录中找出最匹配的记录。
          你必须严格按照以下JSON格式返回结果：
          {
            "matched_record_id": 数字或null,
            "reason": "选择原因"
          }

          可用的字段列表：
          #{format_fields}

          现有的记录示例：
          #{format_records(records)}

          用户描述：#{description}

          请根据字段内容的相似度和语义相关性进行匹配。如果找到匹配的记录，返回其ID；如果没有找到合适的记录，返回null。
          只返回JSON格式的结果，不要包含其他任何内容。
        PROMPT

        # 直接使用 llm.chat，让 Langchain 的适配器处理转换
        response = Bot::LlmFactory.chat_with_llm(
          llm,
          messages: [
            {
              role: 'user',
              content: prompt,
            },
          ],
          temperature: '0.0', # 使用较低的温度以获得更确定的结果
        )

        # 解析响应并获取匹配的记录ID
        begin
          # 清理响应内容，移除可能的markdown标记
          cleaned_response = response.completion.gsub(/```(?:json)?\n?/, '').strip
          result = JSON.parse(cleaned_response)
          result['matched_record_id']
        rescue JSON::ParserError => e
          Rails.logger.error "无法解析LLM响应: #{e.message}"
          Rails.logger.error "原始响应: #{response.completion}"
          nil
        end
      end

      private

      def normalize_fields(fields)
        Array(fields).map(&:to_s)
      end

      def valid_fields
        @valid_fields ||= begin
          normalized_fields = fields.reject { |f| SYSTEM_COLUMNS.include?(f) }
          normalized_fields.select { |field| model_class.columns_hash.key?(field) }
        end
      end

      def format_fields
        valid_fields.map do |field|
          column = model_class.columns_hash[field]
          "#{field}: #{column.type}"
        end.join("\n")
      end

      def format_records(records)
        records.map do |record|
          fields_str = valid_fields.map do |field|
            value = record[:fields][field]
            "#{field}: #{value.inspect}"
          end.join(', ')
          "ID: #{record[:id]}, #{fields_str}"
        end.join("\n")
      end

      def default_llm
        Bot::LlmFactory.create
      end
    end
  end
end
