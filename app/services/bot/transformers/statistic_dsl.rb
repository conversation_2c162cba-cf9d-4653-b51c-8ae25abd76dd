module Bot
  module Transformers
    class StatisticDsl < Base
      attr_reader :model_class, :llm

      # @param model_class [Class] ActiveRecord model class
      # @param llm [Langchain::LLM::Base] LLM实例，默认使用OpenAI
      def initialize(model_class, llm: nil)
        @model_class = model_class
        @llm = llm || default_llm

        return if model_class.included_modules.include?(Bot::Searchable)

        raise ArgumentError, "#{model_class.name} 需要包含 Bot::Searchable 模块"
      end

      # 将自然语言描述转换为集合统计条件
      # @param description [String] 自然语言描述
      # @return [Com::Attr::Stat::Collection] 统计条件对象
      def transform(description)
        Rails.logger.info('=== StatisticDsl Transform Start ===')
        Rails.logger.info("Input: #{description}")

        # 构建提示
        prompt = build_collection_prompt(description)

        # 调用 LLM
        response = Bot::LlmFactory.chat_with_llm(
          llm,
          messages: [
            {
              role: 'user',
              content: prompt,
            },
          ],
          temperature: '0.0',
        )

        Rails.logger.debug("LLM Response: #{response.chat_completion}")

        # 解析统计条件
        stat_config = parse_stat_config(response.chat_completion)
        Rails.logger.info("Generated stat config: #{stat_config.inspect}")

        # 直接合并默认统计条件
        stat_config = merge_default_calc_conditions_directly(stat_config, description)
        Rails.logger.info("Merged with default conditions: #{stat_config.inspect}")

        # 创建统计条件对象
        stat_condition = Com::Attr::Stat::Collection.new(stat_config)
        Rails.logger.info('=== StatisticDsl Transform End ===')

        stat_condition
      end

      # 将自然语言描述转换为资源统计条件
      # @param description [String] 自然语言描述
      # @return [Com::Attr::Stat::Resource] 资源统计条件对象
      # def transform_for_resource(description)
      #   Rails.logger.info('=== StatisticDsl Transform For Resource Start ===')
      #   Rails.logger.info("Input: #{description}")

      #   # 构建提示
      #   prompt = build_resource_prompt(description)

      #   # 调用 LLM
      #   response = Bot::LlmFactory.chat_with_llm(
      #     llm,
      #     messages: [
      #       {
      #         role: 'user',
      #         content: prompt,
      #       },
      #     ],
      #     temperature: '0.0',
      #   )

      #   Rails.logger.debug("LLM Response: #{response.chat_completion}")

      #   # 解析统计条件
      #   stat_config = parse_stat_config(response.chat_completion)
      #   Rails.logger.info("Generated stat config: #{stat_config.inspect}")

      #   # 创建统计条件对象
      #   stat_condition = Com::Attr::Stat::Resource.new(stat_config)
      #   Rails.logger.info('=== StatisticDsl Transform For Resource End ===')

      #   stat_condition
      # end

      private

      def build_fields_prompt
        sections = []
        sections << "可统计的字段说明：\n"

        # 添加属性字段
        if model_class.searchable_attributes.any?
          sections << '属性字段:'
          model_class.searchable_attributes.each do |field|
            desc = model_class.searchable_field_descriptions[field]
            next unless desc.is_a?(Hash)

            field_info = []
            field_info << desc[:name] if desc[:name]
            field_info << "(#{desc[:column_type]})" if desc[:column_type]

            # 根据字段类型添加统计说明
            case desc[:column_type]
            when :integer, :decimal, :float
              field_info << "支持: count(计数), sum(求和), average(平均值), maximum(最大值), minimum(最小值)"
            when :datetime, :date
              field_info << "支持: count(计数), maximum(最新), minimum(最早)"
            when :string, :text
              field_info << "支持: count(计数)"
            else
              field_info << "支持: count(计数)"
            end

            sections << "- #{field}: #{field_info.join(' ')}"
          end
          sections << ''
        end

        # 添加关联查询
        if model_class.searchable_associations.any?
          sections << '关联统计:'
          model_class.searchable_associations.each do |assoc|
            desc = model_class.searchable_field_descriptions[assoc]
            next unless desc.is_a?(Hash)

            sections << "- #{assoc} (#{desc[:association_type]}):"
            sections << "  可以统计关联#{desc[:model]}的数量和相关字段"

            # 显示可用的字段
            if desc[:fields].present?
              field_list = desc[:fields].map { |field, name| "#{field}(#{name})" }.join(', ')
              sections << "  可用字段: #{field_list}"

              # 根据字段数量生成查询格式
              if desc[:fields].keys.include?('name') && desc[:fields].keys.include?('short_name')
                # 有 name 和 short_name 字段，使用 _or_ 组合
                sections << "  查询格式: #{assoc}_name_or_#{assoc}_short_name_eq"
              elsif desc[:fields].keys.size == 1
                # 只有一个字段，使用单个字段
                field = desc[:fields].keys.first
                sections << "  查询格式: #{assoc}_#{field}_eq"
              else
                # 其他情况，列出所有可能的格式
                sections << "  查询格式: #{desc[:fields].keys.map { |field| "#{assoc}_#{field}_eq" }.join(', ')}"
              end
            end
          end
          sections << ''
        end

        # 添加默认统计条件说明
        if model_class.respond_to?(:statisticable_calc_condition)
          default_conditions = model_class.statisticable_calc_condition
          if default_conditions.present?
            sections << '默认统计项:'
            sections << '当进行基础统计时，除了总数外，还会自动包含以下统计项：'
            default_conditions.each do |condition|
              sections << "- #{condition[:name]}"
            end
            sections << ''
          end
        end

        sections.join("\n")
      end

      def build_collection_prompt(description)
        <<~PROMPT
          你是一个数据统计专家，需要帮我将自然语言转换为统计配置。
          你的任务是生成一个Ruby Hash，用于数据统计分析。

          严格要求：
          1. 只返回一个Ruby Hash，不要有任何额外的文字说明
          2. 不要加任何前缀（如"输出:"）
          3. 不要使用代码块标记(```)
          4. 返回的必须是一个有效的Ruby Hash
          5. 只能使用提供的字段，不要使用任何未列出的字段
          6. 对于分组统计，优先级顺序：多个filter条件 > group_attr > group_calc（最后选择）
          7. 注意识别组织+部门组合的层级关系：
             - 对于"杭州市滨江区纪委"：组织是"滨江区"，部门是"纪委"
             - 对于"浙江省杭州市滨江区宣传部"：组织是"滨江区"，部门是"宣传部"
             - 总是选择最具体的地区作为组织，最后的职能部门作为部门
             - 常见组织层级：省->市->区/县，选择最小的行政区划
             - 常见部门：宣传部、纪委、财政局、教育局、人事处等
             - 识别规则：从右往左找，最后一个是部门，倒数第二个带"区"、"县"、"市"的是组织
          8. 字段名称规则：
             - 关联字段查询格式：{关联名}_{字段名}_eq
             - 只能使用关联的 fields 中明确定义的字段
             - 对于有 name 和 short_name 字段的关联（如 orgs、departments），使用 _or_ 组合同时搜索：
               * orgs: 使用 orgs_name_or_orgs_short_name_eq
               * departments: 使用 departments_name_or_departments_short_name_eq
             - 对于只有单个字段的关联（如 duties 只有 name），使用单个字段：
               * duties: 使用 duties_name_eq
             - 不要自动添加不存在的字段
          9. 默认统计项规则：
             - 当用户进行基础统计时（如"统计用户数量"），除了总数外，还要自动包含默认统计项
             - 如果模型定义了默认统计项，必须在 caculations 数组中包含这些项
             - 默认统计项会在字段说明中的"默认统计项"部分列出

          #{build_fields_prompt}

          统计配置格式说明:

          1. 基础统计配置:
          {
            items: [
              {
                key: 'stat_key',                    # 统计结果的键名
                filter: { field_eq: 'value' },     # 可选：过滤条件 (使用Ransack格式)
                caculator: {
                  type: 'caculation',               # 计算器类型
                  caculations: [
                    {
                      name: '显示名称',              # 统计项的显示名称
                      method: 'count',              # 统计方法: count, sum, average, maximum, minimum
                      attr: 'field_name',           # 可选：要统计的字段名，count时可省略
                      filter: { field_eq: 'value' }, # 可选：该统计项的额外过滤条件
                      group_attr: 'field_name',     # 可选：分组字段，用于简单分组统计
                      scopes: ['scope_name'],       # 可选：应用的scope过滤，支持字符串、数组或Hash
                      joins: ['association'],       # 可选：关联表
                      left_joins: ['association'],  # 可选：左关联
                      distinct: true,               # 可选：去重
                      order: 'field_name',          # 可选：排序
                      limit: 10                     # 可选：限制数量
                    }
                  ]
                }
              }
            ]
          }

          2. 分组统计配置方式一（推荐 - 使用多个 filter 条件）:
          {
            items: [
              {
                key: 'group_stat_key',
                filter: { field_eq: 'value' },
                caculator: {
                  type: 'caculation',
                  caculations: [
                    { key: '男', name: '男', filter: { gender_eq: '男' }, method: 'count' },
                    { key: '女', name: '女', filter: { gender_eq: '女' }, method: 'count' }
                  ]
                }
              }
            ]
          }

          3. 分组统计配置方式二（使用 caculation 中的 group_attr）:
          {
            items: [
              {
                key: 'group_stat_key',
                filter: { field_eq: 'value' },
                caculator: {
                  type: 'caculation',
                  caculations: [
                    { name: '分组统计', group_attr: 'gender', method: 'count' }
                  ]
                }
              }
            ]
          }

          4. 分组统计配置方式三（最后选择 - 使用 group_calc）:
          {
            items: [
              {
                key: 'group_stat_key',
                filter: { field_eq: 'value' },     # 可选：过滤条件
                caculator: {
                  type: 'group_calc',              # 分组计算器类型
                  method: 'group_count',           # 分组统计方法: group_count, group_sum, group_average
                  group_attrs: ['field_name']      # 分组字段，如 ['gender'], ['status'], ['department_id']
                }
              }
            ]
          }

          示例转换：

          输入: "统计用户数量"
          {
            items: [
              {
                key: 'user_stats',
                caculator: {
                  type: 'caculation',
                  caculations: [
                    { name: '用户总数', method: 'count' },
                    { name: '男性用户数', filter: { gender_eq: '男' }, method: 'count' },
                    { name: '女性用户数', filter: { gender_eq: '女' }, method: 'count' }
                  ]
                }
              }
            ]
          }

          输入: "统计技术部有多少人"
          {
            items: [
              {
                key: 'department_stats',
                filter: { departments_name_or_departments_short_name_eq: '技术部' },
                caculator: {
                  type: 'caculation',
                  caculations: [
                    { name: '技术部人数', method: 'count' }
                  ]
                }
              }
            ]
          }

          输入: "统计滨江区宣传部有多少人"
          {
            items: [
              {
                key: 'org_department_stats',
                filter: {
                  orgs_name_or_orgs_short_name_eq: '滨江区',
                  departments_name_or_departments_short_name_eq: '宣传部'
                },
                caculator: {
                  type: 'caculation',
                  caculations: [
                    { name: '滨江区宣传部人数', method: 'count' }
                  ]
                }
              }
            ]
          }

          输入: "统计杭州市滨江区纪委男性人数"
          {
            items: [
              {
                key: 'org_department_gender_stats',
                filter: {
                  orgs_name_or_orgs_short_name_eq: '滨江区',
                  departments_name_or_departments_short_name_eq: '纪委',
                  gender_eq: '男'
                },
                caculator: {
                  type: 'caculation',
                  caculations: [
                    { name: '滨江区纪委男性人数', method: 'count' }
                  ]
                }
              }
            ]
          }

          输入: "统计浙江省杭州市西湖区财政局有多少人"
          {
            items: [
              {
                key: 'org_department_stats',
                filter: {
                  orgs_name_or_orgs_short_name_eq: '西湖区',
                  departments_name_or_departments_short_name_eq: '财政局'
                },
                caculator: {
                  type: 'caculation',
                  caculations: [
                    { name: '西湖区财政局人数', method: 'count' }
                  ]
                }
              }
            ]
          }

          输入: "统计滨江区助理岗位人数"
          {
            items: [
              {
                key: 'assistant_position_stats',
                filter: {
                  orgs_name_or_orgs_short_name_eq: '滨江区',
                  members_pos_job_eq: '助理'
                },
                caculator: {
                  type: 'caculation',
                  caculations: [
                    { name: '滨江区助理岗位人数', method: 'count' }
                  ]
                }
              }
            ]
          }

          输入: "统计最近一个月的会议数量和平均时长"
          {
            items: [
              {
                key: 'recent_meeting_stats',
                filter: { created_at_gteq: 30.days.ago },
                caculator: {
                  type: 'caculation',
                  caculations: [
                    { name: '会议数量', method: 'count' },
                    { name: '平均时长', method: 'average', attr: 'duration' }
                  ]
                }
              }
            ]
          }

          输入: "统计技术部男和女各有多少人"
          推荐方式（使用多个 filter 条件）:
          {
            items: [
              {
                key: 'department_gender_stats',
                filter: { departments_name_or_departments_short_name_eq: '技术部' },
                caculator: {
                  type: 'caculation',
                  caculations: [
                    { key: '男', name: '男', filter: { gender_eq: '男' }, method: 'count' },
                    { key: '女', name: '女', filter: { gender_eq: '女' }, method: 'count' }
                  ]
                }
              }
            ]
          }

          输入: "统计各部门的人数分布"
          {
            items: [
              {
                key: 'department_distribution',
                caculator: {
                  type: 'caculation',
                  caculations: [
                    { name: '部门分布', group_attr: 'departments_name', method: 'count' }
                  ]
                }
              }
            ]
          }

          输入: "统计活跃用户中各部门的人数"
          {
            items: [
              {
                key: 'active_department_stats',
                caculator: {
                  type: 'caculation',
                  caculations: [
                    {
                      name: '活跃用户部门分布',
                      group_attr: 'departments_name',
                      method: 'count',
                      scopes: ['active']
                    }
                  ]
                }
              }
            ]
          }

          输入: "统计最近登录的技术部员工数量"
          {
            items: [
              {
                key: 'recent_tech_staff',
                filter: { departments_name_or_departments_short_name_eq: '技术部' },
                caculator: {
                  type: 'caculation',
                  caculations: [
                    {
                      name: '最近登录技术部员工',
                      method: 'count',
                      scopes: ['recent_login']
                    }
                  ]
                }
              }
            ]
          }

          现在，请将以下描述转换为统计配置：
          #{description}
        PROMPT
      end

      def build_resource_prompt(description)
        <<~PROMPT
          你是一个数据统计专家，需要帮我将自然语言转换为资源统计配置。
          你的任务是生成一个Ruby Hash，用于单个资源的关联数据统计。

          严格要求：
          1. 只返回一个Ruby Hash，不要有任何额外的文字说明
          2. 不要加任何前缀（如"输出:"）
          3. 不要使用代码块标记(```)
          4. 返回的必须是一个有效的Ruby Hash
          5. 只能使用提供的关联字段，不要使用任何未列出的字段

          #{build_fields_prompt}

          资源统计配置格式说明:
          {
            refs: [
              {
                relations: ['association_name'],    # 关联路径，可以是多层关联
                scopes: ['scope_name'],             # 可选：关联数据的scope过滤
                item: {
                  key: 'stat_key',                  # 统计结果的键名
                  filter: { field_eq: 'value' },   # 可选：过滤条件
                  caculator: {
                    type: 'caculation',
                    caculations: [
                      {
                        name: '显示名称',
                        method: 'count',
                        attr: 'field_name',         # 可选：要统计的字段
                        filter: { field_eq: 'value' } # 可选：额外过滤条件
                      }
                    ]
                  }
                }
              }
            ]
          }

          示例转换：

          输入: "统计该用户的会议数量"
          {
            refs: [
              {
                relations: ['bot_meetings'],
                item: {
                  key: 'user_meeting_stats',
                  caculator: {
                    type: 'caculation',
                    caculations: [
                      { name: '会议总数', method: 'count' }
                    ]
                  }
                }
              }
            ]
          }

          输入: "统计该用户已完成的会议数量"
          {
            refs: [
              {
                relations: ['bot_meetings'],
                item: {
                  key: 'user_completed_meeting_stats',
                  filter: { state_eq: 'finished' },
                  caculator: {
                    type: 'caculation',
                    caculations: [
                      { name: '已完成会议数', method: 'count' }
                    ]
                  }
                }
              }
            ]
          }

          现在，请将以下描述转换为资源统计配置：
          #{description}
        PROMPT
      end

      def parse_stat_config(completion)
        # 清理和标准化输入
        code = completion.to_s
                         .gsub(/^(输出:|返回:|result:|output:)/i, '')
                         .gsub(/^```\w*\s*|```\s*$/m, '')
                         .gsub(/^#.*$/, '')
                         .strip

        # 提取第一个有效的 Hash
        code = extract_hash_from_text(code)
        Rails.logger.debug("Extracted Hash: #{code}")

        # 确保是一个hash
        unless code.start_with?('{') && code.end_with?('}')
          Rails.logger.error("Invalid Hash Format: #{code}")
          return default_stat_config
        end

        # 安全的代码执行环境
        result = nil
        begin
          result = eval(code)
        rescue SecurityError => e
          Rails.logger.error("Security Error: #{e.message}")
          return default_stat_config
        rescue StandardError => e
          Rails.logger.error("Parse Error: #{e.message}")
          return default_stat_config
        end

        # 确保结果是Hash类型
        unless result.is_a?(Hash)
          Rails.logger.error("Invalid Result Type: #{result.inspect}")
          return default_stat_config
        end

        # 递归处理时间对象
        result = process_time_values(result)
        Rails.logger.debug("Processed Result: #{result.inspect}")

        # 自动合并默认统计条件
        result = merge_default_calc_conditions(result)
        Rails.logger.debug("Merged with default conditions: #{result.inspect}")

        result
      end

      # 直接合并默认统计条件（不依赖 LLM 分析）
      def merge_default_calc_conditions_directly(stat_config, description)
        return stat_config unless model_class.respond_to?(:statisticable_calc_condition)

        default_conditions = model_class.statisticable_calc_condition
        return stat_config if default_conditions.blank?

        # 检查是否是基础统计查询
        if is_basic_statistics_query?(description, stat_config)
          Rails.logger.info("Detected basic statistics query, adding default conditions directly")

          # 遍历所有统计项，直接添加默认条件
          stat_config[:items]&.each do |item|
            next unless item[:caculator]&.dig(:type) == 'caculation'
            next unless item[:caculator][:caculations].is_a?(Array)

            caculations = item[:caculator][:caculations]

            # 直接添加默认统计条件
            default_conditions.each do |condition|
              caculations << condition.dup
            end
          end
        end

        stat_config
      end

      # 判断是否是基础统计查询
      def is_basic_statistics_query?(description, stat_config)
        # 检查描述中是否包含统计关键词
        return false unless description.include?('统计')

        # 检查统计配置是否比较简单（只有基础统计项）
        stat_config[:items]&.each do |item|
          caculations = item[:caculator]&.dig(:caculations)
          next unless caculations.is_a?(Array)

          # 如果只有一个统计项且没有 filter，认为是基础统计
          return true if caculations.size == 1 && caculations.first[:filter].blank?
        end

        false
      end

      # 合并默认统计条件
      def merge_default_calc_conditions(stat_config)
        return stat_config unless model_class.respond_to?(:statisticable_calc_condition)

        default_conditions = model_class.statisticable_calc_condition
        return stat_config if default_conditions.blank?

        # 遍历所有统计项
        stat_config[:items]&.each do |item|
          next unless item[:caculator]&.dig(:type) == 'caculation'
          next unless item[:caculator][:caculations].is_a?(Array)

          caculations = item[:caculator][:caculations]

          # 检查是否是基础统计（只有总数统计，没有其他条件）
          if is_basic_statistics?(caculations)
            Rails.logger.info("Detected basic statistics, adding default conditions")
            # 添加默认统计条件
            default_conditions.each do |condition|
              caculations << condition.dup
            end
          end
        end

        stat_config
      end

      # 判断是否是基础统计（只有总数，没有其他分组条件）
      def is_basic_statistics?(caculations)
        return false if caculations.blank?

        # 如果只有一个统计项，且没有 filter 条件，认为是基础统计
        caculations.size == 1 && caculations.first[:filter].blank?
      end

      def extract_hash_from_text(text)
        # 计算括号的深度来正确提取嵌套的Hash
        depth = 0
        start_index = nil

        text.chars.each_with_index do |char, i|
          case char
          when '{'
            depth += 1
            start_index = i if depth == 1
          when '}'
            depth -= 1
            return text[start_index..i] if depth == 0 && start_index
          end
        end

        '{}' # 如果没有找到有效的Hash结构
      end

      def process_time_values(hash)
        case hash
        when Hash
          hash.transform_values { |v| process_time_values(v) }
        when Array
          hash.map { |v| process_time_values(v) }
        when String
          if hash.include?('Time.current') || hash.include?('days.ago') || hash.include?('.ago')
            eval(hash)
          else
            hash
          end
        else
          hash
        end
      end

      def default_stat_config
        {
          items: [
            {
              key: 'default_stats',
              caculator: {
                type: 'caculation',
                caculations: [
                  { name: '总数', method: 'count' }
                ]
              }
            }
          ]
        }
      end

      def default_llm
        Bot::LlmFactory.create
      end
    end
  end
end

