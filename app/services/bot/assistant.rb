module Bot
  class Assistant < Langchain::Assistant
    def chat(message, conversation:, **options)
      # 创建用户消息
      conversation.messages.create!(
        role: 'user',
        meta: message
      )

      # 如果传入的是消息数组格式，转换为文本
      content = case message
                when Hash
                  if message[:messages].is_a?(Array)
                    message[:messages].map { |msg| format_content(msg[:content]) }.join("\n")
                  else
                    format_content(message)
                  end
                when String
                  message
                else
                  format_content(message)
                end

      # 添加用户消息并运行助手（自动执行工具）
      add_message_and_run!(content: content)

      # 获取最后一条助手消息
      assistant_message = messages.last

      # 收集所有生成的 artifacts
      artifacts = messages.select { |msg| msg.tool_calls.present? }.flat_map do |msg|
        msg.tool_calls.map do |call|
          # 从工具调用的结果中获取 artifact
          tool_response = messages.find { |m| m.tool_call_id == call['id'] }
          next unless tool_response

          # 解析工具返回的 JSON
          begin
            response_data = JSON.parse(tool_response.content, symbolize_names: true)
            Bot::Artifact.new(response_data[:artifact]) if response_data[:artifact].is_a?(Hash)
          rescue JSON::ParserError
            nil
          end
        end.compact
      end

      # 构造返回的消息格式
      response = {
        messages: [
          {
            content_type: 'text',
            content: assistant_message.content
          },
          *artifacts.map do |artifact|
            {
              content_type: 'artifact',
              content: artifact.as_json
            }
          end
        ]
      }

      # 创建助手回复消息
      conversation.messages.create!(
        role: 'assistant',
        meta: response
      )

      response
    end

    private

    def run_tool(tool_call)
      tool_call_args = @llm_adapter.extract_tool_call_args(tool_call: tool_call)
      tool_call_id, tool_name, method_name, tool_arguments, intent_id = tool_call_args

      tool_instance = if intent_id
                        tools.find do |t|
                          t.class.tool_name == tool_name &&
                            t.instance_variable_get(:@intent)&.id.to_s == intent_id
                        end or raise ArgumentError, "Tool: #{tool_name} with intent #{intent_id} not found"
                      else
                        tools.find do |t|
                          t.class.tool_name == tool_name
                        end or raise ArgumentError, "Tool: #{tool_name} not found"
                      end

      # Call the callback if set
      tool_execution_callback.call(tool_call_id, tool_name, method_name, tool_arguments) if tool_execution_callback
      output = tool_instance.send(method_name, **tool_arguments)

      # 确保工具输出被序列化为 JSON
      output_json = output.to_json

      submit_tool_output(tool_call_id: tool_call_id, output: output_json)
    end

    def format_content(content)
      case content
      when Hash then content.to_json
      else content.to_s
      end
    end

    protected

    # 重载 execute_tool! 方法来处理 artifact
    def execute_tool!(name:, arguments:, **options)
      result = super

      # 如果工具返回了 artifact，保存到 tool_call 中
      if result.is_a?(Hash) && result[:artifact]
        options[:tool_call][:function][:artifact] = result[:artifact]
        result = result[:data] # 只返回数据部分给 LLM
      end

      result
    end
  end
end
