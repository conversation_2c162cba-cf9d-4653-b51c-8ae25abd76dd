# app/services/bot/chat_bot.rb
module Bot
  class ChatBot
    attr_reader :assistant

    def self.create
      # llm = Langchain::LLM::OpenAI.new(api_key: '***************************************************', llm_options: { uri_base: 'http://api.tallty.com/gpt/' })
      llm = Langchain::LLM::OpenAI.new(api_key: 'sk-6ef8d6782710415bb1e23cff6779d273', llm_options: { uri_base: 'https://dashscope.aliyuncs.com/compatible-mode/' },
                                       default_options: { chat_model: 'qwen-turbo' },)
      new(
        Langchain::Assistant.new(
          llm: llm,
          tools: [
            Tools::MessageSender.new,
            Tools::ContentWriter.new,
            Tools::ContractReviewer.new,
            Tools::SuggestionTool.new(llm: llm),
          ],
          instructions: <<~INSTRUCTIONS,
            You are a helpful assistant that can, now is #{Time.zone.now}}:
            1. Send messages to recipients based on rules
            2. Write content based on specific requirements
            3. Review and analyze contracts

            After executing any tool function, please provide a clear, friendly response to the user
            explaining what was done and what the next steps might be.
          INSTRUCTIONS
        ),
      )
    end

    def initialize(assistant)
      @assistant = assistant
    end

    def chat(content)
      # 获取当前已有的消息数，作为基准点
      initial_message_count = assistant.messages.size
      messages = assistant.add_message_and_run!(content: content)
      # 获取本次 chat 新增的消息
      new_messages = messages[initial_message_count..-1]
      # 提取本次调用中所有的 tool_calls
      tool_calls = new_messages.flat_map { |msg| msg.tool_calls || [] }

      last_message = messages.last

      # 裁剪消息列表，只保留最近 100 条
      assistant.messages.replace(assistant.messages.last(100))

      Response.new(
        content: last_message.content,
        role: last_message.role,
        tool_calls: tool_calls,
      )
    rescue StandardError => e
      Rails.logger.error("Bot Error: #{e.message}\n#{e.backtrace.join("\n")}")
      Response.new(
        content: "抱歉，我在处理您的请求时遇到了问题。#{e.message}",
        role: 'assistant',
      )
    end
  end

  # 封装响应对象，使其更易于使用
  class Response
    attr_reader :content, :role, :tool_calls

    def initialize(content:, role:, tool_calls: [])
      @content = content
      @role = role
      @tool_calls = tool_calls
    end

    def assistant_message?
      role == 'assistant'
    end

    def has_tool_calls?
      tool_calls.any?
    end

    # 获取友好的展示信息
    def display_message
      content.to_s
    end

    # 用于调试或日志记录
    def to_h
      {
        content: content,
        role: role,
        tool_calls: tool_calls,
      }
    end
  end
end
