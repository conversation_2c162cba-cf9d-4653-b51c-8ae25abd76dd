module Bot
  module Tools
    class MessageSender < Bot::Tool
      attr_reader :rule_finder, :user_finder

      define_function :plan_message, description: '规划消息发送，如需要发送多人也会进行合并发送' do
        property :rule_description, type: 'string', description: '规则的描述，用于查找对应的规则', required: true
        property :recipient_query, type: 'string', description: '收件人的描述，可以是具体的人名或者群体描述', required: true
        property :send_at, type: 'string', description: '发送时间, 格式需要带上时区信息。不填则默认为明天上午9:00。这里给出的时间必须在当前时间之后', required: false
        # property :content, type: 'string', description: '具体要发送的消息内容。注意：这里填写的是实际要发送的具体内容，而不是规则或提醒的描述。如果用户没有明确指定具体内容，这里应该留空，由规则来决定具体内容', required: false
      end

      def initialize(llm: nil, **options)
        super(llm: llm, **options)
        prepare_finders
      end

      # 在类级别注册回调
      before_execute(:plan_message) do |context|
        # 即使参数不全也创建 artifact
        create_artifact(
          context[:params],
          type: 'Bot::MessageSenderArtifact',
        )
      end

      after_execute(:plan_message) do |result, context|
        # 更新 artifact
        update_artifact(
          meta: result,
        )
      end

      def plan_message(rule_description:, recipient_query:, send_at: nil, content: nil)
        # 查找规则
        rule_id = @rule_finder.transform(rule_description)
        # rule_id = rule_description

        # 设置默认发送时间为明天上午9:00
        send_at ||= Time.now.getlocal.tomorrow.change(hour: 9, min: 0, sec: 0).strftime('%Y-%m-%d %H:%M:%S %z')

        # 查找收件人
        user_condition = begin
          @user_finder.transform(recipient_query)
        rescue StandardError
          nil
        end

        {
          current_user_id: ::Current.user.id,
          rule_id: rule_id,
          user_conditions: user_condition,
          send_at: send_at,
          content: content,
        }
      end

      private

      def prepare_finders
        @rule_finder = Bot::Transformers::ActiveRecordLlm.new(
          Serve::Rule,
          fields: [:name, :description],
        )

        @user_finder = Bot::Transformers::RansackDsl.new(::User)
      end

      def find_rule(description)
        Serve::Rule.find_by(id: @rule_finder.transform(description))
      rescue ArgumentError
        nil
      end

      def find_recipients(query)
        conditions = user_finder.transform(query)
        ::Current.user.org_users.ransack(conditions).result
      rescue ArgumentError
        []
      end
    end
  end
end
