module Bot
  module Tools
    class ActivityQueryConfig
      # 默认配置
      DEFAULT_CONFIG = {
        # 模型配置
        model_class: 'Serve::Activity',
        scope_chain: 'all',

        # Artifact配置
        artifact_type: 'Bot::ActivityListArtifact',

        # 类型映射配置
        content_type_mapping: {
          '文章' => '文字',
          '文字' => '文字',
          '图片' => '图片',
          '视频' => '视频',
          '漫画' => '漫画'
        },

        # 状态映射配置
        state_mapping: {
          'published' => '已发布',
          'pending' => '待发布',
          'draft' => '草稿',
          'archived' => '已归档'
        },

        # 时间字段配置
        time_fields: %w[published_at created_at],

        # 时间字段描述映射
        time_field_descriptions: {
          'published_at' => '发布',
          'created_at' => '创建'
        },

        # 关联配置
        associations: {
          content_type_tags: {
            join_table: 'serve_activity_actions',
            foreign_key: 'user_id',
            foreign_type: 'user_type',
            target_key: 'target_id',
            target_type: 'target_type',
            action_type: 'action_type',
            expected_user_type: 'Serve::Activity',
            expected_target_type: 'Serve::ContentTypeTag',
            expected_action_type: 'content_type',
            target_table: 'serve_content_type_tags',
            target_name_field: 'name'
          }
        }
      }.freeze

      attr_reader :config

      def initialize(custom_config = {}, validate_model_existence: false)
        @config = DEFAULT_CONFIG.deep_merge(custom_config)
        @validate_model_existence = validate_model_existence
        validate_config!
      end

      # 获取配置项的便捷方法
      def model_class
        @config[:model_class]
      end

      def scope_chain
        @config[:scope_chain]
      end

      def artifact_type
        @config[:artifact_type]
      end

      def content_type_mapping
        @config[:content_type_mapping]
      end

      def state_mapping
        @config[:state_mapping]
      end

      def time_fields
        @config[:time_fields]
      end

      def time_field_descriptions
        @config[:time_field_descriptions]
      end

      def associations
        @config[:associations]
      end

      def content_type_association
        @config[:associations][:content_type_tags]
      end

      # 根据查询提取内容类型
      def extract_content_type_from_query(query)
        content_type_mapping.each do |keyword, type|
          return type if query.include?(keyword)
        end
        nil
      end

      # 获取状态的中文描述
      def get_state_description(state)
        state_mapping[state] || state
      end

      # 获取时间字段的中文描述
      def get_time_field_description(field)
        time_field_descriptions[field] || ''
      end

      # 构建内容类型统计的SQL查询
      def build_content_type_statistics_sql
        assoc = content_type_association

        <<~SQL
          SELECT #{assoc[:target_name_field]}, COUNT(DISTINCT #{model_class.constantize.table_name}.id) as count
          FROM #{model_class.constantize.table_name}
          INNER JOIN #{assoc[:join_table]} ON #{assoc[:join_table]}.#{assoc[:foreign_key]} = #{model_class.constantize.table_name}.id
            AND #{assoc[:join_table]}.#{assoc[:foreign_type]} = '#{assoc[:expected_user_type]}'
            AND #{assoc[:join_table]}.#{assoc[:target_type]} = '#{assoc[:expected_target_type]}'
            AND #{assoc[:join_table]}.#{assoc[:action_type]} = '#{assoc[:expected_action_type]}'
          INNER JOIN #{assoc[:target_table]} ON #{assoc[:target_table]}.id = #{assoc[:join_table]}.#{assoc[:target_key]}
          GROUP BY #{assoc[:target_name_field]}
          ORDER BY count DESC
        SQL
      end

      # 验证配置的有效性
      def validate_config!
        # 验证模型类是否存在（仅在需要时验证）
        if @validate_model_existence
          begin
            model_class.constantize
          rescue NameError
            raise ArgumentError, "Model class '#{model_class}' does not exist"
          end
        end

        # 验证必要的配置项
        required_keys = %i[model_class artifact_type content_type_mapping state_mapping time_fields]
        required_keys.each do |key|
          raise ArgumentError, "Missing required config key: #{key}" unless @config.key?(key)
        end

        # 验证关联配置
        assoc = content_type_association
        required_assoc_keys = %i[join_table foreign_key target_key target_table target_name_field]
        required_assoc_keys.each do |key|
          raise ArgumentError, "Missing required association config key: #{key}" unless assoc.key?(key)
        end
      end

      # 类方法：获取默认配置实例
      def self.default
        @default_instance ||= new
      end

      # 类方法：创建自定义配置实例
      def self.create(custom_config = {})
        new(custom_config)
      end
    end
  end
end
