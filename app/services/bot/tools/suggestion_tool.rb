module Bot
  module Tools
    class SuggestionTool < Bot::Tool
      define_function :generate_suggestions, description: "生成建议选项" do
        property :context, type: "string", description: "当前对话内容", required: true
        property :last_message, type: "string", description: "最后一条消息", required: true
        property :messages_count, type: "integer", description: "历史消息数量", required: true
      end

      def initialize(llm:)
        @llm = llm
      end

      def generate_suggestions(context:, last_message:, messages_count:)
        response = @llm.chat(
          messages: [
            {
              role: "system",
              content: "你是建议生成专家。请生成3个后续问题建议，以JSON格式返回。每个建议需要包含text和confidence字段。"
            },
            {
              role: "user",
              content: context
            }
          ]
        )

        suggestions = JSON.parse(response.chat_completion)
        suggestions.to_json # 确保返回格式化的JSON字符串
      end
    end
  end
end
