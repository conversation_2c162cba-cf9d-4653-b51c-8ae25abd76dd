module Bot
  module Tools
    class ActiveRecordTool < Bot::Tool
      attr_reader :model_class, :fields, :transformer

      define_function :find_record, description: "根据自然语言描述查找数据库记录" do
        property :description, type: "string", description: "记录的自然语言描述", required: true
      end

      def initialize(model_class:, fields: nil, llm: nil, **options)
        super(llm: llm, **options)
        @model_class = model_class.is_a?(String) ? model_class.constantize : model_class
        @fields = fields
        prepare_transformer
      end

      # 在类级别注册回调
      before_execute(:find_record) do |context|
        # 创建 artifact
        create_artifact(context[:params])
      end

      after_execute(:find_record) do |result, context|
        # 更新 artifact
        update_artifact(
          meta: { conditions: context[:conditions] },
          info: {
            record: result[:record].as_json,
            message: result[:message]
          }
        )
      end

      def find_record(description:)
        # 使用 transformer 将自然语言转换为查询条件
        result_id = transformer.transform(description)

        # 查找记录
        record = model_class.find_by(id: result_id)

        if record.nil?
          {
            status: 'not_found',
            message: "无法找到符合描述的记录: #{description}",
            record: nil
          }
        else
          {
            status: 'success',
            message: "成功找到记录",
            record: record
          }
        end
      rescue ArgumentError => e
        {
          status: 'error',
          message: e.message,
          record: nil
        }
      end

      private

      def prepare_transformer
        @transformer = Bot::Transformers::ActiveRecordLlm.new(
          model_class,
          fields: fields,
          llm: llm
        )
      end
    end
  end
end
