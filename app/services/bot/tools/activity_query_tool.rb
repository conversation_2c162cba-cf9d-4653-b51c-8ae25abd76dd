require_relative 'activity_query_config'

module Bot
  module Tools
    class ActivityQueryTool < RansackTool
      # 重写函数定义以保持接口兼容性
      define_function :query_activities,
                      description: '活动素材查询工具。用于查询活动相关的素材信息，包括数量统计、类型分布、时间范围查询等。当用户询问关于活动素材的问题时，应调用此工具获取最新数据。工具会返回详细的分类统计信息。' do
        property :query, type: 'string', description: '用户关于素材的自然语言问题，如"有多少素材"、"过去一年的素材"、"图片数量"等', required: true
      end

      attr_reader :query_config

      def initialize(model_class: nil, scope_chain: nil, llm: nil, query_config: nil, **options)
        # 初始化查询配置
        @query_config = query_config || ActivityQueryConfig.default

        # 使用配置中的默认值，但允许参数覆盖
        final_model_class = model_class || @query_config.model_class
        final_scope_chain = scope_chain || @query_config.scope_chain

        # 调用父类初始化
        super(model_class: final_model_class, scope_chain: final_scope_chain, llm: llm, **options)
      end

      # 重写回调以支持素材查询特定的artifact和前端集成
      before_execute(:query_activities) do |context|
        create_artifact(
          context[:params],
          type: @query_config.artifact_type
        )
      end

      after_execute(:query_activities) do |result, context|
        # 从方法执行结果中获取查询条件和统计信息
        conditions = result[:conditions] || {}
        total_count = result[:total_count] || 0

        # 更新artifact的meta数据，包含前端需要的完整结构
        update_artifact(
          meta: {
            q: conditions,
            total_count: total_count,
            query_description: build_activity_query_description(conditions),
            # 直接在meta中包含前端期望的结构
            frontend_params: {
              payload: {
                params: {
                  q: conditions
                }
              }
            }
          }
        )
      end

      def query_activities(query:)
        # 使用父类的transformer解析查询条件
        conditions = transformer.transform(query)

        # 设置上下文供回调使用（保留已有的artifact）
        @context[:query_activities] ||= {}
        @context[:query_activities][:conditions] = conditions

        # 使用父类的方法构建基础查询
        base_query = build_base_query

        # 执行ransack查询
        ransack_query = base_query.ransack(conditions)
        records = ransack_query.result
        total_count = records.count

        # 构建智能响应消息
        message = build_intelligent_response(query, conditions, records, total_count)

        # 获取artifact（由before_execute回调创建）
        artifact = @context[:query_activities][:artifact]

        # 返回数据，artifact由Bot::Tool.send方法自动处理
        {
          message: message,
          total_count: total_count,
          conditions: conditions
        }
      end

      private

      def build_intelligent_response(query, conditions, records, total_count)
        # 检测查询类型
        query_type = detect_query_type(query, conditions)

        case query_type
        when :total_count_query
          build_total_count_response(total_count, conditions)
        when :type_specific_query
          build_type_specific_response(query, conditions, total_count)
        else
          build_filtered_response(conditions, total_count)
        end
      end

      def detect_query_type(query, conditions)
        # 如果查询中包含类型相关词汇，认为是类型特定查询
        if query.match?(/文章|图片|视频|漫画|文字/)
          :type_specific_query
        # 如果查询中包含数量相关词汇，认为是总数查询（即使有时间等过滤条件）
        elsif query.match?(/多少|数量|总数|总计|统计/)
          :total_count_query
        else
          :filtered_query
        end
      end

      def build_total_count_response(total_count, conditions = {})
        # 获取类型统计（基于当前查询条件）
        type_stats = get_content_type_statistics_with_conditions(conditions)

        # 根据是否有过滤条件调整消息
        if conditions.present?
          query_desc = build_activity_query_description(conditions)
          base_message = "在#{query_desc}范围内，共有#{total_count}个素材"
        else
          base_message = "目前库里的素材总数为#{total_count}个"
        end

        if type_stats.any?
          type_details = type_stats.map { |type, count| "#{type}#{count}个" }.join('，')
          "#{base_message}，其中#{type_details}。"
        else
          "#{base_message}。"
        end
      end

      def build_type_specific_response(query, conditions, total_count)
        # 识别用户询问的具体类型
        requested_type = extract_content_type_from_query(query)

        if requested_type
          type_count = get_specific_type_count(requested_type)
          "系统里有#{type_count}个#{requested_type}类型的素材。"
        else
          build_filtered_response(conditions, total_count)
        end
      end

      def build_filtered_response(conditions, total_count)
        query_desc = build_activity_query_description(conditions)
        "在#{query_desc}范围内，我们有#{total_count}个素材记录。如果您需要具体的素材列表或详细信息，请告知，我可以为您提供更精确的信息。"
      end

      def get_content_type_statistics
        # 使用配置化的SQL查询
        sql = @query_config.build_content_type_statistics_sql

        results = ActiveRecord::Base.connection.execute(sql)
        results.map { |row| [row['name'], row['count'].to_i] }
      rescue StandardError => e
        Rails.logger.error "获取类型统计失败: #{e.message}"
        []
      end

      def get_content_type_statistics_with_conditions(conditions = {})
        # 如果没有条件，使用原来的方法
        return get_content_type_statistics if conditions.empty?

        # 使用ActiveRecord查询来应用条件
        base_query = build_base_query
        ransack_query = base_query.ransack(conditions)
        filtered_activities = ransack_query.result

        # 统计过滤后的活动的类型分布
        type_counts = {}
        filtered_activities.includes(:content_type_tags).find_each do |activity|
          activity.content_type_tags.each do |tag|
            type_counts[tag.name] = (type_counts[tag.name] || 0) + 1
          end
        end

        # 按数量排序
        type_counts.sort_by { |_, count| -count }
      rescue StandardError => e
        Rails.logger.error "获取条件类型统计失败: #{e.message}"
        []
      end

      def extract_content_type_from_query(query)
        @query_config.extract_content_type_from_query(query)
      end

      def get_specific_type_count(type_name)
        base_query = build_base_query

        # 通过content_type_tags关联查询特定类型的数量
        base_query.joins(:content_type_tags)
                  .where(serve_content_type_tags: { name: type_name })
                  .distinct
                  .count
      rescue StandardError => e
        Rails.logger.error "获取特定类型数量失败: #{e.message}"
        0
      end

      def build_activity_query_description(conditions)
        descriptions = []

        # 时间范围描述 - 支持多种时间字段
        time_field_info = detect_time_field_and_range(conditions)
        if time_field_info
          field_name = time_field_info[:field]
          date = time_field_info[:date]
          days_ago = (Date.current - date).to_i

          field_desc = @query_config.get_time_field_description(field_name)

          time_desc = case days_ago
                      when 0..1
                        '今天'
                      when 2..7
                        '最近一周'
                      when 8..30
                        '最近一个月'
                      when 31..90
                        '最近三个月'
                      else
                        "#{date.strftime('%Y年%m月%d日')}以后"
                      end

          descriptions << "#{time_desc}#{field_desc}"
        end

        # 状态描述
        descriptions << @query_config.get_state_description(conditions['state_eq']) if conditions['state_eq']

        # 关键词描述
        descriptions << "包含「#{conditions['name_cont']}」" if conditions['name_cont']

        descriptions.empty? ? '所有素材' : descriptions.join('的')
      end

      def detect_time_field_and_range(conditions)
        # 使用配置中的时间字段
        @query_config.time_fields.each do |field|
          gteq_key = "#{field}_gteq"
          next unless conditions[gteq_key] || conditions[gteq_key.to_sym]

          date_value = conditions[gteq_key] || conditions[gteq_key.to_sym]
          date = begin
            Date.parse(date_value.to_s)
          rescue StandardError
            nil
          end

          return { field: field, date: date } if date
        end

        nil
      end
    end
  end
end
