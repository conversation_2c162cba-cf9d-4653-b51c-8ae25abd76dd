module Bot
  module Tools
    class RansackTool < Bot::Tool
      attr_reader :model_class, :scope_chain, :transformer

      define_function :query_records, description: '执行数据库记录查询' do
        property :query, type: 'string', description: '自然语言查询描述', required: true
      end

      # 在类级别注册回调
      before_execute(:query_records) do |context|
        # 创建 artifact
        create_artifact(context[:params])
      end

      after_execute(:query_records) do |result, context|
        # 更新 artifact
        update_artifact(
          meta: { q: context[:conditions] },
        )
      end

      def initialize(model_class:, scope_chain: nil, llm: nil, **options)
        @model_class = model_class.is_a?(String) ? model_class.constantize : model_class
        @scope_chain = scope_chain
        @llm = llm
        @transformer = Bot::Transformers::RansackDsl.new(@model_class, llm: @llm)
        super(**options)
      end

      def query_records(query:)
        Rails.logger.info('=== RansackTool Query ===')
        Rails.logger.info("Query: #{query}")

        # 转换自然语言为 ransack 查询条件
        conditions = transformer.transform(query)
        @context[:query_records][:conditions] = conditions
        Rails.logger.info("Conditions: #{conditions.inspect}")

        # 应用 ransack 查询
        base_query = build_base_query
        Rails.logger.debug("Base SQL: #{base_query.to_sql}")

        begin
          ransack_query = base_query.ransack(conditions)
          records = ransack_query.result
          Rails.logger.debug("Final SQL: #{records.to_sql}")

          {
            total_count: records.count,
          }
        rescue StandardError => e
          Rails.logger.error("Query failed: #{e.message}")
          raise e
        end
      end

      private

      def build_base_query
        base_query = model_class

        if scope_chain.present?
          if scope_chain.is_a?(String)
            # 处理字符串形式的 scope_chain
            scopes = scope_chain.split('.')
            current_query = base_query
            scopes.each do |scope|
              current_query = if scope == 'Current'
                                ::Current
                              else
                                current_query.send(scope)
                              end
            end
            base_query = current_query
          else
            # 处理数组形式的 scope_chain
            scope_chain.each do |scope|
              scope_name = scope.to_sym
              base_query = base_query.send(scope_name) if base_query.respond_to?(scope_name)
            end
          end
        end

        base_query
      end
    end
  end
end
