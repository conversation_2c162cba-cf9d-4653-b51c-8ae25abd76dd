module Bot
  module Tools
    class PaperworkTool < Bot::Tool
      attr_reader :prompt_text

      define_function :analyze_document, description: "分析文档内容" do
        property :name, type: "string", description: "文档名称", required: true
        property :attachment, type: "object", description: "文档附件信息", required: false
      end

      def initialize(prompt:, llm: nil, **options)
        super(llm: llm, **options)
        @prompt_text = prompt
      end

      # 在类级别注册回调
      before_execute(:analyze_document) do |context|
        # 创建 artifact 和关联的 paperwork
        paperwork = Bot::Paperwork.create!(
          user: ::Current.user,
          name: context[:params][:name],
          prompt_text: prompt_text,
          attachment: context[:params][:attachment],
          operate_at: Time.current,
        )

        create_artifact(
          context[:params],
          source: paperwork
        )
      end

      after_execute(:analyze_document) do |result, context|
        # 更新 artifact
        update_artifact(
          meta: { prompt: prompt_text },
          info: result
        )
      end

      def analyze_document(name:, attachment: nil)
        # 返回初始状态，实际分析会通过 Paperwork 的异步任务进行
        {
          status: 'processing',
          message: '文档分析任务已创建，正在处理中',
          timestamp: Time.current.iso8601
        }
      end
    end
  end
end
