module Bot
  module Tools
    class StatisticTool < Bot::Tool
      attr_reader :model_class, :scope_chain, :transformer

      define_function :collection_statistic, description: "对数据集合进行统计分析" do
        property :query, type: "string", description: "统计需求的自然语言描述", required: true
      end

      # define_function :resource_statistic, description: "对单个资源进行统计分析" do
      #   property :resource_id, type: "integer", description: "资源ID", required: true
      #   property :query, type: "string", description: "统计需求的自然语言描述", required: true
      # end

      def initialize(model_class:, scope_chain: nil, llm: nil, **options)
        super(llm: llm, **options)
        @model_class = model_class.is_a?(String) ? model_class.constantize : model_class
        @scope_chain = scope_chain
        @transformer = Bot::Transformers::StatisticDsl.new(@model_class, llm: llm)
      end

      # 在类级别注册回调
      before_execute(:collection_statistic) do |context|
        create_artifact(context[:params])
      end

      after_execute(:collection_statistic) do |result, context|
        update_artifact(
          meta: {
            query: context[:params][:query],
            stat_condition: context[:stat_condition],
            model_class: @model_class.name,
            scope_chain: @scope_chain
          },
          info: {
            result: result[:data],
            total_records: result[:total_records]
          }
        )
      end

      # before_execute(:resource_statistic) do |context|
      #   create_artifact(context[:params])
      # end

      # after_execute(:resource_statistic) do |result, context|
      #   update_artifact(
      #     meta: {
      #       query: context[:params][:query],
      #       stat_condition: context[:stat_condition],
      #       resource_id: context[:params][:resource_id],
      #       model_class: @model_class.name
      #     },
      #     info: {
      #       result: result[:data],
      #       resource_found: result[:resource_found]
      #     }
      #   )
      # end

      def collection_statistic(query:)
        Rails.logger.info('=== StatisticTool Collection Statistic ===')
        Rails.logger.info("Model: #{model_class.name}")
        Rails.logger.info("Query: #{query}")

        begin
          # 使用 transformer 将自然语言转换为统计条件
          stat_condition = transformer.transform(query)
          @context[:collection_statistic][:stat_condition] = stat_condition
          Rails.logger.info("Generated stat condition: #{stat_condition.inspect}")

          # 构建基础查询
          base_query = build_base_query
          Rails.logger.debug("Base SQL: #{base_query.to_sql}")

          # 执行统计
          stat_result = base_query.ta_statistic(stat_condition)
          total_records = base_query.count

          Rails.logger.info("Statistic Result: #{stat_result.inspect}")

          {
            status: 'success',
            data: stat_result,
            total_records: total_records,
            message: "完成统计分析: #{query}"
          }
        rescue StandardError => e
          Rails.logger.error("Collection statistic failed: #{e.message}")
          Rails.logger.error(e.backtrace.join("\n"))

          {
            status: 'error',
            data: {},
            total_records: 0,
            message: "统计分析失败: #{e.message}"
          }
        end
      end

      # def resource_statistic(resource_id:, query:)
      #   Rails.logger.info('=== StatisticTool Resource Statistic ===')
      #   Rails.logger.info("Model: #{model_class.name}")
      #   Rails.logger.info("Resource ID: #{resource_id}")
      #   Rails.logger.info("Query: #{query}")

      #   begin
      #     # 查找资源
      #     resource = model_class.find_by(id: resource_id)

      #     if resource.nil?
      #       return {
      #         status: 'not_found',
      #         data: {},
      #         resource_found: false,
      #         message: "无法找到ID为 #{resource_id} 的#{model_class.model_name.human}记录"
      #       }
      #     end

      #     # 使用 transformer 将自然语言转换为统计条件
      #     stat_condition = transformer.transform_for_resource(query)
      #     @context[:resource_statistic][:stat_condition] = stat_condition
      #     Rails.logger.info("Generated stat condition: #{stat_condition.inspect}")

      #     # 执行统计
      #     stat_result = resource.ta_statistic(stat_condition)

      #     Rails.logger.info("Statistic Result: #{stat_result.inspect}")

      #     {
      #       status: 'success',
      #       data: stat_result,
      #       resource_found: true,
      #       message: "完成资源统计分析: #{query}"
      #     }
      #   rescue StandardError => e
      #     Rails.logger.error("Resource statistic failed: #{e.message}")
      #     Rails.logger.error(e.backtrace.join("\n"))

      #     {
      #       status: 'error',
      #       data: {},
      #       resource_found: resource.present?,
      #       message: "资源统计分析失败: #{e.message}"
      #     }
      #   end
      # end

      private

      def build_base_query
        base_query = model_class.all

        if scope_chain.present?
          if scope_chain.is_a?(String)
            # 处理字符串形式的 scope_chain
            scopes = scope_chain.split('.')
            current_query = base_query
            scopes.each do |scope|
              current_query = if scope == 'Current'
                                ::Current
                              else
                                current_query.send(scope)
                              end
            end
            base_query = current_query
          else
            # 处理数组形式的 scope_chain
            scope_chain.each do |scope|
              scope_name = scope.to_sym
              base_query = base_query.send(scope_name) if base_query.respond_to?(scope_name)
            end
          end
        end

        base_query
      end
    end
  end
end

