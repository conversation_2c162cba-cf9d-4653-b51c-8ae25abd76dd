require 'streamio-ffmpeg'

class AudioConvertService
	# 视屏转成音频
	def self.convert(video_path: nil,  **options)
		filename = SecureRandom.hex + '.mp3'
		prefix = App.first&.code
    key = [prefix, Time.now.year, filename].compact.join('/').downcase

		output_dir = File.join(Rails.root, 'public', 'audios', filename)
		movie = FFMPEG::Movie.new(video_path)
		movie.transcode(output_path, { audio_codec: 'libmp3lame', custom: %w(-ac 2 -ar 44100 -q:a 2) })

		result = OssService.put(File.read(output_path), key: key, filename: filename)
		{
			duration: movie.duration,
			url: result['url'],
			status: 'done',
			fileKey: result['key'],
			percent: 100,
			fileSize: result['byte_size'],
			duration: movie.duration,
			fileName: filename,
			fileType: 'mpeg',
			mimeType: 'audio/mpeg',
			fileCategory: 'audio'
		}
	rescue Exception => e
		{}
	end
end
