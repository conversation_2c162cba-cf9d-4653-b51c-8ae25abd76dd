# frozen_string_literal: true

module Irs

  # 政府项目同步服务基类
  # 提供通用的同步逻辑，具体项目类型继承并实现特定逻辑
  class BaseProjectSyncService

    include ActiveModel::Model
    include ActiveModel::Attributes

    # 同步结果结构
    class SyncResult

      include ActiveModel::Model
      include ActiveModel::Attributes

      attribute :success, :boolean, default: false
      attribute :action, :string # created, updated, skipped
      attribute :project, default: nil
      attribute :error, :string
      attribute :irs_data, default: -> { {} }

      def success?
        success
      end

      def failure?
        !success
      end

    end

    # 项目类型配置
    PROJECT_TYPES = {
      lixiang: {
        code_prefix: "LX",
        name_suffix: "立项项目",
        payload_key: "lixiang_project",
        item_name_patterns: ["可行性研究报告", "建议书", "立项"]
      },
      shigong: {
        code_prefix: "SG",
        name_suffix: "施工许可证项目",
        payload_key: "shigong_permit_project",
        item_name_patterns: ["施工许可", "初步设计", "施工图设计"]
      },
      change: {
        code_prefix: "BG",
        name_suffix: "项目变更",
        payload_key: "project_change",
        item_name_patterns: ["变更", "信息变更", "调整"]
      }
    }.freeze

    attr_accessor :project_type, :irs_service

    def initialize(project_type = nil)
      @project_type = project_type&.to_sym
      @irs_service = Irs::RequestService.new
      validate_project_type!
    end

    # 根据政府项目代码同步单个项目
    # @param project_code [String] 政府项目代码
    # @return [SyncResult] 同步结果
    def sync_single_project_by_code(project_code)
      safe_log(:info, "[#{project_type_name}同步] 开始同步项目: #{project_code}")

      # 检查项目是否已存在
      existing_project = find_existing_project(project_code)
      if existing_project
        safe_log(:info, "[#{project_type_name}同步] 项目已存在: #{existing_project.code}")
        return SyncResult.new(
          success: true,
          action: "skipped",
          project: existing_project,
          error: "项目已存在"
        )
      end

      # 获取项目详细信息
      irs_data = fetch_project_details(project_code)
      return SyncResult.new(success: false, error: irs_data[:error]) unless irs_data[:success]

      # 创建项目记录
      begin
        bid_project = create_bid_project_from_irs_data(irs_data[:data])
        safe_log(:info, "[#{project_type_name}同步] 创建成功: #{bid_project.code}")

        SyncResult.new(
          success: true,
          action: "created",
          project: bid_project,
          irs_data: irs_data[:data]
        )
      rescue StandardError => e
        safe_log(:error, "[#{project_type_name}同步] 创建失败: #{e.message}")
        SyncResult.new(success: false, error: "创建项目失败: #{e.message}")
      end
    end

    # 批量同步项目
    # @param government_projects [Array] 政府项目数据数组
    # @param date [Date] 同步日期
    # @return [Hash] 批量同步结果
    def sync_batch_projects(government_projects, _date = Date.current)
      safe_log(:info, "[#{project_type_name}同步] 开始批量同步，共 #{government_projects.count} 个项目")

      results = {
        total: government_projects.count,
        created: 0,
        updated: 0,
        skipped: 0,
        failed: 0,
        errors: []
      }

      government_projects.each_with_index do |gov_project, index|
        project_code = gov_project["deal_code"]
        next unless project_code

        begin
          result = sync_single_project_by_code(project_code)

          case result.action
          when "created"
            results[:created] += 1
          when "updated"
            results[:updated] += 1
          when "skipped"
            results[:skipped] += 1
          else
            results[:failed] += 1
            results[:errors] << "#{project_code}: #{result.error}"
          end
        rescue StandardError => e
          results[:failed] += 1
          results[:errors] << "#{project_code}: #{e.message}"
          safe_log(:error, "[#{project_type_name}同步] 批量同步异常: #{e.message}")
        end

        # 添加请求间延时，避免频率过高导致签名错误
        # 每处理一个项目后延时1秒，但最后一个项目不需要延时
        if index < government_projects.count - 1
          safe_log(:debug, "[#{project_type_name}同步] 延时1秒避免请求过频...")
          sleep(1)
        end
      end

      safe_log(:info, "[#{project_type_name}同步] 批量同步完成: #{results}")
      results
    end

    protected

    # 安全的日志记录方法，确保中文字符编码正确
    def safe_log(level, message)
      return unless defined?(Rails) && Rails.logger

      # 确保消息是UTF-8编码
      safe_message = message.to_s.force_encoding("UTF-8")

      # 如果编码无效，尝试从GBK转换
      unless safe_message.valid_encoding?
        safe_message = message.to_s.force_encoding("GBK").encode("UTF-8", invalid: :replace, undef: :replace)
      end

      Rails.logger.send(level, safe_message)
    rescue StandardError => e
      # 如果还是失败，记录一个简化的消息
      Rails.logger.send(level, "[编码错误] #{e.message}")
    end

    # 获取项目类型配置
    def project_type_config
      PROJECT_TYPES[@project_type]
    end

    # 获取项目类型名称
    def project_type_name
      project_type_config[:name_suffix]
    end

    # 生成项目编号
    # @param date [Date] 日期
    # @return [String] 项目编号
    def generate_project_code(date = Date.current)
      prefix = project_type_config[:code_prefix]
      date_str = date.strftime("%Y%m%d")

      # 查找当天同类型项目的最大序号
      pattern = "#{prefix}#{date_str}%"
      last_project = Serve::BidProject.where("code LIKE ?", pattern)
        .order(code: :desc)
        .first

      sequence = if last_project && last_project.code.match(/#{prefix}#{date_str}(\d{3})/)
                   ::Regexp.last_match(1).to_i + 1
                 else
                   1
                 end

      "#{prefix}#{date_str}#{sequence.to_s.rjust(3, '0')}"
    end

    # 查找已存在的项目
    # @param project_code [String] 政府项目代码
    # @return [Serve::BidProject, nil] 已存在的项目
    def find_existing_project(project_code)
      payload_key = project_type_config[:payload_key]
      Serve::BidProject.where("payload::jsonb ->> ? = ?", "government_project_code", project_code)
        .where("payload::jsonb ->> ? = ?", payload_key, "true")
        .first
    end

    # 获取项目详细信息（子类需要实现）
    # @param project_code [String] 政府项目代码
    # @return [Hash] 项目详细信息
    def fetch_project_details(project_code)
      raise NotImplementedError, "子类必须实现 fetch_project_details 方法"
    end

    # 从IRS数据创建BidProject（子类需要实现）
    # @param irs_data [Hash] IRS数据
    # @return [Serve::BidProject] 创建的项目
    def create_bid_project_from_irs_data(irs_data)
      raise NotImplementedError, "子类必须实现 create_bid_project_from_irs_data 方法"
    end

    private

    # 验证项目类型
    def validate_project_type!
      return if @project_type && PROJECT_TYPES.key?(@project_type)

      raise ArgumentError, "无效的项目类型: #{@project_type}，支持的类型: #{PROJECT_TYPES.keys.join(', ')}"
    end

  end

end
