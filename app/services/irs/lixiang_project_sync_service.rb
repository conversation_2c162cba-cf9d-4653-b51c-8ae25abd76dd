# frozen_string_literal: true

module Irs

  # 立项项目同步服务
  # 负责将政府项目接口中的立项项目同步为 Serve::BidProject 记录
  class LixiangProjectSyncService

    # 立项项目关键词
    LIXIANG_KEYWORDS = [
      "政府投资项目建议书审批",
      "政府投资项目可行性研究报告审批",
      "政府投资项目初步设计审批"
    ].freeze

    # 同步结果
    class SyncResult

      include ActiveModel::Model
      include ActiveModel::Attributes

      attribute :success, :boolean, default: true
      attribute :total_count, :integer, default: 0
      attribute :created_count, :integer, default: 0
      attribute :skipped_count, :integer, default: 0
      attribute :failed_count, :integer, default: 0
      attribute :errors, default: -> { [] }
      attribute :created_projects, default: -> { [] }
      attribute :sync_date, :string
      attribute :message, :string

      def success?
        success && failed_count == 0
      end

      def failure?
        !success?
      end

    end

    # 从政府项目数据中同步立项项目
    # @param projects [Array] 政府项目列表
    # @param date [String] 数据日期
    # @return [SyncResult] 同步结果
    def self.sync_from_government_projects(projects, date)
      new.sync_from_government_projects(projects, date)
    end

    # 批量同步立项项目（与其他同步服务保持接口一致）
    # @param government_projects [Array] 政府项目数据数组
    # @param date [Date] 同步日期
    # @return [Hash] 批量同步结果
    def self.sync_batch_projects(government_projects, date = Date.current)
      date_str = date.is_a?(Date) ? date.strftime("%Y-%m-%d") : date.to_s

      # 调用现有的同步方法
      sync_result = new.sync_from_government_projects(government_projects, date_str)

      # 转换为统一的返回格式
      {
        total: sync_result.total_count,
        created: sync_result.created_count,
        updated: 0, # 立项项目同步不涉及更新，只有创建和跳过
        skipped: sync_result.skipped_count,
        failed: sync_result.failed_count,
        errors: sync_result.errors
      }
    end

    # 从政府项目数据中同步立项项目
    # @param projects [Array] 政府项目列表
    # @param date [String] 数据日期
    # @return [SyncResult] 同步结果
    def sync_from_government_projects(projects, date)
      Rails.logger.info "[立项项目同步] 开始同步，日期: #{date}，总项目数: #{projects.count}"

      result = SyncResult.new(sync_date: date)

      # 过滤立项项目
      lixiang_projects = filter_lixiang_projects(projects)
      result.total_count = lixiang_projects.count

      Rails.logger.info "[立项项目同步] 识别到 #{lixiang_projects.count} 个立项项目"

      return result if lixiang_projects.empty?

      # 逐个同步项目
      lixiang_projects.each do |project|
        sync_single_project(project, result)
      end

      # 设置最终结果
      result.success = result.failed_count == 0
      result.message = build_result_message(result)

      Rails.logger.info "[立项项目同步] 完成，#{result.message}"
      result
    end

    # 同步单个立项项目（通过项目代码）
    # @param project_code [String] 项目代码
    # @return [Hash] 同步结果
    def self.sync_single_project_by_code(project_code)
      new.sync_single_project_by_code(project_code)
    end

    # 同步单个立项项目（通过项目代码）
    # @param project_code [String] 项目代码
    # @return [Hash] 同步结果
    def sync_single_project_by_code(project_code)
      Rails.logger.info "[立项项目同步] 同步单个项目: #{project_code}"

      # 检查是否已存在
      existing = find_existing_project(project_code)
      if existing
        Rails.logger.info "[立项项目同步] 项目已存在: #{existing.code}"
        return { success: true, action: "skipped", project: existing }
      end

      # 调用 IRS 接口获取详细信息
      irs_service = Irs::RequestService.new
      retry_count = 0
      max_retries = 3

      while retry_count < max_retries
        begin
          Rails.logger.info "[立项项目同步] 调用接口 (尝试 #{retry_count + 1}/#{max_retries})"
          response = irs_service.tzxmbjxxypwgx(requestparam: project_code, type: "P")

          if response && response["code"] == "00" && response["msg"] == "成功"
            parsed_datas = JSON.parse(response["datas"])
            return { success: false, error: "接口返回空数据" } unless parsed_datas.any?

            irs_data = parsed_datas.first
            bid_project = create_bid_project_from_irs_data(irs_data)

            Rails.logger.info "[立项项目同步] 创建成功: #{bid_project.code}"
            return { success: true, action: "created", project: bid_project, irs_data: irs_data }

          else
            error_msg = response ? (response["msg"] || "未知错误") : "接口无响应"
            retry_count += 1

            if retry_count < max_retries
              Rails.logger.warn "[立项项目同步] 接口错误，等待重试: #{error_msg}"
              sleep(3)
            end
          end
        rescue StandardError => e
          Rails.logger.error "[立项项目同步] 接口异常: #{e.message}"
          retry_count += 1
          sleep(3) if retry_count < max_retries
        end
      end

      error_msg = "达到最大重试次数"
      Rails.logger.error "[立项项目同步] 最终失败: #{error_msg}"
      { success: false, error: error_msg }
    end

    private

    # 过滤立项项目
    # @param projects [Array] 政府项目列表
    # @return [Array] 立项项目列表
    def filter_lixiang_projects(projects)
      projects.select do |project|
        item_name = project["item_name"]
        LIXIANG_KEYWORDS.any? { |keyword| item_name&.include?(keyword) }
      end
    end

    # 同步单个项目
    # @param project [Hash] 政府项目数据
    # @param result [SyncResult] 同步结果对象
    def sync_single_project(project, result)
      project_code = project["deal_code"]

      begin
        # 检查是否已存在
        existing = find_existing_project(project_code)
        if existing
          Rails.logger.debug { "[立项项目同步] 跳过已存在项目: #{project_code}" }
          result.skipped_count += 1
          return
        end

        # 创建 BidProject 记录
        bid_project = create_bid_project_from_government_data(project)
        result.created_count += 1
        result.created_projects << bid_project

        Rails.logger.info "[立项项目同步] 创建项目: #{bid_project.code} - #{bid_project.name}"
      rescue StandardError => e
        Rails.logger.error "[立项项目同步] 创建项目失败: #{project_code}, 错误: #{e.message}"
        result.failed_count += 1
        result.errors << "#{project_code}: #{e.message}"
      end
    end

    # 查找已存在的项目
    # @param project_code [String] 项目代码
    # @return [Serve::BidProject, nil] 已存在的项目
    def find_existing_project(project_code)
      Serve::BidProject.where("payload::jsonb ->> ? = ?", "government_project_code", project_code).first
    end

    # 从政府项目数据创建 BidProject
    # @param project [Hash] 政府项目数据
    # @return [Serve::BidProject] 创建的项目
    def create_bid_project_from_government_data(project)
      create_bid_project_from_irs_data(project)
    end

    # 从 IRS 数据创建 BidProject
    # @param irs_data [Hash] IRS 接口数据
    # @return [Serve::BidProject] 创建的项目
    def create_bid_project_from_irs_data(irs_data)
      Serve::BidProject.create!(
        app: App.first, # 使用默认 App
        code: generate_lixiang_code,
        name: irs_data["approval_title"],
        state: "setuped",
        setup_at: Time.parse(irs_data["deal_time"]),
        payload: build_payload(irs_data)
      )
    end

    # 生成立项项目编号
    # @return [String] 项目编号
    def generate_lixiang_code
      date_str = Date.current.strftime("%Y%m%d")
      prefix = "LX#{date_str}"

      existing_codes = Serve::BidProject.where("code LIKE ?", "#{prefix}%").pluck(:code)

      if existing_codes.any?
        max_seq = existing_codes.map { |code| code.gsub(prefix, "").to_i }.max
        next_seq = max_seq + 1
      else
        next_seq = 1
      end

      "#{prefix}#{next_seq.to_s.rjust(3, '0')}"
    end

    # 构建 payload 数据
    # @param irs_data [Hash] IRS 数据
    # @return [Hash] payload 数据
    def build_payload(irs_data)
      {
        # 标记为立项项目
        lixiang_project: true,
        sync_source: "government_project_integration",
        sync_method: "auto_sync",

        # 政府项目信息
        government_project_code: irs_data["project_code"],
        government_setup_deal_time: irs_data["deal_time"],
        item_name: irs_data["item_name"],
        dept_name: irs_data["dept_name"],
        current_state: irs_data["current_state"],

        # 审批信息
        approval_number: irs_data["approval_number"],
        approval_itemid: irs_data["approval_itemid"],
        file_path: irs_data["file_path"],

        # 时间信息
        accept_time: irs_data["accept_time"],
        deal_username: irs_data["deal_username"],

        # 存储完整的 IRS 数据
        irs_data: irs_data,
        synced_at: Time.current
      }
    end

    # 构建结果消息
    # @param result [SyncResult] 同步结果
    # @return [String] 结果消息
    def build_result_message(result)
      "总计 #{result.total_count} 个，创建 #{result.created_count} 个，跳过 #{result.skipped_count} 个，失败 #{result.failed_count} 个"
    end

  end

end
