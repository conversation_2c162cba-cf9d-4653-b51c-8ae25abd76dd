class Irs::RequestService

  AppKey = "A330108247711202203000004".freeze
  AppSecret = "4991d888e0ae4dd5b808c9a17d264b73".freeze
  BaseUrl = "https://interface.zjzwfw.gov.cn/gateway".freeze
  RequestSecretKey = "irs_requestSecret".freeze
  RefreshSecretKey = "irs_refreshSecret".freeze
  TokenExpireKey = "irs_token_expire_at".freeze
  TOKEN_EXPIRE_SECONDS = 14 * 60 # 15分钟过期,提早一分钟重新获取

  def http_post(url, body = {})
    timestamp
    log_request(url, body)
    response = Typhoeus.post(
      url,
      headers: default_headers,
      body: body
    )
    log_response(response)
    response
  end

  def log_request(url, body)
    return unless defined?(Rails)

    Rails.logger.info "[IrsAPI] Request: #{url}"
    Rails.logger.info "[IrsAPI] Headers: #{default_headers}"
    Rails.logger.info "[IrsAPI] Body: #{body}"
  end

  def log_response(response)
    return unless defined?(Rails)

    Rails.logger.info "[IrsAPI] Response status: #{response.code}"
    Rails.logger.info "[IrsAPI] Response body: #{response.body}"
  end

  # 公共请求头
  def default_headers
    {
      "X-SECURITY-CHECK-TYPE" => "ZZD",
      "X-SECURITY-USER-ID" => ENV.fetch("IRS_USER_ID", nil),
      "X-SECURITY-USER-IP" => ENV.fetch("IRS_USER_IP", nil)
    }
  end

  def requestSecret
    # 检查token是否存在且未过期
    if token_expired?
      Rails.logger.info "[IrsAPI] Token expired, refreshing..." if defined?(Rails)
      refresh_access_token
      secret = $access_token_redis.get(RequestSecretKey)
      Rails.logger.info "[IrsAPI] Refreshed token: #{secret&.first(10)}..." if defined?(Rails)
      secret
    else
      secret = $access_token_redis.get(RequestSecretKey)
      Rails.logger.info "[IrsAPI] Using cached token: #{secret&.first(10)}..." if defined?(Rails)
      secret
    end
  end

  def refreshSecret
    $access_token_redis.get(RefreshSecretKey) ||
      refresh_access_token
  end

  # 获取access_token
  def refresh_access_token
    # 使用 Redis 分布式锁，设置 5 秒过期时间
    lock_key = "irs:token_refresh_lock"

    # 如果获取到锁，执行 token 刷新
    if $access_token_redis.set(lock_key, "1", nx: true, ex: 5)
      begin
        Rails.logger.info "[IrsAPI] Acquired lock for token refresh" if defined?(Rails)

        url = "#{BaseUrl}/app/refreshTokenByKey.htm"
        ts = timestamp
        sign_str = [AppKey, AppSecret, ts].join
        signature = Digest::MD5.hexdigest(sign_str)
        body = {
          appKey: AppKey,
          sign: signature,
          requestTime: ts
        }

        response = http_post(url, body)
        raise "有错误：#{response.body}" unless response

        body = JSON.parse response.body
        refreshSecret = body.dig("datas", "refreshSecret")
        requestSecret = body.dig("datas", "requestSecret")

        if requestSecret.present? && refreshSecret.present?
          # 使用 multi 确保原子性
          $access_token_redis.multi do |multi|
            multi.setex(RequestSecretKey, TOKEN_EXPIRE_SECONDS, requestSecret)
            multi.setex(RefreshSecretKey, TOKEN_EXPIRE_SECONDS, refreshSecret)
            expire_at = Time.now.to_i + TOKEN_EXPIRE_SECONDS
            multi.setex(TokenExpireKey, TOKEN_EXPIRE_SECONDS, expire_at)

            if defined?(Rails)
              Rails.logger.info "[IrsAPI] Setting tokens with TTL: #{TOKEN_EXPIRE_SECONDS}s"
              Rails.logger.info "[IrsAPI] - RequestSecret: #{requestSecret.first(10)}..."
              Rails.logger.info "[IrsAPI] - RefreshSecret: #{refreshSecret.first(10)}..."
              Rails.logger.info "[IrsAPI] - Token will expire at: #{Time.at(expire_at)}"
            end
          end
          Rails.logger.info "[IrsAPI] Successfully refreshed tokens" if defined?(Rails)
        elsif defined?(Rails)
          if defined?(Rails)
            Rails.logger.error "[IrsAPI] Failed to refresh tokens: requestSecret or refreshSecret is empty"
          end
        end
        body
      ensure
        # 释放锁
        $access_token_redis.del(lock_key)
        Rails.logger.info "[IrsAPI] Released lock for token refresh" if defined?(Rails)
      end
    else
      # 等待一小段时间后重试
      sleep(0.5)
      # 递归调用，但限制最大重试次数
      @retry_count ||= 0
      if @retry_count < 3
        @retry_count += 1
        Rails.logger.info "[IrsAPI] Waiting for token refresh, retry #{@retry_count}/3" if defined?(Rails)
        refresh_access_token
      else
        @retry_count = 0
        raise "获取 token 超时，请稍后重试"
      end
    end
  end

  # 检查token是否过期
  def token_expired?
    expire_time = $access_token_redis.get(TokenExpireKey)
    # 如果没有设置过期时间或当前时间已超过过期时间，则视为已过期
    expire_time.nil? || Time.now.to_i >= expire_time.to_i
  end

  # 强制刷新token（清除缓存后重新获取）
  def force_refresh_token
    Rails.logger.info "[IrsAPI] 强制刷新token..." if defined?(Rails)

    # 清除现有token缓存
    $access_token_redis.del(RequestSecretKey)
    $access_token_redis.del(RefreshSecretKey)
    $access_token_redis.del(TokenExpireKey)

    # 重新获取token
    refresh_access_token
  end

  # 通用的带重试机制的API调用方法
  def call_api_with_retry(url, body, method_name = "API")
    max_retries = 2
    retry_count = 0

    while retry_count <= max_retries
      response = http_post(url, body)
      result = JSON.parse(response.body)

      # 检查是否是签名错误或token相关错误
      if result["code"] == "14" || result["msg"]&.include?("签名错误") || result["msg"]&.include?("token")
        if retry_count < max_retries
          if defined?(Rails)
            Rails.logger.warn "[IrsAPI] #{method_name}签名错误，尝试刷新token并重试 (#{retry_count + 1}/#{max_retries + 1})"
          end

          # 强制刷新token
          force_refresh_token

          # 重新生成签名
          ts = timestamp
          body[:sign] = sign(sign_str(ts))
          body[:requestTime] = ts

          retry_count += 1
          sleep(1) # 等待1秒后重试
          next
        elsif defined?(Rails)
          Rails.logger.error "[IrsAPI] #{method_name}重试#{max_retries}次后仍然失败: #{result['msg']}"
        end
      end

      return result
    end
  end

  def timestamp
    (Time.now.to_f * 1000).to_i.to_s
  end

  # 签名
  def sign(sign_str)
    Digest::MD5.hexdigest(sign_str)
  end

  # 获取签名字符串
  def sign_str(ts = nil)
    secret = requestSecret
    ts ||= timestamp
    str = [AppKey, secret, ts].join
    if defined?(Rails)
      Rails.logger.info "[IrsAPI] Sign string components:"
      Rails.logger.info "[IrsAPI] - AppKey: #{AppKey}"
      Rails.logger.info "[IrsAPI] - requestSecret: #{secret}"
      Rails.logger.info "[IrsAPI] - timestamp: #{ts}"
      Rails.logger.info "[IrsAPI] Final sign string: #{str}"
    end
    str
  end

  def additional
    {
      powerMatters: "许可-0000-00",
      subPowerMatters: "许可-0000-0101",
      accesscardId: "33071918111111784523"
    }
  end

  # 社会保险（包含机关保险）个人参保信息（无医保）
  # 社会保障号码	aac002	社会保障号码
  # 姓名	aac003	姓名
  def shbx(aac003:, aac002:)
    url = "#{BaseUrl}/api/001003001029/certificate/Vh72K53z54GVU704.htm"
    ts = timestamp
    body = {
      additional: additional,
      appKey: AppKey,
      sign: sign(sign_str(ts)),
      requestTime: ts,
      aac002: aac002,
      aac003: aac003
    }
    call_api_with_retry(url, body, "shbx")
  end

  # 二级建造师
  def ejjzs(name:, identity_code:)
    url = "#{BaseUrl}/api/001003001029/certificate/Vh72K53z54GVU704.htm"
    ts = timestamp
    body = {
      additional: additional,
      appKey: AppKey,
      sign: sign(sign_str(ts)),
      requestTime: ts,
      sfzh: identity_code,
      xm: name
    }
    call_api_with_retry(url, body, "ejjzs")
  end

  # 二级造价工程师
  def ejzjgcs(name:, identity_code:)
    url = "#{BaseUrl}/api/proxy/001003001029/certificate/54a0flsn83Owbw53.htm"
    ts = timestamp
    body = {
      additional: additional,
      appKey: AppKey,
      sign: sign(sign_str(ts)),
      requestTime: ts,
      sfzjhm: identity_code,
      xm: name
    }
    call_api_with_retry(url, body, "ejzjgcs")
  end

  # 建筑资质,传入统一社会信用代码
  def jzzz(uscc:)
    url = "#{BaseUrl}/api/proxy/001003001029/certificate/54a0flsn83Owbw53.htm"

    ts = timestamp

    body = {
      additional: additional,
      appKey: AppKey,
      sign: sign(sign_str(ts)),
      requestTime: ts,
      uscc: uscc
    }
    call_api_with_retry(url, body, "jzzz")
  end

  # 企业基本信息查询,
  # 附加信息	additional	在办理行政权力和公共服务事项而需调用数据共享接口时，必须增加一个访问的入参：additional（附加参数），
  # 其中包含powerMatters（主权力事项编码）、subPowerMatters（子权力事项编码）、accesscardId（访问者身份信息，可以是访问者姓名、身份证号或对接接口系统的访问用户名，三选一）、
  # "materialName":"材料/表单名称"、"sponsorName":"申请人姓名/法人名称"、"sponsorCode":"申请人/法人证件编码"、 "projectId":"办件流水号"七部分内容；
  # 具体参数传递的方式为：additional={"powerMatters":"许可-0000-00","subPowerMatters":"许可-0000-0101","accesscardId":"33071918111111784523",
  # "materialName":"社会团体变更登记申请表","sponsorName":"阿里巴巴（中国）有限公司","sponsorCode":"91330100799655058B","projectId":"330000261711151100004"}
  # uniscId 统一社会信用代码(企业名称\注册号任传其一)
  # 企业名称	companyName	企业名称(与社会统一信用代码\注册号任传其一)
  # 企业类型	entType	企业类型 E企业，P个体，必传
  # 注册号	registerNo	注册号(与社会统一信用代码\企业名称任传其一)
  def qyjbxx(uniscId:, companyName: nil, entType: "E", registerNo: nil, materialName: "社会团体变更登记申请表", sponsorName: "阿里巴巴（中国）有限公司",
             sponsorCode: "91330100799655058B", projectId: "330000261711151100004")
    url = "#{BaseUrl}/api/proxy/001003001029/certificate/54a0flsn83Owbw53.htm"
    merge_additional = additional.merge({
      materialName: materialName,
      sponsorName: sponsorName,
      sponsorCode: sponsorCode,
      projectId: projectId
    })
    ts = timestamp
    body = {
      additional: merge_additional,
      appKey: AppKey,
      sign: sign(sign_str(ts)),
      requestTime: ts,
      uniscId: uniscId,
      companyName: companyName,
      entType: entType,
      registerNo: registerNo
    }
    call_api_with_retry(url, body, "qyjbxx")
  end

  # 企业主要人员名单
  # 条件表达式	condition	条件表达式拼接字符串，拼接规范请参考文档
  # 分页页码	pageNum	分页页码
  # 分页大小	pageSize	分页大小
  def qyzyry(condition:, pageNum: 1, pageSize: 15)
    url = "#{BaseUrl}/api/proxy/*********/dataSharing/1x5K3793He1a7V3f.htm"

    ts = timestamp

    body = {
      additional: additional,
      appKey: AppKey,
      sign: sign(sign_str(ts)),
      requestTime: ts,
      condition: condition,
      pageNum: pageNum,
      pageSize: pageSize
    }
    call_api_with_retry(url, body, "qyzyry")
  end

  # 企业主要人员名单（已脱敏）
  # 条件表达式	condition	条件表达式拼接字符串，拼接规范请参考文档
  # 分页页码	pageNum	分页页码
  # 分页大小	pageSize	分页大小
  def listOfKeyPersonnelOfTheEnterprise(condition:, pageNum: 1, pageSize: 10)
    url = "#{BaseUrl}/api/proxy/*********/dataSharing/997ves1k28GYa4kb.htm"
    ts = timestamp

    body = {
      additional: additional,
      appKey: AppKey,
      sign: sign(sign_str(ts)),
      requestTime: ts,
      condition: condition,
      pageNum: pageNum,
      pageSize: pageSize
    }
    call_api_with_retry(url, body, "listOfKeyPersonnelOfTheEnterprise")
  end

  # 企业任职信息查询
  # cerno	中华人民共和国居民身份证
  def qyrzxx(cerno:)
    url = "#{BaseUrl}/api/*********/dataSharing/Q6ZY5zIrLLed4839.htm"

    ts = timestamp

    body = {
      additional: additional,
      appKey: AppKey,
      sign: sign(sign_str(ts)),
      requestTime: ts,
      cerno: cerno
    }
    call_api_with_retry(url, body, "qyrzxx")
  end

  # 省建设厅勘察设计工程师注册证书（含版式文件）
  # 身份证	sfz	身份证
  # 姓名	xm	姓名
  # 证书编号	zsbh	证书编号
  def sjstkcsjgcszczs(sfz:, xm:, zsbh:)
    url = "#{BaseUrl}/api/proxy/*********/dataSharing/997ves1k28GYa4kb.htm"
    ts = timestamp

    body = {
      additional: additional,
      appKey: AppKey,
      sign: sign(sign_str(ts)),
      requestTime: ts,
      sfz: sfz,
      xm: xm,
      zsbh: zsbh
    }
    call_api_with_retry(url, body, "sjstkcsjgcszczs")
  end

  # 二级注册建筑师和注册结构师信息查询
  # cardId	证件号码
  # 在办理行政权力和公共服务事项而需调用数据共享接口时，必须增加一个访问的入参：additional（附加参数），
  # 其中包含powerMatters（主权力事项编码）、subPowerMatters（子权力事项编码）、accesscardId（访问者身份信息，可以是访问者姓名、身份证号或对接接口系统的访问用户名，三选一）、
  # "materialName":"材料/表单名称"、"sponsorName":"申请人姓名/法人名称"、"sponsorCode":"申请人/法人证件编码"、 "projectId":"办件流水号"七部分内容；
  def ejzcjzsAndJgs(cardId:, materialName: "社会团体变更登记申请表", sponsorName: "阿里巴巴（中国）有限公司",
                    sponsorCode: "91330100799655058B", projectId: "330000261711151100004")
    url = "#{BaseUrl}/api/001003016/dataSharing/twoArchitectRegisterInfo.htm"
    merge_additional = additional.merge({
      materialName: materialName,
      sponsorName: sponsorName,
      sponsorCode: sponsorCode,
      projectId: projectId
    })
    ts = timestamp

    body = {
      additional: merge_additional,
      appKey: AppKey,
      sign: sign(sign_str(ts)),
      requestTime: ts,
      cardId: cardId
    }
    call_api_with_retry(url, body, "ejzcjzsAndJgs")
  end

  # 法人登记基本信息
  # 统一社会信用代码、登记注册号、组织机构代码、法人单位名称任传其一
  # 登记注册号	djzch
  # 法人单位名称	frdwmc
  # 统一社会信用代码	tyxydm
  # 组织机构代码	zzjgdm
  def frdjjbxx(tyxydm: nil, djzch: nil, frdwmc: nil, zzjgdm: nil)
    url = "#{BaseUrl}/api/001003085/dataSharing/5bNfNd16923H64J4.htm"
    ts = timestamp

    body = {
      additional: additional,
      appKey: AppKey,
      sign: sign(sign_str(ts)),
      requestTime: ts,
      tyxydm: tyxydm,
      djzch: djzch,
      frdwmc: frdwmc,
      zzjgdm: zzjgdm
    }
    call_api_with_retry(url, body, "frdjjbxx")
  end

  # 省建设厅监理工程师注册证书
  # 身份证号	idcard	身份证号
  # 姓名	personname	姓名
  def ztbwjxxcx(idcard:, personname:)
    url = "#{BaseUrl}/api/001003016/dataSharing/8DeB643v8hbvfF05.htm"
    ts = timestamp

    body = {
      additional: additional,
      appKey: AppKey,
      sign: sign(sign_str(ts)),
      requestTime: ts,
      idcard: idcard,
      personname: personname
    }
    call_api_with_retry(url, body, "ztbwjxxcx")
  end

  # 投资项目基本信息共享接口
  # 请求参数类型	type	P：项目代码，U：统一信用代码，N：企业
  # 请求参数	requestparam	根据请求参数类型输入对应参数内容
  def tzxmjbxx(requestparam:, type: "P")
    url = "#{BaseUrl}/api/*********/dataSharing/603Y3h4ZcD81TEH8.htm"
    ts = timestamp
    body = {
      additional: additional,
      appKey: AppKey,
      sign: sign(sign_str(ts)),
      requestTime: ts,
      type: type,
      requestparam: requestparam
    }

    call_api_with_retry(url, body, "tzxmjbxx")
  end

  # 浙江省企业投资项目信息查询（按单位名称查询）
  # 项目(法人)单位	ENTERPRISE_NAME	项目(法人)单位
  def zjsqytzxmxx(name:)
    url = "#{BaseUrl}/api/*********/dataSharing/gnjFb055S26429s6.htm"
    ts = timestamp

    body = {
      additional: additional,
      appKey: AppKey,
      sign: sign(sign_str(ts)),
      requestTime: ts,
      ENTERPRISE_NAME: name
    }
    call_api_with_retry(url, body, "zjsqytzxmxx")
  end

  # 企业基本信息查询
  # 企业名称	companyName	企业名称(与社会统一信用代码\注册号任传其一)
  # 企业类型	entType	企业类型 E企业，P个体，必传
  # 注册号	registerNo	注册号(与社会统一信用代码\企业名称任传其一)
  # 统一社会信用代码	uniscId	统一社会信用代码(企业名称\注册号任传其一)
  def enterpriseInfo(entType: "E", companyName: nil, uniscId: nil, registerNo: nil)
    url = "#{BaseUrl}/api/enterpriseInfo.htm"
    ts = timestamp
    body = {
      additional: additional.merge({ materialName: "社会团体变更登记申请表", sponsorName: "阿里巴巴（中国）有限公司",
                                     sponsorCode: "91330100799655058B", projectId: "330000261711151100004" }),
      appKey: AppKey,
      sign: sign(sign_str(ts)),
      requestTime: ts,
      companyName: companyName,
      entType: entType,
      registerNo: registerNo,
      uniscId: uniscId
    }
    call_api_with_retry(url, body, "enterpriseInfo")
  end

  # 股东（或发起人或投资人）信息（试用）
  # 统一社会信用代码	uscc
  def stockholderInfo(uscc)
    url = "#{BaseUrl}/api/proxy/001003001029/dataSharing/O15Afl22f8dgNqtc.htm"
    ts = timestamp

    body = {
      additional: additional,
      appKey: AppKey,
      sign: sign(sign_str(ts)),
      requestTime: ts,
      uscc: uscc
    }
    call_api_with_retry(url, body, "stockholderInfo")
  end

  # 市场主体信息查询接口（按市场主体名称和统一社会信用代码和市场主
  # 市场主体类型	type	市场主体类型，取值包括：1：企业 2：个体工商户 3：农民专业合作社
  # 市场主体名称	entname
  # 统一社会信用代码	uniscid
  def marketSubjectInformation(entname:, uniscid:, type: "1")
    url = "#{BaseUrl}/api/010010010010016/dataSharing/h57fif3S5dJ2Tj12.htm"
    ts = timestamp

    body = {
      additional: additional,
      appKey: AppKey,
      sign: sign(sign_str(ts)),
      requestTime: ts,
      entname: entname,
      type: type,
      uniscid: uniscid
    }
    call_api_with_retry(url, body, "marketSubjectInformation")
  end

  # 数据名称	在线平台-投资项目办件信息与批文共享接口
  # 数源单位	浙江省/省发展改革委
  # 数据资源编码	S330000API0220210914004528
  # 接口描述	获取投资在线平台3.0项目名下办件与批文
  #  输入查询参数：type和requestparam
  #  参数说明：
  # （1）查询类型type=P，requestparam输入项目代码，通过项目代码查询项目所有办件信息及批文。
  # （2）查询类型type=S，requestparam输入事项申报号，通过事项申报号查询具体事项信息及批文。

  def tzxmbjxxypwgx(requestparam:, type: "P")
    url = "#{BaseUrl}/api/*********/dataSharing/462FVD4GpaMJB289.htm"
    ts = timestamp
    body = {
      additional: additional,
      appKey: AppKey,
      sign: sign(sign_str(ts)),
      requestTime: ts,
      requestparam: requestparam,
      type: type
    }

    call_api_with_retry(url, body, "tzxmbjxxypwgx")
  end

  # 案件进度查询
  # code 行政相对人证件号
  def queryCaseProgress(code:)
    url = "#{BaseUrl}/api/proxy/001003001029/dataSharing/6Wwafdxr8ZF5Fdk1.htm"
    ts = timestamp

    body = {
      additional: additional,
      appKey: AppKey,
      sign: sign(sign_str(ts)),
      requestTime: ts,
      code: code
    }
    call_api_with_retry(url, body, "queryCaseProgress")
  end

end
