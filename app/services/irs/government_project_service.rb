# frozen_string_literal: true

module Irs

  # 政府项目信息接口服务
  # 用于获取政府投资项目的审批信息
  class GovernmentProjectService

    include ActiveModel::Model
    include ActiveModel::Attributes

    # 接口配置
    BASE_URL = ENV.fetch("GOVERNMENT_PROJECT_API_URL",
                         "http://59.202.39.226:18181/rest/DataSharingRestService/getGovernmentProjectInfoByDate")
    SECURITY_KEY = ENV.fetch("GOVERNMENT_PROJECT_SECURITY_KEY",
                             "f823792357a911f088eefa163e999ea2")

    # 请求超时配置（政府接口处理时间较长，设置为2分钟）
    REQUEST_TIMEOUT = ENV.fetch("GOVERNMENT_PROJECT_TIMEOUT", "120").to_i

    # 响应结果结构
    class Result

      include ActiveModel::Model
      include ActiveModel::Attributes

      attribute :success, :boolean, default: false
      attribute :message, :string
      attribute :data, default: -> { {} }
      attribute :projects, default: -> { [] }
      attribute :count, :integer, default: 0
      attribute :error_code, :string
      attribute :raw_response, :string

      def success?
        success
      end

      def failure?
        !success
      end

    end

    # 获取指定日期的政府项目信息（带缓存）
    # @param date [String] 日期，格式：YYYY-MM-DD，默认为昨天
    # @param force_refresh [Boolean] 是否强制刷新缓存
    # @return [Result] 查询结果
    def self.fetch_projects_by_date(date = nil, force_refresh: false)
      new.fetch_projects_by_date(date, force_refresh: force_refresh)
    end

    # 获取指定日期的政府项目信息（带缓存）
    # @param date [String] 日期，格式：YYYY-MM-DD，默认为昨天
    # @param force_refresh [Boolean] 是否强制刷新缓存
    # @return [Result] 查询结果
    def fetch_projects_by_date(date = nil, force_refresh: false)
      # 默认查询昨天的数据
      date ||= Date.yesterday.strftime("%Y-%m-%d")

      Rails.logger.info "开始获取政府项目信息，日期: #{date}，强制刷新: #{force_refresh}"

      # 参数验证和格式标准化
      unless valid_date_format?(date)
        return build_error_result(
          message: "日期格式错误，请使用YYYY-MM-DD格式",
          error_code: "INVALID_DATE_FORMAT"
        )
      end

      # 标准化日期格式为 YYYY-MM-DD
      date = Date.parse(date).strftime("%Y-%m-%d")

      # 检查缓存（除非强制刷新）
      if !force_refresh && Irs::GovernmentProject.cache_valid?(date)
        Rails.logger.info "使用缓存数据，日期: #{date}"
        return build_cached_result(date)
      end

      Rails.logger.info "缓存无效或强制刷新，调用接口获取数据，日期: #{date}"

      begin
        # 发送HTTP请求
        response = send_request(date)

        # 处理响应
        result = process_response(response)

        # 缓存结果
        cache_result(date, result)

        # 同步各类政府项目（如果获取成功）
        sync_government_projects(result, date) if result.success?

        result
      rescue Timeout::Error, Net::ReadTimeout, Net::OpenTimeout => e
        Rails.logger.error "政府项目接口请求超时: #{e.message}"
        # 尝试返回缓存数据作为降级策略
        fallback_result = try_fallback_cache(date)
        return fallback_result if fallback_result

        build_error_result(
          message: "接口请求超时，请稍后重试",
          error_code: "REQUEST_TIMEOUT"
        )
      rescue Net::HTTPError, SocketError => e
        Rails.logger.error "政府项目接口网络错误: #{e.message}"
        # 尝试返回缓存数据作为降级策略
        fallback_result = try_fallback_cache(date)
        return fallback_result if fallback_result

        build_error_result(
          message: "网络连接错误，请检查网络状态",
          error_code: "NETWORK_ERROR"
        )
      rescue JSON::ParserError => e
        Rails.logger.error "政府项目接口JSON解析错误: #{e.message}"
        build_error_result(
          message: "接口返回数据格式错误",
          error_code: "JSON_PARSE_ERROR"
        )
      rescue StandardError => e
        Rails.logger.error "政府项目接口未知错误: #{e.message}"
        Rails.logger.error e.backtrace.join("\n")
        build_error_result(
          message: "系统错误，请联系管理员",
          error_code: "UNKNOWN_ERROR"
        )
      end
    end

    # 获取指定日期范围的政府项目信息
    # @param start_date [String] 开始日期，格式：YYYY-MM-DD
    # @param end_date [String] 结束日期，格式：YYYY-MM-DD
    # @return [Array<Result>] 查询结果数组
    def self.fetch_projects_by_date_range(start_date, end_date)
      new.fetch_projects_by_date_range(start_date, end_date)
    end

    # 获取指定日期范围的政府项目信息
    # @param start_date [String] 开始日期，格式：YYYY-MM-DD
    # @param end_date [String] 结束日期，格式：YYYY-MM-DD
    # @return [Array<Result>] 查询结果数组
    def fetch_projects_by_date_range(start_date, end_date)
      results = []
      current_date = Date.parse(start_date)
      end_date_obj = Date.parse(end_date)

      while current_date <= end_date_obj
        result = fetch_projects_by_date(current_date.strftime("%Y-%m-%d"))
        results << result
        current_date += 1.day
      end

      results
    end

    private

    # 发送HTTP请求
    # @param date [String] 日期
    # @return [Net::HTTPResponse] HTTP响应
    def send_request(date)
      uri = URI(BASE_URL)
      params = {
        "security_key" => SECURITY_KEY,
        "date" => date
      }

      Rails.logger.debug { "发送请求到: #{uri}" }
      Rails.logger.debug { "请求参数: #{params.except('security_key').merge('security_key' => '***')}" }

      # 设置超时
      Net::HTTP.start(uri.host, uri.port,
                      open_timeout: REQUEST_TIMEOUT,
                      read_timeout: REQUEST_TIMEOUT) do |http|
        request = Net::HTTP::Post.new(uri)
        request.set_form_data(params)
        request["User-Agent"] = "Hz-Iest-API/1.0"
        request["Accept"] = "application/json"

        http.request(request)
      end
    end

    # 处理HTTP响应
    # @param response [Net::HTTPResponse] HTTP响应
    # @return [Result] 处理结果
    def process_response(response)
      Rails.logger.debug { "响应状态码: #{response.code}" }
      Rails.logger.debug { "响应Content-Type: #{response['content-type']}" }

      unless response.is_a?(Net::HTTPSuccess)
        return build_error_result(
          message: "接口返回错误状态: #{response.code} #{response.message}",
          error_code: "HTTP_ERROR_#{response.code}",
          raw_response: response.body
        )
      end

      # 处理编码问题
      body = response.body.force_encoding("UTF-8")
      Rails.logger.debug { "响应体长度: #{body.length} 字符" }

      # 解析JSON
      json_data = JSON.parse(body)

      # 检查业务状态
      if json_data["code"] != "1"
        return build_error_result(
          message: json_data["message"] || "接口返回业务错误",
          error_code: "BUSINESS_ERROR_#{json_data['code']}",
          data: json_data,
          raw_response: body
        )
      end

      # 提取项目列表
      project_list = json_data["governmentProjectList"] || []

      Rails.logger.info "成功获取政府项目信息，共 #{project_list.length} 个项目"

      # 构建成功结果
      Result.new(
        success: true,
        message: json_data["message"] || "获取成功",
        data: json_data,
        projects: project_list,
        count: project_list.length,
        raw_response: body
      )
    end

    # 构建错误结果
    # @param message [String] 错误消息
    # @param error_code [String] 错误代码
    # @param data [Hash] 额外数据
    # @param raw_response [String] 原始响应
    # @return [Result] 错误结果
    def build_error_result(message:, error_code:, data: {}, raw_response: nil)
      Result.new(
        success: false,
        message: message,
        error_code: error_code,
        data: data,
        raw_response: raw_response
      )
    end

    # 验证日期格式
    # @param date [String] 日期字符串
    # @return [Boolean] 是否有效
    def valid_date_format?(date)
      return false unless date.is_a?(String)
      # 支持更宽松的日期格式：YYYY-M-D 或 YYYY-MM-DD
      return false unless date.match?(/^\d{4}-\d{1,2}-\d{1,2}$/)

      Date.parse(date)
      true
    rescue Date::Error
      false
    end

    # 构建缓存结果
    # @param date [String] 日期
    # @return [Result] 缓存结果
    def build_cached_result(date)
      cached_record = Irs::GovernmentProject.find_by_date(date)
      unless cached_record
        return build_error_result(
          message: "缓存数据不存在",
          error_code: "CACHE_NOT_FOUND"
        )
      end

      unless cached_record.success?
        return build_error_result(
          message: cached_record.error_message || "缓存的数据获取失败",
          error_code: "CACHED_ERROR",
          data: cached_record.raw_data || {}
        )
      end

      projects = cached_record.projects_list
      Rails.logger.info "从缓存获取政府项目信息，共 #{projects.length} 个项目"

      Result.new(
        success: true,
        message: "从缓存获取成功",
        data: cached_record.raw_data || {},
        projects: projects,
        count: projects.length,
        raw_response: "[CACHED_DATA]"
      )
    end

    # 缓存结果
    # @param date [String] 日期
    # @param result [Result] 查询结果
    def cache_result(date, result)
      Irs::GovernmentProject.cache_result(date, result)
    rescue StandardError => e
      Rails.logger.error "缓存政府项目数据失败: #{e.message}"
      # 缓存失败不影响主流程
    end

    # 尝试降级到缓存数据
    # @param date [String] 日期
    # @return [Result, nil] 降级结果
    def try_fallback_cache(date)
      cached_record = Irs::GovernmentProject.find_by_date(date)
      return nil unless cached_record&.success?

      Rails.logger.warn "接口调用失败，使用缓存数据作为降级策略，日期: #{date}"

      projects = cached_record.projects_list
      Result.new(
        success: true,
        message: "接口异常，使用缓存数据",
        data: cached_record.raw_data || {},
        projects: projects,
        count: projects.length,
        raw_response: "[FALLBACK_CACHED_DATA]"
      )
    end

    # 同步各类政府项目
    # @param result [Result] 政府项目查询结果
    # @param date [String] 查询日期
    def sync_government_projects(result, date)
      return unless result.success? && result.projects.any?

      Rails.logger.info "[政府项目服务] 开始同步各类政府项目，日期: #{date}，总数: #{result.projects.count}"

      # 按项目类型分类
      project_types = classify_projects(result.projects)

      # 同步立项项目
      sync_projects_by_type(:lixiang, project_types[:lixiang], date) if project_types[:lixiang].any?

      # 同步施工许可证项目
      sync_projects_by_type(:shigong, project_types[:shigong], date) if project_types[:shigong].any?

      # 同步项目变更
      sync_projects_by_type(:change, project_types[:change], date) if project_types[:change].any?
    end

    # 同步立项项目（保持向后兼容）
    # @param result [Result] 政府项目查询结果
    # @param date [String] 查询日期
    def sync_lixiang_projects(result, date)
      return unless result.success? && result.projects.any?

      Rails.logger.info "[政府项目服务] 开始同步立项项目，日期: #{date}"

      begin
        sync_result = Irs::LixiangProjectSyncService.sync_from_government_projects(result.projects, date)

        if sync_result.success?
          Rails.logger.info "[政府项目服务] 立项项目同步成功: #{sync_result.message}"
        else
          Rails.logger.warn "[政府项目服务] 立项项目同步部分失败: #{sync_result.message}"
          sync_result.errors.each do |error|
            Rails.logger.warn "[政府项目服务] 立项项目同步错误: #{error}"
          end
        end
      rescue StandardError => e
        Rails.logger.error "[政府项目服务] 立项项目同步异常: #{e.message}"
        Rails.logger.error e.backtrace.join("\n")
      end
    end

    # 按项目类型分类政府项目
    # @param projects [Array] 政府项目列表
    # @return [Hash] 分类后的项目
    def classify_projects(projects)
      result = { lixiang: [], shigong: [], change: [] }

      projects.each do |project|
        item_name = project["item_name"]
        next unless item_name

        case item_name
        when /可行性研究报告|建议书|立项/
          result[:lixiang] << project
        when /施工许可|初步设计|施工图设计/
          result[:shigong] << project
        when /变更|信息变更|调整/
          result[:change] << project
        end
      end

      Rails.logger.info "[政府项目服务] 项目分类结果: 立项#{result[:lixiang].count}个, 施工许可证#{result[:shigong].count}个, 变更#{result[:change].count}个"
      result
    end

    # 按类型同步项目
    # @param type [Symbol] 项目类型 (:lixiang, :shigong, :change)
    # @param projects [Array] 该类型的项目列表
    # @param date [String] 查询日期
    def sync_projects_by_type(type, projects, date)
      return if projects.empty?

      service_class = case type
                      when :lixiang
                        Irs::LixiangProjectSyncService
                      when :shigong
                        Irs::ShigongPermitSyncService
                      when :change
                        Irs::ProjectChangeSyncService
                      else
                        return
                      end

      type_name = { lixiang: "立项", shigong: "施工许可证", change: "项目变更" }[type]

      begin
        Rails.logger.info "[政府项目服务] 开始同步#{type_name}项目，数量: #{projects.count}"

        sync_result = service_class.sync_batch_projects(projects, Date.parse(date))

        Rails.logger.info "[政府项目服务] #{type_name}项目同步完成: 创建#{sync_result[:created]}个, 跳过#{sync_result[:skipped]}个, 失败#{sync_result[:failed]}个"

        if sync_result[:failed] > 0
          sync_result[:errors].each do |error|
            Rails.logger.warn "[政府项目服务] #{type_name}项目同步错误: #{error}"
          end
        end
      rescue StandardError => e
        Rails.logger.error "[政府项目服务] #{type_name}项目同步异常: #{e.message}"
        Rails.logger.error e.backtrace.join("\n")
      end
    end

  end

end
