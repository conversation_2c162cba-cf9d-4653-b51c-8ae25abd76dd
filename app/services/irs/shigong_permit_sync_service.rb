# frozen_string_literal: true

module Irs

  # 施工许可证项目同步服务
  # 负责将政府项目接口中的施工许可证相关项目同步到本地 Serve::BidProject 表
  class ShigongPermitSyncService < BaseProjectSyncService

    # 最大重试次数
    MAX_RETRIES = 3

    # 重试间隔（秒）
    RETRY_INTERVAL = 2

    def initialize
      super(:shigong)
    end

    # 类方法：根据政府项目代码同步单个项目
    # @param project_code [String] 政府项目代码
    # @return [SyncResult] 同步结果
    def self.sync_single_project_by_code(project_code)
      new.sync_single_project_by_code(project_code)
    end

    # 类方法：批量同步项目
    # @param government_projects [Array] 政府项目数据数组
    # @param date [Date] 同步日期
    # @return [Hash] 批量同步结果
    def self.sync_batch_projects(government_projects, date = Date.current)
      new.sync_batch_projects(government_projects, date)
    end

    # 根据日期同步施工许可证项目
    # @param date [Date] 同步日期，默认为当前日期
    # @return [Hash] 同步结果统计
    def self.sync_projects_by_date(date = Date.current)
      service = new

      # 获取政府项目数据
      result = Irs::GovernmentProjectService.fetch_projects_by_date(date.strftime("%Y-%m-%d"))
      return { error: "获取政府项目数据失败: #{result.message}" } unless result.success?

      # 筛选施工许可证相关项目
      shigong_projects = result.projects.select do |project|
        item_name = project["item_name"]
        next false unless item_name

        service.project_type_config[:item_name_patterns].any? do |pattern|
          item_name.include?(pattern)
        end
      end

      service.safe_log(:info, "[施工许可证同步] 找到 #{shigong_projects.count} 个施工许可证项目")

      # 批量同步
      service.sync_batch_projects(shigong_projects, date)
    end

    protected

    # 获取项目详细信息
    # @param project_code [String] 政府项目代码
    # @return [Hash] 项目详细信息
    def fetch_project_details(project_code)
      retry_count = 0

      while retry_count < MAX_RETRIES
        begin
          safe_log(:info, "[施工许可证同步] 调用接口 (尝试 #{retry_count + 1}/#{MAX_RETRIES})")
          response = irs_service.tzxmbjxxypwgx(requestparam: project_code, type: "P")

          if response && response["code"] == "00" && response["msg"] == "成功"
            parsed_datas = JSON.parse(response["datas"])
            return { success: false, error: "接口返回空数据" } unless parsed_datas.any?

            irs_data = parsed_datas.first
            return { success: true, data: irs_data }
          else
            error_msg = "接口调用失败: #{response&.dig('msg') || '未知错误'}"
            safe_log(:warn, "[施工许可证同步] #{error_msg}")

            retry_count += 1
            return { success: false, error: error_msg } unless retry_count < MAX_RETRIES

            safe_log(:info, "[施工许可证同步] #{RETRY_INTERVAL}秒后重试...")
            sleep(RETRY_INTERVAL)

          end
        rescue StandardError => e
          retry_count += 1
          error_msg = "接口调用异常: #{e.message}"
          safe_log(:error, "[施工许可证同步] #{error_msg}")

          return { success: false, error: error_msg } unless retry_count < MAX_RETRIES

          safe_log(:info, "[施工许可证同步] #{RETRY_INTERVAL}秒后重试...")
          sleep(RETRY_INTERVAL)
        end
      end
    end

    # 从IRS数据创建BidProject
    # @param irs_data [Hash] IRS数据
    # @return [Serve::BidProject] 创建的项目
    def create_bid_project_from_irs_data(irs_data)
      # 获取第一个App（通常是默认App）
      app = App.first
      raise "未找到可用的App" unless app

      # 解析项目信息
      project_name = extract_project_name(irs_data)
      setup_time = parse_setup_time(irs_data)

      # 生成项目编号
      project_code = generate_project_code(setup_time.to_date)

      # 创建项目记录
      bid_project = Serve::BidProject.create!(
        app: app,
        code: project_code,
        name: project_name,
        state: "setuped",
        setup_at: setup_time,
        payload: build_project_payload(irs_data)
      )

      safe_log(:info, "[施工许可证同步] 创建项目成功: #{bid_project.code} - #{bid_project.name}")
      bid_project
    end

    private

    # 提取项目名称
    # @param irs_data [Hash] IRS数据
    # @return [String] 项目名称
    def extract_project_name(irs_data)
      # 优先使用 approval_title，如果为空则使用 item_name
      name = irs_data["approval_title"].presence || irs_data["item_name"]

      # 如果名称过长，截取前100个字符
      name = name[0..99] if name && name.length > 100

      name || "施工许可证项目"
    end

    # 解析立项时间
    # @param irs_data [Hash] IRS数据
    # @return [Time] 立项时间
    def parse_setup_time(irs_data)
      # 优先使用 deal_time，如果为空则使用 accept_time
      time_str = irs_data["deal_time"].presence || irs_data["accept_time"]

      if time_str
        Time.parse(time_str)
      else
        # 如果都没有，使用当前时间
        Time.current
      end
    rescue StandardError => e
      safe_log(:warn, "[施工许可证同步] 时间解析失败: #{e.message}，使用当前时间")
      Time.current
    end

    # 构建项目payload
    # @param irs_data [Hash] IRS数据
    # @return [Hash] payload数据
    def build_project_payload(irs_data)
      {
        # 项目类型标识
        shigong_permit_project: true,

        # 政府项目代码（用于关联查询）
        government_project_code: irs_data["project_code"],

        # 施工许可证办理时间（统一使用 construction_permit_deal_time）
        construction_permit_deal_time: irs_data["deal_time"],

        # 政府审批办理时间（保持兼容性）
        government_setup_deal_time: irs_data["deal_time"],

        # 审批信息
        approval_number: irs_data["approval_number"],
        approval_title: irs_data["approval_title"],

        # 事项信息
        item_name: irs_data["item_name"],
        item_code: irs_data["item_code"],

        # 部门信息
        dept_name: irs_data["dept_name"],
        dept_code: irs_data["dept_code"],

        # 状态信息
        current_state: irs_data["current_state"],

        # 时间信息
        accept_time: irs_data["accept_time"],
        deal_time: irs_data["deal_time"],
        transact_time: irs_data["transact_time"],

        # 文件信息
        file_path: irs_data["file_path"],

        # 完整的IRS数据（用于调试和扩展）
        irs_data: irs_data,

        # 同步信息
        synced_at: Time.current,
        sync_source: "government_project_api"
      }
    end

  end

end
