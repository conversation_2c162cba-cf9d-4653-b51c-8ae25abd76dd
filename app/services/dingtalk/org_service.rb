class Dingtalk::OrgService

  PAGE_SIZE = 100
  MAX_RETRIES = 3
  RETRY_DELAY = 1 # second

  # 特殊节点类型定义
  SPECIAL_NODE_TYPES = ["地（市）", "县（市、区）"].freeze
  VIRTUAL_NODE_TYPE = "行政区划虚节点".freeze

  def initialize(client)
    @client = client
  end

  # 同步组织下的组织，忽略部门
  def sync_orgs_only(org)
    raise ArgumentError, "Organization cannot be nil" unless org && org.code.present?

    process_orgs(org)
  end

  # 同步组织下的部门，忽略子组织
  def sync_departments_of_org(org)
    raise ArgumentError, "Organization cannot be nil" unless org && org.code.present?

    process_departments(org)
  end

  private

  def process_orgs(parent_org)
    # 获取子节点数据
    children = get_org_data(parent_org.code)
    return if children.empty?

    # 清理已删除的组织
    cleanup_removed_orgs(parent_org, children.pluck("organizationCode"))

    children.each do |child_data|
      # 跳过临时组织
      if special_node?(child_data)
        # 对于特殊节点，直接获取并处理其子节点
        process_special_node_children(child_data, parent_org)
      elsif should_create_org?(child_data, parent_org)
        # 创建普通组织节点并递归处理
        created_org = create_or_update_org(child_data, parent_org)
        process_orgs(created_org) unless child_data["leaf"]
      end
    end
  end

  def process_departments(org, parent_dept = nil)
    children = get_org_data(parent_dept&.code || org.code)
    return if children.empty?

    # 清理已删除的部门
    if parent_dept
      cleanup_removed_sub_departments(parent_dept, children.pluck("organizationCode"))
    else
      cleanup_removed_departments(org, children.pluck("organizationCode"))
    end

    children.each do |child_data|
      next if should_create_org?(child_data, org)

      if special_node?(child_data)
        # 特殊节点继续向下查找
        sub_children = get_org_data(child_data["organizationCode"])
        sub_children.each do |sub_data|
          next if should_create_org?(sub_data, org)

          dept = create_department(sub_data, org, parent_dept)
          process_departments(org, dept) unless sub_data["leaf"]
        end
      else
        dept = create_department(child_data, org, parent_dept)
        process_departments(org, dept) unless child_data["leaf"]
      end
    end
  end

  def get_org_data(parent_code)
    # 获取codes
    codes = with_retry { fetch_sub_org_codes(parent_code) }
    return [] if codes.empty?

    # 批量获取组织信息
    orgs = []
    codes.each_slice(PAGE_SIZE) do |batch_codes|
      response = with_retry do
        @client.listOrganizationsByCodes(organizationCodes: batch_codes)
      end
      orgs.concat(response.data.dig("content", "data") || [])
    end
    orgs
  end

  def fetch_sub_org_codes(parent_code)
    all_codes = []
    page = 1

    loop do
      response = @client.pageSubOrganizationCodes(
        organizationCode: parent_code,
        returnTotalSize: true,
        pageSize: PAGE_SIZE,
        pageNo: page
      )

      content = response.data["content"]
      all_codes.concat(content["data"]) if content["data"].present?

      break if all_codes.size >= content["totalSize"]

      page += 1
    end

    all_codes
  end

  def special_node?(data)
    SPECIAL_NODE_TYPES.include?(data["organizationName"]) ||
      data["typeName"] == VIRTUAL_NODE_TYPE
  end

  def should_create_org?(data, parent_org)
    return false unless parent_org

    if data["typeName"] == VIRTUAL_NODE_TYPE
      (parent_org.root? && data["parentName"] == "地（市）") ||
        (parent_org.parent&.root? && data["parentName"] == "县（市、区）")
    else
      SPECIAL_NODE_TYPES.include?(data["parentName"])
    end
  end

  def process_special_node_children(node_data, parent_org)
    sub_children = get_org_data(node_data["organizationCode"])

    sub_children.each do |child_data|
      if should_create_org?(child_data, parent_org)
        created_org = create_or_update_org(child_data, parent_org)
        process_orgs(created_org) unless child_data["leaf"]
      end
    end
  end

  def create_or_update_org(org_data, parent_org)
    org = parent_org.children.find_or_initialize_by(code: org_data["organizationCode"])

    org.assign_attributes(
      name: org_data["organizationName"],
      type_name: org_data["typeName"],
      org_identity: parent_org.org_identity,
      model_payload: org_data
    )

    org.save! if org.changed?
    org
  end

  def create_department(dept_data, org, parent_dept)
    department = org.departments.find_or_initialize_by(code: dept_data["organizationCode"])

    department.assign_attributes(
      name: dept_data["organizationName"],
      type_name: dept_data["typeName"],
      parent: parent_dept,
      model_payload: dept_data
    )

    department.save! if department.changed?
    department
  end

  def cleanup_removed_orgs(parent_org, existing_codes)
    parent_org.children.where.not(code: existing_codes).destroy_all
  end

  def cleanup_removed_departments(org, existing_codes)
    org.departments.where(parent: nil).where.not(code: existing_codes).destroy_all
  end

  def cleanup_removed_sub_departments(parent_dept, existing_codes)
    parent_dept.children.where.not(code: existing_codes).destroy_all
  end

  def with_retry
    retries = MAX_RETRIES
    begin
      yield
    rescue StandardError
      retries -= 1
      if retries.positive?
        sleep(RETRY_DELAY)
        retry
      end
      raise
    end
  end

end
