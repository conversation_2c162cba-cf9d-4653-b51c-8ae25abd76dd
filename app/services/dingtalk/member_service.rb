class Dingtalk::MemberService

  PAGE_SIZE = 100
  MAX_MEMBERS = 10_000
  MAX_RETRIES = 3
  RETRY_DELAY = 1 # second
  BLACK_LIST = ENV.fetch("black_list", "")&.split(/[\s,，、]+/)
  OAUTH_TYPE = "SoaAuth::OauthDingtalk".freeze
  MEMBER_TYPE = "DingtalkMember".freeze
  SPECIAL_DEPARTMENT_TYPES = ["临时组织", "虚拟组织", "内设机构"].freeze
  LOG_PREFIX = "[DingtalkMemberSync]".freeze

  class Error < StandardError; end

  class ProcessingError < Error; end

  class ApiError < ProcessingError; end

  class ValidationError < ProcessingError; end

  def initialize(client)
    @client = client
  end

  def sync_members_by(org_or_department)
    case org_or_department
    when Org
      sync_org_members(org_or_department)
    when Department
      sync_department_members_by_size(org_or_department)
    else
      raise ArgumentError, "Expected Org or Department, got #{org_or_department.class}"
    end
  end

  def sync_members_subtree_by(org_or_department)
    Rails.logger.tagged("MemberSync", org_or_department.class.name, org_or_department.code) do
      log_sync_start(org_or_department)
      process_sync_subtree(org_or_department)
      log_sync_completion(org_or_department)
    end
  rescue StandardError => e
    log_sync_error(org_or_department, e)
    raise
  end

  private

  def get_member_count(organization_code)
    response = fetch_members_page(organization_code, 1)
    validate_api_response!(response, "fetching first page of members")
    response.data.dig("content", "totalSize").to_i
  end

  def sync_org_members(org)
    member_count = get_member_count(org.code)

    if member_count <= MAX_MEMBERS
      sync_org_members_directly(org)
    else
      # 只遍历顶层部门避免重复
      org.departments.roots.each do |department|
        sync_department_members_by_size(department)
      end
    end
  end

  def sync_org_members_directly(org)
    Rails.logger.info "Syncing org #{org.code} members directly (count <= #{MAX_MEMBERS})"
    members_data = fetch_all_members(org.code)
    return if members_data.blank?

    # 预处理所有成员的主要职位
    members_data.each { |member| process_member_position(member) }

    # 按部门代码分组
    members_by_dept = members_data.group_by { |m| m.dig("govEmployeePosition", "organizationCode") }

    members_by_dept.each do |dept_code, dept_members|
      department = org.departments.find_by(code: dept_code)
      next unless department

      valid_members = filter_valid_members(dept_members, dept_code)
      next if valid_members.empty?

      account_info = fetch_account_info(valid_members)

      ActiveRecord::Base.transaction do
        process_department_members(department, valid_members, account_info)
        cleanup_department_members(
          department: department,
          existing_codes: valid_members.map { |m| m["employeeCode"] }
        )
      end
    end
  end

  def sync_department_members_by_size(department)
    member_count = nil
    begin
      member_count = get_member_count(department.code)
    rescue Dingtalk::MemberService::ApiError => e
      Rails.logger.error "跳过无权限的操作: #{e.message}"
      # 跳过当前部门，继续处理下一个
      return
    end

    if member_count <= MAX_MEMBERS
      sync_department_subtree_members(department)
    else
      # 只遍历直接子部门避免重复
      department.children.each do |child_dept|
        sync_department_members_by_size(child_dept)
      end
    end
  end

  def sync_department_subtree_members(department)
    Rails.logger.tagged("DepartmentSubtreeSync: #{department.code}") do
      Rails.logger.info "Starting sync for department and its subtree"

      members_data = fetch_all_members(department.code)
      return if members_data.blank?

      # 预处理所有成员的主要职位
      members_data.each { |member| process_member_position(member) }

      # 按部门代码分组
      members_by_dept = members_data.group_by { |m| m.dig("govEmployeePosition", "organizationCode") }

      # 收集所有处理过的部门代码，用于后续清理
      processed_departments = Set.new

      members_by_dept.each do |dept_code, dept_members|
        target_department = find_department_in_subtree(department, dept_code)
        next unless target_department

        valid_members = filter_valid_members(dept_members, dept_code)
        next if valid_members.empty?

        account_info = fetch_account_info(valid_members)

        ActiveRecord::Base.transaction do
          process_department_members(target_department, valid_members, account_info)
          cleanup_department_members(
            department: target_department,
            existing_codes: valid_members.map { |m| m["employeeCode"] }
          )
        end

        processed_departments.add(dept_code)
      end

      # 清理子树中未处理的部门
      cleanup_unprocessed_departments(department, processed_departments)
    end
  rescue StandardError => e
    Rails.logger.error "Failed to sync department subtree: #{e.message}"
    raise
  end

  def find_department_in_subtree(root_department, dept_code)
    root_department.subtree.find_by(code: dept_code)
  end

  def cleanup_department_members(department:, existing_codes:)
    # 添加日志记录清理前的状态
    before_count = department.memberships.count
    Rails.logger.info "开始清理部门 #{department.code} 的成员关系，当前成员数: #{before_count}"

    removed_memberships = department.memberships
      .joins("INNER JOIN members ON memberships.member_id = members.id")
      .where.not(members: { code: existing_codes })

    return unless removed_memberships.any?

    # 记录将要被清理的成员信息
    removed_memberships.each do |membership|
      Rails.logger.info "准备清理成员关系: User(#{membership.user_id}) - Department(#{department.code})"
    end

    # 使用软删除替代硬删除
    current_time = Time.current
    removed_memberships.update_all(
      invalid_at: current_time,
      updated_at: current_time
    )

    # 记录清理后的状态
    after_count = department.memberships.where(invalid_at: nil).count
    Rails.logger.info "部门 #{department.code} 成员关系清理完成，剩余有效成员数: #{after_count}"
  end

  def cleanup_unprocessed_departments(root_department, processed_department_codes)
    root_department.subtree.where.not(code: processed_department_codes).find_each do |dept|
      ActiveRecord::Base.transaction do
        cleanup_department_members(
          department: dept,
          existing_codes: [] # 传空数组表示清理所有成员
        )
      end
    end
  end

  def process_department_members(department, valid_members, account_info)
    processed_count = 0
    failed_count = 0

    valid_members.each do |member_data|
      account_data = prepare_account_data(member_data, account_info)
      process_member_position(member_data)
      if sync_member(member_data, account_data, org: department.org, department: department)
        processed_count += 1
      else
        failed_count += 1
      end
    rescue StandardError => e
      failed_count += 1
      Rails.logger.error "Unexpected error while processing member in department #{department.code}: #{e.message}"
      Rails.logger.error e.backtrace.join("\n") if Rails.env.development?
    end

    Rails.logger.info "Department #{department.code} sync summary: " \
                      "#{processed_count} members processed successfully, " \
                      "#{failed_count} members failed"
  end

  def prepare_account_data(member_data, account_info)
    account_data = account_info[member_data["employeeCode"]] || {}
    account_data["oauth_app_id"] = @client.code
    account_data
  end

  def sync_member(member_data, account_data, org:, department:)
    validate_sync_params!(member_data, account_data, org, department)

    name = member_data["employeeName"]
    return if BLACK_LIST.include?(name)

    app = org.app
    account_code = account_data["accountCode"]

    begin
      user = find_or_create_user(app, account_code, name, account_data)
      return unless user

      staff_dictionary = JSON.parse(File.read("app/services/staff_dictionary.json"))["职级code表"]

      # 获取原始信息
      original_member = user.members.find_by(code: member_data["employeeCode"])
      original_department = original_member&.user&.departments&.find_by(code: original_member&.model_payload&.dig(
        "govEmployeePosition", "organizationCode"
      ))
      original_duty_level = staff_dictionary.fetch(original_member&.job_level_code, original_member&.job_level_code)

      original_duty = original_member&.pos_job
      change_time = member_data.dig("govEmployeePosition", "gmtCreate")

      # 检查是否有变化
      if original_member.present? && original_member.persisted?
        current_duty = member_data.dig("govEmployeePosition", "govEmpPosJob")
        current_duty_level = staff_dictionary.fetch(member_data["empJobLevelCode"], member_data["empJobLevelCode"])

        # 判断是否是岗位变动而不是新建
        # 比较job_gmt_create和gmt_create，如果job_gmt_create更新，说明是岗位变动
        member_gmt_create = begin
          Time.parse(member_data["gmtCreate"])
        rescue StandardError
          nil
        end
        job_gmt_create = begin
          Time.parse(member_data.dig("govEmployeePosition", "gmtCreate"))
        rescue StandardError
          nil
        end

        is_position_change = false
        if original_member.gmt_create.present? && job_gmt_create.present?
          original_gmt_create = begin
            Time.parse(original_member.gmt_create)
          rescue StandardError
            nil
          end
          original_job_gmt_create = begin
            Time.parse(original_member.job_gmt_create)
          rescue StandardError
            nil
          end

          # 如果任职时间比员工创建时间晚，或者任职时间比原来的任职时间晚，则认为是岗位变动
          is_position_change = (original_gmt_create && job_gmt_create && job_gmt_create > original_gmt_create) ||
                               (original_job_gmt_create && job_gmt_create && job_gmt_create > original_job_gmt_create)
        end

        # 只有当确认是岗位变动，且有实际变化时才记录
        if is_position_change && (
          # 修改部门变更的判断条件
          (original_department != department && (original_department.present? || department.present?) &&
          original_duty != current_duty && original_duty.present? && current_duty.present? && current_duty.exclude?(original_duty) && original_duty.exclude?(current_duty)) ||
          (original_duty_level != current_duty_level && original_duty_level.present? && current_duty_level.present?)
        )
          handle_changes(user, original_department, department, original_duty, current_duty, original_duty_level,
                         current_duty_level, change_time)
        end
      end

      # 同步成员数据
      sync_member_data(user, member_data, org, department)
      Rails.logger.info "Successfully synced member: #{name} (#{member_data['employeeCode']})"
    rescue StandardError => e
      Rails.logger.error "Failed to sync member #{name} (#{member_data['employeeCode']}): #{e.message}"
      Rails.logger.error e.backtrace.join("\n") if Rails.env.development?
      nil # 返回nil表示这个member处理失败，但不影响其他member的处理
    end
  end

  def handle_changes(user, original_department, new_department, original_duty, new_duty, original_duty_level,
                     new_duty_level, change_time)
    changes = {}

    # 检查部门变化
    changes[:original_department_id] = original_department&.id if original_department != new_department
    changes[:department_id] = new_department&.id

    # 检查职务变化
    changes[:original_duty] = original_duty if original_duty != new_duty
    changes[:duty] = new_duty

    # 检查职级变化
    changes[:original_duty_level] = original_duty_level if original_duty_level != new_duty_level
    changes[:duty_level] = new_duty_level

    # 如果有变化，记录变更
    return if changes.empty?

    Rails.logger.info "成员的变更信息: #{changes.inspect}"

    Serve::DutyChange.create!(
      user_id: user.id,
      app: user.app,
      reason: "职务变动",
      change_date: change_time || Time.current,
      **changes
    )
  end

  def find_or_create_user(app, account_code, name, account_data)
    if mobile_number?(account_code)
      create_user_by_mobile(app, account_code, name, account_data)
    else
      find_or_create_user_by_oauth(app, name, account_data)
    end
  end

  def mobile_number?(account_code)
    account_code.to_s.match?(/^1\d{10}$/)
  end

  def create_user_by_mobile(app, account_code, name, account_data)
    user = app.users.find_or_initialize_by(account: account_code)
    user.assign_attributes(
      name: name,
      mobile: account_code,
      account_data: account_data
    )
    user.save! if user.changed?
    user
  end

  def find_or_create_user_by_oauth(app, name, account_data)
    account_id = account_data["accountId"]
    oauth_app_id = account_data["oauth_app_id"]

    oauth = find_oauth(app, oauth_app_id, account_id)

    if oauth
      process_existing_oauth(oauth, app)
    else
      create_new_user_with_oauth(app, name, account_data, account_id)
    end
  end

  def find_oauth(app, oauth_app_id, account_id)
    SoaAuth::Oauth.find_by(
      oauth_app_id: oauth_app_id,
      app_id: app.code,
      openid: account_id
    )
  end

  def process_existing_oauth(oauth, app)
    if oauth.user
      Rails.logger.info "oauth有相关的user，直接使用"
      oauth.user
    else
      Rails.logger.info "oauth:#{oauth.inspect} 不能关联到user，尝试通过auth_account去恢复关联"
      recover_user_from_oauth(oauth, app)
    end
  end

  def recover_user_from_oauth(oauth, app)
    return unless oauth.auth_account

    user = app.users.find_by(account: oauth.auth_account.account)
    if user
      oauth.update(user_id: user.id)
      Rails.logger.info "完成恢复"
      user
    else
      Rails.logger.error "没有通过account：#{oauth.auth_account.account} 找到对应的user"
      nil
    end
  end

  def create_new_user_with_oauth(app, name, account_data, account_id)
    Rails.logger.info "没有找到#{name}的oauth，创建对应的用户和oauth"

    user = app.users.find_or_initialize_by(account: account_id)
    user.assign_attributes(
      name: name,
      account_data: account_data
    )
    user.save! if user.changed?

    user.oauths.create!(
      type: OAUTH_TYPE,
      oauth_app_id: account_data["oauth_app_id"],
      app_id: app.code,
      openid: account_data["accountId"],
      unionid: account_data["employeeCode"]
    )

    user
  end

  def sync_user_gender(user, member_data)
    return if member_data["empGender"].blank?

    gender_code = member_data["empGender"]
    gender_dictionary = JSON.parse(File.read("app/services/staff_dictionary.json"))["性别code表"]
    gender_value = gender_dictionary[gender_code]

    return unless gender_value.present? && user.respond_to?(:gender) && user.gender != gender_value

    user.update(gender: gender_value)
    Rails.logger.info "已更新用户 #{user.id} #{user.name} 的性别信息为: #{gender_value}"
  end

  def sync_member_data(user, member_data, org, department)
    member = sync_member_details(user, member_data)

    # 同步性别信息到 User 模型
    sync_user_gender(user, member_data)

    # 先同步到原始部门
    sync_membership(user, member, org, department)

    # 如果是特殊类型的部门，同时添加到上级单位
    if SPECIAL_DEPARTMENT_TYPES.include?(department.type_name)
      parent_department = find_unit_or_temp_department(department)
      if parent_department
        # 创建新的 membership 记录，而不是更新现有的
        membership = user.memberships.find_or_initialize_by(
          member: member,
          department: parent_department
        )

        membership.assign_attributes(
          org: org,
          invalid_at: nil
        )

        if membership.changed?
          membership.save!
          Rails.logger.info "#{LOG_PREFIX} 更新/创建组织关系: User(#{user.id} #{user.name}) - Department(#{parent_department.name} #{parent_department.code})"
        else
          Rails.logger.info "#{LOG_PREFIX} 组织关系无变更: User(#{user.id} #{user.name}) - Department(#{parent_department.name} #{parent_department.code})"
        end
      end
    end

    Rails.logger.info "#{LOG_PREFIX} Synced member: #{member.name} (#{member.code})"
  end

  def find_unit_or_temp_department(department)
    Rails.logger.info "#{LOG_PREFIX} 开始查找上级单位，当前部门: #{department.name}(#{department.code})"

    while department.parent
      department = department.parent
      if department.type_name == "单位"
        Rails.logger.info "#{LOG_PREFIX} 找到上级单位: #{department.name}(#{department.code})"
        return department
      end
    end

    Rails.logger.info "#{LOG_PREFIX} 未找到上级单位，尝试查找临时组织"
    # 如果没有找到单位部门，则从虚拟组织开始往下找临时组织
    while department
      department = department.parent
      if department&.type_name == "临时组织"
        Rails.logger.info "#{LOG_PREFIX} 找到临时组织: #{department.name}(#{department.code})"
        return department
      end
    end

    Rails.logger.warn "#{LOG_PREFIX} 未找到上级单位或临时组织"
    nil
  end

  def sync_member_details(user, member_data)
    member = user.members.find_or_initialize_by(
      type: MEMBER_TYPE,
      code: member_data["employeeCode"]
    )

    member.assign_attributes(
      gmt_create: member_data["gmtCreate"],
      gender: member_data["empGender"],
      political_code: member_data["empPoliticalStatusCode"],
      job_level_code: member_data["empJobLevelCode"],
      budgeted_post_code: member_data["empBudgetedPostCode"],
      pos_job_rank_code: member_data.dig("govEmployeePosition", "posJobRankCode"),
      job_gmt_create: member_data.dig("govEmployeePosition", "gmtCreate"),
      pos_job: member_data.dig("govEmployeePosition", "govEmpPosJob") ||
               member_data.dig("govEmployeePositions")&.flat_map { |pos| pos["positionExtProperties"] }
               &.find { |prop| prop["code"] == "ext_EMPLOYEEPOSITION_zhiwei_1" }&.dig("value", 0),
      model_payload: member_data
    )

    member.save! if member.changed?
    member
  end

  def sync_membership(user, member, org, department)
    # 查找特定部门的membership
    membership = user.memberships.find_or_initialize_by(
      member: member,
      department: department
    )

    membership.assign_attributes(
      org: org,
      invalid_at: nil
    )

    if membership.changed?
      membership.save!
      Rails.logger.info "#{LOG_PREFIX} 更新/创建组织关系: User(#{user.id} #{user.name}) - Department(#{department.name} #{department.code})"
    else
      Rails.logger.info "#{LOG_PREFIX} 组织关系无变更: User(#{user.id} #{user.name}) - Department(#{department.name} #{department.code})"
    end
  end

  def fetch_all_members(organization_code)
    first_page = fetch_members_page(organization_code, 1)
    validate_api_response!(first_page, "fetching first page of members")

    content = first_page.data["content"]
    total_size = content["totalSize"]
    all_members = content["data"]

    if total_size > PAGE_SIZE
      total_pages = (total_size.to_f / PAGE_SIZE).ceil
      fetch_remaining_pages(organization_code, total_pages, all_members)
    end

    all_members
  end

  def fetch_remaining_pages(organization_code, total_pages, all_members)
    Rails.logger.info "Fetching #{total_pages} pages of members for org code: #{organization_code}"

    (2..total_pages).each do |page|
      Rails.logger.info "Fetching members page #{page}/#{total_pages}"
      response = fetch_members_page(organization_code, page)
      validate_api_response!(response, "fetching members page #{page}")
      all_members.concat(response.data.dig("content", "data"))
    end
  end

  def fetch_members_page(organization_code, page_no)
    with_retry do
      @client.pageOrganizationEmployeePositions(
        organizationCode: organization_code,
        pageSize: PAGE_SIZE,
        pageNo: page_no,
        returnTotalSize: true
      )
    end
  end

  def filter_valid_members(members_data, target_code)
    # 只保留有主职且有效的成员
    members_data.select do |member|
      positions = member["govEmployeePositions"]
      main_position = positions.find { |position| position["mainJob"] == true && position["status"] == "A" }
      main_position.present?
    end
  end

  def fetch_account_info(members)
    employee_codes = members.pluck("employeeCode")
    account_map = {}

    employee_codes.each_slice(PAGE_SIZE) do |codes_batch|
      response = with_retry do
        @client.listEmployeeAccountIds(employeeCodes: codes_batch)
      end

      next unless response&.data&.dig("content", "data")

      response.data.dig("content", "data").each do |account|
        account_map[account["employeeCode"]] = account
      end
    end

    account_map
  end

  def process_member_position(member_data)
    # 只同步主职（mainJob: true 且 status == 'A'）
    main_position = member_data["govEmployeePositions"].find do |position|
      position["mainJob"] == true && position["status"] == "A"
    end
    member_data["govEmployeePosition"] = main_position || {}
    if main_position
      Rails.logger.info "#{LOG_PREFIX} 使用主要职位: #{member_data['employeeName']}, 职位: #{main_position['govEmpPosJob']}, 部门: #{main_position['organizationCode']}"
    else
      Rails.logger.warn "#{LOG_PREFIX} 未找到有效主职: #{member_data['employeeName']}，将从本地删除此成员"
    end
  end

  def with_retry
    retries = MAX_RETRIES
    begin
      yield
    rescue StandardError => e
      retries -= 1
      if retries.positive?
        Rails.logger.warn "Request failed: #{e.message}. Retrying... #{retries} attempts left"
        sleep(RETRY_DELAY)
        retry
      end
      Rails.logger.error "Request failed after #{MAX_RETRIES} attempts: #{e.message}"
      raise
    end
  end

  def validate_api_response!(response, context)
    return if response&.data&.dig("content", "success")

    raise ApiError, "API request failed for #{context}"
  end

  def validate_sync_params!(member_data, account_data, org, department)
    raise ArgumentError, "member_data is required" if member_data.blank?
    raise ArgumentError, "account_data is required" if account_data.blank?
    raise ArgumentError, "org is required" if org.blank?
    raise ArgumentError, "department is required" if department.blank?
  end

  def process_sync_subtree(org_or_department)
    case org_or_department
    when Org
      process_org_subtree(org_or_department)
    when Department
      sync_department_members_by_size(org_or_department)
    else
      raise ArgumentError, "Expected Org or Department, got #{org_or_department.class}"
    end
  end

  def process_org_subtree(org)
    sync_org_members(org)
    # 递归处理子组织
    org.children.each { |child_org| sync_members_subtree_by(child_org) }
  end

  def log_sync_start(target)
    Rails.logger.info "Starting member subtree sync for #{target.class.name}: #{target.code}"
  end

  def log_sync_completion(target)
    Rails.logger.info "Completed member subtree sync for #{target.class.name}: #{target.code}"
  end

  def log_sync_error(target, error)
    Rails.logger.error "Failed to sync members for #{target.class.name} #{target.code}: #{error.message}"
  end

end
