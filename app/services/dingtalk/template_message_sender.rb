module Dingtalk

  class TemplateMessageSender

    def self.call(user_ids, message_structure, async: false, scheduled_at: nil)
      # 检查环境变量是否禁用浙政钉消息发送
      return { status: "disabled", message: "浙政钉消息发送已禁用" } if ENV['DISABLE_ZZD_MESSAGE'].to_s.downcase == 'true'

      if async
        message = message_structure.deep_dup.with_indifferent_access
        pc_url = message.delete(:pc_url)
        mobile_url = message.delete(:mobile_url)

        user_ids.each do |user_id|
          seq = SecureRandom.hex(16)
          if pc_url
            single_pc_url = message[:action_card][:single_pc_url]
            redirect_pc_url = pc_url.include?("?") ? "#{pc_url}&seq=#{seq}" : "#{pc_url}?seq=#{seq}"
            redirect_pc_url = CGI.escape redirect_pc_url
            message[:action_card][:single_pc_url] = "#{single_pc_url}&redirect_url=#{redirect_pc_url}"
          end

          if mobile_url
            single_mobile_url = message[:action_card][:single_url]
            redirect_mobile_url = mobile_url.include?("?") ? "#{mobile_url}&seq=#{seq}" : "#{mobile_url}?seq=#{seq}"
            redirect_mobile_url = CGI.escape redirect_mobile_url
            message[:action_card][:single_url] = "#{single_mobile_url}&url=#{redirect_mobile_url}"
          end

          DingtalkSendMessage.perform_at(scheduled_at, user_id, nil, nil, message.to_json, nil)
        end
        { status: "queued", message: "消息已加入异步发送队列。" }
      else
        new(user_ids, message_structure).call
      end
    end

    def initialize(user_ids, message_structure)
      @user_ids = user_ids
      @message_structure = message_structure
    end

    def call
      result = { error_messages: [], success_messages: [] }
      message = @message_structure.deep_dup.with_indifferent_access

      pc_url = message.delete(:pc_url)
      mobile_url = message.delete(:mobile_url)

      @user_ids.each do |user_id|
        seq = SecureRandom.hex(16)
        if pc_url
          single_pc_url = message[:action_card][:single_pc_url]
          redirect_pc_url = pc_url.include?("?") ? "#{pc_url}&seq=#{seq}" : "#{pc_url}?seq=#{seq}"
          redirect_pc_url = CGI.escape redirect_pc_url
          message[:action_card][:single_pc_url] = "#{single_pc_url}&redirect_url=#{redirect_pc_url}"
        end

        if mobile_url
          single_mobile_url = message[:action_card][:single_url]
          redirect_mobile_url = mobile_url.include?("?") ? "#{mobile_url}&seq=#{seq}" : "#{mobile_url}?seq=#{seq}"
          redirect_mobile_url = CGI.escape redirect_mobile_url
          message[:action_card][:single_url] = "#{single_mobile_url}&url=#{redirect_mobile_url}"
        end

        Dingtalk::TemplateMessage.create!(
          seq: seq,
          notifyable_type: nil,
          notifyable_id: nil,
          user_id: user_id,
          oauth_app_id: ENV.fetch("DINGTALK_NOTIFY_APP_CODE", nil),
          message: message
        )
        result[:success_messages] << "消息成功发送给用户，id:#{user_id}"
      rescue StandardError => e
        result[:error_messages] << "发送消息给用户时出错，id:#{user_id}, 错误:#{e.message}"
      end

      result
    end

  end

end
