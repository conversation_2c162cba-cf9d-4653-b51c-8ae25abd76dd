# frozen_string_literal: true

# 微信抓取任务状态管理服务
class WechatCrawlTaskService
  REDIS_KEY_PREFIX = "wechat_crawl_task"
  DEFAULT_EXPIRY = 24.hours.to_i

  # 任务状态常量
  STATUS_PENDING = "pending"
  STATUS_PROCESSING = "processing"
  STATUS_COMPLETED = "completed"
  STATUS_FAILED = "failed"
  STATUS_NOT_FOUND = "not_found"

  class << self
    # 创建新任务
    # @param task_id [String] 任务ID
    # @param initial_data [Hash] 初始数据
    # @return [Hash] 任务数据
    def create_task(task_id, initial_data = {})
      task_data = {
        task_id: task_id,
        status: STATUS_PENDING,
        message: "任务已提交，正在排队处理...",
        data: {},
        created_at: Time.current.iso8601,
        updated_at: Time.current.iso8601
      }.merge(initial_data)

      set_task_data(task_id, task_data)
      task_data
    end

    # 更新任务状态
    # @param task_id [String] 任务ID
    # @param status [String] 状态
    # @param message [String] 状态消息
    # @param data [Hash] 附加数据
    # @return [Boolean] 是否更新成功
    def update_task(task_id, status, message = nil, data = {})
      current_data = get_task_data(task_id)
      return false unless current_data

      updated_data = current_data.merge(
        status: status,
        message: message || current_data[:message],
        data: current_data[:data].merge(data),
        updated_at: Time.current.iso8601
      )

      set_task_data(task_id, updated_data)
      true
    end

    # 标记任务为处理中
    # @param task_id [String] 任务ID
    # @param message [String] 状态消息
    def mark_processing(task_id, message = "正在处理任务...")
      update_task(task_id, STATUS_PROCESSING, message)
    end

    # 标记任务为完成
    # @param task_id [String] 任务ID
    # @param message [String] 状态消息
    # @param result_data [Hash] 结果数据
    def mark_completed(task_id, message = "任务完成", result_data = {})
      update_task(task_id, STATUS_COMPLETED, message, result_data)
    end

    # 标记任务为失败
    # @param task_id [String] 任务ID
    # @param message [String] 错误消息
    # @param error_data [Hash] 错误数据
    def mark_failed(task_id, message = "任务失败", error_data = {})
      update_task(task_id, STATUS_FAILED, message, { error: error_data })
    end

    # 获取任务状态
    # @param task_id [String] 任务ID
    # @return [Hash, nil] 任务数据或nil
    def get_task_status(task_id)
      get_task_data(task_id)
    end

    # 检查任务是否存在
    # @param task_id [String] 任务ID
    # @return [Boolean] 是否存在
    def task_exists?(task_id)
      $redis.exists(redis_key(task_id)) > 0
    end

    # 删除任务
    # @param task_id [String] 任务ID
    # @return [Boolean] 是否删除成功
    def delete_task(task_id)
      $redis.del(redis_key(task_id)) > 0
    end

    # 获取所有任务（用于调试，生产环境慎用）
    # @return [Array<Hash>] 任务列表
    def list_all_tasks
      keys = $redis.keys("#{REDIS_KEY_PREFIX}:*")
      keys.map do |key|
        task_data_json = $redis.get(key)
        next unless task_data_json

        JSON.parse(task_data_json, symbolize_names: true)
      rescue JSON::ParserError => e
        Rails.logger.error "解析任务数据失败: #{e.message}, key: #{key}"
        nil
      end.compact
    end

    # 清理过期任务（手动清理，Redis会自动过期）
    # @param older_than [Integer] 清理多少秒前的任务
    # @return [Integer] 清理的任务数量
    def cleanup_old_tasks(older_than = 24.hours.to_i)
      cutoff_time = Time.current - older_than.seconds
      cleaned_count = 0

      keys = $redis.keys("#{REDIS_KEY_PREFIX}:*")
      keys.each do |key|
        task_data_json = $redis.get(key)
        next unless task_data_json

        task_data = JSON.parse(task_data_json, symbolize_names: true)
        created_at = Time.parse(task_data[:created_at])

        if created_at < cutoff_time
          $redis.del(key)
          cleaned_count += 1
        end
      rescue StandardError => e
        Rails.logger.error "清理任务失败: #{e.message}, key: #{key}"
      end

      Rails.logger.info "清理了 #{cleaned_count} 个过期任务"
      cleaned_count
    end

    # 删除指定任务
    # @param task_id [String] 任务ID
    # @return [Boolean] 删除是否成功
    def self.delete_task(task_id)
      return false if task_id.blank?

      key = redis_key(task_id)

      # 检查任务是否存在
      unless $redis.exists?(key)
        Rails.logger.warn "尝试删除不存在的任务: #{task_id}"
        return false
      end

      # 获取任务信息用于日志
      task_data_json = $redis.get(key)
      if task_data_json
        task_data = JSON.parse(task_data_json, symbolize_names: true)
        Rails.logger.info "删除任务: #{task_id}, 状态: #{task_data[:status]}"
      end

      # 删除任务
      result = $redis.del(key)
      success = result > 0

      if success
        Rails.logger.info "任务删除成功: #{task_id}"
      else
        Rails.logger.error "任务删除失败: #{task_id}"
      end

      success
    rescue StandardError => e
      Rails.logger.error "删除任务时发生错误: #{e.message}, task_id: #{task_id}"
      false
    end

    # 获取所有任务列表
    def get_all_tasks
      begin
        Rails.logger.info "获取所有任务列表"

        # 从Redis中获取所有任务
        pattern = "#{REDIS_KEY_PREFIX}:*"
        keys = $redis.keys(pattern)

        tasks = keys.map do |key|
          task_data_json = $redis.get(key)
          if task_data_json
            task_data = JSON.parse(task_data_json, symbolize_names: true)
            task_id = key.gsub("#{REDIS_KEY_PREFIX}:", "")
            task_data.merge(task_id: task_id)
          end
        end.compact

        Rails.logger.info "获取到 #{tasks.length} 个任务"
        tasks
      rescue StandardError => e
        Rails.logger.error "获取任务列表异常: #{e.message}"
        []
      end
    end

    private

    # 生成Redis键
    # @param task_id [String] 任务ID
    # @return [String] Redis键
    def redis_key(task_id)
      "#{REDIS_KEY_PREFIX}:#{task_id}"
    end

    # 设置任务数据到Redis
    # @param task_id [String] 任务ID
    # @param task_data [Hash] 任务数据
    # @return [Boolean] 是否设置成功
    def set_task_data(task_id, task_data)
      $redis.setex(redis_key(task_id), DEFAULT_EXPIRY, task_data.to_json)
      true
    rescue StandardError => e
      Rails.logger.error "设置任务数据失败: #{e.message}, task_id: #{task_id}"
      false
    end

    # 从Redis获取任务数据
    # @param task_id [String] 任务ID
    # @return [Hash, nil] 任务数据或nil
    def get_task_data(task_id)
      task_data_json = $redis.get(redis_key(task_id))
      return nil unless task_data_json

      JSON.parse(task_data_json, symbolize_names: true)
    rescue JSON::ParserError => e
      Rails.logger.error "解析任务数据失败: #{e.message}, task_id: #{task_id}"
      nil
    rescue StandardError => e
      Rails.logger.error "获取任务数据失败: #{e.message}, task_id: #{task_id}"
      nil
    end
  end
end
