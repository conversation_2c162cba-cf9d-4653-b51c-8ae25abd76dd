module Bot
  module Searchable
    extend ActiveSupport::Concern

    RANSACK_PREDICATES = {
      eq: "等于",
      not_eq: "不等于",
      matches: "匹配",
      does_not_match: "不匹配",
      lt: "小于",
      lteq: "小于等于",
      gt: "大于",
      gteq: "大于等于",
      in: "在列表中",
      not_in: "不在列表中",
      cont: "包含",
      not_cont: "不包含",
      start: "以...开始",
      not_start: "不以...开始",
      end: "以...结束",
      not_end: "不以...结束",
      true: "为真",
      false: "为假",
      present: "有值",
      blank: "为空",
      null: "为null",
      not_null: "不为null"
    }.freeze

    class_methods do
      # 定义可搜索的属性
      # @param attributes [Array<Symbol>] 可搜索的属性列表
      def searchable_attributes(*attributes)
        @searchable_attributes = attributes if attributes.any?
        @searchable_attributes || []
      end

      # 定义可搜索的关联
      # @param associations [Array<Symbol>] 可搜索的关联列表
      def searchable_associations(*associations)
        @searchable_associations = associations if associations.any?
        @searchable_associations || []
      end

      # 定义可搜索的scope
      # @param scopes [Array<Symbol>] 可搜索的scope列表
      def searchable_scopes(*scopes)
        @searchable_scopes = scopes if scopes.any?
        @searchable_scopes || []
      end

      # 获取所有可搜索字段的描述
      # @return [Hash] 字段描述，格式为 { field_name: description }
      def searchable_field_descriptions
        descriptions = {}

        # 添加属性部分
        searchable_attributes.each do |attr|
          descriptions[attr] = {
            type: :attribute,
            name: human_attribute_name(attr),
            column_type: columns_hash[attr.to_s]&.type
          }
        end

        # 添加关联部分
        searchable_associations.each do |assoc|
          reflection = reflect_on_association(assoc)
          next unless reflection

          descriptions[assoc] = {
            type: :association,
            name: human_attribute_name(assoc),
            association_type: reflection.macro,
            model: reflection.klass.name
          }
        end

        # 添加scope部分
        searchable_scopes.each do |scope_name|
          descriptions[scope_name] = {
            type: :scope,
            name: I18n.t("activerecord.scopes.#{model_name.i18n_key}.#{scope_name}",
                        default: scope_name.to_s.humanize)
          }
        end

        descriptions
      end

      # 获取ransack查询的条件说明
      # @return [Hash] 查询条件说明
      def ransack_predicates
        RANSACK_PREDICATES.keys
      end

      # 获取ransack查询条件的描述
      # @return [Hash] 查询条件描述
      def ransack_predicate_descriptions
        predicates = {}

        # 获取通用查询条件描述
        RANSACK_PREDICATES.each do |key, default_desc|
          predicates[key] = I18n.t("activerecord.ransack.predicates.#{key}",
                                   default: default_desc)
        end

        # 获取模型特定的查询条件描述
        model_predicates = I18n.t("activerecord.#{model_name.i18n_key}.ransack_predicates", default: {})
        predicates.merge!(model_predicates)

        predicates
      end
    end
  end
end
