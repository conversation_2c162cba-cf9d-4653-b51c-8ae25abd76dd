module Bot::Ragable
  extend ActiveSupport::Concern

  included do
    attribute :rag_documents, :jsonb, comment: '多个知识库文件'

    after_create_commit :process_rag_documents

    def process_rag_documents
      return unless rag_enabled?

      temp_files = []
      begin
        # 批量下载所有文件
        temp_files = download_files(rag_documents[files])

        # 批量添加到向量存储
        rag_vector_store.add_data(paths: temp_files.map(&:path)) unless temp_files.empty?
      ensure
        # 清理所有临时文件
        temp_files.each { |file| file.unlink if file && File.exist?(file.path) }
      end
    end

    def rag_enabled?
      rag_documents.present? && rag_documents['files'].present?
    end

    def rag_ask(question)
      rag_vector_store.ask(question: question)
    end

    def rag_prompt(query_text, max_contexts: 3)
      relevant_contexts = rag_retrieve_relevant_contexts(
        query_text,
        max_contexts: max_contexts,
      )

      return '' if relevant_contexts.blank?

      # 构建系统提示词
      system_prompt = <<~PROMPT
        你是一个专业的报告生成助手。在生成报告时，请遵循以下原则：

        1. 参考资料的使用：
           - 参考资料作为背景知识和写作素材
           - 不要直接复制参考资料的内容
           - 将参考资料中的信息自然地融入到报告中
           - 可以借鉴参考资料中的专业术语、数据和论据

        2. 报告结构：
           - 确保报告结构清晰、层次分明
           - 使用Markdown格式组织内容
           - 适当使用标题、列表和引用等格式元素

        3. 内容创作：
           - 根据模板要求和变量信息生成相应内容
           - 对于参考资料未覆盖的部分，使用专业知识进行补充
           - 确保内容的专业性、准确性和完整性
           - 保持写作风格的一致性
      PROMPT

      system_prompt + "参考资料（仅供参考，不要直接复制）：\n\n#{relevant_contexts}"
    end

    private

    def rag_vector_store
      config_hash = ActiveRecord::Base.connection_db_config.configuration_hash
      db_url = "#{config_hash[:adapter]}://" \
        "#{config_hash[:username]}:" \
        "#{URI.encode_www_form_component(config_hash[:password])}@" \
        "#{config_hash[:host]}:" \
        "#{config_hash[:port]}/" \
        "#{config_hash[:database]}"

      @rag_vector_store ||= Langchain::Vectorsearch::Pgvector.new(
        url: db_url,
        index_name: 'bot_document_embeddings',
        llm: Bot::LlmFactory.create,
        namespace: "#{self.class.name.underscore}_rag_#{id}", # 使用文档ID作为命名空间，隔离不同文档的向量
      )
    end

    # 检索相关上下文
    def rag_retrieve_relevant_contexts(query_text, max_contexts: 3)
      return nil unless rag_enabled?

      # 执行相似度搜索
      results = rag_vector_store.similarity_search(
        query_text,
        k: max_contexts,
      )

      # 格式化检索结果
      results.map do |result|
        # 来源：#{result.metadata[:source]}
        <<~CONTENT
          相关内容：#{result.page_content}
          ---
        CONTENT
      end.join("\n")
    end

    def download_files(documents)
      documents.filter_map do |doc|
        next if doc['url'].blank?

        download_file_with_retry(doc['url'])
      end
    end

    def download_file_with_retry(url, max_retries: 3, delay: 1)
      retries = 0
      begin
        download_file(url)
      rescue StandardError => e
        retries += 1
        if retries <= max_retries
          sleep_duration = delay * (2**(retries - 1)) # 指数退避
          Rails.logger.warn("Retry #{retries}/#{max_retries} downloading #{url} after #{sleep_duration}s: #{e.message}")
          sleep(sleep_duration)
          retry
        else
          Rails.logger.error("Failed to download after #{max_retries} retries from #{url}: #{e.message}")
          nil
        end
      end
    end

    def download_file(url)
      # 使用 URL 的 MD5 哈希和时间戳创建唯一的文件名
      unique_id = Digest::MD5.hexdigest("#{url}-#{Time.current.to_f}-#{SecureRandom.uuid}")
      extension = File.extname(url)

      temp_file = Tempfile.new(
        ["document-#{unique_id}", extension],
        encoding: 'ascii-8bit',
      )

      URI.open(url, 'rb') do |remote_file|
        IO.copy_stream(remote_file, temp_file)
      end
      temp_file.close
      temp_file
    rescue StandardError => e
      Rails.logger.error("Error downloading file from #{url}: #{e.message}")
      temp_file.unlink if temp_file && File.exist?(temp_file.path)
      raise
    end
  end

  class_methods do
  end
end
