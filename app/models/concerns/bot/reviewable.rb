module Bot::Reviewable
  extend ActiveSupport::Concern

  included do
    attribute :review_instructions, :text, comment: '审查介绍'

    has_many :review_rules, as: :review_source, dependent: :destroy
  end

  # 执行文档审查
  def perform_review(document_source)
    return if Rails.env.test?
    raise Error, '审查对象不存在' unless document_source.present?
    raise Error, '审查应用不存在' unless review_rules.present?

    document_source.update!(review_state: :processing)

    document_source.review_results.destroy_all

    # 获取所有活跃的规则
    rules = review_rules.where(active: true)

    # 对每个规则进行审查
    rules.each do |rule|
      perform_rule_review(rule, document_source)
    end

    document_source.update!(review_state: :completed)
  rescue StandardError => e
    document_source.update!(review_state: :failed)
    raise Error, e.message
  end

  private

  def perform_rule_review(rule, document_source)
    prompt = build_rule_review_prompt(rule, document_source)

    result = Bot::LlmFactory.chat_with_llm(
      Bot::LlmFactory.create,
      messages: [
        {
          role: 'user',
          content: prompt,
        },
      ],
    )

    parsed_result = JSON.parse(result.chat_completion.gsub(/```json\s*|\s*```/, ''))

    # 处理发现的所有违规项
    violations = parsed_result['violations'] || []

    violations.each do |violation|
      document_source.review_results.create!(
        review_rule: rule,
        name: violation['name'],
        raw: violation['raw'],
        reason: violation['reason'],
        suggest: violation['suggest'],
        level: violation['level'],
        meta: {
          location: violation['location'],
          replacement: violation['replacement'],
        },
      )
    end
  end

  def build_rule_review_prompt(rule, document_source)
    contents = document_source.file_contents
    raise Error, '文档内容为空' if contents.blank?

    instructions = [
      review_instructions,
      '审查规则：',
      rule.content,
      '评分标准：',
      rule.scoring_criteria,
      '待审查文件内容：',
      contents,
      '注意：',
      '1. 请只返回JSON格式的结果，不要包含任何其他解释性文字',
      '2. 对于每个违规项，请提供一个符合规则的替换文本',
      '请返回如下格式的JSON（violations为空数组表示没有违规项）：',
    ].join("\n")

    json_example = '{
  "violations": [
    {
      "name": "违规名称",
      "raw": "违规的原文内容",
      "reason": "违规原因",
      "suggest": "修改建议",
      "level": "high/medium/low",
      "location": "位置信息",
      "replacement": "替换后的内容"
    }
  ]
}'

    "#{instructions}\n#{json_example}"
  end

  class Error < StandardError; end

  class_methods do
  end
end
