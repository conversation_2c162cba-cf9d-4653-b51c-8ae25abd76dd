module Bot::Vectorable
  extend ActiveSupport::Concern
  include Bot::PgvectorConnectionManager

  included do
    attribute :file, :jsonb, comment: '文件或内容'

    after_create_commit :process_document

    def ask(question)
        self.class.with_pgvector_connection do
          vector_store.ask(question: question)
        end
    end

    def process_document
      return if Rails.env.test?
      return if file.blank?

      self.class.with_pgvector_connection do
        # 清空向量存储
        vector_store.documents_model.where(namespace: vector_store.namespace).delete

        if file['url'].present?
          # 下载文件到临时目录
          temp_file = Tempfile.new(['document', File.extname(file['url'])])
          begin
            # 使用 URI.open 并以二进制模式写入
            URI.open(file['url'], 'rb') do |remote_file|
              temp_file.binmode # 设置为二进制模式
              temp_file.write(remote_file.read)
            end
            temp_file.close

            # 将文档内容添加到向量存储
            vector_store.add_data(paths: temp_file.path)
          ensure
            temp_file.unlink
          end
        elsif file['contents'].present?
          # 将json整个内容添加到向量存储
          text = file['contents'].map { |content| content['text'] }.join("\n")
          # 对文档内容进行分块
          chunker = Langchain::Chunker::RecursiveText.new(
            text,
            # 设置较大的 chunk size，确保每个块有足够的上下文
            chunk_size: 1000,
            # 设置较小的重叠，保持上下文连贯性
            chunk_overlap: 100,
            # 按段落、句号等自然分隔符分割
            separators: ["\n\n", "\n", '。', '.', '!', '?', '！', '？'],
          )
          # 将文档内容添加到向量存储
          vector_store.add_texts(
            texts: chunker.chunks.map(&:text),
          )
        end
      end
    rescue => e
      Rails.logger.error "处理文档失败: #{e.message}"
      raise e
    end

    def file_contents
      return '' if file.blank?

      if file['contents'].present?
        file['contents'].map { |content| content['text'] }.join("\n")
      elsif file['url'].present?
        temp_file = Tempfile.new(['document', File.extname(file['url'])])
        begin
          URI.open(file['url'], 'rb') do |remote_file|
            temp_file.binmode
            temp_file.write(remote_file.read)
          end
          temp_file.close

          loader = Langchain::Loader.new(temp_file.path)
          docs = loader.load
          docs.chunks.map(&:text).join("\n")
        ensure
          temp_file.unlink
        end
      else
        ''
      end
    end

    private

    def vector_store
      config_hash = ActiveRecord::Base.connection_db_config.configuration_hash

      # 构建数据库 URL，添加连接池配置
      connection_options = self.class.pgvector_connection_options
      db_url = "#{config_hash[:adapter]}://" \
        "#{config_hash[:username]}:" \
        "#{URI.encode_www_form_component(config_hash[:password])}@" \
        "#{config_hash[:host]}:" \
        "#{config_hash[:port]}/" \
        "#{config_hash[:database]}" \
        "?pool=#{connection_options[:pool]}" \
        "&max_connections=#{connection_options[:max_connections]}" \
        "&connect_timeout=#{connection_options[:connect_timeout]}"

      @vector_store ||= Langchain::Vectorsearch::Pgvector.new(
        url: db_url,
        index_name: 'bot_document_embeddings',
        llm: Bot::LlmFactory.create,
        namespace: "#{self.class.name.underscore}_#{id}" # 使用文档ID作为命名空间，隔离不同文档的向量
      )
    end
  end

  class_methods do
  end
end
