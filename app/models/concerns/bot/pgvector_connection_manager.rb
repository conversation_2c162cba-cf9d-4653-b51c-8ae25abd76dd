module Bot::PgvectorConnectionManager
  extend ActiveSupport::Concern

  class_methods do
    def with_pgvector_connection
      retries = 0
      max_retries = ENV.fetch('PGVECTOR_MAX_RETRY_COUNT', 3).to_i

      begin
        # 确保在同一个数据库事务中使用相同的连接
        ActiveRecord::Base.connection_pool.with_connection do
          yield
        end
      rescue PG::ConnectionBad, Sequel::DatabaseDisconnectError => e
        retries += 1
        if retries <= max_retries
          Rails.logger.warn "Database connection error, retrying (#{retries}/#{max_retries}): #{e.message}"
          # 给连接一个重置的机会
          sleep(ENV.fetch('PGVECTOR_RETRY_INTERVAL', 1).to_i)
          retry
        else
          Rails.logger.error "Failed to reconnect to database after #{max_retries} attempts: #{e.message}"
          raise
        end
      end
    end

    # Sequel 连接池配置
    # 参考：https://sequel.jeremyevans.net/rdoc/files/doc/opening_databases_rdoc.html#label-General+connection+options
    def pgvector_connection_options
      {
        pool: ENV.fetch('RAILS_MAX_THREADS') { 5 }.to_i,           # 连接池大小，与 Rails 保持一致
        max_connections: ENV.fetch('PGVECTOR_MAX_CONNECTIONS', 10).to_i,  # 最大连接数
        connect_timeout: ENV.fetch('PGVECTOR_CONNECT_TIMEOUT', 10).to_i   # 连接超时（秒）
      }
    end
  end
end
