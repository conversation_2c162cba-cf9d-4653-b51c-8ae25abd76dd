module Bot
  module Toolable
    extend ActiveSupport::Concern

    class_methods do
      def function_schemas
        @function_schemas ||= Langchain::ToolDefinition::FunctionSchemas.new(tool_name)
      end
    end

def function_schemas
  @instance_function_schemas ||=
    begin
      schemas = Langchain::ToolDefinition::FunctionSchemas.new(self.class.tool_name)
      original_schema = self.class.function_schemas.instance_variable_get(:@schemas)

      original_schema.each do |method_name, schema|
        new_schema = schema.deep_dup

        # 在function_name中添加intent标识
        base_name = "#{self.class.tool_name}"
        function_name = if @intent
                          "#{base_name}__intent_#{@intent.id}__#{method_name}"
                        else
                          "#{base_name}__#{method_name}"
                        end

        # 在description中添加intent信息
        original_desc = schema.dig(:function, :description).to_s
        intent_desc = @intent ? "[#{@intent.name}] #{@intent.description}" : nil
        combined_desc = [original_desc, intent_desc].reject(&:blank?).join("\n")

        new_schema[:function][:name] = function_name
        new_schema[:function][:description] = combined_desc if combined_desc.present?

        schemas.instance_variable_get(:@schemas)[method_name] = new_schema
      end

      schemas
    end
end
  end
end
