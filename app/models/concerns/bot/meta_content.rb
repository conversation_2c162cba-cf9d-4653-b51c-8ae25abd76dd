module Bot
  # 为模型提供meta字段的MessageContent序列化支持
  module MetaContent
    extend ActiveSupport::Concern

    included do
      # 确保模型有meta字段
      raise "模型必须有meta字段" unless column_names.include?('meta')

      # 序列化meta字段
      serialize :meta, JSON
    end

    # 获取meta内容对象
    # @return [Bot::MessageContent] 消息内容对象
    def meta_content
      @meta_content ||= Bot::MessageContent.from_h(meta || {})
    end

    # 设置meta内容
    # @param content [Bot::MessageContent] 消息内容对象
    def meta_content=(content)
      raise ArgumentError, "参数必须是MessageContent对象" unless content.is_a?(Bot::MessageContent)
      
      @meta_content = content
      self.meta = content.to_h
    end

    # 添加meta消息
    # @param content_type [String] 消息类型
    # @param content [String, Hash] 消息内容
    # @return [self] 返回自身，支持链式调用
    def add_meta_message(content_type:, content:)
      meta_content.add_message(content_type: content_type, content: content)
      self.meta = meta_content.to_h
      self
    end

    # 合并meta内容
    # @param other [Bot::MessageContent, Hash] 要合并的内容
    # @return [self] 返回自身，支持链式调用
    def merge_meta(other)
      other_content = case other
                     when Bot::MessageContent
                       other
                     when Hash
                       Bot::MessageContent.from_h(other)
                     else
                       raise ArgumentError, "参数必须是MessageContent对象或Hash"
                     end

      self.meta_content = meta_content.merge(other_content)
      self
    end

    # 获取指定类型的meta消息
    # @param type [String] 消息类型
    # @return [Array<Hash>] 指定类型的消息数组
    def meta_messages_of_type(type)
      meta_content.messages_of_type(type)
    end
  end
end
