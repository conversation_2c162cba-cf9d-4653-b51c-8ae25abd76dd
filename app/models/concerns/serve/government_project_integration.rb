# frozen_string_literal: true

# 政府项目接口集成模块
# 提供政府项目数据获取、缓存管理和项目状态更新功能
module Serve::GovernmentProjectIntegration

  extend ActiveSupport::Concern

  included do
    # 政府项目相关的scope方法

    # 根据政府项目接口获取指定日期立项的项目（使用缓存）
    # @param rule [Object] rule对象，可为nil
    # @param date [String, Date] 查询日期，默认为昨天
    scope :rule_government_setup_by_date, lambda { |_rule = nil, date = nil|
      query_date = case date
                   when String
                     Date.parse(date)
                   when Date
                     date
                   else
                     Date.yesterday
                   end
      government_projects = fetch_government_projects_by_date(query_date, "立项")

      # 1. 通过 Biz::Zbxm 关联查找传统项目
      traditional_projects = none
      if government_projects.any?
        deal_codes = government_projects.map { |p| p["deal_code"] }
        matching_zbxms = Biz::Zbxm.where(invest_project_code: deal_codes)
        project_codes = matching_zbxms.pluck(:projectCode)
        traditional_projects = where(code: project_codes)

        # 为匹配的项目设置政府立项办理时间
        government_projects.each do |gov_project|
          deal_code = gov_project["deal_code"]
          real_finish_time = gov_project["real_finish_time"]

          next unless deal_code.present? && real_finish_time.present?

          # 通过 deal_code 找到对应的本地项目
          matching_zbxm = matching_zbxms.find { |z| z.invest_project_code == deal_code }
          next unless matching_zbxm

          local_project = traditional_projects.find { |p| p.code == matching_zbxm.projectCode }
          next unless local_project

          # 设置政府立项办理时间到 payload
          local_project.payload ||= {}
          local_project.payload["government_setup_deal_time"] = real_finish_time
          local_project.save! if local_project.changed?
          Rails.logger.info "设置项目 #{local_project.name} 的政府立项办理时间: #{real_finish_time}"
        end
      end

      # 2. 查找同步的政府项目（立项、施工许可证、项目变更）
      synced_projects = none
      if government_projects.any?
        deal_codes = government_projects.map { |p| p["deal_code"] }

        # 查找立项项目
        lixiang_projects = where("payload::jsonb ->> ? = ?", "lixiang_project", "true")
          .where("payload::jsonb ->> ? = ANY(?)", "government_project_code", "{#{deal_codes.join(',')}}")
          .where("DATE(setup_at) = ?", query_date)

        # 查找施工许可证项目
        shigong_projects = where("payload::jsonb ->> ? = ?", "shigong_permit_project", "true")
          .where("payload::jsonb ->> ? = ANY(?)", "government_project_code", "{#{deal_codes.join(',')}}")
          .where("DATE(setup_at) = ?", query_date)

        # 查找项目变更
        change_projects = where("payload::jsonb ->> ? = ?", "project_change", "true")
          .where("payload::jsonb ->> ? = ANY(?)", "government_project_code", "{#{deal_codes.join(',')}}")
          .where("DATE(setup_at) = ?", query_date)

        # 合并所有同步项目的ID
        synced_project_ids = []
        synced_project_ids += lixiang_projects.pluck(:id) if lixiang_projects.exists?
        synced_project_ids += shigong_projects.pluck(:id) if shigong_projects.exists?
        synced_project_ids += change_projects.pluck(:id) if change_projects.exists?

        synced_projects = synced_project_ids.any? ? where(id: synced_project_ids) : none
      end

      # 3. 合并所有类型的项目（修复 UNION 问题）
      traditional_project_ids = traditional_projects.exists? ? traditional_projects.pluck(:id) : []
      synced_project_ids = synced_projects.exists? ? synced_projects.pluck(:id) : []
      all_project_ids = traditional_project_ids + synced_project_ids

      if all_project_ids.any?
        projects = where(id: all_project_ids)

        # 去重：如果有相同 code 的项目，优先选择最新创建的
        projects_array = projects.to_a
        unique_projects = projects_array.group_by(&:code).map do |code, same_code_projects|
          if same_code_projects.size > 1
            Rails.logger.info "[立项去重] 发现重复项目 code: #{code}, 数量: #{same_code_projects.size}, 选择最新的"
            # 如果有多个相同 code 的项目，选择最新创建的
            selected_project = same_code_projects.max_by(&:created_at)

            # 确保选中的项目有立项时间数据，如果没有则从其他项目复制
            if selected_project.payload&.dig("government_setup_deal_time").blank?
              source_project = same_code_projects.find { |p| p.payload&.dig("government_setup_deal_time").present? }
              if source_project
                selected_project.payload ||= {}
                selected_project.payload["government_setup_deal_time"] = source_project.payload["government_setup_deal_time"]
                selected_project.save! if selected_project.changed?
                Rails.logger.info "[立项去重] 复制立项时间到选中项目: #{selected_project.name}"
              end
            end

            selected_project
          else
            same_code_projects.first
          end
        end
        where(id: unique_projects.map(&:id))
      else
        none
      end
    }

    # 根据政府项目接口获取指定日期获得施工许可证的项目（使用缓存）
    # @param rule [Object] rule对象，可为nil
    # @param date [String, Date] 查询日期，默认为昨天
    scope :rule_government_construction_permit_by_date, lambda { |_rule = nil, date = nil|
      query_date = case date
                   when String
                     Date.parse(date)
                   when Date
                     date
                   else
                     Date.yesterday
                   end
      government_projects = fetch_government_projects_by_date(query_date, "施工许可证")
      return none if government_projects.empty?

      # 1. 通过 zbxm 的 invest_project_code 查找传统项目
      deal_codes = government_projects.map { |p| p["deal_code"] }
      matching_zbxms = Biz::Zbxm.where(invest_project_code: deal_codes)
      project_codes = matching_zbxms.pluck(:projectCode)
      traditional_projects = where(code: project_codes)

      # 2. 查找同步的施工许可证项目（通过 payload 中的 government_project_code）
      synced_projects = where("payload::jsonb ->> ? = ?", "shigong_permit_project", "true")
        .where("payload::jsonb ->> ? = ANY(?)", "government_project_code", "{#{deal_codes.join(',')}}")
        .where("DATE(setup_at) = ?", query_date)

      # 3. 合并传统项目和同步项目
      traditional_project_ids = traditional_projects.exists? ? traditional_projects.pluck(:id) : []
      synced_project_ids = synced_projects.exists? ? synced_projects.pluck(:id) : []
      all_project_ids = traditional_project_ids + synced_project_ids

      projects = all_project_ids.any? ? where(id: all_project_ids) : none

      # 为匹配的项目设置施工许可证办理时间
      government_projects.each do |gov_project|
        deal_code = gov_project["deal_code"]
        real_finish_time = gov_project["real_finish_time"]

        next unless deal_code.present? && real_finish_time.present?

        # 通过 deal_code 找到对应的本地项目
        # 查找所有匹配的zbxm（一个invest_project_code可能对应多个zbxm）
        matching_zbxms_for_deal_code = matching_zbxms.select { |z| z.invest_project_code == deal_code }

        matching_zbxms_for_deal_code.each do |zbxm|
          # 查找所有相同code的项目（处理重复项目的情况）
          local_projects = projects.select { |p| p.code == zbxm.projectCode }
          local_projects.each do |local_project|
            # 设置施工许可证办理时间到 payload
            local_project.payload ||= {}
            local_project.payload["construction_permit_deal_time"] = real_finish_time
            local_project.save! if local_project.changed?
            Rails.logger.info "设置项目 #{local_project.name} 的施工许可证办理时间: #{real_finish_time}"
          end
        end

        # 如果没有找到匹配的zbxm，尝试备用匹配逻辑
        next if matching_zbxms_for_deal_code.empty?

        # 如果通过invest_project_code没找到匹配的zbxm，尝试直接通过deal_code匹配
        # 这种情况可能是数据不一致导致的
        all_zbxms_with_deal_code = Biz::Zbxm.where(invest_project_code: deal_code)
        all_zbxms_with_deal_code.each do |zbxm|
          local_project = projects.find { |p| p.code == zbxm.projectCode }
          next unless local_project

          local_project.payload ||= {}
          local_project.payload["construction_permit_deal_time"] = real_finish_time
          local_project.save! if local_project.changed?
          Rails.logger.info "设置项目 #{local_project.name} 的施工许可证办理时间: #{real_finish_time} (通过备用匹配)"
        end
      end

      # 去重：如果有相同 code 的项目，优先选择最新创建的
      projects_array = projects.to_a
      unique_projects = projects_array.group_by(&:code).map do |code, same_code_projects|
        if same_code_projects.size > 1
          Rails.logger.info "[施工许可证去重] 发现重复项目 code: #{code}, 数量: #{same_code_projects.size}, 选择最新的"
          # 如果有多个相同 code 的项目，选择最新创建的
          selected_project = same_code_projects.max_by(&:created_at)

          # 确保选中的项目有施工许可证时间数据，如果没有则从其他项目复制
          if selected_project.payload&.dig("construction_permit_deal_time").blank?
            source_project = same_code_projects.find { |p| p.payload&.dig("construction_permit_deal_time").present? }
            if source_project
              selected_project.payload ||= {}
              selected_project.payload["construction_permit_deal_time"] = source_project.payload["construction_permit_deal_time"]
              selected_project.save! if selected_project.changed?
              Rails.logger.info "[施工许可证去重] 复制施工许可证时间到选中项目: #{selected_project.name}"
            end
          end

          selected_project
        else
          same_code_projects.first
        end
      end
      projects = where(id: unique_projects.map(&:id))

      # 过滤掉已经发送过施工许可证通知的项目
      sent_project_ids = Serve::Pack.joins(:rule)
        .where(source_type: "Serve::BidProject",
               source_id: projects.select(:id),
               serve_rules: { name: ["施工许可证通知", "建设规划许可证通知"] })
        .pluck(:source_id)
      projects.where.not(id: sent_project_ids)
    }

    # 根据政府项目接口获取指定日期有变更的项目（使用缓存）
    # @param rule [Object] rule对象，可为nil
    # @param date [String, Date] 查询日期，默认为昨天
    scope :rule_government_project_change_by_date, lambda { |_rule = nil, date = nil|
      query_date = case date
                   when String
                     Date.parse(date)
                   when Date
                     date
                   else
                     Date.yesterday
                   end
      government_projects = fetch_government_projects_by_date(query_date, "变更")
      return none if government_projects.empty?

      # 1. 通过 zbxm 的 invest_project_code 查找传统项目
      deal_codes = government_projects.map { |p| p["deal_code"] }
      matching_zbxms = Biz::Zbxm.where(invest_project_code: deal_codes)
      project_codes = matching_zbxms.pluck(:projectCode)
      traditional_projects = where(code: project_codes)

      # 2. 查找同步的项目变更（通过 payload 中的 government_project_code）
      synced_projects = where("payload::jsonb ->> ? = ?", "project_change", "true")
        .where("payload::jsonb ->> ? = ANY(?)", "government_project_code", "{#{deal_codes.join(',')}}")
        .where("DATE(setup_at) = ?", query_date)

      # 3. 合并传统项目和同步项目
      traditional_project_ids = traditional_projects.exists? ? traditional_projects.pluck(:id) : []
      synced_project_ids = synced_projects.exists? ? synced_projects.pluck(:id) : []
      all_project_ids = traditional_project_ids + synced_project_ids

      projects = all_project_ids.any? ? where(id: all_project_ids) : none

      # 为匹配的项目设置政府项目变更办理时间
      government_projects.each do |gov_project|
        deal_code = gov_project["deal_code"]
        real_finish_time = gov_project["real_finish_time"]

        next unless deal_code.present? && real_finish_time.present?

        # 通过 deal_code 找到对应的所有本地项目
        matching_zbxms_for_code = matching_zbxms.select { |z| z.invest_project_code == deal_code }
        matching_zbxms_for_code.each do |zbxm|
          local_project = projects.find { |p| p.code == zbxm.projectCode }
          next unless local_project

          # 设置政府项目变更办理时间到 payload
          local_project.payload ||= {}
          local_project.payload["government_change_deal_time"] = real_finish_time
          local_project.save! if local_project.changed?
          Rails.logger.info "设置项目 #{local_project.name} 的政府项目变更办理时间: #{real_finish_time}"
        end
      end

      # 去重：如果有相同 code 的项目，优先选择最新创建的
      projects_array = projects.to_a
      unique_projects = projects_array.group_by(&:code).map do |code, same_code_projects|
        if same_code_projects.size > 1
          Rails.logger.info "[项目变更去重] 发现重复项目 code: #{code}, 数量: #{same_code_projects.size}, 选择最新的"
          # 如果有多个相同 code 的项目，选择最新创建的
          selected_project = same_code_projects.max_by(&:created_at)

          # 确保选中的项目有项目变更时间数据，如果没有则从其他项目复制
          if selected_project.payload&.dig("government_change_deal_time").blank?
            source_project = same_code_projects.find { |p| p.payload&.dig("government_change_deal_time").present? }
            if source_project
              selected_project.payload ||= {}
              selected_project.payload["government_change_deal_time"] = source_project.payload["government_change_deal_time"]
              selected_project.save! if selected_project.changed?
              Rails.logger.info "[项目变更去重] 复制项目变更时间到选中项目: #{selected_project.name}"
            end
          end

          selected_project
        else
          same_code_projects.first
        end
      end
      projects = where(id: unique_projects.map(&:id))

      # 过滤掉当天已经发送过变更通知的项目
      date_start = query_date.beginning_of_day
      date_end = query_date.end_of_day
      sent_project_ids = Serve::Pack.joins(:rule)
        .where(source_type: "Serve::BidProject",
               source_id: projects.select(:id),
               created_at: date_start..date_end,
               serve_rules: { name: ["项目变更通知", "工程变更通知"] })
        .pluck(:source_id)
      projects.where.not(id: sent_project_ids)
    }

    # 兼容性方法：昨日立项项目
    scope :rule_government_setup_yesterday, ->(rule = nil) { rule_government_setup_by_date(rule, Date.yesterday) }

    # 兼容性方法：昨日施工许可证项目
    scope :rule_government_construction_permit_yesterday, lambda { |rule = nil|
      rule_government_construction_permit_by_date(rule, Date.yesterday)
    }

    # 兼容性方法：昨日变更项目
    scope :rule_government_project_change_yesterday, lambda { |rule = nil|
      rule_government_project_change_by_date(rule, Date.yesterday)
    }

    # 兼容性方法：今日立项项目
    scope :rule_government_setup_today, ->(rule = nil) { rule_government_setup_by_date(rule, Time.zone.today) }

    # 兼容性方法：今日施工许可证项目
    scope :rule_government_construction_permit_today, lambda { |rule = nil|
      rule_government_construction_permit_by_date(rule, Time.zone.today)
    }

    # 兼容性方法：今日变更项目
    scope :rule_government_project_change_today, lambda { |rule = nil|
      rule_government_project_change_by_date(rule, Time.zone.today)
    }
  end

  class_methods do
    # 获取政府项目接口数据的类方法（带缓存）
    # @param date [String, Date] 日期，格式：YYYY-MM-DD，默认为昨天
    # @param action_type [String] 操作类型：立项、施工许可证、变更
    # @param force_refresh [Boolean] 是否强制刷新缓存
    # @return [Array] 政府项目数据数组
    def fetch_government_projects_by_date(date = nil, action_type = nil, force_refresh: false)
      # 默认查询昨天的数据
      date ||= Date.yesterday
      date_str = date.is_a?(String) ? date : date.strftime("%Y-%m-%d")

      begin
        # 使用带缓存的服务方法
        result = Irs::GovernmentProjectService.fetch_projects_by_date(date_str, force_refresh: force_refresh)

        unless result.success?
          Rails.logger.warn "[政府项目接口] 获取失败: #{result.message}"
          return []
        end

        projects = result.projects

        # 如果指定了操作类型，则过滤相关项目
        projects = filter_projects_by_action_type(projects, action_type) if action_type.present?

        cache_source = result.raw_response&.include?("CACHED") ? "缓存" : "接口"
        Rails.logger.info "[政府项目接口] 从#{cache_source}获取到 #{projects.size} 个#{action_type}项目 (#{date_str})"
        projects
      rescue StandardError => e
        Rails.logger.error "[政府项目接口] 获取失败: #{e.message}"
        []
      end
    end

    # 根据操作类型过滤项目
    # @param projects [Array] 项目数组
    # @param action_type [String] 操作类型
    # @return [Array] 过滤后的项目数组
    def filter_projects_by_action_type(projects, action_type)
      return projects unless action_type.present?

      case action_type
      when "立项"
        # 匹配可行性研究报告审批和建议书审批
        projects.select do |p|
          name = p["item_name"].to_s
          name.include?("可行性研究") || name.include?("建议书审批") || name.include?("立项")
        end
      when "施工许可证", "建设规划许可证"
        # 匹配建筑工程施工许可
        projects.select do |p|
          name = p["item_name"].to_s
          name.include?("建筑工程施工许可") || (name.include?("建设") && name.include?("许可证"))
        end
      when "变更"
        # 匹配信息变更和其他变更类型
        projects.select do |p|
          name = p["item_name"].to_s
          name.include?("变更") || name.include?("信息变更")
        end
      when "设计审批"
        # 匹配初步设计审批
        projects.select do |p|
          name = p["item_name"].to_s
          name.include?("初步设计") || name.include?("设计审批")
        end
      else
        projects
      end
    end
  end

  # 实例方法

  # 获取施工许可证的办理时间
  # @return [String, nil] 返回格式化的施工许可证办理时间，如果没有则返回 nil
  def construction_permit_deal_time
    time_str = payload&.dig("construction_permit_deal_time")
    return nil if time_str.blank?

    # 如果是字符串格式，尝试解析为时间
    begin
      Time.parse(time_str.to_s).strftime("%Y年%m月%d日")
    rescue StandardError
      # 如果解析失败，返回原字符串或默认值
      time_str.is_a?(String) ? time_str : nil
    end
  end

  # 设置施工许可证的办理时间
  # @param time [Time, String] 办理时间
  # @return [String] 设置后的日期字符串（YYYY-MM-DD格式）
  def construction_permit_deal_time=(time)
    self.payload ||= {}
    time_obj = time.is_a?(Time) ? time : Time.parse(time.to_s)
    self.payload["construction_permit_deal_time"] = time_obj.strftime("%Y-%m-%d")
  end

  # 获取政府立项的办理时间
  # @return [String, nil] 返回格式化的立项办理时间，如果没有则返回 nil
  def government_setup_deal_time
    time_str = payload&.dig("government_setup_deal_time")
    return nil if time_str.blank?

    begin
      Time.parse(time_str.to_s).strftime("%Y年%m月%d日")
    rescue StandardError
      time_str.is_a?(String) ? time_str : nil
    end
  end

  # 设置政府立项的办理时间
  # @param time [Time, String] 办理时间
  # @return [String] 设置后的日期字符串（YYYY-MM-DD格式）
  def government_setup_deal_time=(time)
    self.payload ||= {}
    time_obj = time.is_a?(Time) ? time : Time.parse(time.to_s)
    self.payload["government_setup_deal_time"] = time_obj.strftime("%Y-%m-%d")
  end

  # 获取政府项目变更的办理时间
  # @return [String, nil] 返回格式化的项目变更办理时间，如果没有则返回 nil
  def government_change_deal_time
    time_str = payload&.dig("government_change_deal_time")
    return nil if time_str.blank?

    begin
      Time.parse(time_str.to_s).strftime("%Y年%m月%d日")
    rescue StandardError
      time_str.is_a?(String) ? time_str : nil
    end
  end

  # 设置政府项目变更的办理时间
  # @param time [Time, String] 办理时间
  # @return [String] 设置后的日期字符串（YYYY-MM-DD格式）
  def government_change_deal_time=(time)
    self.payload ||= {}
    time_obj = time.is_a?(Time) ? time : Time.parse(time.to_s)
    self.payload["government_change_deal_time"] = time_obj.strftime("%Y-%m-%d")
  end

  # 更新项目立项状态和时间
  # @param setup_time [Time] 立项时间，默认为当前时间
  # @return [Boolean] 是否更新成功
  def update_setup_status!(setup_time = Time.current)
    update!(
      state: "setuped",
      setup_at: setup_time,
      payload: (payload || {}).merge("government_setup_synced_at" => Time.current)
    )
    Rails.logger.info "[#{name}] 更新立项状态: #{setup_time}"
    true
  rescue StandardError => e
    Rails.logger.error "[#{name}] 更新立项状态失败: #{e.message}"
    false
  end

  # 记录项目变更信息
  # @param change_info [Hash] 变更信息
  # @return [Boolean] 是否记录成功
  def update_project_change_status!(change_info)
    self.payload ||= {}
    self.payload["project_changes"] ||= []

    change_record = change_info.merge(
      "synced_at" => Time.current,
      "recorded_at" => Time.current.iso8601
    )

    self.payload["project_changes"] << change_record
    save!

    Rails.logger.info "[#{name}] 记录项目变更: #{change_info[:change_type]}"
    true
  rescue StandardError => e
    Rails.logger.error "[#{name}] 记录项目变更失败: #{e.message}"
    false
  end

  # 获取项目的政府接口同步状态
  # @return [Hash] 同步状态信息
  def government_sync_status
    {
      setup_synced: payload&.dig("government_setup_synced_at").present?,
      construction_permit_synced: construction_permit_deal_time.present?,
      changes_count: payload&.dig("project_changes")&.size || 0,
      last_sync_at: [
        payload&.dig("government_setup_synced_at"),
        payload&.dig("construction_permit_deal_time"),
        payload&.dig("project_changes")&.last&.dig("synced_at")
      ].compact.max
    }
  end

end
