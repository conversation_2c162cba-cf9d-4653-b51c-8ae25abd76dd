class PromotionGraph

  def initialize(promotion_paths)
    @graph = Hash.new { |hash, key| hash[key] = Set.new }
    build_graph(promotion_paths)
  end

  def build_graph(promotion_paths)
    promotion_paths.each do |from, to_list|
      to_list.each do |to|
        add_edge(from, to)
      end
    end
  end

  def add_edge(from, to)
    @graph[from].add(to)
  end

  def can_promote?(from, to)
    visited = Set.new
    dfs(from, to, visited)
  end

  private

  def dfs(current, target, visited)
    return true if current == target
    return false if visited.include?(current)

    visited.add(current)
    @graph[current].each do |neighbor|
      return true if dfs(neighbor, target, visited)
    end
    false
  end

end
