# winbidbulletincode	中标公告唯一标识
# deliverydate	工期
# bidmanager	项目负责人
# bidamount	中标价
# winbiddercode	中标单位代码
# winbiddername	中标单位
# bulletinissuetime	中标公告发布时间
# bidsectioncodes	标段编号
# bidsectionname	标段名称
# zhiliangchengnuo	质量承诺
# zhongbiaoDate	中标时间
# projectname	项目名称
# regioncode	标段所在行政区域代码
# other_bid_price	费率或其它类型结果
# platformname	交易平台名称
# complaint_content	投诉情况
# handle_result	回复情况
# supervisedeptname	行政监督机构名称
# expert_name	专家姓名
# platformcode	交易平台标识码
# projectcode	项目编号

# 招标结果
class Serve::BidResult < ApplicationRecord

  self.track_migration = true
  seqable

  belongs_to :app
  belongs_to :bid_project, optional: true

  attribute :code,         :string, comment: "项目编号"
  attribute :state,        :string, commen: "状态"
  attribute :win_at,       :datetime, comment: "中标时间"
  attribute :unit_code,    :string,   comment: "中标单位代码"
  attribute :unit,         :string,   comment: "中标单位"
  attribute :amount,       :decimal, scale: 5, precision: 20, comment: "金额"
  attribute :payload,      :jsonb, comment: "额外字段"

  after_commit :async_bid_project!, only: [:create, :update], if: :bid_project

  def zbjg
    Biz::Zbjg.find_by winBidBulletinCode: seq
  end

  # 同步招标项目
  def async_bid_project!
    project = bid_project
    project.state = "finished"
    project.async!
  end

  # 同步数据
  def self.async!(app: App.first, time: nil)
    time ||= Time.zone.today.strftime("%F")
    Biz::Zbjg.ransack(load_time_gt: time).result.order(load_time: :asc).find_each do |zbjg|
      result = Serve::BidResult.find_or_initialize_by(seq: zbjg.winBidBulletinCode)
      result.update(
        app: app,
        bid_project: Serve::BidProject.find_by(code: zbjg.projectcode, app: app),
        code: zbjg.projectcode,
        win_at: zbjg.zhongbiaoDate,
        unit: zbjg.winBidderName,
        unit_code: zbjg.winBidderCode,
        amount: zbjg.bidAmount,
        payload: zbjg.as_json,
        updated_at: zbjg.load_time
      )
    rescue Exception
      next
    end
  end

end
