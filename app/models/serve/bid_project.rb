class Serve::BidProject < ApplicationRecord

  self.track_migration = true
  seqable
  include Region::Ext::AreaSource
  include Serve::GovernmentProjectIntegration
  acts_as_area(level: "area")
  # assocition
  belongs_to :app
  belongs_to :org, class_name: "::Org", optional: true
  belongs_to :contactor, class_name: "::User", optional: true
  belongs_to :manager, class_name: "::User", optional: true
  belongs_to :legaler, class_name: "::User", optional: true

  has_many :packs, as: :source
  has_many :bid_notices, dependent: :destroy
  has_many :bid_results, dependent: :destroy
  has_many :bid_items, dependent: :destroy
  has_many :bid_tenders, dependent: :destroy

  effectable(effective_at: "start_at", invalid_at: "end_at")

  attribute :code, :string, comment: "项目编号"
  attribute :name, :string, comment: "标题"
  attribute :state, :string, commen: "状态"
  attribute :content, :jsonb, comment: "内容"
  attribute :region_code, :string, comment: "地区编码"
  attribute :setup_at, :datetime, comment: "立项时间"
  attribute :register_at, :datetime, comment: "登记时间"
  attribute :open_at, :datetime, comment: "开标时间"
  attribute :win_at, :datetime, comment: "中标时间"
  attribute :manager_name, :string, comment: "负责人"
  attribute :contactor_name, :string, comment: "联系人"
  attribute :legaler_name, :string, comment: "项目法人"
  attribute :phone, :string, comment: "联系电话"
  attribute :unit, :string, comment: "招标单位"
  attribute :unit_code, :string, comment: "招标单位编号"
  attribute :send_state, :string, comment: "发送消息状态"
  attribute :amount, :decimal, scale: 5, precision: 20, comment: "金额"
  attribute :meta, :jsonb, comment: "元数据"
  attribute :payload, :jsonb, comment: "扩展字段"
  attribute :attachments, :jsonb, comment: "附件"

  delegate :name, to: :org, allow_nil: true, prefix: true

  scope :rule_before_open_bid, lambda { |_rule = nil, period = 2|
    start_time = period.days.since.beginning_of_day
    end_time = period.days.since.end_of_day
    ransack(open_at_gteq: start_time, open_at_lteq: end_time).result
  }

  # 后续如果需要发到其他区可以根据org检索
  scope :rule_current_bid, lambda { |_rule = nil, org = nil|
    start_time = Date.yesterday.beginning_of_day
    end_time = Date.yesterday.end_of_day
    projects = Serve::BidProject.between_times(start_time, end_time, field: "register_at")
    if org.present?
      org_name = org.is_a?(String) ? org : org.name
      projects = projects.joins(:org).where(orgs: { name: org_name })
    end
    # 过滤掉已经发送的记录
    send_project_ids = Serve::Pack.where(source: projects).pluck(:source_id)
    projects.where.not(id: send_project_ids)
  }

  # 过滤施工许可证
  scope :rule_construction_permit_current_bid, lambda { |_rule = nil|
    start_time = 5.days.ago.beginning_of_day
    end_time = Date.yesterday.end_of_day

    # 使用 where 替代 between_times 如果 between_times 不是 ActiveRecord 的标准方法
    projects = where(register_at: start_time..end_time)

    # 过滤掉已经发送的记录
    projects = projects.where("payload->>'construction_permit_processed_at' IS NULL")

    # 获取已经发送过的项目ID
    sent_project_ids = Serve::Pack.where(source_type: "Serve::BidProject", source_id: projects.select(:id)).pluck(:source_id)

    # 排除已发送的项目
    projects.where.not(id: sent_project_ids)
  }

  # setup: 立项, opening: 开标, finished: '已完成', closed: 已关闭
  enum :state, {
    pending: "pending",
    setuped: "setuped",
    registerd: "registered",
    opening: "opening",
    finished: "finished",
    closed: "closed"
  }
  default_value_for(:state) { "pending" }

  ### 以下均用于发送模版消息 #####################################
  # 项目联系人
  def rule_contactor_ids(*_args)
    [contactor_id]
  end

  # 项目法人
  def rule_legaler_ids(*_args)
    max_retries = 10
    retry_count = 0
    last_error = nil

    # 重试循环
    while retry_count < max_retries
      begin
        service = Irs::RequestService.new
        response = service.frdjjbxx(tyxydm: unit_code)

        if response["code"] == "00" && response["datas"].present?
          # 解析 JSON 字符串
          begin
            datas = JSON.parse(response["datas"].strip)
            data = datas.first if datas.is_a?(Array) && datas.any?

            if data && (legaler_name = data["fddbr"]).present?
              # 在系统中查找对应单位的人名
              if unit.present?
                department = Department.where("name LIKE ?", "%#{unit}%").first
                unless department.present?
                  Rails.logger.warn "未找到匹配的部门: unit=#{unit}, legaler_name=#{legaler_name}"
                  break
                end
                # 先尝试本单位直接找对应的法人
                user = department.users.find_by(name: legaler_name)
                return [user.id] if user

                # 如果找不到完全匹配的，尝试从上级部门的领导部门找
                parent_department = department.parent
                leader_department = parent_department.children.find_by("name LIKE ?", "%领导%")
                unless leader_department.present?
                  Rails.logger.warn "未找到匹配的领导部门: unit=#{unit}, legaler_name=#{legaler_name}"
                  break
                end
                user = leader_department.users.find_by(name: legaler_name)
                return [user.id] if user

                Rails.logger.warn "未找到匹配的用户: unit=#{unit}, legaler_name=#{legaler_name}"
                # 找到数据但未匹配到用户，不需要重试
                break
              else
                Rails.logger.warn "缺少单位: unit 为空"
                # 缺少必要参数，不需要重试
                break
              end
            else
              last_error = "未找到法人代表信息"
              Rails.logger.warn "#{last_error}: response=#{response}"
            end
          rescue JSON::ParserError => e
            last_error = "解析法人信息JSON失败: #{e.message}"
            Rails.logger.error "#{last_error}\nResponse: #{response&.inspect}"
          end
        else
          last_error = "获取法人信息失败: code=#{response['code']}, msg=#{response['msg']}"
          Rails.logger.warn "#{last_error}, unit_code=#{unit_code}"
        end
      rescue StandardError => e
        last_error = "请求异常: #{e.message}"
        Rails.logger.error "#{last_error}\n#{e.backtrace.join("\n")}"
      end

      # 如果还有重试次数，等待1秒后重试
      if retry_count < max_retries - 1
        sleep_seconds = 1
        Rails.logger.info "等待 #{sleep_seconds} 秒后重试...(#{retry_count + 1}/#{max_retries - 1})"
        sleep(sleep_seconds)
      end
      retry_count += 1
    end

    # 记录最终失败日志
    Rails.logger.error "获取法人信息达到最大重试次数 #{max_retries} 次，最后错误: #{last_error}" if last_error

    # 默认返回空数组
    []
  end

  # 项目负责人
  def rule_manager_ids(*_args)
    [manager_id]
  end

  # 项目名换
  def project_name(*_args)
    name
  end

  # 招标项目
  def template_message_user_name(**args)
    user = args[:user]
    user.id
    contactor_id
    "#{user.name}项目#{user.id == contactor_id ? '联系人' : '负责人'}"
  end

  # 登记日期
  def register_date(*_args)
    register_at&.strftime("%Y年%m月%d日")
  end

  # 立项日期
  def setup_date(*_args)
    setup_at&.strftime("%Y年%m月%d日")
  end

  #############################################################

  # 招标项目
  def zbxm
    Biz::Zbxm.find_by tenderProjectCode: seq
  end

  # 项目
  def xm
    bid_items.first&.xm || zbxm&.xm
  end

  # 招标公告
  def zbgg
    bid_notices.first&.zbgg || zbxm&.zbgg
  end

  # 招标结果
  def zbjg
    bid_results.first&.zbjg || zbxm&.zbjg
  end

  # 招标候选
  def zbhx
    bid_tenders.first&.zbhx || zbxm&.zbhx
  end

  # 同步招投标信息
  def async!
    content = {}
    zbgg = self.zbgg
    # 从招标公告中获取找比起内容
    if zbgg
      content = {
        guimo: zbgg.tenderGuiMo,
        range: zbgg.tenderRange,
        body: zbgg.tenderContents,
        tiaojian: zbgg.tenderTiaoJian
      }
    end

    # 处理联系人
    contactor = nil
    contactor_account = zbxm.tendererTel
    contactor_name = zbxm.tendererLianXiRen
    if contactor_account && contactor_account.to_s =~ /^1\d{10}$/
      contactor = app.users.find_by(account: contactor_account)
      contactor ||= app.users.create(name: contactor_name, account: contactor_account)
    end

    region_area = Region::Area.find_by(code: zbxm&.regionCode)
    org = Org.find_by(name: region_area&.name)

    update!(
      code: zbxm&.projectCode,
      name: zbxm&.projectName,
      region_code: zbxm&.regionCode,
      content: content,
      amount: zbjg&.bidAmount || zbxm.tenderAmount,
      register_at: zbxm.createTime,
      win_at: zbjg&.zhongbiaoDate,
      open_at: zbgg&.bidOpenTime,
      start_at: zbgg&.tenderDocGetStartTime,
      end_at: zbgg&.tenderDocDeadLine,
      contactor_name: contactor_name,
      manager_name: zbhx&.bidManager,
      legaler_name: xm&.legalPerson,
      phone: zbxm&.tendererTel,
      unit: zbxm&.tendererName,
      unit_code: zbxm&.tendererCode,
      updated_at: zbxm.load_time,
      contactor: contactor,
      region_area: region_area,
      org: org
    )
  rescue Exception
    Rails.logger.info "========#{code}===#{Time.zone.now.strftime('%F %T')}========="
  end

  # 不要调用，仅测试使用
  def generate_pack_by_console
    rule = Serve::Rule.find(44)
    Serve::BidProject.ransack(created_at_gt: "2024-11-01",
                              org_id_eq: 107).result.where.not(contactor_id: nil).order(created_at: :asc).find_each do |project|
      send_at = "#{project.created_at.strftime('%F')} 09:30:00"
      Serve::Pack.create(
        rule: rule,
        send_at: send_at,
        source: project,
        creator: rule.creator,
        activity_id: 11_812,
        org: project.org,
        payload: { user_ids: [project.contactor_id], use_custom: true },
        tanent_ids: rule.tanent_ids
      )
    end
  end

  # 同步招投标项目信息
  def self.async_all!(app: App.first, time: nil)
    time ||= Date.yesterday.strftime("%F")
    Biz::Zbxm.ransack(load_time_gt: time).result.order(load_time: :asc).find_each do |zbxm|
      next if zbxm.projectCode.to_s.start_with?("系统")

      project = Serve::BidProject.find_or_initialize_by(app: app, seq: zbxm.tenderProjectCode)
      project.async!
    rescue Exception
      next
    end
  end

end
