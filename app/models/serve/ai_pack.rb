class Serve::AiPack < Serve::Pack
  # pack发送之后立刻生成消息广场信息
  def generate_ai_message_square!
    contents = payload.dig('ai_contents') || []

    contents.each do |content|
      seq = content.dig('seq')
      ai_message_square = ai_message_squares.find_or_initialize_by(seq: seq)
      ai_message_square.update!(
        creator: creator,
        rule: self.rule,
        name: rule.name,
        content: content.dig('content')
      )
    end
  rescue => e
    Rails.logger.error "生成AI消息广场失败: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
  end

  # 发送消息
  def generate_serve_message!
    client = Dingtalk::Client.find_by(code: ENV['DINGTALK_NOTIFY_APP_CODE'])
    title = payload&.dig('message', 'title')
    markdown = payload&.dig('message', 'markdown')
    single_title = payload&.dig('message', 'single_title') || '查看详情'

    # 如果存在activity_id，直接使用活动内容
    if activity_id.present?&&payload.dig('use_custom')==false
      activity = Serve::Activity.find_by(id: activity_id)
      if activity.blank?
        Rails.logger.error "未找到ID为 #{activity_id} 的活动"
        return
      end

      # 生成消息广场信息（为了保持兼容性）
      generate_ai_message_square! if respond_to?(:generate_ai_message_square!)

      # 发送消息
      send_activity_message(activity, client, title, markdown, single_title)
      return
    end

    # 否则走原有的消息广场逻辑
    generate_ai_message_square! if respond_to?(:generate_ai_message_square!)

    # 检查是否有AI消息广场
    if ai_message_squares.blank? || ai_message_squares.count == 0
      Rails.logger.error '没有可用的AI消息广场，无法生成消息'
      return
    end

    send_users.find_each do |user|
      if self.messages.find_by(user: user)
        next # 已经发送过消息了不在发送
      end

      seq = SecureRandom.hex(16)
      tanent = Tanent.find_by(id: rule&.options&.website_url_tanent_id) || current_tanent
      pc_url = CGI.escape(File.join(tanent&.config&.dig('WEBSITE_URL') || ENV['WEBSITE_URL'] || '', "/serve/home/<USER>/#{seq}"))
      mobile_url = CGI.escape(File.join(tanent&.config&.dig('WEBSITE_MOBILE_URL') || ENV['WEBSITE_MOBILE_URL'] || '', "/#/pages/serve/messages/show/index?seq=#{seq}"))
      message_content = {}

      ai_message_square = ai_message_squares.sample

      if !ai_message_square
        Rails.logger.error '无法获取AI消息广场样本'
        next
      end

      if rule.options&.message_template_enabled
        message_template = rule.message_templates.used.sample
        message_content = message_template.get_message(user: user, source: source)
        message_title =  title || message_content.dig(:card, :title) || '清AI'
        message_markdown = markdown || message_content.dig(:card, :content) || '您有一条廉洁提醒，请查收。'
      else
        message_title =  title || (rule_item ? rule_item.template_message_title : rule&.options&.template_message_title) || '清AI'
        message_markdown = markdown || rule_item&.get_card_content(user: user, source: source) || rule&.get_card_content(user: user, source: source) || '您有一条廉洁提醒，请查收。'
      end

      begin
        content = ai_message_square.format_content(user: user, source: source)
        message_content[:content] = { title: message_content.dig(:content, :title), content: content }
      rescue => e
        Rails.logger.error "格式化内容失败: #{e.message}"
        Rails.logger.error e.backtrace.join("\n")
        next
      end

      message = {
        msgtype: 'action_card',
        action_card: {
          title: message_title,
          markdown: message_markdown,
          single_title: single_title,
          single_pc_url: "taurus://taurusclient/action/open_app?corpid=#{client&.corp_id}&container_type=work_platform&app_id=#{ENV['DINGTALK_NOTIFY_APP_CODE']}&redirect_type=jump&redirect_url=#{pc_url}",
          single_url: "taurus://taurusclient/action/open_app?type=1&offline=false&url=#{mobile_url}"
        }
      }

      begin
        msg = messages.create!(
          ai_message: ai_message_square,
          seq: seq,
          type: message_type,
          activity: self.activity,
          notifyable: self,
          content: content,
          user: user,
          sender: creator,
          body: message,
          payload: message_content
        )
      rescue => e
        Rails.logger.error "创建消息失败: #{e.message}"
        Rails.logger.error e.backtrace.join("\n")
      end
    rescue Exception => e
      Rails.logger.error "处理用户 #{user.id} 时出错: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
    end
  end

  private

  # 发送活动消息
  # @param [Serve::Activity] activity 活动对象
  # @param [Dingtalk::Client] client 钉钉客户端
  # @param [String] title 消息标题
  # @param [String] markdown 消息markdown内容
  # @param [String] single_title 单行标题
  def send_activity_message(activity, client, title, markdown, single_title)
    send_users.find_each do |user|
      if self.messages.find_by(user: user)
        next # 已经发送过消息了不在发送
      end

      seq = SecureRandom.hex(16)
      tanent = Tanent.find_by(id: rule&.options&.website_url_tanent_id) || current_tanent
      pc_url = CGI.escape(File.join(tanent&.config&.dig('WEBSITE_URL') || ENV['WEBSITE_URL'] || '', "/serve/home/<USER>/#{seq}"))
      mobile_url = CGI.escape(File.join(tanent&.config&.dig('WEBSITE_MOBILE_URL') || ENV['WEBSITE_MOBILE_URL'] || '', "/#/pages/serve/messages/show/index?seq=#{seq}"))

      # 获取活动内容
      activity_content = activity.content['content']&.first&.dig('body')

      # 构建消息内容
      message_title = title || '【廉洁提醒】'
      message_markdown = markdown || '您有一条廉洁提醒，请查收。'

      # 构建消息体
      message = {
        msgtype: 'action_card',
        action_card: {
          title: message_title,
          markdown: "#{message_markdown}",
          single_title: single_title,
          single_pc_url: "taurus://taurusclient/action/open_app?corpid=#{client.corp_id}&title=廉洁提醒&container_type=work_platform&ddtab=true&app_id=#{ENV['DINGTALK_NOTIFY_APP_CODE']}&redirect_type=jump&redirect_url=#{pc_url}",
          single_url: "taurus://taurusclient/action/open_app?type=1&offline=false&url=#{mobile_url}"
        }
      }

      # 创建消息记录
      begin
        messages.create!(
          seq: seq,
          type: message_type,
          activity: activity,
          notifyable: self,
          user: user,
          sender: creator,
          body: message,
          payload: { content: { title: message_title, markdown: message_markdown } }
        )
      rescue => e
        Rails.logger.error "创建活动消息失败: #{e.message}"
        Rails.logger.error e.backtrace.join("\n")
      end
    end
  rescue Exception => e
    Rails.logger.error "发送活动消息时出错: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
  end
end
