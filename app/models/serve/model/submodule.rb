module Serve::Model::Submodule
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    formable
    treeable
    acts_as_list scope: [:app_id, :ancestry]

    include Res::Ext::Permitable
    # 管理权限
    acts_as_permitable(permit_action: :manage, action_class_name: 'Serve::ManageAction')

    # association
    belongs_to :app
    has_many :banners,    dependent: :destroy
    has_many :catalogs,   dependent: :destroy
    has_many :tags,       dependent: :destroy
    has_many :activities, dependent: :destroy
    has_many :entries,    dependent: :destroy
    has_many :groups,     dependent: :destroy

    attribute :name, :string, comment: '名称'
    attribute :state, :string, comment: '状态'
    attribute :cover_image, :jsonb, comment: '封面图'
    attribute :position, :integer, comment: '排序'
    attribute :key, :string, comment: '程序内使用的标识'
    attribute :layout, :jsonb, comment: '布局方式配置'
  end

  class_methods do
  end
end
