module Serve::Model::Activity
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    include Serve::Model::Sourceable
    include Bpm::Ext::Flowable
    acts_as_flowable_resource only: [:create]

    formable
    stiable
    effectable
    acts_as_list scope: [:submodule_id]

    action_store(
      :relate,
      :catalog,
      class_name: 'Serve::Catalog',
      action_class_name: 'Serve::ActivityAction',
      alias_name: 'catalogs',
      inverse_alias_name: 'activities',
    )

    action_store(
      :relate,
      :tag,
      class_name: 'Serve::Tag',
      action_class_name: 'Serve::ActivityAction',
      alias_name: 'tags',
      inverse_alias_name: 'activities',
    )

    has_event :hot

    include Res::Ext::Permitable
    # 查看权限
    acts_as_permitable(permit_action: :view, action_class_name: 'Serve::PermitAction')
    # 参与使用权限
    acts_as_permitable(permit_action: :use, action_class_name: 'Serve::PermitAction')
    # 管理权限
    acts_as_permitable(permit_action: :manage, action_class_name: 'Serve::PermitAction')

    belongs_to :app
    belongs_to :submodule
    belongs_to :creator, class_name: '::User', optional: true
    belongs_to :target, polymorphic: true, optional: true
    # 具体关联的业务对象
    # 审批: Bpm::Workflow
    # 活动：Exp::Activity
    # 咨询：Notice::Record
    # 预约: Oper::Target
    has_many :entries, dependent: :nullify

    attribute :name, :string, comment: '名称'
    attribute :state, :string, comment: '状态'
    attribute :cover_image, :jsonb, comment: '封面图'
    attribute :icon, :jsonb, comment: 'icon'
    attribute :attachments, :jsonb, comment: '附件'
    attribute :position, :integer, comment: '排序'
    attribute :content, :jsonb, comment: '详情，body / images / video'
    attribute :address, :text, comment: '地址'
    attribute :published_at, :datetime, comment: '发布时间'
    # 给前端使用的
    attribute :layout, :jsonb, comment: '卡片样式'
    attribute :views_count, :integer, default: 0, comment: '浏览数量'

    ### 下个版本删除 ###
    delegate :department_names, to: :creator, allow_nil: true
    ### 下个版本删除 ###

    scope :visible, -> { where(create_instance_state: ['completed', nil]) }
    # 统计使用
    scope :manage_serve_activities, ->(user_id) { User.find(user_id).manage_serve_activities }

    scope :tags_id_contain, ->(*tag_ids) {
      joins(:tags).where(tags: { id: tag_ids })
                  .group('serve_activities.id')
                  .having('COUNT(DISTINCT tags.id) >= ?', tag_ids.size)
    }

    alias_method :check_permit!, :use_permitable_by_user?

    enum state: {
      pending: 'pending',
      published: 'published',
    }

    default_value_for(:app) { |o| o.submodule&.app }
    default_value_for(:name) { |o| o.source.try(:name) }
    default_value_for(:state) { 'pending' }
    default_value_for(:target) { |o| o.creator&.departments&.first }
    before_create :set_published_at

    def set_published_at
      self.published_at ||= created_at
    end

    def entry(user: nil)
      entries.order(id: :desc).find_by(user: user)
    end

    def use_serve_permit_action_names
      use_serve_permit_actions.map{ |action| action.target.try(:name) }.compact
    end

    def target_name
      target.try(:name)
    end

    def tag_names
      tags.pluck(:name)
    end
  end

  class_methods do
    def extra_view_attributes(mod)
      [
        :tag_ids,
        :catalog_ids,
        :tags,
        :tag_names,
        :catalogs,
        :department_names,
        :use_serve_permit_action_names,
        :target_name,
      ]
    end
  end
end
