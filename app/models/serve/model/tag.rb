module Serve::Model::Tag
  extend ActiveSupport::Concern

  included do
    self.track_migration = true
    acts_as_list
    # association
    belongs_to :app
    belongs_to :submodule

    # action_store关联，用于与Activity和Group建立多对多关系
    action_store_by(
      :relate,
      :tag,
      class_name: 'Serve::Activity',
      action_class_name: 'Serve::ActivityAction',
      alias_name: 'tags',
      inverse_alias_name: 'activities',
    )

    action_store_by(
      :relate,
      :tag,
      class_name: 'Serve::Group',
      action_class_name: 'Serve::ActivityAction',
      alias_name: 'tags',
      inverse_alias_name: 'groups',
    )

    # attribute
    attribute :name, :string, comment: '名称'
    attribute :color, :string, comment: '颜色'
    attribute :position, :integer, comment: '排序'
    attribute :option, :jsonb, comment: '配置项'
    attribute :activity_count, :integer, comment: '活动数量'

    default_value_for(:app) { |o| o.submodule&.app }

    def activities_count
      num = activities.count
      update_columns(activity_count: num) unless num == activity_count
      num
    end
  end

  class_methods do
  end
end
