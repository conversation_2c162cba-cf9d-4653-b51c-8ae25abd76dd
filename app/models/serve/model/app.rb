module Serve::Model::App
  extend ActiveSupport::Concern

  included do
    has_many :serve_submodules, class_name: 'Serve::Submodule', dependent: :destroy
    has_many :serve_catalogs, class_name: 'Serve::Catalog', dependent: :destroy
    has_many :serve_tags, class_name: 'Serve::Tag', dependent: :destroy
    has_many :serve_banners, class_name: 'Serve::Banner', dependent: :destroy
    has_many :serve_activities, class_name: 'Serve::Activity', dependent: :destroy
    has_many :serve_entries, class_name: 'Serve::Entry', dependent: :destroy
  end

  class_methods do
  end
end
