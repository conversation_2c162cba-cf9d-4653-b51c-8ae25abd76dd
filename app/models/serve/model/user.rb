module Serve::Model::User
  extend ActiveSupport::Concern

  included do
    # 普通用户
    has_many :serve_entries, class_name: 'Serve::Entry', dependent: :destroy
    has_many :serve_activities, through: :serve_entries, source: :activity, class_name: 'Serve::Activity'

    # 可以管理的submodule
    def manage_serve_submodules
      has_role?(:serve_admin) ? app.serve_submodules : app.serve_submodules.manage_by_user(self)
    end

    # 用户权益
    def serve_permit_actions(only: [:user, :member_identity, :duty, :role, :org, :department], submodule_id: nil)
      permit_array = relate_permit_resources(only: only)
      actions = permit_array.inject(Serve::PermitAction.none) do |collection, target|
                  collection.or(Serve::PermitAction.where(target: target, action_type: 'view'))
                end
      # 活动如果有类别则跳过
      submodule_id ||= ENV.fetch('SERVE_SUBMODULE_ID', 2)
      catalog_ids =  Serve::Catalog.where(submodule_id: submodule_id).pluck(:id)
      activity_ids = Serve::Activity.published.where(submodule_id: submodule_id).pluck(:id)

      action_ids = actions.where(user_type: 'Serve::Catalog', user_id: catalog_ids).pluck(:id)
      action_ids.concat actions.ransack(
                          user_type_eq: 'Serve::Activity',
                          activity_catalogs_id_null: 1,
                          user_id_in: activity_ids
                        ).result.pluck(:id)
      Serve::PermitAction.where(id: action_ids)
    end

    # 管理员活动
    def manage_serve_activities
      has_role?(:serve_admin) ?
        app.serve_activities :
        app.serve_activities.manage_by_user(self)
    end

    # 管理员预约
    def manage_serve_entries
      has_role?(:serve_admin) ?
        app.serve_entries :
        app.serve_entries.where(activity_id: manage_serve_activities.pluck(:id))
    end
  end

  class_methods do
  end
end
