module Serve::Model::Pack
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    broadcasts

    include Bpm::Ext::Flowable
    acts_as_flowable_resource
    acts_as_list scope: [:app_id]

    stiable

    # association
    belongs_to :app
    belongs_to :tanent,   optional: true, class_name: '::Tanent'
    belongs_to :rule,     optional: true
    belongs_to :activity, optional: true
    belongs_to :creator,  optional: true, class_name: '::User'
    belongs_to :org,      optional: true, class_name: '::Org'
    belongs_to :rule_record, optional: true
    belongs_to :source,  polymorphic: true,  optional: true
    belongs_to :rule_item,   optional: true

    has_many :messages, dependent: :nullify
    has_many :ai_messages, dependent: :nullify
    has_many :ai_message_squares,   dependent: :nullify
    has_many :ai_message_templates, dependent: :nullify
    has_many :receivers, dependent: :destroy
    has_many :send_users, through: :receivers, source: :user

    # attribute
    attribute :name,       :string,   comment: '名称'
    attribute :state,      :string,   comment: '状态'
    attribute :seq,        :string,   comment: '标识'
    attribute :flag,       :string,   comment: '标识'
    attribute :period,     :integer,  comment: '周期'
    attribute :operate_at, :datetime, comment: '操作时间'
    attribute :send_at,    :datetime, comment: '发送时间'
    attribute :position,   :integer,  comment: '排序'
    attribute :payload,    :jsonb,    comment: '其他信息'
    attribute :option,     :jsonb,    comment: '配置信息'
    attribute :message_type, :string, comment: '消息类型'
    attribute :content,     :text,    comment: '消息内容'

    enum state: { pending: 'pending', sending: 'sending', finished: 'finished', terminated: 'terminated' }

    default_value_for(:app) { |o| o.rule&.app }
    default_value_for(:state) { 'pending' }
    default_value_for(:org) { |o| o.creator&.orgs&.first }
    default_value_for(:message_type) { 'Serve::DingtalkMessage' }

    # 生化默认的工作流
    after_create :generate_bpm_instance!, if: :require_generate_bpm_instance?
    after_update :reset_rule_latest_send_at!, if: :require_reset_rule_latest_send_at?

    after_update :generate_schedule_pack!, if: :require_generate_schedule_pack?

    # 自动关联租户
    after_create :ensure_tanent_association

    delegate :name, to: :org, allow_nil: true, prefix: true
    delegate :name, to: :activity, allow_nil: true, prefix: true
    delegate :name, to: :rule, allow_nil: true, prefix: true

    action_store(
      :relate_tanent,
      :tanent,
      class_name: '::Tanent',
      action_class_name: 'Serve::TanentAction',
      alias_name: 'tanents',
      inverse_alias_name: 'serve_packs',
    )

    before_destroy :revoke_dingtalk_messages

    # 使用规则刷新候选消息
    def refresh_contents_by_rule(prompt: nil, mode: nil)
      start_time = Time.current
      deepseek_mode = rule.get_deepseek_mode_key(mode)

      Rails.logger.info "开始AI内容生成: #{prompt}，使用DeepSeek模式: #{deepseek_mode}"

      begin
        # 使用ai生成内容，传递mode参数
        contents = rule.generate_content_by_template_prompt(prompt: prompt, mode: mode)

        # 记录耗时
        duration = Time.current - start_time
        Rails.logger.info "AI内容生成完成，耗时: #{duration.round(2)}秒"

        # 获取模式信息
        mode_info = rule.get_deepseek_mode_info_by_key(deepseek_mode)

        # 更新payload，包含DeepSeek模式信息
        _payload = self.payload
        _payload['contents'] = contents
        _payload['deepseek_mode_info'] = mode_info
        _payload['generated_at'] = Time.current.iso8601
        _payload['generation_time'] = duration.round(2)
        update(payload: _payload)

        contents
      rescue => e
        Rails.logger.error "AI内容生成失败: #{e.message}"
        raise e
      end
    end

    def current_tanent
      Tanent.current || tanents.last || rule&.tanents&.last
    end

    scope :current_tanent, -> { Tanent.current ? relate_tanent_action_targets_any(Tanent.current) : all }

    scope :sreen_by_period, ->(period = 10, unit = 'week') {
      result, date = [], Date.today
      period.times do |i|
        start_time = i.try(unit).ago(date.to_datetime).try("beginning_of_#{unit}")
        end_time = i.try(unit).ago(date.to_datetime).try("end_of_#{unit}")
        result.push({
          cweek: start_time.to_date.cweek,
          start_day: start_time.to_date.strftime('%F'),
          end_of_day: end_time.to_date.strftime('%F'),
          pack_count: between_times(start_time, end_time).count
        })
      end
      result
    }

    def send_user_ids_redis_key=(redis_key)
      begin
        self.send_user_ids = ::RedisResources.get_ids_from_redis_key(redis_key)
        self.payload ||= {}
        self.payload['send_user_ids_redis_key'] = redis_key
      rescue => e
        Rails.logger.error "===send_user_ids_redis_key_error===#{e.message}"
      end
    end

    # 保留 send_user_ids_redis_key 避免被直接赋值覆盖导致 key 消失
    def payload=(value)
      super({ **(value || {}), send_user_ids_redis_key: self.payload&.[]('send_user_ids_redis_key') })
    end

    def send_user_ids_redis_key_refresh=(redis_key)
      self.send_user_ids_redis_key = redis_key
    end

    def send_user_ids_redis_key
      ::RedisResources.new(resources: self.send_users).redis_key
    end

    # 根据小项配置设置工作流
    def specific_create_workflow
      return nil unless require_generate_bpm_instance?
      workflow_id = nil
      workflow_id = rule.options.bpm.workflow_id if rule && rule.require_generate_instance?
      workflow_id = rule_item.option.bpm.workflow_id if rule_item && rule_item.require_generate_instance?
      Bpm::Workflow.find_by(id: workflow_id || ModelDefine.find_by(klass: self.class.name).model_setting_by_setable(self, flag: 'create_by_code')&.bpm_workflow_id)
    end

    def ref_model_setting_form_setting
      return nil unless rule_item
      bpm = rule_item.option.bpm
      bpm&.enabled && bpm.generate_instance && bpm.workflow_id ?
        Forms::Attr::FormSetting.init :
        (ModelDefine.find_by(klass: self.class.name).model_setting_by_setable(self, flag: 'create_by_code')&.form_setting || Forms::Attr::FormSetting.init)
    end

    def generate_bpm_instance!
      generate_create_instance(
        user: creator,
        auto_submit: true
      )
    rescue Exception => e
      Rails.logger.info e
    end

    def read_count
      messages.where(is_read: true).count
    end

    def unread_count
      messages.where(is_read: [nil, false]).count
    end

    def failed_count
      messages.where(state: 'failed').count
    end

    # 统计发送人员人数
    def send_users_count
      user_ids = payload&.dig('user_ids') || send_users || []
      user_ids.count
    end

    # 统计最后一次发送时间
    def reset_rule_latest_send_at!
      rule.update(latest_send_at: send_at)
    end

    def require_reset_rule_latest_send_at?
      saved_change_to_state? && finished? && rule
    end

    # 是否需要生成审批流
    def require_generate_bpm_instance?
      # return unless Rails.env.production?
      return true if rule.blank?
      rule_item ? rule_item.require_generate_instance? : rule.require_generate_instance?
    end

    # 是否审批后一次性生成任务
    def require_generate_schedule_pack?
      rule_record && rule_item&.is_a?(Serve::ScheduleRuleItem) && saved_change_to_state?
    end

    # 生成pack
    def generate_schedule_pack!
      rule_item.generate_schedule_pack! pack: self
    end

    # 生成浙政钉任务
    def generate_serve_message!
      user_ids = payload&.dig('user_ids') || []
      client = Dingtalk::Client.find_by(code: ENV['DINGTALK_NOTIFY_APP_CODE'])
      title =  payload&.dig('message', 'title')
      markdown = payload&.dig('message', 'markdown')
      single_title = payload&.dig('message', 'single_title') ||  '查看详情'

      app.users.where(id: user_ids).find_each do |user|
        next if self.messages.find_by(user: user) # 已经发送过消息了不在发送
        seq = SecureRandom.hex(16)
        tanent = Tanent.find_by(id: rule&.options&.website_url_tanent_id) || current_tanent
        pc_url = CGI.escape(File.join(tanent&.config&.dig('WEBSITE_URL') || ENV['WEBSITE_URL'], "/serve/home/<USER>/#{seq}"))
        mobile_url = CGI.escape(File.join(tanent&.config&.dig('WEBSITE_MOBILE_URL') || ENV['WEBSITE_MOBILE_URL'], "/#/pages/serve/messages/show/index?seq=#{seq}"))
        messsage_content = {}

        if rule.options&.message_template_enabled
          message_templates = rule.message_templates.used
          filtered_templates = message_templates.filter { |m| m.option.item_ids.blank? || rule_item_id.in?(m.option.item_ids) }

          # 获取真正的内容模板（ContentTemplate）
          content_templates = []
          filtered_templates.each do |message_template|
            if message_template.option&.content&.enabled && message_template.option.content.templates.present?
              used_content_templates = message_template.option.content.templates.select { |ct| ct.state == 'used' }
              content_templates.concat(used_content_templates)
            end
          end

          if content_templates.any?
            # 简化的内容模板去重逻辑：选择与上次不同的内容模板
            selected_content_template = select_different_content_template(content_templates, user)

            if selected_content_template
              # 使用MessageTemplate的get_message方法，但基于选中的ContentTemplate
              parent_message_template = filtered_templates.first
              template_message = parent_message_template.get_message(user: user, source: source)

              message_title = title || template_message.dig(:card, :title) || '清AI'
              message_markdown = markdown || template_message.dig(:card, :content) || '您有一条廉洁提醒，请查收。'

              # 简单去重：检查内容是否与上一条消息相同
              if content_same_as_last_message?(user, message_title, message_markdown)
                next # 内容相同，跳过发送
              end
            else
              Rails.logger.warn "没有可用的内容模板: 规则=#{rule.id}, 用户=#{user.id}"
              next # 跳过此用户
            end
          else
            Rails.logger.warn "没有启用的内容模板: 规则=#{rule.id}, 用户=#{user.id}"
            next # 跳过此用户
          end
        else
          ### 下个版本废除 ####
          message_title =  title || (rule_item ? rule_item.template_message_title : rule&.options&.template_message_title) || '【廉洁提醒】'
          # 如果使用了模版消息内容， 优先使用模版消息内容
          message_markdown = markdown || rule_item&.get_card_content(user: user, source: source) || rule&.get_card_content(user: user, source: source) || '您有一条廉洁提醒，请查收。'

          content = self.content || rule_item&.get_content(user: user, source: source) || rule&.get_content(user: user, source: source)
        end

        message = {
          msgtype: 'action_card',
          action_card: {
            title: message_title,
            markdown: message_markdown,
            single_title: single_title,
            single_pc_url: "taurus://taurusclient/action/open_app?corpid=#{client.corp_id}&container_type=work_platform&app_id=#{ENV['DINGTALK_NOTIFY_APP_CODE']}&redirect_type=jump&redirect_url=#{pc_url}",
            single_url: "taurus://taurusclient/action/open_app?type=1&offline=false&url=#{mobile_url}"
          }
        }

        # 生成完整的消息内容payload
        message_content = if rule.options&.message_template_enabled && defined?(selected_content_template) && selected_content_template
          # 使用MessageTemplate的get_message方法生成完整消息内容
          parent_message_template = filtered_templates.first
          full_message_content = parent_message_template.get_message(user: user, source: source)
        else
          # 传统方式生成消息内容
          {}
        end

        # 生成简化的content_uuid用于去重
        simple_content_uuid = if rule.options&.message_template_enabled && defined?(selected_content_template) && selected_content_template
          "simple_#{selected_content_template.attributes['_id']}_#{user.id}"
        else
          nil
        end

        messages.create!(
          seq: seq,
          type: message_type,
          activity: self.activity,
          notifyable: self,
          content: content,
          user: user,
          sender: creator,
          body: message,
          payload: message_content,
          content_uuid: simple_content_uuid
        )

      rescue Exception => e
        Rails.logger.error "===error_generate_message===#{user.id}"
        Rails.logger.error "Error details: #{e.class}: #{e.message}"
        Rails.logger.error "Backtrace: #{e.backtrace.first(10).join("\n")}"
        Rails.logger.error "User: #{user.inspect}"
        Rails.logger.error "Pack: #{self.id}, Rule: #{rule&.id}, Source: #{source&.class}##{source&.id}"
      end
    end

    def revoke_dingtalk_messages
      messages.each do |message|
        if message.respond_to?(:revoke_dingtalk_messages)
          # 调用Message的revoke_dingtalk_messages方法
          message.send(:revoke_dingtalk_messages)
        end
      end
    end

    # 撤回并销毁指定的消息
    # @param message_ids [Array<Integer>] 要撤回并销毁的消息ID数组
    # @return [Integer] 成功销毁的消息数量
    def revoke_and_destroy_messages(message_ids)
      return 0 if message_ids.blank?

      target_messages = messages.where(id: message_ids)
      destroyed_count = 0

      target_messages.each do |message|
        if message.respond_to?(:revoke_dingtalk_messages)
          # 调用Message的revoke_dingtalk_messages方法
          message.send(:revoke_dingtalk_messages)
        end

        if message.destroy
          destroyed_count += 1
        end
      end

      destroyed_count
    end

    def generate_ai_message!
      ai_message = Serve::AiMessage.new(
        creator: creator,
        pack: self,
        content: content,
        option: option,
      )
    end

    private

    # 确保Pack有租户关联
    def ensure_tanent_association
      # 检查是否已有租户关联
      return if has_tanent_association?

      # 确定目标租户（可能是多个）
      target_tanents = determine_target_tanents

      if target_tanents.any?
        Rails.logger.info "为Pack #{id} 自动关联租户: #{target_tanents.map(&:name).join(', ')}"

        target_tanents.each do |tanent|
          begin
            Serve::TanentAction.find_or_create_by(
              target_type: 'Serve::Pack',
              target_id: id,
              user_type: 'Tanent',
              user_id: tanent.id,
              action_type: 'relate_tanent'
            ) do |action|
              action.app_id = app_id
            end

            Rails.logger.info "Pack #{id} 成功关联到租户 #{tanent.name}"
          rescue => e
            Rails.logger.error "Pack #{id} 关联租户 #{tanent.name} 失败: #{e.message}"
          end
        end
      else
        Rails.logger.warn "Pack #{id} 无法确定目标租户，跳过自动关联"
      end
    end

    # 检查Pack是否已有租户关联
    def has_tanent_association?
      Serve::TanentAction.exists?(
        target_type: 'Serve::Pack',
        target_id: id,
        action_type: 'relate_tanent'
      )
    end

    # 确定目标租户的优先级逻辑（支持多租户）
    def determine_target_tanents
      # 优先级1: 当前租户上下文
      return [Tanent.current].compact if Tanent.current

      # 优先级2: 检查是否为多租户Rule
      if rule_id.present?
        multi_tanent_rules = get_multi_tanent_rule_config
        if multi_tanent_rules.key?(rule_id)
          tanent_codes = multi_tanent_rules[rule_id]
          tanents = tanent_codes.map { |code| Tanent.find_by(code: code) }.compact
          return tanents if tanents.any?
        end
      end

      # 优先级3: 通过App确定默认租户
      if app_id.present?
        # 对于app_id = 1，默认关联到IEST租户
        if app_id == 1
          iest_tanent = Tanent.find_by(code: 'IEST')
          return [iest_tanent].compact
        end

        # 对于其他app，可以根据需要添加逻辑
        # 例如：app_id = 2 关联到BID租户
        # if app_id == 2
        #   bid_tanent = Tanent.find_by(code: 'BID')
        #   return [bid_tanent].compact
        # end
      end

      # 优先级4: 系统默认租户（IEST）
      iest_tanent = Tanent.find_by(code: 'IEST')
      [iest_tanent].compact
    end

    # 获取多租户Rule配置
    # 返回格式: { rule_id => ['TANENT_CODE1', 'TANENT_CODE2'] }
    def get_multi_tanent_rule_config
      @multi_tenant_config ||= begin
        config = Rails.application.config_for(:multi_tenant_rules)
        # 将字符串键转换为整数键以匹配rule_id类型
        config['multi_tenant_rules'].transform_keys(&:to_i)
      rescue => e
        Rails.logger.warn "无法加载多租户规则配置: #{e.message}，使用默认配置"
        # 兜底配置
        {
          37 => ['IEST', 'BID'],  # 评标专家确定
          38 => ['IEST', 'BID'],  # 招标项目登记
          39 => ['IEST', 'BID'],  # 施工许可证办理
          44 => ['IEST', 'BID'],  # 立项
          66 => ['IEST', 'BID'],  # 项目变更申请
          67 => ['IEST', 'BID'],  # 业主评标专家确定
          385 => ['IEST', 'BID']  # 开标
        }
      end
    end

    private



    # 传统的选择唯一消息模板方法（保持向后兼容）
    # @param templates [Array] 可用的消息模板数组
    # @param user [User] 目标用户
    # @return [MessageTemplate] 选中的消息模板
    def select_unique_message_template(templates, user)
      return templates.sample if templates.empty? || templates.size == 1

      # 查询该用户在当前规则下已收到的消息模板ID
      used_template_ids = get_user_used_template_ids(user)

      # 过滤掉已使用的模板
      available_templates = templates.reject { |template| used_template_ids.include?(template.id) }

      # 如果还有未使用的模板，随机选择一个
      if available_templates.any?
        return available_templates.sample
      end

      # 如果所有模板都用过了，至少确保与上次不同
      last_template_id = get_user_last_template_id(user)
      different_templates = templates.reject { |template| template.id == last_template_id }

      # 如果有不同的模板，选择一个；否则随机选择（兜底）
      different_templates.any? ? different_templates.sample : templates.sample
    end

    # 获取用户在当前规则下已使用的模板ID列表
    # @param user [User] 目标用户
    # @return [Array<Integer>] 已使用的模板ID数组
    def get_user_used_template_ids(user)
      # 查询该用户在当前规则下的历史消息
      user_messages = app.serve_messages
                         .joins(:pack)
                         .where(user: user, serve_packs: { rule_id: rule.id })
                         .where.not(payload: nil)

      # 提取payload中的template_id
      template_ids = []
      user_messages.find_each do |message|
        template_id = message.payload&.dig('template_id')
        template_ids << template_id if template_id.present?
      end

      template_ids.uniq
    end

    # 获取用户最后一次收到消息使用的模板ID
    # @param user [User] 目标用户
    # @return [Integer, nil] 最后使用的模板ID
    def get_user_last_template_id(user)
      last_message = app.serve_messages
                        .joins(:pack)
                        .where(user: user, serve_packs: { rule_id: rule.id })
                        .where.not(payload: nil)
                        .order(created_at: :desc)
                        .first

      last_message&.payload&.dig('template_id')
    end



    # 简化的去重方案辅助方法

    # 简化的去重方案：选择与上次不同的内容模板
    def select_different_content_template(content_templates, user)
      return content_templates.sample if content_templates.empty? || content_templates.size == 1

      # 获取用户最后使用的内容模板ID
      last_content_template_id = get_user_last_content_template_id(user)

      # 优先选择与上次不同的内容模板
      available_templates = content_templates.reject { |template| template.attributes['_id'] == last_content_template_id }

      # 如果有不同的模板，选择一个；否则从所有模板中选择
      if available_templates.any?
        available_templates.sample
      else
        content_templates.sample
      end
    end

    # 获取用户最后使用的内容模板ID
    def get_user_last_content_template_id(user)
      last_message = app.serve_messages
                        .joins(:pack)
                        .where(user: user, serve_packs: { rule_id: rule.id })
                        .order(created_at: :desc)
                        .first

      return nil unless last_message&.payload

      # 从payload中提取内容模板ID
      last_message.payload.dig('content_template_id') ||
      last_message.payload.dig('template_id') ||  # 兼容旧格式
      last_message.payload.dig('metadata', 'template_id')
    end

    # 简单内容去重：检查内容是否与上一条消息相同
    def content_same_as_last_message?(user, title, markdown)
      last_message = app.serve_messages
                        .joins(:pack)
                        .where(user: user, serve_packs: { rule_id: rule.id })
                        .order(created_at: :desc)
                        .first

      return false unless last_message&.body

      # 比较标题和内容
      last_title = last_message.body.dig('action_card', 'title')
      last_markdown = last_message.body.dig('action_card', 'markdown')

      title == last_title && markdown == last_markdown
    end
  end
end
