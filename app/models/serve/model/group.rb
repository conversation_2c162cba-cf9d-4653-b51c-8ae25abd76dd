module Serve::Model::Group
  extend ActiveSupport::Concern

  included do
    self.track_migration = true
    stiable
    formable

    # association
    belongs_to :app
    belongs_to :submodule, optional: true

    acts_as_list scope: [:submodule_id]

    action_store(
      :relate,
      :catalog,
      class_name: 'Serve::Catalog',
      action_class_name: 'Serve::ActivityAction',
      alias_name: 'catalogs',
      inverse_alias_name: 'groups',
    )

    action_store(
      :relate,
      :tag,
      class_name: 'Serve::Tag',
      action_class_name: 'Serve::ActivityAction',
      alias_name: 'tags',
      inverse_alias_name: 'groups',
    )

    # attribute
    attribute :name, :string, comment: '名称'
    attribute :state, :string, comment: '状态'
    attribute :position, :integer, comment: '排序'

    default_value_for(:app) { |o| o.submodule&.app }

    def tags
      super.order(position: :asc)
    end
  end

  class_methods do
    def extra_view_attributes(mod)
      [
        :tag_ids,
        :tags,
      ]
    end
  end
end