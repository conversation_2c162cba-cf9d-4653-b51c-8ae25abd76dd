module Serve::Model::Entry
  extend ActiveSupport::Concern

  included do
    include Serve::Model::Sourceable

    self.track_migration = true

    formable
    stiable

    belongs_to :app
    belongs_to :submodule
    belongs_to :activity
    # 具体关联的业务对象
    # 审批: Bpm::Instance
    # 活动：Exp::Entry
    # 预约: Oper::Task
    belongs_to :user, class_name: '::User'
    # 方便搜索,搜索的时候必须带上type
    with_options foreign_key: :source_id, optional: true do
      belongs_to :oper_task,  class_name: 'Oper::Task'
      belongs_to :exp_entry,  class_name: 'Exp::Entry'
      belongs_to :bpm_instance, class_name: 'Bpm::Instance'
    end

    attribute :state,     :string,   comment: '状态'
    attribute :order_at,  :datetime, comment: '报名时间'

    enum state: { pending: 'pending', registered: 'registered', canceled: 'canceled' }

    scope :manage_serve_entries, ->(user_id) {
      user = User.find(user_id)
      user.manage_serve_entries
    }

    default_value_for(:app) { |o| o.activity&.app }
    default_value_for(:submodule) { |o| o.activity&.submodule }
    default_value_for(:state) { 'pending' }

    alias_method :reg_record_source, :activity
  end

  class_methods do
  end
end
