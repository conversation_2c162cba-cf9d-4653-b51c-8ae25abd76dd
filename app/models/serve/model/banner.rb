module Serve::Model::Banner
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    formable
    effectable
    acts_as_list scope: [:source_type, :source_id, :app_id]
    has_event :publish

    belongs_to :app
    belongs_to :submodule, optional: true
    belongs_to :source, polymorphic: true, optional: true

    attribute :name, :string, comment: '轮播图名称'
    attribute :source_type, :string
    attribute :position, :integer, comment: '位置'
    attribute :cover_image, :jsonb, comment: '封面图'
    attribute :conf, :jsonb, comment: '呈现方式，根据前端需要设置，例如可以是PC或者Mobile的展现等'

    default_scope { order(position: :asc) }
    default_value_for(:app) { |o| o.submodule&.app }

    def source
      super rescue nil
    end

    def can_redirect?(user)
      source&.respond_to?(:view_permitable_by_user?) ?
        source.view_permitable_by_user?(user) :
        true
    end
  end

  class_methods do
  end
end
