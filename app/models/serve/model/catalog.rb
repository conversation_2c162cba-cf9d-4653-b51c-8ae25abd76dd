module Serve::Model::Catalog
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    formable
    acts_as_list scope: [:submodule_id]

    action_store_by(
      :relate,
      :catalog,
      class_name: 'Serve::Activity',
      action_class_name: 'Serve::ActivityAction',
      alias_name: 'catalogs',
      inverse_alias_name: 'activities',
    )

    include Res::Ext::Permitable
    # 查看权限
    acts_as_permitable(permit_action: :view, action_class_name: 'Serve::PermitAction')
    # 参与使用权限
    acts_as_permitable(permit_action: :use, action_class_name: 'Serve::PermitAction')
    # 管理权限
    acts_as_permitable(permit_action: :manage, action_class_name: 'Serve::PermitAction')

    belongs_to :app
    belongs_to :submodule
    belongs_to :creator, class_name: '::User', optional: true
    belongs_to :target, polymorphic: true, optional: true

    has_many :tags, -> { distinct }, through: :activities, source: :tags

    attribute :name, :string, comment: '名称'
    attribute :state, :string, comment: '状态'
    attribute :cover_image, :jsonb, comment: '封面图'
    attribute :icon, :jsonb, comment: 'icon'
    attribute :position, :integer, comment: '排序'
    attribute :layout, :jsonb, comment: '布局方式配置'

    default_value_for(:app) { |o| o.submodule&.app }
    default_value_for(:target) { |o| o.creator&.departments&.first }
    ### 下个版本删除 ###
    delegate :department_names, to: :creator, allow_nil: true
    ### 下个版本删除 ###

    def target_name
      target.try(:name)
    end
  end

  class_methods do
    def extra_view_attributes(mod)
      [:department_names, :target_name, :tags]
    end
  end
end
