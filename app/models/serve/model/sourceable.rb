module Serve::Model::Sourceable
  extend ActiveSupport::Concern

  included do
    belongs_to :source, polymorphic: true, optional: true, autosave: true

    attribute :name, :string, comment: '名称'

    default_value_for(:name) { |o| o.source.try(:name) }

    # 由子类进行继承
    def source_type
      nil
    end

    def source_info=(attributes)
      _source = source || (source_type.present? ? source_type.constantize.new : nil)
      return unless _source.present?

      _source.ta_assign_attributes(attributes: attributes)

      if respond_to?(:assign_default_source_info_attributes)
        _source.assign_attributes(assign_default_source_info_attributes)
      end

      self.source = _source
    end

    def source_info(**options)
      source&.as_jbuilder_json(**options) || source&.as_json
    end
  end

  class_methods do
  end
end
