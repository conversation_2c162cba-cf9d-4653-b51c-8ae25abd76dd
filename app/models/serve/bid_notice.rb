# tenderbulletincode	招标公告编号
# tenderdocdeadline	招标文件获取截止时间
# tenderdocgetstarttime	招标文件获取开始时间
# syndicatedflag	是否允许联合体投标
# tendermode	招标方式
# bulletinissuetime	公告发布时间
# nengliyaoqiu	投标人资格能力要求
# regioncode	招标公告所在行政区域代码
# qual_type	评审办法
# bidopentime	开标时间
# bidsectioncodes	标段编号
# tenderguimo	招标规模
# tenderrange	招标范围
# tendercontents	招标内容
# tendertiaojian	招标条件
# bulletinname	公告标题
# projectname	项目名称
# tenderprojectcode	招标项目编号
# platformname	交易平台名称
# bidsectionamount	标段（包）招标估算金额
# platformcode	交易平台标识码
# projectcode	项目编号

# 招标公告
class Serve::BidNotice < ApplicationRecord

  self.track_migration = true
  seqable
  effectable(effective_at: "start_at", invalid_at: "end_at")

  belongs_to :app
  belongs_to :bid_project, optional: true

  attribute :code,         :string, comment: "项目编号"
  attribute :state,        :string, commen: "状态"
  attribute :open_at,      :datetime, comment: "开标时间"
  attribute :amount,       :decimal, scale: 5, precision: 20, comment: "金额"
  attribute :payload,      :jsonb, comment: "扩展字段"

  after_commit :async_bid_project!, only: [:create, :update], if: :bid_project

  def zbgg
    Biz::Zbgg.find_by tenderbulletincode: seq
  end

  # 通过招标公告更新招标项目信息
  def async_bid_project!
    project = bid_project
    project.state = "opening" unless project.state.to_s.in?(["finished", "closed"])
    project.async!
  end

  # 同步数据使用
  def self.async_all!(app: App.first, time: nil)
    time ||= Date.yesterday.strftime("%F")
    Biz::Zbgg.ransack(load_time_gt: time).result.order(load_time: :asc).find_each do |zbgg|
      notice = Serve::BidNotice.find_or_initialize_by(seq: zbgg.tenderbulletincode)
      notice.update(
        app: app,
        bid_project: Serve::BidProject.find_by(code: zbgg.projectcode, app: app),
        code: zbgg.projectcode,
        open_at: zbgg.bidOpenTime,
        start_at: zbgg.tenderDocGetStartTime,
        end_at: zbgg.tenderDocDeadLine,
        payload: zbgg.as_json,
        updated_at: zbgg.load_time
      )
    rescue Exception
      next
    end
  end

end
