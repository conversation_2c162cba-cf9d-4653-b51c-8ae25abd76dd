class Serve::DutyChange < ApplicationRecord

  self.track_migration = true
  include Bpm::Ext::Flowable
  acts_as_flowable_resource

  # 关联关系
  belongs_to :app
  belongs_to :user, class_name: "::User"
  belongs_to :department, optional: true
  belongs_to :original_department, class_name: "Department", optional: true

  # 属性定义
  attribute :duty, :string, comment: "当前的岗位"
  attribute :duty_level, :string, comment: "当前职级"
  attribute :original_duty, :string, comment: "之前的岗位"
  attribute :original_duty_level, :string, comment: "之前职级"
  attribute :change_date, :datetime, comment: "变更时间"
  attribute :reason, :string, comment: "原因"
  attribute :payload, :jsonb, comment: "扩展字段"
  attribute :state, :string, comment: "状态"

  # 枚举和默认值
  enum :state, { pending: "pending", finished: "finished", closed: "closed" }
  default_value_for(:state) { "pending" }
  default_value_for(:change_date) { Time.current }

  scope :rule_current_record, lambda { |org_name = nil, days_ago = 7|
    # 改用指定日期前后的时间范围
    reference_date = Date.current
    start_time = (reference_date - days_ago.to_i.days).beginning_of_day
    end_time = reference_date.end_of_day

    # 优化：使用正确的关联名称和JSONB查询
    valid_user_ids = User
      .joins(:members)
      .where(members: { type: "DingtalkMember" })
      .where.not(members: { model_payload: nil }) # 确保model_payload不为空
      .where("members.model_payload->'govEmployeePosition' IS NOT NULL") # 确保govEmployeePosition存在
      .where("members.model_payload->'govEmployeePosition'->>'gmtCreate' IS NOT NULL") # 确保gmtCreate存在
      .where("members.model_payload->'govEmployeePosition'->>'gmtCreate' >= ?", start_time.strftime("%Y-%m-%d %H:%M:%S"))
      .where("members.model_payload->'govEmployeePosition'->>'gmtCreate' <= ?", end_time.strftime("%Y-%m-%d %H:%M:%S"))
      .pluck(:id)

    # 使用子查询优化性能
    records = where(user_id: valid_user_ids)
      .ransack(user_orgs_name_eq: org_name, state_eq: "pending")
      .result

    # 使用一次查询获取需要排除的记录
    where(id: records.where.not(id: Serve::Pack.where(source: records).select(:source_id)))
  }

  # 职务变动后，根据选择的原因，关联到不同的规则中
  def self.sync_duty_change_reason(token:)
    instance = token.instance
    duty_change = find_by(id: instance.flowable&.source&.id)
    return unless duty_change

    duty_change.update(state: "finished")

    pack = instance.flowable
    form_payload = token.data_forms&.first&.payload
    pack_payload = pack.payload
    pack_payload["use_custom"] = form_payload["use_custom"] if form_payload.key?("use_custom")
    pack.update(
      state: "sending",
      send_at: form_payload&.dig("send_at"),
      content: form_payload&.dig("content"),
      activity_id: form_payload&.dig("activity_id"),
      payload: pack_payload
    )
    reason = form_payload&.dig("reason")
    rule = Serve::Rule.find_by(name: reason)
    pack.update(rule: rule) if rule && (rule.id != pack.rule_id)
    # 如果没有人和设置，则更新为finished，就不发送消息了
    return if (pack.payload["use_custom"] && pack.content) || pack.payload["activity_ids"].size.positive?

    pack.update(state: "finished")
  end

  ### 以下均用于发送模版消息 #############
  # 项目联系人
  def rule_contactor_ids
    [user_id]
  end

end
