class Serve::Birthday < ApplicationRecord

  self.track_migration = true

  belongs_to :app
  belongs_to :org, class_name: "::Org", optional: true

  has_many :packs, as: :source

  attribute :name, :string, comment: "标题"
  attribute :payload, :jsonb, comment: "扩展字段"
  attribute :start_at, :date, comment: "开始时间"
  attribute :end_at, :date, comment: "结束时间"

  scope :find_record_by_week, lambda { |week = "now", name = "生日"|
    start_date, end_date = date_range_for_week(week)
    where(name: name).where("start_at >= ? AND end_at <= ?", start_date, end_date)
  }

  def self.date_range_for_week(week)
    case week
    when "now"
      [Date.current.beginning_of_week, Date.current.end_of_week]
    when "next"
      [Date.current.next_week.beginning_of_week, Date.current.next_week.end_of_week]
    else
      raise ArgumentError, "Invalid argument: expected 'now' or 'next'"
    end
  end

  def self.find_date_field_for_week(week = "now", field = "birthday")
    start_date, end_date = date_range_for_week(week)
    [User.find_date_field_within(nil, start_date.to_s, end_date.to_s, field), start_date, end_date]
  end

  def self.find_and_create_record(week = "now", field = "birthday", org_name = "滨江区")
    users, start_date, end_date = find_date_field_for_week(week, field)
    org = Org.find_by(name: org_name)
    record = find_or_initialize_by(
      app_id: App.first.id,
      org: org,
      name: field == "birthday" ? "生日" : "政治生日",
      start_at: start_date,
      end_at: end_date
    )
    record.payload = users.each_with_object({}) do |user, payload|
      payload[user.name] = user.str_date_field(field)
    end
    record.save
  end

  def self.find_records_by_week(week = "now", name = "生日")
    start_date, end_date = date_range_for_week(week)
    where(name: name, start_at: start_date, end_at: end_date)
  end

  ################ 以下方法用于规则表单内的方法 ################
  def birthday_person_count
    payload&.size
  end

  def send_to_user_ids(*user_ids)
    user_ids
  end

  def message_for_users
    # Define a method to parse the date string from the payload
    def parse_date(date_str)
      Date.strptime(date_str.split("，").first, "%m月%d日")
    end

    # Sort the payload by the parsed date
    sorted_payload = payload.sort_by { |_, date_str| parse_date(date_str) }

    # Format the sorted payload into the desired message format
    sorted_payload.map { |name, date_str| "#{name}(#{date_str})" }.join(", ")
  end

  # 找出今天生日/政治生日的人
  def people_with_birthday_today
    today = Time.zone.today
    today_str = today.strftime("%m月%d日")
    payload.select do |_name, date_str|
      date_str.include?(today_str)
    end.keys
  end

  def people_with_birthday_today_names
    people_with_birthday_today.join(",")
  end

  delegate :count, to: :people_with_birthday_today, prefix: true

  # 今日生日的有
  def message_template_birthday_today
    return unless people_with_birthday_today_count.positive?

    "今日生日的同志有#{people_with_birthday_today_count}人:#{people_with_birthday_today_names}"
  end

  # 今日政治生日的有
  def message_template_political_birthday_today
    return unless people_with_birthday_today_count.positive?

    "今日政治生日的同志有#{people_with_birthday_today_count}人:#{people_with_birthday_today_names}"
  end

end
