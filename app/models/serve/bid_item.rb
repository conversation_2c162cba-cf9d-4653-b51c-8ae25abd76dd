# projectcode	项目编号
# platformname	交易平台名称
# investmentamount	投资估算（概算）金额
# industriestype	项目行业分类
# contactinformation	联系方式
# connector	联系人
# projectscale	项目规模
# address	项目地址
# legalperson	项目法人
# invest_project_code	投资项目统一代码
# regioncode	项目所在行政区域代码
# projectname	项目名称
# createtime	项目建立时间
# platformcode	交易平台标识码
# approvalName	项目批文名称-补
# approvalNumber	项目批准文号-补
# approvalAuthority	项目批准单位-补
# fundSource	资金来源-补
# tenderProjectType	项目行业分类（招投标）
# approvalCode	立项批复单位信用代码

class Serve::BidItem < ApplicationRecord

  self.track_migration = true
  seqable

  belongs_to :app
  belongs_to :bid_project, optional: true

  attribute :code,         :string,   comment: "项目编号"
  attribute :setup_at,     :datetime, comment: "评标时间"
  attribute :connector,    :string,   comment: "联系人"
  attribute :legaler_name, :string,   comment: "法人"
  attribute :amount,       :decimal, scale: 5, precision: 20, comment: "金额"
  attribute :payload,      :jsonb, comment: "扩展字段"

  after_commit :async_bid_project!, only: [:create, :update], if: :bid_project

  def xm
    Biz::Xm.find_by projectCode: code
  end

  # 同步招标项目
  def async_bid_project!
    bid_project.async!
  end

  # 同步数据
  def self.async_all!(app: App.first, time: nil)
    time ||= Date.yesterday.strftime("%F")
    Biz::Xm.ransack(load_time_gt: time).result.order(load_time: :asc).find_each do |xm|
      item = Serve::BidItem.find_or_initialize_by(seq: xm.projectCode)
      item.update(
        app: app,
        bid_project: Serve::BidProject.find_by(code: xm.projectCode, app: app),
        code: xm.projectCode,
        connector: xm.connector,
        legaler_name: xm.legalPerson,
        payload: xm.as_json,
        updated_at: xm.load_time
      )
    rescue Exception
      next
    end
  end

end
