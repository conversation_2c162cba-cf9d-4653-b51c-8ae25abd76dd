class Serve::Activity < ApplicationRecord

  include Serve::Model::Activity
  include Iest::Model::Serve::Activity

  def self.extra_view_attributes(_mod)
    [
      :tag_ids,
      :tags,
      :origin_id,
      :tag_names,
      :department_names,
      :region_area_id,
      :province,
      :city,
      :district,
      :origin,
      :content_type_tag_names,
      :ai_summary
    ]
  end

  def self.extra_permitted_attributes
    [
      :region_area_id,
      :province,
      :city,
      :district,
      :origin_id
    ]
  end

  ransacker :cover_image_exists, type: :boolean do |parent|
    Arel.sql("CASE WHEN (#{parent.table.name}.cover_image->>'files') IS NOT NULL AND (#{parent.table.name}.cover_image->>'files') != '[]' THEN TRUE ELSE FALSE END")
  end

  def self.ransackable_attributes(auth_object = nil)
    super + ["cover_image_exists"]
  end

  # 从微信公众号URL爬取文章内容
  def self.crawl_from_wechat_url(url, options = {})
    require "nokogiri"

    # 验证URL格式
    raise ArgumentError, "无效的微信公众号URL" unless url.match?(/mp\.weixin\.qq\.com/)

    # 设置当前URL供占位符使用
    @current_url = url
    @current_cover_image_url = nil

    # 首先尝试简单的HTTP请求（用于测试）
    begin
      html = fetch_wechat_content_simple(url)
      doc = Nokogiri::HTML(html)

      # 提取文章信息
      article_data = extract_wechat_article_data(doc, url, html)

      # 如果简单方法失败，尝试使用Selenium
      if article_data[:title].blank?
        Rails.logger.info "简单方法失败，尝试使用Selenium..."
        return crawl_from_wechat_url_with_selenium(url, options)
      end

      # 创建或更新Activity记录
      create_activity_from_wechat_data(article_data, options)
    rescue StandardError => e
      Rails.logger.error "爬取微信文章失败: #{e.message}"

      # 检查是否是网络连接问题
      if e.message.include?("storage.googleapis.com") || e.message.include?("SSL_connect") || e.message.include?("Connection reset")
        Rails.logger.error "检测到网络连接问题，可能是生产环境防火墙限制"
        Rails.logger.error "错误详情: #{e.message}"

        # 提供更友好的错误信息
        error_msg = if e.message.include?("storage.googleapis.com")
                      "网络连接失败：无法访问Google服务器下载ChromeDriver。请检查网络连接或联系系统管理员配置ChromeDriver。"
                    elsif e.message.include?("SSL_connect")
                      "SSL连接失败：无法建立安全连接到微信服务器。请检查网络配置或稍后重试。"
                    else
                      "网络连接被重置：可能是服务器反爬虫机制或网络不稳定。请稍后重试。"
                    end

        raise error_msg
      else
        # 如果不是网络问题，尝试使用Selenium
        Rails.logger.info "尝试使用Selenium..."
        crawl_from_wechat_url_with_selenium(url, options)
      end
    end
  end

  # 检查生产环境WebDriver配置
  def self.check_webdriver_environment
    Rails.logger.info "检查WebDriver环境配置..."

    # 检查Chrome浏览器
    chrome_paths = [
      "/usr/bin/google-chrome",
      "/usr/bin/google-chrome-stable",
      "/usr/bin/chromium-browser",
      "/usr/bin/chromium",
      "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"
    ]

    chrome_found = chrome_paths.find { |path| File.exist?(path) }
    if chrome_found
      Rails.logger.info "找到Chrome浏览器: #{chrome_found}"
    else
      Rails.logger.warn "未找到Chrome浏览器，可用路径: #{chrome_paths.join(', ')}"
    end

    # 检查ChromeDriver
    driver_paths = [
      "#{ENV.fetch('HOME', nil)}/bin/chromedriver", # 新安装的138版本
      "/usr/bin/chromedriver",
      "/usr/local/bin/chromedriver",
      "/opt/chromedriver/chromedriver",
      ENV.fetch("CHROMEDRIVER_PATH", nil)
    ].compact

    driver_found = driver_paths.find { |path| File.exist?(path) }
    if driver_found
      Rails.logger.info "找到ChromeDriver: #{driver_found}"

      # 检查ChromeDriver版本
      begin
        version_output = `#{driver_found} --version 2>&1`
        Rails.logger.info "ChromeDriver版本: #{version_output.strip}"
      rescue StandardError => e
        Rails.logger.warn "无法获取ChromeDriver版本: #{e.message}"
      end
    else
      Rails.logger.warn "未找到ChromeDriver，可用路径: #{driver_paths.join(', ')}"
    end

    {
      chrome_available: !!chrome_found,
      chrome_path: chrome_found,
      chromedriver_available: !!driver_found,
      chromedriver_path: driver_found
    }
  end

  # 使用Selenium的爬取方法
  def self.crawl_from_wechat_url_with_selenium(url, options = {})
    require "selenium-webdriver"
    require "nokogiri"
    require "timeout"

    Rails.logger.info "开始抓取微信文章: #{url}"

    begin
      # 检查WebDriver环境
      env_check = check_webdriver_environment
      unless env_check[:chrome_available] && env_check[:chromedriver_available]
        raise "WebDriver环境不完整: Chrome可用=#{env_check[:chrome_available]}, ChromeDriver可用=#{env_check[:chromedriver_available]}"
      end

      # 配置Chrome选项
      chrome_options = Selenium::WebDriver::Chrome::Options.new
      chrome_options.add_argument("--ignore-certificate-errors")
      chrome_options.add_argument("--headless") # 启用无头模式
      chrome_options.add_argument("--disable-gpu") # 禁用 GPU 加速
      chrome_options.add_argument("--no-sandbox") # 无沙箱模式
      chrome_options.add_argument("--disable-dev-shm-usage") # 共享内存禁用
      chrome_options.add_argument("--disable-web-security") # 禁用网络安全检查
      chrome_options.add_argument("--disable-features=VizDisplayCompositor") # 禁用某些功能

      # 添加版本兼容性参数
      chrome_options.add_argument("--disable-blink-features=AutomationControlled")
      chrome_options.add_argument("--disable-extensions")
      chrome_options.add_argument("--disable-plugins")
      # 注意：不禁用JavaScript，因为需要处理动态内容
      # chrome_options.add_argument("--disable-images") # 可选：禁用图片加载以提高速度

      # 添加用户代理，模拟真实浏览器
      chrome_options.add_argument("--user-agent=Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.0(0x18000029) NetType/WIFI Language/zh_CN")

      # 设置Chrome二进制路径（如果找到的话）
      if env_check[:chrome_path]
        chrome_options.binary = env_check[:chrome_path]
        Rails.logger.info "使用Chrome路径: #{env_check[:chrome_path]}"
      end

      # 创建ChromeDriver服务，指定本地ChromeDriver路径
      service = Selenium::WebDriver::Chrome::Service.new(path: env_check[:chromedriver_path])
      Rails.logger.info "使用ChromeDriver路径: #{env_check[:chromedriver_path]}"

      # 尝试创建WebDriver实例，处理版本不匹配问题
      begin
        driver = Selenium::WebDriver.for(:chrome, service: service, options: chrome_options)
      rescue Selenium::WebDriver::Error::SessionNotCreatedError => e
        raise e unless e.message.include?("This version of ChromeDriver only supports Chrome version")

        Rails.logger.error "ChromeDriver版本不匹配: #{e.message}"
        Rails.logger.info "尝试使用系统默认ChromeDriver..."

        # 尝试使用系统默认ChromeDriver（不指定路径）
        driver = Selenium::WebDriver.for(:chrome, options: chrome_options)
      end

      # 设置基本超时
      driver.manage.timeouts.page_load = 60
      driver.manage.timeouts.implicit_wait = 10

      Rails.logger.info "WebDriver启动成功"

      # 访问页面
      Rails.logger.info "正在访问微信文章: #{url}"
      driver.get(url)

      # 等待页面加载
      wait = Selenium::WebDriver::Wait.new(timeout: 30)

      # 尝试等待页面元素
      begin
        wait.until do
          driver.find_element(css: "#activity-name") ||
            driver.find_element(css: ".rich_media_title") ||
            driver.find_element(css: "h1") ||
            driver.find_element(css: "body")
        end
      rescue Selenium::WebDriver::Error::TimeoutError
        Rails.logger.warn "页面元素加载超时，继续尝试解析"
      end

      # 等待JavaScript执行完成
      sleep(3)

      # 处理动态内容：查找并点击展开按钮（设置超时）
      begin
        Timeout::timeout(30) do  # 30秒超时
          handle_dynamic_content(driver, wait)
        end
      rescue Timeout::Error
        Rails.logger.warn "动态内容处理超时，继续进行..."
      rescue StandardError => e
        Rails.logger.warn "动态内容处理异常: #{e.message}"
      end

      # 再次等待动态内容加载
      sleep(2)

      # 获取页面HTML
      html = driver.page_source
      Rails.logger.info "页面HTML长度: #{html.length}"

      doc = Nokogiri::HTML(html)

      # 提取文章信息
      article_data = extract_wechat_article_data(doc, url, html)

      # 创建或更新Activity记录
      create_activity_from_wechat_data(article_data, options)
    rescue StandardError => e
      Rails.logger.error "微信文章抓取失败: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")

      # 如果WebDriver抓取失败，尝试简单HTTP请求作为备用方案
      Rails.logger.info "尝试使用简单HTTP请求作为备用方案"
      begin
        html = fetch_wechat_content_simple(url)
        doc = Nokogiri::HTML(html)
        article_data = extract_wechat_article_data(doc, url, html)

        if article_data[:title].present?
          return create_activity_from_wechat_data(article_data, options)
        else
          Rails.logger.error "备用方案也无法提取文章数据"
          return nil
        end
      rescue StandardError => backup_error
        Rails.logger.error "备用方案失败: #{backup_error.message}"
        return nil
      end
    ensure
      # 确保关闭WebDriver
      begin
        driver.quit if defined?(driver) && driver
        Rails.logger.info "WebDriver已关闭"
      rescue StandardError => e
        Rails.logger.warn "关闭WebDriver时出错: #{e.message}"
      end
    end
  end

  # 简单的HTTP请求方法（支持重定向）
  def self.fetch_wechat_content_simple(url, max_redirects = 5)
    require "net/http"
    require "uri"
    require "openssl"
    require "zlib"

    current_url = url
    redirects_count = 0
    response = nil

    loop do
      uri = URI(current_url)
      http = Net::HTTP.new(uri.host, uri.port)

      if uri.scheme == "https"
        http.use_ssl = true
        # 解决SSL连接问题
        http.verify_mode = OpenSSL::SSL::VERIFY_NONE
        http.ssl_timeout = 30
        http.ssl_version = :TLSv1_2
      end

      # 设置超时时间
      http.open_timeout = 30
      http.read_timeout = 60

      request = Net::HTTP::Get.new(uri)
      request["User-Agent"] =
        "Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.0 Mobile/15E148 Safari/604.1 MicroMessenger/8.0.0"
      request["Accept"] = "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8"
      request["Accept-Language"] = "zh-CN,zh;q=0.9,en;q=0.8"
      request["Accept-Encoding"] = "gzip, deflate"
      request["Connection"] = "keep-alive"
      request["Upgrade-Insecure-Requests"] = "1"
      request["Referer"] = "https://mp.weixin.qq.com/"

      response = http.request(request)

      case response.code
      when "200"
        break # 成功，退出循环
      when "301", "302", "303", "307", "308"
        # 处理重定向
        redirects_count += 1
        raise "重定向次数过多 (#{redirects_count})" if redirects_count > max_redirects

        location = response["Location"]
        raise "重定向响应缺少Location头" unless location

        # 处理相对URL
        current_url = location.start_with?("http") ? location : URI.join(current_url, location).to_s
        Rails.logger.info "重定向到: #{current_url}"
        next
      else
        raise "HTTP请求失败: #{response.code} #{response.message}"
      end
    end

    # 处理gzip压缩的响应
    body = response.body
    if response["content-encoding"] == "gzip"
      begin
        body = Zlib::GzipReader.new(StringIO.new(body)).read
        Rails.logger.info "成功解压缩gzip内容"
      rescue StandardError => e
        Rails.logger.warn "解压缩gzip失败: #{e.message}"
        # 如果解压缩失败，使用原始内容
      end
    end

    body
  end

  def self.extract_wechat_article_data(doc, url, html_content = nil)
    # 提取标题
    title = doc.css("#activity-name, .rich_media_title, h1").first&.text&.strip

    # 提取作者 - 改进的选择器
    author = extract_wechat_author(doc)

    # 提取发布时间 - 改进的选择器
    publish_time_text = extract_wechat_publish_time_text(doc, html_content)
    publish_time = parse_wechat_publish_time(publish_time_text)

    # 提取正文内容 - 改进的内容提取逻辑
    content_html = extract_article_content(doc, url)

    # 处理图片和视频
    processed_content = process_media_content(content_html, url) if content_html

    # 提取封面图片
    cover_image_url = doc.css(".rich_media_thumb, .msg_cdn_url, img").first&.[]("src")

    # 设置封面图URL供占位符使用
    @current_cover_image_url = cover_image_url

    Rails.logger.info "提取结果 - 标题: #{title}, 作者: #{author}, 内容长度: #{processed_content&.length || 0}, 封面图: #{cover_image_url}"

    {
      title: title,
      author: author,
      publish_time: publish_time,
      content_html: processed_content,
      cover_image_url: cover_image_url,
      original_url: url
    }
  end

  # 改进的文章内容提取方法，支持视频处理
  def self.extract_article_content(doc, url = nil)
    # 尝试多种选择器来找到文章内容
    content_selectors = [
      "#js_content",
      ".rich_media_content",
      ".article_content",
      '[data-role="article-content"]',
      ".rich_media_area_primary",
      ".rich_media_wrp",
      ".rich_media_inner",
      "#page-content"
    ]

    content_element = nil
    content_selectors.each do |selector|
      element = doc.css(selector).first
      next unless element

      content_element = element
      Rails.logger.info "使用选择器找到内容: #{selector}"
      break
    end

    return nil unless content_element

    # 获取内容HTML
    content_html = content_element.inner_html
    Rails.logger.info "原始内容HTML长度: #{content_html.length}"

    # 解析HTML文档
    cleaned_doc = Nokogiri::HTML::DocumentFragment.parse(content_html)

    # 检测视频元素（仅用于日志记录，不添加冲突属性）
    video_elements = detect_video_elements_for_logging(cleaned_doc)
    Rails.logger.info "检测到视频元素数量: #{video_elements.length}"

    # 移除不需要的元素，但保留视频相关元素
    remove_unwanted_elements(cleaned_doc)

    # 移除空的元素，但保留可能包含视频的元素
    remove_empty_elements(cleaned_doc)

    # 处理微信视频：检测、替换为统一的占位符样式
    process_wechat_videos(cleaned_doc, url)
    Rails.logger.info "微信视频处理完成"

    cleaned_html = cleaned_doc.to_html.strip
    Rails.logger.info "清理后内容HTML长度: #{cleaned_html.length}"

    # 检查内容质量
    content_quality = assess_content_quality(cleaned_doc, content_element)
    Rails.logger.info "内容质量评估: #{content_quality}"

    # 检查原始内容是否主要是JavaScript代码
    original_html = content_element.inner_html
    is_mostly_javascript = check_if_mostly_javascript(original_html)
    Rails.logger.info "原始内容主要是JavaScript: #{is_mostly_javascript}"

    # 根据内容质量和JavaScript检测决定处理方式
    if content_quality[:has_meaningful_content] && !is_mostly_javascript
      Rails.logger.info "检测到有意义的内容，保留HTML结构"
      content_html = cleaned_html
    elsif !is_mostly_javascript && cleaned_html.length > 500
      Rails.logger.info "内容可能有用但质量一般，保留清理后的HTML"
      content_html = cleaned_html
    else
      Rails.logger.warn "内容质量较低或主要是JavaScript，尝试智能文本提取"
      content_html = extract_text_content_smart(content_element, cleaned_html, original_html)
    end

    content_html
  end

  # 仅检测视频元素用于日志记录（不添加属性避免冲突）
  def self.detect_video_elements_for_logging(doc)
    video_elements = []

    # 检测各种视频元素 - 扩展选择器以覆盖更多视频类型
    video_selectors = [
      'iframe[src*="video"]',
      'iframe[src*="mp.weixin.qq.com"]',
      "video",
      '[data-src*="video"]',
      ".video_iframe",
      ".rich_media_video",
      '[class*="video"]',
      # 新增的微信视频类型
      "mp-common-videosnap",
      "mp-common-videosnap div",
      "[class*='videosnap']",
      "iframe[data-mpvid]",
      "div[data-mpvid]",
      "[data-vid]",
      "[class*='video_snap']"
    ]

    video_selectors.each do |selector|
      elements = doc.css(selector)
      elements.each do |element|
        video_elements << element
        # 仅记录，不处理，避免与后续处理冲突
      end
    end

    video_elements
  end

  # 检测并处理视频元素（保留原方法以防其他地方调用）
  def self.detect_and_process_videos(doc)
    video_elements = []

    # 检测各种视频元素 - 扩展选择器以覆盖更多视频类型
    video_selectors = [
      'iframe[src*="video"]',
      'iframe[src*="mp.weixin.qq.com"]',
      "video",
      '[data-src*="video"]',
      ".video_iframe",
      ".rich_media_video",
      '[class*="video"]',
      # 新增的微信视频类型
      "mp-common-videosnap",
      "mp-common-videosnap div",
      "[class*='videosnap']",
      "iframe[data-mpvid]",
      "div[data-mpvid]",
      "[data-vid]",
      "[class*='video_snap']"
    ]

    video_selectors.each do |selector|
      elements = doc.css(selector)
      elements.each do |element|
        video_elements << element
        # 为视频元素添加点击跳转功能
        process_video_element(element)
      end
    end

    video_elements
  end

  # 处理单个视频元素
  def self.process_video_element(element)
    # 如果是iframe，确保有适当的属性
    if element.name == "iframe"
      element["style"] = "width: 100%; height: 300px; border: 1px solid #ddd;"
      element["allowfullscreen"] = "true"
    end

    # 添加视频标识类
    existing_class = element["class"] || ""
    element["class"] = "#{existing_class} wechat-video".strip

    # 添加数据属性用于前端处理
    element["data-video"] = "true"
  end

  # 移除不需要的元素
  def self.remove_unwanted_elements(doc)
    # 移除script、style和隐藏元素，但保留视频相关元素
    unwanted_selectors = [
      'script:not([src*="video"])',
      "style",
      ".share_notice",
      "#js_common_share_desc_wrap",
      ".weui-ellipsis__text__wrp",
      ".js_jump_icon",
      ".js_share_notice"
    ]

    unwanted_selectors.each do |selector|
      doc.css(selector).remove
    end

    # 移除隐藏元素，但保留可能包含视频的元素
    doc.css('[style*="display: none"], [style*="display:none"]').each do |element|
      # 如果元素包含视频相关内容，不删除
      has_video_content = element.css('iframe, video, [data-src*="video"], mp-common-videosnap, [data-mpvid], [data-vid], [class*="videosnap"]').any? ||
                         element.name == "mp-common-videosnap" ||
                         element["class"]&.include?("video") ||
                         element["data-video"] ||
                         element["data-video-processed"]

      element.remove unless has_video_content
    end

    # 移除包含大量JavaScript代码的段落
    doc.css("p").each do |p|
      text = p.text.strip
      # 如果段落主要是JavaScript代码，移除它
      next unless text.length > 100 && (
        (text.include?("function") && text.include?("{") && text.include?("}")) ||
        (text.include?("var ") && text.include?("=") && text.include?(";")) ||
        (text.include?("window.") && text.include?("document.")) ||
        text.include?("__INLINE_SCRIPT__") ||
        text.include?("_arrayLikeToArray") ||
        text.include?("prototype.toString")
      )

      p.remove
    end
  end

  # 移除空元素
  def self.remove_empty_elements(doc)
    # 移除空的div和span，但保留可能包含视频的元素
    doc.css("div, span").each do |element|
      # 检查是否是视频相关元素
      is_video_element = element["class"]&.include?("video") ||
                        element["data-video"] ||
                        element["data-video-processed"] ||
                        element["data-mpvid"] ||
                        element["data-vid"] ||
                        element.name == "mp-common-videosnap" ||
                        element["class"]&.include?("videosnap") ||
                        element["class"]&.include?("wechat-video")

      # 只删除真正空的且不是视频相关的元素
      if element.content.strip.empty? && element.children.empty? && !is_video_element
        element.remove
      end
    end
  end

  # 评估内容质量
  def self.assess_content_quality(doc, _original_element)
    text_content = doc.text.strip
    filtered_text = text_content.gsub(/分享到朋友圈|分享给朋友|复制链接|取消|确定|微信扫一扫|关注该公众号/, "").strip

    images = doc.css("img").length
    videos = doc.css('iframe, video, [data-video="true"]').length
    paragraphs = doc.css("p, div, section").length
    links = doc.css("a").length

    # 检查是否有有意义的内容
    has_meaningful_content = filtered_text.length > 100 ||
                             images > 0 ||
                             videos > 0 ||
                             paragraphs > 3 ||
                             links > 0

    {
      text_length: filtered_text.length,
      images: images,
      videos: videos,
      paragraphs: paragraphs,
      links: links,
      has_meaningful_content: has_meaningful_content
    }
  end

  # 检查内容是否主要是JavaScript代码
  def self.check_if_mostly_javascript(html_content)
    return false if html_content.blank?

    js_indicators = [
      "__INLINE_SCRIPT__",
      "function(",
      "var ",
      "window.",
      "document.",
      "_arrayLikeToArray",
      "prototype.toString",
      "Array.isArray",
      "Symbol.iterator",
      "typeof Symbol"
    ]

    js_count = js_indicators.count { |indicator| html_content.include?(indicator) }
    total_length = html_content.length

    # 如果包含多个JavaScript指标且内容很长但实际文本很少，则认为主要是JavaScript
    js_count >= 3 && total_length > 50_000
  end

  # 智能文本提取方法
  def self.extract_text_content_smart(original_element, _cleaned_html, original_html)
    Rails.logger.info "开始智能文本提取"

    # 首先尝试从原始HTML中提取有意义的段落
    doc = Nokogiri::HTML::DocumentFragment.parse(original_html)

    # 移除明显的JavaScript和样式
    doc.css("script, style, noscript").remove

    # 查找可能包含文章内容的元素
    content_candidates = []

    # 查找包含文本的段落和div
    doc.css("p, div, section, article").each do |element|
      text = element.text.strip
      next if text.length < 20 # 忽略太短的文本

      # 过滤JavaScript相关内容
      next if text.include?("function") || text.include?("var ") ||
              text.include?("window.") || text.include?("__INLINE_SCRIPT__")

      # 过滤分享相关内容
      next if text.match?(/分享到朋友圈|分享给朋友|复制链接|取消|确定|微信扫一扫|关注该公众号/)

      # 过滤纯数字或特殊字符
      next if text.match?(/^[\d\s\-_=+\[\]{}().,;:!?'"]*$/)

      content_candidates << text
    end

    Rails.logger.info "找到候选文本段落: #{content_candidates.length}"

    if content_candidates.length > 0
      # 合并有效的文本段落
      valid_paragraphs = content_candidates.select { |text| text.length > 30 }

      if valid_paragraphs.length > 0
        content_html = valid_paragraphs.map { |text| "<p>#{CGI.escapeHTML(text)}</p>" }.join("\n")
        Rails.logger.info "成功提取智能文本内容，段落数: #{valid_paragraphs.length}"
        return content_html
      end
    end

    # 如果智能提取失败，创建占位符内容
    create_placeholder_content(original_element, original_html, @current_url)
  end

  # 创建占位符内容
  def self.create_placeholder_content(original_element, original_html, current_url = nil)
    Rails.logger.info "创建占位符内容"

    # 检查是否包含视频相关元素
    doc = Nokogiri::HTML::DocumentFragment.parse(original_html)
    has_video_indicators = doc.css('iframe[data-mpvid], iframe.video_iframe, video, [data-src*="video"]').any? ||
                           original_html.include?("video") ||
                           original_html.include?("mpvid") ||
                           original_html.include?("iframe")

    # 获取原文链接
    original_url = current_url || begin
      if original_element.respond_to?(:document) && original_element.document
        original_element.document.url
      else
        # 从当前上下文获取URL
        @current_url || "javascript:void(0)"
      end
    rescue StandardError
      "javascript:void(0)"
    end

    if has_video_indicators
      Rails.logger.info "检测到视频内容，创建视频播放器占位符"
      # 尝试获取封面图URL（从当前上下文或元数据中）
      cover_image_url = @current_cover_image_url || nil
      create_video_placeholder(original_url, cover_image_url)
    else
      Rails.logger.info "创建普通文章占位符"
      create_article_placeholder(original_url)
    end
  end

  # 创建视频播放器占位符
  def self.create_video_placeholder(original_url, cover_image_url = nil)
    # 如果有封面图，使用封面图作为背景；否则使用黑屏效果
    background_style = if cover_image_url.present?
                         "background: linear-gradient(rgba(0,0,0,0.3), rgba(0,0,0,0.5)), url('#{cover_image_url}') center/cover;"
                       else
                         "background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);"
                       end

    <<~HTML
      <div class="wechat-video-player" style="
        #{background_style}
        border-radius: 12px;
        position: relative;
        width: 100%;
        max-width: 640px;
        height: 360px;
        margin: 20px auto;
        cursor: pointer;
        transition: all 0.3s ease;
        box-shadow: 0 8px 25px rgba(0,0,0,0.3);
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;
      " onclick="window.open('#{original_url}', '_blank')">

        <!-- 播放按钮 -->
        <div class="play-button" style="
          width: 80px;
          height: 80px;
          background: rgba(255,255,255,0.9);
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          transition: all 0.3s ease;
          box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        ">
          <div style="
            width: 0;
            height: 0;
            border-left: 25px solid #333;
            border-top: 15px solid transparent;
            border-bottom: 15px solid transparent;
            margin-left: 5px;
          "></div>
        </div>

        <!-- 底部控制栏 -->
        <div style="
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          background: linear-gradient(transparent, rgba(0,0,0,0.8));
          padding: 20px 15px 15px;
          color: white;
        ">
          <div style="font-size: 14px; font-weight: 500; margin-bottom: 5px;">
            📹 视频内容
          </div>
          <div style="font-size: 12px; opacity: 0.8;">
            点击播放器查看完整视频内容
          </div>
        </div>

        <!-- 右上角原文链接 -->
        <div style="
          position: absolute;
          top: 15px;
          right: 15px;
          background: rgba(0,0,0,0.6);
          color: white;
          padding: 6px 12px;
          border-radius: 20px;
          font-size: 12px;
          font-weight: 500;
          backdrop-filter: blur(10px);
        ">
          查看原文 →
        </div>
      </div>

      <style>
        .wechat-video-player:hover {
          transform: translateY(-3px);
          box-shadow: 0 15px 40px rgba(0,0,0,0.4);
        }

        .wechat-video-player:hover .play-button {
          transform: scale(1.1);
          background: rgba(255,255,255,1);
        }

        .wechat-video-player:active {
          transform: translateY(-1px);
        }
      </style>
    HTML
  end

  # 创建普通文章占位符
  def self.create_article_placeholder(original_url)
    <<~HTML
      <div class="wechat-article-placeholder" style="
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        border-radius: 12px;
        padding: 30px 20px;
        text-align: center;
        color: white;
        margin: 20px 0;
        cursor: pointer;
        transition: transform 0.3s ease;
        box-shadow: 0 8px 25px rgba(0,0,0,0.15);
      " onclick="window.open('#{original_url}', '_blank')">
        <div style="font-size: 36px; margin-bottom: 15px;">📄</div>
        <h3 style="margin: 0 0 10px 0; font-size: 18px; font-weight: 600;">微信公众号文章</h3>
        <p style="margin: 0 0 15px 0; opacity: 0.9; font-size: 14px;">
          内容正在优化中，点击查看完整原文
        </p>
        <div style="
          background: rgba(255,255,255,0.2);
          border: 1px solid rgba(255,255,255,0.3);
          border-radius: 25px;
          padding: 8px 20px;
          display: inline-block;
          font-size: 13px;
          font-weight: 500;
        ">
          查看原文 →
        </div>
      </div>
      <style>
        .wechat-article-placeholder:hover {
          transform: translateY(-2px);
          box-shadow: 0 12px 35px rgba(0,0,0,0.2);
        }
      </style>
    HTML
  end

  # 提取文本内容
  def self.extract_text_content(original_element, fallback_html)
    # 先创建一个副本进行清理
    temp_doc = Nokogiri::HTML::DocumentFragment.parse(original_element.inner_html)

    # 移除JavaScript相关的元素和内容
    temp_doc.css("script, style").remove

    # 移除包含JavaScript代码的段落
    temp_doc.css("p").each do |p|
      text = p.text.strip
      # 如果段落主要是JavaScript代码，移除它
      next unless text.length > 50 && (
        (text.include?("function") && text.include?("{") && text.include?("}")) ||
        (text.include?("var ") && text.include?("=") && text.include?(";")) ||
        (text.include?("window.") && text.include?("document.")) ||
        text.include?("__INLINE_SCRIPT__") ||
        text.include?("_arrayLikeToArray") ||
        text.include?("prototype.toString") ||
        text.include?("Array.isArray") ||
        text.include?("Symbol.iterator")
      )

      p.remove
    end

    content_text = temp_doc.text.strip
    filtered_text = content_text.gsub(/分享到朋友圈|分享给朋友|复制链接|取消|确定|微信扫一扫|关注该公众号/, "").strip

    Rails.logger.info "原始文本长度: #{original_element.text.length}, 清理后文本长度: #{filtered_text.length}"

    if filtered_text.length > 100
      # 将文本按段落分割并转换为HTML
      paragraphs = filtered_text.split(/\n\s*\n/).reject(&:blank?)
      # 进一步过滤JavaScript相关的段落
      clean_paragraphs = paragraphs.reject do |p|
        p.include?("function") || p.include?("var ") || p.include?("window.") ||
          p.include?("__INLINE_SCRIPT__") || p.length < 10
      end

      if clean_paragraphs.length > 0
        content_html = clean_paragraphs.map { |p| "<p>#{p.strip}</p>" }.join("\n")
        Rails.logger.info "成功提取文本内容，段落数: #{clean_paragraphs.length}"
        content_html
      else
        Rails.logger.warn "所有段落都被过滤，返回提示信息"
        "<p>内容正在加载中，请稍后刷新查看完整内容。</p><p><a href='#{original_element.document.url}' target='_blank'>点击查看原文</a></p>"
      end
    else
      Rails.logger.error "无法提取有效内容，过滤后文本长度: #{filtered_text.length}"
      # 获取原文链接
      original_url = @current_url || original_element.document&.url || "javascript:void(0)"

      if fallback_html.length < 50
        # 创建简单的占位符
        create_article_placeholder(original_url)
      else
        fallback_html
      end
    end
  end

  def self.parse_wechat_publish_time(time_text)
    return Time.current unless time_text

    Rails.logger.info "解析时间文本: #{time_text}"

    # 1. 尝试解析完整的中文时间格式：2024年09月03日 15:45
    if match = time_text.match(/(\d{4})年(\d{1,2})月(\d{1,2})日\s*(\d{1,2}):(\d{2})/)
      year, month, day, hour, minute = match.captures.map(&:to_i)
      begin
        parsed_time = Time.new(year, month, day, hour, minute, 0, "+08:00")
        Rails.logger.info "成功解析完整时间: #{parsed_time}"
        return parsed_time
      rescue StandardError => e
        Rails.logger.warn "解析完整时间失败: #{e.message}"
      end
    end

    # 2. 尝试解析只有日期的中文格式：2024年09月03日
    if match = time_text.match(/(\d{4})年(\d{1,2})月(\d{1,2})日/)
      year, month, day = match.captures.map(&:to_i)
      begin
        parsed_time = Time.new(year, month, day, 0, 0, 0, "+08:00")
        Rails.logger.info "成功解析日期: #{parsed_time}"
        return parsed_time
      rescue StandardError => e
        Rails.logger.warn "解析日期失败: #{e.message}"
      end
    end

    # 3. 尝试解析标准格式：2024-09-03 15:45
    if match = time_text.match(/(\d{4})-(\d{1,2})-(\d{1,2})\s*(\d{1,2}):(\d{2})/)
      year, month, day, hour, minute = match.captures.map(&:to_i)
      begin
        parsed_time = Time.new(year, month, day, hour, minute, 0, "+08:00")
        Rails.logger.info "成功解析标准时间: #{parsed_time}"
        return parsed_time
      rescue StandardError => e
        Rails.logger.warn "解析标准时间失败: #{e.message}"
      end
    end

    # 4. 尝试解析标准日期格式：2024-09-03
    if match = time_text.match(/(\d{4})-(\d{1,2})-(\d{1,2})/)
      year, month, day = match.captures.map(&:to_i)
      begin
        parsed_time = Time.new(year, month, day, 0, 0, 0, "+08:00")
        Rails.logger.info "成功解析标准日期: #{parsed_time}"
        return parsed_time
      rescue StandardError => e
        Rails.logger.warn "解析标准日期失败: #{e.message}"
      end
    end

    # 5. 尝试使用Time.parse作为最后的备选方案
    begin
      parsed_time = Time.parse(time_text)
      Rails.logger.info "通过Time.parse解析: #{parsed_time}"
      return parsed_time
    rescue StandardError => e
      Rails.logger.warn "Time.parse解析失败: #{e.message}"
    end

    Rails.logger.warn "所有时间解析方法都失败，使用当前时间"
    Time.current
  end

  def self.process_media_content(html, source_url)
    return html unless html

    doc = Nokogiri::HTML::DocumentFragment.parse(html)

    # 添加loading动画样式
    add_loading_animation_styles(doc)

    # 处理图片：添加referrerpolicy属性和代理URL
    doc.css("img").each do |img|
      img["referrerpolicy"] = "no-referrer"

      # 处理微信图片防盗链问题 - 检查src和data-src属性
      original_src = nil

      # 优先检查data-src，因为微信文章通常使用data-src
      if img["data-src"] && is_wechat_image_url?(img["data-src"])
        original_src = img["data-src"]
      elsif img["src"] && is_wechat_image_url?(img["src"])
        original_src = img["src"]
      end

      if original_src
        # 选择处理方式：代理或下载存储
        if ENV['WECHAT_IMAGE_DOWNLOAD'] == 'true'
          # 下载并存储图片
          downloaded_url = download_and_store_wechat_image(original_src)
          if downloaded_url
            img["src"] = downloaded_url
            img["data-src"] = downloaded_url if img["data-src"]
            Rails.logger.info "微信图片已下载存储: #{original_src} -> #{downloaded_url}"
          else
            # 下载失败时回退到代理服务
            proxy_url = "#{ENV['API_PATH_PREFIX'] || ''}/api/image_proxy?url=#{CGI.escape(original_src)}"
            img["src"] = proxy_url
            img["data-src"] = proxy_url if img["data-src"]
            Rails.logger.warn "图片下载失败，使用代理服务: #{original_src} -> #{proxy_url}"
          end
        else
          # 使用图片代理服务
          proxy_url = "#{ENV['API_PATH_PREFIX'] || ''}/api/image_proxy?url=#{CGI.escape(original_src)}"
          img["src"] = proxy_url
          img["data-src"] = proxy_url if img["data-src"]
          Rails.logger.info "使用图片代理服务: #{original_src} -> #{proxy_url}"
        end

        # 添加图片加载优化属性
        img["data-original-src"] = original_src
        img["loading"] = "lazy"
        img["onerror"] = "this.style.display='none'; console.log('图片加载失败: ' + this.src);"

        # 添加loading状态处理
        add_image_loading_placeholder(img)
      else
        # 处理非微信图片的data-src属性
        img["src"] = img["data-src"] if img["data-src"] && !img["src"]
      end
    end

    # 处理微信视频：支持多种视频元素类型
    process_wechat_videos(doc, source_url)

    # 处理其他视频：添加链接覆盖（保持原有逻辑，不替换为统一样式）
    doc.css('video, iframe[src*="video"]').each do |media|
      # 跳过已处理的微信视频和已经被我们处理过的元素
      next if media["class"]&.include?("video_iframe") ||
              element_already_processed?(media)

      # 添加链接覆盖（原有逻辑）
      link = doc.document.create_element("a")
      link["href"] = source_url
      link["style"] = "position: absolute; width: 100%; height: 100%; left: 0px; top: 0px; z-index: 10;"
      link["target"] = "_blank"

      # 包装媒体元素
      wrapper = doc.document.create_element("div")
      wrapper["style"] = "position: relative; display: inline-block;"

      media.add_previous_sibling(wrapper)
      wrapper.add_child(media)
      wrapper.add_child(link)
    end

    doc.to_html
  end

  # 下载并存储微信图片
  def self.download_and_store_wechat_image(image_url)
    require 'net/http'
    require 'uri'
    require 'openssl'
    require 'digest'

    begin
      # 生成文件名
      url_hash = Digest::MD5.hexdigest(image_url)
      file_extension = image_url.match(/\.(jpg|jpeg|png|gif|webp)/i)&.captures&.first || 'jpg'
      filename = "wechat_#{url_hash}.#{file_extension}"

      # 下载图片
      image_data = fetch_wechat_image_data(image_url)
      return nil unless image_data

      # 存储到本地public目录（生产环境建议使用OSS）
      upload_dir = Rails.root.join('public', 'uploads', 'wechat_images')
      FileUtils.mkdir_p(upload_dir) unless Dir.exist?(upload_dir)

      file_path = upload_dir.join(filename)

      # 如果文件已存在，直接返回URL
      if File.exist?(file_path)
        return "/uploads/wechat_images/#{filename}"
      end

      # 写入文件
      File.binwrite(file_path, image_data)

      Rails.logger.info "微信图片已下载并存储: #{filename}"
      "/uploads/wechat_images/#{filename}"

    rescue StandardError => e
      Rails.logger.error "下载微信图片失败: #{e.message}"
      nil
    end
  end

  # 检查是否为微信图片URL
  def self.is_wechat_image_url?(url)
    return false unless url.is_a?(String)

    # 支持的微信图片域名
    wechat_domains = [
      'mmbiz.qpic.cn',
      'mmecoa.qpic.cn',
      'mmbiz.qlogo.cn',
      'wx.qlogo.cn'
    ]

    wechat_domains.any? { |domain| url.include?(domain) }
  end

  # 获取微信图片数据
  def self.fetch_wechat_image_data(url)
    uri = URI(url)
    http = Net::HTTP.new(uri.host, uri.port)

    if uri.scheme == 'https'
      http.use_ssl = true
      http.verify_mode = OpenSSL::SSL::VERIFY_NONE
      http.ssl_timeout = 30
    end

    http.open_timeout = 10
    http.read_timeout = 30

    request = Net::HTTP::Get.new(uri)

    # 设置微信兼容的请求头
    request['User-Agent'] = 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.0(0x18000029) NetType/WIFI Language/zh_CN'
    request['Referer'] = 'https://mp.weixin.qq.com/'
    request['Accept'] = 'image/webp,image/apng,image/*,*/*;q=0.8'

    response = http.request(request)

    case response
    when Net::HTTPSuccess
      response.body
    when Net::HTTPRedirection
      # 处理重定向
      location = response['Location']
      return fetch_wechat_image_data(location) if location
      nil
    else
      Rails.logger.warn "获取微信图片失败: #{response.code} #{response.message}"
      nil
    end

  rescue StandardError => e
    Rails.logger.error "获取微信图片异常: #{e.message}"
    nil
  end

  # 处理微信视频的专用方法
  def self.process_wechat_videos(doc, source_url)
    # 原有的微信视频选择器 - 保持原有处理方式
    original_video_selectors = [
      "iframe.video_iframe", # 标准微信视频iframe
      "iframe[data-mpvid]",           # 带有视频ID的iframe
      "div[data-mpvid]",              # 视频容器div
      ".video_iframe",                # 视频iframe类
      'iframe[src*="mp.weixin.qq.com"]' # 微信域名的iframe
    ]

    # 新增的视频选择器 - 主要针对mp-common-videosnap等新类型
    new_video_selectors = [
      "mp-common-videosnap",          # 微信视频号组件
      "mp-common-videosnap div",      # 微信视频号内部div
      "[class*='videosnap']"          # 包含videosnap的类名
    ]

    # 处理原有的微信视频类型 - 保持原有逻辑（添加链接覆盖，不替换样式）
    original_video_selectors.each do |selector|
      doc.css(selector).each do |element|
        # 检查是否已经被处理过，避免重复处理
        next if element["data-video-processed"] == "true"

        Rails.logger.info "处理原有视频元素: #{selector} - #{element.name} - #{element['class']}"

        # 为原有视频添加链接覆盖，保持原有显示效果
        add_video_link_overlay(element, source_url)

        # 标记为已处理
        element["data-video-processed"] = "true"
      rescue StandardError => e
        Rails.logger.warn "处理原有视频元素失败 (#{selector}): #{e.message}"
        # 添加备用提示
        element["title"] = "微信视频，请在原文中观看"
        element["style"] = "#{element['style']}; border: 2px dashed #ccc; padding: 10px;"
      end
    end

    # 处理新增的视频类型 - 只处理mp-common-videosnap等新组件
    new_video_selectors.each do |selector|
      doc.css(selector).each do |element|
        # 检查是否已经被原有选择器处理过
        next if element["data-video-processed"] == "true"

        Rails.logger.info "处理新增视频元素: #{selector} - #{element.name} - #{element['class']}"
        process_single_video_element(element, source_url)
      rescue StandardError => e
        Rails.logger.warn "处理新增视频元素失败 (#{selector}): #{e.message}"
        # 添加备用提示
        element["title"] = "微信视频，请在原文中观看"
        element["style"] = "#{element['style']}; border: 2px dashed #ccc; padding: 10px;"
      end
    end
  end

  # 检查元素是否已经被处理过
  def self.element_already_processed?(element)
    # 检查是否有我们添加的处理标记
    element["class"]&.include?("wechat-video") ||
    element["data-video"] == "true" ||
    element["data-video-processed"] == "true" ||
    element.parent&.[]("class")&.include?("wechat-video-container")
  end

  # 为视频元素添加链接覆盖（保持原有显示效果）
  def self.add_video_link_overlay(element, source_url)
    Rails.logger.info "为视频元素添加链接覆盖: #{element.name}, class: #{element['class']}"

    # 🔧 修复：处理data-src问题
    if element.name == "iframe" && element["data-src"].present? && element["src"].blank?
      Rails.logger.info "检测到iframe使用data-src，转换为src以确保显示"
      element["src"] = element["data-src"]

      # 设置iframe的基本样式，确保有合适的尺寸
      current_style = element["style"] || ""
      unless current_style.include?("width") || current_style.include?("height")
        element["style"] = "#{current_style}; width: 100%; height: 300px; min-height: 200px;"
      end
    end

    # 🔧 修复：对于仍然可能显示为空白的iframe，直接替换为视频占位符
    if element.name == "iframe" && element["data-mpvid"].present?
      Rails.logger.info "检测到微信视频iframe，使用增强的视频占位符替换"

      # 提取视频信息
      video_id = element["data-mpvid"]

      # 创建增强的视频占位符，包含视频ID信息
      video_html = %{
        <div class="wechat-video-container" style="position: relative; display: inline-block; width: 100%; max-width: 500px; height: 280px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 12px; margin: 15px 0; box-shadow: 0 4px 16px rgba(0,0,0,0.2); overflow: hidden;">
          <a href="#{source_url}"
             target="_blank"
             style="position: absolute; top: 0; left: 0; width: 100%; height: 100%;
                    display: flex; flex-direction: column; align-items: center;
                    justify-content: center; text-decoration: none; color: white;
                    transition: all 0.3s ease;"
             onmouseover="this.style.background='rgba(0,0,0,0.2)'"
             onmouseout="this.style.background='transparent'">
            <div style="font-size: 64px; margin-bottom: 15px; filter: drop-shadow(0 2px 4px rgba(0,0,0,0.3));">🎬</div>
            <div style="font-size: 18px; font-weight: 600; text-align: center; margin-bottom: 8px; text-shadow: 0 1px 2px rgba(0,0,0,0.5);">
              微信视频内容
            </div>
            <div style="font-size: 13px; opacity: 0.9; text-align: center; text-shadow: 0 1px 2px rgba(0,0,0,0.5);">
              点击查看原文观看视频
            </div>
            <div style="position: absolute; bottom: 12px; right: 12px;
                        background: rgba(255,255,255,0.2); backdrop-filter: blur(10px);
                        color: white; padding: 6px 12px; border-radius: 20px;
                        font-size: 11px; font-weight: 500;">
              ID: #{video_id[0..10]}...
            </div>
          </a>
        </div>
      }

      # 替换原元素
      new_element = Nokogiri::HTML::DocumentFragment.parse(video_html)
      element.replace(new_element)
      return
    end

    # 原有的链接覆盖逻辑（作为备用方案）
    begin
      # 创建链接覆盖层
      link = element.document.create_element("a")
      link["href"] = source_url
      link["style"] = "position: absolute; width: 100%; height: 100%; left: 0px; top: 0px; z-index: 10; background: rgba(0,0,0,0.1);"
      link["target"] = "_blank"
      link["title"] = "点击查看原文"

      # 包装媒体元素
      wrapper = element.document.create_element("div")
      wrapper["style"] = "position: relative; display: inline-block; width: 100%;"

      element.add_previous_sibling(wrapper)
      wrapper.add_child(element)
      wrapper.add_child(link)

      Rails.logger.info "成功添加视频链接覆盖"
    rescue => e
      Rails.logger.warn "添加视频链接覆盖失败: #{e.message}"
    end
  end

  # 处理单个视频元素
  def self.process_single_video_element(element, source_url)
    Rails.logger.info "处理视频元素: #{element.name}, class: #{element['class']}, id: #{element['id']}"

    # 提取视频信息 - 扩展更多属性检查
    video_id = element["data-mpvid"] || element["data-vid"] || element["data-video-id"] || element["vid"]
    cover_url = element["data-cover"] || element["data-src"] || element["poster"] || element["data-poster"]

    # 如果有封面URL，进行URL解码
    cover_url = CGI.unescape(cover_url) if cover_url

    # 尝试从元素内部查找封面图片 - 扩展查找范围
    if cover_url.blank?
      # 查找各种可能的图片元素
      img_selectors = ["img", "img[src]", "img[data-src]", "[style*='background-image']"]
      img_selectors.each do |selector|
        img_element = element.css(selector).first
        if img_element
          cover_url = img_element["src"] || img_element["data-src"] ||
                     extract_background_image_url(img_element["style"])
          break if cover_url.present?
        end
      end
    end

    # 特殊处理 mp-common-videosnap 组件
    if element.name == "mp-common-videosnap" || element["class"]&.include?("videosnap")
      Rails.logger.info "检测到微信视频号组件: mp-common-videosnap"
      handle_videosnap_component(element, source_url, cover_url)
    elsif video_id.present? || cover_url.present?
      # 如果找到了视频信息，创建可点击的视频封面
      create_video_cover_html(element, cover_url, source_url)
    else
      # 没有找到视频信息，创建通用的视频占位符
      create_generic_video_placeholder(element, source_url)
    end
  end

  # 创建视频封面HTML
  def self.create_video_cover_html(element, cover_url, source_url)
    # 使用默认封面如果没有找到
    if cover_url.blank?
      cover_url = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDMyMCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMjAiIGhlaWdodD0iMTgwIiBmaWxsPSIjRjVGNUY1Ii8+Cjx0ZXh0IHg9IjE2MCIgeT0iOTAiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OTk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+5b6u5L+h6KeG6aKRPC90ZXh0Pgo8L3N2Zz4K"
    end

    video_html = %{
      <div class="wechat-video-container" style="position: relative; display: inline-block; max-width: 100%; border-radius: 8px; overflow: hidden; margin: 10px 0; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
        <img src="#{cover_url}"
             alt="视频封面"
             style="width: 100%; height: auto; display: block; min-height: 180px; object-fit: cover;"
             referrerpolicy="no-referrer"
             onerror="this.src='data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDMyMCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMjAiIGhlaWdodD0iMTgwIiBmaWxsPSIjRjVGNUY1Ii8+Cjx0ZXh0IHg9IjE2MCIgeT0iOTAiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNCIgZmlsbD0iIzk5OTk5OSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+5b6u5L+h6KeG6aKRPC90ZXh0Pgo8L3N2Zz4K'" />
        <a href="#{source_url}"
           target="_blank"
           style="position: absolute; top: 0; left: 0; width: 100%; height: 100%;
                  background: rgba(0,0,0,0.3); display: flex; align-items: center;
                  justify-content: center; text-decoration: none; cursor: pointer;
                  transition: background 0.3s ease;">
          <div style="width: 80px; height: 80px; background: rgba(255,255,255,0.95);
                      border-radius: 50%; display: flex; align-items: center;
                      justify-content: center; font-size: 32px; color: #333;
                      box-shadow: 0 4px 12px rgba(0,0,0,0.3); transition: transform 0.2s ease;"
               onmouseover="this.style.transform='scale(1.1)'"
               onmouseout="this.style.transform='scale(1)'">
            ▶
          </div>
        </a>
        <div style="position: absolute; bottom: 12px; right: 12px;
                    background: rgba(0,0,0,0.8); color: white; padding: 6px 12px;
                    border-radius: 20px; font-size: 12px; font-weight: 500;">
          📹 微信视频
        </div>
      </div>
    }

    # 替换原元素
    new_element = Nokogiri::HTML::DocumentFragment.parse(video_html)
    element.replace(new_element)
  end

  # 创建通用视频占位符
  def self.create_generic_video_placeholder(element, source_url)
    placeholder_html = %{
      <div class="wechat-video-placeholder" style="position: relative; display: inline-block; width: 100%; max-width: 400px; height: 200px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 8px; margin: 10px 0; box-shadow: 0 4px 12px rgba(0,0,0,0.2);">
        <a href="#{source_url}"
           target="_blank"
           style="position: absolute; top: 0; left: 0; width: 100%; height: 100%;
                  display: flex; flex-direction: column; align-items: center;
                  justify-content: center; text-decoration: none; color: white;">
          <div style="font-size: 48px; margin-bottom: 10px;">🎬</div>
          <div style="font-size: 16px; font-weight: 500; text-align: center;">
            微信视频内容<br>
            <span style="font-size: 12px; opacity: 0.8;">点击查看原文</span>
          </div>
        </a>
      </div>
    }

    new_element = Nokogiri::HTML::DocumentFragment.parse(placeholder_html)
    element.replace(new_element)
  end

  # 从样式中提取背景图片URL
  def self.extract_background_image_url(style_attr)
    return nil unless style_attr.present?

    match = style_attr.match(/background-image:\s*url\(['"]?([^'"]+)['"]?\)/)
    match ? match[1] : nil
  end

  # 处理微信视频号组件
  def self.handle_videosnap_component(element, source_url, cover_url)
    Rails.logger.info "处理微信视频号组件"

    # 尝试从组件内部提取更多信息
    if cover_url.blank?
      # 查找可能的封面图片
      cover_img = element.css("img").first
      cover_url = cover_img["src"] || cover_img["data-src"] if cover_img
    end

    # 查找视频相关的数据属性
    video_data = {}
    element.attributes.each do |name, attr|
      if name.start_with?("data-") && (name.include?("video") || name.include?("vid"))
        video_data[name] = attr.value
      end
    end

    Rails.logger.info "视频号数据: #{video_data}"

    # 创建视频号专用的占位符
    create_videosnap_placeholder(element, source_url, cover_url, video_data)
  end

  # 创建视频号专用占位符
  def self.create_videosnap_placeholder(element, source_url, cover_url, video_data)
    # 使用默认的视频号封面
    if cover_url.blank?
      cover_url = "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIwIiBoZWlnaHQ9IjE4MCIgdmlld0JveD0iMCAwIDMyMCAxODAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIzMjAiIGhlaWdodD0iMTgwIiBmaWxsPSIjMUQ5QkY0Ii8+Cjx0ZXh0IHg9IjE2MCIgeT0iODAiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxNiIgZmlsbD0iI0ZGRkZGRiIgdGV4dC1hbmNob3I9Im1pZGRsZSI+5b6u5L+h6KeG6aKR5Y+3PC90ZXh0Pgo8dGV4dCB4PSIxNjAiIHk9IjEwMCIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjEyIiBmaWxsPSIjRkZGRkZGIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBvcGFjaXR5PSIwLjgiPueCueWHu+afpeeci+WOn+aWhzwvdGV4dD4KPC9zdmc+Cg=="
    end

    videosnap_html = %{
      <div class="wechat-videosnap-container" style="position: relative; display: inline-block; max-width: 100%; border-radius: 8px; overflow: hidden; margin: 10px 0; box-shadow: 0 2px 8px rgba(0,0,0,0.1); border: 2px solid #1D9BF4;">
        <img src="#{cover_url}"
             alt="微信视频号封面"
             style="width: 100%; height: auto; display: block; min-height: 180px; object-fit: cover;"
             referrerpolicy="no-referrer" />
        <a href="#{source_url}"
           target="_blank"
           style="position: absolute; top: 0; left: 0; width: 100%; height: 100%;
                  display: flex; align-items: center; justify-content: center;
                  text-decoration: none; background: rgba(0,0,0,0.3);
                  transition: background 0.3s ease;">
          <div style="background: rgba(29, 155, 244, 0.9); border-radius: 50%; width: 60px; height: 60px;
                      display: flex; align-items: center; justify-content: center;
                      box-shadow: 0 4px 12px rgba(0,0,0,0.3);">
            <div style="color: white; font-size: 24px; margin-left: 3px;">▶</div>
          </div>
        </a>
        <div style="position: absolute; bottom: 8px; left: 8px; background: rgba(29, 155, 244, 0.9);
                    color: white; padding: 4px 8px; border-radius: 4px; font-size: 12px;">
          微信视频号
        </div>
      </div>
    }

    new_element = Nokogiri::HTML::DocumentFragment.parse(videosnap_html)
    element.replace(new_element)
  end

  # 为图片添加loading占位符
  def self.add_image_loading_placeholder(img)
    # 创建loading占位符的样式
    loading_style = [
      "position: relative",
      "background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%)",
      "background-size: 200% 100%",
      "animation: loading-shimmer 1.5s infinite",
      "min-height: 200px",
      "display: flex",
      "align-items: center",
      "justify-content: center",
      "color: #999",
      "font-size: 14px"
    ].join("; ")

    # 添加loading相关的属性和事件
    img["data-loading"] = "true"
    img["style"] = "#{img['style']}; #{loading_style}"

    # 添加加载完成后的处理
    img["onload"] = "this.removeAttribute('data-loading'); this.style.animation='none'; this.style.background='none';"

    # 更新错误处理，包含loading状态清理
    original_onerror = img["onerror"] || ""
    img["onerror"] = "#{original_onerror}; this.removeAttribute('data-loading'); this.style.animation='none';"
  end

  # 添加CSS动画样式到文档头部
  def self.add_loading_animation_styles(doc)
    # 检查是否已经添加过样式
    return if doc.css("style#image-loading-styles").any?

    style_content = <<~CSS
      <style id="image-loading-styles">
        @keyframes loading-shimmer {
          0% { background-position: -200% 0; }
          100% { background-position: 200% 0; }
        }

        img[data-loading]::before {
          content: "图片加载中...";
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          color: #999;
          font-size: 14px;
          z-index: 1;
        }

        img[data-loading] {
          background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%) !important;
          background-size: 200% 100% !important;
          animation: loading-shimmer 1.5s infinite !important;
          min-height: 200px !important;
        }
      </style>
    CSS

    # 尝试添加到文档中
    if doc.respond_to?(:document) && doc.document
      # 如果是DocumentFragment，尝试添加到开头
      doc.prepend_child(Nokogiri::HTML::DocumentFragment.parse(style_content))
    else
      # 如果是完整文档，添加到head或body
      head = doc.at("head")
      if head
        head.add_child(Nokogiri::HTML::DocumentFragment.parse(style_content))
      else
        body = doc.at("body") || doc
        body.prepend_child(Nokogiri::HTML::DocumentFragment.parse(style_content))
      end
    end
  end

  def self.create_activity_from_wechat_data(data, options = {})
    return unless data[:title].present?

    # 查找或创建Origin，根据作者信息自动关联
    origin = if options[:origin_id].present?
               Serve::Origin.find_by(id: options[:origin_id])
             else
               find_or_create_wechat_origin(data[:author])
             end

    # 如果指定的origin_id不存在，回退到自动创建
    origin ||= find_or_create_wechat_origin(data[:author])

    # 查找或初始化Activity
    activity = find_or_initialize_by(
      name: data[:title],
      origin: origin,
      submodule_id: options[:submodule_id] || 1
    )

    # 更新Activity属性
    activity.assign_attributes(
      effective_at: data[:publish_time],
      state: options[:state] || "pending",
      content: {
        content: [
          {
            key: data[:publish_time]&.strftime("%Y%m%d%H%M%S") || Time.current.strftime("%Y%m%d%H%M%S"),
            body: data[:content_html]
          }
        ]
      },
      model_payload: {
        author: data[:author],
        link: data[:original_url],
        cover_image_url: data[:cover_image_url],
        crawled_at: Time.current
      },
      tag_ids: options[:tag_ids].presence || []
    )

    # 强制设置published_at字段，确保它被标记为"脏"字段
    if data[:publish_time].present?
      activity.published_at = data[:publish_time]
      Rails.logger.info "强制设置published_at: #{data[:publish_time]}"
    end

    # 保存并返回
    if activity.save
      Rails.logger.info "成功保存微信文章: #{activity.name} -> Origin: #{origin.name}"
      Rails.logger.info "最终published_at: #{activity.published_at}, effective_at: #{activity.effective_at}"
      activity
    else
      Rails.logger.error "保存微信文章失败: #{activity.errors.full_messages.join(', ')}"
      nil
    end
  end

  def self.find_or_create_wechat_origin(author = nil)
    # 如果有作者信息，尝试根据作者名称查找对应的origin
    if author.present?
      Rails.logger.info "开始查找Origin，作者: #{author}"

      # 只在code="公众号"中查找
      existing_origin = Serve::Origin.where(code: "公众号", name: author).first

      if existing_origin
        Rails.logger.info "找到匹配的Origin: #{existing_origin.name} " \
                          "(ID: #{existing_origin.id}, Code: #{existing_origin.code}, 作者: #{author})"
        # 更新payload中的author信息
        updated_payload = (existing_origin.payload || {}).merge(
          author: author,
          matched_at: Time.current
        )
        existing_origin.update(payload: updated_payload)
        return existing_origin
      end

      # 如果没有找到，创建新的origin，使用原始作者名称
      Rails.logger.info "为作者 #{author} 创建新的Origin"

      Serve::Origin.find_or_create_by(
        name: author,
        code: "公众号"
      ) do |origin|
        origin.state = "used"
        origin.app_id = 1 # 设置app_id
        origin.position = 0 # 设置position
        origin.payload = {
          author: author,
          created_at: Time.current
        }
      end
    else
      # 如果没有作者信息，使用默认的微信公众号origin
      Rails.logger.info "使用默认微信公众号Origin"
      Serve::Origin.find_or_create_by(code: "微信公众号") do |origin|
        origin.name = "微信公众号"
        origin.state = "used"
        origin.app_id = 1 # 设置app_id
        origin.position = 0 # 设置position
      end
    end
  end

  # 提取微信文章作者的改进方法
  def self.extract_wechat_author(doc)
    # 尝试多种选择器来提取作者信息
    author_selectors = [
      ".rich_media_meta_text",           # 标准的作者信息位置
      ".profile_nickname",               # 公众号昵称
      ".wx_follow_nickname",             # 关注按钮中的昵称
      "#js_name",                        # 公众号名称
      ".account_nickname_inner",         # 账号昵称内部
      ".weui-desktop-account__nickname", # 桌面版账号昵称
      "[data-account]",                  # 带有账号数据的元素
      ".rich_media_meta .rich_media_meta_text" # 元信息中的文本
    ]

    author_selectors.each do |selector|
      element = doc.css(selector).first
      next unless element

      text = element.text&.strip
      next if text.blank?

      # 过滤掉明显不是作者名的内容
      next if text.match?(/^\d{4}[-年]\d{1,2}[-月]\d{1,2}/) # 日期格式
      next if text.match?(/^\d+:\d+$/) # 时间格式
      next if text.length > 50 # 太长的文本
      next if text.match?(/^(原创|转载|来源|编辑|责编|审核)[:：]/) # 编辑信息前缀

      Rails.logger.info "通过选择器 '#{selector}' 提取到作者: #{text}"
      return text
    end

    # 如果标准选择器都没找到，尝试从页面标题或meta信息中提取
    page_title = doc.css("title").first&.text
    if page_title&.include?("_") && page_title.split("_").length >= 2
      potential_author = page_title.split("_").last&.strip
      if potential_author && potential_author.length < 20
        Rails.logger.info "从页面标题提取到作者: #{potential_author}"
        return potential_author
      end
    end

    Rails.logger.warn "未能提取到作者信息"
    nil
  end

  # 提取微信文章发布时间文本的改进方法
  def self.extract_wechat_publish_time_text(doc, html_content = nil)
    # 1. 首先尝试从HTML内容中提取Unix时间戳
    if html_content.present?
      begin
        # 强制UTF-8编码
        html_content = html_content.force_encoding("UTF-8") if html_content.respond_to?(:force_encoding)

        # 查找Unix时间戳（10位数字）
        timestamps = html_content.scan(/\b(\d{10})\b/)

        # 收集所有有效的时间戳
        valid_timestamps = []
        timestamps.flatten.uniq.each do |timestamp|
          time = Time.at(timestamp.to_i)
          # 检查时间是否合理（2020年到2030年之间）
          if time.year.between?(2020, 2030)
            valid_timestamps << { timestamp: timestamp, time: time }
            Rails.logger.info "找到有效时间戳: #{timestamp} -> #{time}"
          end
        end

        # 如果找到多个时间戳，选择最合适的一个
        if valid_timestamps.any?
          selected_time = select_best_timestamp(valid_timestamps, html_content)
          if selected_time
            Rails.logger.info "从HTML中提取到最佳时间戳: #{selected_time[:timestamp]} -> #{selected_time[:time]}"
            return selected_time[:time].strftime("%Y年%m月%d日 %H:%M")
          end
        end
      rescue StandardError => e
        Rails.logger.warn "从HTML提取时间戳失败: #{e.message}"
      end
    end

    # 2. 尝试使用XPath提取 #publish_time
    begin
      publish_time_element = doc.xpath('//*[@id="publish_time"]').first
      if publish_time_element
        text = publish_time_element.text&.strip
        if text.present?
          Rails.logger.info "通过XPath '//*[@id=\"publish_time\"]' 提取到时间文本: #{text}"
          return text
        end
      end
    rescue StandardError => e
      Rails.logger.warn "XPath提取发布时间失败: #{e.message}"
    end

    # 3. 尝试多种选择器来提取发布时间
    time_selectors = [
      "#publish_time", # 标准发布时间ID
      ".rich_media_meta_text", # 元信息文本（可能包含时间）
      ".rich_media_meta .rich_media_meta_text", # 元信息中的文本
      "[data-time]",                     # 带有时间数据的元素
      ".time",                           # 时间类
      ".publish-time" # 发布时间类
    ]

    time_selectors.each do |selector|
      elements = doc.css(selector)
      elements.each do |element|
        text = element.text&.strip
        next if text.blank?

        # 检查是否包含时间格式
        next unless text.match?(/\d{4}[-年]\d{1,2}[-月]\d{1,2}/) ||
                    text.match?(/\d{1,2}:\d{2}/) ||
                    text.match?(/今天|昨天|前天|\d+天前|\d+小时前|\d+分钟前/)

        Rails.logger.info "通过选择器 '#{selector}' 提取到时间文本: #{text}"
        return text
      end
    end

    Rails.logger.warn "未能提取到发布时间信息"
    nil
  end

  # 从多个时间戳中选择最佳的发布时间
  def self.select_best_timestamp(valid_timestamps, html_content)
    return valid_timestamps.first if valid_timestamps.size == 1

    # 按优先级排序时间戳
    scored_timestamps = valid_timestamps.map do |ts_info|
      score = calculate_timestamp_score(ts_info, html_content)
      { **ts_info, score: score }
    end

    # 按分数排序，选择最高分的
    best_timestamp = scored_timestamps.max_by { |ts| ts[:score] }
    Rails.logger.info "时间戳评分结果: #{scored_timestamps.map do |ts|
      "#{ts[:timestamp]}(#{ts[:time].strftime('%Y-%m-%d %H:%M')}) -> #{ts[:score]}"
    end.join(', ')}"
    Rails.logger.info "选择最佳时间戳: #{best_timestamp[:timestamp]} -> #{best_timestamp[:time]} (分数: #{best_timestamp[:score]})"

    best_timestamp
  end

  # 计算时间戳的评分，分数越高越可能是发布时间
  def self.calculate_timestamp_score(ts_info, html_content)
    timestamp = ts_info[:timestamp]
    time = ts_info[:time]
    score = 0

    # 1. 时间合理性评分（最近的时间得分更高，但不能太新）
    now = Time.current
    days_ago = (now - time) / 1.day

    if days_ago < 0
      # 未来时间，分数很低
      score -= 1000
    elsif days_ago < 1
      # 1天内，可能是抓取时间而不是发布时间
      score += 10
    elsif days_ago < 30
      # 1个月内，很可能是发布时间
      score += 100
    elsif days_ago < 365
      # 1年内，可能是发布时间
      score += 80
    elsif days_ago < 365 * 3
      # 3年内，可能是发布时间
      score += 60
    else
      # 太久远，可能不是发布时间
      score += 20
    end

    # 2. 在HTML中的位置和上下文评分
    if html_content
      # 查找时间戳在HTML中的上下文
      timestamp_contexts = find_timestamp_contexts(timestamp, html_content)

      timestamp_contexts.each do |context|
        # 如果时间戳附近有发布时间相关的关键词
        score += 50 if context.match?(/publish|发布|时间|date|time/i)

        # 如果在meta标签中
        score += 30 if context.match?(/meta|property|content/i)

        # 如果在script标签中但不是明显的发布时间上下文
        score -= 20 if context.match?(/script/i) && !context.match?(/publish|发布|time/i)
      end
    end

    # 3. 时间戳出现频率评分（出现次数少的更可能是发布时间）
    occurrence_count = html_content&.scan(/\b#{timestamp}\b/)&.size || 1
    if occurrence_count == 1
      score += 20  # 唯一出现，更可能是发布时间
    elsif occurrence_count <= 3
      score += 10  # 少量出现
    else
      score -= 10  # 大量出现，可能是其他用途
    end

    score
  end

  # 查找时间戳在HTML中的上下文
  def self.find_timestamp_contexts(timestamp, html_content)
    contexts = []

    # 查找时间戳前后100个字符的上下文
    html_content.scan(/(.{0,100})#{Regexp.escape(timestamp)}(.{0,100})/m) do |before, after|
      contexts << "#{before}#{timestamp}#{after}"
    end

    contexts
  end

  # 处理动态内容：查找并点击展开按钮
  def self.handle_dynamic_content(driver, wait)
    Rails.logger.info "开始处理动态内容..."

    # 常见的展开按钮选择器
    expand_selectors = [
      # 微信公众号常见的展开按钮
      '.rich_media_tool_more',
      '.rich_media_tool_more_btn',
      '.js_more_btn',
      '.more_btn',
      '.expand_btn',
      '.unfold_btn',
      '.show_more',
      '.read_more',
      # 通用的展开按钮文本
      "//span[contains(text(), '展开全文')]",
      "//span[contains(text(), '显示全部')]",
      "//span[contains(text(), '查看更多')]",
      "//span[contains(text(), '阅读全文')]",
      "//a[contains(text(), '展开全文')]",
      "//a[contains(text(), '显示全部')]",
      "//a[contains(text(), '查看更多')]",
      "//a[contains(text(), '阅读全文')]",
      # 英文版本
      "//span[contains(text(), 'Show more')]",
      "//span[contains(text(), 'Read more')]",
      "//a[contains(text(), 'Show more')]",
      "//a[contains(text(), 'Read more')]"
    ]

    expand_selectors.each do |selector|
      begin
        # 尝试查找展开按钮
        if selector.start_with?('//')
          # XPath选择器
          elements = driver.find_elements(xpath: selector)
        else
          # CSS选择器
          elements = driver.find_elements(css: selector)
        end

        elements.each do |element|
          # 检查元素是否可见和可点击
          if element.displayed? && element.enabled?
            Rails.logger.info "找到展开按钮: #{selector}"

            # 滚动到元素位置
            driver.execute_script("arguments[0].scrollIntoView(true);", element)
            sleep(0.5)

            # 点击展开按钮
            begin
              element.click
              Rails.logger.info "成功点击展开按钮"
              sleep(1) # 等待内容展开

              # 检查是否有更多展开按钮（限制次数）
              handle_additional_expand_buttons(driver)

              return true
            rescue StandardError => e
              Rails.logger.warn "点击展开按钮失败: #{e.message}"
              # 尝试使用JavaScript点击
              begin
                driver.execute_script("arguments[0].click();", element)
                Rails.logger.info "使用JavaScript成功点击展开按钮"
                sleep(1)
                handle_additional_expand_buttons(driver)
                return true
              rescue StandardError => js_e
                Rails.logger.warn "JavaScript点击也失败: #{js_e.message}"
              end
            end
          end
        end
      rescue StandardError => e
        Rails.logger.debug "查找展开按钮失败 (#{selector}): #{e.message}"
        next
      end
    end

    # 尝试处理懒加载图片
    handle_lazy_loading_images(driver)

    Rails.logger.info "动态内容处理完成"
    false
  end

  # 处理额外的展开按钮（有些文章可能有多个展开按钮）
  def self.handle_additional_expand_buttons(driver)
    2.times do |i|  # 减少到2次
      Rails.logger.info "检查是否有额外的展开按钮 (第#{i+1}次)"

      additional_selectors = [
        '.rich_media_tool_more:not(.rich_media_tool_more_hide)',
        '.js_more_btn:not(.hide)',
        "//span[contains(text(), '展开全文') and not(contains(@style, 'display: none'))]"
      ]

      found_button = false
      additional_selectors.each do |selector|
        begin
          if selector.start_with?('//')
            elements = driver.find_elements(xpath: selector)
          else
            elements = driver.find_elements(css: selector)
          end

          elements.each do |element|
            if element.displayed? && element.enabled?
              Rails.logger.info "找到额外的展开按钮"
              driver.execute_script("arguments[0].scrollIntoView(true);", element)
              sleep(1)

              begin
                element.click
                Rails.logger.info "成功点击额外的展开按钮"
                found_button = true
                sleep(2)
                break
              rescue StandardError
                driver.execute_script("arguments[0].click();", element)
                Rails.logger.info "使用JavaScript点击额外的展开按钮"
                found_button = true
                sleep(2)
                break
              end
            end
          end
          break if found_button
        rescue StandardError => e
          Rails.logger.debug "查找额外展开按钮失败: #{e.message}"
        end
      end

      break unless found_button
    end
  end

  # 处理懒加载图片
  def self.handle_lazy_loading_images(driver)
    Rails.logger.info "处理懒加载图片..."

    begin
      # 滚动页面以触发懒加载
      driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
      sleep(1)

      # 查找懒加载图片并触发加载（限制数量）
      lazy_images = driver.find_elements(css: "img[data-src], img[data-original], img[data-lazy]")

      lazy_images.first(10).each do |img|  # 只处理前10张图片
        begin
          # 滚动到图片位置
          driver.execute_script("arguments[0].scrollIntoView(true);", img)
          sleep(0.2)

          # 触发懒加载
          driver.execute_script("""
            var img = arguments[0];
            if (img.dataset.src) {
              img.src = img.dataset.src;
            } else if (img.dataset.original) {
              img.src = img.dataset.original;
            } else if (img.dataset.lazy) {
              img.src = img.dataset.lazy;
            }
          """, img)
        rescue StandardError => e
          Rails.logger.debug "处理懒加载图片失败: #{e.message}"
        end
      end

      # 最后滚动回顶部
      driver.execute_script("window.scrollTo(0, 0);")
      sleep(0.5)

      Rails.logger.info "懒加载图片处理完成"
    rescue StandardError => e
      Rails.logger.warn "处理懒加载图片异常: #{e.message}"
    end
  end

end
