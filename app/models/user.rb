class User < ApplicationRecord

  attr_accessor :member_data, :account_data

  attribute :birthday, :date, comment: "生日"
  attribute :political_birthday, :date, comment: "政治生日"

  after_create :verify_user_for_dingtalk, unless: :account_data

  scope :find_date_field, lambda { |_rule = nil, field = "birthday", date_str = Time.zone.today.to_s|
    date = begin
      Date.parse(date_str)
    rescue StandardError
      Time.zone.today
    end
    sanitized_field = ActiveRecord::Base.connection.quote_column_name(field) # 对字段名进行转义处理
    where("extract(month from #{sanitized_field}) = ? AND extract(day from #{sanitized_field}) = ?", date.month, date.day)
  }

  scope :find_date_field_within, lambda { |_rule, start_date_str, end_date_str, field = "birthday"|
    start_date = begin
      Date.parse(start_date_str)
    rescue StandardError
      Time.zone.today
    end
    end_date = begin
      Date.parse(end_date_str)
    rescue StandardError
      Time.zone.today
    end
    sanitized_field = ActiveRecord::Base.connection.quote_column_name(field) # 对字段名进行转义处理

    if start_date.month == end_date.month
      # 同一个月内
      where("extract(month from #{sanitized_field}) = ? AND extract(day from #{sanitized_field}) BETWEEN ? AND ?",
            start_date.month, start_date.day, end_date.day)
    else
      # 跨月查询：使用精确的日期比较逻辑
      # 为了处理生日这种只有月日的字段，我们需要构造当年的完整日期进行比较
      current_year = Time.zone.today.year

      # 构造当年的开始和结束日期
      Date.new(current_year, start_date.month, start_date.day)
      Date.new(current_year, end_date.month, end_date.day)

      # 检查是否跨年（比如12月到1月）
      if start_date.month > end_date.month
        # 跨年情况：需要查询两个范围
        # 1. 当年的开始日期到年底
        # 2. 年初到当年的结束日期
        where(
          "(
            (extract(month from #{sanitized_field}) = ? AND extract(day from #{sanitized_field}) >= ?) OR
            (extract(month from #{sanitized_field}) > ?) OR
            (extract(month from #{sanitized_field}) < ?) OR
            (extract(month from #{sanitized_field}) = ? AND extract(day from #{sanitized_field}) <= ?)
          )",
          start_date.month, start_date.day,
          start_date.month,
          end_date.month,
          end_date.month, end_date.day
        )
      else
        # 同年跨月情况：使用更精确的条件
        conditions = []
        params = []

        # 开始月份的条件：该月且日期>=开始日期
        conditions << "(extract(month from #{sanitized_field}) = ? AND extract(day from #{sanitized_field}) >= ?)"
        params += [start_date.month, start_date.day]

        # 中间完整月份的条件
        if end_date.month - start_date.month > 1
          middle_months = (start_date.month + 1...end_date.month).to_a
          if middle_months.any?
            conditions << "extract(month from #{sanitized_field}) IN (?)"
            params << middle_months
          end
        end

        # 结束月份的条件：该月且日期<=结束日期
        conditions << "(extract(month from #{sanitized_field}) = ? AND extract(day from #{sanitized_field}) <= ?)"
        params += [end_date.month, end_date.day]

        where(conditions.join(" OR "), *params)
      end
    end
  }

  scope :serve_rule_create_at_range, lambda { |_rule, period, unit = "month", duration = 7, orgs_name: nil|
    created_at_gteq = period.to_i.try(unit).ago(Time.zone.now).beginning_of_day
    created_at_lteq = period.to_i.try(unit).ago(Time.zone.now).end_of_day + duration.to_i.day
    ransack(created_at_gteq: created_at_gteq, created_at_lteq: created_at_lteq, orgs_name_eq_any: orgs_name).result
  }
  # 新入职人员筛选
  scope :serve_rule_new_people, lambda { |rule, org_name = "滨江区", days = 15|
    pack = rule.packs.where.not(create_instance_state: nil).where.not(state: "terminated").order("created_at desc").first
    start_date = if pack.nil?
                   Time.zone.now - days.to_i.days
                 else
                   pack.created_at
                 end
    end_date = 1.day.ago.end_of_day
    joins(:members, :departments)
      .where(members: { type: "DingtalkMember" })
      .where("members.gmt_create BETWEEN ? AND ?", start_date.beginning_of_day, end_date.end_of_day)
      .where(departments: { org_id: Org.find_by(name: org_name).try(:id) }).distinct
  }
  # 公职人员筛选
  scope :find_public_official_users, lambda { |_rule = nil|
    staff_dictionary = JSON.parse(File.read("app/services/staff_dictionary.json"))
    job_level_code_params = staff_dictionary["职级code表"].reject { |_k, v| v =~ /专业技术员|其他|总监|主管/ }.keys
    budgeted_post_code_params = ["BIAN_ZHI_GONG_WU_YUAN_BIAN_ZHI", "BIAN_ZHI_CAN_ZHAO_GONG_WU_YUAN_BIAN_ZHI", "BIAN_ZHI_GONG_YI_GANG_WEI"]
    pos_job_params = ENV.fetch("PUBLIC_DUTY_NAMES", "局长,处长,科长,所长,书记,常委,党委委员,调研员,警长,民警,辅警,公务员").split(",")
    ransack(m: "or", res_tags_name_eq: "公职人员", members_job_level_code_in: job_level_code_params, members_budgeted_post_code_in: budgeted_post_code_params, members_pos_job_cont_any: pos_job_params).result
  }

  scope :in_org_and_descendants, lambda { |org_name|
    org = Org.find_by(name: org_name)
    joins(:orgs).where(orgs: { id: org.self_and_descendants.pluck(:id) })
  }

  ransacker :employee_role_code do
    Arel.sql("(SELECT model_payload->'govEmployeePosition'->>'empPosEmployeeRoleCode' FROM members WHERE members.user_id = users.id AND members.type = 'DingtalkMember' LIMIT 1)")
  end

  def ransackable_scopes(auth_object = nil)
    super.push(
      :find_public_official_users,
      :in_org_and_descendants
    )
  end

  def dingtalk_member
    members.find_by(type: "DingtalkMember")
  end

  def member_pos_job
    dingtalk_member&.pos_job
  end

  def openid(oauth_app_id)
    oauth(oauth_app_id)&.openid || oauths&.first&.openid
  end

  def str_date_field(field)
    raise "不允许的字段" unless ["birthday", "political_birthday"].include?(field)

    date = send(field)
    return "" if date.blank?

    # 只保留“MM月dd日”格式
    date_this_year = date.change(year: Time.zone.today.year)
    date_this_year.strftime("%m月%d日")
  end

  def birthday_str
    str_date_field("birthday")
  end

  def political_birthday_str
    str_date_field("political_birthday")
  end

  def sync_membership(org_id: nil, duty_id: nil, department_id: nil, member_id: nil)
    # 确保至少有一个参数不为空
    if org_id.nil? && duty_id.nil? && department_id.nil? && member_id.nil?
      raise ArgumentError, "At least one of org_id, duty_id, department_id, or member_id must be present"
    end

    # 查找部分匹配的记录
    membership = memberships.where(app_id: 1)
      .where("org_id = ? OR duty_id = ? OR department_id = ? OR member_id = ?", org_id, duty_id, department_id, member_id)
      .first

    if membership.nil? || (membership.department_id && membership.department_id != department_id) ||
       (membership.duty_id && membership.duty_id != duty_id) || (membership.member_id && membership.member_id != member_id)
      # 如果没有找到部分匹配的记录，或者某些字段冲突，则创建新记录
      membership = memberships.create!(
        app_id: 1,
        org_id: org_id,
        duty_id: duty_id,
        department_id: department_id,
        member_id: member_id,
        effective_at: Time.zone.now
      )
    else
      # 如果找到了部分匹配的记录，则更新缺失的字段和 effective_at
      membership.update!(
        org_id: org_id || membership.org_id,
        duty_id: duty_id || membership.duty_id,
        department_id: department_id || membership.department_id,
        member_id: member_id || membership.member_id,
        effective_at: Time.zone.now
      )
    end
    membership
  end

  # 手机号通过浙政钉验证真实姓名
  # 需要传入dingtalk::client
  def verify_user_for_dingtalk(client_id = 3)
    return unless /^1\d{10}$/.match?(account)

    client = Dingtalk::Client.find_by(id: client_id)
    response = client.get_by_mobile(mobile: account)
    return unless response.data.dig("content", "success")

    employeeCode = response.data.dig("content", "data", "employeeCode")
    response = client.getEmployeeByCode(employeeCode: employeeCode)
    return unless response.data.dig("content", "success")

    employeeName = response.data.dig("content", "data", "employeeName")
    gmtCreate = response.data.dig("content", "data", "gmtCreate")
    empGender = response.data.dig("content", "data", "empGender")
    gender_map = {
      "1" => "男性",
      "2" => "女性",
      "9" => "未说明的性别",
      "0" => "未知的性别"
    }
    update name: employeeName, created_at: gmtCreate, gender: gender_map[empGender] || "未知的性别"
  end

end
