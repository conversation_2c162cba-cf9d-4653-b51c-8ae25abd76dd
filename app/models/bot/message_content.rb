module Bot
  # 消息内容处理类
  # 用于处理和管理消息的内容格式，支持多种类型的消息内容
  class MessageContent
    VALID_CONTENT_TYPES = %w[
      text
      image
      audio
      video
      file
      artifact
      location
      reference
    ].freeze

    class InvalidContentTypeError < StandardError; end
    class InvalidContentFormatError < StandardError; end

    attr_reader :messages

    # 初始化消息内容
    # @param messages [Array<Hash>] 消息数组，每个消息包含content_type和content
    # @raise [InvalidContentTypeError] 当消息类型无效时
    # @raise [InvalidContentFormatError] 当消息格式无效时
    def initialize(messages: [])
      @messages = []
      messages.each { |msg| add_message(**msg.symbolize_keys) }
    end

    # 添加新消息
    # @param content_type [String] 消息类型
    # @param content [String, Hash] 消息内容
    # @return [self] 返回自身，支持链式调用
    # @raise [InvalidContentTypeError] 当消息类型无效时
    # @raise [InvalidContentFormatError] 当消息格式无效时
    def add_message(content_type:, content:)
      validate_content_type!(content_type)
      validate_content_format!(content_type, content)

      @messages << {
        content_type: content_type,
        content: content
      }
      self
    end

    # 合并另一个MessageContent对象
    # @param other [MessageContent] 要合并的对象
    # @return [MessageContent] 返回新的合并后的对象
    def merge(other)
      raise ArgumentError, '参数必须是MessageContent对象' unless other.is_a?(MessageContent)

      self.class.new(messages: messages + other.messages)
    end

    # 获取指定类型的消息
    # @param type [String] 消息类型
    # @return [Array<Hash>] 指定类型的消息数组
    def messages_of_type(type)
      validate_content_type!(type)
      messages.select { |msg| msg[:content_type] == type }
    end

    # ActiveRecord序列化支持
    def self.dump(obj)
      return if obj.nil?

      unless obj.is_a?(self)
        # 处理不同类型的输入
        case obj
        when Hash
          # 支持Symbol和String key的Hash
          messages = obj[:messages] || obj['messages'] || []
          obj = new(messages: messages)
        when String
          # 如果是字符串，创建一个text类型的消息
          obj = new(messages: [{ content_type: 'text', content: obj }])
        else
          # 其他类型，创建空的MessageContent
          obj = new
        end
      end

      obj.to_h.to_json
    end

    def self.load(json)
      return new if json.nil?

      hash = JSON.parse(json).deep_symbolize_keys
      new(messages: hash[:messages] || [])
    rescue JSON::ParserError
      new
    end

    # 转换为Hash
    # @return [Hash] 包含messages数组的Hash
    def to_h
      { messages: messages.map(&:deep_symbolize_keys) }
    end

    private

    def validate_content_type!(type)
      return if VALID_CONTENT_TYPES.include?(type)

      raise InvalidContentTypeError, "无效的消息类型: #{type}，有效类型: #{VALID_CONTENT_TYPES.join(', ')}"
    end

    def validate_content_format!(type, content)
      case type
        # when 'artifact'
        # validate_artifact_format!(content)
      when 'location'
        validate_location_format!(content)
      when 'reference'
        validate_reference_format!(content)
      end
    end

    # def validate_artifact_format!(content)
    #   unless content.is_a?(Hash) && content[:id].present? && content[:name].present?
    #     raise InvalidContentFormatError, "artifact格式无效，需要包含id和name"
    #   end
    # end

    def validate_location_format!(content)
      return if content.is_a?(Hash) && content[:lat].is_a?(Numeric) && content[:lon].is_a?(Numeric)

      raise InvalidContentFormatError, 'location格式无效，需要包含有效的lat和lon'
    end

    def validate_reference_format!(content)
      unless content.is_a?(Hash) && content[:id].present? && content[:messages].is_a?(Array)
        raise InvalidContentFormatError, 'reference格式无效，需要包含id和messages数组'
      end

      # 验证引用消息的格式
      content[:messages].each do |msg|
        unless msg.is_a?(Hash) && msg[:content_type].present? && msg[:content].present?
          raise InvalidContentFormatError, 'reference中的消息格式无效'
        end

        validate_content_type!(msg[:content_type])
      end
    end
  end
end
