class Bot::MessageSenderArtifact < Bot::Artifact
  def activate
    rule_id = meta['rule_id']
    rule = Serve::Rule.find_by(id: rule_id)
    return { error: '未找到匹配的规则' } unless rule.present?

    current_user = ::User.find_by(id: meta['current_user_id'])
    user_ids = current_user.org_users.ransack(meta['user_conditions']).result.pluck(:id)
    return { error: '未找到匹配的人员' } if user_ids.empty?

    pack = rule.generate_ai_pack!(
      current_user: conversation&.user,
      user_ids: user_ids,
      send_at: meta['send_at'],
      content: meta['content'],
      message: {
        title: meta['title'],
        markdown: meta['markdown']
      }
    )
    update! source: pack

    as_jbuilder_json
  end
end
