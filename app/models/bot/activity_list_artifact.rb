class Bot::ActivityListArtifact < Bot::Artifact
  # 素材列表Artifact类
  # 用于展示素材查询结果的前端组件

  def activate
    # 返回artifact的JSON表示，供前端组件使用
    as_jbuilder_json
  end

  # 重写as_jbuilder_json方法，确保在消息序列化时包含payload
  def as_jbuilder_json
    result = super

    # 如果meta中有frontend_params，则合并到结果中
    if result.is_a?(Hash) && meta&.dig('frontend_params')
      result.merge!(meta['frontend_params'])
    elsif result.is_a?(Hash)
      # 兜底方案：从meta.q构建payload结构
      result[:payload] = {
        params: {
          q: query_conditions
        }
      }
    end

    result
  end

  # 获取查询结果统计信息
  def total_count
    meta&.dig('total_count') || 0
  end

  # 获取查询条件
  def query_conditions
    meta&.dig('q') || {}
  end

  # 获取查询参数的可读描述
  def query_description
    conditions = query_conditions
    descriptions = []

    # 时间范围描述
    if conditions['created_at_gteq']
      date = Date.parse(conditions['created_at_gteq'].to_s) rescue nil
      if date
        days_ago = (Date.current - date).to_i
        case days_ago
        when 0..1
          descriptions << "今天"
        when 2..7
          descriptions << "最近一周"
        when 8..30
          descriptions << "最近一个月"
        when 31..90
          descriptions << "最近三个月"
        else
          descriptions << "#{date.strftime('%Y年%m月%d日')}以后"
        end
      end
    end

    # 状态描述
    if conditions['state_eq']
      state_mapping = {
        'published' => '已发布',
        'pending' => '待发布',
        'draft' => '草稿',
        'archived' => '已归档'
      }
      descriptions << state_mapping[conditions['state_eq']] || conditions['state_eq']
    end

    # 关键词描述
    if conditions['name_cont']
      descriptions << "包含「#{conditions['name_cont']}」"
    end

    descriptions.empty? ? "所有素材" : descriptions.join("的")
  end

  # 获取原始查询文本
  def original_query
    function_params&.dig('query') || ''
  end
end
