module Bot::Model::ReviewDocument
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    include Bot::Vectorable

    belongs_to :app
    belongs_to :user, class_name: '::User'
    belongs_to :reviewer

    has_many :review_rules, through: :reviewer
    has_many :review_results, as: :document_source, dependent: :destroy

    attribute :name, :string, comment: '名称'
    attribute :review_state, :string, comment: '状态'

    enum review_state: {
      pending: 'pending',
      processing: 'processing',
      completed: 'completed',
      failed: 'failed',
    }

    default_value_for(:review_state) { :pending }
    default_value_for(:app) { |o| o.user&.app || o.reviewer&.app }

    after_create_commit :perform_review
  end

  def perform_review
    reviewer&.perform_review(self)
  end

  # 获取违规项统计
  def violation_summary
    results_by_rule = review_results.includes(:review_rule).group_by(&:review_rule)

    results_by_rule.map do |rule, results|
      {
        rule_name: rule.name,
        violations: results.map do |result|
          {
            name: result.name,
            raw: result.raw,
            reason: result.reason,
            suggest: result.suggest,
            level: result.level,
            location: result.meta['location'],
          }
        end,
      }
    end
  end

  private

  # 按严重程度获取违规项
  def violations_by_level(level)
    review_results.includes(:review_rule).where(level: level)
  end

  class_methods do
  end
end
