module Bot::Model::Reviewer
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    include Bot::Reviewable

    formable

    belongs_to :app

    has_many :review_documents, dependent: :destroy
    has_many :review_results, through: :review_documents

    attribute :name, :string, comment: '名称'
    attribute :description, :text, comment: '描述'
    attribute :icon, :jsonb, comment: '图标'
  end

  # 获取所有活跃的规则
  def active_rules
    review_rules.where(active: true)
  end

  # 获取审查统计信息
  def review_statistics
    {
      total_documents: review_documents.count,
      completed_documents: review_documents.completed.count,
      failed_documents: review_documents.failed.count,
      active_rules: active_rules.count,
      total_violations: review_documents.joins(:review_results).count,
      violations_by_severity: review_documents.joins(:review_results)
                                              .group('review_results.level')
                                              .count,
    }
  end

  class_methods do
  end
end
