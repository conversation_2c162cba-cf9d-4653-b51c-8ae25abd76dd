module Bot::Model::ReportTemplate
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    include Bot::Reviewable

    formable

    belongs_to :app

    has_many :reports, dependent: :nullify

    attribute :name, :string, comment: '名称'
    attribute :title, :string, comment: '标题'
    attribute :content, :text, comment: '内容'
    attribute :instructions, :text, comment: '介绍'
    attribute :prompt, :text, comment: '提示词'
    attribute :conf, :jsonb, comment: '变量配置'
    attribute :icon, :jsonb, comment: '图标'
  end

  class_methods do
  end
end
