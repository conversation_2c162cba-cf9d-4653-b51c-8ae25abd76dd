module Bot::Model::Intent
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    stiable

    belongs_to :app

    has_many :intent_relations, dependent: :destroy
    has_many :agents, through: :tool_relations

    attribute :name, :string, comment: '工具名称'
    attribute :description, :text, comment: '工具描述'
    attribute :tool_cname, :string, comment: '工具类的名称'
    attribute :tool_conf, :jsonb, comment: '工具配置'
    attribute :llm_model_key, :string, comment: 'LlmFactory默认的大模型'

    def build_tool
      return unless tool_cname.present?

      tool_class = tool_cname.constantize
      conf = tool_conf || {}

      # 确保配置中包含必要的参数
      tool_params = conf.transform_keys(&:to_sym)

      # 添加 intent 实例和 llm 实例到工具参数中
      tool_params[:intent] = self
      tool_params[:llm] = llm

      begin
        tool_class.new(**tool_params)
      rescue ArgumentError => e
        Rails.logger.error "Invalid tool configuration for #{tool_cname} - #{e.message}"
        nil
      end
    end

    def tool
      @tool ||= build_tool
    end

    def llm
      @llm ||= default_llm
    end

    private

    def default_llm
      Bot::LlmFactory.create(llm_model_key)
    end
  end

  class_methods do
  end
end
