module Bot::Model::MentionVersion
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    belongs_to :conversation
    belongs_to :mention
    belongs_to :mentionable, optional: true

    attribute :state, :string, comment: '状态'
    attribute :meta, :jsonb, comment: '具体的内容,结构同message里的meta'

    serialize :meta, coder: Bot::MessageContent

    default_value_for(:conversation) { |o| o.mention&.conversation }
  end

  class_methods do
  end
end
