module Bot::Model::Report
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    belongs_to :app
    belongs_to :user, class_name: '::User'
    belongs_to :report_template

    has_many :review_results, as: :document_source, dependent: :destroy

    attribute :name, :string, comment: '名称'
    attribute :title, :string, comment: '标题'
    attribute :content, :text, comment: '内容'
    attribute :variables, :jsonb, comment: '变量'
    attribute :review_state, :string, comment: '状态'

    enum review_state: {
      pending: 'pending',
      processing: 'processing',
      completed: 'completed',
      failed: 'failed',
    }

    default_value_for(:app) { |o| o.user&.app }
    defualt_value_for(:name) { |o| o.report_template&.name }
  end

  class_methods do
  end

  # 文本处理操作类型
  TEXT_OPERATIONS = {
    polish: '润色',
    expand: '扩写',
    simplify: '精简',
    continue: '续写',
  }.freeze

  def perform_review
    report_template&.perform_review(self)
  end

  def file_contents
    content
  end

  # 处理文本（润色、扩写、精简、续写）
  def process_text(operation:, content:, requirements: nil)
    # 验证操作类型
    raise ArgumentError, "不支持的操作类型：#{operation}" unless TEXT_OPERATIONS.key?(operation.to_sym)

    # 获取模板 instructions（如果有）
    template_instructions = report_template&.instructions
    instructions_context = template_instructions.present? ? "\n#{template_instructions}" : ''

    system_prompt = case operation.to_sym
    when :polish, :simplify
      <<~PROMPT
        你是一个专业的文本处理助手。请严格按照以下规则处理文本：
        1. 只返回处理后的文本结果
        2. 不要添加任何解释、评论或额外的内容
        3. 不要添加任何标题、序号或分隔符
        4. 保持原文的格式（如段落划分）
        5. 如果原文包含Markdown格式，需要保留这些格式
      PROMPT
    when :expand
      <<~PROMPT
        你是一个专业的文本处理助手。请严格按照以下规则处理文本：
        1. 只返回处理后的文本结果
        2. 不要添加任何解释、评论或额外的内容
        3. 可以添加新的标题、段落来扩充内容，使文章结构更加完整
        4. 如果添加新的标题，需要保持与原文风格一致
        5. 如果原文包含Markdown格式，需要保持格式一致性
      PROMPT
    when :continue
      <<~PROMPT
        你是一个专业的文本处理助手。请严格按照以下规则处理文本：
        1. 只返回处理后的文本结果（包含原文和续写部分）
        2. 不要添加任何解释、评论或额外的内容
        3. 续写部分的结构应该自然衔接原文
        4. 可以添加新的标题、段落，但要与原文风格保持一致
        5. 如果原文包含Markdown格式，续写部分也要使用相同的格式
      PROMPT
    end

    user_prompt = case operation.to_sym
                 when :polish
                   <<~PROMPT
                     任务：对文本进行润色，使其更加优美流畅
                     #{requirements.present? ? "具体要求：#{requirements}" : ''}
                     #{instructions_context}
                     
                     待处理文本：
                     #{content}
                     
                     请直接返回处理后的文本，不要添加任何其他内容。
                   PROMPT
                 when :expand
                   <<~PROMPT
                     任务：对文本进行扩写，补充更多细节和内容
                     #{requirements.present? ? "具体要求：#{requirements}" : ''}
                     #{instructions_context}
                     
                     待处理文本：
                     #{content}
                     
                     请直接返回处理后的文本，不要添加任何其他内容。
                   PROMPT
                 when :simplify
                   <<~PROMPT
                     任务：对文本进行精简，保持核心意思的同时使表达更加简洁
                     #{requirements.present? ? "具体要求：#{requirements}" : ''}
                     #{instructions_context}
                     
                     待处理文本：
                     #{content}
                     
                     请直接返回处理后的文本，不要添加任何其他内容。
                   PROMPT
                 when :continue
                   <<~PROMPT
                     任务：基于文本进行续写
                     #{requirements.present? ? "具体要求：#{requirements}" : ''}
                     #{instructions_context}
                     
                     待续写文本：
                     #{content}
                     
                     请直接返回完整的文本（包含原文和续写部分），不要添加任何其他内容。
                   PROMPT
                 end

    result = Bot::LlmFactory.chat_with_llm(
      Bot::LlmFactory.create,
      messages: [
        {
          role: 'system',
          content: system_prompt
        },
        {
          role: 'user',
          content: user_prompt
        }
      ]
    )

    result.chat_completion
  end

  # 根据模板生成报告
  def generate_from_template
    raise Error, '报告模板不存在' unless report_template

    # 构建完整的 prompt
    full_prompt = [
      '系统：你是一个专业的报告生成助手。请严格按照要求生成报告。',
      '你需要生成一个结构清晰、层次分明的报告，使用Markdown格式组织内容。',
      "\n模板说明：",
      report_template.instructions,
      "\n处理要求：",
      report_template.prompt,
      "\n变量设置：",
    ].compact.join("\n")

    # 添加已有的变量
    variables.each do |key, value|
      full_prompt << "\n#{key}: #{value}"
    end

    # 添加输出格式要求
    full_prompt << <<~FORMAT

      输出格式要求：
      1. 必须只输出 JSON 格式数据
      2. 报告内容必须使用 Markdown 格式，包含以下要求：
         - 使用 # 作为一级标题
         - 使用 ## 作为二级标题
         - 使用 ### 作为三级标题
         - 重要内容可以使用**加粗**或*斜体*
         - 使用 - 或 1. 创建列表
         - 可以使用 > 引用重要内容
         - 适当使用分隔线 --- 分隔不同部分
      3. JSON 必须包含以下字段：
         - title: 报告标题
         - content: 报告内容（使用Markdown格式）

      示例格式：
      {
        "title": "2024年第一季度工作报告",
        "content": "# 2024年第一季度工作报告\\n\\n## 1. 工作概述\\n\\n本季度主要完成了以下工作：\\n\\n### 1.1 项目进展\\n- 完成了A项目的开发\\n- 启动了B项目的规划\\n\\n### 1.2 团队建设\\n1. 新增团队成员3名\\n2. 完成团队技能提升培训\\n\\n## 2. 存在问题\\n\\n> 项目进度略有延迟，需要在下季度加快推进\\n\\n## 3. 下季度计划\\n\\n..."
      }
    FORMAT

    result = Bot::LlmFactory.chat_with_llm(
      Bot::LlmFactory.create,
      messages: [{ role: 'user', content: full_prompt }],
      temperature: 0.7, # 适当提高温度以增加生成内容的创造性
    )

    # 处理可能带有 ```json 标记的响应
    json_content = result.chat_completion.gsub(/```json\s*|\s*```/, '')

    # 解析 JSON 响应
    JSON.parse(json_content)
  rescue JSON::ParserError => e
    raise Error, "LLM 返回格式错误: #{e.message}"
  end
end
