module Bot::Model::App
  extend ActiveSupport::Concern

  included do
    has_many :bot_agents, class_name: 'Bot::Agent', dependent: :destroy
    has_many :bot_intents, class_name: 'Bot::Intent', dependent: :destroy
    has_many :bot_tools, class_name: 'Bot::Tool', dependent: :destroy
    has_many :bot_conversations, class_name: 'Bot::Conversation', dependent: :destroy
    has_many :bot_report_templates, class_name: 'Bot::ReportTemplate', dependent: :destroy
    has_many :bot_reviewers, class_name: 'Bot::Reviewer', dependent: :destroy
    has_many :bot_review_documents, class_name: 'Bot::ReviewDocument', dependent: :destroy
    has_many :bot_meetings, class_name: 'Bot::Meeting', dependent: :destroy
  end

  class_methods do
  end
end
