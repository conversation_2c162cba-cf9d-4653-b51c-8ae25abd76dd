module Bot::Model::Org
  extend ActiveSupport::Concern

  included do
    include Bot::Searchable
    # 定义可搜索的属性
    searchable_attributes :name, :code

    searchable_associations :parent, :children, :descendants, :ancestors

    # 字段描述，用于生成查询提示
    def self.searchable_field_descriptions
      {
        # 属性字段
        name: {
          type: :attribute,
          name: '组织名称',
          column_type: :string,
          description: '组织机构名称，通常是行政区划（如：浙江省、杭州市、滨江区、西湖区、上城区等）或企事业单位名称',
        },
        short_name: {
          type: :attribute,
          name: '组织简称',
          column_type: :string,
          description: '组织简称，如：滨江区、西湖区等',
        },
        code: {
          type: :attribute,
          name: '组织编码',
          column_type: :string,
          description: '组织的唯一标识编码',
        },
        # 关联字段
        parent: {
          type: :association,
          association_type: :belongs_to,
          model: 'Org',
          name: '上级组织',
          description: '直接上级组织，如：滨江区的上级是杭州市',
          fields: {
            name: '上级组织名称',
            short_name: '上级组织简称',
          },
        },
        children: {
          type: :association,
          association_type: :has_many,
          model: 'Org',
          name: '下级组织',
          description: '直接下级组织，如：杭州市的下级有滨江区、西湖区等',
          fields: {
            name: '下级组织名称',
            short_name: '下级组织简称',
          },
        },
        descendants: {
          type: :association,
          association_type: :has_many,
          model: 'Org',
          name: '所有下级组织',
          description: '包含所有层级的下级组织，用于层级查询',
          fields: {
            name: '下级组织名称',
            short_name: '下级组织简称',
          },
        },
        ancestors: {
          type: :association,
          association_type: :has_many,
          model: 'Org',
          name: '所有上级组织',
          description: '包含所有层级的上级组织，用于层级查询',
          fields: {
            name: '上级组织名称',
            short_name: '上级组织简称',
          },
        }
      }
    end
  end

  class_methods do
  end
end
