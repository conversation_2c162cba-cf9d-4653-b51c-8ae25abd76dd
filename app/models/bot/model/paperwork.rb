module Bot::Model::Paperwork
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    belongs_to :app
    belongs_to :user, class_name: '::User'

    has_many :paperwork_results, dependent: :destroy

    attribute :name, :string, comment: '名称'
    attribute :operate_at, :datetime, comment: '操作时间'
    attribute :state, :string, comment: '状态'
    attribute :attachment, :jsonb, comment: '附件结构，单一文件'
    attribute :response, :jsonb, comment: '响应'
    attribute :prompt_text, :text, comment: '提示词，由外部传入'

    before_validation :validate_attachment
    after_commit :create_task_async

    enum state: {
      uploading: 'uploading',
      # pending: 'pending',
      # running: 'running',
      success: 'success',
      failed: 'failed',
    }

    default_value_for(:app) { |o| o.user&.app }
    default_value_for(:state) { 'uploading' }

    def validate_attachment
      raise Error::BaseError.new(message: '请上传文件') unless attachment.present?
      raise Error::BaseError.new(message: '文件仅支持 docx 与 pdf') unless attachment.dig('url').end_with?('docx') || attachment.dig('url').end_with?('pdf')
    end

    # def prompt_text
    #   <<-PROMPT
    #     ** 你是纪检监察方面文件总结助手, 用户将提供一份文件, 请根据内容总结出一份风险问题汇总, 返回一个纯 JSON **
    #     ## 返回的 JSON 格式如下 { "result": [{ "原文": "xxx", "问题": "xxx" }] }
    #     - 原文(key 为 `原文`): 为用户提供的原文内容，请照搬原文返回，**不可省略，不可使用省略号**。
    #     - 风险问题描述(key 为 `问题`): 为总结出的风险问题总结. 50字以内
    #   PROMPT
    # end

    def create_task_async
      Bot::PaperworkTaskJob.perform_later(id)
    end

    # 合同发送审查
    def create_task!
      return unless state.to_s == 'uploading'

      file_url = attachment&.dig('url').to_s

      # prompt_text = prompt&.prompt_result || ''
      url = URI.join(ENV['PAPERWORK_URL'], 'file_url_with_prompt')
      response = post(
        url,
        headers: {
          'Content-Type': 'application/json',
        },
        body: {
          file_url: file_url,
          prompt: prompt_text,
        },
      )

      update!(state: 'success', response: response, operate_at: Time.now)

      response['result']&.each do |item|
        paperwork_results.create!(name: item['问题'], raw: item['原文'])
      end

    rescue => e
      Rails.logger.error e
      update_columns(state: 'failed', operate_at: Time.now, response: { error: e.message })
    end

    def post url, headers: {}, body: {}, params: {}
      response = Typhoeus.post(url, headers: headers, params: params, body: body.to_json)
      JSON.parse(response.body)
    end


  end

  class_methods do
  end
end
