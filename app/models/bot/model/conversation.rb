module Bot::Model::Conversation
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    broadcasts

    belongs_to :app
    belongs_to :agent
    belongs_to :user, class_name: '::User'

    has_many :messages, dependent: :destroy
    has_many :artifacts, dependent: :destroy

    attribute :name, :string, comment: '名称'

    default_value_for(:app) { |o| o.agent&.app }
  end

  class_methods do
  end

  def generate_name_if_empty
    return unless should_generate_name?
    return name if name.present?

    begin
      generated_name = Bot::Services::ConversationNameGenerator.generate(conversation: self)

      if generated_name.present?
        new_name = generated_name.strip.gsub(/["']/, '')
        update(name: new_name)
        new_name
      else
        Rails.logger.warn("Failed to generate conversation name: Empty result")
        nil
      end
    rescue => e
      Rails.logger.error("Error in generate_name_if_empty: #{e.message}")
      nil
    end
  end

  private

  def should_generate_name?
    name.blank? && messages.any?
  end
end
