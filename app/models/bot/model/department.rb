module Bot::Model::Department
  extend ActiveSupport::Concern

  included do
    include Bot::Searchable
    # 定义可搜索的属性
    searchable_attributes :name, :code

    # 定义可搜索的关联
    searchable_associations :org, :parent, :children, :descendants, :ancestors

    # 字段描述，用于生成查询提示
    def self.searchable_field_descriptions
      {
        # 属性字段
        name: {
          type: :attribute,
          name: '部门名称',
          column_type: :string,
          description: '部门或机构名称，如：宣传部、纪委、财政局、教育局、人事处、办公室、技术部等职能部门',
        },
        short_name: {
          type: :attribute,
          name: '部门简称',
          column_type: :string,
          description: '部门简称，如：宣传部、纪委、财政局等',
        },
        code: {
          type: :attribute,
          name: '部门编码',
          column_type: :string,
          description: '部门的唯一标识编码',
        },
        # 关联字段
        org: {
          type: :association,
          association_type: :belongs_to,
          model: 'Org',
          name: '所属组织',
          description: '部门所属的组织机构，如：滨江区宣传部属于滨江区',
          fields: {
            name: '组织名称',
            short_name: '组织简称',
          },
        },
        parent: {
          type: :association,
          association_type: :belongs_to,
          model: 'Department',
          name: '上级部门',
          description: '直接上级部门，如：办公室可能属于某个更大的部门',
          fields: {
            name: '上级部门名称',
            short_name: '上级部门简称',
          },
        },
        children: {
          type: :association,
          association_type: :has_many,
          model: 'Department',
          name: '下级部门',
          description: '直接下级部门或科室',
          fields: {
            name: '下级部门名称',
            short_name: '下级部门简称',
          },
        },
        descendants: {
          type: :association,
          association_type: :has_many,
          model: 'Department',
          name: '所有下级部门',
          description: '包含所有层级的下级部门，用于层级查询',
          fields: {
            name: '下级部门名称',
            short_name: '下级部门简称',
          },
        },
        ancestors: {
          type: :association,
          association_type: :has_many,
          model: 'Department',
          name: '所有上级部门',
          description: '包含所有层级的上级部门，用于层级查询',
          fields: {
            name: '上级部门名称',
            short_name: '上级部门简称',
          },
        }
      }
    end
  end

  class_methods do
  end
end