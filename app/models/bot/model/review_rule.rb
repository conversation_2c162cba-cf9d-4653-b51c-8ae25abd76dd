module Bot::Model::ReviewRule
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    belongs_to :review_source, polymorphic: true

    attribute :name, :string, comment: '名称'
    attribute :content, :text, comment: '内容'
    attribute :scoring_criteria, :text, comment: '评分标准'
    attribute :active, :boolean, comment: '是否启用'

    default_value_for(:active) { true }
  end

  # 获取规则的使用统计
  def usage_statistics
    {
      total_violations: review_results.count,
      violations_by_severity: review_results.group(:level).count,
      affected_documents: review_results.select(:review_document_id).distinct.count,
    }
  end

  # 复制规则
  def duplicate
    new_rule = reviewer.review_rules.new(
      name: "#{name} (复制)",
      content: content,
      scoring_criteria: scoring_criteria,
      active: false,
    )
    new_rule.save!
    new_rule
  end

  class_methods do
  end
end
