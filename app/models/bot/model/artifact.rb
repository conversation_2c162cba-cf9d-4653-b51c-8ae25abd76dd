module Bot::Model::Artifact
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    stiable

    belongs_to :conversation
    belongs_to :source, polymorphic: true, optional: true

    attribute :intent_name, :string, comment: '根据名称'
    attribute :tool_cname, :string, comment: '根据模型名称'
    attribute :tool_function, :string, comment: '根据function名称'
    attribute :tool_conf, :jsonb, comment: '参数配置'
    attribute :function_params, :jsonb, comment: '函数调用信息，转换前'
    attribute :meta, :jsonb, comment: '参数内容，自定义的方式'
    attribute :info, :jsonb, comment: 'message返回的信息'

    # 需要STI子类进行重写
    def activate
      { message: '没有激活的功能' }
    end
  end

  class_methods do
  end
end
