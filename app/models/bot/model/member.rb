module Bot::Model::Member
	extend ActiveSupport::Concern

	included do
    include Res::Model::Member
    include Bot::Searchable
    # 定义可搜索的属性
    searchable_attributes :name, :account, :mobile, :email, :indentity_id, :gender, :created_at, :updated_at

    # 定义可搜索的关联
    searchable_associations :user, :departments, :res_tags, :duties, :member_identity

    # 字段描述，用于生成查询提示
    def self.searchable_field_descriptions
    	{
        member_identity: {
					type: :association,
					association_type: :belongs_to,
					fields: {
							name: '身份',
					}
        },
        user: {
					type: :association,
					association_type: :belongs_to,
					fields: {
						name: '姓名',
						account: '账号',
						mobile: '电话',
						email: '邮箱',
						indentity_id: '证件号码',
						gender: '性别',
						created_at: '创建时间',
						updated_at: '更新时间'
					}
        },
        # 关联字段
        orgs: {
					type: :association,
					association_type: :has_many,
					fields: {
						name: '组织名称'
					}
        },
        departments: {
					type: :association,
					association_type: :has_many,
					fields: {
						name: '部门名称'
        }
        },
        res_tags: {
					type: :association,
					association_type: :has_many,
					fields: {
						name: '标签'
        }
        },
        duties: {
					type: :association,
					association_type: :has_many,
					fields: {
						name: '职称'
        	}
        }
    	}
  	end

    def ai_query_users
    	has_role?(:res_admin) ? app.members : app.members.ransack(orgs_id_eq: orgs.pluck(:id)).result
    end
	end

	class_methods do
	end
end
