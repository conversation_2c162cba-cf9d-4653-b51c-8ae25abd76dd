module Bot::Model::Bpm::Instance
  extend ActiveSupport::Concern

  included do
    include Bot::Searchable

    # 定义可搜索的属性
    searchable_attributes :state, :created_at, :updated_at

    # 定义可搜索的关联
    searchable_associations :creator, :tokens

    # 字段描述，用于生成查询提示
    def self.searchable_field_descriptions
      {
        # 属性字段
        state: {
          type: :attribute,
          name: human_attribute_name(:state),
          column_type: :string,
          description: '流程状态',
          values: ['待审批(created/processing)', '待审核(created/processing)', '进行中(processing)', '待提交(created)',
                   '已完成(completed)', '已拒绝(rejected)']
        },
        created_at: {
          type: :attribute,
          name: human_attribute_name(:created_at),
          column_type: :datetime,
          description: '流程发起时间'
        },
        updated_at: {
          type: :attribute,
          name: human_attribute_name(:updated_at),
          column_type: :datetime,
          description: '流程更新时间'
        },
        level: {
          type: :attribute,
          name: '优先级',
          column_type: :string,
          description: '流程优先级的高低，以ABCD表示，A最高，D最低'
        },

        # 关联字段
        creator: {
          type: :association,
          association_type: :belongs_to,
          fields: {
            name: '创建人姓名'
          }
        },
        tokens: {
          type: :association,
          association_type: :has_many,
          fields: {
            updated_at: '审批时间',
            created_at: '创建时间'
          }
        }
      }
    end
  end

  class_methods do
  end
end
