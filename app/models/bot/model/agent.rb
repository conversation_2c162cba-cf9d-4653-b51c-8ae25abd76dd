module Bot::Model::Agent
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    stiable

    belongs_to :app
    has_many :conversations, dependent: :nullify
    has_many :intent_relations, dependent: :destroy
    has_many :intents, through: :intent_relations

    attribute :name, :string, comment: '工具名称'
    attribute :description, :text, comment: '工具描述'
    attribute :instructions, :text, comment: '工具介绍'
    attribute :llm_model_key, :string, comment: 'LlmFactory默认的大模型'
    attribute :max_history_messages, :integer, comment: '最大历史消息数'

    default_value_for(:max_history_messages) { 10 }
  end

  def initialize_assistant(conversation_id: nil)
    tools = intents.map(&:build_tool).compact

    # 初始化assistant
    @assistant = Bot::Assistant.new(
      llm: default_llm,
      tools: tools,
      instructions: build_instructions,
      tool_choice: 'auto' # 明确设置为auto以启用工具调用
    )

    # 如果有conversation_id，加载历史消息
    if conversation_id
      conversation = conversations.find_by(id: conversation_id)
      if conversation
        # 加载最近的消息历史
        recent_messages = conversation.messages
                                      .order(created_at: :desc)
                                      .limit(max_history_messages)
                                      .to_a
                                      .reverse

        # 将消息添加到assistant
        recent_messages.each do |message|
          content = if message.meta.messages.is_a?(Array)
                      message.meta.messages
                             .reject { |msg| msg[:content_type].to_s == 'artifact' }
                             .map { |msg| msg[:content] }
                             .join("\n")
                    else
                      message.meta.to_s
                    end
          @assistant.add_message(role: message.role, content: content)
        end
      end
    end

    @assistant
  end

  def assistant
    @assistant ||= initialize_assistant
  end

  def build_instructions
    base_instructions = instructions || ''
    intent_descriptions = intents.map do |intent|
      "- #{intent.name}: #{intent.description}"
    end.join("\n")

    <<~INSTRUCTIONS
      #{base_instructions}

      now is #{Time.zone.now}

      可用的功能：
      #{intent_descriptions}

      请根据用户的输入，选择最合适的功能来处理请求。
      每个功能都有其特定的查询范围和处理逻辑，请根据用户的意图选择最匹配的功能。

      在处理请求时，请注意：
      1. 如果涉及到之前上传的文件或生成的内容，请直接使用它们
      2. 保持对话的连续性，理解上下文
      3. 如果需要切换功能，请确保传递正确的上下文信息
    INSTRUCTIONS
  end

  def chat(message, user:, conversation_id: nil, **options)
    conversation = find_or_create_conversation(user, conversation_id)
    log_chat_start(message)

    # 预处理查询
    preprocessing_result = preprocess_query(message, user, conversation)
    processed_message = preprocessing_result[:processed_query]
    preprocessing_info = preprocessing_result[:preprocessing_info]

    # 初始化助手并保存用户消息
    @assistant = initialize_assistant(conversation_id: conversation_id)
    save_user_message(conversation, message)

    # 生成响应
    response = generate_response(processed_message, conversation, **options)

    # 验证和改进响应
    response = validate_and_improve_response(response, preprocessing_info)

    # 添加调试信息
    add_debug_info(response, preprocessing_info) if Rails.env.development?

    response
  end

  private

  def find_or_create_conversation(user, conversation_id)
    if conversation_id
      conversations.find_by!(user: user, id: conversation_id)
    else
      conversations.create!(user: user)
    end
  end

  def log_chat_start(message)
    Rails.logger.info('=== Agent Chat ===')
    Rails.logger.info("User message: #{message.inspect}")
    Rails.logger.debug("Available intents: #{intents.map do |i|
      { id: i.id, name: i.name, description: i.description, tool: i.tool_cname, conf: i.tool_conf }
    end.inspect}")
  end

  def preprocess_query(message, user, conversation)
    Bot::QueryPreprocessor.process(
      message,
      agent: self,
      user: user,
      conversation: conversation
    )
  end

  def save_user_message(conversation, message)
    conversation.messages.create!(
      role: 'user',
      meta: message
    )
  end

  def generate_response(processed_message, conversation, **options)
    chat_with_processed_message(processed_message, conversation, **options)
  end

  def validate_and_improve_response(response, preprocessing_info)
    validation_result = Bot::ResponseValidator.validate(response, preprocessing_info)

    # 如果是强制工具调用但验证失败，尝试改进响应
    if should_improve_response?(validation_result, preprocessing_info)
      response = attempt_response_improvement(response, preprocessing_info, validation_result)
    end

    response
  end

  def should_improve_response?(validation_result, preprocessing_info)
    validation_result.has_issues? &&
      preprocessing_info[:forced_tool_call] &&
      !validation_result.valid?
  end

  def add_debug_info(response, preprocessing_info)
    validation_result = Bot::ResponseValidator.validate(response, preprocessing_info)

    response[:preprocessing_info] = preprocessing_info
    response[:validation_result] = {
      valid: validation_result.valid?,
      issues: validation_result.issues,
      suggestions: validation_result.suggestions
    }
  end

  def chat_with_processed_message(processed_message, conversation, **options)
    content = extract_content_from_message(processed_message)

    # 添加用户消息并运行助手（自动执行工具）
    @assistant.add_message_and_run!(content: content)

    # 构建响应
    response = build_response_from_assistant

    # 保存助手回复
    save_assistant_message(conversation, response)

    response
  end

  def extract_content_from_message(processed_message)
    case processed_message
    when Hash
      if processed_message[:messages].is_a?(Array)
        processed_message[:messages].map { |msg| format_content(msg[:content]) }.join("\n")
      else
        format_content(processed_message)
      end
    when String
      processed_message
    else
      format_content(processed_message)
    end
  end

  def build_response_from_assistant
    assistant_message = @assistant.messages.last
    artifacts = collect_artifacts_from_assistant

    {
      messages: [
        build_text_message(assistant_message),
        *build_artifact_messages(artifacts)
      ]
    }
  end

  def collect_artifacts_from_assistant
    @assistant.messages.select { |msg| msg.tool_calls.present? }.flat_map do |msg|
      msg.tool_calls.map do |call|
        extract_artifact_from_tool_call(call)
      end.compact
    end
  end

  def extract_artifact_from_tool_call(call)
    tool_response = @assistant.messages.find { |m| m.tool_call_id == call['id'] }
    return unless tool_response

    begin
      response_data = JSON.parse(tool_response.content, symbolize_names: true)
      Bot::Artifact.new(response_data[:artifact]) if response_data[:artifact].is_a?(Hash)
    rescue JSON::ParserError
      nil
    end
  end

  def build_text_message(assistant_message)
    {
      content_type: 'text',
      content: assistant_message.content
    }
  end

  def build_artifact_messages(artifacts)
    artifacts.map do |artifact|
      {
        content_type: 'artifact',
        content: artifact.as_json
      }
    end
  end

  def save_assistant_message(conversation, response)
    conversation.messages.create!(
      role: 'assistant',
      meta: response
    )
  end

  def format_content(content)
    case content
    when Hash then content.to_json
    else content.to_s
    end
  end

  def attempt_response_improvement(response, preprocessing_info, validation_result)
    Rails.logger.info('Attempting to improve response quality')

    return response unless preprocessing_info[:tool_result]

    improved_content = build_improved_response_content(
      preprocessing_info[:original_query],
      preprocessing_info[:tool_result],
      validation_result
    )

    update_response_text_content(response, improved_content)
    response
  end

  def update_response_text_content(response, improved_content)
    return unless response[:messages]&.any? { |msg| msg[:content_type] == 'text' }

    text_message = response[:messages].find { |msg| msg[:content_type] == 'text' }
    text_message[:content] = improved_content if text_message
  end

  def build_improved_response_content(original_query, tool_result, validation_result)
    base_content = extract_tool_message(tool_result)
    enhanced_content = add_friendly_prefix(base_content, original_query)
    add_supplementary_info(enhanced_content)
  end

  def extract_tool_message(tool_result)
    tool_result[:message] || tool_result.to_s
  end

  def add_friendly_prefix(content, original_query)
    if original_query.to_s.include?('多少')
      "根据最新数据，#{content}"
    else
      "#{content}。"
    end
  end

  def add_supplementary_info(content)
    content + '如果您需要查看详细的素材列表，可以点击下方的查询组件。'
  end

  def default_llm
    Bot::LlmFactory.create(llm_model_key)
  end

  class_methods do
    def build_agent(app:, name:, description: nil, instructions: nil, llm_model_key: nil)
      create!(
        app: app,
        name: name,
        description: description,
        instructions: instructions,
        llm_model_key: llm_model_key
      )
    end
  end
end
