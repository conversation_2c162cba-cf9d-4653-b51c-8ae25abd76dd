module Bot::Model::User
  extend ActiveSupport::Concern

  included do
    has_many :bot_conversations, class_name: 'Bot::Conversation', dependent: :destroy
    has_many :bot_paperworks,    class_name: 'Bot::Paperwork', dependent: :destroy
    has_many :bot_reports,       class_name: 'Bot::Report', dependent: :destroy
    has_many :bot_review_documents, class_name: 'Bot::ReviewDocument', dependent: :destroy
    has_many :bot_meetings, class_name: 'Bot::Meeting', dependent: :destroy

    include Bot::Searchable

    def bot_relate_meetings
      app.bot_meetings.permit_by_user(self).or(
        app.bot_meetings.where(user: self),
      )
    end

    # 定义可搜索的属性
    searchable_attributes :name, :account, :email, :indentity_id, :gender, :created_at, :updated_at

    # 定义可搜索的关联
    searchable_associations :orgs, :departments, :res_tags, :duties, :members, :member_identities, :org_subtree, :department_subtree

    # 字段描述，用于生成查询提示
    def self.searchable_field_descriptions
      {
        # 属性字段
        name: {
          type: :attribute,
          name: '姓名',
          column_type: :string,
          description: '用户姓名，支持姓氏搜索（如：姓王、姓李、姓张等）和全名搜索',
        },
        account: {
          type: :attribute,
          name: '账号',
          column_type: :string,
          description: '用户账号，通常是手机号码',
        },
        email: {
          type: :attribute,
          name: '邮箱',
          column_type: :string,
          description: '用户邮箱地址',
        },
        gender: {
          type: :attribute,
          name: '性别',
          column_type: :string,
          description: '用户性别，值为：男、女',
        },
        indentity_id: {
          type: :attribute,
          name: '身份证号',
          column_type: :string,
          description: '用户身份证号码',
        },
        created_at: {
          type: :attribute,
          name: '创建时间',
          column_type: :datetime,
          description: '用户账号创建时间，支持时间范围查询',
        },
        updated_at: {
          type: :attribute,
          name: '更新时间',
          column_type: :datetime,
          description: '用户信息最后更新时间，支持时间范围查询',
        },

        # 关联字段
        orgs: {
          type: :association,
          association_type: :has_many,
          model: 'Org',
          name: '所属组织',
          description: '用户所属的组织机构，通常是地区名称（如：滨江区、西湖区、上城区等）',
          fields: {
            name: '组织名称',
            short_name: '组织名称简称',
          },
        },
        org_subtree: {
          type: :association,
          association_type: :has_many,
          model: 'Org',
          name: '组织树',
          description: '用户所属组织及其下级所有组织，用于层级查询',
          fields: {
            name: '组织名称',
            short_name: '组织名称简称',
          },
        },
        members: {
          type: :association,
          association_type: :has_many,
          model: 'Member',
          name: '成员身份岗位',
          description: '用户在不同组织中的成员身份记录，包含用户在各个组织中担任的具体岗位',
          fields: {
            pos_job: '岗位名称（如 村干部、助理、工程师）',
          },
        },
        departments: {
          type: :association,
          association_type: :has_many,
          model: 'Department',
          name: '所属部门',
          description: '用户所属的部门，如：宣传部、财政局、教育局、人事处等政府部门或院校部门',
          fields: {
            name: '部门名称',
            short_name: '部门名称简称',
          },
        },
        department_subtree: {
          type: :association,
          association_type: :has_many,
          model: 'Department',
          name: '部门树',
          description: '用户所属部门及其下级所有部门，用于层级查询',
          fields: {
            name: '部门名称',
            short_name: '部门名称简称',
          },
        },
        res_tags: {
          type: :association,
          association_type: :has_many,
          model: 'ResTag',
          name: '用户标签',
          description: '用户的标签分类，如：党员、干部、专家等身份标签',
          fields: {
            name: '标签名称',
          },
        },
        duties: {
          type: :association,
          association_type: :has_many,
          model: 'Duty',
          name: '职务职称',
          description: '用户的职务或职称，如：主任、副局长、处长等',
          fields: {
            name: '职务名称',
          },
        },
      }
    end

    def ai_query_users
      has_role?(:res_admin) ? app.users : app.users.ransack(orgs_id_eq: orgs.pluck(:id)).result
    end

    def ai_query_orgs
      app.orgs
    end

    def ai_query_departments
      app.departments
    end
  end

  class_methods do
  end
end
