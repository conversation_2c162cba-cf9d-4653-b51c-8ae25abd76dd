module Bot::Model::Message
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    broadcasts

    belongs_to :app
    belongs_to :conversation

    attribute :role, :string, comment: '发送对象'
    attribute :meta, :jsonb, comment: '消息发送的内容'

    serialize :meta, coder: Bot::MessageContent

    default_value_for(:app) { |o| o.conversation&.app }

    # 添加meta消息
    def add_meta_message(content_type:, content:)
      meta.add_message(content_type: content_type, content: content)
      self
    end

    # 获取指定类型的meta消息
    def meta_messages_of_type(type)
      meta.messages_of_type(type)
    end

    # 合并meta内容
    def merge_meta(other)
      self.meta = meta.merge(other)
      self
    end
  end

  class_methods do
  end
end
