module Bot::Model::Plan::Task
	extend ActiveSupport::Concern

  included do
    include Bot::Searchable

    searchable_attributes :name, :state, :token_type, :starting_at, :completed_at, :priority

		searchable_associations :create_user, :tokens

		def self.searchable_field_descriptions
      {
        # 属性字段
        state: {
          type: :attribute,
          name: human_attribute_name(:state),
          column_type: :string,
          description: '任务状态',
          values: ['未处理(pending)', '进行中(starting)', '已完成(completed)', '已关闭(finished)', '已终止(terminated)']
        },
        starting_at: {
          type: :attribute,
          name: human_attribute_name(:created_at),
          column_type: :datetime,
          description: '任务开始时间'
        },
        completed_at: {
          type: :attribute,
          name: human_attribute_name(:updated_at),
          column_type: :datetime,
          description: '任务结束时间'
        },
				priority: {
          type: :attribute,
          name: human_attribute_name(:updated_at),
          column_type: :datetime,
          description: '优先级'
        },
				# 关联字段
        create_user: {
          type: :association,
          association_type: :belongs_to,
          fields: {
            name: '创建人姓名',
						account: '创建人账号'
          }
        }
			}
		end
	end
end
