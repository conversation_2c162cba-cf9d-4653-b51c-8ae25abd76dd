module Bot::Model::Meeting
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    # include Bot::Vectorable
    include Res::Ext::Permitable
    acts_as_permitable
    seqable

    belongs_to :app
    belongs_to :user, class_name: '::User', optional: true

    attribute :name, :string, comment: '会议名称'
    attribute :state, :string, comment: '状态'
    attribute :meeting_time, :string, comment: '会议时间'
    attribute :background, :text, comment: '会议背景'
    attribute :topic, :string, comment: '会议主题'
    attribute :summary, :text, comment: '会议纪要'
    attribute :participants, :string, comment: '与会人员'
    attribute :audio, :jsonb, comment: '会议录音'
    attribute :payload, :jsonb, comment: '额外字段'
    attribute :file, :jsonb, comment: '文件或内容'

    enum state: { pending: 'pending', processing: 'processing', finished: 'finished', failed: 'failed' }
    default_value_for(:app) { |o| o.user&.app }
    default_value_for(:state) { 'processing' }

    # 异步执行
    after_create_commit :async_process_audio
    after_update :reset_file_speaker_name!

    def ask(question)
      meeting_contents = meeting_content_arrays.join("\n")
      messages = [
        { role: 'assistant', content: '你是一个会议纪要助手，根据用户的问题，从会议对话记录中查找相关问题并回复给用户' },
        { role: 'user', content: "会议的内容是： #{meeting_contents}" },
        { role: 'user', content: "问题是：#{question}" },
      ]

      result = Bot::LlmFactory.chat_with_llm(
        Bot::LlmFactory.create,
        messages: messages,
      )
      result.chat_completion
    end

    def async_process_audio
      AudioConvertJob.perform_later(id)
    end

    def async_generate_topic_and_summary
      update(state: 'processing')
      AudioConvertJob.perform_later(id, 'generate_topic_and_summary')
    end

    # 重置文件的 speaker_name
    def reset_file_speaker_name!
      speakers, state = payload&.dig('speakers'), payload&.dig('state')
      if state == 'pending' && file.present? && file['contents'].present? && speakers.present?
        h = speakers.each_with_object({}) do |speaker, hash|
          hash[speaker['key']] = speaker['name']
        end

        contents = file['contents'] || []
        contents.each do |content|
          content['speaker_name'] = h[content['speaker']]
        end

        self.payload['state'] = 'finished'
        self.file['contents'] = contents

        save!
      end
    end

    # 初始化文件的 speaker_name
    def init_file_speaker_name!
      if file.present? && file['contents'].present?
        contents = file['contents'] || []
        speakers = contents.each_with_object([]) do |content, arr|
                     arr.push(content['speaker']) unless content['speaker'].to_s.in?(arr)
                     arr
                   end.uniq.compact

        payload = { speakers: speakers.map{ |speaker| { key: speaker, name: speaker } } }
        update!(payload: payload)
      end
    end

    # 重置音频的播放时长
    def reset_audio_duration!
      audio_info = audio || {}
      if file && file['contents'].present?
        audio_info['origin_duration'] = audio_info['duration']
        audio_info['duration'] = file['contents'].last['end']
        update(audio: audio_info)
      end
    end
  end

  def process_audio
    # 预留方法，用于处理会议录音并生成会议记录
    # TODO: 后续实现音频转文字功能
    return if Rails.env.test?
    return if audio.blank?

    if audio['mimeType'].to_s.start_with?('video')
      result = AudioConvertService.convert(video_path: audio['url'])
      return if result.blank?
      update(audio: result)
    end

    body = {
      url: self.audio['url'],
      seq: self.seq,
    }

    response = Typhoeus.post(
      ENV['MEETING_TRANSCRIBE_URL'],
      headers: { 'Content-Type' => 'application/json' },
      body: body.to_json,
    )
    Rails.logger.info "transcribe result: #{response.body}"
  end

  def meeting_content_arrays
    file['contents'].map { |content| content['speaker_name'] ? "#{content['speaker_name']}：#{content['text']}" : content['text'] }
  end

  def generate_content(fields:, prompt_template:)
    raise Error, '会议记录不存在' unless file.present? && file['contents'].present?

    input_prompt = payload&.dig('prompt')
    meeting_content_arrays.unshift(input_prompt) if input_prompt.present?

    record_content = meeting_content_arrays
    prompt = build_prompt(record_content, prompt_template)

    result = Bot::LlmFactory.chat_with_llm(
      Bot::LlmFactory.create,
      messages: [
        {
          role: 'user',
          content: prompt,
        },
      ],
    )

    # 处理可能带有 ```json 标记的返回结果
    json_content = result.chat_completion.gsub(/```json\s*|\s*```/, '')
    parsed_result = JSON.parse(json_content)

    update_columns(parsed_result.slice(*fields))
  end

  def generate_topic_and_summary
    template = {
      description: [
        '你是一个专业的会议纪要生成助手。请根据以下会议信息生成会议主题和会议纪要。',
        '注意：',
        '1. 会议主题应简短、准确、具有概括性',
        '2. 会议纪要必须使用 Markdown 格式，包含以下要求：',
        '   - 使用 # 作为一级标题（通常是会议主题）',
        '   - 使用 ## 作为二级标题（例如：会议信息、主要议题、讨论要点等）',
        '   - 使用 ### 作为三级标题（具体议题的子项）',
        '   - 重要内容使用**加粗**标记',
        '   - 使用 - 或 1. 创建列表记录要点',
        '   - 使用 > 引用重要决议或结论',
        '   - 适当使用分隔线 --- 分隔不同部分',
        '3. 请只返回JSON格式的结果，不要包含任何其他解释性文字',
      ].join("\n"),
      example: {
        topic: '2025年第一季度产品规划会议',
        summary: [
          '# 2025年第一季度产品规划会议',
          '',
          '## 会议信息',
          '- **时间**：2025年1月15日 14:00-16:00',
          '- **地点**：线上会议',
          '- **主持人**：张三',
          '- **参会人员**：李四、王五等',
          '',
          '## 会议议程',
          '',
          '### 1. 上季度工作回顾',
          '1. 完成了A产品的开发和发布',
          '2. B项目按计划推进中',
          '',
          '### 2. 本季度重点工作',
          '- 启动C项目的研发',
          '- 优化现有产品性能',
          '',
          '## 讨论要点',
          '',
          '### 1. 产品方向',
          '- 市场需求分析',
          '- 技术可行性评估',
          '',
          '### 2. 资源分配',
          '- 人力资源计划',
          '- 预算控制方案',
          '',
          '## 重要决议',
          '',
          '> 1. 确定在Q1完成C项目的需求分析和架构设计',
          '> 2. 下周开始组建项目团队',
          '',
          '---',
          '',
          '## 后续行动',
          '1. 各部门提交详细执行计划',
          '2. 下周三进行项目启动会',
          '',
          '## 遗留问题',
          '- 预算方案需要进一步细化',
          '- 技术方案选型需要进一步论证',
        ].join("\n"),
      },
    }

    generate_content(
      fields: %w[topic summary],
      prompt_template: template,
    )
  end

  private

  def build_prompt(record_content, template)
    [
      template[:description],
      '',
      '会议基本信息：',
      "- 会议时间：#{meeting_time}",
      "- 会议背景：#{background}",
      "- 与会人员：#{participants}",
      '',
      '会议记录内容：',
      record_content.is_a?(Array) ? record_content.join("\n") : record_content,
      '',
      '请返回如下格式的JSON：',
      '{',
      template[:example].map { |k, v| %(  "#{k}": #{v.to_json}) }.join(",\n"),
      '}',
    ].join("\n")
  end

  class Error < StandardError; end

  class_methods do
    # 消息队列回调函数
    def queue_callback(data)
      Rails.logger.info("#{Time.zone.now.strftime('%F %T')}=== Meeting Callback ===")
      meeting = find_by(seq: data['seq'])
      if meeting
        Rails.logger.info("=== Start Update Meeting ===")
        # 设置
        meeting.update(file: data['result'], state: 'finished')
        meeting.init_file_speaker_name!
        Rails.logger.info("=== Finish Update Meeting ===")
        # TODO: 处理会议纪要
        # Rails.logger.info("=== Start Process Document ===")
        # meeting.process_document
        # Rails.logger.info("=== Finish Process Document ===")
        Rails.logger.info("=== Start Generate Topic And Summary ===")
        meeting.generate_topic_and_summary
        Rails.logger.info("=== Finish Generate Topic And Summary ===")
      end
    rescue Exception => e
      Rails.logger.error("Failed to update meeting: #{e.message}")
    end
  end
end
