module Bot::Model::ReviewResult
  extend ActiveSupport::Concern

  included do
    self.track_migration = true

    belongs_to :document_source, polymorphic: true
    belongs_to :review_rule

    attribute :name, :string, comment: '结果名称'
    attribute :raw, :text, comment: '原文'
    attribute :score, :integer, comment: '分数'
    attribute :reason, :text, comment: '原因'
    attribute :suggest, :text, comment: '建议'
    attribute :level, :string, comment: '等级'
    attribute :meta, :jsonb, comment: '额外信息'

    enum level: {
      high: 'high',
      medium: 'medium',
      low: 'low',
    }
  end

  # 获取完整的位置信息
  def location_info
    meta['location'] || {}
  end

  # 获取违规内容的上下文
  def content_context(context_lines: 2)
    location = location_info
    return {} unless location['line'].present?

    content_lines = review_document.content.split("\n")
    line_number = location['line'].to_i

    start_line = [line_number - context_lines, 0].max
    end_line = [line_number + context_lines, content_lines.length - 1].min

    {
      start_line: start_line,
      end_line: end_line,
      context: content_lines[start_line..end_line].join("\n"),
    }
  end

  # 生成修改建议的完整描述
  def full_suggestion
    context = content_context
    [
      "问题描述：#{name}",
      "风险等级：#{level}",
      "原因：#{reason}",
      "违规内容：#{raw}",
      "位置信息：第 #{location_info['line']} 行",
      "上下文：\n#{context[:context]}",
      "修改建议：#{suggest}",
    ].join("\n")
  end

  class_methods do
  end
end
