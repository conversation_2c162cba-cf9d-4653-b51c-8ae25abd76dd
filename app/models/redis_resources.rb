class RedisResources
  attr_accessor :resources, :redis_key

  # @redis_key 存储的是对象 ids 的 set
  def initialize(resources: nil, redis_key: g_redis_key(resources))
    @redis_key = redis_key

    exist_ids = self.class.redis.smembers(redis_key)

    if exist_ids.present?
      klass = redis_key.split(":").first.constantize
      @resources = klass.where(id: exist_ids)
    elsif resources
      ids = resources.pluck(:id)
      if ids.present?
        self.class.redis.sadd(redis_key, ids)
      else
        self.class.redis.sadd(redis_key, 0)
      end
      self.class.redis.expire(redis_key, 60 * 60 * 24 * 1) # 设置过期时间为7天
      @resources = resources
    elsif redis_key.present?
      # 本地没有数据库，简单写法
      # 通用解决方案应是 建一张表，记录 redis_key 和资源类名的对应关系
      pack = Serve::Pack.ransack('payload.send_user_ids_redis_key_eq' => redis_key).result.first
      @resources = pack ? pack.send_users : (raise Error::BaseError.new(message: "页面已过期，请刷新页面重试", status: 422))
    else
      raise Error::BaseError.new(message: "页面已过期，请刷新页面重试", status: 422)
    end
  end

  def remove_ids(*ids)
    self.class.redis.srem(redis_key, ids)
  end

  def add_ids(*ids)
    Rails.logger.info(ids)
    self.class.redis.sadd(redis_key, ids)
  end

  def g_redis_key(r)
    "#{r.klass.name}:#{SecureRandom.uuid}"
  end

  def find(id)
    resources.find(id)
  end

  def clear
    ids = self.class.get_ids_from_redis_key(redis_key)
    remove_ids(*ids) if ids.present?
  end

  def self.get_ids_from_redis_key(redis_key)
    redis.smembers(redis_key).compact.map(&:to_i).reject(&:zero?)
  end

  def self.redis
    $redis_resources_redis
  end
end
