class Bpm::Token < ApplicationRecord

  include Bpm::Model::Token
  before_destroy :revoke_dingtalk_template_messages

  def revoke_dingtalk_template_messages
    Dingtalk::TemplateMessage.where(user_id: operator_id, notifyable_type: "Bpm::Token", notifyable_id: id).find_each { |m| m.revoke }
  end

  def after_generate_notify_info_message(user:, **_options)
    # 一些工作流不需要发送浙政钉消息通知
    return if ENV["NOTIFY_IGNORE_WORKFLOW_IDS"].split(",").include? workflow_id.to_s

    #  如果是规则关联的审批，使用其中规则设定好的浙政钉应用进行通知
    client_id = instance&.flowable&.rule&.options&.client_id
    oauth_app_id = get_oauth_app_id(client_id, instance.creator)
    send_dingtalk_message(user.id, oauth_app_id)
  end

  def get_oauth_app_id(client_id, user)
    return Dingtalk::Client.find(client_id).code if client_id.present?

    # 尝试从用户组织获取oauth_app_id
    user.orgs.where.not(model_payload: nil).find do |org|
      org.model_payload.dig("oauth_app_id")
    end&.model_payload&.dig("oauth_app_id") || ENV.fetch("DINGTALK_NOTIFY_APP_CODE", nil)
  end

  def send_dingtalk_message(user_id, oauth_app_id)
    # 检查环境变量是否禁用浙政钉消息发送
    return if ENV["DISABLE_ZZD_MESSAGE"].to_s.downcase == "true"

    message_args = [
      user_id,
      "Bpm::Token",
      id,
      zzd_message.to_json
    ]
    DingtalkSendMessage.perform_async(*message_args, nil, oauth_app_id)
  end

  def zzd_message
    client = Dingtalk::Client.find_by(code: ENV.fetch("DINGTALK_NOTIFY_APP_CODE", nil))
    tanent = Tanent.current
    instance_url = CGI.escape(File.join(tanent&.config&.dig("WEBSITE_URL") || ENV.fetch("WEBSITE_URL", nil),
                                        "/bpm/user/instances/#{instance_id}"))
    mobile_url = CGI.escape(File.join(tanent&.config&.dig("WEBSITE_MOBILE_URL") || ENV.fetch("WEBSITE_MOBILE_URL", nil),
                                      "/h5/#/bpm-mobile/pages/instance/show/index?id=#{instance_id}"))
    {
      msgtype: "action_card",
      action_card: {
        title: calc_title,
        markdown: "
名称：#{instance.workflow_name}  \n
当前环节：#{name}  \n
发起人：#{instance.creator_name}  \n
        ",
        single_title: "查看详情",
        single_pc_url: "taurus://taurusclient/action/open_app?corpid=#{client.corp_id}&title=流程审批&container_type=work_platform&ddtab=true&app_id=#{ENV.fetch(
          'DINGTALK_NOTIFY_APP_CODE', nil
        )}&redirect_type=jump&redirect_url=#{instance_url}",
        single_url: "taurus://taurusclient/action/open_app?type=1&offline=false&title=流程审批&url=#{mobile_url}"
      }
    }
  end

  def calc_title
    case type
    when "Tokens::Notify"
      "您收到一条抄送的任务"
    else
      case previous_token&.state.to_s
      when "rejected"
        "您有一条任务被驳回!"
      when "failed"
        "您有一条任务被退回!"
      else
        "您有一条任务待审批!"
      end
    end
  end

end
