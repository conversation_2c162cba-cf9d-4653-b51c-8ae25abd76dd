class Bpm::Instance < ApplicationRecord

  include Bpm::Model::Instance
  include Bpm::Model::InstanceState

  def after_generate_notify_info_message(user:, **_options)
    # 检查环境变量是否禁用浙政钉消息发送
    return if ENV['DISABLE_ZZD_MESSAGE'].to_s.downcase == 'true'

    # 一些工作流不需要发送浙政钉消息通知
    workflow_id = self.workflow_id
    notify_ignore_workflow_ids = ENV.fetch("NOTIFY_IGNORE_WORKFLOW_IDS", nil)
    return if notify_ignore_workflow_ids.include? workflow_id.to_s

    # 获取oauth_app_id
    client_id = flowable&.rule&.options&.client_id
    oauth_app_id = get_oauth_app_id(client_id, creator)

    DingtalkSendMessage.perform_async(
      user.id,
      "Bpm::Instance",
      id,
      zzd_message.to_json,
      nil,
      oauth_app_id
    )
  end

  private

  def get_oauth_app_id(client_id, user)
    return Dingtalk::Client.find(client_id).code if client_id.present?

    # 尝试从用户组织获取oauth_app_id
    user.orgs.where.not(model_payload: nil).find do |org|
      org.model_payload.dig("oauth_app_id")
    end&.model_payload&.dig("oauth_app_id") || ENV['DINGTALK_NOTIFY_APP_CODE']
  end

  def zzd_message
    client = Dingtalk::Client.find_by(code: ENV.fetch("DINGTALK_NOTIFY_APP_CODE", nil))
    tanent = Tanent.current
    instance_url = CGI.escape(File.join(tanent&.config&.dig("WEBSITE_URL") || ENV.fetch("WEBSITE_URL", nil),
                                        "/bpm/user/instances/#{id}"))
    mobile_url = CGI.escape(File.join(tanent&.config&.dig("WEBSITE_MOBILE_URL") || ENV.fetch("WEBSITE_MOBILE_URL", nil),
                                      "/h5/#/bpm-mobile/pages/instance/show/index?id=#{id}"))
    {
      msgtype: "action_card",
      action_card: {
        title: calc_title,
        markdown: "\n名称：#{workflow_name}  \n\n当前环节：#{last_token.name}  \n\n发起人：#{creator_name}  \n\n",
        single_title: "查看详情",
        single_pc_url: "taurus://taurusclient/action/open_app?corpid=#{client.corp_id}&container_type=work_platform&app_id=#{ENV.fetch(
          'DINGTALK_NOTIFY_APP_CODE', nil
        )}&redirect_type=jump&redirect_url=#{instance_url}",
        single_url: "taurus://taurusclient/action/open_app?type=1&offline=false&url=#{mobile_url}"
      }
    }
  end

  def calc_title
    case state
    when "processing"
      "您的任务已提交！"
    when "completed"
      "您的任务已结束！"
    when "terminated"
      "您的任务已终止！"
    end
  end

  def history_tokens
    tokens.where.not(operator: nil).where.not(name: nil).where.not(state: ["processing", "pending"]).map do |token|
      "处理节点：#{token.name}，操作者：#{token.operator&.name}，操作时间：#{token.updated_at.strftime('%F %H:%M:%S')}"
    end.join("\n")
  end

end
