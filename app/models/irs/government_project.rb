# frozen_string_literal: true

module Irs
  # 政府项目缓存数据模型
  # 用于存储政府项目接口返回的数据，避免重复调用接口
  class GovernmentProject < ApplicationRecord
    self.table_name = 'irs_government_projects'

    # 验证
    validates :query_date, presence: true, uniqueness: true
    validates :cached_at, presence: true
    validates :projects_count, presence: true, numericality: { greater_than_or_equal_to: 0 }

    # 作用域
    scope :successful, -> { where(success: true) }
    scope :failed, -> { where(success: false) }
    scope :recent, -> { order(cached_at: :desc) }
    scope :by_date_range, ->(start_date, end_date) { where(query_date: start_date..end_date) }

    # 缓存过期时间（24小时）
    CACHE_EXPIRE_HOURS = 24

    # 根据日期查找缓存数据
    # @param date [String, Date] 查询日期
    # @return [GovernmentProject, nil] 缓存记录
    def self.find_by_date(date)
      date_obj = date.is_a?(String) ? Date.parse(date) : date
      find_by(query_date: date_obj)
    end

    # 检查指定日期的缓存是否有效
    # @param date [String, Date] 查询日期
    # @return [Boolean] 缓存是否有效
    def self.cache_valid?(date)
      record = find_by_date(date)
      return false unless record&.success?
      
      # 检查缓存是否过期
      record.cached_at > CACHE_EXPIRE_HOURS.hours.ago
    end

    # 获取指定日期的项目列表（从缓存）
    # @param date [String, Date] 查询日期
    # @return [Array] 项目列表
    def self.get_projects_by_date(date)
      record = find_by_date(date)
      return [] unless record&.success?
      
      record.projects_list
    end

    # 缓存政府项目数据
    # @param date [String, Date] 查询日期
    # @param result [Irs::GovernmentProjectService::Result] 接口返回结果
    # @return [GovernmentProject] 缓存记录
    def self.cache_result(date, result)
      date_obj = date.is_a?(String) ? Date.parse(date) : date
      
      # 使用upsert避免重复插入
      record = find_or_initialize_by(query_date: date_obj)
      record.assign_attributes(
        raw_data: result.data,
        projects_count: result.count,
        success: result.success?,
        error_message: result.success? ? nil : result.message,
        cached_at: Time.current
      )
      
      record.save!
      Rails.logger.info "[政府项目缓存] 已缓存 #{date_obj} 的数据，项目数量: #{result.count}"
      record
    end

    # 获取项目列表
    # @return [Array] 项目列表
    def projects_list
      return [] unless success? && raw_data.present?
      
      raw_data["governmentProjectList"] || []
    end

    # 检查缓存是否过期
    # @return [Boolean] 是否过期
    def expired?
      cached_at < CACHE_EXPIRE_HOURS.hours.ago
    end

    # 获取缓存状态信息
    # @return [Hash] 状态信息
    def cache_status
      {
        date: query_date.strftime("%Y-%m-%d"),
        success: success?,
        projects_count: projects_count,
        cached_at: cached_at,
        expired: expired?,
        error_message: error_message
      }
    end

    # 清理过期缓存
    # @param days [Integer] 保留天数，默认30天
    def self.cleanup_expired_cache(days = 30)
      cutoff_date = days.days.ago
      deleted_count = where("cached_at < ?", cutoff_date).delete_all
      Rails.logger.info "[政府项目缓存] 清理了 #{deleted_count} 条过期缓存记录"
      deleted_count
    end

    # 获取缓存统计信息
    # @return [Hash] 统计信息
    def self.cache_statistics
      {
        total_records: count,
        successful_records: successful.count,
        failed_records: failed.count,
        latest_cache: recent.first&.cache_status,
        total_projects: successful.sum(:projects_count)
      }
    end
  end
end
