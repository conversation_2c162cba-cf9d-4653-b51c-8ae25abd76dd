# 项目编号	projectcode
# 招标项目所在行政区域代码	regioncode
# 招标估算金额	tenderamount
# 招标组织形式	tenderorganizeform
# 招标方式	tendermode
# 监督部门名称	supervisedeptname
# 招标代理机构代码	tenderagencycode
# 招标代理机构名称	tenderagencyname
# 招标人代码	tenderercode
# 招标人名称	tenderername
# 招标项目类型	tenderprojecttype
# 招标项目名称	tenderprojectname
# 招标项目编号	tenderprojectcode
# 项目名称	projectname
# 交易平台名称	platformname
# 招标项目建立时间	createtime
# 交易平台标识码	platformcode
# 投资项目统一代码	INVEST_PROJECT_CODE
# 招标联系人	tendererLianXiRen
# 招标联系方式	tendererTel

# 招标项目信息
class Biz::Zbxm < Biz::Base

  # 获取招标公告
  self.table_name = "biz_019_ggzyjy_zbxm"
  self.primary_key = "tenderProjectCode"

  def zbjg
    Biz::Zbjg.find_by projectcode: projectCode
  end

  def zbgg
    Biz::Zbgg.find_by projectcode: projectCode
  end

  def zbhx
    Biz::Zbhx.find_by projectcode: projectCode
  end

  def xm
    Biz::Xm.find_by projectCode: projectCode
  end

end
