module Iest::Model::Serve::Activity
  extend ActiveSupport::Concern

  included do
    include Serve::Model::Activity
    include Bot::Searchable

    include Region::Ext::AreaSource
    acts_as_area(level: 'area')

    belongs_to :origin, optional: true

    attribute :ai_summary, :text, comment: 'AI 生成的摘要'
    has_neighbors :ai_summary_embedding

    after_create_commit :generate_ai_summary

    default_value_for(:type) { 'Serve::ArticleActivity' }

    action_store(
      :ai_relate,
      :tag,
      class_name: 'Serve::Tag',
      action_class_name: 'Serve::ActivityAction',
      alias_name: 'ai_tags',
      inverse_alias_name: 'activities',
    )

    action_store(
      :content_type,
      :tag,
      class_name: 'Serve::ContentTypeTag',
      action_class_name: 'Serve::ActivityAction',
      alias_name: 'content_type_tags',
      inverse_alias_name: 'activities',
    )

    scope :sreen_by_period, ->(period = 7, unit = 'day', code = '网站') {
      result, date = [], Date.today
      period.times do |i|
        start_time = i.try(unit).ago(date.to_datetime).try("beginning_of_#{unit}")
        end_time = i.weeks.ago(date.to_datetime).try("end_of_#{unit}")
        result.push({
          cweek: start_time.to_date.cweek,
          start_day: start_time.to_date.strftime('%F'),
          end_of_day: end_time.to_date.strftime('%F'),
          acitivities_count: between_times(start_time, end_time).ransack(origin_code_eq: code).result.count
        })
      end
      result
    }

    def content_type_tag_names
      content_type_tags.map(&:name)
    end

    def generate_ai_summary
      Serve::ArticleActivityEmbeddingService.new(self).process
    end

    # 定义可搜索的属性
    searchable_attributes :name, :state, :created_at, :published_at

    # 定义可搜索的关联
    searchable_associations :origin, :ai_tags, :tags

    # 字段描述，用于生成查询提示
    def self.searchable_field_descriptions
      {
        # 属性字段
        name: {
          type: :attribute,
          name: human_attribute_name(:name),
          column_type: :string,
          description: '素材名称'
        },
        state: {
          type: :attribute,
          name: human_attribute_name(:state),
          column_type: :string,
          description: '素材状态',
          values: ['待发布(pending)', '已发布(published)']
        },
        created_at: {
          type: :attribute,
          name: human_attribute_name(:created_at),
          column_type: :datetime,
          description: '创建时间，用于查询最近创建的素材'
        },
        published_at: {
          type: :attribute,
          name: human_attribute_name(:published_at),
          column_type: :datetime,
          description: '发布时间，用于查询最近发布的素材'
        },
        # 关联字段
        origin: {
          type: :association,
          association_type: :belongs_to,
          fields: {
            name: '素材来源名称'
          }
        },
        ai_tags: {
          type: :association,
          association_type: :has_many,
          fields: {
            name: 'AI标签名称'
          }
        },
        tags: {
          type: :association,
          association_type: :has_many,
          fields: {
            name: '用户标签名称'
          }
        }
      }
    end

    # 提供模型特定的Ransack查询指导
    def self.ransack_query_guidance
      [
        '**素材查询时间字段选择指导**：',
        '- published_at: 发布时间，用于查询"今年发布"、"最近发布"的素材（推荐）',
        '- created_at: 创建时间，用于查询"最近创建"、"新增"的素材',
        '- 对于"今年"、"去年"、"本月"等时间查询，优先使用 published_at 字段',
        '- 对于"最近一周"、"最近一个月"等相对时间，根据语境选择合适字段',
        '',
        '**素材标签查询指导**：',
        '- 对于标签类查询（如"招投标类"、"廉政类"），应同时搜索AI标签和用户标签',
        '- 使用OR条件组合：ai_tags_name_cont OR tags_name_cont OR name_cont',
        '- 示例：{:g=>[{:ai_tags_name_cont=>"招投标"}, {:tags_name_cont=>"招投标"}, {:name_cont=>"招投标"}], :m=>"or"}'
      ]
    end
  end
end
