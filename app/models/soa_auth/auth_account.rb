class SoaAuth::AuthAccount < ApplicationRecord

  include SoaAuth::Model::AuthAccount

  after_create :sync_dingtalk_auth_account

  private

  def sync_dingtalk_auth_account
    if user.account_data
      sync_from_account_data
    else
      sync_from_dingtalk_clients
    end
  end

  def sync_from_account_data
    account_data = user.account_data
    oauth_app_code = account_data["oauth_app_id"]
    return if oauths.exists?(oauth_app_id: oauth_app_code, type: "SoaAuth::OauthDingtalk")
    return if user&.oauths&.exists?(oauth_app_id: oauth_app_code, type: "SoaAuth::OauthDingtalk")
    return unless account.match?(/^1\d{10}$/)

    oauth = oauths.where(
      type: "SoaAuth::OauthDingtalk",
      oauth_app_id: oauth_app_code,
      app_id: app_id
    ).first_or_initialize

    oauth.assign_attributes(
      openid: account_data["accountId"],
      unionid: account_data["employeeCode"]
    )

    oauth.save! # Rails 会自动处理是否需要保存
  end

  def sync_from_dingtalk_clients
    user.app.dingtalk_clients.each do |client|
      client.sync_auth_account(self)
    rescue StandardError => e
      Rails.logger.error("Dingtalk sync error: #{e.message}")
      next
    end
  end

end
