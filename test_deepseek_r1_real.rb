require 'json'
require 'langchainrb'
require_relative 'config/environment'

prompt = '给区内干部发送{ 五一节 }节日祝福，内容要温情，不能完全没有廉洁提醒的内容。'

puts "=== 测试deepseek-r1 API ==="
puts "使用prompt: #{prompt}"

llm = Bot::LlmFactory.create(:deepseek)
response = llm.chat(
  messages: [
    {
      role: 'system',
      content: '你是一个纪委宣教处专业的内容生成专家，请根据用户的需求生成5条候选内容，每条200字左右。请以JSON格式返回，格式为：{"contents": ["内容1", "内容2", "内容3", "内容4", "内容5"]}'
    },
    {
      role: 'user',
      content: prompt,
    }
  ],
  response_format: { type: 'json_object' }
)

puts "\n=== 原始响应 ==="
puts "chat_completion: #{response.chat_completion}"
puts "completion: #{response.completion}"
puts "reasoning_content: #{response.reasoning_content}"

begin
  contents = JSON.parse(response.chat_completion)['contents']
  puts "\n=== 解析后的内容 ==="
  contents.each_with_index do |content, index|
    puts "内容 #{index + 1}: #{content[0..50]}..."
  end
  
  if JSON.parse(response.chat_completion)['reasoning']
    puts "\n=== 推理过程 ==="
    puts JSON.parse(response.chat_completion)['reasoning']
  end
rescue JSON::ParserError => e
  puts "JSON解析错误: #{e.message}"
end 