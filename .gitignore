# See https://help.github.com/articles/ignoring-files for more about ignoring files.
#
# Temporary files generated by your text editor or operating system
# belong in git's global ignore instead:
# `$XDG_CONFIG_HOME/git/ignore` or `~/.config/git/ignore`

# Ignore bundler config.
/.bundle

# Ignore all environment files (except templates).
/.env*
!/.env*.erb

# Ignore all logfiles and tempfiles.
/log/*
/tmp/*
!/log/.keep
!/tmp/.keep

# Ignore pidfiles, but keep the directory.
/tmp/pids/*
!/tmp/pids/
!/tmp/pids/.keep

# Ignore storage (uploaded files in development and any SQLite databases).
/storage/*
!/storage/.keep
/tmp/storage/*
!/tmp/storage/
!/tmp/storage/.keep

# Ignore master key for decrypting credentials and more.
/config/master.key
# Ignore swagger files
/swagger/*
# Ignore simplecov coverage files
/coverage/*
# Ignore puma running files
/.pids/*
!/pids/.keep
# Other ignore files
dump.rdb
*.pem
# Ignore public folder
/public/*
# Ignore local files
*.local
.DS_Store
/bin/*

# === 测试相关忽略文件 ===
# 临时测试脚本和报告
/test_*.rb
/detailed_test_report.md
/test
/spec/tmp/
/test_data/
/spec/fixtures/tmp/
.promptx
