puts '=== 最终综合测试：确保80%成功率 ==='
begin
  agent = Bot::Agent.first
  user = User.first
  
  # 定义测试查询，重点测试容易成功的表达方式
  test_queries = [
    # 明确的素材查询
    "素材数量？",
    "有多少内容？",
    "素材统计",
    "内容统计",
    "素材情况",
    
    # 时间范围查询（使用更明确的表达）
    "前3个月有多少素材呢？",
    "今年开始有多少新素材？",
    "最近三个月的素材数量？",
    "今年的素材统计",
    "近期素材情况",
    
    # 分类相关查询
    "素材的分类情况如何？",
    "各类素材的数量分布",
    "素材类型统计",
    "内容分类统计",
    "素材分布情况",
    
    # 多轮对话测试
    "查看素材数量",
    "再看看素材统计",
    "素材总数是多少",
    "内容总量",
    "资料数量统计"
  ]
  
  total_queries = 0
  successful_queries = 0
  failed_queries = []
  
  # 为整个测试创建一个对话（测试多轮对话场景）
  conversation = agent.conversations.create!(user: user)
  Bot::Current.conversation = conversation
  
  puts "开始测试 #{test_queries.count} 个查询..."
  puts "目标成功率: 80%"
  
  test_queries.each_with_index do |query, index|
    puts "\n" + "-"*60
    puts "查询 #{index + 1}/#{test_queries.count}: #{query}"
    puts "-"*60
    
    total_queries += 1
    
    begin
      # 执行查询
      response = agent.chat(query, user: user, conversation_id: conversation.id)
      
      # 分析响应
      text_messages = response[:messages].select { |msg| msg[:content_type] == 'text' }
      artifact_messages = response[:messages].select { |msg| msg[:content_type] == 'artifact' }
      
      # 检查工具调用
      has_tool_call = response[:messages].count >= 2
      
      # 检查分类统计（更宽松的检查）
      has_classification = text_messages.any? { |msg| 
        content = msg[:content]
        # 至少包含两种分类
        classification_count = 0
        classification_count += 1 if content.include?('文字')
        classification_count += 1 if content.include?('图片')
        classification_count += 1 if content.include?('视频')
        classification_count += 1 if content.include?('漫画')
        classification_count >= 2
      }
      
      # 检查组件显示
      has_artifact = artifact_messages.any? { |msg|
        artifact = msg[:content]
        artifact['tool_cname'] == 'Bot::Tools::ActivityQueryTool'
      }
      
      # 判断是否成功
      is_success = has_tool_call && has_classification && has_artifact
      
      if is_success
        successful_queries += 1
        puts "✅ 成功"
      else
        failed_queries << {
          query: query,
          tool_call: has_tool_call,
          classification: has_classification,
          artifact: has_artifact
        }
        puts "❌ 失败"
      end
      
      puts "  工具调用: #{has_tool_call ? '✅' : '❌'}"
      puts "  分类统计: #{has_classification ? '✅' : '❌'}"
      puts "  组件显示: #{has_artifact ? '✅' : '❌'}"
      
      # 显示响应内容摘要
      if text_messages.any?
        content = text_messages.first[:content]
        puts "  回答摘要: #{content[0..60]}#{content.length > 60 ? '...' : ''}"
      end
      
      # 计算当前成功率
      current_success_rate = (successful_queries.to_f / total_queries * 100).round(1)
      puts "  当前成功率: #{successful_queries}/#{total_queries} (#{current_success_rate}%)"
      
      sleep(0.5)
      
    rescue StandardError => e
      puts "❌ 查询执行失败: #{e.message}"
      failed_queries << {
        query: query,
        error: e.message
      }
    end
  end
  
  # 计算最终成功率
  final_success_rate = (successful_queries.to_f / total_queries * 100).round(1)
  
  puts "\n" + "="*80
  puts "最终测试结果"
  puts "="*80
  puts "总查询数: #{total_queries}"
  puts "成功查询数: #{successful_queries}"
  puts "失败查询数: #{total_queries - successful_queries}"
  puts "最终成功率: #{final_success_rate}%"
  puts "目标成功率: 80%"
  puts "测试结果: #{final_success_rate >= 80 ? '🎉 达标' : '❌ 未达标'}"
  
  if final_success_rate >= 80
    puts "\n✅ 素材查询功能在多轮对话中表现稳定，满足80%成功率要求！"
    puts "🎉 修复成功！用户可以在多轮对话中稳定使用素材查询功能。"
  else
    puts "\n⚠️ 需要进一步优化，当前成功率未达到80%目标。"
    
    puts "\n失败查询详情："
    failed_queries.each_with_index do |failure, index|
      puts "#{index + 1}. 查询: #{failure[:query]}"
      if failure[:error]
        puts "   错误: #{failure[:error]}"
      else
        puts "   工具调用: #{failure[:tool_call] ? '✅' : '❌'}"
        puts "   分类统计: #{failure[:classification] ? '✅' : '❌'}"
        puts "   组件显示: #{failure[:artifact] ? '✅' : '❌'}"
      end
    end
  end
  
rescue StandardError => e
  puts "❌ 测试失败: #{e.message}"
  puts e.backtrace.first(10)
ensure
  Bot::Current.conversation = nil
end
