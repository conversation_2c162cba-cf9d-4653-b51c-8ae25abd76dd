# DeepSeek-R1 集成解决方案总结

## 问题描述

在使用 DeepSeek-R1 模型时，API 返回的响应格式与标准的 OpenAI 格式有所不同。具体来说，DeepSeek-R1 会返回额外的 `reasoning_content` 字段，包含模型的推理过程。此外，当使用 `response_format: { type: 'json_object' }` 时，DeepSeek-R1 会将 JSON 响应包装在 Markdown 代码块中（```json ... ```）。这导致了在处理 JSON 格式响应时出现问题，特别是当尝试解析 `response.chat_completion` 时。

## 解决方案

我们实现了以下修改来解决这个问题：

1. **修改 DeepseekResponse 类**：
   - 添加了 `reasoning_content` 方法，用于获取 DeepSeek-R1 返回的推理过程
   - 修改了 `chat_completion` 方法，使其能够处理 `reasoning_content` 字段，并将其合并到 JSON 响应中
   - 添加了对 Markdown 代码块格式的处理，自动提取其中的 JSON 内容

2. **修改 Deepseek 类**：
   - 在 `initialize` 方法中添加了默认的 `response_format` 参数
   - 修改了 `chat` 方法，确保能够正确处理 `response_format` 参数
   - 添加了详细的日志记录，便于调试

3. **修改 LlmFactory 类**：
   - 在 `deepseek` 方法中添加了默认的 `response_format` 参数，确保与 DeepSeek-R1 API 兼容

## 关键代码修改

### DeepseekResponse 类

```ruby
def chat_completion
  # 获取原始内容和推理内容
  content_str = completion
  reasoning = reasoning_content

  # 处理Markdown代码块格式
  if content_str && content_str.include?('```json')
    # 提取JSON内容
    json_match = content_str.match(/```json\s*(.*?)\s*```/m)
    if json_match && json_match[1]
      content_str = json_match[1].strip
    end
  end

  # 处理reasoning_content
  if reasoning && !reasoning.empty?
    begin
      if content_str && content_str.strip.start_with?('{') && content_str.strip.end_with?('}')
        content_json = JSON.parse(content_str)
        # 添加reasoning字段到JSON中
        content_json['reasoning'] = reasoning
        return content_json.to_json
      end
    rescue JSON::ParserError
      Langchain.logger.warn "Failed to parse JSON from deepseek-r1 response: #{content_str}"
    end
  end
  
  # 如果没有reasoning_content或者不是JSON格式，返回处理后的content
  content_str || completion
end

def reasoning_content
  completions&.dig(0, 'message', 'reasoning_content')
end
```

### Deepseek 类

```ruby
def initialize(api_key:, llm_options: {}, default_options: {})
  @client = ::OpenAI::Client.new(
    access_token: api_key,
    uri_base: llm_options[:uri_base] || 'https://api.deepseek.com/v1',
    request_timeout: llm_options[:request_timeout] || 120,
  )

  @defaults = {
    chat_model: 'deepseek-chat',
    completion_model: 'deepseek-chat',
    embedding_model: 'text-embedding-v1',
    dimensions: 1536,
    temperature: 0.0,
    max_tokens: 2048,
    response_format: { type: 'text' },
  }.merge(default_options)
end

def chat(messages:, model: defaults[:chat_model], temperature: defaults[:temperature], tools: [], tool_choice: 'none', parallel_tool_calls: false, **params)
  # 处理response_format参数
  response_format = params[:response_format] || defaults[:response_format]
  
  request_params = {
    model: model,
    messages: messages,
    temperature: temperature,
    max_tokens: defaults[:max_tokens],
    frequency_penalty: 0,
    presence_penalty: 0,
    response_format: response_format,
    stream: false,
  }.merge(params)
  
  # ... 其余代码 ...
end
```

### LlmFactory 类

```ruby
def self.deepseek
  # 使用qwen的大模型接口
  Langchain::LLM::Deepseek.new(
    api_key: ENV['QWEN_API_KEY'],
    llm_options: {
      uri_base: 'https://dashscope.aliyuncs.com/compatible-mode/',
    },
    default_options: {
      chat_model: 'deepseek-r1',
      completion_model: 'deepseek-r1',
      # 默认使用text格式，如果需要JSON格式，可以在调用时指定
      response_format: { type: 'text' },
    },
  )
end
```

## 测试结果

我们创建了测试脚本来验证我们的修改是否能够正确处理 DeepSeek-R1 的响应格式。测试结果表明，我们的修改能够成功处理：

1. DeepSeek-R1 返回的 `reasoning_content` 字段
2. Markdown 代码块格式的 JSON 响应
3. 将推理过程合并到 JSON 响应中

## 使用方法

用户可以像以前一样使用 DeepSeek-R1 模型，但现在他们可以通过 `response.chat_completion` 获取包含推理过程的完整响应，或者通过 `response.reasoning_content` 直接获取推理过程。

```ruby
llm = Bot::LlmFactory.create(:deepseek)
response = llm.chat(
  messages: [...],
  response_format: { type: 'json_object' }
)

# 获取包含推理过程的完整响应
full_response = response.chat_completion

# 解析JSON
parsed_response = JSON.parse(full_response)
contents = parsed_response['contents']
reasoning = parsed_response['reasoning'] # 获取推理过程
```

## 结论

通过这些修改，我们成功地解决了 DeepSeek-R1 模型的响应格式问题，使得用户代码能够正常工作。这些修改不仅兼容了 DeepSeek-R1 的特殊响应格式和 Markdown 代码块格式，还提供了额外的功能，使用户能够访问模型的推理过程。这种解决方案具有很好的扩展性，可以适应未来可能出现的其他格式变化。 