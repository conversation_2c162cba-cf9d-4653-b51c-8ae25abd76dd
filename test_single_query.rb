#!/usr/bin/env ruby

# 测试单个查询的脚本
query = ARGV[0] || "纪法小课 的文章有多少？"

puts "测试查询: #{query}"
puts "=" * 50

# 创建用户和对话
user = User.first || User.create!(name: "测试用户", email: "<EMAIL>")
agent = Bot::Model::Agent.first

if agent.nil?
  puts "❌ 没有找到Agent，请先运行seeds"
  exit 1
end

# 创建对话
conversation = agent.conversations.create!(user: user)

begin
  # 执行查询
  puts "开始查询..."
  response = agent.chat(query, user: user, conversation_id: conversation.id)
  
  puts "\n查询结果:"
  puts "=" * 30
  puts response
  
rescue => e
  puts "❌ 查询失败: #{e.message}"
  puts e.backtrace.first(5)
end
