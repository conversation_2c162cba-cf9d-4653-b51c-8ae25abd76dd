# frozen_string_literal: true

# 创建素材查询相关的 Intent
[
  {
    name: '素材查询',
    description: '专门查询系统中的素材信息。支持时间范围查询（如最近一周、一个月、三个月的素材）、状态查询（已发布、待发布）、以及综合查询。当用户询问关于素材、库、资料、文档、内容等相关查询时，必须使用此工具来提供准确数据和可交互的列表组件。',
    tool_cname: 'Bot::Tools::ActivityQueryTool',
    tool_conf: {
      model_class: 'Serve::Activity',
      scope_chain: 'all'
    }
  },
  {
    name: '发送消息',
    description: '用于创建和发送系统消息的必要工具。任何涉及消息发送、提醒设置的操作都需要通过此工具执行，以确保消息被正确记录和发送。',
    tool_cname: 'Bot::Tools::MessageSender',
    tool_conf: {
      model_class: 'Serve::Pack'
    }
  },
  {
    name: '纪检监察文件总结',
    description: '你是纪检监察方面文件总结助手, 用户将提供一份文件, 请根据内容总结出一份风险问题汇总',
    tool_cname: 'Bot::Tools::PaperworkTool',
    tool_conf: {
      prompt: "你是纪检监察方面文件总结助手, 用户将提供一份文件, 请根据内容总结出一份问题汇总, 返回一个纯 JSON , 返回的 JSON 格式如下 { 'result': [{ '原文': 'xxx', '问题': 'xxx' }] } 原文(key 为 `原\n文`): 为用户提供的原文内容，请照搬原文返回，不可省略，不可使用省略号。风险问题描述(key 为 `问题`): 为总结出的风险问题总结. 50字以内",
      model_class: 'Bot::Paperwork'
    }
  },
  {
    name: '人员数量查询',
    description: "专门查询组织架构中的人员数量。当用户询问关于'人员'、'人数'、'员工'、'干部'等人员相关统计时使用此工具。支持组织、部门及组合查询。",
    tool_cname: 'Bot::Tools::RansackTool',
    tool_conf: {
      model_class: 'User',
      scope_chain: 'Current.user.app.users'
    }
  }
].each do |intent_attrs|
  Bot::Intent.find_or_create_by!(
    app: App.first,
    name: intent_attrs[:name]
  ).update!(intent_attrs.symbolize_keys)
end
