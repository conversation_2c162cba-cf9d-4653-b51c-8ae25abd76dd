# frozen_string_literal: true

# 创建记录查询相关的 Intent
[
  {
    name: "查询待审批流程",
    description: "查询需要我审批的流程",
    tool_cname: "Bot::Tools::RansackTool",
    tool_conf: {
      model_class: "Bpm::Instance",
      scope_chain: "Current.user.approving_instances"
    }
  },
  {
    name: "查询未读流程",
    description: "查询我未读的流程",
    tool_cname: "Bot::Tools::RansackTool",
    tool_conf: {
      model_class: "Bpm::Instance",
      scope_chain: "Current.user.unread_instances"
    }
  },
  {
    name: "查询抄送流程",
    description: "查询抄送给我的流程",
    tool_cname: "Bot::Tools::RansackTool",
    tool_conf: {
      model_class: "Bpm::Instance",
      scope_chain: "Current.user.notified_instances"
    }
  },
  {
    name: "查询已审批流程",
    description: "查询我已经审批过的流程",
    tool_cname: "Bot::Tools::RansackTool",
    tool_conf: {
      model_class: "Bpm::Instance",
      scope_chain: "Current.user.approved_instances"
    }
  }
].each do |intent_attrs|
  Bot::Intent.find_or_create_by!(
    app: app,
    name: intent_attrs[:name]
  ).update!(intent_attrs.symbolize_keys)
end
