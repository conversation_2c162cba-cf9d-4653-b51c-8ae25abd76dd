# This migration comes from rails_iest_engine (originally 20250130120000)
class AddUuidDeduplicationSupport < ActiveRecord::Migration[7.0]
  def change
    # 为消息表添加UUID去重字段
    add_column :serve_messages, :content_uuid, :string, comment: '内容UUID，用于去重判断'

    # 添加索引提升查询性能
    add_index :serve_messages, :content_uuid, name: 'idx_serve_messages_content_uuid'
    add_index :serve_messages, [:user_id, :content_uuid], name: 'idx_serve_messages_user_content_uuid'
  end
end
