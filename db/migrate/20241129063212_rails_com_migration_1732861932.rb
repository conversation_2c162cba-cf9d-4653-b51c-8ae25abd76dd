class RailsComMigration1732861932 < ActiveRecord::Migration[7.1]

  def change
    create_table :opm_hr_transfers do |t|
      t.references :app
      t.references :org
      t.references :member
      t.references :user
      t.string :type, comment: "STI类型"
      t.jsonb :payload, comment: "payload"
      t.timestamps
    end
    create_table :opm_hr_requests do |t|
      t.references :app
      t.references :org
      t.references :ownership
      t.references :user
      t.references :member
      t.references :creator
      t.references :employ_invite
      t.string :create_instance_state, comment: "创建工作流的状态"
      t.datetime :create_instance_timestamp, comment: "创建工作流的操作时间"
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :type, comment: "STI属性"
      t.jsonb :payload, comment: "payload"
      t.timestamps
    end
    create_table :opm_employ_invites do |t|
      t.references :app
      t.references :creator
      t.references :org
      t.references :member_identity
      t.references :department
      t.references :duty
      t.datetime :effective_at, comment: "生效时间"
      t.datetime :invalid_at, comment: "失效时间"
      t.jsonb :payload
      t.timestamps
    end
    create_table :opm_permit_actions do |t|
      t.references :app
      t.references :real_user
      t.references :target, polymorphic: true
      t.references :user, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :action_type, comment: "具体action的业务flag"
      t.string :action_option, comment: "前端可以设置的信息"
      t.string :action_flag, comment: "后端使用的逻辑字段，前端不能设置"
      t.timestamps
      t.index [:user_type, :user_id, :action_type], name: "opm_permit_actions_user_action_type_index"
      t.index [:target_type, :target_id, :action_type], name: "opm_permit_actions_target_action_type_index"
      t.index [:action_type, :target_type, :target_id, :user_type, :user_id, :action_flag], unique: true, name: "opm_permit_actions_uk_action_target_user"
    end
    create_table :opm_ownerships do |t|
      t.references :app
      t.references :org
      t.references :department
      t.references :department_identity
      t.references :member_identity
      t.references :duty
      t.jsonb :manages, comment: "权限设置"
      t.boolean :manage_enable, comment: "是否开启permit的按钮"
      t.jsonb :config, comment: "配置"
      t.timestamps
    end
  end
end
