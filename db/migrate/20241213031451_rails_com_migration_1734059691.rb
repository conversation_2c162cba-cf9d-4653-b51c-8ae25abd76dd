class RailsComMigration1734059691 < ActiveRecord::Migration[7.1]

  def change
    add_column :chat_messages, :meta, :jsonb, comment: "发送的内容"
    add_column :chat_mention_versions, :state, :string
    create_table :assessment_stages do |t|
      t.references :activity
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.datetime :effective_at, comment: "生效时间"
      t.datetime :invalid_at, comment: "失效时间"
      t.string :name, comment: "阶段名称"
      t.string :stage_type
      t.integer :position, comment: "排序"
      t.boolean :enable, comment: "是否启用"
      t.timestamps
    end
    create_table :assessment_evaluations do |t|
      t.references :evaluate_source, polymorphic: true
      t.references :score_template
      t.references :user, polymorphic: true
      t.references :app
      t.float :score, comment: "考核得分"
      t.float :weight, comment: "得分权重"
      t.jsonb :item_payload, comment: "评分列表"
      t.jsonb :catalog_payload, comment: "catalog统计的权重列表"
      t.jsonb :template_conf, comment: "评分选项"
      t.string :name, comment: "评价名称"
      t.string :state
      t.jsonb :payload, comment: "对应score_template的score_form"
      t.string :bpm_token_state, comment: "同步bpm token的状态"
      t.string :user_name, comment: "自定义评审人名"
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :seq, comment: "编号"
      t.timestamps
    end
    create_table :assessment_scores do |t|
      t.references :evaluate_source, polymorphic: true
      t.references :score_template
      t.references :user, polymorphic: true
      t.references :activity
      t.references :entry
      t.references :dimension
      t.float :score, comment: "考核得分"
      t.float :weight, comment: "得分权重"
      t.jsonb :item_payload, comment: "评分列表"
      t.jsonb :catalog_payload, comment: "catalog统计的权重列表"
      t.jsonb :template_conf, comment: "评分选项"
      t.string :name, comment: "评价名称"
      t.string :state
      t.jsonb :payload, comment: "对应score_template的score_form"
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.timestamps
    end
    create_table :assessment_score_templates do |t|
      t.references :app
      t.references :creator
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :name, comment: "评分表名称"
      t.text :desc, comment: "评分表描述"
      t.jsonb :form, comment: "表单配置"
      t.jsonb :options, comment: "其他配置"
      t.jsonb :score_form, comment: "score的相关配置"
      t.timestamps
    end
    create_table :assessment_questions do |t|
      t.references :app
      t.references :question_bank
      t.references :creator
      t.references :parent
      t.references :tanent
      t.text :name, comment: "题干"
      t.string :kind, comment: "题目类型"
      t.jsonb :config, comment: "配置"
      t.timestamps
    end
    create_table :assessment_question_settings do |t|
      t.references :app
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :seq, comment: "编号"
      t.string :name, comment: "题目类型名称"
      t.string :flag, comment: "题目类型flag"
      t.jsonb :form, comment: "TaTemplateFormItem 无标题的表单"
      t.jsonb :config_form, comment: "TaTemplateFormItem  配置项的表单"
      t.timestamps
    end
    create_table :assessment_question_papers do |t|
      t.references :app
      t.references :creator
      t.references :user, polymorphic: true
      t.references :tanent
      t.string :name, comment: "试卷名称"
      t.text :desc, comment: "试卷简介"
      t.string :arrange_mode
      t.jsonb :config, comment: "配置"
      t.timestamps
    end
    create_table :assessment_question_banks do |t|
      t.references :app
      t.references :creator
      t.references :user, polymorphic: true
      t.references :tanent
      t.string :name, comment: "题库名称"
      t.text :desc, comment: "题库简介"
      t.integer :questions_count, comment: "题目数量"
      t.timestamps
    end
    create_table :assessment_groups do |t|
      t.references :activity
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :name, comment: "分组名称"
      t.timestamps
    end
    create_table :assessment_entries do |t|
      t.references :user, polymorphic: true
      t.references :activity
      t.references :catalog
      t.references :group
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.float :score, comment: "考核得分"
      t.integer :position, comment: "打分排序"
      t.string :user_name, comment: "考核对象显示名"
      t.string :state
      t.jsonb :payload, comment: "自评填写表单，对应activity的entry_form"
      t.timestamps
    end
    create_table :assessment_dimensions do |t|
      t.references :activity
      t.references :catalog
      t.references :group
      t.references :score_template
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :type, comment: "STI类型"
      t.string :name, comment: "维度名称"
      t.float :weight, comment: "权重"
      t.jsonb :options, comment: "不同的类型配置"
      t.integer :position, comment: "维度排序"
      t.timestamps
    end
    create_table :assessment_catalogs do |t|
      t.references :activity
      t.references :score_template
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :name, comment: "分类名称"
      t.timestamps
    end
    create_table :assessment_question_sheets do |t|
      t.references :app
      t.references :source, polymorphic: true
      t.references :template
      t.references :create_user
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.datetime :effective_at, comment: "生效时间"
      t.datetime :invalid_at, comment: "失效时间"
      t.string :seq, comment: "编号"
      t.string :name, comment: "问卷名称"
      t.jsonb :cover_image, comment: "封面图"
      t.text :desc, comment: "描述"
      t.string :type, comment: "STI"
      t.string :mode
      t.jsonb :form, comment: "问卷具体表单"
      t.boolean :is_encrypt, comment: "是否加密"
      t.string :passwd, comment: "加密口令"
      t.integer :max_count, comment: "最大单人可提交次数，匿名前端控制，如果实名后端控制"
      t.jsonb :hint_options, comment: "额外配置"
      t.string :category, comment: "类别"
      t.string :exam_mode
      t.timestamps
    end
    create_table :assessment_answer_sheets do |t|
      t.references :app
      t.references :question_sheet
      t.references :user
      t.float :score, comment: "考核得分"
      t.float :weight, comment: "得分权重"
      t.jsonb :item_payload, comment: "评分列表"
      t.jsonb :catalog_payload, comment: "catalog统计的权重列表"
      t.jsonb :template_conf, comment: "评分选项"
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.jsonb :payload, comment: "存储最后的结果"
      t.jsonb :form, comment: "存储 question sheet 表单的 dup"
      t.string :state
      t.datetime :done_at, comment: "提交时间"
      t.timestamps
    end
    create_table :assessment_activities do |t|
      t.references :app
      t.references :creator
      t.references :score_template
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.date :start_on, comment: "生效时间"
      t.date :end_on, comment: "失效时间"
      t.string :name, comment: "考核活动名称"
      t.text :content, comment: "考核活动内容"
      t.string :state, comment: "状态"
      t.jsonb :attachments, comment: "附件"
      t.boolean :catalog_enabled, comment: "是否启用分类"
      t.boolean :group_enabled, comment: "是否启用分组"
      t.jsonb :entry_form, comment: "entry自评填写的内容"
      t.timestamps
    end
    add_column :users, :political, :string, comment: "政治面貌"
    add_column :users, :education, :string, comment: "学位"
    add_column :users, :degree, :string, comment: "学历"
  end
end
