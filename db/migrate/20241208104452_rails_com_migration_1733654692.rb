class RailsComMigration1733654692 < ActiveRecord::Migration[7.1]

  def change
    create_table :opm_relate_actions do |t|
      t.references :app
      t.references :real_user
      t.references :target, polymorphic: true
      t.references :user, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :action_type, comment: "具体action的业务flag"
      t.string :action_option, comment: "前端可以设置的信息"
      t.string :action_flag, comment: "后端使用的逻辑字段，前端不能设置"
      t.timestamps
      t.index [:user_type, :user_id, :action_type], name: "opm_relate_actions_user_action_type_index"
      t.index [:target_type, :target_id, :action_type], name: "opm_relate_actions_target_action_type_index"
      t.index [:action_type, :target_type, :target_id, :user_type, :user_id, :action_flag], unique: true, name: "opm_relate_actions_uk_action_target_user"
    end
    create_table :opm_records do |t|
      t.references :app
      t.references :user
      t.references :member
      t.references :source, polymorphic: true
      t.string :type, comment: "STI属性"
      t.datetime :effective_at, comment: "生效时间"
      t.datetime :invalid_at, comment: "失效时间"
      t.string :name, comment: "名称"
      t.string :state, comment: "状态"
      t.string :code, comment: "编号"
      t.string :origin, comment: "来源"
      t.string :level, comment: "级别"
      t.integer :position, comment: "排序"
      t.jsonb :payload, comment: "其他字段"
      t.timestamps
    end
    create_table :opm_job_titles do |t|
      t.references :app
      t.references :group
      t.string :type, comment: "STI属性"
      t.string :name, comment: "名称"
      t.string :state, comment: "状态"
      t.string :level, comment: "等级"
      t.string :code, comment: "编号"
      t.integer :position, comment: "排序"
      t.jsonb :payload, comment: "其他字段"
      t.timestamps
    end
    create_table :opm_groups do |t|
      t.references :app
      t.string :type, comment: "STI属性"
      t.string :name, comment: "名称"
      t.string :state, comment: "状态"
      t.string :code, comment: "编号"
      t.integer :position, comment: "排序"
      t.jsonb :payload, comment: "额外字段"
      t.timestamps
    end
  end
end
