class SetupPgvector < ActiveRecord::Migration[7.1]
  def up
    # 启用 pgvector 扩展
    enable_extension 'vector'

    # 创建向量搜索表
    execute <<-SQL
      CREATE TABLE bot_document_embeddings (
        id bigserial primary key,
        content text NOT NULL,
        vectors vector(1536),
        metadata jsonb DEFAULT '{}',
        namespace character varying
      );

      CREATE INDEX ON bot_document_embeddings USING ivfflat (vectors vector_cosine_ops);
    SQL
  end

  def down
    execute 'DROP TABLE IF EXISTS bot_document_embeddings;'
    disable_extension 'vector'
  end
end
