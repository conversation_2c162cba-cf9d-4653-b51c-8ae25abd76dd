class RailsComMigration1742566082 < ActiveRecord::Migration[7.1]

  def change
    create_table :opm_holiday_travel_requests do |t|
      t.references :app
      t.references :user
      t.datetime :start_at, comment: "开始时间"
      t.datetime :end_at, comment: "结束时间"
      t.decimal :duration, comment: "时长"
      t.text :reason, comment: "理由"
      t.jsonb :attachment, comment: "附件"
      t.string :state
      t.boolean :is_workday, comment: "是否工作日"
      t.string :destination, comment: "目的地"
      t.string :contact_info, comment: "联系方式"
      t.string :report_type
      t.timestamps
    end
    create_table :opm_transactions do |t|
      t.references :user
      t.references :source, polymorphic: true
      t.string :category
      t.decimal :amount
      t.string :operation
      t.datetime :expires_at
      t.string :reason
      t.jsonb :details
      t.timestamps
    end
    create_table :opm_balances do |t|
      t.references :user
      t.string :category
      t.decimal :total
      t.datetime :expires_at
      t.string :reason
      t.jsonb :details
      t.timestamps
    end
    create_table :opm_leave_requests do |t|
      t.references :app
      t.references :user
      t.references :flowable, polymorphic: true
      t.string :type
      t.datetime :start_at
      t.datetime :end_at
      t.decimal :duration
      t.text :reason
      t.jsonb :attachment
      t.jsonb :model_payload
      t.string :leave_type
      t.string :unit_type
      t.decimal :min_unit
      t.decimal :max_unit
      t.decimal :min_interval
      t.timestamps
    end
    add_reference :users, :supervisor
  end
end
