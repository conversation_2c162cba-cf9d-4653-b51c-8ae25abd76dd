class RailsComMigration1709199468 < ActiveRecord::Migration[6.1]

  def change
    create_table :reg_qrcodes do |t|
      t.references :app
      t.references :record, polymorphic: true
      t.references :creator
      t.string :type, comment: "STI属性", index: true
      t.datetime :effective_at, comment: "生效时间", index: true
      t.datetime :invalid_at, comment: "失效时间", index: true
      t.string :code, comment: "码"
      t.string :state, comment: "状态"
      t.string :mod, comment: "模式"
      t.jsonb :payload, comment: "额外字段"
      t.timestamps
    end
    create_table :reg_records do |t|
      t.references :app
      t.references :origin, polymorphic: true
      t.references :source, polymorphic: true
      t.references :user
      t.references :creator
      t.references :qrcode
      t.string :type, comment: "STI属性", index: true
      t.string :state, comment: "状态"
      t.datetime :register_at, comment: "扫码时间"
      t.jsonb :payload, comment: "额外字段"
      t.timestamps
    end
    create_table :comments do |t|
      t.references :commentable, polymorphic: true
      t.references :user
      t.string :title, comment: "标题"
      t.text :subject, comment: "简述"
      t.text :body, comment: "内容"
      t.jsonb :attachments, comment: "附件"
      t.integer :parent_id, comment: "父节点"
      t.integer :lft, comment: "左节点"
      t.integer :rgt, comment: "右节点"
      t.integer :depth, comment: "层级", default: 0, null: false
      t.integer :children_count, comment: "子评论数量", default: 0, null: false
      t.integer :position, comment: "排序"
      t.integer :likes_count, comment: "点赞次数", default: 0
      t.timestamps
    end
    create_table :notify_like_actions do |t|
      t.references :app
      t.references :real_user
      t.references :target, polymorphic: true
      t.references :user, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :action_type, comment: "具体action的业务flag", null: false
      t.string :action_option, comment: "前端可以设置的信息"
      t.string :action_flag, comment: "后端使用的逻辑字段，前端不能设置"
      t.timestamps
      t.index [:user_type, :user_id, :action_type], name: "notify_like_actions_user_action_type_index"
      t.index [:target_type, :target_id, :action_type], name: "notify_like_actions_target_action_type_index"
      t.index [:action_type, :target_type, :target_id, :user_type, :user_id], unique: true, name: "notify_like_actions_uk_action_target_user"
    end
    add_column :memberships, :create_instance_state, :string, comment: "创建工作流的状态"
    create_table :state_events do |t|
      t.references :app
      t.references :machine
      t.references :transition
      t.references :user, polymorphic: true
      t.references :source
      t.references :target
      t.references :eventable, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :type, comment: "STI"
      t.string :state
      t.string :state_attr_name, comment: "状态机对应的模型属性名称"
      t.timestamps
    end
    create_table :bpm_instances do |t|
      t.references :app
      t.references :workflow
      t.references :creator
      t.references :flowable, polymorphic: true
      t.integer :comments_count, comment: "评论数量"
      t.string :comment_conf, default: "open"
      t.string :seq, comment: "编号", index: {:unique=>true}
      t.string :type, comment: "STI", index: true
      t.jsonb :payload, comment: "流程表单"
      t.jsonb :storage, comment: "instance的数据存储，主要是有配置map_key 的 value，另外保存了token中配置的内容", default: {}
      t.jsonb :summary, comment: "instance在列表页显示的内容", default: {}
      t.string :state, comment: "流程状态", index: true
      t.string :flowable_flag, comment: "flowable不同流程的flag"
      t.integer :spent_time_in_second, comment: "耗时时长"
      t.jsonb :cache_payload, comment: "额外存储的结构，根据场合可以作为payload的存储"
      t.datetime :action_at, comment: "激活时间", index: true
      t.jsonb :last_token_attr, comment: "最新token信息", default: {}
      t.timestamps
    end
    create_table :state_bpm_actions do |t|
      t.references :app
      t.references :real_user
      t.references :target, polymorphic: true
      t.references :user, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :action_type, comment: "具体action的业务flag", null: false
      t.string :action_option, comment: "前端可以设置的信息"
      t.string :action_flag, comment: "后端使用的逻辑字段，前端不能设置"
      t.timestamps
      t.index [:user_type, :user_id, :action_type], name: "state_bpm_actions_user_action_type_index"
      t.index [:target_type, :target_id, :action_type], name: "state_bpm_actions_target_action_type_index"
      t.index [:action_type, :target_type, :target_id, :user_type, :user_id], unique: true, name: "state_bpm_actions_uk_action_target_user"
    end
    create_table :state_machines do |t|
      t.references :app
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.jsonb :permits, default: {}
      t.boolean :permit_enable, comment: "是否开启permit的按钮"
      t.string :name, comment: "状态机名称"
      t.string :state_attr_name, comment: "状态机对应模型属性名称"
      t.string :klass, comment: "类名"
      t.string :klass_singular, comment: "自动生成的模型名称，因为路由上传是以underscore方式来传输，所以在这里需要能进行唯一性查找"
      t.string :flag, comment: "程序使用标识"
      t.timestamps
    end
    create_table :state_places do |t|
      t.references :machine
      t.string :seq, comment: "place的唯一序列号，保持一致"
      t.string :name, comment: "节点名称"
      t.string :state, comment: "节点状态"
      t.string :type, comment: "STI"
      t.integer :position, comment: "排序"
      t.jsonb :options
      t.jsonb :trigger_options
      t.timestamps
    end
    create_table :state_token_defines do |t|
      t.references :machine
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :seq, comment: "token_define的唯一序列号，保持一致"
      t.string :name, comment: "名称"
      t.string :type, comment: "STI"
      t.string :token_type, comment: "对应token的type"
      t.string :token_flag, comment: "对应token的flag"
      t.string :token_default_state, comment: "token生成的默认state"
      t.jsonb :token_form
      t.jsonb :options, comment: "配置信息"
      t.jsonb :limit_options
      t.jsonb :user_options
      t.timestamps
    end
    create_table :state_tokens do |t|
      t.references :app
      t.references :machine
      t.references :event
      t.references :transition
      t.references :token_define
      t.references :token_source, polymorphic: true
      t.references :eventable, polymorphic: true
      t.references :user, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.jsonb :payload, comment: "payload payload存储的字段"
      t.jsonb :payload_summary, comment: "payload summary存储的字段"
      t.string :type, comment: "STI"
      t.string :name, comment: "处理节点名称"
      t.string :flag, comment: "处理节点flag"
      t.string :user_name, comment: "user的名称"
      t.string :state
      t.jsonb :token_source_attributes, comment: "token source的attributes缓存"
      t.timestamps
    end
    create_table :state_transitions do |t|
      t.references :machine
      t.references :source
      t.references :target
      t.references :terminate_place
      t.jsonb :permits, default: {}
      t.boolean :permit_enable, comment: "是否开启permit的按钮"
      t.string :type, comment: "STI"
      t.string :seq, comment: "transition的唯一序列号，保持一致"
      t.string :name, comment: "名称"
      t.string :event_name, comment: "操作的英文名称"
      t.string :flag, comment: "程序使用的标记位"
      t.boolean :auto_trigger, comment: "是否自动触发"
      t.jsonb :options, comment: "状态转换的具体配置信息，根据STI的类型不同而不同"
      t.jsonb :trigger_options
      t.timestamps
    end
    create_table :state_activate_actions do |t|
      t.references :app
      t.references :real_user
      t.references :target, polymorphic: true
      t.references :user, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :action_type, comment: "具体action的业务flag", null: false
      t.string :action_option, comment: "前端可以设置的信息"
      t.string :action_flag, comment: "后端使用的逻辑字段，前端不能设置"
      t.timestamps
      t.index [:user_type, :user_id, :action_type], name: "state_activate_actions_user_action_type_index"
      t.index [:target_type, :target_id, :action_type], name: "state_activate_actions_target_action_type_index"
      t.index [:action_type, :target_type, :target_id, :user_type, :user_id], unique: true, name: "state_activate_actions_uk_action_target_user"
    end
    create_table :notify_info_messages do |t|
      t.references :app
      t.references :user, polymorphic: true
      t.references :create_user
      t.references :notifyable, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :flag, comment: "发送标识，保证唯一性"
      t.string :title, comment: "发送标题"
      t.string :content, comment: "发送内容"
      t.jsonb :meta, comment: "额外信息"
      t.string :url, comment: "链接地址"
      t.datetime :read_at, index: true
      t.boolean :is_read, index: true
      t.timestamps
    end
    create_table :notify_sms_messages do |t|
      t.references :notifyable, polymorphic: true
      t.string :mobile, comment: "发送手机号"
      t.string :title, comment: "发送标题"
      t.string :account, comment: "发送账号"
      t.string :content, comment: "发送内容"
      t.jsonb :response, comment: "发送结果", default: {}
      t.timestamps
    end
    create_table :notify_wechat_template_messages do |t|
      t.references :notifyable, polymorphic: true
      t.references :user
      t.string :oauth_app_id, comment: "微信服务名称的标识"
      t.string :openid, comment: "微信发送的openid"
      t.jsonb :message, comment: "发送内容"
      t.jsonb :response, comment: "发送结果", default: {}
      t.timestamps
    end
    create_table :bpm_catalogs do |t|
      t.references :app
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :name, comment: "名称"
      t.jsonb :icon, comment: "图标"
      t.integer :position, comment: "排序"
      t.boolean :published, comment: "是否发布"
      t.timestamps
    end
    create_table :bpm_workflows do |t|
      t.references :app
      t.references :creator
      t.references :mod
      t.references :catalog
      t.jsonb :permits, default: {}
      t.boolean :permit_enable, comment: "是否开启permit的按钮"
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :type, comment: "STI", index: true
      t.string :name, comment: "流程名称"
      t.text :desc, comment: "流程描述"
      t.jsonb :icon, comment: "流程图标"
      t.jsonb :cover_image, comment: "流程封面"
      t.string :state, default: :todo
      t.integer :position, comment: "catalog内排序"
      t.string :instance_type, comment: "自动生成的instance_type"
      t.string :classify, default: "direct"
      t.jsonb :form, default: {}
      t.jsonb :meta, comment: "工作流额外配置信息 ", default: {}
      t.jsonb :token_actions, default: {}
      t.jsonb :trigger_options, default: {}
      t.boolean :auto_complete_same_handle_token, comment: "是否跳过连续相同的审批人"
      t.jsonb :submit_options
      t.jsonb :form_setting
      t.timestamps
    end
    create_table :bpm_rules do |t|
      t.references :workflow
      t.string :name, comment: "规则名称"
      t.integer :time_in_second, comment: "设定时间范围"
      t.string :type, comment: "STI"
      t.jsonb :options, comment: "具体配置内容"
      t.timestamps
    end
    create_table :bpm_places do |t|
      t.references :workflow
      t.string :type, comment: "STI"
      t.string :seq, comment: "place的唯一序列号，保持一致"
      t.string :name, comment: "节点名称"
      t.string :desc, comment: "节点描述"
      t.integer :position, comment: "根据 tree 边生成的 position"
      t.boolean :is_summary, comment: "是否快捷引用"
      t.jsonb :fields
      t.jsonb :place_form, default: {}
      t.jsonb :options, comment: "节点的配置信息", default: {}
      t.jsonb :timer_options, default: {}
      t.jsonb :trigger_options, default: {}
      t.jsonb :token_actions, default: {}
      t.jsonb :layout_options, comment: "前端页面使用的配置", default: {}
      t.jsonb :activate_options
      t.jsonb :token_source_options
      t.jsonb :form_setting
      t.timestamps
    end
    add_reference :model_settings, :bpm_workflow
    add_reference :model_settings, :ref_model_setting
    add_column :model_settings, :ref_model_setting_flag, :string, comment: "关联model_setting_flag"
    create_table :bpm_tokens do |t|
      t.references :place
      t.references :previous_token
      t.references :operator
      t.references :app
      t.references :workflow
      t.references :instance
      t.references :activate_source, polymorphic: true
      t.references :token_source, polymorphic: true
      t.string :type, comment: "STI", index: true
      t.string :place_type, comment: "Place的类型"
      t.string :transition_type, comment: "Transition的类型"
      t.string :name, comment: "Token的名称，默认取自Place"
      t.string :state, comment: "Token状态", index: true
      t.text :comment, comment: "审批备注"
      t.jsonb :options, comment: "Token的额外信息JSON", default: {}
      t.jsonb :token_payload, comment: "对应place的place_payload，存储审批时候存储在tokne中的信息", default: {}
      t.integer :spent_time_in_second, comment: "耗时时长"
      t.jsonb :operate_logs
      t.string :action_key, comment: "保存上一个action的操作"
      t.string :action_flag, comment: "保存action的操作flag，action key有可能是重复的，通过action_flag来做区分"
      t.integer :timestamp, comment: "时间戳，当前批次"
      t.datetime :action_at, comment: "激活时间", index: true
      t.timestamps
    end
    create_table :bpm_instance_relations do |t|
      t.references :instance
      t.references :source, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :seq, comment: "随机数"
      t.string :model_setting_flag, comment: "对应模型的flag"
      t.timestamps
    end
    create_table :bpm_place_relations do |t|
      t.references :workflow
      t.references :source
      t.references :target
      t.timestamps
    end
    create_table :bpm_stars do |t|
      t.references :user
      t.references :workflow
      t.integer :position, comment: "排序"
      t.timestamps
    end
    create_table :bpm_transitions do |t|
      t.references :workflow
      t.references :place
      t.string :type, comment: "STI"
      t.jsonb :callback_options, comment: "回调设置", default: {}
      t.jsonb :options, comment: "transition跳转的额外设置", default: {}
      t.timestamps
    end
    create_table :bpm_workflow_relations do |t|
      t.references :workflowable, polymorphic: true
      t.references :workflow
      t.timestamps
    end
    add_column :serve_activities, :create_instance_state, :string, comment: "创建工作流的状态"
  end
end
