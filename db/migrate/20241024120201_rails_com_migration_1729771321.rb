class RailsComMigration1729771321 < ActiveRecord::Migration[7.1]

  def change
    create_table :serve_rule_groups do |t|
      t.references :app
      t.references :submodule
      t.string :name, comment: "名称"
      t.string :state, comment: "状态"
      t.integer :position, comment: "排序"
      t.jsonb :payload, comment: "其他字段"
      t.timestamps
    end
    add_column :serve_origins, :latest_send_at, :datetime, comment: "最新来源时间"
    add_reference :serve_rules, :rule_group
  end
end
