class RailsComMigration1728962293 < ActiveRecord::Migration[7.1]

  def change
    create_table :serve_org_actions do |t|
      t.references :app
      t.references :real_user
      t.references :target, polymorphic: true
      t.references :user, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :action_type, comment: "具体action的业务flag"
      t.string :action_option, comment: "前端可以设置的信息"
      t.string :action_flag, comment: "后端使用的逻辑字段，前端不能设置"
      t.timestamps
      t.index [:user_type, :user_id, :action_type], name: "serve_org_actions_user_action_type_index"
      t.index [:target_type, :target_id, :action_type], name: "serve_org_actions_target_action_type_index"
      t.index [:action_type, :target_type, :target_id, :user_type, :user_id, :action_flag], unique: true, name: "serve_org_actions_uk_action_target_user"
    end
    add_reference :serve_packs, :creator
    add_reference :serve_origins, :submodule
    create_table :grant_applications do |t|
      t.references :app
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.datetime :effective_at, comment: "生效时间"
      t.datetime :invalid_at, comment: "失效时间"
      t.string :name, comment: "应用名称"
      t.string :type, comment: "STI类型"
      t.string :app_key, comment: "app key"
      t.string :app_secret, comment: "app secret"
      t.jsonb :options, comment: "额外的配置, { encrypt: sm 或 aes }"
      t.timestamps
    end
    create_table :dingtalk_template_messages do |t|
      t.references :notifyable, polymorphic: true
      t.references :user
      t.string :seq, comment: "编号"
      t.datetime :read_at
      t.boolean :is_read
      t.string :oauth_app_id, comment: "服务名称的标识，对应client的code"
      t.string :openid, comment: "发送的openid，浙政钉是accountId"
      t.jsonb :message, comment: "发送内容"
      t.jsonb :response, comment: "发送结果"
      t.timestamps
    end
    create_table :dingtalk_clients do |t|
      t.references :app
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :name, comment: "名称"
      t.string :code, comment: "标识"
      t.string :app_key, comment: "app key"
      t.string :app_secret, comment: "app secret"
      t.string :corp_id, comment: "商户id或者租户id"
      t.string :type, comment: "STI"
      t.jsonb :options, comment: "配置信息"
      t.timestamps
    end
  end
end
