class RailsComMigration1729583006 < ActiveRecord::Migration[7.1]

  def change
    add_reference :serve_packs, :rule_record
    create_table :schedule_rule_records do |t|
      t.references :rule, polymorphic: true
      t.string :type, comment: "STI属性"
      t.datetime :schedule_next_time, comment: "下个周期时间"
      t.datetime :schedule_offset_at, comment: "实际执行时间（考虑时间偏移）"
      t.datetime :schedule_occurred_at, comment: "原始调度时间"
      t.string :schedule_uuid, comment: "规则schedule val小项的uuid"
      t.jsonb :schedule_data, comment: "存储具体规则的内容"
      t.timestamps
    end
    create_table :schedule_rules do |t|
      t.references :app
      t.references :creator
      t.jsonb :rule_conf, comment: "规则配置"
      t.string :rule_record_type, comment: "rule_record 的 STI 类型"
      t.string :uuid, comment: "规则唯一标识"
      t.string :name, comment: "规则名称"
      t.timestamps
    end
    add_column :serve_rules, :rule_conf, :jsonb, comment: "规则配置"
    add_column :serve_rules, :rule_record_type, :string, comment: "rule_record 的 STI 类型"
  end
end
