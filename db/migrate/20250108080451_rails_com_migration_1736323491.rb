class RailsComMigration1736323491 < ActiveRecord::Migration[7.1]

  def change
    create_table :bot_report_templates do |t|
      t.references :app
      t.string :name, comment: "名称"
      t.string :title, comment: "标题"
      t.text :content, comment: "内容"
      t.text :prompt, comment: "提示词"
      t.jsonb :conf, comment: "变量配置"
      t.timestamps
    end
    create_table :bot_reports do |t|
      t.references :app
      t.references :user
      t.references :report_template
      t.string :name, comment: "名称"
      t.string :title, comment: "标题"
      t.text :content, comment: "内容"
      t.jsonb :variables, comment: "变量"
      t.timestamps
    end
  end
end
