class RailsComMigration1736305424 < ActiveRecord::Migration[7.1]

  def change
    create_table :bot_paperwork_results do |t|
      t.references :paperwork
      t.string :name, comment: "结果名称"
      t.string :raw, comment: "原文"
      t.jsonb :payload, comment: "结果"
      t.timestamps
    end
    create_table :bot_intent_relations do |t|
      t.references :agent
      t.references :intent
      t.timestamps
    end
    create_table :bot_messages do |t|
      t.references :app
      t.references :conversation
      t.string :role, comment: "发送对象"
      t.jsonb :meta, comment: "消息发送的内容"
      t.timestamps
    end
    create_table :bot_conversations do |t|
      t.references :app
      t.references :agent
      t.references :user
      t.string :name, comment: "名称"
      t.timestamps
    end
    create_table :bot_artifacts do |t|
      t.references :conversation
      t.references :source, polymorphic: true
      t.string :intent_name, comment: "根据名称"
      t.string :tool_cname, comment: "根据模型名称"
      t.string :tool_function, comment: "根据function名称"
      t.jsonb :tool_conf, comment: "参数配置"
      t.jsonb :function_params, comment: "函数调用信息，转换前"
      t.jsonb :meta, comment: "参数内容，自定义的方式"
      t.jsonb :info, comment: "message返回的信息"
      t.timestamps
    end
    create_table :bot_paperworks do |t|
      t.references :app
      t.references :user
      t.string :name, comment: "名称"
      t.datetime :operate_at, comment: "操作时间"
      t.string :state
      t.jsonb :attachment, comment: "附件结构，单一文件"
      t.jsonb :response, comment: "响应"
      t.text :prompt_text, comment: "提示词，由外部传入"
      t.timestamps
    end
    create_table :bot_intents do |t|
      t.references :app
      t.string :type, comment: "STI属性"
      t.string :name, comment: "工具名称"
      t.text :description, comment: "工具描述"
      t.string :tool_cname, comment: "工具类的名称"
      t.jsonb :tool_conf, comment: "工具配置"
      t.string :llm_model_key, comment: "LlmFactory默认的大模型"
      t.timestamps
    end
    create_table :bot_agents do |t|
      t.references :app
      t.string :type, comment: "STI属性"
      t.string :name, comment: "工具名称"
      t.text :description, comment: "工具描述"
      t.text :instructions, comment: "工具介绍"
      t.string :llm_model_key, comment: "LlmFactory默认的大模型"
      t.integer :max_history_messages, comment: "最大历史消息数"
      t.timestamps
    end
  end
end
