class RailsComMigration1735601073 < ActiveRecord::Migration[7.1]

  def change
    create_table :bot_paperwork_results do |t|
      t.references :paperwork
      t.string :name, comment: "结果名称"
      t.string :raw, comment: "原文"
      t.jsonb :payload, comment: "结果"
      t.timestamps
    end
    create_table :bot_paperworks do |t|
      t.references :app
      t.references :user
      t.string :name, comment: "名称"
      t.datetime :operate_at, comment: "操作时间"
      t.string :state
      t.jsonb :attachment, comment: "附件结构，单一文件"
      t.jsonb :response, comment: "响应"
      t.text :prompt_text, comment: "提示词，由外部传入"
      t.timestamps
    end
  end
end
