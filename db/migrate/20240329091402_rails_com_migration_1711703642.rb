class RailsComMigration1711703642 < ActiveRecord::Migration[7.1]

  def change
    create_table :active_storage_attachments do |t|
      t.references :record, polymorphic: true
      t.references :blob
      t.string :name
      t.timestamps
      t.index [:record_type, :record_id, :name, :blob_id], unique: true
    end
    create_table :notify_template_messages do |t|
      t.references :app
      t.references :notifyable, polymorphic: true
      t.references :user
      t.string :type, comment: "STI属性"
      t.string :state, comment: "状态"
      t.string :oauth_app_id, comment: "微信服务名称的标识"
      t.string :openid, comment: "微信发送的openid"
      t.jsonb :body, comment: "发送内容"
      t.jsonb :response, comment: "发送结果"
      t.timestamps
    end
    create_table :org_clients do |t|
      t.references :app
      t.references :org
      t.references :client, polymorphic: true
      t.timestamps
    end
    add_column :member_requests, :state, :string, comment: "状态"
    add_column :duty_groups, :code, :string, comment: "岗位组标识"
    create_table :role_permission_relations do |t|
      t.references :role
      t.references :permission, polymorphic: true
      t.timestamps
    end
    create_table :route_settings do |t|
      t.references :app
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :name, comment: "module名称"
      t.jsonb :conf, comment: "module导出路由"
      t.timestamps
    end
    create_table :permit_permissions do |t|
      t.references :app
      t.references :mod
      t.references :tanent
      t.references :user
      t.string :platform, comment: "平台"
      t.string :aname, comment: "action名称"
      t.string :cname, comment: "controller名称"
      t.string :klass, comment: "controller"
      t.string :action, comment: "action"
      t.jsonb :whitelist
      t.jsonb :blacklist
      t.jsonb :payload
      t.timestamps
    end
    create_table :role_permit_actions do |t|
      t.references :app
      t.references :real_user
      t.references :target, polymorphic: true
      t.references :user, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :action_type, comment: "具体action的业务flag"
      t.string :action_option, comment: "前端可以设置的信息"
      t.string :action_flag, comment: "后端使用的逻辑字段，前端不能设置"
      t.timestamps
      t.index [:user_type, :user_id, :action_type], name: "role_permit_actions_user_action_type_index"
      t.index [:target_type, :target_id, :action_type], name: "role_permit_actions_target_action_type_index"
      t.index [:action_type, :target_type, :target_id, :user_type, :user_id], unique: true, name: "role_permit_actions_uk_action_target_user"
    end
    add_column :duties, :code, :string, comment: "岗位标识"
    add_column :member_identities, :config, :jsonb, comment: "配置"
  end
end
