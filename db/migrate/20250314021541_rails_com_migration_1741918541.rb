class RailsComMigration1741918541 < ActiveRecord::Migration[7.1]

  def change
    create_table :audit_logs do |t|
      t.references :app
      t.references :user
      t.references :auditable, polymorphic: true
      t.string :controller_path, comment: "controller 完整路径"
      t.string :controller_name, comment: "controller名"
      t.string :action_name, comment: "action名"
      t.string :request_id, comment: "请求id"
      t.string :request_method, comment: "请求方式"
      t.string :ip_address, comment: "访问IP地址"
      t.text :url, comment: "request url"
      t.text :referrer, comment: "request referrer"
      t.string :user_agent, comment: "访问终端"
      t.string :platform, comment: "访问终端"
      t.jsonb :params, comment: "访问参数"
      t.text :message, comment: "额外信息"
      t.timestamps
    end
  end
end
