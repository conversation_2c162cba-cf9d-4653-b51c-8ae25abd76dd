class RailsComMigration1734579104 < ActiveRecord::Migration[7.1]

  def change
    create_table :bot_intent_relations do |t|
      t.references :agent
      t.references :intent
      t.timestamps
    end
    create_table :bot_intents do |t|
      t.references :app
      t.string :type, comment: "STI属性"
      t.string :name, comment: "工具名称"
      t.text :description, comment: "工具描述"
      t.string :tool_cname, comment: "工具类的名称"
      t.jsonb :tool_conf, comment: "工具配置"
      t.timestamps
    end
    add_reference :bot_conversations, :agent
    remove_column :bot_conversations, :assistant_id, :integer, limit: 8
    create_table :bot_agents do |t|
      t.references :app
      t.string :type, comment: "STI属性"
      t.string :name, comment: "工具名称"
      t.text :description, comment: "工具描述"
      t.text :instructions, comment: "工具介绍"
      t.timestamps
    end
  end
end
