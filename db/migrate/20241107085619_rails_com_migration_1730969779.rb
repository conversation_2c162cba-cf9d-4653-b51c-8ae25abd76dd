class RailsComMigration1730969779 < ActiveRecord::Migration[7.1]

  def change
    add_column :bpm_workflows, :enable_level, :boolean, comment: "是否启用 instance 优先级"
    add_column :bpm_workflows, :level_options, :jsonb, comment: "优先级配置"
    create_table :iest_paperwork_results do |t|
      t.references :paperwork
      t.string :name, comment: "结果名称"
      t.string :raw, comment: "原文"
      t.jsonb :payload, comment: "结果"
      t.timestamps
    end
    create_table :iest_paperworks do |t|
      t.references :user
      t.references :app
      t.references :org
      t.string :name, comment: "名称"
      t.datetime :operate_at, comment: "操作时间"
      t.string :state, comment: "状态"
      t.jsonb :attachment, comment: "附件"
      t.jsonb :response, comment: "响应"
      t.timestamps
    end
    add_column :bpm_instances, :level, :string, comment: "流程级别"
  end
end
