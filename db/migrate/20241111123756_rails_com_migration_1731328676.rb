class RailsComMigration1731328676 < ActiveRecord::Migration[7.1]

  def change
    create_table :serve_bid_tenders do |t|
      t.references :app
      t.references :bid_project
      t.string :seq, comment: "编号"
      t.string :code, comment: "项目编号"
      t.datetime :bid_at, comment: "评标时间"
      t.string :bid_manager_name, comment: "项目负责人姓名"
      t.decimal :amount, comment: "金额"
      t.jsonb :payload, comment: "扩展字段"
      t.timestamps
    end
    create_table :serve_bid_results do |t|
      t.references :app
      t.references :bid_project
      t.string :seq, comment: "编号"
      t.string :code, comment: "项目编号"
      t.string :state
      t.datetime :win_at, comment: "中标时间"
      t.string :unit_code, comment: "中标单位代码"
      t.string :unit, comment: "中标单位"
      t.decimal :amount, comment: "金额"
      t.jsonb :payload, comment: "额外字段"
      t.timestamps
    end
    create_table :serve_bid_notices do |t|
      t.references :app
      t.references :bid_project
      t.string :seq, comment: "编号"
      t.datetime :start_at, comment: "生效时间"
      t.datetime :end_at, comment: "失效时间"
      t.string :code, comment: "项目编号"
      t.string :state
      t.datetime :open_at, comment: "开标时间"
      t.decimal :amount, comment: "金额"
      t.jsonb :payload, comment: "扩展字段"
      t.timestamps
    end
    create_table :serve_bid_items do |t|
      t.references :app
      t.references :bid_project
      t.string :seq, comment: "编号"
      t.string :code, comment: "项目编号"
      t.datetime :setup_at, comment: "评标时间"
      t.string :connector, comment: "联系人"
      t.string :legaler_name, comment: "法人"
      t.decimal :amount, comment: "金额"
      t.jsonb :payload, comment: "扩展字段"
      t.timestamps
    end
    add_column :serve_bid_projects, :seq, :string, comment: "编号"
    add_column :serve_bid_projects, :register_at, :datetime, comment: "登记时间"
    add_column :serve_bid_projects, :win_at, :datetime, comment: "中标时间"
  end
end
