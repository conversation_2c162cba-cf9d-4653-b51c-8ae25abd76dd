class CreateIrsGovernmentProjects < ActiveRecord::Migration[7.1]
  def change
    create_table :irs_government_projects do |t|
      t.date :query_date, null: false, comment: "查询日期"
      t.jsonb :raw_data, comment: "原始JSON响应数据"
      t.integer :projects_count, default: 0, comment: "项目数量"
      t.boolean :success, default: false, comment: "是否成功获取数据"
      t.text :error_message, comment: "错误信息"
      t.datetime :cached_at, null: false, comment: "缓存时间"

      t.timestamps
    end

    # 添加索引
    add_index :irs_government_projects, :query_date, unique: true, comment: "查询日期唯一索引"
    add_index :irs_government_projects, :success, comment: "成功状态索引"
    add_index :irs_government_projects, :cached_at, comment: "缓存时间索引"

    # 添加GIN索引用于JSON查询
    add_index :irs_government_projects, :raw_data, using: :gin, comment: "JSON数据索引"
  end
end
