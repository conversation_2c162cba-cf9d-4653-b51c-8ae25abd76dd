class RailsComMigration1733313048 < ActiveRecord::Migration[7.1]

  def change
    create_table :opm_former_employees do |t|
      t.references :app
      t.references :org
      t.references :member
      t.references :user
      t.date :leave_at, comment: "离职时间"
      t.timestamps
    end
    create_table :opm_items do |t|
      t.references :app
      t.references :user
      t.references :member
      t.string :type, comment: "STI属性"
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.datetime :effective_at, comment: "生效时间"
      t.datetime :invalid_at, comment: "失效时间"
      t.string :seq, comment: "编号"
      t.string :create_instance_state, comment: "创建工作流的状态"
      t.datetime :create_instance_timestamp, comment: "创建工作流的操作时间"
      t.string :name, comment: "名称"
      t.string :state, comment: "状态"
      t.jsonb :content, comment: "内容"
      t.string :origin, comment: "来源"
      t.string :mode, comment: "分类"
      t.jsonb :payload, comment: "其他字段"
      t.timestamps
    end
    create_table :spider_origins do |t|
      t.references :app
      t.string :type, comment: "STI属性"
      t.string :name, comment: "名称"
      t.string :state
      t.integer :position, comment: "排序"
      t.string :mode, comment: "分类"
      t.datetime :latest_send_at, comment: "最新更新时间"
      t.jsonb :payload, comment: "额外字段"
      t.jsonb :option, comment: "配置"
      t.timestamps
    end
  end
end
