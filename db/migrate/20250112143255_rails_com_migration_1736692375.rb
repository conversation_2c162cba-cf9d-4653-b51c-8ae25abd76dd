class RailsComMigration1736692375 < ActiveRecord::Migration[7.1]

  def change
    create_table :bot_meetings do |t|
      t.references :app
      t.references :user
      t.jsonb :file, comment: "文件"
      t.string :name, comment: "会议名称"
      t.string :meeting_time, comment: "会议时间"
      t.text :background, comment: "会议背景"
      t.string :topic, comment: "会议主题"
      t.text :summary, comment: "会议纪要"
      t.string :participants, comment: "与会人员"
      t.jsonb :audio, comment: "会议录音"
      t.timestamps
    end
  end
end
