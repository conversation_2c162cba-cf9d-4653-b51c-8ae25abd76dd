class RailsComMigration1730207810 < ActiveRecord::Migration[7.1]

  def change
    add_reference :iest_ai_chat_intents, :tanent
    add_reference :iest_ai_chat_conversations, :tanent
    create_table :serve_tanent_actions do |t|
      t.references :app
      t.references :real_user
      t.references :target, polymorphic: true
      t.references :user, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :action_type, comment: "具体action的业务flag"
      t.string :action_option, comment: "前端可以设置的信息"
      t.string :action_flag, comment: "后端使用的逻辑字段，前端不能设置"
      t.timestamps
      t.index [:user_type, :user_id, :action_type], name: "serve_tanent_actions_user_action_type_index"
      t.index [:target_type, :target_id, :action_type], name: "serve_tanent_actions_target_action_type_index"
      t.index [:action_type, :target_type, :target_id, :user_type, :user_id, :action_flag], unique: true, name: "serve_tanent_actions_uk_action_target_user"
    end
  end
end
