class RailsComMigration1736985382 < ActiveRecord::Migration[7.1]

  def change
    add_reference :org_members, :tanent
    add_reference :member_requests, :tanent
    add_column :member_requests, :type, :string, comment: "STI"
    create_table :bot_review_results do |t|
      t.references :review_document
      t.references :review_rule
      t.string :name, comment: "结果名称"
      t.text :raw, comment: "原文"
      t.integer :score, comment: "分数"
      t.text :reason, comment: "原因"
      t.text :suggest, comment: "建议"
      t.string :level
      t.jsonb :meta, comment: "额外信息"
      t.timestamps
    end
    create_table :bot_reports do |t|
      t.references :app
      t.references :user
      t.references :report_template
      t.string :name, comment: "名称"
      t.string :title, comment: "标题"
      t.text :content, comment: "内容"
      t.jsonb :variables, comment: "变量"
      t.timestamps
    end
    create_table :bot_review_rules do |t|
      t.references :reviewer
      t.string :name, comment: "名称"
      t.text :content, comment: "内容"
      t.text :scoring_criteria, comment: "评分标准"
      t.boolean :active, comment: "是否启用"
      t.timestamps
    end
    create_table :bot_reviewers do |t|
      t.references :app
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :name, comment: "名称"
      t.text :description, comment: "描述"
      t.text :instructions, comment: "介绍"
      t.jsonb :icon, comment: "图标"
      t.timestamps
    end
    create_table :bot_review_documents do |t|
      t.references :app
      t.references :user
      t.references :reviewer
      t.jsonb :file, comment: "文件或内容"
      t.string :name, comment: "名称"
      t.string :state
      t.timestamps
    end
    create_table :bot_report_templates do |t|
      t.references :app
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :name, comment: "名称"
      t.string :title, comment: "标题"
      t.text :content, comment: "内容"
      t.text :instructions, comment: "介绍"
      t.text :prompt, comment: "提示词"
      t.jsonb :conf, comment: "变量配置"
      t.jsonb :icon, comment: "图标"
      t.timestamps
    end
    create_table :bot_meetings do |t|
      t.references :app
      t.references :user
      t.jsonb :file, comment: "文件或内容"
      t.jsonb :permits, comment: "权限设置"
      t.boolean :permit_enable, comment: "是否开启permit的按钮"
      t.string :name, comment: "会议名称"
      t.string :state
      t.string :meeting_time, comment: "会议时间"
      t.text :background, comment: "会议背景"
      t.string :topic, comment: "会议主题"
      t.text :summary, comment: "会议纪要"
      t.string :participants, comment: "与会人员"
      t.jsonb :audio, comment: "会议录音"
      t.jsonb :payload, comment: "额外字段"
      t.timestamps
    end
    add_reference :orgs, :tanent
    add_reference :members, :tanent
  end
end
