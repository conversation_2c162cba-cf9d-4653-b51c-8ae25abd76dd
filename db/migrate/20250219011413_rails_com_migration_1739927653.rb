class RailsComMigration1739927653 < ActiveRecord::Migration[7.1]

  def change
    add_reference :member_requests, :member
    add_column :bpm_workflows, :conf, :jsonb, comment: "其他配置"
    create_table :dingtalk_work_notifications do |t|
      t.references :notifyable, polymorphic: true
      t.references :user
      t.string :seq, comment: "编号"
      t.datetime :read_at
      t.boolean :is_read
      t.string :oauth_app_id, comment: "服务名称的标识，对应client的code"
      t.string :openid, comment: "接收者的openid"
      t.string :msg_type, comment: "消息类型: text/link/markdown/action_card/oa"
      t.jsonb :content, comment: "消息内容"
      t.jsonb :response, comment: "发送结果"
      t.string :at_mobiles
      t.string :at_user_ids
      t.boolean :at_all, comment: "是否@所有人"
      t.string :task_id, comment: "工作通知任务ID"
      t.jsonb :progress, comment: "发送进度"
      t.jsonb :send_result, comment: "发送结果详情"
      t.timestamps
    end
    add_column :dingtalk_clients, :company_corp_id, :string, comment: "钉钉企业corp_id"
    add_reference :bot_review_results, :document_source, polymorphic: true
    remove_column :bot_review_results, :review_document_id, :integer, limit: 8
    add_reference :bot_review_rules, :review_source, polymorphic: true
    remove_column :bot_review_rules, :reviewer_id, :integer, limit: 8
    add_column :bot_reviewers, :review_instructions, :text, comment: "审查介绍"
    remove_column :bot_reviewers, :instructions, :text, comment: "介绍"
    add_column :bot_review_documents, :review_state, :string
    remove_column :bot_review_documents, :state, :string
    add_column :bot_reports, :review_state, :string
    add_column :bot_report_templates, :review_instructions, :text, comment: "审查介绍"
    add_column :bot_meetings, :seq, :string, comment: "编号"
  end
end
