class RailsComMigration1730351540 < ActiveRecord::Migration[7.1]

  def change
    create_table :serve_bid_projects do |t|
      t.references :region_area
      t.references :app
      t.references :org
      t.references :contactor
      t.references :manager
      t.string :province, comment: "省"
      t.string :city, comment: "市"
      t.string :district, comment: "区"
      t.datetime :start_at, comment: "生效时间"
      t.datetime :end_at, comment: "失效时间"
      t.string :code, comment: "项目编号"
      t.string :name, comment: "标题"
      t.string :state
      t.jsonb :content, comment: "内容"
      t.string :region_code, comment: "地区编码"
      t.datetime :setup_at, comment: "创建时间"
      t.datetime :open_at, comment: "开标时间"
      t.string :manager_name, comment: "负责人"
      t.string :contactor_name, comment: "联系人"
      t.string :phone, comment: "联系电话"
      t.string :unit, comment: "招标单位"
      t.string :unit_code, comment: "招标单位编号"
      t.string :send_state, comment: "发送消息状态"
      t.decimal :amount, comment: "金额"
      t.jsonb :meta, comment: "元数据"
      t.jsonb :payload, comment: "扩展字段"
      t.jsonb :attachments, comment: "附件"
      t.timestamps
    end
  end
end
