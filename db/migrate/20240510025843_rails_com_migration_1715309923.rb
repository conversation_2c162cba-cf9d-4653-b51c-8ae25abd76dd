class RailsComMigration1715309923 < ActiveRecord::Migration[7.1]

  def change
    create_table :serve_groups do |t|
      t.references :app
      t.references :submodule
      t.string :type, comment: "STI属性"
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :name, comment: "名称"
      t.string :state, comment: "状态"
      t.integer :position, comment: "排序"
      t.timestamps
    end
    add_column :serve_activities, :attachments, :jsonb, comment: "附件"
  end
end
