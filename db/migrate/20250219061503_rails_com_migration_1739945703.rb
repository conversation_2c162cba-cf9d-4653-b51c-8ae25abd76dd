class RailsComMigration1739945703 < ActiveRecord::Migration[7.1]

  def change
    add_reference :org_members, :tanent
    add_reference :member_requests, :tanent
    add_reference :member_requests, :member
    add_column :member_requests, :type, :string, comment: "STI"
    add_reference :orgs, :tanent
    add_reference :members, :tanent
    add_column :members, :effective_at, :date, comment: "生效时间"
    add_column :members, :invalid_at, :date, comment: "失效时间"
  end
end
