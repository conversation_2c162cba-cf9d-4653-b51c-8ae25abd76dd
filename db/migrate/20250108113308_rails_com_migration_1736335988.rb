class RailsComMigration1736335988 < ActiveRecord::Migration[7.1]

  def change
    create_table :dingtalk_ding_messages do |t|
      t.references :notifyable, polymorphic: true
      t.references :user
      t.string :seq, comment: "编号"
      t.string :oauth_app_id, comment: "服务名称的标识，对应client的code"
      t.jsonb :creator, comment: "创建者信息"
      t.jsonb :dingBody, comment: "发送内容"
      t.jsonb :receivers, comment: "接收者信息(数组对象)"
      t.jsonb :source, comment: "im会话"
      t.boolean :sendToIm, comment: "是否发送到IM会话"
      t.string :scene, comment: "是否发送到IM会话"
      t.jsonb :response, comment: "发送结果"
      t.timestamps
    end
  end
end
