class RailsComMigration1743491911 < ActiveRecord::Migration[7.1]

  def change
    create_table :serve_receivers do |t|
      t.references :app
      t.references :user
      t.references :pack
      t.string :state, comment: "状态"
      t.jsonb :payload, comment: "其他字段"
      t.timestamps
    end
    add_reference :serve_messages, :ai_message
    create_table :serve_ai_messages do |t|
      t.references :app
      t.references :creator
      t.references :org
      t.references :pack
      t.references :rule
      t.references :ref_ai_message
      t.string :type, comment: "STI属性"
      t.string :seq, comment: "编号"
      t.string :name, comment: "名称"
      t.string :state
      t.text :content, comment: "内容"
      t.jsonb :option, comment: "配置"
      t.jsonb :payload, comment: "其他字段"
      t.timestamps
    end
  end
end
