class RailsComMigration1708346849 < ActiveRecord::Migration[6.1]

  def change
    create_table :member_identities do |t|
      t.references :app
      t.references :org
      t.jsonb :manages, default: {}
      t.boolean :manage_enable, comment: "是否开启permit的按钮"
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :ancestry, comment: "树形结构", index: true
      t.string :name, comment: "身份名称"
      t.string :member_type, comment: "Member的类型"
      t.integer :depth, comment: "树结构深度"
      t.integer :children_count, comment: "子对象的数据", default: 0
      t.jsonb :form, comment: "Member配置的表单", default: {}
      t.timestamps
    end
    create_table :users do |t|
      t.references :app
      t.references :tanent
      t.references :ref_user
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.datetime :effective_at, comment: "生效时间", index: true
      t.datetime :invalid_at, comment: "失效时间", index: true
      t.string :account, comment: "账号，关联登录", index: true
      t.string :name, comment: "用户姓名"
      t.string :nickname, comment: "用户昵称"
      t.string :pinyin, comment: "用户名拼音"
      t.string :mobile, comment: "用户手机号"
      t.string :email, comment: "用户邮箱"
      t.string :gender, comment: "性别"
      t.jsonb :avatar, comment: "用户头像"
      t.string :identity_id, comment: "证件号码，需要时候可以作为唯一标识", index: true
      t.datetime :last_visit_at, comment: "最后访问时间"
      t.datetime :blocked_at, index: true
      t.boolean :is_blocked, index: true
      t.timestamps
    end
    create_table :members do |t|
      t.references :user
      t.references :member_identity
      t.references :app
      t.references :member_request
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.jsonb :payload, comment: "payload payload存储的字段"
      t.jsonb :payload_summary, comment: "payload summary存储的字段"
      t.string :type, comment: "STI类型，可以是集团，或者在某些时候可能是学校这样的类型", index: true
      t.string :code, comment: "用户标识"
      t.datetime :blocked_at, index: true
      t.boolean :is_blocked, index: true
      t.timestamps
    end
    create_table :res_member_actions do |t|
      t.references :app
      t.references :real_user
      t.references :target, polymorphic: true
      t.references :user, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :action_type, comment: "具体action的业务flag", null: false
      t.string :action_option, comment: "前端可以设置的信息"
      t.string :action_flag, comment: "后端使用的逻辑字段，前端不能设置"
      t.timestamps
      t.index [:user_type, :user_id, :action_type], name: "res_member_actions_user_action_type_index"
      t.index [:target_type, :target_id, :action_type], name: "res_member_actions_target_action_type_index"
      t.index [:action_type, :target_type, :target_id, :user_type, :user_id], unique: true, name: "res_member_actions_uk_action_target_user"
    end
    create_table :res_user_actions do |t|
      t.references :app
      t.references :real_user
      t.references :target, polymorphic: true
      t.references :user, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :action_type, comment: "具体action的业务flag", null: false
      t.string :action_option, comment: "前端可以设置的信息"
      t.string :action_flag, comment: "后端使用的逻辑字段，前端不能设置"
      t.timestamps
      t.index [:user_type, :user_id, :action_type], name: "res_user_actions_user_action_type_index"
      t.index [:target_type, :target_id, :action_type], name: "res_user_actions_target_action_type_index"
      t.index [:action_type, :target_type, :target_id, :user_type, :user_id], unique: true, name: "res_user_actions_uk_action_target_user"
    end
    create_table :member_identity_permit_actions do |t|
      t.references :app
      t.references :real_user
      t.references :target, polymorphic: true
      t.references :user, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :action_type, comment: "具体action的业务flag", null: false
      t.string :action_option, comment: "前端可以设置的信息"
      t.string :action_flag, comment: "后端使用的逻辑字段，前端不能设置"
      t.timestamps
      t.index [:user_type, :user_id, :action_type], name: "member_identity_permit_actions_user_action_type_index"
      t.index [:target_type, :target_id, :action_type], name: "member_identity_permit_actions_target_action_type_index"
      t.index [:action_type, :target_type, :target_id, :user_type, :user_id], unique: true, name: "member_identity_permit_actions_uk_action_target_user"
    end
    create_table :duties do |t|
      t.references :duty_group
      t.references :org
      t.references :department
      t.string :name, comment: "职务名称"
      t.string :rank, comment: "职务等级"
      t.integer :position, comment: "排序"
      t.timestamps
    end
    create_table :roles do |t|
      t.references :resource, polymorphic: true
      t.references :mod
      t.string :name, comment: "权限标识", index: true
      t.string :label, comment: "显示名称", index: true
      t.jsonb :permits, default: {}
      t.boolean :permit_enable, comment: "是否开启permit的按钮"
      t.timestamps
    end
    create_table :pundit_permit_actions do |t|
      t.references :app
      t.references :real_user
      t.references :target, polymorphic: true
      t.references :user, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :action_type, comment: "具体action的业务flag", null: false
      t.string :action_option, comment: "前端可以设置的信息"
      t.string :action_flag, comment: "后端使用的逻辑字段，前端不能设置"
      t.timestamps
      t.index [:user_type, :user_id, :action_type], name: "pundit_permit_actions_user_action_type_index"
      t.index [:target_type, :target_id, :action_type], name: "pundit_permit_actions_target_action_type_index"
      t.index [:action_type, :target_type, :target_id, :user_type, :user_id], unique: true, name: "pundit_permit_actions_uk_action_target_user"
    end
    create_table :orgs do |t|
      t.references :app
      t.references :org_identity
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.jsonb :payload, comment: "payload payload存储的字段"
      t.jsonb :payload_summary, comment: "payload summary存储的字段"
      t.integer :parent_id, comment: "closure tree parent_id"
      t.string :code, comment: "组织标识"
      t.string :name, comment: "组织名称"
      t.string :short_name, comment: "组织简称"
      t.string :type, comment: "STI类型，可以是集团，或者在某些时候可能是学校这样的类型"
      t.integer :position, comment: "排序"
      t.timestamps
    end
    create_table :org_hierarchies do |t|
      t.integer :ancestor_id, null: false
      t.integer :descendant_id, null: false, index: {:name=>"org_desc_idx"}
      t.integer :generations, null: false
      t.datetime :created_at, null: true
      t.datetime :updated_at, null: true
      t.index [:ancestor_id, :descendant_id, :generations], unique: true, name: "org_anc_desc_idx"
    end
    create_table :org_identities do |t|
      t.references :app
      t.string :name, comment: "组织类型名称"
      t.string :org_type, comment: "Org的类型，可能会关系到Org的STI"
      t.integer :orgs_count, comment: "关联的Org数量"
      t.jsonb :form, comment: "Member配置的表单", default: {}
      t.timestamps
    end
    create_table :departments do |t|
      t.references :org
      t.references :root_org
      t.references :department_identity
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.integer :parent_id, comment: "closure tree parent_id"
      t.string :code, comment: "组织标识"
      t.string :name, comment: "组织名称"
      t.string :short_name, comment: "组织简称"
      t.string :type, comment: "STI类型，可以是集团，或者在某些时候可能是学校这样的类型"
      t.integer :position, comment: "排序"
      t.timestamps
    end
    create_table :department_hierarchies do |t|
      t.integer :ancestor_id, null: false
      t.integer :descendant_id, null: false, index: {:name=>"department_desc_idx"}
      t.integer :generations, null: false
      t.datetime :created_at, null: true
      t.datetime :updated_at, null: true
      t.index [:ancestor_id, :descendant_id, :generations], unique: true, name: "department_anc_desc_idx"
    end
    create_table :tofu_entries do |t|
      t.references :source, polymorphic: true
      t.jsonb :permits, default: {}
      t.boolean :permit_enable, comment: "是否开启permit的按钮"
      t.string :ancestry, comment: "树形结构", index: true
      t.string :platform, default: "pc"
      t.string :layout, comment: "点击以后前端使用的layout"
      t.string :type, comment: "STI"
      t.string :name, comment: "名称"
      t.string :desc, comment: "描述"
      t.text :icon, comment: "显示的图片或者图标"
      t.text :url, comment: "跳转地址，如果只是menu，可以为空"
      t.string :open_mode, comment: "打开页面的方式"
      t.integer :position, comment: "位置"
      t.integer :depth, comment: "树结构深度"
      t.integer :children_count, comment: "子对象的数据", default: 0
      t.timestamps
    end
    create_table :res_permit_actions do |t|
      t.references :app
      t.references :real_user
      t.references :target, polymorphic: true
      t.references :user, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :action_type, comment: "具体action的业务flag", null: false
      t.string :action_option, comment: "前端可以设置的信息"
      t.string :action_flag, comment: "后端使用的逻辑字段，前端不能设置"
      t.timestamps
      t.index [:user_type, :user_id, :action_type], name: "res_permit_actions_user_action_type_index"
      t.index [:target_type, :target_id, :action_type], name: "res_permit_actions_target_action_type_index"
      t.index [:action_type, :target_type, :target_id, :user_type, :user_id], unique: true, name: "res_permit_actions_uk_action_target_user"
    end
    create_table :permit_controller_actions do |t|
      t.references :app
      t.references :mod
      t.references :tanent
      t.references :user
      t.string :platform, comment: "平台"
      t.string :name, comment: "action名称"
      t.string :klass, comment: "controller"
      t.string :action, comment: "action"
      t.jsonb :whitelist, default: {}
      t.jsonb :blacklist, default: {}
      t.jsonb :payload, default: {}
      t.timestamps
    end
    create_table :data_scopes do |t|
      t.references :app
      t.references :tanent
      t.references :user
      t.string :name, comment: "名称"
      t.jsonb :config, comment: "配置", default: {}
      t.jsonb :payload, default: {}
      t.timestamps
    end
    create_table :mods do |t|
      t.string :name, comment: "模块名称"
      t.string :key, comment: "模块对应查找的key值"
      t.timestamps
    end
    create_table :apps do |t|
      t.string :code, comment: "应用标识"
      t.string :name, comment: "应用的名称"
      t.jsonb :settings, comment: "配置信息", default: {}
      t.timestamps
    end
    create_table :res_book_relations do |t|
      t.references :book
      t.references :source, polymorphic: true
      t.timestamps
    end
    create_table :res_books do |t|
      t.references :app
      t.references :user
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :name, comment: "名称"
      t.text :desc, comment: "说明"
      t.string :relation_type, comment: "通讯录的类型"
      t.timestamps
    end
    create_table :department_identities do |t|
      t.references :app
      t.string :name, comment: "部门类型名称"
      t.string :department_type, comment: "Department的类型，可能会关系到Department的STI"
      t.string :color, comment: "标签颜色"
      t.timestamps
    end
    create_table :duty_groups do |t|
      t.references :app
      t.references :org
      t.string :name, comment: "角色组名称"
      t.integer :position, comment: "排序"
      t.timestamps
    end
    create_table :member_requests do |t|
      t.references :app
      t.references :user
      t.references :member_identity
      t.string :create_instance_state, comment: "状态"
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :name, comment: "请求名称"
      t.jsonb :payload, comment: "相关信息，会存储到member的payload里"
      t.jsonb :member_attributes, comment: "相关信息，会存储到member的attributes里"
      t.jsonb :options
      t.timestamps
    end
    create_table :memberships do |t|
      t.references :app
      t.references :org
      t.references :user
      t.references :member
      t.references :department
      t.references :duty
      t.datetime :effective_at, comment: "生效时间，可以为空"
      t.datetime :invalid_at, comment: "失效时间，可以为空"
      t.jsonb :payload, comment: ""
      t.timestamps
    end
    create_table :org_member_identities do |t|
      t.references :app
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :name, comment: "名称"
      t.string :code, comment: "标识"
      t.string :org_member_type, comment: "OrgMember的类型"
      t.jsonb :settle_in_form, comment: "入驻申请表单", default: {}
      t.jsonb :postpone_form, comment: "延期申请表单", default: {}
      t.jsonb :form, comment: "表单", default: {}
      t.jsonb :config, comment: "配置", default: {}
      t.timestamps
    end
    create_table :org_members do |t|
      t.references :app
      t.references :org
      t.references :org_member_identity
      t.jsonb :payload, comment: "payload payload存储的字段"
      t.jsonb :payload_summary, comment: "payload summary存储的字段"
      t.date :effective_at, comment: "生效时间", index: true
      t.date :invalid_at, comment: "失效时间", index: true
      t.string :type, comment: "STI类型"
      t.timestamps
    end
    create_table :org_requests do |t|
      t.references :app
      t.references :user
      t.references :member_identity
      t.references :member
      t.references :org
      t.references :org_member_identity
      t.references :org_member
      t.references :org_identity
      t.references :tanent
      t.string :create_instance_state, comment: "状态"
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :name, comment: "组织名称"
      t.string :code, comment: "组织标识"
      t.jsonb :org_payload, comment: "相关信息，会存储到org的payload里"
      t.jsonb :member_payload, comment: "相关信息，会存储到member的payload里"
      t.string :type, comment: "STI"
      t.string :state, comment: "状态: draft, approving", default: "draft"
      t.datetime :approval_at, comment: "审批通过时间"
      t.jsonb :options, comment: "其他预留信息"
      t.timestamps
    end
    create_table :res_tags do |t|
      t.references :app
      t.references :org
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :name, comment: "标签名称"
      t.string :color, comment: "标签颜色"
      t.timestamps
    end
    create_table :res_tags_relations do |t|
      t.references :tag
      t.references :user
      t.references :org
      t.references :member
      t.timestamps
    end
    create_table :tanents do |t|
      t.references :app
      t.string :code, comment: "租户标识"
      t.string :name, comment: "租户名称"
      t.jsonb :manages, default: {}
      t.boolean :manage_enable, comment: "是否开启permit的按钮"
      t.jsonb :config, comment: "配置"
      t.timestamps
    end
    create_table :tanent_permit_actions do |t|
      t.references :app
      t.references :real_user
      t.references :target, polymorphic: true
      t.references :user, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :action_type, comment: "具体action的业务flag", null: false
      t.string :action_option, comment: "前端可以设置的信息"
      t.string :action_flag, comment: "后端使用的逻辑字段，前端不能设置"
      t.timestamps
      t.index [:user_type, :user_id, :action_type], name: "tanent_permit_actions_user_action_type_index"
      t.index [:target_type, :target_id, :action_type], name: "tanent_permit_actions_target_action_type_index"
      t.index [:action_type, :target_type, :target_id, :user_type, :user_id], unique: true, name: "tanent_permit_actions_uk_action_target_user"
    end
    create_table :forms_templates do |t|
      t.references :app
      t.string :uuid, comment: "表单的唯一标识，可以替代id给前端使用", index: true
      t.string :name, comment: "表单的名称"
      t.jsonb :form, default: {}
      t.jsonb :form_setting
      t.timestamps
    end
    create_table :org_ownerships do |t|
      t.references :org
      t.references :user
      t.timestamps
    end
    create_table :tanent_resources do |t|
      t.references :app
      t.references :tanent
      t.references :resource, polymorphic: true
      t.jsonb :payload, comment: "存额外信息"
      t.timestamps
    end
    create_table :actions do |t|
      t.references :app
      t.references :real_user
      t.references :target, polymorphic: true
      t.references :user, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :action_type, comment: "具体action的业务flag", null: false
      t.string :action_option, comment: "前端可以设置的信息"
      t.string :action_flag, comment: "后端使用的逻辑字段，前端不能设置"
      t.timestamps
      t.index [:user_type, :user_id, :action_type], name: "actions_user_action_type_index"
      t.index [:target_type, :target_id, :action_type], name: "actions_target_action_type_index"
      t.index [:action_type, :target_type, :target_id, :user_type, :user_id], unique: true, name: "actions_uk_action_target_user"
    end
    create_table :data_view_logs do |t|
      t.references :app
      t.references :user
      t.references :source, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.datetime :view_at, comment: "最新访问时间"
      t.timestamps
    end
    create_table :data_counter_stats do |t|
      t.references :countable, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.date :date, comment: "日期，如果是年、月则存第一天", index: true
      t.integer :hour, comment: "小时", index: true
      t.string :period, default: "hour"
      t.integer :view_count, comment: "浏览量", default: 0
      t.integer :action_count, comment: "使用量", default: 0
      t.integer :user_count, comment: "用户量", default: 0
      t.timestamps
    end
    create_table :data_transfers do |t|
      t.references :app
      t.references :source, polymorphic: true
      t.references :target, polymorphic: true
      t.string :op, comment: "操作"
      t.jsonb :infos, comment: "额外信息"
      t.timestamps
    end
    create_table :com_private_policies do |t|
      t.references :app
      t.string :name, comment: "条款名称"
      t.string :key, comment: "关键字，可能有不同业务模块需要使用的关键字"
      t.jsonb :content, comment: "隐私条款内容"
      t.integer :position, comment: "排序"
      t.timestamps
    end
    create_table :com_version_histories do |t|
      t.references :app
      t.references :creator
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :name, comment: "版本发布名称"
      t.string :version, comment: "版本号"
      t.jsonb :content, comment: "发布说明"
      t.integer :position, comment: "发布顺序"
      t.timestamps
    end
    create_table :api_settings do |t|
      t.references :model_define
      t.references :app
      t.string :klass, comment: "对应的active record class name"
      t.string :action, comment: "对应controller的action"
      t.string :uid, comment: "自动生成的唯一标识", index: true
      t.jsonb :extract_conf
      t.timestamps
    end
    create_table :component_settings do |t|
      t.references :app
      t.string :seq, comment: "编号", index: {:unique=>true}
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :name, comment: "组件配置名称"
      t.string :component_klass, comment: "组件类名称"
      t.string :component_path, comment: "组件类路径"
      t.jsonb :conf, comment: "组件配置的json结构"
      t.timestamps
    end
    create_table :data_forms do |t|
      t.references :app
      t.references :create_user
      t.references :source, polymorphic: true
      t.references :record, polymorphic: true
      t.string :type, comment: "STI属性", index: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :flag, comment: "可用作同一 source 下不同的关联关系的区分"
      t.string :source_flag, comment: "关联source的flag"
      t.string :state, comment: "数据状态"
      t.jsonb :payload, comment: "存储的信息", default: {}
      t.jsonb :summary, comment: "通过form生成的缩略信息"
      t.jsonb :form_conf
      t.jsonb :options, comment: "额外的数据信息"
      t.jsonb :meta, comment: "预留后续的数据存储"
      t.string :form_conf_seq, comment: "表单配置的seq，方便进行检索"
      t.timestamps
    end
    create_table :model_confs do |t|
      t.references :model_define
      t.string :name, comment: "名称"
      t.string :klass, comment: "类名"
      t.jsonb :conf
      t.timestamps
    end
    create_table :model_defines do |t|
      t.string :klass, comment: "对应设置的Model名称"
      t.string :name, comment: "模型设置的中文名"
      t.string :association_chain, comment: "查找的关系列表", array: true
      t.string :klass_singular, comment: "自动生成的模型名称，因为路由上传是以underscore方式来传输，所以在这里需要能进行唯一性查找"
      t.timestamps
    end
    create_table :model_settings do |t|
      t.references :model_define
      t.references :setable, polymorphic: true
      t.references :app
      t.references :forms_template
      t.string :flag, comment: "同一个模型中的不同定义，其中model代表是这个对象的模型", default: "model"
      t.string :flag_name, comment: "flag对应中文名称", default: "模型定义"
      t.jsonb :form
      t.jsonb :form_setting
      t.jsonb :api_config
      t.timestamps
    end
    create_table :page_settings do |t|
      t.references :app
      t.string :seq, comment: "编号", index: {:unique=>true}
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :name, comment: "页面配置名称"
      t.jsonb :conf, comment: "页面配置的json结构"
      t.timestamps
    end
    create_table :com_search_items do |t|
      t.references :app
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :name, comment: "搜索条件 "
      t.integer :position, comment: "位置"
      t.string :group_name, comment: "分组标识"
      t.boolean :enabled, comment: "是否启用"
      t.jsonb :conditions
      t.timestamps
    end
    create_table :com_themes do |t|
      t.references :app
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :name, comment: "主题名称"
      t.jsonb :conf, comment: "主题配置"
      t.timestamps
    end
    create_table :com_record_storages do |t|
      t.references :user
      t.string :key, comment: "缓存区key", index: true
      t.jsonb :storage, comment: "属性暂存区"
      t.timestamps
    end
    create_table :paper_trail_versions do |t|
      t.references :operator, polymorphic: true
      t.string :item_type, null: false
      t.integer :item_id, null: false
      t.string :event, comment: "create, update, destroy", null: false
      t.string :whodunnit, comment: "whodunnit"
      t.jsonb :object, comment: "object attributes"
      t.jsonb :object_changes, comment: "object changes"
      t.jsonb :controller_info, comment: "controller info"
      t.jsonb :model_info, comment: "model info"
      t.timestamps
      t.index [:item_type, :item_id], name: "index_versions_on_item_id_item_type"
    end
    create_table :users_roles do |t|
      t.references :user
      t.references :role
      t.timestamps
    end
    create_table :version_relationships do |t|
      t.references :app
      t.references :resource, polymorphic: true
      t.references :real_resource, polymorphic: true
      t.references :version, polymorphic: true
      t.references :operator, polymorphic: true
      t.timestamps
    end
    create_table :serve_activity_actions do |t|
      t.references :app
      t.references :real_user
      t.references :target, polymorphic: true
      t.references :user, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :action_type, comment: "具体action的业务flag", null: false
      t.string :action_option, comment: "前端可以设置的信息"
      t.string :action_flag, comment: "后端使用的逻辑字段，前端不能设置"
      t.timestamps
      t.index [:user_type, :user_id, :action_type], name: "serve_activity_actions_user_action_type_index"
      t.index [:target_type, :target_id, :action_type], name: "serve_activity_actions_target_action_type_index"
      t.index [:action_type, :target_type, :target_id, :user_type, :user_id], unique: true, name: "serve_activity_actions_uk_action_target_user"
    end
    create_table :serve_manage_actions do |t|
      t.references :app
      t.references :real_user
      t.references :target, polymorphic: true
      t.references :user, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :action_type, comment: "具体action的业务flag", null: false
      t.string :action_option, comment: "前端可以设置的信息"
      t.string :action_flag, comment: "后端使用的逻辑字段，前端不能设置"
      t.timestamps
      t.index [:user_type, :user_id, :action_type], name: "serve_manage_actions_user_action_type_index"
      t.index [:target_type, :target_id, :action_type], name: "serve_manage_actions_target_action_type_index"
      t.index [:action_type, :target_type, :target_id, :user_type, :user_id], unique: true, name: "serve_manage_actions_uk_action_target_user"
    end
    create_table :serve_permit_actions do |t|
      t.references :app
      t.references :real_user
      t.references :target, polymorphic: true
      t.references :user, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :action_type, comment: "具体action的业务flag", null: false
      t.string :action_option, comment: "前端可以设置的信息"
      t.string :action_flag, comment: "后端使用的逻辑字段，前端不能设置"
      t.timestamps
      t.index [:user_type, :user_id, :action_type], name: "serve_permit_actions_user_action_type_index"
      t.index [:target_type, :target_id, :action_type], name: "serve_permit_actions_target_action_type_index"
      t.index [:action_type, :target_type, :target_id, :user_type, :user_id], unique: true, name: "serve_permit_actions_uk_action_target_user"
    end
    create_table :active_storage_blobs do |t|
      t.jsonb :metadata, comment: "额外信息"
      t.string :app_code, comment: "app标识"
      t.string :key, comment: "key"
      t.string :filename, comment: "文件名称"
      t.string :content_type, comment: "文件类型"
      t.string :service_name, comment: "服务名称"
      t.integer :byte_size, comment: "文件大小"
      t.string :checksum, comment: "校验位"
      t.timestamps
      t.index [:key], unique: true
    end
  end
end
