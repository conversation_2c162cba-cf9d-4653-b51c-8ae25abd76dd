class RailsComMigration1742524257 < ActiveRecord::Migration[7.1]

  def change
    create_table :serve_message_templates do |t|
      t.references :app
      t.references :rule
      t.references :rule_item
      t.string :type, comment: "STI属性"
      t.string :name, comment: "名称"
      t.string :state, comment: "状态"
      t.jsonb :payload, comment: "其他数据"
      t.jsonb :option, comment: "配置"
      t.timestamps
    end
    add_column :serve_messages, :payload, :jsonb, comment: "其他内容"
  end
end
