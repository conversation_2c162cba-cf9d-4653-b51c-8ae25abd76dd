class RailsComMigration1735105936 < ActiveRecord::Migration[7.1]

  def change
    create_table :bot_artifacts do |t|
      t.references :conversation
      t.references :source, polymorphic: true
      t.string :tool_canme, comment: "根据模型名称"
      t.string :tool_function, comment: "根据function名称"
      t.jsonb :function_params, comment: "函数调用信息，转换前"
      t.jsonb :meta, comment: "参数内容，自定义的方式"
      t.jsonb :info, comment: "message返回的信息"
      t.timestamps
    end
  end
end
