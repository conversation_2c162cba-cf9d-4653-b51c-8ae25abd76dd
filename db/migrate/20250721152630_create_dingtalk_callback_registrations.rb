class CreateDingtalkCallbackRegistrations < ActiveRecord::Migration[7.1]
  def change
    create_table :dingtalk_callback_registrations do |t|
      t.references :client, null: false, foreign_key: { to_table: :dingtalk_clients }
      t.string :event_tag, null: false, comment: '事件标识'
      t.string :callback_url, null: false, comment: '回调地址'
      t.string :status, default: 'pending', comment: '状态'
      t.datetime :registered_at, comment: '注册时间'
      t.timestamps
    end

    add_index :dingtalk_callback_registrations, [:client_id, :event_tag],
              unique: true, name: 'idx_callback_client_event'
  end
end
