class RailsComMigration1735020864 < ActiveRecord::Migration[7.1]

  def change
    create_table :serve_duty_changes do |t|
      t.references :app
      t.references :user
      t.references :department
      t.references :original_department
      t.string :create_instance_state, comment: "创建工作流的状态"
      t.datetime :create_instance_timestamp, comment: "创建工作流的操作时间"
      t.string :duty, comment: "当前的岗位"
      t.string :duty_level, comment: "当前职级"
      t.string :original_duty, comment: "之前的岗位"
      t.string :original_duty_level, comment: "之前职级"
      t.datetime :change_date, comment: "变更时间"
      t.string :reason, comment: "原因"
      t.timestamps
    end
  end
end
