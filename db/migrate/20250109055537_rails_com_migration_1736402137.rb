class RailsComMigration1736402137 < ActiveRecord::Migration[7.1]

  def change
    create_table :bot_reviewers do |t|
      t.references :app
      t.string :name, comment: "名称"
      t.text :description, comment: "描述"
      t.text :instructions, comment: "介绍"
      t.timestamps
    end
    create_table :bot_review_rules do |t|
      t.references :reviewer
      t.string :name, comment: "名称"
      t.text :content, comment: "内容"
      t.text :scoring_criteria, comment: "评分标准"
      t.timestamps
    end
    create_table :bot_review_results do |t|
      t.references :review_document
      t.references :review_rule
      t.string :name, comment: "结果名称"
      t.text :raw, comment: "原文"
      t.integer :score, comment: "分数"
      t.text :reason, comment: "原因"
      t.text :suggest, comment: "建议"
      t.string :level
      t.jsonb :meta, comment: "额外信息"
      t.timestamps
    end
    create_table :bot_review_documents do |t|
      t.references :app
      t.references :user
      t.references :reviewer
      t.timestamps
    end
  end
end
