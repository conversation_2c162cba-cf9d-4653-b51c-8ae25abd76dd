class RailsComMigration1708348391 < ActiveRecord::Migration[6.1]

  def change
    create_table :serve_activities do |t|
      t.references :app
      t.references :submodule
      t.references :source, polymorphic: true
      t.string :name, comment: "名称"
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :type, comment: "STI属性", index: true
      t.datetime :effective_at, comment: "生效时间", index: true
      t.datetime :invalid_at, comment: "失效时间", index: true
      t.jsonb :views, default: {}
      t.boolean :view_enable, comment: "是否开启permit的按钮"
      t.jsonb :uses, default: {}
      t.boolean :use_enable, comment: "是否开启permit的按钮"
      t.string :state
      t.jsonb :cover_image, comment: "封面图"
      t.integer :position, comment: "排序"
      t.jsonb :content, comment: "详情，body / images / video"
      t.text :address, comment: "地址"
      t.jsonb :layout, comment: "卡片样式"
      t.timestamps
    end
    create_table :serve_catalogs do |t|
      t.references :app
      t.references :submodule
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.jsonb :views, default: {}
      t.boolean :view_enable, comment: "是否开启permit的按钮"
      t.jsonb :uses, default: {}
      t.boolean :use_enable, comment: "是否开启permit的按钮"
      t.string :name, comment: "名称"
      t.integer :position, comment: "排序"
      t.jsonb :cover_image, comment: "封面图"
      t.integer :icon, comment: "排序"
      t.jsonb :layout, comment: "布局方式配置"
      t.timestamps
    end
    create_table :serve_tags do |t|
      t.references :app
      t.references :submodule
      t.string :name, comment: "名称"
      t.string :color, comment: "颜色"
      t.timestamps
    end
    create_table :serve_banners do |t|
      t.references :app
      t.references :source, polymorphic: true
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.datetime :effective_at, comment: "生效时间", index: true
      t.datetime :invalid_at, comment: "失效时间", index: true
      t.datetime :published_at, index: true
      t.boolean :is_published, index: true
      t.string :name, comment: "轮播图名称"
      t.integer :position, comment: "位置"
      t.jsonb :cover_image, comment: "封面图"
      t.string :conf, comment: "呈现方式，根据前端需要设置，例如可以是PC或者Mobile的展现等"
      t.timestamps
    end
    create_table :serve_entries do |t|
      t.references :app
      t.references :submodule
      t.references :activity
      t.references :source, polymorphic: true
      t.references :user
      t.string :name, comment: "名称"
      t.string :type, comment: "STI属性", index: true
      t.timestamps
    end
    create_table :serve_submodules do |t|
      t.references :app
      t.string :model_flag, comment: "model flag，对应model_setting的flag"
      t.jsonb :model_payload, comment: "model payload存储的字段"
      t.jsonb :model_payload_summary, comment: "model summary存储的字段"
      t.jsonb :model_detail, comment: "model 存储的详情字段"
      t.string :ancestry, comment: "树形结构", index: true
      t.integer :depth, comment: "树结构深度"
      t.integer :children_count, comment: "子对象的数据", default: 0
      t.jsonb :manages, default: {}
      t.boolean :manage_enable, comment: "是否开启permit的按钮"
      t.string :name, comment: "名称"
      t.integer :position, comment: "排序"
      t.jsonb :cover_image, comment: "封面图"
      t.integer :icon, comment: "排序"
      t.string :key, comment: "程序内使用的标识"
      t.jsonb :layout, comment: "布局方式配置"
      t.timestamps
    end
  end
end
