class RailsComMigration1736503635 < ActiveRecord::Migration[7.1]

  def change
    add_column :bot_report_templates, :model_flag, :string, comment: "model flag，对应model_setting的flag"
    add_column :bot_report_templates, :model_payload, :jsonb, comment: "model payload存储的字段"
    add_column :bot_report_templates, :model_payload_summary, :jsonb, comment: "model summary存储的字段"
    add_column :bot_report_templates, :model_detail, :jsonb, comment: "model 存储的详情字段"
    add_column :bot_report_templates, :icon, :jsonb, comment: "图标"
  end
end
