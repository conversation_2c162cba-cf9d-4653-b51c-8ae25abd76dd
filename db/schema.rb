# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[7.1].define(version: 2025_07_29_161575) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "plpgsql"
  enable_extension "vector"

  create_table "actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id", "action_flag"], name: "actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_actions_on_app_id"
    t.index ["real_user_id"], name: "index_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_actions_on_user"
  end

  create_table "active_storage_attachments", force: :cascade do |t|
    t.string "record_type"
    t.bigint "record_id"
    t.bigint "blob_id"
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["blob_id"], name: "index_active_storage_attachments_on_blob_id"
    t.index ["record_type", "record_id", "name", "blob_id"], name: "idx_on_record_type_record_id_name_blob_id_0be5805727", unique: true
    t.index ["record_type", "record_id"], name: "index_active_storage_attachments_on_record"
  end

  create_table "active_storage_blobs", force: :cascade do |t|
    t.jsonb "metadata", comment: "额外信息"
    t.string "app_code", comment: "app标识"
    t.string "key", comment: "key"
    t.string "filename", comment: "文件名称"
    t.string "content_type", comment: "文件类型"
    t.string "service_name", comment: "服务名称"
    t.integer "byte_size", comment: "文件大小"
    t.string "checksum", comment: "校验位"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["key"], name: "index_active_storage_blobs_on_key", unique: true
  end

  create_table "api_settings", force: :cascade do |t|
    t.bigint "model_define_id"
    t.bigint "app_id"
    t.string "klass", comment: "对应的active record class name"
    t.string "action", comment: "对应controller的action"
    t.string "uid", comment: "自动生成的唯一标识"
    t.jsonb "extract_conf", comment: "数据抽取配置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_api_settings_on_app_id"
    t.index ["model_define_id"], name: "index_api_settings_on_model_define_id"
  end

  create_table "apps", force: :cascade do |t|
    t.string "code", comment: "应用标识"
    t.string "name", comment: "应用的名称"
    t.jsonb "settings", comment: "配置信息"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "assessment_activities", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "creator_id"
    t.bigint "score_template_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.date "start_on", comment: "生效时间"
    t.date "end_on", comment: "失效时间"
    t.string "name", comment: "考核活动名称"
    t.text "content", comment: "考核活动内容"
    t.string "state", comment: "状态"
    t.jsonb "attachments", comment: "附件"
    t.boolean "catalog_enabled", comment: "是否启用分类"
    t.boolean "group_enabled", comment: "是否启用分组"
    t.jsonb "entry_form", comment: "entry自评填写的内容"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_assessment_activities_on_app_id"
    t.index ["creator_id"], name: "index_assessment_activities_on_creator_id"
    t.index ["score_template_id"], name: "index_assessment_activities_on_score_template_id"
  end

  create_table "assessment_answer_sheets", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "question_sheet_id"
    t.bigint "user_id"
    t.float "score", comment: "考核得分"
    t.float "weight", comment: "得分权重"
    t.jsonb "item_payload", comment: "评分列表"
    t.jsonb "catalog_payload", comment: "catalog统计的权重列表"
    t.jsonb "template_conf", comment: "评分选项"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.jsonb "payload", comment: "存储最后的结果"
    t.jsonb "form", comment: "存储 question sheet 表单的 dup"
    t.string "state"
    t.datetime "done_at", comment: "提交时间"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_assessment_answer_sheets_on_app_id"
    t.index ["question_sheet_id"], name: "index_assessment_answer_sheets_on_question_sheet_id"
    t.index ["user_id"], name: "index_assessment_answer_sheets_on_user_id"
  end

  create_table "assessment_catalogs", force: :cascade do |t|
    t.bigint "activity_id"
    t.bigint "score_template_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "分类名称"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["activity_id"], name: "index_assessment_catalogs_on_activity_id"
    t.index ["score_template_id"], name: "index_assessment_catalogs_on_score_template_id"
  end

  create_table "assessment_dimensions", force: :cascade do |t|
    t.bigint "activity_id"
    t.bigint "catalog_id"
    t.bigint "group_id"
    t.bigint "score_template_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "type", comment: "STI类型"
    t.string "name", comment: "维度名称"
    t.float "weight", comment: "权重"
    t.jsonb "options", comment: "不同的类型配置"
    t.integer "position", comment: "维度排序"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["activity_id"], name: "index_assessment_dimensions_on_activity_id"
    t.index ["catalog_id"], name: "index_assessment_dimensions_on_catalog_id"
    t.index ["group_id"], name: "index_assessment_dimensions_on_group_id"
    t.index ["score_template_id"], name: "index_assessment_dimensions_on_score_template_id"
  end

  create_table "assessment_entries", force: :cascade do |t|
    t.string "user_type"
    t.bigint "user_id"
    t.bigint "activity_id"
    t.bigint "catalog_id"
    t.bigint "group_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.float "score", comment: "考核得分"
    t.integer "position", comment: "打分排序"
    t.string "user_name", comment: "考核对象显示名"
    t.string "state"
    t.jsonb "payload", comment: "自评填写表单，对应activity的entry_form"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["activity_id"], name: "index_assessment_entries_on_activity_id"
    t.index ["catalog_id"], name: "index_assessment_entries_on_catalog_id"
    t.index ["group_id"], name: "index_assessment_entries_on_group_id"
    t.index ["user_type", "user_id"], name: "index_assessment_entries_on_user"
  end

  create_table "assessment_evaluations", force: :cascade do |t|
    t.string "evaluate_source_type"
    t.bigint "evaluate_source_id"
    t.bigint "score_template_id"
    t.string "user_type"
    t.bigint "user_id"
    t.bigint "app_id"
    t.float "score", comment: "考核得分"
    t.float "weight", comment: "得分权重"
    t.jsonb "item_payload", comment: "评分列表"
    t.jsonb "catalog_payload", comment: "catalog统计的权重列表"
    t.jsonb "template_conf", comment: "评分选项"
    t.string "name", comment: "评价名称"
    t.string "state"
    t.jsonb "payload", comment: "对应score_template的score_form"
    t.string "bpm_token_state", comment: "同步bpm token的状态"
    t.string "user_name", comment: "自定义评审人名"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "seq", comment: "编号"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_assessment_evaluations_on_app_id"
    t.index ["evaluate_source_type", "evaluate_source_id"], name: "index_assessment_evaluations_on_evaluate_source"
    t.index ["score_template_id"], name: "index_assessment_evaluations_on_score_template_id"
    t.index ["user_type", "user_id"], name: "index_assessment_evaluations_on_user"
  end

  create_table "assessment_groups", force: :cascade do |t|
    t.bigint "activity_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "分组名称"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["activity_id"], name: "index_assessment_groups_on_activity_id"
  end

  create_table "assessment_question_banks", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "creator_id"
    t.string "user_type"
    t.bigint "user_id"
    t.bigint "tanent_id"
    t.string "name", comment: "题库名称"
    t.text "desc", comment: "题库简介"
    t.integer "questions_count", comment: "题目数量"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_assessment_question_banks_on_app_id"
    t.index ["creator_id"], name: "index_assessment_question_banks_on_creator_id"
    t.index ["tanent_id"], name: "index_assessment_question_banks_on_tanent_id"
    t.index ["user_type", "user_id"], name: "index_assessment_question_banks_on_user"
  end

  create_table "assessment_question_papers", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "creator_id"
    t.string "user_type"
    t.bigint "user_id"
    t.bigint "tanent_id"
    t.string "name", comment: "试卷名称"
    t.text "desc", comment: "试卷简介"
    t.string "arrange_mode"
    t.jsonb "config", comment: "配置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_assessment_question_papers_on_app_id"
    t.index ["creator_id"], name: "index_assessment_question_papers_on_creator_id"
    t.index ["tanent_id"], name: "index_assessment_question_papers_on_tanent_id"
    t.index ["user_type", "user_id"], name: "index_assessment_question_papers_on_user"
  end

  create_table "assessment_question_settings", force: :cascade do |t|
    t.bigint "app_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "seq", comment: "编号"
    t.string "name", comment: "题目类型名称"
    t.string "flag", comment: "题目类型flag"
    t.jsonb "form", comment: "TaTemplateFormItem 无标题的表单"
    t.jsonb "config_form", comment: "TaTemplateFormItem  配置项的表单"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_assessment_question_settings_on_app_id"
  end

  create_table "assessment_question_sheets", force: :cascade do |t|
    t.bigint "app_id"
    t.string "source_type"
    t.bigint "source_id"
    t.bigint "template_id"
    t.bigint "create_user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.datetime "effective_at", comment: "生效时间"
    t.datetime "invalid_at", comment: "失效时间"
    t.string "seq", comment: "编号"
    t.string "name", comment: "问卷名称"
    t.jsonb "cover_image", comment: "封面图"
    t.text "desc", comment: "描述"
    t.string "type", comment: "STI"
    t.string "mode"
    t.jsonb "form", comment: "问卷具体表单"
    t.boolean "is_encrypt", comment: "是否加密"
    t.string "passwd", comment: "加密口令"
    t.integer "max_count", comment: "最大单人可提交次数，匿名前端控制，如果实名后端控制"
    t.jsonb "hint_options", comment: "额外配置"
    t.string "category", comment: "类别"
    t.string "exam_mode"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_assessment_question_sheets_on_app_id"
    t.index ["create_user_id"], name: "index_assessment_question_sheets_on_create_user_id"
    t.index ["source_type", "source_id"], name: "index_assessment_question_sheets_on_source"
    t.index ["template_id"], name: "index_assessment_question_sheets_on_template_id"
  end

  create_table "assessment_questions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "question_bank_id"
    t.bigint "creator_id"
    t.bigint "parent_id"
    t.bigint "tanent_id"
    t.text "name", comment: "题干"
    t.string "kind", comment: "题目类型"
    t.jsonb "config", comment: "配置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_assessment_questions_on_app_id"
    t.index ["creator_id"], name: "index_assessment_questions_on_creator_id"
    t.index ["parent_id"], name: "index_assessment_questions_on_parent_id"
    t.index ["question_bank_id"], name: "index_assessment_questions_on_question_bank_id"
    t.index ["tanent_id"], name: "index_assessment_questions_on_tanent_id"
  end

  create_table "assessment_score_templates", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "creator_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "评分表名称"
    t.text "desc", comment: "评分表描述"
    t.jsonb "form", comment: "表单配置"
    t.jsonb "options", comment: "其他配置"
    t.jsonb "score_form", comment: "score的相关配置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_assessment_score_templates_on_app_id"
    t.index ["creator_id"], name: "index_assessment_score_templates_on_creator_id"
  end

  create_table "assessment_scores", force: :cascade do |t|
    t.string "evaluate_source_type"
    t.bigint "evaluate_source_id"
    t.bigint "score_template_id"
    t.string "user_type"
    t.bigint "user_id"
    t.bigint "activity_id"
    t.bigint "entry_id"
    t.bigint "dimension_id"
    t.float "score", comment: "考核得分"
    t.float "weight", comment: "得分权重"
    t.jsonb "item_payload", comment: "评分列表"
    t.jsonb "catalog_payload", comment: "catalog统计的权重列表"
    t.jsonb "template_conf", comment: "评分选项"
    t.string "name", comment: "评价名称"
    t.string "state"
    t.jsonb "payload", comment: "对应score_template的score_form"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["activity_id"], name: "index_assessment_scores_on_activity_id"
    t.index ["dimension_id"], name: "index_assessment_scores_on_dimension_id"
    t.index ["entry_id"], name: "index_assessment_scores_on_entry_id"
    t.index ["evaluate_source_type", "evaluate_source_id"], name: "index_assessment_scores_on_evaluate_source"
    t.index ["score_template_id"], name: "index_assessment_scores_on_score_template_id"
    t.index ["user_type", "user_id"], name: "index_assessment_scores_on_user"
  end

  create_table "assessment_stages", force: :cascade do |t|
    t.bigint "activity_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.datetime "effective_at", comment: "生效时间"
    t.datetime "invalid_at", comment: "失效时间"
    t.string "name", comment: "阶段名称"
    t.string "stage_type"
    t.integer "position", comment: "排序"
    t.boolean "enable", comment: "是否启用"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["activity_id"], name: "index_assessment_stages_on_activity_id"
  end

  create_table "async_tasks", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "user_id"
    t.string "taskable_type"
    t.bigint "taskable_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "seq", comment: "编号"
    t.string "type", comment: "STI属性"
    t.string "flag", comment: "程序使用参数，唯一标识，前端配合使用"
    t.string "name", comment: "任务名称"
    t.integer "progress", comment: "进度(取整数)"
    t.string "state"
    t.string "perform_args", comment: "执行参数"
    t.jsonb "options", comment: "启动执行参数"
    t.jsonb "payload", comment: "处理信息"
    t.jsonb "result", comment: "异步处理的结果信息"
    t.jsonb "meta", comment: "额外信息"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_async_tasks_on_app_id"
    t.index ["taskable_type", "taskable_id"], name: "index_async_tasks_on_taskable"
    t.index ["user_id"], name: "index_async_tasks_on_user_id"
  end

  create_table "audit_logs", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "user_id"
    t.string "auditable_type"
    t.bigint "auditable_id"
    t.string "controller_path", comment: "controller 完整路径"
    t.string "controller_name", comment: "controller名"
    t.string "action_name", comment: "action名"
    t.string "request_id", comment: "请求id"
    t.string "request_method", comment: "请求方式"
    t.string "ip_address", comment: "访问IP地址"
    t.text "url", comment: "request url"
    t.text "referrer", comment: "request referrer"
    t.string "user_agent", comment: "访问终端"
    t.string "platform", comment: "访问终端"
    t.jsonb "params", comment: "访问参数"
    t.text "message", comment: "额外信息"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_audit_logs_on_app_id"
    t.index ["auditable_type", "auditable_id"], name: "index_audit_logs_on_auditable"
    t.index ["user_id"], name: "index_audit_logs_on_user_id"
  end

  create_table "bot_agents", force: :cascade do |t|
    t.bigint "app_id"
    t.string "type", comment: "STI属性"
    t.string "name", comment: "工具名称"
    t.text "description", comment: "工具描述"
    t.text "instructions", comment: "工具介绍"
    t.string "llm_model_key", comment: "LlmFactory默认的大模型"
    t.integer "max_history_messages", comment: "最大历史消息数"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_bot_agents_on_app_id"
  end

  create_table "bot_artifacts", force: :cascade do |t|
    t.bigint "conversation_id"
    t.string "source_type"
    t.bigint "source_id"
    t.string "intent_name", comment: "根据名称"
    t.string "tool_cname", comment: "根据模型名称"
    t.string "tool_function", comment: "根据function名称"
    t.jsonb "tool_conf", comment: "参数配置"
    t.jsonb "function_params", comment: "函数调用信息，转换前"
    t.jsonb "meta", comment: "参数内容，自定义的方式"
    t.jsonb "info", comment: "message返回的信息"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "type", comment: "STI属性"
    t.index ["conversation_id"], name: "index_bot_artifacts_on_conversation_id"
    t.index ["source_type", "source_id"], name: "index_bot_artifacts_on_source"
  end

  create_table "bot_conversations", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "agent_id"
    t.bigint "user_id"
    t.string "name", comment: "名称"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["agent_id"], name: "index_bot_conversations_on_agent_id"
    t.index ["app_id"], name: "index_bot_conversations_on_app_id"
    t.index ["user_id"], name: "index_bot_conversations_on_user_id"
  end

  create_table "bot_intent_relations", force: :cascade do |t|
    t.bigint "agent_id"
    t.bigint "intent_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["agent_id"], name: "index_bot_intent_relations_on_agent_id"
    t.index ["intent_id"], name: "index_bot_intent_relations_on_intent_id"
  end

  create_table "bot_intents", force: :cascade do |t|
    t.bigint "app_id"
    t.string "type", comment: "STI属性"
    t.string "name", comment: "工具名称"
    t.text "description", comment: "工具描述"
    t.string "tool_cname", comment: "工具类的名称"
    t.jsonb "tool_conf", comment: "工具配置"
    t.string "llm_model_key", comment: "LlmFactory默认的大模型"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_bot_intents_on_app_id"
  end

  create_table "bot_meetings", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "user_id"
    t.jsonb "permits", comment: "权限设置"
    t.boolean "permit_enable", comment: "是否开启permit的按钮"
    t.string "name", comment: "会议名称"
    t.string "state"
    t.string "meeting_time", comment: "会议时间"
    t.text "background", comment: "会议背景"
    t.string "topic", comment: "会议主题"
    t.text "summary", comment: "会议纪要"
    t.string "participants", comment: "与会人员"
    t.jsonb "audio", comment: "会议录音"
    t.jsonb "payload", comment: "额外字段"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "seq", comment: "编号"
    t.jsonb "file", comment: "文件或内容"
    t.index ["app_id"], name: "index_bot_meetings_on_app_id"
    t.index ["user_id"], name: "index_bot_meetings_on_user_id"
  end

  create_table "bot_messages", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "conversation_id"
    t.string "role", comment: "发送对象"
    t.jsonb "meta", comment: "消息发送的内容"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_bot_messages_on_app_id"
    t.index ["conversation_id"], name: "index_bot_messages_on_conversation_id"
  end

  create_table "bot_paperwork_results", force: :cascade do |t|
    t.bigint "paperwork_id"
    t.string "name", comment: "结果名称"
    t.string "raw", comment: "原文"
    t.jsonb "payload", comment: "结果"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["paperwork_id"], name: "index_bot_paperwork_results_on_paperwork_id"
  end

  create_table "bot_paperworks", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "user_id"
    t.string "name", comment: "名称"
    t.datetime "operate_at", comment: "操作时间"
    t.string "state"
    t.jsonb "attachment", comment: "附件结构，单一文件"
    t.jsonb "response", comment: "响应"
    t.text "prompt_text", comment: "提示词，由外部传入"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_bot_paperworks_on_app_id"
    t.index ["user_id"], name: "index_bot_paperworks_on_user_id"
  end

  create_table "bot_report_templates", force: :cascade do |t|
    t.bigint "app_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "名称"
    t.string "title", comment: "标题"
    t.text "content", comment: "内容"
    t.text "instructions", comment: "介绍"
    t.text "prompt", comment: "提示词"
    t.jsonb "conf", comment: "变量配置"
    t.jsonb "icon", comment: "图标"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.text "review_instructions", comment: "审查介绍"
    t.index ["app_id"], name: "index_bot_report_templates_on_app_id"
  end

  create_table "bot_reports", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "user_id"
    t.bigint "report_template_id"
    t.string "name", comment: "名称"
    t.string "title", comment: "标题"
    t.text "content", comment: "内容"
    t.jsonb "variables", comment: "变量"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "review_state"
    t.index ["app_id"], name: "index_bot_reports_on_app_id"
    t.index ["report_template_id"], name: "index_bot_reports_on_report_template_id"
    t.index ["user_id"], name: "index_bot_reports_on_user_id"
  end

  create_table "bot_review_documents", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "user_id"
    t.bigint "reviewer_id"
    t.jsonb "file", comment: "文件或内容"
    t.string "name", comment: "名称"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "review_state"
    t.index ["app_id"], name: "index_bot_review_documents_on_app_id"
    t.index ["reviewer_id"], name: "index_bot_review_documents_on_reviewer_id"
    t.index ["user_id"], name: "index_bot_review_documents_on_user_id"
  end

  create_table "bot_review_results", force: :cascade do |t|
    t.bigint "review_rule_id"
    t.string "name", comment: "结果名称"
    t.text "raw", comment: "原文"
    t.integer "score", comment: "分数"
    t.text "reason", comment: "原因"
    t.text "suggest", comment: "建议"
    t.string "level"
    t.jsonb "meta", comment: "额外信息"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "document_source_type"
    t.bigint "document_source_id"
    t.index ["document_source_type", "document_source_id"], name: "index_bot_review_results_on_document_source"
    t.index ["review_rule_id"], name: "index_bot_review_results_on_review_rule_id"
  end

  create_table "bot_review_rules", force: :cascade do |t|
    t.string "name", comment: "名称"
    t.text "content", comment: "内容"
    t.text "scoring_criteria", comment: "评分标准"
    t.boolean "active", comment: "是否启用"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "review_source_type"
    t.bigint "review_source_id"
    t.index ["review_source_type", "review_source_id"], name: "index_bot_review_rules_on_review_source"
  end

  create_table "bot_reviewers", force: :cascade do |t|
    t.bigint "app_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "名称"
    t.text "description", comment: "描述"
    t.jsonb "icon", comment: "图标"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.text "review_instructions", comment: "审查介绍"
    t.index ["app_id"], name: "index_bot_reviewers_on_app_id"
  end

  create_table "bpm_catalogs", force: :cascade do |t|
    t.bigint "app_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "名称"
    t.jsonb "icon", comment: "图标"
    t.integer "position", comment: "排序"
    t.boolean "published", comment: "是否发布"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_bpm_catalogs_on_app_id"
  end

  create_table "bpm_instance_relations", force: :cascade do |t|
    t.bigint "instance_id"
    t.string "source_type"
    t.bigint "source_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "seq", comment: "随机数"
    t.string "model_setting_flag", comment: "对应模型的flag"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["instance_id"], name: "index_bpm_instance_relations_on_instance_id"
    t.index ["source_type", "source_id"], name: "index_bpm_instance_relations_on_source"
  end

  create_table "bpm_instances", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "workflow_id"
    t.bigint "creator_id"
    t.string "flowable_type"
    t.bigint "flowable_id"
    t.integer "comments_count", comment: "评论数量"
    t.string "comment_conf"
    t.string "seq", comment: "编号"
    t.string "type", comment: "STI"
    t.jsonb "payload", comment: "流程表单"
    t.jsonb "storage", comment: "instance的数据存储，主要是有配置map_key 的 value，另外保存了token中配置的内容"
    t.jsonb "summary", comment: "instance在列表页显示的内容"
    t.string "state", comment: "流程状态"
    t.string "flowable_flag", comment: "flowable不同流程的flag"
    t.integer "spent_time_in_second", comment: "耗时时长"
    t.jsonb "cache_payload", comment: "额外存储的结构，根据场合可以作为payload的存储"
    t.datetime "action_at", comment: "激活时间"
    t.jsonb "last_token_attr", comment: "最新token信息"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "level", comment: "流程级别"
    t.index ["app_id"], name: "index_bpm_instances_on_app_id"
    t.index ["creator_id"], name: "index_bpm_instances_on_creator_id"
    t.index ["flowable_type", "flowable_id"], name: "index_bpm_instances_on_flowable"
    t.index ["workflow_id"], name: "index_bpm_instances_on_workflow_id"
  end

  create_table "bpm_place_relations", force: :cascade do |t|
    t.bigint "workflow_id"
    t.bigint "source_id"
    t.bigint "target_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["source_id"], name: "index_bpm_place_relations_on_source_id"
    t.index ["target_id"], name: "index_bpm_place_relations_on_target_id"
    t.index ["workflow_id"], name: "index_bpm_place_relations_on_workflow_id"
  end

  create_table "bpm_places", force: :cascade do |t|
    t.bigint "workflow_id"
    t.string "type", comment: "STI"
    t.string "seq", comment: "place的唯一序列号，保持一致"
    t.string "name", comment: "节点名称"
    t.string "desc", comment: "节点描述"
    t.integer "position", comment: "根据 tree 边生成的 position"
    t.boolean "is_summary", comment: "是否快捷引用"
    t.jsonb "fields", comment: "workflow form字段在这个place的权限，读写/可见"
    t.jsonb "place_form", comment: "动态表单的json字段"
    t.jsonb "options", comment: "节点的配置信息"
    t.jsonb "timer_options", comment: "节点时间配置"
    t.jsonb "trigger_options", comment: "token进出节点时候可能需要额外操作的内容"
    t.jsonb "token_actions", comment: "操作菜单配置"
    t.jsonb "layout_options", comment: "前端页面使用的配置"
    t.jsonb "activate_options", comment: "同步回调配置"
    t.jsonb "token_source_options", comment: "token place相关配置"
    t.jsonb "form_setting", comment: "表单配置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["workflow_id"], name: "index_bpm_places_on_workflow_id"
  end

  create_table "bpm_rules", force: :cascade do |t|
    t.bigint "workflow_id"
    t.string "name", comment: "规则名称"
    t.integer "time_in_second", comment: "设定时间范围"
    t.string "type", comment: "STI"
    t.jsonb "options", comment: "具体配置内容"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["workflow_id"], name: "index_bpm_rules_on_workflow_id"
  end

  create_table "bpm_stars", force: :cascade do |t|
    t.bigint "user_id"
    t.bigint "workflow_id"
    t.integer "position", comment: "排序"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_bpm_stars_on_user_id"
    t.index ["workflow_id"], name: "index_bpm_stars_on_workflow_id"
  end

  create_table "bpm_tokens", force: :cascade do |t|
    t.bigint "place_id"
    t.bigint "previous_token_id"
    t.bigint "operator_id"
    t.bigint "app_id"
    t.bigint "workflow_id"
    t.bigint "instance_id"
    t.string "activate_source_type"
    t.bigint "activate_source_id"
    t.string "token_source_type"
    t.bigint "token_source_id"
    t.string "type", comment: "STI"
    t.string "place_type", comment: "Place的类型"
    t.string "transition_type", comment: "Transition的类型"
    t.string "name", comment: "Token的名称，默认取自Place"
    t.string "state", comment: "Token状态"
    t.text "comment", comment: "审批备注"
    t.jsonb "options", comment: "Token的额外信息JSON"
    t.jsonb "token_payload", comment: "对应place的place_payload，存储审批时候存储在tokne中的信息"
    t.integer "spent_time_in_second", comment: "耗时时长"
    t.jsonb "operate_logs", comment: "相关的操作日志"
    t.string "action_key", comment: "保存上一个action的操作"
    t.string "action_flag", comment: "保存action的操作flag，action key有可能是重复的，通过action_flag来做区分"
    t.integer "timestamp", comment: "时间戳，当前批次"
    t.datetime "action_at", comment: "激活时间"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "due_at", comment: "审批截止时间"
    t.jsonb "reminder_status", comment: "提醒状态"
    t.index ["activate_source_type", "activate_source_id"], name: "index_bpm_tokens_on_activate_source"
    t.index ["app_id"], name: "index_bpm_tokens_on_app_id"
    t.index ["instance_id"], name: "index_bpm_tokens_on_instance_id"
    t.index ["operator_id"], name: "index_bpm_tokens_on_operator_id"
    t.index ["place_id"], name: "index_bpm_tokens_on_place_id"
    t.index ["previous_token_id"], name: "index_bpm_tokens_on_previous_token_id"
    t.index ["token_source_type", "token_source_id"], name: "index_bpm_tokens_on_token_source"
    t.index ["workflow_id"], name: "index_bpm_tokens_on_workflow_id"
  end

  create_table "bpm_transitions", force: :cascade do |t|
    t.bigint "workflow_id"
    t.bigint "place_id"
    t.string "type", comment: "STI"
    t.jsonb "callback_options", comment: "回调设置"
    t.jsonb "options", comment: "transition跳转的额外设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["place_id"], name: "index_bpm_transitions_on_place_id"
    t.index ["workflow_id"], name: "index_bpm_transitions_on_workflow_id"
  end

  create_table "bpm_workflow_relations", force: :cascade do |t|
    t.string "workflowable_type"
    t.bigint "workflowable_id"
    t.bigint "workflow_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["workflow_id"], name: "index_bpm_workflow_relations_on_workflow_id"
    t.index ["workflowable_type", "workflowable_id"], name: "index_bpm_workflow_relations_on_workflowable"
  end

  create_table "bpm_workflows", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "creator_id"
    t.bigint "mod_id"
    t.bigint "catalog_id"
    t.jsonb "permits", comment: "权限设置"
    t.boolean "permit_enable", comment: "是否开启permit的按钮"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "type", comment: "STI"
    t.string "name", comment: "流程名称"
    t.text "desc", comment: "流程描述"
    t.jsonb "icon", comment: "流程图标"
    t.jsonb "cover_image", comment: "流程封面"
    t.string "state"
    t.integer "position", comment: "catalog内排序"
    t.string "instance_type", comment: "自动生成的instance_type"
    t.string "classify"
    t.jsonb "form", comment: "表单配置 "
    t.jsonb "meta", comment: "工作流额外配置信息 "
    t.jsonb "token_actions", comment: "操作菜单配置"
    t.jsonb "trigger_options", comment: "instance状态改变时候需要额外操作的内容"
    t.boolean "auto_complete_same_handle_token", comment: "是否跳过连续相同的审批人"
    t.jsonb "submit_options", comment: "限制条件"
    t.jsonb "form_setting", comment: "表单配置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.boolean "enable_level", comment: "是否启用 instance 优先级"
    t.jsonb "level_options", comment: "优先级配置"
    t.jsonb "conf", comment: "其他配置"
    t.index ["app_id"], name: "index_bpm_workflows_on_app_id"
    t.index ["catalog_id"], name: "index_bpm_workflows_on_catalog_id"
    t.index ["creator_id"], name: "index_bpm_workflows_on_creator_id"
    t.index ["mod_id"], name: "index_bpm_workflows_on_mod_id"
  end

  create_table "chat_conversations", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "tanent_id"
    t.bigint "user_id"
    t.bigint "current_intent_id"
    t.string "name", comment: "对话名称"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_chat_conversations_on_app_id"
    t.index ["current_intent_id"], name: "index_chat_conversations_on_current_intent_id"
    t.index ["tanent_id"], name: "index_chat_conversations_on_tanent_id"
    t.index ["user_id"], name: "index_chat_conversations_on_user_id"
  end

  create_table "chat_intent_hierarchies", force: :cascade do |t|
    t.integer "ancestor_id"
    t.integer "descendant_id"
    t.integer "generations"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.index ["ancestor_id", "descendant_id", "generations"], name: "chat/intent_anc_desc_idx", unique: true
  end

  create_table "chat_intent_keywords", force: :cascade do |t|
    t.bigint "intent_id"
    t.string "name", comment: "关键词名称"
    t.string "desc", comment: "关键词描述"
    t.string "content_type", comment: "关键词类型"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["intent_id"], name: "index_chat_intent_keywords_on_intent_id"
  end

  create_table "chat_intents", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "tanent_id"
    t.string "type", comment: "STI属性"
    t.integer "parent_id", comment: "closure tree parent_id"
    t.string "name", comment: "意图名称"
    t.string "desc", comment: "意图描述"
    t.string "prompt_summary", comment: "提示词开场白"
    t.integer "position", comment: "排序"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_chat_intents_on_app_id"
    t.index ["tanent_id"], name: "index_chat_intents_on_tanent_id"
  end

  create_table "chat_mention_versions", force: :cascade do |t|
    t.bigint "mention_id"
    t.bigint "message_id"
    t.bigint "conversation_id"
    t.jsonb "payload", comment: "数据"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "ai_response", comment: "当前 AI 返回的数据, 不返回前端"
    t.string "state"
    t.index ["conversation_id"], name: "index_chat_mention_versions_on_conversation_id"
    t.index ["mention_id"], name: "index_chat_mention_versions_on_mention_id"
    t.index ["message_id"], name: "index_chat_mention_versions_on_message_id"
  end

  create_table "chat_mentions", force: :cascade do |t|
    t.string "mentionable_type"
    t.bigint "mentionable_id"
    t.bigint "conversation_id"
    t.bigint "app_id"
    t.bigint "creator_intent_id"
    t.string "type", comment: "STI属性"
    t.string "name", comment: "名称"
    t.string "state"
    t.jsonb "payload", comment: "数据"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "ai_response", comment: "当前 AI 返回的数据, 不返回前端"
    t.index ["app_id"], name: "index_chat_mentions_on_app_id"
    t.index ["conversation_id"], name: "index_chat_mentions_on_conversation_id"
    t.index ["creator_intent_id"], name: "index_chat_mentions_on_creator_intent_id"
    t.index ["mentionable_type", "mentionable_id"], name: "index_chat_mentions_on_mentionable"
  end

  create_table "chat_messages", force: :cascade do |t|
    t.bigint "conversation_id"
    t.string "type", comment: "STI属性"
    t.text "previous_content", comment: "前一句话的内容"
    t.text "content", comment: "内容"
    t.string "result_type", comment: "处理结果标识"
    t.jsonb "suggestions", comment: "提示建议"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "attachment", comment: "附件"
    t.jsonb "meta", comment: "发送的内容"
    t.index ["conversation_id"], name: "index_chat_messages_on_conversation_id"
  end

  create_table "chat_relate_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id", "action_flag"], name: "chat_relate_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_chat_relate_actions_on_app_id"
    t.index ["real_user_id"], name: "index_chat_relate_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "chat_relate_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_chat_relate_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "chat_relate_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_chat_relate_actions_on_user"
  end

  create_table "com_private_policies", force: :cascade do |t|
    t.bigint "app_id"
    t.string "name", comment: "条款名称"
    t.string "key", comment: "关键字，可能有不同业务模块需要使用的关键字"
    t.jsonb "content", comment: "隐私条款内容"
    t.integer "position", comment: "排序"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_com_private_policies_on_app_id"
  end

  create_table "com_record_storages", force: :cascade do |t|
    t.bigint "user_id"
    t.string "key", comment: "缓存区key"
    t.jsonb "storage", comment: "属性暂存区"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_com_record_storages_on_user_id"
  end

  create_table "com_search_items", force: :cascade do |t|
    t.bigint "app_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "搜索条件 "
    t.integer "position", comment: "位置"
    t.string "group_name", comment: "分组标识"
    t.boolean "enabled", comment: "是否启用"
    t.jsonb "conditions", comment: "具体ransack搜索条件"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_com_search_items_on_app_id"
  end

  create_table "com_themes", force: :cascade do |t|
    t.bigint "app_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "主题名称"
    t.jsonb "conf", comment: "主题配置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_com_themes_on_app_id"
  end

  create_table "com_version_histories", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "creator_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "版本发布名称"
    t.string "version", comment: "版本号"
    t.jsonb "content", comment: "发布说明"
    t.integer "position", comment: "发布顺序"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_com_version_histories_on_app_id"
    t.index ["creator_id"], name: "index_com_version_histories_on_creator_id"
  end

  create_table "comments", force: :cascade do |t|
    t.string "commentable_type"
    t.bigint "commentable_id"
    t.bigint "user_id"
    t.string "title", comment: "标题"
    t.text "subject", comment: "简述"
    t.text "body", comment: "内容"
    t.jsonb "attachments", comment: "附件"
    t.integer "parent_id", comment: "父节点"
    t.integer "lft", comment: "左节点"
    t.integer "rgt", comment: "右节点"
    t.integer "depth", comment: "层级"
    t.integer "children_count", comment: "子评论数量"
    t.integer "position", comment: "排序"
    t.integer "likes_count", comment: "点赞次数"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["commentable_type", "commentable_id"], name: "index_comments_on_commentable"
    t.index ["user_id"], name: "index_comments_on_user_id"
  end

  create_table "component_settings", force: :cascade do |t|
    t.bigint "app_id"
    t.string "seq", comment: "编号"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "组件配置名称"
    t.string "component_klass", comment: "组件类名称"
    t.string "component_path", comment: "组件类路径"
    t.jsonb "conf", comment: "组件配置的json结构"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_component_settings_on_app_id"
  end

  create_table "data_counter_stats", force: :cascade do |t|
    t.string "countable_type"
    t.bigint "countable_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.date "date", comment: "日期，如果是年、月则存第一天"
    t.integer "hour", comment: "小时"
    t.string "period"
    t.integer "view_count", comment: "浏览量"
    t.integer "action_count", comment: "使用量"
    t.integer "user_count", comment: "用户量"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["countable_type", "countable_id"], name: "index_data_counter_stats_on_countable"
  end

  create_table "data_forms", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "create_user_id"
    t.string "source_type"
    t.bigint "source_id"
    t.string "record_type"
    t.bigint "record_id"
    t.string "type", comment: "STI属性"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "flag", comment: "可用作同一 source 下不同的关联关系的区分"
    t.string "source_flag", comment: "关联source的flag"
    t.string "state", comment: "数据状态"
    t.jsonb "payload", comment: "存储的信息"
    t.jsonb "summary", comment: "通过form生成的缩略信息"
    t.jsonb "form_conf", comment: "表单的配置，里面支持多态的方式"
    t.jsonb "options", comment: "额外的数据信息"
    t.jsonb "meta", comment: "预留后续的数据存储"
    t.string "form_conf_seq", comment: "表单配置的seq，方便进行检索"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_data_forms_on_app_id"
    t.index ["create_user_id"], name: "index_data_forms_on_create_user_id"
    t.index ["record_type", "record_id"], name: "index_data_forms_on_record"
    t.index ["source_type", "source_id"], name: "index_data_forms_on_source"
  end

  create_table "data_scopes", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "tanent_id"
    t.bigint "user_id"
    t.string "name", comment: "名称"
    t.jsonb "config", comment: "配置"
    t.jsonb "payload"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_data_scopes_on_app_id"
    t.index ["tanent_id"], name: "index_data_scopes_on_tanent_id"
    t.index ["user_id"], name: "index_data_scopes_on_user_id"
  end

  create_table "data_transfers", force: :cascade do |t|
    t.bigint "app_id"
    t.string "source_type"
    t.bigint "source_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "op", comment: "操作"
    t.jsonb "infos", comment: "额外信息"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_data_transfers_on_app_id"
    t.index ["source_type", "source_id"], name: "index_data_transfers_on_source"
    t.index ["target_type", "target_id"], name: "index_data_transfers_on_target"
  end

  create_table "data_view_logs", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "user_id"
    t.string "source_type"
    t.bigint "source_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.datetime "view_at", comment: "最新访问时间"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_data_view_logs_on_app_id"
    t.index ["source_type", "source_id"], name: "index_data_view_logs_on_source"
    t.index ["user_id"], name: "index_data_view_logs_on_user_id"
  end

  create_table "department_hierarchies", force: :cascade do |t|
    t.integer "ancestor_id"
    t.integer "descendant_id"
    t.integer "generations"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.index ["ancestor_id", "descendant_id", "generations"], name: "department_anc_desc_idx", unique: true
  end

  create_table "department_identities", force: :cascade do |t|
    t.bigint "app_id"
    t.string "name", comment: "部门类型名称"
    t.string "department_type", comment: "Department的类型，可能会关系到Department的STI"
    t.string "color", comment: "标签颜色"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_department_identities_on_app_id"
  end

  create_table "departments", force: :cascade do |t|
    t.bigint "org_id"
    t.bigint "root_org_id"
    t.bigint "department_identity_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.integer "parent_id", comment: "closure tree parent_id"
    t.string "code", comment: "组织标识"
    t.string "name", comment: "组织名称"
    t.string "short_name", comment: "组织简称"
    t.string "type", comment: "STI类型，可以是集团，或者在某些时候可能是学校这样的类型"
    t.integer "position", comment: "排序"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "type_name", comment: "节点类型"
    t.index ["department_identity_id"], name: "index_departments_on_department_identity_id"
    t.index ["org_id"], name: "index_departments_on_org_id"
    t.index ["root_org_id"], name: "index_departments_on_root_org_id"
  end

  create_table "dingtalk_callback_registrations", force: :cascade do |t|
    t.bigint "client_id", null: false
    t.string "event_tag", null: false, comment: "事件标识"
    t.string "callback_url", null: false, comment: "回调地址"
    t.string "status", default: "pending", comment: "状态"
    t.datetime "registered_at", comment: "注册时间"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["client_id", "event_tag"], name: "idx_callback_client_event", unique: true
    t.index ["client_id"], name: "index_dingtalk_callback_registrations_on_client_id"
  end

  create_table "dingtalk_clients", force: :cascade do |t|
    t.bigint "app_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "名称"
    t.string "code", comment: "标识"
    t.string "app_key", comment: "app key"
    t.string "app_secret", comment: "app secret"
    t.string "corp_id", comment: "商户id或者租户id"
    t.string "type", comment: "STI"
    t.jsonb "options", comment: "配置信息"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "company_corp_id", comment: "钉钉企业corp_id"
    t.index ["app_id"], name: "index_dingtalk_clients_on_app_id"
  end

  create_table "dingtalk_ding_messages", force: :cascade do |t|
    t.string "notifyable_type"
    t.bigint "notifyable_id"
    t.bigint "user_id"
    t.string "seq", comment: "编号"
    t.string "oauth_app_id", comment: "服务名称的标识，对应client的code"
    t.jsonb "creator", comment: "创建者信息"
    t.jsonb "dingBody", comment: "发送内容"
    t.jsonb "receivers", comment: "接收者信息(数组对象)"
    t.jsonb "source", comment: "im会话"
    t.boolean "sendToIm", comment: "是否发送到IM会话"
    t.string "scene", comment: "是否发送到IM会话"
    t.jsonb "response", comment: "发送结果"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["notifyable_type", "notifyable_id"], name: "index_dingtalk_ding_messages_on_notifyable"
    t.index ["user_id"], name: "index_dingtalk_ding_messages_on_user_id"
  end

  create_table "dingtalk_template_messages", force: :cascade do |t|
    t.string "notifyable_type"
    t.bigint "notifyable_id"
    t.bigint "user_id"
    t.string "seq", comment: "编号"
    t.datetime "read_at"
    t.boolean "is_read"
    t.string "oauth_app_id", comment: "服务名称的标识，对应client的code"
    t.string "openid", comment: "发送的openid，浙政钉是accountId"
    t.jsonb "message", comment: "发送内容"
    t.jsonb "response", comment: "发送结果"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["notifyable_type", "notifyable_id"], name: "index_dingtalk_template_messages_on_notifyable"
    t.index ["user_id"], name: "index_dingtalk_template_messages_on_user_id"
  end

  create_table "dingtalk_work_notifications", force: :cascade do |t|
    t.string "notifyable_type"
    t.bigint "notifyable_id"
    t.bigint "user_id"
    t.string "seq", comment: "编号"
    t.datetime "read_at"
    t.boolean "is_read"
    t.string "oauth_app_id", comment: "服务名称的标识，对应client的code"
    t.string "openid", comment: "接收者的openid"
    t.string "msg_type", comment: "消息类型: text/link/markdown/action_card/oa"
    t.jsonb "content", comment: "消息内容"
    t.jsonb "response", comment: "发送结果"
    t.string "at_mobiles"
    t.string "at_user_ids"
    t.boolean "at_all", comment: "是否@所有人"
    t.string "task_id", comment: "工作通知任务ID"
    t.jsonb "progress", comment: "发送进度"
    t.jsonb "send_result", comment: "发送结果详情"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["notifyable_type", "notifyable_id"], name: "index_dingtalk_work_notifications_on_notifyable"
    t.index ["user_id"], name: "index_dingtalk_work_notifications_on_user_id"
  end

  create_table "duties", force: :cascade do |t|
    t.bigint "duty_group_id"
    t.bigint "org_id"
    t.bigint "department_id"
    t.string "name", comment: "职务名称"
    t.string "rank", comment: "职务等级"
    t.integer "position", comment: "排序"
    t.string "code", comment: "岗位标识"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["department_id"], name: "index_duties_on_department_id"
    t.index ["duty_group_id"], name: "index_duties_on_duty_group_id"
    t.index ["org_id"], name: "index_duties_on_org_id"
  end

  create_table "duty_groups", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "org_id"
    t.string "name", comment: "角色组名称"
    t.integer "position", comment: "排序"
    t.string "code", comment: "岗位组标识"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_duty_groups_on_app_id"
    t.index ["org_id"], name: "index_duty_groups_on_org_id"
  end

  create_table "favor_folders", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "type", comment: "STI属性"
    t.string "name", comment: "收藏夹名称"
    t.jsonb "cover_image", comment: "封面图"
    t.jsonb "content", comment: "详情"
    t.integer "position", comment: "排序"
    t.jsonb "option", comment: "额外配置信息"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_favor_folders_on_app_id"
    t.index ["user_id"], name: "index_favor_folders_on_user_id"
  end

  create_table "favor_mark_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id", "action_flag"], name: "favor_mark_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_favor_mark_actions_on_app_id"
    t.index ["real_user_id"], name: "index_favor_mark_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "favor_mark_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_favor_mark_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "favor_mark_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_favor_mark_actions_on_user"
  end

  create_table "forms_templates", force: :cascade do |t|
    t.bigint "app_id"
    t.string "uuid", comment: "表单的唯一标识，可以替代id给前端使用"
    t.string "name", comment: "表单的名称"
    t.jsonb "form", comment: "表单配置的内容"
    t.jsonb "form_setting", comment: "表单内容"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_forms_templates_on_app_id"
  end

  create_table "grant_applications", force: :cascade do |t|
    t.bigint "app_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.datetime "effective_at", comment: "生效时间"
    t.datetime "invalid_at", comment: "失效时间"
    t.string "name", comment: "应用名称"
    t.string "type", comment: "STI类型"
    t.string "app_key", comment: "app key"
    t.string "app_secret", comment: "app secret"
    t.jsonb "options", comment: "额外的配置, { encrypt: sm 或 aes }"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_grant_applications_on_app_id"
  end

  create_table "iest_ai_chat_conversations", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "user_id"
    t.bigint "current_intent_id"
    t.string "name", comment: "对话名称"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "tanent_id"
    t.index ["app_id"], name: "index_iest_ai_chat_conversations_on_app_id"
    t.index ["current_intent_id"], name: "index_iest_ai_chat_conversations_on_current_intent_id"
    t.index ["tanent_id"], name: "index_iest_ai_chat_conversations_on_tanent_id"
    t.index ["user_id"], name: "index_iest_ai_chat_conversations_on_user_id"
  end

  create_table "iest_ai_chat_intent_hierarchies", force: :cascade do |t|
    t.integer "ancestor_id"
    t.integer "descendant_id"
    t.integer "generations"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.index ["ancestor_id", "descendant_id", "generations"], name: "iest/ai/chat/intent_anc_desc_idx", unique: true
  end

  create_table "iest_ai_chat_intent_keywords", force: :cascade do |t|
    t.bigint "intent_id"
    t.string "name", comment: "关键词名称"
    t.string "desc", comment: "关键词描述"
    t.string "content_type", comment: "关键词类型"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["intent_id"], name: "index_iest_ai_chat_intent_keywords_on_intent_id"
  end

  create_table "iest_ai_chat_intents", force: :cascade do |t|
    t.bigint "app_id"
    t.string "type", comment: "STI属性"
    t.string "name", comment: "意图名称"
    t.string "desc", comment: "意图描述"
    t.string "prompt_summary", comment: "提示词开场白"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "parent_id", comment: "closure tree parent_id"
    t.integer "position", comment: "排序"
    t.bigint "tanent_id"
    t.index ["app_id"], name: "index_iest_ai_chat_intents_on_app_id"
    t.index ["tanent_id"], name: "index_iest_ai_chat_intents_on_tanent_id"
  end

  create_table "iest_ai_chat_mention_versions", force: :cascade do |t|
    t.bigint "mention_id"
    t.bigint "message_id"
    t.bigint "conversation_id"
    t.jsonb "payload", comment: "数据"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["conversation_id"], name: "index_iest_ai_chat_mention_versions_on_conversation_id"
    t.index ["mention_id"], name: "index_iest_ai_chat_mention_versions_on_mention_id"
    t.index ["message_id"], name: "index_iest_ai_chat_mention_versions_on_message_id"
  end

  create_table "iest_ai_chat_mentions", force: :cascade do |t|
    t.string "mentionable_type"
    t.bigint "mentionable_id"
    t.bigint "conversation_id"
    t.bigint "app_id"
    t.string "type", comment: "STI属性"
    t.string "name", comment: "名称"
    t.string "state"
    t.jsonb "payload", comment: "数据"
    t.string "self_intent", comment: "意图标识，用于归集相同意图的消息下的 mentions"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "creator_intent_id"
    t.index ["app_id"], name: "index_iest_ai_chat_mentions_on_app_id"
    t.index ["conversation_id"], name: "index_iest_ai_chat_mentions_on_conversation_id"
    t.index ["creator_intent_id"], name: "index_iest_ai_chat_mentions_on_creator_intent_id"
    t.index ["mentionable_type", "mentionable_id"], name: "index_iest_ai_chat_mentions_on_mentionable"
  end

  create_table "iest_ai_chat_messages", force: :cascade do |t|
    t.bigint "conversation_id"
    t.string "type", comment: "STI属性"
    t.text "previous_content", comment: "前一句话的内容"
    t.text "content", comment: "内容"
    t.string "result_type", comment: "处理结果标识"
    t.jsonb "suggestions", comment: "提示建议"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["conversation_id"], name: "index_iest_ai_chat_messages_on_conversation_id"
  end

  create_table "iest_ai_chat_relate_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id", "action_flag"], name: "iest_ai_chat_relate_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_iest_ai_chat_relate_actions_on_app_id"
    t.index ["real_user_id"], name: "index_iest_ai_chat_relate_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "iest_ai_chat_relate_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_iest_ai_chat_relate_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "iest_ai_chat_relate_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_iest_ai_chat_relate_actions_on_user"
  end

  create_table "iest_paperwork_results", force: :cascade do |t|
    t.bigint "paperwork_id"
    t.string "name", comment: "结果名称"
    t.string "raw", comment: "原文"
    t.jsonb "payload", comment: "结果"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["paperwork_id"], name: "index_iest_paperwork_results_on_paperwork_id"
  end

  create_table "iest_paperworks", force: :cascade do |t|
    t.bigint "user_id"
    t.bigint "app_id"
    t.bigint "org_id"
    t.string "name", comment: "名称"
    t.datetime "operate_at", comment: "操作时间"
    t.string "state", comment: "状态"
    t.jsonb "attachment", comment: "附件"
    t.jsonb "response", comment: "响应"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_iest_paperworks_on_app_id"
    t.index ["org_id"], name: "index_iest_paperworks_on_org_id"
    t.index ["user_id"], name: "index_iest_paperworks_on_user_id"
  end

  create_table "irs_government_projects", force: :cascade do |t|
    t.date "query_date", null: false, comment: "查询日期"
    t.jsonb "raw_data", comment: "原始JSON响应数据"
    t.integer "projects_count", default: 0, comment: "项目数量"
    t.boolean "success", default: false, comment: "是否成功获取数据"
    t.text "error_message", comment: "错误信息"
    t.datetime "cached_at", null: false, comment: "缓存时间"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["cached_at"], name: "index_irs_government_projects_on_cached_at", comment: "缓存时间索引"
    t.index ["query_date"], name: "index_irs_government_projects_on_query_date", unique: true, comment: "查询日期唯一索引"
    t.index ["raw_data"], name: "index_irs_government_projects_on_raw_data", using: :gin, comment: "JSON数据索引"
    t.index ["success"], name: "index_irs_government_projects_on_success", comment: "成功状态索引"
  end

  create_table "member_identities", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "org_id"
    t.jsonb "manages", comment: "权限设置"
    t.boolean "manage_enable", comment: "是否开启permit的按钮"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "ancestry", comment: "树形结构"
    t.string "name", comment: "身份名称"
    t.string "member_type", comment: "Member的类型"
    t.integer "depth", comment: "树结构深度"
    t.integer "children_count", comment: "子对象的数据"
    t.jsonb "form", comment: "Member配置的表单"
    t.jsonb "config", comment: "配置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_member_identities_on_app_id"
    t.index ["org_id"], name: "index_member_identities_on_org_id"
  end

  create_table "member_identity_permit_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id", "action_flag"], name: "member_identity_permit_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_member_identity_permit_actions_on_app_id"
    t.index ["real_user_id"], name: "index_member_identity_permit_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "member_identity_permit_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_member_identity_permit_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "member_identity_permit_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_member_identity_permit_actions_on_user"
  end

  create_table "member_requests", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "user_id"
    t.bigint "member_identity_id"
    t.string "create_instance_state", comment: "创建工作流的状态"
    t.datetime "create_instance_timestamp", comment: "创建工作流的操作时间"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "请求名称"
    t.jsonb "payload", comment: "相关信息，会存储到member的payload里"
    t.jsonb "member_attributes", comment: "相关信息，会存储到member的attributes里"
    t.jsonb "options", comment: "加入什么组织和岗位，相关配置"
    t.string "state", comment: "状态"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "tanent_id"
    t.string "type", comment: "STI"
    t.bigint "member_id"
    t.index ["app_id"], name: "index_member_requests_on_app_id"
    t.index ["member_id"], name: "index_member_requests_on_member_id"
    t.index ["member_identity_id"], name: "index_member_requests_on_member_identity_id"
    t.index ["tanent_id"], name: "index_member_requests_on_tanent_id"
    t.index ["user_id"], name: "index_member_requests_on_user_id"
  end

  create_table "members", force: :cascade do |t|
    t.bigint "user_id"
    t.bigint "member_identity_id"
    t.bigint "app_id"
    t.bigint "member_request_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.jsonb "payload", comment: "payload payload存储的字段"
    t.jsonb "payload_summary", comment: "payload summary存储的字段"
    t.string "type", comment: "STI类型，可以是集团，或者在某些时候可能是学校这样的类型"
    t.string "code", comment: "用户标识"
    t.datetime "blocked_at"
    t.boolean "is_blocked"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "gmt_create", comment: "员工创建时间"
    t.string "gender", comment: "员工性别"
    t.string "political_code", comment: "\t政治面貌"
    t.string "job_level_code", comment: "职级"
    t.string "budgeted_post_code", comment: "编制"
    t.string "pos_job_rank_code", comment: "职务层次"
    t.string "job_gmt_create", comment: "任职创建时间"
    t.string "pos_job", comment: "职务"
    t.date "effective_at", comment: "生效时间"
    t.date "invalid_at", comment: "失效时间"
    t.bigint "tanent_id"
    t.index ["app_id"], name: "index_members_on_app_id"
    t.index ["member_identity_id"], name: "index_members_on_member_identity_id"
    t.index ["member_request_id"], name: "index_members_on_member_request_id"
    t.index ["tanent_id"], name: "index_members_on_tanent_id"
    t.index ["user_id"], name: "index_members_on_user_id"
  end

  create_table "memberships", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "org_id"
    t.bigint "user_id"
    t.bigint "member_id"
    t.bigint "department_id"
    t.bigint "duty_id"
    t.datetime "effective_at", comment: "生效时间，可以为空"
    t.datetime "invalid_at", comment: "失效时间，可以为空"
    t.jsonb "payload"
    t.string "create_instance_state", comment: "创建工作流的状态"
    t.datetime "create_instance_timestamp", comment: "创建工作流的操作时间"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "duty_rank"
    t.boolean "priority", comment: "是否主要岗位"
    t.index ["app_id"], name: "index_memberships_on_app_id"
    t.index ["department_id"], name: "index_memberships_on_department_id"
    t.index ["duty_id"], name: "index_memberships_on_duty_id"
    t.index ["member_id"], name: "index_memberships_on_member_id"
    t.index ["org_id"], name: "index_memberships_on_org_id"
    t.index ["user_id"], name: "index_memberships_on_user_id"
  end

  create_table "model_confs", force: :cascade do |t|
    t.bigint "model_define_id"
    t.string "name", comment: "名称"
    t.string "klass", comment: "类名"
    t.jsonb "conf", comment: "具体配置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["model_define_id"], name: "index_model_confs_on_model_define_id"
  end

  create_table "model_defines", force: :cascade do |t|
    t.string "klass", comment: "对应设置的Model名称"
    t.string "name", comment: "模型设置的中文名"
    t.string "association_chain"
    t.string "klass_singular", comment: "自动生成的模型名称，因为路由上传是以underscore方式来传输，所以在这里需要能进行唯一性查找"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "model_settings", force: :cascade do |t|
    t.bigint "model_define_id"
    t.string "setable_type"
    t.bigint "setable_id"
    t.bigint "app_id"
    t.bigint "forms_template_id"
    t.bigint "bpm_workflow_id"
    t.bigint "ref_model_setting_id"
    t.string "flag", comment: "同一个模型中的不同定义，其中model代表是这个对象的模型"
    t.string "flag_name", comment: "flag对应中文名称"
    t.jsonb "form", comment: "可以直接定义表单"
    t.jsonb "form_setting", comment: "表单结构"
    t.jsonb "api_config", comment: "API Config"
    t.string "ref_model_setting_flag", comment: "关联model_setting_flag"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_model_settings_on_app_id"
    t.index ["bpm_workflow_id"], name: "index_model_settings_on_bpm_workflow_id"
    t.index ["forms_template_id"], name: "index_model_settings_on_forms_template_id"
    t.index ["model_define_id"], name: "index_model_settings_on_model_define_id"
    t.index ["ref_model_setting_id"], name: "index_model_settings_on_ref_model_setting_id"
    t.index ["setable_type", "setable_id"], name: "index_model_settings_on_setable"
  end

  create_table "mods", force: :cascade do |t|
    t.string "name", comment: "模块名称"
    t.string "key", comment: "模块对应查找的key值"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "notify_info_messages", force: :cascade do |t|
    t.bigint "app_id"
    t.string "user_type"
    t.bigint "user_id"
    t.bigint "create_user_id"
    t.string "notifyable_type"
    t.bigint "notifyable_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "flag", comment: "发送标识，保证唯一性"
    t.string "title", comment: "发送标题"
    t.string "content", comment: "发送内容"
    t.jsonb "meta", comment: "额外信息"
    t.string "url", comment: "链接地址"
    t.datetime "read_at"
    t.boolean "is_read"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_notify_info_messages_on_app_id"
    t.index ["create_user_id"], name: "index_notify_info_messages_on_create_user_id"
    t.index ["notifyable_type", "notifyable_id"], name: "index_notify_info_messages_on_notifyable"
    t.index ["user_type", "user_id"], name: "index_notify_info_messages_on_user"
  end

  create_table "notify_like_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id", "action_flag"], name: "notify_like_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_notify_like_actions_on_app_id"
    t.index ["real_user_id"], name: "index_notify_like_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "notify_like_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_notify_like_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "notify_like_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_notify_like_actions_on_user"
  end

  create_table "notify_sms_messages", force: :cascade do |t|
    t.string "notifyable_type"
    t.bigint "notifyable_id"
    t.string "mobile", comment: "发送手机号"
    t.string "title", comment: "发送标题"
    t.string "account", comment: "发送账号"
    t.string "content", comment: "发送内容"
    t.jsonb "response", comment: "发送结果"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["notifyable_type", "notifyable_id"], name: "index_notify_sms_messages_on_notifyable"
  end

  create_table "notify_template_messages", force: :cascade do |t|
    t.bigint "app_id"
    t.string "notifyable_type"
    t.bigint "notifyable_id"
    t.bigint "user_id"
    t.string "type", comment: "STI属性"
    t.string "state", comment: "状态"
    t.string "oauth_app_id", comment: "微信服务名称的标识"
    t.string "openid", comment: "微信发送的openid"
    t.jsonb "body", comment: "发送内容"
    t.jsonb "response", comment: "发送结果"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_notify_template_messages_on_app_id"
    t.index ["notifyable_type", "notifyable_id"], name: "index_notify_template_messages_on_notifyable"
    t.index ["user_id"], name: "index_notify_template_messages_on_user_id"
  end

  create_table "notify_wechat_template_messages", force: :cascade do |t|
    t.string "notifyable_type"
    t.bigint "notifyable_id"
    t.bigint "user_id"
    t.string "oauth_app_id", comment: "微信服务名称的标识"
    t.string "openid", comment: "微信发送的openid"
    t.jsonb "message", comment: "发送内容"
    t.jsonb "response", comment: "发送结果"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["notifyable_type", "notifyable_id"], name: "index_notify_wechat_template_messages_on_notifyable"
    t.index ["user_id"], name: "index_notify_wechat_template_messages_on_user_id"
  end

  create_table "opm_balances", force: :cascade do |t|
    t.bigint "user_id"
    t.string "category"
    t.decimal "total"
    t.datetime "expires_at"
    t.string "reason"
    t.jsonb "details"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_opm_balances_on_user_id"
  end

  create_table "opm_employ_invites", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "creator_id"
    t.bigint "org_id"
    t.bigint "member_identity_id"
    t.bigint "department_id"
    t.bigint "duty_id"
    t.datetime "effective_at", comment: "生效时间"
    t.datetime "invalid_at", comment: "失效时间"
    t.jsonb "payload"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_opm_employ_invites_on_app_id"
    t.index ["creator_id"], name: "index_opm_employ_invites_on_creator_id"
    t.index ["department_id"], name: "index_opm_employ_invites_on_department_id"
    t.index ["duty_id"], name: "index_opm_employ_invites_on_duty_id"
    t.index ["member_identity_id"], name: "index_opm_employ_invites_on_member_identity_id"
    t.index ["org_id"], name: "index_opm_employ_invites_on_org_id"
  end

  create_table "opm_former_employees", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "org_id"
    t.bigint "member_id"
    t.bigint "user_id"
    t.date "leave_at", comment: "离职时间"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_opm_former_employees_on_app_id"
    t.index ["member_id"], name: "index_opm_former_employees_on_member_id"
    t.index ["org_id"], name: "index_opm_former_employees_on_org_id"
    t.index ["user_id"], name: "index_opm_former_employees_on_user_id"
  end

  create_table "opm_groups", force: :cascade do |t|
    t.bigint "app_id"
    t.string "type", comment: "STI属性"
    t.string "name", comment: "名称"
    t.string "state", comment: "状态"
    t.string "code", comment: "编号"
    t.integer "position", comment: "排序"
    t.jsonb "payload", comment: "额外字段"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_opm_groups_on_app_id"
  end

  create_table "opm_holiday_travel_requests", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "user_id"
    t.datetime "start_at", comment: "开始时间"
    t.datetime "end_at", comment: "结束时间"
    t.decimal "duration", comment: "时长"
    t.text "reason", comment: "理由"
    t.jsonb "attachment", comment: "附件"
    t.string "state"
    t.boolean "is_workday", comment: "是否工作日"
    t.string "destination", comment: "目的地"
    t.string "contact_info", comment: "联系方式"
    t.string "report_type"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_opm_holiday_travel_requests_on_app_id"
    t.index ["user_id"], name: "index_opm_holiday_travel_requests_on_user_id"
  end

  create_table "opm_hr_requests", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "org_id"
    t.bigint "ownership_id"
    t.bigint "user_id"
    t.bigint "member_id"
    t.bigint "creator_id"
    t.bigint "employ_invite_id"
    t.string "create_instance_state", comment: "创建工作流的状态"
    t.datetime "create_instance_timestamp", comment: "创建工作流的操作时间"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "type", comment: "STI属性"
    t.jsonb "payload", comment: "payload"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_opm_hr_requests_on_app_id"
    t.index ["creator_id"], name: "index_opm_hr_requests_on_creator_id"
    t.index ["employ_invite_id"], name: "index_opm_hr_requests_on_employ_invite_id"
    t.index ["member_id"], name: "index_opm_hr_requests_on_member_id"
    t.index ["org_id"], name: "index_opm_hr_requests_on_org_id"
    t.index ["ownership_id"], name: "index_opm_hr_requests_on_ownership_id"
    t.index ["user_id"], name: "index_opm_hr_requests_on_user_id"
  end

  create_table "opm_hr_transfers", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "org_id"
    t.bigint "member_id"
    t.bigint "user_id"
    t.string "type", comment: "STI类型"
    t.jsonb "payload", comment: "payload"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_opm_hr_transfers_on_app_id"
    t.index ["member_id"], name: "index_opm_hr_transfers_on_member_id"
    t.index ["org_id"], name: "index_opm_hr_transfers_on_org_id"
    t.index ["user_id"], name: "index_opm_hr_transfers_on_user_id"
  end

  create_table "opm_items", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "user_id"
    t.bigint "member_id"
    t.string "type", comment: "STI属性"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.datetime "effective_at", comment: "生效时间"
    t.datetime "invalid_at", comment: "失效时间"
    t.string "seq", comment: "编号"
    t.string "create_instance_state", comment: "创建工作流的状态"
    t.datetime "create_instance_timestamp", comment: "创建工作流的操作时间"
    t.string "name", comment: "名称"
    t.string "state", comment: "状态"
    t.jsonb "content", comment: "内容"
    t.string "origin", comment: "来源"
    t.string "mode", comment: "分类"
    t.jsonb "payload", comment: "其他字段"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_opm_items_on_app_id"
    t.index ["member_id"], name: "index_opm_items_on_member_id"
    t.index ["user_id"], name: "index_opm_items_on_user_id"
  end

  create_table "opm_job_titles", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "group_id"
    t.string "type", comment: "STI属性"
    t.string "name", comment: "名称"
    t.string "state", comment: "状态"
    t.string "level", comment: "等级"
    t.string "code", comment: "编号"
    t.integer "position", comment: "排序"
    t.jsonb "payload", comment: "其他字段"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_opm_job_titles_on_app_id"
    t.index ["group_id"], name: "index_opm_job_titles_on_group_id"
  end

  create_table "opm_leave_requests", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "user_id"
    t.string "flowable_type"
    t.bigint "flowable_id"
    t.string "type"
    t.datetime "start_at"
    t.datetime "end_at"
    t.decimal "duration"
    t.text "reason"
    t.jsonb "attachment"
    t.jsonb "model_payload"
    t.string "leave_type"
    t.string "unit_type"
    t.decimal "min_unit"
    t.decimal "max_unit"
    t.decimal "min_interval"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_opm_leave_requests_on_app_id"
    t.index ["flowable_type", "flowable_id"], name: "index_opm_leave_requests_on_flowable"
    t.index ["user_id"], name: "index_opm_leave_requests_on_user_id"
  end

  create_table "opm_ownerships", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "org_id"
    t.bigint "department_id"
    t.bigint "department_identity_id"
    t.bigint "member_identity_id"
    t.bigint "duty_id"
    t.jsonb "manages", comment: "权限设置"
    t.boolean "manage_enable", comment: "是否开启permit的按钮"
    t.jsonb "config", comment: "配置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_opm_ownerships_on_app_id"
    t.index ["department_id"], name: "index_opm_ownerships_on_department_id"
    t.index ["department_identity_id"], name: "index_opm_ownerships_on_department_identity_id"
    t.index ["duty_id"], name: "index_opm_ownerships_on_duty_id"
    t.index ["member_identity_id"], name: "index_opm_ownerships_on_member_identity_id"
    t.index ["org_id"], name: "index_opm_ownerships_on_org_id"
  end

  create_table "opm_permit_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id", "action_flag"], name: "opm_permit_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_opm_permit_actions_on_app_id"
    t.index ["real_user_id"], name: "index_opm_permit_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "opm_permit_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_opm_permit_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "opm_permit_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_opm_permit_actions_on_user"
  end

  create_table "opm_records", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "user_id"
    t.bigint "member_id"
    t.string "source_type"
    t.bigint "source_id"
    t.string "type", comment: "STI属性"
    t.datetime "effective_at", comment: "生效时间"
    t.datetime "invalid_at", comment: "失效时间"
    t.string "name", comment: "名称"
    t.string "state", comment: "状态"
    t.string "code", comment: "编号"
    t.string "origin", comment: "来源"
    t.string "level", comment: "级别"
    t.integer "position", comment: "排序"
    t.jsonb "payload", comment: "其他字段"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_opm_records_on_app_id"
    t.index ["member_id"], name: "index_opm_records_on_member_id"
    t.index ["source_type", "source_id"], name: "index_opm_records_on_source"
    t.index ["user_id"], name: "index_opm_records_on_user_id"
  end

  create_table "opm_relate_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id", "action_flag"], name: "opm_relate_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_opm_relate_actions_on_app_id"
    t.index ["real_user_id"], name: "index_opm_relate_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "opm_relate_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_opm_relate_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "opm_relate_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_opm_relate_actions_on_user"
  end

  create_table "opm_transactions", force: :cascade do |t|
    t.bigint "user_id"
    t.string "source_type"
    t.bigint "source_id"
    t.string "category"
    t.decimal "amount"
    t.string "operation"
    t.datetime "expires_at"
    t.string "reason"
    t.jsonb "details"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["source_type", "source_id"], name: "index_opm_transactions_on_source"
    t.index ["user_id"], name: "index_opm_transactions_on_user_id"
  end

  create_table "org_clients", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "org_id"
    t.string "client_type"
    t.bigint "client_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_org_clients_on_app_id"
    t.index ["client_type", "client_id"], name: "index_org_clients_on_client"
    t.index ["org_id"], name: "index_org_clients_on_org_id"
  end

  create_table "org_hierarchies", force: :cascade do |t|
    t.integer "ancestor_id"
    t.integer "descendant_id"
    t.integer "generations"
    t.datetime "created_at"
    t.datetime "updated_at"
    t.index ["ancestor_id", "descendant_id", "generations"], name: "org_anc_desc_idx", unique: true
  end

  create_table "org_identities", force: :cascade do |t|
    t.bigint "app_id"
    t.string "name", comment: "组织类型名称"
    t.string "org_type", comment: "Org的类型，可能会关系到Org的STI"
    t.integer "orgs_count", comment: "关联的Org数量"
    t.jsonb "form", comment: "Member配置的表单"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_org_identities_on_app_id"
  end

  create_table "org_member_identities", force: :cascade do |t|
    t.bigint "app_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "名称"
    t.string "code", comment: "标识"
    t.string "org_member_type", comment: "OrgMember的类型"
    t.jsonb "settle_in_form", comment: "入驻申请表单"
    t.jsonb "postpone_form", comment: "延期申请表单"
    t.jsonb "form", comment: "表单"
    t.jsonb "config", comment: "配置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_org_member_identities_on_app_id"
  end

  create_table "org_members", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "org_id"
    t.bigint "org_member_identity_id"
    t.jsonb "payload", comment: "payload payload存储的字段"
    t.jsonb "payload_summary", comment: "payload summary存储的字段"
    t.date "effective_at", comment: "生效时间"
    t.date "invalid_at", comment: "失效时间"
    t.string "type", comment: "STI类型"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "tanent_id"
    t.index ["app_id"], name: "index_org_members_on_app_id"
    t.index ["org_id"], name: "index_org_members_on_org_id"
    t.index ["org_member_identity_id"], name: "index_org_members_on_org_member_identity_id"
    t.index ["tanent_id"], name: "index_org_members_on_tanent_id"
  end

  create_table "org_ownerships", force: :cascade do |t|
    t.bigint "org_id"
    t.bigint "user_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["org_id"], name: "index_org_ownerships_on_org_id"
    t.index ["user_id"], name: "index_org_ownerships_on_user_id"
  end

  create_table "org_relations", force: :cascade do |t|
    t.string "source_type"
    t.bigint "source_id"
    t.bigint "org_id"
    t.string "mode"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["org_id"], name: "index_org_relations_on_org_id"
    t.index ["source_type", "source_id"], name: "index_org_relations_on_source"
  end

  create_table "org_requests", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "user_id"
    t.bigint "member_identity_id"
    t.bigint "member_id"
    t.bigint "org_id"
    t.bigint "org_member_identity_id"
    t.bigint "org_member_id"
    t.bigint "org_identity_id"
    t.bigint "tanent_id"
    t.string "create_instance_state", comment: "创建工作流的状态"
    t.datetime "create_instance_timestamp", comment: "创建工作流的操作时间"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "组织名称"
    t.string "code", comment: "组织标识"
    t.jsonb "org_payload", comment: "相关信息，会存储到org的payload里"
    t.jsonb "member_payload", comment: "相关信息，会存储到member的payload里"
    t.string "type", comment: "STI"
    t.string "state", comment: "状态: draft, approving"
    t.datetime "approval_at", comment: "审批通过时间"
    t.jsonb "options", comment: "其他预留信息"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_org_requests_on_app_id"
    t.index ["member_id"], name: "index_org_requests_on_member_id"
    t.index ["member_identity_id"], name: "index_org_requests_on_member_identity_id"
    t.index ["org_id"], name: "index_org_requests_on_org_id"
    t.index ["org_identity_id"], name: "index_org_requests_on_org_identity_id"
    t.index ["org_member_id"], name: "index_org_requests_on_org_member_id"
    t.index ["org_member_identity_id"], name: "index_org_requests_on_org_member_identity_id"
    t.index ["tanent_id"], name: "index_org_requests_on_tanent_id"
    t.index ["user_id"], name: "index_org_requests_on_user_id"
  end

  create_table "orgs", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "org_identity_id"
    t.bigint "region_area_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.jsonb "payload", comment: "payload payload存储的字段"
    t.jsonb "payload_summary", comment: "payload summary存储的字段"
    t.integer "parent_id", comment: "closure tree parent_id"
    t.string "code", comment: "组织标识"
    t.string "name", comment: "组织名称"
    t.string "short_name", comment: "组织简称"
    t.string "type", comment: "STI类型，可以是集团，或者在某些时候可能是学校这样的类型"
    t.integer "position", comment: "排序"
    t.string "province", comment: "省"
    t.string "city", comment: "市"
    t.string "district", comment: "区"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "last_synced_time", comment: "最后同步时间"
    t.string "type_name", comment: "节点类型"
    t.bigint "tanent_id"
    t.index ["app_id"], name: "index_orgs_on_app_id"
    t.index ["org_identity_id"], name: "index_orgs_on_org_identity_id"
    t.index ["region_area_id"], name: "index_orgs_on_region_area_id"
    t.index ["tanent_id"], name: "index_orgs_on_tanent_id"
  end

  create_table "page_settings", force: :cascade do |t|
    t.bigint "app_id"
    t.string "seq", comment: "编号"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "页面配置名称"
    t.jsonb "conf", comment: "页面配置的json结构"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_page_settings_on_app_id"
  end

  create_table "paper_trail_versions", force: :cascade do |t|
    t.string "operator_type"
    t.bigint "operator_id"
    t.string "item_type"
    t.integer "item_id"
    t.string "event", comment: "create, update, destroy"
    t.string "whodunnit", comment: "whodunnit"
    t.jsonb "object", comment: "object attributes"
    t.jsonb "object_changes", comment: "object changes"
    t.jsonb "controller_info", comment: "controller info"
    t.jsonb "model_info", comment: "model info"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["item_type", "item_id"], name: "index_versions_on_item_id_item_type"
    t.index ["operator_type", "operator_id"], name: "index_paper_trail_versions_on_operator"
  end

  create_table "permit_permissions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "mod_id"
    t.bigint "tanent_id"
    t.bigint "user_id"
    t.bigint "route_setting_id"
    t.string "platform", comment: "平台"
    t.string "aname", comment: "action名称"
    t.string "cname", comment: "controller名称"
    t.string "klass", comment: "controller"
    t.string "action", comment: "action"
    t.jsonb "whitelist"
    t.jsonb "blacklist"
    t.jsonb "payload"
    t.integer "position"
    t.string "key"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_permit_permissions_on_app_id"
    t.index ["mod_id"], name: "index_permit_permissions_on_mod_id"
    t.index ["route_setting_id"], name: "index_permit_permissions_on_route_setting_id"
    t.index ["tanent_id"], name: "index_permit_permissions_on_tanent_id"
    t.index ["user_id"], name: "index_permit_permissions_on_user_id"
  end

  create_table "reg_qrcodes", force: :cascade do |t|
    t.bigint "app_id"
    t.string "record_type"
    t.bigint "record_id"
    t.bigint "creator_id"
    t.string "type", comment: "STI属性"
    t.datetime "effective_at", comment: "生效时间"
    t.datetime "invalid_at", comment: "失效时间"
    t.string "code", comment: "码"
    t.string "state", comment: "状态"
    t.string "mod", comment: "模式"
    t.jsonb "payload", comment: "额外字段"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_reg_qrcodes_on_app_id"
    t.index ["creator_id"], name: "index_reg_qrcodes_on_creator_id"
    t.index ["record_type", "record_id"], name: "index_reg_qrcodes_on_record"
  end

  create_table "reg_records", force: :cascade do |t|
    t.bigint "app_id"
    t.string "origin_type"
    t.bigint "origin_id"
    t.string "source_type"
    t.bigint "source_id"
    t.bigint "user_id"
    t.bigint "creator_id"
    t.bigint "qrcode_id"
    t.string "type", comment: "STI属性"
    t.string "state", comment: "状态"
    t.datetime "register_at", comment: "扫码时间"
    t.jsonb "payload", comment: "额外字段"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_reg_records_on_app_id"
    t.index ["creator_id"], name: "index_reg_records_on_creator_id"
    t.index ["origin_type", "origin_id"], name: "index_reg_records_on_origin"
    t.index ["qrcode_id"], name: "index_reg_records_on_qrcode_id"
    t.index ["source_type", "source_id"], name: "index_reg_records_on_source"
    t.index ["user_id"], name: "index_reg_records_on_user_id"
  end

  create_table "region_area_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id", "action_flag"], name: "region_area_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_region_area_actions_on_app_id"
    t.index ["real_user_id"], name: "index_region_area_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "region_area_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_region_area_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "region_area_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_region_area_actions_on_user"
  end

  create_table "region_areas", force: :cascade do |t|
    t.string "ancestry", comment: "树形结构"
    t.integer "depth", comment: "树结构深度"
    t.integer "children_count", comment: "子对象的数据"
    t.string "type", comment: "STI"
    t.string "code", comment: "代码"
    t.string "name", comment: "名称"
    t.string "short_name", comment: "简称"
    t.string "name_en", comment: "拼音简称"
    t.string "name_abbr", comment: "拼音缩写"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "res_book_relations", force: :cascade do |t|
    t.bigint "book_id"
    t.string "source_type"
    t.bigint "source_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["book_id"], name: "index_res_book_relations_on_book_id"
    t.index ["source_type", "source_id"], name: "index_res_book_relations_on_source"
  end

  create_table "res_books", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "名称"
    t.text "desc", comment: "说明"
    t.string "relation_type", comment: "通讯录的类型"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_res_books_on_app_id"
    t.index ["user_id"], name: "index_res_books_on_user_id"
  end

  create_table "res_member_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id", "action_flag"], name: "res_member_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_res_member_actions_on_app_id"
    t.index ["real_user_id"], name: "index_res_member_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "res_member_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_res_member_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "res_member_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_res_member_actions_on_user"
  end

  create_table "res_permit_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id", "action_flag"], name: "res_permit_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_res_permit_actions_on_app_id"
    t.index ["real_user_id"], name: "index_res_permit_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "res_permit_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_res_permit_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "res_permit_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_res_permit_actions_on_user"
  end

  create_table "res_tags", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "org_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "标签名称"
    t.string "color", comment: "标签颜色"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_res_tags_on_app_id"
    t.index ["org_id"], name: "index_res_tags_on_org_id"
  end

  create_table "res_tags_relations", force: :cascade do |t|
    t.bigint "tag_id"
    t.bigint "user_id"
    t.bigint "org_id"
    t.bigint "member_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["member_id"], name: "index_res_tags_relations_on_member_id"
    t.index ["org_id"], name: "index_res_tags_relations_on_org_id"
    t.index ["tag_id"], name: "index_res_tags_relations_on_tag_id"
    t.index ["user_id"], name: "index_res_tags_relations_on_user_id"
  end

  create_table "res_user_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id", "action_flag"], name: "res_user_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_res_user_actions_on_app_id"
    t.index ["real_user_id"], name: "index_res_user_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "res_user_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_res_user_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "res_user_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_res_user_actions_on_user"
  end

  create_table "role_permission_relations", force: :cascade do |t|
    t.bigint "role_id"
    t.string "permission_type"
    t.bigint "permission_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["permission_type", "permission_id"], name: "index_role_permission_relations_on_permission"
    t.index ["role_id"], name: "index_role_permission_relations_on_role_id"
  end

  create_table "role_permit_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id", "action_flag"], name: "role_permit_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_role_permit_actions_on_app_id"
    t.index ["real_user_id"], name: "index_role_permit_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "role_permit_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_role_permit_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "role_permit_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_role_permit_actions_on_user"
  end

  create_table "roles", force: :cascade do |t|
    t.string "resource_type"
    t.bigint "resource_id"
    t.bigint "mod_id"
    t.string "name", comment: "权限标识"
    t.string "label", comment: "显示名称"
    t.string "pinyin", comment: "拼音,排序用"
    t.jsonb "permits", comment: "权限设置"
    t.boolean "permit_enable", comment: "是否开启permit的按钮"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["mod_id"], name: "index_roles_on_mod_id"
    t.index ["resource_type", "resource_id"], name: "index_roles_on_resource"
  end

  create_table "route_settings", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "mod_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "module名称"
    t.jsonb "conf", comment: "module导出路由"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_route_settings_on_app_id"
    t.index ["mod_id"], name: "index_route_settings_on_mod_id"
  end

  create_table "schedule_rule_records", force: :cascade do |t|
    t.string "rule_type"
    t.bigint "rule_id"
    t.string "type", comment: "STI属性"
    t.datetime "schedule_next_time", comment: "下个周期时间"
    t.datetime "schedule_offset_at", comment: "实际执行时间（考虑时间偏移）"
    t.datetime "schedule_occurred_at", comment: "原始调度时间"
    t.string "schedule_uuid", comment: "规则schedule val小项的uuid"
    t.jsonb "schedule_data", comment: "存储具体规则的内容"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["rule_type", "rule_id"], name: "index_schedule_rule_records_on_rule"
  end

  create_table "schedule_rules", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "creator_id"
    t.jsonb "rule_conf", comment: "规则配置"
    t.string "rule_record_type", comment: "rule_record 的 STI 类型"
    t.string "uuid", comment: "规则唯一标识"
    t.string "name", comment: "规则名称"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_schedule_rules_on_app_id"
    t.index ["creator_id"], name: "index_schedule_rules_on_creator_id"
  end

  create_table "serve_activities", force: :cascade do |t|
    t.string "source_type"
    t.bigint "source_id"
    t.bigint "app_id"
    t.bigint "submodule_id"
    t.bigint "creator_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "name", comment: "名称"
    t.string "create_instance_state", comment: "创建工作流的状态"
    t.datetime "create_instance_timestamp", comment: "创建工作流的操作时间"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "type", comment: "STI属性"
    t.datetime "effective_at", comment: "生效时间"
    t.datetime "invalid_at", comment: "失效时间"
    t.datetime "hotted_at"
    t.boolean "is_hotted"
    t.jsonb "views", comment: "权限设置"
    t.boolean "view_enable", comment: "是否开启permit的按钮"
    t.jsonb "uses", comment: "权限设置"
    t.boolean "use_enable", comment: "是否开启permit的按钮"
    t.jsonb "manages", comment: "权限设置"
    t.boolean "manage_enable", comment: "是否开启permit的按钮"
    t.string "state"
    t.jsonb "cover_image", comment: "封面图"
    t.jsonb "icon", comment: "icon"
    t.jsonb "attachments", comment: "附件"
    t.integer "position", comment: "排序"
    t.jsonb "content", comment: "详情，body / images / video"
    t.text "address", comment: "地址"
    t.jsonb "layout", comment: "卡片样式"
    t.integer "views_count", comment: "浏览数量"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.text "ai_summary", comment: "AI 生成的摘要"
    t.vector "ai_summary_embedding"
    t.bigint "region_area_id"
    t.string "province", comment: "省"
    t.string "city", comment: "市"
    t.string "district", comment: "区"
    t.bigint "origin_id"
    t.datetime "published_at", comment: "发布时间"
    t.index ["app_id"], name: "index_serve_activities_on_app_id"
    t.index ["creator_id"], name: "index_serve_activities_on_creator_id"
    t.index ["origin_id"], name: "index_serve_activities_on_origin_id"
    t.index ["region_area_id"], name: "index_serve_activities_on_region_area_id"
    t.index ["source_type", "source_id"], name: "index_serve_activities_on_source"
    t.index ["submodule_id"], name: "index_serve_activities_on_submodule_id"
    t.index ["target_type", "target_id"], name: "index_serve_activities_on_target"
  end

  create_table "serve_activity_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id", "action_flag"], name: "serve_activity_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_serve_activity_actions_on_app_id"
    t.index ["real_user_id"], name: "index_serve_activity_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "serve_activity_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_serve_activity_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "serve_activity_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_serve_activity_actions_on_user"
  end

  create_table "serve_ai_messages", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "creator_id"
    t.bigint "org_id"
    t.bigint "pack_id"
    t.bigint "rule_id"
    t.bigint "ref_ai_message_id"
    t.string "type", comment: "STI属性"
    t.string "seq", comment: "编号"
    t.string "name", comment: "名称"
    t.string "state"
    t.text "content", comment: "内容"
    t.jsonb "option", comment: "配置"
    t.jsonb "payload", comment: "其他字段"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "likes_count", comment: "点赞人数"
    t.integer "stars_count", comment: "收藏人数"
    t.datetime "hotted_at"
    t.boolean "is_hotted"
    t.integer "position", comment: "排序"
    t.index ["app_id"], name: "index_serve_ai_messages_on_app_id"
    t.index ["creator_id"], name: "index_serve_ai_messages_on_creator_id"
    t.index ["org_id"], name: "index_serve_ai_messages_on_org_id"
    t.index ["pack_id"], name: "index_serve_ai_messages_on_pack_id"
    t.index ["ref_ai_message_id"], name: "index_serve_ai_messages_on_ref_ai_message_id"
    t.index ["rule_id"], name: "index_serve_ai_messages_on_rule_id"
  end

  create_table "serve_banners", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "submodule_id"
    t.string "source_type"
    t.bigint "source_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.datetime "effective_at", comment: "生效时间"
    t.datetime "invalid_at", comment: "失效时间"
    t.datetime "published_at"
    t.boolean "is_published"
    t.string "name", comment: "轮播图名称"
    t.integer "position", comment: "位置"
    t.jsonb "cover_image", comment: "封面图"
    t.jsonb "conf", comment: "呈现方式，根据前端需要设置，例如可以是PC或者Mobile的展现等"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_serve_banners_on_app_id"
    t.index ["source_type", "source_id"], name: "index_serve_banners_on_source"
    t.index ["submodule_id"], name: "index_serve_banners_on_submodule_id"
  end

  create_table "serve_bid_items", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "bid_project_id"
    t.string "seq", comment: "编号"
    t.string "code", comment: "项目编号"
    t.datetime "setup_at", comment: "评标时间"
    t.string "connector", comment: "联系人"
    t.string "legaler_name", comment: "法人"
    t.decimal "amount", comment: "金额"
    t.jsonb "payload", comment: "扩展字段"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_serve_bid_items_on_app_id"
    t.index ["bid_project_id"], name: "index_serve_bid_items_on_bid_project_id"
  end

  create_table "serve_bid_notices", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "bid_project_id"
    t.string "seq", comment: "编号"
    t.datetime "start_at", comment: "生效时间"
    t.datetime "end_at", comment: "失效时间"
    t.string "code", comment: "项目编号"
    t.string "state"
    t.datetime "open_at", comment: "开标时间"
    t.decimal "amount", comment: "金额"
    t.jsonb "payload", comment: "扩展字段"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_serve_bid_notices_on_app_id"
    t.index ["bid_project_id"], name: "index_serve_bid_notices_on_bid_project_id"
  end

  create_table "serve_bid_projects", force: :cascade do |t|
    t.bigint "region_area_id"
    t.bigint "app_id"
    t.bigint "org_id"
    t.bigint "contactor_id"
    t.bigint "manager_id"
    t.string "province", comment: "省"
    t.string "city", comment: "市"
    t.string "district", comment: "区"
    t.datetime "start_at", comment: "生效时间"
    t.datetime "end_at", comment: "失效时间"
    t.string "code", comment: "项目编号"
    t.string "name", comment: "标题"
    t.string "state"
    t.jsonb "content", comment: "内容"
    t.string "region_code", comment: "地区编码"
    t.datetime "setup_at", comment: "创建时间"
    t.datetime "open_at", comment: "开标时间"
    t.string "manager_name", comment: "负责人"
    t.string "contactor_name", comment: "联系人"
    t.string "phone", comment: "联系电话"
    t.string "unit", comment: "招标单位"
    t.string "unit_code", comment: "招标单位编号"
    t.string "send_state", comment: "发送消息状态"
    t.decimal "amount", comment: "金额"
    t.jsonb "meta", comment: "元数据"
    t.jsonb "payload", comment: "扩展字段"
    t.jsonb "attachments", comment: "附件"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "legaler_id"
    t.string "legaler_name", comment: "项目法人"
    t.string "seq", comment: "编号"
    t.datetime "register_at", comment: "登记时间"
    t.datetime "win_at", comment: "中标时间"
    t.index ["app_id"], name: "index_serve_bid_projects_on_app_id"
    t.index ["contactor_id"], name: "index_serve_bid_projects_on_contactor_id"
    t.index ["legaler_id"], name: "index_serve_bid_projects_on_legaler_id"
    t.index ["manager_id"], name: "index_serve_bid_projects_on_manager_id"
    t.index ["org_id"], name: "index_serve_bid_projects_on_org_id"
    t.index ["region_area_id"], name: "index_serve_bid_projects_on_region_area_id"
  end

  create_table "serve_bid_results", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "bid_project_id"
    t.string "seq", comment: "编号"
    t.string "code", comment: "项目编号"
    t.string "state"
    t.datetime "win_at", comment: "中标时间"
    t.string "unit_code", comment: "中标单位代码"
    t.string "unit", comment: "中标单位"
    t.decimal "amount", comment: "金额"
    t.jsonb "payload", comment: "额外字段"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_serve_bid_results_on_app_id"
    t.index ["bid_project_id"], name: "index_serve_bid_results_on_bid_project_id"
  end

  create_table "serve_bid_tenders", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "bid_project_id"
    t.string "seq", comment: "编号"
    t.string "code", comment: "项目编号"
    t.datetime "bid_at", comment: "评标时间"
    t.string "bid_manager_name", comment: "项目负责人姓名"
    t.decimal "amount", comment: "金额"
    t.jsonb "payload", comment: "扩展字段"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_serve_bid_tenders_on_app_id"
    t.index ["bid_project_id"], name: "index_serve_bid_tenders_on_bid_project_id"
  end

  create_table "serve_birthdays", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "org_id"
    t.string "name", comment: "标题"
    t.jsonb "payload", comment: "扩展字段"
    t.date "start_at", comment: "开始时间"
    t.date "end_at", comment: "结束时间"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_serve_birthdays_on_app_id"
    t.index ["org_id"], name: "index_serve_birthdays_on_org_id"
  end

  create_table "serve_catalogs", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "submodule_id"
    t.bigint "creator_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.jsonb "views", comment: "权限设置"
    t.boolean "view_enable", comment: "是否开启permit的按钮"
    t.jsonb "uses", comment: "权限设置"
    t.boolean "use_enable", comment: "是否开启permit的按钮"
    t.jsonb "manages", comment: "权限设置"
    t.boolean "manage_enable", comment: "是否开启permit的按钮"
    t.string "name", comment: "名称"
    t.string "state", comment: "状态"
    t.jsonb "cover_image", comment: "封面图"
    t.jsonb "icon", comment: "icon"
    t.integer "position", comment: "排序"
    t.jsonb "layout", comment: "布局方式配置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_serve_catalogs_on_app_id"
    t.index ["creator_id"], name: "index_serve_catalogs_on_creator_id"
    t.index ["submodule_id"], name: "index_serve_catalogs_on_submodule_id"
    t.index ["target_type", "target_id"], name: "index_serve_catalogs_on_target"
  end

  create_table "serve_content_type_tags", force: :cascade do |t|
    t.bigint "app_id"
    t.string "name", comment: "名称"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_serve_content_type_tags_on_app_id"
  end

  create_table "serve_duty_changes", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "user_id"
    t.bigint "department_id"
    t.bigint "original_department_id"
    t.string "create_instance_state", comment: "创建工作流的状态"
    t.datetime "create_instance_timestamp", comment: "创建工作流的操作时间"
    t.string "duty", comment: "当前的岗位"
    t.string "duty_level", comment: "当前职级"
    t.string "original_duty", comment: "之前的岗位"
    t.string "original_duty_level", comment: "之前职级"
    t.datetime "change_date", comment: "变更时间"
    t.string "reason", comment: "原因"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "payload", comment: "扩展字段"
    t.string "state"
    t.index ["app_id"], name: "index_serve_duty_changes_on_app_id"
    t.index ["department_id"], name: "index_serve_duty_changes_on_department_id"
    t.index ["original_department_id"], name: "index_serve_duty_changes_on_original_department_id"
    t.index ["user_id"], name: "index_serve_duty_changes_on_user_id"
  end

  create_table "serve_entries", force: :cascade do |t|
    t.string "source_type"
    t.bigint "source_id"
    t.bigint "app_id"
    t.bigint "submodule_id"
    t.bigint "activity_id"
    t.bigint "user_id"
    t.string "name", comment: "名称"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "type", comment: "STI属性"
    t.string "state"
    t.datetime "order_at", comment: "报名时间"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["activity_id"], name: "index_serve_entries_on_activity_id"
    t.index ["app_id"], name: "index_serve_entries_on_app_id"
    t.index ["source_type", "source_id"], name: "index_serve_entries_on_source"
    t.index ["submodule_id"], name: "index_serve_entries_on_submodule_id"
    t.index ["user_id"], name: "index_serve_entries_on_user_id"
  end

  create_table "serve_groups", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "submodule_id"
    t.string "type", comment: "STI属性"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "名称"
    t.string "state", comment: "状态"
    t.integer "position", comment: "排序"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_serve_groups_on_app_id"
    t.index ["submodule_id"], name: "index_serve_groups_on_submodule_id"
  end

  create_table "serve_manage_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id", "action_flag"], name: "serve_manage_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_serve_manage_actions_on_app_id"
    t.index ["real_user_id"], name: "index_serve_manage_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "serve_manage_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_serve_manage_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "serve_manage_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_serve_manage_actions_on_user"
  end

  create_table "serve_message_templates", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "rule_id"
    t.bigint "rule_item_id"
    t.string "type", comment: "STI属性"
    t.string "name", comment: "名称"
    t.string "state", comment: "状态"
    t.jsonb "payload", comment: "其他数据"
    t.jsonb "option", comment: "配置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_serve_message_templates_on_app_id"
    t.index ["rule_id"], name: "index_serve_message_templates_on_rule_id"
    t.index ["rule_item_id"], name: "index_serve_message_templates_on_rule_item_id"
  end

  create_table "serve_messages", force: :cascade do |t|
    t.bigint "app_id"
    t.string "notifyable_type"
    t.bigint "notifyable_id"
    t.bigint "user_id"
    t.string "seq", comment: "编号"
    t.string "type", comment: "STI属性"
    t.datetime "read_at"
    t.boolean "is_read"
    t.string "name", comment: "名称"
    t.string "state", comment: "状态"
    t.datetime "send_at", comment: "发送时间"
    t.jsonb "body", comment: "发送信息内容"
    t.jsonb "response", comment: "发送结果"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "sender_id"
    t.bigint "pack_id"
    t.text "content", comment: "内容"
    t.bigint "rule_id"
    t.bigint "activity_id"
    t.jsonb "payload", comment: "其他内容"
    t.bigint "ai_message_id"
    t.bigint "org_id"
    t.string "content_uuid", comment: "内容UUID，用于去重判断"
    t.index ["activity_id"], name: "index_serve_messages_on_activity_id"
    t.index ["ai_message_id"], name: "index_serve_messages_on_ai_message_id"
    t.index ["app_id"], name: "index_serve_messages_on_app_id"
    t.index ["content_uuid"], name: "idx_serve_messages_content_uuid"
    t.index ["notifyable_type", "notifyable_id"], name: "index_serve_messages_on_notifyable"
    t.index ["org_id"], name: "index_serve_messages_on_org_id"
    t.index ["pack_id"], name: "index_serve_messages_on_pack_id"
    t.index ["rule_id"], name: "index_serve_messages_on_rule_id"
    t.index ["sender_id"], name: "index_serve_messages_on_sender_id"
    t.index ["user_id", "content_uuid"], name: "idx_serve_messages_user_content_uuid"
    t.index ["user_id"], name: "index_serve_messages_on_user_id"
  end

  create_table "serve_org_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id", "action_flag"], name: "serve_org_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_serve_org_actions_on_app_id"
    t.index ["real_user_id"], name: "index_serve_org_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "serve_org_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_serve_org_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "serve_org_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_serve_org_actions_on_user"
  end

  create_table "serve_origins", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "org_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "type", comment: "STI属性"
    t.string "name", comment: "名称"
    t.string "state", comment: "状态"
    t.string "code", comment: "标识"
    t.integer "position", comment: "排序"
    t.jsonb "payload", comment: "其他字段"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "submodule_id"
    t.string "mode", comment: "分类"
    t.datetime "latest_send_at", comment: "最新来源时间"
    t.index ["app_id"], name: "index_serve_origins_on_app_id"
    t.index ["org_id"], name: "index_serve_origins_on_org_id"
    t.index ["submodule_id"], name: "index_serve_origins_on_submodule_id"
  end

  create_table "serve_packs", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "rule_id"
    t.bigint "activity_id"
    t.string "create_instance_state", comment: "创建工作流的状态"
    t.datetime "create_instance_timestamp", comment: "创建工作流的操作时间"
    t.string "type", comment: "STI属性"
    t.string "name", comment: "名称"
    t.string "state"
    t.string "seq", comment: "标识"
    t.integer "period", comment: "周期"
    t.datetime "operate_at", comment: "操作时间"
    t.datetime "send_at", comment: "发送时间"
    t.jsonb "payload", comment: "其他信息"
    t.jsonb "option", comment: "配置信息"
    t.integer "position", comment: "排序"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "creator_id"
    t.bigint "org_id"
    t.bigint "rule_record_id"
    t.string "source_type"
    t.bigint "source_id"
    t.string "flag", comment: "标识"
    t.string "message_type", comment: "消息类型"
    t.bigint "tanent_id"
    t.bigint "rule_item_id"
    t.text "content", comment: "消息内容"
    t.index ["activity_id"], name: "index_serve_packs_on_activity_id"
    t.index ["app_id"], name: "index_serve_packs_on_app_id"
    t.index ["creator_id"], name: "index_serve_packs_on_creator_id"
    t.index ["org_id"], name: "index_serve_packs_on_org_id"
    t.index ["rule_id"], name: "index_serve_packs_on_rule_id"
    t.index ["rule_item_id"], name: "index_serve_packs_on_rule_item_id"
    t.index ["rule_record_id"], name: "index_serve_packs_on_rule_record_id"
    t.index ["source_type", "source_id"], name: "index_serve_packs_on_source"
    t.index ["tanent_id"], name: "index_serve_packs_on_tanent_id"
  end

  create_table "serve_permit_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id", "action_flag"], name: "serve_permit_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_serve_permit_actions_on_app_id"
    t.index ["real_user_id"], name: "index_serve_permit_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "serve_permit_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_serve_permit_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "serve_permit_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_serve_permit_actions_on_user"
  end

  create_table "serve_receivers", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "user_id"
    t.bigint "pack_id"
    t.string "state", comment: "状态"
    t.jsonb "payload", comment: "其他字段"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_serve_receivers_on_app_id"
    t.index ["pack_id"], name: "index_serve_receivers_on_pack_id"
    t.index ["user_id"], name: "index_serve_receivers_on_user_id"
  end

  create_table "serve_rule_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id", "action_flag"], name: "serve_rule_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_serve_rule_actions_on_app_id"
    t.index ["real_user_id"], name: "index_serve_rule_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "serve_rule_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_serve_rule_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "serve_rule_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_serve_rule_actions_on_user"
  end

  create_table "serve_rule_groups", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "submodule_id"
    t.string "name", comment: "名称"
    t.string "state", comment: "状态"
    t.integer "position", comment: "排序"
    t.jsonb "payload", comment: "其他字段"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_serve_rule_groups_on_app_id"
    t.index ["submodule_id"], name: "index_serve_rule_groups_on_submodule_id"
  end

  create_table "serve_rule_items", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "rule_id"
    t.string "name", comment: "名称"
    t.string "state", comment: "状态"
    t.jsonb "option", comment: "配置"
    t.jsonb "payload", comment: "其他字段"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "uuid", comment: "唯一标识号"
    t.string "type", comment: "STI属性"
    t.string "mode", comment: "标识"
    t.index ["app_id"], name: "index_serve_rule_items_on_app_id"
    t.index ["rule_id"], name: "index_serve_rule_items_on_rule_id"
  end

  create_table "serve_rules", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "creator_id"
    t.string "type", comment: "STI属性"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.datetime "effective_at", comment: "生效时间"
    t.datetime "invalid_at", comment: "失效时间"
    t.string "name", comment: "名称"
    t.string "state"
    t.integer "position", comment: "排序"
    t.jsonb "options", comment: "配置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "batch_no", comment: "批次"
    t.string "code", comment: "编号"
    t.datetime "latest_send_at", comment: "最新发送时间"
    t.bigint "catalog_id"
    t.jsonb "rule_conf", comment: "规则配置"
    t.string "rule_record_type", comment: "rule_record 的 STI 类型"
    t.string "message_type", comment: "消息类型"
    t.bigint "rule_group_id"
    t.jsonb "content", comment: "内容"
    t.text "description", comment: "工具描述"
    t.index ["app_id"], name: "index_serve_rules_on_app_id"
    t.index ["catalog_id"], name: "index_serve_rules_on_catalog_id"
    t.index ["creator_id"], name: "index_serve_rules_on_creator_id"
    t.index ["rule_group_id"], name: "index_serve_rules_on_rule_group_id"
  end

  create_table "serve_submodules", force: :cascade do |t|
    t.bigint "app_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "ancestry", comment: "树形结构"
    t.integer "depth", comment: "树结构深度"
    t.integer "children_count", comment: "子对象的数据"
    t.jsonb "manages", comment: "权限设置"
    t.boolean "manage_enable", comment: "是否开启permit的按钮"
    t.string "name", comment: "名称"
    t.string "state", comment: "状态"
    t.jsonb "cover_image", comment: "封面图"
    t.integer "position", comment: "排序"
    t.string "key", comment: "程序内使用的标识"
    t.jsonb "layout", comment: "布局方式配置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_serve_submodules_on_app_id"
  end

  create_table "serve_tags", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "submodule_id"
    t.string "name", comment: "名称"
    t.string "color", comment: "颜色"
    t.integer "position", comment: "排序"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "option", comment: "配置项"
    t.integer "activity_count", comment: "活动数量"
    t.index ["app_id"], name: "index_serve_tags_on_app_id"
    t.index ["submodule_id"], name: "index_serve_tags_on_submodule_id"
  end

  create_table "serve_tanent_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id", "action_flag"], name: "serve_tanent_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_serve_tanent_actions_on_app_id"
    t.index ["real_user_id"], name: "index_serve_tanent_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "serve_tanent_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_serve_tanent_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "serve_tanent_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_serve_tanent_actions_on_user"
  end

  create_table "soa_auth_auth_accounts", force: :cascade do |t|
    t.bigint "user_id"
    t.string "app_id", comment: "对应的app code"
    t.string "account", comment: "对应的app code"
    t.string "password", comment: "加密后的密码"
    t.string "account_type", comment: "用户类型"
    t.string "encrypt_mode", comment: "加密类型"
    t.string "account_mode", comment: "账号类型"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_soa_auth_auth_accounts_on_user_id"
  end

  create_table "soa_auth_oauths", force: :cascade do |t|
    t.bigint "user_id"
    t.bigint "auth_account_id"
    t.string "type", comment: "STI类型，例如Oauth::Wechat"
    t.string "app_id", comment: "对应的app code，解决一个第三方应用绑定多个内部应用"
    t.string "oauth_app_id", comment: "对应的应用，可以是app_id，也可以是配置的app_account"
    t.string "openid", comment: "oauth_id，在对应鉴权系统中的id"
    t.string "unionid", comment: "unionid，在跨平台中使用"
    t.jsonb "options", comment: "oauth平台额外的属性"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_soa_auth_oauths_on_app_id"
    t.index ["auth_account_id"], name: "index_soa_auth_oauths_on_auth_account_id"
    t.index ["oauth_app_id"], name: "index_soa_auth_oauths_on_oauth_app_id"
    t.index ["openid"], name: "index_soa_auth_oauths_on_openid"
    t.index ["user_id"], name: "index_soa_auth_oauths_on_user_id"
  end

  create_table "spider_origins", force: :cascade do |t|
    t.bigint "app_id"
    t.string "type", comment: "STI属性"
    t.string "name", comment: "名称"
    t.string "state"
    t.integer "position", comment: "排序"
    t.string "mode", comment: "分类"
    t.datetime "latest_send_at", comment: "最新更新时间"
    t.jsonb "payload", comment: "额外字段"
    t.jsonb "option", comment: "配置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_spider_origins_on_app_id"
  end

  create_table "tanent_permit_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id", "action_flag"], name: "tanent_permit_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_tanent_permit_actions_on_app_id"
    t.index ["real_user_id"], name: "index_tanent_permit_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "tanent_permit_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_tanent_permit_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "tanent_permit_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_tanent_permit_actions_on_user"
  end

  create_table "tanent_resources", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "tanent_id"
    t.string "resource_type"
    t.bigint "resource_id"
    t.jsonb "payload", comment: "存额外信息"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_tanent_resources_on_app_id"
    t.index ["resource_type", "resource_id"], name: "index_tanent_resources_on_resource"
    t.index ["tanent_id"], name: "index_tanent_resources_on_tanent_id"
  end

  create_table "tanents", force: :cascade do |t|
    t.bigint "app_id"
    t.string "code", comment: "租户标识"
    t.string "name", comment: "租户名称"
    t.jsonb "manages", comment: "权限设置"
    t.boolean "manage_enable", comment: "是否开启permit的按钮"
    t.jsonb "config", comment: "配置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_tanents_on_app_id"
  end

  create_table "tofu_entries", force: :cascade do |t|
    t.string "source_type"
    t.bigint "source_id"
    t.jsonb "permits", comment: "权限设置"
    t.boolean "permit_enable", comment: "是否开启permit的按钮"
    t.string "ancestry", comment: "树形结构"
    t.string "platform"
    t.string "layout", comment: "点击以后前端使用的layout"
    t.string "type", comment: "STI"
    t.string "name", comment: "名称"
    t.string "desc", comment: "描述"
    t.text "icon", comment: "显示的图片或者图标"
    t.text "url", comment: "跳转地址，如果只是menu，可以为空"
    t.string "open_mode", comment: "打开页面的方式"
    t.integer "position", comment: "位置"
    t.integer "depth", comment: "树结构深度"
    t.integer "children_count", comment: "子对象的数据"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["source_type", "source_id"], name: "index_tofu_entries_on_source"
  end

  create_table "users", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "tanent_id"
    t.bigint "ref_user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.datetime "effective_at", comment: "生效时间"
    t.datetime "invalid_at", comment: "失效时间"
    t.string "account", comment: "账号，关联登录"
    t.string "name", comment: "用户姓名"
    t.string "nickname", comment: "用户昵称"
    t.string "pinyin", comment: "用户名拼音"
    t.string "mobile", comment: "用户手机号"
    t.string "email", comment: "用户邮箱"
    t.string "gender", comment: "性别"
    t.jsonb "avatar", comment: "用户头像"
    t.string "identity_id", comment: "证件号码，需要时候可以作为唯一标识"
    t.datetime "last_visit_at", comment: "最后访问时间"
    t.datetime "blocked_at"
    t.boolean "is_blocked"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.date "birthday", comment: "生日"
    t.date "political_birthday", comment: "政治生日"
    t.string "political", comment: "政治面貌"
    t.string "education", comment: "学位"
    t.string "degree", comment: "学历"
    t.bigint "supervisor_id"
    t.index ["app_id"], name: "index_users_on_app_id"
    t.index ["ref_user_id"], name: "index_users_on_ref_user_id"
    t.index ["supervisor_id"], name: "index_users_on_supervisor_id"
    t.index ["tanent_id"], name: "index_users_on_tanent_id"
  end

  create_table "users_roles", force: :cascade do |t|
    t.bigint "user_id"
    t.bigint "role_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["role_id"], name: "index_users_roles_on_role_id"
    t.index ["user_id"], name: "index_users_roles_on_user_id"
  end

  create_table "version_relationships", force: :cascade do |t|
    t.bigint "app_id"
    t.string "resource_type"
    t.bigint "resource_id"
    t.string "real_resource_type"
    t.bigint "real_resource_id"
    t.string "version_type"
    t.bigint "version_id"
    t.string "operator_type"
    t.bigint "operator_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_version_relationships_on_app_id"
    t.index ["operator_type", "operator_id"], name: "index_version_relationships_on_operator"
    t.index ["real_resource_type", "real_resource_id"], name: "index_version_relationships_on_real_resource"
    t.index ["resource_type", "resource_id"], name: "index_version_relationships_on_resource"
    t.index ["version_type", "version_id"], name: "index_version_relationships_on_version"
  end

  add_foreign_key "dingtalk_callback_registrations", "dingtalk_clients", column: "client_id"
end
