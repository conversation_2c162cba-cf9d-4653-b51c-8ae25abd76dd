#!/usr/bin/env ruby

puts "=== 测试简化后的 DeepSeek 模式选择 ==="

# 检查 Bot::LlmFactory 是否支持两种模式
puts "\n1. 检查 Bot::LlmFactory 支持的模式:"

factory_file = 'app/services/bot/llm_factory.rb'
if File.exist?(factory_file)
  content = File.read(factory_file)
  
  if content.include?('def self.deepseek') && content.include?('def self.deepseek_r1')
    puts "✓ Bot::LlmFactory 支持两种模式"
    
    # 检查标准模式配置
    if content.match(/def self\.deepseek.*?chat_model: 'deepseek-chat'/m)
      puts "✓ 标准模式使用 deepseek-chat"
    else
      puts "✗ 标准模式配置错误"
    end
    
    # 检查R1模式配置
    if content.match(/def self\.deepseek_r1.*?chat_model: 'deepseek-r1'/m)
      puts "✓ R1模式使用 deepseek-r1"
    else
      puts "✗ R1模式配置错误"
    end
  else
    puts "✗ Bot::LlmFactory 模式配置不完整"
  end
else
  puts "✗ 未找到 Bot::LlmFactory 文件"
end

# 检查 Rule 模型的简化实现
puts "\n2. 检查 Rule 模型的简化实现:"

rule_file = 'app/models/serve/model/rule.rb'
if File.exist?(rule_file)
  content = File.read(rule_file)
  
  if content.include?('def generate_content_by_template_prompt(prompt: nil, mode: nil)')
    puts "✓ generate_content_by_template_prompt 支持 mode 参数"
  else
    puts "✗ generate_content_by_template_prompt 不支持 mode 参数"
  end
  
  if content.include?('def get_deepseek_mode_key(mode = nil)')
    puts "✓ get_deepseek_mode_key 方法存在"
  else
    puts "✗ get_deepseek_mode_key 方法不存在"
  end
  
  if content.include?('def get_deepseek_mode_info_by_key(mode_key)')
    puts "✓ get_deepseek_mode_info_by_key 方法存在"
  else
    puts "✗ get_deepseek_mode_info_by_key 方法不存在"
  end
else
  puts "✗ 未找到 Rule 模型文件"
end

# 检查 Pack 模型的更新
puts "\n3. 检查 Pack 模型的更新:"

pack_file = 'app/models/serve/model/pack.rb'
if File.exist?(pack_file)
  content = File.read(pack_file)
  
  if content.include?('def refresh_contents_by_rule(prompt: nil, mode: nil)')
    puts "✓ refresh_contents_by_rule 支持 mode 参数"
  else
    puts "✗ refresh_contents_by_rule 不支持 mode 参数"
  end
  
  if content.include?('rule.generate_content_by_template_prompt(prompt: prompt, mode: mode)')
    puts "✓ 正确传递 mode 参数到 generate_content_by_template_prompt"
  else
    puts "✗ 未正确传递 mode 参数"
  end
else
  puts "✗ 未找到 Pack 模型文件"
end

# 检查控制器的更新
puts "\n4. 检查控制器的更新:"

controller_file = 'app/controllers/serve/user/packs_controller.rb'
if File.exist?(controller_file)
  content = File.read(controller_file)
  
  if content.include?('mode: params[:mode]')
    puts "✓ 控制器接收 mode 参数"
  else
    puts "✗ 控制器未接收 mode 参数"
  end
else
  puts "✗ 未找到控制器文件"
end

puts "\n=== 测试总结 ==="
puts "简化方案的核心流程："
puts "1. 前端选择模式 → 传递 mode 参数到 refresh_contents_by_rule 接口"
puts "2. 控制器接收 mode 参数 → 传递给 Pack.refresh_contents_by_rule"
puts "3. Pack 传递 mode 参数给 Rule.generate_content_by_template_prompt"
puts "4. Rule 根据 mode 参数选择对应的 DeepSeek 模型"
puts "5. Bot::LlmFactory 创建对应的模型实例（deepseek 或 deepseek_r1）"
puts "\n优势："
puts "- 无需修改 Rule 的 options 配置"
puts "- 前端直接控制模式选择"
puts "- 实现简单，易于维护"
puts "- 保持向后兼容"
