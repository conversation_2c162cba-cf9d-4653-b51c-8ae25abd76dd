puts '=== 测试多轮对话中的素材查询功能 ==='
begin
  agent = Bot::Agent.first
  user = User.first
  
  # 创建新的对话
  conversation = agent.conversations.create!(user: user)
  Bot::Current.conversation = conversation
  
  puts "Agent: #{agent.name}"
  puts "User: #{user.name}"
  puts "Conversation: #{conversation.id}"
  
  # 定义测试查询
  queries = [
    "过去一年里有多少素材？",
    "上个月有多少？", 
    "前3个月有多少素材呢？",
    "今年开始有多少新素材？"
  ]
  
  queries.each_with_index do |query, index|
    puts "\n" + "="*60
    puts "第#{index + 1}次查询: #{query}"
    puts "="*60
    
    # 执行查询
    response = agent.chat(query, user: user, conversation_id: conversation.id)
    
    puts "\n--- 响应分析 ---"
    puts "响应类型: #{response.class}"
    puts "消息数量: #{response[:messages].count}"
    
    # 分析每个消息
    text_messages = response[:messages].select { |msg| msg[:content_type] == 'text' }
    artifact_messages = response[:messages].select { |msg| msg[:content_type] == 'artifact' }
    
    puts "\n文本消息:"
    text_messages.each_with_index do |msg, i|
      content = msg[:content]
      puts "  #{i + 1}. #{content[0..100]}#{content.length > 100 ? '...' : ''}"
      
      # 检查是否包含分类统计
      has_classification = content.include?('文字') && content.include?('图片') && content.include?('视频')
      puts "     ✅ 包含分类统计" if has_classification
      puts "     ❌ 缺少分类统计" unless has_classification
    end
    
    puts "\nArtifact消息:"
    if artifact_messages.any?
      artifact_messages.each_with_index do |msg, i|
        artifact = msg[:content]
        puts "  #{i + 1}. Artifact ID: #{artifact['id']}"
        puts "     Tool: #{artifact['tool_cname']}"
        puts "     Function: #{artifact['tool_function']}"
        puts "     ✅ 组件应该显示"
      end
    else
      puts "  ❌ 没有Artifact，组件不会显示"
    end
    
    # 总结本轮结果
    has_classification = text_messages.any? { |msg| 
      content = msg[:content]
      content.include?('文字') && content.include?('图片') && content.include?('视频')
    }
    has_artifact = artifact_messages.any?
    
    puts "\n--- 本轮总结 ---"
    puts "分类统计: #{has_classification ? '✅ 有' : '❌ 无'}"
    puts "组件显示: #{has_artifact ? '✅ 有' : '❌ 无'}"
    
    if has_classification && has_artifact
      puts "状态: 🎉 完全正常"
    elsif has_artifact
      puts "状态: ⚠️ 部分失效（无分类统计）"
    else
      puts "状态: ❌ 完全失效"
    end
    
    # 等待一下，模拟真实对话间隔
    sleep(1)
  end
  
  puts "\n" + "="*60
  puts "测试完成"
  puts "="*60
  
rescue StandardError => e
  puts "❌ 失败: #{e.message}"
  puts e.backtrace.first(10)
ensure
  Bot::Current.conversation = nil
end
