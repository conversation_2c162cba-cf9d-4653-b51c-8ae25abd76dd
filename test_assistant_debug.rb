puts '=== 调试Langchain::Assistant的add_message_and_run!方法 ==='
begin
  agent = Bot::Agent.first
  user = User.first
  
  puts "Agent: #{agent.name}"
  puts "User: #{user.name}"
  
  # 初始化assistant
  assistant = agent.initialize_assistant
  llm = assistant.instance_variable_get(:@llm)
  
  puts "\n=== Assistant配置检查 ==="
  puts "LLM类型: #{llm.class}"
  puts "工具数量: #{assistant.tools.count}"
  puts "Tool choice: #{assistant.instance_variable_get(:@tool_choice)}"
  puts "Instructions长度: #{assistant.instructions.length}字符"
  
  # 检查assistant的内部状态
  puts "\n=== Assistant内部状态 ==="
  puts "Messages数量: #{assistant.messages.count}"
  puts "Thread ID: #{assistant.instance_variable_get(:@thread_id)}"
  
  # 测试add_message_and_run!方法
  puts "\n=== 测试add_message_and_run!方法 ==="
  message = '过去一年里有多少素材？'
  puts "测试消息: #{message}"
  
  # 记录调用前状态
  initial_message_count = assistant.messages.count
  puts "调用前消息数量: #{initial_message_count}"
  
  # 添加调试钩子到LLM
  original_chat_method = llm.method(:chat)
  llm.define_singleton_method(:chat) do |**params|
    puts "\n=== LLM.chat被调用 ==="
    puts "参数keys: #{params.keys}"
    puts "messages数量: #{params[:messages]&.count || 0}"
    puts "tools数量: #{params[:tools]&.count || 0}"
    puts "tool_choice: #{params[:tool_choice]}"
    puts "parallel_tool_calls: #{params[:parallel_tool_calls]}"
    
    # 调用原始方法
    result = original_chat_method.call(**params)
    
    puts "响应类型: #{result.class}"
    if result.respond_to?(:tool_calls) && result.tool_calls.present?
      puts "✅ 响应包含工具调用!"
      result.tool_calls.each do |call|
        puts "  工具: #{call.dig('function', 'name')}"
        puts "  参数: #{call.dig('function', 'arguments')}"
      end
    else
      puts "❌ 响应不包含工具调用"
    end
    
    result
  end
  
  # 执行add_message_and_run!
  response = assistant.add_message_and_run!(content: message)
  
  # 检查结果
  puts "\n=== 执行结果 ==="
  final_message_count = assistant.messages.count
  puts "调用后消息数量: #{final_message_count}"
  puts "新增消息数量: #{final_message_count - initial_message_count}"
  
  # 检查最后几条消息
  puts "\n=== 最后的消息 ==="
  assistant.messages.last(3).each_with_index do |msg, index|
    puts "消息 #{index + 1}: role=#{msg.role}"
    if msg.tool_calls.present?
      puts "  ✅ 包含工具调用!"
      msg.tool_calls.each do |call|
        puts "    工具: #{call['function']['name']}"
        puts "    参数: #{call['function']['arguments']}"
      end
    else
      puts "  内容: #{msg.content[0..100] if msg.content}..."
    end
  end
  
  puts "\n=== 响应对象检查 ==="
  puts "响应类型: #{response.class}"
  puts "响应内容: #{response.inspect[0..200]}..."
  
rescue StandardError => e
  puts "❌ 失败: #{e.message}"
  puts e.backtrace.first(10)
end
