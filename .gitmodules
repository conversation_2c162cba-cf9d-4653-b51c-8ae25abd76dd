[submodule "src/lib/vails"]
	path = src/lib/vails
	url = ******************:ta-vue/vails.git
[submodule "src/components/global/ta-component"]
	path = src/components/global/ta-component
	url = ******************:ta-vue/ta-component.git
[submodule "src/engines/com"]
	path = src/engines/com
	url = ******************:ta-vue/com.git
[submodule "src/engines/res"]
	path = src/engines/res
	url = ******************:ta-vue/res.git
[submodule "src/engines/bpm"]
	path = src/engines/bpm
	url = ******************:ta-vue/bpm.git
[submodule "src/engines/tofu"]
	path = src/engines/tofu
	url = ******************:ta-vue/tofu.git
[submodule "src/engines/permit"]
	path = src/engines/permit
	url = ******************:ta-vue/permit.git
[submodule "src/engines/login"]
	path = src/engines/login
	url = ******************:ta-vue/login.git
[submodule "src/engines/notify"]
	path = src/engines/notify
	url = ******************:ta-vue/notify.git
[submodule "src/engines/base"]
	path = src/engines/base
	url = ******************:ta-vue/base.git
[submodule "src/engines/iest"]
	path = src/engines/iest
	url = ******************:ta-vue/iest.git
[submodule "src/engines/screen"]
	path = src/engines/screen
	url = ******************:ta-vue/screen.git
[submodule "src/engines/dingtalk"]
	path = src/engines/dingtalk
	url = ******************:ta-vue/dingtalk.git
[submodule "src/engines/chat"]
	path = src/engines/chat
	url = ******************:ta-vue/chat.git
[submodule "src/engines/opm"]
	path = src/engines/opm
	url = ******************:ta-vue/opm.git
[submodule "src/engines/assessment"]
	path = src/engines/assessment
	url = ******************:ta-vue/assessment.git
[submodule "src/engines/bot"]
	path = src/engines/bot
	url = ******************:ta-vue/bot.git
