# Add your own tasks in files placed in lib/tasks ending in .rake,
# for example lib/tasks/capistrano.rake, and they will automatically be available to Rake.

require_relative "config/application"

Rails.application.load_tasks

task rubocop: :environment do
  sh "rubocop"
end

task scp: :environment do
  sh "scp ./swagger/v1/zj_iest_api.json <EMAIL>:/home/<USER>/swagger-ui/dist/api/"
end

task annotate: :environment do
  sh "annotate -i"
  sh "annotate -i --with-comment"
end

task bundle: :environment do
  sh "bundle config set --local without 'development test'"
  sh "bundle install"
end

task start: :environment do
  sh "RAILS_ENV=production bundle exec puma -C config/puma.rb"
end

task bin: :environment do
  sh "rake app:update:bin"
end
