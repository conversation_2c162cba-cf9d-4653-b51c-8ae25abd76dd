puts '=== 测试工具返回的数据格式 ==='
begin
  agent = Bot::Agent.first
  user = User.first
  
  # 设置Bot::Current.conversation
  conversation = agent.conversations.first || agent.conversations.create!(user: user)
  Bot::Current.conversation = conversation
  
  puts "Agent: #{agent.name}"
  puts "Conversation: #{conversation.id}"
  
  # 直接调用ActivityQueryTool
  intent = agent.intents.find_by(name: '素材查询')
  tool = Bot::Tools::ActivityQueryTool.new(intent: intent)
  
  puts "\n=== 直接调用工具 ==="
  result = tool.query_activities(query: "过去一年里有多少素材？")
  
  puts "工具返回结果:"
  puts "类型: #{result.class}"
  puts "内容: #{result.inspect}"
  
  if result.is_a?(Hash)
    puts "\n详细分析:"
    puts "message: #{result[:message]}"
    puts "total_count: #{result[:total_count]}"
    puts "conditions: #{result[:conditions]}"
    
    # 检查message是否包含分类统计
    message = result[:message]
    if message.include?('文字') && message.include?('图片') && message.include?('视频')
      puts "✅ 工具返回的message包含分类统计"
    else
      puts "❌ 工具返回的message缺少分类统计"
    end
  end
  
  puts "\n=== 检查工具的build_response方法 ==="
  # 模拟一个查询来看build_response的行为
  conditions = { created_at_gteq: 1.year.ago }
  total_count = 5488
  
  # 调用build_response方法
  response = tool.send(:build_response, conditions, total_count)
  puts "build_response返回:"
  puts "类型: #{response.class}"
  puts "内容: #{response.inspect}"
  
rescue StandardError => e
  puts "❌ 失败: #{e.message}"
  puts e.backtrace.first(10)
ensure
  Bot::Current.conversation = nil
end
