puts '=== 测试工具响应内容 ==='
begin
  agent = Bot::Agent.first
  user = User.first
  
  # 设置Bot::Current.conversation
  conversation = agent.conversations.first || agent.conversations.create!(user: user)
  Bot::Current.conversation = conversation
  
  puts "Agent: #{agent.name}"
  puts "Conversation: #{conversation.id}"
  
  # 创建Bot::Assistant实例
  assistant = agent.initialize_assistant(conversation_id: conversation.id)
  
  # 重写add_message_and_run!方法来捕获工具响应
  original_add_message_and_run = assistant.method(:add_message_and_run!)
  assistant.define_singleton_method(:add_message_and_run!) do |content:|
    puts "🔍 调用add_message_and_run!，内容: #{content[0..50]}"
    
    result = original_add_message_and_run.call(content: content)
    
    puts "🔍 add_message_and_run!返回结果:"
    puts "  类型: #{result.class}"
    puts "  内容: #{result.content[0..100] if result.respond_to?(:content)}"
    
    # 检查消息历史
    puts "🔍 消息历史:"
    messages.each_with_index do |msg, index|
      puts "  消息 #{index + 1}: role=#{msg.role}"
      if msg.tool_calls.present?
        puts "    ✅ 包含工具调用!"
        msg.tool_calls.each do |call|
          puts "      工具: #{call['function']['name']}"
          puts "      ID: #{call['id']}"
        end
      elsif msg.tool_call_id.present?
        puts "    ✅ 工具响应消息!"
        puts "      tool_call_id: #{msg.tool_call_id}"
        puts "      content: #{msg.content}"
        
        # 解析工具响应
        begin
          response_data = JSON.parse(msg.content, symbolize_names: true)
          puts "      解析成功:"
          puts "        data: #{response_data[:data] ? '存在' : '不存在'}"
          puts "        artifact: #{response_data[:artifact] ? '存在' : 'null'}"
          if response_data[:artifact]
            puts "        artifact类型: #{response_data[:artifact].class}"
            puts "        artifact内容: #{response_data[:artifact].inspect[0..100]}"
          end
        rescue JSON::ParserError => e
          puts "      ❌ JSON解析失败: #{e.message}"
        end
      else
        puts "    内容: #{msg.content[0..50] if msg.content}"
      end
    end
    
    result
  end
  
  # 执行对话
  puts "\n=== 执行对话 ==="
  response = assistant.add_message_and_run!(content: "过去一年里有多少素材？")
  
  puts "\n=== 最终响应 ==="
  puts "响应类型: #{response.class}"
  puts "响应内容: #{response.content[0..100] if response.respond_to?(:content)}"
  
rescue StandardError => e
  puts "❌ 失败: #{e.message}"
  puts e.backtrace.first(10)
ensure
  Bot::Current.conversation = nil
end
