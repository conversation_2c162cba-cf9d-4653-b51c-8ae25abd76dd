image: dsh0416/tallty-env:latest
cache:
  paths:
    - .bundle
services:
  - mysql:8.0.2
  - redis:latest
before_script:
  - bundle install --path .bundle --retry 3
test:
  variables:
    RACK_ENV: test
    REDIS_ENV: ci
    MYSQL_DATABASE: zj_iest_api_test_db
    MYSQL_ROOT_PASSWORD: test
  script:
    - bundle exec rake db:drop
    - bundle exec rake db:create
    - bundle exec rake db:migrate
    - bundle exec rake spec
    - bundle exec rake rubocop

