#!/usr/bin/env ruby

# 在真实Rails环境中测试ActivityTool
puts "=== 在真实Rails环境中测试ActivityTool ==="

# 加载Rails环境
require_relative 'config/environment'

# 模拟Bot::Current.conversation
class TestConversation
  def user
    @user ||= TestUser.new
  end
end

class TestUser
  def app
    @app ||= App.first || create_test_app
  end
  
  private
  
  def create_test_app
    App.create!(
      code: "test_app_#{Time.current.to_i}",
      name: "测试应用"
    )
  end
end

# 设置测试环境
Bot::Current.conversation = TestConversation.new

puts "当前应用: #{Bot::Current.conversation.user.app.name}"
puts "素材总数: #{Bot::Current.conversation.user.app.activities.count}"

# 测试ActivityTool
tool = Bot::Tools::ActivityTool.new

puts "\n--- 测试1: 用户实际查询场景 ---"
puts "查询: '最近半年里增加了多少素材？'"

begin
  result = tool.query_activities(query: "最近半年里增加了多少素材？")
  
  puts "\n结果分析："
  puts "- 返回结构: #{result.class}"
  puts "- 包含字段: #{result.keys}"
  puts "- total_count: #{result[:total_count]}"
  puts "- message: #{result[:message]}"
  puts "- artifact存在: #{result[:artifact].present?}"
  
  if result[:artifact]
    puts "- artifact类型: #{result[:artifact][:type]}"
    puts "- artifact标题: #{result[:artifact][:title]}"
    puts "- artifact副标题: #{result[:artifact][:subtitle]}"
    puts "- 查询参数存在: #{result[:artifact][:payload][:params].present?}"
    
    if result[:artifact][:payload][:params][:q]
      puts "- 时间过滤参数: #{result[:artifact][:payload][:params][:q][:created_at_gteq].present?}"
    end
  end
  
rescue => e
  puts "❌ 测试失败: #{e.message}"
  puts "错误详情: #{e.backtrace.first(5).join("\n")}"
end

puts "\n--- 测试2: 简单数量查询 ---"
puts "查询: '素材有多少个？'"

begin
  result2 = tool.query_activities(query: "素材有多少个？")
  
  puts "\n结果分析："
  puts "- artifact存在: #{result2[:artifact].present?}"
  puts "- artifact类型: #{result2[:artifact][:type]}" if result2[:artifact]
  
rescue => e
  puts "❌ 测试失败: #{e.message}"
end

puts "\n--- 测试3: 关键词搜索 ---"
puts "查询: '搜索包含\"通知\"的素材'"

begin
  result3 = tool.query_activities(query: "搜索包含'通知'的素材")
  
  puts "\n结果分析："
  puts "- artifact存在: #{result3[:artifact].present?}"
  if result3[:artifact] && result3[:artifact][:payload][:params][:q]
    puts "- 关键词参数: #{result3[:artifact][:payload][:params][:q][:title_cont]}"
  end
  
rescue => e
  puts "❌ 测试失败: #{e.message}"
end

puts "\n=== 修复验证结果 ==="

begin
  # 验证所有查询都创建了artifact
  results = [
    tool.query_activities(query: "最近半年里增加了多少素材？"),
    tool.query_activities(query: "素材有多少个？"),
    tool.query_activities(query: "搜索包含'通知'的素材")
  ]
  
  all_have_artifacts = results.all? { |r| r[:artifact].present? }
  
  if all_have_artifacts
    puts "✅ 修复验证成功！"
    puts "   - 所有查询都创建了artifact组件"
    puts "   - 用户可以在前端看到素材查询组件"
    puts "   - 解决了'最近半年里增加了多少素材？'不显示组件的问题"
  else
    puts "❌ 修复验证失败！"
    puts "   - 仍有查询没有创建artifact组件"
    
    results.each_with_index do |result, index|
      query_names = ["最近半年里增加了多少素材？", "素材有多少个？", "搜索包含'通知'的素材"]
      puts "   - 查询#{index + 1} (#{query_names[index]}): #{result[:artifact].present? ? '✅' : '❌'}"
    end
  end
  
rescue => e
  puts "❌ 验证过程出错: #{e.message}"
end

puts "\n=== 实际用户体验模拟 ==="
puts "模拟用户在前端Bot对话界面的完整体验："

begin
  user_query = "最近半年里增加了多少素材？"
  user_result = tool.query_activities(query: user_query)
  
  puts "\n用户输入: #{user_query}"
  puts "系统回复: #{user_result[:message]}"
  
  if user_result[:artifact]
    puts "显示组件: ✅ 素材查询组件"
    puts "组件标题: #{user_result[:artifact][:title]}"
    puts "组件副标题: #{user_result[:artifact][:subtitle]}"
    puts "用户可以点击查看: ✅ 详细的素材列表"
    puts "前端分页参数: #{user_result[:artifact][:payload][:params].present? ? '✅ 已提供' : '❌ 缺失'}"
  else
    puts "显示组件: ❌ 没有组件显示"
    puts "问题: 用户只能看到文本回复，无法查看详细列表"
  end
  
rescue => e
  puts "❌ 用户体验测试失败: #{e.message}"
end

puts "\n测试完成！"
