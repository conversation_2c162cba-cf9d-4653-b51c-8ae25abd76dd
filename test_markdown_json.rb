require 'json'
require 'langchainrb'
require_relative 'lib/langchain/llm/response/deepseek_response'

# 模拟OpenAIResponse类，用于测试
module Langchain
  module LLM
    class OpenAIResponse
      attr_reader :raw_response

      def initialize(raw_response)
        @raw_response = raw_response
      end
    end
  end
end

# 模拟deepseek-r1的响应，包含Markdown代码块格式的JSON
mock_response = {
  'model' => 'deepseek-r1',
  'choices' => [
    {
      'message' => {
        'role' => 'assistant',
        'content' => "```json\n{\n  \"contents\": [\n    \"内容1\",\n    \"内容2\",\n    \"内容3\",\n    \"内容4\",\n    \"内容5\"\n  ]\n}\n```",
        'reasoning_content' => '我需要为五一节日祝福生成5条内容，每条需要包含温情和廉洁提醒。我会确保每条内容大约200字左右。'
      }
    }
  ],
  'usage' => {
    'prompt_tokens' => 100,
    'completion_tokens' => 500,
    'total_tokens' => 600
  }
}

# 创建DeepseekResponse对象
response = Langchain::LLM::DeepseekResponse.new(mock_response)

# 测试chat_completion方法
puts "=== 测试chat_completion方法 ==="
chat_completion = response.chat_completion
puts "chat_completion: #{chat_completion}"

# 解析JSON
begin
  parsed_json = JSON.parse(chat_completion)
  puts "\n=== 解析后的JSON ==="
  puts "contents: #{parsed_json['contents'].inspect}"
  puts "reasoning: #{parsed_json['reasoning']}"
rescue JSON::ParserError => e
  puts "JSON解析错误: #{e.message}"
end

# 测试原始方法
puts "\n=== 测试原始方法 ==="
puts "completion: #{response.completion}"
puts "reasoning_content: #{response.reasoning_content}"

# 测试没有Markdown格式的JSON
puts "\n=== 测试没有Markdown格式的JSON ==="
mock_response_no_markdown = {
  'model' => 'deepseek-r1',
  'choices' => [
    {
      'message' => {
        'role' => 'assistant',
        'content' => '{"contents": ["内容1", "内容2", "内容3", "内容4", "内容5"]}',
        'reasoning_content' => '这是reasoning内容'
      }
    }
  ]
}
response_no_markdown = Langchain::LLM::DeepseekResponse.new(mock_response_no_markdown)
puts "chat_completion: #{response_no_markdown.chat_completion}"

# 测试非JSON格式的内容
puts "\n=== 测试非JSON格式的内容 ==="
mock_response_non_json = {
  'model' => 'deepseek-r1',
  'choices' => [
    {
      'message' => {
        'role' => 'assistant',
        'content' => '这不是JSON格式的内容',
        'reasoning_content' => '这是reasoning内容'
      }
    }
  ]
}
response_non_json = Langchain::LLM::DeepseekResponse.new(mock_response_non_json)
puts "chat_completion: #{response_non_json.chat_completion}" 