#!/usr/bin/env ruby
# frozen_string_literal: true

# n8n 真实环境集成测试脚本
# 
# 安全限制：所有测试消息仅发送给 user_id: 3959
# 性能考虑：DeepSeek 响应慢，设置长超时时间

require_relative 'config/environment'

class N8nRealEnvironmentTester
  # 严格的安全限制
  TEST_USER_ID = 3959
  
  # 考虑 DeepSeek 性能的超时设置
  CONNECTIVITY_TIMEOUT = 30    # 连通性测试超时
  WEBHOOK_TIMEOUT = 120        # Webhook 调用超时
  CONTENT_GENERATION_TIMEOUT = 180  # 内容生成超时
  
  def initialize
    puts "🔧 n8n 真实环境集成测试"
    puts "🔒 严格安全限制：测试仅使用用户ID #{TEST_USER_ID}"
    puts "⏱️  性能考虑：已设置长超时时间适应 DeepSeek 响应速度"
    puts "=" * 70
  end

  def run_test
    @test_start_time = Time.current
    @test_results = []
    
    puts "🚀 开始真实环境测试"
    puts "测试时间：#{@test_start_time.strftime('%Y-%m-%d %H:%M:%S')}"
    puts "n8n 服务器：#{n8n_base_url}"
    puts

    # 渐进式测试流程
    test_environment_setup
    test_n8n_connectivity
    test_webhook_basic_call
    test_content_generation_flow
    test_performance_metrics
    
    # 生成测试报告
    generate_test_report
    
    puts "\n" + "=" * 70
    puts "✅ 真实环境测试完成"
  end

  private

  def n8n_base_url
    @n8n_base_url ||= ENV.fetch('N8N_BASE_URL', 'https://bjjw.hhtz.gov.cn/n8n')
  end

  def test_environment_setup
    puts "📋 1. 环境设置和安全检查"
    
    begin
      # 验证测试用户
      test_user = User.find_by(id: TEST_USER_ID)
      if test_user
        puts "  ✅ 测试用户验证: #{test_user.name} (ID: #{TEST_USER_ID})"
        record_test_result("环境设置", "用户验证", true, "测试用户存在")
      else
        puts "  ❌ 测试用户不存在"
        record_test_result("环境设置", "用户验证", false, "测试用户不存在")
        return false
      end
      
      # 检查 n8n 配置
      puts "  📊 n8n 服务器: #{n8n_base_url}"
      puts "  📊 连通性超时: #{CONNECTIVITY_TIMEOUT}s"
      puts "  📊 Webhook 超时: #{WEBHOOK_TIMEOUT}s"
      puts "  📊 内容生成超时: #{CONTENT_GENERATION_TIMEOUT}s"
      
      # 创建测试规则
      @test_rule = create_test_rule_for_real_test
      puts "  ✅ 测试规则创建: #{@test_rule.name}"
      
      # 创建测试 Pack
      @test_pack = create_test_pack_for_real_test
      puts "  ✅ 测试 Pack 创建: #{@test_pack.name}"
      
      record_test_result("环境设置", "整体设置", true, "环境准备完成")
      
    rescue => e
      puts "  ❌ 环境设置失败: #{e.message}"
      record_test_result("环境设置", "整体设置", false, e.message)
      return false
    end
    
    puts
    true
  end

  def test_n8n_connectivity
    puts "📋 2. n8n 服务器连通性测试"
    
    begin
      puts "  📤 测试基础连通性..."
      
      # 使用简单的 HTTP 请求测试连通性
      connection = Faraday.new do |faraday|
        faraday.adapter Faraday.default_adapter
        faraday.options.timeout = CONNECTIVITY_TIMEOUT
      end
      
      # 尝试访问 n8n 健康检查端点（如果有的话）
      test_url = "#{n8n_base_url}/healthz"
      
      start_time = Time.current
      begin
        response = connection.get(test_url)
        end_time = Time.current
        response_time = ((end_time - start_time) * 1000).round(2)
        
        puts "  📊 响应时间: #{response_time}ms"
        puts "  📊 响应状态: #{response.status}"
        
        if response.status == 200 || response.status == 404  # 404 也表示服务器可达
          puts "  ✅ n8n 服务器连通性正常"
          record_test_result("连通性测试", "基础连通", true, "响应时间: #{response_time}ms")
        else
          puts "  ⚠️  n8n 服务器响应异常，但可能仍可用于 webhook"
          record_test_result("连通性测试", "基础连通", true, "异常响应但服务器可达")
        end
        
      rescue Faraday::TimeoutError
        puts "  ⚠️  连通性测试超时，但不影响 webhook 测试"
        record_test_result("连通性测试", "基础连通", true, "超时但继续测试")
      end
      
    rescue => e
      puts "  ⚠️  连通性测试遇到问题: #{e.message}"
      puts "  📝 继续进行 webhook 测试..."
      record_test_result("连通性测试", "基础连通", true, "跳过连通性检查")
    end
    
    puts
  end

  def test_webhook_basic_call
    puts "📋 3. Webhook 基础调用测试"
    
    begin
      puts "  📤 准备 webhook 测试数据..."
      
      # 创建 n8n 内容生成器
      generator = Serve::N8nContentGenerator.new(
        rule: @test_rule,
        pack: @test_pack,
        options: { 
          prompt: "真实环境测试 - 基础 webhook 调用",
          timeout: WEBHOOK_TIMEOUT
        }
      )
      
      # 检查配置
      should_use_n8n = generator.send(:should_use_n8n?)
      puts "  📊 n8n 集成状态: #{should_use_n8n ? '启用' : '禁用'}"
      
      if should_use_n8n
        puts "  📤 准备发送数据到 n8n..."
        payload = generator.send(:prepare_n8n_payload)
        puts "  📊 Payload 大小: #{payload.to_json.bytesize} bytes"
        puts "  📊 包含字段: #{payload.keys.join(', ')}"
        
        # 执行实际的 webhook 调用
        puts "  📤 发送 webhook 请求..."
        start_time = Time.current
        
        begin
          response = generator.send(:send_to_n8n, payload)
          end_time = Time.current
          response_time = ((end_time - start_time) * 1000).round(2)
          
          puts "  ✅ Webhook 调用成功"
          puts "  📊 响应时间: #{response_time}ms"
          puts "  📊 响应类型: #{response.class}"
          
          if response.is_a?(Hash) && response['contents']
            puts "  📊 返回内容数量: #{response['contents'].size}"
            record_test_result("Webhook测试", "基础调用", true, "响应时间: #{response_time}ms, 内容: #{response['contents'].size}条")
          else
            puts "  ⚠️  响应格式异常: #{response.inspect[0..100]}..."
            record_test_result("Webhook测试", "基础调用", false, "响应格式异常")
          end
          
        rescue Faraday::TimeoutError
          puts "  ❌ Webhook 调用超时 (#{WEBHOOK_TIMEOUT}s)"
          puts "  💡 建议：DeepSeek 响应较慢，可能需要更长超时时间"
          record_test_result("Webhook测试", "基础调用", false, "超时 #{WEBHOOK_TIMEOUT}s")
          
        rescue => webhook_error
          puts "  ❌ Webhook 调用失败: #{webhook_error.message}"
          puts "  📝 错误详情: #{webhook_error.class}"
          record_test_result("Webhook测试", "基础调用", false, webhook_error.message)
        end
        
      else
        puts "  ⚠️  n8n 集成未启用，跳过 webhook 测试"
        record_test_result("Webhook测试", "基础调用", false, "n8n集成未启用")
      end
      
    rescue => e
      puts "  ❌ Webhook 测试失败: #{e.message}"
      record_test_result("Webhook测试", "基础调用", false, e.message)
    end
    
    puts
  end

  def test_content_generation_flow
    puts "📋 4. 完整内容生成流程测试"
    
    begin
      puts "  📤 测试完整的 n8n 内容生成流程..."
      
      # 使用类方法进行完整测试
      start_time = Time.current
      
      contents = Serve::N8nContentGenerator.generate_for_rule(
        rule: @test_rule,
        pack: @test_pack,
        options: { 
          prompt: "真实环境测试 - 生成岗位变动廉洁提醒",
          timeout: CONTENT_GENERATION_TIMEOUT,
          action: "real_environment_test"
        }
      )
      
      end_time = Time.current
      total_time = ((end_time - start_time) * 1000).round(2)
      
      puts "  ✅ 内容生成完成"
      puts "  📊 总耗时: #{total_time}ms"
      puts "  📊 生成内容数量: #{contents.size}"
      
      # 显示生成的内容
      contents.each_with_index do |content, index|
        puts "  📝 内容 #{index + 1}: #{content['content'][0..60]}..."
        puts "     来源: #{content['source']}"
        puts "     时间: #{content['generated_at']}"
      end
      
      # 测试 Pack 集成
      puts "  📤 测试 Pack 集成..."
      original_payload = @test_pack.payload.dup
      
      @test_pack.refresh_contents_by_rule(prompt: "真实环境 Pack 集成测试")
      @test_pack.reload
      
      if @test_pack.payload['contents']
        puts "  ✅ Pack 集成成功"
        puts "  📊 Pack 中内容数量: #{@test_pack.payload['contents'].size}"
        record_test_result("内容生成", "完整流程", true, "总耗时: #{total_time}ms, 内容: #{contents.size}条")
      else
        puts "  ❌ Pack 集成失败"
        record_test_result("内容生成", "完整流程", false, "Pack集成失败")
      end
      
    rescue => e
      puts "  ❌ 内容生成流程失败: #{e.message}"
      puts "  📝 错误详情: #{e.backtrace.first}"
      record_test_result("内容生成", "完整流程", false, e.message)
    end
    
    puts
  end

  def test_performance_metrics
    puts "📋 5. 性能指标测试"
    
    begin
      puts "  📊 执行性能基准测试..."
      
      # 测试多次调用的性能
      test_times = []
      success_count = 0
      total_tests = 3
      
      total_tests.times do |i|
        puts "  📤 执行第 #{i + 1} 次性能测试..."
        
        start_time = Time.current
        begin
          contents = Serve::N8nContentGenerator.generate_for_rule(
            rule: @test_rule,
            pack: @test_pack,
            options: { 
              prompt: "性能测试 #{i + 1} - 简短提醒",
              timeout: CONTENT_GENERATION_TIMEOUT
            }
          )
          
          end_time = Time.current
          response_time = ((end_time - start_time) * 1000).round(2)
          test_times << response_time
          success_count += 1
          
          puts "    ✅ 第 #{i + 1} 次测试完成: #{response_time}ms"
          
        rescue => e
          puts "    ❌ 第 #{i + 1} 次测试失败: #{e.message}"
          test_times << nil
        end
        
        # 避免频繁请求，稍作延迟
        sleep(2) if i < total_tests - 1
      end
      
      # 计算性能统计
      valid_times = test_times.compact
      if valid_times.any?
        avg_time = (valid_times.sum / valid_times.size).round(2)
        min_time = valid_times.min
        max_time = valid_times.max
        
        puts "  📊 性能统计结果:"
        puts "    成功率: #{success_count}/#{total_tests} (#{(success_count.to_f / total_tests * 100).round(1)}%)"
        puts "    平均响应时间: #{avg_time}ms"
        puts "    最快响应时间: #{min_time}ms"
        puts "    最慢响应时间: #{max_time}ms"
        
        record_test_result("性能测试", "基准测试", true, "成功率: #{success_count}/#{total_tests}, 平均: #{avg_time}ms")
      else
        puts "  ❌ 所有性能测试都失败了"
        record_test_result("性能测试", "基准测试", false, "所有测试失败")
      end
      
    rescue => e
      puts "  ❌ 性能测试失败: #{e.message}"
      record_test_result("性能测试", "基准测试", false, e.message)
    end
    
    puts
  end

  def create_test_rule_for_real_test
    rule_name = "真实环境n8n测试规则"
    existing_rule = Serve::Rule.find_by(name: rule_name)
    
    options = {
      template_prompt: {
        prompt: "根据岗位变动信息生成简洁的廉洁提醒，每条不超过100字。"
      },
      n8n_integration: {
        enabled: true,
        webhook_path: "position-change-reminder",
        timeout: CONTENT_GENERATION_TIMEOUT,
        fallback_on_error: true
      }
    }
    
    app = App.first || App.create!(name: "测试应用")
    
    if existing_rule
      existing_rule.update!(options: options)
      existing_rule
    else
      Serve::Rule.create!(
        name: rule_name,
        app: app,
        state: 'used',
        message_type: 'Serve::DingtalkMessage',
        options: options,
        description: "真实环境 n8n 集成测试专用规则"
      )
    end
  end

  def create_test_pack_for_real_test
    pack_name = "真实环境n8n测试包"
    existing_pack = Serve::Pack.find_by(name: pack_name)
    
    test_user = User.find(TEST_USER_ID)
    
    if existing_pack
      existing_pack.update!(
        rule: @test_rule,
        payload: {
          user_ids: [TEST_USER_ID],
          real_test_mode: true,
          test_timestamp: Time.current.iso8601
        }
      )
      existing_pack
    else
      Serve::Pack.create!(
        name: pack_name,
        rule: @test_rule,
        app: @test_rule.app,
        creator: test_user,
        state: 'pending',
        send_at: 1.hour.from_now,
        payload: {
          user_ids: [TEST_USER_ID],
          real_test_mode: true,
          test_timestamp: Time.current.iso8601
        }
      )
    end
  end

  def record_test_result(category, test_name, success, details)
    @test_results << {
      category: category,
      test_name: test_name,
      success: success,
      details: details,
      timestamp: Time.current
    }
  end

  def generate_test_report
    puts "📊 真实环境测试报告"
    puts "=" * 70
    
    total_tests = @test_results.size
    successful_tests = @test_results.count { |r| r[:success] }
    success_rate = (successful_tests.to_f / total_tests * 100).round(1)
    
    puts "测试概览:"
    puts "  总测试数: #{total_tests}"
    puts "  成功测试: #{successful_tests}"
    puts "  失败测试: #{total_tests - successful_tests}"
    puts "  成功率: #{success_rate}%"
    puts "  测试时长: #{((Time.current - @test_start_time) / 60).round(1)} 分钟"
    puts
    
    # 按类别分组显示结果
    @test_results.group_by { |r| r[:category] }.each do |category, tests|
      puts "#{category}:"
      tests.each do |test|
        status = test[:success] ? "✅" : "❌"
        puts "  #{status} #{test[:test_name]}: #{test[:details]}"
      end
      puts
    end
    
    # 保存详细报告
    report_data = {
      test_summary: {
        total_tests: total_tests,
        successful_tests: successful_tests,
        success_rate: success_rate,
        test_duration_minutes: ((Time.current - @test_start_time) / 60).round(1),
        test_user_id: TEST_USER_ID,
        n8n_server: n8n_base_url
      },
      test_results: @test_results,
      recommendations: generate_recommendations
    }
    
    File.write('tmp/n8n_real_environment_test_report.json', JSON.pretty_generate(report_data))
    puts "📄 详细测试报告已保存到: tmp/n8n_real_environment_test_report.json"
  end

  def generate_recommendations
    recommendations = []
    
    failed_tests = @test_results.select { |r| !r[:success] }
    
    if failed_tests.any?
      recommendations << "发现 #{failed_tests.size} 个失败测试，建议检查网络连接和 n8n 服务器状态"
    end
    
    recommendations << "考虑到 DeepSeek 响应较慢，建议在生产环境中设置更长的超时时间"
    recommendations << "建议实施监控和告警机制，及时发现 n8n 服务异常"
    recommendations << "建议定期执行性能测试，监控响应时间变化"
    
    recommendations
  end
end

# 运行真实环境测试
if __FILE__ == $0
  begin
    tester = N8nRealEnvironmentTester.new
    tester.run_test
  rescue => e
    puts "❌ 真实环境测试执行失败：#{e.message}"
    puts e.backtrace.join("\n")
    exit 1
  end
end
