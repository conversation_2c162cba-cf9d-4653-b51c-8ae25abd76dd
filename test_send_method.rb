puts '=== 测试通过send方法调用ActivityQueryTool ==='
begin
  agent = Bot::Agent.first
  user = User.first
  
  puts "Agent: #{agent.name}"
  puts "User: #{user.name}"
  
  # 直接测试ActivityQueryTool
  puts "\n=== 直接测试ActivityQueryTool ==="
  intent = agent.intents.find_by(name: '素材查询')
  puts "Intent: #{intent.name}"
  
  activity_tool = Bot::Tools::ActivityQueryTool.new(intent: intent)
  
  # 测试通过send方法调用（正确的方式）
  puts "\n=== 通过send方法调用query_activities ==="
  result = activity_tool.send(:query_activities, query: '过去一年里有多少素材？')
  
  puts "结果类型: #{result.class}"
  puts "结果keys: #{result.keys}"
  puts "data keys: #{result[:data].keys if result[:data]}"
  puts "artifact: #{result[:artifact]}"
  
  # 检查artifact
  if result[:artifact]
    artifact = result[:artifact]
    puts "\n🎉 Artifact创建成功!"
    puts "  ID: #{artifact.id}"
    puts "  Type: #{artifact.type}"
    puts "  Class: #{artifact.class}"
    puts "  Meta: #{artifact.meta}"
    puts "  Data: #{artifact.data}"
  else
    puts "\n❌ 没有创建Artifact"
  end
  
  # 检查context
  puts "\n=== 检查工具context ==="
  context = activity_tool.instance_variable_get(:@context)
  puts "Context keys: #{context.keys if context}"
  
  if context && context[:query_activities]
    puts "query_activities context keys: #{context[:query_activities].keys}"
    if context[:query_activities][:artifact]
      puts "✅ Context中有artifact!"
      puts "Artifact类型: #{context[:query_activities][:artifact].class}"
    else
      puts "❌ Context中没有artifact"
    end
  else
    puts "❌ 没有query_activities context"
  end
  
rescue StandardError => e
  puts "❌ 失败: #{e.message}"
  puts e.backtrace.first(10)
end
