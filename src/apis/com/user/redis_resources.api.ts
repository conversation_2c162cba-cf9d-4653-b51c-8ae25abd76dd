import { VApiConfig } from '@/lib/vails/api';
import { MyApi } from '@/apis/MyApi';
import { VObject } from '@/lib/vails';

export class ComUserRedisResourcesApi extends MyApi<VObject> {
  constructor(config?: VApiConfig) {
    super({
      namespace: '/com/user',
      name: 'redis_resources',
      actions: [{ name: 'batch_destroy', method: 'post', on: 'collection' }],
      ...config,
    });
  }
}
