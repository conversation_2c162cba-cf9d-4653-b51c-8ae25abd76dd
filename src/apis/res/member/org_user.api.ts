import { VApiConfig } from '@/lib/vails/api';
import { MyApi } from '@/apis/MyApi';
import { User } from '@/engines/res/res-core/model';

export class ResMemberOrgUserApi extends MyApi<User> {
  constructor(config?: VApiConfig) {
    super({
      namespace: '/res/member',
      name: 'org_user',
      actions: [
        {
          name: 'import_precheck',
          method: 'post',
          on: 'collection',
        },
      ],
      ...config,
    });
  }
}
