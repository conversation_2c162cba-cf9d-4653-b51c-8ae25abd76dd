import { VApiConfig } from '@/lib/vails/api';
import { MyApi } from '@/apis/MyApi';
import { NoticeTask } from '@/types/model';

export class NoticeTasksApi extends MyApi<NoticeTask> {
  constructor(config?: VApiConfig) {
    super({
      namespace: '/notice',
      name: 'task',
      actions: [
        {
          name: 'ai_fill',
          method: 'post',
          on: 'collection',
        },
        {
          name: 'ai_match_serve_article_ids',
          method: 'post',
          on: 'collection',
        },
      ],
      ...config,
    });
  }
}
