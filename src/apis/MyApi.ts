import VApi, { VApiConfig } from '@/lib/vails/api';
import { createRequestClient as baseCreateRequestClient } from '@/engines/base/apis/MyApi';
import { VObject } from '@/lib/vails';

export const createRequestClient = (url?: string, encrypt = false) => {
  const request = baseCreateRequestClient(url, encrypt);
  request.interceptors.request.use((config: any) => {
    Object.assign(config.headers, {
      'tanent-code': 'IEST',
    });
    return config;
  });
  return request;
};

export class MyApi<T extends VObject> extends VApi<T> {
  constructor(config?: VApiConfig) {
    super(config, () => createRequestClient(undefined, config?.encrypt));
  }
}
