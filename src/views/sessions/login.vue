<script lang="ts">
import { computed, defineComponent, ref, toRefs } from 'vue';
import ComLoginBackground from '../../engines/login/components/ComLoginBackground.vue';

const Login = defineComponent({
  name: 'Login',
  components: { ComLoginBackground },
  props: {},
  setup(props) {
    const componentRef = ref<any>(null);
    const defaultTabs = computed(() => [
      { key: 'dingtalk', label: '扫码登录' },
      { key: 'password', label: '密码登录' },
    ]);
    const bgConfig = {
      mainBg:
        'url("https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/party-build-mobile/WechatIMG680.jpeg")',
      innerBg: '',
      leftBg:
        'url(https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/discipline-inspection/%E6%B5%85%E7%BA%A2%E8%83%8C%E6%99%AF.png)',
      rightBg: '',
    };
    const iconConfig = {
      // leftIcon:
      //   'url("https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/discipline-inspection/WechatIMG354.jpeg")',
      lsize: 'S',
    };

    const title = document.title
    const salutatoryConfig = {
      systemName: title,
      ssize: 'L',
      color: '',
      title: '',
    };
    return {
      ...toRefs(props),
      componentRef,
      defaultTabs,
      bgConfig,
      iconConfig,
      salutatoryConfig,
    };
  },
});
export default Login;
</script>

<template lang="pug">
.login-box
  ComLoginBackground(
    ref='componentRef',
    :tabs='defaultTabs',
    :bgConfig='bgConfig',
    :iconConfig='iconConfig',
    :salutatoryConfig='salutatoryConfig',
    appId='BJ_DIC'
  )
</template>

<style lang="stylus" scoped>
.login-box
  width 100%
  height 100%
</style>
