<script lang="ts">
import ComServeHomeFloatingIcon from '@/components/serve/home/<USER>';
import ComServeHomeIndexHeader from '@/components/serve/home/<USER>';
import TabWithRouter, { Tab } from '@/components/serve/home/<USER>';
import { defineComponent, onMounted, ref, toRefs } from 'vue';
import { VObject } from '../../../lib/vails/model/index';
const ServeHomeIndex = defineComponent({
  name: 'ServeHomeIndex',
  components: {
    ComServeHomeIndexHeader,
    ComServeHomeFloatingIcon,
  },
  setup(props) {
    const tabs: Tab[] = [
      {
        name: '首页',
        key: '',
        router: '/serve/home',
      },
      {
        name: '廉政教育库',
        key: 'lib',
        router: '/serve/home/<USER>',
      },
    ];
    const routerTab = new TabWithRouter(tabs);
    onMounted(() => {
      routerTab.init();
    });
    const searchQuery = ref<any>({});
    const onSearch = (query: VObject) => {
      searchQuery.value = query;
    };

    return {
      ...toRefs(props),
      tabs,
      routerTab,
      onSearch,
      searchQuery,
    };
  },
});
export default ServeHomeIndex;
</script>

<template lang="pug">
.serve-home-index.w-full.flex.flex-col.items-center.overflow-auto.h-full.relative
  ComServeHomeFloatingIcon
  ComServeHomeIndexHeader.index__header.w-full.h-233px.flex-shrink-0(
    @search='onSearch',
    :routerTab='routerTab'
  )
  .tabs__container.w-full.h-44px.flex.justify-center
    .tabs.flex
      .tab.w-200px.h-44px.flex.items-center.justify-center.cursor-pointer(
        v-for='(tab, index) in tabs',
        :class='{ activeTab: routerTab.activeTabKey.value === tab.key }',
        @click='routerTab.changeTab(tab)'
      )
        .tab__name.text-base.text-blue-700 {{ tab.name }}
  keep-alive
    router-view(v-slot='{ Component }')
      keep-alive
        component(
          v-if='Component',
          :is='Component',
          :key='$route.path',
          :routerTab='routerTab',
          :q='searchQuery'
        )
</template>

<style lang="stylus" scoped>
@media print
  .serve-home-index
    overflow visible
    .index__header
      display none
    .tabs__container
      display none
.serve-home-index::-webkit-scrollbar
  display none !important
  width 0 !important
.serve-home-index
  min-height 100vh
  .tabs__container
    background conic-gradient(from 90deg at 50% 50%, #C3DDFD 0deg, #A4CAFE 180deg, #76A9FA 360deg)
    .activeTab
      @apply bg-blue-700
      .tab__name
        @apply text-white
</style>
