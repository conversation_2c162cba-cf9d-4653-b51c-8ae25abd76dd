<script lang="ts">
import { ServeManageRuleGroupsApi } from '@/engines/iest/apis/serve/manage/rule_groups.api';
import ComHomeIestRule from '@/engines/iest/components/iest/ComHomeIestRule.vue';
import { ServeRuleGroupModel } from '@/engines/iest/models/serve/manage/rule_groups';
import { defineComponent } from 'vue';
import ComHomeActions from '../../components/home/<USER>';
import ComHomeBpm from '../../components/home/<USER>';
import ComHomeIestOfficial from '../../components/home/<USER>';
import ComHomeNotify from '../../components/home/<USER>';
import { BpmUserApprovingInstances } from '../../engines/bpm/bpm-core/apis/user/approving/instance.api';
import { BpmUserCreatedInstances } from '../../engines/bpm/bpm-core/apis/user/created/instance.api';
import { InstanceModel } from '../../engines/bpm/bpm-core/apis/user/instance.api';
import { BpmUserNotifiedInstances } from '../../engines/bpm/bpm-core/apis/user/notified/instance.api';
import { IestAppsApi } from '../../engines/iest/apis/iest/apps.api';
import { ServeManageMessagesApi } from '../../engines/iest/serve-core/apis/serve/manage/messages.api';
import { ServeManagePacksApi } from '../../engines/iest/serve-core/apis/serve/manage/packs.api';
import { ServeManageRulesApi } from '../../engines/iest/serve-core/apis/serve/manage/rules.api';
import { ServeMessageModel } from '../../engines/iest/serve-core/models/serve/manage/messages';
import { ServePackModel } from '../../engines/iest/serve-core/models/serve/manage/packs';
import { ServeRuleModel } from '../../engines/iest/serve-core/models/serve/manage/rules';
import { VStore } from '../../lib/vails/store/index';

const HomeIndex = defineComponent({
  name: 'HomeIndex',
  components: {
    ComHomeActions,
    ComHomeIestRule,
    ComHomeIestOfficial,
    ComHomeNotify,
    ComHomeBpm,
  },
  setup() {
    const approvingStore = new VStore(new BpmUserApprovingInstances(), InstanceModel);
    const createdStore = new VStore(new BpmUserCreatedInstances(), InstanceModel);
    const notifiedStore = new VStore(new BpmUserNotifiedInstances(), InstanceModel);

    const ruleStore = new VStore(new ServeManageRulesApi({}), ServeRuleModel);
    const catalogStore = new VStore(new ServeManageRuleGroupsApi({}), ServeRuleGroupModel);
    const packStore = new VStore(
      new ServeManagePacksApi({ params: { q: { s: ['updated_at desc'] } } }),
      ServePackModel,
    );
    const messageStore = new VStore(new ServeManageMessagesApi({}), ServeMessageModel);

    const appStore = new VStore(new IestAppsApi({}));
    return {
      approvingStore,
      createdStore,
      catalogStore,
      notifiedStore,
      ruleStore,
      packStore,
      messageStore,
      appStore,
    };
  },
});
export default HomeIndex;
</script>

<template lang="pug">
.home-index.bg-gray-100.min-h-full.bg-cover
  .w-40.h-55px.fixed.left-10.bottom-10.z-1000
    img.w-full.h-full(src="https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iest/hangzhoumainicon.png" alt="logo")
  .max-w-1440px.min-w-768px.h-full.flex.pt-4
    .left.mr-4.space-y-4.w-0
      ComHomeActions
      ComHomeIestRule(
        title='规则库',
        :store='ruleStore',
        :catalogStore='catalogStore'
        url='/serve/manage/rules'
      )
      ComHomeIestOfficial(
        title='对象库',
        :store='appStore',
        url='/res/user'
      )
    .right.space-y-4.flex-shrink-0.w-0
      ComHomeNotify.mb-4(
        title='消息管理',
        url='/serve/manage/packs',
        :packStore='packStore',
        :msgStore='messageStore',
      )
      ComHomeBpm(
        title='我的待办',
        :approvingStore='approvingStore'
        :createdStore='createdStore'
        :notifiedStore='notifiedStore'
        url='/bpm/user/instances'
      )

</template>

<style lang="stylus" scoped>
.home-index
  background-image url(https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iest/hangzhouBg.png)
  .left
    flex-basis 60%
  .right
    flex-grow 1
</style>
