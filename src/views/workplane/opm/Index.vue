<script lang='ts'>
import { ref, defineComponent, toRefs } from 'vue';
import ComOpmWorkplaneIndex from '@/engines/opm/components/workplane/ComOpmWorkplaneIndex.vue';

const OpmWorkplaneIndex = defineComponent({
  name: 'OpmWorkplaneIndex',
  components: { ComOpmWorkplaneIndex },
  props: {},
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
export default OpmWorkplaneIndex;
</script>

<template lang="pug">
.opm-workplane-index.w-full.h-full(class='bg-[#f0f2fc]')
  ComOpmWorkplaneIndex
</template>

<style lang="stylus" scoped></style>
