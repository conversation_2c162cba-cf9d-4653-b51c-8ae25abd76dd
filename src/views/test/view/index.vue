<script lang="ts">
import { ref, defineComponent } from 'vue';
export default defineComponent({
  name: '',
  components: {},
  setup() {
    const value = ref({
      flatData: [
        // { name: 'root', key: 'root', parent_key: null, index: 1 },
        {
          key: 'a123',
          component: 'TaChartImage',
          name: '图片 1',
          parent_key: 'root',
          props: { url: 'https://pic.qqtn.com/up/2019-9/15690311636958128.jpg' },
          index: 1,
          cssc: {
            x: 100,
            y: 100,
          },
        },
        {
          key: 'b123',
          name: '图层2',
          parent_key: 'root',
          cssc: { zIndex: 2 },
          component: '<PERSON><PERSON>ayer',
        },
        {
          key: 'c123',
          component: 'TaChartImage',
          cssc: {
            w: 50,
            h: 50,
            zIndex: 3,
          },
          parent_key: 'b123',
          props: { url: 'https://pic.qqtn.com/up/2019-9/15690311636958128.jpg' },
          name: '图片 2',
        },
      ],
    });

    return {
      value,
    };
  },
});
</script>

<template lang="pug">
//- .list {{ value }}
TaBuilderDesigner(v-model:value='value')

</template>

<style lang="stylus">
.list
  color red
</style>
