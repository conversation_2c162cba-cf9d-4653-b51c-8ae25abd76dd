<script lang='ts'>
import { ref, defineComponent, toRefs } from 'vue';
import ComResTagsIndex from '@/components/res/ComResTagsIndex.vue';
import { VStore } from '@/lib/vails';
import { ResAdminTagApi } from '@/engines/res/res-core/apis/res/admin/tag.api';
import ComIestStatisticBlockCard from '@/engines/iest/components/ComIestStatisticBlockCard.vue';
import { IestManageTagsApi } from '@/engines/iest/apis/iest/manage/tags.api';
import { ResAdminUsersApi } from '@/engines/res/res-core/apis/res/admin/users.api';
import { ResUserModel } from '@/engines/res/res-core/models/res/user';
const ResTagsIndex = defineComponent({
  name: 'ResTagsIndex',
  components: { ComResTagsIndex, ComIestStatisticBlockCard },
  setup(props) {
    const tagStore = new VStore(new IestManageTagsApi())
    const userStore = new VStore(new ResAdminUsersApi(), ResUserModel)
    return {
      ...toRefs(props),
      tagStore,
      userStore,
    };
  },
});
export default ResTagsIndex;
</script>

<template lang="pug">
.res-tags-index.bg-gray-100
  .h-full.pt-4.flex.flex-col
    .grid.grid-cols-4.mb-4
      ComIestStatisticBlockCard(
        label='标签总数',
        :value='tagStore?.totalCount?.value'
        color='yellow',
        icon='file-circle-plus'
      )
    ComResTagsIndex.flex-grow.h-0(:store='tagStore',:userStore='userStore')
</template>

<style lang="stylus" scoped>
.res-tags-index
  height 100%
  width 100%
</style>
