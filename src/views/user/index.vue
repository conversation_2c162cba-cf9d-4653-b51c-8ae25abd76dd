<script lang='ts'>
import { ResAdminUsersApi } from '@/engines/res/res-core/apis/res/admin/users.api';
import { ResUserModel } from '@/engines/res/res-core/models/res/user';
import { VStore } from '@/lib/vails';
import { ref, defineComponent, toRefs } from 'vue';
import ComResUserIndex from '@/components/res/ComResUserIndex.vue';
import { IestManageOrgsApi } from '@/engines/iest/apis/iest/manage/orgs.api';
import { IestOrgModel } from '@/engines/iest/models/iest/manage/orgs';
import ComIestOrgTree from '@/components/res/ComIestOrgTree.vue';

const resUserIndex = defineComponent({
  name: 'resUserIndex',
  components: { ComResUserIndex, ComIestOrgTree },
  props: {},
  setup(props) {
    const store = new VStore(new ResAdminUsersApi(), ResUserModel)
    const orgStore = new VStore(new IestManageOrgsApi(), IestOrgModel)

    const params = ref({})
    const onSelect = (id: number) => {
      if (!id) return params.value = {};
      params.value = {
        q: {
          orgs_id_eq: id
        }
      }
    }

    const display = ref(true)
    const toggleDisplay = () => {
      display.value = !display.value
    }
    return {
      ...toRefs(props),
      store,
      orgStore,
      params,
      onSelect,
      toggleDisplay,
      display,
    };
  },
});
export default resUserIndex;
</script>

<template lang="pug">
.res-user-index.bg-gray-100
  .h-full.pt-4.flex
    ComIestOrgTree.mr-4.transition-all(
      :store='orgStore'
      :class='{"basis-1/4": display,"basis-0":!display}',
      @select='onSelect',
      :display='display',
      @display='toggleDisplay'
    )
    ComResUserIndex(
      :store="store"
      :class='{"basis-3/4": display,"basis-auto":!display}'
      :extraParams='params'
    )
</template>

<style lang="stylus" scoped>
.res-user-index
  height 100%
  width 100%
</style>
