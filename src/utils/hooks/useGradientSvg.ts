export default function useGradientSvg() {
  function addLinearGradient(svgString: string) {
    const parser = new DOMParser();
    const svgDoc = parser.parseFromString(svgString, 'image/svg+xml');
    const defs = svgDoc.createElement('defs');

    // 创建线性渐变
    const linearGradient = svgDoc.createElementNS('http://www.w3.org/2000/svg', 'linearGradient');
    linearGradient.setAttribute('id', 'gradient1');
    linearGradient.setAttribute('x1', '0%');
    linearGradient.setAttribute('y1', '0%');
    linearGradient.setAttribute('x2', '100%');
    linearGradient.setAttribute('y2', '100%');

    // 添加渐变颜色
    const stop1 = svgDoc.createElementNS('http://www.w3.org/2000/svg', 'stop');
    stop1.setAttribute('offset', '0%');
    stop1.setAttribute('stop-color', 'red');
    stop1.setAttribute('stop-opacity', '1');

    const stop2 = svgDoc.createElementNS('http://www.w3.org/2000/svg', 'stop');
    stop2.setAttribute('offset', '100%');
    stop2.setAttribute('stop-color', 'blue');
    stop2.setAttribute('stop-opacity', '1');

    // 将颜色停止点添加到渐变中
    linearGradient.appendChild(stop1);
    linearGradient.appendChild(stop2);
    defs.appendChild(linearGradient);

    // 将 defs 添加到 svgDoc 的根元素中
    const svgRoot = svgDoc.documentElement;
    svgRoot.appendChild(defs);
    return new XMLSerializer().serializeToString(svgDoc);
  }

  return {
    addLinearGradient,
  };
}
