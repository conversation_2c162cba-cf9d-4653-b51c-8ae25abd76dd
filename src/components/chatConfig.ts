import { VObject } from '@/lib/vails';
import { defineAsyncComponent } from 'vue';

export const ChatMentionTypeMapping: VObject = {
  'Iest::Ai::Chat::Mentions::ServePack': defineAsyncComponent(
    () => import('@/engines/iest/components/iest/mentions/ComIestMentionServePackShow.vue'),
  ),
  'Chat::Mentions::SearchBpmInstance': defineAsyncComponent(
    () =>
      import('@/engines/chat/components/chat/mentions/ComChatMentionSearchBpmInstanceIndex.vue'),
  ),
  'Serve::Chat::Mentions::SearchBidProject': defineAsyncComponent(
    () =>
      import('@/engines/iest/components/serve/bid_projects/ComServeBidProjectsMentionIndex.vue'),
  ),
  'Iest::Mentions::Paperwork': defineAsyncComponent(
    () => import('@/engines/iest/components/iest/paperworks/ComIestPaperworkMentionShow.vue'),
  ),
  'Chat::Mentions::UserList': defineAsyncComponent(
    () => import('@/engines/chat/components/chat/mentions/ComChatMentionUserListShow.vue'),
  ),
};

export const ChatNewSuggtionsFn: (type: string) => VObject[] = (type: string) => {
  if (type == 'screen') {
    return [
      { name: '国庆前一天给年轻干部发一条国庆廉洁提醒' },
      { name: '明天给监管人员发一条招投标廉洁提醒' },
      { name: '现在有多少待办' },
      { name: '库里有多少人员' },
      { name: '消息统计' },
      { name: '规则配置情况' },
      { name: '请告诉我现在有多少素材' },
    ];
  }
  return [
    { name: '国庆前一天给年轻干部发一条国庆廉洁提醒' },
    { name: '明天给监管人员发一条招投标廉洁提醒' },
    { name: '现在有多少待办' },
    { name: '库里有多少人员' },
    { name: '消息统计' },
    { name: '规则配置情况' },
    { name: '请告诉我现在有多少素材' },
  ];
};
