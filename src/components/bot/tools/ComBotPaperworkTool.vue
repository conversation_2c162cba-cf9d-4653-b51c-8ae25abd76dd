<script lang='ts'>
import { ref, defineComponent, toRefs, watch } from 'vue';
import { BotManagePaperworksApi } from '../../../apis/bot/manage/paperworks.api';
import { BotPaperworkModel } from '../../../models/bot/manage/paperworks';
import { VStore } from '@/lib/vails/store/index';
import { useCable } from '@/engines/base/channels/useCable';
import ComBotPaperworkRiskCard from '../paperwork/ComBotPaperworkRiskCard.vue';
import ComBotPaperworkState from '../paperwork/ComBotPaperworkState.vue';
import ComBotPaperworkPreviewer from '../paperwork/ComBotPaperworkPreviewer.vue';

const ComBotPaperworkTool = defineComponent({
  name: 'ComBotPaperworkTool',
  components: {
    ComBotPaperworkPreviewer, ComBotPaperworkRiskCard, ComBotPaperworkState
  },
  props: {
    record: { type: Object, default: () => ({}) },
    scopeChain: { type: String, default: '' },
    params: { type: Object, default: () => ({}) },
  },
  setup(props) {
    const store = new VStore(new BotManagePaperworksApi(), BotPaperworkModel);
    store.extra.cable_key = 'bot_paperworks';
    useCable(store, { callback: { afterUpdate: () => store.find(props.record.source_id) } });

    watch(
      () => props.record.source_id,
      () => {
        if (props.record.source_id) {
          store.find(props.record.source_id);
        }
      },
      {
        immediate: true,
      },
    );

    const visible = ref(false);
    return {
      ...toRefs(props),
      store,
      visible,
      paperwork: store.record
    };
  },
});
export default ComBotPaperworkTool;
</script>

<template lang="pug">
.com-bot-paperwork-tool.h-full.w-full.p-4.flex.flex-col
  .tool__name.shadow.rounded-xl.w-full.p-4.mb-4
    .font-medium {{ paperwork.name || '审查任务' }}
    .flex.justify-end.items-center.mt-2(v-if='paperwork?.state')
      a-button(
        v-if='paperwork.state === "success"',
        type='primary',
        @click.stop='() => visible = true',
      )
        | 查看详情
      ComBotPaperworkState(v-else, :record='paperwork')
  .flex-grow.h-0.overflow-y-auto
    .grid.gap-2(v-if='paperwork.response?.result?.length > 0')
      ComBotPaperworkRiskCard(
        v-for='result in paperwork.response.result',
        level='一般风险'
        :ruleName='result.问题',
        :content='{ "原文": result.原文 }',
      )
    .w-full.h-full.flex.justify-center.items-center(v-else)
      .text-gray-400
        | 暂无风险信息


  TaNoPaddingModal(
    v-model:visible='visible',
    v-if='paperwork?.id',
    :title='paperwork.name',
    :footer='null',
    width='1400px',
  )
    .h-85vh.overflow-y-auto
      ComBotPaperworkPreviewer(:record='paperwork')
      //- ComIestPaperworksShow(:store='store')

</template>

<style lang="stylus" scoped></style>
