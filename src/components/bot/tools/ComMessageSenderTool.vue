<script lang="ts">
import ComServePackShowCardWrapper from '@/components/bot/pack/ComServePackShowCardWrapper.vue';
import ChatEventCenter from '@/engines/bot/utils/ChatEventCenter';
import { ServeUserRulesApi } from '@/engines/iest/serve-core/apis/serve/user/rules.api';
import { ServeRuleModel } from '@/engines/iest/serve-core/models/serve/manage/rules';
import { VStore } from '@/lib/vails';
import { message } from 'ant-design-vue';
import dayjs, { Dayjs } from 'dayjs';
import { defineComponent, onMounted, ref, toRefs, watch } from 'vue';

const ComMessageSenderTool = defineComponent({
  name: 'ComMessageSenderTool',
  components: {
    ComServePackShowCardWrapper,
  },
  props: {
    record: { type: Object, default: () => ({}) },
    scopeChain: { type: String, default: '' },
    params: { type: Object, default: () => ({}) },
    store: { type: Object, default: () => ({}) },
  },
  emits: ['update:params', 'update:record'],
  setup(props, { emit }) {
    // 常量定义
    const AI_ICON = 'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iest/ai_summary.png';

    // 状态定义
    const ruleStore = new VStore(new ServeUserRulesApi(), ServeRuleModel);
    const loading = ref(false);
    const localValue = ref(props.params.rule_id as string | number);
    const localDate = ref<Dayjs | null>(props.params.send_at ? dayjs(props.params.send_at) : null);
    const localContent = ref(props.params.content || '');
    const customPrompt = ref('');
    const messageTitle = ref('');
    const messageBody = ref('');
    const selectedRule = ref<any>(null);
    const promptModalVisible = ref(false);
    const tempCustomPrompt = ref('');
    const isFromAiCreation = ref(false);
    const aiContents = ref([]);
    const selectedIndex = ref(-1);
    const contentTextarea = ref<HTMLTextAreaElement | null>(null);

    const tableItems = [
      {
        name: '规则名称',
        type: 'string',
        search: true,
        data_index: 'name',
      },
    ];

    // 方法定义
    const fetchRuleDetails = async (ruleId: string | number) => {
      if (!ruleId) return;
      try {
        // 使用常规的find方法获取完整规则数据
        const rule = await ruleStore.find(ruleId);
        selectedRule.value = rule.data;

        // 处理options数据
        if (selectedRule.value?.options) {
          const options =
            typeof selectedRule.value.options === 'string'
              ? JSON.parse(selectedRule.value.options)
              : selectedRule.value.options;

          // 使用可选链操作符安全访问
          customPrompt.value = options?.prompt || '';

          // 如果是从规则切换触发的，直接使用模板值
          // 如果是从AI创作触发的，只在值为空时使用模板值
          if (!isFromAiCreation.value) {
            // 规则切换 - 更新标题和简介
            messageTitle.value = options?.template_message_title || '';
            messageBody.value = options?.template_message_body || '';
          } else {
            // AI创作 - 保留用户输入
            if (!messageTitle.value) {
              messageTitle.value = options?.template_message_title || '';
            }
            if (!messageBody.value) {
              messageBody.value = options?.template_message_body || '';
            }
          }
        }
      } catch (e) {
        console.error('获取规则详情失败', e);
        message.error('获取规则配置失败');
      }
    };

    // 显示AI提示词编辑弹窗
    const showPromptModal = () => {
      tempCustomPrompt.value = customPrompt.value;
      // 使用setTimeout延迟显示模态框，确保document对象已完全初始化
      setTimeout(() => {
        promptModalVisible.value = true;
      }, 100);
    };

    // 确认编辑并生成AI内容
    const handlePromptModalOk = async () => {
      customPrompt.value = tempCustomPrompt.value;
      promptModalVisible.value = false;
      await generateAiContent();
    };

    // 取消编辑
    const handlePromptModalCancel = () => {
      promptModalVisible.value = false;
      // 使用安全的方式延迟重置焦点
      setTimeout(() => {
        if (typeof document !== 'undefined') {
          try {
            const aiBtn = document.querySelector('.btn-ai-create');
            if (aiBtn) {
              (aiBtn as HTMLElement).focus();
            }
          } catch (e) {
            console.error('Focus error:', e);
          }
        }
      }, 200);
    };

    // 生成AI内容的实际方法
    const generateAiContent = async () => {
      loading.value = true;
      selectedIndex.value = -1;
      message.loading('生成ai内容中...', 0);

      try {
        const res = await ruleStore.sendMemberAction({
          id: props.record?.meta?.rule_id || localValue.value,
          action: 'generate_content_by_template_prompt',
          config: {
            data: {
              prompt: customPrompt.value || undefined,
            },
          },
        } as any);

        if (res.data && Array.isArray(res.data.contents)) {
          aiContents.value = res.data.contents;
        } else {
          console.error('AI内容格式不正确:', res.data);
          message.error('生成内容格式不正确');
        }
      } catch (e) {
        console.error('生成AI内容失败:', e);
        message.error('生成AI内容失败');
      } finally {
        loading.value = false;
        message.destroy();
      }
    };

    const getContentByAi = async () => {
      try {
        // 确保有选中规则
        if (!localValue.value) {
          return message.warning('请先选择消息规则');
        }

        // 标记当前调用是由AI创作触发的
        isFromAiCreation.value = true;

        // 强制重新获取最新规则数据
        await fetchRuleDetails(localValue.value as string | number);

        // 重置标记
        isFromAiCreation.value = false;

        // 无论是否有预设提示词都显示弹窗
        showPromptModal();
      } catch (e) {
        console.error('AI创作准备失败:', e);
        message.error('获取规则配置失败');
        // 确保在出错的情况下也重置标记
        isFromAiCreation.value = false;
      }
    };

    const onSend = async () => {
      if (!localValue.value || !localDate.value) {
        return message.warning('请完善消息参数');
      }
      loading.value = true;
      try {
        const updatedParams = {
          ...props.params,
          rule_id: localValue.value, // 确保包含最新的规则ID
          title: messageTitle.value,
          markdown: messageBody.value,
          content: localContent.value, // 确保包含内容，避免后端重新生成
          send_at: localDate.value.toISOString(), // 确保使用最新的日期值
        };

        await props.record.update({ meta: updatedParams });
        await props.store
          .sendMemberAction({
            id: props.record.id,
            action: 'activate',
          })
          .then((res: any) => {
            // 不直接修改props.record，而是创建一个新的对象
            const updatedRecord = {
              ...props.record,
              source_id: res.data.source_id,
            };

            // 使用新对象来触发update:record事件
            emit('update:record', updatedRecord);

            // 使用ChatEventCenter强制触发父组件的UI重新渲染
            if (updatedRecord.source_id) {
              // 先关闭当前视图，再重新打开，触发重新渲染
              ChatEventCenter.emit('toggleArtifactShow', { bool: false });
              // 短暂延迟后重新打开，确保UI状态更新
              setTimeout(() => {
                ChatEventCenter.emit('toggleArtifactShow', {
                  bool: true,
                  artifact: updatedRecord,
                });
              }, 100);
            }
          });
      } finally {
        loading.value = false;
      }
    };

    const handleAiContentClick = (item: string, index: number) => {
      // 先更新本地状态
      localContent.value = item;
      selectedIndex.value = index;

      // 然后更新父组件
      emit('update:params', { ...props.params, content: item });

      // 使用DOM API确保文本区域内容更新
      setTimeout(() => {
        if (contentTextarea.value) {
          // 直接设置textarea的值
          contentTextarea.value.value = item;

          // 触发input事件，确保组件内部状态更新
          const event = new Event('input', { bubbles: true });
          contentTextarea.value.dispatchEvent(event);
        }
      }, 0);
    };

    // 添加afterClose回调处理模态框关闭后的焦点管理
    const handleModalAfterClose = () => {
      // 延迟处理，避免DOM操作问题
      setTimeout(() => {
        // 仅在非SSR环境下执行DOM操作
        if (typeof document !== 'undefined') {
          try {
            const aiBtn = document.querySelector('.btn-ai-create');
            if (aiBtn) {
              (aiBtn as HTMLElement).focus();
            }
          } catch (e) {
            console.error('Focus error:', e);
          }
        }
      }, 200);
    };

    // 生命周期钩子
    onMounted(() => {
      fetchRuleDetails(localValue.value as string | number);
    });

    // 集中处理所有的 watch
    // 1. 监听 props 的变化，同步到本地状态
    watch(
      () => props.params.rule_id,
      newVal => {
        if (newVal) {
          localValue.value = newVal;
        }
      },
    );

    watch(
      () => props.params.send_at,
      newVal => {
        if (newVal && (!localDate.value || newVal !== localDate.value.toISOString())) {
          localDate.value = dayjs(newVal);
        }
      },
    );

    watch(
      () => props.params.content,
      newVal => {
        if (newVal !== undefined) {
          localContent.value = newVal;
        }
      },
    );

    // 2. 监听本地状态变化，同步回 props 或执行其他操作
    watch(localDate, newVal => {
      if (newVal) {
        emit('update:params', { ...props.params, send_at: newVal.toISOString() });
      }
    });

    watch(localValue, newVal => {
      if (newVal) {
        fetchRuleDetails(newVal as string | number);
      }
    });

    return {
      ...toRefs(props),
      localValue,
      tableItems,
      localDate,
      onSend,
      loading,
      localContent,
      customPrompt,
      messageTitle,
      messageBody,
      AI_ICON,
      getContentByAi,
      aiContents,
      handleAiContentClick,
      selectedIndex,
      isFromAiCreation,
      // 弹窗相关属性和方法
      promptModalVisible,
      tempCustomPrompt,
      handlePromptModalOk,
      handlePromptModalCancel,
      handleModalAfterClose,
      contentTextarea,
    };
  },
});
export default ComMessageSenderTool;
</script>

<template lang="pug">
.com-message-sender-tool.px-4.py-6.flex-grow.h-0.flex.flex-col
  .pack__card(v-if='record.source_id')
    ComServePackShowCardWrapper(:source-id='record.source_id')
  .tool__form.space-y-4.overflow-y-auto.flex-grow.h-0(v-else)
    .form__item
      .label__name.text-gray-900.font-medium.mb-1
        span.text-red-500 *
        | 消息规则
      TaApiSingleField(
        :value="localValue"
        @update:value="localValue = $event"
        recordName='消息规则',
        path='/serve/manage/rules',
        :multiple='false',
        :attrs='["name", "options"]',
        :tableItems='tableItems'
      )

    .form__item
      .label__name.text-gray-900.font-medium.mb-1
        span.text-red-500 *
        | 发送时间
      a-date-picker(show-time, v-model:value='localDate', placeholder='选择发送时间', format="YYYY-MM-DD HH:mm:ss")
    .form__item
      .label__name.text-gray-900.font-medium.mb-1 发送对象
      .text-gray-900.text-sm {{ record.function_params.recipient_query }}
    .form__item
      .label__name.text-gray-900.font-medium.mb-1 工作通知消息标题
      a-input(
        v-model:value='messageTitle',
        placeholder='消息标题，用于显示在浙政钉通知标题中'
      )
    .form__item
      .label__name.text-gray-900.font-medium.mb-1 工作通知消息体
      a-textarea(
        v-model:value='messageBody',
        :auto-size='{ minRows: 2, maxRows: 5 }',
        placeholder='消息体，用于显示在浙政钉通知中'
      )
    .form__item
      .label__name.text-gray-900.font-medium.mb-1 发送内容
      a-textarea(
        :value="localContent"
        @input="(e) => { localContent = e.target.value; emit('update:params', { ...props.params, content: e.target.value }); }"
        :auto-size='{ minRows: 4, maxRows: 10 }'
        placeholder='如果消息内容为空，则会使用规则中设置的消息内容'
        ref="contentTextarea"
      )

    .form__item
      .mb-2.flex.space-x-2
        a-button(
          type='primary'
          @click='getContentByAi'
          :loading='loading'
          class="btn-ai-create"
        ) AI创作
        a-button(
          type='primary'
          @click='onSend'
          :loading='loading'
        ) 发送
      .space-y-2
        template(v-for='(item, index) in aiContents')
          a-card.flex-shrink-0(
            :class='{ "card-selected": selectedIndex === index }',
            :hoverable='true',
            @click='handleAiContentClick(item, index)'
          )
            span.mb-1
              img.w-5.h-5(:src='AI_ICON', alt='icon')
            .content__card__content {{ item }}

  // AI提示词编辑弹窗
  a-modal(
    v-model:visible='promptModalVisible'
    title='编辑AI提示词'
    @ok='handlePromptModalOk'
    @cancel='handlePromptModalCancel'
    :confirm-loading='loading'
    :maskClosable="false"
    :afterClose="handleModalAfterClose"
  )
    a-form
      a-form-item(label='AI提示词')
        a-textarea(
          v-model:value='tempCustomPrompt',
          :auto-size='{ minRows: 5, maxRows: 10 }',
          placeholder='编辑AI提示词，用于指导AI内容生成'
        )
</template>

<style lang="stylus" scoped>
.com-message-sender-tool
  .card-selected
    border 2px solid #1890ff
  .ant-card
    &:hover
      border-color #40a9ff
    &.cursor-pointer
      cursor pointer
    &[disabled]
      cursor not-allowed
      opacity 0.5
  .content__card__content
    &[contenteditable='true']
      outline none
      padding 2px 4px
      &:focus
        border-bottom 1px solid #1890ff
</style>
