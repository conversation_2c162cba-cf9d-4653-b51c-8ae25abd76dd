<script lang="ts">
import { ref, defineComponent, toRefs, computed, onMounted, onUnmounted } from 'vue';
import { ServeManageUserActivitiesApi } from '@/engines/iest/serve-core/apis/serve/manage/user/activities.api';
import { VObject } from '../../../lib/vails/model/index';
import { VStore } from '../../../lib/vails/store/index';
import dayjs from 'dayjs';

const ComActivityListTool = defineComponent({
  name: 'ComActivityListTool',
  components: {},
  props: {
    record: {
      type: Object,
      default: () => ({}),
    },
    store: {
      type: Object,
      default: () => ({}),
    },
    params: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props) {
    // 创建一个简单的state对象，不依赖BotStateKey
    const state = ref<VObject>({ artifactWidth: '600px' });

    // 创建素材查询的API和Store
    const activityStore = new VStore(new ServeManageUserActivitiesApi());

    // 配置TaIndexView
    const config = computed(() => {
      return {
        recordName: '素材列表',
        store: activityStore,
        mode: 'list',
        params: {
          // 不在这里设置查询参数，而是通过store.query.value来管理
        },
        pagination: {
          perPage: 10,
          hideOnSinglePage: false,
        },
        list: {
          scroll: {
            y: 'auto',
          },
        },
      };
    });

    // 详情弹窗状态
    const showDetailModal = ref(false);
    const currentRecord = ref<VObject | null>(null);

    onMounted(() => {
      // 设置artifact宽度
      state.value.artifactWidth = '600px';

      // 加载素材列表，传递查询参数
      const queryParams = props.params?.q || {};

      // 设置查询条件
      const finalQuery = {
        s: ['id desc'], // 默认排序
        ...queryParams, // 合并传入的查询条件
      };

      // 先设置查询参数到store
      activityStore.query.value = finalQuery;

      // 执行查询
      activityStore
        .index()
        .then(() => {
          // 如果没有数据，显示友好提示
          if (activityStore.totalCount.value === 0) {
            console.log('ComActivityListTool 查询结果为空，显示提示信息');
          }
        })
        .catch(error => {
          console.error('ComActivityListTool 查询失败:', error);
          console.error('ComActivityListTool 错误详情:', {
            message: error.message,
            response: error.response,
            status: error.response?.status,
            data: error.response?.data,
          });
        });
    });

    onUnmounted(() => {
      // 恢复默认宽度
      state.value.artifactWidth = undefined;
    });

    // 点击素材卡片显示详情
    const onShow = (record: VObject) => {
      currentRecord.value = record;
      showDetailModal.value = true;

      // 如果有外部链接，直接打开
      if (record.model_payload?.link_to_origin === 'true' && record.model_payload?.link) {
        activityStore.find(record.id).then(() => {
          window.open(record.model_payload.link);
        });
      }
    };

    // 关闭详情弹窗
    const handleModalClose = () => {
      showDetailModal.value = false;
      currentRecord.value = null;
    };

    // 格式化日期
    const formatDate = (date: string) => {
      return dayjs(date).format('YYYY-MM-DD');
    };

    // 状态映射
    const stateMapping: Record<string, string> = {
      draft: '草稿',
      pending: '待发布',
      published: '已发布',
      archived: '已归档',
      completed: '已完成',
      processing: '进行中',
      created: '已创建',
    };

    // 格式化状态
    const formatState = (state: string) => {
      return stateMapping[state] || state;
    };

    // 查看原文
    const handleViewOriginal = () => {
      if (currentRecord.value?.model_payload?.link) {
        window.open(currentRecord.value.model_payload.link, '_blank');
      } else {
        console.warn('原文链接不存在');
      }
    };

    return {
      ...toRefs(props),
      config,
      activityStore,
      showDetailModal,
      currentRecord,
      onShow,
      handleModalClose,
      formatDate,
      formatState,
      handleViewOriginal,
      dayjs,
    };
  },
});
export default ComActivityListTool;
</script>

<template lang="pug">
.com-activity-list-tool.px-4.h-full.flex.flex-col

  .tool-content.flex-grow.h-0
    TaIndexView(:config='config', @onShow='onShow', :showHeader='false')
      template(#card='{ record }')
        .activity-card.p-4.border.border-gray-200.rounded-lg.cursor-pointer.transition-shadow(
          class='hover:shadow-md'
        )
          .card-header.flex.items-center.justify-between.mb-2
            .title.text-sm.font-medium.text-gray-800.flex-grow.mr-2 {{ record.name }}
            .date.text-xs.text-gray-500 {{ formatDate(record.published_at || record.created_at) }}
          .card-content
            .tags.flex.flex-wrap.gap-1.mb-2(v-if='record.tags && record.tags.length > 0')
              .tag.px-2.py-1.bg-blue-100.text-blue-800.text-xs.rounded(
                v-for='tag in record.tags.slice(0, 3)',
                :key='tag.id'
              ) {{ tag.name }}
              .tag.px-2.py-1.bg-gray-100.text-gray-600.text-xs.rounded(
                v-if='record.tags.length > 3'
              ) +{{ record.tags.length - 3 }}
            .summary.text-xs.text-gray-600.line-clamp-2(v-if='record.ai_summary')
              | {{ record.ai_summary }}

  // 详情弹窗
  a-modal(
    :visible='showDetailModal',
    :width='900',
    @cancel='handleModalClose',
    :destroyOnClose='true',
    :title='currentRecord?.name || "素材详情"',
    :footer='null',
    centered,
    :bodyStyle='{ maxHeight: "70vh", overflowY: "auto" }'
  )
    .activity-detail(v-if='showDetailModal && currentRecord')
      .detail-header.mb-4
        .title.text-lg.font-semibold.mb-2 {{ currentRecord.name }}
        .meta.flex.items-center.gap-4.text-sm.text-gray-500
          .date 发布时间：{{ formatDate(currentRecord.published_at || currentRecord.created_at) }}
          .state(v-if='currentRecord.state') 状态：{{ formatState(currentRecord.state) }}

      .detail-tags.mb-4(v-if='currentRecord.tags && currentRecord.tags.length > 0')
        .label.text-sm.font-medium.mb-2 标签：
        .tags.flex.flex-wrap.gap-2
          .tag.px-3.py-1.bg-blue-100.text-blue-800.text-sm.rounded(
            v-for='tag in currentRecord.tags',
            :key='tag.id'
          ) {{ tag.name }}

      .detail-summary.mb-4(v-if='currentRecord.ai_summary')
        .label.text-sm.font-medium.mb-2 AI摘要：
        .content.text-sm.text-gray-700.leading-relaxed {{ currentRecord.ai_summary }}

      .detail-content(v-if='currentRecord.content')
        .label.text-sm.font-medium.mb-2 内容：
        .content.text-sm.text-gray-700.leading-relaxed.max-h-96.overflow-y-auto
          template(v-if='Array.isArray(currentRecord.content.content)')
            .content-module(v-for='(module, index) in currentRecord.content.content', :key='index')
              .text-content(v-if='module.body', v-html='module.body')
              .images-content(v-if='module.images && module.images.length > 0')
                img.max-w-full.h-auto.mb-2(
                  v-for='(image, imgIndex) in module.images',
                  :key='imgIndex',
                  :src='image.url || image',
                  :alt='image.alt || "图片"'
                )
              .videos-content(v-if='module.videos && module.videos.length > 0')
                video.max-w-full.h-auto.mb-2(
                  v-for='(video, videoIndex) in module.videos',
                  :key='videoIndex',
                  controls,
                  :src='video.url || video'
                )
              .audios-content(v-if='module.audios && module.audios.length > 0')
                audio.w-full.mb-2(
                  v-for='(audio, audioIndex) in module.audios',
                  :key='audioIndex',
                  controls,
                  :src='audio.url || audio'
                )
          .single-content(v-else, v-html='currentRecord.content.content')

      .detail-attachments(
        v-if='currentRecord.attachments && currentRecord.attachments.files && currentRecord.attachments.files.length > 0'
      )
        .label.text-sm.font-medium.mb-2 附件：
        .attachments.space-y-2
          .attachment.flex.items-center.gap-2(
            v-for='file in currentRecord.attachments.files',
            :key='file.id || file.fileKey'
          )
            .file-icon.text-blue-500 📎
            a.text-blue-600.text-sm(:href='file.url', target='_blank', class='hover:text-blue-800')
              | {{ file.name || file.fileName || '下载附件' }}

      .detail-actions.mt-6.flex.justify-end.gap-2
        a-button(@click='handleModalClose') 关闭
        a-button(
          type='primary',
          v-if='currentRecord.model_payload?.link',
          @click='handleViewOriginal'
        ) 查看原文
</template>

<style lang="stylus" scoped>
.com-activity-list-tool
  .activity-card
    &:hover
      .title
        color #1890ff
  .line-clamp-2
    display -webkit-box
    -webkit-line-clamp 2
    -webkit-box-orient vertical
    overflow hidden
.activity-detail
  .detail-content
    .content
      word-break break-word
      // 处理HTML内容样式
      :deep(p)
        margin-bottom 8px
      :deep(img)
        max-width 100%
        height auto
        margin 8px 0
      :deep(a)
        color #1890ff
        text-decoration none
        &:hover
          text-decoration underline
</style>
