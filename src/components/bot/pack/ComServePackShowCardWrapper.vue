<script lang='ts'>
import { ServeManagePacksApi } from '@/engines/iest/serve-core/apis/serve/manage/packs.api';
import { ServePackModel } from '@/engines/iest/serve-core/models/serve/manage/packs';
import { VStore } from '@/lib/vails';
import { ref, defineComponent, toRefs, onMounted } from 'vue';
import ComServePackShowCard from '@/engines/iest/components/serve/packs/ComServePackShowCard.vue';
import ComBpmInstanceDetailDialog from '@/engines/bpm/components/ComBpmInstanceDetailDialog.vue';

const ComServePackShowCardWrapper = defineComponent({
  name: 'ComServePackShowCardWrapper',
  components: { ComServePackShowCard, ComBpmInstanceDetailDialog },
  props: {
    sourceId: { type: String, default: '' },
  },
  setup(props) {
    const store = new VStore(new ServeManagePacksApi(), ServePackModel)
    onMounted(() => {
      store.find(props.sourceId)
    })
    const instanceVisible = ref(false)
    return {
      ...toRefs(props),
      record: store.record,
      instanceVisible
    };
  },
});
export default ComServePackShowCardWrapper;
</script>

<template lang="pug">
ComServePackShowCard.cursor-pointer(
  v-if='sourceId && record.id',
  :record='record',
  @click.stop='instanceVisible = true'
)

ComBpmInstanceDetailDialog(
  v-if='instanceVisible'
  v-model:visible='instanceVisible',
  :instanceId='record.create_instance_id'
)
</template>

<style lang="stylus" scoped></style>
