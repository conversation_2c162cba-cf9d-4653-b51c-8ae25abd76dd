<script lang='ts'>
import { ref, defineComponent, toRefs } from 'vue';
import ComColorfulLabel from '@/engines/iest/components/global/ComColorfulLabel.vue'
const ComHomeBpmCard = defineComponent({
  name: 'ComHomeBpmCard',
  components: { ComColorfulLabel },
  props: {
    record: { type: Object, default: () => ({}) }
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
export default ComHomeBpmCard;
</script>

<template lang="pug">
.com-home-bpm-card.py-4
  header.text-sm.text-gray-700.font-medium.mb-1 {{ record.workflow_name }}
  .flex.items-center.text-gray-500
    ComColorfulLabel.py-2px.px-10px.mr-4(
      label='审批',
      color='red',
      :defaultTextColorWeight='500'
    )
    TaIcon.mr-1(type='outline/calendar' class='!w-4 h-4')
    .text-xs {{ record.createdStr }}
</template>

<style lang="stylus" scoped>
.com-home-bpm-card
  border-bottom 1px solid #F3F4F6;

</style>
