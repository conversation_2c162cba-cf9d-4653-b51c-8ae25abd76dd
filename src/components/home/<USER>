<script lang="ts">
import ComHomeCardWrapper from '@/engines/iest/components/iest/ComHomeCardWrapper.vue';
import { computed, defineComponent, onMounted, reactive, toRefs } from 'vue';
import ComHomeIestOfficialCard from './ComHomeIestOfficialCard.vue';

const ComHomeIestOfficial = defineComponent({
  name: 'ComHomeIestOfficial',
  components: { ComHomeCardWrapper, ComHomeIestOfficialCard },
  props: {
    title: { type: String, default: '' },
    url: { type: String, default: '' },
    store: { type: Object, required: true },
  },
  setup(props) {
    const state = reactive({
      公职人员: 0,
      纪检人员: 0,
      标签总数: 0,
    });
    const data = computed(() => [
      {
        name: '公职人员',
        num: state.公职人员,
        icon: 'users-group',
      },
      {
        name: '纪检人员',
        num: state.纪检人员,
        icon: 'user-settings',
      },
      {
        name: '标签总数',
        num: state.标签总数,
        icon: 'tag',
      },
    ]);

    onMounted(() => {
      try {
        props.store
          .sendMemberAction({
            id: 1,
            action: 'ta_resource_statistic',
            config: {
              data: {
                stat_condition: {
                  refs: [
                    {
                      relations: ['users'],
                      item: {
                        key: 'user',
                        caculator: {
                          type: 'caculation',
                          caculations: [
                            {
                              name: '公职人员',
                              method: 'count',
                              scopes: [
                                'find_public_official_users',
                                { in_org_and_descendants: '杭州市' },
                              ],
                              distinct: true,
                            },
                            {
                              name: '纪检人员',
                              method: 'count',
                              filter: { res_tags_name_eq: '纪检人员' },
                              distinct: true,
                            },
                          ],
                        },
                      },
                    },
                    {
                      relations: ['res_tags'],
                      item: {
                        key: 'tag',
                        caculator: {
                          type: 'caculation',
                          caculations: [{ name: 'count', method: 'count' }],
                        },
                      },
                    },
                  ],
                },
              },
            },
          })
          .then((res: any) => {
            state.公职人员 = res.data.user?.公职人员;
            state.纪检人员 = res.data.user?.纪检人员;
            state.标签总数 = res.data.tag?.count;
          });
      } catch (e) {
        console.log(e);
      }
    });
    return {
      ...toRefs(props),
      data,
    };
  },
});
export default ComHomeIestOfficial;
</script>

<template lang="pug">
ComHomeCardWrapper(:title='title', :url='url')
  .grid.grid-cols-3.gap-4.pt-4
    ComHomeIestOfficialCard(v-for='item in data', :item='item')
</template>

<style lang="stylus" scoped></style>
