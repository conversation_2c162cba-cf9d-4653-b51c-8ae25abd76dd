<script lang="ts">
import ComBpmInstanceCreator from '@/engines/bpm/components/ComBpmInstanceCreator.vue';
import useDDLink from '@/engines/dingtalk/utils/useDDLink';
import ComHomeCardWrapper from '@/engines/iest/components/iest/ComHomeCardWrapper.vue';
import { message } from 'ant-design-vue';
import { defineComponent, nextTick, ref, toRefs } from 'vue';
import { useRouter } from 'vue-router';

interface ActionItem {
  label: string;
  icon: string;
  bg: string;
  action: () => void;
}

const ComHomeActions = defineComponent({
  name: 'ComHomeActions',
  components: { ComHomeCardWrapper, ComBpmInstanceCreator },
  props: {},
  setup(props) {
    const router = useRouter();
    const selectedWorkflowId = ref<number>(0);
    const creator = ref<any>(null);
    const onCreateInstance = (workflowId: number) => {
      selectedWorkflowId.value = workflowId;
      message.loading('正在打开...', 1);
      nextTick(() => creator.value.onCreateInstance());
    };

    const { linkTo } = useDDLink();

    const items: ActionItem[] = [
      {
        label: '查风险',
        icon: 'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iest/%E6%9F%A5%E9%A3%8E%E9%99%A9.png',
        bg: '#FF8A4C',
        action: () => {
          router.push(`/iest/manage/paperworks`);
        },
      },
      {
        label: '发消息',
        icon: 'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iest/%E5%8F%91%E6%B6%88%E6%81%AF.png',
        bg: '#8DA2FB',
        action: () => {
          linkTo(`${process.env.VUE_APP_PUBLIC_PATH}serve/manage/ai_packs`);
        },
      },
      {
        label: '供素材',
        icon: 'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iest/%E4%BA%A4%E7%B4%A0%E6%9D%90.png',
        bg: '#16BDCA',
        action: () => {
          onCreateInstance(1);
        },
      },
      {
        label: '录规则',
        icon: 'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iest/%E5%BD%95%E8%A7%84%E5%88%99.png',
        bg: '#76A9FA',
        action: () => {
          onCreateInstance(2);
        },
      },
      {
        label: '找素材',
        icon: 'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iest/%E6%89%BE%E7%B4%A0%E6%9D%90.png',
        bg: '#AC94FA',
        action: () => {
          router.push('/serve/manage/activities');
        },
      },
      {
        label: '找人员',
        icon: 'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iest/%E6%89%BE%E4%BA%BA%E5%91%98.png',
        bg: '#F17EB8',
        action: () => {
          router.push('/res/user');
        },
      },
      // {
      //   label: 'DeepSeek',
      //   icon: 'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/discipline-inspection/deepseek-copy.png',
      //   bg: '#4d6bfe',
      //   action: () => {
      //     linkTo('https://deepseek.tallty.com/');
      //   },
      // },
    ];
    return {
      ...toRefs(props),
      items,
      creator,
      selectedWorkflowId,
    };
  },
});
export default ComHomeActions;
</script>

<template lang="pug">
ComHomeCardWrapper.overflow-x-scroll(:showHeader='false')
  .flex.justify-between.com-home-actions
    .item.flex.flex-col.items-center.px-4.cursor-pointer(v-for='item in items', @click='item.action')
      .item__bg.w-14.h-14.rounded-2xl.p-3.mb-2(:style='{backgroundColor: item.bg}')
        .w-full.h-full.bg-cover.bg-center(:style='{backgroundImage: `url(${item.icon})`}')
      .text-sm.text-gray-900 {{ item.label }}
  ComBpmInstanceCreator(
    ref='creator',
    :workflowId='selectedWorkflowId',
    :canSaveAndCreateNext='true'
  )
</template>

<style lang="stylus" scoped>
@property --angle {
  syntax: '<angle>';
  initial-value: 0deg;
  inherits: false;
}
.com-home-actions .item:nth-child(2) > .item__bg
  background linear-gradient(var(--angle,100deg), rgba(26, 86, 219, 0.71) 11.95%, rgba(199, 45, 212, 0.66) 85.32%) !important
  animation rotate 2s infinite linear
@keyframes rotate {
  to {
    --angle: 360deg;
  }
}
</style>
