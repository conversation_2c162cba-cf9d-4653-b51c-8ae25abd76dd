<script lang='ts'>
import { ref, defineComponent, toRefs, PropType } from 'vue';
interface Item {
  name: string;
  num: number;
  icon: string;
}
const ComHomeIestOfficialCard = defineComponent({
  name: 'ComHomeIestOfficialCard',
  components: {},
  props: {
    item: {
      type: Object as PropType<Item>,
      required: true,
    }
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
export default ComHomeIestOfficialCard;
</script>

<template lang="pug">
.com-home-iest-official-card.rounded-lg.bg-gray-50.px-5.py-3.flex.items-center
  .icon.w-12.h-12.rounded-lg.bg-primary-100.p-3.mr-3
    TaIcon.text-primary-700(:type='`flowbite-v2-solid/${item.icon}`' class='!w-6 !h-6')
  .name__and__num.leading-tight
    .text-2xl.font-bold.text-gray-900.mb-1 {{ item.num }}
    .text-sm.text-gray-500 {{ item.name }}
</template>

<style lang="stylus" scoped>
.com-home-iest-official-card
  border 1px solid #E5E7EB
</style>
