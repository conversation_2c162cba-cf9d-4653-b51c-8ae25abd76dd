<script lang="ts">
import { useCable } from '@/engines/base/channels/useCable';
import ComBpmInstanceDetailDialog from '@/engines/bpm/components/ComBpmInstanceDetailDialog.vue';
import ComColorfulLabel from '@/engines/iest/components/global/ComColorfulLabel.vue';
import ComHomeCardWrapper from '@/engines/iest/components/iest/ComHomeCardWrapper.vue';
import ComServeMessageContent from '@/engines/iest/components/serve/messages/ComServeMessageContent.vue';
import '@/engines/iest/views/table.styl';
import { message } from 'ant-design-vue';
import { cloneDeep } from 'lodash-es';
import { computed, defineComponent, onMounted, PropType, reactive, ref, toRefs } from 'vue';
import ComServePacksCard from '../../engines/iest/components/serve/packs/ComServePacksCard.vue';
import { VObject } from '../../lib/vails/model/index';
import { VStore } from '../../lib/vails/store/index';

const ComHomeNotify = defineComponent({
  name: 'ComHomeNotify',
  components: {
    ComHomeCardWrapper,
    ComServePacksCard,
    ComColorfulLabel,
    ComBpmInstanceDetailDialog,
    ComServeMessageContent,
  },
  props: {
    title: { type: String, default: '' },
    url: { type: String, default: '' },
    packStore: { type: Object as PropType<VStore>, required: true },
    msgStore: { type: Object as PropType<VStore>, required: true },
  },
  setup(props) {
    const BG_URL = 'url(https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iest/image%2065.png)';
    const newMsgStore = cloneDeep(props.msgStore);
    const visible = ref(false);
    const state = reactive({
      batch: 0,
      read: 0,
      unread: 0,
    });
    const config = computed(() => ({
      store: props.packStore,
      mode: 'list',
      pagination: {
        perPage: 3,
        hide: true,
      },
      list: {
        gap: 8,
      },
    }));

    const tabs = computed(() => [
      {
        label: '批次记录',
        key: 'batch',
      },
      {
        label: '消息记录',
        key: 'msg',
        store: props.msgStore,
        mode: 'table',
        template: 'serve_message#home',
      },
    ]);

    const fetchStatistic = () => {
      state.batch = props.packStore.totalCount.value;
      props.msgStore
        .sendCollectionAction({
          action: 'list_index',
          config: {
            data: {
              collection_stat_condition: {
                items: [
                  {
                    key: 'state',
                    caculator: {
                      type: 'caculation',
                      caculations: [
                        { name: 'read', filter: { is_read_true: 1 }, method: 'count' },
                        { name: 'unread', filter: { is_read_false: 1 }, method: 'count' },
                      ],
                    },
                  },
                ],
              },
            },
          },
        })
        .then((res: any) => {
          try {
            const { read, unread } = res.data.statistics.state;
            state.read = read;
            state.unread = unread;
          } catch (e) {
            console.error(e);
          }
        });
    };

    onMounted(fetchStatistic);
    const onIndex = (data: VObject) => {
      fetchStatistic();
    };

    const instanceVisible = ref(false);
    const activeRecord = ref<any>({});

    // 消息详情弹窗相关
    const msgDetailVisible = ref(false);
    const activeMsgRecord = ref<any>({});

    const onShow = (record: VObject) => {
      // 处理消息记录点击
      if (record.flag === 'msg') {
        activeMsgRecord.value = record;
        msgDetailVisible.value = true;
        return;
      }

      // 处理批次记录点击
      if (!record.create_instance_id) {
        return message.warning('暂无对应工作流');
      }

      // if (record.stateConfig.label === "已发送") {
      //   onCheck(record, {})
      //   return;
      // }
      instanceVisible.value = true;
      activeRecord.value = record;
    };

    const taIndexView = ref<any>(null);

    props.packStore.extra.cable_key = 'serve_packs';
    props.msgStore.extra.cable_key = 'serve_messages';

    useCable(props.packStore, { taIndexViewRef: taIndexView });
    useCable(props.msgStore, { taIndexViewRef: taIndexView });

    const msgDrawerParams = ref({});
    const onCheck = (record: VObject, params: VObject) => {
      msgDrawerParams.value = {
        q: {
          pack_id_eq: record.id,
          ...params,
        },
      };
      visible.value = true;
      activeRecord.value = record;
    };
    return {
      ...toRefs(props),
      config,
      tabs,
      BG_URL,
      state,
      visible,
      onShow,
      activeRecord,
      instanceVisible,
      onIndex,
      taIndexView,
      newMsgStore,
      onCheck,
      msgDrawerParams,
      msgDetailVisible,
      activeMsgRecord,
    };
  },
});
export default ComHomeNotify;
</script>

<template lang="pug">
ComHomeCardWrapper(:title='title', :url='url')
  .com-home-notify.pt-4.iest__table__skin
    .flex.items-center
      .flex.items-center.px-4.py-3.rounded-lg.flex-grow(style='background-color: #f5f8ff')
        .icon.w-8.h-8.mr-2.bg-cover
        .name__and__num
          .name.text-sm 批次
          .num.text-2xl.text-bold {{ state.batch > 0 ? state.batch : 0 }}
      .name__and__num.px-4.flex-grow
        .name.text-sm 已读
        .num.text-lg.text-bold {{ state.read }}
      .name__and__num.px-4.flex-grow
        .name.text-sm 未读
        .num.text-lg.text-bold {{ state.unread }}
    TaIndexView.ta-index-view-skin(
      ref='taIndexView',
      :config='config',
      :tabs='tabs',
      :showHeader='false',
      @onShow='onShow',
      @onIndex='onIndex'
    )
      template(#card='{ record }')
        ComServePacksCard.cursor-pointer(
          :record='record',
          @check='params => onCheck(record, params)'
        )
      template(#bodyCell='{ record, column }')
        ComColorfulLabel.px-10px.py-2px(
          v-if='column.dataIndex[0] === "is_read"',
          :label='record.is_read ? "已读" : "未读"',
          :color='record.is_read ? "green" : "red"'
        )

    ComMsgWithPackIdDrawer(
      v-model:visible='visible',
      :msgStore='newMsgStore',
      :params='msgDrawerParams',
      :record='activeRecord'
    )
    ComBpmInstanceDetailDialog(
      v-if='instanceVisible',
      v-model:visible='instanceVisible',
      :instanceId='activeRecord.create_instance_id'
    )

    // 消息详情弹窗
    a-modal(
      v-model:visible='msgDetailVisible',
      :width='1100',
      :title='activeMsgRecord?.name || "消息详情"',
      :footer='null',
      centered,
      :destroyOnClose='true'
    )
      .message-modal-content(v-if='msgDetailVisible && activeMsgRecord')
        ComServeMessageContent(:record='activeMsgRecord')
</template>

<style lang="stylus" scoped>
.com-home-notify {
  :deep(.list-view__pagination_placeholder) {
    display: none;
  }

  .icon {
    background-image: v-bind(BG_URL);
  }

  .name__and__num {
    .name {
      color: rgba(35, 56, 118, 0.5);
    }

    .num {
      font-family: 'DIN Alternate';
      color: #25396F;
    }
  }
}
</style>
