<script lang='ts'>
import { ref, defineComponent, toRefs, computed } from 'vue';
import ComHomeCardWrapper from '@/engines/iest/components/iest/ComHomeCardWrapper.vue';
import ComHomeBpmCard from './ComHomeBpmCard.vue';
import { Instance } from '../../engines/bpm/bpm-core/types';
import ComBpmInstanceDetailEasyDialogFromIndex from '../../engines/bpm/components/ComBpmInstanceDetailEasyDialogFromIndex.vue';


const ComHomeBpm = defineComponent({
  name: 'ComHomeBpm',
  components: {
    ComHomeCardWrapper,
    ComHomeBpmCard,
    ComBpmInstanceDetailEasyDialogFromIndex
  },
  props: {
    title: { type: String, default: '' },
    url: { type: String, default: '' },
    approvingStore: { type: Object, default: () => { } },
    createdStore: { type: Object, default: () => { } },
    notifiedStore: { type: Object, default: () => { } },
  },
  setup(props) {
    const config = computed(() => ({
      store: props.approvingStore,
      mode: 'list',
      pagination: {
        perPage: 5,
        hide: true,
      },
    }))

    const tabs = computed(() => [
      {
        label: '待我处理',
        key: 'approving'
      },
      {
        label: '抄送我的',
        key: 'notified',
        store: props.notifiedStore,
      },
      {
        label: '我发起的',
        key: 'created',
        store: props.createdStore,
      },
    ])

    const visible = ref<Record<string, boolean>>({})
    const onShow = (record: Instance & { flowable_user_info: { mode: string } }) => {
      if (!record.flowable_user_info || record.flowable_user_info?.mode === 'instance') {
        visible.value[`instance-${record.id}`] = true;
      } else {
        visible.value[`token-${record.id}`] = true;
      }
    };
    return {
      ...toRefs(props),
      config,
      tabs,
      onShow,
      visible,
    };
  },
});
export default ComHomeBpm;
</script>

<template lang="pug">
ComHomeCardWrapper(:title='title',:url='url')
  TaIndexView(
    :config='config',
    :tabs='tabs',
    :showHeader='false',
    @onShow='onShow'
  )
    template(#card='{record,actions}')
      ComHomeBpmCard.cursor-pointer(:record='record')
      ComBpmInstanceDetailEasyDialogFromIndex(
        v-if='visible[`token-${record.id}`]',
        v-model:visible='visible[`token-${record.id}`]',
        :instance='record',
        @close='actions.silenceRefresh'
      )

      ComBpmInstanceDetailEasyDialogFromIndex(
        v-if='visible[`instance-${record.id}`]',
        v-model:visible='visible[`instance-${record.id}`]',
        :instance='record',
        @close='actions.silenceRefresh'
      )
</template>

<style lang="stylus" scoped></style>
