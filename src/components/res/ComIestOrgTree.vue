<script lang='ts'>
import { VObject } from '@/lib/vails';
import { ref, defineComponent, toRefs, onMounted, watch } from 'vue';
import { groupBy } from 'lodash-es';


const ComIestOrgTree = defineComponent({
  name: 'ComIestOrgTree',
  components: {},
  props: {
    store: { type: Object, required: true },
    display: { type: Boolean, default: true },
  },
  emits: ['select', 'display'],
  setup(props, { emit }) {
    const treeData = ref<any>([])
    const expandedKeys = ref<string[]>([]);
    const selectedKeys = ref<string[]>([]);
    onMounted(() => {
      props.store.index({ per_page: 120 }).then(() => {
        const records = props.store.records?.value || [];

        const buildTree = (parentId: number | null) => {
          return records
            .filter((record: VObject) => record.parent_id === parentId)
            .map((record: VObject) => ({
              title: record.name,
              key: record.id,
              children: buildTree(record.id) // 递归调用
            }));
        };

        const rootRecord = records.find((r: any) => !records.find((rr: any) => rr.id === r.parent_id))

        treeData.value = [
          {
            title: '全部',
            key: 0,
            children: buildTree(rootRecord.id) // 从根节点开始构建树
          }
        ];
      })
    })

    const handleSelect = (_selectedKeys: VObject, e: VObject) => {
      selectedKeys.value = [e.node.key];
      emit('select', e.node.key);
    };

    const toggleDisplay = () => {
      emit('display')
    }
    return {
      ...toRefs(props),
      treeData,
      expandedKeys,
      selectedKeys,
      handleSelect,
      toggleDisplay,
    };
  },
});
export default ComIestOrgTree;
</script>

<template lang="pug">
.com-iest-org-tree.iest-card-container.overflow-y-scroll.relative
  a-directory-tree(
    v-model:expandedKeys="expandedKeys"
    v-model:selectedKeys="selectedKeys"
    :tree-data="treeData",
    @select='handleSelect'
  )
  .absolute.right-2.top-2.z-99
    TaIcon(
      :type='display?"VerticalRightOutlined":"VerticalLeftOutlined"',
      @click='toggleDisplay'
    )
</template>

<style lang="stylus" scoped></style>
