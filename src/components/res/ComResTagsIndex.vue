<script lang="ts">
import ComResUserIndex from '@/components/res/ComResUserIndex.vue';
import ComColorfulLabel from '@/engines/iest/components/global/ComColorfulLabel.vue';
import ComServePackShowDrawer from '@/engines/iest/components/serve/packs/ComServePackShowDrawer.vue';
import '@/engines/iest/views/table.styl';
import { computed, defineComponent, ref, toRefs } from 'vue';

const ComResTagsIndex = defineComponent({
  name: 'ComResTagsIndex',
  components: { ComColorfulLabel, ComServePackShowDrawer, ComResUserIndex },
  props: {
    store: { type: Object, default: () => {} },
    userStore: { type: Object, default: () => {} },
    extraParams: { type: Object, default: () => {} },
  },
  setup(props) {
    const config = computed(() => ({
      recordName: '人员标签',
      store: props.store,
      mode: 'table',
      params: {
        q: {
          ...(props.extraParams?.q || {}),
        },
      },
      template: 'res_tag#index',
      actions: [
        // { key: 'create', enabled: true },
        // { key: 'update', enabled: true },
        // { key: 'delete', enabled: true },
        // { key: 'import', enabled: true },
        // { key: 'export', enabled: true },
      ],
      table: {
        scroll: { y: 'auto' },
      },
      searcherSimpleOptions: [
        { key: 'name', label: '标签名称', type: 'string' },
        // { key: 'account', label: '手机号', type: 'string' },
      ],
      // searcherAccordionUseDrawer: true,
      // searcherComplicatedOptions: [
      //   {
      //     key: 'orgs_id',
      //     label: '组织',
      //     type: 'select',
      //     path: '/iest/manage/orgs',
      //     multiple: true,
      //   },
      // ],
    }));

    const drawerVisible = ref(false);
    const drawerRecord = ref<any>({});
    const onDrawerOpen = (record: any) => {
      drawerVisible.value = true;
      drawerRecord.value = record;
    };
    return {
      ...toRefs(props),
      config,
      drawerVisible,
      drawerRecord,
      onDrawerOpen,
    };
  },
});
export default ComResTagsIndex;
</script>

<template lang="pug">
.com-res-tags-index.iest__table__skin.flex.flex-col
  TaNoPaddingDrawer(
    v-if='drawerVisible'
    v-model:visible='drawerVisible',
    width='900'
    :title='drawerRecord.name + "人员"'
  )
    ComResUserIndex(
      :store='userStore',
      :needDefaultParams='false',
      :operatorTag='true',
      :extraParams='{ q: { res_tags_id_eq: drawerRecord.id } }'
    )
  .px-4.pt-4.bg-white.flex-grow.h-0.rounded-lg
    TaIndexView.ta-index-view-skin(:config='config')
      template(#bodyCell='{ record, column,text }')
        ComColorfulLabel.px-10px.py-2px.cursor-pointer(
          v-if='column.dataIndex[0] === "users_count"'
          :label='text || 0'
          color='blue',
          @click.stop='onDrawerOpen(record)'
        )
        //- .text-sm(v-if='column.dataIndex.includes("names")') {{ record[column.dataIndex[0]]?.join('、') }}
        //- template(v-else-if='column.dataIndex[0].includes("count")')
        //-   .flex.w-full
        //-     ComColorfulLabel.px-10px.py-2px.cursor-pointer(
        //-       :label='text || 0',
        //-       color='blue',
        //-       @click.stop='onDrawerOpen(record, column.dataIndex[0])'
        //-     )
        //- .text-sm(v-else-if='column.dataIndex[0] === "res_tags"') {{record.res_tags?.map(tag => tag.name).join('、')}}
  //- ComServePackShowDrawer(
  //-   v-model:visible='drawerVisible',
  //-   :title='drawerRecord.name + "消息"'
  //-   :record='drawerRecord',
  //-   :packStore='fakePackStore',
  //-   :params='params',
  //-   width='900'
  //- )
</template>

<style lang="stylus" scoped>
.com-res-tags-index
  height 100%
  width 100%
</style>
