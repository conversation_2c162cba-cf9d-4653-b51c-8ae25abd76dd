<script lang="ts">
import ComColorfulLabel from '@/engines/iest/components/global/ComColorfulLabel.vue';
import ComServePackShowDrawer from '@/engines/iest/components/serve/packs/ComServePackShowDrawer.vue';
import { ServePackModel } from '@/engines/iest/serve-core/models/serve/manage/packs';
import '@/engines/iest/views/table.styl';
import { ResAdminTagsRelationApi } from '@/engines/res/res-core/apis/res/admin/tags_relation.api';
import { VStore } from '@/lib/vails';
import { message } from 'ant-design-vue';
import { computed, defineComponent, ref, toRefs } from 'vue';

const ComResUserIndex = defineComponent({
  name: 'ComResUserIndex',
  components: { ComColorfulLabel, ComServePackShowDrawer },
  props: {
    store: { type: Object, default: () => {} },
    needDefaultParams: { type: Boolean, default: true },
    operatorTag: { type: Boolean, default: false },
    extraParams: { type: Object, default: () => {} },
  },
  setup(props) {
    const taIndexView = ref<any>(null);
    const config = computed(() => ({
      recordName: '对象库',
      store: props.store,
      mode: 'table',
      params: {
        q: {
          ...(props.needDefaultParams
            ? { scopes: ['find_public_official_users', { in_org_and_descendants: '杭州市' }] }
            : {}),
          ...(props.extraParams?.q || {}),
        },
      },
      template: 'user#index',
      table: {
        scroll: { y: 'auto' },
      },
      searcherSimpleOptions: [
        { key: 'name', label: '姓名', type: 'string' },
        { key: 'account', label: '手机号', type: 'string' },
      ],
      searcherAccordionUseDrawer: true,
      searcherComplicatedOptions: [
        {
          key: 'orgs_id',
          label: '组织',
          type: 'select',
          path: '/iest/manage/orgs',
          multiple: true,
        },
      ],
    }));

    const drawerVisible = ref(false);
    const drawerRecord = ref<any>({});
    const params = ref({});
    const fakePackStore = {
      model: ServePackModel,
    };
    const onDrawerOpen = (record: any, type: string) => {
      drawerRecord.value = record;
      drawerVisible.value = true;
      if (type.includes('unread')) {
        params.value = {
          q: {
            user_id_eq: record.id,
            is_read_false: 1,
          },
        };
      } else {
        params.value = {
          q: {
            user_id_eq: record.id,
            is_read_true: 1,
          },
        };
      }
    };

    // 删除用户的标签关系
    const handleDelete = (record: any) => {
      const tagId = props.extraParams?.q?.res_tags_id_eq;
      if (!tagId) {
        message.error('无法获取标签ID');
        return;
      }
      const tagsRelationStore = new VStore(new ResAdminTagsRelationApi());
      tagsRelationStore
        .sendCollectionAction({
          action: 'destroy_relation',
          config: {
            data: {
              user_id: record.id,
              tag_id: tagId,
            },
          },
        })
        .then(data => {
          if (data.data.success) {
            message.success('删除成功');
            taIndexView.value.silenceRefresh();
          } else {
            console.error('删除失败:', data.data.error);
            message.error('删除失败');
          }
        })
        .catch(error => {
          console.error('请求错误:', error);
        });
    };

    return {
      ...toRefs(props),
      taIndexView,
      config,
      drawerVisible,
      drawerRecord,
      onDrawerOpen,
      fakePackStore,
      params,
      handleDelete,
    };
  },
});
export default ComResUserIndex;
</script>

<template lang="pug">
.com-res-user-index.iest__table__skin.flex.flex-col
  .px-4.pt-4.bg-white.flex-grow.h-0.rounded-lg
    TaIndexView.ta-index-view-skin(ref='taIndexView' ,:config='config')
      template(#bodyCell='{ record, column, text }')
        .text-sm(v-if='column.dataIndex.includes("names")') {{ record[column.dataIndex[0]]?.join('、') }}
        template(v-else-if='column.dataIndex[0].includes("count")')
          .flex.w-full.justify-center
            ComColorfulLabel.px-10px.py-2px.cursor-pointer(
              :label='text || 0',
              color='blue',
              @click.stop='onDrawerOpen(record, column.dataIndex[0])'
            )
        template(v-else-if='operatorTag && column.dataIndex[0] === "operator"')
          .flex.w-full.justify-center
            a-tooltip(title='删除')
              TaPopoverConfirm(title='删除', content='确认删除该标签关联内容？', @confirm='() => handleDelete(record)')
                button.delete-btn.text-gray-500.hover_text-red-500.transition-colors(@click.stop='')
                  TaIcon(type='DeleteOutlined')
        .text-sm(v-else-if='column.dataIndex[0] === "res_tags"') {{record.res_tags?.map(tag => tag.name).join('、')}}
  ComServePackShowDrawer(
    v-model:visible='drawerVisible',
    :title='drawerRecord.name + "消息"'
    :record='drawerRecord',
    :packStore='fakePackStore',
    :params='params',
    width='900'
  )
</template>

<style lang="stylus" scoped>
.com-res-user-index
  height 100%
  width 100%
</style>
