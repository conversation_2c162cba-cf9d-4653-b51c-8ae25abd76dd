<script lang='ts'>
import { ref, defineComponent, toRefs, computed } from 'vue';
const ComTaContentFieldDyna = defineComponent({
  name: 'ComTaContentFieldDyna',
  components: {},
  props: {
    value: { type: String, default: '' },
    disabled: { type: Boolean, default: false },
  },
  setup(props, { emit }) {
    const localValue = computed({
      get() {
        return props.value;
      }, set(val) {
        emit('update:value', val)
      }
    })

    const onChange = (val: any) => {
      localValue.value = val.map((item: any) => item.body).join('')
    }
    return {
      ...toRefs(props),
      localValue,
      onChange,
    };
  },
});
export default ComTaContentFieldDyna;
</script>

<template lang="pug">
TaContentField(
  v-if='Array.isArray(localValue)',
  :disabled="disabled"
  v-model:value="localValue",
  :hideVideo='true',
  :hideAudio='true',
  :hideFile='true'
)
TaContentField(
  v-else,
  :disabled='disabled',
  :value='[{body:localValue}]',
  @update:value='onChange'
  :hideVideo='true',
  :hideAudio='true',
  :hideFile='true'
)
</template>

<style lang="stylus" scoped></style>
