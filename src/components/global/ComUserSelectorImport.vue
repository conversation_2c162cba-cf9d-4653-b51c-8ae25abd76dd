<script lang="ts">
import { ComUserRedisResourcesApi } from '@/apis/com/user/redis_resources.api';
import { VStore } from '@/lib/vails';
import { VObject } from '@/lib/vails/model';
import { VRequestResponse } from '@/lib/vails/request';
import { message } from 'ant-design-vue';
import { computed, defineComponent, nextTick, PropType, ref, toRefs, watch } from 'vue';
import { TaIndexImportHeader } from './ta-component/TaIndexView/types';
import { IFile } from './ta-component/file/servers';
import { TaTemplateFormItem } from './ta-component/ta-template-form-core/types';

interface UserRecord {
  id: string | number;
  name: string;
  account?: string;
  mobile?: string;
  member_pos_job?: string;
  department_names?: string;
  org_names?: string;
  res_tags?: Array<{ name: string }>;
}

interface ImportResponse {
  data: {
    uid?: string;
    headers?: any[];
    titles?: string[];
    current_page?: number;
    total_count?: number;
    total_pages?: number;
    records?: any[];
  };
}

const ComUserSelectorImport = defineComponent({
  name: 'ComUserSelectorImport',
  props: {
    value: { type: String, default: '' },
    store: { type: Object as PropType<VStore<any>>, required: true },
    headers: { type: Array as PropType<TaIndexImportHeader[]>, default: () => [] },
    template: { type: Object as PropType<TaTemplateFormItem>, default: undefined },
  },
  emits: ['update:value', 'success'],
  setup(props, { emit }) {
    const localValue = computed({
      get: () => props.value,
      set: (value: string) => emit('update:value', value),
    });

    const uid = ref('');
    const visibleImport = ref(false);
    const loading = ref(false);
    // excel 的表头
    const dataTitles = ref<string[]>([]);
    // excel 的数据
    const rowData = ref<VObject[]>([]);
    // 有改动的 key 值数组，用 index 到 dataHeaders 找对应 name
    const keysChanged = ref<string[]>([]);
    const currentPage = ref(0);
    const totalCount = ref(0);
    const totalPages = ref(0);
    const localHeaders = ref<TaIndexImportHeader[]>([]);
    const optionalHeaders = computed(() => [...dynamicHeaders.value, { key: '', name: null }]);
    const optionalHeadersOptions = computed(() =>
      optionalHeaders.value.map((header: TaIndexImportHeader) => ({
        label: header.name,
        value: header.key,
        disabled: header.key === '' ? false : keysChanged.value.includes(header.key),
      })),
    );

    const dynamicHeaders = computed(() =>
      props.headers.length === 0 ? localHeaders.value : props.headers,
    );

    const primaryKeysOptions = ref<{ name: string | null; key: string }[]>([]);
    const selectedPrimaryKeys = ref<string[]>([]);

    const fetchHeaders = () => {
      if (props.headers.length === 0) {
        return props.store
          .sendCollectionAction({
            action: 'import_headers',
            config: {
              data: { ta_template: props.template },
            },
          })
          .then((res: VRequestResponse) => {
            localHeaders.value = res.data.headers;
            primaryKeysOptions.value = res.data.headers.map((header: TaIndexImportHeader) => ({
              name: header.name,
              key: header.key,
            }));
          });
      } else {
        primaryKeysOptions.value = props.headers.map((header: TaIndexImportHeader) => ({
          name: header.name,
          key: header.key,
        }));
        selectedPrimaryKeys.value = props.headers
          .filter((header: TaIndexImportHeader) => header.primary_key)
          .map((header: TaIndexImportHeader) => header.key);
        return Promise.resolve();
      }
    };

    const setDefaultOptionsValue = () => {
      keysChanged.value = [];
      dataTitles.value.forEach((dataTitle: string) => {
        const existHeader = dynamicHeaders.value.find(
          (i: TaIndexImportHeader) => i.name === dataTitle,
        );
        const key = existHeader ? existHeader.key : '';
        keysChanged.value.push(key);
      });
    };

    watch(dynamicHeaders, setDefaultOptionsValue, { deep: true, immediate: true });
    watch(dataTitles, setDefaultOptionsValue, { deep: true, immediate: true });

    const currentStep = ref(1);
    const checkResult = ref<any>(null);
    const redisResourcesConfigMap = ref<Record<string, any>>({});

    const appendIds = ref<number[]>([]);
    const appendRecords = computed<UserRecord[]>({
      get: () => {
        try {
          return appendIds.value.map(id => ({
            id,
            name: '', // 默认空名称
          }));
        } catch (error) {
          console.error('获取appendRecords时出错:', error);
          return [];
        }
      },
      set: (value: UserRecord[]) => {
        try {
          // 过滤掉无效ID
          appendIds.value = value
            .map(item => {
              const id = Number(item.id);
              return isNaN(id) ? null : id;
            })
            .filter(Boolean) as number[];

          // 打印选中的记录，便于调试
        } catch (error) {
          console.error('设置appendRecords时出错:', error);
          // 发生错误时保留原值
        }
      },
    });

    const finalRedisKey = computed(() => {
      return props.value || checkResult.value.redis_key;
    });

    const onCheck = () => {
      // 显示加载提示
      message.loading({ content: '正在验证数据，请稍候...', duration: 0, key: 'checkLoading' });
      loading.value = true;

      props.store
        .sendCollectionAction({
          action: 'import_precheck',
          config: {
            params: {
              redis_key: finalRedisKey.value,
              uid: uid.value,
              headers: resultHeaders.value,
            },
          },
        })
        .then((res: VRequestResponse) => {
          checkResult.value = res.data;

          // 关闭加载提示
          message.destroy('checkLoading');
          loading.value = false;
          handleCheckResult(checkResult.value);
          return res;
        })
        .catch(error => {
          console.error('验证数据失败:', error);
          loading.value = false;
          // 关闭加载提示并显示错误消息
          message.error({ content: '验证数据失败，请重试', key: 'checkLoading' });
        });
    };

    const onImport = () => {
      if (!fileItems.value?.[0]?.file) return;
      loading.value = true;

      // 显示全局加载提示
      message.loading({ content: '正在上传文件，请稍候...', duration: 0, key: 'importLoading' });

      const formData = new FormData();
      formData.append('file', fileItems.value[0].file);
      props.store
        .sendCollectionAction({
          action: 'upload_excel',
          config: { data: formData },
        })
        .then((res: VRequestResponse) => {
          uid.value = res.data.uid;

          // 更新加载提示
          message.loading({ content: '正在处理文件...', duration: 0, key: 'importLoading' });

          fetchHeaders()
            .then(() => {
              rawDataIndex()
                .then((res: VRequestResponse) => {
                  dataTitles.value = res.data.titles;
                  currentStep.value = 1;
                  loading.value = false;

                  // 关闭加载提示并显示成功消息
                  message.success({ content: '上传文件完成！', key: 'importLoading' });
                })
                .catch(error => {
                  loading.value = false;
                  console.error('获取表格数据异常:', error);
                  // 关闭加载提示并显示错误消息
                  message.error({ content: '获取表格数据异常！', key: 'importLoading' });
                });
            })
            .catch(error => {
              loading.value = false;
              console.error('获取导入表头失败:', error);
              // 关闭加载提示并显示错误消息
              message.error({ content: '获取导入表头失败', key: 'importLoading' });
            });
        })
        .catch(error => {
          loading.value = false;
          console.error('上传文件失败:', error);
          // 关闭加载提示并显示错误消息
          message.error({ content: '上传文件失败！', key: 'importLoading' });
        });
    };

    const onCancel = () => {
      resetImport();
    };

    const resetImport = () => {
      selectedAddIds.value = [];
      selectedAddIds.value = [];
      selectedRemoveIds.value = [];
      appendIds.value = [];
      visibleImport.value = false;
      uid.value = '';
      keysChanged.value = [];
      currentStep.value = 0;
      fileItems.value = [];
      missingUsers.value = [];
      hasValidData.value = false;
      checkResult.value = {
        succeed: true,
        redis_key: '',
        data: {
          dup: {},
          missing_keywords: [],
        },
      };
    };

    const resultHeaders = computed(() => {
      const resultHeaders: TaIndexImportHeader[] = [];
      const headers = JSON.parse(JSON.stringify(dynamicHeaders.value));

      keysChanged.value.forEach((key: string, index: number) => {
        if (!key) {
          return;
        }
        const newTitle = dataTitles.value[index];
        const targetHeader = headers.find((header: TaIndexImportHeader) => header.key === key);
        targetHeader.name = newTitle;
        resultHeaders.push(targetHeader);
      });

      selectedPrimaryKeys.value.forEach((key: string) => {
        const targetHeader = resultHeaders.find(
          (header: TaIndexImportHeader) => header.key === key,
        );
        if (targetHeader) {
          targetHeader.primary_key = true;
        }
      });

      return resultHeaders;
    });

    const onConfirm = () => {
      confirmLoading.value = true;

      // 如果是重名用户选择步骤，需要先保存选择的用户
      if (currentStep.value === 2) {
        const api = new ComUserRedisResourcesApi({
          params: { redis_key: finalRedisKey.value },
        });

        // 如果有选中的用户才进行保存
        const savePromise =
          appendIds.value.length > 0 ? api.create({ ids: appendIds.value }) : Promise.resolve();

        // 处理选择结果
        savePromise
          .then(() => {
            // 检查是否有未找到的用户
            if (missingUsers.value.length > 0) {
              // 如果有未找到的用户，进入未找到用户确认页面
              confirmLoading.value = false;
              currentStep.value = 3;
              message.info('已处理重名用户，但还有部分用户未找到，请确认是否继续');
            } else {
              // 没有未找到的用户，完成导入
              localValue.value = finalRedisKey.value;
              setTimeout(() => {
                visibleImport.value = false;
                resetImport();
                confirmLoading.value = false;
                nextTick(() => {
                  emit('success');
                });
              }, 500);
            }
          })
          .catch(error => {
            console.error('保存选中用户失败:', error);
            confirmLoading.value = false;
            message.error('保存选中用户失败，请重试');
          });
        return;
      }

      // 如果有redis_key则直接使用（非重名用户选择步骤）
      if (finalRedisKey.value) {
        localValue.value = finalRedisKey.value;
        setTimeout(() => {
          visibleImport.value = false;
          resetImport();
          confirmLoading.value = false;
          nextTick(() => {
            emit('success');
          });
        }, 500);
      } else {
        confirmLoading.value = false;
        message.error('未找到有效的导入数据');
      }
    };

    const rawDataIndex = (page = 1, perPage = 15) => {
      return props.store
        .sendCollectionAction({
          action: 'excel',
          config: { params: { uid: uid.value, page: page, per_page: perPage } },
        })
        .then((res: VRequestResponse) => {
          currentPage.value = res.data.current_page;
          totalCount.value = res.data.total_count;
          totalPages.value = res.data.total_pages;
          rowData.value = res.data.records;
          return res;
        });
    };

    const onTableChange = (page = 1, _: VObject, perPage = 15) => {
      rawDataIndex(page, perPage);
    };

    const loadingDownloadTemplate = ref(false);

    const reverseAndJoinArray = (arr: any[]) => {
      if (!Array.isArray(arr)) return arr;
      return [...arr].reverse().join(' > ');
    };

    const onDownloadTemplate = () => {
      loadingDownloadTemplate.value = true;
      props.store
        .sendCollectionAction({
          action: 'export_import_template',
          config: {
            responseType: 'arraybuffer',
            data: { ta_template: props.template },
          } as any,
        })
        .then((res: ImportResponse) => {
          loadingDownloadTemplate.value = false;
          const url = window.URL.createObjectURL(
            new Blob([res.data as unknown as string], {
              type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            }),
          );
          const a = document.createElement('a');
          a.style.display = 'none';
          a.href = url;
          document.body.appendChild(a);
          a.click();
          document.body.removeChild(a);
        })
        .catch((error: Error) => {
          loadingDownloadTemplate.value = false;
          throw error;
        });
    };

    const fileItems = ref<IFile[]>([]);

    const onShow = () => {
      // 重置状态，确保每次打开都是新的状态
      resetImport();
      try {
        visibleImport.value = true;
        currentStep.value = 0;
        // 初始状态从0开始，上传文件
      } catch (error) {
        console.error('打开导入对话框失败:', error);
        message.error('打开导入对话框失败，请刷新页面重试');
      }
    };

    let asyncTimer: any = null;
    const selectedAddIds = ref<number[]>([]);
    const selectedRemoveIds = ref<number[]>([]);
    // 添加确认按钮的loading状态
    const confirmLoading = ref(false);

    const selectedAdd = (record: VObject) => {
      selectedAddIds.value.push(record.id);
      setSyncTimer();
    };

    const selectedRemove = (record: VObject) => {
      selectedRemoveIds.value.push(record.id);
      setSyncTimer();
    };

    const setSyncTimer = () => {
      if (asyncTimer) {
        clearTimeout(asyncTimer);
      }
      asyncTimer = setTimeout(() => {
        // 对比 addIds 和 removeIds
        const finalAddIds = selectedAddIds.value.filter(
          (id: number) => !selectedRemoveIds.value.includes(id),
        );
        const finalRemoveIds = selectedRemoveIds.value.filter(
          (id: number) => !selectedAddIds.value.includes(id),
        );

        const api = new ComUserRedisResourcesApi({
          params: { redis_key: finalRedisKey.value },
        });

        const promise: any[] = [];

        if (finalAddIds.length > 0) {
          promise.push(api.create({ ids: finalAddIds }));
        }
        if (finalRemoveIds.length > 0) {
          promise.push(
            api.sendCollectionAction('batch_destroy', {
              params: { redis_resources: { ids: finalRemoveIds } },
            }),
          );
        }
        if (promise.length === 0) {
          return;
        }
        loading.value = true;
        Promise.all(promise)
          .then(() => {
            message.success('同步成功');
            selectedAddIds.value = [];
            selectedRemoveIds.value = [];
          })
          .catch(() => {
            message.error('同步失败');
          })
          .finally(() => {
            loading.value = false;
          });
      }, 500);
    };

    // 存储未找到的用户名列表
    const missingUsers = ref<string[]>([]);
    // 标记是否有有效数据可以导入
    const hasValidData = ref(false);

    // 处理导入预检查结果
    const handleCheckResult = (result: any) => {
      checkResult.value = result;

      if (!result.succeed) {
        const hasDup = Object.keys(result.data.dup || {}).length > 0;
        const hasMissing = (result.data.missing_keywords || []).length > 0;

        // 无论是否有重复用户，都先保存未找到的用户信息，供后续使用
        if (hasMissing) {
          missingUsers.value = result.data.missing_keywords || [];
          hasValidData.value = !!result.redis_key;
        }

        if (hasDup) {
          // 清空之前的配置
          redisResourcesConfigMap.value = {};

          // 记录需要加载的总数量以显示进度
          const totalNames = Object.keys(result.data.dup).length;
          let loadedCount = 0;

          // 进度提示
          message.loading({
            content: `正在加载重名用户数据 (0/${totalNames})...`,
            duration: 0,
            key: 'loadDupUsers',
          });

          // 处理每个重名用户
          Object.keys(result.data.dup).forEach((name: string) => {
            const redisKey = result.data.dup[name];

            // 设置配置（简化版）
            redisResourcesConfigMap.value[name] = {
              loading: true,
              data: [],
              selectedKeys: [], // 存储选中的ID
              error: '',
            };

            // 加载数据（直接方式）
            const api = new ComUserRedisResourcesApi({
              params: {
                redis_key: redisKey,
                no_page: 'true', // 使用不分页模式获取全部数据
              },
            });

            api
              .index()
              .then((res: any) => {
                let userData = [];
                try {
                  // 解析数据
                  if (res && res.data) {
                    // 三种可能的数据格式
                    if (Array.isArray(res.data)) {
                      userData = res.data;
                    } else if (res.data.records && Array.isArray(res.data.records)) {
                      userData = res.data.records;
                    } else if (typeof res.data === 'object') {
                      userData = [res.data];
                    }
                  }

                  // 处理每个用户对象，确保数据完整
                  userData = userData.map((item: any, index: number) => {
                    // 如果对象有resource字段，可能嵌套了真实用户数据
                    if (item && item.resource && typeof item.resource === 'object') {
                      item = item.resource;
                    }

                    return {
                      ...item,
                      id: item.id || `temp_${index}`,
                      // 确保基本字段存在
                      name: item.name || '未知用户',
                      account: item.account || '-',
                      // 处理可能的数组数据，转换为字符串
                      department_names: Array.isArray(item.department_names)
                        ? item.department_names.join('、')
                        : item.department_names || '-',
                      org_names: Array.isArray(item.org_names)
                        ? item.org_names.join('、')
                        : item.org_names || '-',
                      member_pos_job: Array.isArray(item.member_pos_job)
                        ? item.member_pos_job.join('、')
                        : item.member_pos_job || '-',
                      // 删除标签文本
                      // tags_text: tagsText,
                    };
                  });

                  redisResourcesConfigMap.value[name].data = userData;
                } catch (error) {
                  console.error(`处理${name}数据时出错:`, error);
                  redisResourcesConfigMap.value[name].error = `解析数据时出错: ${error}`;
                }

                redisResourcesConfigMap.value[name].loading = false;

                // 更新计数器和进度提示
                loadedCount++;
                message.loading({
                  content: `正在加载重名用户数据 (${loadedCount}/${totalNames})...`,
                  duration: 0,
                  key: 'loadDupUsers',
                });

                // 如果全部加载完成，关闭提示
                if (loadedCount >= totalNames) {
                  message.success({ content: '数据加载完成', key: 'loadDupUsers' });
                }
              })
              .catch((err: any) => {
                console.error(`${name}数据加载失败:`, err);
                redisResourcesConfigMap.value[name].error = `加载数据失败: ${
                  err.message || '未知错误'
                }`;
                redisResourcesConfigMap.value[name].loading = false;

                // 更新计数器和进度提示
                loadedCount++;
                message.loading({
                  content: `正在加载重名用户数据 (${loadedCount}/${totalNames})...`,
                  duration: 0,
                  key: 'loadDupUsers',
                });

                // 如果全部加载完成，关闭提示
                if (loadedCount >= totalNames) {
                  message.success({ content: '数据加载完成', key: 'loadDupUsers' });
                }
              });
          });

          // 设置步骤并显示提示
          currentStep.value = 2;
          message.info('发现重名用户，请选择具体要导入的用户');
          return;
        } else if (hasMissing) {
          // 如果没有重名用户，但有未找到的用户，直接跳到未找到用户的步骤
          currentStep.value = 3;
          message.info('发现部分用户未找到，请确认是否继续');
          return;
        }
      }

      // 如果全部验证通过
      if (result.redis_key) {
        emit('update:value', result.redis_key);
        emit('success');
        currentStep.value = 1;
        message.success('导入成功');
        visibleImport.value = false;
      }
    };

    // 更新选择的用户列表
    const updateSelectedUsers = () => {
      try {
        // 收集所有选中的ID
        const selectedIds: number[] = [];

        Object.keys(redisResourcesConfigMap.value).forEach(name => {
          const config = redisResourcesConfigMap.value[name];
          if (config.selectedKeys && Array.isArray(config.selectedKeys)) {
            selectedIds.push(...config.selectedKeys);
          }
        });

        // 更新全局选中ID
        appendIds.value = selectedIds;
      } catch (error) {
        console.error('更新选择的用户列表时出错:', error);
      }
    };

    const steps = computed(() => [
      { key: 'upload', label: '上传文件' },
      { key: 'import', label: '导入' },
      ...(checkResult.value?.succeed === false &&
      Object.keys(checkResult.value?.data?.dup || {}).length > 0
        ? [{ key: 'check', label: '选择用户' }]
        : []),
      ...(checkResult.value?.succeed === false &&
      (checkResult.value?.data?.missing_keywords || []).length > 0
        ? [{ key: 'missing', label: '确认导入' }]
        : []),
    ]);

    // 切换行选择状态
    const toggleRowSelection = (id: string | number, selectedKeys: any[]) => {
      try {
        const index = selectedKeys.indexOf(id);
        if (index > -1) {
          selectedKeys.splice(index, 1);
        } else {
          selectedKeys.push(id);
        }
        updateSelectedUsers();
      } catch (error) {
        console.error('切换行选择状态时出错:', error);
      }
    };

    // 切换全选/反选
    const toggleSelectAll = (data: any[], selectedKeys: any[]) => {
      try {
        if (selectedKeys.length === data.length) {
          // 已全选，则清空
          selectedKeys.splice(0, selectedKeys.length);
        } else {
          // 未全选，则全选
          selectedKeys.splice(0, selectedKeys.length);
          data.forEach(item => {
            selectedKeys.push(item.id);
          });
        }
        updateSelectedUsers();
      } catch (error) {
        console.error('切换全选状态时出错:', error);
      }
    };

    // 格式化文件大小的辅助函数
    const formatFileSize = (bytes?: number): string => {
      if (!bytes) return '未知';

      const units = ['B', 'KB', 'MB', 'GB'];
      let size = bytes;
      let unitIndex = 0;

      while (size >= 1024 && unitIndex < units.length - 1) {
        size /= 1024;
        unitIndex++;
      }

      return `${size.toFixed(2)} ${units[unitIndex]}`;
    };

    // 处理未找到用户的确认导入
    const confirmMissingAction = () => {
      if (hasValidData.value) {
        // 有效数据，继续导入流程
        if (finalRedisKey.value) {
          localValue.value = finalRedisKey.value;
          visibleImport.value = false;
          resetImport();
          message.success('部分用户已成功导入');
          nextTick(() => {
            emit('success');
          });
        }
      } else {
        // 无效数据，显示错误
        message.error('没有有效的用户数据可以导入');
        resetImport();
      }
    };

    // 处理未找到用户的取消导入
    const cancelMissingAction = () => {
      resetImport();
      message.info('已取消导入');
    };

    return {
      ...toRefs(props),
      loading,
      visibleImport,
      dataTitles,
      rowData,
      keysChanged,
      currentPage,
      totalCount,
      totalPages,
      steps,
      currentStep,
      fileItems,
      onImport,
      onCancel,
      onShow,
      onConfirm,
      onTableChange,
      resultHeaders,
      dynamicHeaders,
      optionalHeadersOptions,
      primaryKeysOptions,
      selectedPrimaryKeys,
      loadingDownloadTemplate,
      onCheck,
      checkResult,
      onDownloadTemplate,
      redisResourcesConfigMap,
      appendIds,
      appendRecords,
      selectedAdd,
      selectedRemove,
      handleCheckResult,
      updateSelectedUsers,
      toggleRowSelection,
      toggleSelectAll,
      formatFileSize,
      confirmLoading,
      missingUsers,
      hasValidData,
      confirmMissingAction,
      cancelMissingAction,
      reverseAndJoinArray,
    };
  },
});

export default ComUserSelectorImport;
</script>

<template lang="pug">
.ta-import
  a-tooltip
    template(#title) 导入
    .uploader
      TaTextButton(:icon='loading ? "LoadingOutlined" : "UploadOutlined"', @click='onShow')
        .whitespace-nowrap 导入
      slot
  a-modal(
    v-model:visible='visibleImport',
    :width='1280',
    title='导入',
    @cancel='onCancel',
    destroyOnClose
  )
    // 添加全局loading遮罩
    a-spin(:spinning='loading', :tip='`处理中，请稍候...`')
      .header.px-100.mb-4
        a-steps(:current='currentStep')
          a-step(v-for='step in steps', :key='step.key', :title='step.label')

      //- 根据步骤显示不同内容
      .main(v-if='currentStep === 0')
        .pb-8.pt-6.px-4.flex.justify-around
          .text-lg.text-gray-800.font-medium(class='w-1/3')
            .mb-3 1. 下载模板
            a-button(
              type='primary',
              :loading='loadingDownloadTemplate',
              @click='onDownloadTemplate'
            )
              TaIcon(type='DownloadOutlined')
              | 下载模板
          .text-lg.text-gray-800.font-medium(class='w-1/3')
            .mb-3 2. 上传文件
            TaFileUploader(v-model:value='fileItems', :multiple='false')
            //- 文件大小提示
            .text-sm.text-gray-500.mt-2(v-if='fileItems.length > 0')
              //- | 文件大小: {{ formatFileSize(fileItems[0]?.file?.size) }}
              .text-xs.mt-1.text-yellow-500(v-if='fileItems[0]?.file?.size > 10 * 1024 * 1024')
                | 注意: 大文件上传处理可能需要较长时间，请耐心等待

      .main(v-else-if='currentStep === 1')
        a-form(labelAlign='left')
          a-form-item(label='选择参考字段', :label-col='{ span: 3 }', :wrapper-col='{ span: 18 }')
            a-select(
              v-model:value='selectedPrimaryKeys',
              mode='multiple',
              placeholder='请选择参考字段',
              style='width: 460px',
              :disabled='headers.length > 0'
            )
              a-select-option(
                v-for='(item, index) in primaryKeysOptions',
                :key='index',
                :value='item.key'
              )
                | {{ item.name }}

        TaIndexTable(
          :paginatedData='rowData',
          :currentPage='currentPage',
          :totalCount='totalCount',
          :totalPages='totalPages',
          :config='{ showSizeChanger: true }',
          @change='onTableChange'
        )
          a-table-column(
            v-for='(dataTitle, index) in dataTitles',
            :key='index',
            :dataIndex='dataTitle',
            :width='120',
            :title='dataTitle'
          )
          template(#headerCell='{ title }')
            a-select(
              :value='keysChanged[dataTitles.findIndex(t => t == title)]',
              placeholder='请选择表头',
              style='width: 100%; min-width: 100px',
              :options='optionalHeadersOptions',
              @update:value='val => (keysChanged[dataTitles.findIndex(t => t == title)] = val)'
            )
            .table-title {{ title }}

      .main(v-else-if='currentStep === 2')
        .p-6.bg-white.rounded-lg.overflow-y-auto.max-h-60vh
          .mb-6
            .text-xl.font-medium.mb-2 处理重名收件人
            .text-sm.text-gray-600 以下姓名有多个匹配结果，请选择需要包含的收件人（可多选）
          .space-y-6(v-for='(config, name) in redisResourcesConfigMap', :key='name')
            .border.rounded-lg.overflow-hidden.shadow-sm
              .px-4.py-3.bg-gray-50.border-b
                h2.text-lg.font-medium {{ name }}
              .p-4
                a-spin(:spinning='config.loading')
                  .overflow-x-auto(v-if='config.data && config.data.length > 0')
                    a-checkbox-group(
                      v-model:value='config.selectedKeys',
                      @change='updateSelectedUsers'
                    )
                      .flex.items-center.justify-between.mb-2
                        .text-sm.text-gray-600 共 {{ config.data.length }} 条记录，已选择 {{ config.selectedKeys.length }} 条
                        a-button.text-xs(
                          type='link',
                          @click='toggleSelectAll(config.data, config.selectedKeys)'
                        ) {{ config.selectedKeys.length === config.data.length ? '取消全选' : '全选' }}
                      table.w-full.border-collapse.min-w-full(style='table-layout: fixed')
                        thead
                          tr.bg-gray-50
                            th.py-2.px-4.text-left.font-medium.border-b.w-8
                            th.py-2.px-4.text-left.font-medium.border-b(class='w-1/6') 账号
                            th.py-2.px-4.text-left.font-medium.border-b(class='w-1/6') 部门
                            th.py-2.px-4.text-left.font-medium.border-b(class='w-1/6') 岗位
                            th.py-2.px-4.text-left.font-medium.border-b(class='w-1/6') 所属
                        tbody
                          tr(
                            v-for='item in config.data',
                            :key='item.id',
                            @click='toggleRowSelection(item.id, config.selectedKeys)',
                            :class='[{ "bg-blue-50": config.selectedKeys.includes(item.id) }, "hover:bg-gray-50", "cursor-pointer"]'
                          )
                            td.py-3.px-4.border-b.align-middle.w-8
                              a-checkbox(:value='item.id', @click.stop)
                            td.py-3.px-4.border-b.align-middle.whitespace-normal.truncate(class='w-1/6') {{ item.account || '-' }}
                            td.py-3.px-4.border-b.align-middle.whitespace-normal.truncate(class='w-1/6') {{ reverseAndJoinArray(item.department_names) || '-' }}
                            td.py-3.px-4.border-b.align-middle.whitespace-normal.truncate(class='w-1/6') {{ item.member_pos_job || '-' }}
                            td.py-3.px-4.border-b.align-middle.whitespace-normal.truncate(class='w-1/6') {{ item.org_names || '-' }}
                  .text-red-500.text-xs(v-if='config.error') {{ config.error }}

      .main(v-else-if='currentStep === 3')
        .p-6.bg-white.rounded-lg.overflow-y-auto.max-h-60vh
          .mb-6
            .text-xl.font-medium.mb-2 未找到用户确认
            .text-sm.text-gray-600 以下用户在系统中未找到，请确认是否继续导入其他匹配的用户
          .border.rounded-lg.overflow-hidden.shadow-sm
            .px-4.py-3.bg-gray-50.border-b
              h2.text-lg.font-medium 未找到的用户列表
            .p-4
              .overflow-x-auto
                table.w-full.border-collapse.min-w-full(style='table-layout: fixed')
                  thead
                    tr.bg-gray-50
                      th.py-2.px-4.text-left.font-medium.border-b 序号
                      th.py-2.px-4.text-left.font-medium.border-b 用户名称
                  tbody
                    tr(v-for='(user, index) in missingUsers', :key='index', class='hover:bg-gray-50')
                      td.py-3.px-4.border-b.align-middle.w-16 {{ index + 1 }}
                      td.py-3.px-4.border-b.align-middle.whitespace-normal {{ user }}
              .mt-6.p-4.bg-yellow-50.border.border-yellow-200.rounded-lg
                .flex.items-start
                  TaIcon.text-yellow-500.text-xl.mr-2(type='ExclamationCircleOutlined')
                  .flex-1
                    .font-medium.text-yellow-700 注意
                    .text-sm.text-yellow-600.mt-1 上述用户在系统中未找到匹配记录。
                    .text-sm.text-yellow-600.mt-1(v-if='hasValidData') 点击"确认导入"将仅导入其他已匹配的用户。
                    .text-sm.text-yellow-600.mt-1(v-else) 由于没有匹配到任何用户，无法进行导入操作。

    //- 页脚按钮，注意这里的缩进和template(#footer)的位置
    template(#footer)
      template(v-if='currentStep === 0')
        a-button.button(@click='onCancel') 取消
        a-button(
          type='primary',
          :loading='fileItems.length === 1 && loading',
          :disabled='fileItems.length !== 1',
          @click='onImport'
        ) 确定上传
      template(v-else-if='currentStep === 1')
        a-button.button(@click='onCancel') 取消
        a-button(
          type='primary',
          :loading='loading',
          :disabled='fileItems.length !== 1',
          @click='onCheck'
        ) 确认数据
      template(v-else-if='currentStep === 2')
        a-button.button(@click='onCancel') 取消
        a-button(
          type='primary',
          :loading='confirmLoading || store.loading.value',
          @click='onConfirm'
        ) 确定选择
      template(v-else-if='currentStep === 3')
        a-button.button(@click='cancelMissingAction') 取消导入
        a-button(
          type='primary',
          :loading='confirmLoading',
          :disabled='!hasValidData',
          @click='confirmMissingAction'
        ) 确认导入
</template>

<style lang="stylus" scoped>
.hidden
  display none
.ta-import
  cursor pointer
  .uploader
    position relative
    overflow hidden
    display flex
    align-items center
    .icon
      margin-right 5px
    .file-input
      width 100%
      height 100%
      position absolute
      top 0
      left 0
      z-index 9
      display inline
      margin 0
      padding 0
      outline none
      border none
      opacity 0
  .header
    padding 14px 120px
    width 100%
    border-bottom 1px #e8e8e8 solid
    background #eee
  .main
    overflow auto
    padding 0px 20px 20px
    width 100%
    height 100%
.table-title
  padding 10px 10px 0px
  color #808080
.button
  margin-right 5px
// 表格样式
table
  width 100%
  table-layout fixed
  th, td
    word-break break-word
    overflow hidden
  td
    max-width 0
    overflow hidden
    text-overflow ellipsis
</style>
