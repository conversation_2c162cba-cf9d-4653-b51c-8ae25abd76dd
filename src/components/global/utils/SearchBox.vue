<script lang="ts">
import { computed, defineComponent } from 'vue';

export default defineComponent({
  name: 'SearchBox',
  props: {
    modelValue: { type: String, default: '' },
  },
  emits: ['update:modelValue', 'input', 'clear'],
  setup(props, { emit }) {
    return {
      value: computed({
        get: () => props.modelValue,
        set: val => emit('update:modelValue', val),
      }),
      handleInput: (e: Event) => {
        emit('input', e);
      },
      clearSearch: () => {
        emit('clear');
      },
    };
  },
});
</script>

<template lang="pug">
.search-container
  .flex.w-full
    .flex-1.relative
      input.w-full.px-3.py-2.border.rounded-lg(
        type="text"
        placeholder="搜索姓名或账号..."
        :value="value"
        @input="handleInput"
      )
      .absolute.right-3.cursor-pointer.transform(
        v-if="value"
        class="top-1/2 -translate-y-1/2"
        @click="clearSearch"
      )
        ElIcon(i-carbon-close)
</template>

<style lang="stylus" scoped>
.search-container
  width 100%
  .flex-1
    position relative
    input
      transition all 0.3s
      &:focus
        box-shadow 0 0 0 2px rgba(24, 144, 255, 0.2)
</style>
