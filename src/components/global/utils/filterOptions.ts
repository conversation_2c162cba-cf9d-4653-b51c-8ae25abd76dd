// 定义搜索选项的类型接口
export interface SearchOption {
  group: string;
  type: string;
  label: string;
  key: string;
  options: Array<{
    label: string;
    value: string;
  }>;
}

// 定义staff_dictionary的类型接口
interface StaffDictionary {
  职级code表: Record<string, string>;
  政治面貌code表: Record<string, string>;
  性别code表: Record<string, string>;
  人员任职属性表: Record<string, string>;
  人员角色code表: Record<string, string>;
  职务层次code表: Record<string, string>;
  可见性标识code表: Record<string, string>;
  人员数据字典表: Record<string, string>;
}

/**
 * 创建搜索选项
 * @param dictionary 字典数据
 * @returns 搜索选项数组
 */
export function createSearchOptions(dictionary: StaffDictionary): SearchOption[] {
  // 从字典创建职级选项
  const positionLevelOptions = Object.entries(dictionary['职级code表'] || {}).map(
    ([key, value]) => ({
      label: String(value),
      value: key,
    }),
  );

  // 从字典创建政治面貌选项
  const politicalStatusOptions = Object.entries(dictionary['政治面貌code表'] || {}).map(
    ([key, value]) => ({
      label: String(value),
      value: key,
    }),
  );
  const genderOptions = Object.entries(dictionary['性别code表'] || {}).map(([key, value]) => ({
    label: String(value),
    value: key,
  }));
  const budgetedPostCodeOptions = Object.entries(dictionary['人员数据字典表'] || {}).map(
    ([key, value]) => ({
      label: String(value),
      value: key,
    }),
  );
  const empPosEmployeeRoleCodeOptions = Object.entries(dictionary['人员角色code表'] || {}).map(
    ([key, value]) => ({
      label: String(value),
      value: key,
    }),
  );

  // 返回搜索选项数组
  return [
    // 职级选项
    {
      group: '职级',
      type: 'select',
      label: '职级',
      key: 'members_job_level_code',
      options: positionLevelOptions,
    },
    // 政治面貌选项
    {
      group: '政治面貌',
      type: 'select',
      label: '政治面貌',
      key: 'members_political_code',
      options: politicalStatusOptions,
    },
    {
      group: '编制',
      type: 'select',
      label: '编制',
      key: 'members_budgeted_post_code',
      options: budgetedPostCodeOptions,
    },
    // 性别选项
    {
      group: '性别',
      type: 'select',
      label: '性别',
      key: 'members_gender',
      options: genderOptions,
    },
    // 性别选项
    {
      group: '人员角色',
      type: 'select',
      label: '人员角色',
      key: 'employee_role_code',
      options: empPosEmployeeRoleCodeOptions,
    },
  ];
}
