<script lang="ts">
import { computed, defineComponent } from 'vue';
import TaVirtualSelect from './TaVirtualSelect.vue';

interface SearchOption {
  group: string;
  type: string;
  label: string;
  key: string;
  options: Array<{
    label: string;
    value: string;
  }>;
}

interface AdvancedQuery {
  [key: string]: string | string[];
}

export default defineComponent({
  name: 'FilterPanel',
  components: {
    TaVirtualSelect,
  },
  props: {
    localPayload: { type: Object, required: true },
    disabled: { type: Boolean, default: false },
    tableItems: { type: Array, required: true },
    tagTableItems: { type: Array, required: true },
    searchOptions: { type: Array as () => SearchOption[], required: true },
    advancedSearchQuery: { type: Object as () => AdvancedQuery, required: true },
  },
  emits: [
    'toggle-filter-tag',
    'update:department-ids',
    'update:res-tag-ids',
    'clear-all-filter-tags',
  ],
  setup(props, { emit }) {
    const computedDepartmentIds = computed<string[]>(() => {
      const rawValue = props.localPayload?.payload?.department_ids;
      if (Array.isArray(rawValue)) {
        return rawValue
          .map(v => (v !== null && v !== undefined ? String(v) : ''))
          .filter(v => v !== '');
      } else if (rawValue !== null && rawValue !== undefined && rawValue !== '') {
        return [String(rawValue)];
      }
      return [];
    });

    const computedResTagIds = computed<string[]>(() => {
      const rawValue = props.localPayload?.payload?.res_tag_ids;
      if (Array.isArray(rawValue)) {
        return rawValue
          .map(v => (v !== null && v !== undefined ? String(v) : ''))
          .filter(v => v !== '');
      } else if (rawValue !== null && rawValue !== undefined && rawValue !== '') {
        return [String(rawValue)];
      }
      return [];
    });

    const toggleFilterTag = (key: string, value: string) => {
      emit('toggle-filter-tag', key, value);
    };

    const updateDepartmentIds = (value: any) => {
      let processedValue: string[] = [];
      if (Array.isArray(value)) {
        processedValue = value
          .map(v => (v !== null && v !== undefined ? String(v) : ''))
          .filter(Boolean);
      } else if (value !== null && value !== undefined && value !== '') {
        processedValue = [String(value)]; // Wrap single value
      }
      try {
        emit('update:department-ids', processedValue);
      } catch (error) {
        console.error(
          'FilterPanel [updateDepartmentIds] - Error during emit update:department-ids:',
          error,
        );
      }
    };

    const updateResTagIds = (value: any) => {
      let processedValue: string[] = [];
      if (Array.isArray(value)) {
        processedValue = value
          .map(v => (v !== null && v !== undefined ? String(v) : ''))
          .filter(Boolean);
      } else if (value !== null && value !== undefined && value !== '') {
        processedValue = [String(value)]; // Wrap single value
      }
      try {
        emit('update:res-tag-ids', processedValue);
      } catch (error) {
        console.error(
          'FilterPanel [updateResTagIds] - Error during emit update:res-tag-ids:',
          error,
        );
      }
    };

    // 清除筛选条件方法
    const clearFilters = () => {
      emit('update:department-ids', []);
      emit('update:res-tag-ids', []);
      emit('clear-all-filter-tags');
    };

    // 直接返回所有职级选项，不进行过滤
    const allPositionLevelOptions = computed(() => {
      if (!props.searchOptions[0]?.options) return [];
      return props.searchOptions[0].options;
    });

    // 直接返回所有政治面貌选项，不进行过滤
    const allPoliticalStatusOptions = computed(() => {
      if (!props.searchOptions[1]?.options) return [];
      return props.searchOptions[1].options;
    });

    // 编制选项
    const allBudgetedPostOptions = computed(() => {
      if (!props.searchOptions[2]?.options) return [];
      return props.searchOptions[2].options;
    });

    // 所有性别选项
    const allGenderOptions = computed(() => {
      if (!props.searchOptions[3]?.options) return [];
      return props.searchOptions[3].options;
    });

    // 所有人员角色选项
    const allEmployeeRoleOptions = computed(() => {
      if (!props.searchOptions[4]?.options) return [];
      return props.searchOptions[4].options;
    });

    // 工具方法：生成多选查询的ransack键名
    const getRansackKey = (key: string, isMultiple = true) => {
      return isMultiple ? `${key}_in` : `${key}_eq`;
    };

    // 检查值是否在多选数组中
    const isValueSelected = (key: string, value: string) => {
      const queryValue = props.advancedSearchQuery[key];
      // 注意：这里仍然依赖 advancedSearchQuery prop
      if (Array.isArray(queryValue)) {
        return queryValue.includes(value);
      } else if (typeof queryValue === 'string') {
        return queryValue === value;
      }
      return false;
    };

    // 部门筛选参数
    const departmentFilterParams = {
      type_name: '单位',
    };

    return {
      computedDepartmentIds,
      computedResTagIds,
      toggleFilterTag,
      updateDepartmentIds,
      updateResTagIds,
      clearFilters,
      getRansackKey,
      isValueSelected,
      allPositionLevelOptions,
      allPoliticalStatusOptions,
      allBudgetedPostOptions,
      allGenderOptions,
      allEmployeeRoleOptions,
      departmentFilterParams,
    };
  },
});
</script>

<template lang="pug">
.filter-section.w-full
  a-collapse.overflow-auto
    a-collapse-panel(key='1', header='筛选条件')
      .filter-content.mb-4
        .filter-header.mb-3
          .filter-header-content.flex.justify-between.items-center
            .text-gray-700.font-medium 筛选选项
            a-button(type='default', size='small', @click='clearFilters') 清除筛选

        .filter-options.flex.flex-wrap.gap-4.mb-4
          //- 过滤部门部分
          .filter-option-item.flex-auto.min-w-80
            .text-gray-900.text-sm.font-medium.mb-1 过滤部门：
            TaVirtualSelect(
              :value='computedDepartmentIds',
              @update:value='updateDepartmentIds',
              apiPath='res/member/org_departments',
              :multiple='true',
              :disabled='disabled',
              :filterParams='departmentFilterParams',
              labelField='name',
              valueField='id',
              parentField='parent_name',
              placeholder='请选择部门'
            )

          //- 过滤标签部分
          .filter-option-item.flex-auto.min-w-80
            .text-gray-900.text-sm.font-medium.mb-1 过滤标签：
            TaVirtualSelect(
              :value='computedResTagIds',
              @update:value='updateResTagIds',
              apiPath='res/member/tags',
              :multiple='true',
              :disabled='disabled',
              labelField='name',
              valueField='id',
              placeholder='请选择标签'
            )

        .quick-filters.overflow-auto(style='max-height: 250px')
          .text-gray-700.font-medium.mb-2 浙政钉字段筛选：
          .filter-tags.flex.flex-wrap.gap-2
            .filter-group.mr-4.mb-2
              span.filter-label.mr-2 职级:
              a-tag.cursor-pointer.mx-1(
                v-for='option in allPositionLevelOptions',
                :key='option.value',
                :class='{ "ant-tag-checkable-checked": isValueSelected(getRansackKey("members_job_level_code"), option.value) }',
                @click='toggleFilterTag(getRansackKey("members_job_level_code"), option.value)'
              ) {{ option.label }}

            //- 政治面貌 - 显示所有政治面貌
            .filter-group.mr-4.mb-2
              span.filter-label.mr-2 政治面貌:
              a-tag.cursor-pointer.mx-1(
                v-for='option in allPoliticalStatusOptions',
                :key='option.value',
                :class='{ "ant-tag-checkable-checked": isValueSelected(getRansackKey("members_political_code"), option.value) }',
                @click='toggleFilterTag(getRansackKey("members_political_code"), option.value)'
              ) {{ option.label }}

            //- 编制选项
            .filter-group.mr-4.mb-2
              span.filter-label.mr-2 编制:
              a-tag.cursor-pointer.mx-1(
                v-for='option in allBudgetedPostOptions',
                :key='option.value',
                :class='{ "ant-tag-checkable-checked": isValueSelected(getRansackKey("members_budgeted_post_code"), option.value) }',
                @click='toggleFilterTag(getRansackKey("members_budgeted_post_code"), option.value)'
              ) {{ option.label }}

            //- 性别选项
            .filter-group.mr-4.mb-2
              span.filter-label.mr-2 性别:
              a-tag.cursor-pointer.mx-1(
                v-for='option in allGenderOptions',
                :key='option.value',
                :class='{ "ant-tag-checkable-checked": isValueSelected(getRansackKey("members_gender"), option.value) }',
                @click='toggleFilterTag(getRansackKey("members_gender"), option.value)'
              ) {{ option.label }}

            //- 人员角色选项
            .filter-group.mr-4.mb-2
              span.filter-label.mr-2 人员角色:
              a-tag.cursor-pointer.mx-1(
                v-for='option in allEmployeeRoleOptions',
                :key='option.value',
                :class='{ "ant-tag-checkable-checked": isValueSelected(getRansackKey("employee_role_code"), option.value) }',
                @click='toggleFilterTag(getRansackKey("employee_role_code"), option.value)'
              ) {{ option.label }}
</template>

<style lang="stylus" scoped>
.filter-section
  margin-bottom 16px
  padding 8px 12px
  background-color #f9f9f9
  border-radius 4px
.filter-content
  padding 0
.filter-header
  border-bottom 1px solid #eee
  padding-bottom 8px
.filter-options
  display flex
  flex-wrap wrap
  gap 16px
.filter-option-item
  // 移除固定宽度限制，使用最小宽度
  flex 1 1 320px
  margin-bottom 8px
// 选择器包装元素样式
// .select-wrapper
  // width 100%
// 移除文本截断相关样式
:deep(.ant-select)
  width 100%
:deep(.ant-select-selection-overflow)
  flex-wrap wrap
:deep(.ant-select-selection-item)
  max-width none
.filter-group
  display flex
  flex-wrap wrap
  align-items center
.filter-label
  color #666
  font-size 14px
.ant-tag-checkable-checked
  color #1890ff
  background-color #e6f7ff
  border-color #91d5ff
.quick-filters
  box-shadow inset 0 -5px 5px -5px rgba(0, 0, 0, 0.1)
  border-radius 4px
  padding-right 8px
</style>
