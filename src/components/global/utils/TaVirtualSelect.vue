<script lang="ts">
import { My<PERSON><PERSON> } from '@/apis/MyApi';
import { debounce } from 'lodash-es';
import { singular } from 'pluralize';
import type { PropType } from 'vue';
import { computed, defineComponent, ref, watch } from 'vue';

// Utility function for consistent value formatting
const formatSearchParam = (value: string, field = 'name') => {
  return value ? { [`${field}_cont`]: value } : {};
};

// Option interface
interface Option {
  label: string;
  value: string; // Ensure value is always string within options
  [key: string]: any;
}

export default defineComponent({
  name: 'TaVirtualSelect',
  props: {
    value: {
      type: [Array, String, Number] as PropType<string[] | string | number[] | number>,
      default: undefined,
    },
    apiPath: { type: String, required: true },
    placeholder: { type: String, default: '请选择' },
    multiple: { type: Boolean, default: false },
    disabled: { type: Boolean, default: false },
    pageSize: { type: Number, default: 15 },
    filterParams: { type: Object, default: () => ({}) },
    labelField: { type: String, default: 'name' },
    valueField: { type: String, default: 'id' },
    labelFormatter: {
      type: Function as PropType<(item: any) => string>,
      default: null,
    },
    parentField: { type: String, default: '' },
  },
  emits: ['update:value'],
  setup(props, { emit }) {
    const loading = ref(false);
    const options = ref<Option[]>([]);
    const page = ref(1);
    const total = ref(0);
    const searchValue = ref('');
    const hasMore = ref(true);

    // 下拉框定位配置
    const dropdownAlignConfig = {
      // 指定下拉框偏移量，增加与输入框的距离
      offset: [0, 2],
      // 禁止自动调整位置，确保下拉框始终在指定位置显示
      autoAdjustOverflow: false,
      overflow: {
        adjustX: true,
        adjustY: false,
      },
    };

    // Computed property to safely format the incoming value for a-select :value prop
    const selectDisplayValue = computed(() => {
      const rawValue = props.value;
      if (props.multiple) {
        if (Array.isArray(rawValue)) {
          // Ensure all elements are strings and filter out empty strings
          return rawValue
            .map(v => (v !== null && v !== undefined ? String(v) : ''))
            .filter(Boolean);
        }
        return []; // Default to empty array for multiple mode if prop is invalid
      } else {
        // Single select mode
        if (rawValue !== null && rawValue !== undefined && rawValue !== '') {
          return String(rawValue);
        }
        // Use undefined for Ant Select to show placeholder correctly when value is empty/null/undefined
        return undefined;
      }
    });

    // Handles the @change event directly from a-select
    const handleChange = (
      newValueFromSelect: string | number | string[] | number[] | undefined | null,
    ) => {
      let emittedValue: string | string[];

      if (props.multiple) {
        const rawArray = Array.isArray(newValueFromSelect) ? newValueFromSelect : [];
        emittedValue = rawArray
          .map(v => (v !== null && v !== undefined ? String(v) : ''))
          .filter(Boolean); // Filter empty strings after mapping
      } else {
        // Handle single select change
        if (
          newValueFromSelect === null ||
          newValueFromSelect === undefined ||
          newValueFromSelect === ''
        ) {
          emittedValue = ''; // Use empty string when emitting update for a cleared single select
        } else {
          emittedValue = String(newValueFromSelect);
        }
      }
      try {
        emit('update:value', emittedValue);
      } catch (error) {
        console.error('TaVirtualSelect [Emit] - Error during emit update:value:', error);
      }
    };

    // API interaction logic
    const createApi = () => {
      const pathParts = props.apiPath.split('/').filter(Boolean);
      const namespace = `/${pathParts.slice(0, -1).join('/')}`;
      const resource = pathParts[pathParts.length - 1];
      const name = resource ? singular(resource) : '';
      return new MyApi({ namespace, name, params: { q: props.filterParams } });
    };

    // 格式化标签文本的函数
    const formatLabelText = (item: any): string => {
      // 如果提供了自定义格式化函数，则使用它
      if (props.labelFormatter) {
        return props.labelFormatter(item);
      }

      // 如果指定了父字段且该字段存在，则使用"parent > name"格式
      if (props.parentField && item[props.parentField]) {
        return `${item[props.parentField]} > ${item[props.labelField] || '未知'}`;
      }

      // 默认情况下返回指定字段的值
      return String(item[props.labelField] || item.name || '未知');
    };

    // Data loading function
    const loadData = async (isSearch = false) => {
      if (loading.value || (!hasMore.value && !isSearch)) return;
      loading.value = true;
      try {
        const api = createApi();
        const searchParams = formatSearchParam(searchValue.value, props.labelField);
        const response = await api.index({
          page: page.value,
          per: props.pageSize,
          q: { ...props.filterParams, ...searchParams },
        });

        if (!response || !response.data) {
          options.value = isSearch ? [] : options.value;
          total.value = isSearch ? 0 : total.value;
          hasMore.value = false;
          return;
        }
        const responseData = response.data;
        let records: any[] = [];
        let totalCount = 0;
        if (responseData && typeof responseData === 'object' && 'records' in responseData) {
          records = Array.isArray(responseData.records) ? responseData.records : [];
          totalCount = responseData.total_count || 0;
        } else if (Array.isArray(responseData)) {
          records = responseData;
          totalCount = responseData.length;
          hasMore.value = false;
        } else {
          records = [];
          totalCount = 0;
          hasMore.value = false;
        }

        const newOptions: Option[] = records.map(item => ({
          value: String(item[props.valueField] ?? item.id ?? ''),
          label: formatLabelText(item),
          raw: item,
        }));

        if (isSearch) {
          options.value = newOptions;
        } else {
          const currentValues = new Set(options.value.map(opt => opt.value));
          const uniqueNewOptions = newOptions.filter(opt => !currentValues.has(opt.value));
          options.value = [...options.value, ...uniqueNewOptions];
        }
        total.value = totalCount;
        hasMore.value = options.value.length < total.value;
      } catch (error) {
        options.value = isSearch ? [] : options.value;
        hasMore.value = false;
      } finally {
        loading.value = false;
      }
    };

    // Infinite scroll handler
    const handlePopupScroll = (e: Event) => {
      const target = e.target as HTMLElement;
      const { scrollTop, clientHeight, scrollHeight } = target;
      if (scrollHeight - scrollTop <= clientHeight + 50 && !loading.value && hasMore.value) {
        page.value++;
        loadData();
      }
    };

    // Debounced search handler
    const handleSearch = debounce((inputValue: string) => {
      // Trim input value
      const trimmedValue = inputValue.trim();
      // Trigger search only if trimmed value is different from current searchValue
      if (searchValue.value !== trimmedValue) {
        searchValue.value = trimmedValue;
        page.value = 1;
        options.value = []; // Clear options for new search
        hasMore.value = true;
        loadData(true);
      }
    }, 300);

    // Watch for external filter changes
    watch(
      () => props.filterParams,
      (newParams, oldParams) => {
        // Use deep comparison for objects
        if (JSON.stringify(newParams) !== JSON.stringify(oldParams)) {
          page.value = 1;
          options.value = [];
          hasMore.value = true;
          loadData(true);
        }
      },
      { deep: true },
    );

    // Initial data load on component setup
    loadData(true);

    return {
      loading,
      options,
      selectDisplayValue, // Use this for :value binding
      handleChange, // Use this for @change binding
      handlePopupScroll,
      handleSearch, // Ensure comma is present here
      dropdownAlignConfig,
    };
  },
});
</script>

<template lang="pug">
.ta-virtual-select
  a-select(
    :value="selectDisplayValue"
    @change="handleChange"
    :loading="loading"
    :mode="multiple ? 'multiple' : undefined"
    :placeholder="placeholder"
    :disabled="disabled"
    :options="options"
    :allow-clear="true"
    :filter-option="false"
    :show-search="true"
    :virtual="false"
    :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
    :dropdown-match-select-width="true"
    :get-popup-container="trigger => trigger.parentNode || document.body"
    placement="bottomLeft"
    :dropdown-align="dropdownAlignConfig"
    :field-names="{ label: 'label', value: 'value', options: 'options' }"
    style="width: 100%"
    @popupScroll="handlePopupScroll"
    @search="handleSearch"
  )
    template(#notFoundContent)
      .text-center.py-2(v-if="loading") 加载中...
      .text-center.py-2(v-else-if="!options.length && searchValue") 无匹配结果
      .text-center.py-2(v-else-if="!options.length") 暂无选项
      .text-center.py-2(v-else) 没有更多了

    // Custom option rendering (optional)
    // template(#option="{ data }")
    //   span {{ data.label }}

    // Custom tag rendering for multiple selections (optional)
    // template(#tag="{ value, closable, onClose, option }")
    //   a-tag(:closable="closable" @close="onClose" style="margin-right: 3px")
    //     | {{ option?.label || value }}
</template>

<style lang="stylus" scoped>
.ta-virtual-select
  width 100%
  display block

  :deep(.ant-select)
    width 100%

  :deep(.ant-select-selector)
    width 100%
    min-height 32px

  :deep(.ant-select-selection-overflow)
    display flex
    flex-wrap wrap
    gap 2px

  :deep(.ant-select-dropdown)
    min-width 120px
    max-height 250px
</style>
