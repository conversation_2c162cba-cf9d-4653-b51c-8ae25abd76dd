<script lang="ts">
import { defineComponent, toRefs, computed } from 'vue';
import { TaTriggerTypeConfigMapping } from './TaTriggerEditor.vue';
import { TaTriggerType } from '../types';
import { TriggerFunctionTemplate } from './template';

const TaTriggerFunctionEditor = defineComponent({
  name: 'TaTriggerFunctionEditor',
  components: {},
  props: {
    value: { type: Object, required: true },
  },
  setup(props, { emit }) {
    const localValue = computed({
      get: () => props.value,
      set: value => emit('update:value', value),
    });

    const label = TaTriggerTypeConfigMapping[TaTriggerType.Function].label;

    const placeholder = computed(() => {
      if (localValue.value.name) {
        return localValue.value.name;
      }

      if (localValue.value.callback_class_name && localValue.value.callback_method) {
        return `${localValue.value.callback_class_name} ${localValue.value.callback_method}`;
      }

      return `请输入${label}`;
    });

    return {
      ...toRefs(props),
      localValue,
      label,
      TriggerFunctionTemplate,
      placeholder,
    };
  },
});
export default TaTriggerFunctionEditor;
</script>

<template lang="pug">
TaActionCapsuleEditor(
  v-model:value='localValue',
  :label='label',
  :disabled='disabled'
)
  .ta-trigger-function-editor.cursor-pointer.ellipsis
    | {{ placeholder }}

  template(#editor='{ formData, updateFormData, disabled }')
    TaTemplateForm(
      :modelValue='formData',
      :template='TriggerFunctionTemplate'
      :disabled='disabled',
      @update:modelValue='updateFormData',
    )
</template>

<style lang="stylus" scoped></style>
