import { getUserOptionsItemAry } from '@/engines/bpm/components/designer/nodeEditor/template';
import { TaTemplateFormSelect } from '../../ta-template-form-core/types';

export const TriggerFunctionTemplate = {
  key: `layout_1631084601414_14`,
  name: '普通布局',
  type: 'layout',
  fields: [
    // {
    //   key: `input_1631084586661_13`,
    //   name: '名称',
    //   type: 'input',
    //   model: { attr_type: 'string' },
    //   rules: [],
    //   fields: [],
    //   options: { span: 8 },
    //   model_key: 'name',
    //   conditions: [],
    //   model_key_prefix: '',
    // },
    {
      key: `input_1631084578791_12`,
      name: '类名',
      type: 'input',
      model: { attr_type: 'string' },
      rules: [],
      fields: [],
      options: { span: 12 },
      model_key: 'callback_class_name',
      conditions: [],
      model_key_prefix: '',
    },
    {
      key: `input_1631084571776_10`,
      name: '方法',
      type: 'input',
      model: { attr_type: 'string' },
      rules: [],
      fields: [],
      options: { span: 12 },
      model_key: 'callback_method',
      conditions: [],
      model_key_prefix: '',
    },
    {
      key: `json_1631084690495_15`,
      name: '参数',
      type: 'json',
      model: { attr_type: 'object' },
      rules: [],
      fields: [],
      options: { span: 24 },
      model_key: 'callback_params',
      conditions: [],
      model_key_prefix: '',
    },
  ],
  options: { span: 24, label: {} },
  model_key: 'layout_1631084601414_14',
  conditions: [],
  model_key_prefix: '',
};

export const TriggerSourceMethodTemplate = {
  key: `layout_1631084601414_15`,
  name: '普通布局',
  type: 'layout',
  fields: [
    {
      key: `input_1631084571776_10`,
      name: '方法',
      type: 'input',
      model: { attr_type: 'string' },
      rules: [],
      fields: [],
      options: { span: 24 },
      model_key: 'callback_method',
      conditions: [],
      model_key_prefix: '',
    },
    {
      name: '回调参数',
      type: 'json',
      rules: [],
      model: {
        attr_type: 'object',
      },
      options: {
        span: 24,
      },
      key: 'json_1670732252583_22',
      model_key: 'callback_params',
      fields: [],
      conditions: [],
      model_key_prefix: '',
    },
  ],
  options: { span: 24, label: {} },
  model_key: 'layout_1631084601414_15',
  conditions: [],
  model_key_prefix: '',
};

export const TriggerDataFormRecordMethodTemplate = TriggerSourceMethodTemplate;

export const getTriggerActiveRecordTemplateFn = (dataFormOptions: TaTemplateFormSelect[]) => ({
  type: 'container_layout',
  model: {},
  key: 'container_layout_1703572562794_1',
  model_key: 'container_layout_1703572562794_1',
  fields: [
    {
      key: 'TaTemplateFormDesignerKey-1',
      type: 'layout',
      fields: [
        {
          name: '父对象类名',
          icon: 'FolderOutlined',
          type: 'input',
          rules: [],
          model: { attr_type: 'string' },
          options: { span: 12 },
          key: 'input_1703572678523_2',
          model_key: 'parent_klass',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
        {
          name: '父对象 ID',
          icon: 'FolderOutlined',
          type: 'input',
          rules: [],
          model: { attr_type: 'string' },
          options: { span: 12 },
          key: 'input_1703572698431_3',
          model_key: 'parent_Id',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
        {
          name: '父对象表单序列号（parent_form_conf_seq）',
          icon: 'FolderOutlined',
          type: 'select',
          rules: [],
          model: { attr_type: 'string' },
          options: { span: 24, select: dataFormOptions },
          key: 'select_1703572724358_4',
          model_key: 'parent_form_conf_seq',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
        {
          name: '父对象表单序列号（parent_form_conf_seq）',
          icon: 'FolderOutlined',
          type: 'input',
          rules: [],
          model: { attr_type: 'string' },
          options: { span: 24 },
          key: 'input_1703572724358_4',
          model_key: 'parent_form_conf_seq',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
        {
          name: '资源关系名（association）',
          icon: 'FolderOutlined',
          type: 'input',
          rules: [],
          model: { attr_type: 'string' },
          options: { span: 24 },
          key: 'input_1703572762393_5',
          model_key: 'association',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
        {
          name: '对象类名',
          icon: 'FolderOutlined',
          type: 'input',
          rules: [],
          model: { attr_type: 'string' },
          options: { span: 24 },
          key: 'input_1703572788519_6',
          model_key: 'klass',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
        {
          name: '默认值',
          icon: 'FolderOutlined',
          type: 'json',
          rules: [],
          model: { attr_type: 'object' },
          options: { span: 24 },
          key: 'json_1703572802340_7',
          model_key: 'default_values',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
      ],
      options: { label: {}, theme: { card: { content: '<p><br></p>' } } },
      model: {},
      model_key_prefix: '',
    },
  ],
  conditions: [],
  options: {
    label: {},
    disabled_actions: {},
    theme: { card: { content: '<p><br></p>' }, background: {}, form: {} },
  },
  column_attributes: [],
});

export const TriggerBpmSmsNotifyTemplate = {
  key: `layout_1631084601414_14`,
  name: '普通布局',
  type: 'layout',
  fields: [
    {
      name: '收件人',
      type: 'radio',
      rules: [
        {
          rule_type: 'required',
          type: 'string',
          required: true,
          message: '请填写正确的收件人',
        },
      ],
      model: { attr_type: 'string' },
      options: {
        select: [
          { label: '通知发起人', value: 'creator' },
          { label: '通知处理人', value: 'operator' },
        ],
        multiple: false,
        span: 24,
        table_items: [],
        display_configurable_form: {},
        defaultValue: 'creator',
      },
      key: `radio_1648362597784_2`,
      model_key: 'receiver',
      fields: [],
      conditions: [],
      model_key_prefix: '',
    },
    {
      name: '参数',
      type: 'json',
      rules: [],
      model: { attr_type: 'object' },
      options: { span: 24 },
      key: `json_1648362553170_1`,
      model_key: 'callback_params',
      fields: [],
      conditions: [],
      model_key_prefix: '',
    },
  ],
  options: { span: 24, label: {} },
  model_key: 'layout_1631084601414_14',
  conditions: [],
  model_key_prefix: '',
};

export const TriggerBpmCreateTokenTemplate = {
  key: `layout_1631084601414_14`,
  name: '普通布局',
  type: 'layout',
  fields: [
    // {
    //   name: '节点类型（bpm_token_type）',
    //   icon: 'FolderOutlined',
    //   type: 'input',
    //   rules: [],
    //   model: { attr_type: 'string' },
    //   options: { span: 24 },
    //   key: 'input_1703572762393_5',
    //   model_key: 'bpm_token_type',
    //   fields: [],
    //   conditions: [],
    //   model_key_prefix: '',
    // },
    {
      name: '节点类型（bpm_token_type）',
      icon: 'FolderOutlined',
      type: 'select',
      rules: [],
      model: { attr_type: 'string' },
      options: {
        span: 24,
        select: [
          {
            label: '派送',
            value: 'Tokens::Deliver',
          },
        ],
      },
      key: 'select_1703572762393_5',
      model_key: 'bpm_token_type',
      fields: [],
      conditions: [],
      model_key_prefix: '',
    },
    // {
    //   name: '节点初始化状态（bpm_token_state）',
    //   icon: 'FolderOutlined',
    //   type: 'input',
    //   rules: [],
    //   model: { attr_type: 'string' },
    //   options: { span: 24 },
    //   key: 'input_1703572762393_51',
    //   model_key: 'bpm_token_state',
    //   fields: [],
    //   conditions: [],
    //   model_key_prefix: '',
    // },
    {
      name: '节点初始化状态（bpm_token_state）',
      icon: 'FolderOutlined',
      type: 'select',
      rules: [],
      model: { attr_type: 'string' },
      options: {
        span: 24,
        select: [
          {
            label: '进行中',
            value: 'processing',
          },
        ],
      },
      key: 'select_1703572762393_51',
      model_key: 'bpm_token_state',
      fields: [],
      conditions: [],
      model_key_prefix: '',
    },
    {
      key: 'key_layout_1634117081493_16',
      name: 'user_options',
      type: 'key_layout',
      fields: getUserOptionsItemAry('操作者', 'user_options', true),
      options: { span: 24 },
      model_key: 'user_options',
      conditions: [],
      model_key_prefix: 'options',
    },
  ],
  options: { span: 24, label: {} },
  model_key: 'layout_1631084601414_14',
  conditions: [],
  model_key_prefix: '',
};

export const TriggerBpmCreateInstanceTemplate = {
  key: `layout_1631084601414_14`,
  name: '普通布局',
  type: 'layout',
  fields: [
    {
      name: '工作流标识',
      icon: 'FolderOutlined',
      type: 'input',
      rules: [],
      model: { attr_type: 'string' },
      options: { span: 24 },
      key: 'input_1703572762393_5',
      model_key: 'flowable_flag',
      fields: [],
      conditions: [],
      model_key_prefix: '',
    },
    {
      name: '工作流ID',
      icon: 'FolderOutlined',
      type: 'input',
      rules: [],
      model: { attr_type: 'string' },
      options: { span: 24 },
      key: 'input_1703572762393_51',
      model_key: 'bpm_workflow_id',
      fields: [],
      conditions: [],
      model_key_prefix: '',
    },
    {
      name: '工作流STI类型（bpm_instance_type）',
      icon: 'FolderOutlined',
      type: 'input',
      rules: [],
      model: { attr_type: 'string' },
      options: { span: 24 },
      key: 'input_1703572762393_52',
      model_key: 'bpm_instance_type',
      fields: [],
      conditions: [],
      model_key_prefix: '',
    },
    {
      name: '是否自动提交',
      icon: 'FolderOutlined',
      type: 'switch',
      rules: [],
      model: { attr_type: 'string' },
      options: { span: 24 },
      key: 'switch_1703572762393_51',
      model_key: 'bpm_auto_submit',
      fields: [],
      conditions: [],
      model_key_prefix: '',
    },
    {
      name: '参数',
      type: 'json',
      rules: [],
      model: { attr_type: 'object' },
      options: { span: 24 },
      key: `json_1648362553170_1`,
      model_key: 'args',
      fields: [],
      conditions: [],
      model_key_prefix: '',
    },
  ],
  options: { span: 24, label: {} },
  model_key: 'layout_1631084601414_14',
  conditions: [],
  model_key_prefix: '',
};

export const TriggerPerfUnitDefineTemplate = {
  type: 'container_layout',
  key: 'container_layout_1712474396797_1',
  model_key: 'container_layout_1712474396797_1',
  model_key_configuration: [],
  fields: [
    {
      key: 'TaTemplateFormDesignerKey-1',
      type: 'layout',
      fields: [
        {
          name: '指标定义',
          icon: 'FolderOutlined',
          type: 'api_single',
          rules: [],
          model: { attr_type: 'number' },
          options: {
            span: 24,
            table_items: [
              { name: '指标定义名称', data_index: 'name', search: true, type: 'string' },
            ],
            display_configurable_form: {},
            import_export_headers: [{ _id: '1712474406157_0' }],
            path: '/perf/admin/unit_defines',
          },
          key: 'api_single_1712474405136_2',
          model_key: 'unit_define_id',
          model_key_configuration: [],
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
      ],
      options: {},
      model: {},
      model_key_prefix: '',
    },
  ],
  conditions: [],
  options: { label: {}, disabled_actions: {}, theme: { card: {}, background: {}, form: {} } },
  model: { create_default_value: {}, update_default_value: {} },
};
