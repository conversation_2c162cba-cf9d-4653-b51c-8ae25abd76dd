import { merge } from 'lodash';
import { TaTemplateFormItem, TaTemplateFormItemTreeData } from '../../ta-template-form-core/types';

export const TaConditionBpmActionRailsBpmOptions = [
  { label: '创建下一个节点前（after_activation）', value: 'after_create_next_token' },
  { label: '创建节点之后（before_activation）', value: 'after_create_token' },
  { label: '从节点(token)触发事件前', value: 'before_fire_from_token' },
  { label: '从节点(token)触发事件后', value: 'after_fire_from_token' },
  { label: '从节点配置(place)触发事件前', value: 'before_fire_from_place' },
  { label: '从节点配置(place)触发事件后', value: 'after_fire_from_place' },
  { label: '从工作流触发事件前', value: 'before_fire_from_workflow' },
  { label: '从工作流触发事件后', value: 'after_fire_from_workflow' },
];

export const TaConditionUserTemplate: TaTemplateFormItem = {
  key: 'layout_1670730837269_0',
  type: 'layout',
  model: { create_default_value: {} },
  fields: [
    {
      key: 'radio_1670730870165_2',
      name: '流程角色',
      type: 'radio',
      model: { attr_type: 'string' },
      rules: [],
      fields: [],
      options: {
        span: 24,
        select: [
          { label: '发起人', value: 'instance.creator' },
          { label: '操作者', value: 'operator' },
        ],
        multiple: false,
        table_items: [],
        display_configurable_form: {},
      },
      model_key: 'user_method',
      conditions: [],
      model_key_prefix: '',
    },
    {
      key: 'key_layout_1670730971782_4',
      name: 'user_rule',
      type: 'key_layout',
      fields: [
        {
          key: 'select_1670731185367_5',
          name: '类型',
          type: 'select',
          model: { attr_type: 'string' },
          rules: [],
          fields: [],
          options: {
            span: 24,
            select: [
              { label: '部门', value: 'department_in' },
              { label: '组织', value: 'org_in' },
              { label: '职务', value: 'duty_in' },
              { label: '角色', value: 'role_in' },
              { label: '部门包含', value: 'department_cont' },
              { label: '组织包含', value: 'org_cont' },
              { label: '职务包含', value: 'duty_cont' },
              { label: '角色包含', value: 'role_cont' },
              { label: '岗位等级', value: 'duty_rank' },
            ],
            multiple: false,
            table_items: [],
            display_configurable_form: {},
          },
          model_key: 'type',
          conditions: [],
          model_key_prefix: 'user_rule',
        },
        {
          key: 'condition_1670731403801_6',
          name: '条件块',
          type: 'condition',
          fields: [],
          options: { span: 24 },
          model_key: 'condition_1670731403801_6',
          conditions: [
            {
              opt: '==',
              val: 'department_in',
              name: 'department_in',
              type: 'simple',
              fields: [
                {
                  key: 'api_1670731622377_10',
                  name: '部门',
                  type: 'api',
                  model: { attr_type: 'array' },
                  rules: [],
                  fields: [],
                  options: {
                    path: '/res/admin/departments',
                    span: 24,
                    multiple: true,
                    table_items: [
                      {
                        name: '名称',
                        type: 'string',
                        search: true,
                        data_index: 'name',
                      },
                      {
                        name: '',
                        type: 'string',
                        search: false,
                        data_index: 'path_names',
                      },
                    ],
                    display_configurable_form: {},
                  },
                  model_key: 'source_ids',
                  conditions: [],
                  model_key_prefix: 'user_rule',
                },
              ],
              model_key: 'user_rule.type',
              complex_condition: { name: '', groups: [] },
            },
            {
              opt: '==',
              val: 'org_in',
              name: 'org_in',
              type: 'simple',
              fields: [
                {
                  key: 'api_1670731667344_11',
                  name: '组织',
                  type: 'api',
                  model: { attr_type: 'array' },
                  rules: [],
                  fields: [],
                  options: {
                    path: '/res/admin/orgs',
                    span: 24,
                    multiple: true,
                    table_items: [
                      {
                        name: '名称',
                        type: 'string',
                        search: true,
                        data_index: 'name',
                      },
                      {
                        name: '',
                        type: 'string',
                        search: false,
                        data_index: 'path_name',
                      },
                    ],
                    display_configurable_form: {},
                  },
                  model_key: 'source_ids',
                  conditions: [],
                  model_key_prefix: 'user_rule',
                },
              ],
              model_key: 'user_rule.type',
              complex_condition: { groups: [] },
            },
            {
              opt: '==',
              val: 'duty_in',
              name: 'duty_in',
              type: 'simple',
              fields: [
                {
                  key: 'api_1670731705294_12',
                  name: '职务',
                  type: 'api',
                  model: { attr_type: 'array' },
                  rules: [],
                  fields: [],
                  options: {
                    path: '/res/admin/duties',
                    span: 24,
                    multiple: true,
                    table_items: [
                      {
                        name: '名称',
                        type: 'string',
                        search: true,
                        data_index: 'name',
                      },
                    ],
                    display_configurable_form: {},
                  },
                  model_key: 'source_ids',
                  conditions: [],
                  model_key_prefix: 'user_rule',
                },
              ],
              model_key: 'user_rule.type',
              complex_condition: { groups: [] },
            },
            {
              opt: '==',
              val: 'role_in',
              name: 'role_in',
              type: 'simple',
              fields: [
                {
                  key: 'api_1670731734176_13',
                  name: '角色',
                  type: 'api',
                  model: { attr_type: 'array' },
                  rules: [],
                  fields: [],
                  options: {
                    path: '/permit/admin/roles',
                    span: 24,
                    multiple: true,
                    table_items: [
                      {
                        name: '名称',
                        type: 'string',
                        search: true,
                        data_index: 'name',
                      },
                      {
                        name: '标签名',
                        type: 'string',
                        search: true,
                        data_index: 'label',
                      },
                    ],
                    display_configurable_form: {},
                  },
                  model_key: 'source_ids',
                  conditions: [],
                  model_key_prefix: 'user_rule',
                },
              ],
              model_key: 'user_rule.type',
              complex_condition: { groups: [] },
            },
            {
              val: '',
              name: '条件5',
              type: 'complex',
              fields: [
                {
                  key: 'input_1670731768563_14',
                  name: '关键字',
                  type: 'input',
                  model: { attr_type: 'string' },
                  rules: [],
                  fields: [],
                  options: { span: 24 },
                  model_key: 'source_name',
                  conditions: [],
                  model_key_prefix: 'user_rule',
                },
              ],
              model_key: '',
              complex_condition: {
                name: '_cont',
                groups: [
                  {
                    _id: '1670731560129_4',
                    items: [
                      {
                        _id: '1670731560136_5',
                        desc: {
                          name: '类型',
                          optZh: '含有',
                          modelValue: {
                            rule: {
                              val: ['department_cont', 'org_cont', 'duty_cont', 'role_cont'],
                            },
                          },
                          template: {
                            key: 'key',
                            type: 'layout',
                            fields: [
                              {
                                key: 'ditto_1670731926609_17',
                                name: '值',
                                type: 'select',
                                model: { attr_type: 'array' },
                                rules: [],
                                fields: [],
                                options: {
                                  span: 24,
                                  ditto: 'ditto_single',
                                  dynamic_component: '',
                                  select: [
                                    { label: '部门', value: 'department_in' },
                                    { label: '组织', value: 'org_in' },
                                    { label: '职务', value: 'duty_in' },
                                    { label: '角色', value: 'role_in' },
                                    { label: '部门包含', value: 'department_cont' },
                                    { label: '组织包含', value: 'org_cont' },
                                    { label: '职务包含', value: 'duty_cont' },
                                    { label: '角色包含', value: 'role_cont' },
                                    { label: '岗位等级', value: 'duty_rank' },
                                  ],
                                  multiple: true,
                                  table_items: [],
                                  display_configurable_form: {},
                                  formDisabled: false,
                                  disabled: false,
                                  accessibility: 'writeable',
                                },
                                model_key: 'val',
                                conditions: [],
                                model_key_prefix: 'rule',
                              },
                            ],
                          },
                        },
                        rule: {
                          type: 'Com::Attr::ConditionRules::SingleChoice',
                          key: 'user_rule.type',
                          key_name: '类型',
                          opt: 'include',
                          val: ['department_cont', 'org_cont', 'duty_cont', 'role_cont'],
                        },
                      },
                    ],
                  },
                ],
              },
            },
            {
              opt: '==',
              val: 'duty_rank',
              name: 'duty_rank',
              type: 'simple',
              fields: [
                {
                  key: 'string_array_1670731783961_15',
                  name: '职级',
                  type: 'string_array',
                  model: { attr_type: 'array' },
                  rules: [],
                  fields: [],
                  options: { span: 24 },
                  model_key: 'ranks',
                  conditions: [],
                  model_key_prefix: 'user_rule',
                },
              ],
              model_key: 'user_rule.type',
              complex_condition: { groups: [{ items: [{ _id: '1670731585078_8' }] }] },
            },
          ],
          model_key_prefix: 'user_rule',
        },
      ],
      options: { span: 24 },
      model_key: 'user_rule',
      conditions: [],
      model_key_prefix: '',
    },
  ],
  actions: [
    { key: 'create', enabled: true },
    { key: 'update', enabled: true },
    { key: 'delete', enabled: true },
    { key: 'import', enabled: true },
    { key: 'export', enabled: true },
  ],
  options: {
    label: {},
    theme: { card: {}, background: {} },
    create_text: '提交',
    update_text: '提交',
    disabled_actions: {},
  },
  model_key: 'layout_1670730837269_0',
  conditions: [],
  column_attributes: [],
};

export const getTaConditionOldTemplateFn: (
  fieldTreeData: TaTemplateFormItemTreeData[],
  valueFormItem?: TaTemplateFormItem,
) => TaTemplateFormItem = (
  fieldTreeData: TaTemplateFormItemTreeData[],
  valueFormItem?: TaTemplateFormItem,
) => ({
  key: 'layout_1632799069699_0',
  type: 'layout',
  model: {},
  fields: [
    {
      key: 'key_layout_1632801192300_10',
      name: '嵌套对象',
      type: 'key_layout',
      fields: [
        {
          key: 'select_1633101059886_1',
          name: '条件目标',
          type: 'tree',
          model: {},
          rules: [],
          fields: [],
          options: {
            span: 24,
            treeData: fieldTreeData,
          },
          model_key: 'key',
          conditions: [],
          model_key_prefix: 'rule',
        },
        {
          key: 'input_1633101059886_1',
          name: '条件目标',
          type: 'input',
          model: {},
          rules: [],
          fields: [],
          options: {
            span: 24,
          },
          model_key: 'key',
          conditions: [],
          model_key_prefix: 'rule',
        },
        {
          key: 'select_1632801095948_7',
          name: '类型',
          type: 'select',
          model: { attr_type: 'string' },
          rules: [],
          fields: [],
          options: {
            span: 24,
            select: [
              {
                label: '数字',
                value: 'Com::Attr::ConditionRules::Number',
              },
              {
                label: '布尔',
                value: 'Com::Attr::ConditionRules::Boolean',
              },
              {
                label: '字符串',
                value: 'Com::Attr::ConditionRules::String',
              },
              {
                label: '单选',
                value: 'Com::Attr::ConditionRules::SingleChoice',
              },
              {
                label: '多选',
                value: 'Com::Attr::ConditionRules::MultiChoice',
              },
            ],
            multiple: false,
            // formDisabled: true,
          },
          model_key: 'type',
          conditions: [],
          model_key_prefix: 'rule',
        },
        {
          key: 'condition_1632801595713_12',
          name: '条件块',
          type: 'condition',
          fields: [],
          options: { span: 24 },
          model_key: 'condition_1632801595713_12',
          conditions: [
            {
              opt: '==',
              val: 'Com::Attr::ConditionRules::Number',
              name: '数字',
              type: 'simple',
              fields: [
                {
                  key: 'select_1632801709101_14',
                  name: '条件',
                  type: 'select',
                  model: { attr_type: 'string' },
                  rules: [],
                  fields: [],
                  options: {
                    span: 24,
                    select: [
                      { label: '大于', value: '>' },
                      { label: '大于等于', value: '>=' },
                      { label: '等于', value: '==' },
                      { label: '小于', value: '<' },
                      { label: '小于等于', value: '<=' },
                      { label: '不等于', value: '!=' },
                      { label: '范围', value: 'between' },
                    ],
                    multiple: false,
                  },
                  model_key: 'opt',
                  conditions: [],
                  model_key_prefix: 'rule',
                },
                {
                  key: 'condition_1632801705551_13',
                  name: '条件块',
                  type: 'condition',
                  fields: [],
                  options: { span: 24 },
                  model_key: 'condition_1632801705551_13',
                  conditions: [
                    {
                      opt: '==',
                      val: 'between',
                      name: '范围',
                      type: 'simple',
                      fields: [
                        {
                          key: 'string_array_1632802108416_17',
                          name: '开区间',
                          type: 'string_array',
                          model: { attr_type: 'array' },
                          rules: [],
                          fields: [],
                          options: { span: 24 },
                          model_key: 'val',
                          conditions: [],
                          model_key_prefix: 'rule',
                        },
                      ],
                      model_key: 'rule.opt',
                      complex_condition: { groups: [] },
                    },
                    {
                      opt: '!=',
                      val: 'between',
                      name: '非范围',
                      type: 'simple',
                      fields: [
                        {
                          key: 'number_1632802020086_16',
                          name: '值',
                          type: 'number',
                          model: { attr_type: 'number' },
                          rules: [],
                          fields: [],
                          options: { span: 24 },
                          model_key: 'val',
                          conditions: [],
                          model_key_prefix: 'rule',
                        },
                      ],
                      model_key: 'rule.opt',
                      complex_condition: { groups: [] },
                    },
                  ],
                  model_key_prefix: 'rule',
                },
              ],
              model_key: 'rule.type',
              complex_condition: { groups: [] },
            },
            {
              opt: '==',
              val: 'Com::Attr::ConditionRules::Boolean',
              name: '布尔',
              type: 'simple',
              fields: [
                {
                  key: 'select_1632802203913_18',
                  name: '条件',
                  type: 'select',
                  model: { attr_type: 'string' },
                  rules: [],
                  fields: [],
                  options: {
                    span: 24,
                    select: [
                      { label: '等于', value: '==' },
                      { label: '不等于', value: '!=' },
                    ],
                    multiple: false,
                  },
                  model_key: 'opt',
                  conditions: [],
                  model_key_prefix: 'rule',
                },
                {
                  key: 'switch_1632802235314_19',
                  name: '值',
                  type: 'switch',
                  model: { attr_type: 'boolean' },
                  rules: [],
                  fields: [],
                  options: { span: 24, placeholder: '' },
                  model_key: 'val',
                  conditions: [],
                  model_key_prefix: 'rule',
                },
              ],
              model_key: 'rule.type',
              complex_condition: { groups: [] },
            },
            {
              opt: '==',
              val: 'Com::Attr::ConditionRules::String',
              name: '字符串',
              type: 'simple',
              fields: [
                {
                  key: 'select_1632802308881_20',
                  name: '条件',
                  type: 'select',
                  model: { attr_type: 'string' },
                  rules: [],
                  fields: [],
                  options: {
                    span: 24,
                    select: [
                      { label: '等于', value: '==' },
                      { label: '不等于', value: '!=' },
                      { label: '包含', value: 'contains' },
                    ],
                    multiple: false,
                    placeholder: '',
                  },
                  model_key: 'opt',
                  conditions: [],
                  model_key_prefix: 'rule',
                },
                {
                  key: 'input_1632802358717_21',
                  name: '值',
                  type: 'input',
                  model: { attr_type: 'string' },
                  rules: [],
                  fields: [],
                  options: { span: 24 },
                  model_key: 'val',
                  conditions: [],
                  model_key_prefix: 'rule',
                },
              ],
              model_key: 'rule.type',
              complex_condition: { groups: [] },
            },
            {
              opt: '==',
              val: 'Com::Attr::ConditionRules::SingleChoice',
              name: '单选',
              type: 'simple',
              fields: [
                {
                  key: 'select_1633115173979_0',
                  name: '条件',
                  type: 'select',
                  model: { attr_type: 'string' },
                  rules: [],
                  fields: [],
                  options: {
                    span: 24,
                    select: [
                      { label: '含有', value: 'include' },
                      { label: '无一符合', value: 'none' },
                    ],
                    multiple: false,
                    placeholder: '',
                  },
                  model_key: 'opt',
                  conditions: [],
                  model_key_prefix: 'rule',
                },
                valueFormItem
                  ? (merge(valueFormItem, {
                      model_key: 'val',
                      model_key_prefix: 'rule',
                      options: {
                        span: 24,
                      },
                    }) as TaTemplateFormItem)
                  : {
                      key: 'json_1633111975922_1',
                      name: '条件值',
                      type: 'json',
                      model: {},
                      rules: [],
                      fields: [],
                      options: {
                        span: 24,
                      },
                      model_key: 'val',
                      conditions: [],
                      model_key_prefix: 'rule',
                    },
              ],
              model_key: 'rule.type',
              complex_condition: { groups: [] },
            },
            {
              opt: '==',
              val: 'Com::Attr::ConditionRules::MultiChoice',
              name: '多选',
              type: 'simple',
              fields: [
                {
                  key: 'select_1633115233766_1',
                  name: '条件',
                  type: 'select',
                  model: { attr_type: 'string' },
                  rules: [],
                  fields: [],
                  options: {
                    span: 24,
                    select: [
                      { label: '包含', value: 'include' },
                      { label: '无一符合', value: 'none' },
                      { label: '有交集', value: 'any' },
                      { label: '相等', value: '==' },
                    ],
                    multiple: false,
                  },
                  model_key: 'opt',
                  conditions: [],
                  model_key_prefix: 'rule',
                },
                valueFormItem
                  ? (merge(valueFormItem, {
                      model_key: 'val',
                      model_key_prefix: 'rule',
                      options: {
                        span: 24,
                      },
                    }) as TaTemplateFormItem)
                  : {
                      key: 'json_1633111975922_1',
                      name: '条件值',
                      type: 'json',
                      model: {},
                      rules: [],
                      fields: [],
                      options: {
                        span: 24,
                      },
                      model_key: 'val',
                      conditions: [],
                      model_key_prefix: 'rule',
                    },
              ],
              model_key: 'rule.type',
              complex_condition: { groups: [] },
            },
          ],
          model_key_prefix: 'rule',
        },
      ],
      options: { span: 24 },
      model_key: 'rule',
      conditions: [],
      model_key_prefix: '',
    },
  ],
  options: { label: { width: 80 }, theme: 'none' },
  model_key: 'layout_1632799069699_0',
  conditions: [],
  index_attributes: [],
  column_attributes: [],
});
