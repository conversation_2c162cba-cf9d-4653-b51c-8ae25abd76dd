<script lang="ts">
import { defineComponent, toRefs, computed } from 'vue';
import { TaPipelineTypeConfigMapping } from './TaPipelineEditor.vue';
import { TaPipelineType } from '../types';
import { PipelineSourceMethodTemplate } from './template';

const TaPipelineSourceMethodEditor = defineComponent({
  name: 'TaPipelineSourceMethodEditor',
  components: {},
  props: {
    value: { type: Object, required: true },
    disabled: { type: Boolean, default: false },
  },
  setup(props, { emit }) {
    const localValue = computed({
      get: () => props.value,
      set: value => emit('update:value', value),
    });

    const label = TaPipelineTypeConfigMapping[TaPipelineType.SourceMethod].label;

    const placeholder = computed(() => {
      if (localValue.value.name) {
        return localValue.value.name;
      }

      if (localValue.value.method) {
        return localValue.value.method;
      }

      return `请输入 ${label}`;
    });

    return {
      ...toRefs(props),
      localValue,
      label,
      PipelineSourceMethodTemplate,
      placeholder,
    };
  },
});
export default TaPipelineSourceMethodEditor;
</script>

<template lang="pug">
TaActionCapsuleEditor(
  v-model:value='localValue',
  :label='label',
  :disabled='disabled'
)
  .ta-pipeline-function-editor.cursor-pointer.ellipsis
    | {{ placeholder }}

  template(#editor='{ formData, updateFormData, disabled }')
    TaTemplateForm(
      :modelValue='formData',
      :template='PipelineSourceMethodTemplate'
      :disabled='disabled',
      @update:modelValue='updateFormData',
    )
</template>

<style lang="stylus" scoped></style>
