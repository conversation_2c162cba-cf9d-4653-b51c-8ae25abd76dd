export const PipelineFunctionTemplate = {
  key: `layout_1631084601414_14`,
  name: '普通布局',
  type: 'layout',
  fields: [
    // {
    //   key: `input_1631084586661_13`,
    //   name: '名称',
    //   type: 'input',
    //   model: { attr_type: 'string' },
    //   rules: [],
    //   fields: [],
    //   options: { span: 8 },
    //   model_key: 'name',
    //   conditions: [],
    //   model_key_prefix: '',
    // },
    {
      key: `input_1631084578791_12`,
      name: '类名',
      type: 'input',
      model: { attr_type: 'string' },
      rules: [],
      fields: [],
      options: { span: 12 },
      model_key: 'callback_class_name',
      conditions: [],
      model_key_prefix: '',
    },
    {
      key: `input_1631084571776_10`,
      name: '方法',
      type: 'input',
      model: { attr_type: 'string' },
      rules: [],
      fields: [],
      options: { span: 12 },
      model_key: 'callback_method',
      conditions: [],
      model_key_prefix: '',
    },
    {
      key: `json_1631084690495_15`,
      name: '参数',
      type: 'json',
      model: { attr_type: 'object' },
      rules: [],
      fields: [],
      options: { span: 24 },
      model_key: 'callback_params',
      conditions: [],
      model_key_prefix: '',
    },
  ],
  options: { span: 24, label: {} },
  model_key: 'layout_1631084601414_14',
  conditions: [],
  model_key_prefix: '',
};

export const PipelineSourceMethodTemplate = {
  key: `layout_1631084601414_15`,
  name: '普通布局',
  type: 'layout',
  fields: [
    {
      key: `input_1631084571776_10`,
      name: '方法',
      type: 'input',
      model: { attr_type: 'string' },
      rules: [],
      fields: [],
      options: { span: 24 },
      model_key: 'method',
      conditions: [],
      model_key_prefix: '',
    },
    {
      name: '回调参数',
      type: 'json',
      rules: [],
      model: {
        attr_type: 'object',
      },
      options: {
        span: 24,
      },
      key: 'json_1670732252583_22',
      model_key: 'callback_params',
      fields: [],
      conditions: [],
      model_key_prefix: '',
    },
  ],
  options: { span: 24, label: {} },
  model_key: 'layout_1631084601414_15',
  conditions: [],
  model_key_prefix: '',
};
