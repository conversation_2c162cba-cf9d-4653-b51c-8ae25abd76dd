export const searcherTemplate = {
  options: {
    label: {},
    disabled_actions: {},
    theme: { card: { content: '<p><br></p>' }, background: {} },
    create_text: '提交',
    update_text: '提交',
    searcher: [],
  },
  model: {},
  column_attributes: [],
  actions: [
    { key: 'create', enabled: true },
    { key: 'update', enabled: true },
    { key: 'delete', enabled: true },
    { key: 'import', enabled: true },
    { key: 'export', enabled: true },
  ],
  type: 'layout',
  key: 'layout_1683102013908_0',
  model_key: 'layout_1683102013908_0',
  fields: [
    {
      name: '搜索项',
      icon: 'FolderOutlined',
      type: 'list',
      fields: [
        {
          name: '关键字',
          icon: 'FolderOutlined',
          type: 'input',
          rules: [],
          model: { attr_type: 'string' },
          options: { span: 24 },
          key: 'input_1683102109040_3',
          model_key: 'key',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
        {
          name: '名称',
          icon: 'FolderOutlined',
          type: 'input',
          rules: [],
          model: { attr_type: 'string' },
          options: { span: 24 },
          key: 'input_1683102117459_4',
          model_key: 'label',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
        {
          name: '在简单搜索隐藏',
          icon: 'FolderOutlined',
          type: 'switch',
          rules: [],
          model: { attr_type: 'boolean' },
          options: { span: 12 },
          key: 'switch_1683164982660_2',
          model_key: 'simpleHidden',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
        {
          name: '在复杂搜索隐藏',
          icon: 'FolderOutlined',
          type: 'switch',
          rules: [],
          model: { attr_type: 'boolean' },
          options: { span: 12 },
          key: 'switch_1683164979711_1',
          model_key: 'complicatedHidden',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
        {
          name: '类型',
          icon: 'FolderOutlined',
          type: 'radio',
          rules: [],
          model: { attr_type: 'string' },
          options: {
            select: [
              { label: '数字', value: 'number' },
              { label: '文字', value: 'string' },
              { label: '选择', value: 'select' },
              { label: '时间', value: 'time' },
              { label: '日期', value: 'date' },
              { label: '动态组件', value: 'dynamicComponent' },
            ],
            multiple: false,
            span: 24,
            table_items: [],
            display_configurable_form: {},
            import_export_headers: [{ _id: '1683102166933_0' }],
          },
          key: 'radio_1683102165390_5',
          model_key: 'type',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
        {
          name: '条件块',
          icon: 'FolderOutlined',
          type: 'condition',
          conditions: [
            {
              name: '动态组件',
              model_key: 'type',
              val: 'dynamicComponent',
              fields: [
                {
                  name: '组件名',
                  icon: 'FolderOutlined',
                  type: 'input',
                  rules: [],
                  model: { attr_type: 'string' },
                  options: { span: 24 },
                  key: 'input_1683105065252_2',
                  model_key: 'component',
                  fields: [],
                  conditions: [],
                  model_key_prefix: '',
                },
              ],
              type: 'simple',
              complex_condition: { groups: [{ items: [] }] },
              opt: '==',
            },
            {
              name: '选择',
              type: 'simple',
              model_key: 'type',
              val: 'select',
              fields: [
                {
                  name: '是否多选',
                  icon: 'FolderOutlined',
                  type: 'switch',
                  rules: [],
                  model: { attr_type: 'boolean' },
                  options: { span: 24 },
                  key: 'switch_1683105147783_6',
                  model_key: 'multiple',
                  fields: [],
                  conditions: [],
                  model_key_prefix: '',
                },
                {
                  name: '选择项',
                  icon: 'FolderOutlined',
                  type: 'list',
                  fields: [
                    {
                      name: '显示值',
                      icon: 'FolderOutlined',
                      type: 'input',
                      rules: [],
                      model: { attr_type: 'string' },
                      options: { span: 12 },
                      key: 'input_1683105107186_4',
                      model_key: 'label',
                      fields: [],
                      conditions: [],
                      model_key_prefix: '',
                    },
                    {
                      name: '选择值',
                      icon: 'FolderOutlined',
                      type: 'json',
                      rules: [],
                      model: { attr_type: 'object' },
                      options: { span: 12 },
                      key: 'json_1683105111452_5',
                      model_key: 'value',
                      fields: [],
                      conditions: [],
                      model_key_prefix: '',
                    },
                  ],
                  rules: [],
                  model: { attr_type: 'array' },
                  options: {
                    span: 24,
                    disabled_actions: {},
                    edit_directly: true,
                    display: 'normal',
                  },
                  key: 'list_1683105083602_3',
                  model_key: 'select',
                  conditions: [],
                  model_key_prefix: '',
                },
                {
                  name: '分组',
                  icon: 'FolderOutlined',
                  type: 'input',
                  rules: [],
                  model: { attr_type: 'string' },
                  options: { span: 24 },
                  key: 'input_1683105168914_7',
                  model_key: 'group',
                  fields: [],
                  conditions: [],
                  model_key_prefix: '',
                },
              ],
              complex_condition: { groups: [] },
              opt: '==',
            },
            {
              name: '条件3',
              model_key: '',
              val: '',
              fields: [
                {
                  name: '使用范围',
                  icon: 'FolderOutlined',
                  type: 'switch',
                  rules: [],
                  model: { attr_type: 'boolean' },
                  options: { span: 24 },
                  key: 'switch_1683105226967_9',
                  model_key: 'switch_1683105226967_9',
                  fields: [],
                  conditions: [],
                  model_key_prefix: '',
                },
              ],
              type: 'complex',
              complex_condition: {
                groups: [
                  {
                    items: [
                      {
                        _id: '1683105196756_1',
                        rule: {
                          type: 'Com::Attr::ConditionRules::SingleChoice',
                          key: 'type',
                          key_name: '类型',
                          opt: 'include',
                          val: ['time', 'date'],
                        },
                        desc: {
                          name: '类型',
                          optZh: '含有',
                          modelValue: { rule: { val: ['time', 'date'] } },
                          template: {
                            key: 'key',
                            type: 'layout',
                            fields: [
                              {
                                key: 'ditto_1683105208916_8',
                                name: '值',
                                type: 'select',
                                model: { attr_type: 'array' },
                                rules: [],
                                fields: [],
                                options: {
                                  span: 24,
                                  ditto: 'ditto_single',
                                  dynamic_component: '',
                                  select: [
                                    { label: '数字', value: 'number' },
                                    { label: '文字', value: 'string' },
                                    { label: '选择', value: 'select' },
                                    { label: '时间', value: 'time' },
                                    { label: '日期', value: 'date' },
                                    { label: '动态组件', value: 'dynamicComponent' },
                                  ],
                                  multiple: true,
                                  table_items: [],
                                  display_configurable_form: {},
                                  import_export_headers: [{ _id: '1683102166933_0' }],
                                  formDisabled: false,
                                  disabled: false,
                                  accessibility: 'writeable',
                                },
                                model_key: 'val',
                                conditions: [],
                                model_key_prefix: 'rule',
                                icon: 'FolderOutlined',
                              },
                            ],
                          },
                        },
                      },
                    ],
                  },
                ],
                name: '时间或日期',
              },
            },
          ],
          options: { span: 24 },
          key: 'condition_1683105014215_1',
          model_key: 'condition_1683105014215_1',
          fields: [],
          model_key_prefix: '',
        },
      ],
      rules: [],
      model: { attr_type: 'array' },
      options: { span: 24, disabled_actions: {} },
      key: 'list_1683102093752_2',
      model_key: 'searcher',
      conditions: [],
      model_key_prefix: '',
    },
  ],
  conditions: [],
};
