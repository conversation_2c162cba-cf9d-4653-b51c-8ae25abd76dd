export const styleTemplate = {
  type: 'layout',
  model: {},
  key: 'layout_1638002857216_0',
  model_key: 'layout_1638002857216_0',
  fields: [
    {
      name: '创建文案',
      type: 'input',
      rules: [],
      model: { attr_type: 'string' },
      options: { span: 24, defaultValue: '提交' },
      key: 'input_1638003086207_2',
      model_key: 'create_text',
      fields: [],
      conditions: [],
      model_key_prefix: '',
    },
    {
      name: '更新文案',
      type: 'input',
      rules: [],
      model: { attr_type: 'string' },
      options: { span: 24, defaultValue: '提交' },
      key: 'input_1638003082300_1',
      model_key: 'update_text',
      fields: [],
      conditions: [],
      model_key_prefix: '',
    },
    {
      name: '手机样式',
      type: 'label',
      rules: [],
      model: { attr_type: 'string' },
      options: { span: 24, label: {} },
      key: 'label_1638002995805_1',
      model_key: 'label_1638002995805_1',
      fields: [],
      conditions: [],
      model_key_prefix: '',
    },
    {
      name: '嵌套对象',
      type: 'key_layout',
      options: { span: 24 },
      key: 'key_layout_1638002866832_1',
      model_key: 'mobile_style',
      fields: [
        {
          name: '普通布局',
          type: 'layout',
          fields: [
            {
              name: '背景图',
              type: 'image',
              rules: [],
              model: { attr_type: 'array' },
              options: { span: 24, multiple: true },
              key: 'image_1638002903201_3',
              model_key: 'background_image',
              fields: [],
              conditions: [],
              model_key_prefix: 'mobile_style',
            },
            {
              name: 'margin',
              type: 'input',
              rules: [],
              model: { attr_type: 'string' },
              options: { span: 24 },
              key: 'input_1638002921529_4',
              model_key: 'margin',
              fields: [],
              conditions: [],
              model_key_prefix: 'mobile_style',
            },
          ],
          options: { span: 24, label: { width: 50, align: 'left' } },
          key: 'layout_1638002869339_2',
          model_key: 'layout_1638002869339_2',
          conditions: [],
          model_key_prefix: 'mobile_style',
        },
      ],
      conditions: [],
      model_key_prefix: '',
    },
  ],
  conditions: [],
  options: { label: {}, disabled_actions: {} },
  actions: [
    { key: 'create', enabled: true },
    { key: 'update', enabled: true },
    { key: 'delete', enabled: true },
    { key: 'import', enabled: true },
    { key: 'export', enabled: true },
  ],
  input_1638003082300_1: '提交',
  input_1638003086207_2: '提交',
};
