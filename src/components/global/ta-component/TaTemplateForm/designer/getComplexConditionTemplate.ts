import { TaTemplateFormItem } from '../../ta-template-form-core/types';
import { BpmNodeConditionOptField } from '../../../../../engines/bpm/components/designer/nodeEditor/ComBpmNodeEditorCondition.vue';

const getComplexConditionTemplate = (
  fieldOpts: BpmNodeConditionOptField[] | undefined,
  workflowTemplate: TaTemplateFormItem,
  groupCardLabelFunc?: (item: TaTemplateFormItem, index: number) => string,
  itemCardLabelFunc?: (item: TaTemplateFormItem, index: number) => string,
): TaTemplateFormItem => {
  return {
    key: 'layout_1632799069699_0',
    type: 'layout',
    model: {},
    fields: [
      {
        key: 'input_1632801013082_4',
        name: '条件名称',
        type: 'input',
        model: { attr_type: 'string' },
        rules: [],
        fields: [],
        options: { span: 24 },
        model_key: 'name',
        conditions: [],
        model_key_prefix: '',
      },
      {
        key: 'list_1632800888658_2',
        name: '条件组',
        type: 'list',
        fields: [
          {
            key: 'layout_1632801052032_5',
            name: '普通布局',
            type: 'layout',
            fields: [
              {
                key: 'list_1632801079998_6',
                name: '条件项',
                type: 'list',
                fields: [
                  {
                    key: 'layout_1632801149466_9',
                    name: '普通布局',
                    type: 'layout',
                    fields: [
                      {
                        key: 'key_layout_1632801192300_10',
                        name: '嵌套对象',
                        type: 'key_layout',
                        fields: [
                          {
                            key: 'layout_1632802488746_22',
                            name: '普通布局',
                            type: 'layout',
                            fields: [
                              {
                                key: 'dynamic_component_1633101059886_2',
                                name: '表单对象',
                                type: 'dynamic_component',
                                model: {},
                                rules: [],
                                fields: [],
                                options: {
                                  span: 24,
                                  dynamic_component: 'TaTemplateFormConditionFormSelector',
                                  meta: { fieldOpts, workflowTemplate },
                                },
                                model_key: 'form_seq',
                                conditions: [],
                                model_key_prefix: 'rule',
                              },
                              {
                                key: 'condition_1632801595713_121',
                                name: '条件块',
                                type: 'condition',
                                fields: [],
                                options: { span: 24 },
                                model_key: 'condition_1632801595713_121',
                                conditions: [
                                  {
                                    opt: '!=',
                                    val: 'user',
                                    name: '不是 user',
                                    type: 'simple',
                                    fields: [
                                      {
                                        key: 'dynamic_component_1633101059886_1',
                                        name: '条件值',
                                        type: 'dynamic_component',
                                        model: {},
                                        rules: [],
                                        fields: [],
                                        options: {
                                          span: 24,
                                          dynamic_component:
                                            'TaTemplateFormConditionTargetSelector',
                                          // meta: { template },
                                        },
                                        model_key: 'key',
                                        conditions: [],
                                        model_key_prefix: 'rule',
                                      },
                                      // {
                                      //   key: 'input_1632801013082_411',
                                      //   name: '条件 KEY',
                                      //   type: 'input',
                                      //   model: {},
                                      //   rules: [],
                                      //   fields: [],
                                      //   options: {
                                      //     span: 24,
                                      //   },
                                      //   model_key: 'key',
                                      //   conditions: [],
                                      //   model_key_prefix: 'rule',
                                      // },
                                      {
                                        key: 'select_1632801095948_7',
                                        name: '类型',
                                        type: 'select',
                                        model: { attr_type: 'string' },
                                        rules: [],
                                        fields: [],
                                        options: {
                                          span: 24,
                                          select: [
                                            {
                                              label: '数字',
                                              value: 'Com::Attr::ConditionRules::Number',
                                            },
                                            {
                                              label: '布尔',
                                              value: 'Com::Attr::ConditionRules::Boolean',
                                            },
                                            {
                                              label: '字符串',
                                              value: 'Com::Attr::ConditionRules::String',
                                            },
                                            {
                                              label: '单选',
                                              value: 'Com::Attr::ConditionRules::SingleChoice',
                                            },
                                            {
                                              label: '多选',
                                              value: 'Com::Attr::ConditionRules::MultiChoice',
                                            },
                                          ],
                                          multiple: false,
                                          // formDisabled: true,
                                        },
                                        model_key: 'type',
                                        conditions: [],
                                        model_key_prefix: 'rule',
                                      },
                                    ],
                                    model_key: 'source_type',
                                    complex_condition: { groups: [] },
                                  },
                                ],
                                model_key_prefix: '',
                              },
                            ],
                            options: { span: 24, label: { width: 80 } },
                            model_key: 'layout_1632802488746_22',
                            conditions: [],
                            model_key_prefix: 'rule',
                          },
                          {
                            key: 'condition_1632801595713_12',
                            name: '条件块',
                            type: 'condition',
                            fields: [],
                            options: { span: 24 },
                            model_key: 'condition_1632801595713_12',
                            conditions: [
                              {
                                opt: '==',
                                val: 'Com::Attr::ConditionRules::Number',
                                name: '数字',
                                type: 'simple',
                                fields: [
                                  {
                                    key: 'select_1632801709101_14',
                                    name: '',
                                    type: 'select',
                                    model: { attr_type: 'string' },
                                    rules: [],
                                    fields: [],
                                    options: {
                                      span: 24,
                                      select: [
                                        { label: '大于', value: '>' },
                                        { label: '大于等于', value: '>=' },
                                        { label: '等于', value: '==' },
                                        { label: '小于', value: '<' },
                                        { label: '小于等于', value: '<=' },
                                        { label: '不等于', value: '!=' },
                                        { label: '范围', value: 'between' },
                                      ],
                                      multiple: false,
                                    },
                                    model_key: 'opt',
                                    conditions: [],
                                    model_key_prefix: 'rule',
                                  },
                                  {
                                    key: 'condition_1632801705551_13',
                                    name: '条件块',
                                    type: 'condition',
                                    fields: [],
                                    options: { span: 24 },
                                    model_key: 'condition_1632801705551_13',
                                    conditions: [
                                      {
                                        opt: '==',
                                        val: 'between',
                                        name: '范围',
                                        type: 'simple',
                                        fields: [
                                          {
                                            key: 'string_array_1632802108416_17',
                                            name: '开区间',
                                            type: 'string_array',
                                            model: { attr_type: 'array' },
                                            rules: [],
                                            fields: [],
                                            options: { span: 24 },
                                            model_key: 'val',
                                            conditions: [],
                                            model_key_prefix: 'rule',
                                          },
                                        ],
                                        model_key: 'rule.opt',
                                        complex_condition: { groups: [] },
                                      },
                                      {
                                        opt: '!=',
                                        val: 'between',
                                        name: '非范围',
                                        type: 'simple',
                                        fields: [
                                          {
                                            key: 'number_1632802020086_16',
                                            name: '值',
                                            type: 'number',
                                            model: { attr_type: 'number' },
                                            rules: [],
                                            fields: [],
                                            options: { span: 24 },
                                            model_key: 'val',
                                            conditions: [],
                                            model_key_prefix: 'rule',
                                          },
                                        ],
                                        model_key: 'rule.opt',
                                        complex_condition: { groups: [] },
                                      },
                                    ],
                                    model_key_prefix: 'rule',
                                  },
                                ],
                                model_key: 'rule.type',
                                complex_condition: { groups: [] },
                              },
                              {
                                opt: '==',
                                val: 'Com::Attr::ConditionRules::Boolean',
                                name: '布尔',
                                type: 'simple',
                                fields: [
                                  {
                                    key: 'select_1632802203913_18',
                                    name: '',
                                    type: 'select',
                                    model: { attr_type: 'string' },
                                    rules: [],
                                    fields: [],
                                    options: {
                                      span: 24,
                                      select: [
                                        { label: '等于', value: '==' },
                                        { label: '不等于', value: '!=' },
                                      ],
                                      multiple: false,
                                    },
                                    model_key: 'opt',
                                    conditions: [],
                                    model_key_prefix: 'rule',
                                  },
                                  {
                                    key: 'switch_1632802235314_19',
                                    name: '值',
                                    type: 'switch',
                                    model: { attr_type: 'boolean' },
                                    rules: [],
                                    fields: [],
                                    options: { span: 24, placeholder: '' },
                                    model_key: 'val',
                                    conditions: [],
                                    model_key_prefix: 'rule',
                                  },
                                ],
                                model_key: 'rule.type',
                                complex_condition: { groups: [] },
                              },
                              {
                                opt: '==',
                                val: 'Com::Attr::ConditionRules::String',
                                name: '字符串',
                                type: 'simple',
                                fields: [
                                  {
                                    key: 'select_1632802308881_20',
                                    name: '',
                                    type: 'select',
                                    model: { attr_type: 'string' },
                                    rules: [],
                                    fields: [],
                                    options: {
                                      span: 24,
                                      select: [
                                        { label: '等于', value: '==' },
                                        { label: '不等于', value: '!=' },
                                        { label: '包含', value: 'contains' },
                                      ],
                                      multiple: false,
                                      placeholder: '',
                                    },
                                    model_key: 'opt',
                                    conditions: [],
                                    model_key_prefix: 'rule',
                                  },
                                  {
                                    key: 'input_1632802358717_21',
                                    name: '值',
                                    type: 'input',
                                    model: { attr_type: 'string' },
                                    rules: [],
                                    fields: [],
                                    options: { span: 24 },
                                    model_key: 'val',
                                    conditions: [],
                                    model_key_prefix: 'rule',
                                  },
                                ],
                                model_key: 'rule.type',
                                complex_condition: { groups: [] },
                              },
                              {
                                opt: '==',
                                val: 'Com::Attr::ConditionRules::SingleChoice',
                                name: '单选',
                                type: 'simple',
                                fields: [
                                  {
                                    key: 'select_1633115173979_0',
                                    name: '',
                                    type: 'select',
                                    model: { attr_type: 'string' },
                                    rules: [],
                                    fields: [],
                                    options: {
                                      span: 24,
                                      select: [
                                        { label: '含有', value: 'include' },
                                        { label: '无一符合', value: 'none' },
                                      ],
                                      multiple: false,
                                      placeholder: '',
                                    },
                                    model_key: 'opt',
                                    conditions: [],
                                    model_key_prefix: 'rule',
                                  },
                                  {
                                    key: 'dynamic_component_1633111922226_0',
                                    name: '占位组件，供代码替换',
                                    type: 'dynamic_component',
                                    model: {},
                                    rules: [],
                                    fields: [],
                                    options: {
                                      span: 24,
                                      ditto: 'ditto_single',
                                      dynamic_component: '',
                                    },
                                    model_key: '',
                                    conditions: [],
                                    model_key_prefix: 'rule',
                                  },
                                ],
                                model_key: 'rule.type',
                                complex_condition: { groups: [] },
                              },
                              {
                                opt: '==',
                                val: 'Com::Attr::ConditionRules::MultiChoice',
                                name: '多选',
                                type: 'simple',
                                fields: [
                                  {
                                    key: 'select_1633115233766_1',
                                    name: '',
                                    type: 'select',
                                    model: { attr_type: 'string' },
                                    rules: [],
                                    fields: [],
                                    options: {
                                      span: 24,
                                      select: [
                                        { label: '包含', value: 'include' },
                                        { label: '无一符合', value: 'none' },
                                        { label: '有交集', value: 'any' },
                                        { label: '相等', value: '==' },
                                      ],
                                      multiple: false,
                                    },
                                    model_key: 'opt',
                                    conditions: [],
                                    model_key_prefix: 'rule',
                                  },
                                  {
                                    key: 'dynamic_component_1633111975922_1',
                                    name: '占位组件，供代码替换',
                                    type: 'dynamic_component',
                                    model: {},
                                    rules: [],
                                    fields: [],
                                    options: {
                                      span: 24,
                                      ditto: 'ditto_multi',
                                      dynamic_component: '',
                                    },
                                    model_key: '',
                                    conditions: [],
                                    model_key_prefix: 'rule',
                                  },
                                ],
                                model_key: 'rule.type',
                                complex_condition: { groups: [] },
                              },
                            ],
                            model_key_prefix: 'rule',
                          },
                        ],
                        options: { span: 24 },
                        model_key: 'rule',
                        conditions: [],
                        model_key_prefix: '',
                      },
                    ],
                    options: { span: 24, label: { width: 80 } },
                    model_key: 'layout_1632801149466_9',
                    conditions: [],
                    model_key_prefix: '',
                  },
                ],
                options: { span: 24, edit_directly: true, cardLabel: itemCardLabelFunc },
                model_key: 'items',
                conditions: [],
                model_key_prefix: '',
              },
            ],
            options: { span: 24, label: {} },
            model_key: 'layout_1632801052032_5',
            conditions: [],
            model_key_prefix: '',
          },
        ],
        options: { span: 24, edit_directly: true, cardLabel: groupCardLabelFunc },
        model_key: 'groups',
        conditions: [],
        model_key_prefix: '',
      },
    ],
    options: { label: { width: 80 }, theme: 'none' },
    model_key: 'layout_1632799069699_0',
    conditions: [],
    index_attributes: [],
    column_attributes: [],
  };
};

export default getComplexConditionTemplate;
