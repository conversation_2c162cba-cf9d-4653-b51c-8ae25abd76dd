import { TaTemplateFormItem } from '../../ta-template-form-core/types';

const columnAttributeExportTemplate: TaTemplateFormItem = {
  type: 'layout',
  model: {},
  key: 'layout_1644815770735_0',
  model_key: 'layout_1644815770735_0',
  fields: [
    {
      name: 'export',
      type: 'key_layout',
      options: { span: 24 },
      key: 'key_layout_1644815800150_1',
      model_key: 'export',
      fields: [
        {
          key: 'label_1633598995175_1',
          name: '导出',
          type: 'label',
          model: { attr_type: 'string' },
          rules: [],
          fields: [],
          options: { span: 24 },
          model_key: 'label_1633598995175_5',
          conditions: [],
          model_key_prefix: '',
        },
        {
          key: 'switch_1644305256822',
          name: '开启导出',
          type: 'switch',
          model: { attr_type: 'boolean' },
          rules: [],
          fields: [],
          options: { span: 24 },
          model_key: 'on',
          conditions: [],
          model_key_prefix: 'export',
        },
        {
          name: 'export aggregation',
          type: 'list',
          fields: [
            {
              name: '类型',
              type: 'radio',
              rules: [],
              model: { attr_type: 'string' },
              options: { select: [{ label: '$cond', value: '' }], multiple: false, span: 24 },
              key: 'radio_1644815842737_3',
              model_key: 'type',
              fields: [],
              conditions: [],
              model_key_prefix: '',
            },
            {
              name: '条件块',
              type: 'condition',
              conditions: [
                {
                  name: '$cond',
                  model_key: 'type',
                  val: '$cond',
                  fields: [
                    {
                      name: '$cond',
                      type: 'json',
                      rules: [],
                      model: { attr_type: 'object' },
                      options: { span: 24 },
                      key: 'json_1644816012599_6',
                      model_key: '$cond',
                      fields: [],
                      conditions: [],
                      model_key_prefix: '',
                    },
                  ],
                  opt: '==',
                  type: 'simple',
                },
              ],
              options: { span: 24 },
              key: 'condition_1644815919390_4',
              model_key: 'condition_1644815919390_4',
              fields: [],
              model_key_prefix: '',
            },
          ],
          rules: [],
          model: { attr_type: 'array' },
          options: {
            span: 24,
            disabled_actions: {},
            edit_directly: true,
          },
          key: 'list_1644815809436_2',
          model_key: 'aggre',
          conditions: [],
          model_key_prefix: 'export',
        },
      ],
      conditions: [],
      model_key_prefix: '',
    },
  ],
  conditions: [],
  options: {
    label: {},
    disabled_actions: {},
    theme: { card: {}, background: {} },
    create_text: '提交',
    update_text: '提交',
  },
  actions: [
    { key: 'create', enabled: true },
    { key: 'update', enabled: true },
    { key: 'delete', enabled: true },
    { key: 'import', enabled: true },
    { key: 'export', enabled: true },
  ],
};

const columnAttributeImportTemplate: TaTemplateFormItem = {
  type: 'layout',
  model: {},
  key: 'layout_1644815770735_01',
  model_key: 'layout_1644815770735_01',
  fields: [
    {
      name: 'import',
      type: 'key_layout',
      options: { span: 24 },
      key: 'key_layout_1644815800150_11',
      model_key: 'import',
      fields: [
        {
          key: 'label_1633598995175_11',
          name: '导入',
          type: 'label',
          model: { attr_type: 'string' },
          rules: [],
          fields: [],
          options: { span: 24 },
          model_key: 'label_1633598995175_15',
          conditions: [],
          model_key_prefix: '',
        },
        {
          key: 'switch_16443052568221',
          name: '开启导入',
          type: 'switch',
          model: { attr_type: 'boolean' },
          rules: [],
          fields: [],
          options: { span: 24 },
          model_key: 'on',
          conditions: [],
          model_key_prefix: 'import',
        },
        {
          name: 'import aggregation',
          type: 'list',
          fields: [
            {
              name: '类型',
              type: 'radio',
              rules: [],
              model: { attr_type: 'string' },
              options: { select: [{ label: '$cond', value: '' }], multiple: false, span: 24 },
              key: 'radio_1644815842737_13',
              model_key: 'type',
              fields: [],
              conditions: [],
              model_key_prefix: '',
            },
            {
              name: '条件块',
              type: 'condition',
              conditions: [
                {
                  name: '$cond',
                  model_key: 'type',
                  val: '$cond',
                  fields: [
                    {
                      name: '$cond',
                      type: 'json',
                      rules: [],
                      model: { attr_type: 'object' },
                      options: { span: 24 },
                      key: 'json_1644816012599_16',
                      model_key: '$cond',
                      fields: [],
                      conditions: [],
                      model_key_prefix: '',
                    },
                  ],
                  opt: '==',
                  type: 'simple',
                },
              ],
              options: { span: 24 },
              key: 'condition_1644815919390_14',
              model_key: 'condition_1644815919390_14',
              fields: [],
              model_key_prefix: '',
            },
          ],
          rules: [],
          model: { attr_type: 'array' },
          options: {
            span: 24,
            disabled_actions: {},
            edit_directly: true,
          },
          key: 'list_1644815809436_12',
          model_key: 'aggre',
          conditions: [],
          model_key_prefix: 'import',
        },
      ],
      conditions: [],
      model_key_prefix: '',
    },
  ],
  conditions: [],
  options: {
    label: {},
    disabled_actions: {},
    theme: { card: {}, background: {} },
    create_text: '提交',
    update_text: '提交',
  },
  actions: [
    { key: 'create', enabled: true },
    { key: 'update', enabled: true },
    { key: 'delete', enabled: true },
    { key: 'import', enabled: true },
    { key: 'export', enabled: true },
  ],
};

const columnAttributesTemplate: TaTemplateFormItem = {
  key: 'layout_1632713521426_2',
  type: 'layout',
  model: {},
  fields: [
    {
      key: 'layout_1632713528615_3',
      name: '普通布局',
      type: 'layout',
      fields: [
        {
          key: 'input_1632713532140_6',
          name: '表头',
          type: 'input',
          model: { attr_type: 'string' },
          rules: [],
          fields: [],
          options: { span: 9 },
          model_key: 'title',
          conditions: [],
          model_key_prefix: '',
        },

        {
          key: 'switch_1644305256823',
          name: '列表',
          type: 'switch',
          model: { attr_type: 'boolean' },
          rules: [],
          fields: [],
          options: { span: 5, defaultValue: true },
          model_key: 'on',
          conditions: [],
          model_key_prefix: 'index',
        },
        {
          key: 'switch_1644305256825',
          name: '导入',
          type: 'switch',
          model: { attr_type: 'boolean' },
          rules: [],
          fields: [],
          options: { span: 5 },
          model_key: 'on',
          conditions: [],
          model_key_prefix: 'import',
        },
        {
          key: 'switch_1644305256822',
          name: '导出',
          type: 'switch',
          model: { attr_type: 'boolean' },
          rules: [],
          fields: [],
          options: { span: 5 },
          model_key: 'on',
          conditions: [],
          model_key_prefix: 'export',
        },
        {
          name: '关键字',
          type: 'input',
          rules: [],
          model: { attr_type: 'string' },
          options: { span: 12 },
          key: 'input_1632885357435_0',
          model_key: 'dataIndex',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
        {
          key: 'input_1632713529842_4',
          name: '标识',
          type: 'input',
          model: { attr_type: 'string' },
          rules: [],
          fields: [],
          options: { span: 12 },
          model_key: 'key',
          conditions: [],
          model_key_prefix: '',
        },
      ],
      options: { span: 24, label: { width: 80 } },
      model_key: 'layout_1632713528615_3',
      conditions: [],
      model_key_prefix: '',
    },
  ],
  options: { label: { align: 'left' }, layout: 'vertical', theme: 'none' },
  model_key: 'layout_1632713521426_2',
  conditions: [],
  index_attributes: [],
  column_attributes: [],
};

const columnAttributesMoreTemplate: TaTemplateFormItem = {
  key: 'layout_1633598880223_0',
  type: 'layout',
  model: {},
  fields: [
    {
      key: 'layout_1633598887516_1',
      name: '普通布局',
      type: 'layout',
      fields: [
        {
          key: 'label_1633598899409_2',
          name: '文字',
          type: 'label',
          model: { attr_type: 'string' },
          rules: [],
          fields: [],
          options: { span: 24 },
          model_key: 'label_1633598899409_2',
          conditions: [],
          model_key_prefix: '',
        },
        {
          key: 'select_1633598909972_3',
          name: '文字对齐',
          type: 'select',
          model: { attr_type: 'string' },
          rules: [],
          fields: [],
          options: {
            span: 24,
            select: [
              { label: '左', value: 'left' },
              { label: '中', value: 'center' },
              { label: '右', value: 'right' },
            ],
            multiple: false,
          },
          model_key: 'align',
          conditions: [],
          model_key_prefix: '',
        },
        {
          key: 'switch_1633598965293_4',
          name: '文字省略',
          type: 'switch',
          model: { attr_type: 'boolean' },
          rules: [],
          fields: [],
          options: { span: 24 },
          model_key: 'ellipsis',
          conditions: [],
          model_key_prefix: '',
        },

        {
          key: 'label_1633598995175_5',
          name: '样式',
          type: 'label',
          model: { attr_type: 'string' },
          rules: [],
          fields: [],
          options: { span: 24 },
          model_key: 'label_1633598995175_5',
          conditions: [],
          model_key_prefix: '',
        },
        {
          key: 'select_1633599004959_6',
          name: '渲染类型',
          type: 'select',
          model: { attr_type: 'string' },
          rules: [],
          fields: [],
          options: {
            span: 24,
            select: [{ label: '自动', value: 'TableRendersAuto' }],
            multiple: false,
          },
          model_key: 'render',
          conditions: [],
          model_key_prefix: '',
        },
        {
          key: 'input_1633599062027_7',
          name: '渲染组件',
          type: 'input',
          model: { attr_type: 'string' },
          rules: [],
          fields: [],
          options: { span: 24 },
          model_key: 'render',
          conditions: [],
          model_key_prefix: '',
        },
        {
          key: 'string_1633599089728_8',
          name: '列宽',
          type: 'input',
          model: { attr_type: 'string' },
          rules: [],
          fields: [],
          options: { span: 24 },
          model_key: 'width',
          conditions: [],
          model_key_prefix: '',
        },
        {
          key: 'color_1633599004959_6',
          name: '字体颜色',
          type: 'color',
          model: { attr_type: 'string' },
          rules: [],
          fields: [],
          options: {
            span: 24,
          },
          model_key: 'color',
          conditions: [],
          model_key_prefix: '',
        },
        {
          key: 'radio_1633599116078_9',
          name: '固定',
          type: 'radio',
          model: { attr_type: 'string' },
          rules: [],
          fields: [],
          options: {
            span: 24,
            select: [
              { label: '左固定', value: 'left' },
              { label: '右固定', value: 'right' },
            ],
            multiple: false,
            placeholder: '',
          },
          model_key: 'fixed',
          conditions: [],
          model_key_prefix: '',
        },
        {
          key: 'number_1633599162231_10',
          name: 'colSpan',
          type: 'number',
          model: { attr_type: 'number' },
          rules: [],
          fields: [],
          options: { span: 24 },
          model_key: 'colSpan',
          conditions: [],
          model_key_prefix: '',
        },
        {
          key: 'label_1644305225237_1',
          name: '过滤',
          type: 'label',
          model: { attr_type: 'string' },
          rules: [],
          fields: [],
          options: { span: 24, label: {} },
          model_key: 'label_1644305225237_1',
          conditions: [],
          model_key_prefix: '',
        },
        {
          key: 'switch_1644305256820_2',
          name: '开启过滤',
          type: 'switch',
          model: { attr_type: 'boolean' },
          rules: [],
          fields: [],
          options: { span: 24 },
          model_key: 'filtered',
          conditions: [],
          model_key_prefix: '',
        },
        {
          key: 'switch_1644305256820_2',
          name: '显示在表头',
          type: 'switch',
          model: { attr_type: 'boolean' },
          rules: [],
          fields: [],
          options: { span: 24 },
          model_key: 'filterDropdownVisible',
          conditions: [],
          model_key_prefix: '',
        },
        {
          key: 'string_array_1644305292250_3',
          name: '过滤选项',
          type: 'string_array',
          model: { attr_type: 'array' },
          rules: [],
          fields: [],
          options: { span: 24 },
          model_key: 'filteredValue',
          conditions: [],
          model_key_prefix: '',
        },
        {
          name: '过滤详情',
          type: 'list',
          fields: [
            {
              name: '标题',
              type: 'input',
              rules: [],
              model: {
                attr_type: 'string',
              },
              options: {
                span: 24,
              },
              key: 'input_1644723919580_3',
              model_key: 'label',
              fields: [],
              conditions: [],
              model_key_prefix: '',
            },
            {
              name: 'ransack',
              type: 'json',
              rules: [],
              model: {
                attr_type: 'object',
              },
              options: {
                span: 24,
              },
              key: 'json_1644723962033_4',
              model_key: 'ransack',
              fields: [],
              conditions: [],
              model_key_prefix: '',
            },
          ],
          rules: [],
          model: {
            attr_type: 'array',
          },
          options: {
            span: 24,
            disabled_actions: {},
            edit_directly: true,
          },
          key: 'list_1644723893197_1',
          model_key: 'filteredDetails',
          conditions: [],
          model_key_prefix: '',
        },
        {
          name: '排序',
          type: 'label',
          rules: [],
          model: { attr_type: 'string' },
          options: { span: 24, label: {} },
          key: 'label_1644305385894_0',
          model_key: 'label_1644305385894_0',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
        {
          name: '开启排序',
          type: 'switch',
          rules: [],
          model: { attr_type: 'boolean' },
          options: { span: 24 },
          key: 'switch_1644305402249_1',
          model_key: 'sorter',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
        columnAttributeExportTemplate,
        columnAttributeImportTemplate,
      ],
      options: { span: 24, label: { width: 80 } },
      model_key: 'layout_1633598887516_1',
      conditions: [],
      model_key_prefix: '',
    },
  ],
  actions: [
    { key: 'create', enabled: true },
    { key: 'update', enabled: true },
    { key: 'delete', enabled: true },
    { key: 'import', enabled: true },
    { key: 'export', enabled: true },
  ],
  options: {
    label: { align: 'left' },
    create_text: '提交',
    update_text: '提交',
    theme: 'none',
  },
  model_key: 'layout_1633598880223_0',
  conditions: [],
  index_attributes: [],
  column_attributes: [],
};
export { columnAttributesTemplate, columnAttributesMoreTemplate };
