const actionTemplate = {
  key: 'layout_1633575756956_1',
  type: 'layout',
  model: {},
  fields: [
    {
      key: 'list_1633575775602_3',
      name: '操作',
      type: 'list',
      fields: [
        {
          key: 'layout_1633576092851_13',
          name: '普通布局',
          type: 'layout',
          fields: [
            {
              key: 'input_1633575834196_4',
              name: '操作名',
              type: 'input',
              model: { attr_type: 'string' },
              rules: [],
              fields: [],
              options: { span: 24 },
              model_key: 'label',
              conditions: [],
              model_key_prefix: '',
            },
            {
              key: 'input_1633575835150_5',
              name: '操作英文',
              type: 'input',
              model: { attr_type: 'string' },
              rules: [],
              fields: [],
              options: { span: 24 },
              model_key: 'key',
              conditions: [],
              model_key_prefix: '',
            },
            {
              key: 'input_1633575882646_6',
              name: '图标',
              type: 'input',
              model: { attr_type: 'string' },
              rules: [],
              fields: [],
              options: { span: 24 },
              model_key: 'icon',
              conditions: [],
              model_key_prefix: '',
            },
            {
              key: 'radio_1633575995515_11',
              name: '类型',
              type: 'radio',
              model: { attr_type: 'string' },
              rules: [],
              fields: [],
              options: {
                span: 24,
                select: [
                  { label: '选择后出现', value: 'selectable' },
                  { label: '多对象', value: 'collection' },
                  { label: '单一对象', value: 'member' },
                ],
                multiple: false,
              },
              model_key: 'action_type',
              conditions: [],
              model_key_prefix: '',
            },
          ],
          options: { span: 24, label: { width: 50 } },
          model_key: 'layout_1633576092851_13',
          conditions: [],
          model_key_prefix: '',
        },
        {
          key: 'switch_1633575916797_8',
          name: '开启',
          type: 'switch',
          model: { attr_type: 'boolean' },
          rules: [],
          fields: [],
          options: { span: 8 },
          model_key: 'enabled',
          conditions: [],
          model_key_prefix: '',
        },
        {
          key: 'switch_1633575899631_7',
          name: '确认',
          type: 'switch',
          model: { attr_type: 'boolean' },
          rules: [],
          fields: [],
          options: { span: 8 },
          model_key: 'confirm',
          conditions: [],
          model_key_prefix: '',
        },
        {
          key: 'json_1633575899631_7',
          name: '确认文案',
          type: 'json',
          model: { attr_type: 'any' },
          rules: [],
          fields: [],
          options: { span: 24 },
          model_key: 'options.confirmText',
          conditions: [],
          model_key_prefix: '',
        },
        {
          key: 'switch_1633575955698_9',
          name: '收起',
          type: 'switch',
          model: { attr_type: 'boolean' },
          rules: [],
          fields: [],
          options: { span: 8 },
          model_key: 'collapsed',
          conditions: [],
          model_key_prefix: '',
        },
        {
          key: 'textarea_1633576045051_12',
          name: '回调',
          type: 'textarea',
          model: { attr_type: 'string' },
          rules: [],
          fields: [],
          options: { span: 24 },
          model_key: 'callback',
          conditions: [],
          model_key_prefix: '',
        },
      ],
      options: { span: 24, edit_directly: true, disabled_actions: {} },
      model_key: 'actions',
      conditions: [],
      model_key_prefix: '',
    },
  ],
  options: { label: { width: null } },
  model_key: 'layout_1633575756956_1',
  conditions: [],
  index_attributes: [],
  column_attributes: [],
  actions: [
    { key: 'create', enabled: true },
    { key: 'update', enabled: true },
    { key: 'delete', enabled: true },
    { key: 'import', enabled: true },
    { key: 'export', enabled: true },
  ],
};
export default actionTemplate;
