export const normalLayoutTemplate = {
  type: 'layout',
  model: {},
  key: 'layout_1641957420754_0',
  model_key: 'layout_1641957420754_0',
  fields: [
    // {
    //   name: '是否容器化',
    //   type: 'switch',
    //   rules: [],
    //   model: { attr_type: 'boolean' },
    //   // options: { span: 24, defaultValue: true },
    //   options: { span: 24 },
    //   key: 'switch_1641958295873_3',
    //   model_key: 'container',
    //   fields: [],
    //   conditions: [],
    // },
    {
      name: '是否有框线',
      type: 'switch',
      rules: [],
      model: { attr_type: 'boolean' },
      // options: { span: 24, defaultValue: true },
      options: { span: 24 },
      key: 'switch_1641958295873_123',
      model_key: 'layout_border',
      fields: [],
      conditions: [],
    },
    {
      name: '嵌套对象',
      type: 'key_layout',
      options: { span: 24 },
      key: 'key_layout_1641957750353_10',
      model_key: 'theme',
      fields: [
        {
          name: '圆角',
          type: 'switch',
          rules: [],
          model: { attr_type: 'boolean' },
          // options: { span: 24, defaultValue: true },
          options: { span: 24 },
          key: 'switch_1641958295873_13',
          model_key: 'radius',
          fields: [],
          conditions: [],
          model_key_prefix: 'theme',
        },
        {
          name: '背景',
          type: 'label',
          rules: [],
          model: { attr_type: 'string' },
          options: { span: 24, label: {} },
          key: 'label_1641957902554_11',
          model_key: 'label_1641957902554_11',
          fields: [],
          conditions: [],
          model_key_prefix: 'theme',
        },
        {
          name: '嵌套对象',
          type: 'key_layout',
          options: { span: 24 },
          key: 'key_layout_1641957439390_2',
          model_key: 'background',
          fields: [
            {
              name: '背景色',
              type: 'input',
              rules: [],
              model: { attr_type: 'string' },
              options: { span: 24 },
              key: 'color_1641957487133_5',
              model_key: 'color',
              fields: [],
              conditions: [],
              model_key_prefix: 'theme.background',
            },
            {
              name: '表单颜色',
              type: 'input',
              rules: [],
              model: { attr_type: 'string' },
              options: { span: 24 },
              key: 'color_1641957487133_5',
              model_key: 'background',
              fields: [],
              conditions: [],
              model_key_prefix: 'theme.form',
            },
            {
              name: '背景图',
              type: 'image',
              rules: [],
              model: { attr_type: 'array' },
              options: { span: 24, multiple: true },
              key: 'image_1641957469245_4',
              model_key: 'image',
              fields: [],
              conditions: [],
              model_key_prefix: 'theme.background',
            },
          ],
          conditions: [],
          model_key_prefix: 'theme',
        },
        {
          name: '卡片',
          type: 'label',
          rules: [],
          model: { attr_type: 'string' },
          options: { span: 24, label: {} },
          key: 'label_1641957907101_12',
          model_key: 'label_1641957907101_12',
          fields: [],
          conditions: [],
          model_key_prefix: 'theme',
        },
        {
          name: '嵌套对象',
          type: 'key_layout',
          options: { span: 24 },
          key: 'key_layout_1641957441252_3',
          model_key: 'card',
          fields: [
            {
              name: '图片',
              type: 'image',
              rules: [],
              model: { attr_type: 'array' },
              options: { span: 24, multiple: true },
              key: 'image_1641957526266_6',
              model_key: 'image',
              fields: [],
              conditions: [],
              model_key_prefix: 'theme.card',
            },
            {
              name: '标题',
              type: 'input',
              rules: [],
              model: { attr_type: 'string' },
              options: { span: 24 },
              key: 'input_1641957536333_7',
              model_key: 'title',
              fields: [],
              conditions: [],
              model_key_prefix: 'theme.card',
            },
            {
              name: '富文本',
              type: 'rich_text',
              rules: [],
              model: { attr_type: 'string' },
              options: { span: 24 },
              key: 'rich_text_1641957562464_8',
              model_key: 'content',
              fields: [],
              conditions: [],
              model_key_prefix: 'theme.card',
            },
            {
              name: '文件',
              type: 'file',
              rules: [],
              model: { attr_type: 'array' },
              options: { span: 24, multiple: true },
              key: 'file_1641957588535_9',
              model_key: 'files',
              fields: [],
              conditions: [],
              model_key_prefix: 'theme.card',
            },
          ],
          conditions: [],
          model_key_prefix: 'theme',
        },
      ],
      conditions: [],
      model_key_prefix: '',
    },
  ],
  conditions: [],
  options: {
    label: { width: 80 },
    disabled_actions: {},
    create_text: '提交',
    update_text: '提交',
    layout: 'vertical',
    theme: 'none',
  },
  actions: [
    { key: 'create', enabled: true },
    { key: 'update', enabled: true },
    { key: 'delete', enabled: true },
    { key: 'import', enabled: true },
    { key: 'export', enabled: true },
  ],
};
