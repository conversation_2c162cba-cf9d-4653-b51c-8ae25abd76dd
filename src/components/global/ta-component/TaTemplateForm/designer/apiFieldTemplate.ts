export const api<PERSON><PERSON><PERSON>eaderTemplate = {
  type: 'layout',
  model: {},
  key: 'layout_1639295119402_0',
  model_key: 'layout_1639295119402_0',
  fields: [
    {
      name: '',
      type: 'list',
      fields: [
        {
          name: '普通布局',
          type: 'layout',
          fields: [
            {
              name: '名称',
              type: 'input',
              rules: [],
              model: { attr_type: 'string' },
              options: { span: 24 },
              key: 'input_1639295181136_4',
              model_key: 'name',
              fields: [],
              conditions: [],
              model_key_prefix: '',
            },
            {
              name: '字段',
              type: 'input',
              rules: [],
              model: { attr_type: 'string' },
              options: { span: 24 },
              key: 'input_1639295130062_2',
              model_key: 'key',
              fields: [],
              conditions: [],
              model_key_prefix: '',
            },
            {
              name: '索引',
              type: 'switch',
              rules: [],
              model: { attr_type: 'boolean' },
              options: { span: 24 },
              key: 'switch_1639295248675_5',
              model_key: 'primary_key',
              fields: [],
              conditions: [],
              model_key_prefix: '',
            },
          ],
          options: { span: 24, label: { width: 50, align: 'left' } },
          key: 'layout_1639295153322_3',
          model_key: 'layout_1639295153322_3',
          conditions: [],
          model_key_prefix: '',
        },
      ],
      rules: [],
      model: { attr_type: 'array' },
      options: { span: 24, edit_directly: true, disabled_actions: {} },
      key: 'list_1639295128811_1',
      model_key: 'import_export_headers',
      conditions: [],
      model_key_prefix: '',
    },
  ],
  conditions: [],
  options: { label: {}, disabled_actions: {}, create_text: '提交', update_text: '提交' },
  actions: [
    { key: 'create', enabled: true },
    { key: 'update', enabled: true },
    { key: 'delete', enabled: true },
    { key: 'import', enabled: true },
    { key: 'export', enabled: true },
  ],
};
