<script lang="ts">
import { computed, defineComponent, toRefs } from 'vue';
import { getTaTemplateFormFieldMap, getTaTemplateFormFieldExtraPropsMap } from './form/types';

const TaTemplateFormSimpleViewerRender = defineComponent({
  name: 'TaTemplateFormSimpleViewerRender',
  components: {},
  props: {
    infoItem: { type: Object, default: () => ({}) },
  },
  setup(props) {
    const componentName = computed(() => {
      return getTaTemplateFormFieldMap(props.infoItem.field)[props.infoItem.field.type];
    });

    return {
      ...toRefs(props),
      componentName,
      getTaTemplateFormFieldExtraPropsMap,
    };
  },
});
export default TaTemplateFormSimpleViewerRender;
</script>

<template lang="pug">
a-col.ta-template-form-simple-viewer-render.w-full.gap-10(
  v-if='infoItem.format === "list"'
  :span='infoItem.span'
)
  a-row(v-for='aryItem in infoItem.value')
    TaTemplateFormSimpleViewerRender(
      v-for='(item, index) in aryItem',
      :key='index',
      :infoItem='item',
    )

a-col.ta-template-form-simple-viewer-render-item.p-1(
  v-else,
  :span='infoItem.span',
)
  .flex.items-center(:class='{ [`simple-viewer-${infoItem.field?.type}`]: true }')
    label.whitespace-nowrap.text-gray-600.flex-shrink-0(v-if='infoItem.label')
      | {{ infoItem.label }}：
    component.value(
      v-if='infoItem.format === "component"'
      :is='componentName',
      :value='infoItem.value',
      :item='infoItem.field',
      v-bind='getTaTemplateFormFieldExtraPropsMap(infoItem.field)[infoItem.field.type]',
      :disabled='true'
    )

    .flex.gap-2.value(
      v-else-if='infoItem.format === "file"'
    )
      TaAttachmentFile.simple-file(
        v-if='Array.isArray(infoItem.value) && infoItem.value.length',
        v-for='(file, index) in infoItem.value',
        :key='index',
        :attachment='file',
        :attachmentList='infoItem.value',
      )
        .text-primary.underline.whitespace-nowrap.font-medium.truncate.flex-grow-0
          | {{ file.fileName }}

      .text-red-800.whitespace-nowrap(v-else) 暂无文件
    .text-gray-800.value(
      v-else-if='typeof infoItem.format === "function"'
    )
      | {{ infoItem.format(infoItem.value) }}

    .text-gray-800.value(
      v-else-if='infoItem.format === "number" && infoItem.value !== null && infoItem.value !== undefined'
    ) {{ Number(infoItem.value).toLocaleString() }}{{ infoItem.field.options?.unit ? ` ${infoItem.field.options.unit}` : '' }}

    .text-gray-800.value(
      v-else
    ) {{ infoItem.value }}
</template>

<style lang="stylus" scoped>
.ta-template-form-simple-viewer-render-item
  .simple-file
    margin 0
    >>> .attachment
      padding 0
      height auto
</style>
