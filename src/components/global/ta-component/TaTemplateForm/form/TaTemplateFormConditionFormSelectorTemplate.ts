import { TaTemplateFormItem } from '@/components/global/ta-component/ta-template-form-core/types';
import { TaTemplateFormSelect } from '../../ta-template-form-core/types';

export const getTaTemplateFormConditionFormSelectorTemplate = (
  placeSelect: TaTemplateFormSelect[],
) => ({
  type: 'layout',
  model: {},
  key: 'layout_1650201722356_0',
  model_key: 'layout_1650201722356_0',
  fields: [
    {
      name: '表单类型',
      icon: 'FolderOutlined',
      type: 'radio',
      rules: [],
      model: { attr_type: 'string' },
      options: {
        select: [
          { label: '工作流表单', value: 'workflow' },
          { label: '节点表单', value: 'place' },
          { label: '关联对象表单', value: 'flowable' },
          { label: '用户关系条件', value: 'user' },
        ],
        multiple: false,
        span: 24,
        table_items: [],
        display_configurable_form: {},
      },
      key: 'radio_1650201726869_1',
      model_key: 'source_type',
      fields: [],
      conditions: [],
      model_key_prefix: '',
    },
    {
      name: '条件块',
      icon: 'FolderOutlined',
      type: 'condition',
      conditions: [
        {
          name: '节点表单',
          model_key: 'source_type',
          val: 'place',
          fields: [
            {
              name: '节点选择',
              icon: 'FolderOutlined',
              type: 'select',
              rules: [],
              model: { attr_type: 'string' },
              options: {
                select: placeSelect,
                multiple: false,
                span: 24,
                table_items: [],
                display_configurable_form: {},
              },
              key: 'select_1650202307610_4',
              model_key: 'source_id_or_seq',
              fields: [],
              conditions: [],
              model_key_prefix: '',
            },
          ],
          type: 'simple',
          opt: '==',
          complex_condition: { groups: [] },
        },
        {
          name: '关联对象表单',
          model_key: 'source_type',
          val: 'flowable',
          fields: [
            {
              name: 'model_setting Id',
              icon: 'FolderOutlined',
              type: 'api_single',
              rules: [],
              model: { attr_type: 'number' },
              options: {
                span: 24,
                multiple: false,
                attrs: ['flag_name'],
                table_items: [
                  { name: '名称', data_index: 'flag_name', search: true, type: 'string' },
                  { name: '标志', data_index: 'flag', search: true, type: 'string' },
                  {
                    name: '模型名',
                    data_index: 'model_define_name',
                    search: true,
                    type: 'string',
                  },
                  {
                    name: '模型英文名',
                    data_index: 'model_define_klass',
                    search: true,
                    type: 'string',
                  },
                ],
                display_configurable_form: {},
                path: '/com/user/model_settings',
              },
              key: 'api_single_1650201898444_3',
              model_key: 'source_id_or_seq',
              fields: [],
              conditions: [],
              model_key_prefix: '',
            },
          ],
          type: 'simple',
          opt: '==',
          complex_condition: { groups: [] },
        },
        {
          name: '用户关系条件',
          model_key: 'source_type',
          val: 'user',
          fields: [userConditionPartialItem],
          type: 'simple',
          opt: '==',
          complex_condition: { groups: [] },
        },
      ],
      options: { span: 24 },
      key: 'condition_1650201834286_2',
      model_key: 'condition_1650201834286_2',
      fields: [],
      model_key_prefix: '',
    },
  ],
  conditions: [],
  options: {
    label: {},
    disabled_actions: {},
    theme: 'none',
    create_text: '提交',
    update_text: '提交',
  },
  actions: [
    { key: 'create', enabled: true },
    { key: 'update', enabled: true },
    { key: 'delete', enabled: true },
    { key: 'import', enabled: true },
    { key: 'export', enabled: true },
  ],
});

export const userConditionPartialItem: TaTemplateFormItem = {
  key: 'layout_1670730837269_0',
  type: 'layout',
  model: { create_default_value: {} },
  fields: [
    {
      key: 'radio_1670730870165_2',
      name: '流程角色',
      type: 'radio',
      model: { attr_type: 'string' },
      rules: [],
      fields: [],
      options: {
        span: 24,
        select: [
          { label: '发起人', value: 'creator' },
          { label: '操作者', value: 'operator' },
        ],
        multiple: false,
        table_items: [],
        display_configurable_form: {},
      },
      model_key: 'user_mode',
      conditions: [],
      model_key_prefix: '',
    },
    {
      key: 'key_layout_1670730971782_4',
      name: 'user_rule',
      type: 'key_layout',
      fields: [
        {
          key: 'select_1670731185367_5',
          name: '类型',
          type: 'select',
          model: { attr_type: 'string' },
          rules: [],
          fields: [],
          options: {
            span: 24,
            select: [
              { label: '部门', value: 'department_in' },
              { label: '组织', value: 'org_in' },
              { label: '职务', value: 'duty_in' },
              { label: '角色', value: 'role_in' },
              { label: '部门包含', value: 'department_cont' },
              { label: '组织包含', value: 'org_cont' },
              { label: '职务包含', value: 'duty_cont' },
              { label: '角色包含', value: 'role_cont' },
              { label: '岗位等级', value: 'duty_rank' },
            ],
            multiple: false,
            table_items: [],
            display_configurable_form: {},
          },
          model_key: 'type',
          conditions: [],
          model_key_prefix: 'user_rule',
        },
        {
          key: 'condition_1670731403801_6',
          name: '条件块',
          type: 'condition',
          fields: [],
          options: { span: 24 },
          model_key: 'condition_1670731403801_6',
          conditions: [
            {
              opt: '==',
              val: 'department_in',
              name: 'department_in',
              type: 'simple',
              fields: [
                {
                  key: 'api_1670731622377_10',
                  name: '部门',
                  type: 'api',
                  model: { attr_type: 'array' },
                  rules: [],
                  fields: [],
                  options: {
                    path: '/res/admin/departments',
                    span: 24,
                    multiple: true,
                    table_items: [
                      {
                        name: '名称',
                        type: 'string',
                        search: true,
                        data_index: 'name',
                      },
                      {
                        name: '',
                        type: 'string',
                        search: true,
                        data_index: 'path_names',
                      },
                    ],
                    display_configurable_form: {},
                  },
                  model_key: 'source_ids',
                  conditions: [],
                  model_key_prefix: 'user_rule',
                },
              ],
              model_key: 'user_rule.type',
              complex_condition: { name: '', groups: [] },
            },
            {
              opt: '==',
              val: 'org_in',
              name: 'org_in',
              type: 'simple',
              fields: [
                {
                  key: 'api_1670731667344_11',
                  name: '组织',
                  type: 'api',
                  model: { attr_type: 'array' },
                  rules: [],
                  fields: [],
                  options: {
                    path: '/res/admin/orgs',
                    span: 24,
                    multiple: true,
                    table_items: [
                      {
                        name: '名称',
                        type: 'string',
                        search: true,
                        data_index: 'name',
                      },
                      {
                        name: '',
                        type: 'string',
                        search: false,
                        data_index: 'path_name',
                      },
                    ],
                    display_configurable_form: {},
                  },
                  model_key: 'source_ids',
                  conditions: [],
                  model_key_prefix: 'user_rule',
                },
              ],
              model_key: 'user_rule.type',
              complex_condition: { groups: [] },
            },
            {
              opt: '==',
              val: 'duty_in',
              name: 'duty_in',
              type: 'simple',
              fields: [
                {
                  key: 'api_1670731705294_12',
                  name: '职务',
                  type: 'api',
                  model: { attr_type: 'array' },
                  rules: [],
                  fields: [],
                  options: {
                    path: '/res/admin/duties',
                    span: 24,
                    multiple: true,
                    table_items: [
                      {
                        name: '名称',
                        type: 'string',
                        search: true,
                        data_index: 'name',
                      },
                    ],
                    display_configurable_form: {},
                  },
                  model_key: 'source_ids',
                  conditions: [],
                  model_key_prefix: 'user_rule',
                },
              ],
              model_key: 'user_rule.type',
              complex_condition: { groups: [] },
            },
            {
              opt: '==',
              val: 'role_in',
              name: 'role_in',
              type: 'simple',
              fields: [
                {
                  key: 'api_1670731734176_13',
                  name: '角色',
                  type: 'api',
                  model: { attr_type: 'array' },
                  rules: [],
                  fields: [],
                  options: {
                    path: '/permit/admin/roles',
                    span: 24,
                    multiple: true,
                    table_items: [
                      {
                        name: '名称',
                        type: 'string',
                        search: true,
                        data_index: 'name',
                      },
                      {
                        name: '标签名',
                        type: 'string',
                        search: true,
                        data_index: 'label',
                      },
                    ],
                    display_configurable_form: {},
                  },
                  model_key: 'source_ids',
                  conditions: [],
                  model_key_prefix: 'user_rule',
                },
              ],
              model_key: 'user_rule.type',
              complex_condition: { groups: [] },
            },
            {
              val: '',
              name: '条件5',
              type: 'complex',
              fields: [
                {
                  key: 'input_1670731768563_14',
                  name: '关键字',
                  type: 'input',
                  model: { attr_type: 'string' },
                  rules: [],
                  fields: [],
                  options: { span: 24 },
                  model_key: 'source_name',
                  conditions: [],
                  model_key_prefix: 'user_rule',
                },
              ],
              model_key: '',
              complex_condition: {
                name: '_cont',
                groups: [
                  {
                    _id: '1670731560129_4',
                    items: [
                      {
                        _id: '1670731560136_5',
                        desc: {
                          name: '类型',
                          optZh: '含有',
                          modelValue: {
                            rule: {
                              val: ['department_cont', 'org_cont', 'duty_cont', 'role_cont'],
                            },
                          },
                          template: {
                            key: 'key',
                            type: 'layout',
                            fields: [
                              {
                                key: 'ditto_1670731926609_17',
                                name: '值',
                                type: 'select',
                                model: { attr_type: 'array' },
                                rules: [],
                                fields: [],
                                options: {
                                  span: 24,
                                  ditto: 'ditto_single',
                                  dynamic_component: '',
                                  select: [
                                    { label: '部门', value: 'department_in' },
                                    { label: '组织', value: 'org_in' },
                                    { label: '职务', value: 'duty_in' },
                                    { label: '角色', value: 'role_in' },
                                    { label: '部门包含', value: 'department_cont' },
                                    { label: '组织包含', value: 'org_cont' },
                                    { label: '职务包含', value: 'duty_cont' },
                                    { label: '角色包含', value: 'role_cont' },
                                    { label: '岗位等级', value: 'duty_rank' },
                                  ],
                                  multiple: true,
                                  table_items: [],
                                  display_configurable_form: {},
                                  formDisabled: false,
                                  disabled: false,
                                  accessibility: 'writeable',
                                },
                                model_key: 'val',
                                conditions: [],
                                model_key_prefix: 'rule',
                              },
                            ],
                          },
                        },
                        rule: {
                          type: 'Com::Attr::ConditionRules::SingleChoice',
                          key: 'user_rule.type',
                          key_name: '类型',
                          opt: 'include',
                          val: ['department_cont', 'org_cont', 'duty_cont', 'role_cont'],
                        },
                      },
                    ],
                  },
                ],
              },
            },
            {
              opt: '==',
              val: 'duty_rank',
              name: 'duty_rank',
              type: 'simple',
              fields: [
                {
                  key: 'string_array_1670731783961_15',
                  name: '职级',
                  type: 'string_array',
                  model: { attr_type: 'array' },
                  rules: [],
                  fields: [],
                  options: { span: 24 },
                  model_key: 'ranks',
                  conditions: [],
                  model_key_prefix: 'user_rule',
                },
              ],
              model_key: 'user_rule.type',
              complex_condition: { groups: [{ items: [{ _id: '1670731585078_8' }] }] },
            },
          ],
          model_key_prefix: 'user_rule',
        },
      ],
      options: { span: 24 },
      model_key: 'user_rule',
      conditions: [],
      model_key_prefix: '',
    },
  ],
  actions: [
    { key: 'create', enabled: true },
    { key: 'update', enabled: true },
    { key: 'delete', enabled: true },
    { key: 'import', enabled: true },
    { key: 'export', enabled: true },
  ],
  options: {
    label: {},
    theme: { card: {}, background: {} },
    create_text: '提交',
    update_text: '提交',
    disabled_actions: {},
  },
  model_key: 'layout_1670730837269_0',
  conditions: [],
  column_attributes: [],
};
