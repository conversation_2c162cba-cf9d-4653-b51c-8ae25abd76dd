<script lang="ts">
import { defineComponent, ref, reactive, onMounted, nextTick, toRefs } from 'vue';

const TaImageEditor = defineComponent({
  name: 'TaImageEditor',
  components: {},
  props: {
    visible: { type: Boolean, default: false },
    value: { type: Array, default: () => [] },
  },
  emits: ['upload'],
  setup(props, { emit }) {
    const canvasRef = ref<HTMLCanvasElement | null>(null);
    const image = ref<HTMLImageElement | null>(null);
    const imageLoaded = ref(false);
    const dragging = ref(false);
    const dragStart = reactive({ x: 0, y: 0 });
    const imagePos = reactive({ x: 0, y: 0, scale: 1 });
    const cropRect = reactive({ x: 50, y: 50, w: 200, h: 200 });
    const resizing = ref(false);
    const resizeStart = reactive({ x: 0, y: 0 });
    const cropStart = reactive({ x: 0, y: 0, w: 0, h: 0 });
    const mode = ref<'move' | 'crop' | 'resize'>('move');

    // 载入初始图片
    onMounted(() => {
      if (props.value && props.value.length > 0 && (props.value[0] as any).url) {
        loadImage((props.value[0] as any).url);
      }
    });

    function loadImage(url: string) {
      const img = new window.Image();
      img.crossOrigin = 'Anonymous';
      img.onload = () => {
        image.value = img;
        imageLoaded.value = true;
        // 居中图片
        imagePos.x = (500 - img.width) / 2;
        imagePos.y = (400 - img.height) / 2;
        imagePos.scale = 1;
        draw();
      };
      img.src = url;
    }

    function onFileChange(e: Event) {
      const files = (e.target as HTMLInputElement | null)?.files;
      if (files && files[0]) {
        const reader = new FileReader();
        reader.onload = (ev: ProgressEvent<FileReader>) => {
          if (ev.target && typeof ev.target.result === 'string') {
            loadImage(ev.target.result);
          }
        };
        reader.readAsDataURL(files[0]);
      }
    }

    function draw() {
      nextTick(() => {
        const canvas = canvasRef.value;
        const ctx = canvas?.getContext('2d');
        if (!canvas || !ctx) return;
        ctx.clearRect(0, 0, canvas.width, canvas.height);
        if (image.value && imageLoaded.value) {
          ctx.save();
          ctx.translate(imagePos.x, imagePos.y);
          ctx.scale(imagePos.scale, imagePos.scale);
          ctx.drawImage(image.value, 0, 0);
          ctx.restore();
        }
        // 裁剪框
        ctx.save();
        ctx.strokeStyle = '#ccc';
        ctx.lineWidth = 2;
        ctx.strokeRect(cropRect.x, cropRect.y, cropRect.w, cropRect.h);
        ctx.restore();
      });
    }

    function getPointer(e: MouseEvent | TouchEvent) {
      if ('touches' in e) {
        const t = e.touches[0];
        return { x: t.clientX, y: t.clientY };
      } else {
        return { x: (e as MouseEvent).offsetX, y: (e as MouseEvent).offsetY };
      }
    }

    function onMouseDown(e: MouseEvent) {
      if (!imageLoaded.value) return;
      const { x, y } = getPointer(e);
      // 判断是否在裁剪框边缘
      if (
        x > cropRect.x + cropRect.w - 10 &&
        x < cropRect.x + cropRect.w + 10 &&
        y > cropRect.y + cropRect.h - 10 &&
        y < cropRect.y + cropRect.h + 10
      ) {
        mode.value = 'resize';
        resizing.value = true;
        resizeStart.x = x;
        resizeStart.y = y;
        cropStart.w = cropRect.w;
        cropStart.h = cropRect.h;
      } else if (
        x > cropRect.x &&
        x < cropRect.x + cropRect.w &&
        y > cropRect.y &&
        y < cropRect.y + cropRect.h
      ) {
        mode.value = 'crop';
        dragging.value = true;
        dragStart.x = x - cropRect.x;
        dragStart.y = y - cropRect.y;
      } else if (
        image.value &&
        x > imagePos.x &&
        x < imagePos.x + image.value.width * imagePos.scale &&
        y > imagePos.y &&
        y < imagePos.y + image.value.height * imagePos.scale
      ) {
        mode.value = 'move';
        dragging.value = true;
        dragStart.x = x - imagePos.x;
        dragStart.y = y - imagePos.y;
      }
    }

    function onMouseMove(e: MouseEvent) {
      if (!imageLoaded.value) return;
      const { x, y } = getPointer(e);
      if (mode.value === 'move' && dragging.value) {
        imagePos.x = x - dragStart.x;
        imagePos.y = y - dragStart.y;
        draw();
      } else if (mode.value === 'crop' && dragging.value) {
        cropRect.x = x - dragStart.x;
        cropRect.y = y - dragStart.y;
        draw();
      } else if (mode.value === 'resize' && resizing.value) {
        cropRect.w = Math.max(10, cropStart.w + (x - resizeStart.x));
        cropRect.h = Math.max(10, cropStart.h + (y - resizeStart.y));
        draw();
      }
    }

    function onMouseUp() {
      dragging.value = false;
      resizing.value = false;
    }

    function onWheel(e: WheelEvent) {
      if (!imageLoaded.value) return;
      e.preventDefault();
      const delta = e.deltaY > 0 ? -0.05 : 0.05;
      imagePos.scale = Math.max(0.1, imagePos.scale + delta);
      draw();
    }

    function onExport() {
      const canvas = document.createElement('canvas');
      canvas.width = cropRect.w;
      canvas.height = cropRect.h;
      const ctx = canvas.getContext('2d');
      if (!ctx || !image.value) return;
      // 计算图片在canvas上的偏移和缩放
      ctx.save();
      ctx.translate(-cropRect.x + imagePos.x, -cropRect.y + imagePos.y);
      ctx.scale(imagePos.scale, imagePos.scale);
      ctx.drawImage(image.value, 0, 0);
      ctx.restore();
      canvas.toBlob(blob => {
        if (blob) {
          const file = new File([blob], 'cropped.png', { type: 'image/png' });
          emit('upload', file);
        }
      }, 'image/png');
    }

    // 监听图片变化
    watchImage();
    function watchImage() {
      // 监听props.value变化
      // 这里只做一次初始化，后续图片由用户上传
    }

    // 监听canvas事件
    onMounted(() => {
      const canvas = canvasRef.value;
      if (canvas) {
        canvas.addEventListener('wheel', onWheel, { passive: false });
      }
    });

    return {
      ...toRefs(props),
      canvasRef,
      onFileChange,
      onMouseDown,
      onMouseMove,
      onMouseUp,
      onExport,
    };
  },
});
export default TaImageEditor;
</script>

<template lang="pug">
.ta-image-editor.flex.flex-col.items-center.justify-center.w-full
  .text-sm.text-gray-500.mb-2 拖动方框进行裁剪
  canvas(ref="canvasRef" width="500" height="400"
    style="border:1px solid #ccc;display:block;margin:10px 0;cursor:move;"
    @mousedown="onMouseDown"
    @mousemove="onMouseMove"
    @mouseup="onMouseUp"
    @mouseleave="onMouseUp"
  )
  .footer.w-full.my-4.items-center.justify-end.flex.border-t.border-gray-400.pt-4.px-4
    a-button.mt-2(@click="onExport" type="primary") 确定修改
</template>

<style lang="stylus" scoped>
.editor-container {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 10px;
}
</style>
