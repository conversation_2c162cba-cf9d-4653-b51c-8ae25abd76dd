import { VObject } from '@/lib/vails';
import dayjs from 'dayjs';
import { TaTemplateFormItem } from '../../ta-template-form-core/types';

export const getTaTemplateFormFieldMap = (item: TaTemplateFormItem): VObject => ({
  input: 'TaInput',
  input_phone_zh: 'TaZHPhoneNumber',
  input_bank_card_no: 'TaBankCardNo',
  input_password: 'TaPassword',
  input_email_no: 'TaEmailNo',
  input_zh_id_no: 'TaZHIDNo',
  color: 'TaColorInput',
  number: 'TaInputNumber',
  number_range: 'TaInputNumberRange',
  slider: 'TaSlider',
  password: 'TaInputPassword',
  percent: 'TaInputNumberPercent',
  textarea: 'TaTextarea',
  date: 'TaDatePicker',
  year: 'TaYearPicker',
  time: 'TaTimePicker',
  datetime: 'TaDateTimePicker',
  moment: 'TaTimePicker',
  date_range: 'TaDateRangeField',
  time_range: 'TaDateRangeField',
  moment_range: 'TaDateRangeField',
  date_range_new: 'TaDateRangeNewField',
  time_range_new: 'TaDateRangeNewField',
  moment_range_new: 'TaDateRangeNewField',
  radio: 'TaRadioGroup',
  checkbox: 'TaCheckGroup',
  select: 'TaSelect',
  switch: 'TaSwitch',
  string_array: 'TaTagField',
  form_designer: 'TaTemplateFormDesignerDialog',
  form_setting_designer: 'TaFormSettingDesignerDialog',
  duty_group: 'TaDutyGroupField',
  forms_template: 'TaFormsTemplateField',
  user: 'TaUserField',
  single_user: 'TaSingleUserField',
  single_member: 'TaSingleMemberField',
  tree: 'TaTreeSelect',
  file: 'TaFileUploader',
  image_single: 'TaImageSingleUploader',
  image_single_new: 'TaImageSingleUploaderNew',
  image_picker: 'TaImagePicker',
  video_single: 'TaVideoSingleUploader',
  video_single_new: 'TaImageSingleUploaderNew',
  image: 'TaImageUploader',
  api: 'TaApiField',
  api_single: 'TaApiSingleField',
  api_store: 'TaApiStoreField',
  api_single_store: 'TaApiSingleStoreField',
  fixed: 'TaFixedValueField',
  nested_attributes_polymorphic: 'TaNestedAttributesPolymorphicField',
  user_polymorphic: 'TaUserPolymorphicField',
  res_permit_polymorphic: 'TaResPermitPolymorphicField',
  user_type: 'TaUserTypeField',
  dynamic_component: item.options?.dynamic_component,
  computed: 'TaComputedField',
  map: 'TaMapField',
  json: 'TaJsonEditor',
  region: 'TaRegionField',
  region_id: 'TaRegionIdField',
  evaluate: 'TaEvaluateField',
  content: 'TaContentField',
  rich_text: 'TaRichEditor',
  nested_attributes: 'TaNestedAttributesField',
  scan_business_card: 'TaScan',
  scan_id_card: 'TaScan',
  scan_business_license: 'TaScan',
  notice_task_user_selector: 'ComNoticeTaskUserSelector',
});

export const getTaTemplateFormFieldExtraPropsMap = (item: TaTemplateFormItem): VObject => ({
  number: {
    precision: item.options?.precision,
  },
  number_range: {
    precision: item.options?.precision,
  },
  percent: {
    precision: item.options?.precision,
  },
  date_range: {
    format: 'YYYY-MM-DD',
  },
  time_range: {
    format: 'YYYY-MM-DD HH:mm',
    showTime: true,
  },
  date_range_new: {
    format: 'YYYY-MM-DD',
  },
  time_range_new: {
    format: 'YYYY-MM-DD HH:mm:ss',
    showTime: true,
  },
  moment_range_new: {
    format: 'HH:mm:ss',
    showTime: true,
    widgetType: 'time',
  },
  radio: {
    path: item.options?.path,
    attrs: item.options?.attrs || ['name'],
    ransackStr: item.options?.ransack || '{}',
  },
  checkbox: {
    path: item.options?.path,
    attrs: item.options?.attrs || ['name'],
    ransackStr: item.options?.ransack || '{}',
    multiple: item.options?.multiple,
  },
  select: {
    path: item.options?.path,
    attrs: item.options?.attrs || ['name'],
    ransackStr: item.options?.ransack || '{}',
    multiple: item.options?.multiple,
  },
  tree: {
    treeData: item.options?.treeData,
  },
  file: {
    options: { ...item.options, fileSize: item.options?.fileSize || 30 },
    accept: item.options?.accept,
    multiple: item.options?.multiple,
  },
  image_single: {
    options: {
      ...item.options,
      fileSize: item.options?.fileSize || 30,
      size: item.options?.size || 'regular',
    },
    aspectRatio: item.options?.aspect_ratio,
    presetFiles: item.options?.preset_files,
  },
  image_single_new: {
    options: { ...item.options, fileSize: item.options?.fileSize || 3 },
    aspectRatio: item.options?.aspect_ratio,
    presetFiles: item.options?.preset_files,
  },
  image_picker: {
    width: item.options?.width,
    height: item.options?.height,
    presetFiles: item.options?.preset_files,
  },
  video_single: {
    options: {
      ...item.options,
      fileSize: item.options?.fileSize || 30,
      size: item.options?.size || 'regular',
    },
    aspectRatio: item.options?.aspect_ratio,
  },
  video_single_new: {
    options: {
      ...item.options,
      fileSize: item.options?.fileSize || 30,
      size: item.options?.size || 'regular',
    },
    aspectRatio: item.options?.aspect_ratio,
    presetFiles: item.options?.preset_files,
    accept: 'video/*',
    mimeTypeZh: '视频',
    mimeType: 'video',
    includeType: ['video'],
  },
  image: {
    options: { ...item.options, fileSize: item.options?.fileSize || 30 },
  },
  api: getApiFieldExtraProps(item),
  api_single: getApiFieldExtraProps(item),
  api_store: getApiFieldExtraProps(item),
  api_single_store: getApiFieldExtraProps(item),
  nested_attributes_polymorphic: {
    polymorphicKey: item.options?.polymorphic_key,
    multiple: item.options?.multiple,
  },
  user_polymorphic: {
    paths: item.options?.paths,
    polymorphicKey: item.options?.polymorphic_key,
    multiple: item.options?.multiple,
  },
  res_permit_polymorphic: {
    paths: item.options?.paths,
    polymorphicKey: item.options?.polymorphic_key,
    multiple: item.options?.multiple,
  },
  user_type: {
    polymorphicKey: item.options?.polymorphic_key,
  },
  region_id: {
    depth: item.options?.depth,
  },
  evaluate: {
    icon: item.options?.evaluate_icon,
    step: item.options?.step,
    length: item.options?.length,
    color: item.options?.color,
  },
  content: {
    useNormalText: item.options?.use_normal_text,
    hideVideo: item.options?.hide_video,
    hideImage: item.options?.hide_image,
    hideText: item.options?.hide_text,
    hideAudio: item.options?.hide_audio,
    hideFile: item.options?.hide_file,
    imageFileSize: item.options?.image_fileSize,
    videoFileSize: item.options?.video_fileSize,
    audioFileSize: item.options?.audio_fileSize,
    fileFileSize: item.options?.file_fileSize,
  },
  nested_attributes: {
    needLabel: false,
  },
  scan_business_card: {
    mode: 'business_card',
  },
  scan_id_card: {
    mode: 'id_card',
  },
  scan_business_license: {
    mode: 'business_license',
  },
  notice_task_user_selector: {
    // ComNoticeTaskUserSelector组件的额外props
  },
});

const getApiFieldExtraProps = (item: TaTemplateFormItem) => ({
  callback: item.options?.callback,
  path: item.options?.path,
  attrs: item.options?.attrs || ['name'],
  recordName: item.name,
  multiple: item.options?.multiple,
  tableItems: item.options?.table_items,
  ransackStr: item.options?.ransack || '{}',
  useTree: item.options?.use_tree,
  disable_tree_parent: item.options?.disable_tree_parent,
  display: item.options?.display,
  displayConfigurableForm: item.options?.display_configurable_form,
  importExportHeaders: item.options?.import_export_headers,
});

export const taTemplateFormFieldValueTypeMap: VObject = {
  input: 'string',
  input_phone_zh: 'string',
  input_bank_card_no: 'string',
  input_password: 'string',
  input_email_no: 'string',
  input_zh_id_no: 'string',
  color: 'component',
  number: 'number',
  number_range: 'component',
  slider: 'component',
  password: 'string',
  percent: 'component',
  textarea: 'string',
  date: (value: any) => (dayjs(value).isValid() ? dayjs(value).format('YYYY-MM-DD') : null),
  time: (value: any) => (dayjs(value).isValid() ? dayjs(value).format('HH:mm') : null),
  datetime: (value: any) =>
    dayjs(value).isValid() ? dayjs(value).format('YYYY-MM-DD HH:mm') : null,
  moment: (value: any) => (dayjs(value).isValid() ? dayjs(value).format('HH:mm') : null),
  year: (value: any) => (dayjs(value).isValid() ? dayjs(value).format('YYYY') : null),
  date_range: 'string',
  time_range: 'string',
  moment_range: 'string',
  date_range_new: 'string',
  time_range_new: 'string',
  moment_range_new: 'string',
  radio: 'component',
  checkbox: 'component',
  select: 'component',
  switch: (value: any) => (value === true ? '是' : value == false ? '否' : ''),
  string_array: (value: any) => (Array.isArray(value) ? value.join(',') : ''),
  form_designer: 'component',
  form_setting_designer: 'component',
  duty_group: 'component',
  forms_template: 'component',
  user: 'component',
  single_user: 'component',
  single_member: 'component',
  tree: 'component',
  file: 'file',
  image_single: 'file',
  image_single_new: 'file',
  image_picker: 'file',
  video_single: 'file',
  video_single_new: 'file',
  image: 'file',
  api: 'component',
  api_single: 'component',
  api_store: 'component',
  api_single_store: 'component',
  fixed: 'string',
  nested_attributes_polymorphic: 'component',
  user_polymorphic: 'component',
  res_permit_polymorphic: 'component',
  user_type: 'component',
  dynamic_component: 'component',
  computed: 'component',
  map: 'component',
  json: 'component',
  region: 'component',
  region_id: 'component',
  evaluate: 'component',
  content: 'component',
  rich_text: 'component',
  nested_attributes: 'component',
  scan_business_card: 'component',
  scan_id_card: 'component',
  scan_business_license: 'component',
  notice_task_user_selector: 'component',
};
