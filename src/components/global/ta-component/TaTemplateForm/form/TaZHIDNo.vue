<script lang="ts">
import {
  FormItemSize,
  TaTemplateFormItem,
} from '@/components/global/ta-component/ta-template-form-core/types';
import { PropType, computed, defineComponent, nextTick, ref, toRefs } from 'vue';

const TaZHIDNo = defineComponent({
  name: 'TaZHIDNo',
  components: {},
  props: {
    item: { type: Object as PropType<TaTemplateFormItem>, default: () => ({ options: {} }) },
    value: { type: String, default: () => undefined },
    disabled: { type: Boolean, default: false },
    defaultValue: { type: String, default: '' },
    size: { type: String as PropType<FormItemSize>, default: () => 'regular' },
  },
  emits: ['update:value', 'syncValue'],
  setup(props, { emit }) {
    const localValue = computed({
      get: () => props.value,
      set: val => {
        const value = Array.isArray(val) ? val[0] : val;
        emit('update:value', value);
      },
    });

    const finalSize = computed(() => {
      if (props.item?.options?.size) {
        return props.item.options.size;
      }
      return props.size;
    });

    const calPadding = computed(() => {
      switch (finalSize.value) {
        case 'regular':
          return '10px 16px';
        case 'small':
          return '6px 16px';
        case 'large':
          return '14px 16px';
        default:
          return '10px 16px';
      }
    });

    const isHref = computed(() => {
      const value = localValue.value || props.defaultValue;
      return value && typeof value === 'string' && value.trim().startsWith('http');
    });

    const onBlur = () => {
      nextTick(() => {
        emit('syncValue', props.item, localValue.value);
      });
    };

    // 组件验证状态
    // 通过flag 改变css 的部分样式 【仅有部分组件需要 如input类型类的，需要改变边框 css样式型的】
    const flag = ref(true);
    const validate = async () => {
      if (props.value && props.value.trim()) {
        // 15位身份证：6位地区码 + 6位出生日期(YYMMDD) + 3位顺序码
        // 18位身份证：6位地区码 + 8位出生日期(YYYYMMDD) + 3位顺序码 + 1位校验码
        const reg15 = /^\d{6}\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}$/;
        const reg18 = /^\d{6}(18|19|20|21)\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\d|3[01])\d{3}[\dX]$/;

        const idNumber = localValue.value as string;
        const res = idNumber.length === 15 ? reg15.test(idNumber) : reg18.test(idNumber);
        if (!res) {
          flag.value = false;
          return Promise.reject('请输入正确的身份证号');
        }
      }
      const needRequired = props.item?.rules?.some((el: any) => el.required);
      if (needRequired) {
        if (props.value && props.value.trim()) {
          return Promise.resolve();
        } else {
          flag.value = false;
          return Promise.reject(`请输入${props.item?.name}`);
        }
      } else {
        flag.value = true;
        return Promise.resolve();
      }
    };

    const phoneChange = (e: Event) => {
      let value = (e.target as HTMLInputElement).value.trim().toUpperCase();
      // 只允许数字和X，且X只能在最后一位
      value = value.replace(/[^\dX]/g, '');
      // 如果有X，确保只有最后一位可以是X
      if (value.includes('X')) {
        const xIndex = value.indexOf('X');
        if (xIndex !== value.length - 1) {
          value = value.replace(/X/g, '');
        }
      }
      localValue.value = value;
      flag.value = true;
    };

    return {
      ...toRefs(props),
      localValue,
      isHref,
      onBlur,
      finalSize,
      calPadding,
      validate,
      flag,
      phoneChange,
    };
  },
});
export default TaZHIDNo;
</script>

<template lang="pug">
.ta-input(:class="{'warn-state': !flag}")
  template(v-if='!disabled')
    a-input(
      :value='localValue',
      :defaultValue='defaultValue',
      :addonAfter='item.options?.unit',
      :placeholder='item.options?.placeholder || `请输入${item.name || ""}`',
      @input='phoneChange',
      :maxlength='18'
      @blur='onBlur'
      allowClear
      :style="{'--inputPadding': calPadding}"
    )
      template(v-if='item.options?.prefixIcon' #prefix)
        TaIcon(:type="item.options?.prefixIcon")

  template(v-else)
    .value(v-if='isHref')
      a(:href='localValue || defaultValue',  target="_blank") {{ localValue || defaultValue }}
    .value(v-else) {{ localValue || defaultValue }}
</template>

<style lang="stylus" scoped>
.value
  word-break break-all
.ta-input
  >>> .ant-input
    background #F9FAFB !important
    border 1px solid #D1D5DB;
    border-radius 8px
    padding 0 !important
    color #111928
  >>> .ant-input::placeholder
    color: #6B7280
    font-size 14px
    font-weight 400
  >>> .ant-input-affix-wrapper
    background #F9FAFB !important
    padding var(--inputPadding) !important
    border-radius 8px
.warn-state
  >>> .ant-input
    background #FDF2F2 !important
  >>> .ant-input
    color: #E02424
  >>> .ant-input::placeholder
    color: #E02424
  >>> .ant-input-affix-wrapper
    background #FDF2F2 !important
</style>
