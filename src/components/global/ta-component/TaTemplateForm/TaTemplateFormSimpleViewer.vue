<script lang="ts">
import { VModel } from '@/lib/vails';
import { defineComponent, toRefs, PropType, provide, computed, onMounted } from 'vue';
import { TaTemplateFormItem } from '../ta-template-form-core/types';
import useAutoTemplateForm from '../ta-template-form-core/useAutoTemplateForm';
import { useContextProvide } from '../ta-template-form-core/useContext';
import { useRoute } from 'vue-router';
import { merge } from 'lodash';
import useProcessFields from '../ta-template-form-core/useProcessFields';

const TaTemplateFormSimpleViewer = defineComponent({
  name: 'TaTemplateFormSimpleViewer',
  components: {},
  props: {
    template: {
      type: [Object, String] as PropType<TaTemplateFormItem | string>,
      required: true,
    },
    record: { type: Object as PropType<VModel>, default: () => ({}) },
    modelValue: { type: Object, default: undefined },
    hideEmpty: { type: Boolean, default: false },
    context: { type: Object, default: () => ({}) }, // 上下文
  },
  setup(props) {
    provide('isViewer', true);

    onMounted(() => {
      fetchTemplate();
    });

    const { localTemplate, fetchTemplate } = useAutoTemplateForm(props);

    const value = computed(() =>
      typeof props.modelValue !== 'undefined' ? props.modelValue : props.record.formData,
    );

    const route = useRoute();
    const contextRef = computed(() => merge({}, value.value, props.context));
    useContextProvide(contextRef, route);

    const { getFormPayloadInfo } = useProcessFields();

    const formPayloadInfo = computed(() => {
      return getFormPayloadInfo(localTemplate.value, contextRef.value, value.value);
    });

    return {
      ...toRefs(props),
      formPayloadInfo,
    };
  },
});
export default TaTemplateFormSimpleViewer;
</script>

<template lang="pug">
.ta-template-form-simple-viewer
  a-row.w-full
    TaTemplateFormSimpleViewerRender(
      v-for="(item, index) in formPayloadInfo"
      :key="index"
      :infoItem="item"
    )
</template>

<style lang="stylus" scoped></style>
