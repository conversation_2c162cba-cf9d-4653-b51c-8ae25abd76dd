.vdr-container {
  position: absolute;
  box-sizing: border-box;
  border: 1px solid transparent;
}

.vdr-container.active {
  border-color: #000;
  border-style: dashed;
}

.vdr-container.dragging {
  border-color: #000;
  border-style: solid;
}

.vdr-handle {
  position: absolute;
  box-sizing: border-box;
  width: 7px;
  height: 7px;
  background: #f0f0f0;
  border: 1px solid #333;
}

.vdr-handle-tl {
  top: -4px;
  left: -4px;
  cursor: nw-resize;
}

.vdr-handle-tm {
  top: -4px;
  left: 50%;
  margin-left: -3px;
  cursor: n-resize;
}

.vdr-handle-tr {
  top: -4px;
  right: -4px;
  cursor: ne-resize;
}

.vdr-handle-ml {
  top: 50%;
  left: -4px;
  margin-top: -3px;
  cursor: w-resize;
}

.vdr-handle-mr {
  top: 50%;
  right: -4px;
  margin-top: -3px;
  cursor: e-resize;
}

.vdr-handle-bl {
  bottom: -4px;
  left: -4px;
  cursor: sw-resize;
}

.vdr-handle-bm {
  bottom: -4px;
  left: 50%;
  margin-left: -4px;
  cursor: s-resize;
}

.vdr-handle-br {
  right: -4px;
  bottom: -4px;
  cursor: se-resize;
}
