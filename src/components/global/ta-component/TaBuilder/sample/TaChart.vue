<script lang="ts">
import { defineComponent, toRefs } from 'vue';

const TaChart = defineComponent({
  name: '<PERSON><PERSON><PERSON>',
  components: {},
  props: {
    config: { type: Object, default: () => ({}) },
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
export default TaChart;
</script>

<template lang="pug">
.ta-chart
</template>

<style lang="stylus" scoped>
// .ta-chart
</style>
