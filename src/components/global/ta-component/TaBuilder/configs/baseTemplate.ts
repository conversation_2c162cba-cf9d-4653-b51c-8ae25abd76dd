export const baseTemplate = {
  type: 'layout',
  model: {},
  key: 'layout_1645702654541_0',
  model_key: 'layout_1645702654541_0',
  fields: [
    {
      name: '名称',
      icon: 'FolderOutlined',
      type: 'input',
      rules: [],
      model: { attr_type: 'string' },
      options: { span: 24 },
      key: 'input_1645702678020_1',
      model_key: 'name',
      fields: [],
      conditions: [],
      model_key_prefix: '',
    },
    {
      name: 'cssc',
      icon: 'FolderOutlined',
      type: 'key_layout',
      options: { span: 24 },
      key: 'key_layout_1645702694352_2',
      model_key: 'cssc',
      fields: [
        {
          name: 'x',
          icon: 'FolderOutlined',
          type: 'number',
          rules: [],
          model: { attr_type: 'number' },
          options: { span: 12 },
          key: 'number_1645702718206_6',
          model_key: 'x',
          fields: [],
          conditions: [],
          model_key_prefix: 'cssc',
        },
        {
          name: 'y',
          icon: 'FolderOutlined',
          type: 'number',
          rules: [],
          model: { attr_type: 'number' },
          options: { span: 12 },
          key: 'number_1645702713306_4',
          model_key: 'y',
          fields: [],
          conditions: [],
          model_key_prefix: 'cssc',
        },
        {
          name: 'w',
          icon: 'FolderOutlined',
          type: 'number',
          rules: [],
          model: { attr_type: 'number' },
          options: { span: 12 },
          key: 'number_1645702711391_3',
          model_key: 'w',
          fields: [],
          conditions: [],
          model_key_prefix: 'cssc',
        },
        {
          name: 'h',
          icon: 'FolderOutlined',
          type: 'number',
          rules: [],
          model: { attr_type: 'number' },
          options: { span: 12 },
          key: 'number_1645702716540_5',
          model_key: 'h',
          fields: [],
          conditions: [],
          model_key_prefix: 'cssc',
        },
        {
          name: 'z',
          icon: 'FolderOutlined',
          type: 'number',
          rules: [],
          model: { attr_type: 'number' },
          options: { span: 12 },
          key: 'number_1645702762739_7',
          model_key: 'zIndex',
          fields: [],
          conditions: [],
          model_key_prefix: 'cssc',
        },
      ],
      conditions: [],
      model_key_prefix: '',
    },
    {
      name: 'css',
      icon: 'FolderOutlined',
      type: 'textarea',
      rules: [],
      model: { attr_type: 'string' },
      options: {
        span: 24,
        defaultValue: `:root {
}
      `,
      },
      key: 'textarea_1645702718206_6',
      model_key: 'css',
      fields: [],
      conditions: [],
      model_key_prefix: '',
    },
    {
      name: 'click',
      icon: 'FolderOutlined',
      type: 'input',
      rules: [],
      model: { attr_type: 'string' },
      options: {
        span: 24,
      },
      key: 'textarea_1645702718206_6',
      model_key: 'click',
      fields: [],
      conditions: [],
      model_key_prefix: '',
    },
  ],
  conditions: [],
  options: {
    label: {},
    disabled_actions: {},
    theme: 'none',
    create_text: '提交',
    update_text: '提交',
    layout: 'horizontal',
  },
  actions: [
    { key: 'create', enabled: true },
    { key: 'update', enabled: true },
    { key: 'delete', enabled: true },
    { key: 'import', enabled: true },
    { key: 'export', enabled: true },
  ],
};
