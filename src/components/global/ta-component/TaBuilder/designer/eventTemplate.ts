import { TaTemplateFormSelect } from '@/components/global/ta-component/ta-template-form-core/types';

export const eventTemplateFn = (dataSourceSelect: TaTemplateFormSelect[]) => {
  return {
    type: 'layout',
    model: {},
    key: 'layout_1662092557339_0',
    name: '事件',
    model_key: 'layout_1662092557339_0',
    fields: [
      {
        name: '',
        icon: 'FolderOutlined',
        type: 'list',
        options: { span: 24 },
        key: 'key_layout_1662101456380_7',
        model_key: 'events',
        fields: [
          {
            name: '模式',
            icon: 'FolderOutlined',
            type: 'radio',
            rules: [],
            model: { attr_type: 'string' },
            options: {
              select: [
                // { label: '无', value: 'none' },
                { label: '大屏弹窗', value: 'page_setting' },
                { label: '跳转链接', value: 'href' },
                { label: '内部链接', value: 'route' },
                { label: '改变值', value: 'change_value' },
                { label: '关闭弹窗', value: 'close' },
                { label: '文件预览', value: 'file' },
              ],
              multiple: false,
              span: 24,
              table_items: [],
              display_configurable_form: {},
              import_export_headers: [{ _id: '1662092582911_0' }],
              defaultValue: 'none',
            },
            key: 'radio_1662092581855_1',
            model_key: 'mode',
            fields: [],
            conditions: [],
            model_key_prefix: '',
          },
          {
            name: '条件块',
            icon: 'FolderOutlined',
            type: 'condition',
            conditions: [
              {
                name: '大屏弹窗',
                model_key: 'mode',
                val: 'page_setting',
                fields: [
                  {
                    name: '大屏(ta index view record 中 page_setting_id)',
                    icon: 'FolderOutlined',
                    type: 'api_single',
                    rules: [],
                    model: { attr_type: 'number' },
                    options: {
                      span: 24,
                      multiple: false,
                      table_items: [
                        { name: 'ID', data_index: 'id', search: false, type: 'string' },
                        { name: '名称', data_index: 'name', search: true, type: 'string' },
                      ],
                      display_configurable_form: {},
                      import_export_headers: [{ _id: '1662100379459_1' }],
                      path: '/com/admin/page_settings',
                    },
                    key: 'api_single_1662100377747_3',
                    model_key: 'page_setting_id',
                    fields: [],
                    conditions: [],
                    model_key_prefix: '',
                  },
                  {
                    name: '限制大小',
                    icon: 'FolderOutlined',
                    type: 'select',
                    rules: [],
                    model: {
                      attr_type: 'string',
                    },
                    options: {
                      select: [
                        {
                          label: '宽',
                          value: 'width',
                        },
                        {
                          label: '高',
                          value: 'height',
                        },
                      ],
                      multiple: false,
                      span: 8,
                      table_items: [],
                      display_configurable_form: {},
                      import_export_headers: [
                        {
                          _id: '1662112561832_1',
                        },
                      ],
                    },
                    key: 'select_1662112560683_3',
                    model_key: 'limit_size_attribute',
                    fields: [],
                    conditions: [],
                    model_key_prefix: '',
                  },
                  {
                    name: '最大等于',
                    icon: 'FolderOutlined',
                    type: 'number',
                    rules: [],
                    model: {
                      attr_type: 'number',
                    },
                    options: {
                      span: 16,
                    },
                    key: 'number_1662112505588_2',
                    model_key: 'limit_size_value',
                    fields: [],
                    conditions: [],
                    model_key_prefix: '',
                  },
                  {
                    name: '弹窗上边距',
                    icon: 'FolderOutlined',
                    type: 'input',
                    rules: [],
                    model: {
                      attr_type: 'string',
                    },
                    options: {
                      span: 24,
                    },
                    key: 'string_1662112505588_2',
                    model_key: 'offset_top',
                    fields: [],
                    conditions: [],
                    model_key_prefix: '',
                  },
                  {
                    name: '数据源',
                    icon: 'FolderOutlined',
                    type: 'select',
                    rules: [],
                    model: {
                      attr_type: 'string',
                    },
                    options: {
                      select: dataSourceSelect,
                      multiple: false,
                      span: 24,
                      table_items: [],
                      display_configurable_form: {},
                      import_export_headers: [
                        {
                          _id: '1676019019626_0',
                        },
                      ],
                    },
                    key: 'select_1676019019510_2',
                    model_key: 'dataSource',
                    fields: [],
                    conditions: [],
                    model_key_prefix: '',
                  },
                  {
                    name: '数据关键字',
                    icon: 'FolderOutlined',
                    type: 'input',
                    rules: [],
                    model: {
                      attr_type: 'string',
                    },
                    options: {
                      span: 24,
                    },
                    key: 'input_1676018973597_1',
                    model_key: 'dataKey',
                    fields: [],
                    conditions: [],
                    model_key_prefix: '',
                  },
                  {
                    name: '数据静态值',
                    icon: 'FolderOutlined',
                    type: 'json',
                    rules: [],
                    model: {
                      attr_type: 'object',
                    },
                    options: {
                      span: 24,
                    },
                    key: 'json_1676019022575_3',
                    model_key: 'dataStatic',
                    fields: [],
                    conditions: [],
                    model_key_prefix: '',
                  },
                ],
                type: 'simple',
                complex_condition: { groups: [{ items: [] }] },
                opt: '==',
              },
              {
                name: '页面跳转',
                type: 'simple',
                model_key: 'mode',
                val: 'href',
                fields: [
                  {
                    name: '链接(ta index view record 中 url)',
                    icon: 'FolderOutlined',
                    type: 'input',
                    rules: [],
                    model: { attr_type: 'string' },
                    options: { span: 24 },
                    key: 'input_1662101305876_4',
                    model_key: 'url',
                    fields: [],
                    conditions: [],
                    model_key_prefix: '',
                  },
                  {
                    name: '当前页打开',
                    icon: 'FolderOutlined',
                    type: 'switch',
                    rules: [],
                    model: { attr_type: 'string' },
                    options: { span: 24 },
                    key: 'switch_1662101305876_4',
                    model_key: 'location_href',
                    fields: [],
                    conditions: [],
                    model_key_prefix: '',
                  },
                ],
                complex_condition: { groups: [] },
                opt: '==',
              },
              {
                name: '内部页面',
                type: 'simple',
                model_key: 'mode',
                val: 'route',
                fields: [
                  {
                    name: '链接(ta index view record 中 url)',
                    icon: 'FolderOutlined',
                    type: 'input',
                    rules: [],
                    model: { attr_type: 'string' },
                    options: { span: 24 },
                    key: 'input_1662101305876_4',
                    model_key: 'url',
                    fields: [],
                    conditions: [],
                    model_key_prefix: '',
                  },
                ],
                complex_condition: { groups: [] },
                opt: '==',
              },
              {
                name: '改变值',
                type: 'simple',
                model_key: 'mode',
                val: 'change_value',
                fields: [
                  {
                    name: '关键字',
                    icon: 'FolderOutlined',
                    type: 'input',
                    rules: [],
                    model: { attr_type: 'string' },
                    options: { span: 24 },
                    key: 'input_1662101321226_5',
                    model_key: 'key',
                    fields: [],
                    conditions: [],
                    model_key_prefix: '',
                  },
                  {
                    name: '值',
                    icon: 'FolderOutlined',
                    type: 'json',
                    rules: [],
                    model: { attr_type: 'object' },
                    options: { span: 24 },
                    key: 'json_1662101356793_6',
                    model_key: 'value',
                    fields: [],
                    conditions: [],
                    model_key_prefix: '',
                  },
                ],
                complex_condition: { groups: [] },
                opt: '==',
              },
              {
                name: '附件预览',
                type: 'simple',
                model_key: 'mode',
                val: 'file',
                fields: [
                  {
                    name: '附件(ta index view record 中 attachments)',
                    icon: 'FolderOutlined',
                    type: 'file',
                    rules: [],
                    model: { attr_type: 'array' },
                    options: { span: 24 },
                    key: 'file_1662101305876_4',
                    model_key: 'attachments',
                    fields: [],
                    conditions: [],
                    model_key_prefix: '',
                  },
                ],
                complex_condition: { groups: [] },
                opt: '==',
              },
            ],
            options: { span: 24 },
            key: 'condition_1662092671574_2',
            model_key: 'condition_1662092671574_2',
            fields: [],
            model_key_prefix: '',
          },
        ],
        conditions: [],
        model_key_prefix: '',
      },
    ],
    conditions: [],
    options: {
      label: {
        hidden: false,
        width: 100,
      },
      disabled_actions: {},
      theme: 'none',
      create_text: '提交',
      update_text: '提交',
      collapse: true,
      layout: 'horizontal',
    },
    actions: [
      { key: 'create', enabled: true },
      { key: 'update', enabled: true },
      { key: 'delete', enabled: true },
      { key: 'import', enabled: true },
      { key: 'export', enabled: true },
    ],
  };
};
