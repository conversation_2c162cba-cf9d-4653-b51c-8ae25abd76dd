<script lang="ts">
import { computed, defineComponent, toRefs } from 'vue';

const ComNoticeTaskUserSelector = defineComponent({
  name: 'ComNoticeTaskUserSelector',
  components: {},
  props: {
    value: { type: Array, default: () => [] },
    payload: { type: Object, default: () => ({}) },
    disabled: { type: Boolean, default: false },
  },
  setup(props, { emit }) {
    const localValue = computed({
      get: () => props.value,
      set: val => {
        emit('update:value', val);
      },
    });

    const localPayload = computed({
      get: () => props.payload,
      set: val => {
        emit('update:payload', val);
      },
    });

    if (!localPayload.value.payload) {
      localPayload.value.payload = {};
    }

    const userTableItems = [
      {
        name: '姓名',
        type: 'string',
        search: true,
        data_index: 'name',
      },
      {
        name: '账号',
        type: 'string',
        search: true,
        data_index: 'account',
      },
      {
        name: '岗位',
        type: 'string',
        data_index: 'member_pos_job',
      },
      {
        name: '部门',
        type: 'string',
        data_index: 'department_names',
      },
    ];

    const tableItems = [
      {
        name: '名称',
        type: 'string',
        search: true,
        data_index: 'name',
      },
      {
        name: '',
        type: 'string',
        data_index: 'path_names',
      },
    ];

    const tagTableItems = [
      {
        name: '名称',
        type: 'string',
        search: true,
        data_index: 'name',
      },
    ];

    const ransack = computed(() => {
      return {
        res_tags_id_in: localPayload.value.payload?.res_tag_ids || [],
        orgs_id_in: localPayload.value.payload?.org_ids || [],
        departments_id_in: localPayload.value.payload?.department_ids || [],
      };
    });

    return {
      ...toRefs(props),
      localValue,
      tableItems,
      tagTableItems,
      userTableItems,
      ransack,
      localPayload,
    };
  },
});
export default ComNoticeTaskUserSelector;
</script>

<template lang="pug">
.com-notice-task-user-selector
  .flex.flex-col(v-if='localPayload.payload')
    TaApiField.field(
      recordName='过滤部门',
      v-model:value='localPayload.payload.org_ids',
      path='/res/member/orgs',
      :multiple='true',
      :disabled='true',
      :tableItems='tableItems'
    )
      template(#tags='{ records }')
        .tag-display.flex.items-center.flex-wrap.gap-2.mr-2.mb-2
          template(v-for='tag in records', :key='tag.id')
            ComTag(:text='tag.name', title='组织')
    TaApiField.field(
      recordName='过滤标签',
      v-model:value='localPayload.payload.res_tag_ids',
      path='/res/member/tags',
      :multiple='true',
      :disabled='true',
      :tableItems='tagTableItems'
    )
      template(#tags='{ records }')
        .tag-display.flex.items-center.flex-wrap.gap-2
          template(v-for='tag in records', :key='tag.id')
            ComTag(:text='tag.name', title='标签')

  TaApiField(
    v-model:value='localValue',
    recordName='接收人',
    display='table',
    path='/res/member/users',
    :tableItems='userTableItems',
    :attrs='["name", "account", "mobile", "member_pos_job", "department_names"]',
    :multiple='true',
    :disabled='disabled',
    :ransackStr='JSON.stringify(ransack)'
  )
    template(#modal-header-left)
      div
        .flex.items-center(v-if='localPayload.payload')
          .text-gray-900.text-sm.mb-2.font-medium 过滤区划：
          TaApiField.field(
            recordName='过滤区划',
            v-model:value='localPayload.payload.org_ids',
            path='/res/member/orgs',
            :multiple='true',
            :tableItems='tableItems'
          )
        .flex.items-center(v-if='localPayload.payload')
          .text-gray-900.text-sm.mb-2.font-medium 过滤部门：
          TaApiField.field(
            recordName='过滤部门',
            v-model:value='localPayload.payload.department_ids',
            path='/res/member/departments',
            :multiple='true',
            :tableItems='tableItems'
          )
        .flex.items-center(v-if='localPayload.payload')
          .text-gray-900.text-sm.mb-2.font-medium 过滤标签：
          TaApiField.field(
            recordName='过滤标签',
            v-model:value='localPayload.payload.res_tag_ids',
            path='/res/member/tags',
            :multiple='true',
            :tableItems='tagTableItems'
          )
</template>

<style lang="stylus" scoped>
.field
  >>> .display-layout
    flex-direction row !important
    align-items center !important
</style>
