import { DataFormFormSettingInterface } from '../ta-template-form-core/data_form/types';
import { TaTemplateFormSelect } from '../ta-template-form-core/types';

export const DataFormConfTemplate = {
  options: {
    label: {},
    disabled_actions: {},
    theme: { card: { content: '<p><br></p>' }, background: {} },
    create_text: '提交',
    update_text: '提交',
    searcher: [],
  },
  model: {},
  column_attributes: [],
  actions: [
    { key: 'create', enabled: true },
    { key: 'update', enabled: true },
    { key: 'delete', enabled: true },
    { key: 'import', enabled: true },
    { key: 'export', enabled: true },
  ],
  type: 'container_layout',
  key: 'container_layout_1698377505121_0',
  model_key: 'container_layout_1698377505121_0',
  fields: [
    {
      key: 'TaTemplateFormDesignerKey-1',
      type: 'layout',
      fields: [
        {
          name: '表单名称',
          icon: 'FolderOutlined',
          type: 'input',
          rules: [],
          model: { attr_type: 'string' },
          options: { span: 24 },
          key: 'input_1698377507433_1',
          model_key: 'name',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
        {
          name: '表单类型',
          icon: 'FolderOutlined',
          type: 'radio',
          rules: [
            {
              rule_type: 'required',
              type: 'string',
              required: true,
              message: '请填写正确的表单类型',
            },
          ],
          model: { attr_type: 'string' },
          options: {
            select: [
              { label: '配置表单', value: 'copy' },
              // { label: '预置表单', value: 'template' },
              { label: '引用表单', value: 'model_setting' },
              // { label: '指标采集', value: 'unit_define' },
            ],
            multiple: false,
            span: 24,
            table_items: [],
            display_configurable_form: {},
            import_export_headers: [{ _id: '1698377535136_0' }],
            defaultValue: 'copy',
          },
          key: 'radio_1698377532796_2',
          model_key: 'conf.type',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
        {
          name: '条件块',
          icon: 'FolderOutlined',
          type: 'condition',
          conditions: [
            {
              name: '条件1',
              model_key: 'conf.type',
              val: 'copy',
              fields: [
                {
                  name: 'data_form 功能',
                  icon: 'FolderOutlined',
                  type: 'select',
                  rules: [],
                  model: { attr_type: 'string' },
                  options: {
                    span: 24,
                    select: [
                      {
                        label: '关联更新',
                        value: 'DataFormRecordCreate',
                      },
                    ],
                  },
                  key: 'json_1698377647012_4',
                  model_key: 'data_form_type',
                  fields: [],
                  conditions: [],
                  model_key_prefix: '',
                },
                {
                  name: 'options',
                  icon: 'FolderOutlined',
                  type: 'json',
                  rules: [],
                  model: { attr_type: 'string' },
                  options: { span: 24 },
                  key: 'json_1698377647012_4',
                  model_key: 'options',
                  fields: [],
                  conditions: [],
                  model_key_prefix: '',
                },
              ],
              type: 'simple',
              complex_condition: { groups: [{ items: [] }] },
              opt: '==',
            },
            {
              name: '条件1',
              model_key: 'conf.type',
              val: 'model_setting',
              fields: [
                {
                  name: '表单序列号(🌰user#info)',
                  icon: 'FolderOutlined',
                  type: 'input',
                  rules: [],
                  model: { attr_type: 'string' },
                  options: { span: 24 },
                  key: 'input_1698377647012_4',
                  model_key: 'conf.model_setting_identity',
                  fields: [],
                  conditions: [],
                  model_key_prefix: '',
                },
                {
                  name: 'data_form 功能',
                  icon: 'FolderOutlined',
                  type: 'select',
                  rules: [],
                  model: { attr_type: 'string' },
                  options: {
                    span: 24,
                    select: [
                      {
                        label: '关联更新',
                        value: 'DataFormRecordCreate',
                      },
                    ],
                  },
                  key: 'json_1698377647012_4',
                  model_key: 'data_form_type',
                  fields: [],
                  conditions: [],
                  model_key_prefix: '',
                },
                {
                  name: 'options',
                  icon: 'FolderOutlined',
                  type: 'json',
                  rules: [],
                  model: { attr_type: 'string' },
                  options: { span: 24 },
                  key: 'json_1698377647012_4',
                  model_key: 'options',
                  fields: [],
                  conditions: [],
                  model_key_prefix: '',
                },
              ],
              type: 'simple',
              complex_condition: { groups: [{ items: [] }] },
              opt: '==',
            },
            {
              name: '条件2',
              model_key: 'conf.type',
              val: 'unit_define',
              fields: [
                {
                  name: '指标定义',
                  icon: 'FolderOutlined',
                  type: 'api',
                  rules: [],
                  model: { attr_type: 'string' },
                  options: {
                    span: 24,
                    multiple: true,
                    table_items: [
                      { name: 'ID', data_index: 'id', search: false, type: 'string' },
                      { name: '名称', data_index: 'name', search: true, type: 'string' },
                      { name: '类型', data_index: 'unit_type', search: true, type: 'string' },
                      { name: '上报单位', data_index: 'from', search: true, type: 'string' },
                    ],
                    path: '/perf/admin/unit_defines',
                  },
                  key: 'api_1698377647012_4',
                  model_key: 'conf.unit_define_ids',
                  fields: [],
                  conditions: [],
                  model_key_prefix: '',
                },
                {
                  name: '采集对象',
                  icon: 'FolderOutlined',
                  type: 'user_polymorphic',
                  model_key: 'conf.users',
                  rules: [],
                  model: {
                    attr_type: 'array',
                  },
                  options: {
                    span: 24,
                  },
                },
              ],
              type: 'simple',
              complex_condition: { groups: [{ items: [] }] },
              opt: '==',
            },
            {
              name: '条件3',
              model_key: 'conf.type',
              val: 'template',
              fields: [
                {
                  name: '预置表单',
                  icon: 'FolderOutlined',
                  type: 'api_single',
                  rules: [],
                  model: { attr_type: 'string' },
                  options: {
                    span: 24,
                    table_items: [
                      { name: 'uuid', data_index: 'uuid', search: true, type: 'string' },
                      { name: '名称', data_index: 'name', search: true, type: 'string' },
                    ],
                    path: '/forms/user/templates',
                  },
                  key: 'api_1698377647012_41',
                  model_key: 'conf.template_uuid',
                  fields: [],
                  conditions: [],
                  model_key_prefix: '',
                },
              ],
              type: 'simple',
              complex_condition: { groups: [{ items: [] }] },
              opt: '==',
            },
          ],
          options: { span: 24 },
          key: 'condition_1698377625706_3',
          model_key: 'condition_1698377625706_3',
          fields: [],
          model_key_prefix: '',
        },
      ],
      options: {},
      model: {},
      model_key_prefix: '',
    },
  ],
  conditions: [],
  ctype: 'form_copy',
  cotype: 'form_copy',
  contype: 'form_copy',
  conftype: 'form_copy',
  conf: { type: 'form_copy' },
};

export const getDataFormModelSettingTemplate = (
  formSetting?: DataFormFormSettingInterface,
  opts: { dataFormOptions: TaTemplateFormSelect[] } = { dataFormOptions: [] },
) => ({
  options: {
    label: {},
    disabled_actions: {},
    theme: { card: { content: '<p><br></p>' }, background: {} },
    create_text: '提交',
    update_text: '提交',
    searcher: [],
  },
  model: {},
  column_attributes: [],
  actions: [
    { key: 'create', enabled: true },
    { key: 'update', enabled: true },
    { key: 'delete', enabled: true },
    { key: 'import', enabled: true },
    { key: 'export', enabled: true },
  ],
  type: 'container_layout',
  key: 'container_layout_1698545730295_0',
  model_key: 'container_layout_1698545730295_0',
  fields: [
    {
      key: 'TaTemplateFormDesignerKey-1',
      type: 'layout',
      fields: [
        {
          name: '名称',
          icon: 'FolderOutlined',
          type: 'input',
          rules: [],
          model: { attr_type: 'string' },
          options: { span: 24 },
          key: 'input_1698545732897_1',
          model_key: 'name',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
        {
          name: '布局',
          icon: 'FolderOutlined',
          type: 'radio',
          rules: [],
          model: { attr_type: 'string' },
          options: {
            select: [
              { label: '拼接表单', value: 'vertical' },
              { label: '分步表单', value: 'steps' },
            ],
            multiple: false,
            span: 24,
            table_items: [],
            display_configurable_form: {},
            import_export_headers: [{ _id: '1698545747627_0' }],
          },
          key: 'radio_1698545745648_2',
          model_key: 'layout',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
        {
          name: '配置方式',
          icon: 'FolderOutlined',
          type: 'radio',
          rules: [
            {
              rule_type: 'required',
              type: 'string',
              required: true,
              message: '请填写正确的单项选择',
            },
          ],
          model: { attr_type: 'string' },
          options: {
            select: [
              { label: '直接配置', value: 'copy' },
              { label: '引用 ModelSetting', value: 'model_setting' },
              { label: '引用 template', value: 'template' },
            ],
            multiple: false,
            span: 24,
            defaultValue: 'copy',
            table_items: [],
            display_configurable_form: {},
            import_export_headers: [{ _id: '1698545925554_1' }],
          },
          key: 'radio_1698545848785_3',
          model_key: 'setting.type',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
        {
          name: '条件块',
          icon: 'FolderOutlined',
          type: 'condition',
          conditions: [
            {
              name: '条件1',
              model_key: 'setting.type',
              val: 'model_setting',
              fields: [
                {
                  name: '引用（🌰：user#model）',
                  icon: 'FolderOutlined',
                  type: 'input',
                  rules: [],
                  model: { attr_type: 'string' },
                  options: { span: 24 },
                  key: 'input_1698545954967_5',
                  model_key: 'setting.model_setting_identity',
                  fields: [],
                  conditions: [],
                  model_key_prefix: '',
                },
                {
                  name: 'setable_type',
                  icon: 'FolderOutlined',
                  type: 'input',
                  rules: [],
                  model: { attr_type: 'string' },
                  options: { span: 24 },
                  key: 'input_1698545961485_7',
                  model_key: 'setting.setable_type',
                  fields: [],
                  conditions: [],
                  model_key_prefix: '',
                },
                {
                  name: 'setable_id',
                  icon: 'FolderOutlined',
                  type: 'input',
                  rules: [],
                  model: { attr_type: 'string' },
                  options: { span: 24 },
                  key: 'input_1698545956929_6',
                  model_key: 'setting.setable_id',
                  fields: [],
                  conditions: [],
                  model_key_prefix: '',
                },
              ],
              type: 'simple',
              complex_condition: { groups: [{ items: [] }] },
              opt: '==',
            },
            {
              name: '条件2',
              model_key: 'setting.type',
              val: 'template',
              fields: [
                {
                  name: 'template_uuid',
                  icon: 'FolderOutlined',
                  type: 'input',
                  rules: [],
                  model: { attr_type: 'string' },
                  options: { span: 24 },
                  key: 'input_1698546043818_8',
                  model_key: 'setting.template_uuid',
                  fields: [],
                  conditions: [],
                  model_key_prefix: '',
                },
              ],
              type: 'simple',
              complex_condition: { groups: [{ items: [] }] },
              opt: '==',
            },
          ],
          options: { span: 24 },
          key: 'condition_1698545917578_4',
          model_key: 'condition_1698545917578_4',
          fields: [],
          model_key_prefix: '',
        },
        // {
        //   name: '表单行为',
        //   icon: 'FolderOutlined',
        //   type: 'list',
        //   fields: [
        //     {
        //       name: '表单配置',
        //       icon: 'FolderOutlined',
        //       type: 'select',
        //       rules: [
        //         {
        //           rule_type: 'required',
        //           type: 'string',
        //           required: true,
        //           message: '请填写正确的表单配置',
        //         },
        //       ],
        //       model: { attr_type: 'string' },
        //       options: {
        //         select: [
        //           ...(formSetting?.setting?.confs?.map(conf => ({
        //             label: `${conf.name || conf.seq}表单`,
        //             value: conf.seq,
        //           })) || []),
        //         ],
        //         multiple: false,
        //         span: 24,
        //         table_items: [],
        //         display_configurable_form: {},
        //         import_export_headers: [{ _id: '1699142065129_0' }],
        //       },
        //       key: 'select_1699142057987_3',
        //       model_key: 'seq',
        //       fields: [],
        //       conditions: [],
        //       model_key_prefix: '',
        //     },
        //     {
        //       name: '行为类型',
        //       icon: 'FolderOutlined',
        //       type: 'radio',
        //       rules: [
        //         {
        //           rule_type: 'required',
        //           type: 'string',
        //           required: true,
        //           message: '请填写正确的行为类型',
        //         },
        //       ],
        //       model: { attr_type: 'string' },
        //       options: {
        //         select: [
        //           { label: '创建对象', value: 'DataFormCreateRecord' },
        //           { label: '创建工作流', value: 'Bpm::DataFormCreateInstance' },
        //         ],
        //         multiple: false,
        //         span: 24,
        //         table_items: [],
        //         display_configurable_form: {},
        //         import_export_headers: [{ _id: '1699142104314_1' }],
        //       },
        //       key: 'radio_1699142062654_4',
        //       model_key: 'type',
        //       fields: [],
        //       conditions: [],
        //       model_key_prefix: '',
        //     },
        //     {
        //       name: 'options',
        //       icon: 'FolderOutlined',
        //       type: 'json',
        //       rules: [],
        //       model: { attr_type: 'object' },
        //       options: { span: 24 },
        //       key: 'json_1699142095669_5',
        //       model_key: 'options',
        //       fields: [],
        //       conditions: [],
        //       model_key_prefix: '',
        //     },
        //   ],
        //   rules: [],
        //   model: { attr_type: 'array' },
        //   options: { span: 24, disabled_actions: {} },
        //   key: 'list_1699141868087_2',
        //   model_key: 'actions',
        //   conditions: [],
        //   model_key_prefix: '',
        // },

        {
          name: '表单行为',
          icon: 'FolderOutlined',
          type: 'dynamic_component',
          fields: [],
          rules: [],
          model: { attr_type: 'array' },
          options: {
            span: 24,
            disabled_actions: {},
            dynamic_component: 'TaActionsField',
            props: { dataFormOptions: opts.dataFormOptions },
            defaultValue: [],
          },
          key: 'list_1699141868087_2',
          model_key: 'actions',
          conditions: [],
          model_key_prefix: '',
        },
        {
          name: 'actions',
          icon: 'FolderOutlined',
          type: 'json',
          rules: [],
          model: { attr_type: 'object' },
          options: { span: 24 },
          key: 'json_1699142095669_5',
          model_key: 'actions',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
      ],
      options: {},
      model: {},
      model_key_prefix: '',
    },
  ],
  conditions: [],
});
