export default {
  taComponent: {
    upload: {
      video: 'video',
      image: 'image',
    },
    TaImageUploader: {
      text: 'Click or drag in an image',
    },
    TaImageSingleUploader: {
      preview: 'Preset image',
      please1: 'Please upload',
      please2: 'Please put',
      pleaseNext: 'Drag here or click to upload',
    },
    Api: {
      book: 'address book',
      choose: 'choose',
      edit: 'modify',
      sure: 'confirm',
      cancel: 'cancel',
      shelve: 'press',
      pleaseSearch: 'Please search',
      clear: 'clear',
      selected: 'selected',
      delete: 'delete',
    },
    File: {
      chooseFile: 'Select or drop file',
      dragging: 'Drop the file',
      online: 'Select from online resource library',
      window: 'Upload from computer files',
      saveOnSource: 'Upload and save to resource library',
      saveDir: 'Save location',
      MyDir: 'My files',
      replace: 'Replace',
      chunkError:
        'Duplicate upload chunks. Please check the chunks array returned by the interface.',
      fileisIn: 'The file is already in the upload list.',
      typeImageError:
        'The file format does not meet the requirements. Please upload a valid image.',
      countMax: 'Upload cannot exceed the maximum quantity.',
      unit: 'piece',
      sizeMax: 'File size exceeds',
      sizeSub: 'size limit',
      typeError:
        'The file format does not meet the requirements. Please upload a valid file. The format is',
    },
    actionDrawer: {
      title: 'Abandon submission',
      cancel: 'cancel',
      sure: 'confirm',
      text: 'The form has been modified. Data will not be submitted after abandonment.',
    },
    form: {
      cancel: 'cancel',
      create: 'create',
      save: 'save temporarily',
      saveNew: 'Create new item and retain data',
      saveNext: 'Submit and create the next one',
      submit: 'submit',
      edit: 'edit',
      delete: 'delete',
      deleteConfirm: 'Are you sure to delete this record?',
      close: 'close',
    },
    TaContentField: {
      addText: 'Add text',
      addImage: 'Add image',
      addVideo: 'Add video',
      addAudeo: 'Add audio',
      addFile: 'Add file',
      textContent: 'Content text',
      sure: 'confirm',
      cancel: 'cancel',
      edit: 'edit',
      placeText: 'Enter content',
      sortText: 'Long press and drag to sort',
      tip: 'tip',
      sureAction: 'Are you sure you want to continue the operation?',
      delete: 'delete',
      warningWhite: 'Content cannot be empty.',
    },
    numberRange: {
      error: 'The start number cannot be greater than the end number.',
    },
  },
};
