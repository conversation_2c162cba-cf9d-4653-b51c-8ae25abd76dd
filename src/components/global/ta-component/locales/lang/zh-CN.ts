export default {
  taComponent: {
    upload: {
      video: '视频',
      image: '图片',
    },
    TaImageUploader: {
      text: '点击或拖入图片',
    },
    TaImageSingleUploader: {
      preview: '预置图片',
      please1: '请上传',
      please2: '请将',
      pleaseNext: '拖至此处或点击上传',
    },
    Api: {
      book: '通讯录',
      choose: '选择',
      edit: '修改',
      sure: '确定',
      cancel: '取消',
      shelve: '按',
      pleaseSearch: '请搜索',
      clear: '清空',
      selected: '已选',
      delete: '删除',
    },
    File: {
      chooseFile: '选择或拖入文件',
      dragging: '请放开文件',
      online: '从线上资源库选择',
      window: '从电脑文件上传',
      saveOnSource: '上传并保存至资源库',
      saveDir: '保存位置',
      MyDir: '我的文件',
      replace: '更换',
      chunkError: '重复上传分片，请检查接口返回的 chunks 数组。',
      fileisIn: '文件已经在上传列表中',
      typeImageError: '文件格式不符合要求，请上传有效的图片。',
      countMax: '上传不能超过最大数量',
      unit: '个',
      sizeMax: '文件尺寸超过',
      sizeSub: '大小限制',
      typeError: '文件格式不符合要求。请上传有效的文件,格式为',
    },
    actionDrawer: {
      title: '放弃提交',
      cancel: '取消',
      sure: '确定',
      text: '表单有改动，放弃后数据将不会被提交',
    },
    form: {
      cancel: '取消',
      create: '创建',
      save: '暂存',
      saveNew: '新建项保留数据',
      saveNext: '提交并创建下一个',
      submit: '提交',
      edit: '编辑',
      delete: '删除',
      deleteConfirm: '确定删除该记录吗？',
      close: '关闭',
    },
    TaContentField: {
      addText: '添加文字',
      addImage: '添加图片',
      addVideo: '添加视频',
      addAudeo: '添加音频',
      addFile: '添加文件',
      textContent: '内容文字',
      sure: '确定',
      cancel: '取消',
      edit: '编辑',
      placeText: '输入内容',
      sortText: '长按拖动排序',
      tip: '提示',
      sureAction: '确认要继续操作吗？',
      delete: '删除',
      warningWhite: '内容不能为空',
    },
    numberRange: {
      error: '起始数不能大于结束数',
    },
  },
};
