<script lang="ts">
import { defineComponent, toRefs, computed } from 'vue';
import { useFmtNumber } from '../utils/useFmtNumber';

const TableRenderAmount = defineComponent({
  name: 'TableRenderAmount',
  components: {},
  props: {
    value: { type: String, default: '' },
  },
  setup(props) {
    const { thousandSeparator } = useFmtNumber();

    const formattedValue = computed(() => {
      if (!props.value) return props.value;
      return thousandSeparator(Number(props.value));
    });

    return {
      ...toRefs(props),
      formattedValue,
    };
  },
});
export default TableRenderAmount;
</script>

<template lang="pug">
.table-render-amount {{ formattedValue }}
</template>

<style lang="stylus" scoped></style>
