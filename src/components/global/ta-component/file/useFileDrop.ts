import { ref } from 'vue';

const useFileDrop = (addFile: (file: File) => void) => {
  const dragging = ref(false);

  const onDragEnter = (e: any) => {
    e.preventDefault();
    e.stopPropagation();
    console.log('??????');

    dragging.value = true;
  };

  const onDragOver = (e: any) => {
    e.preventDefault();
    e.stopPropagation();
    if (!dragging.value) {
      dragging.value = true;
    }
    // 必须设置这个属性才能接收文件
    e.dataTransfer.dropEffect = 'copy';
  };

  const onDragLeave = (e: any) => {
    e.preventDefault();
    e.stopPropagation();
    dragging.value = false;
  };

  const onDrop = (e: any) => {
    e.preventDefault();
    e.stopPropagation();
    dragging.value = false;

    try {
      // 尝试直接获取原生事件
      const nativeEvent = e.nativeEvent as DragEvent;

      if (nativeEvent?.dataTransfer?.files && nativeEvent.dataTransfer.files.length > 0) {
        Array.from(nativeEvent.dataTransfer.files).forEach(file => addFile(file));

        return;
      }

      // 如果原生事件不可用，尝试其他方法
      if (e.dataTransfer.files && e.dataTransfer.files.length > 0) {
        Array.from(e.dataTransfer.files).forEach(file => addFile(file as File));
      } else {
        // 如果拖放失败，提示用户使用点击上传
        console.log('拖放上传失败，请尝试点击上传');
      }
    } catch (error) {
      console.error('拖放处理错误:', error);
    }
  };

  return {
    onDragEnter,
    onDragOver,
    onDragLeave,
    onDrop,
    dragging,
  };
};

export default useFileDrop;
