<script lang="ts">
import ComColorfulLabel from '@/engines/iest/components/global/ComColorfulLabel.vue';
import { ServeUserGroupsApi } from '@/engines/iest/serve-core/apis/serve/user/groups.api';
import { VStore } from '@/lib/vails';
import { PropType, computed, defineComponent, onMounted, ref, toRefs } from 'vue';
import { TaAccordionSearcherItemInterface } from './ta-component/TaIndexView/TaAccordionSearcher.vue';
let seq = 1;

const ComTagGroupField = defineComponent({
  name: 'ComTagGroupField',
  components: { ComColorfulLabel },
  props: {
    items: { type: Array as PropType<TaAccordionSearcherItemInterface[]>, default: () => [] },
  },
  setup(props, { emit }) {
    const localItems = computed({
      get: () => props.items,
      set: val => emit('update:items', val),
    });

    // 内部状态：当前选中的标签ID（不立即提交）
    const selectedTagIds = ref<number[]>([]);
    // 查询模式：'or' 表示包含任意标签，'and' 表示包含所有标签
    const queryMode = ref<'or' | 'and'>('or');

    const groupStore = new VStore(
      new ServeUserGroupsApi({
        parents: [{ type: 'submodules', id: 1 }],
      }),
    );

    onMounted(() => {
      groupStore.index();
    });

    // 切换标签选中状态（不立即提交）
    const toggleTag = (tag: any) => {
      if (selectedTagIds.value.includes(tag.id)) {
        selectedTagIds.value = selectedTagIds.value.filter(id => id !== tag.id);
      } else {
        selectedTagIds.value.push(tag.id);
      }
    };

    // 切换查询模式
    const toggleQueryMode = () => {
      queryMode.value = queryMode.value === 'or' ? 'and' : 'or';
    };

    // 生成查询对象
    const generateQuery = () => {
      if (selectedTagIds.value.length === 0) return {};

      if (queryMode.value === 'or') {
        // OR 查询：包含任意一个标签
        return { tags_id_in: selectedTagIds.value };
      } else {
        // AND 查询：使用 scopes 方式
        return {
          scopes: {
            tags_id_contain: selectedTagIds.value,
          },
        };
      }
    };

    // 应用筛选（提交选中的标签）
    const applyFilter = () => {
      if (!localItems.value) {
        localItems.value = [];
      }

      const cache = [...localItems.value];

      // 移除现有的标签查询项
      const filteredCache = cache.filter(
        item =>
          !item.key.startsWith('ComTagGroupField-') ||
          !Object.keys(item.query).some(key => key.startsWith('tags_id_in') || key === 'scopes'),
      );

      // 如果有选中的标签，添加新的查询项
      if (selectedTagIds.value.length > 0) {
        const query = generateQuery();
        const modeLabel = queryMode.value === 'or' ? '包含任意' : '包含所有';
        const tagNames = selectedTagIds.value.map(id => {
          const allTags = groupStore.records.value.flatMap((group: any) => group.tags || []);
          const foundTag = allTags.find((t: any) => t.id === id);
          return foundTag?.name || `标签${id}`;
        });

        filteredCache.push({
          key: `ComTagGroupField-${seq++}`,
          label: `${modeLabel}: ${tagNames.join(', ')}`,
          query,
        });
      }

      localItems.value = filteredCache;
    };

    // 清除选择
    const clearSelection = () => {
      selectedTagIds.value = [];
    };

    // 根据标签ID获取标签名称
    const getTagName = (tagId: number) => {
      const allTags = groupStore.records.value.flatMap((group: any) => group.tags || []);
      const foundTag = allTags.find((t: any) => t.id === tagId);
      return foundTag?.name || `标签${tagId}`;
    };

    // 获取某个分组中已选择的标签数量
    const getSelectedCountInGroup = (group: any) => {
      if (!group.tags) return 0;
      return group.tags.filter((tag: any) => selectedTagIds.value.includes(tag.id)).length;
    };

    return {
      ...toRefs(props),
      toggleTag,
      toggleQueryMode,
      applyFilter,
      clearSelection,
      getTagName,
      getSelectedCountInGroup,
      groups: groupStore.records,
      selectedTagIds,
      queryMode,
    };
  },
});
export default ComTagGroupField;
</script>

<template lang="pug">
.com-tag-group-field.space-y-3
  // 顶部控制栏（固定显示，包含模式切换和操作按钮）
  .top-control-bar.flex.items-center.justify-between.p-3.bg-gray-50.rounded-lg.border.mb-3
    .left-section.flex.items-center.gap-4
      .mode-section.flex.items-center.gap-2
        .text-sm.text-gray-600 筛选模式：
        a-button.text-xs(
          :type='queryMode === "or" ? "primary" : "default"',
          size='small',
          @click='toggleQueryMode'
        ) {{ queryMode === 'or' ? '包含任意 (OR)' : '包含所有 (AND)' }}

      .selected-count(v-if='selectedTagIds.length > 0')
        .text-sm.text-blue-600 已选择 {{ selectedTagIds.length }} 个标签

    .right-section.flex.items-center.gap-2
      a-button(v-if='selectedTagIds.length > 0', size='small', @click='clearSelection') 清除选择
      a-button(
        v-if='selectedTagIds.length > 0',
        type='primary',
        size='small',
        @click='applyFilter'
      ) 应用筛选 ({{ selectedTagIds.length }})

  // 选中标签预览（紧凑显示）
  .selected-preview(v-if='selectedTagIds.length > 0')
    .flex.flex-wrap.gap-1.p-2.bg-blue-50.rounded.border.border-blue-200
      .selected-tag(v-for='tagId in selectedTagIds', :key='tagId')
        .inline-flex.items-center.px-2.py-1.bg-blue-100.text-blue-800.text-xs.rounded.cursor-pointer(
          @click='toggleTag({id: tagId})',
          :title='"点击移除: " + getTagName(tagId)'
        )
          | {{ getTagName(tagId) }}
          .ml-1.text-blue-600(class='hover:text-blue-800') ×

  // 标签选择区域（紧凑布局）
  .tag-selection-area
    .group(v-for='group in groups', :key='group.id')
      .text-sm.mb-1.font-medium.text-gray-700.flex.items-center
        | {{ group.name }}
        .ml-2.text-xs.text-gray-500(v-if='getSelectedCountInGroup(group) > 0')
          | (已选 {{ getSelectedCountInGroup(group) }} 个)
      .flex.flex-wrap.gap-1.mb-3
        .tag-item(v-for='tag in group.tags', :key='tag.id')
          ComColorfulLabel.px-2.py-1.cursor-pointer.transition-all.duration-200.text-xs(
            :label='tag.name',
            :color='selectedTagIds.includes(tag.id) ? "blue" : "gray"',
            @click='toggleTag(tag)'
          )

</template>

<style lang="stylus" scoped></style>
