<script lang="ts">
import { ServeManageAiMessageTemplatesApi } from '@/engines/iest/serve-core/apis/serve/manage/ai_message_templates.api';
import { ServeAiMessageTemplateModel } from '@/engines/iest/serve-core/models/serve/manage/ai_message_templates';
import { VStore } from '@/lib/vails';
import { message } from 'ant-design-vue';
import { computed, defineComponent, onMounted, PropType, ref } from 'vue';
import ComAiContentCreator from './ComAiContentGenerator.vue';
import ComMessageTemplateSelector from './ComMessageTemplateSelector.vue';

interface MessageItem {
  id: number;
  content: string;
}

export default defineComponent({
  name: 'ComMessageContent',
  components: {
    ComAiContentCreator,
    ComMessageTemplateSelector,
  },
  props: {
    value: {
      type: Array as PropType<MessageItem[]>,
      default: () => [],
    },
    payload: { type: Object, default: () => ({}) },
    disabled: { type: Boolean, default: false },
  },
  emits: ['update:value', 'edit', 'delete', 'manual-create', 'ai-create', 'template-import'],
  setup(props, { emit }) {
    const createModalVisible = ref(false);
    const editModalVisible = ref(false);
    const aiCreateModalVisible = ref(false);
    const templateSelectorVisible = ref(false);
    const newMessageContent = ref('');
    const editingMessageContent = ref('');
    const editingIndex = ref(-1);
    const saveAsDraft = ref(false);
    const aiMessageTemplateStore = new VStore(
      new ServeManageAiMessageTemplatesApi(),
      ServeAiMessageTemplateModel,
    );

    // 修改：使用计算属性代替ref双向绑定，避免循环更新
    const localMessages = computed({
      get: () => props.value,
      set: newValue => {
        emit('update:value', newValue);
      },
    });

    onMounted(() => {
      if (!props.payload?.rule_id) {
        console.warn('未配置规则ID，AI创建功能可能无法正常使用');
      }
    });

    const handleEdit = (index: number) => {
      if (props.disabled) return; // 禁用状态下不允许编辑
      editingIndex.value = index;
      editingMessageContent.value = props.value[index].content;
      editModalVisible.value = true;
    };

    const handleEditConfirm = () => {
      if (props.disabled) return; // 禁用状态下不允许确认编辑
      if (editingMessageContent.value.trim()) {
        try {
          // 创建新的消息数组，避免直接修改props
          const newMessages = [...props.value];
          newMessages[editingIndex.value] = {
            ...newMessages[editingIndex.value],
            content: editingMessageContent.value,
          };

          // 更新消息
          emit('update:value', newMessages);

          editModalVisible.value = false;
          editingMessageContent.value = '';
          editingIndex.value = -1;
          message.success('修改成功');
        } catch (error) {
          console.error('修改消息失败:', error);
          message.error('修改失败');
        }
      }
    };

    const handleEditCancel = () => {
      editModalVisible.value = false;
      editingMessageContent.value = '';
      editingIndex.value = -1;
    };

    const handleDelete = (index: number) => {
      if (props.disabled) return; // 禁用状态下不允许删除
      try {
        // 创建新的消息数组，避免直接修改props
        const newMessages = [...props.value];
        newMessages.splice(index, 1);

        // 更新消息
        emit('update:value', newMessages);

        message.success('删除成功');
      } catch (error) {
        console.error('删除消息失败:', error);
        message.error('删除失败');
      }
    };

    const handleManualCreate = () => {
      if (props.disabled) return; // 禁用状态下不允许创建
      createModalVisible.value = true;
    };

    const handleAiCreate = () => {
      if (props.disabled) return; // 禁用状态下不允许AI创建
      if (!props.payload?.rule_id) {
        message.warning('请先配置规则ID');
        return;
      }
      aiCreateModalVisible.value = true;
    };

    const handleTemplateImport = () => {
      if (props.disabled) return; // 禁用状态下不允许导入模板
      templateSelectorVisible.value = true;
    };

    const handleCreateConfirm = async () => {
      if (newMessageContent.value.trim()) {
        try {
          const newMessage = {
            id: Date.now(), // 使用时间戳作为临时id
            content: newMessageContent.value,
          };

          // 创建新的消息数组并添加新消息
          const newMessages = [...props.value, newMessage];

          // 更新消息
          emit('update:value', newMessages);

          // 如果选择了保存至我的消息，调用create接口
          if (saveAsDraft.value) {
            try {
              await aiMessageTemplateStore.create({
                content: newMessageContent.value,
                state: 'draft',
                name: `消息_${Date.now()}`,
              });
            } catch (error) {
              console.error('保存至我的消息失败:', error);
            }
          }

          createModalVisible.value = false;
          newMessageContent.value = '';
          saveAsDraft.value = false;
          message.success('创建消息成功');
        } catch (error) {
          console.error('创建消息失败:', error);
          message.error('创建消息失败');
        }
      }
    };

    const handleCreateCancel = () => {
      createModalVisible.value = false;
      newMessageContent.value = '';
      saveAsDraft.value = false;
    };

    const handleAiCreateConfirm = async (contents: string[]) => {
      if (contents.length > 0) {
        try {
          // 将新消息添加到消息列表
          const newMessages = [...props.value];
          const messagesToAdd = contents.map(content => ({
            id: Date.now() + Math.random(), // 使用时间戳+随机数作为临时id
            content: content,
          }));

          // 使用不可变方式添加消息
          emit('update:value', [...newMessages, ...messagesToAdd]);

          // 如果选择了保存至我的消息
          if (saveAsDraft.value) {
            try {
              await Promise.all(
                contents.map(content =>
                  aiMessageTemplateStore.create({
                    content: content,
                    state: 'draft',
                    name: `AI消息_${Date.now()}`,
                  }),
                ),
              );
            } catch (error) {
              console.error('保存至我的消息失败:', error);
            }
          }

          // 重置状态
          aiCreateModalVisible.value = false;
          message.success(`成功添加${contents.length}条消息`);
        } catch (error) {
          console.error('保存AI生成内容失败:', error);
          message.error('保存失败');
        }
      }
    };

    // 处理模板选择器确认
    const handleTemplateSelectorConfirm = async (contents: string[]) => {
      if (contents.length > 0) {
        try {
          // 将选中的模板内容添加到消息列表
          const newMessages = [...props.value];
          const messagesToAdd = contents.map(content => ({
            id: Date.now() + Math.random(), // 使用时间戳+随机数作为临时id
            content: content,
          }));

          // 使用不可变方式添加消息
          emit('update:value', [...newMessages, ...messagesToAdd]);

          message.success(`成功导入${contents.length}条模板消息`);
        } catch (error) {
          console.error('导入模板失败:', error);
          message.error('导入模板失败');
        }
      }
    };

    // 处理模板选择器取消
    const handleTemplateSelectorCancel = () => {
      templateSelectorVisible.value = false;
    };

    return {
      localMessages,
      handleEdit,
      handleDelete,
      handleManualCreate,
      handleAiCreate,
      handleTemplateImport,
      createModalVisible,
      editModalVisible,
      aiCreateModalVisible,
      templateSelectorVisible,
      newMessageContent,
      editingMessageContent,
      saveAsDraft,
      handleAiCreateConfirm,
      handleCreateConfirm,
      handleCreateCancel,
      handleEditConfirm,
      handleEditCancel,
      handleTemplateSelectorConfirm,
      handleTemplateSelectorCancel,
    };
  },
});
</script>

<template lang="pug">
.com-message-content
  .flex.justify-between.items-center.mb-4
    .text-lg.font-medium 消息内容
    .action-buttons.flex.space-x-3(v-if='!disabled')
      button.action-btn.manual-create(@click='handleManualCreate')
        i.fas.fa-edit.mr-1
        span 手动创建
      button.action-btn.ai-create(@click='handleAiCreate')
        i.fas.fa-robot.mr-1
        span AI创建
      button.action-btn.template-import(@click='handleTemplateImport')
        i.fas.fa-file-import.mr-1
        span 模板库导入

  .message-list.space-y-4
    .message-item.bg-white.rounded-lg.p-4.relative(
      v-for='(message, index) in localMessages',
      :key='message.id || index',
      :class='{ "view-mode": disabled }'
    )
      .message-header.flex.justify-between.items-center
        .word-count 字数：{{ message.content?.length || 0 }}
        .action-buttons.flex.space-x-2(v-if='!disabled')
          a-tooltip(title='编辑')
            button.edit-btn(@click='handleEdit(index)')
              svg(
                width='16',
                height='16',
                viewBox='0 0 16 16',
                fill='none',
                xmlns='http://www.w3.org/2000/svg'
              )
                path(
                  d='M9.3335 4L12.0002 6.66667M2.66683 13.3333H5.3335L12.3335 6.33333C12.8638 5.80309 12.8638 4.93024 12.3335 4.4L11.6002 3.66667C11.0699 3.13643 10.1971 3.13643 9.66683 3.66667L2.66683 10.6667V13.3333Z',
                  stroke='currentColor',
                  stroke-width='1.5',
                  stroke-linecap='round',
                  stroke-linejoin='round'
                )
          TaPopoverConfirm(title='删除', content='确认删除该消息？', @confirm='() => handleDelete(index)')
            button.delete-btn(@click.stop='')
              svg(
                width='16',
                height='16',
                viewBox='0 0 16 16',
                fill='none',
                xmlns='http://www.w3.org/2000/svg'
              )
                path(
                  d='M14.5159 3.86842H11.2567V2.18421C11.2567 1.73753 11.085 1.30914 10.7793 0.993294C10.4737 0.677443 10.0592 0.5 9.62703 0.5H6.36777C5.93556 0.5 5.52106 0.677443 5.21544 0.993294C4.90983 1.30914 4.73814 1.73753 4.73814 2.18421V3.86842H1.47888C1.26278 3.86842 1.05552 3.95714 0.902716 4.11507C0.749909 4.27299 0.664063 4.48719 0.664062 4.71053C0.664063 4.93387 0.749909 5.14806 0.902716 5.30598C1.05552 5.46391 1.26278 5.55263 1.47888 5.55263H2.29369V14.8158C2.29369 15.2625 2.46538 15.6909 2.771 16.0067C3.07661 16.3226 3.49112 16.5 3.92332 16.5H12.0715C12.5037 16.5 12.9182 16.3226 13.2238 16.0067C13.5294 15.6909 13.7011 15.2625 13.7011 14.8158V5.55263H14.5159C14.732 5.55263 14.9393 5.46391 15.0921 5.30598C15.2449 5.14806 15.3307 4.93387 15.3307 4.71053C15.3307 4.48719 15.2449 4.27299 15.0921 4.11507C14.9393 3.95714 14.732 3.86842 14.5159 3.86842ZM6.36777 2.18421H9.62703V3.86842H6.36777V2.18421ZM12.0715 14.8158H3.92332V5.55263H12.0715V14.8158Z',
                  fill='#E02424'
                )
                path(
                  d='M6.36777 6.39474C6.15166 6.39474 5.94441 6.48346 5.7916 6.64138C5.6388 6.79931 5.55295 7.0135 5.55295 7.23684V13.1316C5.55295 13.3549 5.6388 13.5691 5.7916 13.727C5.94441 13.885 6.15166 13.9737 6.36777 13.9737C6.58387 13.9737 6.79112 13.885 6.94393 13.727C7.09673 13.5691 7.18258 13.3549 7.18258 13.1316V7.23684C7.18258 7.0135 7.09673 6.79931 6.94393 6.64138C6.79112 6.48346 6.58387 6.39474 6.36777 6.39474Z',
                  fill='#E02424'
                )
                path(
                  d='M9.62703 6.39474C9.41092 6.39474 9.20367 6.48346 9.05086 6.64138C8.89806 6.79931 8.81221 7.0135 8.81221 7.23684V13.1316C8.81221 13.3549 8.89806 13.5691 9.05086 13.727C9.20367 13.885 9.41092 13.9737 9.62703 13.9737C9.84313 13.9737 10.0504 13.885 10.2032 13.727C10.356 13.5691 10.4418 13.3549 10.4418 13.1316V7.23684C10.4418 7.0135 10.356 6.79931 10.2032 6.64138C10.0504 6.48346 9.84313 6.39474 9.62703 6.39474Z',
                  fill='#E02424'
                )
      .message-content.text-gray-700.break-all.overflow-y-scroll.max-h-32 {{ message.content }}

    .empty-state.text-center.py-8(v-if='!localMessages.length')
      .text-gray-400(v-if='!disabled') 暂无消息内容，请通过上方按钮添加
      .text-gray-400(v-else) 暂无消息内容

  // 创建消息弹窗
  a-modal(
    v-model:visible='createModalVisible',
    title='手动创建',
    @ok='handleCreateConfirm',
    @cancel='handleCreateCancel'
  )
    .modal-content
      .form-item
        .label 消息内容
        a-textarea(v-model:value='newMessageContent', :rows='6', placeholder='请输入消息内容')
      .form-item.mt-4
        a-checkbox(v-model:checked='saveAsDraft') 保存至我的消息

  // 编辑消息弹窗
  a-modal(
    v-model:visible='editModalVisible',
    title='编辑消息',
    @ok='handleEditConfirm',
    @cancel='handleEditCancel'
  )
    .modal-content
      .form-item
        .label 消息内容
        a-textarea(v-model:value='editingMessageContent', :rows='6', placeholder='请输入消息内容')

  // AI创建弹窗
  ComAiContentCreator(
    v-model:visible='aiCreateModalVisible',
    :ruleId='payload?.rule_id',
    :currentMessageCount='localMessages.length',
    @confirm='handleAiCreateConfirm'
  )

  // 模板选择器弹窗
  ComMessageTemplateSelector(
    v-model:visible='templateSelectorVisible',
    @confirm='handleTemplateSelectorConfirm',
    @cancel='handleTemplateSelectorCancel'
  )
</template>

<style lang="stylus" scoped>
.com-message-content
  height 100%
  width 100%
  padding 20px
.action-btn
  display flex
  align-items center
  padding 8px 16px
  border-radius 6px
  border 1px solid $primary-color
  color $primary-color
  background transparent
  transition all 0.3s ease
  font-size 14px
  cursor pointer
  &:hover
    background $primary-color
    color white
  i
    font-size 16px
.message-item
  box-shadow 0 1px 3px rgba(0, 0, 0, 0.1)
  transition all 0.3s ease
  border 1px solid #eee
  &:hover
    box-shadow 0 2px 6px rgba(0, 0, 0, 0.15)
  &.view-mode
    box-shadow none
    border 1px solid #f0f0f0
    &:hover
      box-shadow none
  .message-header
    padding-bottom 8px
    border-bottom 1px solid #f0f0f0
    margin-bottom 8px
    .word-count
      color #999
      font-size 12px
  .action-buttons
    opacity 0.6
    transition opacity 0.3s
  &:hover
    .action-buttons
      opacity 1
.edit-btn, .delete-btn
  padding 6px 8px
  cursor pointer
  background transparent
  border none
  outline none
  transition all 0.3s ease
  border-radius 4px
  display flex
  align-items center
  justify-content center
  &:hover
    background-color rgba(0, 0, 0, 0.05)
  svg
    width 16px
    height 17px
.edit-btn
  &:hover
    background-color rgba(107, 114, 128, 0.1)
    svg path
      fill #374151
.delete-btn
  &:hover
    background-color rgba(224, 36, 36, 0.1)
    svg path
      fill #DC2626
.message-content
  padding 8px 0
  line-height 1.5
  font-size 14px
.modal-content
  .form-item
    .label
      color #374151
      font-weight 500
      font-size 14px
</style>
