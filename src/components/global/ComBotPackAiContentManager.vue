<script lang="ts">
import { ServeUserPacksApi } from '@/engines/iest/serve-core/apis/serve/user/packs.api';
import { ServePackModel } from '@/engines/iest/serve-core/models/serve/user/packs';
import { VStore } from '@/lib/vails';
import { CopyOutlined, ReloadOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import { computed, defineComponent, h, nextTick, onMounted, ref, toRefs } from 'vue';

export default defineComponent({
  name: 'ComBotPackAiContentManager',
  components: {
    CopyOutlined,
    ReloadOutlined,
  },
  props: {
    value: { type: String, default: undefined },
    payload: { type: Object, default: () => ({}) },
    disabled: { type: Boolean, default: false },
  },
  emits: ['update:value'],
  setup(props, { emit }) {
    const AI_ICON = 'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iest/ai_summary.png';

    // 创建响应式引用
    const { payload } = toRefs(props);

    // ComBotPackAiContentSelector 相关状态
    const contents = computed(() => payload.value?.payload?.contents);
    const localValue = computed({
      get() {
        return props.value;
      },
      set(val) {
        emit('update:value', val);
      },
    });
    const selectedIndex = computed(() => {
      return contents.value?.findIndex((content: string) => content === localValue.value) || 0;
    });
    const visible = ref(false);
    const cacheContent = ref('');

    // AIAssistantValidation 相关状态
    const isLoading = ref<boolean>(false);
    const inputRef = ref<HTMLElement | null>(null);
    const editedPayload = ref<string>('');
    const selectedMode = ref<string>('deepseek');

    // 撤销功能相关状态
    const undoHistory = ref<string[]>([]);
    const canUndo = computed(() => undoHistory.value.length > 0);

    // 字数统计
    const wordCount = computed(() => {
      const text = editedPayload.value.trim();
      if (!text) return 0;

      // 统计各种字符
      const chineseChars = (text.match(/[\u4e00-\u9fa5]/g) || []).length; // 中文字符
      const englishChars = (text.match(/[a-zA-Z]/g) || []).length; // 英文字符（每个字母）
      const numberChars = (text.match(/\d/g) || []).length; // 数字字符（每个数字）
      const punctuationChars = (text.match(/[^\u4e00-\u9fa5a-zA-Z\d\s]/g) || []).length; // 标点符号

      return chineseChars + englishChars + numberChars + punctuationChars;
    });

    // 内容预览处理
    const getPreviewContent = (content: string, maxLength = 100) => {
      if (content.length <= maxLength) return content;
      return content.substring(0, maxLength) + '...';
    };

    // 键盘快捷键处理
    const handleKeyDown = (event: KeyboardEvent) => {
      // Ctrl+Enter 或 Cmd+Enter 发送请求
      if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
        event.preventDefault();
        if (!isLoading.value && editedPayload.value.trim()) {
          callAIService();
        }
      }
    };

    const store = new VStore(new ServeUserPacksApi(), ServePackModel);

    // 可用的DeepSeek模式配置
    const availableModes = ref([
      {
        key: 'deepseek',
        name: 'DeepSeek 标准模式',
        icon: '⚡',
        description: '标准推理模式，速度相对较快',
        timeout: 120,
        speed: '快速',
        quality: '良好',
        waitTime: '快速响应',
      },
      {
        key: 'deepseek_r1',
        name: 'DeepSeek R1 深度推理',
        icon: '🧠',
        description: '深度推理模式，质量更高但速度较慢',
        timeout: 300,
        speed: '较慢',
        quality: '优秀',
        waitTime: '需要等待',
      },
    ]);

    // ComBotPackAiContentSelector 方法
    const handleSelect = (content: string, index: number) => {
      if (props.disabled) return;
      localValue.value = content;
    };

    const handleContentClick = (content: string, index: number) => {
      if (props.disabled) return;
      cacheContent.value = content;
      visible.value = true;
    };

    const onOk = () => {
      localValue.value = cacheContent.value;
      visible.value = false;
    };

    const onCancel = () => {
      visible.value = false;
    };

    // 撤销功能方法
    const saveToHistory = () => {
      // 保存当前状态到历史记录，最多保存10条
      undoHistory.value.push(editedPayload.value);
      if (undoHistory.value.length > 10) {
        undoHistory.value.shift();
      }
    };

    const handleUndo = () => {
      if (undoHistory.value.length > 0) {
        editedPayload.value = undoHistory.value.pop() || '';
        message.success('已撤销上一次操作');

        // 自动聚焦到输入框
        nextTick(() => {
          inputRef.value?.focus();
          const textarea = (inputRef.value as any)?.$el?.querySelector('textarea');
          if (textarea) {
            textarea.setSelectionRange(textarea.value.length, textarea.value.length);
          }
        });
      }
    };

    // 清空内容功能
    const handleClearContent = () => {
      if (editedPayload.value.trim()) {
        // 保存当前状态到历史记录
        saveToHistory();
        editedPayload.value = '';
        message.success('内容已清空');

        // 自动聚焦到输入框
        nextTick(() => {
          inputRef.value?.focus();
        });
      }
    };

    // 替换AI输入框内容
    const handleReplaceContent = (content: string, event: Event) => {
      event.stopPropagation();
      if (props.disabled) return;

      // 保存当前状态到历史记录
      saveToHistory();

      editedPayload.value = content;
      message.success('内容已替换到AI输入框');

      // 自动聚焦到输入框
      nextTick(() => {
        inputRef.value?.focus();
        // 将光标移到最后
        const textarea = (inputRef.value as any)?.$el?.querySelector('textarea');
        if (textarea) {
          textarea.setSelectionRange(textarea.value.length, textarea.value.length);
        }
      });
    };

    // 添加示例内容到AI输入框
    const handleAddAsExample = (content: string, event: Event) => {
      event.stopPropagation();
      if (props.disabled) return;

      // 保存当前状态到历史记录
      saveToHistory();

      // 在现有内容后面追加，而不是替换
      const currentContent = editedPayload.value;
      const separator = currentContent ? '\n\n' : '';
      const exampleText = `${separator}> ${content}\n\n请帮我：`;

      editedPayload.value = currentContent + exampleText;

      message.success('引用内容已添加到输入框');

      // 自动聚焦并将光标定位到最后
      nextTick(() => {
        inputRef.value?.focus();
        // 将光标移到最后
        const textarea = (inputRef.value as any)?.$el?.querySelector('textarea');
        if (textarea) {
          textarea.setSelectionRange(textarea.value.length, textarea.value.length);
        }
      });
    };

    // AIAssistantValidation 方法
    const modeDisplayInfo = computed(() => {
      return (
        availableModes.value.find(mode => mode.key === selectedMode.value) ||
        availableModes.value[0]
      );
    });

    const waitTimeHint = computed(() => {
      const mode = modeDisplayInfo.value;
      return `${mode.name} - ${mode.description}`;
    });

    const changeModeHandler = (e: any) => {
      const newMode = e.target.value;
      selectedMode.value = newMode;

      // 显示切换成功的消息
      const modeInfo = availableModes.value.find(mode => mode.key === newMode);
      message.success(`已切换到 ${modeInfo?.name}`);
    };

    // 安全访问payload属性（与AIAssistantValidation保持一致）
    const getSafePayload = () => {
      const value = payload.value || {};
      // 先解构出 payload 和 其他属性
      const { payload: originalPayload, ...rest } = value;

      return {
        id: value.id || '',
        ...rest, // 先展开其他属性
        payload: {
          prompt: value.payload?.prompt || value.rule?.options?.prompt,
          use_ai: false,
          contents: [],
          ...(originalPayload || {}), // 最后合并原始 payload
        },
      };
    };

    const initEditContent = () => {
      const safePayload = getSafePayload();
      editedPayload.value = safePayload.payload.prompt || '';
      selectedMode.value = 'deepseek';
    };

    const callAIService = async () => {
      if (!editedPayload.value.trim()) {
        message.warning('请输入内容');
        return;
      }

      const safePayload = getSafePayload();

      try {
        isLoading.value = true;

        // 显示带有模式信息的加载提示
        const loadingMessage = `${modeDisplayInfo.value.icon} 正在使用 ${modeDisplayInfo.value.name} 生成内容，${waitTimeHint.value}...`;
        message.info(loadingMessage, 5);

        // 调用AI接口，传递选择的模式
        await store
          .sendMemberAction({
            action: 'refresh_contents_by_rule',
            id: safePayload.id,
            config: {
              data: {
                prompt: editedPayload.value,
                mode: selectedMode.value, // 传递选择的模式
              },
              timeout: (modeDisplayInfo.value.timeout + 30) * 1000, // 添加30秒缓冲时间
            },
          })
          .then((res: any) => {
            if (res?.data?.contents) {
              // 直接更新payload（就像原始组件一样）
              payload.value.payload.contents = res.data.contents;

              // 显示成功信息，包含生成时间
              let successMsg = 'AI内容生成成功';
              if (res.data.generation_time) {
                successMsg += `，耗时 ${res.data.generation_time} 秒`;
              }
              message.success(successMsg);
            }
          });
      } catch (error) {
        console.error('AI服务调用失败:', error);
        if (error instanceof Error && error.message?.includes('timeout')) {
          message.error(`${modeDisplayInfo.value.name} 响应超时，请稍后重试`);
        } else {
          message.error('AI服务调用失败，请检查网络连接');
        }
      } finally {
        isLoading.value = false;
      }
    };

    onMounted(() => {
      initEditContent();
      nextTick(() => {
        inputRef.value?.focus();
      });
    });

    return {
      // ComBotPackAiContentSelector
      contents,
      selectedIndex,
      handleSelect,
      localValue,
      AI_ICON,
      handleContentClick,
      visible,
      cacheContent,
      onOk,
      onCancel,
      handleReplaceContent,
      handleAddAsExample,
      h,
      CopyOutlined,
      isLoading,
      inputRef,
      editedPayload,
      callAIService,
      modeDisplayInfo,
      waitTimeHint,
      availableModes,
      selectedMode,
      changeModeHandler,

      // 撤销功能
      canUndo,
      handleUndo,
      handleClearContent,
      wordCount,
      getPreviewContent,
      handleKeyDown,
    };
  },
});
</script>

<template lang="pug">
.bot-pack-ai-content-manager
  // AI助手输入框部分（放在上面）
  .ai-assistant-section.mb-4
    .ai-assistant-container
      .ai-assistant-header
        .ai-assistant-mode-selector
          .mode-selector-header 选择AI模式：
          a-radio-group(
            v-model:value='selectedMode',
            button-style='solid',
            size='small',
            @change='changeModeHandler'
          )
            a-radio-button(v-for='mode in availableModes', :key='mode.key', :value='mode.key')
              .flex.items-center.justify-center
                span.mr-1 {{ mode.icon }}
                span {{ mode.name }}
          .mode-description.mt-2.text-xs.text-gray-600
            span 速度: {{ modeDisplayInfo.speed }}
            span 质量: {{ modeDisplayInfo.quality }}
            span 等待时间: {{ modeDisplayInfo.waitTime }}

      .p-3
        .mb-3
          .text-sm.text-gray-600.mb-2 {{ waitTimeHint }}
          a-textarea(
            ref='inputRef',
            v-model:value='editedPayload',
            placeholder='请输入您的提示词... (Ctrl+Enter 发送)',
            :rows='4',
            style='width: 100%; min-height: 30px',
            @keydown='handleKeyDown',
            :loading='isLoading'
          )
        .flex.gap-2
          a-button(type='primary', :loading='isLoading', @click='callAIService', style='flex: 1')
            template(#icon)
              reload-outlined
            | 调用AI
          .text-right.text-xs.text-gray-500.mt-2(v-if='editedPayload')
            | 字数统计: {{ wordCount }}
          a-button(
            v-if='editedPayload.trim()',
            type='default',
            @click='handleClearContent',
            title='清空所有内容'
          ) 清空
          a-button(v-if='canUndo', type='default', @click='handleUndo', title='撤销上一次操作') 撤销

  // 选中内容显示区域
  .selected-content-section.mb-4(v-if='localValue')
    .bg-gray-50.p-3.rounded.border
      .text-sm.text-gray-600.mb-2 当前选中的内容：
      a-textarea(v-model:value='localValue', :rows='3', placeholder='未选择内容', :disabled='disabled')

  // AI内容选择器部分（放在下面）
  .content-selector-section(v-if='contents && contents.length > 0')
    .mb-2
      .text-sm.text-gray-600 AI生成的内容选项：
    .grid.gap-2
      template(v-for='(content, index) in contents', :key='content')
        a-card.cursor-pointer(
          :class='{ "card-selected": selectedIndex === index }',
          :hoverable='!disabled',
          @click='handleSelect(content, index)'
        )
          .card-header.flex.justify-between.items-center.mb-2
            span.flex.items-center
              img.w-5.h-5(:src='AI_ICON', alt='icon')
            .action-buttons.flex.gap-2(v-if='!disabled')
              a-button.add-example-btn(
                type='primary',
                size='small',
                @click='handleAddAsExample(content, $event)',
                title='引用此内容到输入框'
              ) 引用
              a-button.replace-btn(
                type='default',
                size='small',
                :icon='h(CopyOutlined)',
                @click='handleReplaceContent(content, $event)',
                title='替换输入框内容'
              ) 替换
          .content__card__content(@click='handleContentClick(content, index)') {{ content }}

  // 编辑模态框
  a-modal(v-model:open='visible', title='编辑内容', @ok='onOk', @cancel='onCancel')
    a-textarea(v-model:value='cacheContent', :rows='4')
</template>

<style lang="stylus" scoped>
.bot-pack-ai-content-manager
  .ai-assistant-container
    border 1px solid #e8e8e8
    border-radius 4px
    overflow hidden
  .ai-assistant-mode-selector
    .mode-selector-header
      font-size 13px
      margin-bottom 8px
    .ant-radio-group
      width 100%
    .ant-radio-button-wrapper
      flex 1
      text-align center
      &:first-child
        border-radius 4px 0 0 4px
      &:last-child
        border-radius 0 4px 4px 0
    .mode-description
      line-height 1.4
      span
        display inline-block
        margin-right 8px
        &:last-child
          margin-right 0
  .ai-assistant-header
    padding 8px 12px
    background-color #f5f5f5
  .selected-content-section
    .ant-textarea
      background-color #f9f9f9
      border-color #d9d9d9
  .content-selector-section
    .card-selected
      border 2px solid #1890ff
    .ant-card
      &:hover
        border-color #40a9ff
      &.cursor-pointer
        cursor pointer
      &[disabled]
        cursor not-allowed
        opacity 0.5
    .content__card__content
      cursor pointer
      color #333333
      &:hover
        color #1890ff
        text-decoration underline
      &[contenteditable='true']
        outline none
        padding 2px 4px
        &:focus
          border-bottom 1px solid #1890ff
      .content-preview
        line-height 1.5
      .content-full-indicator
        font-size 12px
        color #999
        margin-top 4px
        font-style italic
    .card-header
      .action-buttons
        opacity 0
        transition opacity 0.2s ease-in-out
        .add-example-btn
          background-color #52c41a
          border-color #52c41a
          color white
          font-weight 500
          transition all 0.3s ease
          &:hover
            background-color #73d13d
            border-color #73d13d
            transform translateY(-1px)
            box-shadow 0 2px 8px rgba(82, 196, 26, 0.3)
          &:active
            transform translateY(0)
            box-shadow 0 1px 4px rgba(82, 196, 26, 0.3)
        .replace-btn
          border-color #d9d9d9
          color #666
          transition all 0.3s ease
          &:hover
            border-color #40a9ff
            color #1890ff
            transform translateY(-1px)
            box-shadow 0 2px 8px rgba(64, 169, 255, 0.2)
          &:active
            transform translateY(0)
            box-shadow 0 1px 4px rgba(64, 169, 255, 0.2)
    .ant-card:hover .action-buttons
      opacity 1
  // 主要操作按钮的增强反馈
  .ai-assistant-section
    .ant-btn
      transition all 0.3s ease
      &.ant-btn-primary
        &:hover
          transform translateY(-1px)
          box-shadow 0 4px 12px rgba(24, 144, 255, 0.3)
        &:active
          transform translateY(0)
          box-shadow 0 2px 6px rgba(24, 144, 255, 0.3)
      &.ant-btn-default
        &:hover
          transform translateY(-1px)
          box-shadow 0 2px 8px rgba(0, 0, 0, 0.1)
        &:active
          transform translateY(0)
          box-shadow 0 1px 4px rgba(0, 0, 0, 0.1)
</style>
