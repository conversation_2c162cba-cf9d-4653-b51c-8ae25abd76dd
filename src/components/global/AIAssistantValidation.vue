<script lang="ts">
import { ServeUserPacksApi } from '@/engines/iest/serve-core/apis/serve/user/packs.api';
import { ServePackModel } from '@/engines/iest/serve-core/models/serve/user/packs';
import { VStore } from '@/lib/vails';
import { ReloadOutlined } from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import { computed, defineComponent, nextTick, onMounted, ref, toRefs } from 'vue';

export default defineComponent({
  name: 'AIAssistantValidation',
  components: {
    ReloadOutlined,
  },
  props: {
    payload: {
      type: Object,
      required: true,
      default: () => ({}),
    },
  },
  setup(props) {
    const isLoading = ref<boolean>(false);
    const inputRef = ref<HTMLElement | null>(null);
    const editedPayload = ref<string>('');
    const deepseekModeInfo = ref<any>(null);
    const selectedMode = ref<string>('deepseek'); // 默认标准模式
    const { payload } = toRefs(props);

    const store = new VStore(new ServeUserPacksApi(), ServePackModel);

    // 可用的DeepSeek模式配置
    const availableModes = ref([
      {
        key: 'deepseek',
        name: 'DeepSeek 标准模式',
        icon: '⚡',
        description: '标准推理模式，速度相对较快',
        timeout: 120,
        speed: '快速',
        quality: '良好',
        waitTime: '快速响应',
      },
      {
        key: 'deepseek_r1',
        name: 'DeepSeek R1 深度推理',
        icon: '🧠',
        description: '深度推理模式，质量更高但速度较慢',
        timeout: 300,
        speed: '较慢',
        quality: '优秀',
        waitTime: '预计等待 1-2 分钟',
      },
    ]);

    // 计算当前DeepSeek模式的超时时间和提示信息
    const modeDisplayInfo = computed(() => {
      // 优先使用前端选择的模式信息，确保显示正确
      const currentMode = availableModes.value.find(mode => mode.key === selectedMode.value);
      return currentMode || availableModes.value[0];
    });

    // 计算预估等待时间提示
    const waitTimeHint = computed(() => {
      return modeDisplayInfo.value.waitTime || '';
    });

    // 安全访问payload属性
    const getSafePayload = () => {
      const value = payload.value || {};
      // 先解构出 payload 和 其他属性
      const { payload: originalPayload, ...rest } = value;

      return {
        id: value.id || '',
        ...rest, // 先展开其他属性
        payload: {
          prompt: value.payload?.prompt || value.rule?.options?.prompt,
          use_ai: false,
          contents: [],
          ...(originalPayload || {}), // 最后合并原始 payload
        },
      };
    };

    // 初始化编辑内容
    const initEditContent = () => {
      const safePayload = getSafePayload();
      editedPayload.value = safePayload.payload.prompt || '';

      // 初始化选择的模式（默认标准模式）
      selectedMode.value = 'deepseek';
    };

    // 切换DeepSeek模式（简化版本，只更新本地状态）
    const changeModeHandler = (newMode: string) => {
      selectedMode.value = newMode;

      // 显示切换成功的消息
      const modeInfo = availableModes.value.find(mode => mode.key === newMode);
      message.success(`已切换到 ${modeInfo?.name}`);
    };

    // 调用AI服务接口
    const callAIService = async () => {
      const safePayload = getSafePayload();

      try {
        isLoading.value = true;

        // 显示带有模式信息的加载提示
        const loadingMessage = `${modeDisplayInfo.value.icon} 正在使用 ${modeDisplayInfo.value.name} 生成内容，${waitTimeHint.value}...`;
        message.info(loadingMessage, 5);

        // 调用AI接口，传递选择的模式
        await store
          .sendMemberAction({
            action: 'refresh_contents_by_rule',
            id: safePayload.id,
            config: {
              data: {
                prompt: editedPayload.value,
                mode: selectedMode.value, // 传递选择的模式
              },
              timeout: (modeDisplayInfo.value.timeout + 30) * 1000, // 添加30秒缓冲时间
            },
          })
          .then(res => {
            if (res?.data?.contents) {
              payload.value.payload.contents = res.data.contents;

              // 更新DeepSeek模式信息
              if (res.data.deepseek_mode_info) {
                deepseekModeInfo.value = res.data.deepseek_mode_info;
              }

              // 显示成功信息，包含生成时间
              let successMsg = 'AI内容生成成功';
              if (res.data.generation_time) {
                successMsg += `，耗时 ${res.data.generation_time} 秒`;
              }
              message.success(successMsg);
            }
          });
      } catch (error) {
        console.error('AI服务调用失败:', error);
        if (error instanceof Error && error.message?.includes('timeout')) {
          message.error(`${modeDisplayInfo.value.name} 响应超时，请稍后重试`);
        } else {
          message.error('AI服务调用失败，请检查网络连接');
        }
      } finally {
        isLoading.value = false;
      }
    };

    // 组件挂载后初始化输入框内容
    onMounted(() => {
      initEditContent();
      nextTick(() => {
        inputRef.value?.focus();
      });
    });

    return {
      isLoading,
      inputRef,
      editedPayload,
      callAIService,
      modeDisplayInfo,
      waitTimeHint,
      availableModes,
      selectedMode,
      changeModeHandler,
    };
  },
});
</script>

<template lang="pug">
.ai-assistant-validation
  .ai-assistant-container(v-if='payload?.payload?.use_ai || payload?.use_ai')
    .ai-assistant-mode-selector(style='padding: 12px; background-color: #fafafa; border-bottom: 1px solid #e8e8e8;')
      .mode-selector-header(style='margin-bottom: 8px; font-weight: 500; color: #333;') DeepSeek 模式选择
      a-radio-group(v-model:value='selectedMode', @change='(e) => changeModeHandler(e.target.value)', size='small')
        a-radio-button(v-for='mode in availableModes', :key='mode.key', :value='mode.key')
          span {{ mode.icon }} {{ mode.name }}
      .mode-description(style='margin-top: 8px; font-size: 12px; color: #666;')
        span {{ modeDisplayInfo.description }}
    .ai-assistant-header
      a-space(direction='vertical', style='width: 100%')
        a-textarea(
          ref='inputRef',
          v-model:value='editedPayload',
          placeholder='请输入提示词...',
          :auto-size='{ minRows: 1, maxRows: 4 }',
          style='width: 100%; min-height: 30px',
          @press-enter='callAIService',
          :loading='isLoading'
        )
        a-button(type='primary', :loading='isLoading', @click='callAIService', style='width: 100%')
          template(#icon)
            reload-outlined
          | 调用AI
</template>

<style lang="stylus" scoped>
.ai-assistant-container
  border 1px solid #e8e8e8
  border-radius 4px
  margin-bottom 16px
  overflow hidden

.ai-assistant-mode-selector
  .mode-selector-header
    font-size 13px
    margin-bottom 8px

  .ant-radio-group
    width 100%

  .ant-radio-button-wrapper
    flex 1
    text-align center

    &:first-child
      border-radius 4px 0 0 4px

    &:last-child
      border-radius 0 4px 4px 0

  .mode-description
    line-height 1.4

    span
      display inline-block
      margin-right 8px

      &:last-child
        margin-right 0

.ai-assistant-header
  padding 8px 12px
  background-color #f5f5f5
</style>
