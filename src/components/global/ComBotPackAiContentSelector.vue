<script lang='ts'>
import { ref, defineComponent, toRefs, computed } from 'vue';

const ComBotPackAiContentSelector = defineComponent({
  name: 'ComBotPackAiContentSelector',
  components: {},
  props: {
    value: { type: String, default: undefined },
    payload: { type: Object, default: () => ({}) },
    disabled: { type: Boolean, default: false },
  },
  setup(props, { emit }) {
    const AI_ICON = 'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iest/ai_summary.png'
    const contents = computed(() => props.payload?.payload?.contents)
    const localValue = computed({
      get() {
        return props.value
      },
      set(value) {
        emit('update:value', value)
      }
    })

    const selectedIndex = computed(() => {
      return contents.value?.findIndex((content: string) => content === localValue.value) || 0
    })

    const handleSelect = (content: string, index: number) => {
      if (props.disabled) return
      localValue.value = content
    }

    const visible = ref(false)
    const cacheContent = ref('')
    const handleContentClick = (content: string, index: number) => {
      if (props.disabled || selectedIndex.value !== index) return
      visible.value = true
      cacheContent.value = content
    }

    const onOk = () => {
      visible.value = false
      localValue.value = cacheContent.value
    }

    const onCancel = () => {
      visible.value = false
    }
    return {
      ...toRefs(props),
      contents,
      selectedIndex,
      handleSelect,
      localValue,
      AI_ICON,
      handleContentClick,
      visible,
      cacheContent,
      onOk,
      onCancel,
    };
  },
});
export default ComBotPackAiContentSelector;
</script>

<template lang="pug">
.com-bot-pack-ai-content-selector
  a-modal(
    v-model:visible='visible'
    title='AI内容'
    @ok='onOk'
    @cancel='onCancel'
  )
    a-textarea(
      v-model:value='cacheContent'
      :auto-size='{ minRows: 5, maxRows: 10 }'
    )
  .ai__content(v-if='disabled') {{ localValue}}
  .ai__content__edit(v-else)
    .mb-2
      a-textarea.ai__content(
        v-model:value='localValue'
        :disabled='disabled'
        :auto-size='{ minRows: 5, maxRows: 10 }'
      )
    .grid.gap-2
      template(v-for='(content, index) in contents' :key='content')
        a-card.cursor-pointer(
          :class="{ 'card-selected': selectedIndex === index }"
          :hoverable="!disabled"
          @click="handleSelect(content,index)"
        )
          span.mb-1
            img.w-5.h-5(:src="AI_ICON" alt="icon")
          .content__card__content(@click='handleContentClick(content,index)') {{ content}}
</template>

<style lang="stylus" scoped>
.com-bot-pack-ai-content-selector
  .card-selected
    border 2px solid #1890ff

  .ant-card
    &:hover
      border-color #40a9ff

    &.cursor-pointer
      cursor pointer

    &[disabled]
      cursor not-allowed
      opacity 0.5

  .content__card__content
    &[contenteditable="true"]
      outline none
      padding 2px 4px

      &:focus
        border-bottom 1px solid #1890ff
</style>
