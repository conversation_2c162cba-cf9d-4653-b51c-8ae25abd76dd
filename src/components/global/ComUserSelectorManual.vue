<script lang="ts">
import { ref, defineComponent, toRefs, computed } from 'vue';
import { VObject } from '../../lib/vails/model/index';
import { uniqBy } from 'lodash-es';

const ComUserSelectorManual = defineComponent({
  name: 'ComUserSelectorManual',
  components: {},
  props: {
    redisKey: { type: String, default: '' },
    manualStore: { type: Object, default: () => ({}) },
    manualRedisSourcesApi: { type: Object, default: () => ({}) },
    query: { type: Object, default: () => ({}) },
  },
  setup(props, { emit }) {
    const visible = ref(false);
    const localAddedSelectedRecords = ref<VObject[]>([]);
    const localRemovedSelectedRecords = ref<VObject[]>([]);
    const redisSelectedRecords = ref<VObject[]>([]);

    const onOpen = () => {
      reset();
      visible.value = true;
    };

    const columns = [
      {
        title: '姓名',
        type: 'string',
        dataIndex: 'name',
      },
      {
        title: '账号',
        type: 'string',
        dataIndex: 'account',
      },
      {
        title: '岗位',
        type: 'string',
        dataIndex: 'member_pos_job',
      },
      {
        title: '部门',
        type: 'string',
        dataIndex: 'department_names',
      },
    ];

    const config = computed(() => ({
      store: props.manualStore,
      mode: 'table',
      table: {
        columns,
      },
      params: { q: props.query },
      selection: {
        showByDefault: true,
      },
      searcherSimpleOptions: [
        {
          key: 'name',
          label: '姓名',
          type: 'string',
        },
        {
          key: 'account',
          label: '账号',
          type: 'string',
        },
      ],
    }));

    const onIndex = (data: VObject) => {
      fetchRedisResourcesExistsIds(data.records);
    };

    const selectedRecords = computed(() => {
      const allSelected = [
        ...redisSelectedRecords.value,
        ...localAddedSelectedRecords.value,
      ].filter(item => {
        return !localRemovedSelectedRecords.value.some(removed => removed.id === item.id);
      });
      return uniqBy(allSelected, 'id');
    });

    const fetchRedisResourcesExistsIds = async (records: VObject[]) => {
      if (!props.redisKey) return;
      const { data } = await props.manualRedisSourcesApi.index({
        redis_key: props.redisKey,
        per_page: 1000,
        q: {
          id_in: records.map(item => item.id),
        },
      });
      redisSelectedRecords.value = data.records || [];
    };

    const onSelectedAdd = (record: VObject) => {
      localAddedSelectedRecords.value.push(record);
      localRemovedSelectedRecords.value = localRemovedSelectedRecords.value.filter(
        item => item.id !== record.id,
      );
    };

    const onSelectedRemove = (record: VObject) => {
      localRemovedSelectedRecords.value.push(record);
      localAddedSelectedRecords.value = localAddedSelectedRecords.value.filter(
        item => item.id !== record.id,
      );
    };

    const onOk = async () => {
      if (localAddedSelectedRecords.value.length > 0) {
        await props.manualRedisSourcesApi.create(
          {
            ids: localAddedSelectedRecords.value.map(item => item.id),
          },
          {
            params: {
              redis_key: props.redisKey,
            },
          },
        );
      }

      if (localRemovedSelectedRecords.value.length > 0) {
        await props.manualRedisSourcesApi.sendCollectionAction('batch_destroy', {
          params: {
            redis_key: props.redisKey,
            redis_resources: { ids: localRemovedSelectedRecords.value.map(item => item.id) },
          },
        });
      }
      reset();
      visible.value = false;
      emit('refresh');
    };

    const reset = () => {
      localAddedSelectedRecords.value = [];
      localRemovedSelectedRecords.value = [];
      redisSelectedRecords.value = [];
    };

    return {
      ...toRefs(props),
      onOpen,
      config,
      visible,
      selectedRecords,
      onIndex,
      onSelectedAdd,
      onSelectedRemove,
      onOk,
      // onSelectedRecordsChange,
    };
  },
});
export default ComUserSelectorManual;
</script>

<template lang="pug">
.com-user-selector-manual.flex.items-center
  TaTextButton(
    icon='EditOutlined',
    @click='onOpen'
  )
    | 手动选择

  TaNoPaddingModal(
    v-model:visible='visible',
    v-if='visible',
    width='60wv'
    @ok='onOk'
  )
    .p-4
      slot(name='actions')
      TaIndexView(
        :config='config'
        :selectedRecords='selectedRecords'
        @selectedAdd='onSelectedAdd'
        @selectedRemove='onSelectedRemove'
        @onIndex='onIndex'
      )
        template(#header)
          .flex.items-center.justify-between
            | 已选中 {{ selectedRecords.length }} 人
</template>

<style lang="stylus" scoped></style>
