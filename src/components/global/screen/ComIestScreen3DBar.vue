<script lang='ts'>
import { ref, defineComponent, toRefs, computed } from 'vue';
import * as echarts from 'echarts';
const ComIestScreen3DBar = defineComponent({
  name: 'ComIestScreen3DBar',
  components: {},
  props: {
    data: { type: Array, default: () => [] }
  },
  setup(props) {
    const echartsRef = ref<any>(null);
    const xData = computed(() => props.data.map((item: any) => item.name))
    const barOption = computed(() => ({
      grid: {
        top: '5%',
        bottom: '18%',
        left: '10%',
        right: '5%',
      },
      xAxis: {
        type: 'category',
        data: xData.value,
        boundaryGap: true,
        nameTextStyle: {
          color: 'white'
        },
        axisLabel: {
          formatter: '{value}',
          interval: 0,
          rotate: 25,
          color: 'white'
        },
        axisTick: {
          show: false,
          alignWithLabel: true,
        }
      },
      yAxis: {
        type: 'value',
        nameTextStyle: {
          color: 'white'
        },
        axisLabel: {
          color: 'white'
        },
        splitLine: {
          show: false
        },
      },
      series: [
        {
          data: props.data,
          type: 'bar',
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#2378f7' },
              { offset: 0.7, color: '#2378f7' },
              { offset: 1, color: '#83bff6' }
            ])
          }
        }
      ]
    }))
    return {
      ...toRefs(props),
      echartsRef,
      barOption,
    };
  },
});
export default ComIestScreen3DBar;
</script>

<template lang="pug">
.w-full.h-full
  TaScreenEchartBase(:options='barOption' ref='echartsRef')
</template>

<style lang="stylus" scoped></style>
