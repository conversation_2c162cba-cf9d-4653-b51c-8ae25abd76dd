<script lang='ts'>
import { ref, defineComponent, toRefs, watch, computed, ComputedRef, nextTick } from 'vue';
import { useContextInject } from '../ta-component/ta-template-form-core/useContext';
import { useFetchScreenData, useScreenDataFetchCollectionInject } from '../ta-component/TaBuilder/builder/useScreenDataFetchCollection';
import ComIestScreenBar from './ComIestScreenBar.vue';

const ComIestScreenBiddingRuleByOrg = defineComponent({
  name: 'ComIestScreenBiddingRuleByOrg',
  components: { ComIestScreenBar },
  props: {
    records: {
      type: Array,
      default: () => []
    }
  },
  setup(props) {
    const { context } = useContextInject();
    const { refreshAll, refreshCurrent } = useFetchScreenData();
    const { dataResult, dataSourceCollection } = useScreenDataFetchCollectionInject({});
    console.log(dataResult)
    const activeId = ref<number>(0)
    const computedRecords = computed(() => props.records)
    const activeRecord: ComputedRef<any> = computed(() => computedRecords.value?.find((record: any) => record.id === activeId.value))

    const barState = ref<any>([])
    const computedBarData = computed(() => {
      return barState.value?.map((item: any) => ({
        name: item.name,
        value: item.finished_packs_count,
      }))
    })

    watch(computedRecords, (newVal: any, oldVal) => {
      if (oldVal.length === 0 && newVal.length > 0) {
        activeId.value = newVal[0].id
      }
    })

    let timer: any = null
    watch(activeId, (newVal: any, oldVal) => {
      if (activeId.value) {
        context.activeRuleId = activeId.value
        if (oldVal !== newVal) {
          const _id = dataSourceCollection.value?.find((item: any) => item.name === "a")?._id
          if (_id && dataResult?.value) {
            if (timer) {
              clearTimeout(timer)
            }
            timer = setTimeout(() => {
              const dataMapByOrg = dataResult.value?.[_id]?.规则?.区域
              barState.value = dataMapByOrg?.[activeRecord.value?.name]
              console.log(barState.value)
            }, 1000)

          }
        }
      }
    }, { immediate: true, deep: true })

    return {
      ...toRefs(props),
      dataResult,
      computedBarData,
    };
  },
});
export default ComIestScreenBiddingRuleByOrg;
</script>

<template lang="pug">
.com-iest-screen-bidding-rule-by-org
  ComIestScreenBar(:data='computedBarData')
</template>

<style lang="stylus" scoped>
</style>
