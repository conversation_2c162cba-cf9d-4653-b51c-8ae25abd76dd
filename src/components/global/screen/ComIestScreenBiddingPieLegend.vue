<script lang='ts'>
import { VObject } from '@/lib/vails';
import { usePreviewClickable } from '../ta-component/TaBuilder/builder/usePreview';
import { ref, defineComponent, toRefs } from 'vue';

const colors = [
  '#5470c6',
  '#91cc75',
  '#fac858',
  '#ee6666',
  '#73c0de',
  '#3ba272',
  '#fc8452',
  '#9a60b4',
  '#ea7ccc',
  '#844CDB',
  '#D07EF8',
];
const ComIestScreenBiddingPieLegend = defineComponent({
  name: 'ComIestScreenBiddingPieLegend',
  components: {},
  props: {
    records: {
      type: Array,
      default: () => []
    },
    colors: {
      type: Array,
      default: () => colors
    }
  },
  setup(props) {
    const { onClick } = usePreviewClickable(props)
    const onOpenPackIndexScreenModal = (record: VObject) => {
      const event = { mode: 'page_setting' } as { mode: 'page_setting' }
      const eventExtra = {
        page_setting_id: 10
      }
      onClick(
        event,
        {
          params: {
            rule_id_eq: record.id,
          }
        },
        eventExtra
      )
    };
    return {
      ...toRefs(props),
      onOpenPackIndexScreenModal,
    };
  },
});
export default ComIestScreenBiddingPieLegend;
</script>

<template lang="pug">
.com-iest-screen-bidding-pie-legend.space-y-2.flex.flex-col.justify-center
  template(v-for="(record, index) in records")
    .legend.flex.items-center.cursor-pointer(
      @click.stop='onOpenPackIndexScreenModal(record)'
    )
      .w-7px.h-7px.mr-1.flex-shrink-0(:style='`background-color: ${colors[index]}`')
      span.text-xs.text-white.pr-1 {{ record.name }}
      span.num.text-sm.pr-1.flex-shrink-0 {{ record.total_packs_count }}
      span.unit.flex-shrink-0 批次
</template>

<style lang="stylus" scoped>
.com-iest-screen-bidding-pie-legend
  .legend:hover span
    text-shadow 0 0 10px rgba(255,255,255, 0.80)
  .num
    font-family "DIN Alternate"
    font-weight 700
    background linear-gradient(180deg, #1CECCC 0%, #00E1FF 52.08%, #2A5BFD 97.92%)
    background-clip text
    -webkit-background-clip text
    -webkit-text-fill-color transparent
  .unit
    color rgba(255, 255, 255, 0.80)
    font-family "PingFang SC"
    font-size 10px
    font-weight 300
    line-height normal
</style>
