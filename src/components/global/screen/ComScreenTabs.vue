<script lang='ts'>
import { ref, defineComponent, toRefs, PropType } from 'vue';
import { VObject } from '../../../lib/vails/model/index';
const ComScreenTabs = defineComponent({
  name: 'ComScreenTabs',
  components: {},
  props: {
    tabs: {
      type: Array as PropType<VObject[]>,
      default: () => [
        {
          title: '消息批次',
          key: 'pack',
        },
        {
          title: '消息记录',
          key: 'message',
        }
      ],
    },
  },
  setup(props, { emit }) {
    const activeTabKey = ref(props.tabs[0].key);
    const changeTab = (tab: any) => {
      activeTabKey.value = tab.key
      emit('onChange', tab)
    }
    return {
      ...toRefs(props),
      activeTabKey,
      changeTab,
    };
  },
});
export default ComScreenTabs;
</script>

<template lang="pug">
.com-screen-tabs.h-7.grid.mx-4.mb-6px
  .tab.flex.items-center.justify-center.relative.cursor-pointer(
    v-for='tab in tabs',
    :class="{'tab__active':tab.key === activeTabKey}",
    @click='changeTab(tab)'
  )
    .tab-text.text-white {{ tab.title }}

</template>

<style lang="stylus" scoped>
.com-screen-tabs
  border 1px solid transparent
  border-image linear-gradient(to bottom, white, transparent) 1 100%
  border-bottom 0
  border-top 0
  box-shadow 0 -1px 0 white
  .tab
    background linear-gradient(180deg,
              rgba(92, 165, 232, 0.30) 0%,
              rgba(41, 117, 188, 0.30) 23.96%,
              rgba(22, 75, 157, 0.00) 100%)
    .tab-text
      font-family 'PingFang SC'
      font-size 14px
      font-style normal
      font-weight 400
      line-height normal
  .tab:not(last-child)
    border-right 1px solid transparent
    border-image linear-gradient(to bottom, white, transparent) 1 100%
  .tab__active
    box-shadow 0px 4px 4px 0px rgba(0, 225, 255, 0.34) inset
    &::after
      content ''
      position absolute
      top 0
      width 42px
      height 7px
      background-size cover
      filter blur(.5px)
      background-image url(https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iot/activetab.png)

</style>
