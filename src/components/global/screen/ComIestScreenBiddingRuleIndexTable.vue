<script lang='ts'>
import { ref, defineComponent, toRefs, onMounted } from 'vue';
import ComScreenScrollTableWithMarquee from './ComScreenScrollTableWithMarquee.vue';
import { VObject, VStore } from '@/lib/vails';
import { ServeManageRulesApi } from '@/engines/iest/serve-core/apis/serve/manage/rules.api';
import { ServeRuleModel } from '@/engines/iest/serve-core/models/serve/manage/rules';
import dayjs from 'dayjs';
import { usePreviewClickable } from '../ta-component/TaBuilder/builder/usePreview';

const ComIestScreenBiddingRuleIndexTable = defineComponent({
  name: 'ComIestScreenBiddingRuleIndexTable',
  components: { ComScreenScrollTableWithMarquee },
  setup(props) {
    const ruleStore = new VStore(new ServeManageRulesApi(), ServeRuleModel)
    onMounted(() => {
      ruleStore.index({
        per_page: 99, q: {
          type_eq: 'Serve::BidRule'
        }
      })
    })
    const cols = [
      {
        label: '规则名称',
        prop: 'name',
        width: '20%',
      },
      {
        label: '区域',
        prop: 'orgs',
        width: '20%',
        justify: 'center',
      },
      {
        label: '最近发送时间',
        prop: 'latest_send_at',
        width: '20%',
        justify: 'center',
      },
      {
        label: '发送批次',
        prop: 'total_packs_count',
        width: '20%',
        justify: 'center',
      },
      {
        label: '待审核数',
        prop: 'processing_packs_count',
        width: '20%',
        justify: 'center',
      }
    ];

    const { onClick } = usePreviewClickable(props)
    const openPackInstanceScreenModal = (record: VObject) => {
      const event = { mode: 'page_setting' } as { mode: 'page_setting' }
      const eventExtra = {
        page_setting_id: 5
      }
      onClick(
        event,
        {
          params: {
            "flowable_of_Serve::Pack_type_rule_id_eq": record.id,
            state_in: ["created", "preparing", "processing"]
          }
        },
        eventExtra
      )
    }

    const openPackIndexScreenModal = (record: VObject) => {
      const event = { mode: 'page_setting' } as { mode: 'page_setting' }
      const eventExtra = {
        page_setting_id: 10
      }
      onClick(
        event,
        {
          params: {
            rule_id_eq: record.id,
          }
        },
        eventExtra
      )
    }

    return {
      ...toRefs(props),
      cols,
      records: ruleStore.records,
      openPackInstanceScreenModal,
      openPackIndexScreenModal,
      dayjs,
    };
  },
});
export default ComIestScreenBiddingRuleIndexTable;
</script>

<template lang="pug">
.com-screen-rule-index-table.p-3
  ComScreenScrollTableWithMarquee(
    :records='records',
    :cols='cols'
  )
    template(#header='{cols}')
      .text-sm.flex.items-center.px-2.py-2(
        v-for='col in cols',
        :style='`width:${col.width||"initial"};justify-content:${col.justify||"start"};color:#37E8FF;`'
      ) {{ col.label }}
    template(#cell='{value,label,record,colStyle}')
      .w-full.truncate(
        v-if='label === "orgs"',
        :style='colStyle'
      ) {{ value?.map((org) => org.name)?.join(',') }}
      .text-sm.cursor-pointer(
        v-else-if='label === "processing_packs_count"',
        :style='colStyle'
         @click.stop='openPackInstanceScreenModal(record)'
      ) {{ value }}
      .w-full.truncate(
        v-else-if='label === "latest_send_at"',
        :style='colStyle',

      ) {{ value?dayjs(value).format('YYYY-MM-DD'):'-' }}
      .text-sm.cursor-pointer(
        v-else-if='label === "total_packs_count"',
        :style='colStyle'
         @click.stop='openPackIndexScreenModal(record)'
      ) {{ value }}
</template>

<style lang="stylus" scoped></style>
