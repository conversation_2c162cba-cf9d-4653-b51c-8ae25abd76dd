<script lang='ts'>
import { ref, defineComponent, toRefs, computed } from 'vue';

const ComIestScreenStackBar = defineComponent({
  name: 'ComIestScreenStackBar',
  components: {},
  props: {
    aData: { type: Array, default: () => [] },
    bData: { type: Array, default: () => [] },
  },
  setup(props) {
    const echartsRef = ref<any>(null);
    const xData = computed(() => (props.aData || [])?.map((item: any) => item.start_day)?.reverse())
    const computedAData = computed(() => (props.aData || [])?.map((item: any) => item.acitivities_count)?.reverse())
    const computedBData = computed(() => (props.bData || [])?.map((item: any) => item.acitivities_count)?.reverse())

    const barOption = computed(() => ({
      legend: {
        bottom: 'bottom',
        textStyle: {
          color: 'white'
        }
      },
      grid: {
        top: '4%',
        bottom: '20%'
      },
      color: ['#3F83F8', '#16BDCA'],
      xAxis: {
        type: 'category',
        data: xData.value,
        axisLabel: {
          color: 'white'
        },
        axisPointer: {
          type: 'shadow'
        },

      },
      yAxis: {
        type: 'value',
        axisLabel: {
          color: 'white'
        },
        splitLine: {
          lineStyle: {
            type: 'dashed'
          }
        }
      },
      series: [
        {
          data: computedAData.value,
          type: 'bar',
          stack: 'a',
          name: '网站'
        },
        {
          data: computedBData.value,
          type: 'bar',
          stack: 'a',
          name: '公众号'
        }
      ]
    }))
    return {
      ...toRefs(props),
      echartsRef,
      barOption,
      computedAData,
    };
  },
});
export default ComIestScreenStackBar;
</script>

<template lang="pug">
.com-iest-screen-stack-bar.w-full.h-full
  TaScreenEchartBase(:options='barOption' ref='echartsRef')
</template>

<style lang="stylus" scoped></style>
