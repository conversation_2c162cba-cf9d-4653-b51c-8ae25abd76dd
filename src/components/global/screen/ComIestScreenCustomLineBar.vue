<script lang='ts'>
import { ref, defineComponent, toRefs, computed } from 'vue';
import * as echarts from 'echarts';
const ComIestScreenCustomLineBar = defineComponent({
  name: 'ComIestScreenCustomLineBar',
  components: {},
  props: {
    msgTrend: {
      type: Array, default: () => [
        { value: 0, name: '暂无数据' }
      ]
    },
    packTrend: {
      type: Array, default: () => [
        { value: 0, name: '暂无数据' }
      ]
    }
  },
  setup(props) {
    const echartsRef = ref<any>(null);
    const xData = computed(() => props.msgTrend?.map((item: any) => item.cweek));
    const lineData = computed(() => props.packTrend?.map((item: any) => item.pack_count));
    const barData = computed(() => props.msgTrend?.map((item: any) => item.pack_count));
    const lineBarOption = computed(() => ({
      grid: {
        left: '10%',
        right: '10%',
        bottom: '10%',
        top: '15%',
      },
      xAxis: [
        {
          type: 'category',
          axisPointer: {
            type: 'shadow'
          },
          axisLabel: {
            formatter: '{value}周',
            color: 'white'
          },
          data: xData.value?.reverse(),
        }
      ],
      yAxis: [
        {
          type: 'value',
          min: 0,
          max: Math.max(...(barData.value as any)),
          name: '消息数量(条)',
          nameTextStyle: {
            color: '#D1D5DB'
          },
          axisLabel: {
            color: '#D1D5DB'
          },
          splitLine: {
            lineStyle: {
              type: 'dashed'
            }

          }
        },
        {
          type: 'value',
          min: 0,
          max: Math.max(...(lineData.value as any)),
          name: '消息批次(次)',
          nameTextStyle: {
            color: '#D1D5DB'
          },
          axisLabel: {
            color: '#D1D5DB'
          },
          splitLine: {
            show: false,
          }
        }
      ],
      series: [
        {
          name: '消息数量',
          type: 'bar',
          itemStyle: {
            color: '#3F83F8'
          },
          barWidth: '40',
          data: barData.value.reverse()
        },
        {
          name: '消息批次',
          type: 'line',
          symbol: 'none',
          lineStyle: {
            color: '#16BDCA'
          },
          data: lineData.value.reverse()
        },
      ]
    }))
    return {
      ...toRefs(props),
      echartsRef,
      lineBarOption,
    };
  },
});
export default ComIestScreenCustomLineBar;
</script>

<template lang="pug">
.w-full.h-full
  TaScreenEchartBase(:options='lineBarOption' ref='echartsRef')
</template>

<style lang="stylus" scoped></style>
