<script lang='ts'>
import { ref, defineComponent, toRefs } from 'vue';
import { useContextInject } from '../ta-component/ta-template-form-core/useContext';
import { useFetchScreenData } from '../ta-component/TaBuilder/builder/useScreenDataFetchCollection';

const ComIestStatisticTabChange = defineComponent({
  name: 'ComIestStatisticTabChange',
  components: {},
  setup(props) {
    const { context } = useContextInject();
    const { refreshAll } = useFetchScreenData();

    const tabs = [
      { label: '全部', key: 'all' },
      { label: '2024', key: '2024' },
      { label: '2025', key: '2025' },
    ]
    const activeTabKey = ref(tabs[0].key)
    const onTabChange = (key: string) => {
      //edit context
      activeTabKey.value = key
      refreshAll()
    }
    return {
      ...toRefs(props),
      tabs,
      activeTabKey,
      onTabChange,
    };
  },
});
export default ComIestStatisticTabChange;
</script>

<template lang="pug">
.com-iest-statistic-tab-change.grid.grid-cols-3.gap-5
  template(v-for='tab in tabs')
    .tab.flex.items-center.justify-center.text-base.bg-cover.transition-all.cursor-pointer(
      :class='{ "active__tab": tab.key === activeTabKey }'
      @click='onTabChange(tab.key)'
    ) {{ tab.label }}
</template>

<style lang="stylus" scoped>
.com-iest-statistic-tab-change .tab
  color #1FC6FF
  background-image url(https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iest/year_tab.png)
.com-iest-statistic-tab-change .active__tab
  color white
  background-image url(https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iest/active_tab.png)
</style>
