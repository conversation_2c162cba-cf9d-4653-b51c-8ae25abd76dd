<script lang='ts'>
import { ref, defineComponent, toRefs, onMounted } from 'vue';
import ComScreenTabs from './ComScreenTabs.vue';
import ComScreenScrollTableWithMarquee from './ComScreenScrollTableWithMarquee.vue';
import { ServeManagePacksApi } from '../../../engines/iest/serve-core/apis/serve/manage/packs.api';
import { ServePackModel } from '@/engines/iest/serve-core/models/serve/manage/packs';
import { VObject, VStore } from '@/lib/vails';
import { ServeManageMessagesApi } from '@/engines/iest/serve-core/apis/serve/manage/messages.api';
import { ServeMessageModel } from '@/engines/iest/serve-core/models/serve/manage/messages';
import { usePreviewClickable } from '../ta-component/TaBuilder/builder/usePreview';

const ComIestScreenBiddingPackAndMessageIndexTable = defineComponent({
  name: 'ComIestScreenBiddingPackAndMessageIndexTable',
  components: { ComScreenTabs, ComScreenScrollTableWithMarquee },
  setup(props) {
    const tabs: { key: 'pack' | 'message', title: string }[] = [
      {
        title: '消息批次',
        key: 'pack',
      },
      {
        title: '消息记录',
        key: 'message',
      }
    ]
    const activeTabKey = ref<'pack' | 'message'>(tabs[0].key)

    const packStore = new VStore(new ServeManagePacksApi({
      params: {
        q: {
          rule_type_eq: 'Serve::BidRule',
        }
      }
    }), ServePackModel)
    const msgStore = new VStore(new ServeManageMessagesApi({
      params: {
        q: {
          rule_type_eq: 'Serve::BidRule',
        }
      }
    }), ServeMessageModel)

    const requestMap: Record<'pack' | 'message', () => void> = {
      pack: () => packStore.index({ per_page: 30 }),
      message: () => msgStore.index({ per_page: 30 }),
    }
    onMounted(() => {
      requestMap[activeTabKey.value]()
    })
    const handleTabChange = (tab: any) => {
      activeTabKey.value = tab.key
      requestMap[activeTabKey.value]()
    }

    const packCols = [
      {
        label: '素材名称',
        prop: 'activity.name',
        width: '20%',
      },
      {
        label: '区域',
        prop: 'org_name',
        width: '20%',
        justify: 'center',
      },
      {
        label: '发送人',
        prop: 'creator.name',
        width: '20%',
        justify: 'center',
      },
      {
        label: '发送人数',
        prop: 'send_users_count',
        width: '20%',
        justify: 'center',
      },
      {
        label: '未读消息',
        prop: 'unread_count',
        width: '20%',
        justify: 'center',
      }
    ];
    const msgCols = [
      {
        label: '素材名称',
        prop: 'pack.activity.name',
        width: '20%',
      },
      {
        label: '发送人',
        prop: 'sender.name',
        width: '20%',
        justify: 'center',
      },
      {
        label: '接收人',
        prop: 'user.name',
        width: '20%',
        justify: 'center',
      },
      {
        label: '是否已读',
        prop: 'is_read',
        width: '20%',
        justify: 'center',
      },
      {
        label: '已读时间',
        prop: 'read_at',
        width: '20%',
        justify: 'center',
      }
    ];

    const { onClick } = usePreviewClickable(props)
    const openMsgIndexScreenModal = (record: VObject) => {
      const event = { mode: 'page_setting' } as { mode: 'page_setting' }
      const eventExtra = {
        page_setting_id: 12
      }
      onClick(
        event,
        {
          params: {
            pack_id_eq: record.id,
            is_read_false: 1,
          }
        },
        eventExtra
      )
    }
    const openMsgShowScreenModal = (record: VObject) => {
      const event = { mode: 'page_setting' } as { mode: 'page_setting' }
      const eventExtra = {
        page_setting_id: 14
      }
      onClick(
        event,
        {
          params: {
            messageId: record.id
          }
        },
        eventExtra
      )
    }
    return {
      ...toRefs(props),
      activeTabKey,
      handleTabChange,
      msgs: msgStore.records,
      packs: packStore.records,
      packCols,
      msgCols,
      openMsgIndexScreenModal,
      openMsgShowScreenModal,
    };
  },
});
export default ComIestScreenBiddingPackAndMessageIndexTable;
</script>

<template lang="pug">
.com-iest-screen-pack-and-message-index-table.flex.flex-col
  header.flex.justify-end.h-30px
    ComScreenTabs.grid-cols-2.w-176px(@onChange='handleTabChange')
  section.flex-grow.h-0.overflow-hidden.px-3.py-2
    ComScreenScrollTableWithMarquee(
      v-if='activeTabKey === "pack"',
      :records='packs',
      :cols='packCols'
    )
      template(#header='{cols}')
        .text-sm.flex.items-center.px-2.py-2(
          v-for='col in cols',
          :style='`width:${col.width||"initial"};justify-content:${col.justify||"start"};color:#37E8FF;`'
        ) {{ col.label }}
      template(#cell='{value,label,colStyle,record}')
        .text-sm.cursor-pointer(
          v-if='label === "unread_count"',
          :style='colStyle',
          @click.stop='openMsgIndexScreenModal(record)'
        ) {{ value }}
    ComScreenScrollTableWithMarquee(
      v-else,
      :records='msgs',
      :cols='msgCols'
      @onShow='openMsgShowScreenModal'
    )
      template(#header='{cols}')
        .text-sm.flex.items-center.px-2.py-2(
          v-for='col in cols',
          :style='`width:${col.width||"initial"};justify-content:${col.justify||"start"};color:#37E8FF;`'
        ) {{ col.label }}

</template>

<style lang="stylus" scoped>
.com-iest-screen-pack-and-message-index-table
  :deep(.scroll__col) > div
    padding 8px

</style>
