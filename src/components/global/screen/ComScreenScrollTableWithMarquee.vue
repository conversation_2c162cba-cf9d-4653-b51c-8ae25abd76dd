<script lang="ts">
import { defineComponent, toRefs } from 'vue';
import { Vue3Marquee } from 'vue3-marquee';
import { get } from 'lodash-es';
import { VObject } from '@/lib/vails';
import dayjs from 'dayjs';
const ComScreenScrollTableWithMarquee = defineComponent({
  name: 'ComScreenScrollTableWithMarquee',
  components: { Vue3Marquee },
  props: {
    records: { type: Array, default: () => [] },
    cols: {
      type: Array,
      default: () => [],
    },
  },
  setup(props, { emit }) {
    const getValue = (record: VObject, prop: string): any => {
      const data = get(record, prop);
      if (typeof data === 'boolean') {
        return data ? '是' : '否';
      }

      if (isNaN(data) && !isNaN(Date.parse(data))) {
        return dayjs(data).format('YY-MM-DD')
      }

      return get(record, prop);
    }

    const onShow = (record: VObject) => {
      emit('onShow', record)
    }
    return {
      ...toRefs(props),
      get,
      getValue,
      onShow,
    };
  },
});
export default ComScreenScrollTableWithMarquee;
</script>

<template lang="pug">
.com-screen-scroll-table-with-marquee.w-full.h-full.overflow-hidden.flex.flex-col
  .fix__header.w-full.flex
    slot(
      name='header',
      :cols='cols',
    )
      .text-sm.flex.items-center.px-2(
        v-for='col in cols',
        :style='`width:${col.width||"initial"};justify-content:${col.justify||"start"}`'
      ) {{ col.label }}
  Vue3Marquee.w-full.h-full(
    vertical,
    clone,
    :pauseOnHover='true',
    :duration='records.length * 1.2',
    v-if='records.length>6'
  )
    .scroll__col.w-full.flex(v-for='record in records')
      .text-sm.flex.items-center.px-2.overflow-hidden(
        v-for='col in cols',
        @click='onShow(record)'
        :style='`width:${col.width||"initial"};justify-content:${col.justify||"start"}`'
      )
        slot(
          name='cell',
          :value='get(record,col.prop)',
          :label='col.prop',
          :colStyle='`text-align:${col.justify||"start"}`',
          :record='record'
        )
          a-tooltip(:title='get(record,col.prop)')
            .w-full.truncate(:style='`text-align:${col.justify||"start"}`') {{ getValue(record,col.prop) }}
  .w-full.vue3-marquee.flex.flex-col.overflow-y-auto.flex-grow.h-0(v-else)
    .scroll__col.w-full.flex(v-for='record in records')
      .text-sm.flex.items-center.px-2.py-2(
        v-for='col in cols',
        @click='onShow(record)'
        :style='`width:${col.width||"initial"};justify-content:${col.justify||"start"}`'
      )
        slot(
          name='cell',
          :value='get(record,col.prop)',
          :label='col.prop',
          :record='record'
          :colStyle='`text-align:${col.justify||"start"}`'
        )
          a-tooltip(:title='get(record,col.prop)')
            .w-full.truncate(:style='`text-align:${col.justify||"start"}`') {{ getValue(record,col.prop) }}
</template>

<style lang="stylus" scoped>
.com-screen-scroll-table-with-marquee
  .fix__header
    padding 2px 14px
    background rgba(36, 57, 110, 0.38)
    margin-bottom 2px
    box-shadow 0px 0px 4px 0px rgba(160, 181, 233, 0.08) inset
    >div
      color #AEC3DC
  .vue3-marquee
    width 100%
    .scroll__col
      padding 2px 14px
      box-shadow 0px 0px 4px 0px rgba(160, 181, 233, 0.08) inset
      margin-bottom 2px
      >div
        color #AEC3DC
    .scroll__col:nth-child(2n+1)
      background rgba(36, 57, 110, 0.38)

    .scroll__col:nth-child(2n)
      background rgba(36, 57, 110, 0.68)
</style>
