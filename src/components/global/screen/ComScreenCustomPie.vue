<script lang='ts'>
import { ref, defineComponent, toRefs, computed, watch } from 'vue';
import * as echarts from 'echarts';
const ComScreenCustomPie = defineComponent({
  name: 'ComScreenCustomPie',
  components: {},
  props: {
    data: {
      type: Array, default: () => [
        { value: 0, name: '暂无数据' }
      ]
    },
    colors: {
      type: Array, default: () => []
    }
  },
  setup(props) {
    const echartsRef = ref<any>(null);
    const computedData = computed(() => {
      if (Array.isArray(props.data)) {
        if (Array.isArray(props.data[0])) {
          return props.data.map((item: any) => ({
            name: item[0],
            value: item[1]
          }))
        }

        return props.data
      }


      return [props.data]
    })
    const pieOption = computed(() => ({
      color: props.colors.length > 0 ? props.colors : ['#0E9F6E', '#E74694', '#0F9AFF'],
      grid: {
        bottom: '15%'
      },
      legend: {
        bottom: 'bottom',
        left: 'center',
        icon: 'circle',
        textStyle: {
          color: '#4B5563'
        }
      },
      series: [
        {
          name: 'pie',
          type: 'pie',
          radius: '70%',
          center: ['50%', '45%'],
          itemStyle: {
            // borderColor: 'white',
            // borderWidth: 1,
          },
          label: {
            show: false,
          },
          data: computedData.value,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    }))


    return {
      ...toRefs(props),
      pieOption,
      echartsRef,
    };
  },
});
export default ComScreenCustomPie;
</script>

<template lang="pug">
.w-full.h-full
  TaScreenEchartBase(:options='pieOption' ref='echartsRef')
</template>

<style lang="stylus" scoped></style>
