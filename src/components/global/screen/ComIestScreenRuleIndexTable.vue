<script lang='ts'>
import { ref, defineComponent, toRefs, onMounted, computed, watch } from 'vue';
import ComScreenScrollTableWithMarquee from './ComScreenScrollTableWithMarquee.vue';
import { VObject, VStore } from '@/lib/vails';
import { ServeManageRulesApi } from '@/engines/iest/serve-core/apis/serve/manage/rules.api';
import { ServeRuleModel } from '@/engines/iest/serve-core/models/serve/manage/rules';
import { usePreviewClickable } from '../ta-component/TaBuilder/builder/usePreview';
import { ServeManageRuleGroupsApi } from '@/engines/iest/apis/serve/manage/rule_groups.api';
import { ServeRuleGroupModel } from '@/engines/iest/models/serve/manage/rule_groups';

const ComIestScreenRuleIndexTable = defineComponent({
  name: 'ComIestScreenRuleIndexTable',
  components: { ComScreenScrollTableWithMarquee },
  setup(props) {
    const ruleStore = new VStore(new ServeManageRulesApi(), ServeRuleModel)
    onMounted(() => {
      ruleStore.index({ per_page: 99, q: { s: ['position asc'] } })
    })
    const cols = [
      {
        label: '规则名称',
        prop: 'name',
        width: '22%',
      },
      {
        label: '区域',
        prop: 'orgs',
        width: '18%',
        justify: 'center',
      },
      {
        label: '最近发送时间',
        prop: 'latest_send_at',
        width: '24%',
        justify: 'center',
      },
      {
        label: '已发送批次',
        prop: 'total_packs_count',
        width: '16%',
        justify: 'center',
      },
      {
        label: '待审核数',
        prop: 'processing_packs_count',
        width: '20%',
        justify: 'center',
      }
    ];

    const { onClick } = usePreviewClickable(props)
    const openPackInstanceScreenModal = (record: VObject) => {
      const event = { mode: 'page_setting' } as { mode: 'page_setting' }
      const eventExtra = {
        page_setting_id: 5
      }
      onClick(
        event,
        {
          params: {
            "flowable_of_Serve::Pack_type_rule_id_eq": record.id,
            state_in: ["created", "preparing", "processing"]
          }
        },
        eventExtra
      )
    }
    const openPackIndexScreenModal = (record: VObject) => {
      const event = { mode: 'page_setting' } as { mode: 'page_setting' }
      const eventExtra = {
        page_setting_id: 10
      }
      onClick(
        event,
        {
          params: {
            rule_id_eq: record.id,
          }
        },
        eventExtra
      )
    }

    const ruleGrpStore = new VStore(new ServeManageRuleGroupsApi(), ServeRuleGroupModel)
    const config = computed(() => ({
      store: ruleGrpStore,
      mode: 'list',
      pagination: {
        hide: true,
      },
      list: {
        scroll: { y: 'auto' },
        gap: 14,
      }
    }))
    const selectedRuleId = ref<number>(0)
    const onSelect = (id: number) => {
      if (selectedRuleId.value === id) return selectedRuleId.value = 0;
      selectedRuleId.value = id
    }
    watch(selectedRuleId, () => {
      if (selectedRuleId.value === 0) {
        ruleStore.index({ per_page: 99, q: { s: ['position asc'] } })
        return;
      };

      ruleStore.index(
        {
          per_page: 99,
          q: {
            rule_group_id_eq: selectedRuleId.value,
            s: ['position asc']
          }
        }
      )
    })
    return {
      ...toRefs(props),
      cols,
      records: ruleStore.records,
      openPackInstanceScreenModal,
      openPackIndexScreenModal,
      config,
      onSelect,
      selectedRuleId,
    };
  },
});
export default ComIestScreenRuleIndexTable;
</script>

<template lang="pug">
.com-screen-rule-index-table.p-3.grid.gap-3
  TaIndexView.rule__group(
    :config='config',
    :showHeader='false'
  )
    template(#card='{record}')
      .w-full.flex.justify-center
        .rule__group-card.bg-cover.w-114px.h-8.flex.items-center.justify-center.cursor-pointer(
          :style='`color:${selectedRuleId === record.id? "yellow":"#65DBFB"};`'
          @click='onSelect(record.id)'
        )
          .text-base(style='color:currentColor') {{ record.name }} · {{record.statistic.rules_count || 0}}
  ComScreenScrollTableWithMarquee(
    :records='records',
    :cols='cols'
  )
    template(#header='{cols}')
      .text-sm.flex.items-center.px-2.py-2(
        v-for='col in cols',
        :style='`width:${col.width||"initial"};justify-content:${col.justify||"start"};color:#37E8FF;`'
      ) {{ col.label }}
    template(#cell='{value,label,colStyle,record}')
      a-tooltip(v-if='label === "orgs"',:title='value?.map((org) => org.name)?.join(",")')
        .w-full.truncate(
          :style='colStyle'
        ) {{ value?.map((org) => org.name)?.join(',') }}
      .text-sm.cursor-pointer(
        v-else-if='label === "total_packs_count"',
        @click.stop='openPackIndexScreenModal(record)'
      ) {{ value }}
      .text-sm.cursor-pointer(
        v-else-if='label === "processing_packs_count"',
        @click.stop='openPackInstanceScreenModal(record)'
      ) {{ value }}
</template>

<style lang="stylus" scoped>
.com-screen-rule-index-table
  grid-template-columns 22% 78%
  :deep(.scroll__col) > div
    padding 8px
  .rule__group-card
    background-image url(https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iest/screen_rule_grp_bg.png)
  .rule__group
    :deep(.ta-index-view-layout)
      .ta-sidebar-list
        .list-view__pagination_placeholder
          display none !important
</style>
