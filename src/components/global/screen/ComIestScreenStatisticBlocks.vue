<script lang="ts">
import { ref, defineComponent, toRefs, PropType, computed } from 'vue';
import { usePreviewClickable } from '../ta-component/TaBuilder/builder/usePreview';
import { VObject } from '@/lib/vails';
interface IBlock {
  title: string;
  value: string;
  icon?: string;
  clickable?: boolean;
  key?: string;
}
const defaultBlocks = [
  { title: '风险预警(个)', value: '0', clickable: true, key: 'risk' },
  { title: '来源总数(个)', value: '0' },
  { title: '素材总数(个)', value: '0', clickable: true, key: 'activity' },
  { title: '公职人员数(人)', value: '0', clickable: true, key: 'official' },
  { title: '消息发送总数(条)', value: '0', clickable: true, key: 'message' },
  { title: '规则总数(个)', value: '0', clickable: true, key: 'rule' },
];
const ComIestScreenStatisticBlocks = defineComponent({
  name: 'ComIestScreenStatisticBlocks',
  components: {},
  props: {
    blocks: { type: Array as PropType<IBlock[]>, default: () => defaultBlocks },
  },
  setup(props) {
    const ICONS = [
      'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iest/bid-guard/Group%205931.png',
      'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iest/Group%205931.png',
      // 'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iest/Group%205931%20%281%29.png',
      'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iest/Group%205931%20%282%29.png',
      'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iest/Group%205931%20%283%29.png',
      'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iest/Group%205931%20%284%29.png',
      'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iest/Group%205931%20%285%29.png',
    ];

    const computedData = computed(() =>
      props.blocks.map((block: IBlock, i: number) => ({
        title: block.title || defaultBlocks[i].title,
        value: block.value || 0,
        icon: block.icon || ICONS[i],
        clickable: defaultBlocks[i]?.clickable || false,
        key: defaultBlocks[i].key || '',
      })),
    );

    const { onClick } = usePreviewClickable(props);
    const openOfficialIndexScreenModal = () => {
      const event = { mode: 'page_setting' } as { mode: 'page_setting' };
      const eventExtra = {
        page_setting_id: 11,
      };
      onClick(
        event,
        {
          params: {},
        },
        eventExtra,
      );
    };
    const openRuleIndexScreenModal = () => {
      const event = { mode: 'page_setting' } as { mode: 'page_setting' };
      const eventExtra = {
        page_setting_id: 3,
      };
      onClick(
        event,
        {
          params: {},
        },
        eventExtra,
      );
    };

    const openMsgIndexScreenModal = () => {
      const event = { mode: 'page_setting' } as { mode: 'page_setting' };
      const eventExtra = {
        page_setting_id: 12,
      };
      onClick(
        event,
        {
          params: {},
        },
        eventExtra,
      );
    };
    const openActivityIndexScreenModal = () => {
      const event = { mode: 'page_setting' } as { mode: 'page_setting' };
      const eventExtra = {
        page_setting_id: 15,
      };
      onClick(
        event,
        {
          params: {},
        },
        eventExtra,
      );
    };
    const openPaperworkIndexScreenModal = () => {
      const event = { mode: 'page_setting' } as { mode: 'page_setting' };
      const eventExtra = {
        page_setting_id: 20,
      };
      onClick(
        event,
        {
          params: {
            paperwork_results_id_gt: 0,
            state_eq: 'success',
          },
        },
        eventExtra,
      );
    };

    const openScreenModalMap = {
      official: openOfficialIndexScreenModal,
      rule: openRuleIndexScreenModal,
      message: openMsgIndexScreenModal,
      activity: openActivityIndexScreenModal,
      risk: openPaperworkIndexScreenModal,
    };
    return {
      ...toRefs(props),
      ICONS,
      computedData,
      openScreenModalMap,
    };
  },
});
export default ComIestScreenStatisticBlocks;
</script>

<template lang="pug">
.com-iest-screen-statistic-blocks.grid.grid-cols-6.gap-6.content-center.justify-items-stretch.bg-cover.bg-no-repeat
  template(v-for="(block,i) in computedData")
    .iest__block.px-4.py-6.flex.items-center.justify-between.bg-cover.bg-center(
      :class='{"cursor-pointer": block.clickable}',
      @click.stop='block.clickable && block.key ? openScreenModalMap[block.key]() : null'
    )
      .title__and__value
        .iest__title.text-lg.mb-2 {{ block.title }}
        .iest__value.text-white.leading-normal {{ block.value }}
      .icon__container.w-16.h-16
        img.w-full.h-full.object-cover.object-center(:src='block.icon')

</template>

<style lang="stylus" scoped>
.com-iest-screen-statistic-blocks
  .iest__block
    background-image url(https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iest/bid-guard/Group%20427318976.png)
    .iest__title
      color rgba(167, 255, 255, 1)
    .iest__value
      font-family "DIN Alternate"
      font-size 32px
      line-height 37px;
</style>
