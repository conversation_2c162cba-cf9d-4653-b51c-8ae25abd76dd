<script lang='ts'>
import { ref, defineComponent, toRefs, computed } from 'vue';

const ComIestScreenBar = defineComponent({
  name: 'ComIestScreenBar',
  components: {},
  props: {
    data: {
      type: Array,
      default: () => []
    }
  },
  setup(props) {
    const echartsRef = ref<any>(null);
    const xData = computed(() => props.data.map((item: any) => item.name));

    const barOption = computed(() => ({
      grid: {
        top: '10%',
        left: '5%',
        right: '5%',
        bottom: '10%'
      },
      xAxis: {
        type: 'category',
        data: xData.value,
        axisPointer: {
          type: 'shadow'
        },

      },
      yAxis: {
        type: 'value',
        axisLabel: {
          color: 'white'
        },
        splitLine: {
          lineStyle: {
            type: 'dashed'
          }
        }
      },
      series: [
        {
          type: 'bar',
          data: props.data.map((item: any) => item.value),
        }
      ]
    }))
    return {
      ...toRefs(props),
      echartsRef,
      barOption,
    };
  },
});
export default ComIestScreenBar;
</script>

<template lang="pug">
.com-iest-screen-bar.w-full.h-full
  TaScreenEchartBase(:options='barOption' ref='echartsRef')
</template>

<style lang="stylus" scoped></style>
