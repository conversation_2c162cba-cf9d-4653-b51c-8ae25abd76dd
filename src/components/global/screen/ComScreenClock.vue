<script lang='ts'>
import { ref, defineComponent, toRefs, onMounted } from 'vue';
import dayjs from 'dayjs';

const ComScreenClock = defineComponent({
  name: 'ComScreenClock',
  components: {},
  props: {
    value: { type: String, default: 'copy' },
  },
  setup(props) {
    const value = ref(dayjs().format('HH:mm:ss'))
    let timer: any = null
    const getCurrentTime = () => {
      if (timer) {
        clearTimeout(timer)
      }
      const cur = dayjs()
      value.value = cur.format('HH:mm:ss')
      timer = setTimeout(getCurrentTime, 1000)
    }

    onMounted(() => {
      getCurrentTime()
    })


    const date = ref(dayjs())
    const weekday = ['日', '一', '二', '三', '四', '五', '六',]
    const fmtDate = () => {
      return `${date.value.format('YYYY-MM-DD')} 星期${weekday[date.value.day()]}`
    }
    return {
      ...toRefs(props),
      value,
      date,
      fmtDate,
    };
  },
});
export default ComScreenClock;
</script>

<template lang="pug">
.com-screen-clock.flex.justify-between.items-center.px-2
  .clock.text-base {{ value }}
  .date.text-xs {{ fmtDate() }}
</template>

<style lang="stylus" scoped>
.com-screen-clock
  font-family "PingFang TC"
.com-screen-clock .clock
  color #1FC6FF
.com-screen-clock .date
  color #1FC6FF
  line-height 150%

</style>
