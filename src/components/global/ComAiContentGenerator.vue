# 创建新的AI内容创建组件
<script lang="ts">
import { ServeManageAiMessageTemplatesApi } from '@/engines/iest/serve-core/apis/serve/manage/ai_message_templates.api';
import { ServeUserRulesApi } from '@/engines/iest/serve-core/apis/serve/user/rules.api';
import { ServeAiMessageTemplateModel } from '@/engines/iest/serve-core/models/serve/manage/ai_message_templates';
import { ServeRuleModel } from '@/engines/iest/serve-core/models/serve/manage/rules';
import { VStore } from '@/lib/vails';
import { message, Modal } from 'ant-design-vue';
import { computed, defineComponent, ref, watch } from 'vue';

export default defineComponent({
  name: 'ComAiContentGenerator',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    maxLength: {
      type: Number,
      default: 500,
    },
    ruleId: {
      type: [String, Number],
      default: '',
    },
  },
  emits: ['update:visible', 'confirm', 'cancel'],
  setup(props, { emit }) {
    const tone = ref('(空)');
    const wordCount = ref(120);
    const customPrompt = ref('');
    const selectedContents = ref<string[]>([]);
    const aiContents = ref<string[]>([]);
    const editingContent = ref('');
    const editingIndex = ref(-1);
    const editModalVisible = ref(false);
    const isGenerating = ref(false);
    const aiMessageTemplateStore = new VStore(
      new ServeManageAiMessageTemplatesApi(),
      ServeAiMessageTemplateModel,
    );
    const ruleStore = new VStore(new ServeUserRulesApi(), ServeRuleModel);
    const maxSelectCount = 3;
    const remainingSelections = computed(() => maxSelectCount - selectedContents.value.length);

    const toneOptions = [
      { value: '', label: '(空)' },
      { value: '清晰语气', label: '清晰语气' },
      { value: '正式语气', label: '正式语气' },
      { value: '友好语气', label: '友好语气' },
      { value: '专业语气', label: '专业语气' },
      { value: '幽默语气', label: '幽默语气' },
      { value: '激励语气', label: '激励语气' },
      { value: '简洁语气', label: '简洁语气' },
      { value: '温暖语气', label: '温暖语气' },
      { value: '严肃语气', label: '严肃语气' },
      { value: '机智语气', label: '机智语气' },
      { value: '鼓励语气', label: '鼓励语气' },
      { value: '详细语气', label: '详细语气' },
      { value: '轻松语气', label: '轻松语气' },
      { value: '权威语气', label: '权威语气' },
      { value: '安慰语气', label: '安慰语气' },
      { value: '简洁专业语气', label: '简洁专业语气' },
      { value: '轻松幽默语气', label: '轻松幽默语气' },
      { value: '关怀语气', label: '关怀语气' },
      { value: '简洁友好语气', label: '简洁友好语气' },
      { value: '详细友好语气', label: '详细友好语气' },
      { value: '激励专业语气', label: '激励专业语气' },
      { value: '轻松激励语气', label: '轻松激励语气' },
      { value: '温暖正式语气', label: '温暖正式语气' },
    ];

    // 设置默认的AI内容
    // const defaultAiContents = [];

    // 获取规则信息并设置默认提示词
    const initRulePrompt = async () => {
      if (props.ruleId) {
        try {
          const rule = await ruleStore.find(props.ruleId);
          console.log('rule: ', rule);
          if (rule.data?.options?.prompt) {
            customPrompt.value = rule.data.options.prompt;
          }
        } catch (error) {
          console.error('获取规则信息失败:', error);
        }
      }
    };

    // 监听弹窗显示状态，初始化内容和提示词
    watch(
      () => props.visible,
      async newVal => {
        if (newVal) {
          if (aiContents.value.length === 0) {
            aiContents.value = [];
          }
          await initRulePrompt();
        }
      },
    );

    // 生成AI内容
    const generateAiContent = async (shouldClear = false) => {
      try {
        if (!props.ruleId) {
          message.warning('未找到规则ID，请检查配置');
          return;
        }

        isGenerating.value = true;
        const combinedPrompt = `${customPrompt.value || ''}\n使用${tone.value}，生成${
          wordCount.value
        }字的内容。`;

        const res = await ruleStore.sendMemberAction({
          id: props.ruleId,
          action: 'generate_content_by_template_prompt',
          config: {
            data: {
              prompt: combinedPrompt,
            },
          },
        });

        if (res.data && Array.isArray(res.data.contents)) {
          if (shouldClear) {
            aiContents.value = res.data.contents;
          } else {
            aiContents.value = [...aiContents.value, ...res.data.contents];
          }
          message.success('生成内容成功');
        } else {
          console.error('AI内容格式不正确:', res.data);
          message.error('生成内容格式不正确');
        }
      } catch (error) {
        console.error('生成AI内容失败:', error);
        message.error('生成内容失败');
      } finally {
        isGenerating.value = false;
      }
    };

    const handleGenerateMore = async () => {
      try {
        await generateAiContent(false);
      } catch (error) {
        console.error('生成更多内容失败:', error);
        message.error('生成更多内容失败');
      }
    };

    const handleRegenerate = async () => {
      try {
        await generateAiContent(true);
      } catch (error) {
        console.error('重新生成内容失败:', error);
        message.error('重新生成内容失败');
      }
    };

    const handleContentDelete = (index: number) => {
      aiContents.value.splice(index, 1);
      // 如果删除的内容在已选中列表中，也需要从已选中列表中移除
      const content = aiContents.value[index];
      const selectedIndex = selectedContents.value.indexOf(content);
      if (selectedIndex > -1) {
        selectedContents.value.splice(selectedIndex, 1);
      }
      message.success('删除成功');
    };

    const toggleContentSelection = (content: string) => {
      const index = selectedContents.value.indexOf(content);
      if (index === -1) {
        if (selectedContents.value.length < aiContents.value.length) {
          selectedContents.value.push(content);
        } else {
          message.warning(`最多只能选择${aiContents.value.length}条内容`);
        }
      } else {
        selectedContents.value.splice(index, 1);
      }
    };

    const handleContentEdit = (index: number) => {
      editingIndex.value = index;
      editingContent.value = aiContents.value[index];
      editModalVisible.value = true;
    };

    const handleEditConfirm = () => {
      if (editingContent.value.trim()) {
        aiContents.value[editingIndex.value] = editingContent.value;
        // 如果编辑的内容在已选中列表中，也需要更新
        const oldContent = aiContents.value[editingIndex.value];
        const selectedIndex = selectedContents.value.indexOf(oldContent);
        if (selectedIndex > -1) {
          selectedContents.value[selectedIndex] = editingContent.value;
        }
        editModalVisible.value = false;
        editingContent.value = '';
        editingIndex.value = -1;
        message.success('编辑成功');
      }
    };

    const handleEditCancel = () => {
      editModalVisible.value = false;
      editingContent.value = '';
      editingIndex.value = -1;
    };

    const handleContentSave = async (content: string) => {
      try {
        await aiMessageTemplateStore.create({
          content: content,
          state: 'draft',
          name: `AI消息_${Date.now()}`,
        });
        message.success('保存至我的消息成功');
      } catch (error) {
        console.error('保存至我的消息失败:', error);
        message.error('保存至我的消息失败');
      }
    };

    const handleCancel = () => {
      if (selectedContents.value.length > 0) {
        Modal.confirm({
          title: '确认关闭',
          content: '确定要关闭窗口吗？已选择的内容将会丢失',
          okText: '确定',
          cancelText: '取消',
          onOk: () => {
            resetState();
          },
        });
      } else {
        resetState();
      }
    };

    const resetState = () => {
      selectedContents.value = [];
      aiContents.value = [];
      tone.value = '清晰语气';
      wordCount.value = 120;
      customPrompt.value = '';
      emit('update:visible', false);
      emit('cancel');
    };

    const handleConfirm = () => {
      if (selectedContents.value.length > 0) {
        emit('confirm', selectedContents.value);
        selectedContents.value = [];
        emit('update:visible', false);
      }
    };

    return {
      tone,
      wordCount,
      customPrompt,
      toneOptions,
      aiContents,
      selectedContents,
      handleGenerateMore,
      handleRegenerate,
      toggleContentSelection,
      handleContentEdit,
      handleContentDelete,
      handleCancel,
      handleConfirm,
      editingContent,
      editModalVisible,
      handleEditConfirm,
      handleEditCancel,
      handleContentSave,
      resetState,
      maxSelectCount,
      remainingSelections,
      isGenerating,
    };
  },
});
</script>

<template lang="pug">
a-modal(
  v-model:visible='visible'
  :footer='null'
  :closable='false'
  width='800px'
  :destroyOnClose='true'
  class='ai-content-modal'
  @cancel='handleCancel'
)
  .ai-content-header
    .title AI生成内容
    .subtitle 选择生成的消息内容，或者生成新内容

  .ai-content-body
    .flex.justify-between.mb-4
      .select-group
        span.label 语气
        a-select(
          v-model:value='tone'
          :options='toneOptions'
          placeholder='请选择语气'
          style='width: 200px'
        )
      .select-group
        span.label 字数
        a-input-number(
          v-model:value='wordCount'
          :min='1'
          :max='maxLength'
          placeholder='请输入字数'
          style='width: 200px'
        )

    .select-group.mb-4
      span.w-12.label 提示词
      a-textarea(
        v-model:value='customPrompt'
        :rows='3'
        placeholder='请输入提示词，用于指导AI生成内容的方向'
        :maxlength='500'
        style='width: 100%'
      )

    .selection-bar.flex.justify-between.items-center.mb-4.px-4.py-2.bg-gray-50.rounded
      .selected-count 已选择 {{ selectedContents.length }}/{{ aiContents.length }}
      .action-buttons.flex.space-x-2
        button.action-btn.generate-more(
          @click='handleGenerateMore'
          :disabled='isGenerating'
          :class='{"is-loading": isGenerating, "border border-purple-500": true}'
        )
          svg.icon(width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg")
            path(d="M7 0C3.13438 0 0 3.13438 0 7C0 10.8656 3.13438 14 7 14C10.8656 14 14 10.8656 14 7C14 3.13438 10.8656 0 7 0ZM10.5 7.525H7.525V10.5H6.475V7.525H3.5V6.475H6.475V3.5H7.525V6.475H10.5V7.525Z" fill="currentColor")
          span 生成更多
        button.action-btn.regenerate(
          @click='handleRegenerate'
          :disabled='isGenerating'
          :class='{"is-loading": isGenerating, "border border-purple-500": true}'
        )
          svg.icon(width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg")
            path(d="M11.9125 2.08751C10.6396 0.814642 8.91323 0.0937499 7.08749 0.0937499C3.29062 0.0937499 0.212493 3.17188 0.212493 6.96875C0.212493 10.7656 3.29062 13.8438 7.08749 13.8438C10.2854 13.8438 12.9937 11.6469 13.7021 8.65625H11.9125C11.2604 10.7094 9.33124 12.1875 7.08749 12.1875C4.20312 12.1875 1.86874 9.85313 1.86874 6.96875C1.86874 4.08438 4.20312 1.75 7.08749 1.75C8.45937 1.75 9.69999 2.28125 10.6396 3.16563L7.91874 5.88438H13.7021V0.100001L11.9125 2.08751Z" fill="currentColor")
          span 重新生成

    .content-list.grid.grid-cols-2.gap-4
      .content-item.p-4.border.rounded(
        v-for='(content, index) in aiContents'
        :key='index'
        :class='{"border-blue-500": selectedContents.includes(content)}'
        @click='toggleContentSelection(content)'
      )
        .flex.justify-between.items-center.mb-2
          .word-count 字数：{{ content.length }}
          .action-buttons.flex.space-x-2
            a-tooltip(title='编辑内容')
              button.edit-btn(@click.stop='handleContentEdit(index)')
                svg(width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg")
                  path(d="M9.3335 4L12.0002 6.66667M2.66683 13.3333H5.3335L12.3335 6.33333C12.8638 5.80309 12.8638 4.93024 12.3335 4.4L11.6002 3.66667C11.0699 3.13643 10.1971 3.13643 9.66683 3.66667L2.66683 10.6667V13.3333Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round")
            a-tooltip(title='保存至我的消息')
              button.save-btn(@click.stop='handleContentSave(content)')
                svg(width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg")
                  path(d="M13.3332 14H2.6665C2.31288 14 1.97374 13.8595 1.72369 13.6095C1.47364 13.3594 1.33317 13.0203 1.33317 12.6667V3.33333C1.33317 2.97971 1.47364 2.64057 1.72369 2.39052C1.97374 2.14048 2.31288 2 2.6665 2H11.3332L14.6665 5.33333V12.6667C14.6665 13.0203 14.526 13.3594 14.276 13.6095C14.0259 13.8595 13.6868 14 13.3332 14Z" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round")
                  path(d="M11.9998 14V8.66667H3.99984V14" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round")
                  path(d="M3.99984 2V5.33333H10.6665" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round")
            TaPopoverConfirm(
              title='删除',
              content='确认删除该内容？'
              @confirm='() => handleContentDelete(index)'
            )
              button.delete-btn(@click.stop='')
                svg(width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg")
                  path(d="M14.5159 3.86842H11.2567V2.18421C11.2567 1.73753 11.085 1.30914 10.7793 0.993294C10.4737 0.677443 10.0592 0.5 9.62703 0.5H6.36777C5.93556 0.5 5.52106 0.677443 5.21544 0.993294C4.90983 1.30914 4.73814 1.73753 4.73814 2.18421V3.86842H1.47888C1.26278 3.86842 1.05552 3.95714 0.902716 4.11507C0.749909 4.27299 0.664063 4.48719 0.664062 4.71053C0.664063 4.93387 0.749909 5.14806 0.902716 5.30598C1.05552 5.46391 1.26278 5.55263 1.47888 5.55263H2.29369V14.8158C2.29369 15.2625 2.46538 15.6909 2.771 16.0067C3.07661 16.3226 3.49112 16.5 3.92332 16.5H12.0715C12.5037 16.5 12.9182 16.3226 13.2238 16.0067C13.5294 15.6909 13.7011 15.2625 13.7011 14.8158V5.55263H14.5159C14.732 5.55263 14.9393 5.46391 15.0921 5.30598C15.2449 5.14806 15.3307 4.93387 15.3307 4.71053C15.3307 4.48719 15.2449 4.27299 15.0921 4.11507C14.9393 3.95714 14.732 3.86842 14.5159 3.86842ZM6.36777 2.18421H9.62703V3.86842H6.36777V2.18421ZM12.0715 14.8158H3.92332V5.55263H12.0715V14.8158Z" fill="#E02424")
                  path(d="M6.36777 6.39474C6.15166 6.39474 5.94441 6.48346 5.7916 6.64138C5.6388 6.79931 5.55295 7.0135 5.55295 7.23684V13.1316C5.55295 13.3549 5.6388 13.5691 5.7916 13.727C5.94441 13.885 6.15166 13.9737 6.36777 13.9737C6.58387 13.9737 6.79112 13.885 6.94393 13.727C7.09673 13.5691 7.18258 13.3549 7.18258 13.1316V7.23684C7.18258 7.0135 7.09673 6.79931 6.94393 6.64138C6.79112 6.48346 6.58387 6.39474 6.36777 6.39474Z" fill="#E02424")
                  path(d="M9.62703 6.39474C9.41092 6.39474 9.20367 6.48346 9.05086 6.64138C8.89806 6.79931 8.81221 7.0135 8.81221 7.23684V13.1316C8.81221 13.3549 8.89806 13.5691 9.05086 13.727C9.20367 13.885 9.41092 13.9737 9.62703 13.9737C9.84313 13.9737 10.0504 13.885 10.2032 13.727C10.356 13.5691 10.4418 13.3549 10.4418 13.1316V7.23684C10.4418 7.0135 10.356 6.79931 10.2032 6.64138C10.0504 6.48346 9.84313 6.39474 9.62703 6.39474Z" fill="#E02424")
        .content-text {{ content }}

  .ai-content-footer.flex.justify-end.space-x-2.mt-4
    a-button(@click='handleCancel') 取消
    a-button(type='primary' @click='handleConfirm' :disabled='!selectedContents.length') 应用选中

  // 编辑内容弹窗
  a-modal(
    v-model:visible='editModalVisible'
    title='编辑内容'
    @ok='handleEditConfirm'
    @cancel='handleEditCancel'
    :maskClosable='true'
    :closable='true'
  )
    .modal-content
      a-textarea(
        v-model:value='editingContent'
        :rows='6'
        placeholder='请输入内容'
        :maxlength='maxLength'
      )
</template>
<style lang="stylus" scoped>
.select-group
  display flex
  align-items center
  .label
    margin-right 8px
    color #374151
    font-size 14px

.ai-content-modal
  .ai-content-header
    margin-bottom 20px
    .title
      font-size 16px
      font-weight 500
      color #111827
    .subtitle
      font-size 14px
      color #6B7280
      margin-top 4px

  .selection-bar
    border 1px solid #E5E7EB
    background-color #F9FAFB
    .selected-count
      color #6B7280
      font-size 14px
    .action-buttons
      display flex
      gap 8px
      .action-btn
        display flex
        align-items center
        padding 6px 12px
        border-radius 4px
        border 1px solid #E5E7EB
        color #6B7280
        background-color white
        transition all 0.3s
        cursor pointer
        .icon
          margin-right 6px
          width 14px
          height 14px
        &:hover
          background-color #F3F4F6
          border-color #D1D5DB

  .content-list
    margin-top 16px
    .content-item
      background-color white
      border 1px solid #E5E7EB
      border-radius 8px
      padding 16px
      margin-bottom 0
      cursor pointer
      transition all 0.3s
      height fit-content
      &:hover
        border-color #4B5563
      &.border-blue-500
        border-color $primary-color
        border-width 2px
      .word-count
        color #6B7280
        font-size 12px
      .content-text
        color #374151
        line-height 1.5
        margin-top 8px
        max-height 200px
        overflow-y auto

  .ai-content-footer
    padding-top 16px
    border-top 1px solid #E5E7EB

.save-btn
  &:hover
    svg path
      stroke $primary-color

.delete-btn
  &:hover
    svg path
      fill #DC2626

.action-btn
  display flex
  align-items center
  padding 8px 16px
  border-radius 24px
  border 1px solid $primary-color
  color $primary-color
  background white
  transition all 0.3s ease
  font-size 14px
  cursor pointer
  height 36px
  box-shadow 0 1px 2px rgba(0, 0, 0, 0.05)
  &:hover:not(:disabled)
    background $primary-color
    color white
    .icon path
      fill white
  &:disabled
    opacity 0.5
    cursor not-allowed
  &.is-loading
    position relative
    color transparent !important
    pointer-events none
    &:after
      content ''
      position absolute
      left 50%
      top 50%
      width 16px
      height 16px
      margin -8px 0 0 -8px
      border 2px solid $primary-color
      border-right-color transparent
      border-radius 50%
      animation spin 0.8s linear infinite

  .icon
    margin-right 6px
    width 14px
    height 14px
    path
      fill $primary-color
      transition all 0.3s ease

@keyframes spin
  from
    transform rotate(0deg)
  to
    transform rotate(360deg)
</style>
