<script lang="ts">
import { ServeManageAiMessageTemplatesApi } from '@/engines/iest/serve-core/apis/serve/manage/ai_message_templates.api';
import { ServeAiMessageTemplateModel } from '@/engines/iest/serve-core/models/serve/manage/ai_message_templates';
import { VStore } from '@/lib/vails';
import { message } from 'ant-design-vue';
import { defineComponent, onMounted, ref, watch } from 'vue';

// 简单的模板类型定义
interface Template {
  id: number | string;
  name: string;
  content: string;
  created_at?: string;
  updated_at?: string;
}

export default defineComponent({
  name: 'ComMessageTemplateSelector',
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    maxLength: {
      type: Number,
      default: 500,
    },
  },
  emits: ['update:visible', 'confirm', 'cancel'],
  setup(props, { emit }) {
    const templates = ref<Template[]>([]);
    const selectedTemplates = ref<Template[]>([]);
    const loading = ref(false);
    const aiMessageTemplateStore = new VStore(
      new ServeManageAiMessageTemplatesApi(),
      ServeAiMessageTemplateModel,
    );

    const searchKeyword = ref('');
    const currentPage = ref(1);
    const pageSize = ref(6); // 每页6条
    const total = ref(0);
    let searchTimer: number | null = null; // 用于防抖的定时器

    // 初始加载模板数据
    onMounted(async () => {
      if (props.visible) {
        await fetchTemplates();
      }
    });

    // 获取模板列表
    const fetchTemplates = async () => {
      try {
        loading.value = true;

        // 使用any类型暂时解决类型问题
        const res: any = await aiMessageTemplateStore.index({
          page: currentPage.value,
          per_page: pageSize.value,
          q: searchKeyword.value ? { name_or_content_cont: searchKeyword.value } : undefined,
        });

        // 适配返回的数据结构
        if (res && res.records && Array.isArray(res.records)) {
          templates.value = res.records.map((item: any) => ({
            id: item.id || 0,
            name: item.name || '',
            content: item.content || '',
            created_at: item.created_at,
            updated_at: item.updated_at,
          }));

          // 获取总数和页码信息
          total.value = res.total_count || 0;
          currentPage.value = res.current_page || 1;
        } else if (res && res.data && Array.isArray(res.data)) {
          // 兼容原接口格式
          templates.value = res.data.map((item: any) => ({
            id: item.id || 0,
            name: item.name || '',
            content: item.content || '',
            created_at: item.created_at,
            updated_at: item.updated_at,
          }));

          // 获取总数
          if (res.meta && typeof res.meta.total === 'number') {
            total.value = res.meta.total;
          } else {
            total.value = templates.value.length;
          }
        } else {
          templates.value = [];
          total.value = 0;
        }
      } catch (error) {
        message.error('获取模板列表失败');
      } finally {
        loading.value = false;
      }
    };

    // 搜索模板
    const handleSearch = async () => {
      currentPage.value = 1;
      await fetchTemplates();
    };

    // 分页改变
    const handlePageChange = async (page: number) => {
      currentPage.value = page;
      await fetchTemplates();
    };

    // 切换模板选择状态
    const toggleTemplateSelection = (template: Template) => {
      const index = selectedTemplates.value.findIndex(item => item.id === template.id);
      if (index === -1) {
        selectedTemplates.value.push(template);
      } else {
        selectedTemplates.value.splice(index, 1);
      }
    };

    // 全选当前页
    const selectAllCurrentPage = () => {
      // 将当前页全部添加到已选列表，避免重复添加
      templates.value.forEach(template => {
        if (!selectedTemplates.value.some(item => item.id === template.id)) {
          selectedTemplates.value.push(template);
        }
      });
      message.success('已选择当前页全部模板');
    };

    // 取消全选
    const unselectAll = () => {
      selectedTemplates.value = [];
      message.success('已取消全部选择');
    };

    // 取消
    const handleCancel = () => {
      // 清除定时器
      if (searchTimer) {
        clearTimeout(searchTimer);
      }
      selectedTemplates.value = [];
      emit('update:visible', false);
      emit('cancel');
    };

    // 确认选择
    const handleConfirm = () => {
      if (selectedTemplates.value.length > 0) {
        // 清除定时器
        if (searchTimer) {
          clearTimeout(searchTimer);
        }
        // 返回选中模板的内容
        const selectedContents = selectedTemplates.value.map(template => template.content);
        emit('confirm', selectedContents);
        selectedTemplates.value = [];
        emit('update:visible', false);
      } else {
        message.warning('请至少选择一条模板');
      }
    };
    // 监听弹窗显示状态，加载模板数据
    watch(
      () => props.visible,
      async newVal => {
        if (newVal) {
          await fetchTemplates();
        }
      },
    );

    // 监听搜索关键词变化，添加防抖
    watch(
      () => searchKeyword.value,
      newVal => {
        // 清除之前的定时器
        if (searchTimer) {
          clearTimeout(searchTimer);
        }

        // 设置新的定时器，1秒后执行搜索
        searchTimer = setTimeout(() => {
          handleSearch();
        }, 1000) as unknown as number;
      },
    );
    return {
      templates,
      selectedTemplates,
      loading,
      searchKeyword,
      currentPage,
      pageSize,
      total,
      handleSearch,
      handlePageChange,
      toggleTemplateSelection,
      selectAllCurrentPage,
      unselectAll,
      handleCancel,
      handleConfirm,
    };
  },
});
</script>

<template lang="pug">
a-modal(
  v-model:visible='visible'
  :footer='null'
  :closable='false'
  width='800px'
  :destroyOnClose='true'
  class='template-selector-modal'
  @cancel='handleCancel'
)
  .template-header
    .title 我的消息模板
    .subtitle 选择已保存的消息模板

  .template-body
    .search-bar.flex.justify-between.mb-4
      a-input(
        v-model:value='searchKeyword'
        placeholder='搜索模板名称'
        allow-clear
        @pressEnter='handleSearch'
        style='width: 250px'
      )
        template(#suffix)
          a-button(type='link' size='small' @click='handleSearch')
            i.fas.fa-search

      .batch-actions
        a-button.mr-2(@click='selectAllCurrentPage') 选择当前页
        a-button(@click='unselectAll') 取消全选

    .selection-bar.flex.justify-between.items-center.mb-4.px-4.py-2.bg-gray-50.rounded
      .selected-count 已选择 {{ selectedTemplates.length }} 条模板

    .template-list(v-if="templates.length > 0")
      a-spin(:spinning="loading")
        .grid.grid-cols-2.gap-4
          .template-item.p-4.border.rounded(
            v-for='template in templates'
            :key='template.id'
            :class='{"border-blue-500": selectedTemplates.some(item => item.id === template.id)}'
            @click='toggleTemplateSelection(template)'
          )
            .flex.justify-between.items-center.mb-2
              .template-name {{ template.name || `模板_${template.id}` }}
              .word-count 字数：{{ template.content?.length || 0 }}
            .template-content {{ template.content }}

    .empty-state.text-center.py-8(v-else)
      .text-gray-400 暂无模板，请先创建并保存模板

    .pagination.mt-4.text-center(v-if="total > 0")
      a-pagination(
        v-model:current='currentPage'
        :total='total'
        :pageSize='pageSize'
        @change='handlePageChange'
        :showSizeChanger='false'
      )

  .template-footer.flex.justify-between.space-x-2.mt-4
    .selected-info(v-if="selectedTemplates.length > 0") 已选择 {{ selectedTemplates.length }} 条模板
    .placeholder(v-else)
    .action-buttons
      a-button(@click='handleCancel') 取消
      a-button(type='primary' @click='handleConfirm' :disabled='!selectedTemplates.length') 应用选中
</template>

<style lang="stylus" scoped>
.template-selector-modal
  .template-header
    margin-bottom 20px
    .title
      font-size 16px
      font-weight 500
      color #111827
    .subtitle
      font-size 14px
      color #6B7280
      margin-top 4px

  .template-body
    .search-bar
      display flex
      justify-content space-between
      align-items center

    .selection-bar
      border 1px solid #E5E7EB
      background-color #F9FAFB
      .selected-count
        color #6B7280
        font-size 14px

  .template-item
    background-color white
    border 1px solid #E5E7EB
    border-radius 8px
    padding 16px
    margin-bottom 0
    cursor pointer
    transition all 0.3s
    height fit-content
    &:hover
      border-color #4B5563
    &.border-blue-500
      border-color #3B82F6
      border-width 2px
    .template-name
      font-weight 500
      color #374151
      font-size 14px
      white-space nowrap
      overflow hidden
      text-overflow ellipsis
    .word-count
      color #6B7280
      font-size 12px
    .template-content
      color #374151
      line-height 1.5
      margin-top 8px
      max-height 150px
      overflow-y auto
      font-size 14px

  .template-footer
    padding-top 16px
    border-top 1px solid #E5E7EB
    .selected-info
      color #6B7280
      font-size 14px

.empty-state
  padding 20px
  text-align center
  color #9CA3AF
  font-size 14px

.pagination
  margin-top 20px
  text-align center
</style>
