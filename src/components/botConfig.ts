import { VObject } from '@/lib/vails';
import { defineAsyncComponent } from 'vue';

export const BotMentionTypeMapping: VObject = {
  'Bpm::Instance': defineAsyncComponent(
    () => import('@/engines/bot/components/bot/tools/ComBpmInstanceTool.vue'),
  ),
  'Serve::Pack': defineAsyncComponent(
    () => import('@/components/bot/tools/ComMessageSenderTool.vue'),
  ),
  'Bot::Paperwork': defineAsyncComponent(
    () => import('@/components/bot/tools/ComBotPaperworkTool.vue'),
  ),
  'Serve::Activity': defineAsyncComponent(
    () => import('@/components/bot/tools/ComActivityListTool.vue'),
  ),
};

export const botSuggestionsFn = (type: string) => {
  switch (type) {
    default:
      return [
        // { name: '到现在为止发了多少消息统计' },
        { name: '现在有多少待审批流程' },
        { name: '国庆前一天给年轻干部发一条国庆廉洁提醒' },
        // { name: '库里有多少人员' },
        // { name: '规则配置情况' },
        // { name: '请告诉我现在有多少素材' },
      ];
  }
};
