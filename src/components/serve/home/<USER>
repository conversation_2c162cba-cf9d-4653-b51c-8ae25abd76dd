<script lang="ts">
import { ref, defineComponent, toRefs, computed } from 'vue';
import ComServeTag from '../ComServeTag.vue';
const ComServeHomeCard = defineComponent({
  name: 'ComServeHomeCard',
  components: {
    ComServeTag,
  },
  props: {
    record: { type: Object, default: () => {} },
  },
  setup(props) {
    const bg = computed(() => {
      return `url(${props.record?.cover_image?.files?.[0]?.url})`;
    });
    return {
      ...toRefs(props),
      bg,
    };
  },
});
export default ComServeHomeCard;
</script>

<template lang="pug">
.com-serve-home-card.shadow-sm.bg-white.w-245px.overflow-hidden.cursor-pointer
  .card__header__bg.h-138px.bg-cover.bg-center.w-full
  .card__content.p-3
    .card__title.text-gray-700.text-base.font-normal.mb-1 {{ record?.name }}
    .text-gray-500.text-xs.font-normal.mb-2 {{ record?.model_payload?.source }}
    //- .card__tags.flex.flex-wrap.gap-1
      ComServeTag(
        v-for='tag in record?.tags',
        :text='tag.name'
      )
</template>

<style lang="stylus" scoped>
.com-serve-home-card
  border-radius 8px
  border 1px solid #E5E7EB
  &:hover
    @apply shadow-md
    .card__title
      transition all 250ms
      @apply text-blue-600
  .card__header__bg
    background-image v-bind(bg)
</style>
