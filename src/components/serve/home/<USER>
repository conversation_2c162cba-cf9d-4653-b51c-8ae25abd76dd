<script lang="ts">
import { defineComponent, onMounted, onUnmounted, ref } from 'vue';

export default defineComponent({
  name: 'ComServeHomeFloatingIcon',
  setup() {
    const positionX = ref(20);
    const positionY = ref(50);
    const direction = ref({ x: 1, y: 1 });
    const speed = 0.2;
    const isHovered = ref(false);
    const isDragging = ref(false);
    let animationInterval: number | null = null;

    const moveIcon = () => {
      if (isHovered.value || isDragging.value) return;

      positionX.value += speed * direction.value.x;
      positionY.value += speed * direction.value.y;

      // 检查边界并改变方向
      if (positionX.value < 0 || positionX.value > 20) direction.value.x *= -1;
      if (positionY.value < 0 || positionY.value > 80) direction.value.y *= -1;
    };

    const handleClick = () => {
      if (isDragging.value || wasDragging) return;
      window.open('https://lian.dskb.cn/tenant/hz/#/pages/index/index', '_blank');
    };

    let wasDragging = false;
    const handleMouseDown = (e: MouseEvent) => {
      e.preventDefault();
      isDragging.value = true;
      wasDragging = false;

      const handleMouseMove = (e: MouseEvent) => {
        if (!isDragging.value) return;
        wasDragging = true;
        const container = document.querySelector('.serve-home-index');
        if (!container) return;

        const rect = container.getBoundingClientRect();
        const x = ((e.clientX - rect.left) / rect.width) * 100;
        const y = ((e.clientY - rect.top) / rect.height) * 100;

        positionX.value = Math.max(10, Math.min(90, x));
        positionY.value = Math.max(10, Math.min(90, y));
      };

      const handleMouseUp = () => {
        isDragging.value = false;
        document.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
        setTimeout(() => {
          wasDragging = false;
        }, 100);
      };

      document.addEventListener('mousemove', handleMouseMove);
      document.addEventListener('mouseup', handleMouseUp);
    };

    onMounted(() => {
      animationInterval = window.setInterval(moveIcon, 100);
    });

    onUnmounted(() => {
      if (animationInterval) {
        clearInterval(animationInterval);
      }
    });

    return {
      positionX,
      positionY,
      handleClick,
      isHovered,
      handleMouseDown,
    };
  },
});
</script>

<template lang="pug">
.floating-icon(
  @click='handleClick',
  @mouseenter='isHovered = true',
  @mouseleave='isHovered = false',
  @mousedown='handleMouseDown',
  :style='{ left: positionX + "%", top: positionY + "%" }'
)
  img(
    src='https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/zj_iest/%E7%BA%AA%E6%B3%95%E5%B0%8F%E5%8D%9A%E5%A3%AB.jpg',
    alt='纪法小博士'
  )
</template>

<style lang="stylus" scoped>
.floating-icon
  position fixed
  z-index 1000
  cursor pointer
  transition transform 0.1s ease
  user-select none
  img
    width 80px
    // height 80px
    border-radius 4px
    box-shadow 0 2px 8px rgba(0, 0, 0, 0.15)
    pointer-events none
</style>
