<script lang="ts">
import TaUserAvatarDropdown from '@/components/global/ta-component/TaUserAvatarDropdown.vue';
import { computed, defineComponent, onMounted, ref, toRefs } from 'vue';
import { useRouter } from 'vue-router';
import useDDLink from '../../../engines/dingtalk/utils/useDDLink';
import { AuthSessionApi } from '../../../engines/login/apis/auth/session.api';

const ComServeHomeIndexHeader = defineComponent({
  name: 'ComServeHomeIndexHeader',
  components: {
    TaUserAvatarDropdown,
  },
  props: {
    routerTab: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props, { emit }) {
    const SERVE_HOME_SRC =
      'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/discipline-inspection/ServeHomeBg.png';
    const searchValue = ref('');
    onMounted(() => {
      AuthSessionApi.getCurrentUser();
    });
    const currentUser = computed(() => {
      return AuthSessionApi.currentUser();
    });
    const router = useRouter();

    const onSearch = () => {
      const libUrl = '/serve/home/<USER>';
      if (router.currentRoute.value.path !== libUrl && searchValue.value) {
        router.push(libUrl);
        props.routerTab.changeTabFollowByRouter(libUrl);
      }

      emit('search', {
        name_cont_any: searchValue.value,
      });
    };
    const onChange = (e: any) => {
      if (e.target.value === '') {
        searchValue.value = '';
        onSearch();
      }
    };

    const logout = async () => {
      await new AuthSessionApi().logout();
      // 使用 location 跳转来彻底清空所有 store
      window.location.href = process.env.VUE_APP_LOGIN_HREF || '/login';
    };

    const { linkTo } = useDDLink();
    return {
      ...toRefs(props),
      SERVE_HOME_SRC,
      searchValue,
      currentUser,
      onSearch,
      onChange,
      logout,
      linkTo,
    };
  },
});
export default ComServeHomeIndexHeader;
</script>

<template lang="pug">
.com-serve-home-index-header.relative
  header.absolute.w-full.flex.justify-center
    .w-1080px.h-60px.z-2.flex.items-center.px-6
      .header__title.text-xl.text-white.font-medium.mr-auto.flex-shrink-0

      .search__wrapper.w-325px.h-37px.flex.mr-6.ml-1
        a-input(
          v-model:value='searchValue',
          placeholder='搜索名称',
          allow-clear,
          @keypress.enter='onSearch',
          @change='onChange'
        )
          //- template(#addonBefore)
          //-   TaIcon.w-4.h-4.text-gray-500(type='outline/search')

        .search__btn.w-9.h-9.bg-blue-700.flex.items-center.justify-center.ml-10px.flex-shrink-0.cursor-pointer(
          @click='onSearch'
        )
          TaIcon.text-white.w-5.h-5(type='outline/search')

      TaUserAvatarDropdown.login__wrapper(
        :user='currentUser',
        @logout='logout',
        :whiteBackground='true'
      )
      .text-sm.ml-2.text-white.cursor-pointer(
        v-if='currentUser?.roles_name?.includes("serve_admin") || currentUser?.roles_name?.includes("serve_manage")',
        @click.stop='linkTo("/workplane", false)'
      ) 工作台

  img.h-full.w-full(:src='SERVE_HOME_SRC', alt='serve-home-bg')
  .absolute.top-104px.w-full.flex.justify-center.z-2
    img.w-800px.h-51px.block(src='@/assets/images/serve.png')
</template>

<style lang="stylus" scoped>
.com-serve-home-index-header
  .header__title
    font-family 'PingFang SC'
  .search__wrapper
    .ant-input-affix-wrapper
      border-radius 8px
  .search__btn
    border-radius 8px
    border 1px solid #1a56db
</style>
