import { computed, ref } from 'vue';
import { Router, useRouter } from 'vue-router';
export type Tab = {
  name: string;
  key: string;
  router: string;
};

export default class TabWithRouter {
  private tabs: Tab[];
  private activeTab = ref<Tab | null>(null);
  private router: Router = useRouter();

  readonly activeTabKey = computed(() => {
    return this.activeTab.value?.key || '';
  });

  constructor(tabs: Tab[]) {
    this.tabs = tabs;
    this.activeTab.value = this.tabs[0];
  }

  init() {
    this.activeTab.value =
      this.tabs.find((tab: Tab, i: number) => {
        if (this.router.currentRoute.value.path === tab.router) {
          return true;
        }
        if (tab.name === '廉政教育库') {
          return true;
        }
      }) || this.tabs[0];
  }

  changeTab(tab: Tab) {
    this.activeTab.value = tab;
    this.router.push(tab.router);
  }

  changeTabFollowByRouter(tabOrRouter: Tab | string) {
    if (typeof tabOrRouter === 'string') {
      this.activeTab.value =
        this.tabs.find((tab: Tab) => {
          if (tab.router === tabOrRouter) {
            return true;
          }
          if (tab.name === '廉政教育库') {
            return true;
          }
        }) || this.tabs[0];

      return;
    }
    this.activeTab.value = tabOrRouter;
  }
}
