<script lang='ts'>
import { ref, defineComponent, toRefs, computed } from 'vue';

const ComServeEvalution = defineComponent({
  name: 'ComServeEvalution',
  components: {},
  props: {
    item: {
      type: Object,
      default: () => ({})
    },
    checked: {
      type: Boolean,
      default: false
    }
  },
  setup(props, { emit }) {
    const localChecked = computed({
      get() {
        return props.checked;
      }, set(val: boolean) {
        emit('update:checked', val)
      }
    })
    return {
      ...toRefs(props),
      localChecked,
    };
  },
});
export default ComServeEvalution;
</script>

<template lang="pug">
.com-serve-evalution.flex.flex-col.items-center
  .w-16.h-24.mb-2.overflow-hidden.flex.items-center
    .w-16.h-16.relative.z-2.cursor-pointer(@click.stop='localChecked = !localChecked')
      img.w-full.h-full(:src='item.default')
      .wrapper.absolute.w-full.left-0.top-0(
        :class='{ checked:checked }'
      )
        img.w-16.h-16.mb-4(:src='item.checkedBg')
        img.w-16.h-16(:src='item.unCheckedBg')

  .text-sm.text-gray-400 {{ item.label }}

</template>

<style lang="stylus" scoped>
.com-serve-evalution .wrapper
  z-index -1
  animation move2 1s forwards ease-out

.com-serve-evalution .wrapper.checked
  animation move 1s forwards ease-out

@keyframes move
  0%
    transform translateY(-80px)
  30%
    transform translateY(-90px)
  100%
    transform translateY(0px)

@keyframes move2
  0%
    transform translateY(0)
  30%
    transform translateY(10px)
  100%
    transform translateY(-80px)
</style>
