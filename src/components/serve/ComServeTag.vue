<script lang='ts'>
import { defineComponent, toRefs } from 'vue';
const ComServeTag = defineComponent({
  name: 'ComServeTag',
  components: {},
  props: {
    text: { type: String, default: 'copy' },
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
export default ComServeTag;
</script>

<template lang="pug">
.com-serve-tag.px-10px.py-2px.bg-blue-100.text-blue-800
  .text-xs.font-medium.leading-normal {{ text }}
</template>

<style lang="stylus" scoped>
.com-serve-tag
  border-radius 6px
  width fit-content
</style>
