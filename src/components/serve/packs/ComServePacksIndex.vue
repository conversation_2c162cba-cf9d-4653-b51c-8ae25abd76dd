<script lang="ts">
import { defineComponent, computed, ref, Ref, toRefs, reactive } from 'vue';
import { TaIndexViewTabInterface } from '@/components/global/ta-component/TaIndexView/types';
import { VObject } from '@/lib/vails/model';
import ComServePacksShow from './ComServePacksShow.vue';
import ComBpmInstanceDetailDialog from '@/engines/bpm/components/ComBpmInstanceDetailDialog.vue';
import { AuthSessionApi } from '@/engines/login/apis/auth/session.api';

const ComServePacksIndex = defineComponent({
  name: 'ComServePacksIndex',
  components: {
    ComServePacksShow,
    ComBpmInstanceDetailDialog,
  },
  props: {
    store: { type: Object, required: true },
  },
  setup(props, { emit }) {
    const userInfo = AuthSessionApi.currentUser();
    const serveAdmin = computed(() => userInfo?.roles_name?.includes('serve_admin'));
    const config = computed(() => ({
      recordName: '周期任务',
      store: props.store,
      // pagination: {
      //   perPage: 15,
      //   showPageSizeChanger: false,
      //   hideOnSinglePage: false,
      //   showSizeChanger: false,
      // }
      template: 'serve_pack#form',
      detail: {
        // mode: 'auto',
        mode: 'drawer',
        width: '1100px',
      },
      mode: 'table',
      // editApi:
      // showCount: true,
      actions: [
        { key: 'create', enabled: true },
        { key: 'update', enabled: true },
        { key: 'delete', enabled: true },
        // { key: 'import', enabled: true },
        // { key: 'export', enabled: true },
      ],
      draggable: serveAdmin.value,
      table: {
        scroll: { y: 'auto' },
        // columns: [{ dataIndex: 'id', title: 'ID' }],
      },
      list: {
        scroll: { y: 'auto' },
      },
      // selection: {
      //   showByDefault: false;
      //   showSwitch: false;
      // };
      searcherSimpleOptions: [{ key: 'name', label: '标题', type: 'string' }],
    }));

    const statistics = ref({
      key1: 0,
      key2: 0,
    });

    const tabs = computed<TaIndexViewTabInterface[]>(() => [
      {
        key: 'key1',
        label: '标签1',
        num: statistics.value.key1,
        query: {},
      },
      {
        key: 'key2',
        label: '标签2',
        num: statistics.value.key2,
        query: {},
      },
    ]);

    const onIndex = (data: VObject) => {
      statistics.value = data.statistics;
    };

    const visibleInstance = ref(false);
    const instanceId = ref(0);
    const viewInstance = (record: VObject) => {
      instanceId.value = record.create_instance_id;
      visibleInstance.value = true;
    };

    return {
      ...toRefs(props),
      config,
      tabs,
      onIndex,
      visibleInstance,
      instanceId,
      viewInstance,
    };
  },
});

export default ComServePacksIndex;
</script>

<template lang="pug">
.com-serve-manage-packs-index
  TaIndexView(:config='config', @onIndex='onIndex')
    template(#bodyCell='{ text, record, index, column }')
      a-button(
        @click.stop='viewInstance(record)',
        type='primary',
        v-if='column.key == "bpm" && record.create_instance_id'
      ) 查看工作流
  ComBpmInstanceDetailDialog(v-model:visible='visibleInstance', :instanceId='instanceId')
    //- template(#card='{ record }')
    //- template(#table)
    //-    a-table-column(title='名称' dataIndex='record')
    //- template(#detail='{ record, onClose }')
    //-   ComServePacksShow(
    //-     v-if='record.id'
    //-     :store='store'
    //-     :extendRoute='`/serve/manage/packs/${record.id}`'
    //-     :editable='editable'
    //-     @afterDelete='onClose'
    //-     @afterExtend='onClose'
    //-   )
</template>

<style lang="stylus" scoped>
.com-serve-manage-packs-index
  height 100%
  width 100%
</style>
