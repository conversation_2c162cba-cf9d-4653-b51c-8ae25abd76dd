<script lang='ts'>
import { computed, defineComponent, PropType, ref, toRefs, watch } from 'vue';
import { useRouter } from 'vue-router';
import { ServeUserActivitiesApi } from '../../../engines/iest/apis/serve/user/activities.api';
import { VStore } from '../../../lib/vails/store/index';
import TabWithRouter from '../home/<USER>';
import ComServeLibCard from './ComServeLibCard.vue';
import ComServeLibQuery from './ComServeLibQuery.vue';

const ComServeLibIndex = defineComponent({
  name: 'ComServeLibIndex',
  components: {
    ComServeLibQuery,
    ComServeLibCard,
  },
  props: {
    routerTab: {
      type: Object as PropType<TabWithRouter>,
      required: true,
    },
    q: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props) {
    const router = useRouter();
    const query = ref<any>({
      state_eq: 'published',
      ...props.q,
    });
    const breadcrumbs = computed(() => [
      {
        label: '首页',
        href: '/serve/home',
      },
      {
        label: '廉政教育库',
        href: '/serve/home/<USER>',
      },
    ]);

    const store = new VStore(
      new ServeUserActivitiesApi({
        parents: [{ type: 'submodules', id: 1 }],
        params: { q: { s: ['id desc'] } },
      }),
    );

    const config = computed(() => ({
      store,
      mode: 'list',
      pagination: {
        perPage: 14,
      },
      list: {
        scroll: { y: 'auto' },
      },
      params: {
        q: query.value,
      },
    }));

    const onClicked = (href: string) => {
      router.push(href);
      props.routerTab.changeTabFollowByRouter(href);
    };
    const onShow = (record: any) => {
      if (record.model_payload?.link_to_origin === 'true' && record.model_payload?.link) {
        store.find(record.id).then(() => {
          window.open(record.model_payload.link);
        });
      } else {
        const url = `/serve/home/<USER>/${record.id}`;
        router.push(url);
        props.routerTab.changeTabFollowByRouter(url);
      }
    };
    const onQuery = (ids: any) => {
      query.value = {
        ...query.value,
        scopes: {
          tags_id_contain: ids,
        },
      };
    };
    watch(
      () => props.q,
      newVal => {
        query.value = { ...query.value, ...newVal };
      },
    );
    const goFiling = () => {
      window.open('https://beian.miit.gov.cn/');
    };
    return {
      ...toRefs(props),
      breadcrumbs,
      onClicked,
      config,
      onQuery,
      onShow,
      goFiling,
    };
  },
});
export default ComServeLibIndex;
</script>

<template lang="pug">
.com-serve-lib-index.w-full.bg-gray-50.flex.flex-col.flex-grow.items-center
  .lib__container.w-1080px.px-6.pt-6.flex-grow
    a-breadcrumb(separator='>')
      a-breadcrumb-item(
        v-for='item in breadcrumbs',
        :href='item.href',
        @click='onClicked(item.href)'
      )
        span {{ item.label }}
    main.my-12
      .flex.w-full.h-full.items-start
        .left.p-4.bg-white.mr-4.flex.flex-col.h-900px.flex-grow
          TaIndexView.h-0.flex-grow(:config='config', :showHeader='false')
            template(#card='{ record, index }')
              ComServeLibCard(:record='record', @click='onShow(record)')
        .right.py-4.w-350px.bg-white.h-900px.overflow-auto
          ComServeLibQuery(@query='onQuery', @reset='onReset')
    //- footer.w-full.p-8.flex.justify-between.items-center.bg-white
    //-   .text-base.font-medium.text-gray-900.cursor-pointer(@click='goFiling') 版权所属: 中共杭州市滨江区纪律检查委员会、杭州市滨江区监察委员会
    //- .text-sm.font-normal.text-gray-500 备案证编号: 浙ICP*********号
</template>

<style lang="stylus" scoped>
.com-serve-lib-index {
  footer {
    border-top: 1px solid #e5e7eb;
  }
}
</style>
