<script lang='ts'>
import { defineComponent, toRefs, ref } from 'vue';
import ComServeTag from '../ComServeTag.vue';
const ComServeLibQueryCard = defineComponent({
  name: 'ComServeLibQueryCard',
  components: { ComServeTag },
  props: {
    group: {
      type: Object,
      default: () => ({})
    }
  },
  setup(props, { emit }) {
    const tagIds = ref<number[]>([])

    const reset = () => {
      emit('reset', tagIds.value)
      tagIds.value = []
    }


    const onQuery = (tag: any) => {
      if (tagIds.value.includes(tag.id)) {
        emit('reset', [tag.id])
        tagIds.value = tagIds.value.filter(id => id !== tag.id)
        return;
      } else {
        tagIds.value.push(tag.id)
        emit('query', tagIds.value)
      }

    }
    return {
      ...toRefs(props),
      tagIds,
      reset,
      onQuery,
    };
  },
});
export default ComServeLibQueryCard;
</script>

<template lang="pug">
.com-serve-lib-query-card
  header.flex.items-center.pr-4.mb-2
    .w-1.h-27px.bg-blue-600.mr-4
    .text-lg.font-medium.mr-auto {{ group.name }}
    TaIcon.w-14px.h-14px.text-gray-900.mr-1(
      type='outline/refresh',
      :class='{reset:tagIds.length === 0}'
    )
    .text-gray-700.text-sm.font-normal.cursor-pointer(@click='reset') 重置
  main.px-4.py-2.flex.flex-wrap.gap-2
    ComServeTag.cursor-pointer(
      v-for='(tag,index) in group.tags',
      :text='tag.name'
      :class='{"not__selected":!tagIds.includes(tag.id)}',
      @click='onQuery(tag)'
    )
</template>

<style lang="stylus" scoped>
.com-serve-lib-query-card
  .not__selected
    @apply bg-gray-100 text-gray-900;
  .reset
    animation reset 400ms
@keyframes reset {
  0% {
    transform rotate(0deg)
  }
  100% {
    transform rotate(180deg)
  }
}

</style>
