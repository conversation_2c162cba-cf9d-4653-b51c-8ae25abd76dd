<script lang='ts'>
import { defineComponent, toRefs, onMounted, ref } from 'vue';
import ComServeLibQueryCard from './ComServeLibQueryCard.vue';
import { VStore } from '../../../lib/vails/store/index';
import { ServeUserGroupsApi } from '../../../engines/iest/serve-core/apis/serve/user/groups.api';
const ComServeLibQuery = defineComponent({
  name: 'ComServeLibQuery',
  components: { ComServeLibQueryCard },
  props: {},
  setup(props, { emit }) {
    const tagIds = ref<number[]>([]);
    const groupStore = new VStore(
      new ServeUserGroupsApi({
        parents: [{ type: 'submodules', id: 1 }],
      }),
    );
    onMounted(() => {
      groupStore.index();
    });
    const onQuery = (ids: any) => {
      tagIds.value.push(...ids);
      tagIds.value = tagIds.value.filter((value, index, self) => self.indexOf(value) === index);
      emit('query', tagIds.value);
    };
    const onReset = (ids: any) => {
      tagIds.value = tagIds.value.filter((tag: any) => {
        return !ids.includes(tag);
      });
      tagIds.value = tagIds.value.filter((value, index, self) => self.indexOf(value) === index);
      emit('query', tagIds.value);
    };
    return {
      ...toRefs(props),
      groups: groupStore.records,
      onQuery,
      onReset,
    };
  },
});
export default ComServeLibQuery;
</script>

<template lang="pug">
.com-serve-lib-query.flex.flex-col.gap-4
  ComServeLibQueryCard(v-for='group in groups', :group='group', @query='onQuery', @reset='onReset')
</template>

<style lang="stylus"></style>
