<script lang='ts'>
import { ref, defineComponent, toRefs } from 'vue';
import ComServeTag from '@/components/serve/ComServeTag.vue';
import dayjs from 'dayjs';
const ComServeLibCard = defineComponent({
  name: 'ComServeLibCard',
  components: { ComServeTag },
  props: {
    record: {
      type: Object,
      default: () => ({})
    }
  }, 
  setup(props) {
    return {
      ...toRefs(props),
      dayjs,
    };
  },
});
export default ComServeLibCard;
</script>

<template lang="pug">
.com-serve-lib-card.px-1.py-3.cursor-pointer
  .card__header.flex.items-center.mb-2
    .text-sm.text-gray-700.leading-normal.mr-auto {{ record.name }}
    .text-xs.text-gray-500.leading-normal.mr-2 {{ dayjs(record.published_at).format('YYYY-MM-DD') }}
    TaIcon.w-5.h-5.text-gray-900(type='outline/chevron-right')
  .tags.flex.flex-wrap.gap-1
    ComServeTag(v-for='tag in record.tags',:text='tag.name')
</template>

<style lang="stylus" scoped>
.com-serve-lib-card
  border-bottom 1px solid #E5E7EB
  &:hover
    .card__header
      >div
        @apply text-blue-600;

</style>
