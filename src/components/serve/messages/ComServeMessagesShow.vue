<script lang="ts">
import { DingtalkUserPolymorphicMessagesApi } from '@/apis/dingtalk/user/polymorphic_messages.api';
import ComServeTag from '@/components/serve/ComServeTag.vue';
import { ServeUserMessagesApi } from '@/engines/iest/apis/serve/user/messages.api';
import { AuthSessionApi } from '@/engines/login/apis/auth/session.api';
import { VStore } from '@/lib/vails';
import { message } from 'ant-design-vue';
import { PropType, computed, defineComponent, onMounted, ref, toRefs } from 'vue';
import { useRouter } from 'vue-router';
import TabWithRouter from '../home/<USER>';

const ComServeMessagesShow = defineComponent({
  name: 'ComServeMessagesShow',
  components: {
    ComServeTag,
  },
  props: {
    routerTab: {
      type: Object as PropType<TabWithRouter>,
      required: true,
    },
  },
  setup(props) {
    const router = useRouter();
    const seq = String(router.currentRoute.value.params.seq || '');
    const tableItems = [
      {
        name: '名称',
        data_index: 'name',
        search: true,
        type: 'string',
      },
      {
        name: '单位/部门',
        data_index: 'department_names',
        search: false,
        type: 'string',
      },
    ];
    const user = computed(() => AuthSessionApi.currentUser());
    const shardPermit = computed(
      () =>
        user.value.roles_name?.includes('serve_manage') ||
        user.value.roles_name?.includes('serve_admin'),
    );

    const store = new VStore(new ServeUserMessagesApi({}));

    const breadcrumbs = computed(() => [
      {
        label: '首页',
        href: '/serve/home',
      },
      {
        label: '廉政教育库',
        href: '/serve/home/<USER>',
      },
      {
        label: '正文',
        href: '',
      },
    ]);
    const onClicked = (href: string) => {
      router.push(href);
      props.routerTab.changeTabFollowByRouter(href);
    };
    onMounted(() => {
      store.find(seq);
    });

    const onDownload = (record: any) => {
      if (record.attachments?.files?.[0]?.url) {
        window.open(record.attachments.files[0].url);
      } else {
        record.content?.content?.forEach((item: { videos: any[] }) => {
          if (item.videos && item.videos.length > 0) {
            item.videos?.forEach(video => {
              const videoUrl = video.url;
              // 例如使用window.open打开视频
              if (videoUrl) {
                window.open(videoUrl);
              }
            });
          }
        });
      }
    };
    const dingMStore = new VStore(new DingtalkUserPolymorphicMessagesApi({}));
    const shareUserIds = ref([]);
    const shareToDo = async () => {
      const pc_url = encodeURIComponent(
        `${process.env.VUE_APP_REDIRECT_URL}serve/home/<USER>/${seq}`,
      );
      const mobile_url = encodeURIComponent(
        `${process.env.VUE_APP_MOBILE_REDIRECT_URL}h5/#/pages/serve/messages/show/index?seq=${seq}`,
      );
      let currentTime = new Date();
      let nextMinute = new Date(currentTime.getTime() + 5000);

      const template: any = {
        user_ids: shareUserIds.value,
        async: true,
        scheduled_at: nextMinute,
        message_structure: {
          msgtype: 'action_card',
          action_card: {
            title: '【廉洁提醒】分享消息',
            markdown: '您有一条廉洁教育信息，请查收。',
            single_title: '查看详情',
            single_pc_url: `taurus://taurusclient/action/open_app?corpid=196729&container_type=work_platform&app_id=${process.env.VUE_APP_OAUTH_APP_ID}&redirect_type=jump&redirect_url=${pc_url}`,
            single_url: `taurus://taurusclient/action/open_app?type=1&offline=false&title=廉洁教育&url=${mobile_url}`,
          },
        },
      };
      await dingMStore.create(template).then((res: any) => {
        if (res.status == 'queued') {
          message.success(res.message);
        } else if (res.error_messages.length > 0) {
          res.error_messages.foreach((record: { error_messages: any }) => {
            message.warning(`${record.error_messages}`);
          });
        } else {
          message.success('成功分享');
        }
      });
    };

    const goFiling = () => {
      window.open('https://beian.miit.gov.cn/');
    };

    function getTextFromHTML(htmlString: string) {
      const parser = new DOMParser();
      const doc = parser.parseFromString(htmlString, 'text/html');
      return doc.body.textContent || '';
    }
    const msgContentAndActivityContent = (msg: string, activityContent: Array<any>) => {
      msg = msg || '';
      const htmlString = (activityContent || []).map((item: any) => item.body).join('');
      return msg + getTextFromHTML(htmlString);
    };

    // 添加调试输出

    // 统一处理长路径的数据访问
    const contentData = computed(() => {
      const recordData = store.record?.value || {};
      console.log('recordData: ', recordData.activity);
      return {
        // 标题相关
        title:
          recordData?.payload?.content?.title ||
          recordData?.payload?.card?.title ||
          recordData?.activity?.name ||
          recordData?.name ||
          recordData?.body?.action_card?.title ||
          '廉洁提醒',

        // 活动相关
        hasActivity: !!recordData?.activity?.id,
        activitySource: recordData?.activity?.model_payload?.source,
        activityCreator: recordData?.activity?.model_payload?.create_user,
        activityTags: recordData?.activity?.tags || [],

        // 内容相关
        hasPayloadContent: !!recordData?.payload?.content?.content,
        payloadContent: recordData?.payload?.content?.content,

        // 自定义内容 - 只在没有payload content时显示
        hasCustomContent:
          !recordData?.payload?.content?.content &&
          (!!recordData?.pack?.payload?.use_custom || !!recordData?.content),

        // 活动内容 - 只在没有payload content和自定义内容时显示
        hasActivityContent:
          !recordData?.payload?.content?.content &&
          !recordData?.pack?.payload?.use_custom &&
          !recordData?.content &&
          !!recordData?.activity?.content?.content,
        activityContent: recordData?.activity?.content?.content,
        isActivityContentArray: Array.isArray(recordData?.activity?.content?.content),

        // 图片相关
        hasPayloadImage: !!recordData?.payload?.image?.url,
        payloadImageUrl: recordData?.payload?.image?.url,

        // 附件相关
        hasActivityAttachments: !!recordData?.activity?.attachments,
        activityAttachmentFiles: recordData?.activity?.attachments?.files,
        hasAttachments: !!recordData?.attachments,
        attachmentFiles: recordData?.attachments?.files,

        // 视频相关
        hasVideos: !!recordData?.activity?.content?.content?.[0]?.videos?.length,
        videos: recordData?.activity?.content?.content?.[0]?.videos || [],
      };
    });

    // 添加视频播放器选项
    const videoPlayerOptions = computed(() => ({
      controls: true,
      autoplay: false,
      fluid: true,
      aspectRatio: '16:9',
      playbackRates: [0.5, 1, 1.5, 2],
      width: '100%',
      height: 'auto',
      html5: {
        vhs: {
          overrideNative: true,
          fastPlayThreshold: 10,
        },
        nativeVideoTracks: false,
        nativeAudioTracks: false,
        nativeTextTracks: false,
      },
      techOrder: ['html5'],
    }));

    // 用于直接访问视频URL的方法
    const getDirectVideoUrl = (url: string) => {
      if (!url) return '';
      // 不添加时间戳，避免重复加载视频
      return url;
    };

    return {
      ...toRefs(props),
      breadcrumbs,
      onClicked,
      record: store.record,
      onDownload,
      shardPermit,
      shareToDo,
      shareUserIds,
      tableItems,
      goFiling,
      msgContentAndActivityContent,
      videoPlayerOptions,
      contentData,
      getDirectVideoUrl,
    };
  },
});
export default ComServeMessagesShow;
</script>

<template lang="pug">
.com-serve-messages-show.w-full.bg-gray-50.flex.flex-col.items-center.flex-grow
  .w-1080px.px-6.pt-6.flex-grow.flex.flex-col
    .show__container.w-full.mb-6
      a-breadcrumb(separator='>')
        a-breadcrumb-item(
          v-for='item in breadcrumbs',
          :href='item.href',
          @click='onClicked(item.href)'
        )
          span {{ item.label }}

    article.bg-white.w-full.p-6.rounded-lg.flex-grow.mb-6.flex-col.flex.items-center
      header.article__header.flex.flex-col.items-center.py-4
        .header__title.mb-2 {{ contentData.title }}

        .flex.gap-x-6.mb-4(v-if='contentData.hasActivity')
          .header__desc(v-if='contentData.activitySource') 来源：{{ contentData.activitySource }}
          .header__desc(v-if='contentData.activityCreator') 发布：{{ contentData.activityCreator }}
          TaApiField.ta-api-field(
            v-if='shardPermit',
            v-model:value='shareUserIds',
            :disabled='false',
            path='/res/member/users',
            :tableItems='tableItems',
            :multiple='true',
            @ok='shareToDo'
          )
            template(#display='{ open }')
              TaTextButton.button(icon='ShareAltOutlined', @click.stop='open') 分享
          .action__item.flex.items-center.cursor-pointer(@click='onDownload(record)')
            TaIcon.w-14px.h-14px.text-gray-500.mr-1(type='solid/download')
            .action__item__name 下载

        .tags.flex.flex-wrap.gap-1
          ComServeTag(v-for='tag in contentData.activityTags', :text='tag.name')
      p.pt-2(v-if='contentData.hasPayloadContent', style='text-indent: 2rem') {{ contentData.payloadContent }}
      p.pt-2(
        v-else-if='contentData.hasCustomContent',
        style='text-indent: 2rem'
      ) {{ msgContentAndActivityContent(record.content, contentData.activityContent) }}
      section.pt-2(v-if='contentData.hasActivityContent')
        TaContentField(
          :disabled='true',
          :value='contentData.activityContent',
          v-if='contentData.isActivityContentArray'
        )
        TaContentField(
          :disabled='true',
          :value='[{ body: contentData.activityContent }]',
          v-else
        )
      section.pt-4.flex.justify-center(v-if='contentData.hasPayloadImage')
        img(:src='contentData.payloadImageUrl')
      section.pt-4.flex.justify-center(v-if='contentData.hasActivityAttachments')
        TaAttachments(:attachments='contentData.activityAttachmentFiles', :disabled='true')
      section.pt-4.flex.justify-center(v-if='contentData.hasAttachments')
        TaAttachments(:attachments='contentData.attachmentFiles', :disabled='true')
      section.pt-4.flex.justify-center(v-if='contentData.hasVideos')
        .video-container.w-full.max-w-3xl.overflow-hidden
          video.w-full.mx-auto(
            v-if='contentData.videos[0]'
            controls
            preload="metadata"
            :src='contentData.videos[0].url'
            :poster='contentData.videos[0].poster'
            controlsList="nodownload"
          )
          //- 显示一个直接链接，方便在新窗口打开
          //- .mt-2.text-center
            a.text-blue-500.underline(
              :href='contentData.videos[0]?.url',
              target='_blank'
            ) 视频加载有问题？点此直接打开
      //- section.pt-2(v-if='record.activity?.model_payload?.template_message') {{ record.activity?.model_payload.template_message }}
      //- footer.w-full.p-8.flex.justify-between.items-center.bg-white
      //-   .text-base.font-medium.text-gray-900.cursor-pointer(@click='goFiling') 版权所属: 中共杭州市滨江区纪律检查委员会、杭州市滨江区监察委员会
      //- .text-sm.font-normal.text-gray-500 备案证编号: 浙ICP*********号
</template>

<style lang="stylus" scoped>
@media print
  .show__container
    display none
  footer
    display none
.com-serve-messages-show
  section
    :deep(img)
      margin auto
    :deep(video)
      margin auto
      max-width 100%
      display block
      background-color #000
  .video-container
    background-color #000
    border-radius 4px
    overflow hidden
    video
      max-width 100%
      width 100%
      height auto
  .article__header
    border-bottom 1px solid #E5E7EB
  .header__title
    color #1A56DB
    font-family 'PingFang SC'
    font-size 24px
    font-style normal
    font-weight 500
    line-height 150%
  .header__desc
    color #6B7280
    font-family 'PingFang SC'
    font-size 14px
    font-style normal
    font-weight 400
    line-height 150%
  .action__item__name
    color #374151
    font-family Inter
    font-size 14px
    font-style normal
    font-weight 400
    line-height 150%
  .ta-api-field
    position relative
    top -1px
  footer
    border-top 1px solid #e5e7eb
</style>
