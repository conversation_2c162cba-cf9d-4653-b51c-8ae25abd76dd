<script lang="ts">
import { defineComponent, toRefs, ref } from 'vue';
import ComAssessmentUserQuestionSheetsShow from '@/engines/assessment/components/assessment/question_sheets/ComAssessmentUserQuestionSheetsShow.vue';
import { VStore } from '@/lib/vails';
import { AssessmentUserQuestionSheetsApi } from '@/engines/assessment/assessment-core/apis/assessment/user/question_sheets.api';
import { AssessmentQuestionSheetModel } from '@/engines/assessment/assessment-core/models/assessment/question_sheets';

const ComServeFeedBackShow = defineComponent({
  name: 'ComServeFeedBackShow',
  components: { ComAssessmentUserQuestionSheetsShow },
  props: {},
  emits: ['afterDelete', 'afterExtend'],
  setup(props, { emit }) {
    const isModalVisible = ref(false);
    const feedback = ref('');
    const isHovered = ref(false); // 使用 ref 定义响应式变量

    const store = new VStore(new AssessmentUserQuestionSheetsApi(), AssessmentQuestionSheetModel);

    const showModal = () => {
      store.find(1);
      isModalVisible.value = true;
    };

    const handleOk = () => {
      // 处理提交反馈逻辑
      console.log(feedback.value);
      isModalVisible.value = false;
    };

    const handleCancel = () => {
      isModalVisible.value = false;
    };

    return {
      ...toRefs(props),
      isHovered,
      store,
      record: store.record,
      showModal,
      handleOk,
      handleCancel,
      isModalVisible,
    };
  },
});
export default ComServeFeedBackShow;
</script>

<template lang="pug">
.com-serve-activities-feed-back-show
  .flex.justify-center.items-center.space-x-2.cursor-pointer(
    @click='showModal',
    @mouseover='isHovered = true',
    @mouseout='isHovered = false',
    :class='{ hovered: isHovered }'
  )
    img.icon.w-4.h-5.ml-1(
      v-if='isHovered',
      src='https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/discipline-inspection/Vector.png'
    )
    img.icon.w-5.h-5(
      v-else,
      src='https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/discipline-inspection/Lightbulb.png'
    )
    button 我要反馈

  a-modal.w-full.h-full(
    v-model:visible='isModalVisible',
    title='我要反馈',
    :width='"50%"',
    :bodyStyle='{ height: "50vh" }',
    :footer='null'
  )
    ComAssessmentUserQuestionSheetsShow(
      v-if='record.id',
      :store='store',
      :needAnswer='false',
      :send_emit='true',
      @saveSuccess='handleOk'
    )
</template>

<style lang="stylus" scoped>
.com-serve-activities-feed-back-show
  height 100%
  .hovered
    color #E3A008 // 按钮悬浮时的颜色
    // filter hue-rotate(60deg) // 图标悬浮时的颜色变化
</style>
