<script lang="ts">
import { DingtalkUserPolymorphicMessagesApi } from '@/apis/dingtalk/user/polymorphic_messages.api';
import { AuthSessionApi } from '@/engines/login/apis/auth/session.api';
import { ServeUserActivitiesApi } from '@/engines/iest/apis/serve/user/activities.api';
import { VObject, VStore } from '@/lib/vails';
import { message } from 'ant-design-vue';
import { PropType, computed, defineComponent, onMounted, reactive, ref, toRefs } from 'vue';
import { useRouter } from 'vue-router';
import ComServeTag from '@/components/serve/ComServeTag.vue';
import TabWithRouter from '../home/<USER>';
import ComServeComFeedBackShow from './ComServeFeedBackShow.vue';
import ComServeEvalution from '../ComServeEvalution.vue';
const ComServeActivitiesShow = defineComponent({
  name: 'ComServeActivitiesShow',
  components: {
    ComServeTag,
    ComServeComFeedBackShow,
    ComServeEvalution,
  },
  props: {
    routerTab: {
      type: Object as PropType<TabWithRouter>,
      required: true,
    },
  },
  setup(props) {
    const router = useRouter();
    const activityId = +(router.currentRoute.value.params.activityId || '');
    const tableItems = [
      {
        name: '名称',
        data_index: 'name',
        search: true,
        type: 'string',
      },
      {
        name: '单位/部门',
        data_index: 'department_names',
        search: false,
        type: 'string',
      },
    ];
    const user = computed(() => AuthSessionApi.currentUser());
    const shardPermit = computed(
      () =>
        user.value.roles_name?.includes('serve_manage') ||
        user.value.roles_name?.includes('serve_admin'),
    );

    const store = new VStore(
      new ServeUserActivitiesApi({
        parents: [
          {
            type: 'submodules',
            id: 1,
          },
        ],
      }),
    );

    const breadcrumbs = computed(() => [
      {
        label: '首页',
        href: '/serve/home',
      },
      {
        label: '廉政教育库',
        href: '/serve/home/<USER>',
      },
      {
        label: '正文',
        href: '',
      },
    ]);
    const onClicked = (href: string) => {
      router.push(href);
      props.routerTab.changeTabFollowByRouter(href);
    };
    onMounted(() => {
      store.find(activityId, {
        params: {
          seq: router.currentRoute.value.query.seq || '',
        },
      });
    });

    const onDownload = (record: any) => {
      if (record.attachments?.files?.[0]?.url) {
        window.open(record.attachments.files[0].url);
      } else {
        record.content?.content?.forEach((item: { videos: any[] }) => {
          if (item.videos && item.videos.length > 0) {
            item.videos?.forEach(video => {
              const videoUrl = video.url;
              // 例如使用window.open打开视频
              if (videoUrl) {
                window.open(videoUrl);
              }
            });
          }
        });
      }
    };
    const dingMStore = new VStore(new DingtalkUserPolymorphicMessagesApi({}));
    const shareUserIds = ref([]);
    const shareToDo = async () => {
      const pc_url = encodeURIComponent(
        `${process.env.VUE_APP_REDIRECT_URL}serve/home/<USER>/${activityId}`,
      );
      const mobile_url = encodeURIComponent(
        `${process.env.VUE_APP_MOBILE_REDIRECT_URL}h5/#/pages/serve/activities/show/index?id=${activityId}`,
      );
      let currentTime = new Date();
      let nextMinute = new Date(currentTime.getTime() + 5000);

      const template: any = {
        user_ids: shareUserIds.value,
        async: true,
        scheduled_at: nextMinute,
        message_structure: {
          msgtype: 'action_card',
          action_card: {
            title: '【廉洁提醒】分享消息',
            markdown: '您有一条廉洁教育信息，请查收。',
            single_title: '查看详情',
            single_pc_url: `taurus://taurusclient/action/open_app?corpid=196729&container_type=work_platform&app_id=${process.env.VUE_APP_OAUTH_APP_ID}&redirect_type=jump&redirect_url=${pc_url}`,
            single_url: `taurus://taurusclient/action/open_app?type=1&offline=false&title=廉洁教育&url=${mobile_url}`,
          },
        },
      };
      await dingMStore.create(template).then((res: any) => {
        if (res.status == 'queued') {
          message.success(res.message);
        } else if (res.error_messages.length > 0) {
          res.error_messages.foreach((record: { error_messages: any }) => {
            message.warning(`${record.error_messages}`);
          });
        } else {
          message.success('成功分享');
        }
      });
    };

    const goFiling = () => {
      window.open('https://beian.miit.gov.cn/');
    };

    //让evaluationState最多只能有一个为true

    const evaluationState = reactive({
      satisfaction: false,
      general: false,
      unsatisfied: false,
    });

    const onChecked = (val: boolean, item: VObject) => {
      (evaluationState as any)[item.key] = val;
      if (val) {
        Object.keys(evaluationState).forEach(key => {
          if (key !== item.key) {
            (evaluationState as any)[key] = false;
          }
        });
      }
    };

    const serveEvalutionMap = [
      {
        label: '满意',
        key: 'satisfaction',
        default:
          'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iest/%E6%BB%A1%E6%84%8F-%E8%BE%B9%E6%A1%86.png',
        checkedBg:
          'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iest/%E6%BB%A1%E6%84%8F-%E9%80%89%E4%B8%AD.png',
        unCheckedBg:
          'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iest/%E6%BB%A1%E6%84%8F-%E6%AD%A3%E5%B8%B8.png',
      },
      {
        label: '一般',
        key: 'general',
        default:
          'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iest/%E4%B8%80%E8%88%AC-%E8%BE%B9%E6%A1%86.png',
        checkedBg:
          'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iest/%E4%B8%80%E8%88%AC-%E9%80%89%E4%B8%AD.png',
        unCheckedBg:
          'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iest/%E4%B8%80%E8%88%AC-%E6%AD%A3%E5%B8%B8.png',
      },
      {
        label: '不满意',
        key: 'unsatisfied',
        default:
          'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iest/%E4%B8%8D%E6%BB%A1%E6%84%8F-%E8%BE%B9%E6%A1%86.png',
        checkedBg:
          'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iest/%E4%B8%8D%E6%BB%A1%E6%84%8F-%E9%80%89%E4%B8%AD.png',
        unCheckedBg:
          'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iest/%E4%B8%8D%E6%BB%A1%E6%84%8F-%E6%AD%A3%E5%B8%B8.png',
      },
    ];

    return {
      ...toRefs(props),
      breadcrumbs,
      onClicked,
      record: store.record,
      onDownload,
      shardPermit,
      shareToDo,
      shareUserIds,
      tableItems,
      goFiling,
      serveEvalutionMap,
      evaluationState,
      onChecked,
    };
  },
});
export default ComServeActivitiesShow;
</script>

<template lang="pug">
.com-serve-activities-show.w-full.bg-gray-50.flex.flex-col.items-center.flex-grow
  .w-1080px.px-6.pt-6.flex-grow.flex.flex-col
    .show__container.w-full.mb-6
      a-breadcrumb(separator='>')
        a-breadcrumb-item(
          v-for='item in breadcrumbs',
          :href='item.href',
          @click='onClicked(item.href)'
        )
          span {{ item.label }}
    article.bg-white.w-full.p-6.rounded-lg.flex-grow.mb-6(v-if='record?.id')
      header.article__header.flex.flex-col.items-center.py-4
        .header__title.mb-2 {{ record.name }}

        .flex.gap-x-6.mb-4
          .header__desc(v-if='record.model_payload?.source') 来源：{{ record.model_payload.source }}
          .header__desc(v-if='record.model_payload?.create_user') 发布：{{ record.model_payload.create_user }}
          TaApiField.ta-api-field(
            v-if='shardPermit',
            v-model:value='shareUserIds',
            :disabled='false',
            path='/res/member/users',
            :tableItems='tableItems',
            :multiple='true',
            @ok='shareToDo'
          )
            template(#display='{ open }')
              TaTextButton.button(icon='ShareAltOutlined', @click.stop='open') 分享
          .action__item.flex.items-center.cursor-pointer(@click='onDownload(record)')
            TaIcon.w-14px.h-14px.text-gray-500.mr-1(type='solid/download')
            .action__item__name 下载

        .tags.flex.flex-wrap.gap-1
          ComServeTag(v-for='tag in record.tags', :text='tag.name')
      //- section.pt-2(v-if='record.model_payload?.template_message') {{ record.model_payload.template_message }}
      section.pt-2(v-if='record.content')
        TaContentField(
          :disabled='true',
          :value='record.content.content',
          v-if='Array.isArray(record.content.content)'
        )
        TaContentField(:disabled='true', :value='[{ body: record.content.content }]', v-else)
      section.pt-4.flex.justify-center(v-if='record.attachments')
        TaAttachments(:attachments='record.attachments.files', :disabled='true')
        //- TaVideo.video.max-w-752px.max-h-423px(:src='record.attachments?.files?.[0]?.url')
        //- footer.w-full.p-8.flex.justify-between.items-center.bg-white
        //-   .text-base.font-medium.text-gray-900.cursor-pointer(@click='goFiling') 版权所属: 中共杭州市滨江区纪律检查委员会、杭州市滨江区监察委员会
        //- .text-sm.font-normal.text-gray-500 备案证编号: 浙ICP*********号

      footer.pt-12.w-full.flex.flex-col.items-center
        //- .text-sm.text-gray-500.mb-3 请对本素材进行评价
        //- .grid.grid-cols-3.gap-12.mb-6
        //-   ComServeEvalution(
        //-     v-for='item in serveEvalutionMap'
        //-     :item='item'
        //-     :checked='evaluationState[item.key]'
        //-     @update:checked='(val)=>onChecked(val,item)'
        //-   )
        ComServeComFeedBackShow.mx-auto.my-5

</template>

<style lang="stylus" scoped>
@media print
  .show__container
    display none
  footer
    display none
.com-serve-activities-show
  section
    :deep(img)
      margin auto
    :deep(video)
      margin auto
  .article__header
    border-bottom 1px solid #E5E7EB
  .header__title
    color #1A56DB
    font-family 'PingFang SC'
    font-size 24px
    font-style normal
    font-weight 500
    line-height 150%
  .header__desc
    color #6B7280
    font-family 'PingFang SC'
    font-size 14px
    font-style normal
    font-weight 400
    line-height 150%
  .action__item__name
    color #374151
    font-family Inter
    font-size 14px
    font-style normal
    font-weight 400
    line-height 150%
  .ta-api-field
    position relative
    top -1px
  footer
    border-top 1px solid #e5e7eb
</style>
