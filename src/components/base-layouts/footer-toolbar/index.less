@pro-footer-bar-prefix-cls: ~'@{ant-prefix}-pro-footer-bar';

.@{pro-footer-bar-prefix-cls} {
  position: fixed;
  right: 0;
  bottom: 0;
  z-index: 8;
  display: flex;
  align-items: center;
  width: 100%;
  padding: 0 24px;
  line-height: 44px;
  background: @component-background;
  border-top: 1px solid @border-color-split;
  box-shadow: @shadow-1-up;
  transition: width 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  &-left {
    flex: 1;
  }

  &-right {
    > * {
      margin-right: 8px;
      &:last-child {
        margin: 0;
      }
    }
  }
}
