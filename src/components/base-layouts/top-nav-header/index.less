@import '../basic-layout.less';

@top-nav-header-prefix-cls: ~'@{ant-prefix}-pro-top-nav-header';

.@{top-nav-header-prefix-cls} {
  position: relative;
  width: 100%;
  height: 100%;
  box-shadow: 0 1px 4px 0 rgba(0, 21, 41, 0.12);
  transition: background 0.3s, width 0.2s;
  .@{ant-prefix}-menu {
    background: transparent;
  }
  .ant-menu.ant-menu-dark .ant-menu-item-selected,
  .ant-menu-submenu-popup.ant-menu-dark .ant-menu-item-selected {
    background: @component-background;
  }
  .ant-menu {
    line-height: inherit;
  }

  .@{ant-prefix}-menu-submenu.@{ant-prefix}-menu-submenu-horizontal {
    height: 100%;
    .@{ant-prefix}-menu-submenu-title {
      height: 100%;
    }
  }

  &.light {
    background-color: @component-background;
    .@{top-nav-header-prefix-cls}-logo {
      h1 {
        color: @primary-color;
      }
    }
    .anticon {
      color: inherit;
    }
  }

  &-main {
    display: flex;
    height: 100%;
    padding-left: 16px;
    &-left {
      display: flex;
      min-width: 192px;
    }
  }

  .anticon {
    color: @btn-primary-color;
  }

  &-logo {
    position: relative;
    min-width: 165px;
    height: 100%;
    overflow: hidden;
    transition: all 0.3s;
    img {
      display: inline-block;
      height: 32px;
      vertical-align: middle;
    }
    h1 {
      display: inline-block;
      margin: 0 0 0 12px;
      color: @btn-primary-color;
      font-weight: 400;
      font-size: 16px;
      vertical-align: top;
    }
  }
  &-menu {
    min-width: 0;
    .@{ant-prefix}-menu.@{ant-prefix}-menu-horizontal {
      height: 100%;
      border: none;
    }
  }
}
