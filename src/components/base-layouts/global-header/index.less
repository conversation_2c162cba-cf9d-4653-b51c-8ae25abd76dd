@import '../basic-layout.less';

@pro-layout-global-header-prefix-cls: ~'@{ant-prefix}-pro-global-header';

@pro-layout-header-bg: @component-background;
@pro-layout-header-hover-bg: @component-background;
@pro-layout-header-box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

.@{pro-layout-global-header-prefix-cls} {
  position: relative;
  display: flex;
  align-items: center;
  height: 100%;
  padding: 0 16px;
  background: @pro-layout-header-bg;
  box-shadow: @pro-layout-header-box-shadow;
  > * {
    height: 100%;
  }

  &-collapsed-button {
    display: flex;
    align-items: center;
    margin-left: 16px;
    font-size: 20px;
  }

  &-layout {
    &-mix {
      background-color: @layout-sider-background;
      .@{pro-layout-global-header-prefix-cls}-logo {
        h1 {
          color: @btn-primary-color;
        }
      }
      .anticon {
        color: @btn-primary-color;
      }
    }
  }

  &-logo {
    position: relative;
    overflow: hidden;
    a {
      display: flex;
      align-items: center;
      height: 100%;
      img {
        height: 28px;
      }
      h1 {
        height: 32px;
        margin: 0 0 0 8px;
        margin: 0 0 0 12px;
        color: @primary-color;
        font-weight: 600;
        font-size: 18px;
        line-height: 32px;
      }
    }
  }

  &-menu {
    .anticon {
      margin-right: 8px;
    }
    .@{ant-prefix}-dropdown-menu-item {
      min-width: 160px;
    }
  }

  .dark {
    height: @pro-layout-header-height;
    .action {
      color: rgba(255, 255, 255, 0.85);
      > i {
        color: rgba(255, 255, 255, 0.85);
      }
      &:hover,
      &.opened {
        background: @primary-color;
      }
      .@{ant-prefix}-badge {
        color: rgba(255, 255, 255, 0.85);
      }
    }
  }
}
