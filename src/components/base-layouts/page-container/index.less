@pro-layout-page-container: ~'@{ant-prefix}-pro-page-container';

.@{pro-layout-page-container}-children-content {
  margin: 24px 24px 0;
}

.@{pro-layout-page-container} {
  &-warp {
    background-color: @component-background;
    .@{ant-prefix}-tabs-nav {
      margin: 0;
    }
    // fix antd@4 upgrade PageHeader style.
    .ant-page-header-heading {
      .ant-page-header-heading-title,
      .ant-page-header-heading-extra {
        margin: 4px 0;
      }
      .ant-page-header-heading-sub-title {
        margin: 9px 0;
      }
    }
  }
  &-ghost {
    .@{pro-layout-page-container}-warp {
      background-color: transparent;
    }
  }
}

.@{pro-layout-page-container}-main {
  .@{pro-layout-page-container}-detail {
    display: flex;
  }

  .@{pro-layout-page-container}-row {
    display: flex;
    width: 100%;
  }

  .@{pro-layout-page-container}-title-content {
    margin-bottom: 16px;
  }

  .@{pro-layout-page-container}-title,
  .@{pro-layout-page-container}-content {
    flex: auto;
  }

  .@{pro-layout-page-container}-extraContent,
  .@{pro-layout-page-container}-main {
    flex: 0 1 auto;
  }

  .@{pro-layout-page-container}-main {
    width: 100%;
  }

  .@{pro-layout-page-container}-title {
    margin-bottom: 16px;
  }

  .@{pro-layout-page-container}-logo {
    margin-bottom: 16px;
  }

  .@{pro-layout-page-container}-extraContent {
    min-width: 242px;
    margin-left: 88px;
    text-align: right;
  }
}

@media screen and (max-width: @screen-xl) {
  .@{pro-layout-page-container}-main {
    .@{pro-layout-page-container}-extraContent {
      margin-left: 44px;
    }
  }
}

@media screen and (max-width: @screen-lg) {
  .@{pro-layout-page-container}-main {
    .@{pro-layout-page-container}-extraContent {
      margin-left: 20px;
    }
  }
}

@media screen and (max-width: @screen-md) {
  .@{pro-layout-page-container}-main {
    .@{pro-layout-page-container}-row {
      display: block;
    }

    .@{pro-layout-page-container}-action,
    .@{pro-layout-page-container}-extraContent {
      margin-left: 0;
      text-align: left;
    }
  }
}

@media screen and (max-width: @screen-sm) {
  .@{pro-layout-page-container}-detail {
    display: block;
  }
  .@{pro-layout-page-container}-extraContent {
    margin-left: 0;
  }
}
