<script lang="ts">
import { ref, defineComponent, toRefs, computed } from 'vue';
import dayjs from 'dayjs';

const ServePackFlowable = defineComponent({
  name: 'ServePackFlowable',
  components: {},
  props: {
    record: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props) {
    const dateFormat = (date: string) => {
      if (!date) return '-';
      return dayjs(date).format('YYYY-MM-DD HH:mm:ss');
    };

    const infoShowInOneLine = computed(() => ({
      项目联系人名称: props.record?.flowable_info?.source?.contactor_name,
      项目联系人联系方式: props.record?.flowable_info?.source?.phone,
      统一信用代码: props.record?.flowable_info?.source?.unit_code,
      // 项目负责人证件号码
    }));

    const infoShowInTwoLine = computed(() => ({
      项目建立时间: dateFormat(props.record?.flowable_info?.source?.setup_at),
      // 招标范围: props.record?.flowable_info?.source?.content?.range,
      项目金额: props.record?.flowable_info?.source?.amount,
      项目编号: props.record?.flowable_info?.source?.code,
      //报名开始时间: dateFormat(props.record?.flowable_info?.source?.start_at),
      //报名截止时间: dateFormat(props.record?.flowable_info?.source?.end_at),
      //开标日期: dateFormat(props.record?.flowable_info?.source?.open_at),
    }));
    const activeKey = ref(['1']);
    return {
      ...toRefs(props),
      infoShowInOneLine,
      infoShowInTwoLine,
      activeKey,
    };
  },
});
export default ServePackFlowable;
</script>

<template lang="pug">
.serve-pack-flowable(v-if='record?.flowable_info?.rule?.type === "Serve::BidRule"')
  a-collapse(v-model:activeKey='activeKey')
    a-collapse-panel(key='1', :header='record?.flowable_info?.source?.name')
      .h-320px.flex.flex-col
        .text-lg.text-sm.font-semibold.mb-2 {{ record.flowable_info?.source?.unit }}
        .h-1px.w-full.mb-2(
          style='background: linear-gradient(90deg, #fff 0%, rgba(255, 255, 255, 0) 100%)'
        )
        .content__wrapper.flex-grow.w-full.h-0.overflow-auto.space-y-4
          .one-line-wrapper.space-y-2.text-sm
            .form__item(v-for='(value, key) in infoShowInOneLine') {{ key }}: {{ value }}
          .two-line-wrapper.text-sm.grid.grid-cols-2.gap-4
            .grid__item(v-for='(value, key) in infoShowInTwoLine')
              .text-primary-300.mb-6px {{ key }}
              .text-sm {{ value }}
          TaAttachments(:attachments='record?.attachments')
</template>

<style lang="stylus" scoped></style>
