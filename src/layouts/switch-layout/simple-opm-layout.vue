<script lang="ts">
import TofuLayoutConstruction from '@/engines/tofu/components/global/layout/TofuLayoutConstruction.vue';
import store from '@/store';
import { GET_INFO } from '@/engines/login/store/user/actions';
import { defineComponent, onMounted } from 'vue';
import ComLayoutHeader from './ComLayoutHeader.vue';

const SimpleOpmLayout = defineComponent({
  name: 'SimpleOpmLayout',
  components: {
    ComLayoutHeader,
    TofuLayoutConstruction,
  },
  setup() {
    onMounted(() => {
      store.dispatch(`user/${GET_INFO}`);
    });

    return {
    };
  },
});
export default SimpleOpmLayout;
</script>

<template lang="pug">
TofuLayoutConstruction.opm-layout(:needSubMenu='false')
  template(#header)
    ComLayoutHeader
  template(#sidebar)
  template(#body)
    .pt-20.w-full.h-full
      slot
  TaToasts
</template>

<style lang="stylus" scoped>
.opm-layout
  height 100%
  width 100%
  overflow hidden
</style>
