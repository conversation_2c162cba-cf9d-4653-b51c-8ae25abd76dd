<script lang="ts">
import useAdminTofu from '@/engines/tofu/components/useAdminTofu';
import useAutoMenu, { state } from '@/engines/tofu/components/useAutoMenu';
import { SettingOutlined } from '@ant-design/icons-vue';
import { computed, defineComponent, onMounted } from 'vue';

import TaUserAvatarDropdown from '@/components/global/ta-component/TaUserAvatarDropdown.vue';

export default defineComponent({
  name: 'MessageLayoutSidebar',
  components: {
    TaUserAvatarDropdown,
    SettingOutlined,
  },
  props: {
    width: {
      type: String,
      default: '200px',
    },
    currentUser: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props) {
    const { fetchMenuTree, onSelectMenu } = useAutoMenu();
    const { haveAnyAdminPermit, toSetting } = useAdminTofu();
    const info = computed(() => props.currentUser);
    onMounted(() => {
      fetchMenuTree();
    });

    const messageMenuItems = computed(() => {
      return state.menuTree.filter((menu: any) => menu.layout === 'message');
    });

    const selectedKeys = computed(() => state.menuChain.map(record => record?.id));

    return {
      messageMenuItems,
      onSelectMenu,
      selectedKeys,
      info,
      haveAnyAdminPermit,
      toSetting,
    };
  },
});
</script>

<template lang="pug">
.message-sidebar.flex.h-full
  .icon-bar.flex-shrink-0.bg-gray-100.flex.flex-col.items-center.py-4
    .header-icon.icon-item.w-10.h-10.mb-4.rounded-lg.flex.items-center.justify-center
      span.text-white.font-bold AI

    .flex-1

    .icon-item.w-10.h-10.mb-2.flex.items-center.justify-center.cursor-pointer.rounded-lg.transition-colors(
      v-if='haveAnyAdminPermit',
      @click='toSetting'
    )
      SettingOutlined.text-white.text-xl.transition-colors(class='hover:text-blue-500')

    TaUserAvatarDropdown(:user='info')

  .menu-area.flex-1.bg-white.h-full.flex.flex-col.border-l.border-gray-200(
    :style='`width:${width};`'
  )
    .menu-list.flex-1.overflow-y-auto.py-2
      a-menu.body.menu-list__menu(:selectedKeys='selectedKeys', mode='inline')
        template(v-for='record in messageMenuItems', :key='record.id')
          a-menu-item(v-if='!record.children || record.children.length === 0', :key='record.id')
            .flex.items-center.w-fit(@click='onSelectMenu(record)')
              TaIcon.menu__ta-icon.w-6.h-6.mr-3.text-gray-400.flex-shrink-0(
                :type='`${record.icon || "solid/document-text"}`',
                :size='20'
              )
              div {{ record.name }}

          a-sub-menu(v-else, :title='record.name', :key='record.id')
            template(#icon)
              TaIcon.menu__ta-icon.w-6.h-6.mr-3.text-gray-400.flex-shrink-0(
                :type='`${record.icon || "solid/document-text"}`',
                :size='20'
              )

            template(#title)
              div {{ record.name }}

            a-menu-item(
              v-for='subRecord in record.children',
              :title='subRecord.name',
              :key='subRecord.id'
            )
              .ml-2.flex.items-center.w-fit(@click='onSelectMenu(subRecord)')
                TaIcon.menu__ta-icon.w-6.h-6.mr-3.text-gray-400.flex-shrink-0(
                  :type='`${subRecord.icon || "solid/document-text"}`',
                  :size='20'
                )
                div {{ subRecord.name }}
</template>

<style lang="stylus" scoped>
.message-sidebar
  background-color #f0f2f5
  box-shadow 2px 0px 8px 0px rgba(0, 0, 0, 0.08)
.icon-bar
  width 60px
  .icon-item
    &:hover
      background-color rgba(0, 0, 0, 0.05)
  .header-icon
    background-color $primary-color
.menu-area
  background-color #fff
.menu-list__menu
  &.ant-menu
    border-right none
  .ant-menu-inline
    border-right none
  .ant-menu-item, .ant-menu-submenu-title
    margin-top 4px
    margin-bottom 4px
    height 40px
    line-height 40px
    .flex
      margin-left 16px
  .ant-menu-item::after
    border-right 3px solid #1890ff
  .ant-menu-item-selected
    background-color #e6f7ff
    color #1890ff
  .ant-menu-item:hover, .ant-menu-submenu-title:hover
    color #1890ff
</style>
