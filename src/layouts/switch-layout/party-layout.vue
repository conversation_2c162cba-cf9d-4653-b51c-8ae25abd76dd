<script lang="ts">
import { MultiTab } from '@/components/multi-tab';
import TofuLayoutConstruction from '@/engines/tofu/components/global/layout/TofuLayoutConstruction.vue';
import store from '@/store';
import { GET_INFO } from '@/engines/login/store/user/actions';
import { defineComponent, onMounted, ref, watch } from 'vue';
import ComLayoutSidebarMenu from '../ComLayoutSidebarMenu.vue';
import ComLayoutHeader from './ComLayoutHeader.vue';
import { useDraggable, useMouse } from '@vueuse/core';
import { useRouter } from 'vue-router';

const PartyLayout = defineComponent({
  name: 'PartyLayout',
  components: {
    MultiTab,
    ComLayoutSidebarMenu,
    ComLayoutHeader,
    TofuLayoutConstruction,
  },
  setup() {
    onMounted(() => {
      store.dispatch(`user/${GET_INFO}`);
    });

    const ai = ref<HTMLElement | null>(null);
    const router = useRouter();

    const { x, y, style, isDragging } = useDraggable(ai, {
      initialValue: { x: window.innerWidth - 100, y: window.innerHeight - 200 },
    });

    let lastDragTime = 0;
    const isDragged = ref(false);
    watch(isDragging, (newVal: boolean, oldVal: boolean) => {
      if (!newVal && oldVal) {
        lastDragTime = Date.now();
      }
    });
    const onClickAiIcon = () => {
      const cur = Date.now();
      if (isDragging.value) {
        return;
      }

      if (cur - lastDragTime < 50 && isDragged.value) {
        return (isDragged.value = false);
      }

      return router.push('/bot/user/conversations/new');
    };

    const onDragStart = (event: DragEvent) => {
      isDragged.value = true;
      if (event?.dataTransfer) {
        event.dataTransfer.effectAllowed = 'move';
        ai.value!.style.visibility = 'hidden';
      }
    };

    const onDragEnd = (event: DragEvent) => {
      ai.value!.style.visibility = 'visible';
    };

    return {
      ai,
      style,
      isDragging,
      x,
      y,
      onClickAiIcon,
      onDragStart,
      onDragEnd,
    };
  },
});
export default PartyLayout;
</script>

<template lang="pug">
.relative
  .ai__icon.fixed.z-1001.w-15.h-15.bg-red-500.rounded-full.bg-cover.bg-center.select-none(
    @click.stop='onClickAiIcon'
    :draggable='true',
    ref='ai',
    :class='{ "cursor-move": isDragging,"cursor-pointer":!isDragging }'
    style="touch-action:none;"
    :style="style",
    @dragstart='onDragStart',
    @dragend='onDragEnd'
  )
  TofuLayoutConstruction.party-layout
    template(#header)
      ComLayoutHeader
    template(#sidebar)
      .mt-15.h-full
        ComLayoutSidebarMenu.menu(:needFetch='false', :needBottom='false')
    template(#tabs)
      .mt-15
        MultiTab
    template(#body)
        slot
    TaToasts
</template>

<style lang="stylus" scoped>
.ai__icon
  background-image url("https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/discipline-inspection/%E6%BB%A8%E7%B3%96-%E6%97%A0%E9%99%90-%E5%9C%86.png")
.party-layout
  height 100vh
  width 100%
  overflow hidden
  >>>.com-tofu-sidebar-menu
    .main-menu
      height calc(100vh - 60px)
  @media print
    .navbar, .menu, .tabs
      display none
    .content
      padding 0
      height auto
      width 100vw
      .main-body
        padding 0
        width 100%
        height auto
        .body
          width 100%
</style>
