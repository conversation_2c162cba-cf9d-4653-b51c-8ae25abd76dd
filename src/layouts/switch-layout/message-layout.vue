<script lang="ts">
import TofuLayoutConstruction from '@/engines/tofu/components/global/layout/TofuLayoutConstruction.vue';
import store from '@/store';
import { useDraggable } from '@vueuse/core';
import { computed, defineComponent, ref, watch } from 'vue';
import { useRouter } from 'vue-router';
import MessageLayoutSidebar from './components/MessageLayoutSidebar.vue';

const MessageLayout = defineComponent({
  name: 'MessageLayout',
  components: {
    TofuLayoutConstruction,
    MessageLayoutSidebar,
  },
  setup() {
    const currentUser = computed(() => store.state.user.currentUser || {});
    const ai = ref<HTMLElement | null>(null);
    const router = useRouter();
    let lastDragTime = 0;
    const isDragged = ref(false);

    const { x, y, style, isDragging } = useDraggable(ai, {
      initialValue: { x: window.innerWidth - 100, y: window.innerHeight - 200 },
    });
    const onClickAiIcon = () => {
      const cur = Date.now();
      if (isDragging.value) {
        return;
      }

      if (cur - lastDragTime < 50 && isDragged.value) {
        return (isDragged.value = false);
      }

      return router.push('/bot/user/conversations/new');
    };
    const onDragStart = (event: DragEvent) => {
      isDragged.value = true;
      if (event?.dataTransfer) {
        event.dataTransfer.effectAllowed = 'move';
        ai.value!.style.visibility = 'hidden';
      }
    };

    const onDragEnd = (event: DragEvent) => {
      ai.value!.style.visibility = 'visible';
    };
    watch(isDragging, (newVal: boolean, oldVal: boolean) => {
      if (!newVal && oldVal) {
        lastDragTime = Date.now();
      }
    });
    return {
      currentUser,
      ai,
      style,
      isDragging,
      x,
      y,
      onClickAiIcon,
      onDragStart,
      onDragEnd,
    };
  },
});

export default MessageLayout;
</script>

<template lang="pug">
.relative
  .ai__icon.fixed.z-1001.w-15.h-15.bg-red-500.rounded-full.bg-cover.bg-center.select-none(
    @click.stop='onClickAiIcon'
    :draggable='true',
    ref='ai',
    :class='{ "cursor-move": isDragging,"cursor-pointer":!isDragging }'
    style="touch-action:none;"
    :style="style",
    @dragstart='onDragStart',
    @dragend='onDragEnd'
  )
TofuLayoutConstruction.message-layout
  template(#sidebar)
    MessageLayoutSidebar(:currentUser="currentUser")
  template(#body)
    slot
</template>

<style lang="stylus" scoped>
.ai__icon
  background-image url("https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/discipline-inspection/%E6%BB%A8%E7%B3%96-%E6%97%A0%E9%99%90-%E5%9C%86.png")

.message-layout
  height 100vh
  width 100%
  overflow hidden
  background-color #fff

@media print
  .navbar, .sidebar-menu
    display none
  .content
    padding 0
    height auto
    width 100vw
</style>
