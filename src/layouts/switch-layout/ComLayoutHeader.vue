<script lang="ts">
import TaUserAvatarDropdown from '@/components/global/ta-component/TaUserAvatarDropdown.vue';
import useDDLink from '@/engines/dingtalk/utils/useDDLink';
import { AuthSessionApi } from '@/engines/login/apis/auth/session.api';
import ComSocketIo from '@/engines/notify/socket/ComSocketIo.vue';
import { useAdminTofuPermit2PathMapping } from '@/engines/tofu/components/useAdminTofu';
import { computed, defineComponent, toRefs } from 'vue';
import { useRouter } from 'vue-router';

const ComLayoutHeader = defineComponent({
  name: 'ComLayoutHeader',
  components: {
    ComSocketIo,
    TaUserAvatarDropdown,
  },
  props: {},
  setup(props) {
    const router = useRouter();

    const info = computed(() => {
      return AuthSessionApi.currentUser();
    });

    const logout = async () => {
      await new AuthSessionApi().logout();
      // 使用 location 跳转来彻底清空所有 store
      window.location.href = process.env.VUE_APP_LOGIN_HREF || '/login';
    };

    const haveAnyAdminPermit = computed(
      () =>
        (
          info.value.roles_name?.filter(
            (permit: string) => !!useAdminTofuPermit2PathMapping[permit],
          ) || []
        ).length > 0,
    );

    const toSetting = () => {
      const existedPermit = Object.keys(useAdminTofuPermit2PathMapping).find((permit: string) =>
        info.value.roles_name.includes(permit),
      );
      if (existedPermit) {
        router.push(useAdminTofuPermit2PathMapping[existedPermit]);
      }
    };

    const title = document.title;

    const { linkTo } = useDDLink();

    return {
      ...toRefs(props),
      haveAnyAdminPermit,
      toSetting,
      logout,
      info,
      window,
      title,
      linkTo,
    };
  },
});
export default ComLayoutHeader;
</script>

<template lang="pug">
.com-layout-header.navbar
  .brand
    .logo.w-9.h-9.rounded-full.overflow-hidden.mr-10px.bg-white
      img(
        src='https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/discipline-inspection/%E6%BB%A8%E7%B3%96-%E6%97%A0%E9%99%90-%E5%9C%86.png',
        height='50'
      )
    .name.text-2xl.text-white.font-medium.tracking-widest
      | {{ title }}
    //- img.back-home(src="@/assets/icons/tertiary.svg", height="20", width="20", @click="onBackHome")
  .menus.space-x-2.text-white
    TaIcon.setting(
      v-if='haveAnyAdminPermit',
      type='SettingFilled',
      @click='toSetting',
      :size='20'
    )
    .text-sm |

    TaIcon.setting(
      type='SnippetsFilled',
      @click='linkTo("/serve/home",false)',
      :size='20'
    )

    .text-sm |

    TaIcon.setting(
      type='FullscreenOutlined',
      @click='linkTo("/iest-web/iest/screen/17/dashboard")',
      :size='20'
    )

    .text-sm |

    ComSocketIo
      template(#default='{ ReadMap }')
        a-badge(:count='ReadMap.unread', :overflow-count='99')
          TaIcon(type='BellFilled', :size='20',color='white')

    .text-sm |

    TaUserAvatarDropdown(:user='info', @logout='logout', :whiteBackground='true')
</template>

<style lang="stylus" scoped>
.navbar
  position absolute
  top 0px
  left 0px
  width 100%
  display flex
  justify-content space-between
  align-items center
  padding 0px 16px
  height 60px
  background $primary-color
  color white
  z-index 3
  box-shadow 0 0 8px 0 rgba(0, 0, 0, 0.1)
  .brand
    flex-shrink 0
    // width 370px
    width 430px
    display flex
    align-items center
    // line-height 28px
    .back-home
      cursor pointer
  .menus
    display flex
    flex-shrink 0
    justify-content flex-end
    align-items center
    min-width 320px
    .templatex
      display flex
      align-items center
      padding 20px 0
      width 180px
    .user-info
      margin-top -10px
      .avatar-dropdown-btn
        >>> .ant-avatar-circle
          border 1px solid white
@media print
  .navbar
    display none
</style>
