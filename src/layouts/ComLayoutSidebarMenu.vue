<script lang="ts">
import store from '@/store';
import { NotificationOutlined } from '@ant-design/icons-vue';
import { computed, defineComponent, onMounted, PropType } from 'vue';
import { AuthSessionApi } from '../engines/login/apis/auth/session.api';
import useAdminTofu from '../engines/tofu/components/useAdminTofu';
import useAutoMenu, { state } from '../engines/tofu/components/useAutoMenu';
import { TofuEntry } from '../engines/tofu/model';

const ComTofuSidebarMenu = defineComponent({
  name: 'ComTofuSidebarMenu',
  components: {
    NotificationOutlined,
  },
  props: {
    needFetch: { type: Boolean, default: true },
    menus: { type: Array as PropType<TofuEntry[]>, default: undefined },
  },
  setup(props) {
    const { fetchMenuTree, mainMenu, mainMenus, onSelectMenu } = useAutoMenu();
    // const homeMenu = { id: -1, url: '/menus', icon: '', name: '主页' };
    const selectedKeys = computed(() => state.menuChain.map(record => record?.id));
    onMounted(() => {
      if (props.needFetch) fetchMenuTree();
    });

    const info = computed(() => store.state.user.currentUser);

    const logout = async () => {
      await new AuthSessionApi().logout();
      // 使用 location 跳转来彻底清空所有 store
      window.location.href = process.env.VUE_APP_LOGIN_HREF || '/login';
    };

    const { haveAnyAdminPermit, toSetting } = useAdminTofu();

    return {
      info,
      logout,
      // homeMenu,
      selectedKeys,
      mainMenu,
      mainMenus,
      onSelectMenu,
      haveAnyAdminPermit,
      toSetting,
    };
  },
});

export default ComTofuSidebarMenu;
</script>

<template lang="pug">
.com-layout-sidebar-menu
  .main-menu
    .top.p-3.w-full
      a-menu.body(
        :selectedKeys='selectedKeys',
        mode='inline'
      )
        template(
          v-for='(record,index) in mainMenus.filter((x)=> x.id > 0 && x.layout !== "message")'
          :key='record.id',
        )
          a-menu-item(
            v-if='record.children.length === 0',
            :key='record.id',
          )
            .divide-border(v-if='record.type === "Tofu::Split"')
            .flex.items-center.w-fit(v-else,@click='onSelectMenu(record)')
              TaIcon.menu__ta-icon.w-6.h-6.mr-3.text-gray-400.flex-shrink-0(
                :type='`${record.icon || "solid/document-text"}`',
                :size='20'
              )
              div {{ record.name }}

          a-sub-menu(v-else, :title='record.name', :key='record.id')
            template(#icon)
              TaIcon.menu__ta-icon.w-6.h-6.mr-3.text-gray-400.flex-shrink-0(
                :type='`${record.icon || "solid/document-text"}`',
                :size='20'
              )

            template(#title)
              div {{ record.name }}

            a-menu-item(
              v-for='subRecord in record.children',
              :title='subRecord.name',
              :key='subRecord.id',
            )
              .ml-2.flex.items-center.w-fit(@click='onSelectMenu(subRecord)')
                TaIcon.menu__ta-icon.w-6.h-6.mr-3.text-gray-400.flex-shrink-0(
                  :type='`${subRecord.icon || "solid/document-text"}`',
                  :size='20'
                )
                div {{ subRecord.name }}
      //- .navigation(v-for='menu in (menus || mainMenus.filter(menu => menu.depth >= 0))')
      //- ComTofuMainMenu(
      //-   :menu='menu',
      //-   :active='mainMenu?.id === menu.id',
      //-   @click.native='onSelectMenu(menu)'
      //- )
</template>

<style lang="stylus">
.com-layout-sidebar-menu
  display flex
  height 100%
  .main-menu
    display flex
    padding-bottom 4rem
    overflow-y auto
    flex-direction column
    justify-content space-between
    align-items center
    z-index 999
    background white
    width 256px
    box-shadow 2px 0px 8px 0px rgba(0, 0, 0, 0.08)
    >>> .ant-menu-vertical-left
      border-right 0px solid #fff
</style>
