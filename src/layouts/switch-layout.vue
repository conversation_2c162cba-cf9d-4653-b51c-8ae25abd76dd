<script lang="ts">
import useAutoMenu, { state } from '@/engines/tofu/components/useAutoMenu';
import { computed, defineComponent, onMounted } from 'vue';
import { useRoute } from 'vue-router';
import { MultiTabStoreConsumer } from '../components/multi-tab/multi-tab-store';
import TofuAdminLayout from '../engines/tofu/components/global/layout/TofuAdminLayout.vue';
import TofuSidebarLayout from '../engines/tofu/components/global/layout/TofuSidebarLayout.vue';
import MessageLayout from './switch-layout/message-layout.vue';
import PartyLayout from './switch-layout/party-layout.vue';
import SimpleLayout from './switch-layout/simple-layout.vue';

const SwitchLayout = defineComponent({
  name: 'SwitchLayout',
  components: {
    SimpleLayout,
    MultiTabStoreConsumer,
    TofuAdminLayout,
    TofuSidebarLayout,
    PartyLayout,
    MessageLayout,
  },
  setup() {
    const { fetchMenuTree } = useAutoMenu();
    const route = useRoute();
    onMounted(() => {
      fetchMenuTree();
    });

    const selectedMainTofu = computed(() => {
      const lastIndex = state.menuChain.length - 1;
      return state.menuChain[lastIndex];
    });

    const layoutComponent = computed(() => {
      // 优先检查路由元信息中的布局设置
      const routeMeta: any = route.meta;
      if (routeMeta?.layout === 'message') {
        return 'MessageLayout';
      }

      // 然后检查菜单项的布局设置
      if (selectedMainTofu.value?.layout === 'message') {
        return 'MessageLayout';
      }

      if (selectedMainTofu.value?.layout === 'simple') {
        return 'SimpleLayout';
      }
      if (selectedMainTofu.value?.layout === 'TofuAdminLayout' || selectedMainTofu.value?.id < 0) {
        return 'TofuAdminLayout';
      }

      if (routeMeta?.layout === 'simpleOpm') {
        return 'SimpleOpmLayout';
      }

      return 'PartyLayout';
    });

    return {
      layoutComponent,
    };
  },
});

export default SwitchLayout;
</script>

<template lang="pug">
keep-alive
  component(:is='layoutComponent')
    router-view(v-slot='{ Component }')
      MultiTabStoreConsumer
        component.route-view__shell(v-if='Component', :is='Component')
        slot(v-else)
</template>

<style lang="stylus" scoped>
.route-view__shell
  padding 0 24px 20px 24px
  width 100%
  flex 1
</style>
