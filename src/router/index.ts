import { Component } from 'vue';
import { createRouter, createWebHistory } from 'vue-router';
import { MenuDataItem } from './typing';
import SwitchLayout from '../layouts/switch-layout.vue';
import useAuth from './useAuth';

const routes: MenuDataItem[] = [];
// 引入 engine 文件夹下文件
const engineRoute = require.context('./../engines/', true, /\.route\.ts$/);

engineRoute.keys().forEach(fileName => {
  const moduleRoutes = engineRoute(fileName).default;
  if (Array.isArray(moduleRoutes)) {
    routes.push(...moduleRoutes);
  }
});

// 引入 router 文件夹下文件
const requireRoute = require.context('.', true, /\.route\.ts$/);

requireRoute.keys().forEach(fileName => {
  const moduleRoutes = requireRoute(fileName).default;
  if (Array.isArray(moduleRoutes)) {
    routes.push(...moduleRoutes);
  }
});

const { checkAuth } = useAuth();

const beforeEntry = (to: any, from: any, next: any) => {
  if (to.path.includes('/serve/user/activities')) return next();
  checkAuth(to.path).then(() => next());
};

export const staticRoutes: MenuDataItem[] = [
  {
    name: 'index',
    path: '/',
    redirect: process.env.VUE_APP_HOME_PATH || '/home',
    component: SwitchLayout,
    beforeEnter: [beforeEntry],
    children: [
      ...routes,
      {
        path: '/workplane',
        name: 'workplane',
        meta: { title: '工作台' },
        component: (): Component => import('@/views/workplane/Index.vue'),
      },
      {
        path: '/opm/workplane',
        name: 'opm-workplane',
        meta: { title: '人事工作台' },
        component: (): Component => import('@/views/workplane/opm/Index.vue'),
      },
      {
        path: '/res/user',
        meta: { title: '人员管理' },
        component: () => import('@/views/user/index.vue'),
      },
      {
        path: '/res/tags',
        meta: { title: '人员标签' },
        component: () => import('@/views/tag/index.vue'),
      },
    ],
  },
  {
    path: '/test/view',
    name: 'test-view',
    meta: { icon: 'HistoryOutlined', title: 'pages.dashboard.welcome.title' },
    component: (): Component => import('@/views/test/view/index.vue'),
  },
  {
    path: '/serve/home',
    component: () => import('@/views/serve/home/<USER>'),
    children: [
      {
        path: 'lib',
        component: () => import('@/components/serve/lib/ComServeLibIndex.vue'),
      },
      {
        path: '',
        component: () => import('@/components/serve/home/<USER>'),
      },
      {
        path: 'activities/:activityId',
        component: () => import('@/components/serve/activities/ComServeActivitiesShow.vue'),
      },
      {
        path: 'messages/:seq',
        component: () => import('@/components/serve/messages/ComServeMessagesShow.vue'),
      },
    ],
  },
  {
    path: '/test/show',
    name: 'test-show',
    beforeEnter: [beforeEntry],
    component: (): Component => import('@/views/test/view/Show.vue'),
  },
  {
    path: '/screen/user/page_settings/:pageSettingId',
    name: 'ScreenUserPageSettings',
    beforeEnter: [beforeEntry],
    component: () =>
      import(
        /* webpackChunkName: "ScreenUserPageSettings" */ '@/engines/screen/views/screen/user/page_settings/Show.vue'
      ),
    meta: {
      title: '页面',
    },
  },
  // {
  //   path: '/loading',
  //   name: 'loading',
  //   component: () => import(/* webpackChunkName: "loading" */ '@/views/loading/Index.vue'),
  // },
  {
    path: '/login',
    meta: { icon: 'HistoryOutlined', title: '登录' },
    component: (): Component => import('@/views/sessions/login.vue'),
  },
  {
    name: 'ChatUserConversations',
    path: '/chat/user/',
    beforeEnter: [beforeEntry],
    component: () =>
      import(
        /* webpackChunkName: "ChatUserConversations" */
        '@/engines/chat/components/chat/conversations/ComChatConversationLayout.vue'
      ),
    children: [
      {
        path: '/chat/user/conversations/:conversationId',
        name: 'ChatUserConversationsShow',
        component: () =>
          import(
            /* webpackChunkName: "ChatUserConversationsShow" */ '@/engines/chat/views/chat/user/conversations/Show.vue'
          ),
        meta: {
          title: 'AI',
        },
      },
      {
        path: '/chat/user/conversations/new',
        name: 'ChatUserConversationsNew',
        component: () =>
          import(
            /* webpackChunkName: "ChatUserConversationsNew" */ '@/engines/chat/views/chat/user/conversations/New.vue'
          ),
        meta: {
          title: 'AI',
        },
      },
    ],
  },
  {
    name: 'BotUserChat',
    path: '/bot/user/',
    beforeEnter: [beforeEntry],
    component: () =>
      import(
        /* webpackChunkName: "BotUserChat" */
        '@/engines/bot/components/conversations/ComBotConversationLayout.vue'
      ),
    children: [
      {
        path: '/bot/user/conversations/new',
        name: 'BotUserConversationNew',
        component: () =>
          import(
            /* webpackChunkName: "BotUserConversationNew" */ '@/engines/bot/views/bot/user/conversations/New.vue'
          ),
        meta: {
          title: 'AI',
        },
      },
      {
        path: '/bot/user/conversations/:conversationId',
        name: 'BotUserConversationsShow',
        component: () =>
          import(
            /* webpackChunkName: "BotUserConversationsShow" */ '@/engines/bot/views/bot/user/conversations/Show.vue'
          ),
        meta: {
          title: 'AI',
        },
      },
    ],
  },
  {
    name: 'iestScreenShow',
    path: '/iest/screen/:screenId',
    beforeEnter: [beforeEntry],
    component: () =>
      import(/* webpackChunkName: "iestScreenShow" */ '@/engines/iest/views/iest/screen/Show.vue'),
    children: [
      {
        path: 'dashboard',
        name: 'iestScreenDashboard',
        component: () =>
          import(
            /* webpackChunkName: "iestScreenDashboard" */ '@/engines/iest/views/iest/screen/Dashboard.vue'
          ),
        meta: {
          title: 'AI',
        },
      },
      {
        path: 'ai',
        name: 'iestScreenAi',
        redirect: 'ai/conversations/new',
        component: () =>
          import(/* webpackChunkName: "iestScreenAi" */ '@/engines/iest/views/iest/screen/Ai.vue'),
        meta: {
          title: 'AI',
          type: 'screen',
        },
        children: [
          {
            path: 'conversations/:conversationId',
            name: 'iestScreenShowChatUserConversationsShow',
            component: () =>
              import(
                /* webpackChunkName: "iestScreenShowChatUserConversationsShow" */ '@/engines/chat/views/chat/user/conversations/Show.vue'
              ),
            meta: {
              title: 'AI',
              type: 'screen',
            },
          },
          {
            path: 'conversations/new',
            name: 'iestScreenShowChatUserConversationsNew',
            component: () =>
              import(
                /* webpackChunkName: "iestScreenShowChatUserConversationsNew" */ '@/engines/chat/views/chat/user/conversations/New.vue'
              ),
            meta: {
              title: 'AI',
              type: 'screen',
            },
          },
        ],
      },
    ],
    meta: {
      title: 'AI',
      type: 'screen',
    },
  },

  {
    path: '/:pathMatch(.*)',
    component: () => import('@/views/exception/404.vue'),
  },
];

const router = createRouter({
  history: createWebHistory(process.env.VUE_APP_PUBLIC_PATH),
  routes: staticRoutes,
});

export default router;
