export default {
  'pages.home': 'Home',
  'pages.dashboard.title': 'Dashboard',
  'pages.dashboard.analysis.title': 'Analysis',
  'pages.dashboard.monitor.title': 'Monitor',
  'pages.dashboard.workplace.title': 'Workplace',
  'pages.dashboard.welcome.title': 'Welcome',

  'pages.form.title': 'Form',

  'pages.list.title': 'List',
  'pages.list.tablelist.title': 'Search Table',
  'pages.list.basiclist.title': 'Basic List',
  'pages.list.cardlist.title': 'Card List',

  'pages.exception.title': 'Exception',
  'pages.exception.403.title': '403',
  'pages.exception.404.title': '404',
  'pages.exception.500.title': '500',

  'pages.profile.title': 'Profile',
  'pages.profile.basic.title': 'Basic Profile',
  'pages.profile.advanced.title': 'Advanced Profile',

  'pages.result.title': 'Result',
  'pages.result.success.title': 'Success',
  'pages.result.fail.title': 'Fail',

  'pages.account.title': 'Account',
  'pages.account.center.title': 'Account Center',
  'pages.account.settings.title': 'Account Settings',
  'pages.account.settings.base.title': 'Base',
  'pages.account.settings.security.title': 'Security',
  'pages.account.settings.custom.title': 'Custom',
  'pages.account.settings.binding.title': 'Binding',
  'pages.account.settings.notification.title': 'Notification',

  'pages.system.title': 'System',
  'pages.system.role-list.title': 'Role List',
  'pages.system.permission-list.title': 'Permission List',

  'pages.settings.title': 'Settings',

  'pages.examples.title': 'Components',
  'pages.examples.button.title': 'Button',
  'pages.examples.input.title': 'Input',
  'pages.examples.rate.title': 'Rate',
  'pages.examples.authority.title': 'Authority',

  'pages.nested.title': 'Nested Routes',
  'pages.nested.menu1.title': 'Menu 1',
  'pages.nested.menu1-1.title': 'Menu 1-1',
  'pages.nested.menu1-2.title': 'Menu 1-2',
  'pages.nested.menu1-2-1.title': 'Menu 1-2-1',
  'pages.nested.menu2.title': 'Menu 2',
  'pages.nested.menu3.title': 'Menu 3',
  'pages.nested.menu3-1.title': 'Menu 3-1',

  'pages.jumpUrl.title': 'Jump Url',
  'pages.jumpUrl.router.title': 'Router examples',
  'pages.jumpUrl.github.title': 'Go Github',
};
