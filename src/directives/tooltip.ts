import { Tooltip } from 'ant-design-vue';
import { h, render } from 'vue';
interface ITooltipBindingValue {
  color?: string;
  title: string;
}
export default {
  mounted(el: HTMLElement, binding: { value: ITooltipBindingValue }) {
    const { color, title } = binding.value;

    if (!title) return;

    const container = document.createElement('div');
    const vnode = h(Tooltip, {
      color,
      title,
      visible: false,
      getPopupContainer: () => container,
    });

    render(vnode, container);
    el.appendChild(container);

    (el as any).__tooltipContainer = container;
    (el as any).__tooltipVNode = vnode;

    const showTooltip = () => {
      if (vnode.component) {
        vnode.component.props.visible = true;
      }
    };

    const hideTooltip = () => {
      if (vnode.component) {
        vnode.component.props.visible = false;
      }
    };

    el.addEventListener('mouseenter', showTooltip);
    el.addEventListener('mouseleave', hideTooltip);
  },

  updated(el: HTMLElement, binding: { value: ITooltipBindingValue }) {
    const { color, title } = binding.value;
    const vnode = (el as any).__tooltipVNode;

    if (vnode && vnode.component) {
      vnode.component.props.title = title;
      vnode.component.props.color = color;
    }
  },

  unmounted(el: HTMLElement) {
    const container = (el as any).__tooltipContainer;
    const vnode = (el as any).__tooltipVNode;

    if (container) {
      render(null, container);
      el.removeChild(container);
    }

    const showTooltip = () => {
      if (vnode.component) {
        vnode.component.props.visible = true;
      }
    };

    const hideTooltip = () => {
      if (vnode.component) {
        vnode.component.props.visible = false;
      }
    };

    el.removeEventListener('mouseenter', showTooltip);
    el.removeEventListener('mouseleave', hideTooltip);
  },
};
