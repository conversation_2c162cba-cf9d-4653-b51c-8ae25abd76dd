// drawer
.ant-drawer-body
  padding 20px

// timeline
.ant-timeline-item-head
  z-index 1

.ant-menu-inline
  border-right none

// tree
.ant-tree
  width 100%

.ant-tree li .ant-tree-node-content-wrapper
  width calc(100% - 24px) // 最外层图标宽度

.ant-tree > li:first-child, .ant-tree ul > li:first-child
  padding-top 15px

.ant-tree > li:last-child, .ant-tree-child-tree > li:last-child
  padding-bottom 15px

.ant-tree li
  padding 15px 0px
  // border-bottom 1px #e6e6e6 solid

// ant-table
.ant-table-thead>tr>th
  background none !important
  color #808080 !important

.ant-table-tbody>tr>td
  color rgba(0, 0, 0, 0.65)

.ant-table-tbody>tr:hover>td
  background rgba(245, 245, 245, 1) !important

.ant-table-selection
  width 16px

.ant-avatar
  background #58a8ef

td
  white-space pre-wrap
  p
    white-space pre-wrap
    line-height 25px

.ant-progress-inner
  background-color #EBF6FE

.ant-modal
  top 50px

.ant-select-lg .ant-select-selection__rendered
  line-height 40px

.ant-popover-arrow
  display none

.ant-divider-horizontal.ant-divider-with-text-left .ant-divider-inner-text
  padding 0px 8px 0px 0px
  color #808080
  font-size 14px

.ant-btn-danger
  border-color #ff7866
  background #ff7866
  color white

.fliter
  display flex
  align-items center
  height 20px
  border none
  color #A6A6A6
  font-weight 450
  span
    color #808080
  &:hover
    color #3DA8F5

.ant-drawer-body
  overflow auto
  // height 100%

// .ant-tabs
// height 100%
// .ant-tabs-bar
// margin-bottom 0
// .ant-tabs-top-content
// height calc(100% - 44px)
// .ant-tabs-tabpane
// position relative
// overflow auto
// height 100%

// collapse
.ant-collapse-header
  // padding 12px 40px 12px 12px !important
  .arrow
    right 16px
    left unset !important

.ant-table-filter-dropdown .ant-dropdown-menu
  max-height 30vh !important
