import { VApiConfig } from '@/lib/vails/api';
import { MyApi } from '@/apis/MyApi';
import { NotifyInfoMessage } from '@/engines/notify/types/model';

export class NotifyUserInfoMessagesApi extends MyApi<NotifyInfoMessage> {
  constructor(config?: VApiConfig) {
    super({
      namespace: '/notify/user',
      name: 'info_message',
      actions: [{ name: 'statistics', method: 'post', on: 'collection' }],
      ...config,
    });
  }
}
