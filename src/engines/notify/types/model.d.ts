import { VObject } from '@/lib/vails';

export interface NotifyComment {
  id: number;
  name: string;
  body: string;
  created_at: string;
  commentable_type: string;
  commentable_id: number;
}
export interface NotifyInfoMessage {
  id: number;
  notifyable_type: string;
  notifyable_id: number;
  user_ids: Array<number>;
  title: string;
  content: string;
  flag: string;
  meta: VObject;
  created_at: string;
  read_at?: string;
  is_read?: boolean;
}
