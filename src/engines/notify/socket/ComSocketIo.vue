<!-- eslint-disable @typescript-eslint/no-unused-vars -->
<script setup lang="ts">
import { AuthSessionApi } from '@/engines/login/apis/auth/session.api';
import { useSocketIo, Message } from '../hooks/useSocketIo';
import { computed, onUnmounted, ref, watch, defineAsyncComponent, onMounted } from 'vue';
import { VStore } from '@/lib/vails';
import { NotifyUserInfoMessagesApi } from '../apis/notify/user/info_messages.api';
import { NotifyInfoMessageModel } from '../models/notify/user/info_messages';
import { getComponentId, getDynamicComponentName, isOutSide } from '../utils/index';
import { RedoOutlined } from '@ant-design/icons-vue';
import { useStore } from 'vuex';

const visible = ref(false);

const store = new VStore(new NotifyUserInfoMessagesApi(), NotifyInfoMessageModel);
const vuexStore = useStore();

const openType = ref('');
const outSideVisible = ref(false);

const openModal = (v: any) => {
  visible.value = true;
  freshData();
  checkShow(v);
};
const freshData = () => {
  taIndexView.value?.refresh?.();
  fetchUnread();
};

const onlyOpenModal = () => {
  closeQueue();
  visible.value = true;
};

const checkShow = async (v: any, isRead?: boolean) => {
  try {
    // const res = await store.find(v.id);
    checked.value = { ...v };

    if (!isRead) {
      fetchUnread();
    }
    if (needJump(v)) {
      openMessageUrl(v);
    } else {
      checkOpenType(v);
    }
  } catch (e) {
    // Error handling: failed to show message
  }
  // });
};

// const outSideArea = ['Bpm::Token', 'Bpm::Instance', 'Plan::Token'];
const checkOpenType = (v: any) => {
  if (isOutSide(v.notifyable_type)) {
    openType.value = 'outside';
    outSideVisible.value = true;
  } else {
    openType.value = 'inner';
  }
};
const {
  initSocket,
  closeSocket,
  openQueue,
  closeQueue,
  ReadMap,
  fetchUnread,
  needJump,
  openMessageUrl,
} = useSocketIo({
  clickCallBack: openModal,
});

watch(
  () => ReadMap.value.unread,
  () => {
    vuexStore.commit('setImUnread', ReadMap.value.unread > 0 ? ReadMap.value.unread : '');
  },
);

const total = computed(() => {
  const total = store.totalCount.value;
  if (total <= 0) return 0;
  return total;
});

// setTimeout(() => {
//   openModal({
//     id: 50514,
//     title: '',
//     content: '',
//     url: '',
//     type: 'Bpm::Token',
//   });
// }, 2000);

const isLogin = computed(() => AuthSessionApi.currentUser().id);
const envUrl = computed(() => process.env.VUE_APP_API_BASE_WSS_URL);

const initSocketByLogin = () => {
  if (isLogin.value && envUrl.value) {
    initSocket(`${envUrl.value}?token=${AuthSessionApi.token()}`);
  } else {
    if (envUrl.value) {
      closeSocket();
    }
  }
};

watch(
  () => isLogin.value,
  () => {
    initSocketByLogin();
  },
  {
    immediate: true,
  },
);

const onVisibiltyChange = () => {
  const isHidden = document.hidden;
  if (!isHidden) {
    initSocketByLogin();
  }
};

onMounted(() => {
  document.addEventListener('visibilitychange', onVisibiltyChange);
});

onUnmounted(() => {
  document.removeEventListener('visibilitychange', onVisibiltyChange);
});

const taIndexView = ref();
const config = computed(() => ({
  recordName: '',
  store,
  pagination: {
    hide: true,
    perPage: 18,
  },
  params: {
    q: {
      is_read_eq: type.value === 'unread' ? false : undefined,
    },
  },
  mode: 'list',
  showCount: false,
  scrollLoading: true,
  list: {
    scroll: {
      y: 'auto',
    },
  },
}));

const checked = ref();

const dynamicComponent = computed(() => {
  const nameSplit: string[] = getDynamicComponentName(checked.value);

  const com = defineAsyncComponent(
    () =>
      import(
        '@/engines/' + nameSplit[0].toLowerCase() + '/ComNotify/' + nameSplit.join('') + '.vue'
      ),
  );
  return com;
});
const componentId = computed(() => {
  return getComponentId(checked.value);
});

const onShow = (record: any) => {
  const is_read = record.is_read;
  record.fetch().then(() => {
    checkShow(record, is_read);
  });
};

const type = ref('all');
const setType = (v: string) => {
  checked.value = {};
  type.value = v;
};

const showNotify = () => {
  // if (!envUrl.value) return;
  checked.value = {};
  visible.value = true;
  freshData();
};
const showAnimation = ref(false);

const checkFresh = () => {
  showAnimation.value = true;
  freshData();
};
const resetAnimation = () => {
  showAnimation.value = false;
};
</script>

<template lang="pug">
.div(@click='showNotify')
  slot(:ReadMap='ReadMap')
    .div.clickable.text-gray-50.text-base 站内信{{ ReadMap.unread }}
  a-modal(v-model:visible="visible" :footer='null' width='1200px' @cancel='openQueue' wrapClassName='ad_modal')
   .modal_content.h-full.w-full.flex.flex-row.flex-nowrap.p-4.justify-between
    .w-98.bg-white.rounded.shadow-md
      .h-15.w-full.flex.items-center.justify-between.px-2.notify_title
        .h-full.flex.items-center
          a-button.mr-2(:type='type === "all" ? "primary" : "text"' @click='setType("all")') 全部通知{{`(${ReadMap.total})` }}
          a-button(:type='type === "unread" ? "primary" : "text"' @click='setType("unread")') 未读{{`(${ReadMap.unread})` }}
        RedoOutlined(:class='showAnimation ? "rotateFresh" : undefined' :style="{fontSize: '18px'}" @click='checkFresh' @animationend='resetAnimation')
        //-   template(#icon)
        //-     RedoOutlined
      .overflow-y-auto.w-full.w-98.py-2.pb-0.scrollWrapper
        TaIndexView(
          ref='taIndexView',
          :config='config',
          @onShow='onShow',
        )
          template(#header)
            .empty
          template(#card='{record}')
            div.cursor-pointer.min-h-21.border-b-1.p-4.notify_content_wrapper(:class='checked.id === record.id ? "actived" : undefined' :key='record.id')
              .w-full.text-base.font-medium.text-gray-700.ellipsis(v-if='record.is_read') {{ record.title }}
              .w-full.flex.items-center(v-else)
                .min-w-2.w-2.h-2.rounded-lg.bg-red-500.mr-1
                .text-base.font-medium.text-gray-700.ellipsis {{ record.title }}
              .w-full.flex.items-center.pt-1
                .notify_tag.mr-4(:style=' record.notify_tag?.style') {{ record.notify_tag?.name }}
                TaIcon.text-gray-400.w-4.h-4.mr-1(type='outline/calendar')
                .text-sm.text-gray-400 {{ record.time_text }}
              .w-full.pt-1.ellipsis-2.text-sm.text-gray-400.leading-5 {{ record.content }}

              //- .notify_content_item.flex.justify-between
              //-   .notify_content_item_left.w-6(v-if='!record.is_read')
              //-     img.w-6.h-6(src='https://innomatchoss.oss-cn-hangzhou.aliyuncs.com/notify/is_read.png' )
              //-   .notify_content_item_right.w-80.flex.justify-between.items-center.flex-col
              //-     .notify_content_item_header.flex.justify-between.items-center.w-full
              //-         .w-59.text-base.font-medium.text-gray-700.ellipsis {{ record.title }}
              //-         .notify_flex_1.text-sm.text-gray-400 {{ record.time_text }}
              //-     .notify_content_item_content.text-sm.text-gray-500.leading-5.ellipsis-2.w-full {{ record.content }}
    .w-190.h-fit.bg-white.rounded.shadow-md.notify_inner_wrapper.bg-center
      component.w-full.h-full(:is='dynamicComponent' :id='componentId' v-if='checked?.id && openType === "inner"' :key='componentId')
  //- a-modal( :footer='null' width='1200px' wrapClassName='ad_modal_out')
  component.w-full.h-full(v-model:visible="outSideVisible" :is='dynamicComponent' :id='componentId' v-if='checked?.id && outSideVisible' :key='componentId')
</template>
<style lang="stylus">
@keyframes rotateFresh {
  100% {
    transform rotate(360deg)
  }
}

.ad_modal
  .ant-modal-body
    background #F3F4F6
    border-radius 8px
    padding 0 !important
    height calc(100vh - 300px)
    min-height 660px
    .rotateFresh
      animation rotateFresh .5s ease-out 0s
    .notify_content_wrapper
      border-bottom 1px solid #F3F4F6
    .actived
      background #F3F4F6
      border-bottom none
    .scrollWrapper
      overflow -moz-scrollbars-none
      height calc(100% - 76px)
    .notify_flex_1
      flex 1
    .scroll-list::-webkit-scrollbar
      width 0 !important
    .notify_title
      border-bottom 1px solid #F3F4F6
    .notify_tag
      text-align center
      padding 2px 10px
      border-radius 4px
      font-size 12px
    .notify_inner_wrapper
      background-image url(https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/plan-mobile/lQLPJxl0YJhtgarNAkzNAkyw89ibe5wdbWsEhkKOIoCjAA_588_588.png)
      background-repeat no-repeat
      background-size 200px 200px

  .ant-modal-close-x
    position relative
    border-radius 100px
    top -8px
    right -8px
    background #F3F4F6
    width 28px !important
    height 28px !important
    .ant-modal-close-icon
      position relative
      top -16px !important
      right 0
</style>
