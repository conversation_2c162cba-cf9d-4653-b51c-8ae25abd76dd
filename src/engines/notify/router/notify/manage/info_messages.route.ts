export default [
  {
    path: '/notify/manage/info_messages',
    name: 'notifyManageInfoMessagesIndex',
    component: () =>
      import(
        /* webpackChunkName: "notifyManageInfoMessagesIndex" */ '@/engines/notify/views/notify/manage/info_messages/Index.vue'
      ),
    meta: {
      title: '站内信管理',
    },
  },
  {
    path: '/notify/manage/info_messages/:info_messageId',
    name: 'notifyManageInfoMessagesShow',
    component: () =>
      import(
        /* webpackChunkName: "notifyManageInfoMessagesShow" */ '@/engines/notify/views/notify/manage/info_messages/Show.vue'
      ),
    meta: {
      title: 'notifyManageInfoMessagesShow',
    },
  },
];
