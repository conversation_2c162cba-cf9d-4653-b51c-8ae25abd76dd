import { VObject, VStore } from '@/lib/vails';
import { notification } from 'ant-design-vue';
import { onUnmounted, ref } from 'vue';
import { NotifyUserInfoMessagesApi } from '../apis/notify/user/info_messages.api';
import { NotifyInfoMessageModel } from '../models/notify/manage/info_message';
import { jumpList } from '../utils';

type AnyFc = (...args: any[]) => void;
type PromiseFc = (...args: any[]) => Promise<any>;
type SocketOptions = {
  clickCallBack?: AnyFc | PromiseFc;
};
export type Message = {
  id: number;
  // type: string;
  notifyable_id: number;
  notifyable_type: string;
  meta?: any;
  title: string | null;
  content: string | null;
  url: string | null;
};

export const useSocketIo = (options?: SocketOptions) => {
  // 消息队列
  const queue = ref<Message[]>([]);
  const task = ref<Message[]>([]);
  const socket = ref();

  const openNotification = ref(false);
  const ReadMap = ref({
    total: 0,
    unread: 0,
  });

  const countStore = new VStore(new NotifyUserInfoMessagesApi({}), NotifyInfoMessageModel);
  // countStore.index({
  //   per_page: 1,
  // });

  // 获取未读数据
  const fetchUnread = async () => {
    try {
      const res =
        (await countStore.sendCollectionAction({
          action: 'statistics',
        })) || {};

      ReadMap.value = {
        total: res?.data?.all_count || 0,
        unread: res?.data?.unread_count || 0,
      };
    } catch (e) {
      // Error handling: failed to fetch unread count
    }
  };

  // socket初化
  const initSocket = (url: string) => {
    if (socket.value?.url) {
      closeSocket();
    }
    if ('WebSocket' in window) {
      socket.value = new WebSocket(url); // 创建socket对象
      socket.value.onopen = connectSocket;
      socket.value.onmessage = (e: any) => {
        socketMessage(e);
      };
      socket.value.onclose = disconnectSocket;
      socket.value.onerror = errorSocket;
    } else {
      // WebSocket not supported
    }

    openQueue();
  };

  // 开启webSocket
  const connectSocket = () => {
    // WebSocket connected successfully
    const identifier = JSON.stringify({ channel: 'InfoMessageChannel' });
    sendMessage({ command: 'subscribe', identifier });
    fetchUnread();
  };

  // socket信息接收
  const socketMessage = (v: VObject) => {
    checkQueue(v?.data);
  };

  // 控制队列
  const checkQueue = (v: string) => {
    if (!openNotification.value) return;
    const data = JSON.parse(v);
    if (!data.identifier) return;
    const message = JSON.parse(data.identifier);
    if (message.channel !== 'InfoMessageChannel') return;
    if (!data?.message) return;

    const newMessage: Message = {
      id: data.message?.id,
      notifyable_type: data.message?.notifyable_type,
      notifyable_id: data.message?.notifyable_id,
      title: data.message?.title,
      content: data.message?.content,
      url: data.message?.url,
    };
    pushQueue(newMessage);
  };

  const pushQueue = (v: Message) => {
    queue.value = [...queue.value, v];
    NotificationCeck();
  };

  const NotificationCeck = () => {
    if (task.value.length < 3 && queue.value.length > 0) {
      const v = queue.value?.[0];
      queue.value = [...[...queue.value].filter(el => el.id !== v.id)];
      NotificationOpen(v);
    }
  };

  // 打开通知
  const NotificationOpen = (message: Message) => {
    task.value = [message, ...task.value];
    notification.open({
      message: message.title,
      description: message.content,
      placement: 'bottomRight',
      key: String(message.id),
      onClick: () => {
        clickable(message);
      },
      onClose: () => {
        if (!openNotification.value) return;
        closeNotify(message);
      },
    });
    if (task.value.length < 3) {
      NotificationCeck();
    }
  };

  const needJump = (v: Message) => {
    if (jumpList.includes(v.notifyable_type)) return false;
    return true;
  };
  // 消息点击处理
  const clickable = (v: Message) => {
    if (needJump(v)) {
      // 打开新标签页（待定是否需要新页面打开）
      taskChange(v);
    } else {
      // 打开对话框
      closeQueue();
      notification.close(String(v.id));
      options?.clickCallBack?.(v);
    }
  };

  const closeNotify = (v: Message) => {
    countStore.find(v.id);
    task.value = [...[...task.value].filter(el => el.id !== v.id)];
    NotificationCeck();
  };
  const taskChange = (v: Message) => {
    if (!openNotification.value) return;
    closeNotify(v);
    v?.url && openMessageUrl(v);
  };

  const isValidUrl = (v: string): boolean => {
    try {
      const _url = new URL(v);
      return !!_url;
    } catch (err) {
      return false;
    }
  };
  const openMessageUrl = (v: Message) => {
    if (v?.url && needJump(v)) {
      if (isValidUrl(v.url)) {
        window.open(v.url);
      } else {
        const domain = window.location.host;
        const url = `${process.env.VUE_APP_PUBLIC_PATH}${v.url}`;
        const finalUrl = url.startsWith('/') ? url : `/${url}`;
        window.open(`//${domain}${finalUrl.replace('//', '/')}`);
      }
    }
  };

  // 关闭socket
  const disconnectSocket = () => {
    // Socket disconnected
  };

  // socket 出错
  const errorSocket = () => {
    // Socket error occurred
    // socket 连接失败时也获取一次未读数量，防止socket连接失败时没有未读数量显示
    fetchUnread();
  };

  // socket主动发送信息
  const sendMessage = (v: any) => {
    socket.value.send(JSON.stringify(v));
  };

  // 开启队列
  const openQueue = () => {
    queue.value = [];
    task.value = [];
    openNotification.value = true;
  };

  // 关闭并清空队列
  const closeQueue = () => {
    openNotification.value = false;
    task.value = [];
    queue.value = [];
  };

  const closeSocket = () => {
    if (socket.value?.url) {
      socket.value?.close?.();
      ReadMap.value = {
        ...ReadMap.value,
        total: 0,
        unread: 0,
      };
    }
    closeQueue();
  };

  onUnmounted(() => {
    closeSocket();
  });
  return {
    initSocket,
    sendMessage,
    closeQueue,
    openQueue,
    closeSocket,
    ReadMap,
    fetchUnread,
    needJump,
    openMessageUrl,
  };
};
