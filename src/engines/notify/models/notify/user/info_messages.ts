import { NotifyInfoMessage } from '@/engines/notify/types/model';
import { bpmType, planType } from '@/engines/notify/utils';
import { VModel } from '@/lib/vails';
import dayjs from 'dayjs';

export class NotifyInfoMessageModel extends VModel<NotifyInfoMessage> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return NotifyInfoMessageModel.stateMapping()[this.reactiveRecord.state];
  // });
  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
  time_text = this.computedAttr('time_text', () => {
    return dayjs(this.reactiveRecord.created_at).format('YYYY/MM/DD HH:mm');
  });
  notify_tag = this.computedAttr('notify_tag', () => {
    if (bpmType.includes(this.reactiveRecord.notifyable_type))
      return {
        name: '审批',
        style: {
          background: '#FDE8E8',
          color: '#F05252',
        },
      };
    if (planType.includes(this.reactiveRecord.notifyable_type))
      return {
        name: '任务',
        style: {
          background: '#E1EFFE',
          color: '#1C64F2',
        },
      };
    if (this.reactiveRecord.notifyable_type === 'Notice::Article')
      return {
        name: '资讯',
        style: {
          background: '#E1EFFE',
          color: '#1C64F2',
        },
      };
    return {
      name: '其它',
      style: {
        background: '#E1EFFE',
        color: '#1C64F2',
      },
    };
  });
}
