import { VModel } from '@/lib/vails';
import { NotifyComment } from '@/engines/notify/types/model';
import dayjs from 'dayjs';

export class NotifyCommentModel extends VModel<NotifyComment> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return NotifyCommentModel.stateMapping()[this.reactiveRecord.state];
  // });

  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }

  createdAtStr = this.computedAttr('createdAtStr', () => {
    return dayjs(this.reactiveRecord.created_at).format('YYYY-MM-DD HH:mm');
  });
}
