import { VModel } from '@/lib/vails';
import { NotifyInfoMessage } from '@/engines/notify/types/model';

export class NotifyInfoMessageModel extends VModel<NotifyInfoMessage> {
  user_is_read = this.computedAttr('user_is_read', () => {
    return this.reactiveRecord.is_read ? '是' : '否';
  });

  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}
