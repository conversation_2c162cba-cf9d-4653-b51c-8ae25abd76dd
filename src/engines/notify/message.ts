import { PlanTaskModel } from '../plan/plan-core/models/plan/tasks';
import { PlanTask } from '../plan/plan-core/types/model';

export const zzd_title = (record: PlanTask & PlanTaskModel) => {
  if (record.isOverdue) {
    return '督办提醒！！！';
  } else {
    return '待办任务提醒！';
  }
};
export const zzd_message = (record: PlanTask & PlanTaskModel, name: string) => {
  return `
  <font color='#0D0D0D'>名称：${record.name}</font><br>\n
  <font color='#0D0D0D'>当前状态：${record.stateLabel}</font><br>\n
  <font color='#0D0D0D'>督办人：${name}</font><br>\n
  `;
};
