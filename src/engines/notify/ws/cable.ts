import WebSocketBean from './websocket/WebSocketBean';

enum CableStatus {
  Initial,
  Open,
  Closed,
}

export default abstract class Cable {
  public url: string;
  public channel: string;
  private ws: WebSocketBean | undefined;
  private status: CableStatus;

  constructor(url: string, channel: string) {
    this.url = url;
    this.channel = channel;
    this.status = CableStatus.Initial;
  }

  connect() {
    this.ws = new WebSocketBean({
      url: this.url,
      needReconnect: true,
      reconnectGapTime: 3000,
      onerror: () => {
        this.status = CableStatus.Initial;
        // Cable connection error handled
      },
      onmessage: (ev: MessageEvent<any>) => {
        this.receive(ev);
      },
    });

    this.ws.start();

    this.status = CableStatus.Open;
  }

  subscribe() {
    const identifier = JSON.stringify({ channel: this.channel });
    this.send(JSON.stringify({ command: 'subscribe', identifier: identifier }));
  }

  sendMessage(data: any) {
    const identifier = JSON.stringify({ channel: this.channel });
    this.send(
      JSON.stringify({ command: 'message', identifier: identifier, data: JSON.stringify(data) }),
    );
  }

  send(data: any) {
    if (this.status != CableStatus.Open) return;

    this.ws?.send(data);
  }

  abstract receive(ev: MessageEvent<any>): void;

  close() {
    this.ws?.dispose();

    this.status = CableStatus.Closed;
  }
}
