import { notification } from 'ant-design-vue';
import Cable from './cable';

interface Message {
  id: number;
  type: string;
  title: string;
  content: string;
  url: string;
}

export default class InfoMessageCable extends Cable {
  private messageStack: Array<Message>;
  private messageQueue: Array<Message>;

  constructor(url: string) {
    super(url, 'InfoMessageChannel');

    this.messageStack = new Array<Message>();
    this.messageQueue = new Array<Message>();
  }

  receive(ev: MessageEvent<any>) {
    const data = JSON.parse(ev.data);

    if (!data.identifier) return;

    const channelInfo = JSON.parse(data.identifier);

    if (channelInfo.channel != this.channel) return;

    const newMessage: Message = {
      id: data['message']['id'],
      type: data['message']['type'],
      title: data['message']['title'],
      content: data['message']['content'],
      url: data['message']['url'],
    };

    this.messageStack.push(newMessage);

    let message: Message = {
      id: 0,
      type: '',
      title: '',
      content: '',
      url: '',
    };

    if (this.messageQueue.length < 3 && this.messageStack.length > 0) {
      message = this.pullMessage()!;
    }

    if (message.id == 0) return;

    this.openNotification(message);
  }

  pullMessage(): Message | undefined {
    const message = this.messageStack.pop();

    if (message) this.messageQueue.push(message);

    return message;
  }

  openNotification(message: Message) {
    notification.open({
      message: message.title,
      description: message.content,
      placement: 'bottomRight',
      onClick: () => {
        this.messageQueue.shift();

        if (message.url) {
          // 打开新标签页
        } else {
          // 打开对话框
        }

        const latestMessage = this.pullMessage();
        if (latestMessage) this.openNotification(latestMessage);
      },
      onClose: () => {
        this.messageQueue.shift();

        const latestMessage = this.pullMessage();
        if (latestMessage) this.openNotification(latestMessage);
      },
    });
  }
}

// const visibleModal = ref(false)

// const fn = function(){
//   visibleModal.value = true;
// }
// const token = ref()

// const {} =  useWebSocket(fn,token,)

// openTask = () =>{
//   // open task push
//   // 打开队列
// }

// onClick = () =>{
//   fn();
//   stopTaskPop();
//   clearTask();
//   topTaskPush()
// }

// watch(() =>token.value,() =>{},{
//   immediate:true,
// })
