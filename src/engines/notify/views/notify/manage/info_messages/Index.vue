<script lang="ts">
import ComNotifyInfoMessageIndex from '@/engines/notify/components/notify/info_messages/ComNotifyInfoMessageIndex.vue';
import { defineComponent } from '@vue/runtime-core';
import { NotifyManageInfoMessageApi } from '@/engines/notify/apis/notify/manage/info_message.api';
import { NotifyInfoMessageModel } from '@/engines/notify/models/notify/manage/info_message';
import { VStore } from '@/lib/vails';

import usePolicy from '@/components/global/ta-component/ta-template-form-core/usePolicy';

const NotifyManageInfoMessageIndex = defineComponent({
  name: 'NotifyManageInfoMessageIndex',
  components: {
    ComNotifyInfoMessageIndex,
  },
  setup() {
    usePolicy();

    const store = new VStore(new NotifyManageInfoMessageApi(), NotifyInfoMessageModel);

    return {
      store,
    };
  },
});

export default NotifyManageInfoMessageIndex;
</script>

<template lang="pug">
.notify-manage-info-message-index
  ComNotifyInfoMessageIndex(:store='store' )
</template>

<style lang="stylus" scoped>
.notify-manage-info-message-index
  height 100%
  width 100%
</style>
