<script lang="ts">
import ComNotifyInfoMessageShow from '@/engines/notify/components/notify/info_messages/ComNotifyInfoMessageShow.vue';
import { defineComponent, toRefs, onMounted, computed } from 'vue';
import { NotifyManageInfoMessageApi } from '@/engines/notify/apis/notify/manage/info_message.api';
import { NotifyInfoMessageModel } from '@/engines/notify/models/notify/manage/info_message';
import { VStore } from '@/lib/vails';
import { useRoute } from 'vue-router';

const NotifyManageInfoMessageShow = defineComponent({
  name: 'NotifyManageInfoMessageShow',
  components: {
    ComNotifyInfoMessageShow,
  },
  setup(props) {
    const route = useRoute();

    const store = new VStore(new NotifyManageInfoMessageApi(), NotifyInfoMessageModel);

    const breadcrumbs = computed(() => [{ label: '', route: '/notify/manage/info_messages' }]);

    onMounted(() => {
      store.find(Number(route.params.info_messageId));
    });

    return {
      ...toRefs(props),
      store,
      record: store.record,
      breadcrumbs,
    };
  },
});

export default NotifyManageInfoMessageShow;
</script>

<template lang="pug">
.notify-manage-info-message-show
  ComNotifyInfoMessageShow(v-if='record.id', :store='store', :breadcrumbs='breadcrumbs')
</template>

<style lang="stylus" scoped>
.notify-manage-info-message-show
  height 100%
  width 100%
  .breadcrumbs
    padding 0 24px
</style>
