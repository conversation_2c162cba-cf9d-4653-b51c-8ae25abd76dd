<template lang="pug">
.w-full.h-full 
  a-tabs(v-model:activeKey="type")
    a-tab-pane(key="all" :tab="`全部通知(${ReadMap.total})`")
    a-tab-pane(key="unread" :tab="`未读(${ReadMap.unread})`")
  .w-full 
    TaIndexView(
      ref='taIndexView',
      :config='config',
      @onShow='onShow',
    )
      template(#header)
        .empty
      template(#card='{record}')
        .cursor-pointer.min-h-21.border-b-1.p-4.notify_content_wrapper(:class='checked?.id === record.id ? "actived" : undefined' :key='record.id')
          .w-full.text-base.font-medium.text-gray-700.ellipsis(v-if='record.is_read') {{ record.title }}
          .w-full.flex.items-center(v-else)
            .min-w-2.w-2.h-2.rounded-lg.bg-red-500.mr-1
            .text-base.font-medium.text-gray-700.ellipsis {{ record.title }}
          .w-full.flex.items-center.pt-1
            .notify_tag.mr-4(:style=' record.notify_tag?.style') {{ record.notify_tag?.name }}
            TaIcon.text-gray-400.w-4.h-4.mr-1(type='outline/calendar')
            .text-sm.text-gray-400 {{ record.time_text }}
          .w-full.pt-1.ellipsis-2.text-sm.text-gray-400.leading-5 {{ record.content }}
  component.w-full.h-full(v-model:visible="outSideVisible" :is='dynamicComponent' :id='componentId' v-if='checked?.id && outSideVisible' :key='componentId')
</template>
<script lang="ts">
import { VStore } from '@/lib/vails/store';
import {
  computed,
  defineAsyncComponent,
  defineComponent,
  onMounted,
  ref,
  toRefs,
  watch,
} from 'vue';
import { NotifyUserInfoMessagesApi } from '../../apis/notify/user/info_messages.api';
import { getComponentId, getDynamicComponentName, isOutSide } from '../../utils';
import { useSocketIo } from '../../hooks/useSocketIo';
import { NotifyInfoMessageModel } from '../../models/notify/user/info_messages';
import { useStore } from 'vuex';

const SiteMessageIndex = defineComponent({
  name: 'SiteMessageIndex',
  components: {},
  setup(props) {
    const type = ref('all');
    const taIndexView = ref();
    const store = new VStore(new NotifyUserInfoMessagesApi(), NotifyInfoMessageModel);
    const config = computed(() => ({
      recordName: '',
      store,
      pagination: {
        perPage: 6,
      },
      params: {
        q: {
          is_read_eq: type.value === 'unread' ? false : undefined,
        },
      },
      mode: 'list',
      showCount: false,
      scrollLoading: false,
      list: {},
    }));

    const checked = ref();
    const visible = ref(false);

    const openModal = (v: any) => {
      visible.value = true;
      freshData();
      checkShow(v);
    };

    const freshData = () => {
      taIndexView.value?.refresh?.();
      fetchUnread();
    };

    const { ReadMap, fetchUnread, needJump, openMessageUrl } = useSocketIo({
      clickCallBack: openModal,
    });

    const dynamicComponent = computed(() => {
      const nameSplit: string[] = getDynamicComponentName(checked.value);

      const com = defineAsyncComponent(
        () =>
          import(
            '@/engines/' + nameSplit[0].toLowerCase() + '/ComNotify/' + nameSplit.join('') + '.vue'
          ),
      );
      return com;
    });
    const componentId = computed(() => {
      return getComponentId(checked.value);
    });
    const checkShow = async (v: any, isRead?: boolean) => {
      try {
        // const res = await store.find(v.id);
        checked.value = { ...v };
        if (!isRead) {
          fetchUnread();
        }
        if (needJump(v)) {
          openMessageUrl(v);
        } else {
          checkOpenType(v);
        }
      } catch (e) {
        // Error handling: failed to check and show message
      }
      // });
    };

    const openType = ref('');
    const outSideVisible = ref(false);
    const checkOpenType = (v: any) => {
      if (isOutSide(v.notifyable_type)) {
        openType.value = 'outside';
        outSideVisible.value = true;
      } else {
        openType.value = 'inner';
      }
    };

    const onShow = (record: any) => {
      const is_read = record.is_read;
      record.fetch().then(() => {
        checkShow(record, is_read);
      });
    };

    onMounted(() => {
      fetchUnread();
    });

    const vuexStore = useStore();

    watch(
      () => ReadMap.value.unread,
      () => {
        vuexStore.commit('setImUnread', ReadMap.value.unread > 0 ? ReadMap.value.unread : '');
      },
    );

    return {
      ...toRefs(props),
      type,
      taIndexView,
      config,
      onShow,
      outSideVisible,
      dynamicComponent,
      componentId,
      checked,
      ReadMap,
    };
  },
});
export default SiteMessageIndex;
</script>
<style lang="stylus" scoped>
.notify_content_wrapper
  border-bottom 1px solid #F3F4F6
.actived
  background #F3F4F6
  border-bottom none
  border-raiuds 8px
.scrollWrapper
  overflow -moz-scrollbars-none
  height calc(100% - 76px)
.notify_flex_1
  flex 1
.scroll-list::-webkit-scrollbar
  width 0 !important
.notify_title
  border-bottom 1px solid #F3F4F6
.notify_tag
  text-align center
  padding 2px 10px
  border-radius 4px
  font-size 12px
.notify_inner_wrapper
  background-image url(https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/plan-mobile/lQLPJxl0YJhtgarNAkzNAkyw89ibe5wdbWsEhkKOIoCjAA_588_588.png)
  background-repeat no-repeat
  background-size 200px 200px
</style>
