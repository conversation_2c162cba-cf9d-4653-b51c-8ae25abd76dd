import { message } from 'ant-design-vue';
import ChatEvent<PERSON>enter from './ChatEventCenter';
type AudioStreamClientCallback = 'onStart' | 'onStop' | 'onError' | 'onClose' | 'onResult';

export class AudioStreamClient {
  public websocketUrl = '';
  private websocket: any;
  private stream: any = null;
  private isListening: any = null;
  private audioContext: any = null;
  private audioInput: any = null;
  private processor: any = null;
  private isStreamEnded: boolean = false;

  onResult: null | ((result: string) => void) = null;
  onStart: null | (() => void) = null;
  onStop: null | (() => void) = null;
  onError: null | ((error: any) => void) = null;
  onClose: null | (() => void) = null;

  // 音频配置
  config = {
    sampleRate: 16000, // 采样率
    sampleBits: 16, // 采样位数
    channelCount: 1, // 声道数
    bufferSize: 4096, // 缓冲区大小
  };

  constructor(websocketUrl: string) {
    this.websocketUrl = websocketUrl;
    this.websocket = null;
  }

  //回调函数相关
  setCallbacks({ onStart, onStop, onResult, onError, onClose }: any) {
    this.onStart = onStart;
    this.onStop = onStop;
    this.onResult = onResult;
    this.onError = onError;
    this.onClose = onClose;
  }
  excuteCallBack(cbName: AudioStreamClientCallback, payload?: any) {
    console.log('excuteCallBack', cbName, payload);
    this[cbName] && this[cbName]?.(payload);
  }

  //获取用户媒体权限
  async getUserMedia() {
    return navigator.mediaDevices.getUserMedia({
      audio: {
        sampleRate: this.config.sampleRate,
        channelCount: this.config.channelCount,
        echoCancellation: true,
        // noiseSuppression: true,
      },
    });
  }

  initWebSocket() {
    if (this.websocket) {
      this.websocket.close();
    }
    this.websocket = new WebSocket(this.websocketUrl);

    this.websocket.onopen = () => {
      ChatEventCenter.emit('showMessage', { type: 'info', msg: '请开始说话' });
    };

    this.websocket.onmessage = (event: any) => {
      const result = JSON.parse(event.data);
      if (result.header) {
        this.excuteCallBack('onResult', result);
      }
    };

    this.websocket.onerror = (error: any) => {
      this.excuteCallBack('onError', error);
    };

    this.websocket.onclose = () => {
      ChatEventCenter.emit('showMessage', { type: 'warning', msg: '识别结束' });
      this.excuteCallBack('onClose');
    };
  }

  initAudioContext() {
    if (!this.stream) throw new Error('stream is null');

    this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)({
      sampleRate: this.config.sampleRate,
    });

    this.audioInput = this.audioContext.createMediaStreamSource(this.stream);
    this.processor = this.audioContext.createScriptProcessor(
      this.config.bufferSize,
      this.config.channelCount,
      this.config.channelCount,
    );

    this.audioInput.connect(this.processor);
    this.processor.connect(this.audioContext.destination);
  }

  suspend() {
    this.stream.getTracks().forEach((track: any) => (track.enabled = false));
  }

  resume() {
    this.stream.getTracks().forEach((track: any) => (track.enabled = true));
  }

  async startListening() {
    if (this.isListening) return;

    ChatEventCenter.emit('showMessage', { type: 'loading', msg: '识别开启中..' });
    try {
      this.stream = await this.getUserMedia();
      this.isListening = true;

      this.initAudioContext();
      this.initWebSocket();

      // 处理音频数据
      this.processor.onaudioprocess = this.processAudioData.bind(this);

      this.excuteCallBack('onStart');
    } catch (e) {}
  }

  async stopListening() {
    if (!this.isListening) return;
    this.isListening = false;

    if (this.processor && this.audioInput) {
      this.audioInput.disconnect(this.processor);
      this.processor.disconnect(this.audioContext.destination);
      this.processor = null;
      this.audioInput = null;
      this.stream.getTracks().forEach((track: any) => track.stop());
    }

    // 关闭音频上下文
    if (this.audioContext) {
      this.audioContext.close();
      this.audioContext = null;
    }

    // 关闭WebSocket连接
    if (this.websocket) {
      this.websocket.close();
      this.websocket = null;
    }

    this.excuteCallBack('onStop');
  }

  processAudioData(audioProcessingEvent: any) {
    if (!this.isListening || !this.websocket || this.websocket.readyState !== WebSocket.OPEN) {
      return;
    }

    const inputBuffer = audioProcessingEvent.inputBuffer;
    const inputData = inputBuffer.getChannelData(0);

    // 将Float32Array转换为Int16Array
    const pcmData = new Int16Array(inputData.length);
    for (let i = 0; i < inputData.length; i++) {
      pcmData[i] = inputData[i] * 0x7fff;
    }

    // 发送音频数据
    if (this.websocket.readyState === WebSocket.OPEN) {
      console.log('sended');

      this.websocket.send(pcmData.buffer);
    }

    if (this.isStreamEnded && pcmData.length > 0) {
      this.handleStreamEnd(pcmData.at(-1));
    }
  }

  private handleStreamEnd(finalData: any) {
    if (this.isListening && this.websocket && this.websocket.readyState === WebSocket.OPEN) {
      // 处理最后的音频数据
      this.websocket.send(finalData.buffer);
    }
  }
}
