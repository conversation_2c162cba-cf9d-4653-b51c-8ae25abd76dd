export enum FieldType {
  Input = 'input',
  Select = 'select',
  Textarea = 'textarea',
}

export const fieldTypeLabels = {
  [FieldType.Input]: '单行输入',
  [FieldType.Select]: '下拉选择',
  [FieldType.Textarea]: '多行文本',
};

export type ReportTemplateField = {
  key: string; // 唯一id
  model_key: string; // 取值的key
  type: FieldType;
};

export type ReportTemplateSelectOption = {
  value: string;
};

export type ReportTemplateSelectField = ReportTemplateField & {
  options: ReportTemplateSelectOption[];
};
