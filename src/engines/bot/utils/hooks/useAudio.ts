import { inject, onBeforeUnmount, Ref, ref, watch } from 'vue';
import { AudioStreamClient } from '../AudioStreamClient';
import { VObject } from '@/lib/vails';
import { BotStateKey } from '../../components/conversations/ComBotConversationLayout.vue';
import Chat<PERSON>vent<PERSON>enter from '../ChatEventCenter';

export default function useAudio(websocketUrl: string, isListeningDefault = false) {
  const isListening = ref(isListeningDefault);
  const text = ref('');
  const textMap = new Map();
  const audioStreamClient = new AudioStreamClient(websocketUrl);
  const state = inject<Ref<VObject>>(BotStateKey)!;
  const waitingCount = ref(0);

  audioStreamClient.setCallbacks({
    onResult: (result: any) => {
      if (
        result.header.status === 20000000 &&
        result.header.name === 'TranscriptionResultChanged'
      ) {
        const resultText = result.payload.result;
        const index = result.payload.index;
        textMap.set(index, resultText);

        text.value = Array.from(textMap.values()).join('');

        waitingCount.value = 0;
        console.log(waitingCount.value, 'waitCount');
      }
    },
    onError: (error: any) => {
      console.error('发生错误:', error);
      isListening.value = false;
    },
    onClose: () => {
      console.log('连接已关闭');
      isListening.value = false;
    },
  });

  function timedown() {
    let timer = setTimeout(() => {
      waitingCount.value++;
      console.log(waitingCount.value, 'waitCount');
      if (waitingCount.value >= 4) {
        if (text.value) {
          ChatEventCenter.emit('onConfirm', text.value);
        } else {
          waitingCount.value = 0;
          console.log(waitingCount.value, 'waitCount');
        }
      }
      timedown();
      clearTimeout(timer);
    }, 1000);
  }

  watch(isListening, () => {
    state.value.isListening = isListening.value;
    if (isListening.value) {
      audioStreamClient.startListening();
      timedown();
    } else {
      audioStreamClient.stopListening();
    }
  });

  onBeforeUnmount(() => {
    isListening.value = false;
  });

  return {
    isListening,
    textMap,
    text,
    audioStreamClient,
    waitingCount,
  };
}
