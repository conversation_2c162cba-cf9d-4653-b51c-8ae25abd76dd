import { ref } from 'vue';

export type WidthOption = {
  label: string;
  key: string;
  maxWidth: string;
};
export default function usePaperMaxWidth(widthOptions?: WidthOption[]) {
  const defaultWidthOptions = [
    { label: '标宽', key: 'standard', maxWidth: '860px' },
    { label: '较宽', key: 'wide', maxWidth: '1128px' },
    { label: '全宽', key: 'full', maxWidth: '100%' },
  ];

  widthOptions = widthOptions || defaultWidthOptions;

  const paperMaxWidth = ref(widthOptions[0].maxWidth);
  const updatePaperWidth = (maxWidth: string) => {
    paperMaxWidth.value = maxWidth;
  };

  return [paperMaxWidth, updatePaperWidth, widthOptions];
}
