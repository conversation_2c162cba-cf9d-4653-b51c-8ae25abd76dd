import { VObject } from '@/lib/vails';
import { inject, Ref } from 'vue';
import { BotStateKey } from '../../components/conversations/ComBotConversationLayout.vue';
import markdownit from 'markdown-it';

export default function useMessage(conversationStore?: VObject) {
  const state = inject<Ref<VObject>>(BotStateKey)!;

  function fmtMessage(...items: any) {
    console.log(items, 'items');
    const result: any = [];

    items.forEach((item: any) => {
      if (typeof item === 'string') {
        result.push({
          content_type: 'text',
          content: item,
        });
      } else if (typeof item === 'object' && item && item.url) {
        //附件
        result.push({
          content_type: 'file',
          content: {
            fileName: item.fileName,
            url: item.url,
            byte_size: item.byte_size,
            created_at: item.created_at,
            fileType: item.fileType,
            fileSize: item.fileSize,
            fileCategory: item.fileCategory,
          },
        });
      }
    });

    return result;
  }

  async function sendMessage(conversationId: number, msgs: VObject[]) {
    if (!conversationStore) return;
    console.log(msgs, 'msgs');
    await conversationStore.sendMemberAction({
      id: conversationId,
      action: 'chat',
      config: {
        data: {
          conversation: {
            meta: {
              messages: msgs,
            },
          },
        },
      },
    });
  }

  function markdownRender(text: string) {
    //TODO
    const md = markdownit('commonmark');
    return md.render(text);
  }

  return {
    fmtMessage,
    sendMessage,
    markdownRender,
    state,
  };
}
