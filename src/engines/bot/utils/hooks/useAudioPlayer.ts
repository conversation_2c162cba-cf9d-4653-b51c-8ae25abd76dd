import AudioStreamPlayer from '../AudioStreamPlayer';

export function useAudioPlayer() {
  let player: any = null;

  async function initPlayer() {
    if (player) player.dispose();
    player = new AudioStreamPlayer();

    const wsUrl = `${
      process.env.VUE_APP_AUDIO_STREAM_BASE_URL || 'ws://localhost:3000/chat/user/speech'
    }/generate.json`;

    await player.connect(wsUrl);
  }

  //请求语音合成
  async function requestSpeech(msg: string) {
    console.log(msg, 'msg');
    if (player.ws && player.ws.readyState === WebSocket.OPEN) {
      const request = {
        text: msg,
        options: {
          voice: 'yina',
          format: 'pcm',
          speech_rate: -125,
        },
      };
      player.ws.send(JSON.stringify(request));
    } else {
      console.error('WebSocket未连接');
    }
  }

  return {
    player,
    initPlayer,
    requestSpeech,
  };
}
