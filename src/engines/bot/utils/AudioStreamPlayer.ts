import ChatEventCenter from './ChatEventCenter';

export default class AudioStreamPlayer {
  private audioContext: AudioContext;
  private storedBuffers: Float32Array[] = []; // 存储所有音频数据
  private playbackQueue: Float32Array[] = []; // 播放队列
  private isPlaying = false;
  private scriptNode: ScriptProcessorNode | null = null;
  public ws: any = null;
  private lastDataTime: number = 0;
  private checkInterval: number | null = null;
  private readonly TIMEOUT_THRESHOLD = 1000; // 1秒没有新数据就认为结束了

  constructor() {
    this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)({
      sampleRate: 16000,
    });

    // 创建音频处理节点
    this.setupAudioProcessor();
  }

  // 处理接收到的音频数据
  private handleAudioData(data: ArrayBuffer) {
    const int16Array = new Int16Array(data);
    const float32Array = new Float32Array(int16Array.length);

    // 将Int16转换为Float32
    for (let i = 0; i < int16Array.length; i++) {
      float32Array[i] = int16Array[i] / 32768.0;
    }

    // 尝试合并相邻的缓冲区
    if (this.storedBuffers.length > 0) {
      const lastBuffer = this.storedBuffers[this.storedBuffers.length - 1];
      // 创建新的合并缓冲区
      const newBuffer = new Float32Array(lastBuffer.length + float32Array.length);
      newBuffer.set(lastBuffer);
      newBuffer.set(float32Array, lastBuffer.length);
      // 替换最后一个缓冲区为合并后的缓冲区
      this.storedBuffers[this.storedBuffers.length - 1] = newBuffer;
    } else {
      this.storedBuffers.push(float32Array);
    }

    this.lastDataTime = Date.now();
  }

  // 开始播放存储的音频数据
  private startPlayback() {
    if (!this.audioContext || !this.scriptNode) return;

    // 确保播放队列为空
    this.playbackQueue = [];

    // 将所有存储的缓冲区添加到播放队列
    this.playbackQueue.push(...this.storedBuffers);

    // 清空存储的缓冲区
    this.storedBuffers = [];

    // 开始播放
    this.isPlaying = true;
  }

  // 处理音频输出
  private setupAudioProcessor() {
    if (!this.audioContext) return;

    // 创建ScriptProcessorNode
    this.scriptNode = this.audioContext.createScriptProcessor(4096, 0, 1);

    // 处理音频数据
    this.scriptNode.onaudioprocess = (audioProcessingEvent: AudioProcessingEvent) => {
      const outputBuffer = audioProcessingEvent.outputBuffer;
      const channelData = outputBuffer.getChannelData(0);

      if (this.isPlaying && this.playbackQueue.length > 0) {
        ChatEventCenter.emit('audioPlaying', true);
        const audioData = this.playbackQueue[0];

        // 如果当前缓冲区数据足够填充输出缓冲区
        if (audioData.length >= channelData.length) {
          // 复制需要的数据到输出缓冲区
          channelData.set(audioData.slice(0, channelData.length));

          // 保留剩余的数据
          if (audioData.length > channelData.length) {
            this.playbackQueue[0] = audioData.slice(channelData.length);
          } else {
            this.playbackQueue.shift();
          }
        } else {
          // 如果当前缓冲区数据不足，尝试合并多个缓冲区
          channelData.fill(0);
          let offset = 0;

          while (offset < channelData.length && this.playbackQueue.length > 0) {
            const currentBuffer = this.playbackQueue[0];
            const remainingSpace = channelData.length - offset;
            const copyLength = Math.min(currentBuffer.length, remainingSpace);

            channelData.set(currentBuffer.slice(0, copyLength), offset);
            offset += copyLength;

            if (copyLength < currentBuffer.length) {
              this.playbackQueue[0] = currentBuffer.slice(copyLength);
            } else {
              this.playbackQueue.shift();
            }
          }
        }
      } else {
        channelData.fill(0);
      }

      // 检查是否播放完成
      if (this.isPlaying && this.playbackQueue.length === 0) {
        this.stop();
        ChatEventCenter.emit('audioPlaying', false);
      }
    };

    // 连接到音频输出
    this.scriptNode.connect(this.audioContext.destination);
  }

  // 初始化 WebSocket 连接
  async connect(url: string) {
    return new Promise<void>((resolve, reject) => {
      this.ws = new WebSocket(url);
      this.ws.binaryType = 'arraybuffer';

      this.ws.onopen = () => {
        console.log('WebSocket已连接');
        this.startDataCheck();
        resolve();
      };

      this.ws.onmessage = (event: MessageEvent) => {
        if (event.data instanceof ArrayBuffer) {
          this.handleAudioData(event.data);
        } else {
          try {
            const message = JSON.parse(event.data);
            this.handleControlMessage(message);
          } catch (error) {
            console.error('解析消息失败:', error);
          }
        }
      };

      this.ws.onclose = () => {
        console.log('WebSocket已关闭');
        this.checkForPlayback();
        this.stopDataCheck();
      };

      this.ws.onerror = (error: Event) => {
        console.error('WebSocket错误:', error);
        reject(error);
      };
    });
  }

  private handleControlMessage(message: any) {
    if (message.status === 'completed') {
      console.log('语音合成完成');
      this.checkForPlayback();
    }
  }

  // 开始检查数据接收状态
  private startDataCheck() {
    this.stopDataCheck(); // 先清除可能存在的旧计时器
    this.checkInterval = window.setInterval(() => {
      const now = Date.now();
      if (this.lastDataTime && now - this.lastDataTime > this.TIMEOUT_THRESHOLD) {
        this.checkForPlayback();
      }
    }, 500) as unknown as number;
  }

  // 停止检查数据接收状态
  private stopDataCheck() {
    if (this.checkInterval !== null) {
      clearInterval(this.checkInterval);
      this.checkInterval = null;
    }
  }

  // 检查是否可以开始播放
  private checkForPlayback() {
    if (!this.isPlaying && this.storedBuffers.length > 0) {
      this.startPlayback();
    }
  }

  // 停止播放
  stop() {
    if (this.isPlaying) {
      this.isPlaying = false;
      this.playbackQueue = [];
      this.storedBuffers = [];
      this.stopDataCheck();
    }
  }

  // 清理资源
  dispose() {
    this.stop();
    this.stopDataCheck();

    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }

    if (this.scriptNode) {
      this.scriptNode.disconnect();
      this.scriptNode = null;
    }

    if (this.audioContext) {
      this.audioContext.close();
    }
  }
}
