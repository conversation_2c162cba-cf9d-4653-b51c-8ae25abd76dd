import { BotRoleType } from '../models/bot/user/messages';

export interface BotConversation {
  id: number;
}
export interface BotMessage {
  id: number;
  meta: any;
  role: BotRoleType;
}
export interface BotArtifact {
  id: number;
}
export interface BotAgent {
  id: number;
}
export interface BotIntent {
  id: number;
}
export interface BotReport {
  id: number;
}
export interface BotReport {
  id: number;
}
export interface BotReportTemplate {
  id: number;
}
export interface BotReportTemplate {
  id: number;
}
export interface BotMeeting {
  id: number;
}
export interface BotMeeting {
  id: number;
}
export interface BotReviewer {
  id: number;
}
export interface BotReviewDocument {
  id: number;
}
export interface BotReviewer {
  id: number;
}
export interface BotReviewRule {
  id: number;
}
export interface BotReviewRule {
  id: number;
}
export interface BotReviewResult {
  id: number;
}
