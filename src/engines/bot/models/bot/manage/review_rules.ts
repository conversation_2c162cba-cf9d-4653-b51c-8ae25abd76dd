import { VModel, VObject } from '@/lib/vails';
import { BotReviewRule } from '@/engines/bot/types/model';

export class BotReviewRuleModel extends VModel<BotReviewRule> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return BotReviewRuleModel.stateMapping()[this.reactiveRecord.state];
  // });

  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}
