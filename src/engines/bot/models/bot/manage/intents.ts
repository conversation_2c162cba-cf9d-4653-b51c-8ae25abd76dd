import { VModel, VObject } from '@/lib/vails';
import { BotIntent } from '@/engines/bot/types/model';

export class BotIntentModel extends VModel<BotIntent> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return BotIntentModel.stateMapping()[this.reactiveRecord.state];
  // });

  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}
