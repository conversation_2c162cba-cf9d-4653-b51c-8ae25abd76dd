import { VModel, VObject } from '@/lib/vails';
import { BotReviewer } from '@/engines/bot/types/model';

export class BotReviewerModel extends VModel<BotReviewer> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return BotReviewerModel.stateMapping()[this.reactiveRecord.state];
  // });

  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}
