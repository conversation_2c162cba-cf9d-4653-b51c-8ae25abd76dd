import { VModel, VObject } from '@/lib/vails';
import { BotReportTemplate } from '@/engines/bot/types/model';

export class BotReportTemplateModel extends VModel<BotReportTemplate> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return BotReportTemplateModel.stateMapping()[this.reactiveRecord.state];
  // });

  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}
