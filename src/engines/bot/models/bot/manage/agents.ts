import { VModel, VObject } from '@/lib/vails';
import { BotAgent } from '@/engines/bot/types/model';

export class BotAgentModel extends VModel<BotAgent> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return BotAgentModel.stateMapping()[this.reactiveRecord.state];
  // });

  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}
