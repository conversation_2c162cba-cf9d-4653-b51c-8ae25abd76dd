import { VModel, VObject } from '@/lib/vails';
import { BotMee<PERSON> } from '@/engines/bot/types/model';

export class BotMeetingModel extends VModel<BotMeeting> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return BotMeetingModel.stateMapping()[this.reactiveRecord.state];
  // });

  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}
