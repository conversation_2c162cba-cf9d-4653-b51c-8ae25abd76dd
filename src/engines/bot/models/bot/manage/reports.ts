import { VModel, VObject } from '@/lib/vails';
import { BotReport } from '@/engines/bot/types/model';

export class BotReportModel extends VModel<BotReport> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return BotReportModel.stateMapping()[this.reactiveRecord.state];
  // });

  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}
