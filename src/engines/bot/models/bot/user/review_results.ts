import { VModel, VObject } from '@/lib/vails';
import { BotReviewResult } from '@/engines/bot/types/model';

export enum BotReviewResultLevel {
  HIGH = 'high',
  MEDIUM = 'medium',
  LOW = 'low',
}
export class BotReviewResultModel extends VModel<BotReviewResult> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return BotReviewResultModel.stateMapping()[this.reactiveRecord.state];
  // });
  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}
