import { VModel, VObject } from '@/lib/vails';
import { BotReport } from '@/engines/bot/types/model';
enum BotReportReviewState {
  Pending = 'pending',
  Processing = 'processing',
  Completed = 'completed',
  Failed = 'failed',
}

export const BotReportReviewStateMapping: VObject = {
  [BotReportReviewState.Pending]: { label: '待审查', color: 'gray' },
  [BotReportReviewState.Processing]: { label: '进行中', color: 'blue' },
  [BotReportReviewState.Completed]: { label: '已完成', color: 'green' },
  [BotReportReviewState.Failed]: { label: '失败', color: 'red' },
};
export class BotReportModel extends VModel<BotReport> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return BotReportModel.stateMapping()[this.reactiveRecord.state];
  // });
  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}
