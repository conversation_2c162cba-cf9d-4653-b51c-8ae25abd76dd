import { VModel, VObject } from '@/lib/vails';
import { BotMessage } from '@/engines/bot/types/model';

export class BotMessageModel extends VModel<BotMessage> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return BotMessageModel.stateMapping()[this.reactiveRecord.state];
  // });
  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}

export enum BotRoleType {
  User = 'user',
  Assistant = 'assistant',
}

export enum BotMessageContentType {
  Text = 'text',
  File = 'file',
  Artifact = 'artifact',
}

export enum BotMessageToolCName {
  Ransack = 'Bot::Tools::RansackTool',
}
