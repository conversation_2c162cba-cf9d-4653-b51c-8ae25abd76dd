import { VModel, VObject } from '@/lib/vails';
import { BotConversation } from '@/engines/bot/types/model';

export class BotConversationModel extends VModel<BotConversation> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return BotConversationModel.stateMapping()[this.reactiveRecord.state];
  // });

  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}
