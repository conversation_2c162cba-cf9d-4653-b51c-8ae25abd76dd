import { VModel, VObject } from '@/lib/vails';
import { BotReviewDocument } from '@/engines/bot/types/model';

export class BotReviewDocumentModel extends VModel<BotReviewDocument> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return BotReviewDocumentModel.stateMapping()[this.reactiveRecord.state];
  // });

  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}
