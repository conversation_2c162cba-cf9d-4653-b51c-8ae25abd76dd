import { VModel, VObject } from '@/lib/vails';
import { BotArtifact } from '@/engines/bot/types/model';

export class BotArtifactModel extends VModel<BotArtifact> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return BotArtifactModel.stateMapping()[this.reactiveRecord.state];
  // });

  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}
