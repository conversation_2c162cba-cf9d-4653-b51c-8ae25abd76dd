import { VModel, VObject } from '@/lib/vails';
import { BotMeeting } from '@/engines/bot/types/model';

export enum BotMeetingState {
  PENDING = 'pending',
  PROCESSING = 'processing',
  FINISHED = 'finished',
  FAILED = 'failed',
}

export const BotMeetingStateMapping: VObject = {
  [BotMeetingState.PENDING]: { label: '等待中', color: '' },
  [BotMeetingState.PROCESSING]: { label: '进行中', color: '#1890ff' },
  [BotMeetingState.FINISHED]: { label: '已完成', color: '#52C41A' },
  [BotMeetingState.FAILED]: { label: '失败', color: '#FF4D4F' },
};
export class BotMeetingModel extends VModel<BotMeeting> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return BotMeetingModel.stateMapping()[this.reactiveRecord.state];
  // });
  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}
