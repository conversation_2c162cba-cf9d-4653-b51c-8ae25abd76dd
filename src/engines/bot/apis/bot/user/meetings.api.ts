import { VApiConfig } from '@/lib/vails/api';
import { MyApi } from '@/apis/MyApi';
import { BotMeeting } from '@/engines/bot/types/model';

export class BotUserMeetingsApi extends MyApi<BotMeeting> {
  constructor(config?: VApiConfig) {
    super({
      namespace: '/bot/user',
      name: 'meeting',
      actions: [
        { name: 'ask', method: 'post', on: 'member' },
        { name: 'generate_topic_and_summary', method: 'post', on: 'member' },
      ],
      ...config,
    });
  }
}
