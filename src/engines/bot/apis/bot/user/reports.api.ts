import { VApiConfig } from '@/lib/vails/api';
import { MyApi } from '@/apis/MyApi';
import { BotReport } from '@/engines/bot/types/model';

export class BotUserReportsApi extends MyApi<BotReport> {
  constructor(config?: VApiConfig) {
    super({
      namespace: '/bot/user',
      name: 'report',
      actions: [
        { name: 'process_text', method: 'post', on: 'member' },
        { name: 'generate_from_template', method: 'post', on: 'member' },
        { name: 'perform_review', method: 'post', on: 'member' },
      ],
      ...config,
    });
  }
}
