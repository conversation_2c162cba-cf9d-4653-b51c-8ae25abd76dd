import { VApiConfig } from '@/lib/vails/api';
import { MyApi } from '@/apis/MyApi';
import { BotConversation } from '@/engines/bot/types/model';

export class BotUserConversationsApi extends MyApi<BotConversation> {
  constructor(config?: VApiConfig) {
    super({
      namespace: '/bot/user',
      name: 'conversation',
      actions: [{ name: 'chat', method: 'post', on: 'member' }],
      ...config,
    });
  }
}
