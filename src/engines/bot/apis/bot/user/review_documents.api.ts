import { VApiConfig } from '@/lib/vails/api';
import { MyApi } from '@/apis/MyApi';
import { BotReviewDocument } from '@/engines/bot/types/model';

export class BotUserReviewDocumentsApi extends MyApi<BotReviewDocument> {
  constructor(config?: VApiConfig) {
    super({
      namespace: '/bot/user',
      name: 'review_document',
      actions: [
        { name: 'ask', method: 'post', on: 'member' },
        { name: 'perform_review', method: 'post', on: 'member' },
      ],
      ...config,
    });
  }
}
