import { VApiConfig } from '@/lib/vails/api';
import { MyApi } from '@/apis/MyApi';
import { BotArtifact } from '@/engines/bot/types/model';

export class BotUserArtifactsApi extends MyApi<BotArtifact> {
  constructor(config?: VApiConfig) {
    super({
      namespace: '/bot/user',
      name: 'artifact',
      actions: [{ name: 'activate', method: 'post', on: 'member' }],
      ...config,
    });
  }
}
