<script lang='ts'>
import { ref, defineComponent, toRefs, PropType } from 'vue';
import { B<PERSON>bleMenu, Editor } from '@tiptap/vue-3';
import { useTipTapEditor, EditorMarkAction } from './useTipTapEditor';
const ComTipTapBubbleMenu = defineComponent({
  name: 'ComTipTapBubbleMenu',
  components: { BubbleMenu },
  props: {
    editor: { type: Object as PropType<Editor>, default: undefined },
    tippyOptions: { type: Object, default: () => ({ duration: 100 }) },
  },
  setup(props) {
    const { reactiveEditor, dispatchCommand, dispatchEditorMark, typeWriterEffect } = useTipTapEditor(props.editor);

    return {
      ...toRefs(props),
      dispatchEditorMark,
      reactiveEditor,
      dispatchCommand,
      EditorMarkAction,
      typeWriterEffect,
    };
  },
});
export default ComTipTapBubbleMenu;
</script>

<template lang="pug">
BubbleMenu(
  :editor='reactiveEditor'
  :tippy-options='tippyOptions'
)
  .bubble-menu
    slot(
      name='menu',
      :editor='reactiveEditor',
      :action='{dispatchCommand,dispatchEditorMark}'
    )
      .flex.items-center.space-x-1.shadow.bg-gray-50.rounded.px-2.py-1.leading-none(
        style='width:max-content'
      )
        slot(name='before')

        button.px-2.pt-2px.rounded(:class='{ "hover:bg-gray-100":true }')
          input.w-14px.h-14px(
            type='color',
            style='padding:0'
            :value='reactiveEditor.getAttributes("textStyle").color',
            @input='(e)=>dispatchCommand("setColor",true)(e.target.value).run()',
          )

        button.px-2.pb-1.rounded(
          @click.stop='dispatchEditorMark(EditorMarkAction.Blockquote,true)'
          :class='{ "is-active": reactiveEditor.isActive("blockquote"),"hover:bg-gray-100":true }'
        )
          TaIcon(type='ApiOutlined' size='14')

        button.px-2.pb-1.rounded(
          @click.stop='dispatchEditorMark(EditorMarkAction.Bold,true)'
          :class='{ "is-active": reactiveEditor.isActive("bold"),"hover:bg-gray-100":true }'
        )
          TaIcon(type='BoldOutlined' size='14')

        button.px-2.pb-1.rounded(
          @click.stop='dispatchEditorMark(EditorMarkAction.Italic,true)'
          :class='{ "is-active": reactiveEditor.isActive("italic"),"hover:bg-gray-100":true }'
        )
          TaIcon(type='ItalicOutlined' size='14')

        button.px-2.pb-1.rounded(
          @click.stop='dispatchEditorMark(EditorMarkAction.Strike,true)'
          :class='{ "is-active": reactiveEditor.isActive("strike"),"hover:bg-gray-100":true }'
        )
          TaIcon(type='StrikethroughOutlined' size='14')

        button.px-2.pb-1.rounded(
          @click.stop='dispatchEditorMark(EditorMarkAction.Center,true)'
          :class='{ "is-active": reactiveEditor.isActive({ textAlign: "center" }),"hover:bg-gray-100":true }'
        )
          TaIcon(type='AlignCenterOutlined' size='14')


        button.px-2.pb-1.rounded(
          @click.stop='dispatchEditorMark(EditorMarkAction.BulletList,true)'
          :class='{ "is-active": reactiveEditor.isActive("bulletList"),"hover:bg-gray-100":true }'
        )
          TaIcon(type='UnorderedListOutlined' size='14')

        slot(name='after')
</template>

<style lang="stylus" scoped>
.is-active
  @apply bg-gray-200;
.bubble-menu button input::-webkit-color-swatch-wrapper
  padding 0
  border 0
.bubble-menu button input::-webkit-color-swatch
  border 0
  border-radius 4px
</style>
