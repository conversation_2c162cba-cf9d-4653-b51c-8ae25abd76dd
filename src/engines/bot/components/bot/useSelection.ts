export default function useSelection() {
  function getSelectedTextPosition() {
    // 获取选中的文本
    const selection = window.getSelection()!;
    if (selection.rangeCount === 0 || selection.type === 'Caret') return null; // 如果没有选中内容，返回 null

    const range = selection.getRangeAt(0);
    const rect = range.getBoundingClientRect();

    return {
      text: selection.toString(),
      x: rect.right,
      y: rect.bottom,
    };
  }

  return {
    getSelectedTextPosition,
  };
}
