<script lang="ts">
import { defineComponent, toRefs, PropType, ref, provide, onMounted, inject, Ref, nextTick } from 'vue';
import ComBotMeetingConversation from '../meetings/ComBotMeetingConversation.vue';
import { useTipTapEditor } from '../useTipTapEditor';
import usePaperMaxWidth from '../../../utils/hooks/usePaperMaxWidth';
import ComOfficeEditorActions from '../ComOfficeEditorActions.vue';
import ComTipTapBubbleMenu from '../ComTipTapBubbleMenu.vue';
import ComTipTapFloatingMenu from '../ComTipTapFloatingMenu.vue';
import ComOfficeEditorFooter from '../ComOfficeEditorFooter.vue';
import ComBotReviewDocumentsRisk from './ComBotReviewDocumentsRisk.vue';
import ReportTemplateEventCenter from '@/engines/bot/components/bot/report_templates/ReportTemplateEventCenter';
import { VObject } from '../../../../../lib/vails/model/index';
import { OfficeStateKey } from '../ComOfficeEditorLayout.vue';
import useSelection from '../useSelection';
import ComBotTab from '../../base/ComBotTab.vue';
import { message } from 'ant-design-vue';

const ComBotReviewDocumentsShow = defineComponent({
  name: 'ComBotReviewDocumentsShow',
  components: {
    ComBotMeetingConversation,
    ComOfficeEditorActions,
    ComTipTapBubbleMenu,
    ComTipTapFloatingMenu,
    ComOfficeEditorFooter,
    ComBotReviewDocumentsRisk,
    ComBotTab
  },
  props: {
    store: { type: Object, required: true },
    resultsStore: { type: Object, required: true },
  },
  setup(props, { emit }) {

    const tabs = [{
      label: 'AI聊天',
      key: 'ai',
      icon: 'outline/chat'
    }, {
      label: '风险',
      key: 'risk',
      icon: 'outline/search'
    }]
    const activeTab = ref(tabs[0])
    const reviewLoading = ref(false)

    const state = inject<Ref<VObject>>(OfficeStateKey)!
    const { reactiveEditor, dispatchCommand, findStringFromEditor } = useTipTapEditor();
    const [paperMaxWidth, updatePaperWidth, widthOptions] = usePaperMaxWidth()
    const { getSelectedTextPosition } = useSelection()
    const getFileContent = (url: string) => {
      if (url) {
        fetch(url).then((response) => {
          if (!response.ok) {
            throw new Error('Network response was not ok');
          }
          return response.text()
        }).then((text: string) => {
          dispatchCommand('setContent')!(text)
        })
      }
    }

    onMounted(() => {
      state.value.editor = reactiveEditor
      const fileURL = props.store.record.value?.file?.url
      getFileContent(fileURL)
    })

    ReportTemplateEventCenter.on('findRawText', async (raw: string) => {
      const { from, to } = findStringFromEditor(raw)

      console.log(from, to)
      dispatchCommand('setTextSelection', true)!({ from, to }).run()
      await nextTick()
      const { y } = getSelectedTextPosition()!

      const scrollTop = window.scrollY + y
      window.scrollTo({
        top: scrollTop,
        behavior: "smooth", // 平滑滚动
      });
    })


    const performReviewReport = async (record: VObject) => {
      reviewLoading.value = true

      await props.store.sendMemberAction({
        action: 'perform_review',
        id: record.id,
      });

      await props.resultsStore.index({ per_page: 30 })
      reviewLoading.value = false
    }

    const editorContainer = ref<HTMLDivElement | null>(null)
    const handleTextSelectionAndScroll = async (raw: string, replacement?: string) => {
      const { from, to } = findStringFromEditor(raw);
      if (from === -1) return message.error('未找到文本');

      await dispatchCommand('setTextSelection', true)!({ from, to }).run();

      // Insert replacement text if provided
      if (replacement) {
        await dispatchCommand('insertContent', true)!(replacement).run();
      }

      let timer: any;
      timer = setTimeout(() => {
        const { y } = getSelectedTextPosition()!;
        const containerRect = editorContainer.value!.getBoundingClientRect();

        const scrollTop = y - containerRect.top + editorContainer.value!.scrollTop - (containerRect.height / 2);
        editorContainer.value!.scrollTo({
          top: scrollTop,
          behavior: "smooth",
        });
        clearTimeout(timer);
      }, 100);
    };

    ReportTemplateEventCenter.on('findRawText', async (raw: string) => {
      await handleTextSelectionAndScroll(raw);
    });

    ReportTemplateEventCenter.on('replaceRawText', async (data: { raw: string, replacement: string }) => {
      const { raw, replacement } = data;
      await handleTextSelectionAndScroll(raw, replacement);
    });



    return {
      ...toRefs(props),
      record: props.store.record,
      results: props.resultsStore.records,
      tabs,
      activeTab,
      reactiveEditor,
      dispatchCommand,
      paperMaxWidth,
      updatePaperWidth,
      widthOptions,
      reviewLoading,
      performReviewReport,
      editorContainer
    };
  },
});
export default ComBotReviewDocumentsShow;
</script>

<template lang="pug">
.com-bot-user-review-documents-show.w-full.h-full.flex.text-base
  .left__wrapper.flex-grow.w-0.bg-white.relative.flex.flex-col.items-center(v-if='reactiveEditor' class='min-w-2xl')
    .flex.justify-center.pt-4.pb-2.w-full.px-5(
      :style='{maxWidth: paperMaxWidth}'
    )
      ComOfficeEditorActions.w-full(
        :editor='reactiveEditor',
      )
    .paper.p-5.overflow-y-auto.w-full.flex-grow.h-0.pb-8(
      :style='{maxWidth: paperMaxWidth}'
      ref='editorContainer'
    )
      TaTipTapEditor(
        :editor='reactiveEditor',
        @click.stop=''
      )
      ComTipTapBubbleMenu(
        :editor='reactiveEditor'
      )
        //- template(#before)
        //-   ComOfficeEditorAiDropdown.flex-shrink-0(
        //-     :editor='reactiveEditor'
        //-   )
      ComTipTapFloatingMenu(
        :editor='reactiveEditor'
      )

    ComOfficeEditorFooter.absolute.left-0.bottom-0(
      :characterCount='reactiveEditor.storage.characterCount.characters()'
      :widthOptions='widthOptions'
      @updatePaperWidth='updatePaperWidth'
    )
  .right__ai.flex-shrink-0.w-400px.bg-gray-50.p-6.flex.flex-col
    ComBotTab.bg-white.mb-4(
      :tabs='tabs'
      v-model:activeTab='activeTab'
    )
    .right__ai-content.flex-grow.h-0.overflow-y-auto.relative
      .ai__content.py-6(v-show='activeTab.key === "ai"')
        ComBotMeetingConversation(:store='store')
      .risk__content.py-6.grid.gap-4(v-show='activeTab.key === "risk"')
        .mb-3
          button.w-full.px-3.py-2.bg-purple-500.rounded-lg.flex.items-center.justify-center(
            @click='performReviewReport(record)'
            :disabled='reviewLoading'
            class='!text-white !text-xs hover:bg-purple-600'
          )
            TaIcon(type='ReloadOutlined' :size='12')
            .pl-2 重新检查
        a-spin(:spinning='reviewLoading')
          .space-y-2
            template(v-for='result in results')
              ComBotReviewDocumentsRisk(:record='result')

</template>

<style lang="stylus" scoped>
.com-bot-user-review-documents-show
  height 100%
  .right__ai .active-tab
    @apply bg-purple-100 text-purple-800;

</style>
