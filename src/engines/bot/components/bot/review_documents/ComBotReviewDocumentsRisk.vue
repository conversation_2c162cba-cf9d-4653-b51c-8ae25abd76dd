<script lang='ts'>
import { ref, defineComponent, toRefs } from 'vue';
import { BotReviewResultLevel } from '@/engines/bot/models/bot/user/review_results';
import ReportTemplateEventCenter from '@/engines/bot/components/bot/report_templates/ReportTemplateEventCenter';

const ComBotReviewDocumentsRisk = defineComponent({
  name: 'ComBotReviewDocumentsRisk',
  components: {},
  props: {
    record: { type: Object, required: true },
  },
  setup(props) {
    const isOpened = ref(true)
    const toggleOpened = () => {
      return isOpened.value = !isOpened.value
    }

    const getRiskLevelColor = (level: BotReviewResultLevel) => {
      switch (level) {
        case BotReviewResultLevel.HIGH:
          return 'bg-red-400'
        case BotReviewResultLevel.MEDIUM:
          return 'bg-orange-400'
        case BotReviewResultLevel.LOW:
          return 'bg-blue-400'
      }
    }

    const findRawText = (raw: string) => {
      ReportTemplateEventCenter.emit('findRawText', raw)
    }

    const replaceRawText = (raw: string, replacement: string) => {
      ReportTemplateEventCenter.emit('replaceRawText', {
        raw,
        replacement
      })
    }

    return {
      ...toRefs(props),
      isOpened,
      toggleOpened,
      getRiskLevelColor,
      findRawText,
      replaceRawText
    };
  },
});
export default ComBotReviewDocumentsRisk;
</script>

<template lang="pug">
.com-bot-review-documents-risk.bg-white.rounded.border-1.border-gray-100
  header.py-3.px-2.border-b-1.border-gray-100.flex.items-center.relative
    .risk__level.absolute.left-0.w-2px.h-6(
      :class='getRiskLevelColor(record.level)'
    )
    .text-base.text-black.font-medium.truncate.pr-2.flex-grow.w-0.select-none {{ record.name}}
    TaIcon.ml-auto.cursor-pointer.transform.transition-transform(
      type='RightOutlined' :size='14' color='#6B7280'
      :class='{ "rotate-90": isOpened }'
      @click.stop='toggleOpened'
    )
  section.px-4.pb-3.text-xs.pt-1(
    v-show='isOpened'
  )
    a-typography-paragraph(@click.stop='findRawText(record.raw)')
      blockquote.cursor-pointer {{ record.raw}}

    .risk__tip.pb-3
      .flex.items-center.mb-2
        TaIcon(type='AlertFilled' size='14' color='#9061F9')
        .text-purple-500.text-xs.ml-2 风险提示
      .text-black.text-xs {{ record.reason}}
    hr.mb-3

    .risk__suggestion.pb-3
      .flex.items-center.mb-2
        TaIcon(type='EditFilled' size='14' color='#9061F9')
        .text-purple-500.text-xs.ml-2 修改建议
      .text-black.text-xs {{ record.suggest}}

    .w-full.flex.justify-end
      button.action__btn.px-3.py-2.rounded-lg.bg-purple-500.font-medium(
        class='!text-white !text-xs hover:bg-purple-600 transition-all'
        @click.stop='replaceRawText(record.raw,record.meta.replacement)'
      ) 替换
      //- button.action__btn.px-3.py-2.rounded-lg.bg-white.font-medium.border-1.border-gray-200(
      //-   class='!text-xs'
      //- ) 还原
</template>

<style lang="stylus" scoped></style>
