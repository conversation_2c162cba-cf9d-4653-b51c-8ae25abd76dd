<script lang="ts">
import { defineComponent, computed, ref, Ref, toRefs, reactive, onMounted } from 'vue';
import { TaIndexViewTabInterface } from '@/components/global/ta-component/TaIndexView/types';
import { VObject } from '@/lib/vails/model';
import ComBotReviewDocumentsShow from './ComBotReviewDocumentsShow.vue'
import { useRoute, useRouter } from 'vue-router';

const ComBotReviewDocumentsIndex = defineComponent({
  name: 'ComBotReviewDocumentsIndex',
  components: {
    ComBotReviewDocumentsShow,
  },
  props: {
    store: { type: Object, required: true },
    reviewerId: { type: Number, required: true },
  },
  setup(props, { emit }) {
    const config = computed(() => ({
      recordName: '审查文件',
      store: props.store,
      pagination: {
        perPage: 15,
        showPageSizeChanger: false,
        hideOnSinglePage: true,
        showSizeChanger: false,
      },
      template: 'bot_review_document',
      mode: 'table',
      actions: [
        { key: 'create', enabled: true },
        { key: 'update', enabled: true },
        { key: 'delete', enabled: true },
        { key: 'import', enabled: true },
        { key: 'export', enabled: true },
      ],
      formDataEncode: (formData: any) => {
        return {
          ...formData,
          reviewer_id: props.reviewerId
        };
      },
      table: {
        scroll: { y: 'auto' },
      },
      // searcherSimpleOptions: [{key: 'title', label: '标题', type: 'string' }],
    }));

    onMounted(() => {
    })

    const route = useRoute();
    const router = useRouter();
    const onShow = (record: VObject) => {
      // props.store.sendMemberAction({
      //   action: 'perform_review',
      //   id: record.id
      // })
      router.push(`${route.path}/${record.id}`)
    };

    return {
      ...toRefs(props),
      config,
      onShow
    };
  },
});

export default ComBotReviewDocumentsIndex;
</script>

<template lang="pug">
.com-bot-user-review-documents-index
  TaIndexView(:config='config' :tabs='tabs' @onShow='onShow' emptyText='111')
    //- template(#card='{ record }')
    //- template(#table)
    //-    a-table-column(title='名称' dataIndex='record')
    //- template(#detail='{ record, onClose }')
    //-   ComBotReviewDocumentsShow(
    //-     v-if='record.id'
    //-     :store='store'
    //-     :extendRoute='`/bot/user/review_documents/${record.id}`'
    //-     :editable='editable'
    //-     @afterDelete='onClose'
    //-     @afterExtend='onClose'
    //-   )

</template>

<style lang="stylus" scoped>
.com-bot-user-review-documents-index
  height 100%
  width 100%
</style>
