<script lang='ts'>
import { ref, defineComponent, toRefs, PropType } from 'vue';
import { WidthOption } from '@/engines/bot/utils/hooks/usePaperMaxWidth';

const ComOfficeEditorFooter = defineComponent({
  name: 'ComOfficeEditorFooter',
  components: {},
  props: {
    characterCount: { type: Number, default: 0 },
    widthOptions: {
      type: Array as PropType<WidthOption[]>, default: () => [
        { label: '标宽', key: 'standard', maxWidth: '860px' },
        { label: '较宽', key: 'wide', maxWidth: '1128px' },
        { label: '全宽', key: 'full', maxWidth: '100%' }
      ]
    },
  },
  setup(props, { emit }) {
    const selectedWidthKey = ref(props.widthOptions[0].key);

    const updatePaperWidth = (option: WidthOption) => {
      selectedWidthKey.value = option.key;
      emit('updatePaperWidth', option.maxWidth)
    }
    return {
      ...toRefs(props),
      selectedWidthKey,
      updatePaperWidth
    };
  },
});
export default ComOfficeEditorFooter;
</script>

<template lang="pug">
footer.w-full.px-4.py-10px.flex.justify-between.items-center.bg-white
  .text-xs.text-gray-500.flex-shrink-0.pr-1 {{characterCount}}个字
  .warning.flex.items-center.text-gray-300
    TaIcon(type='InfoCircleOutlined', size='12')
    .text-xs.pl-1 所有内容均由人工智能模型输出，其内容的准确性和完整性无法保证，不代表我们的态度或观点。
  .grid.grid-cols-3.gap-2.flex-shrink-0
    .px-10px.py-2px.rounded-md.text-xs.font-medium.text-gray-900.bg-gray-100.cursor-pointer(
      v-for='option in widthOptions',
      :class='{ "bg-purple-100 text-purple-800": selectedWidthKey === option.key }',
      @click='updatePaperWidth(option)'
    ) {{option.label}}
</template>

<style lang="stylus" scoped></style>
