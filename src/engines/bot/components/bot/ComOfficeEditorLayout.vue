<script lang='ts'>
import { ref, defineComponent, toRefs, provide, onUnmounted, onMounted } from 'vue';
import { VObject } from '../../../../lib/vails/model/index';
import { message } from 'ant-design-vue';
import ReportTemplateEventCenter from './report_templates/ReportTemplateEventCenter';
import { useRouter, useRoute } from 'vue-router';
import { asBlob } from 'html-docx-js-typescript'
import { saveAs } from 'file-saver'
import ComOfficeLayoutTabs from './ComOfficeLayoutTabs.vue';
export const OfficeStateKey = Symbol('OfficeStateKey');

const ComOfficeEditorLayout = defineComponent({
  name: 'ComOfficeEditorLayout',
  components: {
    ComOfficeLayoutTabs
  },
  props: {
    initState: {
      type: Object,
      default: () => ({
        name: '',
        content: '',
        variables: {},
        cache: {},
        loading: false,
      }),
    }
  },
  setup(props) {
    const state = ref<VObject>(props.initState);
    provide(OfficeStateKey, state);

    const handleBeforeUnload = (e: any) => {
      e.preventDefault()
      e.returnValue = ''
    }


    const handleOffline = () => {
      message.error('网络断开,您的保存可能不会生效');
    }

    onMounted(() => {
      window.addEventListener('beforeunload', handleBeforeUnload);
      window.addEventListener('offline', handleOffline);
    })

    onUnmounted(() => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
      window.removeEventListener('offline', handleOffline);
    })


    const save = () => {
      ReportTemplateEventCenter.emit('save')
    }

    const onExport = () => {
      const html = state.value.editor.getHTML()
      const innerHtml = html
        .replace(/<strong>/g, '<b>')
        .replace(/<\/strong>/g, '</b>')
        .replace(/<mark/g, '<span')
        .replace(/<\/mark>/g, '</span>')
        .replace(/<p/g, '<span')
        .replace(/<\/p>/g, '</span>')
      console.log(innerHtml, 'in')
      asBlob(innerHtml).then(data => {
        saveAs(data, `${state.value.name}.docx`)
      })
    }

    const router = useRouter();
    const route = useRoute();
    const onClickHome = () => {
      try {
        const pathArr = route.path.split('/');
        pathArr.pop();
        router.push(pathArr.join('/'));
      } catch (e) {
        router.back()
      }
    }


    const regenerate = () => {
      ReportTemplateEventCenter.emit('regenerate')
    }
    return {
      ...toRefs(props),
      state,
      save,
      onExport,
      onClickHome,
      regenerate,
    };
  },
});
export default ComOfficeEditorLayout;
</script>

<template lang="pug">
.com-office-editor-layout.w-full.h-screen.flex.flex-col.overflow-hidden
  header.py-2.px-6.bg-gray-50.flex.items-center.justify-between
    .w-200px.flex-shrink-0.flex.items-center
      TaIcon.mr-3(
        type='HomeOutlined', :size='16',color='#6B7280'
        class='!hover:text-purple-500'
        @click='onClickHome'
      )
      .editable__div.text-sm.font-medium.text-gray-700(
        contenteditable='true'
        @input='state.name = $event.target.textContent'
      ) {{ state.name}}
    ComOfficeLayoutTabs(
      v-if='state.tabs?.length > 0'
      :tabs='state.tabs'
      :activeTab='state.activeTab'
    )
    .flex.space-x-3.flex-shrink-0.w-200px.justify-end
      button(class='!text-purple-500 hover:bg-purple-100 transition-all' @click='regenerate' v-if='state.code === "meeting"') 重新生成
      button(class='!text-purple-500 hover:bg-purple-100 transition-all' @click='save') 保存
      button(class='!hover:bg-purple-600 transition-all' @click='onExport') 导出

  section.flex-grow.h-0
    .router-view.w-full.h-full
      slot
        router-view(:key='$route.fullPath')

</template>

<style lang="stylus" scoped>
.com-office-editor-layout header button
  @apply text-xs font-medium px-3 py-2 rounded-lg border border-purple-500;
.com-office-editor-layout header button:last-child
  @apply bg-purple-500 text-white;
.com-office-editor-layout header
  border-bottom 1px solid #E5E7EB
.editable__div:focus-visible
  outline none
</style>
