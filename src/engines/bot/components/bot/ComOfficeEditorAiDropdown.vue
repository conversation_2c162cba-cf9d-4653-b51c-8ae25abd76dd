<script lang='ts'>
import { ref, defineComponent, toRefs, inject, Ref, PropType, nextTick, onMounted, onUnmounted, watch } from 'vue';
import ReportTemplateEventCenter from '@/engines/bot/components/bot/report_templates/ReportTemplateEventCenter';
import { TextOperation, useTipTapEditor } from './useTipTapEditor';
import { VObject } from '../../../../lib/vails/model/index';
import { OfficeStateKey } from './ComOfficeEditorLayout.vue';
import { Editor } from '@tiptap/vue-3';
import useSelection from './useSelection';

const ComOfficeEditorAiDropdown = defineComponent({
  name: 'ComOfficeEditorAiDropdown',
  components: {},
  props: {
    editor: {
      type: Object as PropType<Editor>,
      required: true,
    },
    el: {
      type: Object as PropType<HTMLElement>,
      required: true
    }
  },
  setup(props, { emit }) {
    const state = inject<Ref<VObject>>(OfficeStateKey)!
    const contentMap = ref<Map<string, string>>(new Map());
    const isLoading = ref(false);
    const { dispatchCommand } = useTipTapEditor(props.editor);

    type StopTypeWriting = () => void;
    let activeStopper: StopTypeWriting | null = null;
    const stopTypeWriting = () => {
      activeStopper?.()
      activeStopper = null
    }

    function _typeWriterEffect(
      operation: keyof typeof TextOperation,
      text: string,
      stopCallback = () => isLoading.value = false
    ): StopTypeWriting {
      let index = 0;
      let animationFrameId: number;
      contentMap.value.set(operation, '');
      function _type() {
        if (index < text.length) {
          contentMap.value.set(operation, (contentMap.value.get(operation) || '') + text.charAt(index));
          index++;
          index >= text.length && (stopCallback())
          animationFrameId = requestAnimationFrame(() => {
            _type();

          });
        } else {
          cancelAnimationFrame(animationFrameId);
        }
      }

      _type();

      return () => {
        stopCallback()
        if (animationFrameId) {
          cancelAnimationFrame(animationFrameId);
        }
      };
    }

    const activeOperKey = ref('')
    const operAction = (operation: keyof typeof TextOperation) => {
      if (isLoading.value) return;
      isLoading.value = true;
      visible.value = true;
      activeOperKey.value = operation

      dispatchCommand('setHighlight', true)!({ color: '#EDEBFE' })

      ReportTemplateEventCenter.emit(operation, (data: VObject) => {
        activeStopper = _typeWriterEffect(operation, data.text)
      })
    }
    const operMap = {
      'polish': {
        label: TextOperation.polish,
        icon: 'EditOutlined',
        action: () => {
          operAction('polish')
        },
      },
      'expand': {
        label: TextOperation.expand,
        icon: 'PlusOutlined',
        action: () => {
          operAction('expand')
        },
      },
      'simplify': {
        label: TextOperation.simplify,
        icon: 'MinusOutlined',
        action: () => {
          operAction('simplify')
        },
      },
      'continue': {
        label: TextOperation.continue,
        icon: 'ArrowRightOutlined',
        action: () => {
          operAction('continue')
        },
      }
    };

    const updateContentPositionOperMap = {
      'rewrite': {
        label: '重写',
        icon: 'ReloadOutlined',
        action: (operation: keyof typeof TextOperation) => {
          operMap[operation].action()
        }
      },
      'abandon': {
        label: '放弃',
        icon: 'DeleteOutlined',
        action: (operation: keyof typeof TextOperation) => {
          contentMap.value.set(operation, '')
          if (state.value.cache && state.value.cache.operation === operation) {
            state.value.cache = {}
          }
          visible.value = false
        }
      },
      'replace': {
        label: '替换',
        icon: 'RetweetOutlined',
        action: (operation: keyof typeof TextOperation) => {
          const { from, to } = state.value.cache?.position || { from: 0, to: 0 }
          const unknownTypeText = contentMap.value.get(operation) || ''

          dispatchCommand('insertContent', true)!(unknownTypeText)
            .setTextSelection({ from, to: from + unknownTypeText.length })
            .scrollIntoView()
            .run()
        }
      },
      'insert': {
        label: '插入',
        icon: 'VerticalAlignTopOutlined',
        action: (operation: keyof typeof TextOperation) => {
          const { from, to } = state.value.cache?.position || { from: 0, to: 0 }
          const unknownTypeText = contentMap.value.get(operation) || ''

          dispatchCommand('insertContentAt', true)!(to, unknownTypeText)
            .setTextSelection({ from, to })
            .scrollIntoView()
            .run()
        }
      }
    }

    const visible = ref(false)
    const parentEl = ref<HTMLElement | null>(null)
    const onChangeVisible = (value: boolean) => {
      visible.value = value
    }
    const dialogStyle = ref({})
    const { getSelectedTextPosition } = useSelection()

    const changeDialogPosition = () => {
      const position = getSelectedTextPosition();
      if (!position) return;
      dialogStyle.value = {
        top: position?.y + 'px',
      }
    }

    onMounted(() => {
      parentEl.value = props.el || document.body
      document.addEventListener('mouseup', changeDialogPosition);
    })
    onUnmounted(() => {
      document.removeEventListener('mouseup', changeDialogPosition);
    })

    return {
      ...toRefs(props),
      operMap,
      contentMap,
      isLoading,
      updateContentPositionOperMap,
      stopTypeWriting,
      visible,
      activeOperKey,
      dialogStyle,
      onChangeVisible,
      parentEl,
    };
  },
});
export default ComOfficeEditorAiDropdown;
</script>

<template lang="pug">
a-dropdown.com-office-editor-ai-dropdown(
  triggerSubMenuAction='click'
)
  .flex.items-center.cursor-pointer.rounded.px-2.py-1(
    class='hover:bg-gray-100'
  )
    TaIcon(type='ExperimentOutlined' color='#9061F9' :size='14')
    .text-sm.text-gray-900.leading-none AI帮我改
    TaIcon(type='DownOutlined' :size='12' color='#212529')
  template(#overlay)
    a-menu.ai-menu
      a-menu-item.ai__submenu(
        v-for='(oper,key) in operMap'
        :key='key'
        @click.stop='oper.action()'
      )
        .flex.items-center.text-gray-500
          TaIcon.mr-3(
            :type='oper.icon',
            :size='14'
          )
          .text-sm.font-normal {{oper.label}}
        TaNoPaddingModal(
          v-if='visible'
          :getContainer='()=>parentEl'
          :visible='visible',
          @update:visible='onChangeVisible'
          :footer='null'
          :mask='false'
          :top='dialogStyle.top'
          :closable='false'
          :maskClosable='true'
          :width='600'
        )
          template(#title)
            .item__header.flex.items-center
              TaIcon.mr-2(type='ArrowLeftOutlined' :size='12' color='#6B7280')
              .text-sm.text-gray-500 {{operMap[activeOperKey]?.label}}
              TaIcon.ml-auto(type='ClockCircleOutlined' :size='16' color='#6B7280')

          .wrapper.w-full.pb-4
            section.px-4.pt-4.pb-2.overflow-y-auto.min-h-25.max-h-280px
              .text-base.text-gray-700.whitespace-normal {{contentMap.get(activeOperKey)}}

            footer.flex.items-center.px-4.pt-2(v-show='!isLoading && !contentMap.get(activeOperKey)')
              .empty.w-5.h-5

            footer.flex.items-center.px-4.pt-2(v-show='isLoading')
              .w-5.h-5.bg-cover.bg-center.animate-spin(style='background-image:url(https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/ai_editor/Ellipse%2023.png)')
              .text-sm.text-gray-700.pl-2 正在生成中...

              .pause__icon.w-4.h-4.border-1.border-gray-500.rounded-full.grid.ml-auto.mr-1
                .w-7px.h-7px.bg-gray-500.place-self-center
              .text-gray-500.text-sm(@click.stop='stopTypeWriting') 停止

            footer.flex.items-center.px-4.pt-2.gap-x-2(v-show='!isLoading && contentMap.get(activeOperKey)')
              .text-sm.text-gray-500.mr-auto {{contentMap.get(activeOperKey)?.length || 0}}个字

              template(v-for='(op,k,index) in updateContentPositionOperMap')
                button.flex.px-2.py-1.items-center.space-x-1(@click.stop='op.action(activeOperKey)')
                  TaIcon(:type='op.icon' :size='14' color='#6B7280')
                  .text-sm.text-gray-500 {{op.label}}

                .text-gray-200.text-sm(v-if='index !== Object.keys(updateContentPositionOperMap).length - 1') |


</template>

<style lang="stylus">



</style>
