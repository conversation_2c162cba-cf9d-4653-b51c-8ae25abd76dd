<script lang='ts'>
import { ref, defineComponent, toRefs, inject, Ref } from 'vue';
import { VObject } from '../../../../lib/vails/model/index';
import { OfficeStateKey } from './ComOfficeEditorLayout.vue';
const ComOfficeLayoutTabs = defineComponent({
  name: 'ComOfficeLayoutTabs',
  components: {},
  props: {
    tabs: { type: Array, default: () => [] },
    activeTab: { type: Object, default: () => ({}) },
  },
  setup(props) {
    const state = inject<Ref<VObject>>(OfficeStateKey)!
    return {
      ...toRefs(props),
      state,
    };
  },
});
export default ComOfficeLayoutTabs;
</script>

<template lang="pug">
.com-office-layout-tabs.flex.items-center.space-x-4(style='width:fit-content')
  .tab.text-sm.text-gray-400.font-semibold.cursor-pointer(
    v-for='tab in tabs' :class='{"active-tab": tab.key === activeTab?.key }'
    @click.stop='state.activeTab = tab'
  ) {{ tab.label}}
</template>

<style lang="stylus" scoped>
.active-tab
  @apply text-lg text-purple-500 underline underline-offset-8 decoration-yellow-500;

</style>
