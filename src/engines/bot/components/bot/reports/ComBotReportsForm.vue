<script lang='ts'>
import { ref, defineComponent, toRefs, PropType, computed, inject, Ref } from 'vue';
import { ReportTemplateField, ReportTemplateSelectField, FieldType } from '../../../utils/hooks/useReportTemplateField';
import ComBotReportTemplatesInput from '../report_templates/ComBotReportTemplatesInput.vue';
import { VObject } from '../../../../../lib/vails/model/index';
import { OfficeStateKey } from '../ComOfficeEditorLayout.vue';
import ReportTemplateEventCenter from '@/engines/bot/components/bot/report_templates/ReportTemplateEventCenter';
import { debounce } from 'lodash-es';

type ReportForm = {
  fields: (ReportTemplateField | ReportTemplateSelectField)[]
}

const ComBotReportsForm = defineComponent({
  name: 'ComBotReportsForm',
  components: {
    ComBotReportTemplatesInput
  },
  props: {
    formData: { type: Object, default: () => ({}) },
    form: {
      type: Object as PropType<ReportForm>, default: () => ({
        fields: []
      })
    },
    isLoading: { type: Boolean, default: false },
  },
  setup(props, { emit }) {
    const fields = computed(() => props.form.fields);
    const state = inject<Ref<VObject>>(OfficeStateKey)!

    const localFormData = computed({
      get: () => props.formData,
      set: (value) => {
        emit('update:formData', value);
      },
    });

    const onGenerateText = debounce(() => {
      ReportTemplateEventCenter.emit('generate');
    }, 500);
    return {
      ...toRefs(props),
      fields,
      localFormData,
      state,
      FieldType,
      onGenerateText,
    };
  },
});
export default ComBotReportsForm;
</script>

<template lang="pug">
.com-bot-reports-form
  .form__wrapper.space-y-3.mb-3
    template(v-for='(field, index) in fields')
      .form__item(v-if='field.type === FieldType.Input')
        .form__item-label {{ field.model_key }}
        ComBotReportTemplatesInput(
          v-model:value='state.variables[field?.model_key]',
        )

      .form__item(v-else-if='field.type === FieldType.Select')
        .form__item-label {{ field.model_key }}
        a-select.w-full(
          v-model:value='state.variables[field?.model_key]'
          :options='field.options'
        )

      .form__item(v-else-if='field.type === FieldType.Textarea')
        .form__item-label {{ field.model_key }}
        a-textarea.w-full(
          v-model:value='state.variables[field?.model_key]'
          :rows='4'
        )

    slot(name='extra')
      .form__item
        .form__item-label 额外要求
        a-textarea.w-full(
          v-model:value='formData.requirements'
          :rows='4'
        )
  a-spin(:spinning='isLoading')
    button.w-full.px-3.py-2.bg-purple-500.rounded-lg.flex.items-center.justify-center(
      @click='onGenerateText'
      class='!text-white !text-xs hover:bg-purple-600'
    )
      TaIcon(type='PlusOutlined' :size='12')
      .pl-2 开始生成
</template>

<style lang="stylus" scoped>
.com-bot-reports-form .form__wrapper .form__item-label
  @apply text-sm text-gray-900 font-medium;
.com-bot-reports-form .form__wrapper .form__item
  @apply space-y-2;
</style>
