<script lang='ts'>
import { ref, defineComponent, toRefs, onMounted, onUnmounted } from 'vue';
import dayjs from 'dayjs';
const ComBotReportsCard = defineComponent({
  name: 'ComBotReportsCard',
  components: {},
  props: {
    record: { type: Object, required: true },
  },
  setup(props) {
    const REPORT_CARD_HEADER_IMAGE = 'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/ai_editor/report__card.png'
    const FILE_UPLOAD_IMAGE = 'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/ai_editor/File%20Upload.png'

    const card = ref<any>(null);
    const addClass = () => {
      if (card.value) {
        card.value.classList.add('gradient-border')
      }
    };
    const removeClass = () => {
      if (card.value) {
        card.value.classList.remove('gradient-border');
      }
    };


    onMounted(() => {
      card.value?.addEventListener('mouseover', addClass);
      card.value?.addEventListener('mouseout', removeClass);
    })

    onUnmounted(() => {
      try {
        card.value?.removeEventListener('mouseover', addClass);
        card.value?.removeEventListener('mouseout', removeClass);
      } catch (e) { }
    })
    return {
      ...toRefs(props),
      REPORT_CARD_HEADER_IMAGE,
      FILE_UPLOAD_IMAGE,
      dayjs,
      card,
    };
  },
});
export default ComBotReportsCard;
</script>

<template lang="pug">
.com-bot-reports-card.shadow.gray__border.relative.overflow-hidden.rounded-2xl(
  ref='card'
)
  .header.px-2.pt-2.bg-gray-100
    img.block.w-full.h-full(:src="REPORT_CARD_HEADER_IMAGE")
  section.p-3.flex.items-center
    .w-6.h-6.mr-6px
      img.block.w-full.h-full(:src='FILE_UPLOAD_IMAGE')
    .info
      .text-sm.text-gray-700.font-medium {{record.name || record.report_template?.name || '未命名文件'}}
      .text-xs.text-gray-500 最近修改于 {{dayjs(record.updated_at).format('YYYY-MM-DD HH:mm') }}
</template>

<style lang="stylus" scoped>
.com-bot-reports-card img
  object-fit cover
  object-position center
  border-radius 16px 16px 0 0
.com-bot-reports-card section img
  object-fit cover
  object-position center
  border-radius 0


.gray__border::before
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  padding: 2px;
  box-sizing: border-box;
  border-radius: 16px;
  @apply bg-gray-200;
.com-bot-reports-card.gradient-border::before
  content:'';
  background-size:cover;
  background-position: 50% 60%;
  background-image:url(https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/ai_editor/%E8%8A%B1%E7%93%A3%E7%B4%A0%E6%9D%90_%E7%B2%89%E8%93%9D%E6%B8%90%E5%8F%98%E5%BC%A5%E6%95%A3%E8%83%8C%E6%99%AF%E5%9B%BE%E7%89%87_193465581.png)
</style>
