<script lang="ts">
import { defineComponent, toRefs, PropType, inject, Ref, onMounted, nextTick, ref, computed } from 'vue';
import { VObject } from '../../../../../lib/vails/model/index';
import { OfficeStateKey } from '../ComOfficeEditorLayout.vue';
import { useTipTapEditor, TextOperation } from '../useTipTapEditor';
import usePaperMaxWidth from '../../../utils/hooks/usePaperMaxWidth';
import ReportTemplateEventCenter from '@/engines/bot/components/bot/report_templates/ReportTemplateEventCenter';
import { message } from 'ant-design-vue';
import ComOfficeEditorFooter from '../ComOfficeEditorFooter.vue';
import ComBotReportsForm from './ComBotReportsForm.vue';
import ComTipTapBubbleMenu from '@/engines/bot/components/bot/ComTipTapBubbleMenu.vue';
import ComTipTapFloatingMenu from '@/engines/bot/components/bot/ComTipTapFloatingMenu.vue';
import ComOfficeEditorAiDropdown from '../ComOfficeEditorAiDropdown.vue';
import { cloneDeep } from 'lodash-es';
import ComOfficeEditorActions from '../ComOfficeEditorActions.vue';
import ComBotReviewDocumentsRisk from '../review_documents/ComBotReviewDocumentsRisk.vue';
import useSelection from '../useSelection';
import ComBotTab from '../../base/ComBotTab.vue';
import { useRoute, useRouter } from 'vue-router';

const ComBotReportsShow = defineComponent({
  name: 'ComBotReportsShow',
  components: {
    ComBotReportsForm,
    ComOfficeEditorFooter,
    ComTipTapBubbleMenu,
    ComTipTapFloatingMenu,
    ComOfficeEditorAiDropdown,
    ComOfficeEditorActions,
    ComBotReviewDocumentsRisk,
    ComBotTab
  },
  props: {
    store: { type: Object, required: true },
    resultStore: { type: Object, required: true },
  },
  emits: ['afterDelete', 'afterExtend'],
  setup(props, { emit }) {
    const state = inject<Ref<VObject>>(OfficeStateKey)!
    const route = useRoute();
    const router = useRouter();
    const {
      reactiveEditor,
      dispatchCommand,
      getSelectedText,
      typeWriterEffect,
      findStringFromEditor,
    } = useTipTapEditor();
    const { getSelectedTextPosition } = useSelection()

    const [paperMaxWidth, updatePaperWidth, widthOptions] = usePaperMaxWidth()

    onMounted(async () => {
      state.value.name = props.store.record?.value?.title || '未命名文件'
      state.value.editor = reactiveEditor
      state.value.variables = cloneDeep(props.store.record?.value?.variables) || {}

      if (reactiveEditor) {
        dispatchCommand('setContent')!(props.store.record?.value?.content)
        const lastPage = window.history.state.back
        if (lastPage?.includes('report_templates')) {
          await nextTick()
          ReportTemplateEventCenter.emit('generate')
          window.history.replaceState({}, '', window.location.href);
        }
      }
    })

    const processText = async (
      operation: keyof typeof TextOperation,
      successCallback?: (data: VObject) => void
    ) => {
      message.loading(`正在${TextOperation[operation]}中...`);

      try {
        const res = await props.store.sendMemberAction({
          action: 'process_text',
          id: props.store.record.value.id,
          config: {
            data: {
              report: {
                operation,
                content: getSelectedText(),
              },
            },
          },
        });

        message.destroy();
        const data = res.data;
        const position = reactiveEditor.value.state.selection
        console.log('position', position)
        state.value.cache = {
          operation,
          position,
          data,
        }
        successCallback && successCallback?.(data)
      } catch (error) {
        message.destroy();
        console.error(`Error during ${operation}:`, error);
      }
    };

    ReportTemplateEventCenter.on('polish', (cb) => processText('polish', cb));
    ReportTemplateEventCenter.on('expand', (cb) => processText('expand', cb));
    ReportTemplateEventCenter.on('simplify', (cb) => processText('simplify', cb));
    ReportTemplateEventCenter.on('continue', (cb) => processText('continue', cb));

    const isLoading = ref(false)
    ReportTemplateEventCenter.on('generate', async () => {
      message.loading('生成中...', 0)
      isLoading.value = true
      try {
        const res = await props.store.sendMemberAction({
          action: 'generate_from_template',
          id: props.store.record.value.id,
        });

        message.destroy();
        isLoading.value = false

        const text = Array.isArray(res.data.content) ? res.data.content.join('  ') : res.data.content;
        state.value.title = res.data.title

        const startPosition = 0
        dispatchCommand('clearContent')!(true);

        const stopCallback = async () => {
          dispatchCommand('setTextSelection', true)!({ from: startPosition, to: startPosition + text.length })
            .insertContent(text)
            .run()
        }
        typeWriterEffect(
          text,
          startPosition,
          stopCallback
        );
      } catch (error) {
        message.destroy();
        isLoading.value = false
      }
    })

    const tabs = [
      { label: '参数设置', key: 'variable', icon: 'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/ai_editor/ai_file_logo.png' },
      { label: '内容审查', key: 'result', icon: 'flowbite-v2-solid/search' },
    ]
    const activeTab = ref(tabs[0])

    const reviewLoading = ref(false)
    const performReviewReport = async (record: VObject) => {
      reviewLoading.value = true
      await props.store.sendMemberAction({
        action: 'perform_review',
        id: record.id,
      });
      await props.resultStore.index({ per_page: 30 })
      reviewLoading.value = false
    }

    const editorContainer = ref<HTMLDivElement | null>(null)
    const handleTextSelectionAndScroll = async (raw: string, replacement?: string) => {
      const { from, to } = findStringFromEditor(raw);
      if (from === -1) return message.error('未找到文本');

      await dispatchCommand('setTextSelection', true)!({ from, to }).run();

      // Insert replacement text if provided
      if (replacement) {
        await dispatchCommand('insertContent', true)!(replacement).run();
      }

      let timer: any;
      timer = setTimeout(() => {
        const { y } = getSelectedTextPosition()!;
        const containerRect = editorContainer.value!.getBoundingClientRect();

        const scrollTop = y - containerRect.top + editorContainer.value!.scrollTop - (containerRect.height / 2);
        editorContainer.value!.scrollTo({
          top: scrollTop,
          behavior: "smooth",
        });
        clearTimeout(timer);
      }, 100);
    };

    ReportTemplateEventCenter.on('findRawText', async (raw: string) => {
      await handleTextSelectionAndScroll(raw);
    });

    ReportTemplateEventCenter.on('replaceRawText', async (data: { raw: string, replacement: string }) => {
      const { raw, replacement } = data;
      await handleTextSelectionAndScroll(raw, replacement);
    });

    return {
      ...toRefs(props),
      record: props.store.record,
      results: props.resultStore?.records,
      reactiveEditor,
      dispatchCommand,
      paperMaxWidth,
      updatePaperWidth,
      widthOptions,
      tabs,
      activeTab,
      performReviewReport,
      reviewLoading,
      editorContainer,
      isLoading,
    };
  },
});
export default ComBotReportsShow;
</script>

<template lang="pug">
.com-bot-manage-reports-show.w-full.flex.text-base
  .left__wrapper.flex-grow.w-0.bg-white.relative.flex.flex-col.items-center(v-if='reactiveEditor' class='min-w-2xl')
    .flex.justify-center.pt-4.pb-2.w-full.px-4
      ComOfficeEditorActions.w-full(
        :editor='reactiveEditor',
      )
    .paper.py-5.overflow-y-auto.w-full.flex-grow.h-0.pb-8(
      :style='{maxWidth: paperMaxWidth,paddingInline:"56px"}'
      ref='editorContainer'
    )
      TaTipTapEditor(
        :editor='reactiveEditor',
        @click.stop=''
      )
      ComTipTapBubbleMenu(
        :editor='reactiveEditor'
      )
        template(#before)
          ComOfficeEditorAiDropdown.flex-shrink-0(
            :editor='reactiveEditor'
          )
      ComTipTapFloatingMenu(
        :editor='reactiveEditor'
      )

    ComOfficeEditorFooter.absolute.left-0.bottom-0(
      :characterCount='reactiveEditor.storage.characterCount.characters()'
      :widthOptions='widthOptions'
      @updatePaperWidth='updatePaperWidth'
    )

  .right__ai.flex-shrink-0.w-300px.bg-gray-50.p-6.flex.flex-col
    ComBotTab.bg-white.mb-3(
      :tabs='tabs'
      v-model:activeTab='activeTab'
    )
    .flex-grow.h-0.overflow-y-auto(v-show='activeTab.key === "variable"')
      ComBotReportsForm(
        v-if='record.report_template_id'
        :form='record.report_template.conf'
        v-model:formData='record.formData'
        :isLoading='isLoading'
      )
    .flex-grow.h-0.overflow-y-auto(v-show='activeTab.key === "result"')
      .mb-3
        button.w-full.px-3.py-2.bg-purple-500.rounded-lg.flex.items-center.justify-center(
          @click='performReviewReport(record)'
          :disabled='reviewLoading'
          class='!text-white !text-xs hover:bg-purple-600'
        )
          TaIcon(type='ReloadOutlined' :size='12')
          .pl-2 重新检查
      template(
        v-if='record.review_state && (record.review_state !== "pending") && results.length === 0'
      )
        .text-xs.text-gray-400.w-full.text-center 没有不符合规则的内容哦
      template(v-else)
        a-spin(:spinning='reviewLoading')
          .space-y-2
            template(v-for='result in results')
              ComBotReviewDocumentsRisk(:record='result')



</template>

<style lang="stylus" scoped>
.com-bot-manage-reports-show
  height 100%
  .right__ai .active-tab
    @apply bg-purple-100 text-purple-800;
.com-bot-manage-reports-show .right__ai .logo
  background-image url(https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/ai_editor/ai_file_logo.png)
</style>
