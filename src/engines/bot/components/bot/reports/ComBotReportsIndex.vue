<script lang="ts">
import { defineComponent, computed, ref, Ref, toRefs, reactive } from 'vue';
import { TaIndexViewTabInterface } from '@/components/global/ta-component/TaIndexView/types';
import { VObject } from '@/lib/vails/model';
import ComBotReportsShow from './ComBotReportsShow.vue';
import ComBotReportsCard from './ComBotReportsCard.vue';
import { BotReportReviewStateMapping } from '@/engines/bot/models/bot/user/reports';

const ComBotReportsIndex = defineComponent({
  name: 'ComBotReportsIndex',
  components: {
    ComBotReportsShow,
    ComBotReportsCard,
  },
  props: {
    store: { type: Object, required: true },
  },
  setup(props, { emit }) {
    const mode = ref<'list' | 'table'>('table');
    const config = computed(() => ({
      recordName: '文件记录',
      store: props.store,
      pagination: {
        perPage: mode.value === 'table' ? 15 : 9,
        showPageSizeChanger: false,
        hideOnSinglePage: true,
        showSizeChanger: false,
      },
      template: 'bot_report',
      detail: {
        mode: 'route',
      },
      mode: mode.value,
      actions: [
        { key: 'create', enabled: false },
        { key: 'update', enabled: true },
        { key: 'delete', enabled: true },
      ],
      table: {
        scroll: { y: 'auto' },
      },
      list: {
        scroll: { y: 'auto' },
        gap: 16,
        splitCount: 3
      },
      searcherSimpleOptions: [{ key: 'name', label: '文件名称', type: 'string' }],
    }));


    const onIndex = (data: VObject) => { };

    return {
      ...toRefs(props),
      config,
      onIndex,
      mode,
      BotReportReviewStateMapping,
    };
  },
});

export default ComBotReportsIndex;
</script>

<template lang="pug">
.com-bot-manage-reports-index
  TaIndexView.ta-index-view(:config='config' :tabs='tabs' @onIndex='onIndex')
    template(#right-actions)
      .h-34px(style='line-height: 0')
        .border-1.border-gray-200.rounded-md.flex.cursor-pointer.overflow-hidden
          .index__tab.px-3.py-2.border-r-1.border-gray-200(
            class='hover:bg-gray-50',
            :class='{ "bg-gray-100": mode === "list" }',
            @click='mode = "list"'
          )
            TaIcon(type='AppstoreFilled' size='16' color='#1F2A37')
          .index__tab.px-3.py-2(
            class='hover:bg-gray-50',
            :class='{ "bg-gray-100": mode === "table" }',
            @click='mode = "table"'
          )
            TaIcon(type='MenuOutlined' size='16' color='#1F2A37')
    template(#card='{record}')
      ComBotReportsCard.cursor-pointer(:record='record')
    template(#bodyCell='{ text, record, index, column }')
      a-tag(
        v-if='column.dataIndex[0] === "review_state"'
        :color='BotReportReviewStateMapping[text]?.color'
      ) {{ BotReportReviewStateMapping[text]?.label }}

</template>

<style lang="stylus" scoped>
.com-bot-manage-reports-index
  height 100%
  width 100%
  .ta-index-view:deep(.group)
    padding-bottom 4px
</style>
