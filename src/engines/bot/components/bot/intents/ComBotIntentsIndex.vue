<script lang="ts">
import { defineComponent, computed, ref, Ref, toRefs, reactive } from 'vue';
import { TaIndexViewTabInterface } from '@/components/global/ta-component/TaIndexView/types';
import { VObject } from '@/lib/vails/model';
import ComBotIntentsShow from './ComBotIntentsShow.vue'

const ComBotIntentsIndex = defineComponent({
  name: 'ComBotIntentsIndex',
  components: {
    ComBotIntentsShow,
  },
  props: {
    store: { type: Object, required: true },

  },
  setup(props, { emit }) {
    const config = computed(() => ({
      recordName: '意图管理',
      store: props.store,
      // pagination: {
      //   perPage: 15,
      //   showPageSizeChanger: false,
      //   hideOnSinglePage: false,
      //   showSizeChanger: false,
      // }
      template: 'bot_intent',
      // detail: {
      //   mode: 'auto',
      //   mode: 'drawer',
      //   width: '1100px',
      // },
      mode: 'table',
      // editApi:
      // showCount: true,
      actions: [
        { key: 'create', enabled: true },
        { key: 'update', enabled: true },
        { key: 'delete', enabled: true },
      ],
      table: {
        scroll: { y: 'auto' },
        // columns: [{ dataIndex: 'id', title: 'ID' }],
      },
      searcherSimpleOptions: [{ key: 'name', label: '意图名称', type: 'string' }],
    }));


    return {
      ...toRefs(props),
      config,
    };
  },
});

export default ComBotIntentsIndex;
</script>

<template lang="pug">
.com-bot-manage-intents-index
  TaIndexView(:config='config' :tabs='tabs' @onIndex='onIndex')
    //- template(#card='{ record }')
    //- template(#table)
    //-    a-table-column(title='名称' dataIndex='record')
    //- template(#detail='{ record, onClose }')
    //-   ComBotIntentsShow(
    //-     v-if='record.id'
    //-     :store='store'
    //-     :extendRoute='`/bot/manage/intents/${record.id}`'
    //-     :editable='editable'
    //-     @afterDelete='onClose'
    //-     @afterExtend='onClose'
    //-   )

</template>

<style lang="stylus" scoped>
.com-bot-manage-intents-index
  height 100%
  width 100%
</style>
