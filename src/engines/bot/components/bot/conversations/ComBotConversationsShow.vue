<script lang="ts">
import { VObject } from '@/lib/vails';
import { onMounted, Ref, computed } from 'vue';
import { defineComponent, toRefs, PropType, inject } from 'vue';
import { BotStateKey } from '../../conversations/ComBotConversationLayout.vue';
import { ref } from 'vue';
import ComBotChatInput from '../../base/ComBotChatInput.vue';
import ComBotConversationHeader from '../../conversations/ComBotConversationHeader.vue';
import ComBotConversationContainer from '../../conversations/ComBotConversationContainer.vue';
import useMessage from '@/engines/bot/utils/hooks/useMessage';
import ComBotArtifactsList from '../artifacts/ComBotArtifactsList.vue';
import ComBotArtifactsShow from '../artifacts/ComBotArtifactsShow.vue';
import ChatEventCenter from '@/engines/bot/utils/ChatEventCenter';

const ComBotConversationsShow = defineComponent({
  name: 'ComBotConversationsShow',
  components: {
    ComBotChatInput,
    ComBotConversationHeader,
    ComBotConversationContainer,
    ComBotArtifactsList,
    ComBotArtifactsShow
  },
  props: {
    store: { type: Object, required: true },
    messageStore: { type: Object, required: true },
    artifactStore: { type: Object, required: true },
  },
  setup(props, { emit }) {
    const state = inject<Ref<VObject>>(BotStateKey)!
    const loading = ref(false);

    const { fmtMessage, sendMessage } = useMessage(props.store)

    if (state.value.newMessage) {
      sendMessage(props.store.record?.value?.id, state.value.newMessage).then(() => {
        state.value.newMessage = ''
      })
    }

    const onInputConfirm = async (...args: any) => {
      if (loading.value) return;
      loading.value = true

      const msgs = fmtMessage(...args)
      await sendMessage(props.store.record?.value?.id, msgs)
      loading.value = false
    }



    ChatEventCenter.on('toggleArtifactShow', async (opts: { bool: boolean, artifact?: VObject }) => {
      state.value.visibleArtifactShow = opts.bool
      if (opts.bool && opts.artifact?.id) {
        await props.artifactStore.find(opts.artifact.id)
      }
    })
    ChatEventCenter.on('toggleArtifactList', async (opts: { bool: boolean }) => {
      state.value.visibleArtifactList = opts.bool
    })



    const artifactWidth = computed(() => ({
      width: state.value.visibleArtifactList || state.value.visibleArtifactShow ? (state?.value.artifactWidth || '340px') : '0',
    }))
    const onClickedArtifact = (aft: VObject, message: VObject) => {
      ChatEventCenter.emit('toggleArtifactShow', { bool: true, artifact: aft.content })
    }

    return {
      ...toRefs(props),
      record: props.store.record,
      messages: props.messageStore.records,
      state,
      loading,
      onInputConfirm,
      artifactWidth,
      onClickedArtifact,
      artifact: props.artifactStore.record
    };
  },
});
export default ComBotConversationsShow;
</script>

<template lang="pug">
.com-bot-user-conversations-show.w-full.h-full.flex.flex-col.relative
  ComBotConversationHeader.absolute.top-0.left-0.z-99.w-full(
    :record='record',
    :loading='loading',
    v-model:messages='messages'
  )
  .flex.justify-center.w-full.h-0.flex-grow.flex
    .flex-grow.w-0.flex.justify-center.py-4.overflow-y-auto.relative
      ComBotConversationContainer.w-full.transition-all.transform.min-w-343px.max-w-780px.mt-auto(
        v-model:messages='messages',
        :loading='loading',
        @confirm='onInputConfirm',
        @clickArtifact='onClickedArtifact'
      )
      div.transition-all(:style='artifactWidth')

    div(style='width:auto')
      .content.h-full.flex.flex-col.transition-all.ease-linear.overflow-hidden.transform.fixed.bottom-0.top-0.right-4(
        :style='artifactWidth',
      )
        transition-group(name='list')
          ComBotArtifactsList(
            v-if='state.visibleArtifactList && !state.visibleArtifactShow',
            :store='artifactStore'
          )
          ComBotArtifactsShow(
            v-else-if='state.visibleArtifactShow',
            :record='artifact'
            :store='artifactStore'
          )

    //- template(#xxx_tab)
</template>

<style lang="stylus" scoped>
.com-bot-user-conversations-show
  height 100%
</style>
