<script lang='ts'>
import { ref, defineComponent, toRefs, computed } from 'vue';
const ComBotReportTemplatesInput = defineComponent({
  name: 'ComBotReportTemplatesInput',
  components: {},
  props: {
    value: { type: String, default: '' },
    placeholder: { type: String, default: '' },
  },
  setup(props, { emit }) {
    const localValue = computed({
      get: () => props.value,
      set: (value: any) => {
        emit('update:value', value);
      },
    });
    return {
      ...toRefs(props),
      localValue,
    };
  },
});
export default ComBotReportTemplatesInput;
</script>

<template lang="pug">
a-input.com-bot-report-templates-input(
  v-model:value='localValue',
  :placeholder='placeholder'
)
</template>

<style lang="stylus" scoped></style>
