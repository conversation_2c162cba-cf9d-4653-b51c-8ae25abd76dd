<script lang='ts'>
import { ref, defineComponent, toRefs, PropType } from 'vue';
import { ReportTemplateField, fieldTypeLabels, FieldType } from '@/engines/bot/utils/hooks/useReportTemplateField';
import ComBotReportTemplatesInput from './ComBotReportTemplatesInput.vue';
import ComBotReportTemplatesSelect from './ComBotReportTemplatesSelect.vue';

const ComBotReportTemplatesField = defineComponent({
  name: 'ComBotReportTemplatesField',
  components: {
    ComBotReportTemplatesInput,
    ComBotReportTemplatesSelect
  },
  props: {
    field: { type: Object as PropType<ReportTemplateField>, default: () => ({}) },
  },
  setup(props, { emit }) {
    const updateFieldType = (type: keyof typeof fieldTypeLabels) => {
      props.field.type = type;
      emit('update', props.field)
    }
    return {
      ...toRefs(props),
      fieldTypeLabels,
      updateFieldType,
      FieldType
    };
  },
});
export default ComBotReportTemplatesField;
</script>

<template lang="pug">
.com-bot-report-templates-field.bg-white.p-3.rounded-lg
  header.flex.items-center.pb-3
    a-dropdown
      .flex.items-center
        .text-sm.text-gray-500.mr-1 {{ fieldTypeLabels[field.type] }}
        TaIcon(type='CaretDownOutlined', :size='12')
      template(#overlay)
        a-menu
          a-menu-item(
            v-for='(value, key) in fieldTypeLabels',
            :key='key'
            @click.stop='updateFieldType(key)'
          ) {{ value}}
    TaIcon.ml-auto.cursor-pointer(
      type='CloseOutlined',
      :size='12',
      @click.stop='() => $emit("delete",field)'
    )

  section.form__main.space-y-3
    //- .form__item
    //-   .form__item-label 标题
    //-   ComBotReportTemplatesInput(v-model:value='field.label', placeholder='请输入标题')
    .form__item
      .form__item-label 参数
      ComBotReportTemplatesInput(v-model:value='field.model_key', placeholder='请输入参数')

    .select__wrapper.space-y-3(v-if='field.type === FieldType.Select')
      .form__item
        .form__item-label 选项
        ComBotReportTemplatesSelect(
          v-model:field='field',
        )

</template>

<style lang="stylus" scoped>
.com-bot-report-templates-field .form__main .form__item-label
  @apply text-sm text-gray-900 font-medium;
.com-bot-report-templates-field .form__main .form__item
  @apply space-y-2;

</style>
