<script lang='ts'>
import { ref, defineComponent, toRefs } from 'vue';
const ComBotReportTemplatesSideBar = defineComponent({
  name: 'ComBotReportTemplatesSideBar',
  components: {},
  props: {
    value: { type: String, default: 'copy' },
  },
  setup(props) {
    let msg = ref('');
    return {
      ...toRefs(props),
      msg,
    };
  },
});
export default ComBotReportTemplatesSideBar;
</script>

<template lang="pug">
.container
  | component
</template>

<style lang="stylus" scoped></style>
