<script lang="ts">
import { defineComponent, toRefs, Ref, inject, onMounted, ref, PropType } from 'vue';
import ComOfficeEditorLayout from '../ComOfficeEditorLayout.vue';
import { OfficeStateKey } from '../ComOfficeEditorLayout.vue';
import { VObject } from '../../../../../lib/vails/model/index';
import { useTipTapEditor, TextOperation } from '../useTipTapEditor';
import usePaperMaxWidth from '../../../utils/hooks/usePaperMaxWidth';
import ComOfficeEditorFooter from '../ComOfficeEditorFooter.vue';
import ComTipTapBubbleMenu from '@/engines/bot/components/bot/ComTipTapBubbleMenu.vue';
import ComTipTapFloatingMenu from '@/engines/bot/components/bot/ComTipTapFloatingMenu.vue';
import ComBotReportTemplatesForm from './ComBotReportTemplatesForm.vue';
import ComBotReportsForm from '../reports/ComBotReportsForm.vue';
import ComOfficeEditorAiDropdown from '../ComOfficeEditorAiDropdown.vue';
import ReportTemplateEventCenter from '@/engines/bot/components/bot/report_templates/ReportTemplateEventCenter';
import { message } from 'ant-design-vue';
import ComOfficeEditorActions from '../ComOfficeEditorActions.vue';
import ComBotTab from '../../base/ComBotTab.vue';
import { useRouter } from 'vue-router';
import { cloneDeep } from 'lodash-es';

const ComBotReportTemplatesShow = defineComponent({
  name: 'ComBotReportTemplatesShow',
  components: {
    ComOfficeEditorFooter,
    ComOfficeEditorLayout,
    ComTipTapBubbleMenu,
    ComTipTapFloatingMenu,
    ComBotReportTemplatesForm,
    ComBotReportsForm,
    ComOfficeEditorAiDropdown,
    ComOfficeEditorActions,
    ComBotTab
  },
  props: {
    store: { type: Object, required: true },
    isManage: { type: Boolean, default: false },
    reportStore: { type: Object, default: () => ({}) },
    report: { type: Object, default: () => ({}) },
    ruleStore: { type: Object, default: () => ({}) },
  },
  emits: ['afterDelete', 'afterExtend'],
  setup(props, { emit }) {
    const state = inject<Ref<VObject>>(OfficeStateKey)!
    const router = useRouter()
    const { reactiveEditor, dispatchCommand, getSelectedText, typeWriterEffect } = useTipTapEditor();

    const [paperMaxWidth, updatePaperWidth, widthOptions] = usePaperMaxWidth()

    onMounted(() => {
      state.value.name = props.store.record?.value?.name
      state.value.editor = reactiveEditor

      if (reactiveEditor) {
        dispatchCommand('setContent')!(props.store.record?.value?.content)
      }
    })


    const processText = async (
      operation: keyof typeof TextOperation,
      successCallback?: (data: VObject) => void
    ) => {
      if (!props.report.id) {
        ReportTemplateEventCenter.emit('save')
        await state.value.promise
      }

      message.loading(`正在${TextOperation[operation]}中...`);

      try {
        const res = await props.reportStore.sendMemberAction({
          action: 'process_text',
          id: props.report.id,
          config: {
            data: {
              report: {
                operation,
                content: getSelectedText(),
              },
            },
          },
        });

        message.destroy();
        const data = res.data;
        const position = reactiveEditor.value.state.selection
        state.value.cache = {
          operation,
          position,
          data,
        }
        successCallback && successCallback?.(data)
      } catch (error) {
        message.destroy();
        console.error(`Error during ${operation}:`, error);
      }
    };

    ReportTemplateEventCenter.on('polish', (cb) => processText('polish', cb));
    ReportTemplateEventCenter.on('expand', (cb) => processText('expand', cb));
    ReportTemplateEventCenter.on('simplify', (cb) => processText('simplify', cb));
    ReportTemplateEventCenter.on('continue', (cb) => processText('continue', cb));

    const isLoading = ref(false)
    ReportTemplateEventCenter.on('generate', async () => {
      isLoading.value = true
      if (!props.report.id) {
        ReportTemplateEventCenter.emit('save')
        await state.value.promise
      }

      if (!props.isManage) {
        router.push({
          path: `/bot/user/reports/${props.report.id}`,
        });

        return
      }


      message.loading('生成中...', 0)
      try {
        const res = await props.reportStore.sendMemberAction({
          action: 'generate_from_template',
          id: props.report.id,
        });

        message.destroy();
        isLoading.value = false
        const text = Array.isArray(res.data.content) ? res.data.content.join('/n/n') : res.data.content;
        state.value.title = res.data.title
        const startPosition = 0

        dispatchCommand('clearContent')!(true);

        const stopCallback = async () => {
          dispatchCommand('setTextSelection', true)!({ from: startPosition, to: startPosition + text.length })
            .insertContent(text)
            .run()
        }

        typeWriterEffect(text, startPosition, stopCallback);
      } catch (error) {
        message.destroy();
        isLoading.value = false
      }

    })

    const tabs = [
      { label: '参数设置', key: 'variable', icon: 'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/ai_editor/ai_file_logo.png' },
      { label: '审查规则', key: 'rule', icon: 'flowbite-v2-solid/search' },
    ]
    const activeTab = ref(tabs[0])

    return {
      ...toRefs(props),
      record: props.store.record,
      rules: props.ruleStore?.records,
      reactiveEditor,
      dispatchCommand,
      paperMaxWidth,
      updatePaperWidth,
      widthOptions,
      tabs,
      activeTab,
      isLoading,
    };
  },
});
export default ComBotReportTemplatesShow;
</script>

<template lang="pug">
.com-bot-report-templates-show.w-full.h-full.flex.text-base
  .manage__sidebar.flex-shrink-0.w-480px.border-r-1.border-gray-100.p-6(v-if='isManage')
    header.flex.items-center.mb-3
      .logo.w-6.h-6.bg-cover.mr-1
      .text-base.text-black.font-medium 提示词设置
    section
      .py-3.rounded-lg.space-y-3.text-sm.text-gray-900
        .flex.items-center.font-medium 提示词
        a-textarea(v-model:value='record.formData.prompt' show-count :rows='8')

        .flex.items-center.font-medium 说明
        a-textarea(v-model:value='record.formData.instructions' show-count :rows='6')

  .left__wrapper.flex-grow.w-0.bg-white.relative.flex.flex-col.items-center(v-if='reactiveEditor' class='min-w-300px')
    .flex.justify-center.pt-4.pb-2.w-full.px-4
      ComOfficeEditorActions.w-full(
        :editor='reactiveEditor',
      )
    .paper.p-5.overflow-y-auto.w-full.flex-grow.h-0.pb-8(
      :style='{maxWidth: paperMaxWidth,paddingInline:"56px"}'
    )
      TaTipTapEditor(
        :editor='reactiveEditor',
        @click.stop=''
      )
      ComTipTapBubbleMenu(
        :editor='reactiveEditor'
      )
        template(#before)
          ComOfficeEditorAiDropdown.flex-shrink-0(
            :editor='reactiveEditor'
          )
      ComTipTapFloatingMenu(
        :editor='reactiveEditor'
      )

    ComOfficeEditorFooter.absolute.left-0.bottom-0(
      v-if='!isManage'
      :characterCount='reactiveEditor.storage.characterCount.characters()'
      :widthOptions='widthOptions'
      @updatePaperWidth='updatePaperWidth'
    )

  .right__ai.flex-shrink-0.w-400px.bg-gray-50.p-6.flex.flex-col(v-if='isManage')
    header.flex.items-center.mb-3
      .logo.w-6.h-6.bg-cover.mr-1
      .text-base.text-black.font-medium 模板参数设置
    .flex-grow.h-0.overflow-y-auto
      ComBotReportTemplatesForm(
        v-model:form='record.conf'
       )

  .right__ai.flex-shrink-0.w-300px.bg-gray-50.p-6.flex.flex-col(v-else)
    header.flex.items-center.mb-3
      .logo.w-6.h-6.bg-cover.mr-1
      .text-base.text-black.font-medium AI模板设置
    .flex-grow.h-0.overflow-y-auto
      ComBotReportsForm(
        :form='record.conf'
        v-model:formData='report.formData'
        :isLoading='isLoading'
      )

</template>

<style lang="stylus" scoped>
.com-bot-report-templates-show
  height 100%
  .right__ai .active-tab
    @apply bg-purple-100 text-purple-800;

.com-bot-report-templates-show .logo
  background-image url(https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/ai_editor/ai_file_logo.png)
</style>
