<script lang='ts'>
import { ref, defineComponent, toRefs, PropType, computed, onMounted } from 'vue';
import { ReportTemplateSelectOption } from '@/engines/bot/utils/hooks/useReportTemplateField';
import ComBotReportTemplatesInput from './ComBotReportTemplatesInput.vue';
import { ReportTemplateSelectField } from '../../../utils/hooks/useReportTemplateField';
const ComBotReportTemplatesSelect = defineComponent({
  name: 'ComBotReportTemplatesSelect',
  components: {
    ComBotReportTemplatesInput
  },
  props: {
    field: { type: Object as PropType<ReportTemplateSelectField>, default: () => ({}) },
  },
  setup(props, { emit }) {
    const localField = computed({
      get: () => props.field,
      set: val => emit('update:field', val),
    });

    const addOption = (index: number) => {
      localField.value.options.splice(index + 1, 0, { value: '' });
    }
    const deleteOption = (index: number) => {
      localField.value.options.splice(index, 1);
    }

    onMounted(() => {
      if (props.field.options?.length === 0 || !props.field.options) {
        localField.value.options = [
          { value: '' }
        ]
      }
    })
    return {
      ...toRefs(props),
      localField,
      addOption,
      deleteOption,
    };
  },
});
export default ComBotReportTemplatesSelect;
</script>

<template lang="pug">
.com-bot-report-templates-select.space-y-2
  .select__item.flex.space-x-2(v-for='(option, index) in localField.options')
    //- ComBotReportTemplatesInput(v-model:value='option.label', placeholder='选项')
    ComBotReportTemplatesInput(v-model:value='option.value', placeholder='值')
    .action.flex.items-center.space-x-2.pl-2
      TaIcon(type='PlusOutlined', :size='20', color='#9061F9', @click='addOption(index)')
      TaIcon(type='DeleteOutlined', :size='20', color='#6B7280', @click='deleteOption(index)')
</template>

<style lang="stylus" scoped></style>
