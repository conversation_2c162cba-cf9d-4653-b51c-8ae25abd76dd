<script lang='ts'>
import { ref, defineComponent, toRefs, computed, watch, reactive } from 'vue';
import { VObject } from '../../../../../lib/vails/model/index';
import { useRouter } from 'vue-router';
const ComBotUserReportTemplatesIndex = defineComponent({
  name: 'ComBotUserReportTemplatesIndex',
  components: {},
  props: {
    store: { type: Object, required: true },
  },
  setup(props) {
    const searchText = ref('');
    const searchQuery = reactive({
      name_cont_any: ''
    })
    let searchTimeoutId: any;
    const search = (text: string) => {
      searchQuery.name_cont_any = text;
    }
    watch(searchText, (newValue) => {
      clearTimeout(searchTimeoutId);
      searchTimeoutId = setTimeout(() => {
        search(newValue);
      }, 700);
    });

    const config = computed(() => ({
      store: props.store,
      mode: 'list',
      pagination: {
        perPage: 20,
        hideOnSinglePage: true,
      },
      scrollLoading: true,
      params: {
        q: searchQuery
      },
      list: {
        scroll: { y: 'auto' },
        gap: 16,
      },
    }));

    const tabs = computed(() => [
      {
        label: '全部',
        key: 'all',
      },
    ])

    const router = useRouter();
    const onShow = (record: VObject) => {
      router.push(`/bot/user/report_templates/${record.id}`);
    }

    const onIndex = (data: VObject) => {
      const uniqueIds = new Set();
      props.store.records.value = props.store.records.value.filter((record: any) => {
        const duplicate = uniqueIds.has(record.id);
        uniqueIds.add(record.id);
        return !duplicate;
      });
    }
    return {
      ...toRefs(props),
      config,
      tabs,
      searchText,
      onShow,
      onIndex
    };
  },
});
export default ComBotUserReportTemplatesIndex;
</script>

<template lang="pug">
.com-bot-user-report-templates-index.flex.flex-col.h-full
  header.py-3.flex.w-full.mb-4.h-69px.items-center.justify-between
    .text-lg.font-semibold.text-gray-900 AI模板
    .w-100.px-4.py-2px.rounded-lg.border-1.border-gray-300.bg-gray-50.flex.items-center
      TaIcon.w-4.h-4.text-gray-300(type='outline/search')
      a-input(v-model:value='searchText' placeholder='在模板库中搜索' :bordered="false")
  .index__bg.px-4.flex-grow.h-0.rounded-2xl.overflow-hidden.bg-cover
    TaIndexView.h-full(
      :config='config'
      :tabs='tabs'
      :showHeader='false'
      @onShow='onShow'
      @onIndex='onIndex'
    )
      template(#card='{record}')
        .index__card.min-h-112px.px-3.py-4.shadow.rounded-2xl.border-1.border-gray-100.flex.items-center.bg-white.cursor-pointer
          .icon.w-9.h-9.mr-3.bg-cover.bg-center(
            :style='`background-image: url(${record.icon?.files?.[0]?.url})`'
          )
          .card__info
            .text-base.text-gray-700.font-semibold.mb-1 {{ record.name }}
            .text-sm.text-gray-500 {{ record.description || "AI分析" }}
      template(#virtual_layout_tabs='{tabs, onTabClick, onTabIndexChange, activeTab}')
        .tabs.py-5.px-6.my-6.bg-white.rounded-2xl.border-1.border-gray-200.shadow.flex.space-x-2.w-full
          .px-5.py-10px.rounded-lg.border-1.border-gray-200.bg-white.text-gray-900.transition-all.cursor-pointer(
            v-for='(tab, index) in tabs',
            :class='{ "active-tab": activeTab.key === tab.key }',
            @click='onTabIndexChange(index)'
          ) {{ tab.label }}

</template>

<style lang="stylus" scoped>
.com-bot-user-report-templates-index .index__bg
  background-image url(https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/ai_editor/user_template_bg.png)
.active-tab
  color white
  background-color #9061F9
.com-bot-user-report-templates-index:deep(.ta-index-list) .group
  grid-template-columns repeat(auto-fill, minmax(240px, 1fr)) !important
</style>
