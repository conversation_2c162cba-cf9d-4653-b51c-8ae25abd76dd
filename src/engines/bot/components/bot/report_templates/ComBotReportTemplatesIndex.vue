<script lang="ts">
import { defineComponent, computed, toRefs, ref, reactive, watch, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { VObject } from '../../../../../lib/vails/model/index';

const ComBotReportTemplatesIndex = defineComponent({
  name: 'ComBotReportTemplatesIndex',
  components: {
  },
  props: {
    store: { type: Object, required: true },
  },
  setup(props, { emit }) {
    const searchText = ref('');
    const searchQuery = reactive({
      name_cont_any: ''
    })
    let searchTimeoutId: any;
    const search = (text: string) => {
      searchQuery.name_cont_any = text;
    }
    watch(searchText, (newValue) => {
      clearTimeout(searchTimeoutId);
      searchTimeoutId = setTimeout(() => {
        search(newValue);
      }, 700);
    });

    const config = computed(() => ({
      recordName: '模板管理',
      store: props.store,
      detail: {
        mode: 'route'
      },
      params: {
        q: searchQuery
      },
      pagination: {
        perPage: 20,
        showPageSizeChanger: false,
        hideOnSinglePage: true,
        showSizeChanger: false,
      },
      scrollLoading: true,
      template: 'bot_report_template',
      mode: 'list',
      actions: [
        { key: 'create', enabled: true },
        { key: 'update', enabled: true },
        { key: 'delete', enabled: true },
        { key: 'import', enabled: true },
        { key: 'export', enabled: true },
      ],
      list: {
        scroll: { y: 'auto' },
        gap: 16,
      },
    }));

    const tabs = computed(() => [
      {
        label: '全部',
        key: 'all',
      },
    ])

    const router = useRouter();
    const onShow = (record: VObject) => {
      router.push(`/bot/manage/report_templates/${record.id}`);
    }

    const taindexview = ref<any>(null);
    const onCreate = () => {
      taindexview.value?.slotActions?.onCreate()
    }
    const onEdit = (record: VObject) => {
      taindexview.value?.slotActions?.onEdit(record)
    }
    const onDelete = (record: VObject) => {
      taindexview.value?.slotActions?.onDelete(record)
    }

    const onIndex = (data: VObject) => {
      const uniqueIds = new Set();
      props.store.records.value = props.store.records.value.filter((record: any) => {
        const duplicate = uniqueIds.has(record.id);
        uniqueIds.add(record.id);
        return !duplicate;
      });
    }

    return {
      ...toRefs(props),
      config,
      tabs,
      searchText,
      onShow,
      taindexview,
      onCreate,
      onEdit,
      onDelete,
      onIndex,
    };
  },
});

export default ComBotReportTemplatesIndex;
</script>

<template lang="pug">
.com-bot-report-templates-index.flex.flex-col.h-full
  header.py-3.flex.w-full.mb-4.h-69px.items-center
    .text-lg.font-semibold.text-gray-900 模板管理
    .w-100.px-4.py-2px.rounded-lg.border-1.border-gray-300.bg-gray-50.flex.items-center.mr-3.ml-auto
      TaIcon.w-4.h-4.text-gray-300(type='outline/search')
      a-input(v-model:value='searchText' placeholder='在模板库中搜索' :bordered="false")
    button.text-sm(class='bg-purple-500 rounded !text-white !px-3 py-1 hover:bg-purple-600' @click.stop='onCreate') 新建
  .index__bg.px-4.flex-grow.h-0.rounded-2xl.overflow-hidden.bg-cover
    TaIndexView.h-full(
      :config='config'
      :tabs='tabs'
      :showHeader='false'
      @onShow='onShow'
      @onIndex='onIndex'
      ref='taindexview'
    )
      template(#card='{record}')
        .index__card.relative.min-h-112px.px-3.py-4.shadow.rounded-2xl.border-1.border-gray-100.flex.items-center.bg-white.cursor-pointer
          .index__card__actions.absolute.right-3.top-3.grid.grid-cols-2.gap-1.opacity-0(
            @click.stop=''
          )
            .w-5.h-5.rounded.grid(class='hover:bg-gray-100' @click.stop='onEdit(record)')
              TaIcon.place-self-center(type='outline/pencil' class='!w-3 !h-3')
            TaPopoverConfirm(
              title='删除',
              content='您确认删除该数据吗',
              @confirm='onDelete(record)'
            )
              .w-5.h-5.rounded.grid(class='hover:bg-gray-100')
                TaIcon.place-self-center(type='outline/trash' class='!w-3 !h-3')
          .icon.w-9.h-9.mr-3.bg-cover.bg-center(
            :style='`background-image: url(${record.icon?.files?.[0]?.url})`'
          )
          .card__info
            .text-base.text-gray-700.font-semibold.mb-1 {{ record.name }}
            .text-sm.text-gray-500 {{ record.description || "AI分析" }}
      template(#virtual_layout_tabs='{tabs, onTabClick, onTabIndexChange, activeTab}')
        .tabs.py-5.px-6.my-6.bg-white.rounded-2xl.border-1.border-gray-200.shadow.flex.space-x-2.w-full
          .px-5.py-10px.rounded-lg.border-1.border-gray-200.bg-white.text-gray-900.transition-all.cursor-pointer(
            v-for='(tab, index) in tabs',
            :class='{ "active-tab": activeTab.key === tab.key }',
            @click='onTabIndexChange(index)'
          ) {{ tab.label }}

</template>

<style lang="stylus" scoped>
.com-bot-report-templates-index .index__bg
  background-image url(https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/ai_editor/user_template_bg.png)
.active-tab
  color white
  background-color #9061F9
.index__card:hover .index__card__actions
  opacity 1

//滚动加载时,列表样式
.com-bot-report-templates-index:deep(.ta-index-list) .group
  grid-template-columns repeat(auto-fill, minmax(240px, 1fr)) !important
</style>
