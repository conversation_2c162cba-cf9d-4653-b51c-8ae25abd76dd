<script lang='ts'>
import { ref, defineComponent, toRefs, computed } from 'vue';
import ComBotReportTemplatesField from './ComBotReportTemplatesField.vue';
import { ReportTemplateField } from '@/engines/bot/utils/hooks/useReportTemplateField';

const ComBotReportTemplatesForm = defineComponent({
  name: 'ComBotReportTemplatesForm',
  components: { ComBotReportTemplatesField },
  props: {
    form: { type: Object, default: () => ({}) },
  },
  setup(props, { emit }) {
    const updateForm = (value: any) => {
      emit('update:form', value);
    }

    const _form = computed({
      get: () => props.form || {},
      set: (value) => {
        updateForm(value)
      },
    });



    const createField = () => {
      const newField = {
        type: 'input',
        key: `form_${Date.now()}`,
        model_key: ``,
      };

      if (!_form.value?.fields) {
        _form.value.fields = []
      }

      _form.value.fields.push(newField);
      updateForm(_form.value)
    };

    const _findFieldIndex = (key: string) => {
      const index = _form.value.fields.findIndex((item: any) => {
        return item.key === key;
      });
      return index
    }

    const updateField = (field: ReportTemplateField) => {
      const index = _findFieldIndex(field.key)

      if (index !== -1) {
        _form.value.fields[index] = field
        updateForm(_form.value)
      }
    }

    const deleteField = (field: ReportTemplateField) => {
      const index = _findFieldIndex(field.key)

      if (index !== -1) {
        _form.value.fields.splice(index, 1);
        updateForm(_form.value)
      }
    };
    return {
      ...toRefs(props),
      _form,
      createField,
      deleteField,
      updateField,
    };
  },
});
export default ComBotReportTemplatesForm;
</script>

<template lang="pug">
.com-bot-report-templates-form
  .form.grid.gap-4.mb-4
    template(v-for='(field, index) in _form.fields')
      ComBotReportTemplatesField(
        :field='field'
        @delete='deleteField',
        @update='updateField'
      )


  button.w-full.px-3.py-2.bg-purple-500.rounded-lg.flex.items-center.justify-center(
    @click='createField'
    class='!text-white !text-xs'
  )
    TaIcon(type='PlusOutlined' :size='12')
    .pl-2 添加参数
</template>

<style lang="stylus" scoped>
.com-bot-report-templates-form button:hover
  @apply bg-purple-600;
</style>
