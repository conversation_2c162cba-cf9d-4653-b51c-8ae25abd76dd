class ReportTemplateEventCenter {
  private listeners: Map<string, ((data: any) => void)[]> = new Map();

  on(event: string, callback: (data: any) => void) {
    this.listeners.set(event, [callback]);
  }

  emit(event: string, data?: any) {
    if (this.listeners.has(event)) {
      this.listeners.get(event)!.forEach(callback => callback(data));
    }
  }

  has(event: string) {
    return this.listeners.has(event);
  }
}

export default new ReportTemplateEventCenter();
