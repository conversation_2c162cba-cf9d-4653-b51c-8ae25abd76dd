<script lang='ts'>
import { ref, defineComponent, toRefs, inject, Ref, computed, onMounted, onUnmounted } from 'vue';
import { BpmUserApprovedInstances } from '@/engines/bpm/bpm-core/apis/user/approved/instance.api';
import { BpmUserApprovingInstances } from '@/engines/bpm/bpm-core/apis/user/approving/instance.api';
import { VObject } from '../../../../../lib/vails/model/index';
import { BotStateKey } from '../../conversations/ComBotConversationLayout.vue';
import { BpmUserUnreadInstances } from '../../../../bpm/bpm-core/apis/user/unread/instance.api';
import { InstanceModel } from '../../../../bpm/bpm-core/apis/user/instance.api';
import { VStore } from '../../../../../lib/vails/store/index';
import ComBpmInstanceCard from '@/engines/bpm/components/ComBpmInstanceCard.vue';
import ComBpmInstanceDetailEasyDialogFromIndex from '@/engines/bpm/components/ComBpmInstanceDetailEasyDialogFromIndex.vue';

const ComBpmInstanceTool = defineComponent({
  name: 'ComBpmInstanceTool',
  components: {
    ComBpmInstanceCard,
    ComBpmInstanceDetailEasyDialogFromIndex
  },
  props: {
    record: { type: Object, default: () => ({}) },
    scopeChain: { type: String, default: '' },
    params: { type: Object, default: () => ({}) },
  },
  setup(props) {
    const state = inject<Ref<VObject>>(BotStateKey)!
    const api = new BpmUserUnreadInstances()
    const fmtScopeChain = (scopeChain: string, prefix = '/bpm', name = '/instances') => {
      const scopeChainArr = scopeChain.split('.').slice(1)
      const result = scopeChainArr.map((item: string) => {
        if (item.includes('_')) {
          return item.replace('_', '/')
        }
        return item
      })
      return `${prefix}/${result.join('/').replace(name, '')}`
    }

    if (props.scopeChain) {
      api.namespace = fmtScopeChain(props.scopeChain)
    }

    const visibleMap = ref<VObject>({});
    const store = new VStore(api, InstanceModel);
    const config = computed(() => ({
      recordName: props.record.intentName,
      store,
      mode: 'list',
      params: props.params || {},
      pagination: {
        hideOnSinglePage: true,
      },
      list: {
        scroll: {
          y: 'auto',
        },
      },
    }))

    onMounted(() => {
      state.value.artifactWidth = '560px'
    })

    onUnmounted(() => {
      state.value.artifactWidth = undefined
    })

    const onShow = (record: VObject) => {
      visibleMap.value[`instance-${record.id}`] = true;
    };

    return {
      ...toRefs(props),
      config,
      visibleMap,
      onShow,
    };
  },
});
export default ComBpmInstanceTool;
</script>

<template lang="pug">
.com-bpm-instance-tool.px-4.h-full
  TaIndexView(:config='config', @onShow='onShow' :showHeader='false')
    template(#card='{ record, actions }')
      ComBpmInstanceCard(:record='record', @dialogClose='actions.silenceRefresh')
      ComBpmInstanceDetailEasyDialogFromIndex(
        v-if='visibleMap[`instance-${record.id}`]',
        v-model:visible='visibleMap[`instance-${record.id}`]',
        :instance='record',
        @close='actions.silenceRefresh'
      )
</template>

<style lang="stylus" scoped></style>
