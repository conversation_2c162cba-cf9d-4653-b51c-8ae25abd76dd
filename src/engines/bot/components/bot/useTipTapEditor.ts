import { Editor, useEditor, SingleCommands, EditorEvents } from '@tiptap/vue-3';
import StarterKit from '@tiptap/starter-kit';
import Image from '@tiptap/extension-image';
import Highlight from '@tiptap/extension-highlight';
import CharacterCount from '@tiptap/extension-character-count';
import TextAlign from '@tiptap/extension-text-align';
import TextStyle from '@tiptap/extension-text-style';
import Color from '@tiptap/extension-color';
import { Markdown } from 'tiptap-markdown';
import TaskItem from '@tiptap/extension-task-item';
import TaskList from '@tiptap/extension-task-list';
import CodeBlockLowlight from '@tiptap/extension-code-block-lowlight';
import { nextTick, ref, unref } from 'vue';
import { lowlight } from 'lowlight';
import css from 'highlight.js/lib/languages/css';
import js from 'highlight.js/lib/languages/javascript';
import ts from 'highlight.js/lib/languages/typescript';
import html from 'highlight.js/lib/languages/xml';

// create a lowlight instance
lowlight.registerLanguage('css', css);
lowlight.registerLanguage('javascript', js);
lowlight.registerLanguage('typescript', ts);
lowlight.registerLanguage('html', html);

export enum EditorMarkAction {
  Bold = 'toggleBold',
  Italic = 'toggleItalic',
  Code = 'toggleCode',
  Strike = 'toggleStrike',
  Blockquote = 'toggleBlockquote',
  BulletList = 'toggleBulletList',
}

export enum TextOperation {
  'polish' = '润色',
  'expand' = '扩写',
  'simplify' = '精简',
  'continue' = '续写',
}

export function useTipTapEditor(
  editor?: Editor,
  onUpdate?: (props: EditorEvents['update']) => void,
) {
  const reactiveEditor = ref(
    editor ??
      useEditor({
        content: '',
        extensions: [
          StarterKit,
          Image.configure({ allowBase64: true }),
          Highlight.configure({ multicolor: true }),
          CharacterCount.configure({
            limit: 9999,
          }),
          TextAlign.configure({
            types: ['heading', 'paragraph'],
            alignments: ['left', 'center', 'right', 'justify'],
          }),
          Markdown.configure({
            transformPastedText: true,
            transformCopiedText: true,
            bulletListMarker: '-',
            breaks: true,
          }),
          TextStyle,
          Color,
          TaskList,
          TaskItem.configure({
            nested: true,
          }),
          CodeBlockLowlight.configure({
            lowlight,
          }),
        ],
        editorProps: {
          attributes: {
            class: 'mx-auto focus:outline-none',
          },
        },
        onUpdate: props => {
          if (onUpdate) {
            onUpdate(props);
          }
        },
      }),
  );

  function dispatchCommand(
    command: keyof SingleCommands,
    isChainable: boolean = false,
    ed: Editor = unref(reactiveEditor),
  ): Function | undefined {
    if (isChainable) return ed.chain().focus()[command];
    return ed.commands[command];
  }

  function dispatchEditorMark(
    action: EditorMarkAction,
    isChainable: boolean = true,
    ed: Editor = unref(reactiveEditor),
  ): void {
    if (Object.values(EditorMarkAction).includes(action) === false)
      throw new Error('action not found');

    isChainable ? ed.chain().focus()[action]().run() : ed.commands[action]();
  }

  type StopTypeWriting = () => void;
  function typeWriterEffect(
    text: string,
    at?: number,
    stopCallback = () => {},
    ed: Editor = unref(reactiveEditor),
  ): StopTypeWriting {
    let index = 0;
    let animationFrameId: number;
    let hasStopped = false;
    function _type() {
      if (index < text.length) {
        const char = text.charAt(index);
        const node = {
          type: 'text',
          text: char,
        };
        at
          ? dispatchCommand('insertContentAt')!(at, node)
          : dispatchCommand('insertContent')!(node);

        index++;
        animationFrameId = requestAnimationFrame(() => {
          _type();
          at && at++;
          if (index === text.length && !hasStopped) {
            stopCallback?.();
            hasStopped = true;
          }
        });
      } else {
        cancelAnimationFrame(animationFrameId);
      }
    }

    _type();

    return () => {
      if (animationFrameId) {
        cancelAnimationFrame(animationFrameId);
      }
    };
  }

  function getSelectedText(ed: Editor = unref(reactiveEditor)) {
    const { from, to } = ed.state.selection;
    const selectedText = ed.state.doc.textBetween(from, to);
    return selectedText;
  }

  type TextAlignType = 'left' | 'center' | 'right' | 'justify';
  function toggleTextAlign(align: TextAlignType, ed: Editor = unref(reactiveEditor)) {
    if (ed.isActive({ textAlign: align })) {
      dispatchCommand('unsetTextAlign', true, ed)!().run();

      return;
    }

    dispatchCommand('setTextAlign', true, ed)!(align).run();
  }

  function isMarkdown(text: string) {
    const markdownPatterns = [
      /(^|\n)#{1,6} /, // 标题
      /(^|\n)(-|\*|\+|\d+\.) /, // 列表
      /(\*|_)(.*?)\1/, // 强调
      /\[.*\]\(.*\)/, // 链接
      /!\[.*\]\(.*\)/, // 图片
      /```/, // 代码块
      /`.*?`/, // 行内代码
    ];

    return markdownPatterns.some(pattern => pattern.test(text));
  }

  function findStringFromEditor(text: string, ed: Editor = unref(reactiveEditor)) {
    const content = ed.getText();
    console.log(content, 'content');
    const nospaceContent = content.replace(/\s+/g, '');
    const nospaceText = text.replace(/\s+/g, '');

    if (nospaceContent.includes(nospaceText)) {
      //获取nospaceText的前两个字符
      const firstTwoChars = text.slice(0, 2);
      const from = content.indexOf(text);
      const to = from + text.length;
      return { from: Math.max(from - 1, 0), to };
    }

    return {
      from: -1,
      to: -1,
    };
  }

  return {
    reactiveEditor,
    dispatchCommand,
    dispatchEditorMark,
    typeWriterEffect,
    getSelectedText,
    toggleTextAlign,
    isMarkdown,
    findStringFromEditor,
  };
}
