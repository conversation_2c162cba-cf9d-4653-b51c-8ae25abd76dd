<script lang='ts'>
import { ref, defineComponent, toRefs, onMounted, onUnmounted } from 'vue';
const ComBotReviewRulesCard = defineComponent({
  name: 'ComBotReviewRulesCard',
  components: {},
  props: {
    record: { type: Object, required: true },
  },
  emits: ['edit', 'delete'],
  setup(props) {
    const card = ref<any>(null);
    const addClass = () => {
      if (card.value) {
        card.value.classList.add('gradient-border')
      }
    };
    const removeClass = () => {
      if (card.value) {
        card.value.classList.remove('gradient-border');
      }
    };


    onMounted(() => {
      card.value?.addEventListener('mouseover', addClass);
      card.value?.addEventListener('mouseout', removeClass);
    })

    onUnmounted(() => {
      try {
        card.value?.removeEventListener('mouseover', addClass);
        card.value?.removeEventListener('mouseout', removeClass);
      } catch (e) { }
    })
    return {
      ...toRefs(props),
      card,
    };
  },
});
export default ComBotReviewRulesCard;
</script>

<template lang="pug">
.com-bot-review-rules-card.shadow.gray__border.relative.overflow-hidden.rounded-lg.p-6.flex.flex-col(
  ref='card'
  :class='{ "items-center justify-center": !record.id }'
)
  template(v-if='record.id')
    .header.flex.items-center.w-full.text-lg.font-medium.text-gray-900
      .truncate.flex-grow {{record.name}}
      .actions.flex-shrink-0.grid.grid-cols-2.gap-1
        .w-5.h-5.rounded.grid.text-gray-500(class='hover:bg-purple-500 hover:text-white',@click.stop='$emit("edit")')
          TaIcon.place-self-center(type='outline/pencil' class='!w-3 !h-3')
        TaPopoverConfirm(
          title='删除',
          content='您确认删除该数据吗',
          @confirm='$emit("delete")'
        )
          .w-5.h-5.rounded.grid.text-gray-500(class='hover:bg-purple-500 hover:text-white',@click.stop='')
            TaIcon.place-self-center(type='outline/trash' class='!w-3 !h-3')
    .text-gray-500.text-sm.mt-auto.ellipsis-two {{ record.content}}
  temlate(v-else)
    .w-full.flex.justify-center
      .w-5.h-5.rounded-full.bg-purple-500.grid.mb-2
        TaIcon.text-white.place-self-center(type='solid/plus' class='!w-3 !h-3')
    .text-base.font-medium.text-purple-500 新建规则


</template>

<style lang="stylus" scoped>
.ellipsis-two
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
.gray__border::before
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  padding: 2px;
  z-index:-1;
  box-sizing: border-box;
  border-radius: 8px;
  @apply bg-gray-200;
.com-bot-review-rules-card.gradient-border::before
  content:'';
  background-size:cover;
  background-position: 50% 60%;
  background-image:url(https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/ai_editor/%E8%8A%B1%E7%93%A3%E7%B4%A0%E6%9D%90_%E7%B2%89%E8%93%9D%E6%B8%90%E5%8F%98%E5%BC%A5%E6%95%A3%E8%83%8C%E6%99%AF%E5%9B%BE%E7%89%87_193465581.png)
</style>
