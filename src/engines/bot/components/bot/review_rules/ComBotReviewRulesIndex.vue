<script lang="ts">
import { defineComponent, computed, ref, Ref, toRefs, reactive } from 'vue';
import { TaIndexViewTabInterface } from '@/components/global/ta-component/TaIndexView/types';
import { VObject } from '@/lib/vails/model';
import ComBotReviewRulesShow from './ComBotReviewRulesShow.vue';
import ComBotReviewRulesCard from './ComBotReviewRulesCard.vue';

const ComBotReviewRulesIndex = defineComponent({
  name: 'ComBotReviewRulesIndex',
  components: {
    ComBotReviewRulesShow,
    ComBotReviewRulesCard,
  },
  props: {
    store: { type: Object, required: true },

  },
  setup(props, { emit }) {
    const config = computed(() => ({
      recordName: '审查规则',
      store: props.store,
      pagination: {
        perPage: 15,
        showPageSizeChanger: false,
        hideOnSinglePage: true,
        showSizeChanger: false,
      },
      template: 'bot_review_rule',
      mode: 'list',
      actions: [
        { key: 'create', enabled: false },
        { key: 'update', enabled: true },
        { key: 'delete', enabled: true },
        { key: 'import', enabled: true },
        { key: 'export', enabled: true },
      ],
      list: {
        scroll: { y: 'auto' },
        gap: 16,
        splitCount: 5,
      }
      // searcherSimpleOptions: [{key: 'title', label: '标题', type: 'string' }],
    }));


    const onIndex = (data: VObject) => {
      const hasCreateCard = !!props.store.records.value?.find((record: VObject) => record.id === 0)
      if (data.current_page === 1 && !hasCreateCard) {
        props.store.records.value.unshift({
          id: 0,
          name: '新建规则'
        })
      }
    };

    const taindexview = ref<any>(null);
    const onCreate = (record: VObject) => {
      taindexview.value?.slotActions?.onCreate(record)
    }

    const onEdit = (record: VObject) => {
      taindexview.value?.slotActions?.onEdit(record)
    }

    const onDelete = (record: VObject) => {
      taindexview.value?.slotActions?.onDelete(record)
    }

    const onShow = (record: VObject) => {
      if (record.id) return;
      taindexview.value?.slotActions?.onCreate()
    }
    return {
      ...toRefs(props),
      config,
      onIndex,
      taindexview,
      onCreate,
      onEdit,
      onDelete,
      onShow
    };
  },
});

export default ComBotReviewRulesIndex;
</script>

<template lang="pug">
.com-bot-manage-review-rules-index.p-4
  TaIndexView(:config='config' @onIndex='onIndex' @onShow='onShow' ref='taindexview')
    template(#header)
      .flex.items-center.mb-4
        .w-2.h-2.rounded-full.bg-purple-500.mr-1
        .text-base.text-black.font-medium 审查规则
    template(#card='{record}')
      .z-2.relative
        ComBotReviewRulesCard.h-40.cursor-pointer(:record='record' @edit='onEdit(record)' @delete='onDelete(record)')


</template>

<style lang="stylus" scoped>
.com-bot-manage-review-rules-index
  height 100%
  width 100%
</style>
