<script lang="ts">
import { defineComponent, computed, ref, Ref, toRefs, reactive } from 'vue';
import { TaIndexViewTabInterface } from '@/components/global/ta-component/TaIndexView/types';
import { VObject } from '@/lib/vails/model';
import ComBotReviewersShow from './ComBotReviewersShow.vue'
import { useRouter, useRoute } from 'vue-router';

const ComBotReviewersIndex = defineComponent({
  name: 'ComBotReviewersIndex',
  components: {
    ComBotReviewersShow,
  },
  props: {
    store: { type: Object, required: true },
  },
  setup(props, { emit }) {
    const config = computed(() => ({
      recordName: '审查应用',
      store: props.store,
      pagination: {
        perPage: 15,
        showPageSizeChanger: false,
        hideOnSinglePage: true,
        showSizeChanger: false,
      },
      template: 'bot_reviewer',
      mode: 'table',
      // editApi:
      // showCount: true,
      actions: [
        { key: 'create', enabled: true },
        { key: 'update', enabled: true },
        { key: 'delete', enabled: true },
        { key: 'import', enabled: true },
        { key: 'export', enabled: true },
      ],
      table: {
        scroll: { y: 'auto' },
      },
      // searcherSimpleOptions: [{key: 'title', label: '标题', type: 'string' }],
    }));

    const router = useRouter();
    const route = useRoute();
    const onShow = (record: VObject) => {
      router.push(`/bot/user/reviewers/${record.id}/review_documents`)
    }

    return {
      ...toRefs(props),
      config,
      onShow
    };
  },
});

export default ComBotReviewersIndex;
</script>

<template lang="pug">
.com-bot-user-reviewers-index
  TaIndexView(:config='config' :tabs='tabs' @onShow='onShow')
    //- template(#card='{ record }')
    //- template(#table)
    //-    a-table-column(title='名称' dataIndex='record')
    //- template(#detail='{ record, onClose }')
    //-   ComBotReviewersShow(
    //-     v-if='record.id'
    //-     :store='store'
    //-     :extendRoute='`/bot/user/reviewers/${record.id}`'
    //-     :editable='editable'
    //-     @afterDelete='onClose'
    //-     @afterExtend='onClose'
    //-   )

</template>

<style lang="stylus" scoped>
.com-bot-user-reviewers-index
  height 100%
  width 100%
</style>
