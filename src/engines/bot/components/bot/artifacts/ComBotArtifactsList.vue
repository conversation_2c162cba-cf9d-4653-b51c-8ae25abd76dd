<script lang='ts'>
import { ref, defineComponent, toRefs, inject, Ref, onMounted } from 'vue';
import { VObject } from '../../../../../lib/vails/model/index';
import { BotStateKey } from '../../conversations/ComBotConversationLayout.vue';
import ChatEventCenter from '@/engines/bot/utils/ChatEventCenter';
import { BotUserArtifactsApi } from '../../../apis/bot/user/artifacts.api';
import { BotArtifactModel } from '../../../models/bot/user/artifacts';
import { VStore } from '../../../../../lib/vails/store/index';
const ComBotArtifactsList = defineComponent({
  name: 'ComBotArtifactsList',
  components: {},
  props: {
    store: { type: Object, required: true },
  },
  setup(props) {
    const state = inject<Ref<VObject>>(BotStateKey)!

    onMounted(() => {
      props.store.index()
    })

    const onClickArtifact = (record: VObject) => {
      ChatEventCenter.emit('toggleArtifactShow', { bool: true, artifact: record })
    }
    const onCloseArtifactList = () => {
      ChatEventCenter.emit('toggleArtifactList', { bool: false })
    }
    return {
      ...toRefs(props),
      state,
      onCloseArtifactList,
      onClickArtifact,
      records: props.store.records,
    };
  },
});
export default ComBotArtifactsList;
</script>

<template lang="pug">
.com-bot-artifacts-list.h-full.flex.flex-col.my-8
  .bg-white.rounded-xl.overflow-hidden.w-full.h-full.flex.flex-col
    header.flex.justify-between.items-center.p-4.border-b.border-gray-200
      .text-base.text-gray-800.font-semibold(class='dark:text-white') 关联内容
      TaIcon.cursor-pointer(type='outline/x' class='!w-5 !h-5' @click.stop='onCloseArtifactList')
    .flex-grow.h-0.p-4
      .rounded.bg-gray-100.flex.overflow-hidden.cursor-pointer.mb-2(
        class='dark:bg-primary-900'
        v-for='record in records',
        @click='onClickArtifact(record)',
      )
        .bg-gray-200.p-2(class='dark:bg-primary-700') {{ '</>' }}
        .p-2 {{ record.intent_name }}
</template>

<style lang="stylus" scoped></style>
