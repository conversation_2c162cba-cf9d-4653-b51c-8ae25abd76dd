<script lang='ts'>
import { ref, defineComponent, toRefs, computed } from 'vue';
import { BotMentionTypeMapping } from '@/components/botConfig';
import ChatEventCenter from '@/engines/bot/utils/ChatEventCenter';
const ComBotArtifactsShow = defineComponent({
  name: 'ComBotArtifactsShow',
  components: {},
  props: {
    record: {
      type: Object,
      default: () => ({})
    },
    store: {
      type: Object,
      default: () => ({})
    }
  },
  setup(props) {
    const artifactComponent = computed(() => {
      return BotMentionTypeMapping[props.record?.tool_conf?.model_class]
    })
    const scopeChain = computed(() => {
      return props.record?.tool_conf?.scope_chain
    })
    const params = computed(() => {
      return props.record.meta
    })

    const onCloseArtifactShow = () => {
      ChatEventCenter.emit('toggleArtifactShow', { bool: false })
    }

    const onBackArtifactList = () => {
      ChatEventCenter.emit('toggleArtifactShow', { bool: false })
      let timer = setTimeout(() => {
        ChatEventCenter.emit('toggleArtifactList', { bool: true })
        clearTimeout(timer)
      }, 300)

    }
    return {
      ...toRefs(props),
      artifactComponent,
      onCloseArtifactShow,
      onBackArtifactList,
      scopeChain,
      params,
    };
  },
});
export default ComBotArtifactsShow;
</script>

<template lang="pug">
.com-bot-artifacts-show.h-full.flex.flex-col.my-8
  .bg-white.rounded-xl.overflow-hidden.w-full.h-full.flex.flex-col
    header.flex.justify-between.items-center.p-4.border-b.border-gray-200
      TaIcon.cursor-pointer(type='outline/arrow-left' class='!w-5 !h-5' @click.stop='onBackArtifactList')
      .text-base.text-gray-800.font-semibold(class='dark:text-white') {{ record.intent_name}}
      TaIcon.cursor-pointer(type='outline/x' class='!w-5 !h-5' @click.stop='onCloseArtifactShow')
    .w-full.bg-white.h-full.flex.flex-col
      component(
        v-if='record?.id',
        v-model:record='record',
        :store='store'
        :key='`${record.id}`',
        :is='artifactComponent',
        :scopeChain='scopeChain',
        :params='params'
      )
</template>

<style lang="stylus" scoped></style>
