<script lang='ts'>
import { ref, defineComponent, toRefs, computed } from 'vue';
const ComBotArtifactsCard = defineComponent({
  name: 'ComBotArtifactsCard',
  components: {},
  props: {
    record: {
      type: Object,
      default: () => ({})
    }
  },
  setup(props) {
    const intentName = computed(() => {
      return props.record?.content?.intent_name;
    })
    return {
      ...toRefs(props),
      intentName
    };
  },
});
export default ComBotArtifactsCard;
</script>

<template lang="pug">
.com-bot-artifacts-card.flex.rounded-lg.overflow-hidden.text-sm.cursor-pointer
  .bg-gray-100.p-2.pl-3.flex.items-center {{ "</>" }}
  .bg-gray-50.artifact.p-2.pr-3
    .name {{ intentName }}
    .notice.text-xs.text-gray-400(class='dark:text-primary-300') 点击查看详情
</template>

<style lang="stylus" scoped></style>
