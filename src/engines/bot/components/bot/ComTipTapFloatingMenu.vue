<script lang='ts'>
import { ref, defineComponent, toRefs, PropType } from 'vue';
import { Editor, FloatingMenu } from '@tiptap/vue-3';
import { useTipTapEditor, EditorMarkAction } from './useTipTapEditor';
const ComTipTapFloatingMenu = defineComponent({
  name: 'ComTipTapFloatingMenu',
  components: { FloatingMenu },
  props: {
    editor: { type: Object as PropType<Editor>, default: undefined },
    tippyOptions: { type: Object, default: () => ({ duration: 100 }) },
  },
  setup(props) {
    const {
      reactiveEditor,
      dispatchCommand,
      dispatchEditorMark
    } = useTipTapEditor(props.editor);
    return {
      ...toRefs(props),
      reactiveEditor,
      dispatchCommand,
      dispatchEditorMark,
      EditorMarkAction,
    };
  },
});
export default ComTipTapFloatingMenu;
</script>

<template lang="pug">
FloatingMenu(
  :editor='reactiveEditor'
  :tippy-options='tippyOptions'
)
  .floating-menu
    slot(
      name='menu',
      :editor='reactiveEditor',
      :action='{dispatchCommand, dispatchEditorMark}'
    )
      .flex.items-center.space-x-1.shadow.bg-gray-50.rounded.px-2.py-1.leading-none
        button.px-2.pt-2px.rounded(:class='{ "hover:bg-gray-100":true }')
          input.w-14px.h-14px(
            type='color',
            style='padding:0'
            :value='reactiveEditor.getAttributes("textStyle").color',
            @input='(e)=>dispatchCommand("setColor",true)(e.target.value).run()',
          )

        button.px-2.pb-1.rounded(
          @click.stop='dispatchEditorMark(EditorMarkAction.Blockquote,true)'
          :class='{ "is-active": reactiveEditor.isActive("blockquote"),"hover:bg-gray-100":true }'
        )
          TaIcon(type='ApiOutlined' size='14')

        button.px-2.pb-1.rounded(
          @click.stop='dispatchEditorMark(EditorMarkAction.Bold,true)'
          :class='{ "is-active": reactiveEditor.isActive("bold"),"hover:bg-gray-100":true }'
        )
          TaIcon(type='BoldOutlined' size='14')

        button.px-2.pb-1.rounded(
          @click.stop='dispatchEditorMark(EditorMarkAction.Italic,true)'
          :class='{ "is-active": reactiveEditor.isActive("italic"),"hover:bg-gray-100":true }'
        )
          TaIcon(type='ItalicOutlined' size='14')

        button.px-2.pb-1.rounded(
          @click.stop='dispatchEditorMark(EditorMarkAction.Strike,true)'
          :class='{ "is-active": reactiveEditor.isActive("strike"),"hover:bg-gray-100":true }'
        )
          TaIcon(type='StrikethroughOutlined' size='14')

        a-dropdown
          button.px-2.pb-1.rounded(
            @click.stop=''
            :class='{ "is-active": reactiveEditor.isActive("strike"),"hover:bg-gray-100":true }'
          )
            .text-sm.leading-none.pt-2px Hx
          template(#overlay)
            a-menu
              a-menu-item(
                v-for='(item) in [1,2,3,4,5,6]',
                :key='item'
                @click.stop='dispatchCommand("toggleHeading",true)({level:item}).run()'
              ) H{{ item }} {{item}}级标题
</template>

<style lang="stylus" scoped>
.floating-menu button input::-webkit-color-swatch-wrapper
  padding 0
  border 0
.floating-menu button input::-webkit-color-swatch
  border 0
  border-radius 4px
</style>
