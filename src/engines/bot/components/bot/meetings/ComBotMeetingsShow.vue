<script lang="ts">
import { defineComponent, toRefs, inject, Ref, onMounted, ref, computed } from 'vue';
import { VObject } from '../../../../../lib/vails/model/index';
import { OfficeStateKey } from '../ComOfficeEditorLayout.vue';
import usePaperMaxWidth from '../../../utils/hooks/usePaperMaxWidth';
import { useTipTapEditor } from '../useTipTapEditor';
import ComBotMeetingsProgress from './ComBotMeetingsProgress.vue';
import ComTipTapBubbleMenu from '../ComTipTapBubbleMenu.vue';
import ComTipTapFloatingMenu from '../ComTipTapFloatingMenu.vue';
import ComBotMeetingAudioSegment from './ComBotMeetingAudioSegment.vue';
import ComBotMeetingConversation from './ComBotMeetingConversation.vue';
import ComBotTab from '../../base/ComBotTab.vue';
import { message } from 'ant-design-vue';
import { uniqBy } from 'lodash-es';

const ComBotMeetingsShow = defineComponent({
  name: 'ComBotMeetingsShow',
  components: {
    ComBotMeetingsProgress,
    ComTipTapBubbleMenu,
    ComTipTapFloatingMenu,
    ComBotMeetingAudioSegment,
    ComBotMeetingConversation,
    ComBotTab
  },
  props: {
    store: { type: Object, required: true },
  },
  setup(props, { emit }) {
    const state = inject<Ref<VObject>>(OfficeStateKey)!
    const { reactiveEditor, dispatchCommand } = useTipTapEditor();

    const [paperMaxWidth, updatePaperWidth, widthOptions] = usePaperMaxWidth()

    const tabs = [{
      label: '音频',
      key: 'audio',
      icon: 'outline/volume-up'
    }, {
      label: 'AI聊天',
      key: 'ai',
      icon: 'outline/chat'
    }]
    const activeTab = ref(tabs[0])

    onMounted(() => {
      state.value.editor = reactiveEditor

      if (reactiveEditor) {
        const summary = props.store.record?.value?.summary
        dispatchCommand('setContent', true)!(summary).run()
      }
    })

    const handleAudioSegmentClick = (segment: VObject) => {
      state.value.setAudioCurrentTime(segment.start)
    }

    const contents = computed(() => {
      return uniqBy(props.store.record.value?.file?.contents, 'speaker')
    })

    const editSpeakerModalVisible = ref(false)
    const confirmLoading = ref(false)
    const currentSpeakerName = ref('')
    const currentSpeakerIndex = ref(-1)
    const onOpenEditSpeakerModal = (segment: VObject, index: number) => {
      editSpeakerModalVisible.value = true
      currentSpeakerName.value = segment.speaker
      currentSpeakerIndex.value = index
    }

    const onOK = async () => {
      confirmLoading.value = true
      const contents = props.store.record.value?.file?.contents
      contents[currentSpeakerIndex.value].speaker = currentSpeakerName.value

      await props.store.record.value.update({
        file: {
          contents
        }
      })
      message.success('编辑成功')
      editSpeakerModalVisible.value = false
      confirmLoading.value = false
      currentSpeakerName.value = ''
      currentSpeakerIndex.value = -1
    }

    const onCancel = () => {
      editSpeakerModalVisible.value = false
      currentSpeakerName.value = ''
      currentSpeakerIndex.value = -1
    }

    const onReplaceAll = async () => {
      confirmLoading.value = true
      const contents = props.store.record.value?.file?.contents

      const oldSpeakerName = contents[currentSpeakerIndex.value].speaker
      contents.forEach((item: VObject) => {
        if (item.speaker === oldSpeakerName) {
          item.speaker = currentSpeakerName.value
        }

      })
      await props.store.record.value.update({
        file: { contents }
      })

      message.success('编辑成功')
      editSpeakerModalVisible.value = false
      confirmLoading.value = false
      currentSpeakerName.value = ''
      currentSpeakerIndex.value = -1
    }


    return {
      ...toRefs(props),
      record: props.store.record,
      paperMaxWidth,
      updatePaperWidth,
      widthOptions,
      reactiveEditor,
      tabs,
      activeTab,
      state,
      handleAudioSegmentClick,
      onOpenEditSpeakerModal,
      editSpeakerModalVisible,
      currentSpeakerName,
      onOK,
      onCancel,
      confirmLoading,
      contents,
      onReplaceAll,
    };
  },
});
export default ComBotMeetingsShow;
</script>

<template lang="pug">
.com-bot-manage-meetings-show.w-full.h-full.flex
  a-modal(
    v-model:visible='editSpeakerModalVisible'
    title='编辑发言人'
    @ok='onOK'
    @cancel='onCancel',
    :confirm-loading="confirmLoading"
  )
    a-input(v-model:value='currentSpeakerName')
    template(#footer)
      a-button(type='primary' @click='onOK' class='!bg-purple-500 !border-purple-500') 修改当前发言人
      a-button(type='primary' @click='onReplaceAll' class='!bg-purple-500 !border-purple-500') 替换全部
      a-button(@click='onCancel') 取消
  .left__wrapper.flex-grow.w-0.bg-white.relative.flex.flex-col.items-center.px-16.pt-6(class='min-w-2xl')
    .editable__wrapper.p-4.rounded-2xl.bg-cover.w-full.mb-4(
      style='background-image:url(https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/ai_editor/ai__bg2.png);'
    )
      .editable__div.text-base.font-medium.text-black.mb-10px.min-w-4(
        contenteditable
        @input='record.formData.name = $event.target.innerText'
      ) {{ record.formData.name }}

      .flex.items-center.text-gray-500.text-sm.mb-10px
        .mr-10px 会议时间
        div.editable__div.flex-grow.w-0(
          contenteditable
          @input='record.formData.meeting_time = $event.target.innerText'
        ) {{ record.formData.meeting_time }}

      .flex.items-center.text-gray-500.text-sm.mb-10px
        .mr-10px 参会人
        div.editable__div.flex-grow.w-0(
          contenteditable
          @input='record.formData.participants = $event.target.innerText'
        ) {{ record.formData.participants }}

      .flex.items-center.text-gray-500.text-sm
        .mr-10px 发言人
        .flex.flex-grow.flex-wrap.gap-x-2.gap-y-1
          div.rounded-md.px-3.py-2px.bg-purple-100.text-purple-500.text-xs(
            v-for='content in contents'
          ) {{ content.speaker }}



    .text-sm.text-gray-700.mb-2.w-full 会议纪要
    .editor.flex-grow.h-0.overflow-y-auto.w-full(v-if='reactiveEditor')
      TaTipTapEditor(
        :editor='reactiveEditor',
        @click.stop=''
      )
      ComTipTapBubbleMenu(
        :editor='reactiveEditor'
      )
      ComTipTapFloatingMenu(
        :editor='reactiveEditor'
      )
    footer.w-full.py-4
      ComBotMeetingsProgress.space-y-5(:record='record')
  .right__ai.flex-shrink-0.w-500px.bg-gray-50.p-6.flex.flex-col
    ComBotTab.bg-white.mb-4(
      :tabs='tabs'
      v-model:activeTab='activeTab'
    )
    .right__ai-content.flex-grow.h-0.overflow-y-auto.relative(v-show='activeTab.key === "audio"')
      .audio___content.space-y-2
        template(v-for='(segment, index) in record.file?.contents')
          .space-y-1.flex-shirnk-0
            .rounded-md.px-3.py-2px.bg-purple-100.flex.items-center.gap-x-1(
              style='width:fit-content'
            )
              TaIcon.text-purple-500(type='solid/user',class='!w-3 !h-3')
              .text-xs.text-purple-500.font-medium(
              ) {{  segment.speaker }}
              TaIcon.text-purple-500.cursor-pointer(
                type='solid/pencil',
                class='!w-3 !h-3',
                @click.stop='onOpenEditSpeakerModal(segment,index)'
              )
            ComBotMeetingAudioSegment.cursor-pointer(
              :value='segment',
              @click.stop='handleAudioSegmentClick(segment)'
            )
    .right__ai-content.flex-grow.h-0(v-show='activeTab.key === "ai"')
      ComBotMeetingConversation(:store='store')
</template>

<style lang="stylus" scoped>
.com-bot-manage-meetings-show
  height 100%
  .editable__div:focus-visible
    outline none
  .right__ai .active-tab
    @apply bg-purple-100 text-purple-800;

</style>
