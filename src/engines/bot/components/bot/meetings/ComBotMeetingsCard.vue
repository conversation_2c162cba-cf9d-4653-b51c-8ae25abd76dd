<script lang='ts'>
import { ref, defineComponent, toRefs, onMounted, onUnmounted } from 'vue';
import dayjs from 'dayjs';
import TaPopoverConfirm from '../../../../../components/global/ta-component/TaPopoverConfirm.vue';
const ComBotMeetingsCard = defineComponent({
  name: 'ComBotMeetingsCard',
  components: {},
  props: {
    record: { type: Object, default: () => ({}) },
  },
  emits: ['edit', 'delete', 'regenerate'],
  setup(props) {
    const card = ref<any>(null);
    const addClass = () => {
      if (card.value) {
        card.value.classList.add('gradient-border')
      }
    };
    const removeClass = () => {
      if (card.value) {
        card.value.classList.remove('gradient-border');
      }
    };


    onMounted(() => {
      card.value?.addEventListener('mouseover', addClass);
      card.value?.addEventListener('mouseout', removeClass);
    })

    onUnmounted(() => {
      try {
        card.value?.removeEventListener('mouseover', addClass);
        card.value?.removeEventListener('mouseout', removeClass);
      } catch (e) { }
    })

    return {
      ...toRefs(props),
      dayjs,
      card,
    };
  },
});
export default ComBotMeetingsCard;
</script>

<template lang="pug">
.meeting__card.rounded-2xl.bg-white.shadow.px-4.py-6.cursor-pointer.relative.gray-border(
  ref='card'
)
  .meeting__icon.w-8.h-8.rounded-lg.bg-primary-500.grid.mb-3
    TaIcon.text-white.place-self-center(type='solid/user-group' class='!w-4 !h-4')
  .meeting__name.text-base.text-gray-700.font-semibold.truncate.w-full.mb-1 {{ record.name }}
  .meeting__date.text-sm.text-gray-500 {{ dayjs(record.created_at).format('YYYY-MM-DD HH:mm') }}

  .more.absolute.right-4.bottom-2.flex.items-center.text-purple-700.opacity-0
    .text-sm.pr-2 查看详情
    TaIcon.w-3.h-3(type='outline/arrow-right')
  .actions.absolute.right-3.top-3.flex.items-center.opacity-0.grid.grid-cols-3.gap-1
    .w-7.h-7.rounded.grid.text-gray-500(class='hover:bg-purple-500 hover:text-white',@click.stop='$emit("regenerate")')
      TaIcon.place-self-center(type='outline/refresh' class='!w-4 !h-4')
    .w-7.h-7.rounded.grid.text-gray-500(class='hover:bg-purple-500 hover:text-white',@click.stop='$emit("edit")')
      TaIcon.place-self-center(type='outline/pencil' class='!w-4 !h-4')
    TaPopoverConfirm(
      title='删除',
      content='您确认删除该数据吗',
      @confirm='$emit("delete")'
    )
      .w-7.h-7.rounded.grid.text-gray-500(class='hover:bg-purple-500 hover:text-white',@click.stop='')
        TaIcon.place-self-center(type='outline/trash' class='!w-4 !h-4')
</template>

<style lang="stylus" scoped>
.meeting__card:hover
  background linear-gradient(0deg, rgba(255, 255, 255, 0.70) 0%, rgba(255, 255, 255, 0.70) 100%), url(https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/ai_editor/%E8%8A%B1%E7%93%A3%E7%B4%A0%E6%9D%90_%E7%B2%89%E8%93%9D%E6%B8%90%E5%8F%98%E5%BC%A5%E6%95%A3%E8%83%8C%E6%99%AF%E5%9B%BE%E7%89%87_193465581.png) lightgray 50% / cover no-repeat;
  background-size:cover;
  background-position: 50% 60%;
  .more
    opacity 1
  .actions
    opacity 1

.gray-border::before
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  padding: 1px;
  box-sizing: border-box;
  border-radius: 16px;
  @apply bg-gray-200;
.meeting__card.gradient-border::before
  content:'';
  background-image:url(https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/ai_editor/%E8%8A%B1%E7%93%A3%E7%B4%A0%E6%9D%90_%E7%B2%89%E8%93%9D%E6%B8%90%E5%8F%98%E5%BC%A5%E6%95%A3%E8%83%8C%E6%99%AF%E5%9B%BE%E7%89%87_193465581.png)
</style>
