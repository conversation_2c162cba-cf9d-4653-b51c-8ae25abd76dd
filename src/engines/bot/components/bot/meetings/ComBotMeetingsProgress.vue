<script lang='ts'>
import { ref, defineComponent, toRefs, computed, nextTick, watch, inject, Ref } from 'vue';
import { useMeetingsProgress } from './useMeetingsProgress';
import { VObject } from '../../../../../lib/vails/model/index';
import { OfficeStateKey } from '../ComOfficeEditorLayout.vue';
const ComBotMeetingsProgress = defineComponent({
  name: 'ComBotMeetingsProgress',
  components: {},
  props: {
    record: { type: Object, required: true },
  },
  setup(props) {
    const progressWrapper = ref<HTMLDivElement | null>(null);
    const state = inject<Ref<VObject>>(OfficeStateKey)!
    const {
      canvasRef,
      audioRef,
      audioDuration,
      play,
      pause,
      backwardSeconds,
      forwardSeconds,
      isPlaying,
      audioCurrentTime
    } = useMeetingsProgress();

    state.value.setAudioCurrentTime = (time: number) => {
      audioRef.value.currentTime = time
    }

    const computedAudioDuration = computed(() => {
      return formatTime(audioDuration.value || 0);
    });
    const computedAudioCurrentTime = computed(() => {
      return formatTime(audioCurrentTime.value || 0);
    });

    const left = ref(0);
    watch(() => audioCurrentTime.value, () => {
      state.value.audioCurrentTime = audioCurrentTime.value
      nextTick(() => {
        left.value = (audioCurrentTime.value / audioDuration.value * 100) || 0;
        console.log('left', left.value);
      })
    }, { immediate: true })

    function formatTime(seconds: number) {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const secs = Math.floor(seconds % 60);

      // 使用 padStart 确保每个部分都是两位数
      return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(secs).padStart(2, '0')}`;
    }

    const onClickWrapper = (e: any) => {
      const x = e.offsetX;
      const width = progressWrapper.value?.offsetWidth || 0;
      const percentage = (x / width) * 100;
      audioRef.value.currentTime = (percentage / 100) * audioDuration.value;
      play()
    }


    return {
      ...toRefs(props),
      canvasRef,
      audioRef,
      computedAudioDuration,
      computedAudioCurrentTime,
      left,
      play,
      pause,
      forwardSeconds,
      backwardSeconds,
      isPlaying,
      progressWrapper,
      onClickWrapper,
    };
  },
});
export default ComBotMeetingsProgress;
</script>

<template lang="pug">
.com-bot-meetings-progress
  .rounded-lg.bg-gray-100.h-10.relative.w-full.overflow-hidden(
    ref='progressWrapper'
    @click='onClickWrapper'
  )
    .absolute.bg-purple-500.w-1.h-full.left-0.rounded-lg.transition-all(:style='`left:${left}%`')
    canvas.w-full.h-full(ref='canvasRef', width="800" height="200")
    audio.hidden(
      ref='audioRef',
      :autoplay='false'
      controls
      :src='record?.audio?.url'
    )
  .p-10px.text-center.text-2xl.font-semibold.text-black {{ computedAudioCurrentTime }} / {{ computedAudioDuration }}

  .actions.flex.items-center.justify-center
    .audio__btns.space-x-6
      button(@click='backwardSeconds()')
        TaIcon(type='RedoOutlined', :size='24', color='#6B7280')
      button(@click='play()',v-if='!isPlaying')
        TaIcon(type='PlayCircleOutlined', :size='24', color='#6B7280')
      button(@click='pause()',v-else)
        TaIcon(type='PauseOutlined', :size='24', color='#6B7280')
      button(@click='forwardSeconds()')
        TaIcon(type='UndoOutlined', :size='24', color='#6B7280')
</template>

<style lang="stylus" scoped></style>
