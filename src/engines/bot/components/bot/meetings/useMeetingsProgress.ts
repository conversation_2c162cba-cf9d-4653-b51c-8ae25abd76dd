import { onMounted, onUnmounted, ref } from 'vue';

export function useMeetingsProgress() {
  const audioRef = ref<any>(null);
  const canvasRef = ref<any>(null);
  const isDrawing = ref(false);
  const isPlaying = ref(false);
  const audioDuration = ref<number>(0);
  const audioCurrentTime = ref<number>(0);

  let audioContext: AudioContext;
  let analyser: AnalyserNode;
  let source: MediaElementAudioSourceNode;
  let audioBuffer: AudioBuffer;
  let volumeData: any[];
  let animationFrameId: number | null;

  // 初始化 Web Audio API
  function initAudioContext() {
    audioContext = new (window.AudioContext || (window as any).webkitAudioContext)();
    analyser = audioContext.createAnalyser();
    analyser.fftSize = 2048; // 设置 FFT 大小
  }

  function connectAudioElement() {
    source = audioContext.createMediaElementSource(audioRef.value);
    source.connect(analyser);
    analyser.connect(audioContext.destination);
  }

  async function loadAudioFile(url: string) {
    const response = await fetch(url);
    const arrayBuffer = await response.arrayBuffer();
    audioBuffer = await audioContext.decodeAudioData(arrayBuffer);
  }

  function analyzeVolumeData() {
    const channelData = audioBuffer.getChannelData(0); // 获取第一个声道的数据
    const totalSamples = channelData.length;
    const segmentCount = 200; // 固定分段数量
    const samplesPerSegment = Math.floor(totalSamples / segmentCount);
    volumeData = [];

    for (let i = 0; i < segmentCount; i++) {
      const start = i * samplesPerSegment;
      const end = start + samplesPerSegment;
      const segment = channelData.slice(start, end);
      const maxAmplitude = segment.reduce((max, value) => Math.max(max, Math.abs(value)), 0);
      volumeData.push(maxAmplitude); // 存储每段的最大振幅
    }
    console.log('volumeData', volumeData);
  }

  async function onAudioLoaded() {
    initAudioContext();
    connectAudioElement();
    audioDuration.value = audioRef.value.duration;
    // await loadAudioFile(audioRef.value.src);
    // analyzeVolumeData();
    // isDrawing.value = true;
    // renderVisualization();
  }

  function renderVisualization() {
    const ctx = canvasRef.value.getContext('2d');
    const barCount = volumeData.length;
    const barWidth = canvasRef.value.width / barCount;

    function draw() {
      if (!isDrawing.value) return;
      animationFrameId = requestAnimationFrame(draw);

      ctx.clearRect(0, 0, canvasRef.value.width, canvasRef.value.height);

      const currentTime = audioRef.value.currentTime;
      const duration = audioRef.value.duration;
      const progress = currentTime / duration;

      for (let i = 0; i < barCount; i++) {
        const barHeight = volumeData[i] * canvasRef.value.height;

        // 设置颜色和透明度
        const opacity = i / barCount < progress ? 1 : 0.3; // 未播放部分透明度为 80%
        ctx.fillStyle = `rgba(144, 97, 249, ${opacity})`;
        ctx.fillRect(i * barWidth, canvasRef.value.height - barHeight, barWidth - 1, barHeight);
        if (i === barCount - 1) {
          stopDynamicVisualization();
        }
      }
    }

    draw();
  }

  // 开始动态绘制
  function startDynamicVisualization() {
    isDrawing.value = true;
    renderVisualization();
  }

  // 停止动态绘制
  function stopDynamicVisualization() {
    isDrawing.value = false;
    if (animationFrameId) {
      cancelAnimationFrame(animationFrameId);
      animationFrameId = null;
    }
  }

  function play() {
    if (audioRef.value && audioContext) {
      audioContext.state === 'suspended' && audioContext.resume();
      audioRef.value.play();
      isPlaying.value = true;
    }
  }

  function pause() {
    if (audioRef.value) {
      audioRef.value.pause();
      isPlaying.value = false;
    }
  }

  function onEnded() {
    isDrawing.value = false;
    isPlaying.value = false;
    audioCurrentTime.value = 0;
  }

  const forwardSeconds = (seconds: number = 15) => {
    if (audioRef.value) {
      const newTime = audioRef.value.currentTime + seconds;
      audioRef.value.currentTime = Math.min(newTime, audioDuration.value);
    }
  };

  const backwardSeconds = (seconds: number = 15) => {
    if (audioRef.value) {
      const newTime = audioRef.value.currentTime - seconds;
      audioRef.value.currentTime = Math.max(newTime, 0);
    }
  };

  onMounted(async () => {
    // 监听音频加载完成事件
    audioRef.value.crossOrigin = 'anonymous';
    audioRef.value.addEventListener('loadeddata', onAudioLoaded);
    audioRef.value.addEventListener('timeupdate', () => {
      audioCurrentTime.value = audioRef.value.currentTime;
    });
    // audioRef.value.addEventListener('play', startDynamicVisualization);
    // audioRef.value.addEventListener('pause', stopDynamicVisualization);
    audioRef.value.addEventListener('ended', onEnded);
  });

  onUnmounted(() => {
    if (audioRef.value) {
      audioRef.value.removeEventListener('loadeddata', onAudioLoaded);
      // audioRef.value.removeEventListener('play', startDynamicVisualization);
      // audioRef.value.removeEventListener('pause', stopDynamicVisualization);
      audioRef.value.removeEventListener('ended', onEnded);
    }
  });

  return {
    audioRef,
    canvasRef,
    isDrawing,
    audioDuration,
    startDynamicVisualization,
    stopDynamicVisualization,
    play,
    pause,
    forwardSeconds,
    backwardSeconds,
    isPlaying,
    audioCurrentTime,
  };
}
