<script lang='ts'>
import { ref, defineComponent, toRefs, PropType, computed, inject, Ref, watch } from 'vue';
import { VObject } from '../../../../../lib/vails/model/index';
import { OfficeStateKey } from '../ComOfficeEditorLayout.vue';
type Segment = {
  start: number,
  end: number,
  text: string,
}
const ComBotMeetingAudioSegment = defineComponent({
  name: 'ComBotMeetingAudioSegment',
  components: {},
  props: {
    value: { type: Object as PropType<Segment>, default: () => ({}) },
  },
  setup(props) {
    const state = inject<Ref<VObject>>(OfficeStateKey)!
    const segmentRef = ref<HTMLDivElement | null>(null)
    const isActive = computed(() => {
      return state.value.audioCurrentTime >= Math.floor(props.value.start) && state.value.audioCurrentTime < Math.floor(props.value.end)
    })

    watch(() => isActive.value, () => {
      if (isActive.value) {
        segmentRef.value?.scrollIntoView({
          block: 'center',
          inline: 'center',
          behavior: 'smooth',
        })
      }
    })
    function formatTime(seconds: number) {
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const secs = Math.floor(seconds % 60);

      if (hours < 1) {
        return `${String(minutes).padStart(2, '0')}:${String(secs).padStart(2, '0')}`
      }

      return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(secs).padStart(2, '0')}`;
    }

    const localValue = computed(() => {
      return `[${formatTime(props.value.start)} -${formatTime(props.value.end)}] ${props.value.text}`
    })

    return {
      ...toRefs(props),
      localValue,
      isActive,
      segmentRef,
    };
  },
});
export default ComBotMeetingAudioSegment;
</script>

<template lang="pug">
.com-bot-meeting-audio-segment.font-semibold.text-gray-400(
  ref='segmentRef'
  :class='{ "segment__active": isActive }'
)
  | {{ localValue}}
</template>

<style lang="stylus" scoped>
.com-bot-meeting-audio-segment
  font-size 1rem
.segment__active
  @apply text-purple-700 transition-all;

</style>
