<script lang='ts'>
import { ref, defineComponent, toRefs, nextTick, DirectiveBinding } from 'vue';
import ComBotChatInput from '../../base/ComBotChatInput.vue';
import { message } from 'ant-design-vue';
import markdownit from 'markdown-it';

const enterHandler = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    const textarea = el.querySelector('textarea') || el;

    if (!textarea) return;

    textarea.addEventListener('keydown', (e: KeyboardEvent) => {
      if (e.key === 'Enter') {
        if (e.shiftKey) {
          // Shift+Enter: 允许换行，不做任何特殊处理
          return;
        } else {
          // 仅 Enter: 阻止默认行为并触发自定义处理函数
          console.log('//////');

          e.preventDefault();
          if (typeof binding.value === 'function') {
            binding.value(e);
          }
        }
      }
    });
  },
};
enum Role {
  USER = 'user',
  ASSISTANT = 'assistant',
}
type Msg = {
  role: Role;
  content: string;
};
const ComBotMeetingConversation = defineComponent({
  name: 'ComBotMeetingConversation',
  components: { ComBotChatInput },
  props: {
    store: { type: Object, required: true },
  },
  setup(props) {
    const AI_CHAT_AVATAR = 'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iest/ai.png';
    const loading = ref(false);
    const messages = ref<Msg[]>([]);

    const inputValue = ref('');
    const scrollEl = ref<any>(null);

    function markdownRender(text: string) {
      //TODO
      const md = markdownit('commonmark');
      return md.render(text);
    }

    const onHandleConfirm = async () => {
      if (loading.value) {
        message.warning('点击过快');
        return;
      }
      loading.value = true;
      const text = inputValue.value;

      messages.value.push({
        role: Role.USER,
        content: text,
      });
      inputValue.value = '';

      messages.value.push({
        role: Role.ASSISTANT,
        content: '正在思考中...',
      });

      try {
        const res = await props.store.sendMemberAction({
          action: 'ask',
          id: props.store.record?.value?.id,
          config: {
            data: {
              question: text,
            },
          },
        });

        const response_msg = res.data?.answer
          ? { role: Role.ASSISTANT, content: res.data?.answer }
          : {
              role: Role.ASSISTANT,
              content: '无法回答您的问题',
            };
        messages.value.pop();
        messages.value.push(response_msg);
        await nextTick();
        scrollEl.value.scrollIntoView({
          behavior: 'smooth',
          block: 'center',
        });
        loading.value = false;
      } catch (err) {
        console.log(err);
        message.error('发送失败');
        messages.value.pop();
        loading.value = false;
      }
    };

    return {
      ...toRefs(props),
      messages,
      inputValue,
      AI_CHAT_AVATAR,
      onHandleConfirm,
      Role,
      markdownRender,
      scrollEl,
      loading,
      enterHandler,
    };
  },
});
export default ComBotMeetingConversation;
</script>

<template lang="pug">
.com-bot-meeting-conversation.h-full.flex.flex-col
  .space-y-4.flex-grow.h-0.overflow-y-auto.pb-4
    .message__card.w-full.text-base(v-for='message in messages')
      .flex.items-stretch(:class='{ "user-message": message.role === Role.USER }')
        .avatar.mr-2(v-if='message.role === Role.ASSISTANT')
          a-avatar(:src='AI_CHAT_AVATAR')
        .dots__wrapper.flex.flex-col.justify-center.mr-2.cursor-pointer(v-else)
          .dots.space-y-1(style='width: min-content')

        .content__wrapper.bg-white.p-4.space-y-4.rounded-2xl
          .content__text.text-sm.text-gray-900(v-html='markdownRender(message.content)')
    .scrollbar(ref='scrollEl')
  a-spin(:spinning='loading')
    .input__wrapper.flex.items-center.w-full.flex-shrink-0
      .px-4.py-3.bg-gray-50.rounded-lg.border-1.border-gray-300.flex-grow.w-0.mr-2
        input.w-full(v-model='inputValue', placeholder='请输入消息', v-enter-handler='onHandleConfirm')

      .w-42px.h-42px.grid.cursor-pointer(@click.stop='onHandleConfirm')
        svg.place-self-center(
          xmlns='http://www.w3.org/2000/svg',
          width='20',
          height='20',
          viewBox='0 0 20 20',
          fill='none'
        )
          g(clip-path='url(#clip0_259_19748)')
            path(
              d='M1.40598 19.0787L19.4057 10.9307C19.5826 10.8507 19.7329 10.7202 19.8384 10.5549C19.9439 10.3897 20 10.1968 20 9.99974C20 9.80264 19.9439 9.60978 19.8384 9.44454C19.7329 9.2793 19.5826 9.14879 19.4057 9.06882L1.40598 0.920732C1.2347 0.843228 1.04549 0.81631 0.859913 0.84305C0.674337 0.86979 0.499876 0.94911 0.356424 1.07196C0.212975 1.19482 0.106308 1.35626 0.0485935 1.53788C-0.00912094 1.7195 -0.0155621 1.91398 0.0300064 2.09915L1.71898 8.98123L10.9998 8.98123C11.2651 8.98123 11.5194 9.08854 11.7069 9.27954C11.8945 9.47055 11.9998 9.72961 11.9998 9.99974C11.9998 10.2699 11.8945 10.5289 11.7069 10.7199C11.5194 10.9109 11.2651 11.0183 10.9998 11.0183L1.71898 11.0183L0.0300064 17.9013C-0.0153217 18.0864 -0.00869942 18.2808 0.049118 18.4622C0.106936 18.6437 0.213625 18.805 0.357031 18.9277C0.500439 19.0504 0.674799 19.1296 0.860252 19.1563C1.0457 19.183 1.2348 19.1561 1.40598 19.0787Z',
              fill='#1A56DB'
            )
          defs
            clipPath#clip0_259_19748
              rect(width='20', height='20', fill='white', transform='matrix(0 1 -1 0 20 0)')
</template>

<style lang="stylus" scoped>
.com-bot-meeting-conversation .content__wrapper {
  border-top-left-radius: 0;
}

.com-bot-meeting-conversation .user-message {
  justify-content: flex-end;
}

.com-bot-meeting-conversation .user-message .content__wrapper {
  border-top-right-radius: 0;
  border-top-left-radius: 8px;
}

.com-bot-meeting-conversation .input__wrapper input {
  @apply: bg-gray-50;

  &:focus, &:focus-visible {
    outline: none;
  }
}

.com-bot-meeting-conversation .content__text:deep(ol), .com-bot-meeting-conversation .content__text:deep(ul) {
  padding-left: 1em;
}

.com-bot-meeting-conversation .content__text:deep(p) {
  margin-bottom: 0;
}

.com-bot-meeting-conversation .content__text:deep(img) {
  object-fit: cover;
  object-position: center;
}
</style>
