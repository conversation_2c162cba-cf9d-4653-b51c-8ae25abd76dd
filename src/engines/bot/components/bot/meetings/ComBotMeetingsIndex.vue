<script lang="ts">
import { defineComponent, computed, ref, Ref, toRefs, reactive } from 'vue';
import { TaIndexViewTabInterface } from '@/components/global/ta-component/TaIndexView/types';
import { VObject } from '@/lib/vails/model';
import { BotMeetingState, BotMeetingStateMapping } from '@/engines/bot/models/bot/user/meetings';
import { useRouter, useRoute } from 'vue-router';
import { message } from 'ant-design-vue';
import ComBotMeetingsCard from './ComBotMeetingsCard.vue';

const ComBotMeetingsIndex = defineComponent({
  name: 'ComBotMeetingsIndex',
  components: {
    ComBotMeetingsCard,
  },
  props: {
    store: { type: Object, required: true },
    recordName: { type: String, default: '会议记录' },
  },
  setup(props, { emit }) {
    const taindexview = ref<any>(null);
    const config = computed(() => ({
      recordName: props.recordName,
      store: props.store,
      pagination: {
        perPage: 15,
        showPageSizeChanger: false,
        hideOnSinglePage: true,
        showSizeChanger: false,
      },
      template: 'bot_meeting',
      mode: 'list',
      actions: [
        { key: 'create', enabled: false },
        { key: 'update', enabled: true },
        { key: 'delete', enabled: true },
        { key: 'import', enabled: true },
        { key: 'export', enabled: true },
      ],
      list: {
        scroll: { y: 'auto' },
        splitCount: 3,
        gap: 16,
      },
      searcherSimpleOptions: [{ key: 'name', label: '会议名称', type: 'string' }],
    }));


    const router = useRouter()
    const route = useRoute()
    const onShow = (record: VObject) => {
      if (record.state === BotMeetingState.FINISHED || record?.file?.contents?.length > 0) {
        router.push(`${route.path}/${record.id}`)
        return;
      }

      message.warning('会议总结中，暂时无法查看会议记录');
    }

    const onIndex = (data: VObject) => {
      const hasCreateCard = !!props.store.records.value?.find((record: VObject) => record.id === 0)
      if (data.current_page === 1 && !hasCreateCard) {
        props.store.records.value.unshift({
          id: 0,
          name: '新建会议'
        })
      }
    };

    const onCreate = (record: VObject) => {
      taindexview.value?.slotActions?.onCreate(record)
    }

    const onEdit = (record: VObject) => {
      taindexview.value?.slotActions?.onEdit(record)
    }

    const onDelete = (record: VObject) => {
      taindexview.value?.slotActions?.onDelete(record)
    }

    const regenerateModalVisible = ref(false)
    const confirmLoading = ref(false)
    const cacheRegeneratePrompt = ref('')
    const cacheRegenerateRecord = ref<any>({})

    const onRegenerate = async (record: VObject) => {
      regenerateModalVisible.value = true
      cacheRegenerateRecord.value = record
    }

    const regenerateMeeting = async () => {
      confirmLoading.value = true
      await props.store.sendMemberAction({
        action: 'generate_topic_and_summary',
        id: cacheRegenerateRecord.value.id,
        config: {
          data: {
            payload: {
              ...cacheRegenerateRecord.value.payload,
              prompt: cacheRegeneratePrompt.value
            }
          }
        }

      })

      message.success('重新生成中...')
      cacheRegeneratePrompt.value = ''
      regenerateModalVisible.value = false
      confirmLoading.value = false
    }

    const onCancel = () => {
      regenerateModalVisible.value = false
      cacheRegeneratePrompt.value = ''
      cacheRegenerateRecord.value = {}
    }
    return {
      ...toRefs(props),
      config,
      BotMeetingState,
      BotMeetingStateMapping,
      onShow,
      onIndex,
      onCreate,
      onEdit,
      onDelete,
      taindexview,
      onRegenerate,
      regenerateModalVisible,
      confirmLoading,
      cacheRegeneratePrompt,
      regenerateMeeting,
      onCancel
    };
  },
});

export default ComBotMeetingsIndex;
</script>

<template lang="pug">
.com-bot-manage-meetings-index
  a-modal(
    v-model:visible='regenerateModalVisible'
    title='重新生成会议纪要'
    @ok='regenerateMeeting'
    @cancel='onCancel',
    :confirm-loading="confirmLoading"
  )
    .px-2
      .text-sm.text-gray-900.font-bold.mb-2 提示词
      a-textarea(v-model:value='cacheRegeneratePrompt')
  TaIndexView(
    ref='taindexview'
    :config='config'
    :tabs='tabs'
    @onIndex='onIndex'
    @onShow='onShow'
  )
    template(#card='{ record }')
      template(v-if='record.id')
        ComBotMeetingsCard(
          :record='record'
          @edit='onEdit(record)'
          @delete='onDelete(record)'
          @regenerate='onRegenerate(record)'
        )
      template(v-else)
        .gradient-border.relative.rounded-2xl.bg-white.flex.flex-col.items-center.justify-center.cursor-pointer.h-142px(
          @click.stop='onCreate(record)'
        )
          .w-5.h-5.rounded-full.bg-purple-500.grid.mb-2
            TaIcon.text-white.place-self-center(type='solid/plus' class='!w-3 !h-3')
          .text-base.font-medium.text-purple-500 新建会议
    template(#bodyCell='{ text, record, index, column }')
      .state(v-if='column.dataIndex[0] === "state"')
        a-tag(
          :color='BotMeetingStateMapping[record.state]?.color'
          ) {{ BotMeetingStateMapping[record.state]?.label }}

</template>

<style lang="stylus" scoped>
.com-bot-manage-meetings-index
  height 100%
  width 100%
  .ta-index-view:deep(.draggable)
    grid-template-columns repeat(auto-fill, minmax(240px, 1fr)) !important
  .ta-index-view:deep(.ta-index-view-header)
    margin-bottom 2rem


@property --border-width {
  syntax: '<length>';
  inherits: false;
  initial-value: 3px;
}

@property --border-radius {
  syntax: '<length>';
  inherits: false;
  initial-value: 16px;
}

.gradient-border::before
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: url(https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/ai_editor/%E8%8A%B1%E7%93%A3%E7%B4%A0%E6%9D%90_%E7%B2%89%E8%93%9D%E6%B8%90%E5%8F%98%E5%BC%A5%E6%95%A3%E8%83%8C%E6%99%AF%E5%9B%BE%E7%89%87_193465581.png) /* 渐变背景 */
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  padding: var(--border-width);
  box-sizing: border-box;
  border-radius: var(--border-radius);
  background-size:cover;
  background-position: center center;

</style>
