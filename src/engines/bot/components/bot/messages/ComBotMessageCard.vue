<script lang="ts">
import { VObject } from '@/lib/vails';
import { defineComponent, toRefs, computed } from 'vue';
import { BotMessageContentType, BotRoleType } from '../../../models/bot/user/messages';
import ComBotArtifactsCard from '../artifacts/ComBotArtifactsCard.vue';
import useMessage from '../../../utils/hooks/useMessage';
import { BotMentionTypeMapping } from '@/components/botConfig';

const ComBotMessageCard = defineComponent({
  name: 'ComBotMessageCard',
  components: { ComBotArtifactsCard },
  props: {
    record: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props, { emit }) {
    const AI_CHAT_AVATAR = 'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iest/ai.png';

    const content = computed(() => {
      return props.record?.meta?.messages;
    });

    const { markdownRender } = useMessage();
    const onClickArtifact = (artifact: VObject, message: VObject) => {
      emit('clickArtifact', artifact, message);
    };

    // 检查工件是否有对应的前端组件
    const hasArtifactComponent = (item: VObject) => {
      try {
        const modelClass = item?.content?.tool_conf?.model_class;
        return !!(modelClass && BotMentionTypeMapping[modelClass]);
      } catch (error) {
        console.warn('检查工件组件时出错:', error);
        return true; // 出错时默认显示，避免影响正常功能
      }
    };

    return {
      ...toRefs(props),
      AI_CHAT_AVATAR,
      content,
      BotRoleType,
      BotMessageContentType,
      markdownRender,
      onClickArtifact,
      hasArtifactComponent,
    };
  },
});
export default ComBotMessageCard;
</script>

<template lang="pug">
.com-bot-message-card.w-full.text-base
  .flex.items-stretch(:class='{ "user-message": record.role === BotRoleType.User }')
    .avatar.mr-2(v-if='record.role === BotRoleType.Assistant')
      a-avatar(:src='AI_CHAT_AVATAR')

    .dots__wrapper.flex.flex-col.justify-center.mr-2.cursor-pointer(v-else)
      .dots.space-y-1(style='width: min-content')

    .content__wrapper.bg-white.p-4.space-y-4.rounded-2xl
      template(v-for='item in content')
        .content__text.text-sm.text-gray-900(
          v-if='item.content_type === BotMessageContentType.Text',
          v-html='markdownRender(item.content)'
        )
        TaAttachments(
          v-else-if='item.content_type === BotMessageContentType.File',
          :attachments='[item.content]'
        )
        ComBotArtifactsCard(
          v-else-if='item.content_type === BotMessageContentType.Artifact && hasArtifactComponent(item)',
          :record='item',
          @click.stop='onClickArtifact(item, record)'
        )
</template>

<style lang="stylus" scoped>
.com-bot-message-card .content__wrapper
  border-top-left-radius 0
.com-bot-message-card .user-message
  justify-content flex-end
.com-bot-message-card .user-message .content__wrapper
  border-top-right-radius 0
  border-top-left-radius 8px
.com-bot-message-card :deep(.attachment-wrapper .attachment img + div)
  width max-content
.com-bot-message-card .content__text:deep(ol), .com-bot-message-card .content__text:deep(ul)
  padding-left 1em
.com-bot-message-card .content__text:deep(p)
  margin-bottom 0
.com-bot-message-card .content__text:deep(img)
  object-fit cover
  object-position center
</style>
