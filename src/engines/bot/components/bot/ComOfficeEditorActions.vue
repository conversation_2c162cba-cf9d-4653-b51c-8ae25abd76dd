<script lang='ts'>
import { ref, defineComponent, toRefs, PropType, nextTick } from 'vue';
import { Editor } from '@tiptap/vue-3';
import { useTipTapEditor, EditorMarkAction } from './useTipTapEditor';
const ComOfficeEditorActions = defineComponent({
  name: 'ComOfficeEditorActions',
  components: {},
  props: {
    editor: { type: Object as PropType<Editor>, required: true },
  },
  setup(props) {
    const { reactiveEditor, dispatchEditorMark, dispatchCommand, toggleTextAlign } = useTipTapEditor(props.editor);

    return {
      ...toRefs(props),
      reactiveEditor,
      dispatchEditorMark,
      EditorMarkAction,
      dispatchCommand,
      toggleTextAlign,
    };
  },
});
export default ComOfficeEditorActions;
</script>

<template lang="pug">
.com-office-editor-actions.px-4.py-2.rounded.shadow.bg-white.border-1.border-gray-100
  .flex.items-center.justify-center
    //- undo redo
    button.px-2.pb-1.rounded(
      @click.stop='dispatchCommand("undo",true)().run()'
      :class='{ "hover:bg-gray-100":true }'
    )
      TaIcon(type='UndoOutlined' size='14')
    button.px-2.pb-1.rounded(
      @click.stop='dispatchCommand("redo",true)().run()'
      :class='{ "hover:bg-gray-100":true }'
    )
      TaIcon(type='RedoOutlined' size='14')

    //- 基本操作
    button.px-2.pt-6px.rounded(:class='{ "hover:bg-gray-100":true }')
      input.w-14px.h-14px(
        type='color',
        style='padding:0'
        :value='reactiveEditor.getAttributes("textStyle").color',
        @input='(e)=>dispatchCommand("setColor",true)(e.target.value).run()',
      )
    button.px-2.pb-1.rounded(
      @click.stop='dispatchEditorMark(EditorMarkAction.Bold,true)'
      :class='{ "is-active": reactiveEditor.isActive("bold"),"hover:bg-gray-100":true }'
    )
      TaIcon(type='BoldOutlined' size='14')
    button.px-2.pb-1.rounded(
      @click.stop='dispatchEditorMark(EditorMarkAction.Italic,true)'
      :class='{ "is-active": reactiveEditor.isActive("italic"),"hover:bg-gray-100":true }'
    )
      TaIcon(type='ItalicOutlined' size='14')

    button.px-2.pb-1.rounded(
      @click.stop='dispatchEditorMark(EditorMarkAction.Strike,true)'
      :class='{ "is-active": reactiveEditor.isActive("strike"),"hover:bg-gray-100":true }'
    )
      TaIcon(type='StrikethroughOutlined' size='14')

    //- 字体布局
    button.px-2.pb-1.rounded(
      @click.stop='toggleTextAlign("left")'
      :class='{ "is-active": reactiveEditor.isActive({ textAlign: "left" }),"hover:bg-gray-100":true }'
    )
      TaIcon(type='AlignLeftOutlined' size='14')

    button.px-2.pb-1.rounded(
      @click.stop='toggleTextAlign("center")'
      :class='{ "is-active": reactiveEditor.isActive({ textAlign: "center" }),"hover:bg-gray-100":true }'
    )
      TaIcon(type='AlignCenterOutlined' size='14')
    button.px-2.pb-1.rounded(
      @click.stop='toggleTextAlign("right")'
      :class='{ "is-active": reactiveEditor.isActive({ textAlign: "right" }),"hover:bg-gray-100":true }'
    )
      TaIcon(type='AlignRightOutlined' size='14')

    a-dropdown
      button.px-2.pt-2px.pb-1px.rounded(
        @click.stop=''
        :class='{ "is-active": reactiveEditor.isActive("strike"),"hover:bg-gray-100":true }'
      )
        .text-base.pt-1 Hx
      template(#overlay)
        a-menu
          a-menu-item(
            v-for='(item) in [1,2,3,4,5,6]',
            :key='item'
            @click.stop='dispatchCommand("toggleHeading",true)({level:item}).run()'
          ) H{{ item }} {{item}}级标题

    button.px-2.pb-1.rounded(
      @click.stop='dispatchCommand("toggleBulletList",true)().run()'
      :class='{ "is-active": reactiveEditor.isActive("bulletList"),"hover:bg-gray-100":true }'
    )
      TaIcon(type='UnorderedListOutlined' size='14')
    button.px-2.pb-1.rounded(
      @click.stop='dispatchCommand("toggleOrderedList",true)().run()'
      :class='{ "is-active": reactiveEditor.isActive("orderedList"),"hover:bg-gray-100":true }'
    )
      TaIcon(type='OrderedListOutlined' size='14')
    //- 高亮
    button.px-2.pb-1.rounded(
      @click.stop='dispatchCommand("toggleHighlight",true)({color:"#EDEBFE"}).run()'
      :class='{ "is-active": reactiveEditor.isActive("highlight"),"hover:bg-gray-100":true }'
    )
      TaIcon(type='HighlightOutlined' size='14')

    button.px-2.pb-1.rounded(
      @click.stop='dispatchCommand("toggleTaskList",true)().run()'
      :class='{ "is-active": reactiveEditor.isActive("taskList"),"hover:bg-gray-100":true }'
    )
      TaIcon(type='DiffOutlined' size='14')

</template>

<style lang="stylus" scoped>
.com-office-editor-actions .is-active
  @apply bg-gray-200;
.com-office-editor-actions button input::-webkit-color-swatch-wrapper
  padding 0
  border 0
.com-office-editor-actions button input::-webkit-color-swatch
  border 0
  border-radius 4px
</style>
