<script lang='ts'>
import { ref, defineComponent, toRefs, computed } from 'vue';
const TaOfficeOnlineEditorModal = defineComponent({
  name: 'TaOfficeOnlineEditorModal',
  components: {},
  props: {
    visible: { type: Boolean, default: false },
    fileItem: { type: Object, default: () => ({}) },
    wrapComponent: { type: String, default: 'a-modal' },
    type: { type: String, default: 'desktop' },
  },
  setup(props, { emit }) {
    const localVisible = computed({
      get: () => props.visible,
      set: val => emit('update:visible', val),
    });
    const editor = ref<any>(null);
    return {
      ...toRefs(props),
      localVisible,
      editor,
    };
  },
});
export default TaOfficeOnlineEditorModal;
</script>

<template lang="pug">
TaOfficeOnlineEditorModel.ta-office-online-editor-modal(
  ref='editor',
  v-bind='$props',
  v-model:visible='localVisible'
)
</template>

<style lang="stylus" scoped></style>
