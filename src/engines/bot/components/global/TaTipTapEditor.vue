<script lang='ts'>
import { defineComponent, toRefs, PropType, onUnmounted } from 'vue';
import { EditorContent, Editor } from '@tiptap/vue-3';
import { useTipTapEditor } from '../bot/useTipTapEditor';

const TaTipTapEditor = defineComponent({
  name: 'TaTipTapEditor',
  components: { EditorContent },
  props: {
    editor: { type: Object as PropType<Editor>, default: undefined },
  },
  setup(props) {
    const { reactiveEditor } = useTipTapEditor(props.editor);

    onUnmounted(() => {
      reactiveEditor.value?.destroy();
    })

    return {
      ...toRefs(props),
      reactiveEditor,
    };
  },
});
export default TaTipTapEditor;
</script>

<template lang="pug">
.ta-tip-tap-editor
  EditorContent(:editor="reactiveEditor")
</template>

<style lang="stylus" scoped>
--purple = #EDEBFE

.ta-tip-tap-editor :deep(.tiptap)
  p
    margin 1em 0
    text-indent 0em
  &:first-child
    margin-top 0

  // headings
  h1,
  h2,
  h3,
  h4,
  h5,
  h6
    line-height 1.1
    margin-top 2.5rem
    text-wrap pretty

  h1
    font-size 1.5rem
  h2
    font-size 1.3rem
  h3
    font-size 1.1rem

  h4,
  h5,
  h6
    font-size 1rem

  // list
  ul,
  ol
    padding 0 1rem
    margin 1.25rem 1rem 1.25rem 0.4rem
    li p
      margin-top 0.25em
      margin-bottom 0.25em
      text-indent 0em

  ul li
    list-style-type disc !important
  ol li
    list-style-type decimal !important

  //task list
  ul[data-type="taskList"]
    list-style-type none
    margin-left 0
    padding 0
    li
      list-style-type none !important
      align-items start
      display flex
      margin 0.25rem 0
      > label
        flex 0 0 auto
        margin-right 0.5rem
        margin-top 0.25rem //和ul li p 保持统一
        margin-bottom 0.25rem
        user-select none
      > div
        flex 1 1 auto

    input[type="checkbox"]
      cursor pointer

    ul[data-type="taskList"]
      margin 0


  //code
  pre
    background #2b2c2e
    border-radius 0.5rem
    color #e5e7eb
    font-family 'JetBrainsMono', monospace
    margin 1.5rem 0
    padding 0.75rem 1rem
    overflow-x auto
    white-space pre !important

    code
      background none
      color inherit
      font-size 0.8rem
      padding 0

    .hljs-comment,
    .hljs-quote
      color: #616161;

    .hljs-variable,
    .hljs-template-variable,
    .hljs-attribute,
    .hljs-tag,
    .hljs-name,
    .hljs-regexp,
    .hljs-link,
    .hljs-name,
    .hljs-selector-id,
    .hljs-selector-class
      color: #f98181;

    .hljs-number,
    .hljs-meta,
    .hljs-built_in,
    .hljs-builtin-name,
    .hljs-literal,
    .hljs-type,
    .hljs-params
      color: #fbbc88;

    .hljs-string,
    .hljs-symbol,
    .hljs-bullet
      color: #b9f18d;

    .hljs-title,
    .hljs-section
      color: #faf594;

    .hljs-keyword,
    .hljs-selector-tag
      color: #70cff8;

    .hljs-emphasis
      font-style: italic;

    .hljs-strong
      font-weight: 700;


  //image
  img
    display block
    height auto
    margin 1.5rem 0
    max-width 100%

    &:ProseMirror-selectednode
      outline: 3px solid --purple

  blockquote
    border-left 3px solid --purple
    margin 1.5rem 0
    padding-left: 1rem

</style>
