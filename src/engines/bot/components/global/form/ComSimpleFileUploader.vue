<script lang='ts'>
import { ref, defineComponent, toRefs, computed, onMounted } from 'vue';
import { IFile } from '../../../../../components/global/ta-component/file/servers/types';
const ComSimpleFileUploader = defineComponent({
  name: 'ComSimpleFileUploader',
  components: {},
  props: {
    value: { type: Object, default: () => ({}) },
  },
  setup(props, { emit }) {
    const fileItems = ref<IFile[]>([]);
    const localValue = computed({
      get: () => props.value,
      set: (val: any) => {
        emit('update:value', val);
      },
    });

    onMounted(() => {
      if (props.value?.url && fileItems.value.length === 0) {
        fileItems.value.push(props.value as any)
      }
    })

    const onAllSettled = () => {
      localValue.value = fileItems.value[0];
    }
    return {
      ...toRefs(props),
      localValue,
      fileItems,
      onAllSettled,
    };
  },
});
export default ComSimpleFileUploader;
</script>

<template lang="pug">
TaFileUploader(
  v-model:value='fileItems',
  :multiple='false',
  :options='{fileSize:100}'
  @allSettle='onAllSettled'
 )
</template>

<style lang="stylus" scoped></style>
