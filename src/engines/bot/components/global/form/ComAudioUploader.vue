<script lang="ts">
import { computed, defineComponent, onMounted, ref, toRefs } from 'vue';
import { IFile } from '../../../../../components/global/ta-component/file/servers/types';
const ComAudioUploader = defineComponent({
  name: 'ComAudioUploader',
  components: {},
  props: {
    value: { type: Object, default: () => ({}) },
    fileSize: { type: Number, default: 100 }, // 新增 fileSize 属性
  },
  setup(props, { emit }) {
    const fileItems = ref<IFile[]>([]);
    const localValue = computed({
      get: () => props.value,
      set: (val: any) => {
        emit('update:value', val);
      },
    });

    onMounted(() => {
      if (props.value?.url && fileItems.value.length === 0) {
        fileItems.value.push(props.value as any);
      }
    });

    const onAllSettled = () => {
      localValue.value = fileItems.value[0];
    };
    return {
      ...toRefs(props),
      fileItems,
      localValue,
      onAllSettled,
    };
  },
});
export default ComAudioUploader;
</script>

<template lang="pug">
.com-audio-uploader
  TaFileUploader(
    v-model:value='fileItems',
    :multiple='false',
    :options='{fileSize: fileSize}'
    accept='audio/*'
    @allSettle='onAllSettled'
  )
</template>

<style lang="stylus" scoped></style>
