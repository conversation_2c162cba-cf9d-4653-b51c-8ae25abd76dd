<script lang='ts'>
import { ref, defineComponent, toRefs, computed } from 'vue';
const ComBotChatInput = defineComponent({
  name: 'ComBotChatInput',
  components: {},
  props: {
    value: { type: String, default: '' },
    loading: { type: Boolean, default: false },
    disabled: { type: Boolean, default: false },
    minRows: { type: Number, default: 1 },
    maxRows: { type: Number, default: 4 },
    placeholder: { type: String, default: '请输入内容' },
    isListening: { type: Boolean, default: false },
    noSpeech: { type: Boolean, default: false },
  },
  emits: ['update:value', 'file', 'confirm', 'update:isListening'],
  setup(props, { emit }) {
    const localValue = computed({
      get: () => props.value,
      set: (value) => {
        emit('update:value', value);
      },
    })
    const localListening = computed({
      get: () => props.isListening,
      set: (value) => {
        emit('update:isListening', value);
      },
    })

    const openFileUploader = () => {
      emit('file')
    }
    const onConfirm = () => {
      const text = String(localValue.value)
      emit('confirm', text)
      localValue.value = ''
    }
    return {
      ...toRefs(props),
      localValue,
      openFileUploader,
      onConfirm,
      localListening
    };
  },
});
export default ComBotChatInput;
</script>

<template lang="pug">
.com-bot-chat-input.w-full.pl-4.py-3.pr-3.rounded-xl.flex.items-end.bg-white
  .chat-input__prefix(style='align-self:start')
    slot(name='prefix')
      .w-8.h-8.rounded-md.cursor-pointer.grid.mb-1(
        class='hover:bg-black/5'
        @click.stop='openFileUploader'
      )
        TaIcon.place-self-center(type='LinkOutlined', size='1rem')

  a-textarea(
    size='large'
    v-model:value='localValue'
    style='align-self:center'
    :bordered='false'
    :disabled='disabled'
    :auto-size="{ minRows, maxRows }"
    :placeholder='placeholder'
    @keyup.enter='onConfirm'
  )

  .chat-input__suffix
    slot(name='suffix')
      .flex.items-center.mb-1
        .speech.w-8.h-8.rounded-md.cursor-pointer.grid(
          v-if='!noSpeech'
          :class='{"listening": localListening}',
          @click.stop='localListening = !localListening'
        )
          TaIcon.place-self-center(type='AudioOutlined' size='1rem' color='#4096ff')

        .sender.w-8.h-8.rounded-md.cursor-pointer.grid(
          @click.stop='onConfirm'
        )
          TaIcon.place-self-center(type='SendOutlined' size='1rem' color='#4096ff')
</template>

<style lang="stylus" scoped>
.com-bot-chat-input
  border 0px solid #d9d9d9
  box-shadow 0 1px 2px 0 rgba(0, 0, 0, 0.06), 0 1px 6px -1px rgba(0, 0, 0, 0.04), 0 2px 4px 0 rgba(0, 0, 0, 0.04)
  .chat-input__suffix .speech:hover , .chat-input__suffix .sender:hover
    @apply bg-blue-50;
  .listening
    @apply bg-blue-100;
</style>
