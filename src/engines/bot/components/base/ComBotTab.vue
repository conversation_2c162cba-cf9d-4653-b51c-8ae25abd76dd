<script lang='ts'>
import { ref, defineComponent, toRefs, PropType, computed } from 'vue';
interface ITab {
  label: string,
  key: string,
  icon?: string
}
const ComBotTab = defineComponent({
  name: 'ComBotTab',
  components: {},
  props: {
    tabs: { type: Array as PropType<ITab[]>, default: () => [] },
    activeTab: { type: Object as PropType<ITab>, default: () => ({}) },
  },
  setup(props, { emit }) {
    const localActiveTab = computed({
      get: () => props.activeTab,
      set: (val) => {
        emit('update:activeTab', val);
      },
    });
    const changeActiveTab = (tab: ITab) => {
      localActiveTab.value = tab
    };
    return {
      ...toRefs(props),
      localActiveTab,
      changeActiveTab,
    };
  },
});
export default ComBotTab;
</script>

<template lang="pug">
.com-bot-tabs.p-1.rounded-lg.grid(
  :style='`grid-template-columns: repeat(${tabs.length}, minmax(0, 1fr)); gap: 10px;`'
)
  .bot__tab.rounded-lg.py-2.px-3.flex.items-center.justify-center.cursor-pointer(
    v-for='tab in tabs',
    :class='{ "bot__active-tab": tab.key === localActiveTab?.key }',
    @click.stop='changeActiveTab(tab)'
  )
    img.w-3.h-3.block.object-cover.mr-2(:src='tab.icon' v-if='tab.icon && tab.icon.startsWith("http")')
    TaIcon.w-3.h-3.mr-2.ta__icon.text-purple-500(:type='tab.icon' v-else-if='tab.icon')
    .bot__tab-label.text-xs.text-gray-900.font-medium.leading-normal {{ tab.label }}
</template>

<style lang="stylus" scoped>
.com-bot-tabs
  .bot__active-tab
    @apply bg-purple-500;
    .bot__tab-label
      @apply text-white;
    .ta__icon
      @apply text-white;
</style>
