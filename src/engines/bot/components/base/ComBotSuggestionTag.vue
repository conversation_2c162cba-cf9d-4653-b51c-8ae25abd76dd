<script lang='ts'>
import { ref, defineComponent, toRefs } from 'vue';
const ComBotSuggestionTag = defineComponent({
  name: 'ComBotSuggestionTag',
  components: {},
  props: {
    suggestion: {
      type: Object,
      default: () => ({})
    }
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
export default ComBotSuggestionTag;
</script>

<template lang="pug">
.com-bot-suggestion-tag.flex.items-center
  .rounded-lg.w-6.h-6.bg-yellow-100.flex.items-center.justify-center.mr-1(
    class='dark:bg-yellow-900'
  )
    TaIcon.text-yellow-800(
      type='flowbite-v2-solid/fire' class='!w-3 !h-3 dark:text-yellow-400'
    )
  .text-sm.text-gray-500(
    class='hover:text-blue-500 dark:text-white'
  ) {{ suggestion.name }}
</template>

<style lang="stylus" scoped></style>
