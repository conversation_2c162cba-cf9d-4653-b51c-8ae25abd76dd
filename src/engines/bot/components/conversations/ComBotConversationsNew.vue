<script lang='ts'>
import { ref, defineComponent, toRefs, inject, Ref } from 'vue';
import ComBotChatInput from '../base/ComBotChatInput.vue';
import { VObject } from '@/lib/vails';
import ChatEventCenter from '../../utils/ChatEventCenter';
import { BotStateKey } from './ComBotConversationLayout.vue';
import { botSuggestionsFn } from '../../../../components/botConfig';
import ComBotSuggestionTag from '../base/ComBotSuggestionTag.vue';
import useAudio from '../../utils/hooks/useAudio';
import useMessage from '../../utils/hooks/useMessage';

const ComBotConversationNew = defineComponent({
  name: 'ComBotConversationNew',
  components: { ComBotChatInput, ComBotSuggestionTag },
  props: {
    store: {
      type: Object,
      required: true
    },
    aiLogoUrl: {
      type: String,
      default: ''
    }
  },
  setup(props) {
    const AI_LOGO_URL = props.aiLogoUrl || process.env.VUE_APP_AI_LOGO_URL || 'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iest/aibg.webp';

    const text = ref('');
    const textCacheMap = new Map() // 用于缓存文本

    const loading = ref(false)
    const fileUploader = ref<any>(null);
    const fileAllSettled = ref(true)
    const files = ref<VObject[]>([]);
    const state = inject<Ref<VObject>>(BotStateKey)!

    const suggestions = botSuggestionsFn('new')

    // useAudio(String(process.env.VUE_APP_AUDIO_STREAM_BASE_URL))
    const { fmtMessage } = useMessage()

    const onOpenFileUploader = () => {
      fileUploader.value?.openFile();
    }

    const onConfirm = async (value: string) => {
      if (!value) {
        ChatEventCenter.emit('showMessage', { type: 'warning', msg: '请输入内容' })
        return
      }

      if (!fileAllSettled.value) {
        ChatEventCenter.emit('showMessage', { type: 'warning', msg: '文件上传中，请稍后' })
        return
      }

      try {
        if (loading.value) return
        loading.value = true
        const record = props.store.new({
          agent_id: 1
        })
        await record.save()

        state.value.newMessage = fmtMessage(value, ...files.value)

        text.value = ''
        files.value = []

        ChatEventCenter.emit('to', `${state?.value?.routeBase}${record.id}`)
      } catch (error) {
        console.log(error, 'err')
        ChatEventCenter.emit('showMessage', { type: 'error', msg: '创建会话失败' })
      } finally {
        loading.value = false
      }
    }
    return {
      ...toRefs(props),
      text,
      files,
      fileUploader,
      fileAllSettled,
      onOpenFileUploader,
      state,
      onConfirm,
      suggestions,
      AI_LOGO_URL,
      title: process.env.VUE_APP_AI_TITLE || '智能助手',
    };
  },
});
export default ComBotConversationNew;
</script>

<template lang="pug">
.com-bot-conversation-new.h-full.w-full
  .grid.h-full
    .w-full.max-w-3xl.place-self-center.px-4
      .w-72.h-72.relative.mb-20.mx-auto
        img.logo.w-full.h-full.object-cover(:src='AI_LOGO_URL')
        .absolute.text-2xl.font-semibold.text-black.bottom-0.left-0.right-0.mx-auto.underline.underline-offset-16.decoration-blue-500.whitespace-nowrap.w-full.text-center(
          class='dark:text-white'
        ) {{ title }}

      ComBotChatInput(
        v-model:value='text',
        v-model:isListening='state.isListening',
        minRows='3'
        @confirm='onConfirm'
        @file='onOpenFileUploader'
      )

      TaFileUploader.mb-4(
        ref='fileUploader',
        v-model:isAllSettled='fileAllSettled',
        v-model:value='files',
        :multiple='false',
        class='!block',
      )
        .empty

      .suggestions.w-full.flex.justify-start
        .flex.flex-wrap
          ComBotSuggestionTag.mr-2.mb-2.cursor-pointer.px-10px.py-2.bg-white.rounded-lg(
            v-for='suggestion in suggestions'
            :suggestion='suggestion'
            @click.stop='onConfirm(suggestion.name)'
          )
</template>

<style lang="stylus" scoped></style>
