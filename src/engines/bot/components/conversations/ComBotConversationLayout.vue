<script lang='ts'>
import { AuthSessionApi } from '@/engines/login/apis/auth/session.api';
import { VObject, VStore } from '@/lib/vails';
import { defineComponent, provide, ref, toRefs } from 'vue';
import ComBotConversationSide from './ComBotConversationSide.vue';
import ChatEventCenter from '../../utils/ChatEventCenter';
import { message } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import { BotUserConversationsApi } from '../../apis/bot/user/conversations.api';
import { BotConversationModel } from '../../models/bot/user/conversations';

export const BotStateKey = Symbol('BotStateKey');

const ComBotConversationLayout = defineComponent({
  name: 'ComBotConversationLayout',
  components: { ComBotConversationSide },
  props: {
    initState: {
      type: Object,
      default: () => ({
        expanded: false,
        visibleArtifactShow: false,
        visibleArtifactList: false,
        isListening: false,
        bgColor: '#f3f4f6',
        routeBase: '/bot/user/conversations/',
        newPage: '/bot/user/conversations/'
      }),
    },
  },
  setup(props) {
    const state = ref<VObject>(props.initState);

    const router = useRouter()
    const store = new VStore(new BotUserConversationsApi(), BotConversationModel);
    store.index({ per_page: 30 })

    provide(BotStateKey, state);


    //show antd message
    ChatEventCenter.on('showMessage', (payload: VObject) => {
      const { type, msg } = payload as { type: string, msg: string }
      (message as any)[type](msg)
    })

    ChatEventCenter.on('to', (url: string) => {
      router.push(url)
    })
    ChatEventCenter.on('refreshConversations', () => {
      store.index({ per_page: 30 })
    })

    return {
      ...toRefs(props),
      state,
      currentUser: AuthSessionApi.currentUser(),
      title: process.env.VUE_APP_AI_TITLE || '智能助手',
      records: store.records,
    };
  },
});
export default ComBotConversationLayout;
</script>

<template lang="pug">
.com-bot-conversation-layout.w-full.h-full.flex(:style='`background-color:${state.bgColor}`')
  ComBotConversationSide.flex-shrink-0(
    :state='state'
    :title='title'
    :currentUser='currentUser'
    :records='records'
    @toggle='state.expanded = !state.expanded'
  )
  .router-view.flex-grow.w-0
    router-view(:key='$route.fullPath')
</template>

<style lang="stylus" scoped></style>
