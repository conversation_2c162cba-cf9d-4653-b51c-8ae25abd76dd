<script lang='ts'>
import { computed, Ref, inject } from 'vue';
import { ref, defineComponent, toRefs } from 'vue';
import ChatEventCenter from '../../utils/ChatEventCenter';
import { BotStateKey } from './ComBotConversationLayout.vue';
import { VObject } from '../../../../lib/vails/model/index';
const ComBotConversationHeader = defineComponent({
  name: 'ComBotConversationHeader',
  components: {},
  props: {
    record: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props, { emit }) {
    const modalVisible = ref(false)

    const loading = ref(false);
    const state = inject<Ref<VObject>>(BotStateKey)!

    const formData = ref({ name: '' })

    const operMap = {
      edit: {
        label: '修改名称',
        icon: 'EditOutlined',
        color: 'rgba(107, 114, 128, 1)',
        action: () => {
          formData.value.name = props.record.name;
          modalVisible.value = true
        },
      },
      delete: {
        label: '删除会话',
        icon: 'DeleteOutlined',
        color: 'rgba(224, 36, 36, 1)',
        action: () => {
          props.record
            .delete()
            .then(() => {
              emit('afterDelete');
              ChatEventCenter.emit('showMessage', { type: 'success', msg: '删除成功' })
            })
            .catch(() => {
              ChatEventCenter.emit('showMessage', { type: 'error', msg: '删除失败' })
            });
        },
      },
    };

    const onSave = () => {
      if (!formData.value.name) {
        ChatEventCenter.emit('showMessage', { type: 'warning', msg: '请输入名称' })
        return;
      }
      loading.value = true

      props.record.update({ name: formData.value.name }).then(() => {
        ChatEventCenter.emit('showMessage', { type: 'success', msg: '修改成功' })
      }).catch(() => {
        ChatEventCenter.emit('showMessage', { type: 'error', msg: '修改失败' })
      }).finally(() => {
        loading.value = false
        modalVisible.value = false
      })
    }

    const toggleArtifactList = () => {
      ChatEventCenter.emit('toggleArtifactList', { bool: !state.value.visibleArtifactList })
    }
    return {
      ...toRefs(props),
      operMap,
      onSave,
      formData,
      modalVisible,
      artifactListVisible: state.value.visibleArtifactList,
      toggleArtifactList,
    };
  },
});
export default ComBotConversationHeader;
</script>

<template lang="pug">
.com-bot-conversation-header.flex.items-center.justify-between.py-2
  a-modal(
    v-model:visible='modalVisible',
    title='修改名称',
    :confirmLoading='loading',
    @ok='onSave',
  )
    .flex.items-center.justify-center
      a-input(v-model:value='formData.name', :maxLength='20', :autoFoucs='true')

  .flex.items-center.pl-4
    a-dropdown(trigger='click' placement='bottomRight')
      .title__wrapper.flex.items-center.px-2.py-1.space-x-2.rounded.cursor-pointer(
        class='hover:bg-gray-200'
        style='width:fit-content'
      )
        TaIcon(type='MessageOutlined')
        .text-sm.text-gray-700.pr-1 {{ record.name || '-' }}
        TaIcon(type='DownOutlined' :size='12')
      template(#overlay)
        a-menu
          a-menu-item(v-for='(oper,key) in operMap' :key='key')
            .flex.items-center(
              :style='`color:${oper.color}`',v-if='key !== "delete"'
              @click='oper.action'
            )
              TaIcon.mr-3(
                :type='oper.icon',
                :size='14'
              )
              .text-sm.font-normal {{oper.label}}
            TaPopoverConfirm(
              v-else
              title='确定删除吗？',
              okText='确定',
              cancelText='取消',
              @click.stop='',
              @confirm='operMap["delete"].action()'
            )
              .flex.items-center(:style='`color:${oper.color}`')
                TaIcon.mr-3(
                  :type='oper.icon',
                  :size='14'
                )
                .text-sm.font-normal {{oper.label}}

  .mention__list.flex.items-center.justify-space.px-4.justify-end
    .w-5.h-5.cursor-pointer.flex.items-center.justify-center.rounded(
      :class='artifactListVisible ? "bg-gray-300" : "hover:bg-gray-300"',
      @click.stop='()=>toggleArtifactList()'
    )
      TaIcon(type='AppstoreFilled')
</template>

<style lang="stylus" scoped></style>
