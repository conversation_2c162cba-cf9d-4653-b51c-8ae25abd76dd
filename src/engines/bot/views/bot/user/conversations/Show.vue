<script lang="ts">
import ComBotConversationsShow from '@/engines/bot/components/bot/conversations/ComBotConversationsShow.vue';
import { defineComponent, toRefs, onMounted, computed, inject, Ref, nextTick } from 'vue';
import { BotUserConversationsApi } from '@/engines/bot/apis/bot/user/conversations.api';
import { BotConversationModel } from '@/engines/bot/models/bot/user/conversations';
import { VStore } from '@/lib/vails';
import { useRoute } from 'vue-router';
import { BotUserMessagesApi } from '@/engines/bot/apis/bot/user/messages.api';
import { useCable } from '@/engines/base/channels/useCable';
import { BotUserArtifactsApi } from '../../../../apis/bot/user/artifacts.api';
import { VObject } from '../../../../../../lib/vails/model/index';
import { Bot<PERSON>tateKey } from '../../../../components/conversations/ComBotConversationLayout.vue';
import { BotRoleType } from '@/engines/bot/models/bot/user/messages';
import ChatEventCenter from '@/engines/bot/utils/ChatEventCenter';
import AudioStreamPlayer from '../../../../utils/AudioStreamPlayer';
import { useAudioPlayer } from '../../../../utils/hooks/useAudioPlayer';

const BotUserConversationsShow = defineComponent({
  name: 'BotUserConversationsShow',
  components: {
    ComBotConversationsShow,
  },
  setup(props) {
    const route = useRoute();
    const state = inject<Ref<VObject>>(BotStateKey)!;
    const conversationId = Number(route.params.conversationId);
    const { player, initPlayer, requestSpeech } = useAudioPlayer();

    const store = new VStore(new BotUserConversationsApi(), BotConversationModel);
    const messageStore = new VStore(
      new BotUserMessagesApi({
        parents: [{ type: 'conversations', id: conversationId }],
        params: { q: { s: ['id asc'] } },
      }),
    );
    const artifactStore = new VStore(
      new BotUserArtifactsApi({
        parents: [{ type: 'conversations', id: conversationId }],
        params: { q: { s: ['id asc'] } },
      }),
    );

    const loadingMessage = messageStore.new({
      meta: {
        messages: [
          {
            content_type: 'text',
            content: '正在思考中...',
          },
        ],
      },
      role: BotRoleType.Assistant,
    });

    store.extra.cable_key = 'bot_conversations';
    useCable(store, {
      callback: {
        afterUpdate: (data: any) => {
          store?.record?.value?.fetch();
          ChatEventCenter.emit('refreshConversations');
        },
      },
    });
    messageStore.extra.cable_key = 'bot_messages';
    useCable(messageStore, {
      callback: {
        afterCreate: (data: any) => {
          const conversationId = store.record?.value?.id;
          if (conversationId && conversationId !== data.conversation_id) return;

          const response = messageStore.new(data) as any;
          messageStore.records.value.push(response);

          if (data.role === BotRoleType.User) {
            messageStore.records.value.push(loadingMessage as any);
          } else {
            nextTick(async () => {
              //移除加载消息
              messageStore.records.value.splice(store.records.value.length - 2, 1);
              if (state.value.isListening) {
                const msgs = data.meta?.messages
                  ?.filter((msg: any) => msg.content_type === 'text')
                  ?.map((msg: any) => msg.content);
                await initPlayer();
                await requestSpeech(msgs.join(';'));
              }
            });
          }
        },
      },
    });

    onMounted(() => {
      store.find(Number(route.params.conversationId));
      messageStore.index({ per_page: 999999 });
    });
    return {
      ...toRefs(props),
      store,
      record: store.record,
      messageStore,
      artifactStore,
    };
  },
});

export default BotUserConversationsShow;
</script>

<template lang="pug">
.bot-user-conversations-show
  ComBotConversationsShow(
    v-if='record.id',
    :store='store',
    :messageStore='messageStore',
    :artifactStore='artifactStore'
  )
</template>

<style lang="stylus" scoped>
.bot-user-conversations-show
  height 100%
  width 100%
</style>
