<script lang="ts">
import ComBotConversationsIndex from '@/engines/bot/components/bot/conversations/ComBotConversationsIndex.vue';
import { defineComponent } from '@vue/runtime-core';
import { BotUserConversationsApi } from '@/engines/bot/apis/bot/user/conversations.api';
import { BotConversationModel } from '@/engines/bot/models/bot/user/conversations';
import { VStore } from '@/lib/vails';

import usePolicy from '@/components/global/ta-component/ta-template-form-core/usePolicy';

const BotUserConversationsIndex = defineComponent({
  name: 'BotUserConversationsIndex',
  components: {
    ComBotConversationsIndex,
  },
  setup() {
    usePolicy();

    

    
    const store = new VStore(new BotUserConversationsApi(), BotConversationModel);
    

    return {
      store,
      
    };
  },
});

export default BotUserConversationsIndex;
</script>

<template lang="pug">
.bot-user-conversations-index
  ComBotConversationsIndex(:store='store' )
</template>

<style lang="stylus" scoped>
.bot-user-conversations-index
  height 100%
  width 100%
</style>
