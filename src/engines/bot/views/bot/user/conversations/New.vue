<script lang='ts'>
import { ref, defineComponent, toRefs } from 'vue';
import ComBotConversationNew from '@/engines/bot/components/conversations/ComBotConversationsNew.vue';
import { VStore } from '@/lib/vails';
import { BotUserConversationsApi } from '@/engines/bot/apis/bot/user/conversations.api';
import { BotConversationModel } from '@/engines/bot/models/bot/user/conversations';

const BotUserConversationsNew = defineComponent({
  name: 'BotUserConversationsNew',
  components: { ComBotConversationNew },
  setup(props) {
    const store = new VStore(new BotUserConversationsApi(), BotConversationModel);
    return {
      ...toRefs(props),
      store,
    };
  },
});
export default BotUserConversationsNew;
</script>

<template lang="pug">
.bot-user-conversations-new
  ComBotConversationNew(:store='store')
</template>

<style lang="stylus" scoped>
.bot-user-conversations-new
  width 100%
  height 100%
</style>
