<script lang="ts">
import ComBotUserReportTemplatesIndex from '@/engines/bot/components/bot/report_templates/ComBotUserReportTemplatesIndex.vue';
import { defineComponent } from '@vue/runtime-core';
import { BotUserReportTemplatesApi } from '@/engines/bot/apis/bot/user/report_templates.api';
import { BotReportTemplateModel } from '@/engines/bot/models/bot/user/report_templates';
import { VStore } from '@/lib/vails';

import usePolicy from '@/components/global/ta-component/ta-template-form-core/usePolicy';

const BotUserReportTemplatesIndex = defineComponent({
  name: 'BotUserReportTemplatesIndex',
  components: {
    ComBotUserReportTemplatesIndex,
  },
  setup() {
    usePolicy();




    const store = new VStore(new BotUserReportTemplatesApi(), BotReportTemplateModel);


    return {
      store,

    };
  },
});

export default BotUserReportTemplatesIndex;
</script>

<template lang="pug">
.bot-user-report-templates-index
  ComBotUserReportTemplatesIndex(:store='store' )
</template>

<style lang="stylus" scoped>
.bot-user-report-templates-index
  height 100%
  width 100%
  padding 0 !important
</style>
