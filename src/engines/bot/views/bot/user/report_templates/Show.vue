<script lang="ts">
import ComBotReportTemplatesShow from '@/engines/bot/components/bot/report_templates/ComBotReportTemplatesShow.vue';
import { defineComponent, toRefs, onMounted, computed, inject, Ref, ref } from 'vue';
import { BotUserReportTemplatesApi } from '@/engines/bot/apis/bot/user/report_templates.api';
import { BotReportTemplateModel } from '@/engines/bot/models/bot/user/report_templates';
import { VStore } from '@/lib/vails';
import { useRoute } from 'vue-router';
import { VObject } from '../../../../../../lib/vails/model/index';
import { OfficeStateKey } from '../../../../components/bot/ComOfficeEditorLayout.vue';
import { BotUserReportsApi } from '../../../../apis/bot/user/reports.api';
import { BotReportModel } from '../../../../models/bot/user/reports';
import ReportTemplateEventCenter from '@/engines/bot/components/bot/report_templates/ReportTemplateEventCenter';
import { message } from 'ant-design-vue';

const BotUserReportTemplatesShow = defineComponent({
  name: 'BotUserReportTemplatesShow',
  components: {
    ComBotReportTemplatesShow,
  },
  setup(props) {
    const route = useRoute();
    const state = inject<Ref<VObject>>(OfficeStateKey)!
    const isManage = false
    const store = new VStore(new BotUserReportTemplatesApi(), BotReportTemplateModel);
    const reportStore = new VStore(new BotUserReportsApi(), BotReportModel);
    const report = ref(reportStore.new())


    onMounted(() => {
      store.find(Number(route.params.report_templateId));
    });

    ReportTemplateEventCenter.on('save', async () => {
      if (isManage) return
      message.loading('保存中...')
      report.value.formData = {
        ...report.value.formData,
        content: state.value.editor.storage.markdown.getMarkdown(),
        variables: state.value.variables,
        report_template_id: store.record.value.id
      }
      console.log(report.value.formData, state.value.variables)
      state.value.promise = new Promise(async (resolve, reject) => {
        await report.value.save()
        resolve('保存成功')
      })
    })

    return {
      ...toRefs(props),
      store,
      record: store.record,
      reportStore,
      isManage,
      report,
    };
  },
});

export default BotUserReportTemplatesShow;
</script>

<template lang="pug">
.bot-user-report-templates-show
  ComBotReportTemplatesShow(
    v-if='record.id',
    :store='store',
    :isManage='isManage',
    :report='report',
    :reportStore='reportStore'
  )
</template>

<style lang="stylus" scoped>
.bot-user-report-templates-show
  height 100%
  width 100%
  .breadcrumbs
    padding 0 24px
</style>
