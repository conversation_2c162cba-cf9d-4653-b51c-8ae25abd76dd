<script lang="ts">
import ComBotReviewersIndex from '@/engines/bot/components/bot/reviewers/ComBotReviewersIndex.vue';
import { defineComponent } from '@vue/runtime-core';
import { BotUserReviewersApi } from '@/engines/bot/apis/bot/user/reviewers.api';
import { BotReviewerModel } from '@/engines/bot/models/bot/user/reviewers';
import { VStore } from '@/lib/vails';

import usePolicy from '@/components/global/ta-component/ta-template-form-core/usePolicy';

const BotUserReviewersIndex = defineComponent({
  name: 'BotUserReviewersIndex',
  components: {
    ComBotReviewersIndex,
  },
  setup() {
    usePolicy();

    

    
    const store = new VStore(new BotUserReviewersApi(), BotReviewerModel);
    

    return {
      store,
      
    };
  },
});

export default BotUserReviewersIndex;
</script>

<template lang="pug">
.bot-user-reviewers-index
  ComBotReviewersIndex(:store='store' )
</template>

<style lang="stylus" scoped>
.bot-user-reviewers-index
  height 100%
  width 100%
</style>
