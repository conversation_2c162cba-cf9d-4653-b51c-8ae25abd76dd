<script lang="ts">
  import ComBotReviewersShow from '@/engines/bot/components/bot/reviewers/ComBotReviewersShow.vue';
  import { defineComponent, toRefs, onMounted, computed } from 'vue';
  import { BotUserReviewersApi } from '@/engines/bot/apis/bot/user/reviewers.api';
  import { BotReviewerModel } from '@/engines/bot/models/bot/user/reviewers';
  import { VStore } from '@/lib/vails';
  import { useRoute } from 'vue-router';

  const BotUserReviewersShow = defineComponent({
    name: 'BotUserReviewersShow',
    components: {
    ComBotReviewersShow,
  },
    setup(props) {
      const route = useRoute();
    
    const store = new VStore(new BotUserReviewersApi(), BotReviewerModel);
    
    const breadcrumbs = computed(() => [
      
    { label: '', route: '/bot/user/reviewers' },
          ]);

  onMounted(() => {
    store.find(Number(route.params.reviewerId));
  });

  return {
    ...toRefs(props),
    store,
    record: store.record,
    breadcrumbs,
  };
  },
});

  export default BotUserReviewersShow;
</script>

<template lang="pug">
.bot-user-reviewers-show
  ComBotReviewersShow(v-if='record.id', :store='store', :breadcrumbs='breadcrumbs')
</template>

<style lang="stylus" scoped>
.bot-user-reviewers-show
  height 100%
  width 100%
  .breadcrumbs
    padding 0 24px
</style>
