<script lang="ts">
import ComBotReportsIndex from '@/engines/bot/components/bot/reports/ComBotReportsIndex.vue';
import { defineComponent } from '@vue/runtime-core';
import { BotUserReportsApi } from '@/engines/bot/apis/bot/user/reports.api';
import { BotReportModel } from '@/engines/bot/models/bot/user/reports';
import { VStore } from '@/lib/vails';

import usePolicy from '@/components/global/ta-component/ta-template-form-core/usePolicy';

const BotUserReportsIndex = defineComponent({
  name: 'BotUserReportsIndex',
  components: {
    ComBotReportsIndex,
  },
  setup() {
    usePolicy();




    const store = new VStore(new BotUserReportsApi(), BotReportModel);


    return {
      store,

    };
  },
});

export default BotUserReportsIndex;
</script>

<template lang="pug">
.bot-user-reports-index
  ComBotReportsIndex.pt-4(:store='store' )
</template>

<style lang="stylus" scoped>
.bot-user-reports-index
  height 100%
  width 100%
</style>
