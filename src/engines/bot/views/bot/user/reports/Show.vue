<script lang="ts">
import ComBotReportsShow from '@/engines/bot/components/bot/reports/ComBotReportsShow.vue';
import { defineComponent, toRefs, onMounted, computed, inject, Ref } from 'vue';
import { BotUserReportsApi } from '@/engines/bot/apis/bot/user/reports.api';
import { BotReportModel } from '@/engines/bot/models/bot/user/reports';
import { VStore } from '@/lib/vails';
import { useRoute } from 'vue-router';
import ReportTemplateEventCenter from '@/engines/bot/components/bot/report_templates/ReportTemplateEventCenter';
import { message } from 'ant-design-vue';
import { VObject } from '../../../../../../lib/vails/model/index';
import { OfficeStateKey } from '../../../../components/bot/ComOfficeEditorLayout.vue';
import { BotReviewResultModel } from '@/engines/bot/models/bot/user/review_results';
import { BotUserReviewResultsApi } from '../../../../apis/bot/user/review_results.api';

const BotUserReportsShow = defineComponent({
  name: 'BotUserReportsShow',
  components: {
    ComBotReportsShow,
  },
  setup(props) {
    const route = useRoute();
    const state = inject<Ref<VObject>>(OfficeStateKey)!

    const reportId = Number(route.params.reportId);

    const store = new VStore(new BotUserReportsApi(), BotReportModel);
    const resultStore = new VStore(new BotUserReviewResultsApi({
      parents: [{ type: 'reports', id: reportId }]
    }), BotReviewResultModel);

    onMounted(() => {
      Promise.all([
        store.find(reportId),
        resultStore.index({ per_page: 30 }),
      ])
    });

    ReportTemplateEventCenter.on('save', () => {
      store.record.value.formData = {
        title: state.value.name,
        content: state.value.editor.storage.markdown.getMarkdown(),
        variables: state.value.variables,
      }

      store.record.value.save().then(() => {
        message.destroy();
        message.success('保存成功', 1);
      });
    });

    return {
      ...toRefs(props),
      store,
      record: store.record,
      resultStore,
    };
  },
});

export default BotUserReportsShow;
</script>

<template lang="pug">
.bot-user-reports-show
  ComBotReportsShow(
    v-if='record.id',
    :store='store',
    :resultStore='resultStore'
  )
</template>

<style lang="stylus" scoped>
.bot-user-reports-show
  height 100%
  width 100%
  .breadcrumbs
    padding 0 24px
</style>
