<script lang="ts">
import ComBotReviewDocumentsShow from '@/engines/bot/components/bot/review_documents/ComBotReviewDocumentsShow.vue';
import { defineComponent, toRefs, onMounted, computed, inject, Ref } from 'vue';
import { BotUserReviewDocumentsApi } from '@/engines/bot/apis/bot/user/review_documents.api';
import { BotReviewDocumentModel } from '@/engines/bot/models/bot/user/review_documents';
import { VStore } from '@/lib/vails';
import { useRoute } from 'vue-router';
import { VObject } from '../../../../../../lib/vails/model/index';
import { OfficeStateKey } from '../../../../components/bot/ComOfficeEditorLayout.vue';
import ReportTemplateEventCenter from '@/engines/bot/components/bot/report_templates/ReportTemplateEventCenter';
import { message } from 'ant-design-vue';
import { BotUserReviewRulesApi } from '../../../../apis/bot/user/review_rules.api';
import { BotReviewRuleModel } from '../../../../models/bot/user/review_rules';
import { BotUserReviewResultsApi } from '../../../../apis/bot/user/review_results.api';
import { BotReviewResultModel } from '../../../../models/bot/user/review_results';

const BotUserReviewDocumentsShow = defineComponent({
  name: 'BotUserReviewDocumentsShow',
  components: {
    ComBotReviewDocumentsShow,
  },
  setup(props) {
    const route = useRoute();
    const documentId = Number(route.params.review_documentId);
    const state = inject<Ref<VObject>>(OfficeStateKey)!

    const store = new VStore(new BotUserReviewDocumentsApi(), BotReviewDocumentModel);
    const resultsStore = new VStore(new BotUserReviewResultsApi({
      parents: [{ type: 'review_documents', id: documentId }]
    }), BotReviewResultModel);


    onMounted(async () => {
      await Promise.all([
        store.find(documentId),
        resultsStore.index(),
      ])
      state.value.name = store.record.value?.name
    });

    ReportTemplateEventCenter.on('save', () => {
      message.loading('保存中...')
      state.value.promise = new Promise(async (resolve, reject) => {
        await store.record.value.save()
        state.value.name = store.record.value?.name
        resolve('保存成功')
      })
    })

    return {
      ...toRefs(props),
      store,
      record: store.record,
      resultsStore,
    };
  },
});

export default BotUserReviewDocumentsShow;
</script>

<template lang="pug">
.bot-user-review-documents-show
  ComBotReviewDocumentsShow(
    v-if='record.id',
    :store='store'
    :resultsStore='resultsStore'
  )
</template>

<style lang="stylus" scoped>
.bot-user-review-documents-show
  height 100%
  width 100%
  .breadcrumbs
    padding 0 24px
</style>
