<script lang="ts">
import ComBotReviewDocumentsIndex from '@/engines/bot/components/bot/review_documents/ComBotReviewDocumentsIndex.vue';
import { defineComponent } from '@vue/runtime-core';
import { BotUserReviewDocumentsApi } from '@/engines/bot/apis/bot/user/review_documents.api';
import { BotReviewDocumentModel } from '@/engines/bot/models/bot/user/review_documents';
import { VStore } from '@/lib/vails';

import usePolicy from '@/components/global/ta-component/ta-template-form-core/usePolicy';
import { useRoute } from 'vue-router';

const BotUserReviewDocumentsIndex = defineComponent({
  name: 'BotUserReviewDocumentsIndex',
  components: {
    ComBotReviewDocumentsIndex,
  },
  setup() {
    usePolicy();


    const route = useRoute();
    const reviewerId = Number(route.params.reviewerId);

    const store = new VStore(new BotUserReviewDocumentsApi({
      params: {
        q: {
          reviewer_id_eq: reviewerId
        }
      }
    }), BotReviewDocumentModel);


    return {
      store,
      reviewerId,
    };
  },
});

export default BotUserReviewDocumentsIndex;
</script>

<template lang="pug">
.bot-user-review-documents-index
  ComBotReviewDocumentsIndex(
    :store='store',
    :reviewerId='reviewerId'
  )
</template>

<style lang="stylus" scoped>
.bot-user-review-documents-index
  height 100%
  width 100%
</style>
