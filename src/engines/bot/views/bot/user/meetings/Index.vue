<script lang="ts">
import ComBotMeetingsIndex from '@/engines/bot/components/bot/meetings/ComBotMeetingsIndex.vue';
import { defineComponent } from '@vue/runtime-core';
import { BotUserMeetingsApi } from '@/engines/bot/apis/bot/user/meetings.api';
import { BotMeetingModel } from '@/engines/bot/models/bot/user/meetings';
import { VStore } from '@/lib/vails';

import usePolicy from '@/components/global/ta-component/ta-template-form-core/usePolicy';

const BotUserMeetingsIndex = defineComponent({
  name: 'BotUserMeetingsIndex',
  components: {
    ComBotMeetingsIndex,
  },
  setup() {
    usePolicy();

    

    
    const store = new VStore(new BotUserMeetingsApi(), BotMeetingModel);
    

    return {
      store,
      
    };
  },
});

export default BotUserMeetingsIndex;
</script>

<template lang="pug">
.bot-user-meetings-index
  ComBotMeetingsIndex(:store='store' )
</template>

<style lang="stylus" scoped>
.bot-user-meetings-index
  height 100%
  width 100%
</style>
