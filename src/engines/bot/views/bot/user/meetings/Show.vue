<script lang="ts">
import ComBotMeetingsShow from '@/engines/bot/components/bot/meetings/ComBotMeetingsShow.vue';
import { defineComponent, toRefs, onMounted, computed, ref, inject, Ref } from 'vue';
import { BotUserMeetingsApi } from '@/engines/bot/apis/bot/user/meetings.api';
import { BotMeetingModel } from '@/engines/bot/models/bot/user/meetings';
import { VStore } from '@/lib/vails';
import { useRoute } from 'vue-router';
import { VObject } from '../../../../../../lib/vails/model/index';
import { OfficeStateKey } from '../../../../components/bot/ComOfficeEditorLayout.vue';
import ReportTemplateEventCenter from '@/engines/bot/components/bot/report_templates/ReportTemplateEventCenter';
import { message } from 'ant-design-vue';

const BotUserMeetingsShow = defineComponent({
  name: 'BotUserMeetingsShow',
  components: {
    ComBotMeetingsShow,
    ReportTemplateEventCenter,
  },
  setup(props) {
    const route = useRoute();
    const store = new VStore(new BotUserMeetingsApi(), BotMeetingModel);
    const state = inject<Ref<VObject>>(OfficeStateKey)!

    onMounted(async () => {
      state.value.code = 'meeting'
      await store.find(Number(route.params.meetingId));
      state.value.name = store.record.value?.name
    });


    ReportTemplateEventCenter.on('save', () => {
      message.loading('保存中...')
      store.record.value.formData.summary = state.value.editor.storage.markdown.getMarkdown()
      state.value.promise = new Promise(async (resolve, reject) => {
        await store.record.value.save()
        state.value.name = store.record.value?.name
        resolve('保存成功')
      })
    })


    const regenerateModalVisible = ref(false)
    const confirmLoading = ref(false)
    const cacheRegeneratePrompt = ref('')

    const onRegenerate = async () => {
      confirmLoading.value = true
      await store.sendMemberAction({
        action: 'generate_topic_and_summary',
        id: store.record.value.id!,
        config: {
          data: { payload: { ...store.record.value.payload, prompt: cacheRegeneratePrompt.value } }
        }
      })

      confirmLoading.value = false
      cacheRegeneratePrompt.value = ''
      message.success('重新生成中...')
    }
    const onCancel = () => {
      regenerateModalVisible.value = false
      cacheRegeneratePrompt.value = ''
    }
    ReportTemplateEventCenter.on('regenerate', () => {
      regenerateModalVisible.value = true
      cacheRegeneratePrompt.value = store.record.value.payload.prompt
    })

    return {
      ...toRefs(props),
      store,
      record: store.record,
      regenerateModalVisible,
      confirmLoading,
      cacheRegeneratePrompt,
      onCancel,
      onRegenerate,
    };
  },
});

export default BotUserMeetingsShow;
</script>

<template lang="pug">
.bot-user-meetings-show
  a-modal(
    v-model:visible='regenerateModalVisible',
    title='重新生成会议纪要',
    @ok='onRegenerate',
    @cancel='onCancel',
    :confirm-loading="confirmLoading"
  )
    .px-2
      .text-sm.text-gray-900.font-bold.mb-2 提示词
      a-textarea(v-model:value='cacheRegeneratePrompt')
  ComBotMeetingsShow(v-if='record.id', :store='store')
</template>

<style lang="stylus" scoped>
.bot-user-meetings-show
  height 100%
  width 100%
  .breadcrumbs
    padding 0 24px
</style>
