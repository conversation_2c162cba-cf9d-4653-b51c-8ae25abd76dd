<script lang="ts">
import ComBotReportsIndex from '@/engines/bot/components/bot/reports/ComBotReportsIndex.vue';
import { defineComponent } from '@vue/runtime-core';
import { BotManageReportsApi } from '@/engines/bot/apis/bot/manage/reports.api';
import { BotReportModel } from '@/engines/bot/models/bot/manage/reports';
import { VStore } from '@/lib/vails';

import usePolicy from '@/components/global/ta-component/ta-template-form-core/usePolicy';

const BotManageReportsIndex = defineComponent({
  name: 'BotManageReportsIndex',
  components: {
    ComBotReportsIndex,
  },
  setup() {
    usePolicy();

    

    
    const store = new VStore(new BotManageReportsApi(), BotReportModel);
    

    return {
      store,
      
    };
  },
});

export default BotManageReportsIndex;
</script>

<template lang="pug">
.bot-manage-reports-index
  ComBotReportsIndex(:store='store' )
</template>

<style lang="stylus" scoped>
.bot-manage-reports-index
  height 100%
  width 100%
</style>
