<script lang="ts">
import ComBotReportsShow from '@/engines/bot/components/bot/reports/ComBotReportsShow.vue';
import { defineComponent, toRefs, onMounted, computed } from 'vue';
import { BotManageReportsApi } from '@/engines/bot/apis/bot/manage/reports.api';
import { BotReportModel } from '@/engines/bot/models/bot/manage/reports';
import { VStore } from '@/lib/vails';
import { useRoute } from 'vue-router';

const BotManageReportsShow = defineComponent({
  name: 'BotManageReportsShow',
  components: {
    ComBotReportsShow,
  },
  setup(props) {
    const route = useRoute();

    const store = new VStore(new BotManageReportsApi(), BotReportModel);


    onMounted(() => {
      store.find(Number(route.params.reportId));
    });

    return {
      ...toRefs(props),
      store,
      record: store.record,
    };
  },
});

export default BotManageReportsShow;
</script>

<template lang="pug">
.bot-manage-reports-show
  ComBotReportsShow(v-if='record.id', :store='store')
</template>

<style lang="stylus" scoped>
.bot-manage-reports-show
  height 100%
  width 100%
  .breadcrumbs
    padding 0 24px
</style>
