<script lang="ts">
import ComBotReviewersIndex from '@/engines/bot/components/bot/reviewers/ComBotReviewersIndex.vue';
import { defineComponent } from '@vue/runtime-core';
import { BotManageReviewersApi } from '@/engines/bot/apis/bot/manage/reviewers.api';
import { BotReviewerModel } from '@/engines/bot/models/bot/manage/reviewers';
import { VStore } from '@/lib/vails';

import usePolicy from '@/components/global/ta-component/ta-template-form-core/usePolicy';

const BotManageReviewersIndex = defineComponent({
  name: 'BotManageReviewersIndex',
  components: {
    ComBotReviewersIndex,
  },
  setup() {
    usePolicy();

    

    
    const store = new VStore(new BotManageReviewersApi(), BotReviewerModel);
    

    return {
      store,
      
    };
  },
});

export default BotManageReviewersIndex;
</script>

<template lang="pug">
.bot-manage-reviewers-index
  ComBotReviewersIndex(:store='store' )
</template>

<style lang="stylus" scoped>
.bot-manage-reviewers-index
  height 100%
  width 100%
</style>
