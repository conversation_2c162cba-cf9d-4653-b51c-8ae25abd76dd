<script lang="ts">
  import ComBotReviewersShow from '@/engines/bot/components/bot/reviewers/ComBotReviewersShow.vue';
  import { defineComponent, toRefs, onMounted, computed } from 'vue';
  import { BotManageReviewersApi } from '@/engines/bot/apis/bot/manage/reviewers.api';
  import { BotReviewerModel } from '@/engines/bot/models/bot/manage/reviewers';
  import { VStore } from '@/lib/vails';
  import { useRoute } from 'vue-router';

  const BotManageReviewersShow = defineComponent({
    name: 'BotManageReviewersShow',
    components: {
    ComBotReviewersShow,
  },
    setup(props) {
      const route = useRoute();
    
    const store = new VStore(new BotManageReviewersApi(), BotReviewerModel);
    
    const breadcrumbs = computed(() => [
      
    { label: '', route: '/bot/manage/reviewers' },
          ]);

  onMounted(() => {
    store.find(Number(route.params.reviewerId));
  });

  return {
    ...toRefs(props),
    store,
    record: store.record,
    breadcrumbs,
  };
  },
});

  export default BotManageReviewersShow;
</script>

<template lang="pug">
.bot-manage-reviewers-show
  ComBotReviewersShow(v-if='record.id', :store='store', :breadcrumbs='breadcrumbs')
</template>

<style lang="stylus" scoped>
.bot-manage-reviewers-show
  height 100%
  width 100%
  .breadcrumbs
    padding 0 24px
</style>
