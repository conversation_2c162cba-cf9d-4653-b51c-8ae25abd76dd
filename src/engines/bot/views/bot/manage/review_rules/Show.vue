<script lang="ts">
  import ComBotReviewRulesShow from '@/engines/bot/components/bot/review_rules/ComBotReviewRulesShow.vue';
  import { defineComponent, toRefs, onMounted, computed } from 'vue';
  import { BotManageReviewRulesApi } from '@/engines/bot/apis/bot/manage/review_rules.api';
  import { BotReviewRuleModel } from '@/engines/bot/models/bot/manage/review_rules';
  import { VStore } from '@/lib/vails';
  import { useRoute } from 'vue-router';

  const BotManageReviewRulesShow = defineComponent({
    name: 'BotManageReviewRulesShow',
    components: {
    ComBotReviewRulesShow,
  },
    setup(props) {
      const route = useRoute();
    
    const store = new VStore(new BotManageReviewRulesApi(), BotReviewRuleModel);
    
    const breadcrumbs = computed(() => [
      
    { label: '', route: '/bot/manage/review_rules' },
          ]);

  onMounted(() => {
    store.find(Number(route.params.review_ruleId));
  });

  return {
    ...toRefs(props),
    store,
    record: store.record,
    breadcrumbs,
  };
  },
});

  export default BotManageReviewRulesShow;
</script>

<template lang="pug">
.bot-manage-review-rules-show
  ComBotReviewRulesShow(v-if='record.id', :store='store', :breadcrumbs='breadcrumbs')
</template>

<style lang="stylus" scoped>
.bot-manage-review-rules-show
  height 100%
  width 100%
  .breadcrumbs
    padding 0 24px
</style>
