<script lang="ts">
  import ComBotAgentsShow from '@/engines/bot/components/bot/agents/ComBotAgentsShow.vue';
  import { defineComponent, toRefs, onMounted, computed } from 'vue';
  import { BotManageAgentsApi } from '@/engines/bot/apis/bot/manage/agents.api';
  import { BotAgentModel } from '@/engines/bot/models/bot/manage/agents';
  import { VStore } from '@/lib/vails';
  import { useRoute } from 'vue-router';

  const BotManageAgentsShow = defineComponent({
    name: 'BotManageAgentsShow',
    components: {
    ComBotAgentsShow,
  },
    setup(props) {
      const route = useRoute();
    
    const store = new VStore(new BotManageAgentsApi(), BotAgentModel);
    
    const breadcrumbs = computed(() => [
      
    { label: '', route: '/bot/manage/agents' },
          ]);

  onMounted(() => {
    store.find(Number(route.params.agentId));
  });

  return {
    ...toRefs(props),
    store,
    record: store.record,
    breadcrumbs,
  };
  },
});

  export default BotManageAgentsShow;
</script>

<template lang="pug">
.bot-manage-agents-show
  ComBotAgentsShow(v-if='record.id', :store='store', :breadcrumbs='breadcrumbs')
</template>

<style lang="stylus" scoped>
.bot-manage-agents-show
  height 100%
  width 100%
  .breadcrumbs
    padding 0 24px
</style>
