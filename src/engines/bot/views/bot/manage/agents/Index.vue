<script lang="ts">
import ComBotAgentsIndex from '@/engines/bot/components/bot/agents/ComBotAgentsIndex.vue';
import { defineComponent } from '@vue/runtime-core';
import { BotManageAgentsApi } from '@/engines/bot/apis/bot/manage/agents.api';
import { BotAgentModel } from '@/engines/bot/models/bot/manage/agents';
import { VStore } from '@/lib/vails';

import usePolicy from '@/components/global/ta-component/ta-template-form-core/usePolicy';

const BotManageAgentsIndex = defineComponent({
  name: 'BotManageAgentsIndex',
  components: {
    ComBotAgentsIndex,
  },
  setup() {
    usePolicy();

    

    
    const store = new VStore(new BotManageAgentsApi(), BotAgentModel);
    

    return {
      store,
      
    };
  },
});

export default BotManageAgentsIndex;
</script>

<template lang="pug">
.bot-manage-agents-index
  ComBotAgentsIndex(:store='store' )
</template>

<style lang="stylus" scoped>
.bot-manage-agents-index
  height 100%
  width 100%
</style>
