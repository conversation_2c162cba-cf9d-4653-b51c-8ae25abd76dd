<script lang="ts">
import ComBotReviewRulesIndex from '@/engines/bot/components/bot/review_rules/ComBotReviewRulesIndex.vue';
import { defineComponent } from '@vue/runtime-core';
import { BotManageReviewRulesApi } from '@/engines/bot/apis/bot/manage/review_rules.api';
import { BotReviewRuleModel } from '@/engines/bot/models/bot/manage/review_rules';
import { VStore } from '@/lib/vails';

import usePolicy from '@/components/global/ta-component/ta-template-form-core/usePolicy';
import { useRoute } from 'vue-router';

const BotManageReviewRulesIndex = defineComponent({
  name: 'BotManageReviewRulesIndex',
  components: {
    ComBotReviewRulesIndex,
  },
  setup() {
    usePolicy();


    const route = useRoute();
    const reportTemplateId = Number(route.params.report_templateId);

    const store = new VStore(new BotManageReviewRulesApi({
      parents: [{ type: 'report_templates', id: reportTemplateId }]
    }), BotReviewRuleModel);


    return {
      store,

    };
  },
});

export default BotManageReviewRulesIndex;
</script>

<template lang="pug">
.bot-manage-review-rules-index
  ComBotReviewRulesIndex(:store='store' )
</template>

<style lang="stylus" scoped>
.bot-manage-review-rules-index
  height 100%
  width 100%
</style>
