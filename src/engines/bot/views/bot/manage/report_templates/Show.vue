<script lang="ts">
import ComBotReportTemplatesShow from '@/engines/bot/components/bot/report_templates/ComBotReportTemplatesShow.vue';
import { defineComponent, toRefs, onMounted, inject, Ref, ref, onUnmounted } from 'vue';
import { BotManageReportTemplatesApi } from '@/engines/bot/apis/bot/manage/report_templates.api';
import { BotReportTemplateModel } from '@/engines/bot/models/bot/manage/report_templates';
import { VStore } from '@/lib/vails';
import { useRoute } from 'vue-router';
import ReportTemplateEventCenter from '@/engines/bot/components/bot/report_templates/ReportTemplateEventCenter';
import { message } from 'ant-design-vue';
import { VObject } from '../../../../../../lib/vails/model/index';
import { OfficeStateKey } from '../../../../components/bot/ComOfficeEditorLayout.vue';
import { BotManageReviewRulesApi } from '../../../../apis/bot/manage/review_rules.api';
import { BotReviewRuleModel } from '../../../../models/bot/user/review_rules';
import BotManageReviewRulesIndex from '@/engines/bot/views/bot/manage/report_templates/review_rules/Index.vue';

const BotManageReportTemplatesShow = defineComponent({
  name: 'BotManageReportTemplatesShow',
  components: {
    ComBotReportTemplatesShow,
    BotManageReviewRulesIndex,
  },
  setup(props) {
    const route = useRoute();
    const state = inject<Ref<VObject>>(OfficeStateKey)!
    const reportTemplateId = Number(route.params.report_templateId)

    const store = new VStore(new BotManageReportTemplatesApi(), BotReportTemplateModel);
    const ruleStore = new VStore(new BotManageReviewRulesApi({
      parents: [{ type: 'report_templates', id: reportTemplateId }]
    }), BotReviewRuleModel)


    ReportTemplateEventCenter.on('save', () => {
      message.loading('保存中...')
      store.record.value.update({
        conf: store.record.value.conf,
        content: state.value.editor.storage.markdown.getMarkdown(),
        prompt: store.record.value.formData.prompt,
        instructions: store.record.value.formData.instructions
      }).then(() => {
        message.destroy();
        message.success('保存成功')
      })
    })

    const tabs = [
      {
        key: 'template',
        label: '模板编辑',
      },
      {
        key: 'rules',
        label: '规则配置',
      },
    ]
    const activeTab = ref(tabs[0])


    onMounted(() => {
      state.value.tabs = tabs
      state.value.activeTab = activeTab
      Promise.all([
        store.find(reportTemplateId),
        ruleStore.index({ per_page: 30 }),
      ])
    });

    onUnmounted(() => {
      state.value.tabs = []
      state.value.activeTab = ''
    })

    return {
      ...toRefs(props),
      store,
      record: store.record,
      ruleStore,
      activeTab,
    };
  },
});

export default BotManageReportTemplatesShow;
</script>

<template lang="pug">
.bot-manage-report-templates-show
  template(v-if='record.id')
    ComBotReportTemplatesShow(
      v-show='activeTab.key === "template"',
      :store='store',
      :ruleStore='ruleStore'
      :isManage='true'
    )
    BotManageReviewRulesIndex(
      v-show='activeTab.key === "rules"',
    )
</template>

<style lang="stylus" scoped>
.bot-manage-report-templates-show
  height 100%
  width 100%
  .breadcrumbs
    padding 0 24px
</style>
