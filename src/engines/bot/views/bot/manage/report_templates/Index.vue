<script lang="ts">
import ComBotReportTemplatesIndex from '@/engines/bot/components/bot/report_templates/ComBotReportTemplatesIndex.vue';
import { defineComponent } from '@vue/runtime-core';
import { BotManageReportTemplatesApi } from '@/engines/bot/apis/bot/manage/report_templates.api';
import { BotReportTemplateModel } from '@/engines/bot/models/bot/manage/report_templates';
import { VStore } from '@/lib/vails';

import usePolicy from '@/components/global/ta-component/ta-template-form-core/usePolicy';

const BotManageReportTemplatesIndex = defineComponent({
  name: 'BotManageReportTemplatesIndex',
  components: {
    ComBotReportTemplatesIndex,
  },
  setup() {
    usePolicy();




    const store = new VStore(new BotManageReportTemplatesApi(), BotReportTemplateModel);


    return {
      store,

    };
  },
});

export default BotManageReportTemplatesIndex;
</script>

<template lang="pug">
.bot-manage-report-templates-index
  ComBotReportTemplatesIndex(:store='store' )
</template>

<style lang="stylus" scoped>
.bot-manage-report-templates-index
  height 100%
  width 100%
  padding 0 !important
</style>
