<script lang="ts">
  import ComBotIntentsShow from '@/engines/bot/components/bot/intents/ComBotIntentsShow.vue';
  import { defineComponent, toRefs, onMounted, computed } from 'vue';
  import { BotManageIntentsApi } from '@/engines/bot/apis/bot/manage/intents.api';
  import { BotIntentModel } from '@/engines/bot/models/bot/manage/intents';
  import { VStore } from '@/lib/vails';
  import { useRoute } from 'vue-router';

  const BotManageIntentsShow = defineComponent({
    name: 'BotManageIntentsShow',
    components: {
    ComBotIntentsShow,
  },
    setup(props) {
      const route = useRoute();
    
    const store = new VStore(new BotManageIntentsApi(), BotIntentModel);
    
    const breadcrumbs = computed(() => [
      
    { label: '', route: '/bot/manage/intents' },
          ]);

  onMounted(() => {
    store.find(Number(route.params.intentId));
  });

  return {
    ...toRefs(props),
    store,
    record: store.record,
    breadcrumbs,
  };
  },
});

  export default BotManageIntentsShow;
</script>

<template lang="pug">
.bot-manage-intents-show
  ComBotIntentsShow(v-if='record.id', :store='store', :breadcrumbs='breadcrumbs')
</template>

<style lang="stylus" scoped>
.bot-manage-intents-show
  height 100%
  width 100%
  .breadcrumbs
    padding 0 24px
</style>
