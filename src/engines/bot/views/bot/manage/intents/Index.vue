<script lang="ts">
import ComBotIntentsIndex from '@/engines/bot/components/bot/intents/ComBotIntentsIndex.vue';
import { defineComponent } from '@vue/runtime-core';
import { BotManageIntentsApi } from '@/engines/bot/apis/bot/manage/intents.api';
import { BotIntentModel } from '@/engines/bot/models/bot/manage/intents';
import { VStore } from '@/lib/vails';

import usePolicy from '@/components/global/ta-component/ta-template-form-core/usePolicy';

const BotManageIntentsIndex = defineComponent({
  name: 'BotManageIntentsIndex',
  components: {
    ComBotIntentsIndex,
  },
  setup() {
    usePolicy();

    

    
    const store = new VStore(new BotManageIntentsApi(), BotIntentModel);
    

    return {
      store,
      
    };
  },
});

export default BotManageIntentsIndex;
</script>

<template lang="pug">
.bot-manage-intents-index
  ComBotIntentsIndex(:store='store' )
</template>

<style lang="stylus" scoped>
.bot-manage-intents-index
  height 100%
  width 100%
</style>
