<script lang="ts">
  import ComBotMeetingsShow from '@/engines/bot/components/bot/meetings/ComBotMeetingsShow.vue';
  import { defineComponent, toRefs, onMounted, computed } from 'vue';
  import { BotManageMeetingsApi } from '@/engines/bot/apis/bot/manage/meetings.api';
  import { BotMeetingModel } from '@/engines/bot/models/bot/manage/meetings';
  import { VStore } from '@/lib/vails';
  import { useRoute } from 'vue-router';

  const BotManageMeetingsShow = defineComponent({
    name: 'BotManageMeetingsShow',
    components: {
    ComBotMeetingsShow,
  },
    setup(props) {
      const route = useRoute();
    
    const store = new VStore(new BotManageMeetingsApi(), BotMeetingModel);
    
    const breadcrumbs = computed(() => [
      
    { label: '', route: '/bot/manage/meetings' },
          ]);

  onMounted(() => {
    store.find(Number(route.params.meetingId));
  });

  return {
    ...toRefs(props),
    store,
    record: store.record,
    breadcrumbs,
  };
  },
});

  export default BotManageMeetingsShow;
</script>

<template lang="pug">
.bot-manage-meetings-show
  ComBotMeetingsShow(v-if='record.id', :store='store', :breadcrumbs='breadcrumbs')
</template>

<style lang="stylus" scoped>
.bot-manage-meetings-show
  height 100%
  width 100%
  .breadcrumbs
    padding 0 24px
</style>
