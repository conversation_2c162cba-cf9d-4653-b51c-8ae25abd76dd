<script lang="ts">
import ComBotMeetingsIndex from '@/engines/bot/components/bot/meetings/ComBotMeetingsIndex.vue';
import { defineComponent } from '@vue/runtime-core';
import { BotManageMeetingsApi } from '@/engines/bot/apis/bot/manage/meetings.api';
import { BotMeetingModel } from '@/engines/bot/models/bot/manage/meetings';
import { VStore } from '@/lib/vails';

import usePolicy from '@/components/global/ta-component/ta-template-form-core/usePolicy';

const BotManageMeetingsIndex = defineComponent({
  name: 'BotManageMeetingsIndex',
  components: {
    ComBotMeetingsIndex,
  },
  setup() {
    usePolicy();

    

    
    const store = new VStore(new BotManageMeetingsApi(), BotMeetingModel);
    

    return {
      store,
      
    };
  },
});

export default BotManageMeetingsIndex;
</script>

<template lang="pug">
.bot-manage-meetings-index
  ComBotMeetingsIndex(:store='store' )
</template>

<style lang="stylus" scoped>
.bot-manage-meetings-index
  height 100%
  width 100%
</style>
