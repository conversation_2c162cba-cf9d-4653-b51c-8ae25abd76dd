export default [
  {
    path: '/bot/user/conversations',
    name: 'botUserConversationsIndex',
    component: () =>
      import(
        /* webpackChunkName: "botUserConversationsIndex" */ '@/engines/bot/views/bot/user/conversations/Index.vue'
      ),
    meta: {
      title: 'botUserConversationsIndex',
    },
  },
  // {
  //   path: '/bot/user/conversations/:conversationId',
  //   name: 'botUserConversationsShow',
  //   component: () => import(/* webpackChunkName: "botUserConversationsShow" */ '@/engines/bot/views/bot/user/conversations/Show.vue'),
  //   meta: {
  //     title: 'botUserConversationsShow',
  //   },
  // },
];
