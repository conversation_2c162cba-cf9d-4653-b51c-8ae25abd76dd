export default [
  {
    path: '/bot/manage/reviewers/:reviewerId/review_rules',
    name: 'botManageReviewRulesIndex',
    component: () =>
      import(
        /* webpackChunkName: "botManageReviewRulesIndex" */ '@/engines/bot/views/bot/manage/review_rules/Index.vue'
      ),
    meta: {
      title: 'botManageReviewRulesIndex',
      layout: 'bot',
    },
  },
  {
    path: '/bot/manage/report_templates/:report_templateId/review_rules',
    name: 'botManageReportTemplatesReviewRulesIndex',
    component: () =>
      import(
        /* webpackChunkName: "botManageReportTemplatesReviewRulesIndex" */ '@/engines/bot/views/bot/manage/report_templates/review_rules/Index.vue'
      ),
    meta: {
      title: 'botManageReportTemplatesReviewRulesIndex',
      layout: 'bot',
    },
  },
  {
    path: '/bot/manage/review_rules/:review_ruleId',
    name: 'botManageReviewRulesShow',
    component: () =>
      import(
        /* webpackChunkName: "botManageReviewRulesShow" */ '@/engines/bot/views/bot/manage/review_rules/Show.vue'
      ),
    meta: {
      title: 'botManageReviewRulesShow',
    },
  },
];
