export default [
  {
    path: '/bot/manage/report_templates',
    name: 'botManageReportTemplatesIndex',
    component: () =>
      import(
        /* webpackChunkName: "botManageReportTemplatesIndex" */ '@/engines/bot/views/bot/manage/report_templates/Index.vue'
      ),
    meta: {
      title: '模板管理',
      layout: 'bot',
    },
  },

  // {
  //   path: '/bot/manage/report_templates/:report_templateId',
  //   name: 'botManageReportTemplatesShow',
  //   component: () =>
  //     import(
  //       /* webpackChunkName: "botManageReportTemplatesShow" */ '@/engines/bot/views/bot/manage/report_templates/Show.vue'
  //     ),
  //   meta: {
  //     title: 'botManageReportTemplatesShow',
  //     layout: 'bot',
  //   },
  // },
];
