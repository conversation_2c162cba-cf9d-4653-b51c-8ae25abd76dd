export default [
  {
    path: '/bot/manage/agents',
    name: 'botManageAgentsIndex',
    component: () =>
      import(
        /* webpackChunkName: "botManageAgentsIndex" */ '@/engines/bot/views/bot/manage/agents/Index.vue'
      ),
    meta: {
      title: '智能体管理',
    },
  },
  {
    path: '/bot/manage/agents/:agentId',
    name: 'botManageAgentsShow',
    component: () =>
      import(
        /* webpackChunkName: "botManageAgentsShow" */ '@/engines/bot/views/bot/manage/agents/Show.vue'
      ),
    meta: {
      title: 'botManageAgentsShow',
    },
  },
];
