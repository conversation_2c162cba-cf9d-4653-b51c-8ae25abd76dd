import { <PERSON><PERSON><PERSON> } from '@/apis/MyApi';
import { DingtalkSession } from '@/engines/dingtalk/types/model';
import { CLEAR, SET_FROM_SESSION } from '@/engines/login/store/user/actions';
import { VObject } from '@/lib/vails';
import { VApiConfig } from '@/lib/vails/api';
import store from '@/store';
import { message } from 'ant-design-vue';

const getRandomInt = (min: number, max: number) => {
  min = Math.ceil(min);
  max = Math.floor(max);
  return Math.floor(Math.random() * (max - min + 1)) + min;
};

const getLimitIdentifier = () => {
  return new Date().getTime() + '' + getRandomInt(1, 100);
};
export class DingtalkOauthSessionApi extends MyApi<DingtalkSession> {
  constructor(config?: VApiConfig) {
    super({
      namespace: '/dingtalk/oauth',
      name: 'session',
      mode: 'single',
      actions: [
        { name: 'bind', method: 'post', on: 'member' },
        { name: 'signin', method: 'post', on: 'member' },
        { name: 'unbind', method: 'post', on: 'member' },
        { name: 'account_bind', method: 'post', on: 'member' },
      ],
      ...config,
    });
  }

  login(session: VObject) {
    return this.request
      .post(`${this.namespace}/session`, {
        limit_identifier: getLimitIdentifier(),
        ...session,
      })
      .then(async res => {
        return await new Promise((resolve, reject) => {
          if (res.data.token) {
            store.dispatch(`user/${SET_FROM_SESSION}`, res.data);
          }
          resolve(res);
        });
      })
      .catch(error => {
        console.log('login catch');
        message.info(error.response?.data?.message || '登录失败');
        store.dispatch(`user/${CLEAR}`);
        throw error;
      });
  }
}
