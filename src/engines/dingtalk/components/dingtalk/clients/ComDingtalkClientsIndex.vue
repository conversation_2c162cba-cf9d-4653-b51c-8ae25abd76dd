<script lang="ts">
import { defineComponent, computed, ref, Ref, toRefs, reactive } from 'vue';
import { TaIndexViewTabInterface } from '@/components/global/ta-component/TaIndexView/types';
import { VObject } from '@/lib/vails/model';
import ComDingtalkClientsShow from './ComDingtalkClientsShow.vue';

const ComDingtalkClientsIndex = defineComponent({
  name: 'ComDingtalkClientsIndex',
  components: {
    ComDingtalkClientsShow,
  },
  props: {
    store: { type: Object, required: true },
  },
  setup(props, { emit }) {
    const config = computed(() => ({
      recordName: '浙政钉参数',
      store: props.store,
      // pagination: {
      //   perPage: 15,
      //   showPageSizeChanger: false,
      //   hideOnSinglePage: false,
      //   showSizeChanger: false,
      // }
      template: 'dingtalk_client',
      detail: {
        mode: 'auto',
        // mode: 'drawer',
        width: '1100px',
      },
      mode: 'table',
      // editApi:
      // showCount: true,
      // actions: [
      //  { key: 'create', enabled: true },
      //  { key: 'update', enabled: true },
      //  { key: 'delete', enabled: true },
      //  { key: 'import', enabled: true },
      //  { key: 'export', enabled: true },
      // ],
      table: {
        scroll: { y: 'auto' },
        // columns: [{ dataIndex: 'id', title: 'ID' }],
      },
      list: {
        scroll: { y: 'auto' },
      },
      // selection: {
      //   showByDefault: false;
      //   showSwitch: false;
      // };
      // searcherSimpleOptions: [{key: 'title', label: '标题', type: 'string' }],
    }));

    const onIndex = (data: VObject) => {
      console.log('data: ', data);
    };

    return {
      ...toRefs(props),
      config,
      onIndex,
    };
  },
});

export default ComDingtalkClientsIndex;
</script>

<template lang="pug">
.com-dingtalk-manage-clients-index
  TaIndexView(:config='config', @onIndex='onIndex')
    //- template(#card='{ record }')
    //- template(#table)
    //-    a-table-column(title='名称' dataIndex='record')
    //- template(#detail='{ record, onClose }')
    //-   ComDingtalkClientsShow(
    //-     v-if='record.id'
    //-     :store='store'
    //-     :extendRoute='`/dingtalk/manage/clients/${record.id}`'
    //-     :editable='editable'
    //-     @afterDelete='onClose'
    //-     @afterExtend='onClose'
    //-   )
</template>

<style lang="stylus" scoped>
.com-dingtalk-manage-clients-index
  height 100%
  width 100%
</style>
