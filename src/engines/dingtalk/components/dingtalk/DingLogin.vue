<script lang="ts">
import { DingtalkOauthSessionApi } from '@/engines/dingtalk/apis/dingtalk/oauth/session.api';
import useAutoNavigate from '@/engines/tofu/components/useAutoNavigate';
import { VObject } from '@/lib/vails';
import { message } from 'ant-design-vue';
import { computed, defineComponent, onMounted, ref, toRefs } from 'vue';
import { useRoute } from 'vue-router';

const DingLogin = defineComponent({
  name: 'DingLogin',
  props: {
    appId: { type: String, require: true },
  },
  setup(props, { emit }) {
    // 专有钉钉
    const proprietaryNailDomain = 'login.dg-work.cn';
    // 浙政钉
    const zhejiangPoliticalNailDomain = 'login-pro.ding.zj.gov.cn';
    const loading = ref(false);
    const { tofuAutoNavigate } = useAutoNavigate();

    const route = useRoute();
    const src = computed(() => {
      const client_id = process.env.VUE_APP_CLIENT_ID;
      const redirect_uri = process.env.VUE_APP_REDIRECT_URL;
      const VUE_APP_ZHUAN_YOU_DING_DING_LOGIN = process.env.VUE_APP_ZHUAN_YOU_DING_DING_LOGIN;
      if (VUE_APP_ZHUAN_YOU_DING_DING_LOGIN == 'true') {
        return `https://${proprietaryNailDomain}/oauth2/auth.htm?response_type=code&client_id=${client_id}&redirect_uri=${redirect_uri}&scope=get_user_info&authType=QRCODE&embedMode=true`;
      }
      return `https://${zhejiangPoliticalNailDomain}/oauth2/auth.htm?response_type=code&client_id=${client_id}&redirect_uri=${redirect_uri}&scope=get_user_info&authType=QRCODE&embedMode=true`;
    });
    const sendCodeToLogin = (code: VObject) => {
      new DingtalkOauthSessionApi()
        .login(code)
        .then((res: any) => {
          if (res?.data?.need_verify) {
            return emit('secondaryVerify', res.data);
          } else if (res?.data?.token) {
            if (route.query.response_type) {
              return emit('redirect');
            }
            tofuAutoNavigate();
          } else if (!res?.data?.token && res?.data?.data?.content?.data) {
            message.info('未绑定手机号');
            emit('verify', res.data.data.content.data);
          } else if (!res.data.token && res.data.user_id) {
            message.info('请绑定手机号');
            emit('verify', res.data);
          } else {
            message.error('登录出错');
          }
        })
        .catch((err: any) => {
          console.log(err, 'outer');
        });
    };

    onMounted(() => {
      window.addEventListener('message', event => {
        // 这里的event.data 就是登录成功的code信息
        // 数据格式：{ "code": "aaaa" }
        if (event.data.code && !loading.value) {
          loading.value = true;
          sendCodeToLogin({
            code: event.data.code,
            app_id: props.appId,
            oauth_app_id: process.env.VUE_APP_SCAN_CODE_OAUTH_APP_ID,
          });
        }
      });
    });

    return {
      ...toRefs(props),
      src,
    };
  },
});
export default DingLogin;
</script>

<template lang="pug">
.ding-login
  iframe.frame_style(:src='src', width='300', height='320')
</template>

<style lang="stylus" scoped>
.ding-login
  .frame_style
    margin 0 auto
    border medium none
</style>
