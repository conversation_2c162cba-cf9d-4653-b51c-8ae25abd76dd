<script lang="ts">
import { ref, defineComponent, toRefs, computed, onMounted } from 'vue';
import DingLogin from '@/engines/dingtalk/components/dingtalk/DingLogin.vue';
import { message } from 'ant-design-vue';
import { VObject } from '../../../../lib/vails/model/index';
import { DingtalkOauthSessionApi } from '../../apis/dingtalk/oauth/session.api';
import { AuthSessionApi } from '../../../login/apis/auth/session.api';
import { useRoute } from 'vue-router';
import dd from 'gdt-jsapi';
import { SmsAuthRegistApi } from '../../../login/apis/sms_auth/regist_session.api';
import { VStore } from '../../../../lib/vails/store/index';
import ComLoginBackgroundSmsLogin from '../../../login/components/ComLoginBackgroundSmsLogin.vue';
import { SmsAuthUserAccountApi } from '../../../login/apis/sms_auth/user_account.api';

const ComDingScanLogin = defineComponent({
  name: 'ComDingScanLogin',
  components: {
    DingLogin,
    ComLoginBackgroundSmsLogin,
  },
  props: {
    user: { type: Object, default: () => ({}) },
    loading: { type: Boolean, default: false },
  },
  setup(props, { emit }) {
    const localUser = computed({
      get: () => props.user,
      set: val => emit('update:user', val),
    });

    const localLoading = computed({
      get: () => props.loading,
      set: val => emit('update:loading', val),
    });

    const visibleSecondVerify = ref(false);
    const visibleBind = ref(false);

    const route = useRoute();

    // let locked = false;

    onMounted(() => {
      new AuthSessionApi().sendCollectionAction('check').then(() => {});
      if (route.query?.auth_code) {
        message.loading('进行浙政钉登录');
        onDingLogin({ auth_code: route.query.auth_code });
      } else {
        dd.getAuthCode({}).then((result: VObject) => {
          if (result.code || result.auth_code) {
            // if (locked) return;
            // locked = true;
            message.loading('进行浙政钉登录');
            onDingLogin(result);
          }
        });
      }
    });

    /** 自动登录 */
    const onDingLogin = async (result: VObject) => {
      if (localLoading.value) return;
      localLoading.value = true;
      try {
        await new DingtalkOauthSessionApi()
          .login({
            app_id: localUser.value.app_id,
            oauth_app_id: process.env.VUE_APP_OAUTH_APP_ID,
            auth_code: result.auth_code || result.code,
          })
          .then((res: any) => {
            if (res.status == 201) {
              if (res.data?.need_verify) {
                onSecondVerify(res.data);
              } else if (!res?.data?.token && res?.data?.data?.content?.data) {
                message.warning('未绑定手机号');
                toVerify(res.data.data.content.data);
              } else if (!res.data.token && res.data.user_id) {
                message.info('请绑定手机号');
                toVerify(res.data);
              } else {
                onSuccess();
              }
            }
          });
      } catch (err) {
        console.log(err);
        message.error('登录失败');
      }
      localLoading.value = false;
      // locked = false;
    };

    const userId = ref(0);
    const toVerify = (data: VObject) => {
      if (data.clientId) {
        localUser.value.name = data.lastName;
        localUser.value.openid = data.accountId;
        // 扫码获取的只有扫码的oauth_app_id，去掉_dingoa才是完整的应用id，能接收消息通知
        localUser.value.oauth_app_id = data.clientId.replace(/_dingoa$/, '');
        localUser.value.unionid = data.employeeCode;
      } else {
        localUser.value.openid = data.openid;
        localUser.value.oauth_app_id = data.oauth_app_id.replace(/_dingoa$/, '');
        localUser.value.app_id = data.app_id;
        userId.value = data.user_id;
      }

      visibleBind.value = true;
    };

    const onBindSubmit = async () => {
      if (localLoading.value) return;
      const dingtalkStore = new VStore(new DingtalkOauthSessionApi());
      if (!userId.value) {
        try {
          localLoading.value = true;
          await new SmsAuthRegistApi().login({ ...localUser.value }).then(() => {
            dingtalkStore.sendMemberAction({
              id: 1,
              action: 'bind',
              config: {
                data: {
                  openid: localUser.value.openid,
                  unionid: localUser.value.unionid,
                  oauth_app_id: localUser.value.oauth_app_id,
                },
              },
            });
          });
          onSuccess();
        } catch (err) {
          console.log(err);
          message.error('登录失败');
        }
      } else {
        try {
          localLoading.value = true;
          await new SmsAuthUserAccountApi().login({
            ...localUser.value,
            user_id: userId.value,
          } as any);
          onSuccess();
        } catch (e) {
          console.log(e);
          message.error('登录失败');
        }
      }

      localLoading.value = false;
    };

    const onSecondVerify = (data: { account: string; need_verify: boolean }) => {
      message.warning('请验证手机号');
      localUser.value.account = data.account;
      visibleSecondVerify.value = true;
    };

    const onSecondVerifySubmit = () => {
      new SmsAuthRegistApi()
        .login({ ...localUser.value })
        .then(() => {
          onSuccess();
        })
        .catch(() => {
          message.error('验证失败');
        });
    };

    const onSuccess = () => {
      emit('success');
    };

    return {
      ...toRefs(props),
      visibleSecondVerify,
      onSecondVerify,
      toVerify,
      onSuccess,
      visibleBind,
      onBindSubmit,
      onSecondVerifySubmit,
      localLoading,
    };
  },
});
export default ComDingScanLogin;
</script>

<template lang="pug">
.com-ding-scan-login
  template(v-if='visibleBind')
    ComLoginBackgroundSmsLogin(
      v-model:user='user',
      :loading='localLoading',
      @submit='onBindSubmit'
    )

  template(v-else-if='!visibleSecondVerify')
    DingLogin.-mt-24.-ml-8(
      :appId='user.app_id',
      @verify='toVerify',
      @redirect='onSuccess',
      @secondaryVerify='onSecondVerify'
    )

  template(v-else-if='visibleSecondVerify')
    ComLoginBackgroundSmsLogin(
      v-model:user='user',
      :loading='localLoading',
      @submit='onSecondVerifySubmit'
    )
</template>

<style lang="stylus" scoped></style>
