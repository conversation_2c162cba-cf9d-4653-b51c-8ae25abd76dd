export default [
  {
    path: '/dingtalk/manage/clients',
    name: 'dingtalkManageClientsIndex',
    component: () =>
      import(
        /* webpackChunkName: "dingtalkManageClientsIndex" */ '@/engines/dingtalk/views/dingtalk/manage/clients/Index.vue'
      ),
    meta: {
      title: '钉钉参数列表',
    },
  },
  {
    path: '/dingtalk/manage/clients/:clientId',
    name: 'dingtalkManageClientsShow',
    component: () =>
      import(
        /* webpackChunkName: "dingtalkManageClientsShow" */ '@/engines/dingtalk/views/dingtalk/manage/clients/Show.vue'
      ),
    meta: {
      title: '钉钉参数',
    },
  },
];
