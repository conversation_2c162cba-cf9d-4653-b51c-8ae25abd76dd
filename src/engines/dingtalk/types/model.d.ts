import { VObject } from '@/lib/vails';

export interface DingtalkClient {
  id: number;
  app: VObject;
  app_id: number;
  app_key: string;
  app_secret: string;
  code: string;
  corp_id: string;
  created_at: string;
  model_flag: string;
  model_payload: VObject;
  model_payload_summary: VObject;
  name: string;
  options: VObject;
  type: string;
  updated_at: string;
}
export interface DingtalkSession {
  id: number;
}
export interface DingtalkTemplateMessage {
  id: number;
  notifyable_type: string;
  notifyable_id: number;
  user_id: number;
  oauth_app_id: string;
  openid: string;
  message: VObject;
  response: VObject;
  created_at: string;
  updated_at: string;
}
