import { VModel, VObject } from '@/lib/vails';
import { DingtalkTemplateMessage } from '@/engines/dingtalk/types/model';

export class DingtalkTemplateMessageModel extends VModel<DingtalkTemplateMessage> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return DingtalkTemplateMessageModel.stateMapping()[this.reactiveRecord.state];
  // });

  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}
