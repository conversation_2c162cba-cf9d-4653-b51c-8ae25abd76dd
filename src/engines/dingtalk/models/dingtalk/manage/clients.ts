import { VModel, VObject } from '@/lib/vails';
import { DingtalkClient } from '@/engines/dingtalk/types/model';

export class DingtalkClientModel extends VModel<DingtalkClient> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return DingtalkClientModel.stateMapping()[this.reactiveRecord.state];
  // });
  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}
