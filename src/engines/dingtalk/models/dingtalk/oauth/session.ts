import { VModel, VObject } from '@/lib/vails';
import { DingtalkSession } from '@/engines/dingtalk/types/model';

export class DingtalkSessionModel extends VModel<DingtalkSession> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return DingtalkSessionModel.stateMapping()[this.reactiveRecord.state];
  // });

  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}
