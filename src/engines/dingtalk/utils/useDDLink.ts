import dd from 'gdt-jsapi';
import { onMounted, ref } from 'vue';
import { useRouter } from 'vue-router';
const path = process.env.VUE_APP_PATH as string;
const publicPath = process.env.VUE_APP_PUBLIC_PATH as string;
export default function useDDLink() {
  const isDDEnv = ref(false);
  const router = useRouter();

  onMounted(() => {
    dd.getAuthCode({}).then(() => {
      isDDEnv.value = true;
    });
  });

  const linkTo = (url: string, isOpenNewWindow = true) => {
    const currentPath = path + (isOpenNewWindow ? '/' : publicPath);

    if (isDDEnv.value) {
      url.startsWith('/') && (url = url.slice(1));
      return dd.openLink({
        url: url.startsWith('http') ? `${url}?ddtab=true` : `${currentPath}${url}?ddtab=true`,
      });
    }

    if (isOpenNewWindow) return window.open(url);

    return router.push(url);
  };

  return {
    linkTo,
    isDDEnv,
  };
}
