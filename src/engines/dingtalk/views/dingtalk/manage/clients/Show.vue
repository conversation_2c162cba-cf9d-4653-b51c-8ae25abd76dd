<script lang="ts">
import ComDingtalkClientsShow from '@/engines/dingtalk/components/dingtalk/clients/ComDingtalkClientsShow.vue';
import { defineComponent, toRefs, onMounted, computed } from 'vue';
import { DingtalkManageClientsApi } from '@/engines/dingtalk/apis/dingtalk/manage/clients.api';
import { DingtalkClientModel } from '@/engines/dingtalk/models/dingtalk/manage/clients';
import { VStore } from '@/lib/vails';
import { useRoute } from 'vue-router';

const DingtalkManageClientsShow = defineComponent({
  name: 'DingtalkManageClientsShow',
  components: {
    ComDingtalkClientsShow,
  },
  setup(props) {
    const route = useRoute();

    const store = new VStore(new DingtalkManageClientsApi(), DingtalkClientModel);

    const breadcrumbs = computed(() => [{ label: '', route: '/dingtalk/manage/clients' }]);

    onMounted(() => {
      store.find(Number(route.params.clientId));
    });

    return {
      ...toRefs(props),
      store,
      record: store.record,
      breadcrumbs,
    };
  },
});

export default DingtalkManageClientsShow;
</script>

<template lang="pug">
.dingtalk-manage-clients-show
  ComDingtalkClientsShow(v-if='record.id', :store='store', :breadcrumbs='breadcrumbs')
</template>

<style lang="stylus" scoped>
.dingtalk-manage-clients-show
  height 100%
  width 100%
  .breadcrumbs
    padding 0 24px
</style>
