<script lang="ts">
import ComDingtalkClientsIndex from '@/engines/dingtalk/components/dingtalk/clients/ComDingtalkClientsIndex.vue';
import { defineComponent } from '@vue/runtime-core';
import { DingtalkManageClientsApi } from '@/engines/dingtalk/apis/dingtalk/manage/clients.api';
import { DingtalkClientModel } from '@/engines/dingtalk/models/dingtalk/manage/clients';
import { VStore } from '@/lib/vails';

import usePolicy from '@/components/global/ta-component/ta-template-form-core/usePolicy';

const DingtalkManageClientsIndex = defineComponent({
  name: 'DingtalkManageClientsIndex',
  components: {
    ComDingtalkClientsIndex,
  },
  setup() {
    usePolicy();

    const store = new VStore(new DingtalkManageClientsApi(), DingtalkClientModel);

    return {
      store,
    };
  },
});

export default DingtalkManageClientsIndex;
</script>

<template lang="pug">
.dingtalk-manage-clients-index
  ComDingtalkClientsIndex(:store='store' )
</template>

<style lang="stylus" scoped>
.dingtalk-manage-clients-index
  height 100%
  width 100%
</style>
