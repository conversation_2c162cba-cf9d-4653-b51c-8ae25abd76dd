export type RequiredRecursively<T> = Exclude<
  T extends string | number | boolean
    ? T
    : {
        [P in keyof T]-?: T[P] extends (infer U)[]
          ? RequiredRecursively<U>[]
          : T[P] extends Array<infer U>
          ? RequiredRecursively<U>[]
          : RequiredRecursively<T[P]>;
      },
  null | undefined
>;

export type AccessorFunction<T, R> = (object: RequiredRecursively<T>) => R;

export function get<T, R>(object: T, accessorFn: AccessorFunction<T, R>): R | undefined;
export function get<T, R>(object: T, accessorFn: AccessorFunction<T, R>, defaultValue: R): R;
export function get<T, R extends number>(
  object: T,
  accessorFn: AccessorFunction<T, R>,
  defaultValue?: any,
): R | undefined {
  if (defaultValue === undefined) {
    defaultValue = '-';
  }
  try {
    const result = accessorFn(object as unknown as RequiredRecursively<T>);
    return result === undefined || result === null || isNaN(result) ? defaultValue : result;
  } catch (e) {
    return defaultValue;
  }
}

export function toThousands(mount: any) {
  if (mount === '-') {
    return mount;
  } else {
    const T = Math.floor(mount / 10000) || 0;
    return T.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,');
  }
}
export const thousandString = (str = '') => {
  if (str === '-') {
    return str;
  } else {
    if ([...str].find(item => item === '.')) {
      const pointIndex = [...str].findIndex(item => item === '.');
      const numInt = [...str].slice(0, pointIndex).join('');
      const int = [...str]
        .slice(0, pointIndex)
        .join('')
        .replace(/(\d)(?=(?:\d{3})+$)/g, '$1,');
      if (Number(numInt) > 1 || Number(numInt) === 0) {
        return int;
      } else {
        return int + [...str].slice(pointIndex).join('');
      }
    } else {
      return str.replace(/(\d)(?=(?:\d{3})+$)/g, '$1,');
    }
  }
};

export const thousandStringWithPoint = (str = '') => {
  if (str === '-') {
    return str;
  } else {
    if ([...str].find(item => item === '.')) {
      const pointIndex = [...str].findIndex(item => item === '.');
      const numInt = [...str].slice(0, pointIndex).join('');
      const int = [...str]
        .slice(0, pointIndex)
        .join('')
        .replace(/(\d)(?=(?:\d{3})+$)/g, '$1,');
      return int + [...str].slice(pointIndex).join('');
      // if (Number(numInt) > 1 || Number(numInt) === 0) {
      //   return int;
      // } else {
      //   return int + [...str].slice(pointIndex).join('');
      // }
    } else {
      return str.replace(/(\d)(?=(?:\d{3})+$)/g, '$1,');
    }
  }
};
export function toThousandsYI(mount: any) {
  if (mount === '-' || mount === undefined) {
    return mount;
  } else {
    const T = (mount / 10000).toFixed(2);
    return thousandString(T.toString());
  }
}

export function toYI(mount: any, toFixed?: number) {
  if (mount === '-' || mount === undefined) {
    return mount;
  } else {
    const T = (mount / 100000000).toFixed(toFixed || 3);
    return thousandStringWithPoint(T.toString());
  }
}

export function toThousandNumber(mount: any) {
  if (mount === '-') {
    return mount;
  } else {
    return thousandString(mount.toString());
  }
}

export function toPercentInterger(mount: any) {
  if (mount === '-') {
    return mount;
  } else {
    const T = `${(mount * 100).toFixed(0)}`;
    // console.log(T);
    return T;
  }
}

export function toPercentFloat(mount: any) {
  if (mount === '-') {
    return mount;
  } else {
    const T = `${Math.round(mount * 10000) / 100}`;
    // console.log(T);
    return T;
  }
}

export function roundToNearestLargeNumber(num: number) {
  const highestDigit = Math.pow(10, Math.floor(Math.log10(num) - 1));
  const roundedNum = Math.ceil(num / highestDigit) * highestDigit;

  const judgeDigit = roundedNum >= 10000 ? 10000 : 1;
  const step = (Math.ceil(roundedNum / highestDigit / 5) * highestDigit) / judgeDigit;
  const adjustedMax = step * 5;

  return { adjustedMax, step, judgeDigit };
}

export function toCustomFactor(mount: any, factor: number, toFixedNum?: number) {
  if (mount === '-' || mount === 0) {
    return mount;
  } else {
    const T = (mount / factor).toFixed(toFixedNum || 2);
    return T;
    // const T = Math.floor(mount / factor) || 0;
    // return Number(T.toString().replace(/(\d)(?=(?:\d{3})+$)/g, '$1,'));
  }
}

export function getThemeColor() {
  // only use label text color
  return process.env.VUE_APP_DARK_THEME ? '#000000' : '#FFFFFF';
}

export function getBorderThemeColor() {
  // only use label text color
  return process.env.VUE_APP_DARK_THEME ? '#000000' : '#FFFFFF';
}

export function getThemeLabelLineColor() {
  // only use label text color
  return process.env.VUE_APP_DARK_THEME ? 'rgba(0, 0, 0, 0.1)' : 'rgba(255, 255, 255, 0.1)';
}

export function getThemeItemLineColor() {
  // only use label text color
  return process.env.VUE_APP_DARK_THEME ? 'rgba(0, 0, 0, 0.08)' : 'rgba(255, 255, 255, 0.08)';
}

export function getThemeBgColor() {
  // only use label text color
  return process.env.VUE_APP_DARK_THEME ? '#FFFFFF' : '#0e2a55';
}

export function getThemeWaterMarkColor() {
  // only use label text color
  return process.env.VUE_APP_DARK_THEME ? '#6199DE' : '#6199DE';
}
