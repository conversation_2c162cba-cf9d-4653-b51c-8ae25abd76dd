export const pageSettingTemplate = {
  type: 'layout',
  model: {},
  key: 'layout_1660102159623_0',
  model_key: 'layout_1660102159623_0',
  fields: [
    {
      name: '页面名称',
      icon: 'FolderOutlined',
      type: 'input',
      rules: [
        { rule_type: 'required', type: 'string', required: true, message: '请填写正确的名称' },
      ],
      model: { attr_type: 'string' },
      options: { span: 24 },
      key: 'input_1660102165368_1',
      model_key: 'name',
      fields: [],
      conditions: [],
      model_key_prefix: '',
    },
  ],
  conditions: [],
  options: {
    label: {},
    disabled_actions: {},
    theme: 'none',
    create_text: '提交',
    update_text: '提交',
  },
  actions: [
    { key: 'create', enabled: true },
    { key: 'update', enabled: true },
    { key: 'delete', enabled: true },
    { key: 'import', enabled: true },
    { key: 'export', enabled: true },
  ],
  column_attributes: [],
};

export const componentSettingTemplate = {
  type: 'layout',
  model: {},
  key: 'layout_1660102159623_0',
  model_key: 'layout_1660102159623_0',
  fields: [
    {
      name: '自定义组件名称',
      icon: 'FolderOutlined',
      type: 'input',
      rules: [
        { rule_type: 'required', type: 'string', required: true, message: '请填写正确的名称' },
      ],
      model: { attr_type: 'string' },
      options: { span: 24 },
      key: 'input_1660102165368_1',
      model_key: 'name',
      fields: [],
      conditions: [],
      model_key_prefix: '',
    },
  ],
  conditions: [],
  options: {
    label: {},
    disabled_actions: {},
    theme: 'none',
    create_text: '提交',
    update_text: '提交',
  },
  actions: [
    { key: 'create', enabled: true },
    { key: 'update', enabled: true },
    { key: 'delete', enabled: true },
    { key: 'import', enabled: true },
    { key: 'export', enabled: true },
  ],
  column_attributes: [],
};
