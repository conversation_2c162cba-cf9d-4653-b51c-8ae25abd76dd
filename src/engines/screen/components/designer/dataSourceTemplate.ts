const statItemFields = [
  {
    key: 'input_1669364068841_4',
    name: 'key',
    type: 'input',
    model: { attr_type: 'string' },
    rules: [],
    fields: [],
    options: { span: 24 },
    model_key: 'key',
    conditions: [],
    model_key_prefix: 'item',
  },
  {
    key: 'json_1669364146307_5',
    name: 'filter',
    type: 'json',
    model: { attr_type: 'object' },
    rules: [],
    fields: [],
    options: { span: 24 },
    model_key: 'filter',
    conditions: [],
    model_key_prefix: 'item',
  },
  {
    key: 'string_array_1669364160260_6',
    name: 'joins',
    type: 'string_array',
    model: { attr_type: 'array' },
    rules: [],
    fields: [],
    options: { span: 24 },
    model_key: 'joins',
    conditions: [],
    model_key_prefix: 'item',
  },
  {
    key: 'string_array_1669364170212_7',
    name: 'left_joins',
    type: 'string_array',
    model: { attr_type: 'array' },
    rules: [],
    fields: [],
    options: { span: 24 },
    model_key: 'left_joins',
    conditions: [],
    model_key_prefix: 'item',
  },
  {
    key: 'key_layout_1669364254091_8',
    name: 'caculator',
    type: 'key_layout',
    fields: [
      {
        key: 'radio_1669364310073_9',
        name: 'caculator',
        type: 'radio',
        model: { attr_type: 'string' },
        rules: [],
        fields: [],
        options: {
          span: 24,
          select: [
            { label: 'Com::Attr::Stat::GroupCalc', value: 'Com::Attr::Stat::GroupCalc' },
            {
              label: 'Com::Attr::Stat::Caculation',
              value: 'Com::Attr::Stat::Caculation',
            },
          ],
          multiple: false,
          table_items: [],
          import_export_headers: [{ _id: '1669364314185_0' }],
          display_configurable_form: {},
        },
        model_key: 'caculator',
        conditions: [],
        model_key_prefix: 'item.caculator',
      },
      {
        key: 'condition_1669364516652_14',
        name: '条件块',
        type: 'condition',
        fields: [],
        options: { span: 24 },
        model_key: 'condition_1669364516652_14',
        conditions: [
          {
            opt: '==',
            val: 'Com::Attr::Stat::GroupCalc',
            name: 'Com::Attr::Stat::GroupCalc',
            type: 'simple',
            fields: [
              {
                key: 'radio_1669364785648_22',
                name: 'method',
                type: 'radio',
                model: { attr_type: 'string' },
                rules: [],
                fields: [],
                options: {
                  span: 24,
                  select: [
                    { label: 'group_sum', value: '' },
                    { label: 'group_count', value: '' },
                  ],
                  multiple: false,
                  table_items: [],
                  import_export_headers: [{ _id: '1669364789900_2' }],
                  display_configurable_form: {},
                },
                model_key: 'method',
                conditions: [],
                model_key_prefix: 'item.caculator',
              },
              {
                key: 'input_1669364504385_13',
                name: 'method',
                type: 'input',
                model: { attr_type: 'string' },
                rules: [],
                fields: [],
                options: { span: 24, defaultValue: 'group_sum' },
                model_key: 'method',
                conditions: [],
                model_key_prefix: 'item.caculator',
              },
              {
                key: 'string_array_1669364625022_15',
                name: 'group_attrs',
                type: 'string_array',
                model: { attr_type: 'array' },
                rules: [],
                fields: [],
                options: { span: 24 },
                model_key: 'group_attrs',
                conditions: [],
                model_key_prefix: 'item.caculator',
              },
              {
                key: 'list_1669364639723_16',
                name: 'group_dates',
                type: 'list',
                model: { attr_type: 'array' },
                rules: [],
                fields: [
                  {
                    key: 'input_1669364363511_10',
                    name: 'name',
                    type: 'input',
                    model: { attr_type: 'string' },
                    rules: [],
                    fields: [],
                    options: { span: 24 },
                    model_key: 'name',
                    conditions: [],
                    model_key_prefix: '',
                  },
                  {
                    key: 'input_1669364375363_11',
                    name: 'attr',
                    type: 'input',
                    model: { attr_type: 'string' },
                    rules: [],
                    fields: [],
                    options: { span: 24 },
                    model_key: 'attr',
                    conditions: [],
                    model_key_prefix: '',
                  },
                  {
                    key: 'radio_1669364392096_12',
                    name: 'by',
                    type: 'radio',
                    model: { attr_type: 'string' },
                    rules: [],
                    fields: [],
                    options: {
                      span: 24,
                      select: [
                        { label: 'day' },
                        { label: 'month' },
                        { label: 'week' },
                        { label: 'year' },
                        { label: 'quarter' },
                      ],
                      multiple: false,
                      table_items: [],
                      import_export_headers: [{ _id: '1669364397207_1' }],
                      display_configurable_form: {},
                    },
                    model_key: 'by',
                    conditions: [],
                    model_key_prefix: '',
                  },
                  {
                    key: 'json_1669364685394_17',
                    name: 'options',
                    type: 'json',
                    model: { attr_type: 'object' },
                    rules: [],
                    fields: [],
                    options: { span: 24 },
                    model_key: 'options',
                    conditions: [],
                    model_key_prefix: '',
                  },
                ],
                options: { span: 24, disabled_actions: {} },
                model_key: 'group_dates',
                conditions: [],
                model_key_prefix: 'item.caculator',
              },
              {
                key: 'string_array_1669364713252_18',
                name: 'stat_attrs',
                type: 'string_array',
                model: { attr_type: 'array' },
                rules: [],
                fields: [],
                options: { span: 24 },
                model_key: 'stat_attrs',
                conditions: [],
                model_key_prefix: 'item.caculator',
              },
              {
                key: 'json_1669364726025_19',
                name: 'group_options',
                type: 'json',
                model: { attr_type: 'object' },
                rules: [],
                fields: [],
                options: { span: 24 },
                model_key: 'group_options',
                conditions: [],
                model_key_prefix: 'item.caculator',
              },
              {
                key: 'json_1669364734105_20',
                name: 'stat_options',
                type: 'json',
                model: { attr_type: 'object' },
                rules: [],
                fields: [],
                options: { span: 24 },
                model_key: 'stat_options',
                conditions: [],
                model_key_prefix: 'item.caculator',
              },
              {
                key: 'input_1669364750565_21',
                name: 'jsonata',
                type: 'input',
                model: { attr_type: 'string' },
                rules: [],
                fields: [],
                options: { span: 24 },
                model_key: 'jsonata',
                conditions: [],
                model_key_prefix: 'item.caculator',
              },
            ],
            model_key: 'item.caculator.caculator',
            complex_condition: { groups: [{ items: [] }] },
          },
          {
            opt: '==',
            val: 'Com::Attr::Stat::Caculation',
            name: 'Com::Attr::Stat::Caculation',
            type: 'simple',
            fields: [
              {
                key: 'list_1669364898727_23',
                name: 'caculations',
                type: 'list',
                model: { attr_type: 'array' },
                rules: [],
                fields: [
                  {
                    key: 'input_1669364921296_24',
                    name: 'name',
                    type: 'input',
                    model: { attr_type: 'string' },
                    rules: [],
                    fields: [],
                    options: { span: 24 },
                    model_key: 'name',
                    conditions: [],
                    model_key_prefix: '',
                  },
                  {
                    key: 'input_1669364925330_25',
                    name: 'attr',
                    type: 'input',
                    model: { attr_type: 'string' },
                    rules: [],
                    fields: [],
                    options: { span: 24 },
                    model_key: 'attr',
                    conditions: [],
                    model_key_prefix: '',
                  },
                  {
                    key: 'radio_1669364940410_26',
                    name: 'method',
                    type: 'radio',
                    model: { attr_type: 'string' },
                    rules: [],
                    fields: [],
                    options: {
                      span: 24,
                      select: [
                        { label: 'sum' },
                        { label: 'count' },
                        { label: 'average' },
                        { label: 'maximum' },
                        { label: 'minimum' },
                      ],
                      multiple: false,
                      table_items: [],
                      import_export_headers: [{ _id: '1669364943237_3' }],
                      display_configurable_form: {},
                    },
                    model_key: 'method',
                    conditions: [],
                    model_key_prefix: '',
                  },
                  {
                    key: 'json_1669364963449_27',
                    name: 'filter',
                    type: 'json',
                    model: { attr_type: 'object' },
                    rules: [],
                    fields: [],
                    options: { span: 24 },
                    model_key: 'filter',
                    conditions: [],
                    model_key_prefix: '',
                  },
                  {
                    key: 'input_1669364979150_28',
                    name: 'group_attr',
                    type: 'input',
                    model: { attr_type: 'string' },
                    rules: [],
                    fields: [],
                    options: { span: 24 },
                    model_key: 'group_attr',
                    conditions: [],
                    model_key_prefix: '',
                  },
                  {
                    key: 'switch_1669364996081_29',
                    name: 'use_pluck',
                    type: 'switch',
                    model: { attr_type: 'boolean' },
                    rules: [],
                    fields: [],
                    options: { span: 24 },
                    model_key: 'use_pluck',
                    conditions: [],
                    model_key_prefix: '',
                  },
                ],
                options: { span: 24, disabled_actions: {} },
                model_key: 'caculations',
                conditions: [],
                model_key_prefix: 'item.caculator',
              },
            ],
            model_key: 'item.caculator.caculator',
            complex_condition: { groups: [{ items: [] }] },
          },
        ],
        model_key_prefix: 'item.caculator',
      },
    ],
    options: { span: 24 },
    model_key: 'caculator',
    conditions: [],
    model_key_prefix: 'item',
  },
];

export const dataSourceStatCollectionTemplate = {
  type: 'layout',
  model: { create_default_value: { item: { caculator: { by: 'day' } } } },
  fields: [
    {
      key: 'list_1669363948546_0',
      name: 'stat_condition.refs',
      type: 'list',
      model: { attr_type: 'array' },
      rules: [],
      fields: [
        {
          key: 'string_array_1669364001894_2',
          name: 'relations',
          type: 'string_array',
          model: { attr_type: 'array' },
          rules: [],
          fields: [],
          options: { span: 24 },
          model_key: 'relations',
          conditions: [],
          model_key_prefix: '',
        },
        {
          key: 'key_layout_1669364020319_3',
          name: 'item',
          type: 'key_layout',
          fields: statItemFields,
          options: { span: 24 },
          model_key: 'item',
          conditions: [],
          model_key_prefix: '',
        },
      ],
      options: { span: 24, disabled_actions: {} },
      model_key: 'params.stat_condition.refs',
      conditions: [],
      model_key_prefix: '',
    },
  ],
  actions: [
    { key: 'create', enabled: true },
    { key: 'update', enabled: true },
    { key: 'delete', enabled: true },
    { key: 'import', enabled: true },
    { key: 'export', enabled: true },
  ],
  options: {
    label: {},
    theme: { card: {}, form: {}, background: {} },
    create_text: '提交',
    update_text: '提交',
  },
  model_key: 'layout_25466781579',
  column_attributes: [],
};

export const dataSourceStatResourceTemplate = {
  type: 'layout',
  model: { create_default_value: { item: { caculator: { by: 'day' } } } },
  fields: [
    {
      key: 'list_1669363948546_0',
      name: 'stat_condition.refs',
      type: 'list',
      model: { attr_type: 'array' },
      rules: [],
      fields: [
        {
          key: 'string_array_1669364001894_2',
          name: 'relations',
          type: 'string_array',
          model: { attr_type: 'array' },
          rules: [],
          fields: [],
          options: { span: 24 },
          model_key: 'relations',
          conditions: [],
          model_key_prefix: '',
        },
        {
          key: 'key_layout_1669364020319_3',
          name: 'item',
          type: 'key_layout',
          fields: statItemFields,
          options: { span: 24 },
          model_key: 'item',
          conditions: [],
          model_key_prefix: '',
        },
      ],
      options: { span: 24, disabled_actions: {} },
      model_key: 'params.stat_condition.refs',
      conditions: [],
      model_key_prefix: '',
    },
  ],
  actions: [
    { key: 'create', enabled: true },
    { key: 'update', enabled: true },
    { key: 'delete', enabled: true },
    { key: 'import', enabled: true },
    { key: 'export', enabled: true },
  ],
  options: {
    label: {},
    theme: { card: {}, form: {}, background: {} },
    create_text: '提交',
    update_text: '提交',
  },
  model_key: 'layout_25466781579',
  column_attributes: [],
};
