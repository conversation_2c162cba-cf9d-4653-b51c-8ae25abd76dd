<script lang="ts">
import { ref, defineComponent, toRefs, computed, PropType } from 'vue';
import ComScreenDesignerDataSource from './ComScreenDesignerDataSource.vue';
import { useScreenDataFetchCollectionProvide } from '@/components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';
import ComScreenDesignerSaveAndLoad from './ComScreenDesignerSaveAndLoad.vue';
import ComScreenDesignerSaveCustomComponent from './ComScreenDesignerSaveCustomComponent.vue';
import {
  ComScreenDesignerConfig,
  useScreenDesignerConfigProvide,
} from '@/components/global/ta-component/TaBuilder/builder/useScreenDesignerConfig';
import { useRoute } from 'vue-router';
import { useContextProvide } from '@/components/global/ta-component/ta-template-form-core/useContext';
import { message } from 'ant-design-vue';

const ComScreenDesigner = defineComponent({
  name: 'ComScreenDesigner',
  components: {
    ComScreenDesignerDataSource,
    ComScreenDesignerSaveAndLoad,

    ComScreenDesignerSaveCustomComponent,
  },
  props: {
    pageSettingId: { type: Number, default: undefined },
    value: {
      type: Object as PropType<ComScreenDesignerConfig>,
      default: () => ({
        dataSource: [],
        screenConfiguration: {
          flatData: [],
        },
      }),
    },
  },
  setup(props, { emit }) {
    const route = useRoute();
    useContextProvide(ref({}) as any, route);

    const config = computed<ComScreenDesignerConfig>({
      get: () => props.value,
      set: val => emit('update:value', val),
    });

    useScreenDesignerConfigProvide(config);
    useScreenDataFetchCollectionProvide();

    const designerRef = ref<any>(null);
    const editor = ref<any>(null);

    const onSaveToHTML = (val: string) => {
      editor.value.record
        .update({ render_html: val })
        .then(() => {
          message.success('保存成功');
        })
        .catch(() => {
          message.error('保存失败');
        });
    };

    const useDark = ref(false);

    return {
      ...toRefs(props),
      config,
      designerRef,
      editor,
      onSaveToHTML,
      useDark,
    };
  },
});
export default ComScreenDesigner;
</script>

<template lang="pug">
.com-screen-designer.h-full.flex-col.flex(:class='{ dark: useDark }')
  //- | {{ config.screenConfiguration }}
  TaBuilderDesigner.flex-grow.h-0(
    v-model:value='config.screenConfiguration',
    ref='designerRef',
    @saveToHTML='onSaveToHTML'
  )
    template(#left-actions)
      .flex.items-center
        ComScreenDesignerDataSource
        ComScreenDesignerSaveAndLoad(
          ref='editor',
          :pageSettingId='pageSettingId',
          :designerRef='designerRef',
        )
        .flex.items-center.space-x-2
          div 深色
          a-switch(v-model:checked='useDark')
    template(#right-actions)
      ComScreenDesignerSaveCustomComponent(v-model:designerRef='designerRef')

</template>

<style lang="stylus" scoped>
.dark
  >>> .canvas-relative
    background rgb(2, 5, 66)
</style>
