<script lang="ts">
import { ref, defineComponent, toRefs, nextTick, getCurrentInstance } from 'vue';
import {
  useScreenDesignerConfigInject,
  ComScreenDesignerConfig,
} from '@/components/global/ta-component/TaBuilder/builder/useScreenDesignerConfig';
import { VStore } from '@/lib/vails';
import { ComAdminPageSettingsApi } from '../../apis/com/admin/page_settings.api';
import { message } from 'ant-design-vue';
import { pageSettingTemplate } from './template';
import { onMounted } from '@vue/runtime-core';
import { initRoot } from '@/components/global/ta-component/TaBuilder/designer/initRoot';

const ComScreenDesignerSaveAndLoad = defineComponent({
  name: 'ComScreenDesignerSaveAndLoad',
  components: {},
  props: {
    pageSettingId: { type: Number, default: undefined },
    designerRef: { type: Object, default: null },
  },
  setup(props) {
    const { config } = useScreenDesignerConfigInject();

    const pageSettingStore = new VStore(new ComAdminPageSettingsApi());

    const reset = () => {
      props.designerRef?.drawer?.activeItemReset?.();
    };

    onMounted(() => {
      nextTick(() => {
        reset();
        if (config) {
          if (props.pageSettingId) {
            pageSettingStore.find(props.pageSettingId).then(() => {
              config.value = pageSettingStore.record.value.formData.conf;
            });
          } else {
            pageSettingStore.record.value = pageSettingStore.new({ conf: config.value });
            config.value = pageSettingStore.record.value.formData.conf;
          }
        }
      });
    });

    const { proxy } = getCurrentInstance() as any;

    const onSave = () => {
      pageSettingStore.record.value
        .save()
        .then(() => {
          message.success('保存成功');
          nextTick(() => proxy.$forceUpdate());
        })
        .catch(() => {
          message.error('保存失败');
        });
    };

    const visibleForm = ref(false);

    const onCreate = () => {
      visibleForm.value = true;
    };

    const selectedRecordId = ref<number | null>(null);

    const tableItems = [
      {
        name: 'ID',
        type: 'string',
        search: true,
        data_index: 'id',
      },
      {
        name: '页面名称',
        type: 'string',
        search: true,
        data_index: 'name',
      },
      {
        name: '最后修改时间',
        type: 'string',
        // search: true,
        data_index: 'updated_at',
      },
    ];

    const pageSelector = ref<any>(null);

    const openPageSelector = () => pageSelector.value?.open?.();

    const onLoad = () => {
      if (config && selectedRecordId.value) {
        config.value = {
          dataSource: [],
          screenConfiguration: {
            treeStruct: { ...initRoot, children: [] },
            flatData: [initRoot],
            template: {},
          },
        };
        nextTick(() => {
          if (selectedRecordId.value) {
            pageSettingStore.find(selectedRecordId.value).then(() => {
              config.value = pageSettingStore.record.value.formData.conf;
              message.success('加载页面成功');
            });
            reset();
          }
        });
      }
    };

    const onSaveAs = () => {
      if (config) {
        pageSettingStore.record.value = pageSettingStore.new({
          conf: config.value,
          name: `${pageSettingStore.record.value.name} 副本`,
        });
        pageSettingStore.record.value.save().then(() => {
          config.value = pageSettingStore.record.value.formData.conf;
          message.success('另存为成功');
          reset();
          nextTick(() => proxy.$forceUpdate());
        });
      }
    };

    const visibleJSON = ref(false);

    return {
      ...toRefs(props),
      record: pageSettingStore.record,
      selectedRecordId,
      onSave,
      onCreate,
      visibleForm,
      pageSettingTemplate,
      tableItems,
      pageSelector,
      openPageSelector,
      onLoad,
      onSaveAs,
      visibleJSON,
    };
  },
});
export default ComScreenDesignerSaveAndLoad;
</script>

<template lang="pug">
.com-screen-designer-save-and-load.flex.space-x-2
  .flex.items-center.flex-grow
    template(v-if='record.id')
      .id {{ `[#${record.id}] ` }}
      TaDblclickInput.name.flex-grow(v-model:value='record.formData.name', @confirm='onSave')

    .no-saved.text-red-500(v-else) 未保存

  .flex.items-center
    a-button(v-if='record.id', type='primary', @click='onSave') 保存
    a-button(v-else, type='primary', @click='onCreate') 保存

  .flex.items-center
    a-button(@click='openPageSelector') 加载页面

  .flex.items-center(v-if='record.id')
    a-button(@click='onSaveAs') 另存为

  .flex.items-center
    a-button(type='primary', @click='() => visibleJSON = true') JSON

  a-modal(
    v-model:visible='visibleJSON',
    width='90vw',
  )
    TaJsonEditor(v-model:value='record.formData.conf')

  TaTemplateFormModal(
    v-model:visible='visibleForm',
    :title='record.id ? "编辑页面" : "创建页面"'
    :template='pageSettingTemplate',
    :record='record',
    @confirm='onSave',
  )

  TaApiNoDisplaySingleField(
    ref='pageSelector',
    v-model:value='selectedRecordId',
    path='/com/admin/page_settings',
    :attrs='["id", "name"]',
    recordName='加载页面',
    :tableItems='tableItems',
    @ok='onLoad'
  )
</template>

<style lang="stylus" scoped></style>
