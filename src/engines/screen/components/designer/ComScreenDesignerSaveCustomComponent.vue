<script lang="ts">
import { ref, defineComponent, toRefs, computed } from 'vue';
import { VStore } from '@/lib/vails';
import { ComAdminComponentSettingsApi } from '../../apis/com/admin/component_settings.api';
import component from '*.vue';
import { ComComponentSetting } from '../../types/model';
import { componentSettingTemplate } from './template';
import { message } from 'ant-design-vue';
import { cloneDeep } from 'lodash';
import { TaBuilderComponent } from '@/components/global/ta-component/TaBuilder/types';
import useTaBuilderProcessItem from '@/components/global/ta-component/TaBuilder/useTaBuilderProcessItem';

const ComScreenDesignerSaveCustomComponent = defineComponent({
  name: 'ComScreenDesignerSaveCustomComponent',
  components: {},
  props: {
    designerRef: { type: Object, default: null },
  },
  setup(props, { emit }) {
    const localDesignerRef = computed({
      get: () => props.designerRef,
      set: val => emit('update:designerRef', val),
    });

    const componentSettingsStore = new VStore(new ComAdminComponentSettingsApi());
    const visibleForm = ref(false);
    const onCreate = () => {
      componentSettingsStore.record.value = componentSettingsStore.new({
        conf: {
          flatData: cloneDeep(
            localDesignerRef.value.drawer.getRelativeCells(props.designerRef.drawer.activeItem),
          ),
        },
      });
      visibleForm.value = true;
    };

    const onSave = () => {
      componentSettingsStore.record.value
        .save()
        .then(() => {
          message.success('保存成功');
          // nextTick(() => proxy.$forceUpdate());
        })
        .catch(() => {
          message.error('保存失败');
        });
    };

    const selectedRecordId = ref<number | null>(null);

    const tableItems = [
      {
        name: 'ID',
        type: 'string',
        search: true,
        data_index: 'id',
      },
      {
        name: '组件名称',
        type: 'string',
        search: true,
        data_index: 'name',
      },
    ];

    const componentSelector = ref<any>(null);

    const openComponentSelector = () => componentSelector.value?.open?.();

    const onLoad = () => {
      if (selectedRecordId.value) {
        componentSettingsStore.find(selectedRecordId.value).then(() => {
          localDesignerRef.value.drawer.pasteCells(
            componentSettingsStore.record.value.formData.conf.flatData,
          );
        });
        message.success('加载页面成功');
      }
    };

    return {
      ...toRefs(props),
      onCreate,
      visibleForm,
      componentSettingTemplate,
      record: componentSettingsStore.record,
      onSave,
      selectedRecordId,
      tableItems,
      onLoad,
      componentSelector,
      openComponentSelector,
    };
  },
});
export default ComScreenDesignerSaveCustomComponent;
</script>

<template lang="pug">
.com-screen-designer-save-custom-component.space-x-2
  a-button(type='primary', @click='onCreate') 保存选中组件
  a-button(type='primary', @click='openComponentSelector') 加载组件
  TaTemplateFormModal(
    v-model:visible='visibleForm',
    :title='record.id ? "编辑页面" : "创建页面"'
    :template='componentSettingTemplate',
    :record='record',
    :z-index='99999',
    @confirm='onSave',
  )

  TaApiNoDisplaySingleField(
    ref='componentSelector',
    v-model:value='selectedRecordId',
    path='/com/admin/component_settings',
    :attrs='["id", "name"]',
    recordName='加载自定义组件',
    :tableItems='tableItems',
    @ok='onLoad'
  )
</template>

<style lang="stylus" scoped></style>
