<script lang="ts">
import { ref, defineComponent, toRefs, provide, computed } from 'vue';
import {
  useScreenDesignerConfigInject,
  ComScreenDesignerConfigDataSource,
} from '@/components/global/ta-component/TaBuilder/builder/useScreenDesignerConfig';
import { MetaUserMetaRecordsApi } from '@/components/global/ta-component/TaBuilder/apis/meta/user/meta_records.api';
import {
  dataSourceStatResourceTemplate,
  dataSourceStatCollectionTemplate,
} from './dataSourceTemplate';
import {
  useScreenDataFetchCollectionInject,
  useFetchScreenData,
} from '@/components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';

const ComScreenDesignerDataSource = defineComponent({
  name: 'ComScreenDesignerDataSource',
  components: {},
  props: {},
  setup(props) {
    const { dataResult } = useScreenDataFetchCollectionInject(props);
    const { fetchScreenData } = useFetchScreenData();
    const { config, dataSourceSelect } = useScreenDesignerConfigInject();

    const template = computed(() => ({
      type: 'layout',
      model: {},
      key: 'layout_1657204971260_0',
      model_key: 'layout_1657204971260_0',
      fields: [
        {
          name: '接口',
          icon: 'FolderOutlined',
          type: 'list',
          fields: [
            {
              name: '名称',
              icon: 'FolderOutlined',
              type: 'input',
              rules: [],
              model: {
                attr_type: 'string',
              },
              options: {
                span: 24,
              },
              key: 'input_1657206733874_2',
              model_key: 'name',
              fields: [],
              conditions: [],
              model_key_prefix: '',
            },
            {
              name: 'aggregation',
              icon: 'FolderOutlined',
              type: 'json',
              rules: [],
              model: {
                attr_type: 'object',
              },
              options: {
                span: 24,
              },
              key: 'json_1657206737190_3',
              model_key: 'aggregation',
              fields: [],
              conditions: [],
              model_key_prefix: '',
            },
            {
              name: '静态数据',
              icon: 'FolderOutlined',
              type: 'json',
              rules: [],
              model: {
                attr_type: 'object',
              },
              options: {
                span: 24,
              },
              key: 'json_1657206737190_4',
              model_key: 'static',
              fields: [],
              conditions: [],
              model_key_prefix: '',
            },
            {
              name: '接口',
              icon: 'FolderOutlined',
              type: 'input',
              rules: [],
              model: {
                attr_type: 'string',
              },
              options: {
                span: 24,
              },
              key: 'string_1657206737190_4',
              model_key: 'path',
              fields: [],
              conditions: [],
              model_key_prefix: '',
            },
            {
              name: '轮询间隔(毫秒，0为无轮询)',
              icon: 'FolderOutlined',
              type: 'number',
              rules: [],
              model: {
                attr_type: 'number',
              },
              options: {
                span: 24,
              },
              key: 'string_1657206737190_4',
              model_key: 'interval',
              fields: [],
              conditions: [],
              model_key_prefix: '',
            },
            {
              name: '接口静态参数',
              icon: 'FolderOutlined',
              type: 'json',
              rules: [],
              model: {
                attr_type: 'object',
              },
              options: {
                span: 24,
              },
              key: 'json_1657206737190_411',
              model_key: 'params',
              fields: [],
              conditions: [],
              model_key_prefix: '',
            },
            {
              key: 'dataSource',
              name: '接口参数 - 数据源',
              model_key: 'dataSource',
              type: 'select',
              options: { select: dataSourceSelect.value },
            },
            {
              key: 'dataKey',
              name: '接口参数 - 关键字',
              model_key: 'dataKey',
              type: 'textarea',
              options: {},
            },
            {
              key: '1',
              name: 'ta_stat_collection',
              type: 'label',
            },
            dataSourceStatCollectionTemplate,
            {
              key: '2',
              name: 'ta_stat_resource',
              type: 'label',
            },
            dataSourceStatResourceTemplate,
            {
              name: 'HTTP方法',
              icon: 'FolderOutlined',
              type: 'radio',
              rules: [],
              model: {
                attr_type: 'string',
              },
              options: {
                span: 24,
                select: [{ label: 'GET' }, { label: 'POST' }],
                defaultValue: 'POST',
              },
              key: 'radio_1657206737190_411',
              model_key: 'method',
              fields: [],
              conditions: [],
              model_key_prefix: '',
            },
          ],
          rules: [],
          model: {
            attr_type: 'array',
          },
          options: {
            span: 24,
            disabled_actions: {},
          },
          key: 'list_1657206731885_1',
          model_key: 'dataSource',
          conditions: [],
          model_key_prefix: '',
        },
      ],
      conditions: [],
      options: {
        label: {},
        disabled_actions: {},
        theme: {
          card: {},
          background: {},
        },
        create_text: '提交',
        update_text: '提交',
      },
    }));

    const fetchData = () => {
      if (config && dataResult) {
        fetchScreenData(config.value.dataSource, dataResult);
      }
    };

    const visible = ref(false);
    return {
      ...toRefs(props),
      config,
      template,
      visible,
      fetchData,
    };
  },
});
export default ComScreenDesignerDataSource;
</script>

<template lang="pug">
.com-screen-designer-data-source.space-x-2
  a-button(type='primary', @click='() => visible = true') 数据源
  a-button(type='primary', @click='fetchData') 获取数据
  TaNoPaddingModal(
    v-model:visible='visible'
    title='数据源',
    :bodyStyle='{ overflow: "auto", height: "fit-content" }',
    :modalContentStyle='{ "border-radius": "0.75rem", overflow: "hidden" }'
    width='50vw',
    :z-index='1000',
    @cancel='() => visible = false'
  )
    TaTemplateForm(v-model:modelValue='config', :template='template')
</template>

<style lang="stylus" scoped></style>
