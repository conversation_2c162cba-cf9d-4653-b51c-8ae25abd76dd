<script lang="ts">
import { ref, defineComponent, toRefs } from 'vue';
import { taBuilderProps } from '@/components/global/ta-component/TaBuilder/builder/TaBuilder.vue';
import ComFigmaRenderTreeNode from './ComFigmaRenderTreeNode.vue';

const ComFigmaRenderBuilder = defineComponent({
  name: 'ComFigmaRenderBuilder',
  components: {
    ComFigmaRenderTreeNode,
  },
  props: {
    ...taBuilderProps,
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
export default ComFigmaRenderBuilder;
</script>

<template lang="pug">
TaBuilder(:value='value')
  template(#default='{ componentSchema }')
    //- 拆开 root 节点，figma 不需要 root 节点
    template(v-for='schema in componentSchema.children')
      ComFigmaRenderTreeNode(:componentSchema='schema')
</template>

<style lang="stylus" scoped></style>
