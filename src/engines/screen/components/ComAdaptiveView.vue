<script lang="ts">
import { ref, defineComponent, toRefs, computed, onUnmounted, onMounted, nextTick } from 'vue';

const ComAdaptiveView = defineComponent({
  name: 'ComAdaptiveView',
  components: {},
  props: {
    root: { type: Boolean, default: true },
    byWidth: { type: Boolean, default: false },
    rootElementId: { type: String, default: 'app' },
  },
  setup(props, { emit }) {
    const clientHeight = ref(0);
    const clientWidth = ref(0);
    const containerWidth = ref(0);
    const containerHeight = ref(0);
    const container = ref<any>(null);

    // const proportion = computed(() => clientHeight.value / containerHeight.value);
    const proportion = computed(() => {
      const result = props.byWidth
        ? clientWidth.value / containerWidth.value
        : clientHeight.value / containerHeight.value;
      emit('getProportion', result);
      return result;
    });
    const getViewSize = () => {
      clientHeight.value = document.getElementById(props.rootElementId)!.clientHeight;
      clientWidth.value = document.getElementById(props.rootElementId)!.clientWidth;
      containerHeight.value = container.value?.clientHeight || 0;
      containerWidth.value = container.value?.clientWidth || 0;
    };

    window.addEventListener('resize', getViewSize);

    onMounted(() => nextTick(() => getViewSize()));

    onUnmounted(() => {
      window.removeEventListener('resize', getViewSize);
    });

    const style = computed(() => {
      const offset = (clientWidth.value - containerWidth.value * proportion.value) / 2;
      const marginLeft = offset > 0 ? offset : 0;
      emit('getMarginLeft', marginLeft);
      return {
        transform: `scale(${proportion.value})`,
        'margin-left': `${marginLeft}px`,
        'min-width': proportion.value > 0 ? '0px' : '100vw',
      };
    });

    // + 2 是给边框留位置
    const shellStyle = computed(() => ({
      // overflow: props.rootElementId === 'app' ?  : undefined,
      height:
        props.rootElementId !== 'app'
          ? `${containerHeight.value * proportion.value + 2}px`
          : undefined,
      width:
        props.rootElementId !== 'app'
          ? `${containerWidth.value * proportion.value + 2}px`
          : undefined,
    }));

    const DarkCardBg = computed(() => {
      return process.env.VUE_APP_DARK_THEME ? '#fff' : '#09183F';
    });

    return {
      ...toRefs(props),
      proportion,
      container,
      containerWidth,
      style,
      getViewSize,
      shellStyle,
      DarkCardBg,
    };
  },
});
export default ComAdaptiveView;
</script>

<template lang="pug">
.com-adaptive-view(:style='shellStyle',)
  .view-container.float-left(ref='container', :style='style', :class='{ root }')
    slot
</template>

<style lang="stylus" scoped>
.root
  min-width 100vw
  height 100%
  width 100%
.com-adaptive-view
  background v-bind(DarkCardBg)
  &::-webkit-scrollbar
    width 0 !important
    display none !important
  .view-container
    height fit-content
    width fit-content
    background v-bind(DarkCardBg) // scale > 1 时需要该背景色
    // transform-origin top center
    transform-origin left top
    &::-webkit-scrollbar
      width 0 !important
      display none !important
</style>
