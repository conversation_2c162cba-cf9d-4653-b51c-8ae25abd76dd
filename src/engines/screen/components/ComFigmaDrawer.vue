<script lang="ts">
import { ref, defineComponent, toRefs, onUnmounted, PropType, watch } from 'vue';
import { useComFigmaDrawerRenderComponentsProvide } from './useComFigmaDrawerRenderComponents';
import { onMounted, computed } from '@vue/runtime-core';
import ComFigmaRender from './ComFigmaRender.vue';
import { MessageCenter } from './MessageCenter';
import { VObject } from '@/lib/vails';
import { provideTaBuilderDesignerActiveItem } from '@/components/global/ta-component/TaBuilder/designer/useActiveItem';
import ComFigmaRenderWidgets from './ComFigmaRenderWidgets.vue';
import ComFigmaRenderBuilder from './ComFigmaRenderBuilder.vue';

import {
  TaBuilderComponent,
  TaBuilderConfiguration,
  TaBuilderConfigurationTreeStruct,
} from '@/components/global/ta-component/TaBuilder/types';
import { cloneDeep, isEqual, merge } from 'lodash';
import useTaBuilderProcessItem from '@/components/global/ta-component/TaBuilder/useTaBuilderProcessItem';

const ComFigmaDrawer = defineComponent({
  name: 'ComFigmaDrawer',
  components: {
    ComFigmaRenderBuilder,
    ComFigmaRenderWidgets,
  },
  props: {
    value: { type: Object as PropType<TaBuilderConfiguration>, default: () => ({}) },
  },
  setup(props, { emit }) {
    const flag = ref(false);

    const localValue = computed({
      get: () => {
        flag.value;
        return props.value;
      },
      set: val => emit('update:value', val),
    });

    const { renderComponents } = useComFigmaDrawerRenderComponentsProvide();
    const messageCenter = new MessageCenter();

    messageCenter.defineListener('heartbeat', (data: VObject) => {
      data.changedNodes?.forEach((info: VObject) => {
        const comp = renderComponents.value.find((comp: any) => comp.seq === info.seq);
        if (info._destroy) {
          const index = localValue.value.flatData.findIndex(
            (item: TaBuilderComponent) => item.key === comp.data.key,
          );
          localValue.value.flatData.splice(index);
          return;
        }
        comp.setInfo(info);
      });
    });

    const { activeItem, activeItemMeta } = provideTaBuilderDesignerActiveItem();

    messageCenter.defineListener('selection-change', (data: VObject) => {
      const comp = renderComponents.value.find((comp: any) => comp.seq === data.selection.seq);
      activeItem.value = comp.data;
      activeItemMeta.value.componentRef = comp.slotComponent;
    });

    onMounted(() => {
      window.addEventListener('message', messageCenter.listen);
    });

    onUnmounted(() => {
      window.removeEventListener('message', messageCenter.listen);
    });

    const getPreviewSchema = (item: TaBuilderComponent) => ({
      name: 'preview',
      key: 'preview',
      component: 'TaBuilder',
      css: 'position:relative;',
      props: {
        value: {
          flatData: [{ ...item, key: 'root' }],
          treeStruct: {},
        },
      },
    });

    const { sortByZIndex, generateTreeData } = useTaBuilderProcessItem();

    const generateStructChildren = (
      item: TaBuilderComponent,
    ): TaBuilderConfigurationTreeStruct[] => {
      return (item.children || []).map(
        (child: TaBuilderComponent): TaBuilderConfigurationTreeStruct => ({
          key: child.key,
          children: generateStructChildren(child) || [],
        }),
      );
    };

    const getTreeStruct = () => {
      const sourceTreeData = generateTreeData(cloneDeep(localValue.value.flatData))[0];

      // NOTE: 一个根节点
      const result: TaBuilderConfigurationTreeStruct = { key: 'root', children: [] };
      if (sourceTreeData) {
        result.children = generateStructChildren(sourceTreeData);
      }
      return result;
    };

    let oldValue: VObject[] = [];

    watch(
      () => localValue.value.flatData,
      (newValue: VObject[]) => {
        if (!isEqual(newValue, oldValue)) {
          oldValue = cloneDeep(newValue);
          localValue.value.treeStruct = getTreeStruct();
          flag.value = !flag.value;
        }
      },
      { deep: true },
    );

    return {
      ...toRefs(props),
      localValue,
      getPreviewSchema,
    };
  },
});
export default ComFigmaDrawer;
</script>

<template lang="pug">
.com-figma-drawer.flex-col
  //- a-button(type='primary', @click='() => count++') Add
  .config.flex
    ComFigmaRenderWidgets(v-model:value='localValue.flatData')
    TaBuilderDesignerConfig
  .relative
    ComFigmaRenderBuilder(:value='localValue')
</template>

<style lang="stylus" scoped></style>

<style lang="stylus">
.com-figma-drawer
  ._css_root
    width 100%
    min-width 50px
    height 100%
    min-height 50px
</style>
