<script lang="ts">
import { ref, defineComponent, toRefs, onUnmounted, PropType } from 'vue';
import { onMounted, computed } from '@vue/runtime-core';
import { MessageCenter } from './MessageCenter';
import { injectTaBuilderDesignerActiveItem } from '@/components/global/ta-component/TaBuilder/designer/useActiveItem';
import { widgets } from '@/components/global/ta-component/TaBuilder/widgets';
import { TaBuilderComponent } from '@/components/global/ta-component/TaBuilder/types';
import useTaBuilderProcessItem from '@/components/global/ta-component/TaBuilder/useTaBuilderProcessItem';
import { merge } from 'lodash';

const ComFigmaRenderWidgets = defineComponent({
  name: 'ComFigmaRenderWidgets',
  components: {},
  props: {
    value: { type: Array as PropType<Partial<TaBuilderComponent>[]>, required: true },
  },
  setup(props, { emit }) {
    const localValue = computed({
      get: () => props.value,
      set: val => emit('update:value', val),
    });

    const { generateKey } = useTaBuilderProcessItem();
    const { activeItem } = injectTaBuilderDesignerActiveItem();

    const generateCell = (widget: Partial<TaBuilderComponent>) => {
      const newCell = merge({}, widget, {
        name: `${widget.name} ${
          localValue.value.filter(
            (item: Partial<TaBuilderComponent>) =>
              item.key !== 'root' && item.component === widget.component,
          ).length + 1
        }`,
        key: generateKey(),
        parent_key: 'root',
        cssc: {
          zIndex: localValue.value.length,
        },
      });
      localValue.value.push(newCell);
      if (activeItem) {
        activeItem.value = newCell;
      }
    };

    const onClickWidget = (widget: Partial<TaBuilderComponent>) => {
      generateCell(widget);
    };

    return {
      ...toRefs(props),
      widgets,
      onClickWidget,
    };
  },
});
export default ComFigmaRenderWidgets;
</script>

<template lang="pug">
.com-figma-render-widgets.grid.grid-cols-2.gap-2.w-60
  .widget.flex-center.border.border-gray-400.cursor-pointer(
    v-for='widget in widgets',
    @click='onClickWidget(widget)'
  )
    | {{ widget.name }}

</template>

<style lang="stylus" scoped></style>
