<template lang="pug">
.com_base_poly_line.w-full.h-full
  ComScreenBaseLine(
    :data='data', 
    :xAxisLabel='xAxisLabel' 
    :showUnit='showUnit' 
    :showLabel='true'
    :unit='unit',
    :unitPadding='unitPadding'
    :showLengend='showLengend'
    :lengendPosition='lengendPosition'
    :customizeLineColor='customizeLineColor'
    )
</template>
<!-- 带数据折线图 -->
<script lang="ts">
import { PropType, defineComponent, toRefs } from 'vue';
import ComScreenBaseLine from './ComScreenBaseLine.vue';
import { BaseData } from '../baseUtils/baseChat';

const ComBasePolyLabelLine = defineComponent({
  name: 'ComBasePolyLabelLine',
  components: {
    ComScreenBaseLine,
  },
  props: {
    data: { type: Array as PropType<BaseData[]>, default: () => [] },
    xAxisLabel: { type: Array as PropType<string[]>, default: () => [] },
    showUnit: { type: Boolean, default: false },
    unit: { type: String, default: () => '' },
    unitPadding: { type: Array as PropType<number[]>, default: () => [0, 0, 0, 0] },
    showLengend: { type: Boolean, default: false },
    lengendPosition: { type: Array, default: () => [0, 0, 0, 0] },
    // 自定义色彩，【或者可以支持组选新颜色?】
    customizeLineColor: { type: Array, default: () => [] },
    showLabel: {
      type: Boolean,
      default: () => false,
    },
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
export default ComBasePolyLabelLine;
</script>
<style lang="stylus" scoped></style>
