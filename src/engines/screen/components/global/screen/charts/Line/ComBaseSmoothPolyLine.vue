<template lang="pug">
.com_base_poly_line.w-full.h-full
  ComScreenBaseLine(
    v-bind='$props'
    :showUnit='true' 
    :isSmooth='true' 
  )
</template>
<!-- 基础平滑折线图 -->
<script lang="ts">
import { PropType, defineComponent, toRefs } from 'vue';
import ComScreenBaseLine from './ComScreenBaseLine.vue';
import { BaseData } from '../baseUtils/baseChat';

const ComBaseSmoothPolyLine = defineComponent({
  name: 'ComBaseSmoothPolyLine',
  components: {
    ComScreenBaseLine,
  },
  props: {
    data: { type: Array as PropType<BaseData[]>, default: () => [] },
    xAxisLabel: { type: Array as PropType<string[]>, default: () => [] },
    unit: { type: String, default: () => '' },
    unitPadding: { type: Array as PropType<number[]>, default: () => [0, 0, 0, 0] },
    showLengend: { type: Boolean, default: false },
    lengendPosition: { type: Array, default: () => [0, 0, 0, 0] },
    customizeLineColor: { type: Array, default: () => [] },
    showLabel: {
      type: Boolean,
      default: () => false,
    },
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
export default ComBaseSmoothPolyLine;
</script>
<style lang="stylus" scoped></style>
