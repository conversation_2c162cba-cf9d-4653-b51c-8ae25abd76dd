<template lang="pug">
.w-full.h-full
  TaScreenEchartBase(:options='lineOption' ref='echartsRef')
</template>
<script lang="ts">
import { VObject } from '@/lib/vails';
import { PropType, computed, defineComponent, ref } from 'vue';
import echarts from 'echarts';
import { BaseData, lineColor, lineAreaColor } from '../baseUtils/baseChat';
import { getThemeColor, getThemeLabelLineColor } from '@/engines/screen/utils/util';

type VFn = () => void;

const ComScreenBaseLine = defineComponent({
  name: 'ComScreenBaseLine',
  props: {
    // 需要merge的option - 高级功能
    mergeOption: { type: Object, default: () => {} },
    // 是否完全替换
    isOnlyMerge: { type: Boolean, default: false },

    // 自定义色彩，【或者可以支持组选新颜色?】
    customizeLineColor: { type: Array, default: () => [] },
    customizeAreaColor: { type: Array, default: () => [] },

    // ----------------------------------------------

    // 是否显示图例
    showLengend: { type: Boolean, default: false },
    // 图表数据 seriesData 为纯数据项 , seriesName 对应的每一例数据代表的数据别称
    data: { type: Array as PropType<BaseData[]>, default: () => [] },
    // 图例位置 top right bottom right
    lengendPosition: { type: Array, default: () => ['top', 0, 0, 'center'] },
    // X轴显示名称，为空则不显示X轴标
    xAxisLabel: { type: Array as PropType<string[]>, default: () => [] },
    // 是否显示Y轴
    showYAxis: { type: Boolean, default: true },
    // Y轴数据，为2则代表有两条Y轴，最大为2
    yAxisNum: { type: Number as PropType<1 | 2>, default: () => 1 },
    // 是否显示Y轴单位
    showUnit: { type: Boolean, default: false },
    // 主轴单位
    unit: { type: String, default: () => '' },
    // 副轴单位
    subUnit: { type: String, default: () => '' },
    // 主轴单位位置
    unitPadding: { type: Array as PropType<number[]>, default: () => [0, 0, 0, 0] },
    // 主轴单位位置
    subUnitPadding: { type: Array as PropType<number[]>, default: () => [0, 0, 0, 0] },
    // 拆线点形状
    symbol: {
      type: [String, Function] as PropType<string | VFn>,
      default: () => 'circle',
    },
    // 折线点尺寸
    symbolSize: {
      type: [Number, Array, Function] as PropType<number | [number, number] | VFn>,
      default: () => 0,
    },
    // 是否使用平滑曲线
    isSmooth: {
      type: Boolean,
      default: () => false,
    },
    // 是否要显示数据点标注
    showLabel: {
      type: Boolean,
      default: () => false,
    },
    // 拆线区域颜色填充
    showArea: {
      type: Boolean,
      default: () => false,
    },
    // 是否要展示tooltip
    showTootip: {
      type: Boolean,
      default: false,
    },
    // 图例文字颜色
    legendTextColor: {
      type: String,
      default: () => getThemeColor(),
    },
    // 坐标轴颜色
    axisColor: {
      type: String,
      default: () => getThemeColor(),
    },
    // 坐标轴分割线颜色
    splitLineColor: {
      type: String,
      default: () => getThemeLabelLineColor(),
    },
    // 坐标颜色
    labelColor: {
      type: String,
      default: () => getThemeColor(),
    },
    // 图位置 top right bottom right
    gridPosition: {
      type: Array,
      default: () => ['10%', '10%', '10%', '10%'],
    },
    unitTitle: {
      type: String,
      default: '单位',
    },
    // 坐标轴类型。
    yAxisType: {
      type: String as PropType<'value' | 'category' | 'time' | 'log'>,
      default: () => 'value',
    },
    // yAxis最大值
    yAxisMax: {
      type: [String, Number],
      default: () => '',
    },
    yAxisMin: {
      type: [String, Number],
      default: () => '',
    },
    yAxisMinInterval: {
      type: String || Number,
      default: '',
    },
  },
  setup(props) {
    const echartsRef = ref();
    const mergeBaseOption = (options: VObject, onlyMerge: boolean) => {
      echartsRef.value?.mergeOption(options, onlyMerge);
    };
    const lineOption = computed(() => {
      const [top, right, bottom, left] = props.lengendPosition;
      const positionOpt = { top, right, bottom, left };
      const finalLineColor = [...props.customizeLineColor, ...lineColor];
      const finalAreaColor = [...props.customizeAreaColor, ...lineAreaColor];
      const options = {
        tooltip: {
          show: props.showTootip,
          trigger: 'axis',
          backgroundColor: 'white',
          textStyle: {
            color: 'black',
          },
          formatter: (record: VObject) => {
            const getChildren = () => {
              return record
                .map((item: any) => {
                  const color = item.color?.colorStops
                    ? item.color?.colorStops[1].color
                    : item.color;
                  return `<div class="flex">
                      <span style="display:flex;margin-right:5px;margin-top:5.5px;border-radius:10px;width:10px;height:10px;background-color:${color};"></span>
                      <div class="mr-1">${item.seriesName}: </div>
                      <div>
                        ${item.data}${props.unit}
                      </div>
                    </div>`;
                })
                .join(' ');
            };
            return `<div>
                <div>${record[0].name}</div>
                ${getChildren()}
              </div>`;
          },
        },
        ...(props.showLengend
          ? {
              legend: {
                show: true,
                data: props.data.map((el: any) => el.seriesName),
                itemHeight: 2,
                itemWidth: 8,
                icon: 'roundRect',
                textStyle: {
                  color: props.legendTextColor,
                },
                ...positionOpt,
              },
            }
          : { legend: { show: false } }),
        xAxis: {
          type: 'category',
          data: props.xAxisLabel,
          axisTick: {
            show: false,
          },
          axisLine: {
            lineStyle: {
              color: props.axisColor,
              width: 0,
            },
          },
        },
        grid: {
          top: props.gridPosition[0] || 0,
          right: props.gridPosition[1] || 0,
          bottom: props.gridPosition[2] || 0,
          left: props.gridPosition[3] || 0,
        },
        yAxis: new Array(props.yAxisNum).fill('').map((el: any, index: number) => {
          return {
            type: props.yAxisType || 'value',
            name: props.showUnit
              ? `${props.unitTitle}：` + (index === 0 ? props.unit : props.subUnit)
              : undefined,
            nameTextStyle: {
              padding: [...(index === 0 ? props.unitPadding : props.subUnitPadding)],
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              lineStyle: {
                color: props.splitLineColor,
              },
              show: index < 1,
            },
            axisLine: {
              lineStyle: {
                color: props.axisColor,
                width: 0,
              },
            },
            ...(props?.yAxisMinInterval ? { minInterval: props?.yAxisMinInterval } : {}),
            yAxisIndex: el.yAxisIndex !== '' ? el.yAxisIndex : index,
            show: props.showYAxis,
            ...(props.yAxisMax
              ? {
                  max: props.yAxisMax,
                }
              : {}),
            ...(props.yAxisMin
              ? {
                  min: props.yAxisMin,
                }
              : {}),
          };
        }),

        series: props.data.map((el: any, index: number) => {
          return {
            data: el.seriesData,
            name: el.seriesName,
            type: 'line',
            symbol: props.symbol || 'circle',
            symbolSize: props.symbolSize || undefined,
            smooth: props.isSmooth || false,
            ...(props.showLabel
              ? {
                  label: {
                    show: true,
                    color: props.labelColor,
                    position: 'top',
                  },
                }
              : {}),
            lineStyle: {
              type: 'solid',
            },
            itemStyle: {
              color: finalLineColor[index],
            },
            ...(props.showArea
              ? {
                  areaStyle: {
                    color: {
                      type: 'linear',
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      colorStops: [
                        {
                          offset: 1,
                          color: 'rgba(86, 204, 242, 0.00)', // 0% 处的颜色
                        },
                        {
                          offset: 0,
                          color: finalAreaColor[index], // 100% 处的颜色
                        },
                      ],
                    },
                  },
                }
              : {}),
            yAxisIndex: el.yAxisIndex !== '' ? el.yAxisIndex : props.yAxisNum > 1 ? index : 0,
          };
        }),
      };
      if (props?.mergeOption && Object.keys(props?.mergeOption).length > 0) {
        return (echarts as any).util.merge(options, props.mergeOption, props.isOnlyMerge);
      } else {
        return options;
      }
    });
    return {
      lineOption,
      echartsRef,
      mergeBaseOption,
    };
  },
});
export default ComScreenBaseLine;
</script>
<style lang="stylus" scoped></style>
