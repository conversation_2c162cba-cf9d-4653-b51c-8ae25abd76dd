<template lang="pug">
.com_base_poly_line.w-full.h-full
  ComScreenBaseLine(
    :data='data', 
    :xAxisLabel='xAxisLabel' 
    :showLengend='showLengend'
    :lengendPosition='lengendPosition'
    :symbol='symbol'
    :symbolSize='symbolSize'
    :showYAxis='false'
    :customizeLineColor='customizeLineColor'
  )
</template>
<!-- 带标记点折线图 -->
<script lang="ts">
import { PropType, defineComponent, toRefs } from 'vue';
import ComScreenBaseLine from './ComScreenBaseLine.vue';
import { BaseData } from '../baseUtils/baseChat';

type fn = () => void;
const ComBaseMarkPointLine = defineComponent({
  name: 'ComBaseMarkPointLine',
  components: {
    ComScreenBaseLine,
  },
  props: {
    data: { type: Array as PropType<BaseData[]>, default: () => [] },
    xAxisLabel: { type: Array as PropType<string[]>, default: () => [] },
    showLengend: { type: Boolean, default: false },
    lengendPosition: { type: Array, default: () => [0, 0, 0, 0] },
    showYAxis: { type: Boolean, default: true },
    symbol: {
      type: [String, Function] as PropType<string | fn>,
      default: () => 'none',
    },
    // 折线点尺寸
    symbolSize: {
      type: [Number, Array, Function] as PropType<number | [number, number] | fn>,
      default: () => 6,
    },
    // 自定义色彩，【或者可以支持组选新颜色?】
    customizeLineColor: { type: Array, default: () => [] },
    showLabel: {
      type: Boolean,
      default: () => false,
    },
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
export default ComBaseMarkPointLine;
</script>
<style lang="stylus" scoped></style>
