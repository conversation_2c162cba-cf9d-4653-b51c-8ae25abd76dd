<template lang="pug">
.com_base_poly_line.w-full.h-full
  ComScreenBaseLine(
    v-bind='$props'
    :showUnit='true' 
    :showArea='true',
    :isSmooth='true'
  )
</template>
<!-- 基础平滑面积图 -->
<script lang="ts">
import { PropType, defineComponent, toRefs } from 'vue';
import ComScreenBaseLine from './ComScreenBaseLine.vue';
import { BaseData } from '../baseUtils/baseChat';

type VFn = () => void;

const ComBaseSmoothAreaLine = defineComponent({
  name: 'ComBaseSmoothAreaLine',
  components: {
    ComScreenBaseLine,
  },
  props: {
    data: { type: Array as PropType<BaseData[]>, default: () => [] },
    xAxisLabel: { type: Array as PropType<string[]>, default: () => [] },
    unit: { type: String, default: () => '' },
    unitPadding: { type: Array as PropType<number[]>, default: () => [0, 0, 0, 0] },
    showLengend: { type: Boolean, default: false },
    lengendPosition: { type: Array, default: () => [0, 0, 0, 0] },
    customizeLineColor: { type: Array, default: () => [] },
    customizeAreaColor: { type: Array, default: () => [] },
    unitTitle: {
      type: String,
      default: '单位',
    },
    gridPosition: {
      type: Array,
      default: () => ['10%', '10%', '10%', '10%'],
    },
    showTootip: {
      type: Boolean,
      default: false,
    },
    showLabel: {
      type: Boolean,
      default: () => false,
    },
    symbol: {
      type: [String, Function] as PropType<string | VFn>,
      default: () => 'none',
    },
    // 折线点尺寸
    symbolSize: {
      type: [Number, Array, Function] as PropType<number | [number, number] | VFn>,
      default: () => 6,
    },
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
export default ComBaseSmoothAreaLine;
</script>
<style lang="stylus" scoped></style>
