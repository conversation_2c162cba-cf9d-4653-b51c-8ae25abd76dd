<template lang="pug">
.com_base_poly_line.w-full.h-full
  ComScreenBaseLine(
    v-bind='$props'
    :showUnit='true' 
    :showArea='true'
  )
</template>
<!-- 基础面积图 -->
<script lang="ts">
import { PropType, defineComponent, toRefs } from 'vue';
import ComScreenBaseLine from './ComScreenBaseLine.vue';
import { BaseData } from '../baseUtils/baseChat';
import { getThemeColor, getThemeLabelLineColor } from '@/engines/screen/utils/util';

const ComBaseAreaLine = defineComponent({
  name: 'ComBaseAreaLine',
  components: {
    ComScreenBaseLine,
  },
  props: {
    customizeLineColor: { type: Array, default: () => [] },
    customizeAreaColor: { type: Array, default: () => [] },
    data: { type: Array as PropType<BaseData[]>, default: () => [] },
    xAxisLabel: { type: Array as PropType<string[]>, default: () => [] },
    unit: { type: String, default: () => '' },
    unitPadding: { type: Array as PropType<number[]>, default: () => [0, 0, 0, 0] },
    showLengend: { type: Boolean, default: false },
    lengendPosition: { type: Array, default: () => [0, 0, 0, 0] },
    showTootip: {
      type: Boolean,
      default: false,
    },
    legendTextColor: {
      type: String,
      default: () => getThemeColor(),
    },
    axisColor: {
      type: String,
      default: () => getThemeColor(),
    },
    splitLineColor: {
      type: String,
      default: () => getThemeLabelLineColor(),
    },
    labelColor: {
      type: String,
      default: () => getThemeColor(),
    },
    gridPosition: {
      type: Array,
      default: () => ['10%', '10%', '10%', '10%'],
    },
    // 是否要显示数据点标注
    showLabel: {
      type: Boolean,
      default: () => false,
    },
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
export default ComBaseAreaLine;
</script>
<style lang="stylus" scoped></style>
