<template lang="pug">
.com_base_poly_line.w-full.h-full
  ComScreenBaseLine(
    v-bind='$props',
    :showUnit='true' 

  )
</template>
<!-- 基础折线图 -->
<script lang="ts">
import { PropType, defineComponent, toRefs } from 'vue';
import ComScreenBaseLine from './ComScreenBaseLine.vue';
import { BaseData } from '../baseUtils/baseChat';

const ComBasePolyLine = defineComponent({
  name: 'ComBasePolyLine',
  components: {
    ComScreenBaseLine,
  },
  props: {
    data: { type: Array as PropType<BaseData[]>, default: () => [] },
    xAxisLabel: { type: Array as PropType<string[]>, default: () => [] },
    unit: { type: String, default: () => '' },
    unitPadding: { type: Array as PropType<number[]>, default: () => [0, 0, 0, 0] },
    showLengend: { type: Boolean, default: false },
    lengendPosition: { type: Array, default: () => [0, 0, 0, 0] },
    customizeLineColor: { type: Array, default: () => [] },
    showLabel: {
      type: Boolean,
      default: () => false,
    },
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
export default ComBasePolyLine;
</script>
<style lang="stylus" scoped></style>
