<template lang="pug">
.com_base_poly_line.w-full.h-full
  ComScreenBaseLine(
    v-bind='$props'
    :showUnit='true' 
    :showLengend='true'
    :showArea='true'
  )
</template>
<!-- 堆叠面积图 -->
<script lang="ts">
import { PropType, defineComponent, toRefs } from 'vue';
import ComScreenBaseLine from './ComScreenBaseLine.vue';
import { BaseData } from '../baseUtils/baseChat';

const ComBaseStackAreaPolyLine = defineComponent({
  name: 'ComBaseStackAreaPolyLine',
  components: {
    ComScreenBaseLine,
  },
  props: {
    data: { type: Array as PropType<BaseData[]>, default: () => [] },
    xAxisLabel: { type: Array as PropType<string[]>, default: () => [] },
    unit: { type: String, default: () => '' },
    unitPadding: { type: Array as PropType<number[]>, default: () => [0, 0, 0, 0] },
    showLengend: { type: Boolean, default: false },
    lengendPosition: { type: Array, default: () => [0, 0, 0, 0] },
    customizeLineColor: { type: Array, default: () => [] },
    customizeAreaColor: { type: Array, default: () => [] },
    showLabel: {
      type: Boolean,
      default: () => false,
    },
  },

  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
export default ComBaseStackAreaPolyLine;
</script>
<style lang="stylus" scoped></style>
