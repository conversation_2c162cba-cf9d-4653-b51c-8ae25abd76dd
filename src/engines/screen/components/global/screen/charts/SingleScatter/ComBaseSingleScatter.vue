<template lang="pug">
.com_base_single_scatter.w-full.h-full
  ComScreenSingleScatter(
    v-bind='$props'
  )
</template>
<!-- 玫瑰图 -->
<script lang="ts">
import { PropType, defineComponent, toRefs } from 'vue';
import ComScreenSingleScatter from './ComScreenSingleScatter.vue';
import { BaseData, RadarMax, lineColor, ScatterData } from '../baseUtils/baseChat';

const ComBaseSingleScatter = defineComponent({
  name: 'ComBaseSingleScatter',
  components: {
    ComScreenSingleScatter,
  },
  props: {
    // 需要merge的option - 高级功能
    mergeOption: { type: Object, default: () => {} },
    // 是否完全替换
    isOnlyMerge: { type: Boolean, default: false },

    // 自定义色彩，【或者可以支持组选新颜色?】
    customizeAreaColor: { type: Array, default: () => [] },

    // 图表数据 seriesData 为纯数据项 , seriesName 对应的每一例数据代表的数据别称
    data: {
      type: Array as PropType<ScatterData[]>,
      default: () => [],
    },
    showTooltip: { type: Boolean, default: () => true },
    current: {
      type: Object,
      default: () => {},
    },
    currentText: {
      type: String,
      default: () => '所处阶段',
    },
    minSize: {
      type: Number,
      default: () => 3,
    },
    maxSize: {
      type: Number,
      default: () => 64,
    },
    currentTextColor: {
      type: String,
      default: () => '',
    },
    currentTextPadding: {
      type: Array,
      default: () => [0, 0, 0, 0],
    },
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
export default ComBaseSingleScatter;
</script>
<style lang="stylus" scoped></style>
