<template lang="pug">
.w-full.h-full
  TaScreenEchartBase(:options='option' ref='echartsRef')
</template>
<script lang="tsx">
import { VObject } from '@/lib/vails';
import { PropType, computed, defineComponent, ref, toRefs } from 'vue';
import { ScatterData, RadarMax, lineColor, scatterAreaColor } from '../baseUtils/baseChat';
import echarts from 'echarts';
import { getThemeColor, getThemeLabelLineColor } from '../../../../../utils/util';

const ComScreenSingleScatter = defineComponent({
  name: 'ComScreenSingleScatter',
  components: {},
  props: {
    // 需要merge的option - 高级功能
    mergeOption: { type: Object, default: () => { } },
    // 是否完全替换
    isOnlyMerge: { type: Boolean, default: false },

    // 自定义色彩，【或者可以支持组选新颜色?】
    customizeAreaColor: { type: Array, default: () => [] },

    // 图表数据 seriesData 为纯数据项 , seriesName 对应的每一例数据代表的数据别称
    data: {
      type: Array as PropType<ScatterData[]>,
      default: () => [],
    },
    showTooltip: { type: Boolean, default: () => true },
    current: {
      type: Object,
      default: () => { },
    },
    currentText: {
      type: String,
      default: () => '所处阶段',
    },
    currentTextColor: {
      type: String,
      default: getThemeColor(),
    },
    minSize: {
      type: Number,
      default: () => 3,
    },
    maxSize: {
      type: Number,
      default: () => 64,
    },
    currentTextPadding: {
      type: Array,
      default: () => [0, 0, 0, 0],
    },
  },
  setup(props: any) {
    const echartsRef = ref();
    const mergeBaseOption = (options: VObject, onlyMerge: boolean) => {
      echartsRef.value?.mergeOption(options, onlyMerge);
    };
    const option = computed(() => {
      const finalAreaColor = [...props.customizeAreaColor, ...scatterAreaColor];
      const options = {
        ...(props.showTooltip
          ? {
            tooltip: {
              trigger: 'axis',
              // formatter: (opts: any) => {
              //   const formatterText = [...opts.value]
              //     .map((el: any, index: number) => {
              //       const name = props.radarMax?.[index]?.name || '';
              //       return `${name}: ${el}`;
              //     })
              //     .join('<br />');
              //   return `${formatterText}`;
              // },
            },
          }
          : {}),

        grid: {
          left: 2,
          bottom: 10,
          right: 10,
          top: 10,
          containLabel: true,
        },
        color: finalAreaColor,
        title: [...props.data].map((el: any, index: number) => {
          return {
            textBaseline: 'middle',
            top: (index * 70) / props.data.length + 12 + '%',
            text: el.seriesName,
            textStyle: {
              color: getThemeColor(),
              fontSize: 14,
              fontWeight: 400,
            },
          };
        }),
        singleAxis: [...props.data].map((el: any, index) => {
          return {
            left: 80,
            right: 50,
            type: 'category',
            boundaryGap: false,
            data: el.seriesData.map((item: any) => item?.name),
            top: (index * 70) / props.data.length + 14 + '%',
            height: 0,
            axisLabel: {
              // interval: 2,
              color: getThemeColor(),
              fontSize: 14,
              margin: 45,
              fontWeight: 400,
              rotate: 25,
              show: index === props.data.length - 1,
            },
            axisLine: {
              lineStyle: {
                color: getThemeLabelLineColor(),
                width: 1,
                // type: 'dashed',
              },
            },
            splitLine: {
              show: false,
            },
            axisTick: {
              inside: true,
              show: index > props.data.length - 1,
              length: 4,
            },
          };
        }),
        series: [...props.data].map((el: any, index: number) => {
          const arr: any[] = [...props.data]
            .map((el: any) => {
              return el?.seriesData.map((item: any) => item?.value);
            })
            .flat();
          return {
            singleAxisIndex: index,
            coordinateSystem: 'singleAxis',
            type: 'scatter',
            symbolOffset: [0, 0],
            data: el?.seriesData.map((item: any, index: number) => {
              const getLabelFunc = () => {
                const curLabel = props?.current?.[el?.seriesName];
                if (curLabel === item?.name) {
                  return {
                    label: {
                      show: true,
                      formatter: () => {
                        return `{num|${item?.value}}\n{name|${props?.currentText}}`;
                      },
                      color: getThemeColor(),
                      fontWeight: 400,
                      rich: {
                        num: {
                          fontSize: 14,
                          color: getThemeColor(),
                          fontWeight: 400,
                          align: 'center',
                          padding: [18, 0, 4, 0],
                        },
                        name: {
                          fontSize: 14,
                          color: props.currentTextColor,
                          fontWeight: 400,
                          align: 'center',
                          padding: props.currentTextPadding,
                        },
                      },
                    },
                  };
                } else {
                  return {
                    label: {
                      show: true,
                      formatter: () => {
                        return item.value;
                      },
                      color: getThemeColor(),
                      fontWeight: 400,
                    },
                  };
                }
              };

              return {
                value: [index, item?.value],
                ...getLabelFunc(),
              };
            }),
            symbolSize: (dataItem: any) => {
              const max = [...arr].sort(function (a, b) {
                return a - b;
              })[arr.length - 1];
              let dataSize = (dataItem[1] / max) * props.maxSize;
              dataSize = dataSize > props.maxSize ? props.maxSize : dataSize;
              dataSize = dataSize === 0 ? 0 : dataSize;
              if (dataSize <= props.minSize && dataSize !== 0) {
                dataSize = props.minSize;
              }
              return dataSize;
            },
          };
        }),
      };
      if (props?.mergeOption && Object.keys(props?.mergeOption).length > 0) {
        return (echarts as any).util.merge(options, props.mergeOption, props.isOnlyMerge);
      } else {
        return options;
      }
    });
    return {
      ...toRefs(props),
      option,
      echartsRef,
    };
  },
});
export default ComScreenSingleScatter;
</script>
<style lang="stylus" scoped></style>
../baseUtils/themeColor
