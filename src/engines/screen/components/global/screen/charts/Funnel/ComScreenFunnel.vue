<template lang="pug">
.w-full.h-full
  TaScreenEchartBase(:options='funnelOption' ref='echartsRef')
</template>

<script lang="tsx">
import { Prop, PropType, computed, defineComponent, ref } from 'vue';
import { lineAreaColor, BaseFunnelData, FunnelData } from '../baseUtils/baseChat';
import { VObject } from '@/lib/vails';
import { getThemeColor } from '@/engines/screen/utils/util';

export default defineComponent({
  name: 'ComScreenFunnel',
  props: {
    // 需要merge的option - 高级功能
    mergeOption: { type: Object, default: () => {} },
    // 是否完全替换
    isOnlyMerge: { type: Boolean, default: false },
    customizeAreaColor: { type: Array, default: () => [] },
    data: {
      type: Array as PropType<FunnelData[]>,
      default: () => [
        {
          seriesName: 'funnel-1',
          seriesData: [
            { value: 140, name: '图例' },
            { value: 180, name: '咨询' },
            { value: 100, name: '订单' },
            { value: 60, name: '点击' },
            { value: 30, name: '展现' },
          ],
        },
      ],
    },
    // 是否显示图例
    showLengend: { type: Boolean, default: true },
    // 图例位置 top right bottom right
    lengendPosition: { type: Array, default: () => ['center', 0, 0, 'auto'] },
    // 平底还是尖底的漏斗图
    bottomTye: {
      type: String as PropType<'flat' | 'tip'>,
      default: 'flat',
    },
    // FunnelOrient 漏斗图水平还是垂直
    funnelOrient: {
      type: String as PropType<'horizontal' | 'vertical'>,
      default: () => 'vertical',
    },
    // 图例水平还是垂直
    lengendOrient: {
      type: String as PropType<'horizontal' | 'vertical'>,
      default: () => 'vertical',
    },
    insideLabelWithValue: {
      type: Boolean,
      default: () => true,
    },
    insideLabelWithValueBrackets: {
      type: Boolean,
      default: () => false,
    },
    insideDigit: {
      type: String,
      default: () => '个',
    },
    showOutSideLabel: {
      type: Boolean,
      default: () => true,
    },
    // 数据是否排序
    isSort: {
      type: Boolean,
      default: () => true,
    },
    // 定义minSize,maxSize

    minSize: {
      type: String,
      default: () => '',
    },
    maxSize: {
      type: String,
      default: () => '',
    },
    sortType: {
      type: String as PropType<'ascending' | 'descending' | 'none'>,
      default: () => 'none',
    },
    funnelWidth: {
      type: Number,
      default: () => 80,
    },
    showOutsideCurrentStage: {
      type: Boolean,
      default: () => false,
    },
    showOutsideCurrentStageText: {
      type: String,
      default: () => 'text',
    },
  },
  setup(props) {
    const echartsRef = ref();
    const mergeBaseOption = (options: VObject, onlyMerge: boolean) => {
      echartsRef.value?.mergeOption(options, onlyMerge);
    };

    const funnelOption = computed(() => {
      const finalAreaColor = [...props.customizeAreaColor, ...lineAreaColor];

      const rawData = props.isSort
        ? (props.data[0].seriesData as BaseFunnelData[]).sort((a, b) => b.value - a.value)
        : props.data[0].seriesData;
      const data = rawData.map((item, index) => {
        return {
          ...item,
          itemStyle: {
            color: finalAreaColor[index],
          },

          ...(index > 0
            ? {
                percent: ((rawData[index].value / rawData[index - 1].value) * 100).toFixed(0),
              }
            : {}),
        };
      });
      const minData = Math.min(...data.map((item: any) => item.value));
      const [top, right, bottom, left] = props.lengendPosition;
      const legendOpt = { top, right, bottom, left };
      return {
        ...(props.showLengend
          ? {
              legend: {
                data: data.map((item: any) => item.name),
                itemHeight: 8,
                itemWidth: 8,
                icon: 'roundRect',
                textStyle: {
                  color: getThemeColor(),
                },
                orient: props.lengendOrient,
                ...legendOpt,
              },
            }
          : {}),
        series: [
          {
            name: 'Funnel',
            type: 'funnel',
            left: 'center',
            top: 20,
            bottom: 20,
            width: props.funnelWidth + '%',
            min: props.bottomTye === 'flat' ? minData : 0,
            minSize: props.minSize ? props.minSize : props.bottomTye === 'flat' ? '40%' : '0%',
            orient: props.funnelOrient,
            maxSize: props.maxSize ? props.maxSize : '90%',
            gap: 1,
            label: {
              show: true,
              position: 'inside',
              color: getThemeColor(),
              formatter: (params: any) => {
                const { name, value } = params;
                if (props.insideLabelWithValue) {
                  if (props.insideLabelWithValueBrackets) {
                    return `${name}(${value}${props.insideDigit})`;
                  } else {
                    return `${name}: ${value}${props.insideDigit}`;
                  }
                } else {
                  return name;
                }
              },
            },
            labelLine: {
              length: 10,
              lineStyle: {
                width: 1,
                type: 'solid',
              },
            },
            itemStyle: {
              borderWidth: 0,
            },
            emphasis: {
              label: {
                fontSize: 20,
              },
            },
            data,
            sort: props.sortType,
          },
          ...(props.showOutSideLabel
            ? [
                {
                  name: 'Funnel',
                  type: 'funnel',
                  left: 'center',
                  top: 20,
                  bottom: 20,
                  width: props.funnelWidth + '%',
                  silent: true,
                  min: props.bottomTye === 'flat' ? minData : 0,
                  minSize: props.minSize
                    ? props.minSize
                    : props.bottomTye === 'flat'
                    ? '40%'
                    : '0%',
                  maxSize: props.maxSize ? props.maxSize : '90%',
                  gap: 1,
                  orient: props.funnelOrient,
                  label: {
                    show: true,
                    position: props.funnelOrient === 'vertical' ? 'rightTop' : 'leftTop',
                    color: getThemeColor(),
                    formatter: (params: any) => {
                      if (params.data.percent) {
                        return `转化率：${params.data.percent}%`;
                      } else {
                        return '';
                      }
                    },
                  },
                  labelLine: {
                    length: 10,
                    lineStyle: {
                      width: 1,
                      type: 'solid',
                      color: getThemeColor(),
                    },
                  },
                  itemStyle: {
                    borderWidth: 0,
                  },
                  // emphasis: {
                  //   label: {
                  //     fontSize: 20,
                  //   },
                  // },
                  sort: props.sortType,
                  data: data.map((item, index) => {
                    if (index === 0) {
                      return { ...item, label: { show: false }, labelLine: { show: false } };
                    } else {
                      return item;
                    }
                  }),
                },
              ]
            : []),
          ...(props.showOutsideCurrentStage
            ? [
                {
                  name: 'Funnel',
                  type: 'funnel',
                  left: 'center',
                  top: 20,
                  bottom: 20,
                  width: props.funnelWidth + '%',
                  silent: true,
                  min: props.bottomTye === 'flat' ? minData : 0,
                  minSize: props.minSize
                    ? props.minSize
                    : props.bottomTye === 'flat'
                    ? '40%'
                    : '0%',
                  maxSize: props.maxSize ? props.maxSize : '90%',
                  gap: 1,
                  orient: props.funnelOrient,
                  label: {
                    show: true,
                    fontSize: 20,
                    // position: props.funnelOrient === 'vertical' ? 'rightTop' : 'leftTop',
                    // color: 'white',
                    formatter: (params: any) => {
                      // if (params.data.current) {
                      return props.showOutsideCurrentStageText;
                      // }
                      // return '';
                    },
                  },
                  labelLine: {
                    length: 30,
                    lineStyle: {
                      width: 1,
                      type: 'solid',
                      // color: 'white',
                    },
                  },
                  itemStyle: {
                    borderWidth: 0,
                  },
                  // emphasis: {
                  //   label: {
                  //     fontSize: 20,
                  //   },
                  // },
                  sort: props.sortType,
                  data: data.map((item, index) => {
                    if (!item.current) {
                      return { ...item, label: { show: false }, labelLine: { show: false } };
                    } else {
                      return { ...item, label: { color: finalAreaColor[index] } };
                    }
                  }),
                },
              ]
            : []),
        ],
      };
    });

    return {
      funnelOption,
      echartsRef,
      mergeBaseOption,
    };
  },
});
</script>

<style lang="stylus" scoped></style>
