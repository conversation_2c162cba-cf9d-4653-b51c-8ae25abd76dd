<template lang="pug">
.com_base_funnel.w-full.h-full
  ComScreenFunnel(
    v-bind='$props'
  )
</template>
<!-- 玫瑰图 -->
<script lang="ts">
import { PropType, defineComponent, toRefs } from 'vue';
import ComScreenFunnel from './ComScreenFunnel.vue';
import { FunnelData } from '../baseUtils/baseChat';

const ComBaseFunnel = defineComponent({
  name: 'ComBaseFunnel',
  components: {
    ComScreenFunnel,
  },
  props: {
    mergeOption: { type: Object, default: () => {} },
    // 是否完全替换
    isOnlyMerge: { type: Boolean, default: false },
    customizeAreaColor: { type: Array, default: () => [] },
    data: {
      type: Array as PropType<FunnelData[]>,
      default: () => [
        {
          seriesName: 'funnel-1',
          seriesData: [
            { value: 140, name: '图例' },
            { value: 180, name: '咨询' },
            { value: 100, name: '订单' },
            { value: 60, name: '点击' },
            { value: 30, name: '展现' },
          ],
        },
      ],
    },
    // 是否显示图例
    showLengend: { type: Boolean, default: true },
    // 图例位置 top right bottom right
    lengendPosition: { type: Array, default: () => ['center', 0, 0, 'auto'] },
    // 平底还是尖底的漏斗图
    bottomTye: {
      type: String as PropType<'flat' | 'tip'>,
      default: 'tip',
    },
    // FunnelOrient 漏斗图水平还是垂直
    funnelOrient: {
      type: String as PropType<'horizontal' | 'vertical'>,
      default: () => 'vertical',
    },
    // 图例水平还是垂直
    lengendOrient: {
      type: String as PropType<'horizontal' | 'vertical'>,
      default: () => 'vertical',
    },
    insideLabelWithValue: {
      type: Boolean,
      default: () => true,
    },
    insideDigit: {
      type: String,
      default: () => '个',
    },
    showOutSideLabel: {
      type: Boolean,
      default: () => true,
    },
    // 数据是否排序
    isSort: {
      type: Boolean,
      default: () => true,
    },
    // 定义minSize,maxSize
    minSize: {
      type: String,
      default: () => '',
    },
    maxSize: {
      type: String,
      default: () => '',
    },
    sortType: {
      type: String as PropType<'ascending' | 'descending' | 'none'>,
      default: () => 'none',
    },
    insideLabelWithValueBrackets: {
      type: Boolean,
      default: () => false,
    },
    funnelWidth: {
      type: Number,
      default: () => 80,
    },
    showOutsideCurrentStage: {
      type: Boolean,
      default: () => false,
    },
    showOutsideCurrentStageText: {
      type: String,
      default: () => 'text',
    },
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
export default ComBaseFunnel;
</script>
<style lang="stylus" scoped></style>
