<template lang="pug">
.w-full.h-full 
  TaScreenEchartBase(:options='lineOption' ref='echartsRef')
</template>
<script lang="ts">
import { VObject } from '@/lib/vails';
import { PropType, computed, defineComponent, ref } from 'vue';
import echarts from 'echarts';
import { BaseData, lineColor, lineAreaColor } from '../baseUtils/baseChat';

type VFn = () => void;

const ComBaseCircleGraph = defineComponent({
  name: 'ComBaseCircleGraph',
  props: {
    // 需要merge的option - 高级功能
    mergeOption: { type: Object, default: () => {} },
    // 是否完全替换
    isOnlyMerge: { type: Boolean, default: false },

    // 自定义色彩，【或者可以支持组选新颜色?】
    customizeLineColor: { type: Array, default: () => [] },
    customizeAreaColor: {
      type: Array,
      default: () => ['#ED777A', '#F8DE74', '#9FFCB9', '#5C91F8'],
    },

    // ----------------------------------------------

    // 是否显示图例
    showLengend: { type: Boolean, default: false },
    // 图表数据 seriesData 为纯数据项 , seriesName 对应的每一例数据代表的数据别称
    data: {
      type: Array as PropType<BaseData[]>,
      default: () => [
        {
          seriesName: '123',
          seriesData: [
            {
              name: '高价值专利',
              value: 80,
              unit: '件',
            },
            { name: '授权发明专利', value: 80, unit: '件' },
            { name: '授权专利', value: 464, unit: '件' },
            { name: '申请专利', value: 100, unit: '家' },
          ],
        },
      ],
    },

    // 图例位置 top right bottom right
    lengendPosition: { type: Array, default: () => [0, 0, 0, 0] },
    // X轴显示名称，为空则不显示X轴标
    unit: { type: String, default: () => '家' },
  },
  setup(props: VObject) {
    const echartsRef = ref();
    const mergeBaseOption = (options: VObject, onlyMerge: boolean) => {
      echartsRef.value?.mergeOption(options, onlyMerge);
    };
    const lineOption = computed(() => {
      const finalAreaColor = [...props.customizeAreaColor, ...lineAreaColor];
      const options = {
        tooltip: {},
        animationDurationUpdate: 1500,
        animationEasingUpdate: 'quinticInOut',
        series: props.data[0].seriesData.map((item: any, index: number) => {
          const length = props.data[0].seriesData?.length;
          return {
            type: 'pie',
            silent: true,
            name: 'pie' + index,
            data: [
              {
                ...item,
                itemStyle: {
                  borderWidth: 0,
                },
              },
              {
                ...item,
                value: item.value * 2,
                label: {
                  show: false,
                },
                labelLine: {
                  show: false,
                },
                itemStyle: {
                  borderWidth: 0,
                },
              },
            ],

            radius: `${40 + index * 20}%`,
            center: ['28%', `${40 + (length - index) * 10}%`],
            label: {
              show: true,
              position: 'outside',
              formatter: (record: VObject) => {
                return `{name|${record.name}} {value|${record.value}}{unit|${
                  record.data.unit || props.unit
                }}`;
              },
              rich: {
                name: {
                  color: '#BFF0FD',
                  fontWeight: 400,
                  fontSize: 12,
                  padding: [0, 4, 0, 0],
                },
                value: {
                  color: '#00E1FF',
                  fontWeight: 700,
                  fontSize: 12,
                  padding: [0, 2, 0, 0],
                },
                unit: {
                  color: '#FFFFFFCC',
                  fontSize: 12,
                  fontWeight: 300,
                },
              },
            },
            labelLine: {
              // smooth: 0.2,
              length: 10,
              length2: `${(length - index) * 5}%`,
            },
            itemStyle: {
              color: finalAreaColor[index],
              borderWidth: 0,
            },
            z: length - index,
          };
        }),
      };
      if (props?.mergeOption && Object.keys(props?.mergeOption).length > 0) {
        return (echarts as any).util.merge(options, props.mergeOption, props.isOnlyMerge);
      } else {
        return options;
      }
    });
    return {
      lineOption,
      echartsRef,
      mergeBaseOption,
    };
  },
});
export default ComBaseCircleGraph;
</script>
<style lang="stylus" scoped></style>
