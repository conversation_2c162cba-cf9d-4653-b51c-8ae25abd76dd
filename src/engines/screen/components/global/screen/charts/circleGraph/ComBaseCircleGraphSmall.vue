<template lang="pug">
.w-full.h-full 
  TaScreenEchartBase(:options='lineOption' ref='echartsRef')
</template>
<script lang="ts">
import { VObject } from '@/lib/vails';
import { PropType, computed, defineComponent, ref } from 'vue';
import echarts from 'echarts';
import { BaseData, lineColor, lineAreaColor } from '../baseUtils/baseChat';

type VFn = () => void;

const ComBaseCircleGraph = defineComponent({
  name: 'ComBaseCircleGraphSmall',
  props: {
    // 需要merge的option - 高级功能
    mergeOption: { type: Object, default: () => {} },
    // 是否完全替换
    isOnlyMerge: { type: Boolean, default: false },

    // 自定义色彩，【或者可以支持组选新颜色?】
    customizeLineColor: { type: Array, default: () => [] },
    customizeAreaColor: {
      type: Array,
      default: () => ['#ED777A', '#F8DE74', '#9FFCB9', '#5C91F8'],
    },

    // ----------------------------------------------

    // 是否显示图例
    showLengend: { type: Boolean, default: false },
    // 图表数据 seriesData 为纯数据项 , seriesName 对应的每一例数据代表的数据别称
    data: {
      type: Array as PropType<BaseData[]>,
      default: () => [
        {
          seriesName: '123',
          seriesData: [
            {
              name: '高价值专利',
              value: 80,
              unit: '件',
            },
            { name: '授权发明专利', value: 80, unit: '件' },
            { name: '授权专利', value: 464, unit: '件' },
            { name: '申请专利', value: 100, unit: '家' },
          ],
        },
      ],
    },

    // 图例位置 top right bottom right
    lengendPosition: { type: Array, default: () => [0, 0, 0, 0] },
    // X轴显示名称，为空则不显示X轴标
    unit: { type: String, default: () => '家' },
  },
  setup(props: VObject) {
    const echartsRef = ref();
    const mergeBaseOption = (options: VObject, onlyMerge: boolean) => {
      echartsRef.value?.mergeOption(options, onlyMerge);
    };
    const justifyValue = (value: number, index: number) => {
      switch (index) {
        case 0:
          return value;
        case 1:
          return value * 2;
        case 2:
          return value * 3;
        case 3:
          return value * 4;
        default:
          return value * 2;
      }
    };
    const justifyLength2 = (index: number) => {
      switch (index) {
        case 0:
          return 35;
        case 1:
          return 30;
        case 2:
          return 25;
        case 3:
          return 20;
        default:
          return 15;
      }
    };
    const lineOption = computed(() => {
      const finalAreaColor = [...props.customizeAreaColor, ...lineAreaColor];
      const options = {
        tooltip: {},
        animationDurationUpdate: 1500,
        animationEasingUpdate: 'quinticInOut',
        series: props.data[0].seriesData.map((item: any, index: number) => {
          const length = props.data[0].seriesData?.length;
          return {
            type: 'pie',
            silent: true,
            name: 'pie' + index,
            data: [
              {
                ...item,
                itemStyle: {
                  borderWidth: 0,
                },
              },
              {
                ...item,
                value: justifyValue(item.value, index),
                label: {
                  show: false,
                },
                labelLine: {
                  show: false,
                },
                itemStyle: {
                  borderWidth: 0,
                },
              },
              // {
              //   ...item,
              //   label: {
              //     show: false,
              //   },
              //   labelLine: {
              //     show: false,
              //   },
              //   itemStyle: {
              //     borderWidth: 0,
              //     borderType: 'solid',
              //   },
              // },
            ],

            radius: `${30 + index * 20}%`,
            center: ['28%', `${45 + (length - index) * 10}%`],
            label: {
              show: true,
              position: 'outside',
              formatter: (record: VObject) => {
                return `{name|${record.name}}\n{value|${record.value}}{unit|${
                  record.data.unit || props.unit
                }}`;
              },
              rich: {
                name: {
                  color: '#BFF0FD',
                  fontWeight: 400,
                  fontSize: 12,
                  padding: [0, 4, 0, 0],
                },
                value: {
                  color: '#00E1FF',
                  fontWeight: 700,
                  fontSize: 12,
                  padding: [0, 2, 0, 0],
                },
                unit: {
                  color: '#FFFFFFCC',
                  fontSize: 12,
                  fontWeight: 300,
                },
              },
            },
            labelLine: {
              // smooth: 0.2,
              length: 10,
              length2: justifyLength2(index),
            },
            itemStyle: {
              color: finalAreaColor[index],
              borderWidth: 0,
            },
            z: length - index,
          };
        }),
        // series: [
        //   // {
        //   //   type: 'graph',
        //   //   layout: 'none',
        //   //   symbolSize: 50,
        //   //   roam: true,
        //   //   silent: true,
        //   //   edgeSymbol: ['circle', 'arrow'],
        //   //   edgeSymbolSize: [4, 10],
        //   //   edgeLabel: {
        //   //     fontSize: 20,
        //   //   },
        //   //   symbolOffset: [0, '-50%'],
        //   //   data: props.data[0].seriesData
        //   //     .map((item: any, index) => {
        //   //       return {
        //   //         ...item,
        //   //         x: 300,
        //   //         y: 300,
        //   //         symbolSize: 50 + index * 30,
        //   //         itemStyle: {
        //   //           color: finalAreaColor[index],
        //   //         },

        //   //       };
        //   //     })
        //   //     .reverse(),
        //   //   // data: [
        //   //   //   {
        //   //   //     name: '节点1',
        //   //   //     x: 300,
        //   //   //     y: 300,
        //   //   //     symbolSize: 50,
        //   //   //   },
        //   //   //   {
        //   //   //     name: '节点2',
        //   //   //     x: 300,
        //   //   //     y: 300,
        //   //   //     symbolSize: 81,
        //   //   //     itemStyle: {
        //   //   //       color: 'blue',
        //   //   //     },
        //   //   //   },
        //   //   //   {
        //   //   //     name: '节点3',
        //   //   //     x: 300,
        //   //   //     y: 300,
        //   //   //     symbolSize: 111,
        //   //   //     itemStyle: {
        //   //   //       color: 'green',
        //   //   //     },
        //   //   //   },
        //   //   //   {
        //   //   //     name: '节点4',
        //   //   //     x: 300,
        //   //   //     y: 300,
        //   //   //     symbolSize: 144,
        //   //   //     itemStyle: {
        //   //   //       color: 'grey',
        //   //   //       zIndex: 0,
        //   //   //     },
        //   //   //   },
        //   //   // ].reverse(),
        //   //   // links: [],
        //   // },
        // ],
      };
      if (props?.mergeOption && Object.keys(props?.mergeOption).length > 0) {
        return (echarts as any).util.merge(options, props.mergeOption, props.isOnlyMerge);
      } else {
        return options;
      }
    });

    return {
      lineOption,
      echartsRef,
      mergeBaseOption,
    };
  },
});
export default ComBaseCircleGraph;
</script>
<style lang="stylus" scoped></style>
