<template lang="pug">
.com_base_radar.w-full.h-full
  ComScreenBaseRadar(
    v-bind='$props'
  )
</template>
<!-- 玫瑰图 -->
<script lang="ts">
import { PropType, defineComponent, toRefs } from 'vue';
import ComScreenBaseRadar from './ComScreenBaseRadar.vue';
import { BaseData, RadarMax, lineColor } from '../baseUtils/baseChat';

const ComBaseRadar = defineComponent({
  name: 'ComBaseRadar',
  components: {
    ComScreenBaseRadar,
  },
  props: {
    mergeOption: { type: Object, default: () => {} },
    // 是否完全替换
    isOnlyMerge: { type: Boolean, default: false },

    // 图表数据 seriesData 为纯数据项 , seriesName 对应的每一例数据代表的数据别称
    data: {
      type: Array as PropType<BaseData[]>,
      default: () => [
        {
          seriesName: '2018',
          seriesData: [100, 33, 25, 42],
        },
        {
          seriesName: '2019',
          seriesData: [100, 33, 25, 42].reverse(),
        },
        {
          seriesName: '2020',
          seriesData: [53, 33, 51, 154],
        },
        {
          seriesName: '2021',
          seriesData: [34, 83, 35, 126],
        },
        {
          seriesName: '2022',
          seriesData: [34, 83, 35, 126].reverse(),
        },
      ],
    },
    radarMax: {
      type: Array as PropType<RadarMax[]>,
      default: () => [
        { name: '企业集聚力', max: 200 },
        { name: '成果辐射力', max: 200 },
        { name: '要素流动力', max: 200 },
        { name: '环境吸引力', max: 200 },
      ],
    },
    showTitle: { type: Boolean, default: () => false },
    title: { type: String, default: () => 'radar' },
    showWithNum: { type: Boolean, default: () => true },
    showTooltip: { type: Boolean, default: () => true },
    showLengend: { type: Boolean, default: true },
    legendPosition: { type: Array, default: () => ['bottom', 0, 0, 'center'] },
    radius: { type: String || Number, default: () => '55%' },
    nameGap: { type: Number, default: () => 5 },
    center: { type: Array, default: () => ['50%', '50%'] },
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
export default ComBaseRadar;
</script>
<style lang="stylus" scoped></style>
