<template lang="pug">
.w-full.h-full
  TaScreenEchartBase(:options='option' ref='echartsRef')
</template>
<script lang="ts">
import { VObject } from '@/lib/vails';
import { PropType, computed, defineComponent, ref, toRefs } from 'vue';
import { BaseData, RadarMax, lineColor, lineAreaColor } from '../baseUtils/baseChat';
import echarts from 'echarts';
import { getThemeColor } from '../../../../../utils/util';

const ComScreenBaseRadar = defineComponent({
  name: 'ComScreenBaseRadar',
  components: {},
  props: {
    // 需要merge的option - 高级功能
    mergeOption: { type: Object, default: () => { } },
    // 是否完全替换
    isOnlyMerge: { type: Boolean, default: false },

    // 自定义色彩，【或者可以支持组选新颜色?】
    customizeLineColor: { type: Array, default: () => [] },
    customizeAreaColor: { type: Array, default: () => [] },

    // 图表数据 seriesData 为纯数据项 , seriesName 对应的每一例数据代表的数据别称
    data: {
      type: Array as PropType<BaseData[]>,
      default: () => [],
    },
    // 是否显示图例
    showLengend: { type: Boolean, default: false },
    // 图例位置 top right bottom right
    legendPosition: { type: Array, default: () => [0, 0, 0, 0] },
    showTitle: { type: Boolean, default: () => true },
    // 标题
    title: { type: String, default: () => 'radar' },
    // 各项数据极值
    radarMax: {
      type: Array as PropType<RadarMax[]>,
      default: () => [],
    },
    showWithNum: { type: Boolean, default: () => false },
    // 是否显示tooltip
    showTooltip: { type: Boolean, default: () => false },

    // 雷达图大小
    radius: { type: String || Number, default: () => '75%' },

    // 雷达图指示器名称和指示器轴的距离
    nameGap: { type: Number, default: () => 15 },

    // 中心（圆心）坐标，数组的第一项是横坐标，第二项是纵坐标
    center: { type: Array, default: () => ['50%', '50%'] },

    // 显示雷达图首项刻度线和标签值
    showSingleAxis: { type: Boolean, default: () => false },

    // 显示雷达图区域颜色
    showAreaColor: { type: Boolean, default: () => false },
  },
  setup(props: any) {
    const echartsRef = ref();
    const mergeBaseOption = (options: VObject, onlyMerge: boolean) => {
      echartsRef.value?.mergeOption(options, onlyMerge);
    };
    const option = computed(() => {
      const finalLineColor = [...props.customizeLineColor, ...lineColor];
      const finalAreaColor = [...props.customizeAreaColor, ...lineAreaColor];

      const [top, right, bottom, left] = props.legendPosition;
      const positionOpt = { top, right, bottom, left };
      const richMap = new Map();
      [...finalLineColor]
        .map((el, index) => {
          return {
            key: `richValue${index}`,
            color: el,
            fontSize: 12,
          };
        })
        .forEach(el => {
          richMap.set(el.key, { ...el, key: undefined });
        });
      const options = {
        ...(props.showLengend
          ? {
            legend: {
              ...positionOpt,
              data: props.data.map((el: any) => el.seriesName),
              itemHeight: 2,
              itemWidth: 8,
              icon: 'roundRect',
              textStyle: {
                color: getThemeColor(),
              },
            },
          }
          : {}),
        title: {
          show: props.showTitle,
          text: props.title,
        },
        ...(props.showTooltip
          ? {
            tooltip: {
              trigger: 'axis',
              formatter: (opts: any) => {
                const formatterText = [...opts.value]
                  .map((el: any, index: number) => {
                    const name = props.radarMax?.[index]?.name || '';
                    return `${name}: ${el}`;
                  })
                  .join('<br />');
                return `${formatterText}`;
              },
            },
          }
          : {}),

        radar: {
          name: {
            formatter: (params: string) => {
              const idx = props.radarMax.findIndex((el: RadarMax) => el.name === params);
              const richData = [...props.data].map(el => el.seriesData[idx]);
              const richText = richData
                .map((el, index) => {
                  return `{richValue${index}|${el}}`;
                })
                .join('{section|/}');
              return props.showWithNum
                ? `{twoLineName|${params}}\n${richText}`
                : `{name|${params}}`;
            },
            textStyle: {
              color: 'blue',
              rich: {
                name: {
                  color: getThemeColor(),
                  fontSize: 14,
                },
                twoLineName: {
                  color: getThemeColor(),
                  fontSize: 14,
                  padding: [4, 0, 0, 0],
                },
                section: {
                  color: getThemeColor(),
                  fontSize: 12,
                },
                ...Object.fromEntries(richMap),
              },
            },
          },

          indicator: [...props.radarMax].map((el: RadarMax, index: number) => {
            return {
              name: el.name,
              max: el.max,
              ...(index === 0 && props.showSingleAxis
                ? {
                  axisLabel: {
                    show: true,
                  },
                  axisTick: {
                    show: true,
                  },
                }
                : {}),
            };
          }),

          radius: props.radius,
          nameGap: props.nameGap,
          center: props.center,
        },

        series: [
          {
            type: 'radar',
            tooltip: {
              trigger: 'item',
            },
            data: [...props.data].map((el: BaseData, index) => {
              return {
                value: [...el.seriesData],
                name: el.seriesName,
                itemStyle: {
                  color: lineColor[index],
                },
                ...(props.showAreaColor
                  ? {
                    areaStyle: {
                      color: finalAreaColor[index],
                    },
                  }
                  : {}),
              };
            }),
          },
        ],
      };
      if (props?.mergeOption && Object.keys(props?.mergeOption).length > 0) {
        return (echarts as any).util.merge(options, props.mergeOption, props.isOnlyMerge);
      } else {
        return options;
      }
    });
    return {
      ...toRefs(props),
      option,
      echartsRef,
    };
  },
});
export default ComScreenBaseRadar;
</script>
<style lang="stylus" scoped></style>
../baseUtils/themeColor
