<template lang="pug">
.com_base_radar.w-full.h-full
  ComScreenBaseRadar(
    v-bind='$props'
  )
</template>
<!-- 玫瑰图 -->
<script lang="ts">
import { PropType, defineComponent, toRefs } from 'vue';
import ComScreenBaseRadar from './ComScreenBaseRadar.vue';
import { BaseData, RadarMax, lineColor } from '../baseUtils/baseChat';

const ComBaseAreaRadar = defineComponent({
  name: 'ComBaseAreaRadar',
  components: {
    ComScreenBaseRadar,
  },
  props: {
    mergeOption: { type: Object, default: () => {} },
    // 是否完全替换
    isOnlyMerge: { type: Boolean, default: false },

    // 图表数据 seriesData 为纯数据项 , seriesName 对应的每一例数据代表的数据别称
    data: {
      type: Array as PropType<BaseData[]>,
      default: () => [
        {
          seriesName: '2018',
          seriesData: [954, 630, 255, 427, 732, 839, 300],
        },
        {
          seriesName: '2019',
          seriesData: [954, 630, 255, 427, 732, 839, 300].reverse(),
        },
      ],
    },
    radarMax: {
      type: Array as PropType<RadarMax[]>,
      default: () => [
        { name: '星期一', max: 1000 },
        { name: '星期二', max: 1000 },
        { name: '星期三', max: 1000 },
        { name: '星期四', max: 1000 },
        { name: '星期五', max: 1000 },
        { name: '星期六', max: 1000 },
        { name: '星期天', max: 1000 },
      ],
    },
    showTitle: { type: Boolean, default: () => false },
    title: { type: String, default: () => '' },
    showTooltip: { type: Boolean, default: () => true },
    center: { type: Array, default: () => ['50%', '50%'] },
    showSingleAxis: { type: Boolean, default: () => true },
    showAreaColor: { type: Boolean, default: () => true },
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
export default ComBaseAreaRadar;
</script>
<style lang="stylus" scoped></style>
