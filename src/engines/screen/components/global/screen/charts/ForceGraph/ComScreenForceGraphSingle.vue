<template lang="pug">
.w-full.h-full
  TaScreenEchartBase(:options='option' ref='echartsRef')
</template>
<script lang="ts">
import { VObject } from '@/lib/vails';
import { PropType, computed, defineComponent, ref, toRefs } from 'vue';
import { ForceGraphData } from '../baseUtils/baseChat';
import { getThemeColor } from '@/engines/screen/utils/util';

const ComScreenForceGraphSingle = defineComponent({
  name: 'ComScreenForceGraphSingle',
  components: {},
  props: {
    // 需要merge的option - 高级功能
    mergeOption: { type: Object, default: () => {} },
    // 是否完全替换
    isOnlyMerge: { type: Boolean, default: false },

    // 图表数据 seriesData 为纯数据项 , seriesName 对应的每一例数据代表的数据别称
    data: {
      type: Array as PropType<ForceGraphData[]>,
      default: () => [],
    },
    // 有几个点值
    pointName: {
      type: Array as PropType<string[]>,
      default: () => [],
    },

    // 多对一 输入的节点
    curPoint: {
      type: String,
      default: () => '',
    },
  },
  setup(props: any) {
    const echartsRef = ref();
    const mergeBaseOption = (options: VObject, onlyMerge: boolean) => {
      echartsRef.value?.mergeOption(options, onlyMerge);
    };

    function convertGraphData(e: any[], a: string) {
      const b = props.pointName;
      const n = [];
      const l = [];
      const t: any = [];
      const r: any = [];
      const o = 0.5;
      const c = 20;
      e.forEach(function (e, n) {
        (e.fromName !== a && e.toName !== a) ||
          (r.push(e.value),
          t.push({
            source: e.fromName,
            target: e.toName,
            value: e.value,
          }));
      });
      for (
        var u = Math.max.apply(null, r), h = Math.min.apply(null, r), v = [], m = 0;
        m < r.length;
        m++
      )
        v.push(r[m] === 0 ? 0 : ((c - o) * (r[m] - h)) / (u - h) + o);
      for (var i = 0; i < t.length; i++) {
        const p: any = v.shift();
        t[i].source === a &&
          l.push({
            source: a,
            target: t[i].target,
            value: t[i].value,
            symbol: ['none', 'arrow'],
            symbolSize: p + 10,
            lineStyle: {
              width: p,
              cap: 'round',
              opacity: 0.8,
              color: 'rgba(255,185,128,1)',
            },
            label: {
              show: !0,
              position: 'middle',
              formatter: function (e: any) {
                return parseInt(e.value);
              },
              fontSize: 18,
              color: 'rgba(255,185,128,1)',
              textShadowColor: '#032e6f',
              textShadowBlur: 10,
            },
          });
        t[i].target === a &&
          l.push({
            source: t[i].source,
            target: a,
            value: t[i].value,
            symbol: ['none', 'arrow'],
            symbolSize: p + 10,
            lineStyle: {
              width: p,
              cap: 'round',
              opacity: 0.8,
              color: 'rgba(46,199,201,1)',
            },
            label: {
              show: !0,
              position: 'middle',
              formatter: function (e: any) {
                return parseInt(e.value);
              },
              color: 'rgba(46,199,201,1)',
              fontSize: 18,
              textShadowColor: '#032e6f',
              textShadowBlur: 10,
            },
          });
      }
      for (i = 0; i < b.length; i++)
        n.push({
          name: b[i],
          value: 1,
          itemStyle: {
            color: b[i] === a ? '#bec21b' : 'transparent',
          },
          label: {
            position: b[i] === a ? 'inside' : 'top',
            fontSize: b[i] === a ? 28 : 18,
          },
          symbolSize: b[i] === a ? 30 : 1,
        });
      return {
        nodes: n,
        links: l,
      };
    }

    const option: any = computed(() => {
      return {
        series: [
          {
            type: 'graph',
            layout: 'force',
            force: {
              layoutAnimation: !0,
              friction: 0.5,
            },
            zoom: 6,
            nodes: convertGraphData(props.data[0]?.seriesData, props.curPoint).nodes,
            links: convertGraphData(props.data[0]?.seriesData, props.curPoint).links,
            roam: !1,
            silent: !0,
            label: {
              show: !0,
              formatter: '{b}',
              color: getThemeColor(),
            },
            lineStyle: {
              curveness: 0.3,
            },
          },
        ],
      };
    });

    return {
      ...toRefs(props),
      option,
      echartsRef,
      mergeBaseOption,
    };
  },
});
export default ComScreenForceGraphSingle;
</script>
<style lang="stylus" scoped></style>
