<template lang="pug">
.w-full.h-full
  TaScreenEchartBase(:options='option' ref='echartsRef')
</template>
<script lang="ts">
import { VObject } from '@/lib/vails';
import { PropType, computed, defineComponent, ref, toRefs } from 'vue';
import {
  ForceGraphLineLevelData,
  ForceGraphData,
  forceGraphColor,
  forceGraphAreaColor,
} from '../baseUtils/baseChat';
import echarts from 'echarts';
import { getThemeColor } from '@/engines/screen/utils/util';

const ComScreenForceGraphAll = defineComponent({
  name: 'ComScreenForceGraphAll',
  components: {},
  props: {
    // 需要merge的option - 高级功能
    mergeOption: { type: Object, default: () => {} },
    // 是否完全替换
    isOnlyMerge: { type: Boolean, default: false },

    // 自定义色彩，【或者可以支持组选新颜色?】
    customizeLineColor: { type: Array, default: () => [] },
    customizeAreaColor: { type: Array, default: () => [] },

    // 图表数据 seriesData 为纯数据项 , seriesName 对应的每一例数据代表的数据别称
    data: {
      type: Array as PropType<ForceGraphData[]>,
      default: () => [],
    },
    // 有几个点值
    pointName: {
      type: Array as PropType<string[]>,
      default: () => [],
    },

    // 图例各区间范围值
    lineLevel: {
      type: Array as PropType<ForceGraphLineLevelData[]>,
      default: () => [],
    },
    // 力图 圆形还是星形
    graphType: {
      type: String as PropType<'round' | 'star'>,
      default: () => 'star',
    },
  },
  setup(props: any) {
    const echartsRef = ref();
    const mergeBaseOption = (options: VObject, onlyMerge: boolean) => {
      echartsRef.value?.mergeOption(options, onlyMerge);
    };

    function convertGraphAll(e: any[], a: any[]) {
      const n = [];
      const l = [];
      let t = a;
      const r: any = [];
      const o = 0.5;
      const c = 5;
      e.forEach(function (e) {
        r.push(e.value);
      });
      const u = Math.max.apply(null, r);
      const h = Math.min.apply(null, r);
      t || (t = [0.25 * u, 0.5 * u, 0.75 * u]);
      for (var v = [], m = 0; m < r.length; m++)
        v.push(r[m] === 0 ? 0 : ((c - o) * (r[m] - h)) / (u - h) + o);
      for (var i = 0; i < e.length; i++) {
        const p = v.shift();
        // let s = 'rgba(0,255,255,.5)';
        // e[i].value > t[2]
        //   ? (s = 'rgba(193,46,52,.8)')
        //   : e[i].value > t[1]
        //   ? (s = 'rgba(232,124,37,.8)')
        //   : e[i].value > t[0] && (s = 'rgba(181,195,52,.8)');
        const lineColorIndex = props.lineLevel.findIndex(
          ({ gt, lte }: VObject) => e[i].value >= gt && e[i].value <= lte,
        );
        l.push({
          source: e[i].fromName,
          target: e[i].toName,
          value: e[i].value,
          lineStyle: {
            width: p,
            cap: 'round',
            opacity: 0.8,
            // ========
            color: finalAreaColor[lineColorIndex],
          },
        });
      }
      for (i = 0; i < props.pointName.length; i++)
        n.push({
          name: props.pointName[i],
          value: 1,
          symbolSize: 1,
        });
      return {
        nodes: n,
        links: l,
      };
    }
    const finalLineColor = [...props.customizeLineColor, ...forceGraphColor];
    const finalAreaColor = [...props.customizeAreaColor, ...forceGraphAreaColor];
    const option: any = computed(() => {
      return {
        visualMap: {
          show: !0,
          type: 'piecewise',
          pieces: props.lineLevel.map((item: VObject, index: number) => {
            return {
              ...item,
              color: finalLineColor[index],
              symbol:
                'path://M576.08,55.1,478.6,94.22a8.29,8.29,0,0,1-11.38-7.7V80.46a8.3,8.3,0,0,0-8-8.29L8,55.71a8.3,8.3,0,0,1-8-8.29H0a8.3,8.3,0,0,1,8-8.3L459.22,22.66a8.31,8.31,0,0,0,8-8.3v-6A8.3,8.3,0,0,1,478.6.6l97.48,39.1A8.29,8.29,0,0,1,576.08,55.1Z',
            };
          }),
          itemSymbol: 'arrow',
          left: -4,
          bottom: 15,
          itemWidth: 56,
          itemHeight: 14,
          textStyle: {
            color: getThemeColor(),
            fontSize: 12,
          },
          seriesIndex: -1,
          // target: 'links',
          // silent: true,
        },
        series: [
          {
            type: 'graph',
            layout: 'circular',
            zoom: 1,
            left: 'center',
            data: convertGraphAll(props.data[0]?.seriesData, [1000, 3000, 5000]).nodes,
            links: convertGraphAll(props.data[0]?.seriesData, [1000, 3000, 5000]).links,
            roam: !0,
            label: {
              show: true,
              position: 'top',
              fontSize: 18,
              formatter: '{b}',
              color: getThemeColor(),
            },
            lineStyle: {
              curveness: props.graphType === 'round' ? 0 : 0.2,
            },
          },
        ],
      };
    });

    return {
      ...toRefs(props),
      option,
      echartsRef,
      mergeBaseOption,
    };
  },
});
export default ComScreenForceGraphAll;
</script>
<style lang="stylus" scoped></style>
../baseUtils/themeColor
