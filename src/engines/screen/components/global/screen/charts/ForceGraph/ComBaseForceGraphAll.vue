<template lang="pug">
.com_base_force_graph_all.w-full.h-full
  ComScreenForceGraphAll(
    v-bind='$props'
  )
</template>
<script lang="ts">
import { PropType, defineComponent, toRefs, computed } from 'vue';
import ComScreenForceGraphAll from './ComScreenForceGraphAll.vue';
import { ForceGraphLineLevelData, ForceGraphData, forceGraphLineData } from '../baseUtils/baseChat';

const ComBaseForceGraphAll = defineComponent({
  name: 'ComBaseForceGraphAll',
  components: {
    ComScreenForceGraphAll,
  },
  props: {
    mergeOption: { type: Object, default: () => {} },
    // 是否完全替换
    isOnlyMerge: { type: Boolean, default: false },

    // 自定义色彩，【或者可以支持组选新颜色?】
    customizeLineColor: { type: Array, default: () => [] },
    customizeAreaColor: { type: Array, default: () => [] },

    // 图表数据 seriesData 为纯数据项 , seriesName 对应的每一例数据代表的数据别称
    data: {
      type: Array as PropType<ForceGraphData[]>,
      default: () => [
        {
          seriesName: '各区流向图',
          seriesData: [],
        },
      ],
    },
    // 有几个点值
    pointName: {
      type: Array as PropType<string[]>,
      default: () => [],
    },

    // 图例各区间范围值
    lineLevel: {
      type: Array as PropType<ForceGraphLineLevelData[]>,
      default: () => [],
    },

    // 力图 圆形还是星形
    graphType: {
      type: String as PropType<'round' | 'star'>,
      default: () => 'star',
    },
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
export default ComBaseForceGraphAll;
</script>
<style lang="stylus" scoped></style>
