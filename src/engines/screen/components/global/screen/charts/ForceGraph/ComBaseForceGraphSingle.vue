<template lang="pug">
.com_base_force_graph_all.w-full.h-full
  ComScreenForceGraphSingle(
    v-bind='$props'
  )
</template>
<script lang="ts">
import { PropType, defineComponent, toRefs, computed } from 'vue';
import ComScreenForceGraphSingle from './ComScreenForceGraphSingle.vue';
import { ForceGraphData } from '../baseUtils/baseChat';

const ComBaseForceGraphSingle = defineComponent({
  name: 'ComBaseForceGraphSingle',
  components: {
    ComScreenForceGraphSingle,
  },
  props: {
    // 需要merge的option - 高级功能
    mergeOption: { type: Object, default: () => {} },
    // 是否完全替换
    isOnlyMerge: { type: Boolean, default: false },

    // 图表数据 seriesData 为纯数据项 , seriesName 对应的每一例数据代表的数据别称
    data: {
      type: Array as PropType<ForceGraphData[]>,
      default: () => [],
    },
    // 有几个点值
    pointName: {
      type: Array as PropType<string[]>,
      default: () => [],
    },

    // 多对一 输入的节点
    curPoint: {
      type: String,
      default: () => '',
    },
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
export default ComBaseForceGraphSingle;
</script>
<style lang="stylus" scoped></style>
