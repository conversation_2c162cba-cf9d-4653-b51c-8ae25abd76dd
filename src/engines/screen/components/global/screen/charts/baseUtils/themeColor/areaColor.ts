// area color
export const lineAreaColor = [
  'rgba(42, 177, 253, 0.64)',
  'rgba(125, 215, 245, 0.64)',
  'rgba(159, 252, 185, 0.64)',
  'rgba(248, 222, 116, 0.64)',
  'rgba(237, 119, 122, 0.64)',
  'rgba(18, 232, 223, 0.64)',
  'rgba(208, 126, 248, 0.64)',
  'rgba(92, 145, 248, 0.64)',
  'rgba(87, 189, 148, 0.64)',
  'rgba(240, 144, 84, 0.64)',
  'rgba(240, 144, 84, 0.64)',
];

export const barAreaColor = [
  'rgba(42, 177, 253, 1)',
  'rgba(125, 215, 245, 1)',
  'rgba(159, 252, 185, 1)',
  'rgba(248, 222, 116, 1)',
  'rgba(237, 119, 122, 1)',
  'rgba(18, 232, 223, 1)',
  'rgba(208, 126, 248, 1)',
  'rgba(92, 145, 248, 1)',
  'rgba(87, 189, 148, 1)',
  'rgba(240, 144, 84, 1)',
  'rgba(240, 144, 84, 1)',
];

export const pieAreaColor = [
  'rgba(42, 177, 253, 1)',
  'rgba(125, 215, 245, 1)',
  'rgba(159, 252, 185, 1)',
  'rgba(248, 222, 116, 1)',
  'rgba(237, 119, 122, 1)',
  'rgba(18, 232, 223, 1)',
  'rgba(208, 126, 248, 1)',
  'rgba(92, 145, 248, 1)',
  'rgba(87, 189, 148, 1)',
  'rgba(240, 144, 84, 1)',
  'rgba(240, 144, 84, 1)',
];

export const forceGraphAreaColor = [
  'rgba(0,255,255,.5)',
  'rgba(181,195,52,.8)',
  'rgba(232,124,37,.8)',
  'rgba(193,46,52,.8)',
];

export const scatterAreaColor = [
  '#485ABB',
  '#A8674F',
  '#0E7565',
  '#E7CA92',
  '#FAC79E',
  '#F3B0AE',
  '#B0AED3',
  '#A4B7F0',
  '#B6CCF3',
  '#91E0ED',
  '#9BADCD',
  '#90BB8E',
  '#A4C9E1',
  '#E7CA92',
  '#FAC79E',
  '#F3B0AE',
  '#B0AED3',
  '#A4B7F0',
  '#B6CCF3',
  '#91E0ED',
];
