import { VObject } from '@/lib/vails';

const initParameter = () => {
  let sumValue = 0;
  let startValue = 0;
  let endValue = 0;
  return {
    sumValue,
    startValue,
    endValue,
  };
};

const generateSerieItem = (pieData: VObject, i: number, k: number) => {
  return {
    name: pieData[i].name,
    type: 'surface',
    parametric: true,
    wireframe: {
      show: false,
    },
    pieData: pieData[i],
    pieStatus: {
      selected: false,
      hovered: false,
      k,
    },
    itemStyle: pieData[i].itemStyle,
    parametricEquation: {},
  };
};

const get3DPie = (
  pieData: VObject[],
  ratio: number,
  hoverIndex: number | null,
  hh: number,
  gap = 0,
  distance = 150,
  beta = 0,
  autoRotate = true,
  rotateSensitivity = 0.5,
  top = '-10%',
) => {
  const series: VObject[] = [];
  const legendData = [];
  let { sumValue, startValue, endValue } = initParameter();
  const k = ratio || ratio === 0 ? (1 - ratio) / (1 + ratio) : 1 / 3;

  for (let i = 0; i < pieData.length; i++) {
    sumValue += pieData[i].value + gap;
    const seriesItem = generateSerieItem(pieData, i, k);
    series.push(seriesItem);
  }

  series.forEach((serie: VObject, index) => {
    endValue = startValue + serie.pieData.value;
    serie.pieData.startRatio = startValue / sumValue;
    serie.pieData.endRatio = endValue / sumValue;
    serie.parametricEquation = getParametricEquation(
      serie.pieData.startRatio,
      serie.pieData.endRatio,
      false,
      false,
      k,
      index === hoverIndex ? hh : serie.pieData.h || 0,
    );
    startValue = endValue + gap;

    legendData.push(serie.name);
  });

  const option = {
    // animation:false,
    tooltip: {
      formatter: (params: any) => {
        if (params.seriesName !== 'mouseoutSeries') {
          return `${
            params.seriesName
          }<br/><span style="display:inline-block;margin-right:5px;border-radius:10px;width:10px;height:10px;background-color:${
            params.color
          };"></span>${option.series[params.seriesIndex].pieData.value}`;
        }
        return '';
      },
    },
    xAxis3D: {
      min: -1,
      max: 1,
    },
    yAxis3D: {
      min: -1,
      max: 1,
    },
    zAxis3D: {
      min: -1,
      max: 1,
    },
    grid3D: {
      show: false,
      boxHeight: 5,
      top,
      viewControl: {
        alpha: 35,
        beta,
        rotateSensitivity,
        zoomSensitivity: 0,
        panSensitivity: 0,
        autoRotate,
        distance,
      },
      postEffect: {
        enable: false,
        bloom: {
          enable: true,
          bloomIntensity: 0.1,
        },
        SSAO: {
          enable: true,
          quality: 'medium',
          radius: 2,
        },
      },
    },
    series,
  };
  return option;
};

function getParametricEquation(
  startRatio: number,
  endRatio: number,
  isSelected: boolean,
  isHovered: boolean,
  k: number,
  h: number,
) {
  // 计算
  const midRatio = (startRatio + endRatio) / 2;

  const startRadian = startRatio * Math.PI * 2;
  const endRadian = endRatio * Math.PI * 2;
  const midRadian = midRatio * Math.PI * 2;

  // 如果只有一个扇形，则不实现选中效果。
  if (startRatio === 0 && endRatio === 1) {
    isSelected = false;
  }

  // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）
  const offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0;
  const offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0;

  // 计算高亮效果的放大比例（未高亮，则比例为 1）
  const hoverRate = isHovered ? 1.05 : 1;

  // 返回曲面参数方程
  return {
    u: {
      min: -Math.PI,
      max: Math.PI * 3,
      step: Math.PI / 32,
    },

    v: {
      min: 0,
      max: Math.PI * 2,
      step: Math.PI / 20,
    },

    x(u: number, v: number) {
      if (u < startRadian) {
        return offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate;
      }
      if (u > endRadian) {
        return offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate;
      }
      return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate;
    },

    y(u: number, v: number) {
      if (u < startRadian) {
        return offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate;
      }
      if (u > endRadian) {
        return offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate;
      }
      return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate;
    },

    z(u: number, v: number) {
      if (u < -Math.PI * 0.5) {
        return Math.sin(u);
      }
      if (u > Math.PI * 2.5) {
        return Math.sin(u) * h * 0.1;
      }
      // 当前图形的高度是Z根据h（每个value的值决定的）
      return Math.sin(v) > 0 ? 1 * h * 0.1 : -1;
    },
  };
}

const debounceFunc = (fn: Function, duration = 100) => {
  let timerId: any;
  return (...args: any[]) => {
    clearTimeout(timerId);
    timerId = setTimeout(() => {
      fn(...args);
    }, duration);
  };
};
export { get3DPie, debounceFunc };
