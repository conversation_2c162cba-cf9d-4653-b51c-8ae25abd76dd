export * from './themeColor';
export * from './mock';

export interface BaseData {
  seriesData: number[];
  seriesName: string;
}

export interface ScatterData {
  seriesData: BaseScatterData[];
  seriesName: string;
}
export interface VScatterData {
  seriesData: [number, number][];
  seriesName: string;
}

export interface VBubbleData {
  seriesData: BubbleType[];
  seriesName: string;
}

export interface BubbleType {
  value: [number, number];
  size: number;
}

export interface ForceGraphData {
  seriesData: BaseForceGraphData[];
  seriesName: string;
}

export interface ForceGraphLineLevelData {
  gt: number;
  lte: number;
  label: string;
}

export type BaseScatterData = {
  value: string;
  name?: string;
};

export interface ScreenLinePointMap {
  name: string;
  value: [number, number];
}

export interface BarData {
  seriesData: number[];
  seriesName: string;
}

export type BasePieData = {
  value: string;
  name?: string;
};

export type BaseFunnelData = {
  value: number;
  name?: string;
  current?: boolean;
};
export type BaseForceGraphData = {
  value: number;
  fromName: string;
  toName: string;
};
export interface PieData {
  seriesData: BasePieData[];
  seriesName: string;
}

export interface FunnelData {
  seriesData: BaseFunnelData[];
  seriesName: string;
}

export interface SankeyData {
  seriesName: string;
  seriesData: BaseSankeyData;
}

export interface BaseSankeyData {
  data: { name: string }[];
  links: { source: string; target: string; value: number }[];
}
export interface progressScreenData {
  seriesData: number;
  seriesName: string;
}
export type PieRadius = string[] | number[];
export type PieRoseType = 'area' | 'radius' | false;
export type PieLengendPosition = (string | number)[];

export type LegendOptType = 'plain' | 'scroll';
export type LegendOptOrient = 'horizontal' | 'vertical';

export type RadarMax = { name: string; max: number };

export type ThemeRiverDataType = [any, any, any];
export type ThemeRiverData = ThemeRiverDataType[];

export interface RankingBarData {
  seriesName: string;
  seriesData: number;
}

export interface SortListData {
  seriesName: string;
  seriesData: {
    value: number;
    percent: number;
  };
}
