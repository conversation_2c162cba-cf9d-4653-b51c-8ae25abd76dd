<template lang="pug">
.w-full.h-full
  TaScreenEchartBase(:options='options' ref='echartsRef')
</template>

<script lang="tsx">
import { PropType, computed, defineComponent, ref } from 'vue';
import { VObject } from '@/lib/vails';
import { getThemeColor } from '@/engines/screen/utils/util';

const ComScreenLiquidFill = defineComponent({
  name: 'ComScreenLiquidFill',
  props: {
    // 需要merge的option - 高级功能
    mergeOption: { type: Object, default: () => {} },
    // 是否完全替换
    isOnlyMerge: { type: Boolean, default: false },
    // 百分比值 - 0-100
    data: {
      type: Object,
      default: () => ({ liquidData: 80 }),
    },
    // 区域问字大小
    fontSize: { type: Number, default: () => 28 },
    // 边框宽度
    outWidth: { type: Number, default: () => 12 },
    // 边框颜色
    outColor: { type: String, default: () => 'rgba(0,150,255,0.2)' },
    // 填充颜色
    colorStops: {
      type: Array as PropType<string[]>,
      default: () => ['#0CB8EA', '#0CB8EA', '#0CB8EA'],
    },
    liquidNums: {
      type: Number,
      default: () => 2,
    },
  },
  setup(props) {
    const echartsRef = ref();
    const mergeBaseOption = (options: VObject, onlyMerge: boolean) => {
      echartsRef.value?.mergeOption(options, onlyMerge);
    };

    const options = computed(() => {
      const percent = `${props.data.liquidData}%`;
      const percentValue = props.data.liquidData / 100;
      return {
        title: {
          text: percent,
          textStyle: {
            fontSize: props.fontSize,
            fontFamily: 'DINCondensedBold',
            fontWeight: 'normal',
            color: getThemeColor(),
            rich: {
              a: {
                fontSize: props.fontSize,
              },
            },
          },
          x: 'center',
          y: 'center',
        },
        series: [
          {
            type: 'liquidFill',
            radius: '78.1%',
            center: ['50%', '50%'],

            outline: {
              borderDistance: 0,
              itemStyle: {
                borderWidth: props.outWidth,
                borderColor: props.outColor,
              },
            },
            globalCoord: false,
            color: [
              {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: props.colorStops[0], // 0% 处的颜色
                  },
                  {
                    offset: 0.5,
                    color: props.colorStops[1], // 100% 处的颜色
                  },
                  {
                    offset: 1,
                    color: props.colorStops[2], // 100% 处的颜色
                  },
                ],
              },
            ],
            data: Array.from({ length: props.liquidNums }).map(() => percentValue), // data个数代表波浪数
            backgroundStyle: {
              borderColor: 'rgba(0,150,255,0.0)', // 背景透明
              color: 'rgba(0,150,255,0.0)', // 背景透明
            },
            label: {
              normal: {
                formatter: '',
              },
            },
          },
        ],
      };
    });

    return {
      options,
      echartsRef,
      mergeBaseOption,
    };
  },
});
export default ComScreenLiquidFill;
</script>

<style lang="stylus" scoped></style>
