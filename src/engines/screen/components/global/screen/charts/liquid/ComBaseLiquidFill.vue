<template lang="pug">
.com_base_liquid_fill.w-full.h-full
  ComScreenLiquidFill(
    v-bind='$props'
  )
</template>
<script lang="ts">
import { PropType, defineComponent, toRefs } from 'vue';
import ComScreenLiquidFill from './ComScreenLiquidFill.vue';
const ComBaseLiquidFill = defineComponent({
  name: 'ComBaseLiquidFill',
  components: {
    ComScreenLiquidFill,
  },
  props: {
    // 需要merge的option - 高级功能
    mergeOption: { type: Object, default: () => {} },
    // 是否完全替换
    isOnlyMerge: { type: Boolean, default: false },
    // 百分比值 - 0-100
    data: {
      type: Object,
      default: () => ({ liquidData: 80 }),
    },
    // 区域问字大小
    fontSize: { type: Number, default: () => 28 },
    // 边框宽度
    outWidth: { type: Number, default: () => 12 },
    // 边框颜色
    outColor: { type: String, default: () => 'rgba(0,150,255,0.2)' },
    // 填充颜色
    colorStops: {
      type: Array as PropType<string[]>,
      default: () => ['#0CB8EA', '#0CB8EA', '#0CB8EA'],
    },
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
export default ComBaseLiquidFill;
</script>
<style lang="stylus" scoped></style>
