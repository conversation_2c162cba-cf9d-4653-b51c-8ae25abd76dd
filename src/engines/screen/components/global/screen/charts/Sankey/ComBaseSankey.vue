<template lang="pug">
.com_base_sankey.w-full.h-full
  ComScreenSankey(
    v-bind='$props'
  )
</template>
<script lang="ts">
import { PropType, defineComponent, toRefs } from 'vue';
import ComScreenSankey from './ComScreenSankey.vue';
import { SankeyData } from '../baseUtils/baseChat';

const ComBaseSankey = defineComponent({
  name: 'ComBaseSankey',
  components: {
    ComScreenSankey,
  },
  props: {
    data: {
      type: Array as PropType<SankeyData[]>,
      default: () => [],
    },
    colorSet: {
      type: Array as PropType<string[]>,
      default: () => [],
    },
    direction: {
      type: String as PropType<'horizontal' | 'vertical'>,
      default: () => 'horizontal',
    },
    gridPosition: {
      type: Array as PropType<string[]>,
      default: () => ['5%', '20%', '5%', '5%'],
    },
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
export default ComBaseSankey;
</script>
<style lang="stylus" scoped></style>
