<template lang="pug">
.w-full.h-full
  TaScreenEchartBase(:options='sankeyOption' ref='echartsRef')
</template>

<script lang="tsx">
import { Prop, PropType, computed, defineComponent, ref } from 'vue';
import { SankeyData } from '../baseUtils/baseChat';
import { VObject } from '@/lib/vails';
import { getThemeColor } from '../../../../../utils/util';

export default defineComponent({
  name: 'ComScreenSankey',
  props: {
    // 需要merge的option - 高级功能
    mergeOption: { type: Object, default: () => { } },
    // 是否完全替换
    isOnlyMerge: { type: Boolean, default: false },
    data: {
      type: Array as PropType<SankeyData[]>,
      default: () => [
        {
          seriesName: 'sankey-1',
          seriesData: {
            data: [
              { name: 'a' },
              { name: 'b' },
              { name: 'a1' },
              { name: 'b1' },
              { name: 'c' },
              { name: 'e' },
            ],
            links: [
              { source: 'a', target: 'a1', value: 16 },
              { source: 'e', target: 'b', value: 3 },
              { source: 'a', target: 'b1', value: 3 },
              { source: 'b1', target: 'a1', value: 1 },
              { source: 'b1', target: 'c', value: 2 },
              { source: 'b', target: 'c', value: 1 },
            ],
          },
        },
      ],
    },
    colorSet: {
      type: Array as PropType<string[]>,
      default: () => [],
    },
    direction: {
      type: String as PropType<'horizontal' | 'vertical'>,
      default: () => 'horizontal',
    },
    gridPosition: {
      type: Array as PropType<string[]>,
      default: () => ['5%', '20%', '5%', '5%'],
    },
  },
  setup(props) {
    const echartsRef = ref();
    const mergeBaseOption = (options: VObject, onlyMerge: boolean) => {
      echartsRef.value?.mergeOption(options, onlyMerge);
    };

    const sankeyOption = computed(() => {
      const { data, links } = props.data[0].seriesData;
      const [top, right, bottom, left] = props.gridPosition;
      const gridOpts = { top, right, bottom, left };
      return {
        ...(props.colorSet.length ? { color: props.colorSet } : {}),
        tooltip: {
          show: true,
          trigger: 'item',
          backgroundColor: 'white',
          textStyle: {
            color: 'black',
          },
        },
        series: {
          ...gridOpts,
          type: 'sankey',
          focusNodeAdjacency: 'allEdges',
          data,
          links,
          orient: props.direction,
          label: {
            position: props.direction === 'horizontal' ? 'right' : 'top',
            color: getThemeColor(),
          },
          itemStyle: {
            borderWidth: 0,
          },
          lineStyle: {
            color: 'source',
            curveness: 0.5,
          },
        },
      };
    });

    return {
      sankeyOption,
      echartsRef,
      mergeBaseOption,
    };
  },
});
</script>

<style lang="stylus" scoped></style>
