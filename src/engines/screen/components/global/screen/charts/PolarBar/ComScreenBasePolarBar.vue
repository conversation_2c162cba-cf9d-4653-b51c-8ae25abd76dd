<script lang='ts'>
import { ref, defineComponent, toRefs, computed, PropType, ComputedRef } from 'vue';
import { VObject } from '../../../../../../../lib/vails/model/index';
import { useScreenDataFetchCollectionInject } from '../../../../../../../components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';
const ComScreenBasePolarBar = defineComponent({
  name: 'ComScreenBasePolarBar',
  components: {},
  props: {
    data: {
      type: Array,
      default: () => [
        { value: 2, name: 'a' },
        { value: 1.2, name: 'b' },
        { value: 2.4, name: 'c' },
        { value: 3.6, name: 'd' },
      ]
    },
    colors: {
      type: Array as PropType<string[]>,
      default: () => []
    }
  },
  setup(props) {
    const { getScreenData, dataResult } = useScreenDataFetchCollectionInject(props);

    const echartsRef = ref<any>(null)
    const mergeBaseOption = (options: VObject, onlyMerge: boolean) => {
      echartsRef.value?.mergeOption(options, onlyMerge);
    };

    const colors = computed({
      get() {
        const currentColors: string[] = props.colors.length > 0 ?
          props.colors :
          ['#217EFD', '#1AD598', '#FFB536', '#F3654A']
        return currentColors
      }, set(val) { }
    }) as ComputedRef<string[]>

    //将颜色塞入每个柱
    const polarData = computed(() => {
      return props.data.map((item: any, index) => ({
        name: item.name,
        value: item.value,
        itemStyle: {
          color: colors.value?.[index]
        }
      }))
    })

    //获取最大值
    const maxValue = computed(() => {
      return props.data.reduce((pre: any, pro: any) => {
        return Math.max(pre, +pro.value)
      }, 0)
    })

    //将angleAxis.max设为最大数据的2倍多
    const angleAxisMax = computed(() => maxValue.value as number * 2.1)
    const barOption = computed(() => ({
      polar: {
        radius: [20, '80%']
      },
      angleAxis: {
        max: angleAxisMax.value,
        startAngle: 180,
        show: false
      },
      radiusAxis: {
        type: 'category',
        show: false,
      },
      series: {
        type: 'bar',
        barWidth: 14,
        data: polarData.value,
        coordinateSystem: 'polar',
        label: {
          show: false,
          position: 'middle',
          formatter: '{b}: {c}'
        }
      }
    }))
    return {
      ...toRefs(props),
      barOption,
      echartsRef,
    };
  },
});
export default ComScreenBasePolarBar;
</script>

<template lang="pug">
view.com-screen-base-polar-bar.w-full.h-full
  TaScreenEchartBase(:options='barOption' ref='echartsRef')
</template>

<style lang="stylus"></style>
