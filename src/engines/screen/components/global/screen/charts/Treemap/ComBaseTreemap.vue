<template lang="pug">
.w-full.h-full
  TaScreenEchartBase(:options='treemapOption' ref='echartsRef')
</template>

<script lang="ts">
import { PropType, computed, defineComponent, ref } from 'vue';
import { VObject } from '@/lib/vails';
//- 矩形图
export default defineComponent({
  name: 'ComBaseTreemap',
  props: {
    // 需要merge的option - 高级功能
    mergeOption: { type: Object, default: () => {} },
    // 是否完全替换
    isOnlyMerge: { type: Boolean, default: false },
    // ----------------------------------------------
    data: {
      type: Array as PropType<any>,
      default: () => [
        {
          name: 'nodeA',
          value: 10,
          children: [
            {
              name: 'nodeAa',
              value: 4,
            },
            {
              name: 'nodeAb',
              value: 6,
            },
          ],
        },
        {
          name: 'nodeB',
          value: 20,
          children: [
            {
              name: 'nodeBa',
              value: 20,
              children: [
                {
                  name: 'nodeBa1',
                  value: 20,
                },
              ],
            },
          ],
        },
      ],
    },
    showBreadcrumb: {
      type: Boolean,
      default: false,
    },
    color: {
      type: Array,
      default: () => [],
    },
  },
  setup(props) {
    const echartsRef = ref();
    const mergeBaseOption = (options: VObject, onlyMerge: boolean) => {
      echartsRef.value?.mergeOption(options, onlyMerge);
    };

    const treemapOption = computed(() => {
      const options = {
        color: props.color,
        tooltip: {
          formatter: '{b}:{c}',
        },
        roam: false,
        series: {
          type: 'treemap',
          width: '92%',
          breadcrumb: {
            show: props.showBreadcrumb,
          },
          label: {
            formatter: '{b}:{c}',
          },
          data: props.data,
        },
      };
      return options;
    });
    return {
      treemapOption,
      echartsRef,
      mergeBaseOption,
    };
  },
});
</script>

<style lang="stylus" scoped></style>
