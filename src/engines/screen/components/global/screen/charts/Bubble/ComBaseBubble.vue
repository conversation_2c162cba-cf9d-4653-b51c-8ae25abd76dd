<template lang="pug">
.com_screen_bubble.w-full.h-full
  ComScreenBubble(
    v-bind='$props'
  )
</template>
<script lang="ts">
import { PropType, defineComponent, toRefs } from 'vue';
import ComScreenBubble from './ComScreenBubble.vue';
import { VBubbleData } from '../baseUtils/baseChat';
import { getThemeLabelLineColor } from '@/engines/screen/utils/util';

const defaultData = [
  {
    seriesName: 'line-1',
    seriesData: [
      {
        value: [10, 8.04],
        size: 48.70603132833804,
      },
      {
        value: [8.07, 6.95],
        size: 45.22563391833423,
      },
      {
        value: [13, 7.58],
        size: 44.57120287457246,
      },
      {
        value: [9.05, 8.81],
        size: 39.84461314351828,
      },
      {
        value: [11, 8.33],
        size: 97.04860357659439,
      },
      {
        value: [14, 7.66],
        size: 30.67929525730171,
      },
      {
        value: [13.4, 6.81],
        size: 46.05951025476611,
      },
      {
        value: [10, 6.33],
        size: 77.18306775038317,
      },
      {
        value: [14, 8.96],
        size: 27.247809886189312,
      },
      {
        value: [12.5, 6.82],
        size: 11.968846570386704,
      },
      {
        value: [9.15, 7.2],
        size: 83.42133233924504,
      },
      {
        value: [11.5, 7.2],
        size: 81.04113360670733,
      },
      {
        value: [3.03, 4.23],
        size: 48.12878825718811,
      },
      {
        value: [12.2, 7.83],
        size: 22.64673177763392,
      },
      {
        value: [2.02, 4.47],
        size: 52.68753488435409,
      },
      {
        value: [1.05, 3.33],
        size: 30.063056383880028,
      },
      {
        value: [4.05, 4.96],
        size: 60.61004778572525,
      },
      {
        value: [6.03, 7.24],
        size: 88.26257242705032,
      },
    ],
  },
];

export default defineComponent({
  name: 'ComBaseBubble',
  components: {
    ComScreenBubble,
  },
  props: {
    // 需要merge的option - 高级功能
    mergeOption: { type: Object, default: () => {} },
    // 是否完全替换
    isOnlyMerge: { type: Boolean, default: false },

    // 自定义色彩，【或者可以支持组选新颜色?】
    customizeAreaColor: { type: Array, default: () => [] },

    // ----------------------------------------------

    // 是否显示图例
    showLengend: { type: Boolean, default: false },
    // 图表数据 seriesData 为纯数据项 , seriesName 对应的每一例数据代表的数据别称
    data: { type: Array as PropType<VBubbleData[]>, default: () => defaultData },
    // 图例位置 top right bottom left
    lengendPosition: { type: Array, default: () => [4, 0, 0, 4] },
    // X轴显示名称，为空则不显示X轴标
    xAxisLabel: { type: Array as PropType<string[]>, default: () => [] },
    // 是否显示Y轴
    showYAxis: { type: Boolean, default: true },
    // Y轴数据，为2则代表有两条Y轴，最大为2
    yAxisNum: { type: Number as PropType<1 | 2>, default: () => 1 },
    // 是否显示Y轴单位
    showUnit: { type: Boolean, default: true },
    // 主轴单位
    unit: { type: String, default: () => '' },
    // 副轴单位
    subUnit: { type: String, default: () => '' },
    // 主轴单位位置
    unitPadding: { type: Array as PropType<number[]>, default: () => [0, 0, 0, 0] },
    // 主轴单位位置
    subUnitPadding: { type: Array as PropType<number[]>, default: () => [0, 0, 0, 0] },
    // 图例文字颜色
    legendTextColor: {
      type: String,
      default: () => 'white',
    },
    // 坐标轴颜色
    axisColor: {
      type: String,
      default: () => 'white',
    },
    // 坐标轴分割线颜色
    splitLineColor: {
      type: String,
      default: () => getThemeLabelLineColor(),
    },
    // 显示标签
    showLabel: {
      type: Boolean,
      default: () => false,
    },
    // 图位置 top right bottom left
    gridPosition: {
      type: Array,
      default: () => ['10%', '3%', '12%', '5%'],
    },
    minSize: {
      type: Number,
      default: () => 3,
    },
    maxSize: {
      type: Number,
      default: () => 64,
    },
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
</script>
<style lang="stylus" scoped></style>
