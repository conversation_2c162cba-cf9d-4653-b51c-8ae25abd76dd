<template lang="pug">
.com_base_half_pie.w-full.h-full
  ComScreenBasePie(
    v-bind='$props'
  )
</template>
<!-- 半环形图 -->
<script lang="ts">
import { PropType, defineComponent, toRefs, computed } from 'vue';
import ComScreenBasePie from './ComScreenBasePie.vue';
import { getBorderThemeColor, getThemeColor } from '../../../../../utils/util';
import {
  LegendOptOrient,
  LegendOptType,
  PieData,
  PieLengendPosition,
  PieRadius,
} from '../baseUtils/baseChat';

const ComBaseHalfPie = defineComponent({
  name: 'ComBaseHalfPie',
  components: {
    ComScreenBasePie,
  },
  props: {
    mode: { type: String, default: 'half' },
    data: {
      type: Array as PropType<PieData[]>,
      default: () => [
        {
          seriesName: 'pie-1',
          seriesData: [
            { value: 1048, name: 'Search Engine' },
            { value: 735, name: 'Direct' },
            { value: 580, name: 'Email' },
            { value: 484, name: 'Union Ads' },
            { value: 300, name: 'Video Ads' },
          ],
        },
      ],
    },
    customizeAreaColor: { type: Array, default: () => [] },
    unit: { type: String, default: () => '' },
    radius: { type: Array as PropType<PieRadius>, default: () => ['54%', '90%'] },
    center: {
      type: Array,
      default: () => ['50%', '50%'],
    },
    showLegend: { type: Boolean, default: false },
    showLabel: {
      type: Boolean,
      default: () => false,
    },
    showPercent: {
      type: Boolean,
      default: () => false,
    },
    borderColor: { type: String, default: getBorderThemeColor() },
    borderWidth: { type: Number, default: 0 },
    labelLine: {
      type: Number,
      default: () => 1,
    },
    labelGap: {
      type: Number,
      default: () => 4,
    },
    labelLineLength: {
      type: Number,
      default: () => 10,
    },
    labelLineLength2: {
      type: Number,
      default: () => 10,
    },
    labelColor: {
      type: String,
      default: getThemeColor(),
    },
    secLabelColor: {
      type: String,
      default: getThemeColor(),
    },
    percentLabelColor: {
      type: String,
      default: getThemeColor(),
    },
    legendOrient: { type: String as PropType<LegendOptOrient>, default: 'horizontal' },
    legendType: { type: String as PropType<LegendOptType>, default: () => 'plain' },
    legendTextColor: { type: String, default: getBorderThemeColor() },
    legendPosition: { type: Array as PropType<PieLengendPosition>, default: () => [0, 0, 0, 0] },
    otherLegendOpts: { type: Object, default: () => { } },
    showTooltip: {
      type: Boolean,
      default: () => false,
    },
    showOutsideCurrentStage: {
      type: Boolean,
      default: () => false,
    },
    showOutsideCurrentStageText: {
      type: String,
      default: () => 'text',
    },
    currentTextColor: {
      type: String,
      default: getThemeColor(),
    },
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
export default ComBaseHalfPie;
</script>
<style lang="stylus" scoped></style>
