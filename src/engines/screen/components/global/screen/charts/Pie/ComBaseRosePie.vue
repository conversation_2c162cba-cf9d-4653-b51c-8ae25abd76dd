<template lang="pug">
.com_base_rose_pie.w-full.h-full
  ComScreenBasePie(
    v-bind='$props'
  )
</template>
<!-- 玫瑰图 -->
<script lang="ts">
import { PropType, defineComponent, toRefs } from 'vue';
import ComScreenBasePie from './ComScreenBasePie.vue';
import { getThemeColor, getBorderThemeColor } from '../../../../../utils/util';
import {
  PieData, PieRadius, PieRoseType, PieLengendPosition,
  LegendOptType,
  LegendOptOrient,
} from '../baseUtils/baseChat';

const ComBaseRosePie = defineComponent({
  name: 'ComBaseRosePie',
  components: {
    ComScreenBasePie,
  },
  props: {
    data: {
      type: Array as PropType<PieData[]>,
      default: () => [
        {
          seriesName: 'pie-1',
          seriesData: [
            { value: 1048, name: 'Search Engine' },
            { value: 735, name: 'Direct' },
            { value: 580, name: 'Email' },
            { value: 484, name: 'Union Ads' },
            { value: 300, name: 'Video Ads' },
          ],
        },
      ],
    },
    customizeAreaColor: { type: Array, default: () => [] },
    center: {
      type: Array,
      default: () => ['50%', '50%'],
    },
    radius: { type: Array as PropType<PieRadius>, default: () => ['10%', '80%'] },
    roseType: { type: String as PropType<PieRoseType>, default: 'radius' },
    unit: { type: String, default: () => '' },
    showTooltip: {
      type: Boolean,
      default: () => false,
    },
    showLegend: { type: Boolean, default: false },
    legendOrient: { type: String as PropType<LegendOptOrient>, default: 'horizontal' },
    legendTextColor: { type: String, default: getBorderThemeColor() },
    // 图例位置 top right bottom right
    legendPosition: { type: Array as PropType<PieLengendPosition>, default: () => [0, 0, 0, 0] },
    legendType: { type: String as PropType<LegendOptType>, default: () => 'plain' },
    otherLegendOpts: { type: Object, default: () => { } },
    showLabel: {
      type: Boolean,
      default: () => true,
    },
    showPercent: {
      type: Boolean,
      default: () => false,
    },
    labelLine: {
      type: Number,
      default: () => 1,
    },
    labelGap: {
      type: Number,
      default: () => 4,
    },
    labelLineLength: {
      type: Number,
      default: () => 10,
    },
    labelLineLength2: {
      type: Number,
      default: () => 10,
    },
    labelColor: {
      type: String,
      default: getThemeColor(),
    },
    secLabelColor: {
      type: String,
      default: getThemeColor(),
    },
    percentLabelColor: {
      type: String,
      default: getThemeColor(),
    },
    borderColor: { type: String, default: getBorderThemeColor() },
    borderRadius: { type: Number, default: 20 },
    borderWidth: { type: Number, default: 2 },
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
export default ComBaseRosePie;
</script>
<style lang="stylus" scoped></style>
