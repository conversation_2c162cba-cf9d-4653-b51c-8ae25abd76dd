<template lang="pug">
.w-full.h-full
  TaScreenEchartBase(:options='pieOption' ref='echartsRef')
</template>

<script lang="ts">
import echarts from 'echarts';
import { PropType, computed, defineComponent, ref, watch } from 'vue';
import { VObject } from '@/lib/vails';
import 'echarts-gl';
import { get3DPie } from '@/engines/screen/components/global/screen/charts/baseUtils/base3DPie'
//- 立体环形图
export default defineComponent({
  name: 'ComBase3DPie',
  props: {
    // 需要merge的option - 高级功能
    mergeOption: { type: Object, default: () => { } },
    // 是否完全替换
    isOnlyMerge: { type: Boolean, default: false },
    // ----------------------------------------------
    data: {
      type: Array as PropType<any>, default: [
        {
          name: 'aa',
          value: 70,
        },
        {
          name: 'bb',
          value: 32,
        },
        {
          name: 'ee',
          value: 20,
        },
        {
          name: 'cc',
          value: 40,
        },
      ]
    },
    colors: { type: Array as PropType<string[]>, default: ["#1EF0D7", "#3D67FF", "#EBC039", "#47BC79"] },
    gap: { type: Number, default: 0 },
    //扇形区域最大高度
    hoverHeight: { type: Number, default: 35 },
    maxHeight: { type: Number, default: 105 },
    minHeight: { type: Number, default: 35 },
    ratio: { type: Number, default: 0.59 },
    distance: { type: Number, default: 210 },
    //图形左右转动角度
    beta: { type: Number, default: 0 },
    autoRotate: { type: Boolean, default: false },
    //旋转操作的灵敏度，值越大越灵敏,最大1,最小0无法旋转
    rotateSensitivity: { type: Number, default: 0.5 },
    top: { type: String, default: '-10%' },
  },
  setup(props) {
    const echartsRef = ref();
    const mergeBaseOption = (options: VObject, onlyMerge: boolean) => {
      echartsRef.value?.mergeOption(options, onlyMerge);
    };

    const getAreaHeight = (nums: number[], cur: number) => {
      const max = Math.max(...nums)
      const min = Math.min(...nums)
      return (cur - min) / (max - min) * (props.maxHeight - props.minHeight) + props.minHeight
    }
    const hoverIndex = ref(null);

    const pieData = computed(() => props.data.map((d: any, i: number) => ({
      name: d.name,
      value: Number(d.value),
      itemStyle: { color: props.colors[i] },
      h: getAreaHeight(props.data.map((x: VObject) => Number(x.value)), Number(d.value))
    })))

    const pieOption = computed(() => {
      const options = get3DPie(
        pieData.value,
        0,
        hoverIndex.value,
        props.hoverHeight,
        props.gap,
        props.distance,
        props.beta,
        props.autoRotate,
        props.rotateSensitivity,
        props.top,
      )
      if (props?.mergeOption && Object.keys(props?.mergeOption).length > 0) {
        return (echarts as any).util.merge(options, props.mergeOption, props.isOnlyMerge);
      } else {
        return options;
      }
    });

    return {
      pieOption,
      echartsRef,
      mergeBaseOption,
    };
  },
});
</script>

<style lang="stylus" scoped></style>
