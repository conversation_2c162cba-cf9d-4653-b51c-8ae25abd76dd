<template lang="pug">
.w-full.h-full
  TaScreenEchartBase(:options='pieOption' ref='echartsRef')
</template>

<script lang="ts">
import echarts from 'echarts';
import { PropType, computed, defineComponent, ref, watch } from 'vue';
import { VObject } from '@/lib/vails';
import 'echarts-gl';
import {
  get3DPie,
  debounceFunc,
} from '@/engines/screen/components/global/screen/charts/baseUtils/base3DPie';
//- 立体环形图
export default defineComponent({
  name: 'ComBase3DDoughnutPie',
  props: {
    // 需要merge的option - 高级功能
    mergeOption: { type: Object, default: () => {} },
    // 是否完全替换
    isOnlyMerge: { type: Boolean, default: false },
    // ----------------------------------------------
    data: {
      type: Array as PropType<any>,
      default: [
        {
          name: 'cc',
          value: 50,
        },
        {
          name: 'aa',
          value: 59,
        },
        {
          name: 'bb',
          value: 32,
        },
        {
          name: 'ee',
          value: 16,
        },
      ],
    },
    colors: {
      type: Array as PropType<string[]>,
      default: ['#1EF0D7', '#3D67FF', '#EBC039', '#47BC79'],
    },
    gap: { type: Number, default: 1 },
    //扇形区域最大高度
    hoverHeight: { type: Number, default: 35 },
    firstHeight: { type: Number, default: 25 },
    ratio: { type: Number, default: 0.59 },
    distance: { type: Number, default: 150 },
    //图形左右转动角度
    beta: { type: Number, default: 0 },
    autoRotate: { type: Boolean, default: false },
    rotateSensitivity: { type: Number, default: 0.5 },
  },
  setup(props) {
    const echartsRef = ref();
    const mergeBaseOption = (options: VObject, onlyMerge: boolean) => {
      echartsRef.value?.mergeOption(options, onlyMerge);
    };
    const hoverIndex = ref(null);
    const pieData = computed(() =>
      props.data.map((d: any, i: number) => ({
        name: d.name,
        value: Number(d.value),
        itemStyle: { color: props.colors[i] },
        ...(i === 0 ? { h: props.firstHeight } : {}),
      })),
    );

    const pieOption = computed(() => {
      const options = get3DPie(
        pieData.value,
        props.ratio,
        hoverIndex.value,
        props.hoverHeight,
        props.gap,
        props.distance,
        props.beta,
        props.autoRotate,
        props.rotateSensitivity,
      );
      if (props?.mergeOption && Object.keys(props?.mergeOption).length > 0) {
        return (echarts as any).util.merge(options, props.mergeOption, props.isOnlyMerge);
      } else {
        return options;
      }
    });

    const hoverFunc = (params: any, mode = 'hover') => {
      if (hoverIndex.value === params.seriesIndex) return;
      if (mode === 'hover') {
        return (hoverIndex.value = params.seriesIndex);
      }
      if (mode === 'out') {
        return (hoverIndex.value = null);
      }
    };
    const hoverDebFunc = debounceFunc(hoverFunc);
    watch(
      () => echartsRef.value?.chartRef,
      () => {
        echartsRef.value?.chartRef?.on('mouseover', 'series.surface', (params: any) => {
          hoverDebFunc(params);
        });
        echartsRef.value?.chartRef?.on('globalout', 'series.surface', (params: any) => {
          hoverDebFunc(params, 'out');
        });
      },
      { immediate: true },
    );

    return {
      pieOption,
      echartsRef,
      mergeBaseOption,
    };
  },
});
</script>

<style lang="stylus" scoped></style>
