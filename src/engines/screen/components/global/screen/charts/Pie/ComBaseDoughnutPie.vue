<template lang="pug">
.com_base_doughnut_pie.w-full.h-full
  ComScreenBasePie(
    v-bind='$props'
  )
</template>
<!-- 环形图 -->
<script lang="ts">
import { PropType, defineComponent, toRefs } from 'vue';
import ComScreenBasePie from './ComScreenBasePie.vue';
import { PieData, PieRadius } from '../baseUtils/baseChat';
import { getBorderThemeColor } from '../../../../../utils/util';

const ComBaseDoughnutPie = defineComponent({
  name: 'ComBaseDoughnutPie',
  components: {
    ComScreenBasePie,
  },
  props: {
    data: {
      type: Array as PropType<PieData[]>,
      default: () => [
        {
          seriesName: 'pie-1',
          seriesData: [{ value: 1048, name: 'Search Engine' },
          { value: 735, name: 'Direct' },
          { value: 580, name: 'Email' },
          { value: 484, name: 'Union Ads' },
          { value: 300, name: 'Video Ads' }],
        },

      ],
    },
    radius: { type: Array as PropType<PieRadius>, default: () => ['54%', '90%'] },
    unit: { type: String, default: () => '' },
    showLegend: { type: Boolean, default: false },
    showLabel: {
      type: Boolean,
      default: () => false,
    },
    borderColor: { type: String, default: getBorderThemeColor() },
    borderWidth: { type: Number, default: 0 },
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
export default ComBaseDoughnutPie;
</script>
<style lang="stylus" scoped></style>
