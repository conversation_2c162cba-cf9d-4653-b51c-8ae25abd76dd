<template lang="pug">
.com_base_custom_pie.w-full.h-full
  ComScreenBasePie(
    v-bind='$props'
  )
</template>
<!-- 玫瑰图 -->
<script lang="ts">
import { PropType, defineComponent, toRefs } from 'vue';
import ComScreenBasePie from './ComScreenBasePie.vue';
import { PieData, PieRadius, PieRoseType } from '../baseUtils/baseChat';
import { getBorderThemeColor } from '../../../../../utils/util';

const ComBaseCustomPie = defineComponent({
  name: 'ComBaseCustomPie',
  components: {
    ComScreenBasePie,
  },
  props: {
    data: {
      type: Array as PropType<PieData[]>,
      default: () => [
        {
          seriesName: 'pie-1',
          seriesData: [{ value: 1048, name: 'Search Engine' },
          { value: 735, name: 'Direct' },
          { value: 580, name: 'Email' },
          { value: 484, name: 'Union Ads' },
          { value: 300, name: 'Video Ads' }],
        },
      ],
    },
    radius: { type: Array as PropType<PieRadius>, default: () => ['0%', '80%'] },
    roseType: { type: String as PropType<PieRoseType>, default: 'radius' },
    unit: { type: String, default: () => '' },
    showLegend: { type: Boolean, default: false },
    showLabel: {
      type: Boolean,
      default: () => true,
    },
    borderColor: { type: String, default: getBorderThemeColor() },
    borderRadius: { type: Number, default: 20 },
    borderWidth: { type: Number, default: 2 },
    isCustom: {
      type: Boolean,
      default: true,
    },
    customAreaColor: { type: String, default: '#76A9FA' },
    max: { type: Number, default: 1500 },
    min: { type: Number, default: 0 },
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
export default ComBaseCustomPie;
</script>
<style lang="stylus" scoped></style>
