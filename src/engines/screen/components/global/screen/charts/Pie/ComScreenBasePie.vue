<template lang="pug">
.w-full.h-full
  TaScreenEchartBase(:options='pieOption' ref='echartsRef')
</template>

<script lang="tsx">
import echarts from 'echarts';
import { PropType, computed, defineComponent, ref } from 'vue';
import {
  PieData,
  PieRadius,
  PieRoseType,
  PieLengendPosition,
  pieAreaColor,
  LegendOptType,
  LegendOptOrient,
} from '../baseUtils/baseChat';
import { VObject } from '@/lib/vails';
import { getBorderThemeColor, getThemeColor } from '../../../../../utils/util';

export default defineComponent({
  name: 'ComScreenBasePie',
  props: {
    // 需要merge的option - 高级功能
    mergeOption: { type: Object, default: () => { } },
    // 是否完全替换
    isOnlyMerge: { type: Boolean, default: false },
    customizeAreaColor: { type: Array, default: () => [] },
    // ----------------------------------------------
    // 是否显示图例
    showLegend: { type: Boolean, default: true },
    // vertical | horizontal
    legendOrient: { type: String as PropType<LegendOptOrient>, default: 'horizontal' },
    legendTextColor: { type: String, default: getBorderThemeColor() },
    // 图例位置 top right bottom right
    legendPosition: { type: Array as PropType<PieLengendPosition>, default: () => [0, 0, 0, 0] },
    legendType: { type: String as PropType<LegendOptType>, default: () => 'plain' },
    otherLegendOpts: { type: Object, default: () => { } },
    center: {
      type: Array,
      default: () => ['50%', '50%'],
    },
    mode: { type: String as PropType<'half' | 'normal'>, default: 'normal' },
    // 图表数据 seriesData 为纯数据项 , seriesName 对应的每一例数据代表的数据别称
    unit: { type: String, default: () => '' },
    bgColor: { type: String, default: () => getBorderThemeColor() },
    borderRadius: { type: Number, default: 4 },
    borderColor: { type: String, default: getBorderThemeColor() },
    borderWidth: { type: Number, default: 0 },
    data: {
      type: Array as PropType<PieData[]>,
      default: () => [
        {
          seriesName: 'pie-1',
          seriesData: [
            { value: 1048, name: 'Search Engine' },
            { value: 735, name: 'Direct' },
            { value: 580, name: 'Email' },
            { value: 484, name: 'Union Ads' },
            { value: 300, name: 'Video Ads' },
          ],
        },
      ],
    },
    radius: { type: Array as PropType<PieRadius>, default: () => ['0%', '60%'] },
    roseType: { type: String as PropType<PieRoseType>, default: false },
    // 图例标题
    title: {
      type: String,
      default: () => '',
    },
    showLabel: {
      type: Boolean,
      default: () => true,
    },
    showPercent: {
      type: Boolean,
      default: () => false,
    },
    labelLine: {
      type: Number,
      default: () => 1,
    },
    labelGap: {
      type: Number,
      default: () => 4,
    },
    labelLineLength: {
      type: Number,
      default: () => 10,
    },
    labelLineLength2: {
      type: Number,
      default: () => 10,
    },
    labelColor: {
      type: String,
      default: getThemeColor(),
    },
    secLabelColor: {
      type: String,
      default: getThemeColor(),
    },
    percentLabelColor: {
      type: String,
      default: getThemeColor(),
    },
    showBg: {
      type: Boolean,
      default: () => false,
    },
    isCustom: {
      type: Boolean,
      default: false,
    },
    customAreaColor: { type: String, default: '#c23531' },
    max: { type: Number, default: 1500 },
    min: { type: Number, default: 0 },
    showTooltip: {
      type: Boolean,
      default: () => false,
    },
    showOutsideCurrentStage: {
      type: Boolean,
      default: () => false,
    },
    showOutsideCurrentStageText: {
      type: String,
      default: () => 'text',
    },
    currentTextColor: {
      type: String,
      default: () => '',
    },
  },
  setup(props) {
    const echartsRef = ref();
    const mergeBaseOption = (options: VObject, onlyMerge: boolean) => {
      echartsRef.value?.mergeOption(options, onlyMerge);
    };
    const colors = [
      '#5470c6',
      '#91cc75',
      '#fac858',
      '#ee6666',
      '#73c0de',
      '#3ba272',
      '#fc8452',
      '#9a60b4',
      '#ea7ccc',
      '#844CDB',
      '#D07EF8',
    ];
    const pieOption = computed(() => {
      const finalAreaColor = [...props.customizeAreaColor, ...pieAreaColor];
      const [top, right, bottom, left] = props.legendPosition;
      const positionOpt = { top, right, bottom, left };
      const options = {
        backgroundColor: props.showBg ? props.bgColor : 'transparent',
        color: colors,
        ...(props.showTooltip
          ? {
            tooltip: {
              show: true,
              trigger: 'item',
              padding: 0,
              formatter: (params: any) => {
                console.log(params.dataIndex);
                return `<div style="padding:12px;color:#000;background:${finalAreaColor[params.dataIndex]
                  }">${params.name}: ${params.value}（${params.percent}%）</div>`;
              },
              borderWidth: 2,
              borderColor: '#fff',
            },
          }
          : {}),
        ...(props.showLegend
          ? {
            legend: {
              ...positionOpt,
              orient: props.legendOrient,
              type: props.legendType,
              ...props.otherLegendOpts,
              textStyle: {
                ...props.otherLegendOpts?.textStyle,
                color: props.legendTextColor,
              },
            },
          }
          : {}),
        ...(props.isCustom
          ? {
            visualMap: {
              show: false,
              min: props.min,
              max: props.max,
              inRange: {
                colorLightness: [0, 1],
              },
            },
          }
          : {}),
        series:
          props?.data?.map((el: any, index: number) => {
            const halfValue = el.seriesData.reduce((pre: any, pro: any) => pre + pro.value, 0);
            console.log(halfValue, 'halfValue', el.seriesData);
            const lastDataItem = {
              value: halfValue,
              itemStyle: { color: 'none', decal: { symbol: 'none' } },
            };
            const halfData = [...el.seriesData, lastDataItem];
            return {
              name: el.seriesName,
              type: 'pie',
              center: props.center,
              roseType: props.roseType,
              radius: props.radius,
              data: (props.mode === 'half' ? halfData : el.seriesData).map(
                (item: any, i: number) => {
                  if (props.showOutsideCurrentStage) {
                    if (item?.current) {
                      return {
                        value: item.value,
                        name: item?.name,
                        itemStyle: item.itemStyle || {
                          color: finalAreaColor[i],
                        },
                        label: {
                          show: true,
                          color: props.currentTextColor || finalAreaColor[i],
                          fontSize: 20,
                          formatter: () => {
                            return props.showOutsideCurrentStageText;
                          },
                        },
                        labelLine: {
                          show: true,
                          length: 10,
                          length2: 20,
                          lineStyle: {
                            width: 1,
                            type: 'solid',
                            color: props.currentTextColor || finalAreaColor[i],
                          },
                        },
                      };
                    } else {
                      return {
                        value: item.value,
                        name: item?.name,
                        label: { show: false },
                        labelLine: { show: false },
                        itemStyle: item.itemStyle || {
                          color: finalAreaColor[i],
                        },
                      };
                    }
                  } else {
                    return {
                      value: item.value,
                      name: item?.name,
                      itemStyle: item.itemStyle || {
                        color: finalAreaColor[i],
                      },
                    };
                  }
                },
              ),
              avoidLabelOverlap: true,
              startAngle: 180,
              label: {
                show: props.showLabel,
                formatter: (record: VObject) => {
                  if (record.name) {
                    if (props.showPercent) {
                      const curPercent = ((record.value / halfValue) * 100).toFixed(2);
                      if (props.labelLine === 1) {
                        return `{firstLine|${record.name}} {valueLine|${record.value}${props.unit}} {percentLine|${curPercent}%}`;
                      } else if (props.labelLine === 2) {
                        return `{firstLine|${record.name}} {valueLine|${record.value}${props.unit}}\n{secondPercentLine|${curPercent}%}`;
                      }
                    } else {
                      if (props.labelLine === 1) {
                        return `{firstLine|${record.name}} {valueLine|${record.value}${props.unit}}`;
                      } else if (props.labelLine === 2) {
                        return `{firstLine|${record.name}}\n{secondValueLine|${record.value}${props.unit}}`;
                      }
                    }
                  }
                },
                rich: {
                  firstLine: {
                    ...(props.labelColor
                      ? {
                        color: props.labelColor,
                      }
                      : {}),
                  },
                  valueLine: {
                    ...(props.secLabelColor
                      ? {
                        color: props.secLabelColor,
                      }
                      : {}),
                  },
                  secondValueLine: {
                    ...(props.secLabelColor
                      ? {
                        color: props.secLabelColor,
                      }
                      : {}),
                    padding: [props.labelGap, 0, props.labelGap, 0],
                  },
                  percentLine: {
                    ...(props.percentLabelColor
                      ? {
                        color: props.percentLabelColor,
                      }
                      : {}),
                  },
                  secondPercentLine: {
                    ...(props.percentLabelColor
                      ? {
                        color: props.percentLabelColor,
                      }
                      : {}),
                    padding: [props.labelGap, 0, props.labelGap, 0],
                  },
                },
              },
              labelLine: {
                show: true,
                lineStyle: {
                  type: 'solid',
                  cap: 'round',
                  width: 1,
                },
                length: props.labelLineLength,
                length2: props.labelLineLength2,
              },
              itemStyle: {
                borderRadius: props.borderRadius,
                borderColor: props.borderColor,
                borderWidth: props.borderWidth,
                ...(props.isCustom
                  ? {
                    color: props.customAreaColor,
                  }
                  : {}),
              },
              emphasis: {
                itemStyle: {
                  shadowBlur: 10,
                  shadowOffsetX: 0,
                  shadowColor: 'rgba(0, 0, 0, 0.5)',
                },
              },
            };
          }) || [],
      };
      if (props?.mergeOption && Object.keys(props?.mergeOption).length > 0) {
        return (echarts as any).util.merge(options, props.mergeOption, props.isOnlyMerge);
      } else {
        return options;
      }
    });

    return {
      pieOption,
      echartsRef,
      mergeBaseOption,
    };
  },
});
</script>

<style lang="stylus" scoped></style>
