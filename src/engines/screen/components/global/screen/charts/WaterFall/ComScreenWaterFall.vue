<template lang="pug">
.w-full.h-full 
  TaScreenEchartBase(:options='option' ref='echartsRef')
</template>

<script lang="ts">
import { ref, defineComponent, toRefs, computed, PropType } from 'vue';
import { barAreaColor, BarData } from '../baseUtils/baseChat';
import { cloneDeep, flatten, sum } from 'lodash';

const defaultData = [
  {
    seriesName: 'waterfall-1',
    seriesData: [200, -120, 150, 80, 70, 110],
  },
];

export default defineComponent({
  name: 'ComScreenWaterFall',
  components: {},
  props: {
    // 需要merge的option - 高级功能
    // mergeOption: { type: Object, default: () => {} },
    mergeOption: {
      type: Object,
      default: () => {},
    },
    // 是否完全替换
    isOnlyMerge: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Array as PropType<BarData[]>,
      default: () => defaultData,
    },
    chartType: {
      type: String as PropType<'vertical' | 'horizontal'>,
      default: 'vertical',
    },
    showLengend: { type: Boolean, default: false },

    // 图例位置 top right bottom right
    lengendPosition: {
      type: Array,
      default: () => ['top', 0, 0, 'center'],
    },
    // 图位置 top right bottom right
    gridPosition: {
      type: Array,
      default: () => ['10%', '10%', '10%', '10%'],
    },
    // X轴显示名称，为空则不显示X轴标
    axisLabel: {
      type: Array as PropType<string[]>,
      default: () => ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
    },
    // 瀑布图总量名称
    totalAxisLabelName: {
      type: String,
      default: '总量',
    },
    // 瀑布图总量位置
    reverseChart: {
      type: Boolean,
      default: false,
    }, // 是否显示单位
    showUnit: { type: Boolean, default: false },
    // 主轴单位
    unit: { formType: 'input', default: '' },
    // 主轴单位位置
    unitPadding: {
      type: Array as PropType<number[]>,
      default: () => [0, 0, 0, 0],
    },
    unitTitle: {
      type: String,
      default: '单位',
    },
    // 是否要显示数据点标注
    showLabel: {
      type: Boolean,
      default: false,
    },
    // 柱状图宽度
    barWidth: {
      type: String || Number,
      default: '',
    },
    // 柱条单种颜色or多种颜色or变化图正副值两种颜色
    colorType: {
      type: String as PropType<'single' | 'multiple' | 'doubleChange'>,
      default: 'single',
    },
    // 总量柱颜色
    totalAreaColor: {
      type: String,
      default: '#FF70CF',
    },
    barAreaColor: {
      type: Array as PropType<string[]>,
      default: () => barAreaColor,
    },
    // 是否要展示tooltip
    showTootip: {
      type: Boolean,
      default: true,
    },
    // 正值图例名
    positiveLegendName: {
      type: String,
      default: '正值',
    },
    // 负值图例名
    negativeLegendName: {
      type: String,
      default: '负值',
    },
    // 总量图例名
    totalLegendName: {
      type: String,
      default: '总量',
    },
    // 图例标题
    title: {
      type: String,
      default: '',
    },
    // 图例标题布局
    titlePadding: {
      type: Array as PropType<number[]>,
      default: () => [0, 0, 0, 16],
    },
  },
  setup(props) {
    const echartsRef = ref();

    const mergeBaseOption = (options: Object, onlyMerge: boolean) => {
      echartsRef.value?.mergeOption(options, onlyMerge);
    };
    const option = computed(() => {
      const [top, right, bottom, left] = props.lengendPosition;
      const positionOpt = { top, right, bottom, left };
      const cateData = cloneDeep([...props.axisLabel, props.totalAxisLabelName]);
      const categoryAxis = {
        type: 'category',
        data: props.reverseChart ? cateData.reverse() : cateData,
        axisTick: {
          show: false,
        },
        axisLine: {
          lineStyle: {
            color: '#EBF5FF',
            width: 0,
          },
        },
      };
      const unitOpt = {
        name: props.showUnit ? `${props.unitTitle}：` + props.unit : undefined,
        nameTextStyle: {
          padding: props.unitPadding,
        },
      };
      const valueAxis = {
        type: 'value',

        axisTick: {
          show: false,
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(255,255,255,0.1)',
          },
          show: true,
        },
        axisLine: {
          lineStyle: {
            color: '#EBF5FF',
            width: 0,
          },
        },
        max: sum(props.data[0]?.seriesData),
        min: 0,
      };
      return {
        grid: {
          top: props.gridPosition[0] || 0,
          right: props.gridPosition[1] || 0,
          bottom: props.gridPosition[2] || 0,
          left: props.gridPosition[3] || 0,
        },
        tooltip: {
          show: props.showTootip,
          trigger: 'axis',
          backgroundColor: 'white',
          textStyle: {
            color: 'black',
          },
          formatter: (raw_record: any) => {
            const record = raw_record.filter(
              (item: any, index: number) => item.seriesName !== '辅助空白',
            );
            const getChildren = () => {
              return record
                .map((item: any) => {
                  const color = item.color;
                  return `<div class="flex">
                      <span style="display:flex;margin-right:5px;margin-top:5.5px;border-radius:10px;width:10px;height:10px;background-color:${color};"></span>
                      <div class="mr-1">${record[0].name}: </div>
                      <div>
                        ${item.data?.label}${props.unit}
                      </div>
                    </div>`;
                })
                .join(' ');
            };
            return `<div>
                <div>${record[0]?.seriesName}</div>
                ${getChildren()}
              </div>`;
          },
        },
        ...(props.showLengend
          ? {
              legend: {
                data: [
                  props.positiveLegendName,
                  props.negativeLegendName,
                  props.totalLegendName,
                ].filter((ele: string) => ele),
                selectedMode: false,
                itemHeight: 6,
                itemWidth: 6,
                icon: 'roundRect',
                textStyle: {
                  color: 'white',
                },
                ...positionOpt,
              },
            }
          : {}),
        ...(props.chartType === 'vertical'
          ? {
              xAxis: categoryAxis,
              yAxis: { ...valueAxis, ...unitOpt },
            }
          : {
              xAxis: valueAxis,
              yAxis: { ...categoryAxis, ...unitOpt },
            }),
        ...(props.title
          ? {
              title: {
                text: props.title,
                padding: props.titlePadding,
                textStyle: {
                  color: '#FFFFFF',
                  'font-family': 'Inter',
                  'font-size': '14px',
                  'font-weight': 400,
                },
              },
            }
          : {}),
        series: flatten(
          props.data.map((el: any, index: number) => {
            const total = sum(el.seriesData);
            const rawData = [...el.seriesData, total];

            const blankSeries = rawData.map((item: any, index: any) => {
              if (index === 0 || index + 1 === rawData?.length) return 0;
              console.log(sum(el.seriesData.slice(0, index)));
              if (item >= 0) {
                return sum(el.seriesData.slice(0, index));
              } else {
                return sum(el.seriesData.slice(0, index)) + item;
              }
            });

            const dataSeries = rawData.map((raw: any, index: number) => {
              const isTotal = index === rawData.length - 1;

              return {
                label: raw,
                value: Math.abs(raw),
                isTotal,
                itemStyle: { color: getColorProps(raw, index, isTotal) },
                groupId: isTotal ? 2 : raw >= 0 ? 0 : 1,
              };
            });

            return [
              {
                type: 'bar', // 系列类型
                name: '辅助空白', // 系列名称, 用于tooltip的显示, legend 的图例筛选
                barWidth: props.barWidth || 'auto',
                itemStyle: {
                  normal: {
                    color: 'rgba(0,0,0,0)', // 柱条的颜色
                  },
                },
                data: props.reverseChart ? cloneDeep(blankSeries).reverse() : blankSeries, // 系列中的数据内容数组
                stack: true,
              },
              {
                type: 'bar', // 系列类型
                name: el.seriesName,
                barWidth: props.barWidth || 'auto',
                // 图形的样式
                itemStyle: {},
                // 系列中的数据内容数组
                data: props.reverseChart ? cloneDeep(dataSeries).reverse() : dataSeries, // 系列中的数据内容数组
                stack: true,
                ...(props.showLabel
                  ? {
                      label: {
                        show: true,
                        color: 'white',
                        position: props.chartType === 'vertical' ? 'top' : 'right',
                        formatter: (params: any) => {
                          return params.data?.label;
                        },
                      },
                    }
                  : {}),
              },
              // 只描述name用于图例
              {
                type: 'bar', // 系列类型
                name: props?.positiveLegendName || el.seriesName,
                barWidth: props.barWidth || 'auto',
                // 图形的样式
                // 系列中的数据内容数组
                data: [],
                stack: true,
                itemStyle: {
                  normal: {
                    color: props.barAreaColor[0], // 柱条的颜色
                  },
                },
              },
              {
                type: 'bar', // 系列类型
                name: props?.negativeLegendName || el.seriesName,
                barWidth: props.barWidth || 'auto',
                // 图形的样式
                // 系列中的数据内容数组
                data: [],
                stack: true,
                itemStyle: {
                  normal: {
                    color: props.barAreaColor[1], // 柱条的颜色
                  },
                },
              },
              {
                type: 'bar', // 系列类型
                name: props?.totalLegendName || el.seriesName,
                barWidth: props.barWidth || 'auto',
                // 图形的样式
                // 系列中的数据内容数组
                data: [],
                stack: true,
                itemStyle: {
                  normal: {
                    color: props.totalAreaColor, // 柱条的颜色
                  },
                },
              },
            ];
          }),
        ),
      };
    });

    const getColorProps = (val: any, index: number, isTotal: boolean): string => {
      if (isTotal) return props.totalAreaColor;
      switch (props.colorType) {
        case 'single':
          return props.barAreaColor[0];
        case 'multiple':
          return props.barAreaColor[index];
        case 'doubleChange':
          return val >= 0 ? props.barAreaColor[0] : props.barAreaColor[1];
      }
    };
    return {
      ...toRefs(props),
      option,
      echartsRef,
      mergeBaseOption,
    };
  },
});
</script>

<style lang="stylus" scoped></style>
