<template lang="pug">
.com_base_bg_bar.w-full.h-full
  ComScreenWaterFall(
    v-bind='$props'
  )
</template>
<script lang="ts">
import { PropType, defineComponent, toRefs } from 'vue';
import ComScreenWaterFall from './ComScreenWaterFall.vue';
import { barAreaColor, BarData, BaseData } from '../baseUtils/baseChat';

const defaultData = [
  {
    seriesName: 'waterfall-1',
    seriesData: [200, -120, 260, -10, -70, 110],
  },
];

export default defineComponent({
  name: 'ComBaseVerticalChangeWaterFall',
  components: {
    ComScreenWaterFall,
  },
  props: {
    // 需要merge的option - 高级功能
    // mergeOption: { type: Object, default: () => {} },
    mergeOption: {
      type: Object,
      default: () => {},
    },
    // 是否完全替换
    isOnlyMerge: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Array as PropType<BarData[]>,
      default: () => defaultData,
    },
    chartType: {
      type: String as PropType<'vertical' | 'horizontal'>,
      default: 'vertical',
    },
    showLengend: { type: Boolean, default: true },
    // 图例位置 top right bottom right
    lengendPosition: {
      type: Array,
      default: () => ['top', 0, 0, 'center'],
    },
    // 图位置 top right bottom right
    gridPosition: {
      type: Array,
      default: () => ['10%', '10%', '10%', '10%'],
    },
    // X轴显示名称，为空则不显示X轴标
    axisLabel: {
      type: Array as PropType<string[]>,
      default: () => ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
    },
    // 瀑布图总量名称
    totalAxisLabelName: {
      type: String,
      default: '总量',
    },
    // 瀑布图总量位置
    reverseChart: {
      type: Boolean,
      default: false,
    }, // 是否显示单位
    showUnit: { type: Boolean, default: false },
    // 主轴单位
    unit: { formType: 'input', default: '' },
    // 主轴单位位置
    unitPadding: {
      type: Array as PropType<number[]>,
      default: () => [0, 0, 0, 0],
    },
    unitTitle: {
      type: String,
      default: '单位',
    },
    // 是否要显示数据点标注
    showLabel: {
      type: Boolean,
      default: false,
    },
    // 柱状图宽度
    barWidth: {
      type: String || Number,
      default: '',
    },
    // 柱条单种颜色or多种颜色or变化图正副值两种颜色
    colorType: {
      type: String as PropType<'single' | 'multiple' | 'doubleChange'>,
      default: 'doubleChange',
    },
    // 总量柱颜色
    totalAreaColor: {
      type: String,
      default: '#FF70CF',
    },
    barAreaColor: {
      type: Array as PropType<string[]>,
      default: () => barAreaColor,
    },
    // 是否要展示tooltip
    showTootip: {
      type: Boolean,
      default: true,
    },
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
</script>
<style lang="stylus" scoped></style>
