<template lang="pug">
.w-full.h-full 
  TaScreenEchartBase(:options='option' ref='echartsRef')
</template>
<script lang="ts">
import { VObject } from '@/lib/vails';
import { PropType, computed, defineComponent, ref, toRefs } from 'vue';
import { uniqBy } from 'lodash-es';
import { ThemeRiverData, ThemeRiverDataType, lineColor } from '../baseUtils/baseChat';
import echarts from 'echarts';

const ComBaseThemeRiver = defineComponent({
  name: 'ComBaseThemeRiver',
  components: {},
  props: {
    // 需要merge的option - 高级功能
    mergeOption: { type: Object, default: () => {} },
    // 是否完全替换
    isOnlyMerge: { type: Boolean, default: false },
    // 自定义色彩，【或者可以支持组选新颜色?】
    customizeLineColor: { type: Array, default: () => [] },

    // 数据源
    data: { type: Array as PropType<ThemeRiverData>, default: () => [] },
  },
  setup(props) {
    const echartsRef = ref();
    const mergeBaseOption = (options: VObject, onlyMerge: boolean) => {
      echartsRef.value?.mergeOption(options, onlyMerge);
    };
    const option = computed(() => {
      const finalLineColor = [...props.customizeLineColor, lineColor];
      const options = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'line',
            lineStyle: {
              color: 'rgba(255,255,255,0.2)',
              width: 1,
              type: 'solid',
            },
          },
        },
        legend: {
          data: uniqBy(props.data, (r: ThemeRiverDataType) => r[2]).map(
            (el: ThemeRiverDataType) => el[2],
          ),
          itemHeight: 2,
          itemWidth: 8,
          icon: 'roundRect',
          textStyle: {
            color: 'white',
          },
        },
        singleAxis: {
          top: 50,
          bottom: 50,
          axisTick: {},
          axisLabel: {},
          type: 'time',
          axisPointer: {
            animation: true,
            label: {
              show: false,
            },
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: '#fff',
              width: 1,
            },
          },
          splitLine: {
            show: false,
          },
        },
        series: [
          {
            type: 'themeRiver',
            label: {
              show: false,
            },

            emphasis: {
              itemStyle: {
                shadowBlur: 6,
                shadowColor: 'rgba(0, 0, 0, 0.8)',
              },
            },
            data: [...props.data],
            color: [...finalLineColor].splice(
              0,
              uniqBy(props.data, (r: ThemeRiverDataType) => r[2]).length,
            ),
          },
        ],
      };
      if (props?.mergeOption && Object.keys(props?.mergeOption).length > 0) {
        return (echarts as any).util.merge(options, props.mergeOption, props.isOnlyMerge);
      } else {
        return options;
      }
    });
    return {
      ...toRefs(props),
      option,
      echartsRef,
      mergeBaseOption,
    };
  },
});
export default ComBaseThemeRiver;
</script>
<style lang="stylus" scoped></style>
