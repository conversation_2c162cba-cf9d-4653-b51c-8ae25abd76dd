<template lang="pug">
.w-full.h-full.overflow-y-scroll 
  .flex.flex-col
    .w-full(v-for='(record,index) in scheduleList')
      template(v-if='showType === "type1"')
        .w-full.flex.items-center.justify-between.my-2
          .type1_name.text-sm.text-white(:style='{width: `${labelWidth}px`,minWidth:`${labelWidth}px`,maxWidth:`${labelWidth}px`}') No.{{index+1}} {{ record.name }}
          .w-0.flex-grow.px-5.h-14px
            .w-full.relative.h-10px.bg-ranking-bar-base
              .h-10px.bg-base-ranking-type-1.relative(:style='{width: `${record.percent}`}')
                .absolute.h-14px.w-2px.right-0.bg-white.rank_bar_bar
          .type1_value.text-sm.text-white.text-right(:style='{width: `${valueWidth}px`,minWidth:`${valueWidth}px`,maxWidth:`${valueWidth}px`}') {{ record.value }}{{ record.unit }}
      template(v-if='showType === "type2"')
        .w-full.flex.flex-col.mb-4 
          .w-full.flex.items-center.justify-between.pb-1 
            .text-sm.text-white(:style='{width: `${labelWidth}px`,minWidth:`${labelWidth}px`,maxWidth:`${labelWidth}px`}') No.{{index+1}} {{ record.name }}
            .text-sm.text-white.text-right(:style='{width: `${valueWidth}px`,minWidth:`${valueWidth}px`,maxWidth:`${valueWidth}px`}') {{ record.value }}{{ record.unit }}
          .w-full.relative.h-10px.bg-ranking-bar-base
            .h-10px.bg-base-ranking-type-2.relative(:style='{width: `${record.percent}`}')
              .absolute.h-14px.w-2px.right-0.bg-white.rank_bar_bar
      template(v-if='showType === "type3"')
        .w-full.flex.flex-col.mb-4 
          .w-full.flex.items-center.justify-between.pb-2
            .flex.items-center
              img.w-10px.h-10px.mr-1(src='https://innomatch.oss-cn-shanghai.aliyuncs.com/die.png')
              .text-sm.text-white.border_type3.mr-1.px-2px No.{{index+1}}
              .text-sm.text-white {{ record.name }}
            .flex.items-center 
              .text-sm.text-white {{ record.value }}
              .text-sm.oth-color(v-if='record.unit') {{ record.unit }}
          .w-full.relative.h-10px.bg-ranking-bar-base
            .h-10px.bg-base-ranking-type-3.relative(:style='{width: `${record.percent}`}')
      template(v-if='showType === "type4"')
        .w-full.flex.flex-col.mb-4 
          .w-full.flex.items-center.pb-2
            .flex.items-center
              img.w-3.h-2.mr-1(src='https://innomatch.oss-cn-shanghai.aliyuncs.com/die_2.png')
              .text-sm.text-white.border_type4.mr-1.px-2px No.{{index+1}}
              .text-sm.text-white {{ record.name }}
            
          .w-full.flex.items-center
            .w-0.flex-grow.relative.h-10px.bg-ranking-bar-base.rounded-lg
              .h-10px.bg-base-ranking-type-4.relative.rounded-lg(:style='{width: `${record.percent}`}')
            .type1_value.text-sm.b_4_color.text-right.ml-1(:style='{width: `${valueWidth}px`,minWidth:`${valueWidth}px`,maxWidth:`${valueWidth}px`}') {{ record.value }}{{ record.unit }}
      template(v-if='showType === "type5"')
        .w-full.flex.flex-col.mb-4 
          .w-full.flex.items-center.pb-6px.justify-between
            .flex.items-center
              .text-sm.font-bold.rank_type_4_c(v-if='index <= 2' :class='`rank_type_4_${index+1}`') Top{{index+1}}
              .text-sm.font-bold.rank_type_4_c(v-else='index > 2' :class='`rank_type_4_${index+1}`') Top{{index+1}}
              .text-sm.text-white.ml-2 {{ record.name }}
            
            .text-sm.b_4_color  {{ record.value }}
          .w-full.relative.h-10px.bg-ranking-bar-base.rounded-lg
            .h-10px.bg-base-ranking-type-4.relative.rounded-lg(:style='{width: `${record.percent}`}')
      template(v-if='showType === "type6"')
        .w-full.flex.flex-col.mb-4 
          .w-full.flex.items-center.pb-2
            .flex.items-center
              .text-sm.text-white.border_type3.mr-1.px-2px No.{{index+1}}
              .text-sm.text-white {{ record.name }}
            
          .w-full.flex.items-center
            .h-10px.bg-base-ranking-type-6.relative.flex.overflow-hidden.flex-nowrap(:style='{width: `${record.percent}`}')
              .w-3.h-10px.min-w-3.max-w-3.flex.flex-row.items-center(v-for='item in 30')
                .w-2.h-10px.bg-base-ranking-type-3
                .w-1.h-10px
            .text-white.text-sm.b_4_color.text-right.ml-1(:style='{width: `${valueWidth}px`,minWidth:`${valueWidth}px`,maxWidth:`${valueWidth}px`}') {{ record.value }}{{ record.unit }}
</template>
<script lang="ts">
import { PropType, computed, defineComponent, toRefs } from 'vue';
import { RankingBarData } from '../baseUtils/baseChat';
const ComBaseRankingBar = defineComponent({
  name: 'ComBaseRankingBar',
  components: {},
  props: {
    data: {
      type: Array as PropType<RankingBarData[]>,
      default: () => [],
    },
    showType: {
      type: String,
      default: () => 'type1',
    },
    unit: {
      type: String,
      default: () => '',
    },
    serierMax: {
      type: Number,
      default: () => 0,
    },
    labelWidth: {
      type: Number,
      default: () => 80,
    },
    valueWidth: {
      type: Number,
      default: () => 60,
    },
  },
  setup(props) {
    const name = computed(() => {
      return [...props.data].map((el: any) => el.seriesName);
    });

    const scheduleList = computed(() => {
      const vdata = [...props.data].sort(
        (a: RankingBarData, b: RankingBarData) => b.seriesData - a.seriesData,
      )[0].seriesData;
      const maxWidth = props.serierMax ? props.serierMax : vdata * 1.3;
      const getPercent = (v: number) => {
        return `${(v / maxWidth) * 100}%`;
      };
      return [...props.data]
        .sort((a: RankingBarData, b: RankingBarData) => b.seriesData - a.seriesData)
        .map((el: RankingBarData) => {
          return {
            name: el.seriesName,
            value: isNaN(Number(el.seriesData))
              ? el.seriesData
              : Number(el.seriesData).toLocaleString(),
            percent: getPercent(el.seriesData),
            unit: props.unit,
          };
        });
    });

    return {
      ...toRefs(props),
      name,
      scheduleList,
    };
  },
});
export default ComBaseRankingBar;
</script>
<style lang="stylus" scoped>
.bg-ranking-bar-base
  background rgba(255,255,255,0.1)
.bg-base-ranking-type-1{
  background linear-gradient(to left, #3F95CE, #1EE7E7)
}
.bg-base-ranking-type-2{
  background linear-gradient(to left, #36EAAF, #4BB3D2)
}
.bg-base-ranking-type-3{
  background linear-gradient(to left, #1890FF, #1EE7E7)
}
.bg-base-ranking-type-4{
  background linear-gradient(to left, rgba(5,214,231,1.000), rgba(5,214,231,0.1))
}
.rank_bar_bar
  top -2px
.border_type3
  border: 1px solid;
  border-image: linear-gradient(to right,#1EE7E7, #1890FF) 1;
.border_type4
  border: 1px solid;
  border-image: linear-gradient(to right,#1EE7E7, #18D5FF) 1;
.oth_color
  color #E6F7FF;
.b_4_color
  color #06D6E7
.rank_type_4_c
  color #3F83F8
.rank_type_4_1
  color #FACA15
.rank_type_4_2
  color #C3DDFD
.rank_type_4_3
  color #FF8A4C
</style>
