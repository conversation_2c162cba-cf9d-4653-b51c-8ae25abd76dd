<template lang="pug">
.w-full.h-full
  TaScreenEchartBase(:options='barOption', ref='echartsRef')
</template>

<script lang="ts">
import { VObject } from '@/lib/vails';
import { PropType, computed, defineComponent, ref } from 'vue';
import echarts from 'echarts';
import { flatten } from 'lodash';
import {
  BarData,
  Square3DColor,
  barAreaColor,
  lineAreaColor,
  lineColor,
  Circle3DColor,
  Circle3DBgColor,
} from '../baseUtils/baseChat';

import {
  axisMarginPropsFn,
  axisPropsFn,
  FontPropsFn,
  legendPropsFn,
  lineSeriesFn,
  barSeriesFn,
  axisLabelFormatter,
} from '../../components/echartBaseProps';
import {
  getThemeColor,
  getThemeItemLineColor,
  getThemeLabelLineColor,
} from '@/engines/screen/utils/util';

export default defineComponent({
  name: 'ComScreenBaseBarLine',
  props: {
    // 需要merge的option - 高级功能
    // mergeOption: { type: Object, default: () => {} },
    mergeOption: {
      type: Object,
      default: () => {},
    },
    // 是否完全替换
    isOnlyMerge: {
      type: Boolean,
      default: false,
    },

    // ----------------------------------------------
    // 是否显示图例
    showLengend: { type: Boolean, default: false },
    // 图表数据 seriesData 为纯数据项 , seriesName 对应的每一例数据代表的数据别称

    data: {
      label: '数据静态值',
      formType: 'json',
      type: Array,
      default: () => [],
    },
    // data: {
    //   type: Array as PropType<BarData[]>,
    //   default: () => [
    //     {
    //       seriesName: 'bar-1',
    //       seriesData: [120, 188, 150, 80, 70, 110],
    //     },
    //     // {
    //     //   seriesName: 'bar-2',
    //     //   seriesData: [120, 200, 150, 80, 70, 110, 130].reverse(),
    //     // },
    //     // {
    //     //   seriesName: 'bar-3',
    //     //   seriesData: [90, 100, 350, 180, 30, 100, 230],
    //     // },
    //     // {
    //     //   seriesName: 'bar-4',
    //     //   seriesData: [120, 200, 150, 80, 70, 110, 130].reverse(),
    //     // },
    //     // {
    //     //   seriesName: 'bar-5',
    //     //   seriesData: [10, 20, 30, 50, 30, 10, 20],
    //     // },
    //   ],
    // },
    // 图例位置 top right bottom right
    lengendPosition: {
      type: Array,

      default: () => ['top', 0, 0, 'center'],
    },
    // 图位置 top right bottom right
    gridPosition: {
      type: Array,
      default: () => ['10%', '10%', '10%', '10%'],
    },
    // X轴显示名称，为空则不显示X轴标
    xAxisLabel: {
      type: Array as PropType<string[]>,
      default: () => ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
    },
    // 图例标题
    title: {
      type: String,
      default: '',
    },
    // 图例标题布局
    titlePadding: {
      type: Array as PropType<number[]>,
      default: () => [0, 0, 0, 16],
    },
    // 是否显示Y轴
    showYAxis: { type: Boolean, default: true },
    // Y轴数据，为2则代表有两条Y轴，最大为2
    yAxisNum: {
      type: Number as PropType<1 | 2>,
      default: 2,
    },
    // 是否显示Y轴单位
    showUnit: { type: Boolean, default: true },
    // 主轴单位
    unit: { type: String, default: '' },
    // 副轴单位
    subUnit: { type: String, default: '' },
    // 单位标题
    unitTitle: {
      type: String,
      default: '单位',
    },
    // 副轴单位标题
    subUnitTitle: {
      type: String,
      default: '单位',
    },
    // 主轴单位位置
    unitPadding: {
      type: Array as PropType<number[]>,
      default: () => [0, 0, 0, 0],
    },
    // 辅轴单位位置
    subUnitPadding: {
      type: Array as PropType<number[]>,
      default: () => [0, 0, 0, 0],
    },
    // 是否要显示数据点标注
    showLabel: {
      type: Boolean,
      default: false,
    },
    // 是否要展示柱状图背景
    showBg: {
      type: Boolean,
      default: false,
    },
    // 柱状图宽度
    barWidth: {
      type: String || Number,
      default: '',
    },
    // x轴label倾斜度
    labelXRotate: {
      type: Number,
      default: 0,
    },
    // 是否堆积图
    isStack: {
      type: Boolean,
      default: false,
    },
    // 是否3d柱状图
    is3DSquare: {
      type: Boolean,
      default: false,
    },
    // 是否3d圆柱图
    is3DCircle: {
      type: Boolean,
      default: false,
    },
    // 柱条单色、渐变色
    colorType: {
      type: String as PropType<'solid' | 'gradient'>,
      default: 'gradient',
    },
    barAreaColor: {
      type: Array as PropType<string[]>,
      default: () => barAreaColor,
    },
    lineAreaColor: {
      type: Array as PropType<string[]>,
      default: () => lineAreaColor,
    },
    lineColor: {
      type: Array as PropType<string[]>,
      default: () => lineColor,
    },
    // 是否要展示tooltip
    showTootip: {
      type: Boolean,
      default: true,
    },
    // 是否反转Y轴单位
    reverseUnitYAxis: {
      type: Boolean,
      default: false,
    },
    // 是否使用平滑曲线
    isSmooth: {
      type: Boolean,
      default: () => true,
    },
  },
  setup(props: VObject) {
    // const seriesData = getScreenData(props, 'series');
    // console.log('seriesData', seriesData);
    const echartsRef = ref();
    const mergeBaseOption = (options: VObject, onlyMerge: boolean) => {
      echartsRef.value?.mergeOption(options, onlyMerge);
    };
    const barOption = computed(() => {
      return {
        grid: {
          top: props.gridPosition[0] || 0,
          right: props.gridPosition[1] || 0,
          bottom: props.gridPosition[2] || 0,
          left: props.gridPosition[3] || 0,
        },
        tooltip: {
          show: props.showTootip,
          trigger: 'axis',
          backgroundColor: 'white',
          textStyle: {
            color: 'black',
          },
          formatter: (record: VObject) => {
            const getChildren = () => {
              return record
                .map((item: any) => {
                  console.log('item', item);
                  const color = item.color?.colorStops
                    ? item.color?.colorStops[1].color
                    : item.color;
                  return `<div class="flex">
                      <span style="display:flex;margin-right:5px;margin-top:5.5px;border-radius:10px;width:10px;height:${'10px'};background-color:${color};"></span>
                      <div class="mr-1">${item.seriesName}: </div>
                      <div>
                        ${item.data}${item.seriesType === 'bar' ? props.unit : props.subUnit}
                      </div>
                    </div>`;
                })
                .join(' ');
            };
            return `<div>
                <div>${record[0].name}</div>
                ${getChildren()}
              </div>`;
          },
        },
        ...(props.title
          ? {
              title: {
                text: props.title,
                padding: props.titlePadding,
                textStyle: {
                  color: getThemeColor(),
                  'font-family': 'Inter',
                  'font-size': '14px',
                  'font-weight': 400,
                },
              },
            }
          : {}),
        ...(props.showLengend
          ? {
              legend: {
                data: props.data.map((el: any) => el.seriesName),
                itemHeight: 6,
                itemWidth: 6,
                icon: 'roundRect',
                textStyle: {
                  color: getThemeColor(),
                },
                top: props.lengendPosition[0] || 0,
                right: props.lengendPosition[1] || 0,
                bottom: props.lengendPosition[2] || 0,
                left: props.lengendPosition[3] || 0,
              },
            }
          : {}),
        xAxis: {
          type: 'category',
          data: props.xAxisLabel,
          axisTick: {
            show: false,
          },
          axisLine: {
            lineStyle: {
              color: '#EBF5FF',
              width: 0,
            },
          },
          axisLabel: {
            rotate: props.labelXRotate,
          },
        },
        yAxis: new Array(props.yAxisNum).fill('').map((el: any, index: number) => {
          return {
            type: 'value',
            name: props.showUnit
              ? `${index === 0 ? props.unitTitle : props.subUnitTitle}：` +
                (index === 0 ? props.unit : props.subUnit)
              : undefined,
            nameTextStyle: {
              padding: [...(index === 0 ? props.unitPadding : props.subUnitPadding)],
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              lineStyle: {
                color: getThemeLabelLineColor(),
              },
              show: index < 1,
            },
            axisLine: {
              lineStyle: {
                color: '#EBF5FF',
                width: 0,
              },
            },
            yAxisIndex: props.reverseUnitYAxis ? (index === 0 ? 1 : 0) : index,
            show: props.showYAxis,
          };
        }),
        series: flatten(
          props.data
            .map((el: any, index: number) => {
              if (el.seriesType === 'bar') {
                if (props.is3DSquare) {
                  const barWidth3D = Number(props.barWidth || 30);
                  const colorArr = Square3DColor[index];
                  const sumList = Array.from(
                    { length: props.data?.[0]?.seriesData?.length },
                    (_, i) =>
                      props.data
                        .slice(0, index + 1)
                        .reduce((acc: number, curr: VObject) => acc + curr.seriesData[i], 0),
                  );
                  const sumListBottom = Array.from(
                    { length: props.data?.[0]?.seriesData?.length },
                    (_, i) =>
                      props.data
                        .slice(0, index)
                        .reduce((acc: number, curr: VObject) => acc + curr.seriesData[i], 0),
                  );

                  const color = {
                    type: 'linear',
                    x: 0,
                    x2: 1,
                    y: 0,
                    y2: 0,
                    colorStops: [
                      {
                        offset: 0,
                        color: colorArr[0],
                      },
                      {
                        offset: 0.5,
                        color: colorArr[0],
                      },
                      {
                        offset: 0.5,
                        color: colorArr[1],
                      },
                      {
                        offset: 1,
                        color: colorArr[1],
                      },
                    ],
                  };
                  return [
                    {
                      z: index * 3,
                      name: el.seriesName,
                      type: 'bar',
                      barWidth: barWidth3D,
                      barGap: '0%',
                      data: el.seriesData,
                      itemStyle: {
                        normal: {
                          color,
                        },
                      },
                      stack: props.isStack,
                    },
                    props.isStack && {
                      z: index * 3 + 1,
                      name: el.seriesName,
                      type: 'pictorialBar',
                      data: sumListBottom,
                      symbol: 'diamond',
                      symbolPosition: index === 0 ? 'start' : 'end',

                      symbolOffset: ['0%', '-50%'],
                      symbolSize: [barWidth3D, 10],
                      itemStyle: {
                        normal: {
                          color,
                        },
                      },
                      tooltip: {
                        show: false,
                      },
                      stack: props.isStack,
                    },
                    (!props.isStack || index + 1 === props.data.length) && {
                      z: index * 3 + 2,
                      name: el.seriesName,
                      type: 'pictorialBar',
                      symbolPosition: 'end',
                      data: sumList,
                      symbol: 'diamond',
                      symbolOffset: ['0%', '-50%'],
                      symbolSize: [barWidth3D - 4.5, (10 * (barWidth3D - 4.5)) / barWidth3D],
                      itemStyle: {
                        normal: {
                          borderColor: colorArr[2],
                          borderWidth: 2,
                          color: colorArr[2],
                        },
                      },
                      tooltip: {
                        show: false,
                      },
                      stack: props.isStack,
                    },
                  ] as any;
                } else if (props.is3DCircle) {
                  console.log('Circle3DColor', Circle3DColor);
                  const barWidth3D = Number(props.barWidth || 16);
                  const getColor = (i: number) => {
                    return {
                      type: 'linear',
                      x: 0,
                      y: 0,
                      x2: 0,
                      y2: 1,
                      colorStops: [
                        {
                          offset: 0,
                          color: Circle3DColor[i][0], // 0% 处的颜色
                        },
                        {
                          offset: 1,
                          color: Circle3DColor[i][1], // 100% 处的颜色
                        },
                      ],
                    };
                  };
                  const digit =
                    Math.floor(Math.log10(Math.max(...el.seriesData))) > 0
                      ? Math.floor(Math.log10(Math.max(...el.seriesData)))
                      : 1;

                  const maxValue =
                    Math.ceil(Math.max(...el.seriesData) / Math.pow(10, digit)) *
                    Math.pow(10, digit);
                  console.log(
                    'maxValue',
                    digit,
                    maxValue,
                    el.seriesData,
                    Math.floor(Math.log10(Math.max(...el.seriesData))),
                  );

                  return [
                    {
                      z: index * 3,
                      name: el.seriesName,
                      type: 'bar',
                      barWidth: barWidth3D,
                      barGap: '0%',
                      data: el.seriesData.map((el: number, i: number) => ({
                        value: el,
                        itemStyle: {
                          color: getColor(i),
                        },
                      })),
                      stack: true,
                    },
                    {
                      z: index * 3 + 1,
                      name: el.seriesName,
                      type: 'pictorialBar',
                      data: el.seriesData.map((el: number, i: number) => ({
                        value: el,
                        itemStyle: {
                          color: Circle3DColor[i][1],
                        },
                      })),
                      symbolPosition: 'start',

                      symbolOffset: ['0%', '30%'],
                      symbolSize: [barWidth3D, 8],
                      itemStyle: {
                        normal: {
                          color: Circle3DColor[index][1],
                        },
                      },
                      tooltip: {
                        show: false,
                      },
                    },

                    {
                      z: index * 3 + 2,
                      name: el.seriesName,
                      type: 'pictorialBar',
                      data: el.seriesData.map((el: number, i: number) => ({
                        value: el,
                        itemStyle: {
                          color: Circle3DColor[i][2],
                        },
                      })),
                      symbolPosition: 'end',

                      symbolOffset: ['0%', '-50%'],
                      symbolSize: [barWidth3D, 8],
                      tooltip: {
                        show: false,
                      },
                    },

                    {
                      z: index * 3,
                      name: el.seriesName,
                      type: 'bar',
                      barWidth: barWidth3D,
                      barGap: '0%',
                      data: el.seriesData.map((el: number, i: number) => ({
                        value: maxValue - el,
                        itemStyle: {
                          color: Circle3DBgColor[i][0],
                        },
                      })),
                      stack: true,
                      label: {
                        show: true,
                        position: 'top',
                        color: getThemeColor(),
                        distance: 10,
                        formatter: (params: any) => {
                          return (((maxValue - params.value) / maxValue) * 100).toFixed(0) + '%';
                        },
                      },
                    },
                    {
                      z: index * 3 + 2,
                      name: el.seriesName,
                      type: 'pictorialBar',
                      data: el.seriesData.map((el: number, i: number) => ({
                        value: maxValue,
                        itemStyle: {
                          color: Circle3DBgColor[i][1],
                        },
                      })),
                      symbolPosition: 'end',

                      symbolOffset: ['0%', '-50%'],
                      symbolSize: [barWidth3D, 8],
                      tooltip: {
                        show: false,
                      },
                    },
                  ];
                } else {
                  return {
                    data: el.seriesData,
                    name: el.seriesName,
                    type: 'bar',
                    itemStyle: {
                      color:
                        props.colorType === 'gradient'
                          ? {
                              type: 'linear',
                              x: 0,
                              y: 0,
                              x2: 0,
                              y2: 1,
                              colorStops: [
                                {
                                  offset: 1,
                                  color: 'rgba(86, 204, 242, 0)', // 0% 处的颜色
                                },
                                {
                                  offset: 0,
                                  color: props.barAreaColor[index], // 100% 处的颜色
                                },
                              ],
                            }
                          : props.barAreaColor[index],
                    },
                    barWidth: props.barWidth || 'auto',
                    stack: props.isStack,
                    yAxisIndex: props.yAxisNum > 1 ? 0 : 0,
                    ...(props.showBg
                      ? {
                          showBackground: true,
                          backgroundStyle: {
                            color: getThemeItemLineColor(),
                          },
                        }
                      : {}),
                  };
                }
              } else if (el.seriesType === 'line') {
                return {
                  data: el.seriesData,
                  name: el.seriesName,
                  type: 'line',
                  symbol: 'none',

                  smooth: props.isSmooth || false,

                  lineStyle: {
                    type: 'solid',
                    width: 4,
                  },
                  itemStyle: {
                    color: props.lineColor[index],
                  },
                  ...(props.lineAreaColor[index] && {
                    areaStyle: {
                      color: {
                        type: 'linear',
                        x: 0,
                        y: 0,
                        x2: 0,
                        y2: 1,
                        colorStops: [
                          {
                            offset: 1,
                            color: 'rgba(86, 204, 242, 0.00)', // 0% 处的颜色
                          },
                          {
                            offset: 0,
                            color: props.lineAreaColor[index], // 100% 处的颜色
                          },
                        ],
                      },
                    },
                  }),
                  yAxisIndex: props.yAxisNum > 1 ? 1 : 0,
                };
              }
            })
            .filter((item: any) => item),
        ),
        // series: [
        //   {
        //     z: 1,
        //     name: '数据',
        //     type: 'bar',
        //     barWidth: props.barWidth || 'auto',
        //     barGap: '0%',
        //     data,
        //     itemStyle: {
        //       normal: {
        //         color,
        //       },
        //     },
        //     stack: props.isStack,
        //   },
        //   // {
        //   //   z: 2,
        //   //   name: '数据',
        //   //   type: 'pictorialBar',
        //   //   data: constData,
        //   //   symbol: 'diamond',
        //   //   symbolOffset: ['0%', '50%'],
        //   //   symbolSize: [barWidth, 10],
        //   //   itemStyle: {
        //   //     normal: {
        //   //       color,
        //   //     },
        //   //   },
        //   //   tooltip: {
        //   //     show: false,
        //   //   },
        //   // },
        //   {
        //     z: 2,
        //     name: '数据',
        //     type: 'pictorialBar',
        //     symbolPosition: 'end',
        //     data,
        //     symbol: 'diamond',
        //     symbolOffset: ['0%', '-50%'],
        //     symbolSize: [barWidth - 4, (10 * (barWidth - 4)) / barWidth],
        //     itemStyle: {
        //       normal: {
        //         borderColor: colorArr[2],
        //         borderWidth: 2,
        //         color: colorArr[2],
        //       },
        //     },
        //     tooltip: {
        //       show: false,
        //     },
        //     stack: props.isStack,
        //   },
        // ],
      };
    });

    return {
      barOption,
      echartsRef,
      mergeBaseOption,
    };
  },
});
</script>

<style lang="stylus" scoped></style>
