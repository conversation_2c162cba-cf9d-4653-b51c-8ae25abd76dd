<template lang="pug">
.w-full.h-full
  TaScreenEchartBase(:options='lineOption' ref='echartsRef')
</template>
<script lang="ts">
import { VObject } from '@/lib/vails';
import { PropType, computed, defineComponent, ref } from 'vue';
import echarts from 'echarts';
import { VScatterData } from '../baseUtils/baseChat';
import { getThemeColor, getThemeLabelLineColor } from '../../../../../utils/util';

const ComScreenScatter = defineComponent({
  name: 'ComScreenScatter',
  props: {
    // 需要merge的option - 高级功能
    mergeOption: { type: Object, default: () => { } },
    // 是否完全替换
    isOnlyMerge: { type: Boolean, default: false },

    // 自定义色彩，【或者可以支持组选新颜色?】
    customizeLineColor: { type: Array, default: () => [] },
    customizeAreaColor: { type: Array, default: () => [] },

    // ----------------------------------------------

    // 是否显示图例
    showLengend: { type: Boolean, default: false },
    // 图表数据 seriesData 为纯数据项 , seriesName 对应的每一例数据代表的数据别称
    data: { type: Array as PropType<VScatterData[]>, default: () => [] },
    // 图例位置 top right bottom left
    lengendPosition: { type: Array, default: () => [4, 0, 0, 4] },
    // X轴显示名称，为空则不显示X轴标
    xAxisLabel: { type: Array as PropType<string[]>, default: () => [] },
    // 是否显示Y轴
    showYAxis: { type: Boolean, default: true },
    // Y轴数据，为2则代表有两条Y轴，最大为2
    yAxisNum: { type: Number as PropType<1 | 2>, default: () => 1 },
    // 是否显示Y轴单位
    showUnit: { type: Boolean, default: true },
    // 主轴单位
    unit: { type: String, default: () => '' },
    // 副轴单位
    subUnit: { type: String, default: () => '' },
    // 主轴单位位置
    unitPadding: { type: Array as PropType<number[]>, default: () => [0, 0, 0, 0] },
    // 主轴单位位置
    subUnitPadding: { type: Array as PropType<number[]>, default: () => [0, 0, 0, 0] },
    // 图例文字颜色
    legendTextColor: {
      type: String,
      default: getThemeColor(),
    },
    // 坐标轴颜色
    axisColor: {
      type: String,
      default: getThemeColor(),
    },
    // 坐标轴分割线颜色
    splitLineColor: {
      type: String,
      default: getThemeLabelLineColor(),
    },
    // 坐标轴颜色
    labelColor: {
      type: String,
      default: getThemeColor(),
    },
    // 图位置 top right bottom left
    gridPosition: {
      type: Array,
      default: () => ['10%', '3%', '12%', '5%'],
    },
    customSymbols: {
      type: Array as PropType<string[]>,
      default: () => [],
    },
    customSymbolSize: {
      type: Array as PropType<number[]>,
      default: () => [],
    },
  },
  setup(props) {
    const echartsRef = ref();
    const mergeBaseOption = (options: VObject, onlyMerge: boolean) => {
      echartsRef.value?.mergeOption(options, onlyMerge);
    };
    const symbolList = [
      'circle',
      'rect',
      'roundRect',
      'triangle',
      'diamond',
      'pin',
      'arrow',
      'none',
    ];
    const lineOption = computed(() => {
      const [top, right, bottom, left] = props.lengendPosition;
      const positionOpt = { top, right, bottom, left };
      // const finalLineColor = [...props.customizeLineColor, ...lineColor];
      const finalSymbolList = [...props.customSymbols, ...symbolList];
      const options = {
        tooltip: {
          show: false,
        },
        ...(props.showLengend
          ? {
            legend: {
              show: true,
              textStyle: {
                color: getThemeColor(),
              },
              ...positionOpt,
            },
          }
          : {}),
        xAxis: {
          type: 'category',
          nameTextStyle: {
            color: getThemeColor(),
          },
          axisTick: {
            length: 0,
            lineStyle: {
              color: getThemeColor(),
            },
          },
          axisLine: {
            lineStyle: {
              color: '#5F5F60',
              width: 1,
            },
          },
          axisLabel: {
            color: getThemeLabelLineColor(),
          },
        },
        grid: {
          top: props.gridPosition[0] || 0,
          right: props.gridPosition[1] || 0,
          bottom: props.gridPosition[2] || 0,
          left: props.gridPosition[3] || 0,
        },
        yAxis: new Array(props.yAxisNum).fill('').map((el: any, index: number) => {
          return {
            type: 'value',
            name: props.unit,
            nameTextStyle: {
              padding: [...(index === 0 ? props.unitPadding : props.subUnitPadding)],
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              lineStyle: {
                color: props.splitLineColor,
              },
              show: index < 1,
            },
            axisLine: {
              lineStyle: {
                color: props.axisColor,
                width: 0,
              },
            },
            yAxisIndex: index,
            show: props.showYAxis,
          };
        }),

        series: props.data.map((el: any, index: number) => {
          return {
            symbolSize: props.customSymbolSize?.[index] || 20,
            symbol: finalSymbolList[index],
            data: el.seriesData,
            name: el.seriesName,
            type: 'scatter',
            lineStyle: {
              type: 'solid',
            },
            // itemStyle: {
            //   color: finalLineColor[index],
            // },
            yAxisIndex: props.yAxisNum > 1 ? index : 0,
          };
        }),
      };
      if (props?.mergeOption && Object.keys(props?.mergeOption).length > 0) {
        return (echarts as any).util.merge(options, props.mergeOption, props.isOnlyMerge);
      } else {
        return options;
      }
    });
    return {
      lineOption,
      echartsRef,
      mergeBaseOption,
    };
  },
});
export default ComScreenScatter;
</script>
<style lang="stylus" scoped></style>
