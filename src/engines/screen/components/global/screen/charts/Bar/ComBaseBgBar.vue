<template lang="pug">
.com_base_bg_bar.w-full.h-full
  ComScreenBaseBar(
    v-bind='$props'
  )
</template>
<script lang="ts">
import { PropType, defineComponent, toRefs } from 'vue';
import ComScreenBaseBar from './ComScreenBaseBar.vue';
import { BaseData } from '../baseUtils/baseChat';

export default defineComponent({
  name: 'ComBaseBgBar',
  components: {
    ComScreenBaseBar,
  },
  props: {
    data: {
      type: Array as PropType<BaseData[]>,
      default: () => [
        {
          seriesName: 'bar-1',
          seriesData: [120, 200, 150, 80, 70, 110, 130],
        },
      ],
    },
    xAxisLabel: {
      type: Array as PropType<string[]>,
      default: () => ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
    },
    showUnit: { type: Boolean, default: () => false },
    showBg: { type: Boolean, default: () => true },

    unit: { type: String, default: '万' },
    unitPadding: { type: Array as PropType<number[]>, default: () => [0, 0, 0, 0] },
    barWidth: { type: String || Number, default: '' },
    gridPosition: {
      type: Array,
      default: () => ['10%', '10%', '10%', '10%'],
    },
    barAreaColor: {
      type: Array,
      required: true,
      // default: () => [],
    },
    showLengend: { type: Boolean, default: () => true },
    borderRadius: {
      type: Array,
      default: () => [0, 0, 0, 0],
    },
    colorType: {
      type: String as PropType<'solid' | 'gradient'>,
      default: 'gradient',
    },
    unitTitle: {
      type: String,
      default: '单位',
    },
    showLabel: {
      type: Boolean,
      default: false,
    },
    labelXRotate: { type: Number, default: 0 },
    showOutsideCurrentStage: {
      type: Boolean,
      default: () => false,
    },
    showOutsideCurrentStageText: {
      type: String,
      default: () => 'text',
    },
    currentTextColor: {
      type: String,
      default: () => 'white',
    },
    showAverageMarkLine: {
      type: Boolean,
      default: () => false,
    },
    yAxisType: {
      type: String as PropType<'value' | 'category' | 'time' | 'log'>,
      default: () => 'value',
    },
    yAxisTextColor: { type: String, default: '' },
    xAxisTextColor: { type: String, default: '' },
    unitColor: { type: String, default: '' },
    yAxisSplitColor: { type: String, default: '' },
    xAxisTooltipShow: { type: Boolean, default: () => false },
    xAxisLabelWidth: { type: Number, default: () => 40 },
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
</script>
<style lang="stylus" scoped></style>
