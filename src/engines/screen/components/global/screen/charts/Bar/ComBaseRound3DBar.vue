<template lang="pug">
.com_base_bg_bar.w-full.h-full
  ComScreenBaseBar(
    :data='data', 
    :xAxisLabel='xAxisLabel' 
    :unitPadding='unitPadding',
    :title='title',
    :titlePadding='titlePadding',
    :barWidth='barWidth',
    :gridPosition="gridPosition"
    colorType='solid'
    :is3DCircle='true',
  )
</template>
<script lang="ts">
import { PropType, defineComponent, toRefs } from 'vue';
import ComScreenBaseBar from './ComScreenBaseBar.vue';
import { BarData } from '../baseUtils/baseChat';

export default defineComponent({
  name: 'ComBaseRound3DBar',
  components: {
    ComScreenBaseBar,
  },
  props: {
    // data为单个数组 最多6个元素
    data: {
      type: Array as PropType<BarData[]>,
      default: () => [],
    },
    xAxisLabel: {
      type: Array as PropType<string[]>,
      default: () => [],
    },
    unit: { type: String, default: '亿元' },
    unitPadding: { type: Array as PropType<number[]>, default: () => [0, 0, 0, 0] },
    barWidth: {
      type: String,
      default: () => '16',
    },
    title: {
      type: String,
      default: () => '这是文字标题',
    },
    // 图例标题布局
    titlePadding: { type: Array as PropType<number[]>, default: () => [0, 0, 0, 16] },
    gridPosition: {
      type: Array,
      default: () => ['10%', '10%', '10%', '10%'],
    },
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
</script>
<style lang="stylus" scoped></style>
