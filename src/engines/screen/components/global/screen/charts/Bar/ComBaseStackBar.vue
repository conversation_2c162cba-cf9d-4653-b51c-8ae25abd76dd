<template lang="pug">
.com_base_bg_bar.w-full.h-full
  ComScreenBaseBar(
    v-bind='$props'
    :isStack='true',
    colorType='solid'
  )
</template>
<script lang="ts">
import { PropType, defineComponent, toRefs } from 'vue';
import ComScreenBaseBar from './ComScreenBaseBar.vue';
import { BaseData } from '../baseUtils/baseChat';
import { getThemeColor } from '@/engines/screen/utils/util';

const defaultData = [
  {
    seriesName: 'bar-1',
    seriesData: [120, 200, 150, 80, 70, 110, 130],
  },
  {
    seriesName: 'bar-2',
    seriesData: [120, 200, 150, 80, 70, 110, 130].reverse(),
  },
  {
    seriesName: 'bar-3',
    seriesData: [90, 100, 350, 180, 30, 100, 230],
  },
  {
    seriesName: 'bar-4',
    seriesData: [120, 200, 150, 80, 70, 110, 130].reverse(),
  },
  {
    seriesName: 'bar-5',
    seriesData: [10, 20, 30, 50, 30, 10, 20],
  },
];

export default defineComponent({
  name: 'ComBaseStackBar',
  components: {
    ComScreenBaseBar,
  },
  props: {
    data: {
      type: Array as PropType<BaseData[]>,
      default: () => defaultData,
    },
    xAxisLabel: {
      type: Array as PropType<string[]>,
      default: () => [],
    },
    unit: { type: String, default: '亿元' },
    unitPadding: { type: Array as PropType<number[]>, default: () => [0, 0, 0, 0] },
    showUnit: { type: Boolean, default: () => false },
    showLengend: { type: Boolean, default: () => true },
    gridPosition: {
      type: Array,
      default: () => ['10%', '10%', '10%', '10%'],
    },
    barWidth: { type: String || Number, default: '' },
    unitTitle: {
      type: String,
      default: '单位',
    },
    barAreaColor: {
      type: Array,
      required: true,
      // default: () => [],
    },
    showLabel: {
      type: Boolean,
      default: false,
    },
    lengendPosition: {
      type: Array,
      default: () => ['top', 0, 0, 'center'],
    },
    yAxisTextColor: { type: String, default: getThemeColor() },
    xAxisTextColor: { type: String, default: getThemeColor() },
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
</script>
<style lang="stylus" scoped></style>
