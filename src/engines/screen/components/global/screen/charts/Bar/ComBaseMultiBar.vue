<template lang="pug">
.com_base_bg_bar.w-full.h-full
  ComScreenBaseBar(
    v-bind='$props'
  )
</template>
<script lang="ts">
import { PropType, defineComponent, toRefs } from 'vue';
import ComScreenBaseBar from './ComScreenBaseBar.vue';
import { BaseData } from '../baseUtils/baseChat';

export default defineComponent({
  name: 'ComBaseMultiBar',
  components: {
    ComScreenBaseBar,
  },
  props: {
    data: {
      type: Array as PropType<BaseData[]>,
      default: () => [],
    },
    xAxisLabel: {
      type: Array as PropType<string[]>,
      default: () => [],
    },
    yAxisNum: {
      type: Number as PropType<1 | 2>,
      default: 1,
    },
    // 主轴单位
    unit: { formType: 'input', default: '' },
    // 副轴单位
    subUnit: { formType: 'input', default: '' },
    // 主轴单位位置
    unitPadding: {
      type: Array as PropType<number[]>,
      default: () => [0, 0, 0, 0],
    },
    // 辅轴单位位置
    subUnitPadding: {
      type: Array as PropType<number[]>,
      default: () => [0, 0, 0, 0],
    },
    showUnit: { type: Boolean, default: () => false },
    showLengend: { type: Boolean, default: () => true },
    gridPosition: {
      type: Array,
      default: () => ['10%', '10%', '10%', '10%'],
    },
    lengendPosition: {
      type: Array,
      default: () => ['auto', 0, 0, 'center'],
    },
    labelXRotate: { type: Number, default: 30 },
    colorType: {
      type: String as PropType<'solid' | 'gradient'>,
      default: 'gradient',
    },
    barAreaColor: {
      type: Array,
      required: true,
      // default: () => [],
    },
    showTootip: {
      type: Boolean,
      default: true,
    },
    // 柱状图宽度
    barWidth: {
      type: String || Number,
      default: '',
    },
    showLabel: {
      type: Boolean,
      default: false,
    },
    unitTitle: {
      type: String,
      default: '单位',
    },
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
</script>
<style lang="stylus" scoped></style>
