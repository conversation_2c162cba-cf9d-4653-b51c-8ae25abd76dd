<template lang="pug">
.w-full.h-full 
  TaScreenEchartBase(:options='barOption' ref='echartsRef')
</template>

<script lang="ts">
import { VObject } from '@/lib/vails';
import { PropType, computed, defineComponent, ref } from 'vue';
import { flatten, zip, sum } from 'lodash';
import {
  getThemeColor,
  getThemeItemLineColor,
  getThemeLabelLineColor,
} from '@/engines/screen/utils/util';
import {
  BarData,
  Square3DColor,
  barAreaColor,
  lineColor,
  Circle3DColor,
  Circle3DBgColor,
} from '../baseUtils/baseChat';

// import { useScreenDataFetchCollectionInject } from '@/components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';

const ComScreenBaseBar = defineComponent({
  name: 'ComScreenBaseBar',
  props: {
    // 需要merge的option - 高级功能
    // mergeOption: { type: Object, default: () => {} },
    mergeOption: {
      type: Object,
      default: () => ({}),
    },
    // 是否完全替换
    isOnlyMerge: {
      type: Boolean,
      default: false,
    },

    // ----------------------------------------------
    // 是否显示图例
    showLengend: { type: Boolean, default: false },
    // 图表数据 seriesData 为纯数据项 , seriesName 对应的每一例数据代表的数据别称
    // series: {
    //   formType: 'list',
    //   label: '数据线',
    //   type: Array,
    //   subProps: {
    //     seriesName: {
    //       formType: 'input',
    //       label: '标题',
    //       type: String,
    //       default: '',
    //     },
    //     seriesData: {
    //       label: '数据静态值',
    //       formType: 'json',
    //       type: [Object, Number, String, Array],
    //     },
    //   },
    // },
    // data: {
    //   label: '数据静态值',
    //   formType: 'json',
    //   type: Array,
    //   default: [
    //     {
    //       seriesName: 'bar-1',
    //       seriesData: [120, 188, 150, 80, 70, 110],
    //     },
    //   ],
    // },
    data: {
      type: Array as PropType<BarData[]>,
      default: () => [],
    },
    // 图例位置 top right bottom right
    lengendPosition: {
      type: Array,
      default: () => ['top', 0, 0, 'center'],
    },
    // 图位置 top right bottom right
    gridPosition: {
      type: Array,
      default: () => ['10%', '10%', '10%', '10%'],
    },
    // X轴显示名称，为空则不显示X轴标
    xAxisLabel: {
      type: Array as PropType<string[]>,
      default: () => ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
    },
    // 图例标题
    title: {
      type: String,
      default: '',
    },
    // 图例标题布局
    titlePadding: {
      type: Array as PropType<number[]>,
      default: () => [0, 0, 0, 16],
    },
    // 是否显示Y轴
    showYAxis: { type: Boolean, default: true },
    // Y轴数据，为2则代表有两条Y轴，最大为2
    yAxisNum: {
      type: Number as PropType<1 | 2>,
      default: 1,
    },
    // 柱图是否分割
    isSection: { type: Boolean, default: () => false },
    // 是否显示Y轴单位
    showUnit: { type: Boolean, default: false },
    // 主轴单位
    unit: { formType: 'input', default: '' },
    // 副轴单位
    subUnit: { formType: 'input', default: '' },
    // 主轴单位位置
    unitPadding: {
      type: Array as PropType<number[]>,
      default: () => [0, 0, 0, 0],
    },
    // 辅轴单位位置
    subUnitPadding: {
      type: Array as PropType<number[]>,
      default: () => [0, 0, 0, 0],
    },
    // 是否要显示数据点标注
    showLabel: {
      type: Boolean,
      default: false,
    },
    // 是否要展示柱状图背景
    showBg: {
      type: Boolean,
      default: false,
    },
    // 柱状图宽度
    barWidth: {
      type: String || Number,
      default: '',
    },
    // x轴label倾斜度
    labelXRotate: {
      type: Number,
      default: 0,
    },
    // 是否堆积图
    isStack: {
      type: Boolean,
      default: false,
    },
    // 是否3d柱状图
    is3DSquare: {
      type: Boolean,
      default: false,
    },
    // 是否3d圆柱图
    is3DCircle: {
      type: Boolean,
      default: false,
    },
    // 柱条单色、渐变色
    colorType: {
      type: String as PropType<'solid' | 'gradient'>,
      default: 'gradient',
    },
    barAreaColor: {
      type: Array as PropType<string[]>,
      default: () => barAreaColor,
    },
    // 是否要展示tooltip
    showTootip: {
      type: Boolean,
      default: true,
    },
    axisLabelFormatter: {
      type: Function,
      default: (value: any) => {
        return value;
      },
    },
    borderRadius: {
      type: Array,
      default: () => [0, 0, 0, 0],
    },
    unitTitle: {
      type: String,
      default: '单位',
    },
    yAxisTextColor: { type: String, default: getThemeColor() },
    xAxisTextColor: { type: String, default: getThemeColor() },
    unitColor: { type: String, default: () => getThemeColor() },
    yAxisSplitColor: { type: String, default: getThemeLabelLineColor() },
    showOutsideCurrentStage: {
      type: Boolean,
      default: () => false,
    },
    showOutsideCurrentStageText: {
      type: String,
      default: () => 'text',
    },
    currentTextColor: {
      type: String,
      default: () => '',
    },
    showAverageMarkLine: {
      type: Boolean,
      default: () => false,
    },
    // 坐标轴类型。
    yAxisType: {
      type: String as PropType<'value' | 'category' | 'time' | 'log'>,
      default: () => 'value',
    },
    yAxisMinInterval: {
      type: String || Number,
      default: '',
    },
    lengendColor: { type: String, default: () => getThemeColor() },
    tooltipBgColor: { type: String, default: () => 'white' },
    tooltipTextColor: { type: String, default: () => 'black' },
    xAxisTooltipShow: { type: Boolean, default: () => false },
    xAxisLabelWidth: { type: Number, default: () => 40 },
  },
  setup(props: VObject) {
    // const { getScreenData, dataResult } = useScreenDataFetchCollectionInject(props);
    const echartsRef = ref();
    const mergeBaseOption = (options: VObject, onlyMerge: boolean) => {
      echartsRef.value?.mergeOption(options, onlyMerge);
    };
    const barOption = computed(() => {
      const [top, right, bottom, left] = props.lengendPosition;
      const positionOpt = { top, right, bottom, left };
      const totalDataArray = zip(
        ...props.data.map((el: any) => {
          return el.seriesData;
        }),
      ).map((process_el: any) => {
        return sum(process_el);
      });
      return {
        grid: {
          top: props.gridPosition[0] || 0,
          right: props.gridPosition[1] || 0,
          bottom: props.gridPosition[2] || 0,
          left: props.gridPosition[3] || 0,
        },
        tooltip: {
          show: props.showTootip,
          trigger: 'axis',
          backgroundColor: props.tooltipBgColor || 'white',
          textStyle: {
            color: props.tooltipTextColor || 'black',
          },
          formatter: (record: VObject) => {
            const getChildren = () => {
              return record
                .map((item: any) => {
                  const color = item.color?.colorStops
                    ? item.color?.colorStops[1].color
                    : item.color;
                  return `<div class="flex">
                      <span style="display:flex;margin-right:5px;margin-top:5.5px;border-radius:10px;width:10px;height:10px;background-color:${color};"></span>
                      <div class="mr-1">${item.seriesName}: </div>
                      <div>
                        ${item.data?.value || item?.value}${props.unit}
                      </div>
                    </div>`;
                })
                .join(' ');
            };
            return `<div>
                <div>${record[0].name}</div>
                ${getChildren()}
              </div>`;
          },
        },
        ...(props.title
          ? {
              title: {
                text: props.title,
                padding: props.titlePadding,
                textStyle: {
                  color: getThemeColor(),
                  'font-family': 'Inter',
                  'font-size': '14px',
                  'font-weight': 400,
                },
              },
            }
          : {}),
        ...(props.showLengend
          ? {
              legend: {
                data: props?.data.map((el: any) => el.seriesName),
                itemHeight: 6,
                itemWidth: 6,
                icon: 'roundRect',
                textStyle: {
                  color: props.lengendColor || 'white',
                },
                ...positionOpt,
              },
            }
          : {}),
        xAxis: {
          type: 'category',
          data: props.xAxisLabel,
          axisTick: {
            show: false,
          },
          axisLine: {
            lineStyle: {
              color: getThemeLabelLineColor(),
              width: 0,
            },
          },
          axisLabel: {
            rotate: props.labelXRotate,
            interval: 0,
            formatter: props.axisLabelFormatter,
            textStyle: {
              color: props.xAxisTextColor,
            },
            ...(props.xAxisTooltipShow ? { width: props.xAxisLabelWidth, overflow: 'truncate' } : {}),
          },
          ...(props.xAxisTooltipShow
            ? {
                tooltip: {
                  show: true,
                },
              }
            : {}),
        },
        yAxis: new Array(props.yAxisNum).fill('').map((el: any, index: number) => {
          return {
            type: props.yAxisType || 'value',
            name: props.showUnit
              ? `${props.unitTitle}：` + (index === 0 ? props.unit : props.subUnit)
              : undefined,
            nameTextStyle: {
              padding: [...(index === 0 ? props.unitPadding : props.subUnitPadding)],
              color: props?.unitColor ? props.unitColor : undefined,
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              lineStyle: {
                color: props.yAxisSplitColor,
                type: 'dashed',
              },
              show: index < 1,
            },
            axisLine: {
              lineStyle: {
                color: '#EBF5FF',
                width: 0,
              },
            },
            yAxisIndex: index,
            show: props.showYAxis,
            axisLabel: {
              textStyle: {
                color: props.yAxisTextColor,
              },
            },
            ...(props?.yAxisMinInterval ? { minInterval: props?.yAxisMinInterval } : {}),
          };
        }),
        series: flatten([
          ...props?.data.map((el: any, index: number) => {
            if (props.is3DSquare) {
              const barWidth3D = Number(props.barWidth || 30);
              const colorArr = Square3DColor[index];
              const sumList = Array.from({ length: props.data?.[0]?.seriesData?.length }, (_, i) =>
                props.data
                  .slice(0, index + 1)
                  .reduce((acc: number, curr: VObject) => acc + curr.seriesData[i], 0),
              );
              const sumListBottom = Array.from(
                { length: props.data?.[0]?.seriesData?.length },
                (_, i) =>
                  props.data
                    .slice(0, index)
                    .reduce((acc: number, curr: VObject) => acc + curr.seriesData[i], 0),
              );

              const color = {
                type: 'linear',
                x: 0,
                x2: 1,
                y: 0,
                y2: 0,
                colorStops: [
                  {
                    offset: 0,
                    color: colorArr[0],
                  },
                  {
                    offset: 0.5,
                    color: colorArr[0],
                  },
                  {
                    offset: 0.5,
                    color: colorArr[1],
                  },
                  {
                    offset: 1,
                    color: colorArr[1],
                  },
                ],
              };
              return [
                {
                  z: index * 3,
                  name: el.seriesName,
                  type: 'bar',
                  barWidth: barWidth3D,
                  barGap: '0%',
                  data: el.seriesData,
                  itemStyle: {
                    normal: {
                      color,
                    },
                  },
                  stack: props.isStack,
                },
                props.isStack && {
                  z: index * 3 + 1,
                  name: el.seriesName,
                  type: 'pictorialBar',
                  data: sumListBottom,
                  symbol: 'diamond',
                  symbolPosition: index === 0 ? 'start' : 'end',

                  symbolOffset: ['0%', '-50%'],
                  symbolSize: [barWidth3D, 10],
                  itemStyle: {
                    normal: {
                      color,
                    },
                  },
                  tooltip: {
                    show: false,
                  },
                  stack: props.isStack,
                },
                (!props.isStack || index + 1 === props.data.length) && {
                  z: index * 3 + 2,
                  name: el.seriesName,
                  type: 'pictorialBar',
                  symbolPosition: 'end',
                  data: sumList,
                  symbol: 'diamond',
                  symbolOffset: ['0%', '-50%'],
                  symbolSize: [barWidth3D - 4.5, (10 * (barWidth3D - 4.5)) / barWidth3D],
                  itemStyle: {
                    normal: {
                      borderColor: colorArr[2],
                      borderWidth: 2,
                      color: colorArr[2],
                    },
                  },
                  tooltip: {
                    show: false,
                  },
                  stack: props.isStack,
                },
              ] as any;
            } else if (props.is3DCircle) {
              console.log('Circle3DColor', Circle3DColor);
              const barWidth3D = Number(props.barWidth || 16);
              const getColor = (i: number) => {
                return {
                  type: 'linear',
                  x: 0,
                  y: 0,
                  x2: 0,
                  y2: 1,
                  colorStops: [
                    {
                      offset: 0,
                      color: Circle3DColor[i][0], // 0% 处的颜色
                    },
                    {
                      offset: 1,
                      color: Circle3DColor[i][1], // 100% 处的颜色
                    },
                  ],
                };
              };
              const digit =
                Math.floor(Math.log10(Math.max(...el.seriesData))) > 0
                  ? Math.floor(Math.log10(Math.max(...el.seriesData)))
                  : 1;

              const maxValue =
                Math.ceil(Math.max(...el.seriesData) / Math.pow(10, digit)) * Math.pow(10, digit);
              console.log(
                'maxValue',
                digit,
                maxValue,
                el.seriesData,
                Math.floor(Math.log10(Math.max(...el.seriesData))),
              );

              return [
                {
                  z: index * 3,
                  name: el.seriesName,
                  type: 'bar',
                  barWidth: barWidth3D,
                  barGap: '0%',
                  data: el.seriesData.map((el: number, i: number) => ({
                    value: el,
                    itemStyle: {
                      color: getColor(i),
                    },
                  })),
                  stack: true,
                },
                {
                  z: index * 3 + 1,
                  name: el.seriesName,
                  type: 'pictorialBar',
                  data: el.seriesData.map((el: number, i: number) => ({
                    value: el,
                    itemStyle: {
                      color: Circle3DColor[i][1],
                    },
                  })),
                  symbolPosition: 'start',

                  symbolOffset: ['0%', '30%'],
                  symbolSize: [barWidth3D, 8],
                  itemStyle: {
                    normal: {
                      color: Circle3DColor[index][1],
                    },
                  },
                  tooltip: {
                    show: false,
                  },
                },

                {
                  z: index * 3 + 2,
                  name: el.seriesName,
                  type: 'pictorialBar',
                  data: el.seriesData.map((el: number, i: number) => ({
                    value: el,
                    itemStyle: {
                      color: Circle3DColor[i][2],
                    },
                  })),
                  symbolPosition: 'end',

                  symbolOffset: ['0%', '-50%'],
                  symbolSize: [barWidth3D, 8],
                  tooltip: {
                    show: false,
                  },
                },

                {
                  z: index * 3,
                  name: el.seriesName,
                  type: 'bar',
                  barWidth: barWidth3D,
                  barGap: '0%',
                  data: el.seriesData.map((el: number, i: number) => ({
                    value: maxValue - el,
                    itemStyle: {
                      color: Circle3DBgColor[i][0],
                    },
                  })),
                  stack: true,
                  label: {
                    show: true,
                    position: 'top',
                    color: getThemeColor(),
                    distance: 10,
                    formatter: (params: any) => {
                      return (((maxValue - params.value) / maxValue) * 100).toFixed(0) + '%';
                    },
                  },
                },
                {
                  z: index * 3 + 2,
                  name: el.seriesName,
                  type: 'pictorialBar',
                  data: el.seriesData.map((el: number, i: number) => ({
                    value: maxValue,
                    itemStyle: {
                      color: Circle3DBgColor[i][1],
                    },
                  })),
                  symbolPosition: 'end',

                  symbolOffset: ['0%', '-50%'],
                  symbolSize: [barWidth3D, 8],
                  tooltip: {
                    show: false,
                  },
                },
              ];
            } else {
              return [
                ...(props.isSection
                  ? [
                      {
                        data: el.seriesData,
                        name: el.seriesName,
                        type: 'pictorialBar',
                        itemStyle: {
                          color:
                            props.colorType === 'gradient'
                              ? {
                                  type: 'linear',
                                  x: 0,
                                  y: 0,
                                  x2: 1,
                                  y2: 0,
                                  colorStops: [
                                    {
                                      offset: 1,
                                      color: '#1EE7E7', // 0% 处的颜色
                                    },
                                    {
                                      offset: 0,
                                      color: props.barAreaColor[index], // 100% 处的颜色
                                    },
                                  ],
                                }
                              : props.barAreaColor[index] || lineColor[index],
                        },
                        stack: props.isStack,
                        yAxisIndex: props.yAxisNum > 1 ? index : 0,
                        symbolRepeat: 'fixed',
                        symbolMargin: 2,
                        symbol: 'rect',
                        symbolClip: true,
                        symbolSize: [props.barWidth || 24, 8],
                        width: 2,
                        z: 10,
                        zlevel: 1,
                        symbolPosition: 'start',
                        symbolOffset: [0.2, 5],
                        label: {
                          show: false,
                        },
                      },
                    ]
                  : [
                      {
                        data:
                          props.showOutsideCurrentStage === true
                            ? el.seriesData.map((item: any, i: number) => {
                                if (item?.current) {
                                  return {
                                    ...item,
                                    label: {
                                      show: true,
                                      color: props.currentTextColor || barAreaColor[i],
                                      fontSize: 14,
                                      formatter: () => {
                                        return `${item?.value}${props.showOutsideCurrentStageText}`;
                                      },
                                    },
                                    // labelLine: {
                                    //   show: true,
                                    //   length: 10,
                                    //   length2: 20,
                                    //   lineStyle: {
                                    //     width: 1,
                                    //     type: 'solid',
                                    //     color: props.currentTextColor || barAreaColor[i],
                                    //   },
                                    // },
                                  };
                                } else {
                                  return item;
                                }
                              })
                            : el.seriesData,
                        name: el.seriesName,
                        type: 'bar',
                        itemStyle: {
                          color:
                            props.colorType === 'gradient'
                              ? {
                                  type: 'linear',
                                  x: 0,
                                  y: 0,
                                  x2: 0,
                                  y2: 1,
                                  colorStops: [
                                    {
                                      offset: 1,
                                      color: 'rgba(86, 204, 242, 0)', // 0% 处的颜色
                                    },
                                    {
                                      offset: 0,
                                      color: props.barAreaColor[index], // 100% 处的颜色
                                    },
                                  ],
                                }
                              : props.barAreaColor[index] || lineColor[index],
                          barBorderRadius: props.borderRadius,
                        },
                        barWidth: props.barWidth || 'auto',
                        stack: props.isStack,
                        yAxisIndex: props.yAxisNum > 1 ? index : 0,
                        ...(props.showBg
                          ? {
                              showBackground: true,
                              backgroundStyle: {
                                color: getThemeItemLineColor(),
                              },
                            }
                          : {}),
                        ...(props.showLabel
                          ? {
                              label: {
                                show: true,
                                color: getThemeColor(),
                                position: props.isStack ? 'inside' : 'top',
                                formatter: (params: any) => {
                                  if (Number(params.value)) {
                                    return params.value;
                                  }
                                  return '';
                                },
                              },
                            }
                          : {}),
                        ...(props.showAverageMarkLine
                          ? {
                              markLine: {
                                // symbol: 'none',
                                lineStyle: {
                                  color: '#0E9F6E',
                                },
                                label: {
                                  position: 'insideEndTop',
                                  color: getThemeColor(),
                                  padding: [0, -50, 20, 10],
                                  formatter: (params: any) => {
                                    return '总平均值：' + Math.round(params.value);
                                  },
                                },
                                data: [{ type: 'average', name: 'Avg' }],
                              },
                            }
                          : {}),
                      },
                    ]),
              ];
            }
          }),
          ...(props.isStack && props.showLabel && !props.is3DSquare && !props.is3DCircle
            ? [
                {
                  data: totalDataArray,
                  name: '总数',
                  type: 'line',
                  label: {
                    show: true,
                    color: getThemeColor(),
                    position: 'top',
                    formatter: (el: any) => (el.value ? el.value : ''),
                  },
                  lineStyle: {
                    width: 0,
                  },
                  symbol: 'circle',
                  symbolSize: 0,
                },
              ]
            : []),
        ]),
      };
    });

    return {
      barOption,
      echartsRef,
      mergeBaseOption,
      getThemeColor,
      getThemeLabelLineColor,
    };
  },
});
export default ComScreenBaseBar;
</script>

<style lang="stylus" scoped></style>
