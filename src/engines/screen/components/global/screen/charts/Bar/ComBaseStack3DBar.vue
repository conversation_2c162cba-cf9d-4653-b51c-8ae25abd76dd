<template lang="pug">
.com_base_bg_bar.w-full.h-full
  ComScreenBaseBar(
    :data='data', 
    v-bind='$props'
    :isStack='true',
    :is3DSquare='true',
    colorType='solid'
  )
</template>
<script lang="ts">
import { PropType, defineComponent, toRefs } from 'vue';
import ComScreenBaseBar from './ComScreenBaseBar.vue';
import { BarData } from '../baseUtils/baseChat';

export default defineComponent({
  name: 'ComBaseStack3DBar',
  components: {
    ComScreenBaseBar,
  },
  props: {
    data: {
      type: Array as PropType<BarData[]>,
      default: () => [
        {
          seriesName: 'bar-1',
          seriesData: [120, 200, 150, 80, 70, 110, 130],
        },
        {
          seriesName: 'bar-2',
          seriesData: [120, 200, 150, 80, 70, 110, 130].reverse(),
        },
        {
          seriesName: 'bar-3',
          seriesData: [90, 100, 350, 180, 30, 100, 230],
        },
        {
          seriesName: 'bar-4',
          seriesData: [120, 200, 150, 80, 70, 110, 130].reverse(),
        },
        {
          seriesName: 'bar-5',
          seriesData: [10, 20, 30, 50, 30, 10, 20],
        },
      ],
    },
    xAxisLabel: {
      type: Array as PropType<string[]>,
      default: () => [],
    },
    unit: { type: String, default: '亿元' },
    unitPadding: { type: Array as PropType<number[]>, default: () => [0, 0, 0, 0] },
    barWidth: {
      type: String,
      default: () => '30',
    },
    showUnit: { type: Boolean, default: () => false },
    showLengend: { type: Boolean, default: () => true },
    gridPosition: {
      type: Array,
      default: () => ['10%', '10%', '10%', '10%'],
    },
    axisLabelFormatter: {
      type: Function,
      default: (value: any) => {
        return value;
      },
    },
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
</script>
<style lang="stylus" scoped></style>
