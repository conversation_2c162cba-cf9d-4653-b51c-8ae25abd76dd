<template lang="pug">
.com_base_bg_bar.w-full.h-full
  ComScreenBaseBar(
    v-bind='$props'
    :isSection="true"
  )
</template>
<script lang="ts">
import { PropType, defineComponent, toRefs } from 'vue';
import ComScreenBaseBar from './ComScreenBaseBar.vue';
import { BaseData } from '../baseUtils/baseChat';

export default defineComponent({
  name: 'ComBaseSectionBar',
  components: {
    ComScreenBaseBar,
  },
  props: {
    data: {
      type: Array as PropType<BaseData[]>,
      default: () => [
        {
          seriesName: 'bar-1',
          seriesData: [120, 200, 150, 80, 70, 110, 130],
        },
      ],
    },
    xAxisLabel: {
      type: Array as PropType<string[]>,
      default: () => ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
    },
    showUnit: { type: Boolean, default: () => false },
    isSection: { type: Boolean, default: () => true },
    unit: { type: String, default: '万' },
    unitPadding: { type: Array as PropType<number[]>, default: () => [0, 0, 0, 0] },
    barWidth: { type: String || Number, default: 24 },
    gridPosition: {
      type: Array,
      default: () => ['10%', '10%', '10%', '10%'],
    },
    barAreaColor: {
      type: Array,
      required: true,
      // default: () => [],
    },
    showLengend: { type: Boolean, default: () => true },
    borderRadius: {
      type: Array,
      default: () => [0, 0, 0, 0],
    },
    colorType: {
      type: String as PropType<'solid' | 'gradient'>,
      default: 'gradient',
    },
    unitTitle: {
      type: String,
      default: '单位',
    },
    showLabel: {
      type: Boolean,
      default: false,
    },
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
</script>
<style lang="stylus" scoped></style>
