<template lang="pug">
.w-full.h-full
  TaScreenEchartBase(:options='options' ref='echartsRef')
</template>

<script lang="tsx">
import { PropType, computed, defineComponent, ref } from 'vue';
import { VObject } from '@/lib/vails';
import { RankingBarData } from '../baseUtils/baseChat';
import { getThemeColor, getThemeLabelLineColor } from '@/engines/screen/utils/util';

const ComScreenBaseJade = defineComponent({
  name: 'ComScreenBaseJade',
  props: {
    // 需要merge的option - 高级功能
    mergeOption: { type: Object, default: () => {} },
    // 是否完全替换
    isOnlyMerge: { type: Boolean, default: false },
    // 百分比值 - 0-100
    data: {
      type: Array as PropType<RankingBarData[]>,
      default: () => [],
    },
    showLegend: {
      type: Boolean,
      default: () => false,
    },
    sumValue: { type: Number, default: () => 1000 },
    customColor: { type: Array as PropType<string[]>, default: () => [] },
  },
  setup(props) {
    const echartsRef = ref();
    const mergeBaseOption = (options: VObject, onlyMerge: boolean) => {
      echartsRef.value?.mergeOption(options, onlyMerge);
    };
    const colorList = ['#8277E9', '#3D7FFF', '#00FFFF', '#6236FF', '#FEDB65'];

    const options = computed(() => {
      const finalColor = [...props.customColor, ...colorList];
      const res = [...props.data]
        .map((el, index) => {
          return [
            {
              // name: '学历',
              type: 'pie',
              clockWise: true,
              color: finalColor[index],
              z: 2,
              hoverAnimation: false,
              radius: [73 - index * 15 + '%', 68 - index * 15 + '%'],
              center: ['50%', '55%'],
              label: {
                show: false,
                formatter: '{d}%',
                color: 'RGB(246,175,101)',
                fontSize: 25,

                position: 'inside',
              },
              labelLine: {
                show: false,
              },
              data: [
                {
                  value: el.seriesData,
                  name: el.seriesName,
                },
                {
                  value: props.sumValue - el.seriesData,
                  name: '',
                  itemStyle: {
                    color: getThemeColor(),
                    borderWidth: 0,
                  },
                  tooltip: {
                    show: false,
                  },
                  label: {
                    show: false,
                  },
                  hoverAnimation: false,
                },
              ],
            },
            {
              name: '背景线',
              type: 'pie',
              silent: true,
              z: 1,
              clockWise: true,
              hoverAnimation: false,
              radius: [73 - index * 15 + '%', 68 - index * 15 + '%'],
              center: ['50%', '55%'],
              label: {
                show: false,
              },
              itemStyle: {
                label: {
                  show: false,
                },
                labelLine: {
                  show: false,
                },
                borderWidth: 5,
              },
              data: [
                {
                  value: 100,
                  itemStyle: {
                    color: getThemeLabelLineColor(),
                    borderWidth: 0,
                  },
                  tooltip: {
                    show: false,
                  },
                  hoverAnimation: false,
                },
              ],
            },
          ];
        })
        .flat();
      console.log(res);
      return {
        title: {
          text: '',
          textStyle: {
            color: getThemeColor(), // 坐标的字体颜色
          },
        },
        legend: {
          show: props.showLegend,
          left: 'center',
          top: '0',
          itemHeight: 8,
          itemWidth: 8,
          icon: 'roundRect',
          data: props.data.map(el => el.seriesName),
          textStyle: {
            color: getThemeColor(),
          },
        },

        grid: {
          top: '16%',
          bottom: '54%',
          left: '50%',
          containLabel: false,
        },

        xAxis: [
          {
            show: false,
          },
        ],
        series: [...res],
      };
    });

    return {
      options,
      echartsRef,
      mergeBaseOption,
    };
  },
});
export default ComScreenBaseJade;
</script>

<style lang="stylus" scoped></style>
