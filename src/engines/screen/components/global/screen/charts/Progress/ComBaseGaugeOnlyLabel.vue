<template lang="pug">
.com_base_gauge_normal.w-full.h-full
  ComScreenGauge(
    v-bind='$props',
    gaugeType='onlyLabel'
  )
</template>
<script lang="ts">
import { PropType, defineComponent, toRefs, computed } from 'vue';
import ComScreenGauge from './ComScreenGauge.vue';
import { progressScreenData } from '../baseUtils/baseChat';

const ComBaseGaugeOnlyLabel = defineComponent({
  name: 'ComBaseGaugeOnlyLabel',
  components: {
    ComScreenGauge,
  },
  props: {
    // 需要merge的option - 高级功能
    mergeOption: { type: Object, default: () => {} },
    // 是否完全替换
    isOnlyMerge: { type: Boolean, default: false },
    data: {
      type: Array as PropType<progressScreenData[]>,
      default: () => [
        {
          seriesName: 'progress-1',
          seriesData: 80,
        },
      ],
    },
    center: {
      type: Array,
      default: () => ['50%', '50%'],
    },
    color: {
      type: String,
      default: () => '#91c7ae',
    },
    showTitle: {
      type: Boolean,
      default: () => true,
    },
    title: {
      type: String,
      default: () => '通过率',
    },
    lastWeekCompare: {
      type: Number,
      default: () => 120,
    },
    lastMonthCompare: {
      type: Number,
      default: () => -140,
    },
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
export default ComBaseGaugeOnlyLabel;
</script>
<style lang="stylus" scoped></style>
