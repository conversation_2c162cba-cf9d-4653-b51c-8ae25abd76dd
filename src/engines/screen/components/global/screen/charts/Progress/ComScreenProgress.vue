<template lang="pug">
.w-full.h-full
  .com_screen_progress.bg-center.bg-cover.bg-no-repeat.flex.items-center.justify-center.relative(:style='`--borderColor:${borderColor}`')
    .absolute.-bottom-1.z-5.left-0.rc_mask.bg-cover(v-if="progressType === 'dashboard'",class="w-full h-[24%]")
    .rc_top_left_wrapper.absolute.flex.flex-col.items-center.z-30.text-white
      .rc_top_left_wrapper_num {{getPercent}}{{ digit }}
      .rc_top_left_wrapper_text.text-xs(v-if="showLabel") {{ label }}
    a-progress.z-5.w-full.h-full.items-center.justify-center(
      :type="progressType"
      :strokeColor="strokeColor",
      :trailColor="trailColor"
      :strokeWidth='12'
      :percent='getPercent',
      :width='150',
      :showInfo="false",
      class="!flex"
      :style='`--progressWidth:${getProgressSize};--progressHeight:${getProgressSize}`'
    )
</template>

<script lang="tsx">
import echarts from 'echarts';
import { PropType, computed, defineComponent, toRefs } from 'vue';
import { progressScreenData } from '../baseUtils/baseChat';

export default defineComponent({
  name: 'ComScreenProgress',
  props: {
    data: {
      type: Array as PropType<progressScreenData[]>,
      default: () => [],
    },
    progressType: {
      type: String as PropType<'dashboard' | 'circle'>,
      default: () => 'dashboard',
    },
    // 进度条有进度部分颜色
    strokeColor: {
      type: Object,
      default: () => {
        return {};
      },
    },
    // 进度条没进度部分颜色
    trailColor: {
      type: String,
      default: () => {
        return '';
      },
    },

    // 进度条边框颜色
    borderColor: {
      type: String,
      default: () => {
        return '';
      },
    },
    showLabel: {
      type: Boolean,
      default: () => false,
    },
    // 描述标题
    label: {
      type: String,
      default: () => '',
    },
    // 数量单位
    digit: {
      type: String,
      default: () => '',
    },
  },
  setup(props) {
    const getPercent = computed(() => props.data?.[0].seriesData);
    const getProgressSize = computed(() => {
      return props.progressType === 'dashboard' ? '80%' : '95%';
    });

    return {
      ...toRefs(props),
      getPercent,
      getProgressSize,
    };
  },
});
</script>

<style lang="stylus" scoped>
.com_screen_progress
  width 100%
  height 100%
  &::after
    content: "";
    position: absolute;
    width: calc(100% - 4px);
    height: calc(100% - 4px);
    background: #050b21;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 3;
    border-radius 50%

  &::before
    content: "";
    position: absolute;
    width 100%
    height 100%
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    border-radius: 2px;
    background: var(--borderColor)
    z-index: 2;
    border-radius 50%

  .rc_top_left_wrapper_num
    font-size 28px
    font-weight 700
  .ant-progress-text
    display none
  .rc_mask
    background-color #050b21

  ::v-deep(.ant-progress-inner)
    width var(--progressWidth) !important
    height var(--progressHeight) !important
</style>
