<script lang="ts">
import { defineComponent, toRefs, computed } from 'vue';
const ComScreenBaseSvgProgress = defineComponent({
  name: 'ComScreenBaseSvgProgress',
  components: {},
  props: {
    data: {
      type: Array,
      default: () => [
        {
          name: 'rust',
          percent: 40,
        },
        {
          name: 'ruby',
          percent: 20,
        },
        {
          name: 'python',
          percent: 10,
        },
      ],
    },
    radius: {
      type: Number,
      default: 30,
    },
    strokeWidth: {
      type: String,
      default: '5',
    },
    baseStrokeWidth: {
      type: String,
      default: '5',
    },
    baseStrokeColor: {
      type: String,
      default: 'transparent',
    },
    colorArr: {
      type: Array,
      default: ['red', 'yellow', 'green'],
    },
    showText: {
      type: Boolean,
      default: true,
    },
    rotate: {
      type: Number,
      default: 0,
    },
    textColor: {
      type: String,
      default: 'black',
    },
    fontSize: {
      type: Number,
      default: 14,
    },
  },
  setup(props) {
    const totalValue = computed(() => {
      return props.data.reduce((pre: number, pro: any): number => {
        return pre + Number(pro.percent) / 100;
      }, 0);
    });
    const calcRotateAngle = (index: number) => {
      if (index === 0) return 0;
      const gap = (1 - Number(totalValue.value)) / props.data.length;
      const slice = props.data.slice(0, index).map((item: any) => Number(item.percent) / 100);
      const result = (slice.reduce((pre: number, pro: number) => pre + pro, 0) + index * gap) * 360;
      return result;
    };
    const localData = computed(() => {
      const l = 2 * props.radius * 3.14;
      return props.data.map((item: any, index: number) => ({
        ...item,
        solid: Number((item.percent / 100) * l),
        dashed: l - Number((item.percent / 100) * l),
        angle: calcRotateAngle(index),
      }));
    });
    const generatorD = computed(() => {
      const r = props.radius;
      return `M${100 - r} 100 A${r} ${r} 0 0 1 ${100 + r} 100 A${r} ${r} 0 0 1 ${100 - r} 100`;
    });
    return {
      ...toRefs(props),
      localData,
      generatorD,
      totalValue,
    };
  },
});
export default ComScreenBaseSvgProgress;
</script>

<template lang="pug">
svg.com-screen-base-svg-progress(
  width='100%',
  height="100%",
  viewBox="0 0 200 200",
  preserveAspectRatio="xMidYMid meet"
)
  path.z-1.base__circle.relative(
    :stroke='baseStrokeColor',
    :stroke-width="baseStrokeWidth",
    :d="generatorD",
    fill='none'
  )
  text(
    x="50%",
    y="50%",
    text-anchor="middle",
    dominant-baseline="central",
    :fill="textColor"
    :font-size='fontSize'
    v-if='showText'
  ) {{ ~~(totalValue * 100) }}%
  path.z-2.circle__progress(
    v-for="(item,index) in localData",
    :stroke='`${colorArr[index]}`',
    :stroke-width="strokeWidth",
    stroke-linecap='round',
    :d="generatorD",
    fill='none',
    :transform='`rotate(${item.angle + rotate} 100 100)`',
    :style='`--solid:${item.solid};--dashed:${item.dashed};`'
  )
</template>

<style lang="stylus" scoped>
.com-screen-base-svg-progress
  path
    transition all .3s
  .circle__progress
    stroke-dasharray var(--solid,0),var(--dashed,0)
</style>
