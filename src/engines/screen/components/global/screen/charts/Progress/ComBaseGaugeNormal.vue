<template lang="pug">
.com_base_gauge_normal.w-full.h-full
  ComScreenGauge(v-bind='$props', gaugeType='border')
</template>
<script lang="ts">
import { PropType, defineComponent, toRefs } from 'vue';
import { progressScreenData } from '../baseUtils/baseChat';
import ComScreenGauge from './ComScreenGauge.vue';

const ComBaseGaugeNormal = defineComponent({
  name: 'ComBaseGaugeNormal',
  components: {
    ComScreenGauge,
  },
  props: {
    // 需要merge的option - 高级功能
    mergeOption: { type: Object, default: () => {} },
    // 是否完全替换
    isOnlyMerge: { type: Boolean, default: false },
    data: {
      type: Array as PropType<progressScreenData[]>,
      default: () => [
        {
          seriesName: 'progress-1',
          seriesData: 80,
        },
      ],
    },
    center: {
      type: Array,
      default: () => ['50%', '60%'],
    },
    color: {
      type: String,
      default: () => '#91c7ae',
    },
    showTitle: {
      type: Boolean,
      default: () => true,
    },
    title: {
      type: String,
      default: () => '通过率',
    },
    lastWeekCompare: {
      type: Number,
      default: () => 120,
    },
    lastMonthCompare: {
      type: Number,
      default: () => -140,
    },
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
export default ComBaseGaugeNormal;
</script>
<style lang="stylus" scoped></style>
