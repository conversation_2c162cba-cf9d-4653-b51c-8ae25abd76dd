<template lang="pug">
.com_base_progress.w-full.h-full
  ComScreenProgress(
    v-bind='$props'
  )
</template>
<script lang="ts">
import { PropType, defineComponent, toRefs, computed } from 'vue';
import ComScreenProgress from './ComScreenProgress.vue';
import { progressScreenData } from '../baseUtils/baseChat';

const ComBaseProgress = defineComponent({
  name: 'ComBaseProgress',
  components: {
    ComScreenProgress,
  },
  props: {
    data: {
      type: Array as PropType<progressScreenData[]>,
      default: () => [],
    },
    progressType: {
      type: String as PropType<'dashboard' | 'circle'>,
      default: () => 'dashboard',
    },
    // 进度条有进度部分颜色
    strokeColor: {
      type: Object,
      default: () => {
        return {};
      },
    },
    // 进度条没进度部分颜色
    trailColor: {
      type: String,
      default: () => {
        return '';
      },
    },

    // 进度条边框颜色
    borderColor: {
      type: String,
      default: () => {
        return '';
      },
    },
    // 描述标题
    label: {
      type: String,
      default: () => '',
    },
    showLabel: {
      type: Boolean,
      default: () => false,
    },
    // 数量单位
    digit: {
      type: String,
      default: () => '',
    },
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
export default ComBaseProgress;
</script>
<style lang="stylus" scoped></style>
