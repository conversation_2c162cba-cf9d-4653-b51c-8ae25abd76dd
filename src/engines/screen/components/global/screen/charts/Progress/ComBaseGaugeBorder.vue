<template lang="pug">
.com_base_gauge_border.w-full.h-full
  ComScreenGauge(
    v-bind='$props',
    gaugeType='border'
  )
</template>
<script lang="ts">
import { PropType, defineComponent, toRefs, computed } from 'vue';
import ComScreenGauge from './ComScreenGauge.vue';
import { progressScreenData } from '../baseUtils/baseChat';

const ComBaseGaugeBorder = defineComponent({
  name: 'ComBaseGaugeBorder',
  components: {
    ComScreenGauge,
  },
  props: {
    // 需要merge的option - 高级功能
    mergeOption: { type: Object, default: () => {} },
    // 是否完全替换
    isOnlyMerge: { type: Boolean, default: false },
    data: {
      type: Array as PropType<progressScreenData[]>,
      default: () => [
        {
          seriesName: 'progress-1',
          seriesData: 80,
        },
      ],
    },
    center: {
      type: Array,
      default: () => ['50%', '60%'],
    },
    color: {
      type: String,
      default: () => '#91c7ae',
    },
    showTitle: {
      type: Boolean,
      default: () => true,
    },
    title: {
      type: String,
      default: () => '通过率',
    },
    lastWeekCompare: {
      type: Number,
      default: () => 120,
    },
    lastMonthCompare: {
      type: Number,
      default: () => -140,
    },
    colorBorderSet: {
      type: Array,
      default: () => [
        [0.25, '#2174FF'],
        [0.5, '#07A872'],
        [0.75, '#ED8139'],
        [1, '#FB6E77'],
      ],
    },
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
export default ComBaseGaugeBorder;
</script>
<style lang="stylus" scoped></style>
