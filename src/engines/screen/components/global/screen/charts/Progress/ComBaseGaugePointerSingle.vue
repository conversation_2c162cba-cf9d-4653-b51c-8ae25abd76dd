<template lang="pug">
.com_base_gauge_pointer_single.w-full.h-full
  ComScreenGauge(
    v-bind='$props',
    gaugeType='labelWithPointer'
  )
</template>
<script lang="ts">
import { PropType, defineComponent, toRefs, computed } from 'vue';
import ComScreenGauge from './ComScreenGauge.vue';
import { progressScreenData } from '../baseUtils/baseChat';

const ComBaseGaugePointerSingle = defineComponent({
  name: 'ComBaseGaugePointerSingle',
  components: {
    ComScreenGauge,
  },
  props: {
    // 需要merge的option - 高级功能
    mergeOption: { type: Object, default: () => {} },
    // 是否完全替换
    isOnlyMerge: { type: Boolean, default: false },
    data: {
      type: Array as PropType<progressScreenData[]>,
      default: () => [
        {
          seriesName: 'progress-1',
          seriesData: 80,
        },
      ],
    },
    center: {
      type: Array,
      default: () => ['50%', '50%'],
    },
    color: {
      type: String,
      default: () => '#91c7ae',
    },
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
export default ComBaseGaugePointerSingle;
</script>
<style lang="stylus" scoped></style>
