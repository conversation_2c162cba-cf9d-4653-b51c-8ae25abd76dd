<template lang="pug">
.com_base_line_shanghai_map.w-full.h-full
  ComScreenLineShanghaiMap(
    v-bind='$props'
  )
</template>
<script lang="ts">
import { PropType, defineComponent, toRefs, computed } from 'vue';
import ComScreenLineShanghaiMap from './ComScreenLineShanghaiMap.vue';
import { ForceGraphLineLevelData, ForceGraphData, ScreenLinePointMap } from '../baseUtils/baseChat';

const ComBaseLineShanghaiMap = defineComponent({
  name: 'ComBaseLineShanghaiMap',
  components: {
    ComScreenLineShanghaiMap,
  },
  props: {
    // 需要merge的option - 高级功能
    mergeOption: { type: Object, default: () => {} },
    // 是否完全替换
    isOnlyMerge: { type: Boolean, default: false },

    // 自定义色彩，【或者可以支持组选新颜色?】
    customizeLineColor: { type: Array, default: () => [] },
    customizeAreaColor: { type: Array, default: () => [] },

    // 图表数据 seriesData 为纯数据项 , seriesName 对应的每一例数据代表的数据别称
    data: {
      type: Array as PropType<ForceGraphData[]>,
      default: () => [],
    },
    // 有几个点值
    pointMap: {
      type: Array as PropType<ScreenLinePointMap[]>,
      default: () => [],
    },
    // 图例各区间范围值
    lineLevel: {
      type: Array as PropType<ForceGraphLineLevelData[]>,
      default: () => [],
    },
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
export default ComBaseLineShanghaiMap;
</script>
<style lang="stylus" scoped></style>
