<template lang="pug">
.w-full.h-full 
  TaScreenEchartBase(:options='barOption' ref='echartsRef')
</template>

<script lang="ts">
import { VObject } from '@/lib/vails';
import { PropType, computed, defineComponent, ref, toRefs } from 'vue';
import echarts from 'echarts';
import { cloneDeep, flatten, max, min } from 'lodash';
import { BarData, barAreaColor, lineColor } from '../baseUtils/baseChat';

import { useScreenDataFetchCollectionInject } from '@/components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';
import {
  getThemeColor,
  getThemeItemLineColor,
  getThemeLabelLineColor,
} from '@/engines/screen/utils/util';

const ComScreenHorizontalBar = defineComponent({
  name: 'ComScreenHorizontalBar',
  props: {
    // 需要merge的option - 高级功能
    // mergeOption: { type: Object, default: () => {} },
    mergeOption: {
      type: Object,
      default: () => {},
    },
    // 是否完全替换
    isOnlyMerge: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Array as PropType<BarData[]>,
      default: () => [],
    },
    // 图位置 top right bottom right
    gridPosition: {
      type: Array,
      default: () => ['10%', '10%', '10%', '15%'],
    },
    // X轴显示名称，为空则不显示X轴标
    yAxisLabel: {
      type: Array as PropType<string[]>,
      default: () => ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
    },
    // 图例标题
    title: {
      type: String,
      default: '',
    },
    // 图例标题布局
    titlePadding: {
      type: Array as PropType<number[]>,
      default: () => [0, 0, 0, 16],
    },
    unit: {
      type: String,
      default: '',
    },
    // 是否显示x轴和对应轴线
    showXAxis: {
      type: Boolean,
      default: false,
    },
    // 是否要显示数据点标注
    showLabel: {
      type: Boolean,
      default: false,
    },
    // 是否要展示柱状图背景
    showBg: {
      type: Boolean,
      default: false,
    },
    // 柱状图宽度
    barWidth: {
      type: String || Number,
      default: '',
    },
    // 柱条单色、渐变色
    colorType: {
      type: String as PropType<'solid' | 'gradient'>,
      default: 'gradient',
    },
    barAreaColor: {
      type: Array as PropType<string[]>,
      default: () => barAreaColor,
    },
    // 是否要展示tooltip
    showTootip: {
      type: Boolean,
      default: true,
    },
    axisLabelFormatter: {
      type: Function,
      default: (value: any) => {
        return value;
      },
    },
    //  翻转数据
    reverseChart: {
      type: Boolean,
      default: () => false,
    },
    borderRadius: {
      type: Array,
      default: () => [0, 0, 0, 0],
    },
    maxValue: {
      type: Number,
      default: '',
    },
    isStack: {
      type: Boolean,
      default: false,
    },
    // 是否显示图例
    showLengend: { type: Boolean, default: false },
    // 图例文字颜色
    legendTextColor: {
      type: String,
      default: () => 'white',
    },
    lengendPosition: {
      type: Array,
      default: () => ['bottom', 0, 0, 'left'],
    },
    yAxisTextColor: { type: String, default: () => getThemeColor() },
    xAxisTextColor: { type: String, default: () => getThemeColor() },
    xAxisSplitColor: { type: String, default: () => getThemeLabelLineColor() },
  },
  setup(props: VObject) {
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    const { getScreenData, dataResult } = useScreenDataFetchCollectionInject(props);

    const echartsRef = ref();
    const mergeBaseOption = (options: VObject, onlyMerge: boolean) => {
      echartsRef.value?.mergeOption(options, onlyMerge);
    };
    const barOption = computed(() => {
      const [top, right, bottom, left] = props.lengendPosition;
      const positionOpt = { top, right, bottom, left };
      return {
        grid: {
          top: props.gridPosition[0] || 0,
          right: props.gridPosition[1] || 0,
          bottom: props.gridPosition[2] || 0,
          left: props.gridPosition[3] || 0,
        },
        tooltip: {
          show: props.showTootip,
          trigger: 'axis',
          backgroundColor: 'white',
          textStyle: {
            color: 'black',
          },
          formatter: (record: VObject) => {
            const getChildren = () => {
              return record
                .filter((item: any, index: number) => item.seriesName !== 'bg')
                .map((item: any) => {
                  const color = item.color?.colorStops
                    ? item.color?.colorStops[1].color
                    : item.color;
                  return `<div class="flex">
                      <span style="display:flex;margin-right:5px;margin-top:5.5px;border-radius:10px;width:10px;height:10px;background-color:${color};"></span>
                      <div class="mr-1">${item.seriesName}: </div>
                      <div>
                        ${item.data}${props.unit}
                      </div>
                    </div>`;
                })
                .join(' ');
            };
            return `<div>
                <div>${record[0].name}</div>
                ${getChildren()}
              </div>`;
          },
        },
        ...(props.showLengend
          ? {
              legend: {
                data: props?.data.map((el: any) => el.seriesName),
                itemHeight: 8,
                itemWidth: 12,
                icon: 'roundRect',
                textStyle: {
                  color: getThemeColor(),
                },
                ...positionOpt,
              },
            }
          : {}),
        ...(props.title
          ? {
              title: {
                text: props.title,
                padding: props.titlePadding,
                textStyle: {
                  color: getThemeColor(),
                  'font-family': 'Inter',
                  'font-size': '14px',
                  'font-weight': 400,
                },
              },
            }
          : {}),
        ...(props.showXAxis
          ? {
              xAxis: {
                type: 'value',
                show: true,
                axisTick: {
                  show: false,
                },
                splitLine: {
                  show: true,
                  lineStyle: {
                    type: 'dashed',
                    color: props.xAxisSplitColor,
                    width: 1,
                  },
                },
                axisLine: {
                  show: false,
                  lineStyle: {
                    type: 'dashed',
                    color: '#EBF5FF',
                    width: 0,
                  },
                },
                axisLabel: {
                  rotate: props.labelXRotate,
                  interval: 0,
                  formatter: props.axisLabelFormatter,
                  textStyle: {
                    color: props.xAxisTextColor,
                  },
                },
              },
            }
          : {
              xAxis: {
                type: 'value',
                show: false,
              },
            }),

        yAxis: {
          type: 'category',
          data: props.reverseChart
            ? (cloneDeep(props.yAxisLabel) || []).reverse()
            : props.yAxisLabel,
          axisTick: {
            show: false,
          },
          axisLine: {
            lineStyle: {
              color: '#®',
              width: 0,
            },
          },

          axisLabel: {
            textStyle: {
              color: props.yAxisTextColor,
            },
          },
        },
        series: flatten([
          props?.data.map((el: any, index: number) => {
            const maxValue: any = max(el.seriesData);

            return {
              data: props.reverseChart ? (cloneDeep(el.seriesData) || []).reverse() : el.seriesData,
              name: el.seriesName,
              z: 2,
              type: 'bar',
              itemStyle: {
                color:
                  props.colorType === 'gradient'
                    ? {
                        type: 'linear',
                        ...(maxValue >= 0
                          ? {
                              x: 1,
                              y: 0,
                              x2: 0,
                              y2: 0,
                            }
                          : {
                              x: 0,
                              y: 0,
                              x2: 1,
                              y2: 0,
                            }),
                        colorStops: Array.isArray(props.barAreaColor[index])
                          ? [
                              {
                                offset: 1,
                                color: props.barAreaColor[index]?.[0], // 0% 处的颜色
                              },
                              {
                                offset: 0,
                                color: props.barAreaColor[index]?.[1], // 100% 处的颜色
                              },
                            ]
                          : [
                              {
                                offset: 1,
                                color: 'rgba(3, 44, 90, 1)', // 0% 处的颜色
                              },
                              {
                                offset: 0,
                                color: props.barAreaColor[index], // 100% 处的颜色
                              },
                            ],
                      }
                    : props.barAreaColor[index] || lineColor[index],
                barBorderRadius: props.borderRadius,
              },
              barWidth: props.barWidth || 'auto',
              stack: props.isStack,

              ...(props.showLabel && !props.showBg
                ? {
                    label: {
                      show: true,
                      color: 'white',
                      position: props.isStack ? 'inside' : 'right',
                      formatter: (data: any) => data.value?.toLocaleString() + props.unit,
                    },
                  }
                : {}),
            };
          }),
          ...(props.showBg
            ? props?.data.map((el: any, index: number) => {
                const process_data = props.reverseChart
                  ? (cloneDeep(el.seriesData) || []).reverse()
                  : el.seriesData;
                const maxValue: any = max(el.seriesData);
                const process_value = maxValue >= 0 ? maxValue : min(el.seriesData);
                const finalMaxValue = Array.isArray(props.maxValue)
                  ? props.maxValue[index]
                  : props.maxValue;
                return {
                  comType: 'bg',
                  data: process_data.map((val: any) => ({
                    value: finalMaxValue || process_value,
                    label: val,
                  })),
                  name: 'bg',
                  z: 1,
                  type: 'bar',
                  barGap: '-100%',

                  itemStyle: {
                    color: getThemeItemLineColor(),
                    // barBorderRadius: props.borderRadius,
                  },
                  barWidth: props.barWidth || 'auto',
                  ...(props.showLabel
                    ? {
                        label: {
                          show: true,
                          color: getThemeColor(),
                          position: 'right',
                          formatter: (data: any) => {
                            return data.data?.label?.toLocaleString() + props.unit;
                          },
                        },
                      }
                    : {}),
                };
              })
            : []),
        ]),
      };
    });

    return {
      ...toRefs(props),
      barOption,
      echartsRef,
      mergeBaseOption,
    };
  },
});
export default ComScreenHorizontalBar;
</script>

<style lang="stylus" scoped></style>
