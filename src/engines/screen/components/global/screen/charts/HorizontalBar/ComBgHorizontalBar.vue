<template lang="pug">
.com_base_bg_bar.w-full.h-full
  ComScreenHorizontalBar(
    v-bind='$props'
  )
</template>
<script lang="ts">
import { PropType, defineComponent, toRefs } from 'vue';
import ComScreenHorizontalBar from './ComScreenHorizontalBar.vue';
import { barAreaColor, BarData, BaseData } from '../baseUtils/baseChat';

export default defineComponent({
  name: 'ComBgHorizontalBar',
  components: {
    ComScreenHorizontalBar,
  },
  props: {
    mergeOption: {
      type: Object,
      default: () => {},
    },
    // 是否完全替换
    isOnlyMerge: {
      type: Boolean,
      default: false,
    },
    data: {
      type: Array as PropType<BarData[]>,
      default: () => [],
    },
    // 图位置 top right bottom right
    gridPosition: {
      type: Array,
      default: () => ['10%', '10%', '10%', '10%'],
    },
    // X轴显示名称，为空则不显示X轴标
    yAxisLabel: {
      type: Array as PropType<string[]>,
      default: () => ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
    },
    // 图例标题
    title: {
      type: String,
      default: '',
    },
    // 图例标题布局
    titlePadding: {
      type: Array as PropType<number[]>,
      default: () => [0, 0, 0, 16],
    },

    // 是否要显示数据点标注
    showLabel: {
      type: Boolean,
      default: true,
    },
    // 是否要展示柱状图背景
    showBg: {
      type: Boolean,
      default: true,
    },
    // 柱状图宽度
    barWidth: {
      type: String || Number,
      default: '',
    },
    // 柱条单色、渐变色
    colorType: {
      type: String as PropType<'solid' | 'gradient'>,
      default: 'gradient',
    },
    barAreaColor: {
      type: Array as PropType<string[]>,
      default: () => barAreaColor,
    },
    // 是否要展示tooltip
    showTootip: {
      type: Boolean,
      default: true,
    },
    axisLabelFormatter: {
      type: Function,
      default: (value: any) => {
        return value;
      },
    },
    unit: {
      type: String,
      default: '',
    },
    //  翻转数据
    reverseChart: {
      type: Boolean,
      default: () => false,
    },
    // 是否显示x轴和对应轴线
    showXAxis: {
      type: Boolean,
      default: false,
    },
    borderRadius: {
      type: Array,
      default: () => [0, 0, 0, 0],
    },
    maxValue: {
      type: Number,
      default: '',
    },
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
</script>
<style lang="stylus" scoped></style>
