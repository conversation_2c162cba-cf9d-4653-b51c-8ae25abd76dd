<script lang="ts">
import { ref, defineComponent, toRefs, computed } from 'vue';
import { defineTaBuilderCellComponent } from '@/components/global/ta-component/TaBuilder/builder/defineTaBuilderCellComponent';
import { useScreenDataFetchCollectionInject } from '@/components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';
import { VObject } from '@/lib/vails';
import { sankeyDataProps, sankeyLinksProps } from './echartBaseProps';

const TaScreenSankeyChart = defineTaBuilderCellComponent({
  name: 'TaScreenSankeyChart',
  components: {},
  props: {
    ...sankeyDataProps(),
    ...sankeyLinksProps(),
  },
  setup(props: any, { emit }: any) {
    const { getScreenData } = useScreenDataFetchCollectionInject(props);

    const options = computed(() => {
      return {
        color: ['#5470C6', '#91CC75', '#FAC858', '#EE6666 ', '#73C0DE'],
        series: {
          type: 'sankey',
          layout: 'none',
          emphasis: {
            focus: 'adjacency',
          },
          label: {
            color: '#fff',
          },
          itemStyle: {
            borderColor: 'transparent',
          },
          data: (props.data || []).map((dataProps: VObject) => {
            return {
              name: dataProps.name,
              itemStyle: { color: dataProps.color },
            };
          }),
          links: (props.links || []).map((linkProps: VObject) => {
            return {
              source: linkProps.source,
              target: linkProps.target,
              value: getScreenData(linkProps, ''),
              // color: barProps.color,
              lineStyle: {
                color: 'target',
              },
            };
          }),
        },
      };
    });

    const onPop = (key: string) => {
      emit('pop', key);
    };

    return {
      ...toRefs(props),
      options,
      onPop,
    };
  },
});
export default TaScreenSankeyChart;
</script>

<template lang="pug">
.ta-screen-sankey-chart.w-full.h-full
  TaScreenEchartBase(:options='options', @pop='onPop')
</template>

<style lang="stylus" scoped></style>
