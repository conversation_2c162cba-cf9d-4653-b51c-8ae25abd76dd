<script lang="ts">
import { toRefs } from 'vue';
import { defineTaBuilderCellComponent } from '@/components/global/ta-component/TaBuilder/builder/defineTaBuilderCellComponent';

const TaScreenPopover = defineTaBuilderCellComponent({
  name: 'TaScreenPopover',
  components: {},
  props: {
    title: {
      label: '标题',
      type: String,
      formType: 'input',
      default: '',
    },
  },
  setup(props: any) {
    return {
      ...toRefs(props),
    };
  },
});
export default TaScreenPopover;
</script>

<template lang="pug">
a-popover.ta-screen-popover(:title='title')
  template(#content)
    slot(name='content')
  template(#default)
    slot
</template>

<style lang="stylus" scoped></style>
