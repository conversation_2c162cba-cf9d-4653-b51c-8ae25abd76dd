<script lang="ts">
import { defineComponent, toRefs, computed, ref, onUnmounted, PropType, onUpdated } from 'vue';
import { useScreenDataFetchCollectionInject } from '@/components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';
import { defineTaBuilderCellComponent } from '@/components/global/ta-component/TaBuilder/builder/defineTaBuilderCellComponent';
import { dataPropsFn } from './echartBaseProps';
import { onMounted } from '@vue/runtime-core';
import { VObject } from '@/lib/vails';
// 说明文档
// https://github.com/xfy520/vue3-seamless-scroll
// https://gitee.com/longxinziyan/vue3-seamless-scroll
import { usePreviewClickable } from '@/components/global/ta-component/TaBuilder/builder/usePreview';
import { TaBuilderEvent } from '@/components/global/ta-component/TaBuilder/types';
import { get } from 'lodash';

export interface ScreenTableColumnInterface {
  title: string;
  dataIndex: string;
  color?: string;
  width?: string;
  component?: string;
}

const TaScreenScrollTable = defineTaBuilderCellComponent({
  name: 'TaScreenScrollTable',
  components: {},
  props: {
    ...dataPropsFn({ group: '', modelKeyPrefix: '' }, 'props', 'json'),
    ellipsis: {
      label: '文本省略',
      formType: 'switch',
      type: Boolean,
      default: false,
    },
    skin: {
      label: '皮肤',
      formType: 'radio',
      type: String,
      select: [
        { label: '无', value: '' },
        { label: '斑马纹-1', value: 'zebra' },
        { label: '斑马纹-2', value: 'zebra-2' },
        { label: '纯色', value: 'pure' },
      ],
      default: '',
    },
    columns: {
      label: 'columns',
      formType: 'json',
      type: Array as PropType<ScreenTableColumnInterface[]>,
      default: [
        {
          title: '轮次',
          dataIndex: 'name',
        },
        {
          title: '单位',
          dataIndex: 'user_name',
        },
        {
          title: '环节',
          dataIndex: 'tour_mode',
        },
      ],
    },
    data: {
      type: Array,
      hidden: true,
      default: [
        { name: '第二轮', user_name: '市民政局', tour_mode: '整改' },
        { name: '第三轮', user_name: '市委党校', tour_mode: '了解' },
        { name: '第四轮', user_name: '市民政局', tour_mode: '整改' },
        { name: '第五轮', user_name: '市委党校', tour_mode: '了解' },
        { name: '第六轮', user_name: '市民政局', tour_mode: '整改' },
        { name: '第七轮', user_name: '市民政局', tour_mode: '整改' },
      ],
    },
  },
  setup(props: any) {
    const { getScreenData } = useScreenDataFetchCollectionInject(props);
    const value = computed(() => getScreenData(props, '') || props.data);

    const hContainer = ref<any>(null);

    const { onClick: onPreviewClick } = usePreviewClickable(props);

    const onClick = (item: VObject) => {
      props.componentSchema?.events?.forEach((event: TaBuilderEvent) => {
        onPreviewClick(event, { ...item, item }, item);
      });
    };

    const getData = (column: ScreenTableColumnInterface, item: VObject) => {
      return typeof get(item, column.dataIndex) === 'undefined' ? '-' : get(item, column.dataIndex);
    };

    const offsetTop = ref(0);

    const syncOffsetTop = () => {
      offsetTop.value = hContainer.value?.clientHeight;
    };

    onMounted(syncOffsetTop);
    onUpdated(syncOffsetTop);
    setTimeout(syncOffsetTop, 2000);

    return {
      ...toRefs(props),
      value,
      hContainer,
      onClick,
      getData,
      offsetTop,
    };
  },
});
export default TaScreenScrollTable;
</script>

<template lang="pug">
.w-full.ta-screen-scroll-table.overflow-hidden.w-full.h-full.bg-transparent(:class='skin')
  .h-full.overflow-hidden.relative(@click.stop='')
    table.header-table.w-full.pointer-events-none(
      :style='{ "table-layout": ellipsis ? "fixed" : "auto" }'
    )
      colgroup
        col(v-for='column in columns', :style='{ width: column.width }')
      thead(ref='hContainer')
        tr
          th.text-center.border-b.border-white.border-solid(v-for='column in columns')
            .h-full.text-white.py-2.text-3xl.text-center(:style='{ width: column.width }')
              | {{ column.title }}
      tbody.opacity-0
        tr.text-3xl.text-white.truncate(
          v-for='(item, index) in value',
          :key='index',
          :style='{ background: item.background }'
        )
          td.title.text-3xl.text-white.text-center.font-light(
            v-for='column in columns',
            :class='index % 2 === 0 ? "light-tr" : "dark-tr"',
            :style='{ width: column.width }'
          )
            .py-4.px-1.h-full.w-full.text-center(
              :class='{ truncate: ellipsis }',
              :style='{ color: column.color || item.color }'
            )
              component.h-full.w-full.text-center(
                :is='column.component || "div"',
                :item='item',
                :column='column',
                :value='getData(column, item)',
                :style='{ color: column.color || item.color }',
                :class='{ truncate: ellipsis }'
              )
                | {{ getData(column, item) }}

    .body-container.absolute.w-full.h-full(:style='{ top: `${offsetTop}px` }')
      TaSeamlessScroll.w-full.overflow-hidden(:step='0.8')
        table.body-table.w-full(:style='{ "table-layout": ellipsis ? "fixed" : "auto" }')
          colgroup
            col(v-for='column in columns', :style='{ width: column.width }')
          thead
            tr
              th.text-center.border-b.border-white.border-solid(v-for='column in columns')
                .h-0.overflow-hidden.text-white.text-3xl.text-center(
                  :style='{ width: column.width }',
                  class='!p-0 !m-0 !h-0'
                )
                  | {{ column.title }}
          tbody
            tr.title.py-2.text-3xl.px-4.text-white.text-center.font-light(
              v-for='(item, index) in value',
              :key='index',
              :style='{ background: item.background }',
              @click.stop='() => onClick(item)'
            )
              td.title.text-3xl.text-white.text-center.font-light(
                v-for='column in columns',
                :style='{ width: column.width }',
                :class='index % 2 === 0 ? "light-tr" : "dark-tr"'
              )
                .py-4.px-1.h-full.w-full.text-center
                  component.h-full.w-full.text-center(
                    :is='column.component || "div"',
                    :item='item',
                    :column='column',
                    :value='getData(column, item)',
                    :style='{ color: column.color || item.color }',
                    :class='{ truncate: ellipsis }'
                  )
                    | {{ getData(column, item) }}
</template>

<style lang="stylus" scoped>
.h-fit
  height fit-content
.light-tr
  background linear-gradient(93.3deg, rgba(105, 180, 243, 0.2) 22.08%, rgba(105, 180, 243, 0.2) 91.33%)
.dark-tr
  background linear-gradient(93.3deg, rgba(105, 180, 243, 0.6) 22.08%, rgba(105, 180, 243, 0.6) 91.33%)
@import './table.styl'
</style>
