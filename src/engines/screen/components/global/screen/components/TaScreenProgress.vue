<script lang="ts">
import { ref, toRefs, computed } from 'vue';
import { defineTaBuilderCellComponent } from '@/components/global/ta-component/TaBuilder/builder/defineTaBuilderCellComponent';
import { dataPropsFn } from './echartBaseProps';
import { useScreenDataFetchCollectionInject } from '@/components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';

const TaScreenProgress = defineTaBuilderCellComponent({
  name: 'TaScreenProgress',
  components: {},
  props: {
    options: { type: Object, formType: 'json', default: () => ({}) },
    ...dataPropsFn({ group: '进度条百分比', modelKeyPrefix: 'percent' }, 'props', 'percent'),
    ...dataPropsFn({ group: '进度条显示值', modelKeyPrefix: 'value' }, 'props', 'number'),
  },
  setup(props: any) {
    const { getScreenData } = useScreenDataFetchCollectionInject(props);

    const percent = computed(() => getScreenData(props, 'percent'));
    const value = computed(() => getScreenData(props, 'value'));

    return {
      ...toRefs(props),
      percent,
      value,
    };
  },
});
export default TaScreenProgress;
</script>

<template lang="pug">
.ta-screen-progress.w-full.h-full
  a-progress(v-bind='{ percent, ...options }', style='position: static')
    template(#format='percent', v-if='typeof value === "number"')
      | {{ value }}
</template>

<style lang="stylus" scoped>
.ta-screen-progress
  >>> .ant-progress-text
    color white
</style>
