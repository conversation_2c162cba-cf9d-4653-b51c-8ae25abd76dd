<script lang="ts">
import { ref, toRefs, computed, watch } from 'vue';
import { defineTaBuilderCellComponent } from '@/components/global/ta-component/TaBuilder/builder/defineTaBuilderCellComponent';
import useConfigurableApi from '@/components/global/ta-component/ta-template-form-core/useConfigurableApi';
import { VObject } from '@/lib/vails';
import { dataPropsFn } from './echartBaseProps';
import { useScreenDataFetchCollectionInject } from '@/components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';
import { usePreviewClickable } from '@/components/global/ta-component/TaBuilder/builder/usePreview';
import { TaBuilderEvent } from '@/components/global/ta-component/TaBuilder/types';
import { jsonataGet } from '@/components/global/ta-component/ta-template-form-core/useJsonata';
import { cloneDeep } from 'lodash';
// import { jsonataGet } from './useJsonata';

const TaScreenTaIndexView = defineTaBuilderCellComponent({
  name: 'TaScreenTaIndexView',
  components: {},
  props: {
    path: { label: 'API 地址', type: String, default: '' },
    interval: { label: '轮询时间(毫秒)', type: Number, default: 0 },
    // ransackStr: { label: 'ransack', type: String, default: '' },
    ...dataPropsFn({ group: '列表静态数据', modelKeyPrefix: 'data' }, 'props', 'json'),
    ...dataPropsFn({ group: 'params', modelKeyPrefix: 'params' }, 'props', 'json'),
    ...dataPropsFn({ group: '表单', modelKeyPrefix: 'template' }, 'props', 'json'),
    mode: {
      label: '显示模式',
      type: String,
      // default: 'list',
      default: 'seamlessScrollTable',
      formType: 'radio',
      select: [
        { label: '列表', value: 'list' },
        { label: '表格', value: 'table' },
        { label: '滚动表格', value: 'seamlessScrollTable' },
      ],
    },
    skin: {
      label: '皮肤',
      formType: 'radio',
      type: String,
      select: [
        { label: '无', value: '' },
        { label: '斑马纹-1', value: 'zebra' },
        { label: '斑马纹-2', value: 'zebra-2' },
        { label: '纯色', value: 'pure' },
      ],
      default: '',
    },
    hideHeader: { label: '隐藏头部', type: Boolean, default: true, formType: 'switch' },
    // isTable: { label: '启用滚动表格', type: Boolean, default: false, formType: 'switch' },
    templateStr: { label: '表单静态值', type: String, default: undefined },
    onShowJsonata: {
      label: '详情数据处理(jsonata)，`$$` 取全局数据',
      type: String,
      default: '',
      formType: 'textarea',
    },
    tableConfig: { label: '表格配置', type: Object, default: {}, formType: 'json' },
    listConfig: { label: '列表配置', type: Object, default: {}, formType: 'json' },
    paginationConfig: { label: '分页配置', type: Object, default: {}, formType: 'json' },
  },
  setup(props: any) {
    const { getScreenData, dataResult } = useScreenDataFetchCollectionInject(props);
    const { store } = useConfigurableApi(props);

    const config = computed(() => {
      const paramsResult = getScreenData(props, 'params');
      const params =
        typeof paramsResult == 'object' && !Array.isArray(paramsResult) ? paramsResult : {};

      const templateResult = getScreenData(props, 'template');

      return {
        store,
        mode: props.isTable || props.mode == 'seamlessScrollTable' ? 'table' : props.mode,
        interval: props.interval,
        template:
          typeof templateResult == 'string' && templateResult ? templateResult : props.templateStr,
        pagination: { showPageSizeChanger: false, ...props.paginationConfig },
        table: {
          seamlessScroll: props.mode == 'seamlessScrollTable',
          skin: props.skin,
          scroll: props.mode == 'seamlessScrollTable' ? {} : { y: 'auto' },
          ...props.tableConfig,
        },
        list: props.listConfig,
        params,
      };
    });

    const defaultApiIndexFn = store.api.index;
    const taIndexView = ref<any>(null);

    watch(
      () => getScreenData(props, 'data'),
      (newValue: any) => {
        if (props['data:dataKey'] || props['data:dataStatic']) {
          if (newValue && Array.isArray(newValue)) {
            store.api.index = (params?: VObject) => {
              return new Promise<any>(resolve => {
                const [page, perPage] = [params?.page, params?.per_page];

                const records = [...newValue].splice((page - 1) * perPage, perPage);

                resolve({
                  data: {
                    current_page: page,
                    total_pages: Math.ceil(newValue.length / perPage),
                    total_count: newValue.length,
                    records,
                  },
                });
              });
            };

            taIndexView.value?.refresh?.();
          }
        } else {
          store.api.index = defaultApiIndexFn;
        }
      },
      {
        immediate: true,
      },
    );

    const { onClick: onPreviewClick } = usePreviewClickable(props);

    const onClick = (item: VObject) => {
      props.componentSchema?.events?.forEach((event: TaBuilderEvent) => {
        onPreviewClick(event, { ...item, item }, item);
      });
    };

    const onShow = (record: VObject) => {
      const item = props.onShowJsonata
        ? jsonataGet(
            // 不能用 merge， 防止 vue route bug 会导致死循环
            { ...(record.rawData || record), $$: dataResult?.value },
            props.onShowJsonata,
          )
        : record;

      onClick(item);
    };

    return {
      ...toRefs(props),
      config,
      onShow,
      taIndexView,
    };
  },
});
export default TaScreenTaIndexView;
</script>

<template lang="pug">
.ta-screen-ta-index-view.w-full.h-full
  TaIndexView.h-full.w-full(
    ref='taIndexView',
    :config='config',
    :class='{ "hidden-header": hideHeader }',
    @onShow='onShow',
    @click.stop=''
  )
    template(#card='{ record }')
      slot(name='card', :record='record')

    template(#header, v-if='hideHeader')
      .empty
</template>

<style lang="stylus" scoped>
.ta-screen-ta-index-view
  >>> *:hover
    text-shadow none !important
.hidden-header
  >>> .ta-index-view-header
    display none
  >>> .ta-index-table-action-column
    display none
</style>
