<script lang="ts">
import { ref, defineComponent, toRefs, computed } from 'vue';
import { defineTaBuilderCellComponent } from '@/components/global/ta-component/TaBuilder/builder/defineTaBuilderCellComponent';
import echarts from 'echarts';
import {
  FontPropsFn,
  axisPropsFn,
  axisMarginPropsFn,
  barSeriesFn,
  legendPropsFn,
  lineSeriesFn,
  axisLabelFormatter,
} from './echartBaseProps';
import { useScreenDataFetchCollectionInject } from '@/components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';
import { VObject } from '@/lib/vails';
import utils from '@/components/global/ta-component/utils';

const TaScreen3DBarChart = defineTaBuilderCellComponent({
  name: 'TaScreen3DBarChart',
  components: {},
  props: {
    ['title:show']: {
      type: Boolean,
      formType: 'switch',
      label: '显示',
      default: true,
      group: '标题设置',
    },
    ...FontPropsFn({ group: '标题设置', modelKeyPrefix: 'title' }),
    ...FontPropsFn({ group: '副标题设置', modelKeyPrefix: 'subTitle' }),
    ...axisPropsFn({ group: 'X轴设置', modelKeyPrefix: 'xAxis' }),
    ...axisPropsFn({ group: 'Y轴设置', modelKeyPrefix: 'yAxis' }),
    ['number:off']: {
      type: Boolean,
      formType: 'switch',
      label: '隐藏',
      default: true,
      group: '数值设置',
    },
    ...FontPropsFn({ group: '数值设置', modelKeyPrefix: 'number' }),
    ...FontPropsFn({ group: '提示语设置', modelKeyPrefix: 'notice' }),
    ...axisMarginPropsFn({ group: '坐标轴边距设置', modelKeyPrefix: 'margin' }),
    stack: {
      type: Boolean,
      formType: 'switch',
      label: '并列',
      default: false,
      group: '样式设置',
    },
    ...barSeriesFn({ group: '数据条', modelKeyPrefix: 'bar' }),
    ...lineSeriesFn({ group: '数据线', modelKeyPrefix: 'line' }),
    ...legendPropsFn({ group: '图例', modelKeyPrefix: 'legend' }),
  },
  setup(props: any, { emit }: any) {
    const { getScreenData } = useScreenDataFetchCollectionInject(props);

    const options = computed(() => {
      // console.log(props, 'props123');
      const xAxisData = getScreenData(props, 'xAxis');

      return {
        grid: {
          left: `${props['margin:left']}%`,
          top: `${props['margin:top']}%`,
          right: `${props['margin:right']}%`,
          bottom: `${props['margin:bottom']}%`,
        },
        color: [],
        title: {
          text: props['title:text'],
          show: props['title:show'],
          textStyle: {
            color: props['title:fontColor'],
            fontSize: props['title:fontSize'],
            fontWeight: props['title:fontWeight'],
          },
          subtext: props['subTitle:text'],
          subtextStyle: {
            color: props['subTitle:fontColor'],
            fontSize: props['subTitle:fontSize'],
            fontWeight: props['subTitle:fontWeight'],
          },
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b} : {c}',
        },
        legend: {
          right: `${props['legend:right']}%`,
          top: `${props['legend:top']}%`,
          left: `${props['legend:left']}%`,
          bottom: `${props['legend:bottom']}%`,
          orient: props['legend:orient'],
          textStyle: {
            color: '#fff',
          },
        },
        xAxis: {
          type: props['xAxis:type'] || 'category',
          data: xAxisData && !utils.isEmpty(xAxisData) ? xAxisData : [],
          show: props['xAxis:show'],
          name: props['xAxis:name'],
          nameTextStyle: {
            color: props['xAxis:nameFontColor'],
            fontSize: props['xAxis:nameFontSize'],
            fontWeight: props['xAxis:nameFontWeight'],
          },
          inverse: props['xAxis:reversal'],
          axisLabel: {
            show: true,
            interval: props['xAxis:textInterval'], // 文字间隔
            rotate: props['xAxis:textAngle'], // 文字角度
            textStyle: {
              color: props['xAxis:fontColor'], // x轴 坐标文字颜色
              fontSize: props['xAxis:fontSize'],
            },
            formatter: axisLabelFormatter(props['xAxis:textMaxCount']),
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: props['xAxis:lineColor'],
            },
          },
          splitLine: {
            show: props['xAxis:isShowSplitLine'],
            lineStyle: {
              color: props['xAxis:splitLineColor'],
            },
          },
        },
        yAxis: {
          // type: props['xAxis:type'] || 'value',
          show: props['yAxis:show'],
          name: props['yAxis:name'],
          nameTextStyle: {
            color: props['yAxis:nameFontColor'],
            fontSize: props['yAxis:nameFontSize'],
            fontWeight: props['yAxis:nameFontWeight'],
          },
          inverse: props['yAxis:reversal'],
          axisLabel: {
            show: true,
            interval: props['yAxis:textInterval'], // 文字间隔
            rotate: props['yAxis:textAngle'], // 文字角度
            textStyle: {
              color: props['yAxis:fontColor'], // x轴 坐标文字颜色
              fontSize: props['yAxis:fontSize'],
            },
            formatter: axisLabelFormatter(props['yAxis:textMaxCount']),
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: props['yAxis:lineColor'],
            },
          },
          splitLine: {
            show: props['yAxis:isShowSplitLine'],
            lineStyle: {
              color: props['yAxis:splitLineColor'],
            },
          },
          splitNumber: props['yAxis:splitNumber'],
        },
        series: [
          ...(props['line:series'] || []).map((lineProps: VObject) => {
            // console.log(getScreenData(lineProps, ''), 'lineProps');
            return {
              data: getScreenData(lineProps, ''),
              type: 'line',
              symbol: 'circle',
              color: lineProps.lineColor || '#34d399',
              symbolSize: 10,
              zlevel: 99, //层级
              lineStyle: {
                color: lineProps.lineColor || '#34d399',
              },
            };
          }),

          ...(props['bar:series'] || [])
            .map((barProps: VObject) => {
              return [
                {
                  //最下的圆片
                  name: '',
                  type: 'pictorialBar',
                  symbolSize: [52, 12],
                  symbolOffset: [0, 5],
                  z: 12,
                  tooltip: {
                    show: false,
                  },
                  formatter: (val: VObject) => (val.value === 0 ? 0 : val.value),
                  itemStyle: {
                    opacity: 1,
                    color: (params: any) => {
                      return new echarts.graphic.LinearGradient(
                        0,
                        0,
                        0,
                        1,
                        [
                          {
                            offset: 0,
                            color: '#1D5FB0', // 0% 处的颜色
                          },
                          {
                            offset: 0.7,
                            color: '#2066B2', // 70% 处的颜色
                          },
                        ],
                        false,
                      );
                    },
                  },
                  symbolPosition: 'start',
                  data: getScreenData(barProps, ''),
                },
                {
                  //下半截柱状图
                  type: 'bar',
                  barWidth: 52,
                  barGap: '-100%',
                  barCategoryGap: 54.5,
                  z: 10,
                  // label: {
                  //   show: true,
                  //   formatter: (val: any) => {
                  //     return val.name;
                  //   },
                  //   distance: 12,
                  //   color: '#fff',
                  //   position: 'top',
                  // },
                  tooltip: {
                    show: false,
                  },
                  itemStyle: {
                    opacity: 0.7,
                    color: (params: any) => {
                      return new echarts.graphic.LinearGradient(
                        0,
                        0,
                        0,
                        1,
                        [
                          {
                            offset: 0,
                            color: '#1D5FB0', // 0% 处的颜色
                          },

                          {
                            offset: 1,
                            color: '#216DC4', // 0% 处的颜色
                          },
                        ],
                        false,
                      );
                    },
                  },

                  data: getScreenData(barProps, ''),
                },
                {
                  //最上面圆
                  type: 'pictorialBar',
                  symbolSize: [52, 12],
                  symbolOffset: [0, -5],
                  z: 12,
                  tooltip: {
                    show: false,
                  },
                  itemStyle: {
                    opacity: 1,
                    color: (params: any) => {
                      return new echarts.graphic.LinearGradient(
                        0,
                        0,
                        0,
                        1,
                        [
                          {
                            offset: 0.3,
                            color: '#14BEE8', // 0% 处的颜色
                          },
                          {
                            offset: 0.7,
                            color: '#2066B2', // 100% 处的颜色
                          },
                        ],
                        false,
                      );
                    },
                  },
                  symbolPosition: 'end',
                  data: getScreenData(barProps, ''),
                },
              ];
            })
            .reduce((a: VObject[], b: VObject[]) => a.concat(b), []),
          {
            //底部平行四边形
            type: 'pictorialBar',
            symbolSize: ['700%', 78],
            symbolOffset: [18, 40],
            symbol:
              'image://https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/tech/%E7%AB%8B%E4%BD%93%E6%9F%B1%E7%8A%B6%E5%9B%BE%E5%9C%B0%E6%9D%BF.png',
            z: 12,
            tooltip: {
              show: false,
            },
            symbolPosition: 'start',
            data: [0, 0, 100, 0, 0],
          },
          {
            //底部平行四边形
            type: 'pictorialBar',
            symbolSize: ['700%', '500%'],
            symbolOffset: [18, 40],
            symbol:
              'image://https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/tech/%E8%83%8C%E6%99%AF%E7%BA%BF.png',
            z: 0,
            tooltip: {
              show: false,
            },
            symbolPosition: 'start',
            data: [0, 0, 100, 0, 0],
          },
        ],
      };
    });

    const onPop = (key: string) => {
      emit('pop', key);
    };

    return {
      ...toRefs(props),
      options,
      onPop,
    };
  },
});
export default TaScreen3DBarChart;
</script>

<template lang="pug">
.ta-screen-3d-bar-chart.w-full.h-full
  TaScreenEchartBase(:options='options', @pop='onPop')
</template>

<style lang="stylus" scoped></style>
