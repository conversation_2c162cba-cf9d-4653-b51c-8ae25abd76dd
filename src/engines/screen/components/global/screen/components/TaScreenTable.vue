<script lang="ts">
import { defineTaBuilderCellComponent } from '@/components/global/ta-component/TaBuilder/builder/defineTaBuilderCellComponent';
import { usePreviewClickable } from '@/components/global/ta-component/TaBuilder/builder/usePreview';
import { useScreenDataFetchCollectionInject } from '@/components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';
import { TaBuilderEvent } from '@/components/global/ta-component/TaBuilder/types';
import { VObject } from '@/lib/vails';
import { get } from 'lodash';
import { computed, toRefs } from 'vue';
import { ScreenTableColumnInterface } from './TaScreenScrollTable.vue';
import { dataPropsFn } from './echartBaseProps';

const TaScreenTable = defineTaBuilderCellComponent({
  name: 'TaScreenTable',
  components: {},
  props: {
    columns: { type: Array, label: 'columns', formType: 'json', default: [] },
    dataSource: { type: Array, label: 'dataSource', formType: 'json', default: [] },
    // options: { type: Object, label: 'options', formType: 'json', default: {} },
    ...dataPropsFn({ group: '', modelKeyPrefix: '' }, 'props', 'json'),
    ellipsis: {
      label: '文本省略',
      formType: 'switch',
      type: Boolean,
      default: false,
    },
    skin: {
      label: '皮肤',
      formType: 'radio',
      type: String,
      select: [
        { label: '无', value: '' },
        { label: '斑马纹-1', value: 'zebra' },
        { label: '斑马纹-2', value: 'zebra-2' },
        { label: '纯色', value: 'pure' },
      ],
      default: '',
    },
  },
  setup(props: any) {
    const { getScreenData } = useScreenDataFetchCollectionInject(props);
    const value = computed(
      () =>
        (Array.isArray(getScreenData(props, '')) ? getScreenData(props, '') : null) ||
        (Array.isArray(props.dataSource) ? props.dataSource : []),
    );

    const { onClick: onPreviewClick } = usePreviewClickable(props);

    const onClick = (item: VObject) => {
      props.componentSchema?.events?.forEach((event: TaBuilderEvent) => {
        onPreviewClick(event, { ...item, item }, item);
      });
    };

    // const customRow = (record: VObject) => {
    //   return {
    //     onClick: () => {
    //       onClick(record);
    //     }, // click row
    //   };
    // };

    const getData = (column: ScreenTableColumnInterface, item: VObject) => {
      return typeof get(item, column.dataIndex) === 'undefined' ? '-' : get(item, column.dataIndex);
    };

    return {
      ...toRefs(props),
      value,
      onClick,
      getData,
    };
  },
});
export default TaScreenTable;
</script>

<template lang="pug">
.ta-screen-table.w-full.h-full.overflow-y-auto
  table.w-full(:style='{ "table-layout": ellipsis ? "fixed" : "auto" }', :class='skin')
    colgroup
      col(v-for='column in columns', :style='{ width: column.width }')
    thead.sticky.top-0
      tr
        th.text-center.border-b.border-white.border-solid(v-for='column in columns')
          .text-white.text-3xl.text-center(:style='{ width: column.width }')
            | {{ column.title }}
    tbody
      tr.title.py-2.text-3xl.px-4.text-white.text-center.font-light(
        v-for='(item, index) in value',
        :key='index',
        :style='{ background: item.background }',
        @click.stop='() => onClick(item)'
      )
        td.title.text-3xl.text-white.text-center.font-light(
          v-for='column in columns',
          :style='{ width: column.width }',
          :class='index % 2 === 0 ? "light-tr" : "dark-tr"'
        )
          .py-4.px-1.h-full.w-full.text-center
            component.h-full.w-full.text-center(
              :is='column.component || "div"',
              :item='item',
              :column='column',
              :value='getData(column, item)',
              :style='{ color: column.color || item.color }',
              :class='{ truncate: ellipsis }'
            )
              | {{ getData(column, item) }}
</template>

<style lang="stylus" scoped>
@import './table.styl'
</style>
