<script lang="ts">
import dayjs from 'dayjs';
import { useScreenDataFetchCollectionInject } from '@/components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';
import { defineTaBuilderCellComponent } from '@/components/global/ta-component/TaBuilder/builder/defineTaBuilderCellComponent';
import { dataPropsFn } from './echartBaseProps';
import { computed, toRefs } from 'vue';
import { VObject } from '@/lib/vails';
import { TaBuilderEvent } from '@/components/global/ta-component/TaBuilder/types';
import { usePreviewClickable } from '@/components/global/ta-component/TaBuilder/builder/usePreview';

const TaScreenCommentCard = defineTaBuilderCellComponent({
  name: 'TaScreenCommentCard',
  components: {},
  props: {
    ...dataPropsFn({ group: '', modelKeyPrefix: '' }, 'props', 'json'),
    data: {
      type: Array,
      hidden: true,
      default: [
        { date: '2022-11-03', name: '滨江区开展动员部署会。' },
        { date: '2022-11-05', name: '滨江区就酒驾醉驾问题开展。' },
        {
          date: '2022-11-07',
          name: '滨江区就酒驾醉驾问题开展。',
        },
        {
          date: '2022-11-08',
          name: '滨江区就酒驾醉驾问题开展。滨江区就酒驾醉驾问题开展。滨江区就酒驾醉驾问题开展。',
        },
        {
          date: '2022-11-05',
          name: '滨江区就酒驾醉驾问题开展。滨江区就酒驾醉驾问题开展。滨江区就酒驾醉驾问题开展。',
        },
      ],
    },
    step: { type: Number, default: 0.8 },
    limitScrollNum: { typr: Number, default: 3 },
  },
  setup(props: any) {
    const { getScreenData } = useScreenDataFetchCollectionInject(props);
    const data_array = computed(() => getScreenData(props, '') || props.data);

    const formatTime = (str: string) => {
      return dayjs(str).format('MM/DD/YYYY');
    };
    const { onClick: onPreviewClick } = usePreviewClickable(props);

    const onClick = (item: VObject) => {
      props.componentSchema?.events?.forEach((event: TaBuilderEvent) => {
        onPreviewClick(event, { ...item, item }, item);
      });
    };
    return {
      ...toRefs(props),
      data_array,
      formatTime,
      onClick,
    };
  },
});

export default TaScreenCommentCard;
</script>

<template lang="pug">
TaSeamlessScroll.overflow-hidden(:step='step', @click.stop='')
  .ta-screen-comment-card.flex.pb-4(v-for='record in data', @click.stop='() => onClick(record)')
    .left
      .relative.bg-blue-500
        .out_circle
        .inner_circle
    .right
      .items-center.text-xs
        .time.min-w-25 {{ formatTime(record.date) }}
        .name {{ record.name }}
</template>

<style lang="stylus" scoped>
.ta-screen-comment-card
  margin-top 5px
  margin-bottom 13px
  .left
    width 15px
    position relative
    left 20px
    .out_circle
      position absolute
      width 24px
      height 24px
      left -9px
      top 0px
      background rgba(2, 32, 128, 0.01)
      border 1px solid rgba(111, 199, 251, 0.8)
      box-shadow inset 0px 0px 26px 5px #022080
      border-radius 12px
    .inner_circle
      position absolute
      width 20px
      height 20px
      left -7px
      top 2px
      background rgba(2, 32, 128, 0.01)
      border 1px solid rgba(111, 199, 251, 0.8)
      box-shadow inset 0px 0px 26px 5px #022080
      border-radius 10px
    &::after
      content ''
      display inline-block
      position relative
      top 23px
      left 3px
      width 1px
      height 90%
      background linear-gradient(180deg, #93C5FD 0%, rgba(147, 197, 253, 0.4) 100%)
      z-index 1
  .right
    width calc(100% - 20px)
    padding-left 32px
    .time
      font-family 'Inter'
      font-style normal
      font-weight 300
      font-size 24px
      line-height 150%
      color #FFFFFF
    .name
      font-family 'Inter'
      font-style normal
      font-weight 400
      font-size 30px
      line-height 150%
      color #FFFFFF
</style>
