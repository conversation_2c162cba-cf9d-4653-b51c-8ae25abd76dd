<script lang="ts">
import { defineTaBuilderCellComponent } from '@/components/global/ta-component/TaBuilder/builder/defineTaBuilderCellComponent';
import { useScreenDataFetchCollectionInject } from '@/components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';
import utils from '@/components/global/ta-component/utils';
import { VObject } from '@/lib/vails';
import echarts from 'echarts';
import { computed, toRefs, watch } from 'vue';
import {
  axisMarginPropsFn,
  axisPropsFn,
  FontPropsFn,
  legendPropsFn,
  lineSeriesFn,
  barSeriesFn,
  axisLabelFormatter,
} from './echartBaseProps';

const TaScreenLineChart = defineTaBuilderCellComponent({
  name: 'TaScreenLineChart',
  components: {},
  props: {
    markPoint: {
      label: '标记点',
      type: Boolean,
      formType: 'switch',
      default: true,
      group: '折线设置',
    },
    smoothCurve: {
      formType: 'switch',
      type: Boolean,
      label: '平滑曲线',
      default: true,
      group: '折线设置',
    },
    // pointSize: {
    //   formType: 'slider',
    //   type: Number,
    //   label: '点大小',
    //   default: 10,
    //   group: '折线设置',
    // },
    // area: {
    //   formType: 'switch',
    //   type: Boolean,
    //   label: '面积堆积',
    //   default: true,
    //   group: '折线设置',
    // },
    // areaThickness: {
    //   formType: 'slider',
    //   type: Number,
    //   label: '面积厚度',
    //   default: 5,
    //   group: '折线设置',
    // },
    lineWidth: {
      formType: 'slider',
      type: Number,
      label: '线条宽度',
      default: 4,
      group: '折线设置',
    },
    areaStyleShow: {
      formType: 'switch',
      type: Boolean,
      label: '是否显示区域面积图',
      default: true,
      group: '折线设置',
    },
    opacity: {
      formType: 'slider',
      type: Number,
      label: '图形透明度',
      default: 80,
      group: '折线设置',
    },
    shadowBlur: {
      formType: 'slider',
      type: Number,
      label: '图形阴影的模糊大小',
      default: 10,
      group: '折线设置',
    },
    shadowColor: {
      formType: 'color',
      type: String,
      label: '阴影颜色',
      default: '#fff',
      group: '折线设置',
    },

    ['title:show']: {
      type: Boolean,
      formType: 'switch',
      label: '显示',
      default: true,
      group: '标题设置',
    },
    ...FontPropsFn({ group: '标题设置', modelKeyPrefix: 'title' }),
    ...FontPropsFn({ group: '副标题设置', modelKeyPrefix: 'subTitle' }),
    ...axisPropsFn({ group: 'X轴设置', modelKeyPrefix: 'xAxis' }),
    ...axisPropsFn({ group: 'Y轴设置', modelKeyPrefix: 'yAxis' }),
    ...axisPropsFn({ group: 'Y2轴设置', modelKeyPrefix: 'yAxis2' }),
    ['number:off']: {
      type: Boolean,
      formType: 'switch',
      label: '隐藏',
      default: true,
      group: '数值设置',
    },
    ...FontPropsFn({ group: '数值设置', modelKeyPrefix: 'number' }),
    ...FontPropsFn({ group: '提示语设置', modelKeyPrefix: 'notice' }),
    ...axisMarginPropsFn({ group: '坐标轴边距设置', modelKeyPrefix: 'margin' }),
    ...lineSeriesFn({ group: '数据线', modelKeyPrefix: '' }),
    ...barSeriesFn({ group: '数据条', modelKeyPrefix: 'bar' }),
    ...legendPropsFn({ group: '图例', modelKeyPrefix: 'legend' }),
  },
  setup(props: any, { emit }: any) {
    const { getScreenData } = useScreenDataFetchCollectionInject(props);

    const options = computed(() => {
      console.log('xAxis: ', props.xAxis);
      const xAxisData = getScreenData(props, 'xAxis');
      const yAxisData = getScreenData(props, 'yAxis');

      return {
        grid: {
          left: `${props['margin:left']}%`,
          top: `${props['margin:top']}%`,
          right: `${props['margin:right']}%`,
          bottom: `${props['margin:bottom']}%`,
        },
        color: [],
        title: {
          text: props['title:text'],
          show: props['title:show'],
          textStyle: {
            color: props['title:fontColor'],
            fontSize: props['title:fontSize'],
            fontWeight: props['title:fontWeight'],
          },
          subtext: props['subTitle:text'],
          subtextStyle: {
            color: props['subTitle:fontColor'],
            fontSize: props['subTitle:fontSize'],
            fontWeight: props['subTitle:fontWeight'],
          },
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b} : {c}',
          textStyle: {
            color: props['notice:fontColor'],
            fontSize: props['notice:fontSize'],
            fontWeight: props['notice:fontWeight'],
          },
        },
        legend: {
          right: `${props['legend:right']}%`,
          top: `${props['legend:top']}%`,
          left: `${props['legend:left']}%`,
          bottom: `${props['legend:bottom']}%`,
          orient: props['legend:orient'],
          textStyle: {
            color: props['legend:fontColor'] || props['legend:color'] || '#fff',
            fontSize: props['legend:fontSize'],
            fontWeight: props['legend:fontWeight'],
          },
        },
        xAxis: {
          scale: true,
          type: props['xAxis:type'] || 'category',
          data: xAxisData && !utils.isEmpty(xAxisData) ? xAxisData : [],
          show: props['xAxis:show'],
          name: props['xAxis:name'],
          nameTextStyle: {
            color: props['xAxis:nameFontColor'],
            fontSize: props['xAxis:nameFontSize'],
            fontWeight: props['xAxis:nameFontWeight'],
          },
          inverse: props['xAxis:reversal'],
          axisLabel: {
            show: true,
            interval: props['xAxis:textInterval'], // 文字间隔
            rotate: props['xAxis:textAngle'], // 文字角度
            textStyle: {
              color: props['xAxis:fontColor'], // x轴 坐标文字颜色
              fontSize: props['xAxis:fontSize'],
            },
            formatter: axisLabelFormatter(props['xAxis:textMaxCount']),
            margin: props['xAxis:margin'],
          },
          axisLine: {
            show: true,
            lineStyle: {
              color: props['xAxis:lineColor'],
            },
          },
          splitLine: {
            show: props['xAxis:isShowSplitLine'],
            lineStyle: {
              color: props['xAxis:splitLineColor'],
            },
          },
        },
        yAxis: [
          {
            scale: true,

            type: props['yAxis:type'] || 'value',
            data: yAxisData && !utils.isEmpty(yAxisData) ? yAxisData : [],
            show: props['yAxis:show'],
            name: props['yAxis:name'],
            nameTextStyle: {
              color: props['yAxis:nameFontColor'],
              fontSize: props['yAxis:nameFontSize'],
              fontWeight: props['yAxis:nameFontWeight'],
            },
            inverse: props['yAxis:reversal'],
            axisLabel: {
              show: true,
              interval: props['yAxis:textInterval'], // 文字间隔
              rotate: props['yAxis:textAngle'], // 文字角度
              textStyle: {
                color: props['yAxis:fontColor'], // x轴 坐标文字颜色
                fontSize: props['yAxis:fontSize'],
              },
              margin: props['yAxis:margin'],
              formatter: axisLabelFormatter(props['yAxis:textMaxCount']),
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: props['yAxis:lineColor'],
              },
            },
            splitLine: {
              show: props['yAxis:isShowSplitLine'],
              lineStyle: {
                color: props['yAxis:splitLineColor'],
              },
            },
            splitNumber: props['yAxis:splitNumber'],
          },

          {
            scale: true,
            type: props['yAxis2:type'] || 'value',
            show: props['yAxis2:show'],
            name: props['yAxis2:name'],
            nameTextStyle: {
              color: props['yAxis2:nameFontColor'],
              fontSize: props['yAxis2:nameFontSize'],
              fontWeight: props['yAxis2:nameFontWeight'],
            },
            inverse: props['yAxis2:reversal'],
            axisLabel: {
              show: true,
              interval: props['yAxis2:textInterval'], // 文字间隔
              rotate: props['yAxis2:textAngle'], // 文字角度
              textStyle: {
                color: props['yAxis2:fontColor'], // x轴 坐标文字颜色
                fontSize: props['yAxis2:fontSize'],
              },
              margin: props['yAxis2:margin'],
              formatter: axisLabelFormatter(props['yAxis2:textMaxCount']),
            },
            axisLine: {
              show: true,
              lineStyle: {
                color: props['yAxis2:lineColor'],
              },
            },
            splitLine: {
              show: props['yAxis2:isShowSplitLine'],
              lineStyle: {
                color: props['yAxis2:splitLineColor'],
              },
            },
            splitNumber: props['yAxis2:splitNumber'],
          },
        ],
        series: [
          ...(props.series || []).map((lineProps: VObject) => {
            // console.log(getScreenData(lineProps, ''), 'lineProps');
            let options = {
              data: getScreenData(lineProps, ''),
              type: 'line',
              smooth: props.smoothCurve,
              showSymbol: false,
              lineStyle: {
                width: props.lineWidth,
                color: lineProps.lineColor || props.lineColor,
              },
              name: lineProps.name,
              yAxisIndex: lineProps.yAxisIndex,
              emphasis: {
                focus: 'series',
              },
            };
            if (props.areaStyleShow) {
              options = Object.assign(options, {
                areaStyle: {
                  opacity: props.opacity / 100,
                  shadowBlur: props.shadowBlur,
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: props.shadowColor || 'rgb(128, 255, 165)',
                    },
                    {
                      offset: 1,
                      color: 'rgba(0,0,0,0)',
                    },
                  ]),
                },
              });
            }
            return options;
          }),
          ...(props['bar:series'] || []).map((barProps: VObject, index: number) => {
            // console.log(getScreenData(barProps, ''), 'barProps');

            return {
              name: barProps.name,
              type: 'bar',
              data: getScreenData(barProps, ''),
              // color: barProps.color,
              barWidth: `${barProps.width}%`,
              stack: props.stack ? '并列' : index,
              label: {
                show: barProps.label,
                position: barProps.labelPosition,
                color: barProps.labelColor,
                fontSize: barProps.labelFontSize,
              },
              formatter: (val: VObject) => (val.value === 0 ? 0 : val.value),
              itemStyle: {
                normal: {
                  barBorderRadius: barProps.barBorderRadius || [30, 30, 0, 0],
                  color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: barProps.color,
                    },
                    {
                      offset: 1,
                      color: barProps.gradient_color || 'rgba(255, 255, 255, 0)',
                    },
                  ]),
                },
              },
            };
          }),
        ],
      };
    });

    const onPop = (key: string) => {
      emit('pop', key);
    };

    // console.log('options: ', options.value);
    return {
      ...toRefs(props),
      options,
      onPop,
    };
  },
});
export default TaScreenLineChart;
</script>

<template lang="pug">
.ta-screen-line-chart.w-full.h-full
  TaScreenEchartBase(:options='options', @pop='onPop')
</template>

<style lang="stylus" scoped></style>
