<script lang="ts">
import { toRefs, computed } from 'vue';
import { useScreenDataFetchCollectionInject } from '@/components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';
import { defineTaBuilderCellComponent } from '@/components/global/ta-component/TaBuilder/builder/defineTaBuilderCellComponent';
import { dataPropsFn } from './echartBaseProps';
import dayjs from 'dayjs';

const TaScreenTimeLine = defineTaBuilderCellComponent({
  name: 'TaScreenTimeLine',
  components: {},
  props: {
    ...dataPropsFn({ group: '', modelKeyPrefix: '' }, 'props', 'json'),
    data: {
      type: Array,
      hidden: true,
      default: [
        {
          time: '2022-11-03',
          name: '滨江区开展动员部署会。',
          custom_dot: true,
          color: 'blue',
        },
        { time: '2022-11-05', name: '滨江区就酒驾醉驾问题开展。', custom_dot: true, color: 'blue' },
        { time: '2022-11-05', name: '滨江区就酒驾醉驾问题开展。', custom_dot: true },
      ],
    },
  },
  setup(props: any) {
    const { getScreenData } = useScreenDataFetchCollectionInject(props);
    const data_array = computed(() => getScreenData(props, '') || props.data);
    const timeStr = (str: string) => {
      return dayjs(str).format('MM/DD/YYYY');
    };
    return {
      ...toRefs(props),
      data_array,
      timeStr,
    };
  },
});
export default TaScreenTimeLine;
</script>

<template lang="pug">
.ta-screen-time-line
  a-timeline(:pending='true', pendingDot=' ')
    a-timeline-item(v-for='item in data_array', :color='item?.color')
      template(v-if='item.custom_dot', #dot)
        slot(name='dot')
          //- .out_circle
          //- .inner_circle
      .time {{ timeStr(item.time) }}
      .name {{ item.name }}
</template>

<style lang="stylus" scoped>
.ta-screen-time-line
  >>>.ant-timeline-item-tail
    background-color #69B4F3
  .out_circle
    position absolute
    width 24px
    height 24px
    left -11px
    top 0px
    background rgba(2, 32, 128, 0.01)
    border 1px solid rgba(111, 199, 251, 0.8)
    box-shadow inset 0px 0px 26px 5px #022080
    border-radius 12px
  .inner_circle
    position absolute
    width 20px
    height 20px
    left -9px
    top 2px
    background rgba(2, 32, 128, 0.01)
    border 1px solid rgba(111, 199, 251, 0.8)
    box-shadow inset 0px 0px 26px 5px #022080
    border-radius 10px
  .time
    font-family 'Inter'
    font-style normal
    font-weight 300
    font-size 24px
    line-height 150%
    color #FFFFFF
  .name
    font-family 'Inter'
    font-style normal
    font-weight 400
    font-size 30px
    line-height 150%
    color #FFFFFF
</style>
