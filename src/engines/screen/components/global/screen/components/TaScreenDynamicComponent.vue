<script lang="ts">
import { ref, defineComponent, toRefs, computed } from 'vue';
import { defineTaBuilderCellComponent } from '@/components/global/ta-component/TaBuilder/builder/defineTaBuilderCellComponent';
import { dataPropsFn } from './echartBaseProps';
import { merge } from 'lodash';
import { useScreenDataFetchCollectionInject } from '@/components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';

const TaScreenDynamicComponent = defineTaBuilderCellComponent({
  name: 'TaScreenDynamicComponent',
  components: {},
  props: {
    componentName: { label: '组件名（请直接粘贴）', type: String, default: '' },
    componentParams: { label: '参数', formType: 'json', type: Object, default: {} },
    ...dataPropsFn({ group: '', modelKeyPrefix: '' }, 'props', 'json'),
  },
  setup(props: any, opts: any) {
    const { getScreenData } = useScreenDataFetchCollectionInject(props);
    const params = computed(() => {
      const screenData = getScreenData(props, '');
      return merge(
        {},
        Array.isArray(props.componentParams) || typeof props.componentParams != 'object'
          ? {}
          : props.componentParams,
        Array.isArray(screenData) || typeof screenData != 'object' ? {} : screenData,
      );
    });

    return {
      ...toRefs(props),
      params,
    };
  },
});
export default TaScreenDynamicComponent;
</script>

<template lang="pug">
component.ta-screen-dynamic-component.w-full.h-full(
  v-bind='{ ...$props, ...params }',
  :is='componentName',
)
</template>

<style lang="stylus" scoped></style>
