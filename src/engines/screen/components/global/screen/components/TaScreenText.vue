<script lang="ts">
import { ref, defineComponent, toRefs, computed } from 'vue';
import { defineTaBuilderCellComponent } from '@/components/global/ta-component/TaBuilder/builder/defineTaBuilderCellComponent';
import { dataPropsFn } from './echartBaseProps';
import { useScreenDataFetchCollectionInject } from '@/components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';

const TaScreenText = defineTaBuilderCellComponent({
  name: 'TaScreenText',
  components: {},
  props: {
    text: { label: '值', type: [String, Number], default: '' },
    // shadow: { label: '阴影', type: Boolean, formType: 'switch', default: false },
    ...dataPropsFn({ group: '', modelKeyPrefix: '' }, 'props', 'input'),
    unit: { label: '单位', type: String, default: '' },
  },
  setup(props: any) {
    const { getScreenData } = useScreenDataFetchCollectionInject(props);

    const value = computed(() => {
      const result = getScreenData(props, '');
      return result == 0 ? result : result || props.text;
    });

    return {
      ...toRefs(props),
      value,
      props,
    };
  },
});
export default TaScreenText;
</script>

<template lang="pug">
.ta-screen-text.text-3xl.font-bold.text-blue-500(
)
  | {{ value }}
  span.unit.ml-1.text-base.font-thin.text-white(v-if='unit') {{ unit }}
</template>

<style lang="stylus" scoped>
.text-shadow
   text-shadow 0 0 10px red, 0 0 20px red, 0 0 30px red, 0 0 40px red
</style>
