export const axisLabelFormatter = (maxCount: number) => {
  return (val: string) => {
    const length = maxCount || 0;
    if (!length) return val;
    const strAry = String(val).split('');
    const result = [];
    let index = 0;
    while (length && strAry.length > 0) {
      index++;
      result.push(strAry.shift());
      if (index % length === 0) result.push('\n');
    }

    return result.join('');
  };
};

const getKey = (key: string, prefix?: string) => {
  return [prefix, key].filter(i => i).join(':');
};

export const FontPropsFn = ({ group, modelKeyPrefix } = { group: '', modelKeyPrefix: '' }) => ({
  [getKey('text', modelKeyPrefix)]: {
    formType: 'textarea',
    label: '标题',
    type: String,
    default: '',
    group,
  },
  [getKey('width', modelKeyPrefix)]: {
    formType: 'input',
    label: '宽度',
    type: String,
    default: '',
    group,
  },
  [getKey('fontColor', modelKeyPrefix)]: {
    formType: 'color',
    label: '字体颜色',
    type: String,
    default: '#fff',
    group,
  },
  [getKey('fontWeight', modelKeyPrefix)]: {
    type: String,
    formType: 'select',
    label: '字体粗细',
    default: 'normal',
    select: [
      { value: 'normal', label: '正常' },
      { value: 'bold', label: '粗体' },
      { value: 'bolder', label: '特粗体' },
      { value: 'lighter', label: '细体' },
    ],
    group,
  },
  [getKey('fontSize', modelKeyPrefix)]: {
    type: Number,
    formType: 'number',
    label: '字体字号',
    default: 20,
    group,
  },
  [getKey('fontAlign', modelKeyPrefix)]: {
    type: String,
    formType: 'select',
    label: '字体位置',
    default: 'center',
    select: [
      { value: 'center', label: '居中' },
      { value: 'left', label: '左对齐' },
      { value: 'right', label: '右对齐' },
    ],
    group,
  },
});

export const axisPropsFn = ({ group, modelKeyPrefix } = { group: '', modelKeyPrefix: '' }) => ({
  [getKey('show', modelKeyPrefix)]: {
    formType: 'switch',
    label: '显示',
    type: Boolean,
    default: true,
    group,
  },
  [getKey('type', modelKeyPrefix)]: {
    formType: 'radio',
    label: '模式',
    type: String,
    select: [
      { value: 'value', label: '值' },
      { value: 'category', label: '分类' },
    ],
    group,
  },
  ...dataPropsFn({ group, modelKeyPrefix }),
  [getKey('name', modelKeyPrefix)]: {
    formType: 'textarea',
    label: '轴名',
    type: String,
    default: '',
    group,
  },
  [getKey('nameGap', modelKeyPrefix)]: {
    formType: 'number',
    label: '轴名间隔',
    type: Number,
    default: 2,
    group,
  },
  [getKey('min', modelKeyPrefix)]: {
    formType: 'input',
    label: '坐标最小值',
    type: String,
    group,
  },
  [getKey('max', modelKeyPrefix)]: {
    formType: 'input',
    label: '坐标最大值',
    type: String,
    group,
  },
  [getKey('nameColor', modelKeyPrefix)]: {
    formType: 'color',
    label: '坐标名颜色',
    type: String,
    default: '#fff',
    group,
  },
  [getKey('nameFontSize', modelKeyPrefix)]: {
    formType: 'number',
    label: '坐标字号',
    type: Number,
    default: 14,
    group,
  },
  [getKey('fontColor', modelKeyPrefix)]: {
    formType: 'color',
    label: '数值颜色',
    type: String,
    default: '#fff',
    group,
  },
  [getKey('fontSize', modelKeyPrefix)]: {
    formType: 'number',
    label: '数值字号',
    type: Number,
    default: 14,
    group,
  },
  [getKey('textMaxCount', modelKeyPrefix)]: {
    formType: 'number',
    label: '每行字数',
    type: Number,
    group,
  },
  [getKey('textAngle', modelKeyPrefix)]: {
    formType: 'slider',
    label: '数值角度',
    type: Number,
    default: 0,
    group,
  },
  [getKey('textInterval', modelKeyPrefix)]: {
    formType: 'number',
    label: '数值间隔',
    type: String,
    default: '',
    group,
  },
  [getKey('margin', modelKeyPrefix)]: {
    formType: 'number',
    label: '与轴间隔',
    type: Number,
    default: 2,
    group,
  },
  [getKey('reversal', modelKeyPrefix)]: {
    formType: 'switch',
    label: '轴反转',
    type: Boolean,
    default: false,
    group,
  },
  [getKey('lineColor', modelKeyPrefix)]: {
    formType: 'color',
    label: '轴颜色',
    type: String,
    default: '#fff',
    group,
  },
  [getKey('lineShow', modelKeyPrefix)]: {
    formType: 'switch',
    label: '轴线显示',
    type: Boolean,
    default: true,
    group,
  },
  [getKey('tickLineShow', modelKeyPrefix)]: {
    formType: 'switch',
    label: '轴刻度线显示',
    type: Boolean,
    default: true,
    group,
  },
  [getKey('isShowSplitLine', modelKeyPrefix)]: {
    formType: 'switch',
    label: '分割线显示',
    type: Boolean,
    default: false,
    group,
  },
  [getKey('splitLineColor', modelKeyPrefix)]: {
    formType: 'color',
    label: '分割线颜色',
    type: String,
    default: '#fff',
    group,
  },
  [getKey('splitNumber', modelKeyPrefix)]: {
    formType: 'number',
    label: '份数',
    type: Number,
    group,
  },
});

export const axisMarginPropsFn = (
  { group, modelKeyPrefix } = { group: '', modelKeyPrefix: '' },
) => ({
  [getKey('left', modelKeyPrefix)]: {
    formType: 'slider',
    label: '左边距（%）',
    type: Number,
    default: 0,
    group,
  },
  [getKey('top', modelKeyPrefix)]: {
    formType: 'slider',
    label: '顶边距（%）',
    type: Number,
    default: 0,
    group,
  },
  [getKey('right', modelKeyPrefix)]: {
    formType: 'slider',
    label: '右边距（%）',
    type: Number,
    default: 0,
    group,
  },
  [getKey('bottom', modelKeyPrefix)]: {
    formType: 'slider',
    label: '底边距（%）',
    type: Number,
    default: 0,
    group,
  },
});

export const axisCenterPropsFn = (
  { group, modelKeyPrefix } = { group: '', modelKeyPrefix: '' },
) => ({
  [getKey('x', modelKeyPrefix)]: {
    formType: 'slider',
    label: '左右(%)',
    type: Number,
    default: 0,
    group,
  },
  [getKey('y', modelKeyPrefix)]: {
    formType: 'slider',
    label: '上下(%)',
    type: Number,
    default: 0,
    group,
  },
  [getKey('show', modelKeyPrefix)]: {
    formType: 'switch',
    label: '显示中间字体',
    type: Boolean,
    default: false,
    group,
  },
  [getKey('fontColor', modelKeyPrefix)]: {
    formType: 'color',
    label: '字体颜色',
    type: String,
    default: '#fff',
    group,
  },
  [getKey('fontWeight', modelKeyPrefix)]: {
    type: String,
    formType: 'select',
    label: '字体粗细',
    default: 'normal',
    select: [
      { value: 'normal', label: '正常' },
      { value: 'bold', label: '粗体' },
      { value: 'bolder', label: '特粗体' },
      { value: 'lighter', label: '细体' },
    ],
    group,
  },
  [getKey('fontSize', modelKeyPrefix)]: {
    type: Number,
    formType: 'number',
    label: '字体字号',
    default: 20,
    group,
  },
});

export const dataPropsFn = (
  { group, modelKeyPrefix } = { group: '', modelKeyPrefix: '' },
  formModelKeyPrefix = 'props',
  valueFormType = 'json',
  defaultStaticValueFn?: () => any,
) => ({
  [getKey('dataSource', modelKeyPrefix)]: {
    formType: 'dataSource',
    label: '数据源',
    type: String,
    modelKeyPrefix: formModelKeyPrefix,
    group,
  },
  [getKey('dataKey', modelKeyPrefix)]: {
    label: '数据关键字, `$$` 取全局数据',
    formType: 'textarea',
    type: String,
    default: '',
    modelKeyPrefix: formModelKeyPrefix,
    group,
  },
  [getKey('dataStatic', modelKeyPrefix)]: {
    label: '数据静态值',
    formType: valueFormType,
    type: [Object, Number, String, Array],
    modelKeyPrefix: formModelKeyPrefix,
    group,
    default: defaultStaticValueFn,
  },
});

export const legendPropsFn = ({ group, modelKeyPrefix } = { group: '', modelKeyPrefix: '' }) => ({
  ...axisMarginPropsFn({ group, modelKeyPrefix }),
  [getKey('itemGap', modelKeyPrefix)]: {
    formType: 'slider',
    label: '间距',
    type: Number,
    group,
    default: 10,
  },
  [getKey('orient', modelKeyPrefix)]: {
    formType: 'radio',
    label: '朝向',
    type: String,
    group,
    default: 'horizontal',
    select: [
      { label: '水平', value: 'horizontal' },
      { label: '垂直', value: 'vertical' },
    ],
  },
  [getKey('icon', modelKeyPrefix)]: {
    formType: 'select',
    label: '标记类型',
    type: String,
    default: 'circle',
    select: [
      { label: '圆形', value: 'circle' },
      { label: '方形', value: 'rect' },
      { label: '方形圆边', value: 'roundRect' },
      { label: '三角形', value: 'triangle' },
      { label: '四边菱形', value: 'diamond' },
      { label: '箭头', value: 'arrow' },
    ],
    group,
  },
  // [getKey('color', modelKeyPrefix)]: {
  //   formType: 'color',
  //   label: '颜色',
  //   type: String,
  //   group,
  //   default: '#fff',
  // },
  ...FontPropsFn({ group, modelKeyPrefix }),
});

export const lineSeriesFn = ({ group, modelKeyPrefix } = { group: '', modelKeyPrefix: '' }) => ({
  [getKey('series', modelKeyPrefix)]: {
    formType: 'list',
    label: '数据线',
    type: Array,
    group,
    subProps: {
      ...dataPropsFn({ group: '', modelKeyPrefix: '' }, ''),
      name: {
        formType: 'input',
        label: '名称',
        type: String,
        default: '',
        modelKeyPrefix: '',
      },
      yAxisIndex: {
        formType: 'number',
        label: 'Y轴index',
        type: Number,
        default: 0,
        modelKeyPrefix: '',
      },
      lineColor: {
        formType: 'color',
        label: '线条颜色',
        type: String,
        default: '#000',
        modelKeyPrefix: '',
      },
      areaColor: {
        formType: 'color',
        label: '面积颜色',
        type: String,
        default: '#000',
        modelKeyPrefix: '',
      },
    },
  },
});

export const pieSeriesFn = ({ group, modelKeyPrefix } = { group: '', modelKeyPrefix: '' }) => ({
  [getKey('series', modelKeyPrefix)]: {
    formType: 'list',
    label: '数据饼',
    type: Array,
    group,
    subProps: {
      ...dataPropsFn({ group: '', modelKeyPrefix: '' }, '', 'number'),
      color: {
        formType: 'color',
        label: '饼颜色',
        type: String,
        default: '#000',
        modelKeyPrefix: '',
      },
      name: {
        label: '名称',
        type: String,
        default: '',
        modelKeyPrefix: '',
      },
    },
  },
});

export const barSeriesFn = ({ group, modelKeyPrefix } = { group: '', modelKeyPrefix: '' }) => ({
  [getKey('series', modelKeyPrefix)]: {
    formType: 'list',
    label: '数据条',
    type: Array,
    group,
    subProps: {
      ...dataPropsFn({ group: '', modelKeyPrefix: '' }, '', 'json'),
      yAxisIndex: {
        formType: 'number',
        label: 'Y轴index',
        type: Number,
        default: 0,
        modelKeyPrefix: '',
      },
      barBorderRadius: {
        formType: 'json',
        label: '条形的圆角',
        type: String,
        default: [0, 0, 0, 0],
        modelKeyPrefix: '',
      },
      gradient_color_direction: {
        formType: 'switch',
        label: '渐变条颜色方向',
        type: Boolean,
        default: false,
        modelKeyPrefix: '',
      },
      color: {
        formType: 'color',
        label: '渐变条颜色顶色',
        type: String,
        default: '#000',
        modelKeyPrefix: '',
      },
      gradient_color: {
        formType: 'color',
        label: '渐变条颜色底色',
        type: String,
        default: 'rgba(255, 255, 255, 0)',
        modelKeyPrefix: '',
      },
      name: {
        label: '名称',
        type: String,
        default: '',
        modelKeyPrefix: '',
      },
      width: {
        formType: 'slider',
        label: '宽度',
        type: String,
        default: 10,
        modelKeyPrefix: '',
      },
      gap: {
        formType: 'slider',
        label: '柱间距离',
        type: String,
        default: 0,
        modelKeyPrefix: '',
      },
      label: {
        formType: 'switch',
        label: '显示标签',
        type: Boolean,
        default: 0,
        modelKeyPrefix: '',
      },
      labelPosition: {
        formType: 'input',
        label: '标签位置',
        type: Boolean,
        default: 0,
        modelKeyPrefix: '',
      },
      labelColor: {
        formType: 'color',
        label: '标签颜色',
        type: String,
        default: 0,
        modelKeyPrefix: '',
      },
      labelFontSize: {
        formType: 'number',
        label: '标签字体大小',
        type: Number,
        default: 0,
        modelKeyPrefix: '',
      },
      labelOffsetY: {
        formType: 'number',
        label: '标签纵向offset',
        type: Number,
        default: 0,
        modelKeyPrefix: '',
      },
    },
  },
});

export const sankeyDataProps = ({ group, modelKeyPrefix } = { group: '', modelKeyPrefix: '' }) => ({
  [getKey('data', modelKeyPrefix)]: {
    formType: 'list',
    label: '数据点',
    type: Array,
    group,
    subProps: {
      name: {
        formType: 'input',
        label: '名称',
        type: String,
        default: '',
        modelKeyPrefix: '',
      },
      color: {
        formType: 'color',
        label: '颜色',
        type: String,
        default: '#000',
        modelKeyPrefix: '',
      },
    },
  },
});

export const sankeyLinksProps = (
  { group, modelKeyPrefix } = { group: '', modelKeyPrefix: '' },
) => ({
  [getKey('links', modelKeyPrefix)]: {
    formType: 'list',
    label: '连接线',
    type: Array,
    group,
    subProps: {
      ...dataPropsFn({ group: '', modelKeyPrefix: '' }, '', 'number'),
      source: {
        formType: 'input',
        label: '左端点',
        type: String,
        default: '',
        modelKeyPrefix: '',
      },
      target: {
        formType: 'input',
        label: '右端点',
        type: String,
        default: '',
        modelKeyPrefix: '',
      },
    },
  },
});

export const radarIndicatorsProps = (
  { group, modelKeyPrefix } = { group: '', modelKeyPrefix: '' },
) => ({
  [getKey('indicators', modelKeyPrefix)]: {
    formType: 'list',
    label: '雷达端点',
    type: Array,
    group,
    subProps: {
      ...dataPropsFn({ group: '', modelKeyPrefix: '' }, '', 'number'),
      name: {
        formType: 'input',
        label: '名称',
        type: String,
        default: '',
        modelKeyPrefix: '',
      },
    },
  },
});

export const radarDataProps = ({ group, modelKeyPrefix } = { group: '', modelKeyPrefix: '' }) => ({
  [getKey('data', modelKeyPrefix)]: {
    formType: 'list',
    label: '雷达线',
    type: Array,
    group,
    subProps: {
      ...dataPropsFn({ group: '', modelKeyPrefix: '' }, '', 'json'),
      name: {
        formType: 'input',
        label: '名称',
        type: String,
        default: '',
        modelKeyPrefix: '',
      },
      lineColor: {
        formType: 'color',
        label: '线条颜色',
        type: String,
        default: '#000',
        modelKeyPrefix: '',
      },
      areaColor: {
        formType: 'color',
        label: '线条颜色',
        type: String,
        default: '#000',
        modelKeyPrefix: '',
      },
    },
  },
});
