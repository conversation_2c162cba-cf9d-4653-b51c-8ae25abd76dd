<script lang="ts">
import { ref, defineComponent, toRefs, computed } from 'vue';
import { defineTaBuilderCellComponent } from '@/components/global/ta-component/TaBuilder/builder/defineTaBuilderCellComponent';
import echarts from 'echarts';
import { dataPropsFn } from './echartBaseProps';
import { useScreenDataFetchCollectionInject } from '@/components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';
import TaScreenEchartBase from './TaScreenEchartBase.vue';

const TaScreenGauge = defineTaBuilderCellComponent({
  name: 'TaScreenGauge',
  components: {
    TaScreenEchartBase,
  },
  props: {
    // percent: { type: Number, default: 0 },
    // color: { type: String, default: ''},
    ...dataPropsFn({ group: '', modelKeyPrefix: '' }, 'props', 'json'),
    primaryColor: { type: String, label: '主题色', default: '#01FFF6' },
    lineColor1: { type: String, label: '环颜色1', default: 'rgba(13, 53, 71, 0.7)' },
    lineColor2: { type: String, label: '环颜色2', default: 'rgba(0, 255, 252, 0.79)' },
    lineBackgroundColor: { type: String, label: '环背景颜色', default: 'rgb(15, 46, 72)' },
  },
  setup(props: any, { emit }: any) {
    const { getScreenData } = useScreenDataFetchCollectionInject(props);

    const value = computed(() => getScreenData(props, ''));

    const options = computed(() => {
      return {
        series: [
          {
            type: 'gauge',
            startAngle: 225,
            endAngle: -45,
            pointer: {
              show: false,
            },
            axisLine: {
              lineStyle: {
                width: 35,
                color: [[1, props.lineBackgroundColor]],
              },
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: false,
            },
            axisLabel: {
              show: false,
            },
            anchor: {
              show: false,
            },
            title: {
              show: false,
            },
            detail: {
              valueAnimation: true,
              fontSize: 36,
              offsetCenter: [0, 0],
              textShadowColor: 'rgba(1, 255, 246, 0.6)',
              textShadowBlur: 10,
              color: 'white',
              formatter: (value: number) => {
                return `${(value || 0) * 100}%`;
              },
            },
            data: [
              {
                value: value.value,
              },
            ],
          },
          {
            type: 'gauge',
            pointer: {
              show: false,
            },
            startAngle: 225,
            endAngle: 225 - value.value * (225 + 45),
            axisLine: {
              lineStyle: {
                width: 35,
                color: [
                  [
                    1,
                    new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                      { offset: 0, color: props.lineColor1 },
                      { offset: 0.7, color: props.lineColor2 },
                    ]),
                  ],
                ],
              },
            },
            axisTick: {
              show: false,
            },
            splitLine: {
              show: false,
            },
            axisLabel: {
              show: false,
            },
            anchor: {
              show: false,
            },
            title: {
              show: false,
            },
            detail: {
              show: false,
            },
          },
        ],
      };
    });

    const onPop = (key: string) => {
      emit('pop', key);
    };

    const boxShadow = computed(() => `0rem 0rem 0.5rem 0rem ${props.primaryColor}`); //rgba(1,255,246,0.6)

    return {
      ...toRefs(props),
      options,
      onPop,
      boxShadow,
    };
  },
});
export default TaScreenGauge;
</script>

<template lang="pug">
.ta-screen-gauge.w-full.h-full
  .w-full.h-full.relative
    .bg.absolute.top-0.left-0.transform.scale-80.origin-center.w-full.h-full.rounded-full
      .border-line.w-full.transform.scale-50.origin-bottom(class='h-1/2')
      .absolute.bottom-10.w-full
        .shadow-box.mx-auto
    TaScreenEchartBase(:options='options', @pop='onPop')
</template>

<style lang="stylus" scoped>
.ta-screen-gauge
  .bg
    background rgba(0,173,255,0.05)
  .border-line
    border 0.1rem solid rgba(0, 148, 248, 1)
    border-color v-bind(primaryColor)
    border-top-left-radius 999999px
    border-top-right-radius 999999px
    border-bottom 0
  .shadow-box
    width 2.95rem
    height 0.53rem
    background v-bind(primaryColor)
    box-shadow v-bind(boxShadow)
    border-radius 0.58rem
</style>
