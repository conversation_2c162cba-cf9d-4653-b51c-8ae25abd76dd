<script lang="ts">
import { ref, defineComponent, toRefs, computed, watch } from 'vue';
import { defineTaBuilderCellComponent } from '@/components/global/ta-component/TaBuilder/builder/defineTaBuilderCellComponent';
import echarts from 'echarts';
import {
  FontPropsFn,
  axisPropsFn,
  axisMarginPropsFn,
  barSeriesFn,
  legendPropsFn,
  lineSeriesFn,
  axisLabelFormatter,
} from './echartBaseProps';
import { useScreenDataFetchCollectionInject } from '@/components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';
import { VObject } from '@/lib/vails';
import utils from '@/components/global/ta-component/utils';
import { usePreviewClickable } from '@/components/global/ta-component/TaBuilder/builder/usePreview';
import { jsonataGet } from '@/components/global/ta-component/ta-template-form-core/useJsonata';
import { TaBuilderEvent } from '@/components/global/ta-component/TaBuilder/types';

const TaScreenBarChart = defineTaBuilderCellComponent({
  name: 'TaScreenBarChart',
  components: {},
  props: {
    ['title:show']: {
      type: Boolean,
      formType: 'switch',
      label: '显示',
      default: true,
      group: '标题设置',
    },
    ...FontPropsFn({ group: '标题设置', modelKeyPrefix: 'title' }),
    ...FontPropsFn({ group: '副标题设置', modelKeyPrefix: 'subTitle' }),
    ...axisPropsFn({ group: 'X轴设置', modelKeyPrefix: 'xAxis' }),
    ...axisPropsFn({ group: 'Y轴设置', modelKeyPrefix: 'yAxis' }),
    ...axisPropsFn({ group: 'Y2轴设置', modelKeyPrefix: 'yAxis2' }),
    ['number:off']: {
      type: Boolean,
      formType: 'switch',
      label: '隐藏',
      default: true,
      group: '数值设置',
    },
    ...FontPropsFn({ group: '数值设置', modelKeyPrefix: 'number' }),
    ...FontPropsFn({ group: '提示语设置', modelKeyPrefix: 'notice' }),
    ...axisMarginPropsFn({ group: '坐标轴边距设置', modelKeyPrefix: 'margin' }),
    stack: {
      type: Boolean,
      formType: 'switch',
      label: '并列',
      default: false,
      group: '样式设置',
    },
    ...barSeriesFn({ group: '数据条', modelKeyPrefix: '' }),
    ...lineSeriesFn({ group: '数据线', modelKeyPrefix: 'line' }),
    ...legendPropsFn({ group: '图例', modelKeyPrefix: 'legend' }),
    onShowJsonata: {
      label: '详情数据处理(jsonata)，`$$` 取全局数据',
      type: String,
      default: '',
      formType: 'textarea',
    },
  },
  setup(props: any, { emit }: any) {
    const { getScreenData, dataResult } = useScreenDataFetchCollectionInject(props);

    const options = computed(() => {
      // console.log(props, 'props123');
      const xAxisData = getScreenData(props, 'xAxis');
      const yAxisData = getScreenData(props, 'yAxis');

      return {
        grid: {
          left: `${props['margin:left']}%`,
          top: `${props['margin:top']}%`,
          right: `${props['margin:right']}%`,
          bottom: `${props['margin:bottom']}%`,
        },
        color: [],
        title: {
          text: props['title:text'],
          show: props['title:show'],
          textStyle: {
            color: props['title:fontColor'],
            fontSize: props['title:fontSize'],
            fontWeight: props['title:fontWeight'],
          },
          subtext: props['subTitle:text'],
          subtextStyle: {
            color: props['subTitle:fontColor'],
            fontSize: props['subTitle:fontSize'],
            fontWeight: props['subTitle:fontWeight'],
          },
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b} : {c}',
          textStyle: {
            color: props['notice:fontColor'],
            fontSize: props['notice:fontSize'],
            fontWeight: props['notice:fontWeight'],
          },
        },
        legend: {
          right: `${props['legend:right']}%`,
          top: `${props['legend:top']}%`,
          left: `${props['legend:left']}%`,
          bottom: `${props['legend:bottom']}%`,
          orient: props['legend:orient'],
          textStyle: {
            color: props['legend:fontColor'] || props['legend:color'],
            fontSize: props['title:fontSize'],
            fontWeight: props['title:fontWeight'],
          },
        },
        xAxis: {
          scale: true,
          type: props['xAxis:type'] || 'category',
          data: xAxisData && !utils.isEmpty(xAxisData) ? xAxisData : [],
          show: props['xAxis:show'],
          name: props['xAxis:name'],
          nameGap: props['xAxis:nameGap'],
          min: props['xAxis:min'] || null,
          max: props['xAxis:max'] || null,
          nameTextStyle: {
            color: props['xAxis:nameFontColor'],
            fontSize: props['xAxis:nameFontSize'],
            fontWeight: props['xAxis:nameFontWeight'],
          },
          inverse: props['xAxis:reversal'],
          axisLabel: {
            show: true,
            interval: props['xAxis:textInterval'], // 文字间隔
            rotate: props['xAxis:textAngle'], // 文字角度
            textStyle: {
              color: props['xAxis:fontColor'], // x轴 坐标文字颜色
              fontSize: props['xAxis:fontSize'],
            },
            margin: props['xAxis:margin'],
            formatter: axisLabelFormatter(props['xAxis:textMaxCount']),
          },
          axisLine: {
            show: props['xAxis:lineShow'],
            lineStyle: {
              color: props['xAxis:lineColor'],
            },
          },
          axisTick: {
            show: props['xAxis:tickLineShow'],
          },
          splitLine: {
            show: props['xAxis:isShowSplitLine'],
            lineStyle: {
              color: props['xAxis:splitLineColor'],
            },
          },
        },
        yAxis: [
          {
            type: props['yAxis:type'] || 'value',
            scale: true,
            show: props['yAxis:show'],
            name: props['yAxis:name'],
            nameGap: props['yAxis:nameGap'],
            min: props['yAxis:min'] || null,
            max: props['yAxis:max'] || null,
            data: yAxisData && !utils.isEmpty(yAxisData) ? yAxisData : [],
            nameTextStyle: {
              color: props['yAxis:nameFontColor'],
              fontSize: props['yAxis:nameFontSize'],
              fontWeight: props['yAxis:nameFontWeight'],
            },
            inverse: props['yAxis:reversal'],
            axisLabel: {
              show: true,
              interval: props['yAxis:textInterval'], // 文字间隔
              rotate: props['yAxis:textAngle'], // 文字角度
              textStyle: {
                color: props['yAxis:fontColor'], // x轴 坐标文字颜色
                fontSize: props['yAxis:fontSize'],
              },
              margin: props['yAxis:margin'],
              formatter: axisLabelFormatter(props['yAxis:textMaxCount']),
            },
            axisLine: {
              show: props['yAxis:lineShow'],
              lineStyle: {
                color: props['yAxis:lineColor'],
              },
            },
            axisTick: {
              show: props['yAxis:tickLineShow'],
            },
            splitLine: {
              show: props['yAxis:isShowSplitLine'],
              lineStyle: {
                color: props['yAxis:splitLineColor'],
              },
            },
            splitNumber: props['yAxis:splitNumber'],
          },

          {
            // type: props['yAxis2:type'],
            scale: true,
            show: props['yAxis2:show'],
            name: props['yAxis2:name'],
            min: props['yAxis:min'] || null,
            max: props['xAxis:max'] || null,
            nameTextStyle: {
              color: props['yAxis2:nameFontColor'],
              fontSize: props['yAxis2:nameFontSize'],
              fontWeight: props['yAxis2:nameFontWeight'],
            },
            inverse: props['yAxis2:reversal'],
            axisLabel: {
              show: true,
              interval: props['yAxis2:textInterval'], // 文字间隔
              rotate: props['yAxis2:textAngle'], // 文字角度
              textStyle: {
                color: props['yAxis2:fontColor'], // x轴 坐标文字颜色
                fontSize: props['yAxis2:fontSize'],
              },
              margin: props['yAxis2:margin'],
              formatter: axisLabelFormatter(props['yAxis2:textMaxCount']),
            },
            axisLine: {
              show: props['yAxis2:lineShow'],
              lineStyle: {
                color: props['yAxis2:lineColor'],
              },
            },
            axisTick: {
              show: props['yAxis2:tickLineShow'],
            },
            splitLine: {
              show: props['yAxis2:isShowSplitLine'],
              lineStyle: {
                color: props['yAxis2:splitLineColor'],
              },
            },
            splitNumber: props['yAxis2:splitNumber'],
          },
        ],
        series: [
          ...(props.series || []).map((barProps: VObject, index: number) => {
            // console.log(getScreenData(barProps, ''), 'barProps');

            return {
              name: barProps.name,
              type: 'bar',
              data: getScreenData(barProps, ''),
              // color: barProps.color,
              barWidth: `${barProps.width}%`,
              stack: props.stack ? '并列' : index,
              formatter: (val: VObject) => (val.value === 0 ? 0 : val.value),
              label: {
                show: barProps.label,
                position: barProps.labelPosition,
                color: barProps.labelColor,
                fontSize: barProps.labelFontSize,
                offset: [0, barProps.labelOffsetY],
              },
              itemStyle: {
                normal: {
                  barBorderRadius: barProps.barBorderRadius || [30, 30, 0, 0],
                  color: barProps.gradient_color_direction
                    ? new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                        {
                          offset: 0,
                          color: barProps.color,
                        },
                        {
                          offset: 1,
                          color: barProps.gradient_color || 'rgba(255, 255, 255, 0)',
                        },
                      ])
                    : new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        {
                          offset: 0,
                          color: barProps.color,
                        },
                        {
                          offset: 1,
                          color: barProps.gradient_color || 'rgba(255, 255, 255, 0)',
                        },
                      ]),
                  // opacity: 1,
                },
              },
            };
          }),
          ...(props['line:series'] || []).map((lineProps: VObject) => {
            // console.log(getScreenData(lineProps, ''), 'lineProps');

            return {
              data: getScreenData(lineProps, ''),
              type: 'line',
              smooth: true,
              showSymbol: false,
              lineStyle: {
                width: 1,
                color: lineProps.lineColor,
              },
              name: lineProps.name,
              yAxisIndex: lineProps.yAxisIndex,
              areaStyle: {
                opacity: 0.8,
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                  {
                    offset: 0,
                    color: lineProps.areaColor || 'rgb(128, 255, 165)',
                  },
                  {
                    offset: 1,
                    color: 'rgba(255, 255, 255 ,0)',
                  },
                ]),
              },
              emphasis: {
                focus: 'series',
              },
            };
          }),
        ],
      };
    });

    const onPop = (key: string) => {
      emit('pop', key);
    };

    const chart = ref<any>(null);

    const { onClick: onPreviewClick } = usePreviewClickable(props);

    const onClick = (item: VObject) => {
      props.componentSchema?.events?.forEach((event: TaBuilderEvent) => {
        onPreviewClick(event, { ...item, item }, item);
      });
    };

    const onShow = (record: VObject) => {
      const item = props.onShowJsonata
        ? jsonataGet(
            // 不能用 merge， 防止 vue route bug 会导致死循环
            { ...(record.rawData || record), $$: dataResult?.value },
            props.onShowJsonata,
          )
        : record;

      onClick(item);
    };

    const stopWatch = watch(
      () => chart.value?.chartRef,
      () => {
        if (chart.value?.chartRef) {
          chart.value.chartRef.on('click', 'series.bar', (params: any) => {
            onShow(params);
          });
          stopWatch();
        }
      },
    );

    console.log('options: ', options.value);
    return {
      ...toRefs(props),
      options,
      onPop,
      chart,
    };
  },
});
export default TaScreenBarChart;
</script>

<template lang="pug">
.ta-screen-bar-chart.w-full.h-full
  TaScreenEchartBase(ref='chart', :options='options', @pop='onPop', @click.stop='')
</template>

<style lang="stylus" scoped></style>
