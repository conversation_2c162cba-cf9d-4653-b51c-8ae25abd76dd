<script lang="ts">
import { ref, defineComponent, toRefs, computed } from 'vue';
import { defineTaBuilderCellComponent } from '@/components/global/ta-component/TaBuilder/builder/defineTaBuilderCellComponent';
import { dataPropsFn } from './echartBaseProps';
import { useScreenDataFetchCollectionInject } from '@/components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';

const TaScreenTaTemplateFormViewer = defineTaBuilderCellComponent({
  name: 'TaScreenTaTemplateFormViewer',
  components: {},
  props: {
    ...dataPropsFn({ group: '数据', modelKeyPrefix: 'data' }, 'props', 'json'),
    ...dataPropsFn({ group: '表单', modelKeyPrefix: 'template' }, 'props', 'json'),
  },
  setup(props: any) {
    const { getScreenData } = useScreenDataFetchCollectionInject(props);
    const data = computed(() => getScreenData(props, 'data'));
    const template = computed(() => getScreenData(props, 'template'));

    return {
      ...toRefs(props),
      data,
      template,
    };
  },
});
export default TaScreenTaTemplateFormViewer;
</script>

<template lang="pug">
.ta-screen-ta-template-form-viewer.h-full.w-full.overflow-auto
  TaTemplateFormViewer(
    :modelValue='data',
    :useDefaultTheme='false',
    :template='template',
    :hideEmpty='true'
  )
</template>

<style lang="stylus" scoped>
.ta-screen-ta-template-form-viewer
  >>> .ta-template-form-theme-card__default
    background transparent
  >>> .ta-template-form-list-layout
    background transparent
  >>> .ant-form-item-label > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::before
    display none
  >>> .ant-form-item-no-colon
    height fit-content
  >>> .viewer-label
    font-family 'Inter'
    font-style normal
    font-weight 600
    font-size 30px
    word-break break-all
    color #01CDFE !important
  >>> .value
    font-family 'Inter'
    font-style normal
    font-weight 400
    font-size 30px
    color #F9FAFB !important
  >>> .ta-template-form-field__item
    margin-bottom 10px
</style>
