<template lang="pug">
.com_scroll_table.w-full.h-full.rc_bottom.overflow-hidden
  .rc_bt_item_head.flex.items-center.w-full.mh-7.justify-between.px-3.py-1(v-if="showTableTitle")
    .rc_bt_item_name.text-sm.w-6(
      v-if="showIndex"
    )
    .rc_bt_item_name.text-sm(
      style="color: #37E8FF !important;", 
      v-for="item in columnTitle"
      :style="{width: `${item.width || '150px'}`}"
    ) {{ item.name }}
  template(v-if="isAutoScroll")
    Vue3Marquee(
      :pauseOnHover="true" :duration='bannerDuration' :vertical='true',
      class="!w-full"
    )
      .rc_bt_item.flex.items-center.w-full.h-7.justify-between.px-3.py-1(v-for="(list, index) in dataSource")
        .text-sm.ellipsis.w-6.text-center(
          v-if="showIndex",
          :class='`rc_bt_item_${calCss(index)}`'
        ) {{ index + 1 }}
        template(v-for="(ele, key) in list")
          component(
            :is="calParams(key)?.showTooltip ? 'a-tooltip':'div'",
            :title="ele + calParams(key)?.extra",
            color="#164F61"
          )
            .rc_bt_item_name.text-sm.ellipsis(
              :style="{width: `${calParams(key)?.width || '150px'}`}"
            ) {{ ele }}{{ calParams(key)?.extra }}
  template(v-else)
    .rc_bt_item.flex.items-center.w-full.h-7.justify-between.px-3.py-1(v-for="(list, index) in dataSource")
      .text-sm.ellipsis.w-6.text-center(
        v-if="showIndex"
        :class='`rc_bt_item_${calCss(index)}`'
      ) {{ index + 1 }}
      template(v-for="(ele, key) in list")
        component(
          :is="calParams(key)?.showTooltip ? 'a-tooltip':'div'",
          :title="ele + calParams(key)?.extra",
          color="#164F61"
        )
          .rc_bt_item_name.text-sm.ellipsis(
            :style="{width: `${calParams(key)?.width || '150px'}`}"
          ) {{ ele }}{{ calParams(key)?.extra }}
      //- .rc_bt_item_name.text-sm.ellipsis(
      //-   v-for="(ele, key) in list"
      //-   :style="{width: `${calParams(key)?.width || '150px'}`}"
      //- ) {{ ele }}{{ calParams(key)?.extra }}
</template>
<script lang="ts">
import { ref, defineComponent, reactive, toRefs, PropType, computed } from 'vue';
import { Vue3Marquee } from 'vue3-marquee';

export interface columnTitleType {
  width: string;
  name: string;
  id: string;
  extra: string;
  showTooltip: boolean;
}
export interface DataSourceType {
  value: string;
  name: string;
}

export default defineComponent({
  name: 'ComScrollTable',
  components: {
    Vue3Marquee,
  },
  props: {
    columnTitle: {
      type: Array as PropType<columnTitleType[]>,
      default: () => [],
    },
    dataSource: {
      type: Array as PropType<any[]>,
      default: () => [],
    },
    isAutoScroll: {
      type: Boolean,
      default: () => true,
    },
    showTableTitle: {
      type: Boolean,
      default: () => true,
    },
    showIndex: {
      type: Boolean,
      default: () => false,
    },
  },
  setup(props) {
    const infos = reactive({});
    const calParams = (key: string) => {
      const columnParams = props.columnTitle.find((item: columnTitleType) => item.id === key);
      return {
        width: columnParams?.width || '150px',
        extra: columnParams?.extra || '',
        showTooltip: columnParams?.showTooltip || false,
      };
    };
    const bannerDuration = computed(() => {
      return props.dataSource.length * 2;
    });
    const calCss = (index: number) => {
      if (index < 3) {
        return index + 1;
      } else if (index < 10) {
        return 'top6';
      }
      return 'name';
    };
    return {
      ...toRefs(props),
      ...toRefs(infos),
      calParams,
      bannerDuration,
      calCss,
    };
  },
});
</script>
<style lang="stylus" scoped>

.rc_bt_item_head
  background: rgba(46, 84, 138, 0.24);

.rc_bottom
  .rc_bt_item:nth-child(even)
    background: rgba(46, 84, 138, 0.24);
  .rc_bt_item:nth-child(odd)
    background: rgba(19, 36, 67, 0.24);
  .rc_bt_item_ic
    color #3F83F8
  .rc_bt_item_1
    color #FACA15
  .rc_bt_item_2
    color #C3DDFD
  .rc_bt_item_3
    color #FF8A4C
  .rc_bt_item_top6
    color #3F83F8

  .rc_bt_item_name,.rc_bt_item_count
    color #EBF5FF
  .rc_bt_item_name
    // overflow: hidden;
    // text-overflow: ellipsis;
    // white-space: nowrap;
  .rc_bt_item_count
    span
      opacity: 0.52;
.rc_bottom::-webkit-scrollbar{
  width: 4px;
}
.rc_bottom:hover::-webkit-scrollbar-thumb{
    background: #2E4B79;
    border-radius:14px;
  }
</style>
