<script lang="ts">
import { defineTaBuilderCellComponent } from '@/components/global/ta-component/TaBuilder/builder/defineTaBuilderCellComponent';
import { useScreenDataFetchCollectionInject } from '@/components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';
import { VObject } from '@/lib/vails';
import { computed, toRefs } from 'vue';
import {
  FontPropsFn,
  axisCenterPropsFn,
  axisMarginPropsFn,
  dataPropsFn,
  legendPropsFn,
  pieSeriesFn,
} from './echartBaseProps';

const TaScreenPieChart = defineTaBuilderCellComponent({
  name: 'TaScreenPieChart',
  components: {},
  props: {
    ['title:show']: {
      type: Boolean,
      formType: 'switch',
      label: '显示',
      default: true,
      group: '标题设置',
    },
    ...FontPropsFn({ group: '标题设置', modelKeyPrefix: 'title' }),
    ...FontPropsFn({ group: '副标题设置', modelKeyPrefix: 'subTitle' }),
    rose: {
      type: Boolean,
      formType: 'switch',
      label: '玫瑰图',
      default: false,
      group: '样式设置',
    },
    shadow: {
      type: Boolean,
      formType: 'switch',
      label: '阴影',
      default: false,
      group: '样式设置',
    },
    radius: {
      type: Array,
      formType: 'string_array',
      label: '半径[内圈, 外圈]',
      default: ['50%'],
      group: '样式设置',
    },
    selectedMode: {
      type: String,
      formType: 'select',
      label: '选中模式',
      default: 'single',
      select: [
        { value: 'single', label: '单选' },
        { value: 'multiple', label: '多选' },
        { value: 'series', label: '整个列' },
      ],
      group: '样式设置',
    },
    selectePosition: {
      type: String,
      formType: 'select',
      label: '显示的标签位置',
      default: 'inside',
      select: [
        { value: 'inside', label: '饼图扇区内部' },
        { value: 'outside', label: '饼图扇区外侧' },
        { value: 'center', label: '在饼图中心位置' },
        { value: '', label: '无' },
      ],
      group: '样式设置',
    },
    labelFormatter: {
      type: String,
      formType: 'textarea',
      label: '自定义标签显示内容',
      default: '{b}: {c} ({d}%)',
      group: '样式设置',
    },
    // ...axisPropsFn({ group: 'X轴设置', modelKeyPrefix: 'xAxis' }),
    // ...axisPropsFn({ group: 'Y轴设置', modelKeyPrefix: 'yAxis' }),
    // ['number:off']: {
    //   type: Boolean,
    //   formType: 'switch',
    //   label: '隐藏',
    //   default: true,
    //   group: '数值设置',
    // },
    // ...FontPropsFn({ group: '数值设置', modelKeyPrefix: 'number' }),
    // ...FontPropsFn({ group: '提示语设置', modelKeyPrefix: 'notice' }),
    ...axisCenterPropsFn({ group: '图形设置', modelKeyPrefix: 'graphicIntermediateText' }),
    ...dataPropsFn({ group: '图形设置', modelKeyPrefix: 'graphicIntermediateText' }),
    ...axisMarginPropsFn({ group: '坐标轴边距设置', modelKeyPrefix: 'margin' }),
    ...pieSeriesFn({ group: '数据饼', modelKeyPrefix: '' }),
    ...legendPropsFn({ group: '图例', modelKeyPrefix: 'legend' }),
  },
  setup(props: any, { emit }: any) {
    const { getScreenData } = useScreenDataFetchCollectionInject(props);
    const graphicIntermediateTextData = getScreenData(props, 'graphicIntermediateText');

    const options = computed(() => {
      const label_normal = !props.selectePosition
        ? {
            normal: {
              show: props['graphicIntermediateText:show'],
              width: props['graphicIntermediateText:width'],
              position: props['graphicIntermediateText:fontAlign'] || 'center',
              color: props['graphicIntermediateText:fontColor'],
              fontSize: props['graphicIntermediateText:fontSize'],
              fontWeight: props['graphicIntermediateText:fontWeight'],
              formatter: `${graphicIntermediateTextData}`,
            },
          }
        : {};

      return {
        left: `${props['margin:left']}%`,
        top: `${props['margin:top']}%`,
        right: `${props['margin:right']}%`,
        bottom: `${props['margin:bottom']}%`,
        title: {
          text: props['title:text'],
          show: props['title:show'],
          textStyle: {
            color: props['title:fontColor'],
            fontSize: props['title:fontSize'],
            fontWeight: props['title:fontWeight'],
          },
          subtext: props['subTitle:text'],
          subtextStyle: {
            color: props['subTitle:fontColor'],
            fontSize: props['subTitle:fontSize'],
            fontWeight: props['subTitle:fontWeight'],
          },
        },
        tooltip: {
          trigger: 'item',
          formatter: (val: any) => {
            return val.name + ':' + val.value; //标题
          },
        },
        color: (props.series || []).map((pieProps: VObject) => pieProps.color),
        legend: {
          icon: props['legend:icon'],
          orient: props['legend:orient'],
          right: `${props['legend:right']}%`,
          top: `${props['legend:top']}%`,
          left: `${props['legend:left']}%`,
          bottom: `${props['legend:bottom']}%`,
          textStyle: {
            color: props['legend:fontColor'] || props['legend:color'] || '#fff',
            fontSize: props['legend:fontSize'],
            fontWeight: props['legend:fontWeight'],
          },
        },
        markLine: {
          show: false,
        },
        series: [
          {
            name: '',
            type: 'pie',
            radius: props.radius.length === 1 ? props.radius[0] : props.radius,
            roseType: props.rose,
            selectedMode: props.selectedMode,
            data: (props.series || [])
              .map((pieProps: VObject) => ({
                ...pieProps,
                value: getScreenData(pieProps, ''),
              }))
              .sort((a: VObject, b: VObject) => b.value - a.value),
            // 饼图中间的内容
            label: {
              formatter: props.labelFormatter || '{d}%',
              position: props.selectePosition,
              // show: true,
              ...label_normal,
              emphasis: {
                show: props['graphicIntermediateText:show'],
              },
            },
            labelLine: {
              show: false,
            },
            center: [
              `${props['graphicIntermediateText:x'] || 50}%`,
              `${props['graphicIntermediateText:y'] || 50}%`,
            ],
            itemStyle: {
              color: (params: VObject) => {
                return params.data.color;
              },
              // borderRadius: '10%',
              // borderColor: '#fff',
              // borderWidth: 2,
              ...(props.shadow
                ? {
                    borderRadius: 5,
                    shadowColor: 'rgba(0, 0, 0, 0.9)',
                    shadowOffsetX: 4,
                    shadowOffsetY: -8,
                    shadowBlur: 5,
                    borderWidth: 2,
                  }
                : {}),
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 40,
                fontWeight: 'bold',
              },
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)',
              },
            },
          },
        ],
      };
    });
    console.log('options: ', options);
    const onPop = (key: string) => {
      emit('pop', key);
    };
    return {
      ...toRefs(props),
      options,
      onPop,
    };
  },
});
export default TaScreenPieChart;
</script>

<template lang="pug">
.ta-screen-pie-chart.w-full.h-full
  TaScreenEchartBase(:options='options', @pop='onPop')
</template>

<style lang="stylus" scoped></style>
