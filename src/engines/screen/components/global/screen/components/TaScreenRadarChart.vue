<script lang="ts">
import { ref, defineComponent, toRefs, computed } from 'vue';
import { defineTaBuilderCellComponent } from '@/components/global/ta-component/TaBuilder/builder/defineTaBuilderCellComponent';
import { useScreenDataFetchCollectionInject } from '@/components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';
import { VObject } from '@/lib/vails';
import { radarIndicatorsProps, radarDataProps } from './echartBaseProps';

const TaScreenRadarChart = defineTaBuilderCellComponent({
  name: 'TaScreenRadarChart',
  components: {},
  props: {
    ...radarIndicatorsProps(),
    ...radarDataProps(),
  },
  setup(props: any, { emit }: any) {
    const { getScreenData } = useScreenDataFetchCollectionInject(props);

    const radarData = computed(() =>
      (props.data || []).map((dataProps: VObject) => {
        return {
          name: dataProps.name,
          value: getScreenData(dataProps, ''),
          areaStyle: {
            color: dataProps.areaColor,
          },
          lineStyle: {
            color: dataProps.lineColor,
          },
        };
      }),
    );

    const options = computed(() => {
      const max = Math.max(
        ...radarData.value
          .map((data: VObject) => (Array.isArray(data.value) ? data.value : []))
          .reduce((a: number[], b: number[]) => [...a, ...b], []),
      );

      return {
        legend: {
          data: (props.data || []).map((dataProps: VObject) => dataProps.name),
        },
        radar: {
          // shape: 'circle',
          indicator: (props.indicators || []).map((indicatorProps: VObject) => {
            return {
              name: indicatorProps.name,
              max: getScreenData(indicatorProps, '') || max,
            };
          }),
        },
        series: [
          {
            name: '',
            type: 'radar',
            data: radarData.value,
          },
        ],
      };
    });

    const onPop = (key: string) => {
      emit('pop', key);
    };

    return {
      ...toRefs(props),
      options,
      onPop,
    };
  },
});
export default TaScreenRadarChart;
</script>

<template lang="pug">
.ta-screen-radar-chart.w-full.h-full
  TaScreenEchartBase(:options='options', @pop='onPop')
</template>

<style lang="stylus" scoped></style>
