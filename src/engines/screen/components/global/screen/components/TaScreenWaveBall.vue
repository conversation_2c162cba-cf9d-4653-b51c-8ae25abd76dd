<script lang="ts">
import { toRefs, watch, onMounted, ref, computed } from 'vue';
import { defineTaBuilderCellComponent } from '@/components/global/ta-component/TaBuilder/builder/defineTaBuilderCellComponent';
import { dataPropsFn } from './echartBaseProps';
import * as echarts from 'echarts';
import 'echarts-liquidfill';
import { VObject } from '@/lib/vails';
import { useScreenDataFetchCollectionInject } from '@/components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';

const TaScreenWaveBall = defineTaBuilderCellComponent({
  name: 'TaScreenWaveBall',
  components: {},
  props: {
    ...dataPropsFn({ group: '', modelKeyPrefix: '' }, 'props', 'json'),
    graphName: { label: '图形名称', type: String, default: '' },
    graphValue: { label: '图形显示值(数值)', type: String, default: '50' },
    max: { label: '最大值', type: Number, default: 100 },
    radius: { label: '水球图的半径', type: String, default: '95%' },
    width: { label: '宽度', type: String, default: '100' },
    height: { label: '高度', type: String, default: '100' },
  },
  setup(props: any) {
    const { getScreenData } = useScreenDataFetchCollectionInject(props);

    const hygrometer = ref<any>(null);
    const varStyle = computed(() => {
      return `width: ${props.width}px;height: ${props.height}px;`;
    });
    const liquidFill = () => {
      var liquid = echarts.init(hygrometer.value);
      let max = props.max;
      let currentVal = Number(getScreenData(props, '') || props.graphValue);
      let val = Number((currentVal / max).toFixed(4));
      let perVal = (val * 100).toFixed(2);
      var data = [val];

      var option = {
        // 图表主标题
        // 提示框组件
        tooltip: {
          show: false,
          trigger: 'item', // 触发类型, 数据项图形触发，主要在散点图，饼图等无类目轴的图表中使用。
          textStyle: {
            color: '#fff', // 文字颜色
          },
          // 提示框浮层内容格式器，支持字符串模板和回调函数两种形式
          // 水球图: {a}（系列名称），{b}（无），{c}（数值）
          // 使用函数模板   传入的数据值 -> value: number|Array,
          formatter: function (params: VObject) {
            return params.seriesName + ': ' + perVal + '%';
          },
        },

        series: [
          {
            type: 'liquidFill',
            cursor: 'default',
            name: props.graphName, // 系列名称，用于tooltip的显示，legend 的图例筛选
            radius: props.radius, // 水球图的半径
            // 水填充图的形状 circle 默认圆形  rect 圆角矩形  triangle 三角形
            // diamond 菱形  pin 水滴状 arrow 箭头状  还可以是svg的path
            shape: 'circle',
            color: ['#69B4F3', '#fff'], // 波浪颜色、、#3EABFF
            phase: 50, // 波的相位弧度 不设置  默认自动
            amplitude: '10', //波的振幅
            direction: 'right', // 波浪移动的速度  两个参数  left 从右往左 right 从左往右
            outline: {
              show: true,
              borderDistance: 10, // 边框线与图表的距离 数字
              itemStyle: {
                opacity: 1, // 边框的透明度   默认为 1
                borderWidth: 2, // 边框的宽度
                shadowBlur: 1, // 边框的阴影范围 一旦设置了内外都有阴影
                shadowColor: '#fff', // 边框的阴影颜色,
                borderColor: '#69B4F3', // 边框颜色
              },
            },
            // 图形样式
            itemStyle: {
              color: '#3EABFF', // 水球显示的背景颜色
              opacity: 0.7, // 波浪的透明度
              cursor: 'default',
              shadowBlur: 10, // 波浪的阴影范围
            },
            backgroundStyle: {
              color: '#3EABFF', // 水球未到的背景颜色
              opacity: 0.1, // 波浪的透明度
              borderWidth: 0,
              borderColor: 'rgba(26,108,177,0.1)',
            },
            // 图形上的文本标签
            label: {
              show: true,
              normal: {
                // formatter: this.proportion * 100 + '%',
                textStyle: {
                  color: '#fff',
                  fontSize: 20,
                },
              },
            },
            // 图形的高亮样式
            emphasis: {
              itemStyle: {
                cursor: 'default',
                opacity: 0.8, // 鼠标经过波浪颜色的透明度
              },
            },
            data: data,
          },
        ],
      };

      liquid.setOption(option as any, true);
    };

    onMounted(() => {
      if (props.max) {
        liquidFill();
      }
    });
    watch(
      () => [props.max, props.graphValue, props.width, props.height, getScreenData(props, '')],
      () => {
        if (props.max) {
          liquidFill();
        }
      },
    );
    return {
      ...toRefs(props),
      hygrometer,
      varStyle,
    };
  },
});
export default TaScreenWaveBall;
</script>

<template lang="pug">
.ta-screen-wave-ball(:style='varStyle')
  .liquidFill(ref='hygrometer')
</template>

<style lang="stylus" scoped>
.liquidFill
  width 100%
  height 100%
</style>
