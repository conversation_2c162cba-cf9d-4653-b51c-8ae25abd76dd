<script lang="ts">
import { ref, toRefs, computed, watch } from 'vue';
import { defineTaBuilderCellComponent } from '@/components/global/ta-component/TaBuilder/builder/defineTaBuilderCellComponent';
import { dataPropsFn } from './echartBaseProps';
import { useScreenDataFetchCollectionInject } from '@/components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';
import { VObject } from '@/lib/vails';

const TaScreenList = defineTaBuilderCellComponent({
  name: 'TaScreenList',
  components: {},
  props: {
    ...dataPropsFn({ group: '', modelKeyPrefix: '' }, 'props', 'json'),
  },
  setup(props: any) {
    const { getScreenData } = useScreenDataFetchCollectionInject(props);
    const value = computed(() => getScreenData(props, ''));

    const innerValue = ref<VObject>([]);

    watch(value, () => {
      [...value.value].reverse().forEach((i: VObject) => {
        innerValue.value.unshift(i);
      });
    });

    return {
      ...toRefs(props),
      value,
    };
  },
});
export default TaScreenList;
</script>

<template lang="pug">
.ta-screen-list.w-full.h-full.overflow-auto
  transition-group(name='list')
    .item.flex.w-full.text-white(v-for='item in value', :key='item.id')
      .m-2.point.flex-shrink-0.rounded-full
      .content.flex-grow.w-0 {{ item.name }}
</template>

<style lang="stylus" scoped>
.point
  width 0.5rem
  height 0.5rem
  background linear-gradient(180deg, rgba(7,37,106,0) 0%, rgba(7,37,106,0.7) 100%)

.list-enter-active, .list-leave-active
  transition all 0.5s ease

.list-enter-from, .list-leave-to
  opacity 0
  transform translateX(30px)
</style>
