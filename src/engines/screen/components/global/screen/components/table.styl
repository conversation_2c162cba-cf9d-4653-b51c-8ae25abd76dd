
.zebra
  *
    font-size 30px
  table
    border none
    border-collapse separate
    border-spacing 0 1rem
  .body-container
    transform translateY(1rem)
  .header-table
    transform translateY(-1rem)
    th div
      border-bottom 1px solid #8AE9ED
  .body-table
    transform translateY(-2rem)
  tr
    height 1px
    th
      // background #0069AA !important
      height 1px
      border-bottom 0px !important
      & > div
        margin 0.5rem
        padding 0.5rem 0
        height 100%
        display flex
        justify-content center
        align-items flex-end
        flex-grow 1
  .dark-tr
    background linear-gradient(93.3deg, rgba(105, 180, 243, 0.2) 22.08%, rgba(105, 180, 243, 0.2) 91.33%)
  .light-tr
    background linear-gradient(93.3deg, rgba(105, 180, 243, 0.6) 22.08%, rgba(105, 180, 243, 0.6) 91.33%)
  tr td:first-child
    border-radius 6px 0 0 6px
  tr td:last-child
    border-radius 0 6px 6px 0
.zebra-2
  *
    font-size 30px
  table
    border none
    border-collapse separate
    border-spacing 0 1rem
  .body-table
    transform translateY(-1rem)
  th
    background #0069AA !important
    & > div
      margin 1rem 0
      // border-bottom 1px solid #8AE9ED
  .dark-tr
    background transparent
  .light-tr
    background linear-gradient(93.3deg, rgba(105, 180, 243, 0.4) 22.08%, rgba(105, 180, 243, 0.4) 91.33%)
  tr td:first-child
    border-radius 6px 0 0 6px
  tr td:last-child
    border-radius 0 6px 6px 0
.pure
  *
    font-size 30px
  table
    border none
    border-collapse separate
    border-spacing 0 1rem
  .body-table
    transform translateY(-1rem)
  th
    background #0069AA !important
    & > div
      margin 1rem 0
      // border-bottom 1px solid #8AE9ED
  .dark-tr
    background transparent
  // .light-tr
  // background linear-gradient(93.3deg, rgba(105, 180, 243, 0.4) 22.08%, rgba(105, 180, 243, 0.4) 91.33%)
  tr td:first-child
    border-radius 6px 0 0 6px
  tr td:last-child
    border-radius 0 6px 6px 0
