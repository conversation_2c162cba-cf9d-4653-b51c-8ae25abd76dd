<script lang="ts">
import { defineTaBuilderCellComponent } from '@/components/global/ta-component/TaBuilder/builder/defineTaBuilderCellComponent';
import { useScreenDataFetchCollectionInject } from '@/components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';
import { computed, onMounted, ref, toRefs } from 'vue';
import { dataPropsFn } from './echartBaseProps';
import TaScreenEchartBase from './TaScreenEchartBase.vue';

// 添加地图加载函数
const loadChinaMap = async (): Promise<boolean> => {
  try {
    console.log('尝试加载中国地图数据...');

    // 使用 require.ensure 方式加载
    return new Promise<boolean>(resolve => {
      try {
        require.ensure(
          [],
          require => {
            try {
              require('echarts/map/js/china');
              console.log('成功加载中国地图数据');
              resolve(true);
            } catch (e) {
              console.debug('require方式加载失败，尝试其他方式');
              resolve(false);
            }
          },
          'china-map',
        );
      } catch (e) {
        console.debug('require.ensure加载失败，尝试直接导入');
        // 降级尝试直接导入
        try {
          require('echarts/map/js/china');
          console.log('直接导入成功加载中国地图数据');
          resolve(true);
        } catch (innerError) {
          console.info('所有加载方式都失败，将使用基础地图显示');
          resolve(false);
        }
      }
    });
  } catch (error) {
    console.info('地图加载过程发生错误，将使用基础地图显示:', error);
    return false;
  }
};

const TaScreenChinaMap = defineTaBuilderCellComponent({
  name: 'TaScreenChinaMap',
  components: {
    TaScreenEchartBase,
  },
  props: {
    ...dataPropsFn({ group: '', modelKeyPrefix: '' }, 'props', 'json'),
    // 是否需要加载中国地图
    needChinaMap: { type: Boolean, default: true },
  },
  setup(props: any, { emit }: any) {
    const { getScreenData } = useScreenDataFetchCollectionInject(props);
    const mapLoaded = ref(false);

    onMounted(async () => {
      if (props.needChinaMap) {
        mapLoaded.value = await loadChinaMap();
        console.log('地图加载状态:', mapLoaded.value ? '成功' : '未加载');
      }
    });

    const value = computed(
      () =>
        getScreenData(props, '') || [
          { name: '海门', value: [121.15, 31.89] },
          { name: '鄂尔多斯', value: [109.781327, 39.608266] },
          { name: '招远', value: [120.38, 37.35] },
          { name: '舟山', value: [122.207216, 29.985295] },
          { name: '齐齐哈尔', value: [123.97, 47.33] },
          { name: '盐城', value: [120.13, 33.38] },
          { name: '赤峰', value: [118.87, 42.28] },
          { name: '青岛', value: [120.33, 36.07] },
          { name: '乳山', value: [121.52, 36.89] },
        ],
    );

    const options = computed(() => {
      const baseOption = {
        tooltip: {
          show: false,
        },
        geo: {
          map: props.needChinaMap && mapLoaded.value ? 'china' : 'none',
          roam: false,
          zoom: 1.23,
          center: [105, 36],
          label: {
            normal: {
              show: false,
              fontSize: '10',
              color: 'rgba(0,0,0,0.7)',
            },
            emphasis: {
              show: false,
            },
          },
          itemStyle: {
            normal: {
              areaColor: 'rgba(0,0,0,0)',
              borderColor: '#389dff',
              borderWidth: 1,
              shadowBlur: 5,
              shadowOffsetY: 8,
              shadowOffsetX: 0,
              shadowColor: '#01012a',
            },
            emphasis: {
              areaColor: '#184cff',
              shadowOffsetX: 0,
              shadowOffsetY: 0,
              shadowBlur: 5,
              borderWidth: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
            },
          },
        },
        series: [
          {
            name: '',
            type: 'scatter',
            coordinateSystem: 'geo',
            data: value.value,
            symbol: 'circle',
            symbolSize: 8,
            hoverSymbolSize: 10,
            tooltip: {
              formatter(value: any) {
                return value.data.name + '<br/>' + '：' + '22';
              },
              show: true,
            },
            encode: {
              value: 2,
            },
            label: {
              formatter: '{b}',
              position: 'right',
              show: false,
            },
            itemStyle: {
              color: 'white',
            },
            emphasis: {
              label: {
                show: false,
              },
            },
          },
        ],
      };

      return baseOption;
    });

    const onPop = (key: string) => {
      emit('pop', key);
    };

    return {
      ...toRefs(props),
      options,
      onPop,
      mapLoaded,
    };
  },
});
export default TaScreenChinaMap;
</script>

<template lang="pug">
.ta-screen-china-map.w-full.h-full
  TaScreenEchartBase(:options='options', @pop='onPop')
</template>

<style lang="stylus" scoped></style>
