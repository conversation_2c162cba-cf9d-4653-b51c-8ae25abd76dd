<script lang="ts">
import { ref, toRefs, computed, watch } from 'vue';
import { defineTaBuilderCellComponent } from '@/components/global/ta-component/TaBuilder/builder/defineTaBuilderCellComponent';
import { dataPropsFn } from './echartBaseProps';
import { useScreenDataFetchCollectionInject } from '@/components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';
import { debounce } from 'lodash';

const TaScreenIcon = defineTaBuilderCellComponent({
  name: 'TaScreenIcon',
  components: {},
  props: {
    ...dataPropsFn({ group: '', modelKeyPrefix: '' }, 'props', 'input'),
  },
  setup(props: any) {
    const { getScreenData } = useScreenDataFetchCollectionInject(props);

    const value = computed(() => getScreenData(props, ''));

    const type = ref('');

    const syncType = debounce(() => (type.value = value.value), 500);
    watch(value, syncType, { immediate: true });

    return {
      ...toRefs(props),
      type,
      value,
    };
  },
});
export default TaScreenIcon;
</script>

<template lang="pug">
TaIcon.ta-screen-text-icon(:key='type', :type='type')
</template>

<style lang="stylus" scoped></style>
