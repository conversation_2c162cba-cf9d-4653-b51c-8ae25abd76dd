<script lang="ts">
import { ref, defineComponent, toRefs, computed } from 'vue';
import echarts from 'echarts';
import { onMounted } from 'vue';
const ComEchartsGaugeCard = defineComponent({
  name: 'ComEchartsGaugeCard',
  components: {},
  props: {
    gaugeKey: { type: String, default: 'gaugeId' },
    seriesData: { type: Array, default: () => [{ value: 90, name: '模范视窗' }] },
  },
  setup(props) {
    const gauge = ref<any>(null);
    const option: any = computed(() => ({
      series: [
        {
          type: 'gauge',
          radius: '80%',
          startAngle: 210, // 开始角度
          endAngle: -30, // 结束角度
          min: 0, //最大数值
          max: 100, //最小数值
          axisLine: {
            //仪表盘轴线相关配置。
            show: true,
            lineStyle: {
              //仪表盘轴线样式。
              width: 40, //轴线宽度。
              color: [
                [0.3, '#EEF0F0'], //分段颜色，10%是什么颜色
                [0.5, '#FFC82C'],
                [0.7, '#78A4FA'],
                [1, '#FF4949'],
              ],
            },
          },
          axisTick: {
            //刻度样式。
            show: true,
            splitNumber: 1, //分隔线之间分割的刻度数。
            length: 10, //刻度线长
          },
          splitLine: {
            //分隔线样式
            show: false,
          },
          axisLabel: {
            // 刻度标签
            show: false,
            color: '#fff',
          },
          progress: {
            //展示当前进度
            show: true,
          },
          pointer: {
            //仪表盘指针。
            show: true,
            length: '50%', //指针长度
            width: 5,
            // offsetCenter: [0, '50%'], //相对于仪表盘中心的偏移位置 [水平，垂直]
          },
          title: {
            // 仪表盘标题。
            show: false,
          },
          detail: {
            valueAnimation: true, //是否开启标签的数字动画。
            show: false,
            offsetCenter: [0, '50%'],
            textStyle: {
              fontSize: 25,
              color: '#000', //刻度颜色
            },
            formatter: ['{value}'].join('\n'),
            rich: {
              name: {
                fontSize: 20,
                lineHeight: 10,
                color: '#ddd',
                padding: [30, 0, 0, 0],
              },
            },
          },
          itemStyle: {
            //仪表盘指针样式。
            borderCap: 'square',
            normal: {
              color: '#00c4fa',
            },
          },
          roundCap: true,
          // data: [
          //   {
          //     value: 90,
          //     name: '1',
          //   },
          // ],
          data: props.seriesData,
        },
      ],
    }));
    // const createChart = () => {
    //   let domId = document.getElementById(`${props.gaugeKey}`) as HTMLDivElement;
    //   gauge.value = echarts.init(domId);
    //   gauge.value.setOption(option.value);
    // };
    onMounted(() => {
      // createChart();
    });
    return {
      ...toRefs(props),
      option,
    };
  },
});
export default ComEchartsGaugeCard;
</script>

<template lang="pug">
.com-echarts-gauge-card(v-if='gaugeKey')
  .echarts-gauge-card-main
    TaScreenEchartBase.echarts-gauge(:options='option')
    .indicators-box
      .indicators-data {{ seriesData[0].value }}
      //- .indicators-title {{ seriesData[0].name }}
      //- .indicators-title {{ seriesData[0].name }}
    //- .wingman.text-white
      .circular.one
        img.img(
          src='https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/party-build-mobile/large-screen/rotateBtnGreen.4c6db6a5.png'
        )
        .data-num 95
          //- text.unit %
        .data-title 讲政治
      .circular.tew
        img.img(
          src='https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/party-build-mobile/large-screen/rotateBtn.e629aa36.png'
        )
        .data-num 86
          //- text.unit %
        .data-title 守纪律
      .circular.three
        img.img(
          src='https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/party-build-mobile/large-screen/rotateBtnYellow.e993cceb.png'
        )
        .data-num 90
          //- text.unit %
        .data-title 负责任
      .circular.four
        img.img(
          src='https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/party-build-mobile/large-screen/rotateBtnRed.4a347dc0.png'
        )
        .data-num 91
          //- text.unit %
        .data-title 有效率
</template>

<style lang="stylus" scoped>
.com-echarts-gauge-card
  // display flex
  // justify-content center
  // align-items center
  // width 350px
  // height 350px
  // position relative
  .echarts-gauge-card-main
    position absolute
    top -50%
    left -50%
    transform scale(0.65)
    background url('https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/party-build-mobile/large-screen/dialBgImg.png') no-repeat
    background-size cover
    background-position 50% 0
    width 700px
    height 700px
    display flex
    justify-content center
    align-items center
    // position relative
    .echarts-gauge
      position relative
      top -8.5%
      left 0%
      width 110%
      height 110%
    .indicators-box
      height 700px
      width 700px
      text-align center
      position absolute
      top 24%
      .indicators-data
        font-size 60px
        font-weight 500
        color #ffffff
        margin-bottom 30%
      .indicators-title
        font-size 40px
        color #ffffff
    .wingman
      width 700px
      height 30%
      position absolute
      bottom 10px
      display flex
      justify-content space-between
      padding 0 25px
      .circular
        width 120px
        height 120px
        display flex
        flex-direction column
        align-items center
        justify-content center
        position relative
        .data-num
          font-size 28px
          line-height 16px
          font-weight 500
          line-height 34px
          .unit
            font-size 14px
        .data-title
          font-size 18px
          line-height 14px
        .img
          width 120px
          height 120px
          position absolute
          top 0
          left 0
          animation mymove 8s infinite
          animation-timing-function linear
      .one
        position relative
        top 10%
        .img
          animation-delay 0.3s
      .tew
        position relative
        top 45%
        .img
          animation-delay 0.1s
      .three
        position relative
        top 45%
        .img
          animation-delay 1s
      .four
        position relative
        top 10%
        .img
          animation-delay 0.8s
@keyframes mymove
  from
    transform rotate(0deg)
  to
    transform rotate(360deg)
</style>
