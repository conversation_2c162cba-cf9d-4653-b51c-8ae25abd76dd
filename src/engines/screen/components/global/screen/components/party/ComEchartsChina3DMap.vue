<script lang="ts">
import { ref, defineComponent, toRefs, onMounted, computed } from 'vue';
import * as echarts from 'echarts';
import axios from 'axios';
import 'echarts-gl';
import { VObject } from '@/lib/vails';
const ComEchartsChina3DMap = defineComponent({
  name: 'ComEchartsChina3DMap',
  components: {},
  props: {
    label: { type: String, default: '党员教育示范基地' },
  },
  setup(props) {
    const colorArray = [
      '#eff6ff',
      '#dbeafe',
      '#bfdbfe',
      '#93c5fd',
      '#60a5fa',
      '#3b82f6',
      '#2563eb',
      '#1d4ed8',
      '#1e40af',
      '#1e3a8a',
    ];

    // const mapDom = ref<any>(null);
    // const map = ref<any>(null);
    const geoCoordMap = ref<any>({
      杭州市: [120.15, 30.28],
      上城区: [120.17, 30.25],
      下城区: [120.17, 30.28],
      临平区: [120.17, 30.25],
      江干区: [120.2, 30.27],
      拱墅区: [120.13, 30.32],
      西湖区: [120.13, 30.27],
      滨江区: [120.2, 30.2],
      萧山区: [120.27, 30.17],
      余杭区: [120.3, 30.42],
      钱塘区: [120.29, 30.19],
      桐庐县: [119.67, 29.8],
      淳安县: [119.03, 29.6],
      建德区: [119.28, 29.48],
      富阳区: [119.95, 30.05],
      临安区: [119.72, 30.23],
      建德市: [119.17, 29.28],
    });

    const data = [
      // { name: '杭州市', value: 10, all:  },
      { name: '上城区', all: 1858, value: 1788 },
      // { name: '下城区', all: 30, value:  },
      { name: '临平区', all: 263, value: 239 },
      // { name: '江干区', all: 40, value:  },
      { name: '拱墅区', all: 2506, value: 2369 },
      { name: '西湖区', all: 1280, value: 1279 },
      { name: '滨江区', all: 626, value: 615 },
      { name: '萧山区', all: 2465, value: 2350 },
      { name: '余杭区', all: 2193, value: 2184 },
      { name: '桐庐县', all: 3459, value: 3453 },
      { name: '钱塘区', all: 75, value: 61 },
      { name: '淳安县', all: 3612, value: 3587 },
      { name: '建德市', all: 4776, value: 4759 },
      { name: '富阳区', all: 3146, value: 3146 },
      { name: '临安区', all: 3681, value: 3527 },
    ];

    const option = computed(() => ({
      grid: {
        left: 0,
      },
      // visualMap: {
      //   left: 'right',
      //   // min: 10,
      //   // max: 200,
      //   inRange: {
      //     color: [
      //       'rgba(84, 112, 198, 0.1)',
      //       'rgba(84, 112, 198, 0.1)',
      //       'rgba(84, 112, 198, 0.2)',
      //       'rgba(84, 112, 198, 0.3)',
      //       'rgba(84, 112, 198, 0.4)',
      //       'rgba(84, 112, 198, 0.5)',
      //       'rgba(84, 112, 198, 0.6)',
      //       'rgba(84, 112, 198, 0.7)',
      //       'rgba(84, 112, 198, 0.8)',
      //       'rgba(84, 112, 198, 0.9)',
      //       'rgba(84, 112, 198, 1)',
      //     ],
      //   },
      //   text: ['High', 'Low'],
      //   calculable: false,
      //   itemWidth: 0,
      //   itemHeight: 0,
      //   hoverLink: false,
      // },
      tooltip: {
        trigger: 'item',
        show: true,
        enterable: true,
        textStyle: {
          fontSize: 20,
          color: '#fff',
        },
        backgroundColor: 'rgba(0,2,89,0.8)',
        formatter: (val: any) => {
          if (!val.data.all) return ``;
          return `${val.data.name}<br>${val.data.value}/${val.data.all}`;
        },
      },
      geo3D: {
        map: 'hangzhou',
        left: '10%',
        show: false,
      },
      series: [
        // 柱状体的主干
        {
          geoIndex: 0,
          // coordinateSystem: 'geo',
          // viewControl: { autoRotate: true },
          showLegendSymbol: true,
          type: 'map3D',
          roam: true,
          label: {
            normal: {
              show: false,
              textStyle: {
                color: '#fff',
              },
            },
            emphasis: {
              show: false,
              textStyle: {
                color: '#fff',
              },
            },
          },
          itemStyle: {
            borderColor: '#2ab8ff',
            borderWidth: 1.5,
            color: (params: VObject) => {
              return (
                colorArray[
                  Math.round(
                    (((params.value as number) - minAll.value) / (maxAll.value - minAll.value)) *
                      colorArray.length,
                  ) - 1
                ] || colorArray[0]
              );
            },
          },
          emphasis: {
            itemStyle: {
              borderWidth: 0,
              color: '#2AB8FF',
            },
          },
          map: 'hangzhou',
          data: data,
          left: '10%',
        },
      ],
    }));

    const maxAll = computed<number>(() => {
      return Math.max(...data.map(item => item.all));
    });

    const minAll = computed<number>(() => {
      return Math.min(...data.map(item => item.all));
    });

    // 柱状体的主干
    // const lineData = computed(() => {
    //   return data
    //     .map(item => {
    //       if (!geoCoordMap.value[item.name]) return null;
    //       // console.log(item);
    //       return {
    //         name: item.name,
    //         value: [...geoCoordMap.value[item.name], item.all - item.value],
    //       };
    //     })
    //     .filter(i => i);
    // });

    // const lineDoneData = computed(() => {
    //   return data
    //     .map(item => {
    //       if (!geoCoordMap.value[item.name]) return null;
    //       // console.log(item);
    //       return {
    //         name: item.name,
    //         value: [...geoCoordMap.value[item.name], item.value],
    //         itemStyle: {
    //           color: '#91cc75',
    //         },
    //       };
    //     })
    //     .filter(i => i);
    // });

    const loaded = ref(false);
    onMounted(() => {
      axios
        .get('https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/party-build-web/hangzhou.json')
        .then((res: any) => {
          echarts.registerMap('hangzhou', res.data);
          loaded.value = true;
          // console.log(res.data);
          // createChart(res.data);
        });
    });
    return {
      ...toRefs(props),
      option,
      loaded,
      // lineData,
      // lineDoneData,
    };
  },
});
export default ComEchartsChina3DMap;
</script>

<template lang="pug">
TaScreenEchartBase.echarts(v-if='loaded', :options='option')
</template>

<style lang="stylus" scoped>
.com-e-charts-china-3d-map
  .echart-map-line-main
    width 100%
    height 100%
    .echart-map-line
      width 100%
      height 100%
</style>
