<script lang="ts">
import { ref, defineComponent, toRefs, computed } from 'vue';
import echarts from 'echarts';
const ComEChartsPie2 = defineComponent({
  name: 'ComEChartsPie2',
  components: {},
  props: {
    pieSeriesData: {
      type: Array,
      default: () => [
        { value: 1, color: '#6ABDF7', name: '上城区' },
        { value: 1, color: '#8DD39A', name: '拱墅区' },
        { value: 1, color: '#F8B862', name: '西湖区' },
        { value: 1, color: '#EC6D71', name: '滨江区' },
        { value: 1, color: '#9270CA', name: '其他区' },
      ],
    },
    config: {
      type: Object,
      default: () => ({
        roseType: false, //玫瑰图 area || false
        radius: '50%', //可切换成环型图：[内圈百分比，外圈百分比]
        title: {}, //参展 //配置项 title 环型图标题可显示在中间
      }),
    },
  },
  setup(props) {
    const pieDom = ref<any>(null);
    const pie = ref<any>(null);
    const seriesData = computed(() => {
      return props.pieSeriesData;
    });
    const color = computed(() => {
      return props.pieSeriesData.map((item: any) => item.color);
    });
    const option = computed(() => ({
      title: props.config.title,
      tooltip: {
        trigger: 'item',
        formatter: (val: any) => {
          return val.name + ':' + val.value; //标题
        },
      },
      color: color.value,
      legend: {
        icon: 'circle',
        orient: 'vertical',
        x: 'right',
        // right: 0,
        top: '10%',
        textStyle: {
          color: '#fff',
        },
      },
      markLine: {
        show: false,
      },
      series: [
        {
          name: '',
          type: 'pie',
          radius: props.config.radius || '50%',
          roseType: props.config.roseType || false,
          data: seriesData.value,
          label: {
            show: false,
          },
          labelLine: {
            show: false,
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
            },
          },
        },
      ],
    }));
    const createChart = () => {
      pie.value = echarts.init(pieDom.value);
      pie.value.setOption(option.value);
    };
    // onMounted(() => {
    //   createChart();
    // });
    return {
      ...toRefs(props),
      pieDom,
      option,
    };
  },
});
export default ComEChartsPie2;
</script>

<template lang="pug">
TaScreenEchartBase.com-e-charts-pie(:options='option')
</template>

<style lang="stylus" scoped>
.com-e-charts-pie
  .echart-pie-main
    width 100%
    height 100%
    .echart-pie
      width 100%
      height 100%
</style>
