<script lang="ts">
import { ref, defineComponent, toRefs, onMounted, computed, PropType } from 'vue';
import echarts from 'echarts';
interface LineType {
  name: string;
  data: number[];
  color?: string;
}
const ComEchartLine1 = defineComponent({
  name: 'ComEchartLine1',
  components: {},
  props: {
    //x轴数据
    lineXAxisData: {
      type: Array,
      default: () => [1, 2, 3, 8],
    },
    lineSeriesData: {
      type: Array as PropType<LineType[]>,
      default: () => [
        {
          name: '理论中心组学习次数',
          data: [130, 109, 152, 206],
          color: '#fcd34d',
        },
        {
          name: '理论中心缉交流硏讨次数',
          data: [132, 232, 282, 608],
          color: '#84cc16',
        },
        // {
        //   name: 'Wed',
        //   data: [201, 101, 111, 231],
        //   color: '#22d3ee',
        // },
        // {
        //   name: 'Thu',
        //   data: [334, 264, 234, 134],
        //   color: '#818cd8',
        // },
        // {
        //   name: 'Fri',
        //   data: [190, 90, 220, 190],
        //   color: '#ec4899',
        // },
        // {
        //   name: 'Sat',
        //   data: [130, 340, 340, 230],
        //   color: '#f87171',
        // },
        // {
        //   name: 'Sun',
        //   data: [220, 250, 310, 120],
        //   color: '#1e40af',
        // },
      ],
    },
    config: {
      type: Object,
      default: () => ({
        area: true, // 是否显示面积
      }),
    },
  },
  setup(props) {
    const lineDom = ref<any>(null);
    const line = ref<any>(null);
    const xAxisData = computed(() => {
      return props.lineXAxisData;
    });
    const seriesData = computed(() => {
      return props.lineSeriesData.map((item: any) => {
        return {
          data: item.data,
          name: item.name,
          type: 'line',
          smooth: false, //角度是否圆滑
          // stack: 'Total',
          showSymbol: false,
          lineStyle: {
            width: 1,
            color: item.color || '#fff',
          },
          areaStyle: {
            opacity: props.config.area ? 0.8 : 0,
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              {
                offset: 0,
                color: item.color,
              },
              {
                offset: 1,
                color: `${item.color}1A`,
              },
            ]),
          },
          emphasis: {
            focus: 'series',
          },
        };
      });
    });
    const legendData = computed(() => {
      return props.lineSeriesData.map((item: any) => {
        return item.name;
      });
    });
    const color = computed(() => {
      return props.lineSeriesData.map((item: any) => item.color);
    });
    const option = computed(() => ({
      color: color.value,
      grid: {
        bottom: 10,
        top: 10,
        left: 10,
        right: 10,
      },
      xAxis: {
        type: 'category',
        boundaryGap: false,
        data: xAxisData.value, //数据
        splitLine: {
          show: false,
        },
        //x轴线样式
        axisLine: {
          show: true,
          lineStyle: {
            color: '#fff',
          },
        },
        //x轴刻度线样式
        axisTick: {
          show: false,
        },
        //x轴标题样式
        axisLabel: {
          show: true,
          color: '#fff',
        },
      },
      yAxis: {
        type: 'value',
        splitLine: {
          show: false,
        },
        axisLine: {
          show: true,
          lineStyle: {
            color: '#fff',
          },
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: true,
          color: '#fff',
        },
      },
      //图例
      legend: {
        show: true,
        icon: 'circle',
        data: legendData.value,
        textStyle: {
          color: '#fff',
        },
      },
      //提示
      tooltip: {},

      series: seriesData.value,
    }));
    const createChart = () => {
      line.value = echarts.init(lineDom.value);
      line.value.setOption(option.value);
    };
    // onMounted(() => {
    //   createChart();
    // });
    return {
      ...toRefs(props),
      lineDom,
      option,
    };
  },
});
export default ComEchartLine1;
</script>

<template lang="pug">
TaScreenEchartBase.echart-line(:options='option')

//- .com-echart-line
//-   .echart-line-main
//-     .echart-line(ref="lineDom")
</template>

<style lang="stylus" scoped>
.com-echart-line
  width 100%
  height 100%
  .echart-line-main
    width 100%
    height 100%
    .echart-line
      width 100%
      height 100%
</style>
