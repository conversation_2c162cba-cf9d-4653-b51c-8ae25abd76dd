<script lang="ts">
import { ref, defineComponent, toRefs, onMounted, computed, PropType } from 'vue';
import echarts from 'echarts';

const ComEChartsBar2 = defineComponent({
  name: 'ComEChartsBar2',
  components: {},
  props: {
    xAxisData: {
      type: Array as PropType<string[]>,
      default: () => ['巡视巡擦', '意识形态', '专项督查', '文明暗访'],
    },
    barSeriesData: {
      type: Array,
      default: () => [
        {
          name: '问题数',
          data: [0, 0, 0, 56],
          color: '#fcd34d',
        },
        {
          name: '已整改数',
          data: [0, 0, 0, 56],
          color: '#84cc16',
        },
        // {
        //   name: '完好率',
        //   data: [70, 48, 73, 68, 53, 47, 50, 72, 96, 86],
        //   color: '#22d3ee',
        // },
      ],
    },
    config: {
      type: Object,
      default: () => ({
        stack: true, //并列
      }),
    },
  },
  setup(props) {
    const barDom = ref<any>(null);
    const bar = ref<any>(null);
    const seriesData = computed(() => {
      return props.barSeriesData.map((item: any, index: number) => {
        return {
          name: item.name,
          type: 'bar',
          data: item.data,
          color: item.color,
          barWidth: '40%',
          stack: props.config.stack ? '并列' : index,
          // yAxisIndex: index,
          itemStyle: {
            normal: {
              barBorderRadius: props.barSeriesData.length - 1 == index ? [30, 30, 0, 0] : 0,
              // color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              //   {
              //     offset: 1,
              //     color: item.color,
              //   },
              //   {
              //     offset: 0,
              //     color: `${item.color}00`,
              //   },
              // ]),
              // opacity: 1,
            },
          },
        };
      });
    });
    const legendData = computed(() => {
      return props.barSeriesData.map((item: any) => {
        return item.name;
      });
    });
    const option = computed(() => ({
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
      },
      legend: {
        data: legendData.value,
        left: '50%',
        // align: 'auto',
        icon: 'pin',
        textStyle: {
          color: '#fff',
        },
        right: 0,
      },
      grid: {
        containLabel: true,
        bottom: 10,
      },
      xAxis: {
        type: 'category',
        axisLine: {
          show: true,
          lineStyle: {
            color: '#fff',
          },
        },
        data: props.xAxisData,
        axisTick: {
          show: false,
        },
        axisLabel: {
          hideOverlap: false,
          textStyle: {
            color: '#fff',
          },
        },
      },
      yAxis: {
        type: 'value',
        axisLine: {
          show: true,
          lineStyle: {
            color: '#fff',
          },
        },
        splitLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          show: false,
          textStyle: {
            color: '#fff',
          },
        },
      },
      series: seriesData.value,
    }));
    const createChart = () => {
      bar.value = echarts.init(barDom.value);
      bar.value.setOption(option.value);
    };
    // onMounted(() => {
    //   createChart();
    // });
    return {
      ...toRefs(props),
      barDom,
      option,
    };
  },
});
export default ComEChartsBar2;
</script>

<template lang="pug">
TaScreenEchartBase.echart-bar(:options='option')

</template>

<style lang="stylus" scoped>
.com-e-charts-bar
  width 100%
  height 100%
  .echart-bar-main
    width 100%
    height 100%
    .echart-bar
      width 100%
      height 100%
</style>
