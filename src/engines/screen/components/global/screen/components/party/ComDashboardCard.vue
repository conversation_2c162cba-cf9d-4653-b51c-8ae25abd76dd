<script lang="ts">
import { ref, defineComponent, toRefs } from 'vue';
import ComEchartsGaugeCard from './ComEchartsGaugeCard.vue';

const ComDashboardCard = defineComponent({
  name: 'ComDashboardCard',
  components: {
    ComEchartsGaugeCard,
  },
  props: {
    value: { type: String, default: 'copy' },
  },
  setup(props) {
    const data = [
      {
        value: 90,
        name: '红雁清风',
      },
    ];
    return {
      ...toRefs(props),
      data,
    };
  },
});
export default ComDashboardCard;
</script>

<template lang="pug">
.com-dashboard-card
  .dashboard
    .card
      ComEchartsGaugeCard(gaugeKey='qaz')
  .dashboard
    .card
      ComEchartsGaugeCard(gaugeKey='wsx', :seriesData='data')
</template>

<style lang="stylus" scoped>
.com-dashboard-card
  // width 100%
  display flex
  justify-content space-between
  align-items center
  padding 0 60px
  .dashboard
    width 250px
    height 250px
    display flex
    justify-content center
    align-items center
    .card
      width fit-content
      height fit-content
</style>
