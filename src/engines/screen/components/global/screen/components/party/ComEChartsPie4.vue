<script lang="ts">
import { ref, defineComponent, toRefs, onMounted, computed } from 'vue';
import echarts from 'echarts';
const ComEChartsPie4 = defineComponent({
  name: 'ComEChartsPie4',
  components: {},
  props: {
    pieSeriesData: {
      type: Array,
      default: () => [
        { value: 128, color: '#6ABDF7', name: '党委数' },
        { value: 167, color: '#8DD39A', name: '党总支数' },
        { value: 2298, color: '#D1D5DB', name: '党支部数' },
      ],
    },
    config: {
      type: Object,
      default: () => ({
        roseType: false, //玫瑰图 area || false
        radius: ['50%', '80%'], //可切换成环型图：[内圈百分比，外圈百分比]
        title: {}, //参展 //配置项 title 环型图标题可显示在中间
      }),
    },
  },
  setup(props) {
    const pieDom = ref<any>(null);
    const pie = ref<any>(null);
    const seriesData = computed(() => {
      return props.pieSeriesData;
    });
    const color = computed(() => {
      return props.pieSeriesData.map((item: any) => item.color);
    });
    const option = computed(() => ({
      title: props.config.title,
      tooltip: {
        trigger: 'item',
        formatter: (val: any) => {
          return val.name + ':' + val.value; //标题
        },
      },
      color: color.value,
      legend: {
        show: false,
        orient: 'vertical',
        right: '0px', //图例位置
        top: '20%',
        icon: 'circle',
      },
      // markLine: {
      //   show: false,
      // },
      series: [
        {
          name: '',
          type: 'pie',
          radius: props.config.radius || '50%',
          roseType: props.config.roseType || false,
          data: seriesData.value,
          roundCap: 2, //边弧度
          label: {
            show: false,
            position: 'center',
          },
          // labelLine: {
          //   show: false,
          // },
          emphasis: {
            //- 中间文字
            label: {
              show: true,
              fontSize: '180%',
              fontWeight: 'bold',
              formatter: (val: any) => {
                return val.value;
              },
            },
            // itemStyle: {
            //   shadowBlur: 10,
            //   shadowOffsetX: 0,
            //   shadowColor: 'rgba(0, 0, 0, 0.5)',
            // },
          },
        },
      ],
    }));
    const createChart = () => {
      pie.value = echarts.init(pieDom.value);
      pie.value.setOption(option.value);
    };
    // onMounted(() => {
    //   createChart();
    // });
    return {
      ...toRefs(props),
      pieDom,
      option,
    };
  },
});
export default ComEChartsPie4;
</script>

<template lang="pug">
TaScreenEchartBase.com-e-charts-pie(:options='option')
</template>

<style lang="stylus" scoped>
.com-e-charts-pie
  .echart-pie-main
    width 100%
    height 100%
    .echart-pie
      width 100%
      height 100%
</style>
