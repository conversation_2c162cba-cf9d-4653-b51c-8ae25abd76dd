<script lang="ts">
import { ref, defineComponent, toRefs, onMounted, computed, PropType } from 'vue';
import echarts from 'echarts';

const ComEChartsBar1 = defineComponent({
  name: 'ComEChartsBar1',
  components: {},
  props: {
    config: {
      type: Object,
      default: () => ({
        stack: false, //并列
      }),
    },
  },
  setup(props) {
    const name = ['疫情防控', '政策宣讲', '心理疏导', '应急救援'];

    const tabs = ref([
      {
        name: '预备队人数',
        data: [20, 50, 80, 58],
        color: '#B991FF',
      },
      {
        name: '第一梯队',
        data: [68, 57, 80, 42],
        color: '#2BFEC0',
      },
      {
        name: '8976',
        data: [87, 60, 62, 86],
        color: '#FDDD60',
      },
      {
        name: '后备队',
        data: [48, 73, 68, 53],
        color: '#58D9F9',
      },
      {
        name: '32175',
        data: [70, 48, 73, 68],
        color: '#008AD9',
      },
    ]);
    const tabKey = ref(tabs.value[0].name);
    const tab = ref([tabs.value[0]]);
    const barDom = ref<any>(null);
    const bar = ref<any>(null);
    const seriesData = computed(() => {
      return tab.value.map((item: any, index: number) => {
        return {
          name: item.name,
          type: 'bar',
          data: item.data,
          color: item.color,
          barWidth: '20%',
          stack: props.config.stack ? '并列' : index,
          itemStyle: {
            normal: {
              barBorderRadius: [30, 30, 0, 0],
              // color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              //   {
              //     offset: 1,
              //     color: item.color,
              //   },
              //   {
              //     offset: 0,
              //     color: `${item.color}00`,
              //   },
              // ]),
              // opacity: 1,
            },
          },
        };
      });
    });
    const option = computed(() => ({
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
      },
      grid: {
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        axisLine: {
          show: true,
          lineStyle: {
            color: '#fff',
          },
        },
        data: name,
        axisTick: {
          show: false,
        },
        axisLabel: {
          textStyle: {
            color: '#fff',
          },
        },
      },
      yAxis: {
        type: 'value',
        axisLine: {
          show: true,
          lineStyle: {
            color: '#fff',
          },
        },
        splitLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          textStyle: {
            color: '#fff',
          },
        },
      },
      series: seriesData.value,
    }));
    const createChart = () => {
      bar.value = echarts.init(barDom.value);
      bar.value.setOption(option.value);
    };

    const onTab = (data: any) => {
      tabKey.value = data.name;
      tab.value[0] = data;
      bar.value.setOption(option.value);
    };
    // onMounted(() => {
    //   createChart();
    // });
    return {
      ...toRefs(props),
      barDom,
      tabs,
      onTab,
      tabKey,
      option,
    };
  },
});
export default ComEChartsBar1;
</script>

<template lang="pug">
.com-e-charts-bar
  .flex.flex-between
    .tab.h-7.cursor-pointer.transition-all.duration-150.pl-4(
      :class="`${tabKey == item.name ? 'text-gray-700 text-sm':'text-white text-xs'}`"
      @click="onTab(item)"
      v-for="item in tabs"
      ) {{item.name}}


  TaScreenEchartBase.echart-bar(:options='option')
</template>

<style lang="stylus" scoped>
.com-e-charts-bar
  .echart-bar-main
    width 100%
    height 100%
    .echart-bar
      width 100%
      height 100%
</style>
