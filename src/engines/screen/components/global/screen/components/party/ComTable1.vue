<script lang="ts">
import { ref, defineComponent, toRefs } from 'vue';

const ComTable1 = defineComponent({
  name: 'ComTable1',
  components: {},
  props: {},
  setup(props) {
    const dataSource = [
      { title: '市直机关举办“迎亚运 助共富——壬寅迎新春联展”迎新送福活动' },
      { title: '市税务局的这场个人摄影展，带你邂逅最美西湖！' },
      { title: '典籍中的杭州！市志办以“活力”典籍赋能亚运盛典' },
      { title: '亚运在我身边，场馆先睹为快！' },
      { title: '杭州交通青年话“共富”！' },
      { title: '金牌宣讲员花落谁家？市直机关微型党课比赛暨“共同富裕·青年说”圆满落幕！' },
    ];
    return {
      ...toRefs(props),
      dataSource,
    };
  },
});
export default ComTable1;
</script>

<template lang="pug">
a-table.com-table-1.w-full.h-full.overflow-hidden.px-4(:dataSource='dataSource')
  a-table-column(:autoHeight='true' title='序号')
    template(#default='{ index }')
      | {{ index + 1 }}

  a-table-column(:autoHeight='true' title='标题', dataIndex='title')
  //- a-table-item(title='图片')
</template>

<style lang="stylus" scoped>
.com-table-1
  >>> .ant-table
    background transparent
    color white
  >>> .ant-table-tbody>tr>td
    color white
  >>> .ant-table-tbody>tr>td
    color white
</style>
