<script lang="ts">
import { ref, defineComponent, toRefs, onMounted, computed } from 'vue';
import * as echarts from 'echarts';
import axios from 'axios';

const ComEChartsMapLines = defineComponent({
  name: 'ComEChartsMapLines',
  components: {},
  props: {
    label: { type: String, default: '党员教育示范基地' },
  },
  setup(props) {
    const mapDom = ref<any>(null);
    const map = ref<any>(null);
    const geoCoordMap = ref<any>({
      杭州市: [120.15, 30.28],
      上城区: [120.17, 30.25],
      下城区: [120.17, 30.28],
      江干区: [120.2, 30.27],
      拱墅区: [120.13, 30.32],
      西湖区: [120.13, 30.27],
      滨江区: [120.2, 30.2],
      萧山区: [120.27, 30.17],
      余杭区: [120.3, 30.42],
      桐庐县: [119.67, 29.8],
      淳安县: [119.03, 29.6],
      建德区: [119.28, 29.48],
      富阳区: [119.95, 30.05],
      临安区: [119.72, 30.23],
    });
    const data = [
      { name: '杭州市', value: 10, num: 60 },
      { name: '上城区', value: 20, num: 17 },
      { name: '下城区', value: 30, num: 0 },
      { name: '临平区', value: 35, num: 2 },
      { name: '江干区', value: 40, num: 0 },
      { name: '拱墅区', value: 50, num: 5 },
      { name: '西湖区', value: 60, num: 5 },
      { name: '滨江区', value: 70, num: 1 },
      { name: '萧山区', value: 80, num: 5 },
      { name: '余杭区', value: 100, num: 3 },
      { name: '桐庐县', value: 120, num: 3 },
      { name: '钱塘区', value: 150, num: 0 },
      { name: '淳安县', value: 140, num: 4 },
      { name: '建德市', value: 160, num: 6 },
      { name: '富阳区', value: 180, num: 5 },
      { name: '临安区', value: 200, num: 4 },
    ];
    const option = computed(() => ({
      grid: {
        left: 0,
      },
      visualMap: {
        left: 'right',
        min: 10,
        max: 200,
        inRange: {
          color: [
            '#313695',
            '#FFA500',
            '#74add1',
            '#abd9e9',
            '#e0f3f8',
            '#ffffbf',
            '#fee090',
            '#fdae61',
            '#f46d43',
            '#ffcccc',
            '#66ff33',
          ],
        },
        text: ['High', 'Low'],
        calculable: false,
        itemWidth: 0,
        itemHeight: 0,
        hoverLink: false,
      },
      tooltip: {
        trigger: 'item',
        show: true,
        enterable: true,
        textStyle: {
          fontSize: 20,
          color: '#fff',
        },
        backgroundColor: 'rgba(0,2,89,0.8)',
        formatter: (val: any) => {
          return `${val.data.name}<br>${props.label}:${val.data.num}`;
        },
      },
      geo: [
        {
          map: 'hangzhou',
          aspectScale: 0.9,
          roam: false, // 是否允许缩放
          zoom: 1.2, // 默认显示级别
          layoutSize: '90%',
          layoutCenter: ['55%', '50%'],
          label: {
            show: true,
          },
          itemStyle: {
            normal: {
              areaColor: {
                type: 'linear-gradient',
                x: 0,
                y: 400,
                x2: 0,
                y2: 0,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(37,108,190,0.3)', // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: 'rgba(15,169,195,0.3)', // 50% 处的颜色
                  },
                ],
                global: true, // 缺省为 false
              },
              borderColor: '#4ecee6',
              borderWidth: 1,
            },
            emphasis: {
              areaColor: {
                type: 'linear-gradient',
                x: 0,
                y: 300,
                x2: 0,
                y2: 0,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(37,108,190,1)', // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: 'rgba(15,169,195,1)', // 50% 处的颜色
                  },
                ],
                global: true, // 缺省为 false
              },
            },
          },
          emphasis: {
            itemStyle: {
              areaColor: '#0160AD',
            },
            label: {
              show: 0,
              color: '#fff',
            },
          },
          zlevel: 3,
        },
        {
          map: 'hangzhou',
          aspectScale: 0.9,
          roam: false, // 是否允许缩放
          zoom: 1.2, // 默认显示级别
          layoutSize: '90%',
          layoutCenter: ['55%', '50%'],
          itemStyle: {
            normal: {
              borderColor: 'rgba(192,245,249,.6)',
              borderWidth: 2,
              shadowColor: '#2C99F6',
              shadowOffsetY: 0,
              shadowBlur: 120,
              areaColor: 'rgba(29,85,139,.2)',
            },
          },
          zlevel: 2,
          silent: true,
        },
        {
          map: 'hangzhou',
          aspectScale: 0.9,
          roam: false, // 是否允许缩放
          zoom: 1.2, // 默认显示级别
          layoutSize: '90%',
          layoutCenter: ['55%', '51.5%'],
          itemStyle: {
            // areaColor: '#005DDC',
            areaColor: 'rgba(0,27,95,0.4)',
            borderColor: '#004db5',
            borderWidth: 1,
          },
          zlevel: 1,
          silent: true,
        },
      ],
      series: [
        // map
        {
          geoIndex: 0,
          // coordinateSystem: 'geo',
          showLegendSymbol: true,
          type: 'map',
          roam: true,
          label: {
            normal: {
              show: false,
              textStyle: {
                color: '#fff',
              },
            },
            emphasis: {
              show: false,
              textStyle: {
                color: '#fff',
              },
            },
          },
          itemStyle: {
            normal: {
              borderColor: '#2ab8ff',
              borderWidth: 1.5,
              areaColor: '#12235c',
            },
            emphasis: {
              areaColor: '#2AB8FF',
              borderWidth: 0,
              color: 'red',
            },
          },
          map: 'hangzhou', // 使用
          data: data,
          // data: this.difficultData //热力图数据   不同区域 不同的底色
        },
        // // 柱状体的主干
        // {
        //   type: 'lines',
        //   zlevel: 5,
        //   showBackground: true,
        //   effect: {
        //     show: false,
        //     // period: 4, //箭头指向速度，值越小速度越快
        //     // trailLength: 0.02, //特效尾迹长度[0,1]值越大，尾迹越长重
        //     // symbol: 'arrow', //箭头图标
        //     // symbol: imgDatUrl,
        //     symbolSize: 5, // 图标大小
        //   },
        //   lineStyle: {
        //     width: 20, // 尾迹线条宽度
        //     color: 'rgb(22,255,255, .6)',
        //     opacity: 1, // 尾迹线条透明度
        //     curveness: 0, // 尾迹线条曲直度
        //   },
        //   label: {
        //     show: 0,
        //     position: 'end',
        //     formatter: '245',
        //   },
        //   silent: true,
        //   data: lineData(),
        // },
        // // 柱状体的顶部
        // {
        //   type: 'scatter',
        //   coordinateSystem: 'geo',
        //   geoIndex: 0,
        //   zlevel: 5,
        //   label: {
        //     show: true,
        //     formatter: (val: any) => {
        //       // console.log(val);
        //       return ``;
        //     },
        //     position: 'top',
        //   },
        //   symbol: 'circle',
        //   symbolSize: [20, 10],
        //   itemStyle: {
        //     color: 'rgb(22,255,255, 1)',
        //     opacity: 1,
        //   },
        //   silent: true,
        //   data: scatterData(),
        // },
        // // 柱状体的底部
        // {
        //   type: 'scatter',
        //   coordinateSystem: 'geo',
        //   geoIndex: 0,
        //   zlevel: 4,
        //   label: {
        //     // 这儿是处理的
        //     formatter: '{b}',
        //     position: 'bottom',
        //     color: '#fff',
        //     fontSize: 12,
        //     distance: 10,
        //     show: true,
        //   },
        //   symbol: 'circle',
        //   symbolSize: [20, 10],
        //   itemStyle: {
        //     // color: '#F7AF21',
        //     color: 'rgb(22,255,255, 1)',
        //     opacity: 1,
        //   },
        //   silent: true,
        //   data: scatterData2(),
        // },
        // // 底部外框
        // {
        //   type: 'scatter',
        //   coordinateSystem: 'geo',
        //   geoIndex: 0,
        //   zlevel: 4,
        //   label: {
        //     show: false,
        //   },
        //   symbol: 'circle',
        //   symbolSize: [40, 20],
        //   itemStyle: {
        //     color: {
        //       type: 'radial',
        //       x: 0.5,
        //       y: 0.5,
        //       r: 0.5,
        //       colorStops: [
        //         {
        //           offset: 0,
        //           color: 'rgb(22,255,255, 0)', // 0% 处的颜色
        //         },
        //         {
        //           offset: 0.75,
        //           color: 'rgb(22,255,255, 0)', // 100% 处的颜色
        //         },
        //         {
        //           offset: 0.751,
        //           color: 'rgb(22,255,255, 1)', // 100% 处的颜色
        //         },
        //         {
        //           offset: 1,
        //           color: 'rgb(22,255,255, 1)', // 100% 处的颜色
        //         },
        //       ],
        //       global: false, // 缺省为 false
        //     },

        //     opacity: 1,
        //   },
        //   silent: true,
        //   data: scatterData2(),
        // },
      ],
    }));
    // 动态计算柱形图的高度（定一个max）
    const lineMaxHeight = () => {
      const maxValue = Math.max(...data.map(item => item.value));
      return 0.3 / maxValue;
    };
    // 柱状体的主干
    const lineData = () => {
      return data.map(item => {
        // console.log(item);
        return {
          coords: [
            geoCoordMap.value?.[item.name],
            [
              geoCoordMap.value[item.name][0],
              geoCoordMap.value[item.name][1] + item.value * lineMaxHeight(),
            ],
          ],
        };
      });
    };
    // 柱状体的顶部
    const scatterData = () => {
      return data.map(item => {
        return [
          geoCoordMap.value[item.name][0],
          geoCoordMap.value[item.name][1] + item.value * lineMaxHeight(),
        ];
      });
    };
    // 柱状体的底部
    const scatterData2 = () => {
      return data.map(item => {
        return {
          name: item.name,
          value: geoCoordMap.value[item.name],
        };
      });
    };
    // const createChart = (data: any) => {
    //   echarts.registerMap('hangzhou', data);
    //   map.value = echarts.init(mapDom.value);
    //   map.value.setOption(option.value);
    // };

    const loaded = ref(false);
    onMounted(() => {
      axios
        .get('https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/party-build-web/hangzhou.json')
        .then((res: any) => {
          echarts.registerMap('hangzhou', res.data);
          loaded.value = true;
          // console.log(res.data);
          // createChart(res.data);
        });
    });
    return {
      ...toRefs(props),
      mapDom,
      option,
      loaded,
    };
  },
});
export default ComEChartsMapLines;
</script>

<template lang="pug">
TaScreenEchartBase.echarts(v-if='loaded', :options='option')
</template>

<style lang="stylus" scoped>
.com-e-charts-map-lines
  .echart-map-line-main
    width 100%
    height 100%
    .echart-map-line
      width 100%
      height 100%
</style>
