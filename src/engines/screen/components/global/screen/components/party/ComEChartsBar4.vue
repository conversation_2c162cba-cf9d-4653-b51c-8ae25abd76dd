<script lang="ts">
import { ref, defineComponent, toRefs, onMounted, computed, PropType } from 'vue';
import echarts from 'echarts';

const ComEChartsBar4 = defineComponent({
  name: 'ComEChartsBar4',
  components: {},
  props: {
    xAxisData: {
      type: Array as PropType<string[]>,
      default: () => [
        '日常监督',
        '正风肃纪',
        '执纪问责',
        '清廉教育',
        '协助推进',
        '自身建设',
        '其他工作',
      ],
    },
    barSeriesData: {
      type: Array,
      default: () => [
        {
          name: '',
          data: [20, 50, 80, 58, 83, 68, 57],
          color: '#fcd34d',
        },
        // {
        //   name: '在线率',
        //   data: [50, 70, 60, 61, 75, 87, 60, 62, 86, 46],
        //   color: '#84cc16',
        // },
        // {
        //   name: '完好率',
        //   data: [70, 48, 73, 68, 53, 47, 50, 72, 96, 86],
        //   color: '#22d3ee',
        // },
      ],
    },
    config: {
      type: Object,
      default: () => ({
        stack: false, //并列
      }),
    },
  },
  setup(props) {
    const barDom = ref<any>(null);
    const bar = ref<any>(null);
    const seriesData = computed(() => {
      return props.barSeriesData.map((item: any, index: number) => {
        return {
          name: item.name,
          type: 'bar',
          data: item.data,
          color: item.color,
          stack: props.config.stack ? '并列' : index,
          barWidth: '30%',
          itemStyle: {
            normal: {
              barBorderRadius: 0,
              // color: new echarts.graphic.LinearGradient(0, 0, 1, 0, [
              //   {
              //     offset: 1,
              //     color: item.color,
              //   },
              //   {
              //     offset: 0,
              //     color: `${item.color}00`,
              //   },
              // ]),
              // opacity: 1,
            },
          },
        };
      });
    });
    const legendData = computed(() => {
      return props.barSeriesData.map((item: any) => {
        return item.name;
      });
    });
    const option = computed(() => ({
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
      },
      legend: {
        data: legendData.value,
        icon: 'pin',
        textStyle: {
          color: '#fff',
        },
        right: 0,
      },
      grid: {
        containLabel: true,
        top: 10,
        bottom: 0,
        left: 0,
        right: 0,
      },
      xAxis: {
        type: 'category',
        axisLine: {
          show: true,
          lineStyle: {
            color: '#fff',
          },
        },
        data: props.xAxisData,
        axisTick: {
          show: false,
        },
        axisLabel: {
          textStyle: {
            color: '#fff',
          },
        },
      },
      yAxis: {
        type: 'value',
        axisLine: {
          show: true,
          lineStyle: {
            color: '#fff',
          },
        },
        splitLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          textStyle: {
            color: '#fff',
          },
        },
      },
      series: seriesData.value,
    }));
    const createChart = () => {
      bar.value = echarts.init(barDom.value);
      bar.value.setOption(option.value);
    };
    // onMounted(() => {
    //   createChart();
    // });
    return {
      ...toRefs(props),
      barDom,
      option,
    };
  },
});
export default ComEChartsBar4;
</script>

<template lang="pug">
TaScreenEchartBase.echart-bar(:options='option')
</template>

<style lang="stylus" scoped>
.com-e-charts-bar
  .echart-bar-main
    width 100%
    height 100%
    .echart-bar
      width 100%
      height 100%
</style>
