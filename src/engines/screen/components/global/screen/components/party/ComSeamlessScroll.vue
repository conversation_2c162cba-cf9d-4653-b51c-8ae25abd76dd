<script lang="ts">
// 说明文档
// https://github.com/xfy520/vue3-seamless-scroll
// https://gitee.com/longxinziyan/vue3-seamless-scroll
import { defineTaBuilderCellComponent } from '@/components/global/ta-component/TaBuilder/builder/defineTaBuilderCellComponent';
import { usePreviewClickable } from '@/components/global/ta-component/TaBuilder/builder/usePreview';
import { useScreenDataFetchCollectionInject } from '@/components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';
import { TaBuilderEvent } from '@/components/global/ta-component/TaBuilder/types';
import { VObject } from '@/lib/vails';
import { computed, toRefs } from 'vue';
import { dataPropsFn } from '../echartBaseProps';

const ComSeamlessScroll = defineTaBuilderCellComponent({
  name: 'ComSeamlessScroll',
  components: {},
  props: {
    ...dataPropsFn({ group: '', modelKeyPrefix: '' }, 'props', 'json'),
    columns: {
      label: 'columns',
      formType: 'json',
      type: Array,
      default: [
        {
          dataIndex: 'title',
        },
      ],
    },
    data: {
      type: Array,
      hidden: true,
      default: [
        { title: '市直机关举办“迎亚运 助共富——壬寅迎新春联展”迎新送福活动' },
        { title: '市税务局的这场个人摄影展，带你邂逅最美西湖！' },
      ],
    },
    limitScrollNum: { label: '滚动条数限制', type: Number, default: 5 },
  },
  setup(props: any) {
    const { getScreenData } = useScreenDataFetchCollectionInject(props);
    const value = computed(() => getScreenData(props, '') || props.data);

    const { onClick: onPreviewClick } = usePreviewClickable(props);

    const onClick = (item: VObject) => {
      props.componentSchema?.events?.forEach((event: TaBuilderEvent) => {
        onPreviewClick(event, { ...item, item }, item);
      });
    };

    return {
      ...toRefs(props),
      value,
      onClick,
    };
  },
});
export default ComSeamlessScroll;
</script>

<template lang="pug">
TaSeamlessScroll.h-63.w-57.overflow-hidden(
  :list='value',
  :hover='true',
  :limitScrollNum='limitScrollNum',
  :step='0.8'
)
  table.items.w-full
    tr.text-xl.text-white(v-for='item in value')
      td.title.py-2.text-xl.text-white(
        v-for='column in columns',
        :width='column.width || "100%"',
        @click.stop='() => onClick(item)'
      )
        | {{ item[column.dataIndex] }}
</template>

<style lang="stylus" scoped>
table
  border-collapse separate
  border-spacing 22px 16px
  tr
    .title
      margin-top 16px
      margin-bottom 16px
      background linear-gradient(84.71deg, rgba(105, 180, 243, 0.2) 38.43%, rgba(54, 77, 198, 0.1) 87.66%)
      border-radius 12px
    td
      padding 12px 16px 12px 16px
      &:hover
        font-size 24px
        background linear-gradient(84.71deg, rgba(105, 180, 243, 0.6) 38.43%, rgba(54, 77, 198, 0.3) 87.66%)
</style>
