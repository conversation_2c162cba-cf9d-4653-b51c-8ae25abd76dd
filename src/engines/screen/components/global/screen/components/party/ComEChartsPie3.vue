<script lang="ts">
import echarts from 'echarts';
import { computed, defineComponent, ref, toRefs } from 'vue';
const ComEChartsPie3 = defineComponent({
  name: 'ComEChartsPie3',
  components: {},
  props: {
    pieSeriesData: {
      type: Array,
      default: () => [
        { value: 0, color: '#6ABDF7', name: '机关党员数' },
        { value: 0, color: '#8DD39A', name: '爭业兑员效' },
        { value: 0, color: '#F8B862', name: '国企党员数' },
        { value: 0, color: '#EC6D71', name: '两新党员数' },
        { value: 0, color: '#9270CA', name: '其他党员效' },
      ],
    },
    config: {
      type: Object,
      default: () => ({
        roseType: 'area', //玫瑰图 area || false
        radius: '80%', //可切换成环型图：[内圈百分比，外圈百分比]
        title: {}, //参展 //配置项 title 环型图标题可显示在中间
      }),
    },
  },
  setup(props) {
    const pieDom = ref<any>(null);
    const pie = ref<any>(null);
    const seriesData = computed(() => {
      return props.pieSeriesData;
    });
    const color = computed(() => {
      return props.pieSeriesData.map((item: any) => item.color);
    });
    const option = computed(() => ({
      title: props.config.title,
      grid: {
        top: 10,
        left: 10,
        right: 10,
        bottom: 10,
      },
      tooltip: {
        trigger: 'item',
        formatter: (val: any) => {
          return val.name + ':' + val.value; //标题
        },
      },
      color: color.value,
      legend: {
        orient: 'vertical',
        right: '0px', //图例位置
        top: '20%',
        icon: 'circle',
        textStyle: {
          color: '#fff',
        },
      },
      markLine: {
        show: false,
      },
      series: [
        {
          name: '',
          type: 'pie',
          radius: props.config.radius || '50%',
          roseType: props.config.roseType || false,
          data: seriesData.value,
          label: {
            show: false,
          },
          labelLine: {
            show: false,
          },
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)',
            },
          },
        },
      ],
    }));
    const createChart = () => {
      pie.value = echarts.init(pieDom.value);
      pie.value.setOption(option.value);
    };
    // onMounted(() => {
    //   createChart();
    // });
    return {
      ...toRefs(props),
      pieDom,
      option,
    };
  },
});
export default ComEChartsPie3;
</script>

<template lang="pug">
TaScreenEchartBase.com-e-charts-pie(:options='option')
</template>

<style lang="stylus" scoped>
.com-e-charts-pie
  .echart-pie-main
    width 100%
    height 100%
    .echart-pie
      width 100%
      height 100%
</style>
