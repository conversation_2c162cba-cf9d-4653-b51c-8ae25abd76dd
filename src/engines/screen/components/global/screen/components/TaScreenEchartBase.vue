<script lang="ts">
import {
  ref,
  defineComponent,
  toRefs,
  computed,
  onMounted,
  shallowRef,
  watch,
  onUnmounted,
} from 'vue';
import elementDetectorGenerator from 'element-resize-detector';
import { VObject } from '@/lib/vails';
import 'echarts-liquidfill';

let echarts: any;

if (typeof window !== 'undefined' && typeof (window as any).echarts !== 'undefined') {
  echarts = (window as any).echarts;
} else {
  import('echarts')
    .then(echartsModule => {
      echarts = echartsModule.default || echartsModule;
    })
    .catch(error => {
      console.error('Failed to dynamically import echarts:', error);
    });
}
const TaScreenEchartBase = defineComponent({
  name: 'TaScreenEchartBase',
  components: {},
  props: {
    options: { type: Object, default: () => ({}) },
    hasDevicePixelRatio: {
      type: Boolean,
      default: false,
    },
  },
  setup(props, { emit }) {
    const echart = ref<any>(null);
    const chartRef = shallowRef<any>(null);

    const createChart = () => {
      if (props.hasDevicePixelRatio) {
        chartRef.value = echarts.init(echart.value, null, { devicePixelRatio: 2 });
      } else {
        chartRef.value = echarts.init(echart.value);
      }
      chartRef.value.setOption(props.options);
      chartRef.value.on('click', () => {
        emit('clickCallback', container, chartRef);
      });
    };

    onMounted(() => {
      setTimeout(createChart, 500);
    });

    const container = ref<any>(null);

    onMounted(() => {
      const elementDetector = elementDetectorGenerator({});
      elementDetector.listenTo(container.value, () => {
        chartRef.value?.resize?.();
        emit('pop', 'compileToImage');
      });
    });

    onUnmounted(() => {
      if (chartRef.value) {
        chartRef.value.dispose();
        chartRef.value = null;
      }
    });

    watch(
      () => props.options,
      () => {
        if (chartRef.value) {
          setTimeout(() => {
            chartRef.value.setOption(props.options);
            emit('pop', 'compileToImage');
          });
        }
      },
      { deep: true },
    );

    const mergeOption = (options: VObject, onlyMerge = false) => {
      chartRef.value.setOption(options, onlyMerge);
    };

    const exportToImage = () => {
      const a = document.createElement('a');

      a.href = chartRef.value.getDataURL();
      a.download = '';
      a.click();
    };

    return {
      ...toRefs(props),
      echart,
      chartRef,
      container,
      exportToImage,
      mergeOption,
    };
  },
});
export default TaScreenEchartBase;
</script>

<template lang="pug">
.ta-screen-echart-base.w-full.h-full(ref='container')
  //- a-button(@click='exportToImage') 导出
  .echart.h-full.w-full(ref="echart")
</template>

<style lang="stylus" scoped></style>
