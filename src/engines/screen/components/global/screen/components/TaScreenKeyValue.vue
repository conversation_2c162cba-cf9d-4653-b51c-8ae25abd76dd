<script lang="ts">
import { ref, defineComponent, toRefs, computed, getCurrentInstance } from 'vue';
import { defineTaBuilderCellComponent } from '@/components/global/ta-component/TaBuilder/builder/defineTaBuilderCellComponent';
import { TaTemplateFormItem } from '@/components/global/ta-component/ta-template-form-core/types';
import { useScreenDataFetchCollectionInject } from '@/components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';

import { get } from 'lodash';
import { useScreenDesignerConfigInject } from '@/components/global/ta-component/TaBuilder/builder/useScreenDesignerConfig';

const TaScreenKeyValue = defineTaBuilderCellComponent({
  name: 'TaScreenKeyValue',
  components: {},
  props: {
    label: { label: '标题', type: String, default: '' },
    dataIndex: { label: '数据 key', type: String, default: '' },
    dataSource: { label: '数据源', formType: 'select', type: String, default: '' },
  },
  setup(props: any) {
    const { proxy } = getCurrentInstance() as any;

    const { config, dataSourceSelect } = useScreenDesignerConfigInject();

    const template = computed(() => {
      const result = proxy.__defaultTemplate;
      if (config) {
        const dataSourceSelectItem = result.fields.find(
          (field: TaTemplateFormItem) => field.model_key === 'dataSource',
        );
        dataSourceSelectItem.options.select = dataSourceSelect.value;
      }
      return result;
    });

    const { dataResult } = useScreenDataFetchCollectionInject(props);

    const value = computed(() => {
      if (dataResult && dataResult.value[props.dataSource]) {
        return get(dataResult.value[props.dataSource], props.dataIndex);
      }
      return undefined;
    });

    return {
      ...toRefs(props),
      template,
      value,
    };
  },
});
export default TaScreenKeyValue;
</script>

<template lang="pug">
.ta-screen-key-value.w-full.h-full.bg-light-300
  | 【{{ label }}】  -> {{ value }}
</template>

<style lang="stylus" scoped>
// .ta-chart
</style>
