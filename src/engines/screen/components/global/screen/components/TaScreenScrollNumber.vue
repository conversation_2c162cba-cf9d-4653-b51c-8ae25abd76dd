<script lang="ts">
import { ref, defineComponent, toRefs, computed, watch } from 'vue';
import { defineTaBuilderCellComponent } from '@/components/global/ta-component/TaBuilder/builder/defineTaBuilderCellComponent';
import { VObject } from '@/lib/vails';
import { useScreenDataFetchCollectionInject } from '@/components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';
import { dataPropsFn } from './echartBaseProps';

const TaScreenScrollNumber = defineTaBuilderCellComponent({
  name: 'TaScreenScrollNumber',
  components: {},
  props: {
    number: { label: '值', type: String, default: '0' },
    height: { label: '高度(rem)', formType: 'number', type: Number, default: 6 },
    width: { label: '宽度(rem)', formType: 'number', type: Number, default: 4 },
    space: { label: '间距(rem)', formType: 'number', type: Number, default: 1 },
    ...dataPropsFn({ group: '', modelKeyPrefix: '' }, 'props', 'input'),
  },
  setup(props: any) {
    const { getScreenData } = useScreenDataFetchCollectionInject(props);
    const value = computed(() => getScreenData(props, '') || props.number);

    const configAry = computed(() => {
      const result: VObject[] = [];
      String(value.value)
        .split('')
        .reverse()
        .forEach((str, index) => {
          if ((index + 1) % 3 === 1) {
            result.unshift({ value: ',', bottom: 0 });
          }
          result.unshift({ value: Number(str), top: `-${Number(str) * props.height}rem` });
        });
      result.pop();
      return result;
    });

    const cssHeight = computed(() => `${props.height}rem`);
    const cssFontSize = computed(() => `${props.height * 0.6}rem`);
    const cssWidth = computed(() => `${props.width}rem`);
    const cssSpace = computed(() => `${props.space}rem`);
    const pointBottom = computed(() => `${-props.height * 0.8}rem`);
    const pointLeft = computed(() => `${-props.width * 0.1}rem`);

    return {
      ...toRefs(props),
      configAry,
      cssHeight,
      cssWidth,
      cssSpace,
      cssFontSize,
      pointBottom,
      pointLeft,
    };
  },
});
export default TaScreenScrollNumber;
</script>

<template lang="pug">
.ta-screen-number.size-config.font-bold.text-blue-500
  .flex.items-center.justify-center.w-full.h-full
    .shell.relative.flex-center.width-config(v-for='config in configAry', :style='{ "margin-right": cssSpace }')
      .number.size-config.w-full.relative.overflow-hidden(v-if='typeof config.value === "number"')
        .w-full.flex-col.flex.absolute.transition-all.justify-center.items-center(:style='`top: ${config.top}`')
          .number-unit.size-config.flex-center(v-for='i in "01234567890"') {{ i }}

      .absolute.flex.w-full.flex-col(v-else)
        .point.absolute {{ config.value }}
</template>

<style lang="stylus" scoped>
.number
  background linear-gradient(180deg, rgba(7,37,106,0) 0%, #07256A 100%)
  box-shadow inset 0px 0px 11px 0px #04131E
  border-radius 8px
  border 2px solid
  border-image linear-gradient(180deg, rgba(0, 180, 255, 0.1), rgba(0, 180, 255, 1)) 2 2
.number-unit,
  font-family DINAlternate-Bold, DINAlternate
  font-weight bold
  color #94E9FF

 .point
  color #94E9FF

.size-config
  height v-bind(cssHeight)
  width v-bind(cssWidth)
  font-size v-bind(cssFontSize)

.point
  bottom v-bind(pointBottom)
  left v-bind(pointLeft)
</style>
