<script lang="ts">
import { ref, defineComponent, toRefs, computed } from 'vue';
import { defineTaBuilderCellComponent } from '@/components/global/ta-component/TaBuilder/builder/defineTaBuilderCellComponent';

const TaScreenNumber = defineTaBuilderCellComponent({
  name: 'TaScreenNumber',
  components: {},
  props: {
    number: { label: '值', type: String, default: '0' },
  },
  setup(props: any) {
    const strAry = computed(() => {
      const result: (string | number)[] = [];
      String(props.number)
        .split('')
        .reverse()
        .forEach((str, index) => {
          if ((index + 1) % 3 === 1) {
            result.unshift(',');
          }
          result.unshift(Number(str));
        });
      result.pop();
      return result;
    });

    return {
      ...toRefs(props),
      strAry,
    };
  },
});
export default TaScreenNumber;
</script>

<template lang="pug">
.ta-screen-number.flex.items-center.font-bold.text-3xl.text-blue-500
  .shell.relative.flex-center(v-for='unit in strAry')
    .number(v-if='typeof unit === "number"') {{ unit }}
    //- .absolute.-bottom-4(v-else) {{ unit }}
</template>

<style lang="stylus" scoped></style>
