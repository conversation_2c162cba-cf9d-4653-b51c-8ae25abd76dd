<template lang="pug">
ComBubbleFix(
  v-bind='$props',
  :data='runtimeCheckRule(localData)'
)
</template>
<script lang="ts">
import { toRefs, PropType, computed } from 'vue';
import { defineTaBuilderCellComponent } from '@/components/global/ta-component/TaBuilder/builder/defineTaBuilderCellComponent';
import { dataPropsFn } from '../../../screen/components/echartBaseProps';

import { RankingBarData } from '../../../screen/charts/baseUtils/baseChat';
import { VObject } from '@/lib/vails';
import { useScreenDataFetchCollectionInject } from '@/components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';
import ComBubbleFix from './ComBubbleFix.vue';

const defaultData = [
  {
    seriesName: '气泡排布图',
    seriesData: [
      {
        name: '电子信息',
        value: 1074.31,
        extra: '元',
      },
      {
        name: '先进制造',
        value: 743.48,
        extra: '元',
      },
      {
        name: '电子信息',
        value: 1074.31,
        extra: '元',
      },
      {
        name: '先进制造',
        value: 743.48,
        extra: '元',
      },
      {
        name: '电子信息',
        value: 1074.31,
        extra: '元',
      },
      {
        name: '先进制造',
        value: 743.48,
        extra: '元',
      },
      {
        name: '电子信息',
        value: 1074.31,
        extra: '元',
      },
      {
        name: '先进制造',
        value: 743.48,
        extra: '元',
      },
      {
        name: '电子信息',
        value: 1074.31,
        extra: '元',
      },
      {
        name: '先进制造',
        value: 743.48,
        extra: '元',
      },
    ],
  },
];

export default defineTaBuilderCellComponent({
  name: 'ComBubbleFixDesign',
  components: {
    ComBubbleFix,
  },
  props: {
    ...dataPropsFn({ group: '', modelKeyPrefix: '' }, 'props', 'json', () => defaultData),
  },

  setup(props: VObject) {
    const { getScreenData } = useScreenDataFetchCollectionInject(props);
    const localData = computed(() => {
      return props.data || getScreenData(props, '');
    });
    const runtimeCheckRule = (data: RankingBarData[]) => {
      let flag = true;
      if (data.length > 10) flag = false;
      data.map(line => {
        !Array.isArray(line.seriesData) ? (flag = false) : null;
        return line;
      });
      if (flag) return data;

      return defaultData;
    };
    return {
      ...toRefs(props),
      localData,
      runtimeCheckRule,
    };
  },
});
</script>
<style lang="stylus" scoped></style>
