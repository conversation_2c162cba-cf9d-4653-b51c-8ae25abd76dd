<template lang="pug">
ComSpinCircleBase(
  v-bind='$props',
  :data='runtimeCheckRule(localData)'
)
</template>
<script lang="ts">
import { toRefs, PropType, computed } from 'vue';
import { defineTaBuilderCellComponent } from '@/components/global/ta-component/TaBuilder/builder/defineTaBuilderCellComponent';
import { dataPropsFn } from '../../../screen/components/echartBaseProps';

import { RankingBarData } from '../../../screen/charts/baseUtils/baseChat';
import { VObject } from '@/lib/vails';
import { useScreenDataFetchCollectionInject } from '@/components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';
import ComSpinCircleBase from './ComSpinCircleBase.vue';

const defaultData = [
  {
    seriesName: '专利数',
    seriesData: 1984,
  },
  {
    seriesName: '著作权',
    seriesData: 1984,
  },
  {
    seriesName: '商标数',
    seriesData: 1984,
  },
];

export default defineTaBuilderCellComponent({
  name: 'ComSpinCircleBaseDesign',
  components: {
    ComSpinCircleBase,
  },
  props: {
    ...dataPropsFn({ group: '', modelKeyPrefix: '' }, 'props', 'json', () => defaultData),
    // icon列表
    iconList: {
      formType: 'json',
      label: 'icon地址列表，默认为空',
      type: Array as PropType<string[]>,
      default: () => [],
    },
    // 颜色列表
    customColor: {
      formType: 'json',
      label: '颜色列表，默认为空',
      type: Array as PropType<string[]>,
      default: () => [],
    },
  },

  setup(props: VObject) {
    const { getScreenData } = useScreenDataFetchCollectionInject(props);
    const localData = computed(() => {
      return props.data || getScreenData(props, '');
    });
    const runtimeCheckRule = (data: RankingBarData[]) => {
      let flag = true;
      if (data.length > 6) flag = false;
      data.map(line => {
        Number.isNaN(Number(line.seriesData)) ? (flag = false) : null;
        return line;
      });
      if (flag) return data;

      return defaultData;
    };
    return {
      ...toRefs(props),
      localData,
      runtimeCheckRule,
    };
  },
});
</script>
<style lang="stylus" scoped></style>
