<template lang="pug">
.w-full.h-full
  .flex.flex-col.w-full
    .w-full.flex.flex-row.items-center.mb-3(v-for='(record, index) in finalData')
      .left.text-sm(:style='{ width: `${leftWidth}px` }') {{ record.seriesName }}
      .w-0.flex-grow.px-5
        .w-full.flex.items-center.justify-center
          .h-6.flex.items-center.justify-center.text-sm.text-white(:style='record.v_style') {{ record.seriesData?.value?.toLocaleString() }} {{ unit }}
      .right.text-sm.flex.flex-row.justify-end(
        :style='{ width: `${rightWidth}px` }',
        v-if='isShowRight'
      )
        .text-sm.pr-1 {{ percentName }}
        .text-sm {{ record.p_unit }}{{ record.seriesData.percent }}%
</template>
<script lang="ts">
import { PropType, computed, defineComponent, toRefs } from 'vue';
import { SortListData } from '../../../screen/charts/baseUtils/baseChat';
const ComBaseList = defineComponent({
  name: 'ComBaseList',
  components: {},
  props: {
    data: {
      type: Array as PropType<SortListData[]>,
      default: () => [
        {
          seriesName: '上市企业',
          seriesData: {
            value: 9,
            percent: 2,
          },
        },
        {
          seriesName: '上市企业',
          seriesData: {
            value: 9,
            percent: 2,
          },
        },
        {
          seriesName: '上市企业',
          seriesData: {
            value: 9,
            percent: 2,
          },
        },
        {
          seriesName: '上市企业',
          seriesData: {
            value: 9,
            percent: 2,
          },
        },
        {
          seriesName: '上市企业',
          seriesData: {
            value: 9,
            percent: 2,
          },
        },
        {
          seriesName: '上市企业',
          seriesData: {
            value: 9,
            percent: 2,
          },
        },
      ],
    },
    leftWidth: {
      type: Number,
      default: () => 90,
    },
    rightWidth: {
      type: Number,
      default: () => 70,
    },
    unit: {
      type: String,
      default: () => '家',
    },
    percentName: {
      type: String,
      default: () => '年环比',
    },
    customColors: {
      type: Array as PropType<string[]>,
      default: () => [],
    },
    isShowRight: {
      type: Boolean,
      default: () => true,
    },
  },
  setup(props) {
    const colorlist = [
      '#DE9449',
      '#EB6152',
      '#7F58F5',
      '#55B7EE',
      '#48E5E5',
      '#3CD495',
      '#2B8EF3',
      '#3254DD',
    ];
    const finalData = computed(() => {
      const finalColor = [...props.customColors, ...colorlist];
      const len = props.data.length;
      const vdeps = 90 / len;
      const maxValue = Math.max(...props.data.map(item => item.seriesData.value));

      return [...props.data].map((el: SortListData, index: number) => {
        return {
          ...el,
          color: finalColor[index],
          width: 20 + vdeps * index,
          v_style: `width: ${20 + (el.seriesData.value / maxValue) * 100}%;background-color: ${
            finalColor[index]
          }`,
          p_unit: el.seriesData.percent > 0 ? '+' : '-',
        };
      });
    });
    return {
      ...toRefs(props),
      finalData,
    };
  },
});
export default ComBaseList;
</script>
<style lang="stylus" scoped>
.left, .right
  color #E6F7FF
</style>
