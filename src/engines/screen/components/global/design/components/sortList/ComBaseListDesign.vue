<template lang="pug">
ComBaseList(v-bind='$props', :data='runtimeCheckRule(localData)')
</template>
<script lang="ts">
import { defineTaBuilderCellComponent } from '@/components/global/ta-component/TaBuilder/builder/defineTaBuilderCellComponent';
import { PropType, computed, toRefs } from 'vue';
import { dataPropsFn } from '../../../screen/components/echartBaseProps';

import { useScreenDataFetchCollectionInject } from '@/components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';
import { VObject } from '@/lib/vails';
import { SortListData } from '../../../screen/charts/baseUtils/baseChat';
import ComBaseList from './ComBaseList.vue';

const defaultData = [
  {
    seriesName: '上市企业',
    seriesData: {
      value: 9,
      percent: 2,
    },
  },
  {
    seriesName: '上市企业',
    seriesData: {
      value: 9,
      percent: 2,
    },
  },
  {
    seriesName: '上市企业',
    seriesData: {
      value: 9,
      percent: 2,
    },
  },
  {
    seriesName: '上市企业',
    seriesData: {
      value: 9,
      percent: 2,
    },
  },
  {
    seriesName: '上市企业',
    seriesData: {
      value: 9,
      percent: 2,
    },
  },
  {
    seriesName: '上市企业',
    seriesData: {
      value: 9,
      percent: 2,
    },
  },
];

export default defineTaBuilderCellComponent({
  name: 'ComBaseListDesign',
  components: {
    ComBaseList,
  },
  props: {
    ...dataPropsFn({ group: '', modelKeyPrefix: '' }, 'props', 'json', () => defaultData),
    // icon列表
    leftWidth: {
      type: Number,
      formType: 'number',
      label: '左侧宽度',
      default: 90,
    },
    rightWidth: {
      type: Number,
      formType: 'number',
      label: '右侧宽度',
      default: 70,
    },
    unit: {
      type: String,
      formType: 'input',
      label: '单位',
      default: '家',
    },
    percentName: {
      type: String,
      formType: 'input',
      label: '右侧单位',
      default: '年环比',
    },
    // 颜色列表
    customColors: {
      formType: 'json',
      label: '颜色列表，默认为空',
      type: Array as PropType<string[]>,
      default: () => [],
    },
    isShowRight: {
      formType: 'switch',
      label: '是否展示右侧',
      type: Boolean,
      default: () => true,
    },
  },

  setup(props: VObject) {
    const { getScreenData } = useScreenDataFetchCollectionInject(props);
    const localData = computed(() => {
      return props.data || getScreenData(props, '');
    });
    const runtimeCheckRule = (data: SortListData[]) => {
      let flag = true;
      if (data.length > 6) flag = false;
      data.map(line => {
        Number.isNaN(Number(line.seriesData.value)) ? (flag = false) : null;
        return line;
      });
      if (flag) return data;

      return defaultData;
    };
    return {
      ...toRefs(props),
      localData,
      runtimeCheckRule,
    };
  },
});
</script>
<style lang="stylus" scoped></style>
