<template lang="pug">
ComFunnelFix(
  v-bind='$props',
  :data='runtimeCheckRule(localData)'
)
</template>
<script lang="ts">
import { toRefs, PropType, computed } from 'vue';
import { defineTaBuilderCellComponent } from '@/components/global/ta-component/TaBuilder/builder/defineTaBuilderCellComponent';
import { dataPropsFn } from '../../../screen/components/echartBaseProps';

import { RankingBarData } from '../../../screen/charts/baseUtils/baseChat';
import { VObject } from '@/lib/vails';
import { useScreenDataFetchCollectionInject } from '@/components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';
import ComFunnelFix from './ComFunnelFix.vue';

const defaultData = [
  {
    seriesName: '漏斗排布图',
    seriesData: [
      {
        name: '对接中的需求',
        value: 7024,
        extra: '个',
      },
      {
        name: '投递方案',
        value: 8231,
        extra: '个',
        x: 50,
        y: 125,
      },
      {
        name: '意向投入金额',
        value: '3,385.20',
        extra: '万元',
        x: 250,
        y: 125,
      },
      {
        name: '意向对接',
        value: 231,
        extra: '个',
        x: 90,
        y: 260,
      },
      {
        name: '对接次数',
        value: 267,
        extra: '次',
        x: 280,
        y: 260,
      },
      {
        name: '签约',
        value: 45,
        extra: '个',
        x: 100,
        y: 395,
      },
      {
        name: '签约金额',
        value: '283,948',
        extra: '元',
        x: 250,
        y: 395,
      },
    ],
  },
  {
    seriesName: '著作权',
    seriesData: 1984,
  },
  {
    seriesName: '商标数',
    seriesData: 1984,
  },
];

export default defineTaBuilderCellComponent({
  name: 'ComFunnelFixDesign',
  components: {
    ComFunnelFix,
  },
  props: {
    ...dataPropsFn({ group: '', modelKeyPrefix: '' }, 'props', 'json', () => defaultData),
  },

  setup(props: VObject) {
    const { getScreenData } = useScreenDataFetchCollectionInject(props);
    const localData = computed(() => {
      return props.data || getScreenData(props, '');
    });
    const runtimeCheckRule = (data: RankingBarData[]) => {
      let flag = true;
      data.map(line => {
        Number.isNaN(Number(line.seriesData)) ? (flag = false) : null;
        return line;
      });
      if (flag) return data;

      return defaultData;
    };
    return {
      ...toRefs(props),
      localData,
      runtimeCheckRule,
    };
  },
});
</script>
<style lang="stylus" scoped></style>
