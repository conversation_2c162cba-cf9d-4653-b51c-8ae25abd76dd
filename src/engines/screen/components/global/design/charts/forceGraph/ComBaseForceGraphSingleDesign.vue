<template lang="pug">
.com_base_force_graph_single_design.w-full.h-full
  ComBaseForceGraphSingle(
    v-bind='$props',
    :data='runtimeCheckRule(localData)'
  )
</template>
<!-- 半环形图 -->
<script lang="ts">
import { PropType, computed, toRefs } from 'vue';
import { defineTaBuilderCellComponent } from '@/components/global/ta-component/TaBuilder/builder/defineTaBuilderCellComponent';
import ComBaseForceGraphSingle from '../../../screen/charts/ForceGraph/ComBaseForceGraphSingle.vue';
import { ForceGraphData, forceGraphLineData } from '../../../screen/charts/baseUtils/baseChat';
import { dataPropsFn } from '../../../screen/components/echartBaseProps';
import { useScreenDataFetchCollectionInject } from '@/components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';

const defaultData = [
  {
    seriesName: '各区流向图多对一',
    seriesData: forceGraphLineData,
  },
];

const ComBaseForceGraphSingleDesign = defineTaBuilderCellComponent({
  name: 'ComBaseForceGraphSingleDesign',
  components: {
    ComBaseForceGraphSingle,
  },
  props: {
    ...dataPropsFn({ group: '', modelKeyPrefix: '' }, 'props', 'json', () => defaultData),
    pointName: {
      label: '节点名称',
      formType: 'json',
      type: Array as PropType<string[]>,
      default: () => [
        '黄浦区',
        '徐汇区',
        '长宁区',
        '静安区',
        '普陀区',
        '虹口区',
        '杨浦区',
        '闵行区',
        '宝山区',
        '嘉定区',
        '浦东新区',
        '金山区',
        '松江区',
        '青浦区',
        '奉贤区',
        '崇明区',
      ],
    },
    curPoint: {
      label: '当前输入的节点',
      formType: 'input',
      type: String,
      default: () => '黄浦区',
    },
  },
  setup(props: any) {
    const { getScreenData } = useScreenDataFetchCollectionInject(props);
    const localData = computed(() => {
      return props.data || getScreenData(props, '');
    });
    const runtimeCheckRule = (data: ForceGraphData[]) => {
      let flag = true;
      data.map(line => {
        !Array.isArray(line.seriesData) ? (flag = false) : null;
        return line;
      });
      if (flag) return data;

      return defaultData;
    };
    return {
      ...toRefs(props),
      localData,
      runtimeCheckRule,
    };
  },
});
export default ComBaseForceGraphSingleDesign;
</script>
<style lang="stylus" scoped></style>
