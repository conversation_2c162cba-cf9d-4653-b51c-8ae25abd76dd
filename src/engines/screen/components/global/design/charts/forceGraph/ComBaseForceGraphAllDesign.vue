<template lang="pug">
.com_base_force_graph_all_design.w-full.h-full
  ComBaseForceGraphAll(
    v-bind='$props',
    :data='runtimeCheckRule(localData)'
  )
</template>
<!-- 半环形图 -->
<script lang="ts">
import { PropType, computed, toRefs } from 'vue';
import { defineTaBuilderCellComponent } from '@/components/global/ta-component/TaBuilder/builder/defineTaBuilderCellComponent';
import ComBaseForceGraphAll from '../../../screen/charts/ForceGraph/ComBaseForceGraphAll.vue';
import {
  ForceGraphLineLevelData,
  ForceGraphData,
  forceGraphLineData,
} from '../../../screen/charts/baseUtils/baseChat';
import { dataPropsFn } from '../../../screen/components/echartBaseProps';
import { useScreenDataFetchCollectionInject } from '@/components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';

const defaultData = [
  {
    seriesName: '各区流向图',
    seriesData: forceGraphLineData,
  },
];

const ComBaseForceGraphAllDesign = defineTaBuilderCellComponent({
  name: 'ComBaseForceGraphAllDesign',
  components: {
    ComBaseForceGraphAll,
  },
  props: {
    ...dataPropsFn({ group: '', modelKeyPrefix: '' }, 'props', 'json', () => defaultData),

    pointName: {
      label: '节点名称',
      formType: 'json',
      type: Array as PropType<string[]>,
      default: () => [
        '黄浦区',
        '徐汇区',
        '长宁区',
        '静安区',
        '普陀区',
        '虹口区',
        '杨浦区',
        '闵行区',
        '宝山区',
        '嘉定区',
        '浦东新区',
        '金山区',
        '松江区',
        '青浦区',
        '奉贤区',
        '崇明区',
      ],
    },
    lineLevel: {
      label: '区间范围界定',
      formType: 'json',
      type: Array as PropType<ForceGraphLineLevelData[]>,
      default: () => [
        {
          gt: 7,
          lte: 100,
          label: '7 - 100人次',
        },
        {
          gt: 100,
          lte: 200,
          label: '100 - 200人次',
        },
        {
          gt: 200,
          lte: 300,
          label: '200 - 300人次',
        },
        {
          gt: 300,
          lte: 477,
          label: '300 - 477人次',
        },
      ],
    },
    customizeLineColor: {
      type: Array as PropType<string[]>,
      formType: 'json',
      label: '自定义填充力图“线”的颜色，按区间顺序,默认则为空数组',
      default: () => [],
    },
    customizeAreaColor: {
      type: Array as PropType<string[]>,
      formType: 'json',
      label: '自定义填充力图“图例线”的颜色，按区间顺序,默认则为空数组',
      default: () => [],
    },
    graphType: {
      type: String as PropType<'round' | 'star'>,
      default: () => 'star',
      formType: 'radio',
      label: '力图形状是圆形还是星形',
      select: [
        { label: '圆形', value: 'round' },
        { label: '星形', value: 'star' },
      ],
    },
  },
  setup(props: any) {
    const { getScreenData } = useScreenDataFetchCollectionInject(props);
    const localData = computed(() => {
      return props.data || getScreenData(props, '');
    });
    const runtimeCheckRule = (data: ForceGraphData[]) => {
      let flag = true;
      data.map(line => {
        !Array.isArray(line.seriesData) ? (flag = false) : null;
        return line;
      });
      if (flag) return data;

      return defaultData;
    };
    return {
      ...toRefs(props),
      localData,
      runtimeCheckRule,
    };
  },
});
export default ComBaseForceGraphAllDesign;
</script>
<style lang="stylus" scoped></style>
