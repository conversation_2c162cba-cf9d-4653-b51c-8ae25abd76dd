<template lang="pug">
.com_base_sankey_design.w-full.h-full
  ComBaseVerticalAddWaterFall(
    v-bind='$props',
    :data='runtimeCheckRule(localData)',
    negativeLegendName=''
  )
</template>
<!-- 垂直组成瀑布图 -->
<script lang="ts">
import { PropType, computed, toRefs } from 'vue';
import { defineTaBuilderCellComponent } from '@/components/global/ta-component/TaBuilder/builder/defineTaBuilderCellComponent';
import { barAreaColor, SankeyData } from '../../../screen/charts/baseUtils/baseChat';
import ComBaseSankey from '../../../screen/charts/Sankey/ComBaseSankey.vue';
import { dataPropsFn } from '../../../screen/components/echartBaseProps';
import { useScreenDataFetchCollectionInject } from '@/components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';

const defaultData = [
  {
    seriesName: 'waterfall-1',
    seriesData: [200, 120, 150, 80, 70, 110],
  },
];

const ComBaseVerticalAddWaterFallDesign = defineTaBuilderCellComponent({
  name: 'ComBaseVerticalAddWaterFallDesign',
  components: {
    ComBaseSankey,
  },
  props: {
    ...dataPropsFn({ group: '', modelKeyPrefix: '' }, 'props', 'json', () => defaultData),

    barAreaColor: {
      label: '颜色序列',
      formType: 'json',
      type: Array as PropType<string[]>,
      default: () => barAreaColor,
    },
    chartType: {
      label: '图表方向',
      formType: 'radio',
      type: String as PropType<'horizontal' | 'vertical'>,
      select: [
        { label: '垂直', value: 'vertical' },
        { label: '水平', value: 'horizontal' },
      ],
      default: () => 'vertical',
    },
    showLengend: { type: Boolean, formType: 'switch', label: '是否显示图例', default: true },
    // 图例位置 top right bottom right
    lengendPosition: {
      type: Array,
      formType: 'json',
      label: '图例位置[上, 右，下，左]',
      default: () => ['top', 0, 0, 'center'],
    },
    // 图位置 top right bottom right
    gridPosition: {
      type: Array,
      formType: 'json',
      label: 'grid位置[上, 右，下，左]',
      default: () => ['10%', '10%', '10%', '10%'],
    },
    // X轴显示名称，为空则不显示X轴标
    axisLabel: {
      type: Array as PropType<string[]>,
      formType: 'json',
      label: 'X轴显示名称，为空则不显示X轴标',
      default: () => ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
    },
    // 瀑布图总量名称
    totalAxisLabelName: {
      type: String,
      default: '总量',
      formType: 'input',
      label: '瀑布图总量名称',
    },
    // 数据反转
    reverseChart: {
      type: Boolean,
      default: false,
      formType: 'switch',
      label: '数据反转',
    }, // 是否显示单位
    showUnit: { type: Boolean, default: false, formType: 'switch', label: '是否显示单位' },
    // 主轴单位
    unit: { formType: 'input', label: '主轴单位', type: String, default: '' },
    // 主轴单位位置
    unitPadding: {
      formType: 'json',
      label: '主轴单位位置[上, 右，下，左]',
      type: Array as PropType<number[]>,
      default: () => [0, 0, 0, 0],
      // 是否要展示柱状图背景
    },
    unitTitle: {
      type: String,
      default: '单位',
      formType: 'input',
      label: '主轴单位标题',
    },
    // 是否要显示数据点标注
    showLabel: {
      type: Boolean,
      default: false,
      formType: 'switch',
      label: '是否要显示数据点标注',
    },
    // 柱状图宽度
    barWidth: {
      type: String || Number,
      default: '',
      formType: 'number',
      label: '柱状图宽度',
    },
    // 柱条单种颜色or多种颜色or变化图正副值两种颜色
    colorType: {
      label: '柱条单种颜色or多种颜色or变化图正副值两种颜色',

      type: String as PropType<'single' | 'multiple' | 'doubleChange'>,
      default: 'single',
      formType: 'radio',
      select: [
        { label: '单色', value: 'single' },
        { label: '多色', value: 'multiple' },
        { label: '正负双色', value: 'doubleChange' },
      ],
    },
    // 总量柱颜色
    totalAreaColor: {
      type: String,
      default: '#FF70CF',
      formType: 'input',
      label: '总量柱颜色',
    },
    // 是否要展示tooltip
    showTootip: {
      type: Boolean,
      default: true,
      formType: 'switch',
      label: '是否要展示tooltip',
    },
    // 正值图例名
    positiveLegendName: {
      type: String,
      default: '子项',
      formType: 'input',
      label: '正值图例名',
    },
    // 总量图例名
    totalLegendName: {
      type: String,
      default: '总项',
      formType: 'input',
      label: '总量图例名',
    },
    // 图例标题
    title: {
      formType: 'input',
      label: '图例标题',
      type: String,
      default: '',
    },
    // 图例标题布局
    titlePadding: {
      type: Array as PropType<number[]>,
      formType: 'json',
      label: '图例标题[上, 右，下，左]',
      default: () => [0, 0, 0, 0],
    },
  },
  setup(props: any) {
    const { getScreenData } = useScreenDataFetchCollectionInject(props);
    const localData = computed(() => {
      return props.data || getScreenData(props, '');
    });
    const runtimeCheckRule = (data: SankeyData) => {
      const flag = true;

      // !Array.isArray(data.seriesData) ? (flag = false) : null;
      if (flag) return data;

      return defaultData;
    };
    return {
      ...toRefs(props),
      runtimeCheckRule,
      localData,
    };
  },
});
export default ComBaseVerticalAddWaterFallDesign;
</script>
<style lang="stylus" scoped></style>
