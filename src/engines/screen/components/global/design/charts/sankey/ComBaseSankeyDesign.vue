<template lang="pug">
.com_base_sankey_design.w-full.h-full
  ComBaseSankey(
    v-bind='$props',
    :data='runtimeCheckRule(localData)',

  )
</template>
<!-- 半环形图 -->
<script lang="ts">
import { PropType, computed, toRefs } from 'vue';
import { defineTaBuilderCellComponent } from '@/components/global/ta-component/TaBuilder/builder/defineTaBuilderCellComponent';
import { SankeyData } from '../../../screen/charts/baseUtils/baseChat';
import ComBaseSankey from '../../../screen/charts/Sankey/ComBaseSankey.vue';
import { dataPropsFn } from '../../../screen/components/echartBaseProps';
import { useScreenDataFetchCollectionInject } from '@/components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';

const defaultData = [
  {
    seriesName: 'sankey-1',
    seriesData: {
      data: [
        {
          name: 'a',
        },
        {
          name: 'b',
        },
        {
          name: 'a1',
        },
        {
          name: 'a2',
        },
        {
          name: 'b1',
        },
        {
          name: 'c',
        },
      ],
      links: [
        {
          source: 'a',
          target: 'a1',
          value: 5,
        },
        {
          source: 'a',
          target: 'a2',
          value: 3,
        },
        {
          source: 'b',
          target: 'b1',
          value: 8,
        },
        {
          source: 'a',
          target: 'b1',
          value: 3,
        },
        {
          source: 'b1',
          target: 'a1',
          value: 1,
        },
        {
          source: 'b1',
          target: 'c',
          value: 2,
        },
      ],
    },
  },
];

const ComBaseSankeyDesign = defineTaBuilderCellComponent({
  name: 'ComBaseSankeyDesign',
  components: {
    ComBaseSankey,
  },
  props: {
    ...dataPropsFn({ group: '', modelKeyPrefix: '' }, 'props', 'json', () => defaultData),

    colorSet: {
      label: '颜色序列',
      formType: 'json',
      type: Array as PropType<string[]>,
      default: () => [
        // '#67001f',
        // '#b2182b',
        // '#d6604d',
        // '#f4a582',
        // '#fddbc7',
        // '#d1e5f0',
        // '#92c5de',
        // '#4393c3',
        // '#2166ac',
        // '#053061',
      ],
    },
    direction: {
      label: '图表方向',
      formType: 'radio',
      type: String as PropType<'horizontal' | 'vertical'>,
      select: [
        { label: '水平', value: 'horizontal' },
        { label: '垂直', value: 'vertical' },
      ],
      default: () => 'horizontal',
    },
    gridPosition: {
      label: '图例位置',
      formType: 'json',
      type: Array as PropType<string[]>,
      default: () => ['5%', '20%', '5%', '5%'],
    },
  },
  setup(props: any) {
    const { getScreenData } = useScreenDataFetchCollectionInject(props);
    const localData = computed(() => {
      return props.data || getScreenData(props, '');
    });
    const runtimeCheckRule = (data: SankeyData) => {
      const flag = true;

      // !Array.isArray(data.seriesData) ? (flag = false) : null;
      if (flag) return data;

      return defaultData;
    };
    return {
      ...toRefs(props),
      runtimeCheckRule,
      localData,
    };
  },
});
export default ComBaseSankeyDesign;
</script>
<style lang="stylus" scoped></style>
