<template lang="pug">
.com_ranking_bar_chart.w-full.h-full
  ComBaseRankingBar(
    v-bind='$props',
    :data='runtimeCheckRule(localData)'
  )
</template>
<!-- 半环形图 -->
<script lang="ts">
import { computed, toRefs } from 'vue';
import { RankingBarData } from '@/engines/screen/components/global/screen/charts/baseUtils/baseChat';
import { defineTaBuilderCellComponent } from '@/components/global/ta-component/TaBuilder/builder/defineTaBuilderCellComponent';
import { dataPropsFn } from '../../../screen/components/echartBaseProps';
import { useScreenDataFetchCollectionInject } from '@/components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';
import ComBaseRankingBar from '../../../screen/charts/Rankiing/ComBaseRankingBar.vue';

const defaultData = [
  {
    seriesName: '2018',
    seriesData: 12123,
  },
  {
    seriesName: '2019',
    seriesData: 13242,
  },
  {
    seriesName: '2020',
    seriesData: 42314,
  },
  {
    seriesName: '2021',
    seriesData: 4231,
  },
  {
    seriesName: '2022',
    seriesData: 412312,
  },
];

const ComBaseRankingBarDesign = defineTaBuilderCellComponent({
  name: 'ComBaseRankingBarDesign',
  components: {
    ComBaseRankingBar,
  },
  props: {
    ...dataPropsFn({ group: '', modelKeyPrefix: '' }, 'props', 'json', () => defaultData),
    unit: {
      type: String,
      label: '单位',
      formType: 'input',
      default: () => '',
    },
    showType: {
      formType: 'radio',
      label: '显示样式',
      select: [
        { label: '样式一', value: 'type1' },
        { label: '样式二', value: 'type2' },
        { label: '样式三', value: 'type3' },
        { label: '样式四', value: 'type4' },
        { label: '样式五', value: 'type5' },
        { label: '样式六', value: 'type6' },
      ],
      type: String,
      default: () => 'type2',
    },
    serierMax: {
      type: Number,
      formType: 'number',
      label: '最大值',
      default: 0,
    },
    labelWidth: {
      type: Number,
      formType: 'number',
      label: '标签宽度',
      default: 80,
    },
    valueWidth: {
      type: Number,
      formType: 'number',
      label: '值宽度',
      default: 60,
    },
  },
  setup(props: any) {
    const { getScreenData } = useScreenDataFetchCollectionInject(props);
    const localData = computed(() => {
      return props.data || getScreenData(props, '');
    });
    const runtimeCheckRule = (data: RankingBarData[]) => {
      let flag = true;
      data.map(line => {
        !Array.isArray(line.seriesData) && typeof line.seriesData !== 'number' ? (flag = false) : null;
        return line;
      });
      if (flag) return data;

      return defaultData;
    };
    return {
      ...toRefs(props),
      localData,
      runtimeCheckRule,
    };
  },
});
export default ComBaseRankingBarDesign;
</script>
<style lang="stylus" scoped></style>
