<template lang="pug">
.com_base_radar.w-full.h-full
  ComBaseAreaRadar(
    v-bind='$props',
    :data='runtimeCheckRule(localData)'
  )
</template>
<!-- 半环形图 -->
<script lang="ts">
import { PropType, computed, toRefs } from 'vue';
import {
  BaseData,
  RadarMax,
} from '@/engines/screen/components/global/screen/charts/baseUtils/baseChat';
import { defineTaBuilderCellComponent } from '@/components/global/ta-component/TaBuilder/builder/defineTaBuilderCellComponent';
import ComBaseAreaRadar from '../../../screen/charts/Radar/ComBaseAreaRadar.vue';
import { dataPropsFn } from '../../../screen/components/echartBaseProps';
import { useScreenDataFetchCollectionInject } from '@/components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';

const defaultData = [
  {
    seriesName: '2018',
    seriesData: [954, 630, 255, 427, 732, 839, 300],
  },
  {
    seriesName: '2019',
    seriesData: [954, 630, 255, 427, 732, 839, 300].reverse(),
  },
];

const ComBaseAreaRadarDesign = defineTaBuilderCellComponent({
  name: 'ComBaseAreaRadarDesign',
  components: {
    ComBaseAreaRadar,
  },
  props: {
    ...dataPropsFn({ group: '', modelKeyPrefix: '' }, 'props', 'json', () => defaultData),

    customizeAreaColor: {
      type: Array as PropType<string[]>,
      formType: 'json',
      label: '自定义填充区域颜色序列,默认则为空数组',
      default: () => [],
    },
    radarMax: {
      label: '雷达图各维度属性名称和最大值',
      formType: 'json',
      type: Array as PropType<RadarMax[]>,
      default: () => [
        { name: '星期一', max: 1000 },
        { name: '星期二', max: 1000 },
        { name: '星期三', max: 1000 },
        { name: '星期四', max: 1000 },
        { name: '星期五', max: 1000 },
        { name: '星期六', max: 1000 },
        { name: '星期天', max: 1000 },
      ],
    },
    showSingleAxis: {
      type: Boolean,
      formType: 'switch',
      label: '是否显示雷达图首项刻度线和标签值',
      default: () => true,
    },
    showTooltip: {
      type: Boolean,
      formType: 'switch',
      label: '是否展示tooltip提示',
      default: () => false,
    },
    showLengend: { type: Boolean, formType: 'switch', label: '是否展示图例', default: false },
    legendPosition: {
      type: Array,
      label: '图例位置',
      formType: 'json',
      default: () => ['bottom', 0, 0, 'center'],
    },
    radius: {
      type: String || Number,
      label: '雷达图大小',
      formType: 'input',
      default: () => '75%',
    },
    nameGap: {
      type: Number,
      label: '雷达图指示器名称和指示器轴的距离',
      formType: 'number',
      default: () => 15,
    },
    center: {
      type: Array,
      label: '雷达图位置：中心（圆心）坐标，数组的第一项是横坐标，第二项是纵坐标。',
      formType: 'json',
      default: () => ['50%', '50%'],
    },
  },
  setup(props: any) {
    const { getScreenData } = useScreenDataFetchCollectionInject(props);
    const localData = computed(() => {
      return props.data || getScreenData(props, '');
    });
    const runtimeCheckRule = (data: BaseData[]) => {
      let flag = true;
      data.map(line => {
        !Array.isArray(line.seriesData) ? (flag = false) : null;
        return line;
      });
      if (flag) return data;

      return defaultData;
    };
    return {
      ...toRefs(props),
      localData,
      runtimeCheckRule,
    };
  },
});
export default ComBaseAreaRadarDesign;
</script>
<style lang="stylus" scoped></style>
