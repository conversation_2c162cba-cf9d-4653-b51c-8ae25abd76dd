<template lang="pug">
.com_base_radar.w-full.h-full
  ComBaseRadar(
    v-bind='$props',
    :data='runtimeCheckRule(localData)'

  )
</template>
<!-- 半环形图 -->
<script lang="ts">
import { PropType, computed, toRefs } from 'vue';
import {
  BaseData,
  RadarMax,
} from '@/engines/screen/components/global/screen/charts/baseUtils/baseChat';
import { defineTaBuilderCellComponent } from '@/components/global/ta-component/TaBuilder/builder/defineTaBuilderCellComponent';
import ComBaseRadar from '../../../screen/charts/Radar/ComBaseRadar.vue';
import { dataPropsFn } from '../../../screen/components/echartBaseProps';
import { useScreenDataFetchCollectionInject } from '@/components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';

const defaultData = [
  {
    seriesName: '2018',
    seriesData: [100, 33, 25, 42],
  },
  {
    seriesName: '2019',
    seriesData: [100, 33, 25, 42].reverse(),
  },
  {
    seriesName: '2020',
    seriesData: [53, 33, 51, 154],
  },
  {
    seriesName: '2021',
    seriesData: [34, 83, 35, 126],
  },
  {
    seriesName: '2022',
    seriesData: [34, 83, 35, 126].reverse(),
  },
];

const ComBaseRadarDesign = defineTaBuilderCellComponent({
  name: 'ComBaseRadarDesign',
  components: {
    ComBaseRadar,
  },
  props: {
    ...dataPropsFn({ group: '', modelKeyPrefix: '' }, 'props', 'json', () => defaultData),

    radarMax: {
      label: '雷达图各维度属性名称和最大值',
      formType: 'json',
      type: Array as PropType<RadarMax[]>,
      default: () => [
        { name: '企业集聚力', max: 200 },
        { name: '成果辐射力', max: 200 },
        { name: '要素流动力', max: 200 },
        { name: '环境吸引力', max: 200 },
      ],
    },
    showWithNum: {
      type: Boolean,
      formType: 'switch',
      label: '是否展示各维度详细数据',
      default: () => true,
    },
    showTooltip: {
      type: Boolean,
      formType: 'switch',
      label: '是否展示tooltip提示',
      default: () => true,
    },
    showLengend: { type: Boolean, formType: 'switch', label: '是否展示图例', default: true },
    legendPosition: {
      type: Array,
      label: '图例位置',
      formType: 'json',
      default: () => ['bottom', 0, 0, 'center'],
    },
    radius: {
      type: String || Number,
      label: '雷达图大小',
      formType: 'input',
      default: () => '55%',
    },
    nameGap: {
      type: Number,
      label: '雷达图指示器名称和指示器轴的距离',
      formType: 'number',
      default: () => 5,
    },
    center: {
      type: Array,
      label: '雷达图位置：中心（圆心）坐标，数组的第一项是横坐标，第二项是纵坐标。',
      formType: 'json',
      default: () => ['50%', '50%'],
    },
  },
  setup(props: any) {
    const { getScreenData } = useScreenDataFetchCollectionInject(props);
    const localData = computed(() => {
      return props.data || getScreenData(props, '');
    });
    const runtimeCheckRule = (data: BaseData[]) => {
      let flag = true;
      data.map(line => {
        !Array.isArray(line.seriesData) ? (flag = false) : null;
        return line;
      });
      if (flag) return data;

      return defaultData;
    };
    return {
      ...toRefs(props),
      localData,
      runtimeCheckRule,
    };
  },
});
export default ComBaseRadarDesign;
</script>
<style lang="stylus" scoped></style>
