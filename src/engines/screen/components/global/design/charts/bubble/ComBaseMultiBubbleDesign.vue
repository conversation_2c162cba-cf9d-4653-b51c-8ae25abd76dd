<template lang="pug">
.w-full.h-full
  ComBaseMultiBubble(
    v-bind='$props',
    :data='runtimeCheckRule(localData)'
  )
</template>
<!-- 基础面积图 -->
<script lang="ts">
import { PropType, computed, toRefs } from 'vue';
import { VScatterData } from '../../../screen/charts/baseUtils/baseChat';
import { defineTaBuilderCellComponent } from '@/components/global/ta-component/TaBuilder/builder/defineTaBuilderCellComponent';
import { dataPropsFn } from '../../../screen/components/echartBaseProps';
import { useScreenDataFetchCollectionInject } from '@/components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';
import { VObject } from '@/lib/vails';
import ComBaseMultiBubble from '@/engines/screen/components/global/screen/charts/Bubble/ComBaseMultiBubble.vue';

const defaultData = [
  {
    seriesName: 'line-1',
    seriesData: [
      {
        value: [10, 8.04],
        size: 48.70603132833804,
      },
      {
        value: [8.07, 6.95],
        size: 45.22563391833423,
      },
      {
        value: [13, 7.58],
        size: 44.57120287457246,
      },
      {
        value: [9.05, 8.81],
        size: 39.84461314351828,
      },
      {
        value: [11, 8.33],
        size: 97.04860357659439,
      },
      {
        value: [14, 7.66],
        size: 30.67929525730171,
      },
      {
        value: [13.4, 6.81],
        size: 46.05951025476611,
      },
      {
        value: [10, 6.33],
        size: 77.18306775038317,
      },
      {
        value: [14, 8.96],
        size: 27.247809886189312,
      },
      {
        value: [12.5, 6.82],
        size: 11.968846570386704,
      },
      {
        value: [9.15, 7.2],
        size: 83.42133233924504,
      },
      {
        value: [11.5, 7.2],
        size: 81.04113360670733,
      },
      {
        value: [3.03, 4.23],
        size: 48.12878825718811,
      },
      {
        value: [12.2, 7.83],
        size: 22.64673177763392,
      },
      {
        value: [2.02, 4.47],
        size: 52.68753488435409,
      },
      {
        value: [1.05, 3.33],
        size: 30.063056383880028,
      },
      {
        value: [4.05, 4.96],
        size: 60.61004778572525,
      },
      {
        value: [6.03, 7.24],
        size: 88.26257242705032,
      },
    ],
  },
  {
    seriesName: 'line-2',
    seriesData: [
      {
        value: [8.04, 10],
        size: 48.70603132833804,
      },
      {
        value: [6.95, 8.07],
        size: 45.22563391833423,
      },
      {
        value: [7.58, 13],
        size: 44.57120287457246,
      },
      {
        value: [8.81, 9.05],
        size: 39.84461314351828,
      },
      {
        value: [8.33, 11],
        size: 97.04860357659439,
      },
      {
        value: [7.66, 14],
        size: 30.67929525730171,
      },
      {
        value: [6.81, 13.4],
        size: 46.05951025476611,
      },
      {
        value: [6.33, 10],
        size: 77.18306775038317,
      },
      {
        value: [8.96, 14],
        size: 27.247809886189312,
      },
      {
        value: [6.82, 12.5],
        size: 11.968846570386704,
      },
      {
        value: [7.2, 9.15],
        size: 83.42133233924504,
      },
      {
        value: [7.2, 11.5],
        size: 81.04113360670733,
      },
      {
        value: [4.23, 3.03],
        size: 48.12878825718811,
      },
      {
        value: [7.83, 12.2],
        size: 22.64673177763392,
      },
      {
        value: [4.47, 2.02],
        size: 52.68753488435409,
      },
      {
        value: [3.33, 1.05],
        size: 30.063056383880028,
      },
      {
        value: [4.96, 4.05],
        size: 60.61004778572525,
      },
      {
        value: [7.24, 6.03],
        size: 88.26257242705032,
      },
    ],
  },
];

const ComBaseMultiBubbleDesign = defineTaBuilderCellComponent({
  name: 'ComBaseMultiBubbleDesign',
  components: {
    ComBaseMultiBubble,
  },
  props: {
    ...dataPropsFn({ group: '', modelKeyPrefix: '' }, 'props', 'json', () => defaultData),

    unit: {
      formType: 'input',
      label: '显示单位',
      type: String,
      default: '',
    },
    unitPadding: {
      formType: 'json',
      label: '主轴单位位置[上, 右，下，左]',
      type: Array as PropType<number[]>,
      default: () => [0, 0, 0, 0],
    },
    showLengend: {
      type: Boolean,
      formType: 'switch',
      label: '是否显示图例',
      default: false,
    },
    lengendPosition: {
      formType: 'json',
      label: '图例位置[上, 右，下，左]',
      type: Array as PropType<number[]>,
      default: () => [4, 0, 0, 'right'],
    },
    customizeAreaColor: {
      type: Array as PropType<string[]>,
      formType: 'json',
      label: '自定义点颜色序列',
      default: () => [],
    },
    minSize: {
      type: Number,
      default: 3,
      formType: 'number',
      label: '节点最小值',
    },
    maxSize: {
      type: Number,
      default: 64,
      formType: 'number',
      label: '节点最大值',
    },
    showLabel: {
      type: Boolean,
      label: '是否显示label标签',
      formType: 'switch',
      default: false,
    },
  },
  setup(props: VObject) {
    const { getScreenData } = useScreenDataFetchCollectionInject(props);
    const localData = computed(() => {
      return props.data || getScreenData(props, '');
    });
    const runtimeCheckRule = (data: VScatterData[]) => {
      let flag = true;
      data.map(line => {
        !Array.isArray(line.seriesData) ? (flag = false) : null;
        return line;
      });
      if (flag) return data;

      return defaultData;
    };
    return {
      ...toRefs(props),
      localData,
      runtimeCheckRule,
    };
  },
});
export default ComBaseMultiBubbleDesign;
</script>
<style lang="stylus" scoped></style>
