<template lang="pug">
.com_base_funnel_design.w-full.h-full
  ComScreenBaseJade(
    v-bind='$props',
    :data='runtimeCheckRule(localData)'
  )
</template>
<script lang="ts">
import { PropType, computed, toRefs } from 'vue';

import { defineTaBuilderCellComponent } from '@/components/global/ta-component/TaBuilder/builder/defineTaBuilderCellComponent';
import { useScreenDataFetchCollectionInject } from '@/components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';
import { dataPropsFn } from '../../../screen/components/echartBaseProps';
import ComScreenBaseJade from '../../../screen/charts/jade/ComScreenBaseJade.vue';
import { RankingBarData } from '../../../screen/charts/baseUtils/baseChat';

const defaultData = [
  {
    seriesName: '监控风险点数量',
    seriesData: 754,
  },
  {
    seriesName: '已发生风险数',
    seriesData: 611,
  },
  {
    seriesName: '未发生风险数',
    seriesData: 400,
  },
  {
    seriesName: '已处置风险数',
    seriesData: 200,
  },
  {
    seriesName: '未处置风险数',
    seriesData: 200,
  },
];

const ComScreenBaseJadeDesign = defineTaBuilderCellComponent({
  name: 'ComScreenBaseJadeDesign',
  components: {
    ComScreenBaseJade,
  },
  props: {
    ...dataPropsFn({ group: '', modelKeyPrefix: '' }, 'props', 'json', () => defaultData),
    showLegend: {
      type: Boolean,
      formType: 'switch',
      label: '是否显示图例',
      default: false,
    },
    sumValue: {
      type: Number,
      formType: 'number',
      label: '极大值',
      default: 1000,
    },

    customColor: {
      type: Array as PropType<string[]>,
      formType: 'json',
      label: '自定义填充区域颜色序列',
      default: () => [],
    },
  },
  setup(props: any) {
    const { getScreenData } = useScreenDataFetchCollectionInject(props);
    const localData = computed(() => {
      return props.data || getScreenData(props, '');
    });
    const runtimeCheckRule = (data: RankingBarData[]) => {
      let flag = true;
      data.map(line => {
        !Array.isArray(line.seriesData) ? (flag = false) : null;
        return line;
      });
      if (flag) return data;

      return defaultData;
    };
    return {
      ...toRefs(props),
      runtimeCheckRule,
      localData,
    };
  },
});
export default ComScreenBaseJadeDesign;
</script>
<style lang="stylus" scoped></style>
