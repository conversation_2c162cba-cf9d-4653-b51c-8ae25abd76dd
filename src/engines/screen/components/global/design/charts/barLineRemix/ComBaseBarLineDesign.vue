<template lang="pug">
.w-full.h-full
  ComScreenBaseBarLine(
    v-bind='$props',
    :data='runtimeCheckRule(localData)'
  )
</template>

<script lang="ts">
import { VObject } from '@/lib/vails';
import { PropType, computed, toRefs } from 'vue';
import {
  BaseData,
  barAreaColor,
  lineAreaColor,
  lineColor,
} from '../../../screen/charts/baseUtils/baseChat';
import { defineTaBuilderCellComponent } from '@/components/global/ta-component/TaBuilder/builder/defineTaBuilderCellComponent';
import ComScreenBaseBarLine from '../../../screen/charts/BarLineRemix/ComScreenBaseBarLine.vue';
import { useScreenDataFetchCollectionInject } from '@/components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';
import { dataPropsFn } from '../../../screen/components/echartBaseProps';

const defaultData = [
  {
    seriesName: 'bar-1',
    seriesData: [120, 188, 150, 80, 70, 110],
    seriesType: 'bar',
  },
  {
    seriesName: 'line-1',
    seriesData: [120, 188, 150, 80, 70, 110].reverse(),
    seriesType: 'line',
  },
  {
    seriesName: 'line-2',
    seriesData: [120, 188, 150, 80, 70, 110],
    seriesType: 'line',
  },
];

export default defineTaBuilderCellComponent({
  name: 'ComScreenBaseBarLineDesign',
  components: {
    ComScreenBaseBarLine,
  },
  props: {
    ...dataPropsFn({ group: '', modelKeyPrefix: '' }, 'props', 'json', () => defaultData),

    // 是否显示图例
    showLengend: { type: Boolean, default: false, formType: 'switch', label: '是否显示图例' },

    // 图例位置 top right bottom right
    lengendPosition: {
      type: Array,
      formType: 'json',
      label: '图例位置[上, 右，下，左]',
      default: () => [0, 0, 0, 'center'],
    },
    // 图位置 top right bottom right
    gridPosition: {
      type: Array,
      formType: 'json',
      label: 'grid位置[上, 右，下，左]',
      default: () => ['10%', '10%', '10%', '10%'],
    },
    // X轴显示名称，为空则不显示X轴标
    xAxisLabel: {
      type: Array as PropType<string[]>,
      formType: 'json',
      label: 'X轴显示名称，为空则不显示X轴标',
      default: () => ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
    },
    // 图例标题
    title: {
      formType: 'input',
      label: '图例标题',
      type: String,
      default: '',
    },
    // 图例标题布局
    titlePadding: {
      type: Array as PropType<number[]>,
      formType: 'json',
      label: '图例标题[上, 右，下，左]',
      default: () => [0, 0, 0, 16],
    },
    // 是否显示Y轴单位
    showUnit: { type: Boolean, formType: 'switch', label: '是否显示Y轴单位', default: false },
    // 是否反转Y轴单位
    reverseUnitYAxis: {
      type: Boolean,
      formType: 'switch',
      label: '是否反转Y轴单位',
      default: false,
    },
    // 主轴单位(line)
    unit: { formType: 'input', label: '主轴单位', type: String, default: '' },
    // 副轴单位(bar)
    subUnit: { formType: 'input', label: '副轴单位', type: String, default: '' },
    // 主轴单位标题
    unitTitle: { formType: 'input', label: '主轴单位标题', type: String, default: '单位' },
    // 副轴单位标题
    subUnitTitle: { formType: 'input', label: '副轴单位标题', type: String, default: '单位' },
    // 主轴单位位置
    unitPadding: {
      formType: 'json',
      label: '主轴单位位置[上, 右，下，左]',
      type: Array as PropType<number[]>,
      default: () => [0, 0, 0, 0],
    },
    // 辅轴单位位置
    subUnitPadding: {
      formType: 'json',
      label: '辅轴单位位置[上, 右，下，左]',
      type: Array as PropType<number[]>,
      default: () => [0, 0, 0, 0],
    },
    // 柱状图宽度
    barWidth: {
      type: String || Number,
      formType: 'number',
      label: '柱状图宽度',
      default: '',
    },
    colorType: {
      label: '柱条单种颜色or渐变色',
      type: String as PropType<'solid' | 'gradient'>,
      default: 'gradient',
      formType: 'radio',
      select: [
        { label: '纯色', value: 'solid' },
        { label: '渐变色', value: 'gradient' },
      ],
    },
    barAreaColor: {
      type: Array,
      formType: 'json',
      label: '柱状图颜色序列 ["#fff"]',
      default: barAreaColor,
    },
    lineColor: {
      type: Array as PropType<string[]>,
      formType: 'json',
      label: '折线图颜色序列 ["#fff"]',
      default: lineColor,
    },
    lineAreaColor: {
      type: Array,
      formType: 'json',
      label: '折线图区域颜色序列 ["#fff"]',
      default: lineAreaColor,
    },
    isSmooth: {
      type: Boolean,
      formType: 'switch',
      label: '是否使用平滑曲线',
      default: () => true,
    },
  },
  setup(props: VObject) {
    const { getScreenData } = useScreenDataFetchCollectionInject(props);
    const localData = computed(() => {
      return props.data || getScreenData(props, '');
    });
    const runtimeCheckRule = (data: BaseData[]) => {
      let flag = true;
      data.map(line => {
        !Array.isArray(line.seriesData) ? (flag = false) : null;
        return line;
      });
      if (flag) return data;

      return defaultData;
    };
    return {
      ...toRefs(props),
      localData,
      runtimeCheckRule,
    };
  },
});
</script>

<style lang="stylus" scoped></style>
