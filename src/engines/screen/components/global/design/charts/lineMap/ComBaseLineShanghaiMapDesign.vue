<template lang="pug">
.com_base_line_shanghai_map_design.w-full.h-full
  ComBaseLineShanghaiMap(
    v-bind='$props',
    :data='runtimeCheckRule(localData)'
  )
</template>
<!-- 半环形图 -->
<script lang="ts">
import { PropType, computed, toRefs } from 'vue';
import { defineTaBuilderCellComponent } from '@/components/global/ta-component/TaBuilder/builder/defineTaBuilderCellComponent';
import ComBaseLineShanghaiMap from '../../../screen/charts/LineMap/ComBaseLineShanghaiMap.vue';
import {
  ForceGraphLineLevelData,
  ForceGraphData,
  forceGraphLineData,
  ScreenLinePointMap,
} from '../../../screen/charts/baseUtils/baseChat';
import { dataPropsFn } from '../../../screen/components/echartBaseProps';
import { useScreenDataFetchCollectionInject } from '@/components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';

const defaultData = [
  {
    seriesName: '各区流向图',
    seriesData: forceGraphLineData,
  },
];

const ComBaseLineShanghaiMapDesign = defineTaBuilderCellComponent({
  name: 'ComBaseLineShanghaiMapDesign',
  components: {
    ComBaseLineShanghaiMap,
  },
  props: {
    ...dataPropsFn({ group: '', modelKeyPrefix: '' }, 'props', 'json', () => defaultData),

    pointMap: {
      label: '节点名称',
      formType: 'json',
      type: Array as PropType<ScreenLinePointMap[]>,
      default: () => [
        {
          name: '黄浦区',
          value: [121.483572, 31.20946],
        },
        {
          name: '徐汇区',
          value: [121.440405, 31.142992],
        },
        {
          name: '长宁区',
          value: [121.380949, 31.20737],
        },
        {
          name: '静安区',
          value: [121.450659, 31.230821],
        },
        {
          name: '普陀区',
          value: [121.362042, 31.287895],
        },
        {
          name: '虹口区',
          value: [121.475443, 31.29749],
        },
        {
          name: '杨浦区',
          value: [121.541302, 31.27835],
        },
        {
          name: '闵行区',
          value: [121.428901, 31.0413],
        },
        {
          name: '宝山区',
          value: [121.404861, 31.392111],
        },
        {
          name: '嘉定区',
          value: [121.24439, 31.358138],
        },
        {
          name: '浦东新区',
          value: [121.742177, 31.083823],
        },
        {
          name: '金山区',
          value: [121.255144, 30.818932],
        },
        {
          name: '松江区',
          value: [121.220231, 31.015194],
        },
        {
          name: '青浦区',
          value: [121.085188, 31.124681],
        },
        {
          name: '奉贤区',
          value: [121.56251, 30.897998],
        },
        {
          name: '崇明区',
          value: [121.568484, 31.635916],
        },
      ],
    },
    lineLevel: {
      label: '区间范围界定',
      formType: 'json',
      type: Array as PropType<ForceGraphLineLevelData[]>,
      default: () => [
        {
          gt: 7,
          lte: 100,
          label: '7 - 100人次',
        },
        {
          gt: 100,
          lte: 200,
          label: '100 - 200人次',
        },
        {
          gt: 200,
          lte: 300,
          label: '200 - 300人次',
        },
        {
          gt: 300,
          lte: 477,
          label: '300 - 477人次',
        },
      ],
    },
    customizeLineColor: {
      type: Array as PropType<string[]>,
      formType: 'json',
      label: '自定义填充力图“线”的颜色，按区间顺序,默认则为空数组',
      default: () => [],
    },
    customizeAreaColor: {
      type: Array as PropType<string[]>,
      formType: 'json',
      label: '自定义填充力图“图例线”的颜色，按区间顺序,默认则为空数组',
      default: () => [],
    },
  },
  setup(props: any) {
    const { getScreenData } = useScreenDataFetchCollectionInject(props);
    const localData = computed(() => {
      return props.data || getScreenData(props, '');
    });
    const runtimeCheckRule = (data: ForceGraphData[]) => {
      let flag = true;
      data.map(line => {
        !Array.isArray(line.seriesData) ? (flag = false) : null;
        return line;
      });
      if (flag) return data;

      return defaultData;
    };
    return {
      ...toRefs(props),
      localData,
      runtimeCheckRule,
    };
  },
});
export default ComBaseLineShanghaiMapDesign;
</script>
<style lang="stylus" scoped></style>
