<template lang="pug">
ComBaseMultiBar(
  v-bind='$props',
  :data='runtimeCheckRule(localData)'
)
</template>
<script lang="ts">
import { toRefs, PropType, computed } from 'vue';
import ComBaseMultiBar from '../../../screen/charts/Bar/ComBaseMultiBar.vue';
import { defineTaBuilderCellComponent } from '@/components/global/ta-component/TaBuilder/builder/defineTaBuilderCellComponent';

import { BaseData, barAreaColor } from '../../../screen/charts/baseUtils/baseChat';
import { VObject } from '@/lib/vails';
import { dataPropsFn } from '../../../screen/components/echartBaseProps';
import { useScreenDataFetchCollectionInject } from '../../../../../../../components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';

const defaultData = [
  {
    seriesName: 'bar-1',
    seriesData: [120, 200, 150, 80, 70, 110, 130],
  },
  {
    seriesName: 'bar-2',
    seriesData: [120, 200, 150, 80, 70, 110, 130].reverse(),
  },
  {
    seriesName: 'bar-3',
    seriesData: [90, 100, 350, 180, 30, 100, 230],
  },
];

export default defineTaBuilderCellComponent({
  name: 'ComBaseMultiBarDesign',
  components: {
    ComBaseMultiBar,
  },
  props: {
    ...dataPropsFn({ group: '', modelKeyPrefix: '' }, 'props', 'json', () => defaultData),
    gridPosition: {
      type: Array,
      formType: 'json',
      label: 'grid位置[上, 右，下，左]',
      default: () => ['10%', '10%', '10%', '10%'],
    },
    // X轴显示名称，为空则不显示X轴标
    xAxisLabel: {
      type: Array as PropType<string[]>,
      formType: 'json',
      label: 'X轴显示名称，为空则不显示X轴标',
      default: () => ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
    },

    // 是否显示Y轴单位
    showUnit: { type: Boolean, formType: 'switch', label: '是否显示Y轴单位', default: false },
    // 主轴单位
    unit: { formType: 'input', label: '主轴单位', type: String, default: '' },
    unitTitle: { formType: 'input', label: '主轴单位标题', type: String, default: '单位' },

    // 主轴单位位置
    unitPadding: {
      formType: 'json',
      label: '主轴单位位置[上, 右，下，左]',
      type: Array as PropType<number[]>,
      default: () => [0, 0, 0, 0],
    },
    // 柱状图宽度
    barWidth: {
      type: String || Number,
      formType: 'number',
      label: '柱状图宽度',
      default: '',
    },
    barAreaColor: {
      type: Array,
      formType: 'json',
      label: '区域颜色序列 ["#fff"]',
      default: barAreaColor,
    },
    showLengend: {
      type: Boolean,
      formType: 'switch',
      label: '是否要显示数据点标注',
      default: true,
    },
    labelXRotate: {
      type: Number,
      formType: 'number',
      label: 'X轴标签旋转角度',
      default: 30,
    },
    colorType: {
      label: '柱条单种颜色or渐变色',
      type: String as PropType<'solid' | 'gradient'>,
      default: 'gradient',
      formType: 'radio',
      select: [
        { label: '纯色', value: 'solid' },
        { label: '渐变色', value: 'gradient' },
      ],
    },
  },

  setup(props: VObject) {
    const { getScreenData } = useScreenDataFetchCollectionInject(props);
    const localData = computed(() => props.data || getScreenData(props, ''));
    const runtimeCheckRule = (data: BaseData[]) => {
      let flag = true;
      data.map(line => {
        !Array.isArray(line.seriesData) ? (flag = false) : null;
        return line;
      });
      if (flag) return data;

      return defaultData;
    };

    return {
      ...toRefs(props),
      localData,
      runtimeCheckRule,
    };
  },
});
</script>
<style lang="stylus" scoped></style>
