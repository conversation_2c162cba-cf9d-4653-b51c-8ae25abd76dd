<template lang="pug">
ComBaseStack3DBar(
  :data='localData',
  :xAxisLabel='xAxisLabel'
  :showUnit='showUnit'
  :unit='unit',
  :showLengend='showLengend',
  :unitPadding='unitPadding',
  :barWidth='barWidth',
  :gridPosition="gridPosition"
)
</template>
<script lang="ts">
import { defineComponent, toRefs, PropType, computed } from 'vue';
import ComBaseStack3DBar from '../../../screen/charts/Bar/ComBaseStack3DBar.vue';
import { defineTaBuilderCellComponent } from '@/components/global/ta-component/TaBuilder/builder/defineTaBuilderCellComponent';
import { dataPropsFn } from '../../../screen/components/echartBaseProps';

import { BaseData } from '../../../screen/charts/baseUtils/baseChat';
import { VObject } from '@/lib/vails';
import { useScreenDataFetchCollectionInject } from '@/components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';

const defaultData = [
  {
    seriesName: 'bar-1',
    seriesData: [120, 200, 150, 80, 70, 110, 130],
  },
  {
    seriesName: 'bar-2',
    seriesData: [120, 200, 150, 80, 70, 110, 130].reverse(),
  },
  {
    seriesName: 'bar-3',
    seriesData: [90, 100, 350, 180, 30, 100, 230],
  },
  {
    seriesName: 'bar-4',
    seriesData: [120, 200, 150, 80, 70, 110, 130].reverse(),
  },
  {
    seriesName: 'bar-5',
    seriesData: [10, 20, 30, 50, 30, 10, 20],
  },
];

export default defineTaBuilderCellComponent({
  name: 'ComBaseStack3DBarDesign',
  components: {
    ComBaseStack3DBar,
  },
  props: {
    ...dataPropsFn({ group: '', modelKeyPrefix: '' }, 'props', 'json', () => defaultData),

    gridPosition: {
      type: Array,
      formType: 'json',
      label: 'grid位置[上, 右，下，左]',
      default: () => ['10%', '10%', '10%', '10%'],
    },
    // X轴显示名称，为空则不显示X轴标
    xAxisLabel: {
      type: Array as PropType<string[]>,
      formType: 'json',
      label: 'X轴显示名称，为空则不显示X轴标',
      default: () => ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
    },

    // 是否显示Y轴单位
    showUnit: { type: Boolean, formType: 'switch', label: '是否显示Y轴单位', default: false },
    // 主轴单位
    unit: { formType: 'input', label: '主轴单位', type: String, default: '' },
    // 主轴单位位置
    unitPadding: {
      formType: 'json',
      label: '主轴单位位置[上, 右，下，左]',
      type: Array as PropType<number[]>,
      default: () => [0, 0, 0, 0],
    },
    // 柱状图宽度
    barWidth: {
      type: String || Number,
      formType: 'number',
      label: '柱状图宽度',
      default: '',
    },
    showLengend: {
      type: Boolean,
      formType: 'switch',
      label: '是否显示图例',
      default: true,
    },
  },

  setup(props: VObject) {
    const { getScreenData } = useScreenDataFetchCollectionInject(props);
    const localData = computed(() => {
      return props.data || getScreenData(props, '');
    });
    const runtimeCheckRule = (data: BaseData[]) => {
      let flag = true;
      data.map(line => {
        !Array.isArray(line.seriesData) ? (flag = false) : null;
        return line;
      });
      if (flag) return data;

      return defaultData;
    };
    return {
      ...toRefs(props),
      localData,
      runtimeCheckRule,
    };
  },
});
</script>
<style lang="stylus" scoped></style>
