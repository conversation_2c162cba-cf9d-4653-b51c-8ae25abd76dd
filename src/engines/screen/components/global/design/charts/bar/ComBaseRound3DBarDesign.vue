<template lang="pug">
ComBaseRound3DBar(
  v-bind='$props',
  :data='runtimeCheckRule(localData)'
)
</template>
<script lang="ts">
import { defineComponent, toRefs, PropType, computed } from 'vue';
import ComBaseRound3DBar from '../../../screen/charts/Bar/ComBaseRound3DBar.vue';
import { defineTaBuilderCellComponent } from '@/components/global/ta-component/TaBuilder/builder/defineTaBuilderCellComponent';

import { BaseData } from '../../../screen/charts/baseUtils/baseChat';
import { VObject } from '@/lib/vails';
import { dataPropsFn } from '../../../screen/components/echartBaseProps';
import { useScreenDataFetchCollectionInject } from '@/components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';

const defaultData = [
  {
    seriesName: 'bar-1',
    seriesData: [120, 188, 150, 80, 70, 110],
    seriesColor: '',
  },
];

export default defineTaBuilderCellComponent({
  name: 'ComBaseRound3DBarDesign',
  components: {
    ComBaseRound3DBar,
  },
  props: {
    ...dataPropsFn({ group: '', modelKeyPrefix: '' }, 'props', 'json', () => defaultData),

    gridPosition: {
      type: Array,
      formType: 'json',
      label: 'grid位置[上, 右，下，左]',
      default: () => ['10%', '10%', '10%', '10%'],
    },
    // X轴显示名称，为空则不显示X轴标
    xAxisLabel: {
      type: Array as PropType<string[]>,
      formType: 'json',
      label: 'X轴显示名称，为空则不显示X轴标',
      default: () => ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
    },

    // 是否显示Y轴单位
    showUnit: { type: Boolean, formType: 'switch', label: '是否显示Y轴单位', default: false },
    // 主轴单位
    unit: { formType: 'input', label: '主轴单位', type: String, default: '' },
    // 主轴单位位置
    unitPadding: {
      formType: 'json',
      label: '主轴单位位置[上, 右，下，左]',
      type: Array as PropType<number[]>,
      default: () => [0, 0, 0, 0],
    },
    // 柱状图宽度
    barWidth: {
      type: String || Number,
      formType: 'number',
      label: '柱状图宽度',
      default: '',
    },
    showLengend: {
      type: Boolean,
      formType: 'switch',
      label: '是否要显示数据点标注',
      default: true,
    },
    // 图例标题
    title: {
      formType: 'input',
      label: '图例标题',
      type: String,
      default: '',
    },
    // 图例标题布局
    titlePadding: {
      type: Array as PropType<number[]>,
      formType: 'json',
      label: '图例标题[上, 右，下，左]',
      default: () => [0, 0, 0, 16],
    },
  },

  setup(props: VObject) {
    const { getScreenData } = useScreenDataFetchCollectionInject(props);
    const localData = computed(() => {
      return props.data || getScreenData(props, '');
    });
    const runtimeCheckRule = (data: BaseData[]) => {
      let flag = true;
      data.map(line => {
        !Array.isArray(line.seriesData) ? (flag = false) : null;
        return line;
      });
      if (flag) return data;

      return defaultData;
    };
    return {
      ...toRefs(props),
      localData,
      runtimeCheckRule,
    };
  },
});
</script>
<style lang="stylus" scoped></style>
