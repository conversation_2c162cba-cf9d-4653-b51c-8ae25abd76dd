<template lang="pug">
.w-full.h-full
  ComBaseTreemap(
    v-bind='$props',
    :data='data'
  )
</template>
<!-- 基础矩形图 -->
<script lang="ts">
import { computed, toRefs } from 'vue';
import { defineTaBuilderCellComponent } from '@/components/global/ta-component/TaBuilder/builder/defineTaBuilderCellComponent';
import { dataPropsFn } from '../../../screen/components/echartBaseProps';
import { useScreenDataFetchCollectionInject } from '@/components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';
import { VObject } from '@/lib/vails';

const defaultColor = ['#1A56DB', '#1C64F2', '#3F83F8', '#76A9FA'];
const ComScreenTreemapDesign = defineTaBuilderCellComponent({
  name: 'ComScreenTreemapDesign',
  props: {
    ...dataPropsFn({ group: '', modelKeyPrefix: '' }, 'props', 'json', () => []),
    color: {
      type: Array,
      formType: 'json',
      label: '区域颜色序列 ["#fff"]',
      default: defaultColor,
    },
    showBreadcrumb: {
      type: Boolean,
      formType: 'switch',
      label: '是否有面包屑',
      default: false,
    },
  },
  setup(props: VObject) {
    const { getScreenData } = useScreenDataFetchCollectionInject(props);
    const localData = computed(() => {
      return props.data || getScreenData(props, '');
    });
    return {
      ...toRefs(props),
      localData,
    };
  },
});
export default ComScreenTreemapDesign;
</script>
<style lang="stylus" scoped></style>
