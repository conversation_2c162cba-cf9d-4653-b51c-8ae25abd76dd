<template lang="pug">
.com_base_gauge_normal_design.w-full.h-full
  ComBaseGaugeOnlyLabel(
    v-bind='$props',
    :data='runtimeCheckRule(localData)'
  )
</template>
<script lang="ts">
import { PropType, computed, toRefs } from 'vue';
import ComBaseGaugeOnlyLabel from '@/engines/screen/components/global/screen/charts/Progress/ComBaseGaugeOnlyLabel.vue';
import { defineTaBuilderCellComponent } from '@/components/global/ta-component/TaBuilder/builder/defineTaBuilderCellComponent';
import { progressScreenData } from '../../../screen/charts/baseUtils/baseChat';
import { dataPropsFn } from '../../../screen/components/echartBaseProps';
import { useScreenDataFetchCollectionInject } from '@/components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';

const defaultData = [
  {
    seriesName: 'progress-1',
    seriesData: 80,
  },
];

const ComBaseGaugeOnlyLabelDesign = defineTaBuilderCellComponent({
  name: 'ComBaseGaugeOnlyLabelDesign',
  components: {
    ComBaseGaugeOnlyLabel,
  },
  props: {
    ...dataPropsFn({ group: '', modelKeyPrefix: '' }, 'props', 'json', () => defaultData),

    center: {
      label: '仪表盘方位设置',
      formType: 'json',
      type: Array,
      default: () => ['50%', '50%'],
    },
    color: {
      label: '仪表盘颜色',
      formType: 'input',
      type: String,
      default: () => '#91c7ae',
    },
    showTitle: {
      label: '是否显示仪表盘标题',
      formType: 'switch',
      type: Boolean,
      default: () => true,
    },
    title: {
      label: '仪表盘标题',
      formType: 'input',
      type: String,
      default: () => '通过率',
    },
    lastWeekCompare: {
      label: '较上周数值',
      formType: 'number',
      type: Number,
      default: () => 120,
    },
    lastMonthCompare: {
      label: '较上月数值',
      formType: 'number',
      type: Number,
      default: () => -140,
    },
  },
  setup(props: any) {
    const { getScreenData } = useScreenDataFetchCollectionInject(props);
    const localData = computed(() => {
      return props.data || getScreenData(props, '');
    });
    const runtimeCheckRule = (data: progressScreenData[]) => {
      let flag = true;
      data.map(progress => {
        !progress.seriesData ? (flag = false) : null;
        return progress;
      });
      if (flag) return data;

      return defaultData;
    };
    return {
      ...toRefs(props),
      localData,
      runtimeCheckRule,
    };
  },
});
export default ComBaseGaugeOnlyLabelDesign;
</script>
<style lang="stylus" scoped></style>
