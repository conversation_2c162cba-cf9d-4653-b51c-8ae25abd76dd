<template lang="pug">
.com_base_progress_design.w-full.h-full
  ComBaseProgress(
    v-bind='$props',
    mode='half',
    :data='runtimeCheckRule(localData)'
  )
</template>
<script lang="ts">
import { PropType, computed, toRefs } from 'vue';
import ComBaseProgress from '@/engines/screen/components/global/screen/charts/Progress/ComBaseProgress.vue';
import { defineTaBuilderCellComponent } from '@/components/global/ta-component/TaBuilder/builder/defineTaBuilderCellComponent';
import { progressScreenData } from '../../../screen/charts/baseUtils/baseChat';
import { dataPropsFn } from '../../../screen/components/echartBaseProps';
import { useScreenDataFetchCollectionInject } from '@/components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';

const defaultData = [
  {
    seriesName: 'progress-1',
    seriesData: 80,
  },
];

const ComBaseProgressDesign = defineTaBuilderCellComponent({
  name: 'ComBaseProgressDesign',
  components: {
    ComBaseProgress,
  },
  props: {
    ...dataPropsFn({ group: '', modelKeyPrefix: '' }, 'props', 'json', () => defaultData),

    progressType: {
      label: '进度条类型',
      formType: 'radio',
      type: String as PropType<'dashboard' | 'circle'>,
      select: [
        { label: '仪表盘', value: 'dashboard' },
        { label: '圆', value: 'circle' },
      ],
      default: () => 'dashboard',
    },
    strokeColor: {
      label: '进度条有进度部分颜色',
      formType: 'json',
      type: Object,
      default: () => {
        return { '0%': '#24CAFF', '100%': '#3D7FFF' };
      },
    },
    trailColor: {
      label: '进度条没进度部分颜色',
      formType: 'input',
      type: String,
      default: () => {
        return 'rgba(61, 127, 255, .3)';
      },
    },
    borderColor: {
      label: '进度条边框颜色',
      formType: 'input',
      type: String,
      default: () => {
        return 'linear-gradient(91.67deg, #0461E3 1.42%, #00FBF2 53.05%, #015EE2 98.58%)';
      },
    },
    showLabel: {
      label: '显示标题',
      formType: 'switch',
      type: Boolean,
      default: () => false,
    },
    label: {
      label: '描述标题',
      formType: 'input',
      type: String,
      default: () => '数量占比',
    },
    digit: {
      label: '数量单位',
      formType: 'input',
      type: String,
      default: () => '%',
    },
  },
  setup(props: any) {
    const { getScreenData } = useScreenDataFetchCollectionInject(props);
    const localData = computed(() => {
      return props.data || getScreenData(props, '');
    });
    const runtimeCheckRule = (data: progressScreenData[]) => {
      let flag = true;
      data.map(progress => {
        !progress.seriesData ? (flag = false) : null;
        return progress;
      });
      if (flag) return data;

      return defaultData;
    };
    return {
      ...toRefs(props),
      localData,
      runtimeCheckRule,
    };
  },
});
export default ComBaseProgressDesign;
</script>
<style lang="stylus" scoped></style>
