<template lang="pug">
.com_base_gauge_pointer_multi_design.w-full.h-full
  ComBaseGaugePointerMulti(
    v-bind='$props',
    :data='runtimeCheckRule(localData)'
  )
</template>
<script lang="ts">
import { PropType, computed, toRefs } from 'vue';
import ComBaseGaugePointerMulti from '@/engines/screen/components/global/screen/charts/Progress/ComBaseGaugePointerMulti.vue';
import { defineTaBuilderCellComponent } from '@/components/global/ta-component/TaBuilder/builder/defineTaBuilderCellComponent';
import { progressScreenData } from '../../../screen/charts/baseUtils/baseChat';
import { dataPropsFn } from '../../../screen/components/echartBaseProps';
import { useScreenDataFetchCollectionInject } from '@/components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';

const defaultData = [
  {
    seriesName: 'progress-1',
    seriesData: 80,
  },
];

const ComBaseGaugePointerMultiDesign = defineTaBuilderCellComponent({
  name: 'ComBaseGaugePointerMultiDesign',
  components: {
    ComBaseGaugePointerMulti,
  },
  props: {
    ...dataPropsFn({ group: '', modelKeyPrefix: '' }, 'props', 'json', () => defaultData),

    center: {
      label: '仪表盘方位设置',
      formType: 'json',
      type: Array,
      default: () => ['50%', '50%'],
    },
    color: {
      label: '仪表盘颜色',
      formType: 'input',
      type: String,
      default: () => '#91c7ae',
    },
    colorBorderSet: {
      label: '仪表盘轴线颜色',
      formType: 'json',
      type: Array,
      default: () => [
        [0.33, '#07A872'],
        [0.66, '#ED8139'],
        [1, '#FB6E77'],
      ],
    },
  },
  setup(props: any) {
    const { getScreenData } = useScreenDataFetchCollectionInject(props);
    const localData = computed(() => {
      return props.data || getScreenData(props, '');
    });
    const runtimeCheckRule = (data: progressScreenData[]) => {
      let flag = true;
      data.map(progress => {
        !progress.seriesData ? (flag = false) : null;
        return progress;
      });
      if (flag) return data;

      return defaultData;
    };
    return {
      ...toRefs(props),
      localData,
      runtimeCheckRule,
    };
  },
});
export default ComBaseGaugePointerMultiDesign;
</script>
<style lang="stylus" scoped></style>
