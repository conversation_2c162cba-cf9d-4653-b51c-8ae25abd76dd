<template lang="pug">
.com_base_gauge_normal_design.w-full.h-full
  ComBaseGaugeNormal(v-bind='$props', :data='runtimeCheckRule(localData)')
</template>
<script lang="ts">
import { defineTaBuilderCellComponent } from '@/components/global/ta-component/TaBuilder/builder/defineTaBuilderCellComponent';
import { useScreenDataFetchCollectionInject } from '@/components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';
import ComBaseGaugeNormal from '@/engines/screen/components/global/screen/charts/Progress/ComBaseGaugeNormal.vue';
import { computed, toRefs } from 'vue';
import { progressScreenData } from '../../../screen/charts/baseUtils/baseChat';
import { dataPropsFn } from '../../../screen/components/echartBaseProps';

const defaultData = [
  {
    seriesName: 'progress-1',
    seriesData: 80,
  },
];

const ComBaseGaugeNormalDesign = defineTaBuilderCellComponent({
  name: 'ComBaseGaugeNormalDesign',
  components: {
    ComBaseGaugeNormal,
  },
  props: {
    ...dataPropsFn({ group: '', modelKeyPrefix: '' }, 'props', 'json', () => defaultData),

    center: {
      label: '仪表盘方位设置',
      formType: 'json',
      type: Array,
      default: () => ['50%', '60%'],
    },
    color: {
      label: '仪表盘颜色',
      formType: 'input',
      type: String,
      default: () => '#91c7ae',
    },
    showTitle: {
      label: '是否显示仪表盘标题',
      formType: 'switch',
      type: Boolean,
      default: () => true,
    },
    title: {
      label: '仪表盘标题',
      formType: 'input',
      type: String,
      default: () => '通过率',
    },
    lastWeekCompare: {
      label: '较上周数值',
      formType: 'number',
      type: Number,
      default: () => 120,
    },
    lastMonthCompare: {
      label: '较上月数值',
      formType: 'number',
      type: Number,
      default: () => -140,
    },
  },
  setup(props: any) {
    const { getScreenData } = useScreenDataFetchCollectionInject(props);
    const localData = computed(() => {
      return props.data || getScreenData(props, '');
    });
    const runtimeCheckRule = (data: progressScreenData[]) => {
      let flag = true;
      data.map(progress => {
        !progress.seriesData ? (flag = false) : null;
        return progress;
      });
      if (flag) return data;

      return defaultData;
    };
    return {
      ...toRefs(props),
      localData,
      runtimeCheckRule,
    };
  },
});
export default ComBaseGaugeNormalDesign;
</script>
<style lang="stylus" scoped></style>
