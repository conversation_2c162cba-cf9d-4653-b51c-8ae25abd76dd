<template lang="pug">
.com_base_gauge_pointer_single_design.w-full.h-full
  ComBaseGaugePointerSingle(
    v-bind='$props',
    :data='runtimeCheckRule(localData)'
  )
</template>
<script lang="ts">
import { PropType, computed, toRefs } from 'vue';
import ComBaseGaugePointerSingle from '@/engines/screen/components/global/screen/charts/Progress/ComBaseGaugePointerSingle.vue';
import { defineTaBuilderCellComponent } from '@/components/global/ta-component/TaBuilder/builder/defineTaBuilderCellComponent';
import { progressScreenData } from '../../../screen/charts/baseUtils/baseChat';
import { dataPropsFn } from '../../../screen/components/echartBaseProps';
import { useScreenDataFetchCollectionInject } from '@/components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';

const defaultData = [
  {
    seriesName: 'progress-1',
    seriesData: 80,
  },
];

const ComBaseGaugePointerSingleDesign = defineTaBuilderCellComponent({
  name: 'ComBaseGaugePointerSingleDesign',
  components: {
    ComBaseGaugePointerSingle,
  },
  props: {
    ...dataPropsFn({ group: '', modelKeyPrefix: '' }, 'props', 'json', () => defaultData),

    center: {
      label: '仪表盘方位设置',
      formType: 'json',
      type: Array,
      default: () => ['50%', '50%'],
    },
    color: {
      label: '仪表盘颜色',
      formType: 'input',
      type: String,
      default: () => '#91c7ae',
    },
  },
  setup(props: any) {
    const { getScreenData } = useScreenDataFetchCollectionInject(props);
    const localData = computed(() => {
      return props.data || getScreenData(props, '');
    });
    const runtimeCheckRule = (data: progressScreenData[]) => {
      let flag = true;
      data.map(progress => {
        !progress.seriesData ? (flag = false) : null;
        return progress;
      });
      if (flag) return data;

      return defaultData;
    };
    return {
      ...toRefs(props),
      localData,
      runtimeCheckRule,
    };
  },
});
export default ComBaseGaugePointerSingleDesign;
</script>
<style lang="stylus" scoped></style>
