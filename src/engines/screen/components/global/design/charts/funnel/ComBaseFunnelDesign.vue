<template lang="pug">
.com_base_funnel_design.w-full.h-full
  ComBaseFunnel(
    v-bind='$props',
    :data='runtimeCheckRule(localData)'
  )
</template>
<!-- 半环形图 -->
<script lang="ts">
import { PropType, computed, toRefs } from 'vue';
import {
  BaseData,
  RadarMax,
} from '@/engines/screen/components/global/screen/charts/baseUtils/baseChat';
import { defineTaBuilderCellComponent } from '@/components/global/ta-component/TaBuilder/builder/defineTaBuilderCellComponent';
import ComBaseFunnel from '../../../screen/charts/Funnel/ComBaseFunnel.vue';
import { useScreenDataFetchCollectionInject } from '@/components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';
import { dataPropsFn } from '../../../screen/components/echartBaseProps';

const defaultData = [
  {
    seriesName: 'funnel-1',
    seriesData: [
      { value: 140, name: '图例' },
      { value: 180, name: '咨询' },
      { value: 100, name: '订单' },
      { value: 60, name: '点击' },
      { value: 30, name: '展现' },
    ],
  },
];

const ComBaseFunnelDesign = defineTaBuilderCellComponent({
  name: 'ComBaseFunnelDesign',
  components: {
    ComBaseFunnel,
  },
  props: {
    ...dataPropsFn({ group: '', modelKeyPrefix: '' }, 'props', 'json', () => defaultData),

    customizeAreaColor: {
      type: Array as PropType<string[]>,
      formType: 'json',
      label: '自定义填充区域颜色序列,默认则为空数组',
      default: () => ['#2174FF', '#3CB1FB', '#07A872', '#D29E08', '#ED8139'],
    },

    showLengend: { formType: 'switch', label: '是否显示图例', type: Boolean, default: true },
    lengendPosition: {
      formType: 'json',
      label: '图例位置',
      type: Array,
      default: () => ['center', 0, 0, 'auto'],
    },
    // 平底还是尖底的漏斗图
    bottomTye: {
      formType: 'radio',
      label: '平底还是尖底的漏斗图',
      select: [
        { label: '平底', value: 'flat' },
        { label: '尖底', value: 'tip' },
      ],
      type: String as PropType<'flat' | 'tip'>,
      default: 'flat',
    },
    // FunnelOrient 漏斗图水平还是垂直
    funnelOrient: {
      formType: 'radio',
      label: '漏斗图水平还是垂直',
      select: [
        { label: '横向', value: 'horizontal' },
        { label: '纵向', value: 'vertical' },
      ],
      type: String as PropType<'horizontal' | 'vertical'>,
      default: () => 'vertical',
    },
    lengendOrient: {
      formType: 'radio',
      label: '是否显示雷达图首项刻度线和标签值',
      select: [
        { label: '横向', value: 'horizontal' },
        { label: '纵向', value: 'vertical' },
      ],
      type: String as PropType<'horizontal' | 'vertical'>,
      default: () => 'vertical',
    },
    insideLabelWithValue: {
      type: Boolean,
      formType: 'switch',
      label: '内部标签是否显示值',
      default: () => true,
    },
    insideDigit: {
      type: String,
      formType: 'input',
      label: '内部标签单位',
      default: () => '个',
    },
    showOutSideLabel: {
      type: Boolean,
      formType: 'switch',
      label: '是否显示外部标签',
      default: () => true,
    },
  },
  setup(props: any) {
    const { getScreenData } = useScreenDataFetchCollectionInject(props);
    const localData = computed(() => {
      return props.data || getScreenData(props, '');
    });
    const runtimeCheckRule = (data: BaseData[]) => {
      let flag = true;
      data.map(line => {
        !Array.isArray(line.seriesData) ? (flag = false) : null;
        return line;
      });
      if (flag) return data;

      return defaultData;
    };

    return {
      ...toRefs(props),
      localData,
      runtimeCheckRule,
    };
  },
});
export default ComBaseFunnelDesign;
</script>
<style lang="stylus" scoped></style>
