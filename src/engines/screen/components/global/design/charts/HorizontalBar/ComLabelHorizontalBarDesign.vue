<template lang="pug">
ComLabelHorizontalBar(
  v-bind='$props',
  :data='runtimeCheckRule(localData)'
)
</template>
<script lang="ts">
import { toRefs, PropType, computed } from 'vue';
import ComLabelHorizontalBar from '@/engines/screen/components/global/screen/charts/HorizontalBar/ComLabelHorizontalBar.vue';
import { defineTaBuilderCellComponent } from '@/components/global/ta-component/TaBuilder/builder/defineTaBuilderCellComponent';
import { dataPropsFn } from '../../../screen/components/echartBaseProps';

import { BaseData, barAreaColor } from '../../../screen/charts/baseUtils/baseChat';
import { VObject } from '@/lib/vails';
import { useScreenDataFetchCollectionInject } from '@/components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';

const defaultData = [
  {
    seriesName: 'bar-1',
    seriesData: [120, 188, 150, 80, 70, 110, 130],
    seriesColor: '',
  },
];

export default defineTaBuilderCellComponent({
  name: 'ComLabelHorizontalBarDesign',
  components: {
    ComLabelHorizontalBar,
  },
  props: {
    ...dataPropsFn({ group: '', modelKeyPrefix: '' }, 'props', 'json', () => defaultData),
    gridPosition: {
      type: Array,
      formType: 'json',
      label: 'grid位置[上, 右，下，左]',
      default: () => ['10%', '10%', '10%', '15%'],
    },
    // X轴显示名称，为空则不显示X轴标
    yAxisLabel: {
      type: Array as PropType<string[]>,
      formType: 'json',
      label: 'Y轴显示名称，为空则不显示X轴标',
      default: () => ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
    },
    showTootip: {
      type: Boolean,
      formType: 'switch',
      label: '是否要展示tooltip',
      default: true,
    },
    showBg: {
      type: Boolean,
      formType: 'switch',
      label: '是否要展示背景',
      default: false,
    },
    showLabel: {
      type: Boolean,
      formType: 'switch',
      label: '是否要展示标签',
      default: true,
    },
    // 柱状图宽度
    barWidth: {
      type: String || Number,
      formType: 'number',
      label: '柱状图宽度',
      default: 12,
    },
    barAreaColor: {
      type: Array,
      formType: 'json',
      label: '区域颜色序列 ["#fff"]',
      default: barAreaColor,
    },
    unit: {
      type: String,
      formType: 'input',
      label: '填写单位',
      default: '',
    },
    //  翻转数据
    reverseChart: {
      type: Boolean,
      default: () => false,
      label: '是否翻转数据',
      formType: 'switch',
    },
    showXAxis: {
      type: Boolean,
      default: () => false,
      label: '显示x轴和对应轴线',
      formType: 'switch',
    },
    borderRadius: {
      type: Array,
      formType: 'json',
      label: '条形图圆角设置',
      default: () => [0, 2, 2, 0],
    },
  },

  setup(props: VObject) {
    const { getScreenData } = useScreenDataFetchCollectionInject(props);
    const localData = computed(() => {
      return props.data || getScreenData(props, '');
    });
    const runtimeCheckRule = (data: BaseData[]) => {
      let flag = true;
      data.map(line => {
        !Array.isArray(line.seriesData) ? (flag = false) : null;
        return line;
      });
      if (flag) return data;

      return defaultData;
    };
    return {
      ...toRefs(props),
      localData,
      runtimeCheckRule,
    };
  },
});
</script>
<style lang="stylus" scoped></style>
