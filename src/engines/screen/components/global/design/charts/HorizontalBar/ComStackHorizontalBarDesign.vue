<template lang="pug">
ComStackHorizontalBar(
  v-bind='$props',
  :data='runtimeCheckRule(localData)'
)
</template>
<script lang="ts">
import { toRefs, PropType, computed } from 'vue';
import ComStackHorizontalBar from '@/engines/screen/components/global/screen/charts/HorizontalBar/ComStackHorizontalBar.vue';
import { defineTaBuilderCellComponent } from '@/components/global/ta-component/TaBuilder/builder/defineTaBuilderCellComponent';
import { dataPropsFn } from '../../../screen/components/echartBaseProps';

import { BaseData, barAreaColor } from '../../../screen/charts/baseUtils/baseChat';
import { VObject } from '@/lib/vails';
import { useScreenDataFetchCollectionInject } from '@/components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';

const defaultData = [
  {
    seriesName: 'bar-1',
    seriesData: [120, 200, 150, 80, 70, 110, 130],
  },
  {
    seriesName: 'bar-2',
    seriesData: [120, 200, 150, 80, 70, 110, 130].reverse(),
  },
  {
    seriesName: 'bar-3',
    seriesData: [90, 100, 350, 180, 30, 100, 230],
  },
  {
    seriesName: 'bar-4',
    seriesData: [120, 200, 150, 80, 70, 110, 130].reverse(),
  },
  {
    seriesName: 'bar-5',
    seriesData: [10, 20, 30, 50, 30, 10, 20],
  },
];
export default defineTaBuilderCellComponent({
  name: 'ComStackHorizontalBarDesign',
  components: {
    ComStackHorizontalBar,
  },
  props: {
    ...dataPropsFn({ group: '', modelKeyPrefix: '' }, 'props', 'json', () => defaultData),
    gridPosition: {
      type: Array,
      formType: 'json',
      label: 'grid位置[上, 右，下，左]',
      default: () => ['10%', '10%', '10%', '15%'],
    },
    // X轴显示名称，为空则不显示X轴标
    yAxisLabel: {
      type: Array as PropType<string[]>,
      formType: 'json',
      label: 'Y轴显示名称，为空则不显示X轴标',
      default: () => ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
    },
    showTootip: {
      type: Boolean,
      formType: 'switch',
      label: '是否要展示tooltip',
      default: true,
    },
    isStack: {
      type: Boolean,
      formType: 'switch',
      label: '是否堆积数据',
      default: false,
    },
    showLabel: {
      type: Boolean,
      formType: 'switch',
      label: '是否要展示标签',
      default: true,
    },
    // 柱状图宽度
    barWidth: {
      type: String || Number,
      formType: 'number',
      label: '柱状图宽度',
      default: 12,
    },
    barAreaColor: {
      type: Array,
      formType: 'json',
      label: '区域颜色序列 ["#fff"]',
      default: barAreaColor,
    },
    unit: {
      type: String,
      formType: 'input',
      label: '填写单位',
      default: '',
    },
    //  翻转数据
    reverseChart: {
      type: Boolean,
      default: () => false,
      label: '是否翻转数据',
      formType: 'switch',
    },
    showLengend: { type: Boolean, formType: 'switch', label: '是否显示图例', default: true },
    lengendPosition: {
      type: Array,
      formType: 'json',
      label: '图例位置[上, 右，下，左]',
      default: () => ['bottom', 0, 0, 'left'],
    },
  },

  setup(props: VObject) {
    const { getScreenData } = useScreenDataFetchCollectionInject(props);
    const localData = computed(() => {
      return props.data || getScreenData(props, '');
    });
    const runtimeCheckRule = (data: BaseData[]) => {
      let flag = true;
      data.map(line => {
        !Array.isArray(line.seriesData) ? (flag = false) : null;
        return line;
      });
      if (flag) return data;

      return defaultData;
    };
    return {
      ...toRefs(props),
      localData,
      runtimeCheckRule,
    };
  },
});
</script>
<style lang="stylus" scoped></style>
