<template lang="pug">
.w-full.h-full
  TaScreenEchartBase(:options='pieOption' ref='echartsRef')
</template>

<script lang="ts">
import { PropType, computed, ref } from 'vue';
import { VObject } from '@/lib/vails';
import 'echarts-gl';
import { dataPropsFn } from '../../../screen/components/echartBaseProps';
import { get3DPie } from '@/engines/screen/components/global/screen/charts/baseUtils/base3DPie';
import { useScreenDataFetchCollectionInject } from '../../../../../../../components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';
import { defineTaBuilderCellComponent } from '@/components/global/ta-component/TaBuilder/builder/defineTaBuilderCellComponent';
// - 立体环形图

const defaultData = [
  {
    name: 'aa',
    value: 70,
  },
  {
    name: 'bb',
    value: 32,
  },
  {
    name: 'ee',
    value: 20,
  },
  {
    name: 'cc',
    value: 40,
  },
];
export default defineTaBuilderCellComponent({
  name: 'ComBase3DPieDesign',
  props: {
    ...dataPropsFn({ group: '', modelKeyPrefix: '' }, 'props', 'json', () => defaultData),
    colors: {
      type: Array as PropType<string[]>,
      formType: 'json',
      label: '颜色',
      default: ['#1EF0D7', '#3D67FF', '#EBC039', '#47BC79'],
    },
    gap: {
      type: Number,
      formType: 'number',
      label: '间距',
      default: 0,
    },
    // 扇形区域最大高度
    hoverHeight: {
      type: Number,
      formType: 'number',
      label: 'hover高度',
      default: 35,
    },
    maxHeight: {
      type: Number,
      formType: 'number',
      label: '扇形区域最大高度',
      default: 105,
    },
    minHeight: {
      type: Number,
      formType: 'number',
      label: '扇形区域最小高度',
      default: 35,
    },
    ratio: {
      type: Number,
      formType: 'number',
      label: '比例',
      default: 0.59,
    },
    distance: {
      type: Number,
      formType: 'number',
      label: '距离',
      default: 210,
    },
    // 图形左右转动角度
    beta: {
      type: Number,
      formType: 'number',
      label: '左右转动角度',
      default: 0,
    },
    autoRotate: {
      type: Boolean,
      formType: 'switch',
      label: '是否自动旋转',
      default: false,
    },
    // 旋转操作的灵敏度，值越大越灵敏,最大1,最小0无法旋转
    rotateSensitivity: {
      type: Number,
      formType: 'number',
      label: '旋转灵敏度',
      default: 0.5,
    },
  },
  setup(props: VObject) {
    const echartsRef = ref();
    const { getScreenData } = useScreenDataFetchCollectionInject(props);
    const localData = computed(() => {
      return props.data || getScreenData(props, '');
    });
    const getAreaHeight = (nums: number[], cur: number) => {
      const max = Math.max(...nums);
      const min = Math.min(...nums);
      return ((cur - min) / (max - min)) * (props.maxHeight - props.minHeight) + props.minHeight;
    };
    const hoverIndex = ref(null);

    const pieData = computed(() =>
      localData.value.map((d: any, i: number) => ({
        name: d.name,
        value: Number(d.value),
        itemStyle: { color: props.colors[i] },
        h: getAreaHeight(
          localData.value.map((x: VObject) => Number(x.value)),
          Number(d.value),
        ),
      })),
    );

    const pieOption = computed(() => {
      const options = get3DPie(
        pieData.value,
        0,
        hoverIndex.value,
        props.hoverHeight,
        props.gap,
        props.distance,
        props.beta,
        props.autoRotate,
        props.rotateSensitivity,
      );
      return options;
    });

    return {
      pieOption,
      echartsRef,
    };
  },
});
</script>

<style lang="stylus" scoped></style>
