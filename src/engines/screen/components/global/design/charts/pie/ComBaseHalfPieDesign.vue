<template lang="pug">
.com_base_half_pie.w-full.h-full
  ComScreenBasePie(
    v-bind='$props',
    :data='runtimeCheckRule(localData)'
  )
</template>
<!-- 半环形图 -->
<script lang="ts">
import { PropType, toRefs, computed } from 'vue';
import ComScreenBasePie from '@/engines/screen/components/global/screen/charts/Pie/ComScreenBasePie.vue';
import {
  PieData,
  PieRadius,
} from '@/engines/screen/components/global/screen/charts/baseUtils/baseChat';
import { defineTaBuilderCellComponent } from '@/components/global/ta-component/TaBuilder/builder/defineTaBuilderCellComponent';
import { PieLengendPosition } from '../../../screen/charts/baseUtils/baseChat';
import { dataPropsFn } from '../../../screen/components/echartBaseProps';
import { useScreenDataFetchCollectionInject } from '../../../../../../../components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';

const defaultData = [
  {
    seriesName: 'pie-1',
    seriesData: [
      { value: 1048, name: 'Search Engine' },
      { value: 735, name: 'Direct' },
      { value: 580, name: 'Email' },
      { value: 484, name: 'Union Ads' },
      { value: 300, name: 'Video Ads' },
    ],
  },
];
const ComBaseHalfPieDesign = defineTaBuilderCellComponent({
  name: 'ComBaseHalfPieDesign',
  components: {
    ComScreenBasePie,
  },
  props: {
    mode: {
      label: '饼图类型',
      formType: 'radio',
      type: Number,
      select: [
        { label: '半环', value: 'half' },
        { label: '普通', value: 'normal' },
      ],
      default: () => 'half',
    },
    center: {
      type: Array,
      formType: 'json',
      label: '饼图中心',
      default: ['50%', '50%'],
    },
    ...dataPropsFn({ group: '', modelKeyPrefix: '' }, 'props', 'json', () => defaultData),
    // data: {
    //   label: '数据静态值',
    //   formType: 'json',
    //   type: Array as PropType<PieData[]>,
    //   default: [
    //     {
    //       seriesName: 'pie-1',
    //       seriesData: [
    //         { value: 1048, name: 'Search Engine' },
    //         { value: 735, name: 'Direct' },
    //         { value: 580, name: 'Email' },
    //         { value: 484, name: 'Union Ads' },
    //         { value: 300, name: 'Video Ads' },
    //       ],
    //     },
    //   ],
    // },
    customizeAreaColor: {
      type: Array as PropType<string[]>,
      formType: 'json',
      label: '自定义填充区域颜色序列,默认则为空数组',
      default: () => [],
    },
    unit: {
      label: '饼图单位',
      formType: 'input',
      type: String,
      default: '',
    },
    radius: {
      formType: 'json',
      label: '饼图半径',
      type: Array as PropType<PieRadius>,
      default: ['54%', '90%'],
    },
    showLegend: { type: Boolean, formType: 'switch', label: '是否展示图例', default: false },
    legendOrient: { type: String, formType: 'input', label: '图例排布', default: 'horizontal' },
    legendTextColor: { type: String, formType: 'input', label: '图例文字颜色', default: '#000' },
    legendPosition: {
      type: Array as PropType<PieLengendPosition>,
      formType: 'json',
      label: '图例位置',
      default: ['auto', 'auto', 'auto', 'auto'],
    },
    showLabel: {
      type: Boolean,
      formType: 'switch',
      label: '是否展示标签',
      default: true,
    },
    showPercent: {
      type: Boolean,
      formType: 'switch',
      label: '是否展示百分比（适用于需要value和percent的场景）',
      default: false,
    },
    labelLine: {
      label: 'label显示1行还是两行',
      formType: 'radio',
      type: Number,
      select: [
        { label: '1', value: 1 },
        { label: '2', value: 2 },
      ],
      default: () => 1,
    },
    labelGap: {
      label: 'label显示2行时，2行的间隔',
      formType: 'number',
      type: Number,
      default: () => 4,
    },
    labelLineLength: {
      label: 'label第一段指引线长度',
      formType: 'number',
      type: Number,
      default: () => 10,
    },
    labelLineLength2: {
      label: 'label第二段指引线长度',
      formType: 'number',
      type: Number,
      default: () => 10,
    },
    labelColor: {
      label: 'label-name颜色',
      formType: 'input',
      type: String,
      default: () => 'white',
    },
    secLabelColor: {
      label: 'label-value颜色',
      formType: 'input',
      type: String,
      default: () => 'white',
    },
    percentLabelColor: {
      label: 'label-percent颜色',
      formType: 'input',
      type: String,
      default: () => 'white',
    },
    borderColor: {
      label: '边框颜色',
      formType: 'input',
      type: String,
      default: '#000',
    },
    borderRadius: {
      label: '边框圆角',
      formType: 'input',
      type: Number,
      default: 4,
    },
    borderWidth: {
      label: '边框宽度',
      formType: 'number',
      type: Number,
      default: 0,
    },
  },
  setup(props: any) {
    const { getScreenData } = useScreenDataFetchCollectionInject(props);
    const localData = computed(() => {
      return props.data || getScreenData(props, '');
    });
    const runtimeCheckRule = (data: PieData[]) => {
      let flag = Array.isArray(data);
      flag &&
        data.map(pie => {
          !Array.isArray(pie.seriesData) ? (flag = false) : null;
        });

      if (flag) return data;

      return defaultData;
    };
    return {
      ...toRefs(props),
      localData,
      runtimeCheckRule,
    };
  },
});
export default ComBaseHalfPieDesign;
</script>
<style lang="stylus" scoped></style>
