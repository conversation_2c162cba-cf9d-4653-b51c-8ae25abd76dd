<template lang="pug">
.w-full.h-full
  TaScreenEchartBase(:options='pieOption' ref='echartsRef')
</template>

<script lang="ts">
/* eslint-disable */
import { PropType, computed, ref, watch } from 'vue';
import 'echarts-gl';
import {
  get3DPie,
  debounceFunc,
} from '@/engines/screen/components/global/screen/charts/baseUtils/base3DPie';
import { defineTaBuilderCellComponent } from '@/components/global/ta-component/TaBuilder/builder/defineTaBuilderCellComponent';
import { dataPropsFn } from '../../../screen/components/echartBaseProps';
import { useScreenDataFetchCollectionInject } from '../../../../../../../components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';
import { VObject } from '../../../../../../../lib/vails/model/index';
//- 立体环形图
const defaultData = [
  {
    name: 'aa',
    value: 70,
  },
  {
    name: 'bb',
    value: 32,
  },
  {
    name: 'ee',
    value: 20,
  },
  {
    name: 'cc',
    value: 40,
  },
];
export default defineTaBuilderCellComponent({
  name: 'ComBase3DDoughnutPieDesign',
  props: {
    ...dataPropsFn({ group: '', modelKeyPrefix: '' }, 'props', 'json', () => defaultData),
    colors: {
      type: Array as PropType<string[]>,
      formType: 'json',
      label: '颜色',
      default: ['#1EF0D7', '#3D67FF', '#EBC039', '#47BC79'],
    },
    gap: {
      type: Number,
      formType: 'number',
      label: '间距',
      default: 0,
    },
    hoverHeight: {
      type: Number,
      formType: 'number',
      label: 'hover高度',
      default: 35,
    },
    firstHeight: {
      type: Number,
      formType: 'number',
      label: '首块高度',
      default: 25,
    },
    ratio: {
      type: Number,
      formType: 'number',
      label: '比例',
      default: 0.59,
    },
    distance: {
      type: Number,
      formType: 'number',
      label: '距离',
      default: 150,
    },
    //图形左右转动角度
    beta: {
      type: Number,
      formType: 'number',
      label: '左右转动角度',
      default: 0,
    },
    autoRotate: {
      type: Boolean,
      formType: 'switch',
      label: '是否自动旋转',
      default: false,
    },
    // 旋转操作的灵敏度，值越大越灵敏,最大1,最小0无法旋转
    rotateSensitivity: {
      type: Number,
      formType: 'number',
      label: '旋转灵敏度',
      default: 0.5,
    },
  },
  setup(props: VObject) {
    const echartsRef = ref();
    const { getScreenData } = useScreenDataFetchCollectionInject(props);
    const hoverIndex = ref(null);
    const localData = computed(() => {
      return props.data || getScreenData(props, '');
    });
    const pieData = computed(() =>
      localData.value.map((d: any, i: number) => ({
        name: d.name,
        value: Number(d.value),
        itemStyle: { color: props.colors[i] },
        ...(i === 0 ? { h: props.firstHeight } : {}),
      })),
    );

    const pieOption = computed(() => {
      const options = get3DPie(
        pieData.value,
        props.ratio,
        hoverIndex.value,
        props.hoverHeight,
        props.gap,
        props.distance,
        props.beta,
        props.autoRotate,
        props.rotateSensitivity,
      );
      return options;
    });

    const hoverFunc = (params: any, mode = 'hover') => {
      if (hoverIndex.value === params.seriesIndex) return;
      if (mode === 'hover') {
        return (hoverIndex.value = params.seriesIndex);
      }
      if (mode === 'out') {
        return (hoverIndex.value = null);
      }
    };
    const hoverDebFunc = debounceFunc(hoverFunc);
    watch(
      () => echartsRef.value?.chartRef,
      () => {
        echartsRef.value?.chartRef?.on('mouseover', 'series.surface', (params: any) => {
          hoverDebFunc(params);
        });
        echartsRef.value?.chartRef?.on('globalout', 'series.surface', (params: any) => {
          hoverDebFunc(params, 'out');
        });
      },
      { immediate: true },
    );

    return {
      pieOption,
      echartsRef,
    };
  },
});
</script>

<style lang="stylus" scoped></style>
