<template lang="pug">
.com_base_rose_pie_design.w-full.h-full
  ComBaseRosePie(
    v-bind='$props',
    mode='half',
    :data='runtimeCheckRule(localData)'
)
</template>
<!-- 半环形图 -->
<script lang="ts">
import { PropType, computed, toRefs } from 'vue';
import ComBaseRosePie from '@/engines/screen/components/global/screen/charts/Pie/ComBaseRosePie.vue';
import {
  PieData,
  PieRadius,
  PieRoseType,
} from '@/engines/screen/components/global/screen/charts/baseUtils/baseChat';
import { defineTaBuilderCellComponent } from '@/components/global/ta-component/TaBuilder/builder/defineTaBuilderCellComponent';
import { dataPropsFn } from '../../../screen/components/echartBaseProps';
import { useScreenDataFetchCollectionInject } from '../../../../../../../components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';
import { LegendOptOrient, PieLengendPosition, LegendOptType } from '../../../screen/charts/baseUtils/baseChat';

const defaultData = [
  {
    seriesName: 'pie-1',
    seriesData: [
      { value: 1048, name: 'Search Engine' },
      { value: 735, name: 'Direct' },
      { value: 580, name: 'Email' },
      { value: 484, name: 'Union Ads' },
      { value: 300, name: 'Video Ads' },
    ],
  },
];

const ComBaseRosePieDesign = defineTaBuilderCellComponent({
  name: 'ComBaseRosePieDesign',
  components: {
    ComBaseRosePie,
  },
  props: {
    ...dataPropsFn({ group: '', modelKeyPrefix: '' }, 'props', 'json', () => defaultData),

    customizeAreaColor: {
      type: Array as PropType<string[]>,
      formType: 'json',
      label: '自定义填充区域颜色序列,默认则为空数组',
      default: () => [],
    },
    center: {
      type: Array,
      formType: 'json',
      label: '饼图中心',
      default: ['50%', '50%'],
    },
    unit: {
      label: '饼图单位',
      formType: 'input',
      type: String,
      default: '',
    },
    radius: {
      formType: 'json',
      label: '饼图半径',
      type: Array as PropType<PieRadius>,
      default: ['10%', '80%'],
    },
    roseType: {
      type: String as PropType<PieRoseType>,
      formType: 'radio',
      label: '玫瑰图类型',
      select: [
        { label: '按面积', value: 'area' },
        { label: '按弧度', value: 'radius' },
      ],
      default: 'radius',
    },
    showTooltip: {
      type: Boolean,
      formType: 'switch',
      label: '是否展示tooltip',
      default: true,
    },
    showLegend: { type: Boolean, formType: 'switch', label: '是否展示图例', default: false },
    showLabel: {
      type: Boolean,
      formType: 'switch',
      label: '是否展示标签',
      default: true,
    },
    legendOrient: {
      type: String as PropType<LegendOptOrient>,
      formType: 'radio',
      label: '图例方向',
      select: [
        { label: '平行', value: 'horizontal' },
        { label: '垂直', value: 'vertical' },
      ],
      default: 'horizontal'
    },
    legendTextColor: {
      label: '图例文本颜色',
      type: String,
      formType: 'input',
      default: () => '#000000',
    },
    // 图例位置 top right bottom right
    legendPosition: {
      type: Array as PropType<PieLengendPosition>,
      formType: 'json',
      label: '图例位置',
      default: () => [0, 0, 0, 0]
    },
    legendType: {
      type: String as PropType<LegendOptType>,
      formType: 'radio',
      label: '图例类型',
      select: [
        { label: '平放', value: 'plain' },
        { label: '滚动', value: 'scroll' },
      ],
      default: () => 'plain'
    },
    otherLegendOpts: {
      type: Object,
      formType: 'json',
      label: '图例额外配置',
      default: () => { }
    },
    showPercent: {
      type: Boolean,
      formType: 'switch',
      label: '是否展示百分比（适用于需要value和percent的场景）',
      default: false,
    },
    labelLine: {
      label: 'label显示1行还是两行',
      formType: 'radio',
      type: Number,
      select: [
        { label: '1', value: 1 },
        { label: '2', value: 2 },
      ],
      default: () => 1,
    },
    labelGap: {
      label: 'label显示2行时，2行的间隔',
      formType: 'number',
      type: Number,
      default: () => 4,
    },
    labelLineLength: {
      label: 'label第一段指引线长度',
      formType: 'number',
      type: Number,
      default: () => 10,
    },
    labelLineLength2: {
      label: 'label第二段指引线长度',
      formType: 'number',
      type: Number,
      default: () => 10,
    },
    labelColor: {
      label: 'label第一行颜色',
      formType: 'input',
      type: String,
      default: () => 'white',
    },
    secLabelColor: {
      label: 'label第二行颜色',
      formType: 'input',
      type: String,
      default: () => 'white',
    },
    percentLabelColor: {
      label: 'label-percent颜色',
      formType: 'input',
      type: String,
      default: () => 'white',
    },
    borderColor: {
      label: '边框颜色',
      formType: 'input',
      type: String,
      default: '#000',
    },
    borderRadius: { label: '饼图外圈圆角', formType: 'number', type: Number, default: 4 },

    borderWidth: {
      label: '边框宽度',
      formType: 'number',
      type: Number,
      default: 0,
    },
  },
  setup(props: any) {
    const { getScreenData } = useScreenDataFetchCollectionInject(props);
    const localData = computed(() => {
      return props.data || getScreenData(props, '');
    });
    const runtimeCheckRule = (data: PieData[]) => {
      let flag = true;
      data.map(pie => {
        !Array.isArray(pie.seriesData) ? (flag = false) : null;
        return pie;
      });
      if (flag) return data;

      return defaultData;
    };
    return {
      ...toRefs(props),
      localData,
      runtimeCheckRule,
    };
  },
});
export default ComBaseRosePieDesign;
</script>
<style lang="stylus" scoped></style>
