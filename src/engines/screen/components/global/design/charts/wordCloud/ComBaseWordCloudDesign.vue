<template lang="pug">
ComBaseWordCloud(
  v-bind='$props',
  :data='runtimeCheckRule(localData)',
)
</template>
<script lang="ts">
import { toRefs, PropType, computed } from 'vue';
import { defineTaBuilderCellComponent } from '@/components/global/ta-component/TaBuilder/builder/defineTaBuilderCellComponent';

import { wordCloudColor } from '../../../screen/charts/baseUtils/baseChat';
import { VObject } from '@/lib/vails';
import ComBaseWordCloud from '../../../screen/charts/WordCloud/ComBaseWordCloud.vue';
import { dataPropsFn } from '../../../screen/components/echartBaseProps';
import { useScreenDataFetchCollectionInject } from '@/components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';

const defaultData = {
  seriesName: 'wordCloud-1',
  seriesData: [
    {
      name: '通信',
      weight: '',
    },
    {
      name: '移动通信',
      weight: '',
    },
    {
      name: '通信协议及信号处理',
      weight: '',
    },
    {
      name: '计算机',
      weight: '',
    },
    {
      name: '网络接入及传输',
      weight: '',
    },
    {
      name: '有线电通信',
      weight: '',
    },
    {
      name: '互联网通信',
      weight: '',
    },
    {
      name: '电子',
      weight: '',
    },
    {
      name: '通信服务',
      weight: '',
    },
    {
      name: '移动通信终端',
      weight: '',
    },
    {
      name: '基站',
      weight: '',
    },
    {
      name: '网络空间安全',
      weight: '',
    },
    {
      name: '网络规划设计',
      weight: '',
    },
    {
      name: '网络端点与互联安全',
      weight: '',
    },
    {
      name: '计算机应用软件',
      weight: '',
    },
    {
      name: '消费电子',
      weight: '',
    },
    {
      name: '计算机硬件',
      weight: '',
    },
    {
      name: '无线电通信',
      weight: '',
    },
    {
      name: '仪器仪表',
      weight: '',
    },
  ],
};

export default defineTaBuilderCellComponent({
  name: 'ComBaseWordCloudDesign',
  components: {
    ComBaseWordCloud,
  },
  props: {
    ...dataPropsFn({ group: '', modelKeyPrefix: '' }, 'props', 'json', () => defaultData),

    colorList: {
      label: '词云颜色数组',
      formType: 'json',
      type: Array,
      default: wordCloudColor,
    },
    isMaskImage: {
      type: Boolean,
      formType: 'switch',
      label: '是否显示遮罩形状',
      default: true,
    },
    sizeRange: {
      label: '词云大小范围',
      formType: 'json',
      type: Array,
      default: [12, 24],
    },
    gridSize: {
      type: Number,
      formType: 'number',
      label: '词云间隔大小',
      default: 18,
    },
  },

  setup(props: VObject) {
    const { getScreenData } = useScreenDataFetchCollectionInject(props);
    const localData = computed(() => {
      return props.data || getScreenData(props, '');
    });
    const runtimeCheckRule = (data: VObject) => {
      let flag = true;

      !Array.isArray(data.seriesData) ? (flag = false) : null;
      if (flag) return data;

      return defaultData;
    };
    return {
      ...toRefs(props),
      runtimeCheckRule,
      localData,
    };
  },
});
</script>
<style lang="stylus" scoped></style>
