<template lang="pug">
.w-full.h-full
  ComScreenScatter(
    v-bind='$props',
    :data='runtimeCheckRule(localData)'
  )
</template>
<!-- 基础面积图 -->
<script lang="ts">
import { PropType, computed, toRefs } from 'vue';
import { VScatterData } from '../../../screen/charts/baseUtils/baseChat';
import { defineTaBuilderCellComponent } from '@/components/global/ta-component/TaBuilder/builder/defineTaBuilderCellComponent';
import { dataPropsFn } from '../../../screen/components/echartBaseProps';
import { useScreenDataFetchCollectionInject } from '@/components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';
import { VObject } from '@/lib/vails';
import ComScreenScatter from '../../../screen/charts/scatter/ComScreenScatter.vue';

const defaultData = [
  {
    seriesName: 'line-1',
    seriesData: [
      [10.0, 8.04],
      [8.07, 6.95],
      [13.0, 7.58],
      [9.05, 8.81],
      [11.0, 8.33],
      [14.0, 7.66],
      [13.4, 6.81],
      [10.0, 6.33],
      [14.0, 8.96],
      [12.5, 6.82],
      [9.15, 7.2],
      [11.5, 7.2],
      [3.03, 4.23],
      [12.2, 7.83],
      [2.02, 4.47],
      [1.05, 3.33],
      [4.05, 4.96],
      [6.03, 7.24],
    ],
  },
];

const ComScreenScatterDesign = defineTaBuilderCellComponent({
  name: 'ComScreenScatterDesign',
  components: {
    ComScreenScatter,
  },
  props: {
    ...dataPropsFn({ group: '', modelKeyPrefix: '' }, 'props', 'json', () => defaultData),

    unit: {
      formType: 'input',
      label: '显示单位',
      type: String,
      default: '',
    },
    unitPadding: {
      formType: 'json',
      label: '主轴单位位置[上, 右，下，左]',
      type: Array as PropType<number[]>,
      default: () => [0, 0, 0, 0],
    },
    showLengend: {
      type: Boolean,
      formType: 'switch',
      label: '是否显示图例',
      default: false,
    },
    lengendPosition: {
      formType: 'json',
      label: '图例位置[上, 右，下，左]',
      type: Array as PropType<number[]>,
      default: () => [4, 0, 0, 'right'],
    },

    customSymbols: {
      type: Array as PropType<string[]>,
      formType: 'json',
      label: '自定义点形状，默认为空',
      default: () => [],
    },
    customSymbolSize: {
      type: Array as PropType<number[]>,
      formType: 'json',
      label: '自定义点大小，默认为空',
      default: () => [],
    },
  },
  setup(props: VObject) {
    const { getScreenData } = useScreenDataFetchCollectionInject(props);
    const localData = computed(() => {
      return props.data || getScreenData(props, '');
    });
    const runtimeCheckRule = (data: VScatterData[]) => {
      let flag = true;
      data.map(line => {
        !Array.isArray(line.seriesData) ? (flag = false) : null;
        return line;
      });
      if (flag) return data;

      return defaultData;
    };
    return {
      ...toRefs(props),
      localData,
      runtimeCheckRule,
    };
  },
});
export default ComScreenScatterDesign;
</script>
<style lang="stylus" scoped></style>
