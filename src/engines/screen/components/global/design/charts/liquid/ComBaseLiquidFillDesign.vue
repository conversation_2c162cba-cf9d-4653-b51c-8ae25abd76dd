<template lang="pug">
.com_base_funnel_design.w-full.h-full
  ComBaseLiquidFill(
    v-bind='$props',
    :data='runtimeCheckRule(localData)'
  )
</template>
<script lang="ts">
import { PropType, computed, toRefs } from 'vue';
import ComBaseLiquidFill from '../../../screen/charts/liquid/ComBaseLiquidFill.vue';

import { defineTaBuilderCellComponent } from '@/components/global/ta-component/TaBuilder/builder/defineTaBuilderCellComponent';
import { useScreenDataFetchCollectionInject } from '@/components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';
import { dataPropsFn } from '../../../screen/components/echartBaseProps';

const defaultData = {
  liquidData: 80,
};

const ComBaseLiquidFillDesign = defineTaBuilderCellComponent({
  name: 'ComBaseLiquidFillDesign',
  components: {
    ComBaseLiquidFill,
  },
  props: {
    ...dataPropsFn({ group: '', modelKeyPrefix: '' }, 'props', 'json', () => defaultData),
    fontSize: {
      type: Number,
      formType: 'number',
      label: '字体大小',
      default: 28,
    },
    outWidth: {
      type: Number,
      formType: 'number',
      label: '外框大小',
      default: 4,
    },
    outColor: {
      type: String,
      formType: 'input',
      label: '外框颜色',
      default: 'rgba(0,150,255)',
    },
    colorStops: {
      type: Array as PropType<string[]>,
      formType: 'json',
      label: '自定义填充区域颜色序列',
      default: () => ['#0CB8EA', '#0CB8EA', '#0CB8EA'],
    },
  },
  setup(props: any) {
    const { getScreenData } = useScreenDataFetchCollectionInject(props);
    const localData = computed(() => {
      return props.data || getScreenData(props, '');
    });
    const runtimeCheckRule = (data: number) => {
      let flag = true;
      if (flag) return data;

      return defaultData;
    };
    return {
      ...toRefs(props),
      runtimeCheckRule,
      localData,
    };
  },
});
export default ComBaseLiquidFillDesign;
</script>
<style lang="stylus" scoped></style>
