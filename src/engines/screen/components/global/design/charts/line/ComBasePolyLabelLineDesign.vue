<template lang="pug">
.w-full.h-full
  ComBasePolyLabelLine(
    v-bind='$props',
    :data='runtimeCheckRule(localData)'
  )
</template>
<!-- 带数据折线图 -->
<script lang="ts">
import { PropType, computed, toRefs } from 'vue';
import ComBasePolyLabelLine from '../../../screen/charts/Line/ComBasePolyLabelLine.vue';

import { BaseData } from '../../../screen/charts/baseUtils/baseChat';
import { defineTaBuilderCellComponent } from '@/components/global/ta-component/TaBuilder/builder/defineTaBuilderCellComponent';
import { useScreenDataFetchCollectionInject } from '@/components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';
import { VObject } from '@/lib/vails';
import { dataPropsFn } from '../../../screen/components/echartBaseProps';

const defaultData = [
  {
    seriesName: 'line-1',
    seriesData: [120, 188, 150, 80, 70, 110],
  },
];

const ComBasePolyLabelLineDesign = defineTaBuilderCellComponent({
  name: 'ComBasePolyLabelLineDesign',
  components: {
    ComBasePolyLabelLine,
  },
  props: {
    ...dataPropsFn({ group: '', modelKeyPrefix: '' }, 'props', 'json', () => defaultData),

    customizeLineColor: {
      type: Array as PropType<string[]>,
      formType: 'json',
      label: '自定义线条颜色序列,默认则为空数组',
      default: () => [],
    },
    xAxisLabel: {
      type: Array as PropType<string[]>,
      formType: 'json',
      label: 'X轴显示名称，为空则不显示X轴标',
      default: () => ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'],
    },
    showUnit: {
      type: Boolean,
      formType: 'switch',
      label: '是否显示单位',
      default: false,
    },
    unit: {
      formType: 'input',
      label: '主轴单位',
      type: String,
      default: '',
    },
    unitPadding: {
      formType: 'json',
      label: '主轴单位位置[上, 右，下，左]',
      type: Array as PropType<number[]>,
      default: () => [0, 0, 0, 0],
    },
    showLengend: {
      type: Boolean,
      formType: 'switch',
      label: '是否显示图例',
      default: false,
    },
    lengendPosition: {
      type: Array,
      formType: 'json',
      label: '图例位置[上, 右，下，左]',
      default: () => [0, 0, 0, 0],
    },
    showLabel: {
      type: Boolean,
      label: '是否显示label标签',
      formType: 'switch',
      default: false,
    },
  },
  setup(props: VObject) {
    const { getScreenData } = useScreenDataFetchCollectionInject(props);
    const localData = computed(() => {
      return props.data || getScreenData(props, '');
    });
    const runtimeCheckRule = (data: BaseData[]) => {
      let flag = true;
      data.map(line => {
        !Array.isArray(line.seriesData) ? (flag = false) : null;
        return line;
      });
      if (flag) return data;

      return defaultData;
    };
    return {
      ...toRefs(props),
      localData,
      runtimeCheckRule,
    };
  },
});
export default ComBasePolyLabelLineDesign;
</script>
<style lang="stylus" scoped></style>
