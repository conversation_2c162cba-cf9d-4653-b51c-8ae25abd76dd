<template lang="pug">
.w-full.h-full
  ComBaseThemeRiver(
    :data='runtimeCheckRule(localData)'
    :customizeLineColor='customizeLineColor'
  )
</template>
<!-- 常规河流图 -->
<script lang="ts">
import { PropType, computed, defineComponent, toRefs } from 'vue';
import ComBaseThemeRiver from '../../../screen/charts/ThemeRiver/ComBaseThemeRiver.vue';
import { ThemeRiverData } from '../../../screen/charts/baseUtils/baseChat';
import { defineTaBuilderCellComponent } from '@/components/global/ta-component/TaBuilder/builder/defineTaBuilderCellComponent';
import { VObject } from '@/lib/vails';
import { dataPropsFn } from '../../../screen/components/echartBaseProps';
import { useScreenDataFetchCollectionInject } from '@/components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';

const defaultData = [
  ['2015/11/08', 10, 'DQ'],
  ['2015/11/09', 15, 'DQ'],
  ['2015/11/10', 35, 'DQ'],
  ['2015/11/08', 35, 'TY'],
  ['2015/11/09', 36, 'TY'],
  ['2015/11/10', 37, 'TY'],
  ['2015/11/11', 22, 'TY'],
  ['2015/11/08', 21, 'SS'],
  ['2015/11/09', 25, 'SS'],
  ['2015/11/10', 27, 'SS'],
  ['2015/11/08', 10, 'QG'],
  ['2015/11/09', 15, 'QG'],
  ['2015/11/10', 35, 'QG'],
  ['2015/11/08', 10, 'SY'],
  ['2015/11/09', 15, 'SY'],
  ['2015/11/10', 35, 'SY'],
  ['2015/11/08', 10, 'DD'],
  ['2015/11/09', 15, 'DD'],
  ['2015/11/10', 35, 'DD'],
];

const ComBaseThemeRiverDesign = defineTaBuilderCellComponent({
  name: 'ComBaseThemeRiverDesign',
  components: {
    ComBaseThemeRiver,
  },
  props: {
    ...dataPropsFn({ group: '', modelKeyPrefix: '' }, 'props', 'json', () => defaultData),

    // 自定义色彩，【或者可以支持组选新颜色?】
    customizeLineColor: {
      type: Array as PropType<string[]>,
      formType: 'json',
      label: '自定义线条颜色序列,默认则为空数组',
      default: () => [],
    },
    // 数据源
  },
  setup(props: VObject) {
    const { getScreenData } = useScreenDataFetchCollectionInject(props);
    const localData = computed(() => {
      return props.data || getScreenData(props, '');
    });
    const runtimeCheckRule = (data: any[]) => {
      let flag = true;
      data.map(line => {
        !Array.isArray(line) ? (flag = false) : null;
        return line;
      });
      if (flag) return data;

      return defaultData;
    };
    return {
      ...toRefs(props),
      localData,
      runtimeCheckRule,
    };
  },
});
export default ComBaseThemeRiverDesign;
</script>
<style lang="stylus" scoped></style>
