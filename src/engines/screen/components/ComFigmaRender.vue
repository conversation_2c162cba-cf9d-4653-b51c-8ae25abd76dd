<script lang="ts">
import domToImage from 'dom-to-image';
import { merge } from 'lodash';
import { computed, defineComponent, nextTick, onMounted, onUpdated, ref, toRefs, watch } from 'vue';
import { MessageCenter } from './MessageCenter';
import { useComFigmaDrawerRenderComponentsInject } from './useComFigmaDrawerRenderComponents';

let uniqSeq = 1;

const ComFigmaRender = defineComponent({
  name: 'ComFigmaRender',
  components: {},
  props: { data: { type: Object, default: () => ({}) } },
  mounted() {
    useComFigmaDrawerRenderComponentsInject(this);
  },
  setup(props, { emit }) {
    const seq = uniqSeq++;
    const elementRef = ref(null);

    const localData = computed({
      get: () => props.data,
      set: val => emit('update:data', val),
    });

    const compileToImage = () => {
      if (elementRef.value) {
        domToImage.toPng(elementRef.value).then(async (dataUrl: string) => {
          const img = new Image();
          img.src = dataUrl;

          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d')!;
          img.setAttribute('crossOrigin', 'Anonymous');

          img.onload = async () => {
            ctx.canvas.width = img.width;
            ctx.canvas.height = img.height;
            ctx.drawImage(img, 0, 0);

            // const imageData = ctx.getImageData(0, 0, img.width, img.height);
            // console.log(imageData, 'imageData');

            const buffer = await new Promise((resolve, reject) => {
              canvas.toBlob(blob => {
                const reader = new FileReader();
                reader.onload = () => resolve(new Uint8Array(reader.result as ArrayBuffer));
                reader.onerror = () => reject(new Error('Could not read from blob'));
                reader.readAsArrayBuffer(blob!);
              });
            });

            MessageCenter.trigger('render-node', {
              seq,
              buffer,
              width: img.width,
              height: img.height,
            });
          };
        });
      }
    };

    onMounted(compileToImage);
    onUpdated(compileToImage);

    let timeout: any = null;

    watch(
      () => props,
      () => {
        if (timeout) {
          clearTimeout(timeout);
        }
        timeout = setTimeout(compileToImage, 500);
      },
      { deep: true },
    );

    const width = ref('fit-content');
    const height = ref('fit-content');
    const x = ref('0px');
    const y = ref('0px');
    const transform = ref('');

    const setInfo = (info: any) => {
      width.value = `${info.width}px`;
      height.value = `${info.height}px`;
      x.value = `${info.x}px`;
      y.value = `${info.y}px`;
      transform.value = `rotate(${-info.rotation}deg)`;

      merge(localData.value.cssc, { x: info.x, y: info.y, w: info.width, h: info.height });

      nextTick(compileToImage);
    };

    const slotComponent = ref<any>(null);

    const setSlotComponent = (comp: any) => {
      slotComponent.value = comp;
    };

    return {
      ...toRefs(props),
      elementRef,
      seq,
      setInfo,
      width,
      height,
      x,
      y,
      transform,
      setSlotComponent,
      slotComponent,
      compileToImage,
    };
  },
});
export default ComFigmaRender;
</script>

<template lang="pug">
.com-figma-render.absolute
  .w-full.h-full(ref='elementRef')
    slot(:setSlotComponent='setSlotComponent', :compileToImage='compileToImage')
</template>

<style lang="stylus" scoped>
.com-figma-render
  width v-bind(width)
  height v-bind(height)
  top v-bind(y)
  left v-bind(x)
  transform v-bind(transform)
  transform-origin left top
</style>
