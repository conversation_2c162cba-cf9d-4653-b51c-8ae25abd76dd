<script lang="ts">
import { ref, defineComponent, toRefs, watch } from 'vue';
import lottie from 'lottie-web';

const ComLottiePlayer = defineComponent({
  name: 'ComLottiePlayer',
  components: {},
  props: {
    path: { type: String, default: '' },
    animationData: { type: Object, default: undefined },
  },
  setup(props) {
    const container = ref<any>(null);

    watch(container, () => {
      if (container.value) {
        lottie.loadAnimation({
          container: container.value,
          renderer: 'svg',
          loop: true,
          autoplay: true,
          path: props.path,
          animationData: props.animationData,
        });
      }
    });

    return {
      ...toRefs(props),
      container,
    };
  },
});
export default ComLottiePlayer;
</script>

<template lang="pug">
.com-lottie-player(ref='container')
</template>

<style lang="stylus" scoped></style>
