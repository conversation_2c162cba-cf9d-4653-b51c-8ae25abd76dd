import { provide } from '@vue/runtime-core';
import { inject, ref, Ref } from 'vue';

const KEY = 'ComFigmaDrawerRenderComponents';

export const useComFigmaDrawerRenderComponentsProvide = () => {
  const renderComponents = ref<any[]>([]);
  provide(KEY, renderComponents);
  return { renderComponents };
};

export const useComFigmaDrawerRenderComponentsInject = (proxy: any) => {
  const renderComponents = inject<Ref<any[]> | null>(KEY, null);
  if (renderComponents) {
    renderComponents.value.push(proxy);
  }
  return { renderComponents };
};
