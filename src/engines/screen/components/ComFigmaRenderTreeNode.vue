<script lang="ts">
import { ref, defineComponent, toRefs } from 'vue';
import ComFigmaRender from './ComFigmaRender.vue';
import { taBuilderTreeNodeProps } from '@/components/global/ta-component/TaBuilder/builder/TaBuilderTreeNode.vue';
import { VObject } from '@/lib/vails';

const ComFigmaRenderTreeNode = defineComponent({
  name: 'ComFigmaRenderTreeNode',
  components: {
    ComFigmaRender,
  },
  props: {
    ...taBuilderTreeNodeProps,
  },
  setup(props, { emit }) {
    const runPop = (key: string, compileToImage: () => void) => {
      if (key === 'compileToImage') {
        compileToImage();
      }
    };

    return {
      ...toRefs(props),
      runPop,
    };
  },
});
export default ComFigmaRenderTreeNode;
</script>

<template lang="pug">
ComFigmaRender.com-figma-render-tree-node(v-model:data='componentSchema')
  template(#default='{ setSlotComponent, compileToImage }')
    TaBuilderTreeNode(v-bind='$props', @reportComponent='(comp) => setSlotComponent(comp)', @pop='(key) => runPop(key, compileToImage)')
      template(#default='{ componentSchema, slotArguments }')
        ComFigmaRenderTreeNode(:componentSchema='componentSchema', :slotArguments='slotArguments')
</template>

<style lang="stylus" scoped></style>
