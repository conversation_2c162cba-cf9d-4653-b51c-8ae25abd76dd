import { VObject } from '@/lib/vails/model/index';

interface listenerInterface {
  type: string;
  callback: (data: VObject) => void;
}

export class MessageCenter {
  static trigger(type: string, data: VObject) {
    parent.postMessage(
      {
        pluginMessage: {
          type,
          data,
        },
      },
      '*',
    );
  }

  listeners: listenerInterface[] = [];

  defineListener(type: string, callback: (data: VObject) => void) {
    this.listeners.push({ type, callback });
  }

  listen = (event: VObject) => {
    if (event.data?.recallMessage) {
      const { type, data } = event.data.recallMessage;

      this.listeners.forEach(listener => {
        if (listener.type === type) {
          listener.callback(data);
        }
      });
    }
  };
}
