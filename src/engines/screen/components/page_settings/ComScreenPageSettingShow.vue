<script lang="ts">
import { ref, defineComponent, toRefs, computed } from 'vue';
import { useScreenDataFetchCollectionProvide } from '@/components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';
import ComScreenPageSettingConstruction from './ComScreenPageSettingConstruction.vue';
import { useContextProvide } from '@/components/global/ta-component/ta-template-form-core/useContext';
import { useRoute } from 'vue-router';

const ComScreenPageSettingShow = defineComponent({
  name: 'ComScreenPageSettingShow',
  components: {
    ComScreenPageSettingConstruction,
  },
  props: {
    pageSetting: { type: Object, default: undefined },
    initContext: { type: Object, default: () => ({}) },
  },
  setup(props, { emit }) {
    const route = useRoute();
    const { dataResult } = useScreenDataFetchCollectionProvide(props.initContext);

    const context = useContextProvide(dataResult as any, route);
    return {
      ...toRefs(props),
      context,
    };
  },
});
export default ComScreenPageSettingShow;
</script>

<template lang="pug">
ComScreenPageSettingConstruction(:pageSetting='pageSetting')
</template>

<style lang="stylus" scoped></style>
