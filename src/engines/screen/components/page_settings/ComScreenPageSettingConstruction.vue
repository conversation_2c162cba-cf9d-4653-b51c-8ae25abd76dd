<script lang="ts">
import { ref, defineComponent, toRefs, computed, nextTick, watch } from 'vue';
import { cloneDeep, isEqual } from 'lodash';
import { useRoute } from 'vue-router';
import { VStore, VObject } from '@/lib/vails';
import {
  useScreenDataFetchCollectionInject,
  useFetchScreenData,
} from '@/components/global/ta-component/TaBuilder/builder/useScreenDataFetchCollection';

let seq = 1;
const ComScreenPageSettingConstruction = defineComponent({
  name: 'ComScreenPageSettingConstruction',
  components: {},
  props: {
    pageSetting: { type: Object, default: undefined },
  },
  setup(props, { emit }) {
    const _id = String(seq++);
    const {
      dataResult,
      dataSourceCollectionMapping,
      // dataSourceCollection,
    } = useScreenDataFetchCollectionInject(props);
    const { fetchScreenData } = useFetchScreenData();

    const config = ref<VObject>({});
    // const dataSource = ref([]);

    watch(
      () => props.pageSetting,
      (newValue: VObject, oldValue?: VObject) => {
        if (props.pageSetting && !isEqual(newValue, oldValue)) {
          config.value = props.pageSetting.formData.conf.screenConfiguration;
          dataSourceCollectionMapping!.value[_id] = props.pageSetting.formData.conf.dataSource;
          if (dataResult) fetchScreenData(dataSourceCollectionMapping!.value[_id], dataResult);
        }
      },
      { immediate: true },
    );

    let oldValueCache: VObject | null = null;
    if (dataResult) {
      watch(
        dataResult,
        (newValue: VObject) => {
          let changed = false;
          const newKeys = Object.keys(newValue);
          const oldKeys = Object.keys(oldValueCache || {});
          const addedKeys = newKeys.filter(key => !oldKeys.includes(key));
          // 新增的 key
          addedKeys.forEach((key: string) => {
            if (typeof newValue[key] !== 'object') changed = true;
          });
          // 原有的 key， object 会保留引用
          if (!changed) {
            oldKeys.forEach((key: string) => {
              const oldVal = oldValueCache?.[key];
              const newVal = newValue[key];

              if (!isEqual(oldVal, newVal)) {
                changed = true;
              }
            });
          }
          if (changed) {
            fetchScreenData(dataSourceCollectionMapping!.value[_id], dataResult);
            oldValueCache = cloneDeep(newValue);
          }
        },
        { deep: true },
      );
    }

    const previewSchema = computed(() => ({
      name: 'preview',
      key: 'preview',
      component: 'TaBuilder',
      css: 'position:relative;',
      props: {
        value: cloneDeep(config.value),
      },
    }));

    return {
      ...toRefs(props),
      previewSchema,
      config,
    };
  },
});
export default ComScreenPageSettingConstruction;
</script>

<template lang="pug">
TaBuilderTreeNode.com-screen-page-setting-show(v-if='Object.keys(config).length > 0', :componentSchema='previewSchema')
</template>

<style lang="stylus" scoped></style>
