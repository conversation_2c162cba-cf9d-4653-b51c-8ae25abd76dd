<script lang="ts">
import { ComUserPageSettingsApi } from '@/engines/screen/apis/com/user/page_settings.api';
import { getThemeWaterMarkColor } from '@/engines/screen/utils/util';
import { VObject, VStore } from '@/lib/vails';
import { STORAGE_TOKEN_KEY } from '@/store/mutation-type';
import localStorage from '@/utils/local-storage';
import { cloneDeep, debounce } from 'lodash';
import { computed, defineComponent, nextTick, reactive, ref, toRefs, watch } from 'vue';
import { useRoute } from 'vue-router';
import { provideTaBuilderPreview } from '../../../../../../components/global/ta-component/TaBuilder/builder/usePreview';
import ComAdaptiveView from '../../../../components/ComAdaptiveView.vue';
import ComLottiePlayer from '../../../../components/ComLottiePlayer.vue';
import ComScreenPageSettingShow from '../../../../components/page_settings/ComScreenPageSettingShow.vue';

const EnginesScreenPagesShow = defineComponent({
  name: 'EnginesScreenPagesShow',
  components: {
    ComAdaptiveView,
    ComScreenPageSettingShow,
    ComLottiePlayer,
  },
  props: {
    root: { type: Boolean, default: true },
    pageSettingId: { type: Number, default: undefined },
    byWidth: { type: Boolean, default: false },
    rootElementId: { type: String, default: 'app' },
    skipLoading: { type: Boolean, default: false },
  },
  setup(props) {
    const route = useRoute();

    if (route.query.token) {
      localStorage.set(STORAGE_TOKEN_KEY, route.query.token);
    }
    const pageSettingId = Number(props.pageSettingId || route.params.pageSettingId);

    const pageSettingStore = new VStore(new ComUserPageSettingsApi());
    const container = ref<any>(null);

    const proportion = ref(1);
    const marginLeft = ref(0);

    const pageSettingMap = reactive<Record<number, VObject>>({});

    const loading = ref(false);

    const getPageSetting = (id: number, key = id) => {
      return pageSettingStore.find(id).then(() => {
        pageSettingMap[key] = pageSettingStore.record.value;
      });
    };

    if (!props.skipLoading) loading.value = true;
    getPageSetting(pageSettingId, 0).then(() => {
      nextTick(() => {
        container.value?.getViewSize?.();
        loading.value = false;
      });
    });

    const { previewMeta } = provideTaBuilderPreview();

    const visiblePageSettingIds = ref<number[]>([]);

    // if (previewMeta)
    const oldVisibleMap = ref<VObject>({});
    watch(
      () => previewMeta?.visibleMap,
      (newValue?: Record<string, boolean>) => {
        if (newValue) {
          Object.keys(newValue).forEach((idStr: string) => {
            if (!oldVisibleMap.value[idStr] && newValue[idStr]) {
              // 旧数据里 没有或为false，新数据里 有且为 true，为新增
              triggerAddVisible(Number(idStr));
            }
          });
          oldVisibleMap.value = cloneDeep(newValue);
        }
      },
      {
        deep: true,
      },
    );

    const triggerAddVisible = (id: number) => {
      if (!props.skipLoading) loading.value = true;
      getPageSetting(id).then(() => {
        getMarginLeft(id);
        if (!visiblePageSettingIds.value.includes(id)) {
          visiblePageSettingIds.value.push(id);
        }
        nextTick(() => (loading.value = false));
      });
    };

    const scalePage = (id: number) => {
      if (
        previewMeta?.limitSizeMap?.[id] &&
        previewMeta.limitSizeMap[id]?.attribute &&
        previewMeta.limitSizeMap[id]?.value
      ) {
        const pageValue =
          pageSettingMap[id].conf.screenConfiguration.flatData[0].cssc[
            previewMeta.limitSizeMap[id].attribute === 'width' ? 'w' : 'h'
          ];

        return (proportion.value * previewMeta.limitSizeMap[id].value!) / pageValue;
      }

      return proportion.value;
    };

    // const modalSizeMap = ref<Record<number, { w: number; h: number }>>({});
    const modalMarginLeftMap = reactive<Record<number, string>>({});

    const getMarginLeft = (id: number) => {
      nextTick(() => {
        if (pageSettingMap[id]) {
          const pageOriginWidth = pageSettingMap[0].conf.screenConfiguration.flatData[0].cssc.w;
          const modalOriginWidth = pageSettingMap[id].conf.screenConfiguration.flatData[0].cssc.w;
          const scale = scalePage(id);

          modalMarginLeftMap[id] = `${
            (pageOriginWidth * proportion.value - modalOriginWidth * scale) / 2 + marginLeft.value
          }px`;
        } else {
          modalMarginLeftMap[id] = `${marginLeft.value}px`;
        }
      });
    };

    const onClose = debounce((id: number, visible: boolean) => {
      if (
        previewMeta &&
        !visible &&
        previewMeta.pageSettingIdHistory[previewMeta.pageSettingIdHistory.length - 1] == String(id)
      ) {
        previewMeta.pageSettingIdHistory.pop();
      }
    }, 500);

    const showComponent = ref<any>(null);
    const context = computed(() => showComponent.value?.context);

    const showWaterMark = process.env.VUE_APP_SHOW_WATERMARK === 'true';

    const waterMarkText = process.env.VUE_APP_WATERMARK_TEXT || '';

    const modalClosable = process.env.VUE_APP_SCREEN_MODAL_CLOSABLE !== 'false';

    return {
      ...toRefs(props),
      loading,
      visibleMap: previewMeta?.visibleMap || {},
      contextMap: previewMeta?.contextMap || {},
      offsetTopMap: previewMeta?.offsetTopMap || {},
      pageSettingIdHistory: previewMeta?.pageSettingIdHistory || [],
      previewMeta,
      visiblePageSettingIds,
      container,
      proportion,
      marginLeft,
      scalePage,
      getMarginLeft,
      modalMarginLeftMap,
      pageSettingMap,
      onClose,
      showComponent,
      context,
      showWaterMark,
      waterMarkText,
      modalClosable,
      getThemeWaterMarkColor,
    };
  },
});
export default EnginesScreenPagesShow;
</script>

<template lang="pug">
.fixed.z-10000.inset-0.w-screen.h-screen.flex.justify-center.items-center.relative(
  v-if='loading',
)
  ComLottiePlayer.w-100.h-100(
    :animationData="require('@/engines/screen/assets/loading.json')"
  )
ComAdaptiveView.engines-screen-pages-show.w-full.h-full._css_preview(
  ref='container',
  :byWidth='byWidth',
  :root='root',
  :rootElementId='rootElementId',
  @getProportion='(val) => proportion = val'
  @getMarginLeft='(val) => marginLeft = val'
)
  ComScreenPageSettingShow(ref='showComponent', :v-if='pageSettingMap[0]?.id', :pageSetting='pageSettingMap[0]')
  .watermark_class.bottom-8px.text-center.w-full(
    v-if='showWaterMark' ,
    style='position: absolute; z-index: 10000;'
    :style='{"color": getThemeWaterMarkColor()}'
  ) {{ waterMarkText }}

  TaNoPaddingModal(
    v-for='id in visiblePageSettingIds'
    v-model:visible='visibleMap[id]',
    :footer='null',
    :headerStyle='{ display: "none" }',
    :bodyStyle='{ height: "fit-content", width: "fit-content" }',
    :modalContentStyle='{ height: "fit-content", width: "fit-content", background: "transparent", transform: `scale(${scalePage(id)})`, "transform-origin": "left top", marginLeft: modalMarginLeftMap[id] }',
    :antModalStyle='{ margin: proportion > 0 ? "0" : undefined }',
    :zIndex='400 + pageSettingIdHistory.findIndex(i => i === String(id))',
    width='fit-content',
    :closable='modalClosable',
    :top='offsetTopMap[id]',
    @update:visible='() => onClose(id, $event)',
  )
    ComScreenPageSettingShow(
      v-if='pageSettingMap[id]?.id && visibleMap[id]',
      :pageSetting='pageSettingMap[id]',
      :initContext='contextMap[id]'
    )
    .watermark_class.bottom-8px.text-center.w-full(
      v-if='showWaterMark' ,
      style='position: absolute; z-index: 10000;'
      :style='{"color": getThemeWaterMarkColor()}'
    ) {{ waterMarkText }}

  TaFilePreviewer(
    v-if='previewMeta.visibleFilePreview && previewMeta.previewAttachments.length > 0'
    v-model:visible='previewMeta.visibleFilePreview',
    :attachment='previewMeta.previewAttachments[0]',
    :attachmentList='previewMeta.previewAttachments',
  )
</template>

<style lang="stylus" scoped></style>
