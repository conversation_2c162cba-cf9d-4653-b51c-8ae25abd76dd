import { TaTemplateFormSelect } from '@/components/global/ta-component/ta-template-form-core/types';
import {
  insertJsonata,
  jsonataGet,
} from '@/components/global/ta-component/ta-template-form-core/useJsonata';
import { VObject, VStore } from '@/lib/vails';
import { isEqual, isEqualWith } from 'lodash';
import { Ref, WritableComputedRef, computed, ref } from 'vue';
import { AssessmentUserQuestionSettingsApi } from './apis/assessment/user/question_settings.api';
import { AssessmentQuestionSettingModel } from './models/assessment/question_settings';
import { AssessmentQuestionSheetModel } from './models/assessment/question_sheets';
import {
  AssessmentQuestionSetting,
  AssessmentQuestionSheet,
  AssessmentQuestionSheetFormItem,
} from './types/model';

export interface AssessmentQuestionSheetItemInterface {
  _id: string;
  name: string;
  type: string;
  depth?: number;
  enable_answer?: boolean;
  answer?: VObject;
  score?: number;
  config: VObject;
  materials?: VObject[];
  indexAry?: number[]; // 实时计算的索引数组
}

export interface AssessmentQuestionSheetCatalogInterface {
  _id: string;
  name: string;
  weight?: number;
  items: AssessmentQuestionSheetItemInterface[];
}

const answerEqualCustomizer = (value: any, otherValue: any) => {
  if (Array.isArray(value) && Array.isArray(otherValue)) {
    return isEqual(value.sort(), otherValue.sort());
  }
};

export const isAnswerEqual = (answer: any, question: AssessmentQuestionSheetItemInterface) => {
  return isEqualWith(answer, question.answer, answerEqualCustomizer);
};

export const useAssessmentQuestionSheet = (
  questionSheet:
    | Ref<AssessmentQuestionSheet>
    | WritableComputedRef<AssessmentQuestionSheet & AssessmentQuestionSheetModel>,
) => {
  const insertQuestionSheet = (name: string) => {
    return insertJsonata(name, (dataKey: string) =>
      jsonataGet({ questionSheet: questionSheet.value }, dataKey),
    );
  };

  const descAry = computed(
    () =>
      insertQuestionSheet(questionSheet.value.desc || '')
        ?.split('\n')
        .filter(i => i) || [],
  );

  const noCatalogFields = computed<AssessmentQuestionSheetItemInterface[]>({
    get: () => questionSheet.value.form!.fields!,
    set: val => {
      questionSheet.value.form!.fields = val;
    },
  });

  const noCatalog = computed({
    get: () => ({
      _id: 'no_catalog',
      name: '未分组',
      items: noCatalogFields.value,
    }),
    set: val => (noCatalogFields.value = val.items),
  });

  const questionSettingStore = new VStore(
    new AssessmentUserQuestionSettingsApi(),
    AssessmentQuestionSettingModel,
  );

  const getTemplate = (question: AssessmentQuestionSheetFormItem) => {
    return AssessmentQuestionSettingModel.insertConfigToTemplate(
      questionSettingFlag2Record.value[question.type],
      question.config,
      question.name,
    );
  };

  const isRequired = (question: AssessmentQuestionSheetFormItem) => {
    return AssessmentQuestionSettingModel.isRequired(question.config);
  };

  const questionSettingFlag2Record = ref<VObject>({});
  const questionSettingOptions = ref<TaTemplateFormSelect[]>([]);

  const loading = ref(true);

  const fetchQuestionSettings = () => {
    questionSettingStore.index({ per_page: 999999 }).then(() => {
      questionSettingStore.records.value.forEach((questionSetting: AssessmentQuestionSetting) => {
        questionSettingOptions.value.push({
          label: questionSetting.name,
          value: questionSetting.flag,
        });
        questionSettingFlag2Record.value[questionSetting.flag] = questionSetting;
      });
      loading.value = false;
      console.log('loading.value: ', loading);
    });
  };

  const calcIndexAry = (
    current: AssessmentQuestionSheetItemInterface,
    previous?: AssessmentQuestionSheetItemInterface,
  ) => {
    let result: number[] = [];
    if (!previous) {
      result = [1];
    } else {
      const currentDepth = current.depth || 1;
      const previousDepth = previous.depth || 1;

      //  有材料时作为大题分隔
      if ((current.materials?.length || 0) > 0) {
        result = [previous.indexAry![0] + 1, 1];
      } else if (previousDepth > currentDepth) {
        result = [...previous.indexAry!];
        console.log(result[result.length - 1], 'result[result.length - 1] ');

        if (!result![currentDepth - 1]) result![currentDepth - 1] = result[result.length - 1] || 0;
        result![currentDepth - 1] += 1;
        result = result.slice(0, currentDepth);
      } else if (previousDepth < currentDepth) {
        result = [
          ...previous.indexAry!.slice(0, previous.indexAry!.length - 1),
          previous.indexAry![previous.indexAry!.length - 1] + 1,
          1,
        ];
      } else if (previousDepth === currentDepth) {
        result = [...previous.indexAry!];
        result![result.length - 1] += 1;
      }
    }

    if (!isEqual(result, current.indexAry)) {
      current.indexAry = result;
    }

    return result;
  };

  return {
    insertQuestionSheet,
    descAry,
    noCatalogFields,
    noCatalog,
    questionSettingStore,
    questionSettingOptions,
    getTemplate,
    isRequired,
    questionSettingFlag2Record,
    loading,
    fetchQuestionSettings,
    calcIndexAry,
  };
};
