import { VModel } from '@/lib/vails';
import { AssessmentQuestionSheet } from '@/engines/assessment/assessment-core/types/model';
import FileServer from '@/components/global/ta-component/file/servers';
import dayjs from 'dayjs';

const fileServer = new FileServer();

export class AssessmentQuestionSheetModel extends VModel<AssessmentQuestionSheet> {
  coverImageUrl = this.computedAttr('coverImageUrl', () => {
    if (this.reactiveRecord.cover_image?.files?.[0]) {
      return fileServer.getCDNUrl(this.reactiveRecord.cover_image.files[0]);
    }

    return '';
  });

  getTimeRangeStr = (format = 'YYYY/MM/DD', format2 = 'MM/DD', onlyShow = '') => {
    const start = this.reactiveRecord.effective_at;
    const end = this.reactiveRecord.invalid_at;

    if (!start && !end) {
      return '暂未设置';
    } else if (start && !end) {
      return `${dayjs(start).format(format)} 开始`;
    } else if (!start && end) {
      return `${dayjs(end).format(format)} 截止`;
    } else if (onlyShow == 'end' && dayjs(end).year() == dayjs().year()) {
      return `${dayjs(end).format(format)}`;
    } else if (onlyShow == 'start' && dayjs(end).year() == dayjs().year()) {
      return `${dayjs(start).format(format)}`;
    } else if (dayjs(start).year() != dayjs(end).year()) {
      return `${dayjs(start).format(format)} - ${dayjs(end).format(format)}`;
    } else {
      return `${dayjs(start).format(format)} - ${dayjs(end).format(format2)}`;
    }
  };

  timeRangeStr = this.computedAttr('timeRangeStr', () => {
    return this.getTimeRangeStr();
  });
}
