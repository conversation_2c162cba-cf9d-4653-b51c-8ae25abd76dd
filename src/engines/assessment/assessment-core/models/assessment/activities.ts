import { VModel } from '@/lib/vails';
import { AssessmentActivity } from '@/engines/assessment-mobiile/types/model';

export class AssessmentActivityModel extends VModel<AssessmentActivity> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return AssessmentActivityModel.stateMapping()[this.reactiveRecord.state];
  // });
  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}
