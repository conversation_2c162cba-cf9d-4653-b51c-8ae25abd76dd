import { VModel } from '@/lib/vails';
import { AssessmentScore } from '../../types/model';
import { VObject } from '../../../../../lib/vails/model/index';
import { TaTemplateFormSelect } from '../../../../../components/global/ta-component/ta-template-form-core/types';

export class AssessmentScoreModel extends VModel<AssessmentScore> {
  alreadyEdited = this.computedAttr('alreadyEdited', () => {
    return !!this.formData?.item_payload && JSON.stringify(this.formData.item_payload) !== '{}';
  });

  allFinished = this.computedAttr('allFinished', () => {
    return (
      (this.formData.score_template?.form?.catalogs || []).reduce(
        (o: number, catalog: VObject) =>
          o +
          catalog.items.filter(
            (item: VObject) =>
              Object.keys(this.formData.item_payload).includes(item._id) &&
              (!!this.formData.item_payload[item._id] ||
                this.formData.item_payload[item._id] === 0),
          ).length,
        0,
      ) >=
        (this.formData.score_template?.form?.catalogs || []).reduce(
          (o: number, catalog: VObject) => o + catalog.items.length,
          0,
        ) &&
      Object.values(this.formData.item_payload || {}).filter(i => typeof i === 'undefined')
        .length === 0
    );
  });

  static getAllFinished(formData: VObject): boolean {
    return (
      (formData.score_template?.form?.catalogs || []).reduce(
        (o: number, catalog: VObject) =>
          o +
          catalog.items.filter(
            (item: VObject) =>
              Object.keys(formData.item_payload).includes(item._id) &&
              !!formData.item_payload[item._id] &&
              formData.item_payload[item._id] !== 0,
          ).length,
        0,
      ) >=
        (formData.score_template?.form?.catalogs || []).reduce(
          (o: number, catalog: VObject) => o + catalog.items.length,
          0,
        ) &&
      Object.values(formData.item_payload || {}).filter(i => typeof i === 'undefined').length === 0
    );
  }

  getItemComponentType = (item: VObject) => {
    return item.type || this.reactiveRecord.score_template?.form?.type;
  };

  getItemComponentSelect = (item: VObject) => {
    return item.select || this.reactiveRecord.score_template?.form?.select;
  };

  getItemLabel = (item: VObject) => {
    const val = this.formData.item_payload[item._id];
    return this.getItemComponentSelect(item).find(
      (selectItem: TaTemplateFormSelect) =>
        (val && selectItem.value === val) || selectItem.label === val,
    )?.label;
  };
}
