import { VModel, VObject } from '@/lib/vails';
import { AssessmentQuestion } from '../../types/model';

export class AssessmentQuestionModel extends VModel<AssessmentQuestion> {
  kindText = this.computedAttr('kindText', () => {
    return AssessmentQuestionModel.kingMapping()[this.reactiveRecord.kind]?.label;
  });

  static kingMapping(): VObject {
    return {
      'Assessment::Attr::SingleChoice': { label: '单选题' },
      'Assessment::Attr::MultipleChoice': { label: '多选题' },
      'Assessment::Attr::FillBlank': { label: '填空题' },
      'Assessment::Attr::ShortAnswer': { label: '简答题' },
      'Assessment::Attr::Material': { label: '材料题' },
    };
  }
}
