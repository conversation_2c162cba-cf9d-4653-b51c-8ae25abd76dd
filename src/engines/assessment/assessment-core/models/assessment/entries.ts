import { VModel } from '@/lib/vails';
import { AssessmentEntry } from '@/engines/assessment-mobiile/types/model';

export class AssessmentEntryModel extends VModel<AssessmentEntry> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return AssessmentEntryModel.stateMapping()[this.reactiveRecord.state];
  // });
  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}
