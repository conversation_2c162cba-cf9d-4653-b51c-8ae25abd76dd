export const questionSettingSelectTemplateForm = {
  type: 'layout',
  model: {},
  key: 'layout_1663575015216_0',
  model_key: 'layout_1663575015216_0',
  fields: [
    {
      name: '选项',
      icon: 'FolderOutlined',
      type: 'list',
      fields: [
        {
          name: '选项',
          icon: 'FolderOutlined',
          type: 'input',
          rules: [
            {
              rule_type: 'required',
              type: 'string',
              required: true,
              message: '请填写正确的选项',
            },
          ],
          model: {
            attr_type: 'string',
          },
          options: {
            span: 24,
          },
          key: 'input_1663575034633_3',
          model_key: 'label',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
        {
          name: '值',
          icon: 'FolderOutlined',
          type: 'input',
          rules: [],
          model: {
            attr_type: 'string',
          },
          options: {
            span: 24,
          },
          key: 'input_1663575032166_2',
          model_key: 'value',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
      ],
      rules: [],
      model: {
        attr_type: 'array',
      },
      options: {
        span: 24,
        disabled_actions: {},
        edit_directly: true,
      },
      key: 'list_1663575027128_1',
      model_key: 'select',
      conditions: [],
      model_key_prefix: '',
    },
  ],
  conditions: [],
  options: {
    label: {},
    disabled_actions: {},
    theme: 'none',
    create_text: '提交',
    update_text: '提交',
  },
};

export const questionSettingPlaceholderTemplate = {
  type: 'layout',
  model: {},
  key: 'layout_1663575015216_0',
  model_key: 'layout_1663575015216_0',
  fields: [
    {
      name: '提示语',
      icon: 'FolderOutlined',
      type: 'input',
      rules: [],
      model: {
        attr_type: 'string',
      },
      options: {
        span: 24,
      },
      key: 'input_1663575139147_4',
      model_key: 'placeholder',
      fields: [],
      conditions: [],
      model_key_prefix: '',
    },
  ],
  conditions: [],
  options: {
    label: {},
    disabled_actions: {},
    theme: 'none',
    create_text: '提交',
    update_text: '提交',
  },
};

export const questionSettingRequiredTemplateForm = {
  type: 'layout',
  model: {},
  key: 'layout_1663588566563_0',
  model_key: 'layout_1663588566563_0',
  fields: [
    {
      name: '必填',
      icon: 'FolderOutlined',
      type: 'switch',
      rules: [],
      model: {
        attr_type: 'boolean',
      },
      options: {
        span: 24,
      },
      key: 'switch_1663588574005_2',
      model_key: 'required',
      fields: [],
      conditions: [],
      model_key_prefix: '',
    },
  ],
  conditions: [],
  options: {
    label: {},
    disabled_actions: {},
    theme: 'none',
    create_text: '提交',
    update_text: '提交',
  },
};

export const questionSettingConditionTargetTemplate = {
  type: 'layout',
  model: {},
  key: 'layout_1663588566563_0',
  model_key: 'layout_1663588566563_0',
  fields: [
    {
      name: '目标值',
      icon: 'FolderOutlined',
      type: 'input',
      rules: [],
      model: {
        attr_type: 'string',
      },
      options: {
        span: 24,
      },
      key: 'input_1663588574005_2',
      model_key: 'condition_target',
      fields: [],
      conditions: [],
      model_key_prefix: '',
    },
  ],
  conditions: [],
  options: {
    label: {},
    disabled_actions: {},
    theme: 'none',
    create_text: '提交',
    update_text: '提交',
  },
};

export const questionSettingConditionFieldsTemplate = {
  type: 'layout',
  model: {},
  key: 'layout_1663588566563_0',
  model_key: 'layout_1663588566563_0',
  fields: [
    {
      name: '自定义条件表单',
      icon: 'FolderOutlined',
      type: 'form_designer',
      rules: [],
      model: {
        attr_type: 'object',
      },
      options: {
        span: 24,
      },
      key: 'form_designer_1663588574005_2',
      model_key: 'condition_field',
      fields: [],
      conditions: [],
      model_key_prefix: '',
    },
  ],
  conditions: [],
  options: {
    label: {},
    disabled_actions: {},
    theme: 'none',
    create_text: '提交',
    update_text: '提交',
  },
};

export const questionSettingListFieldsTemplate = {
  type: 'container_layout',
  key: 'container_layout_1719640844100_6',
  model_key: 'container_layout_1719640844100_6',
  model_key_configuration: [],
  fields: [
    {
      name: '小题',
      icon: 'FolderOutlined',
      type: 'list',
      fields: [
        {
          name: '小题题目',
          icon: 'FolderOutlined',
          type: 'textarea',
          rules: [],
          model: { attr_type: 'string' },
          options: { span: 24 },
          key: 'textarea_1719640859645_8',
          model_key: 'name',
          model_key_configuration: [],
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
      ],
      rules: [],
      model: { attr_type: 'array' },
      options: { span: 24, disabled_actions: {}, edit_directly: true },
      key: 'list_1719640848741_7',
      model_key: 'list',
      model_key_configuration: [],
      conditions: [],
      model_key_prefix: '',
    },
  ],
  conditions: [],
  options: { label: {}, disabled_actions: {}, theme: { card: {}, background: {}, form: {} } },
  model: { create_default_value: {}, update_default_value: {} },
};
