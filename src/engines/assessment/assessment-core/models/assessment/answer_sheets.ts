import { VModel, VStore } from '@/lib/vails';
import { AssessmentAnswerSheet } from '../../types/model';
import dayjs from 'dayjs';
import { ref } from 'vue';
import { AssessmentUserQuestionSheetsApi } from '../../apis/assessment/user/question_sheets.api';
import { AssessmentQuestionSheetModel } from './question_sheets';
import { cloneDeep } from 'lodash';

export class AssessmentAnswerSheetModel extends VModel<AssessmentAnswerSheet> {
  createdAtStr = this.computedAttr('createdAtStr', () => {
    return dayjs(this.reactiveRecord.created_at).format('YYYY-MM-DD HH:mm');
  });

  questionSheetApi = ref<any>(new AssessmentUserQuestionSheetsApi());

  questionSheetModel = this.computedAttr('questionSheetModel', () => {
    return this.returnNestedModel(
      new AssessmentQuestionSheetModel(
        cloneDeep(this.reactiveRecord.question_sheet || {}),
        new VStore(this.questionSheetApi.value, AssessmentQuestionSheetModel),
      ),
    );
  });
}
