import { VModel, VObject } from '@/lib/vails';
import { AssessmentQuestionPaper } from '@/engines/assessment/assessment-core/types/model';

export class AssessmentQuestionPaperModel extends VModel<AssessmentQuestionPaper> {
  arrangeModeText = this.computedAttr('arrangeModeText', () => {
    return AssessmentQuestionPaperModel.arrangeModeMapping()[this.reactiveRecord.arrange_mode]
      ?.label;
  });

  static arrangeModeMapping(): VObject {
    return {
      random: { label: '抽取题目' },
      fixed: { label: '固定题目' },
    };
  }
}
