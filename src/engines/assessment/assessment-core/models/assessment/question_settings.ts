import { VModel, VObject } from '@/lib/vails';
import { AssessmentQuestionSetting } from '@/engines/assessment/assessment-core/types/model';
import useProcessFields from '@/components/global/ta-component/ta-template-form-core/useProcessFields';
import { TaTemplateFormItem } from '@/components/global/ta-component/ta-template-form-core/types';
import {
  questionSettingSelectTemplateForm,
  questionSettingPlaceholderTemplate,
  questionSettingRequiredTemplateForm,
} from './template';
import { AssessmentQuestionSheetFormItemConfig } from '@/engines/assessment/assessment-core/types/model';
import { cloneDeep } from 'lodash';
import {
  questionSettingConditionTargetTemplate,
  questionSettingConditionFieldsTemplate,
  questionSettingListFieldsTemplate,
} from './template';

export class AssessmentQuestionSettingModel extends VModel<AssessmentQuestionSetting> {
  configFormFormAttr = this.formDataAttr('config_form', () => {
    const { processField } = useProcessFields();
    const result: VObject = {};

    if (this.reactiveRecord.formData.form) {
      processField(this.reactiveRecord.formData.form, {}, (item: TaTemplateFormItem) => {
        const fields: VObject[] = [];
        if (!result[item.key!]) result[item.key!] = {};

        if (AssessmentQuestionSettingModel.needRequired(item)) {
          fields.push(questionSettingRequiredTemplateForm);
        }

        if (AssessmentQuestionSettingModel.needSelect(item)) {
          fields.push(questionSettingSelectTemplateForm);
        }

        if (AssessmentQuestionSettingModel.needPlaceholder(item)) {
          fields.push(questionSettingPlaceholderTemplate);
        }

        if (AssessmentQuestionSettingModel.needConditionTarget(item)) {
          fields.push(questionSettingConditionTargetTemplate);
          fields.push(questionSettingConditionFieldsTemplate);
        }

        if (AssessmentQuestionSettingModel.needMultiQuestion(item)) {
          fields.push(questionSettingListFieldsTemplate);
        }

        if (fields.length > 0) {
          result[item.key!] = {
            key: `layout-${this.reactiveRecord.id}`,
            type: 'layout',
            fields,
            options: {
              theme: 'none',
            },
          };
        } else {
          result[item.key!] = undefined;
        }
      });
    }

    return result;
  });

  static needRequired(item: TaTemplateFormItem) {
    return !['layout', 'condition', 'key_layout', 'container_layout', 'list'].includes(item.type!);
  }

  static needSelect(item: TaTemplateFormItem) {
    return ['radio', 'select', 'checkbox'].includes(item.type!);
  }

  static needPlaceholder(item: TaTemplateFormItem) {
    return ['input', 'textarea', 'rich_text'].includes(item.type!);
  }

  static needConditionTarget(item: TaTemplateFormItem) {
    return ['condition'].includes(item.type!);
  }

  static needMultiQuestion(item: TaTemplateFormItem) {
    return ['list'].includes(item.type!);
  }

  static insertConfigToTemplate(
    questionSetting: AssessmentQuestionSetting,
    config: AssessmentQuestionSheetFormItemConfig,
    name: string,
  ) {
    if (!questionSetting) return {};
    const keys = Object.keys(config);
    const { processField } = useProcessFields();
    const result = cloneDeep(questionSetting.form);

    if (result.options) result.options.theme = 'none';
    processField(
      result,
      {},
      (item: TaTemplateFormItem, parent: TaTemplateFormItem) => {
        if (AssessmentQuestionSettingModel.needSelect(item) && item.options) {
          item.options.select = config[item.key!].select;
        }

        if (
          AssessmentQuestionSettingModel.needPlaceholder(item) &&
          item.options &&
          parent.type !== 'list'
        ) {
          item.options.placeholder = config[item.key!].placeholder;
        }

        if (AssessmentQuestionSettingModel.needRequired(item) && config[item.key!]?.required) {
          if (!item.rules) item.rules = [];
          item.rules.push({
            required: true,
            type: item.model?.attr_type as any,
            message: `请填写正确的${name}`,
          });
        }
        if (
          AssessmentQuestionSettingModel.needConditionTarget(item) &&
          config[item.key!]?.condition_target
        ) {
          if (item.conditions && item.conditions.length > 0) {
            item.conditions[0].val = config[item.key!].condition_target!;
          }
        }

        if (
          AssessmentQuestionSettingModel.needConditionTarget(item) &&
          config[item.key!]?.condition_field
        ) {
          if (item.conditions && item.conditions.length > 0) {
            item.conditions[0].fields!.push(config[item.key!].condition_field!);
          }
        }

        if (AssessmentQuestionSettingModel.needMultiQuestion(item) && item.type === 'list') {
          item.type = 'layout';
          item.fields = (config[item.key!] as any).list.map((item: VObject, index: number) => {
            return {
              name: item.name,
              model_key: `${item._id}-${index}`,
              type: 'textarea',
              model: {
                attr_type: 'string',
              },
              options: {
                span: 24,
              },
              rules:
                item.fields?.[0]?.key && config[item.fields[0].key!].required
                  ? [
                      {
                        required: true,
                        type: 'any',
                        message: `请填写正确的${item.name}`,
                      },
                    ]
                  : [],
            };
          });

          item.fields!.map((field: TaTemplateFormItem, index: number) => {
            if (keys.includes(field.key!)) {
              field.name = (config[field.key!] as any).list[index]?.name;
            }
            return field;
          });
        }
      },
      (item: TaTemplateFormItem, parent: TaTemplateFormItem) => {
        return keys.includes(item.key!) && parent.type !== 'list';
      },
    );

    return result;
  }

  static isRequired(config: AssessmentQuestionSheetFormItemConfig) {
    return Object.values(config)
      .map(i => i.required)
      .includes(true);
  }
}
