import { VApiConfig } from '@/lib/vails/api';
import { MyApi } from '@/apis/MyApi';
import { AssessmentScoreTemplate } from '@/engines/assessment/assessment-core/types/model';

export class AssessmentUserScoreTemplatesApi extends MyApi<AssessmentScoreTemplate> {
  constructor(config?: VApiConfig) {
    super({
      namespace: '/assessment/user',
      name: 'score_template',
      ...config,
    });
  }
}
