import { MyApi } from '@/apis/MyApi';
import { AssessmentScore } from '@/engines/assessment/assessment-core/types/model';
import { VApiConfig } from '@/lib/vails/api';

export class AssessmentUserScoredScoresApi extends MyApi<AssessmentScore> {
  constructor(config?: VApiConfig) {
    super({
      namespace: '/assessment/user/scored',
      name: 'score',
      actions: [
        { name: 'statistics', method: 'post', on: 'collection' },
        { name: 'score_stat', method: 'post', on: 'collection' },
      ],
      ...config,
    });
  }
}
