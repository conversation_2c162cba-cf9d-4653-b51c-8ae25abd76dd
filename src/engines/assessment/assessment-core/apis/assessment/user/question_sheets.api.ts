import { VApiConfig } from '@/lib/vails/api';
import { MyApi } from '@/apis/MyApi';
import { AssessmentQuestionSheet } from '../../../types/model';

export class AssessmentUserQuestionSheetsApi extends MyApi<AssessmentQuestionSheet> {
  constructor(config?: VApiConfig) {
    super({
      namespace: '/assessment/user',
      name: 'question_sheet',
      actions: [{ method: 'post', name: 'valid_passwd', on: 'member' }],
      ...config,
    });
  }
}
