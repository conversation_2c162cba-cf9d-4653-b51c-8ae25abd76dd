import { MyApi } from '@/apis/MyApi';
import { VApiConfig } from '@/lib/vails/api';
import { AssessmentAnswerSheet } from '../../../types/model';

export class AssessmentUserAnswerSheetsApi extends MyApi<AssessmentAnswerSheet> {
  constructor(config?: VApiConfig) {
    super({
      namespace: '/assessment/user',
      name: 'answer_sheet',
      actions: [{ name: 'ta_collection_statistic', method: 'post', on: 'collection' }],
      ...config,
    });
  }
}
