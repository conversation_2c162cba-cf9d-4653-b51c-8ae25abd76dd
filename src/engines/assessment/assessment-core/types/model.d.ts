import { TaTemplateFormSelect } from '@/components/global/ta-component/ta-template-form-core/types';
import { TaTemplateFormItem } from '@/components/global/ta-component/ta-template-form-core/types';
import { VObject } from '@/lib/vails/model/index';

export interface AssessmentActivity {
  id: number;
}
export interface AssessmentGroup {
  id: number;
}
export interface AssessmentCatalog {
  id: number;
  name: string;
}
export interface AssessmentDimension {
  id: number;
}
export interface AssessmentEntry {
  id: number;
}
export interface AssessmentScore {
  id: number;
  state: string;
  score_template?: AssessmentScoreTemplate;
}
export interface AssessmentEntries {
  id: number;
}

export interface AssessmentScoreTemplate {
  id: number;
  form: AssessmentScoreTemplateForm & AssessmentScoreTemplateFormType;
}

export interface AssessmentScoreTemplateFormType {
  type: string;
  select: TaTemplateFormSelect[];
}

export interface AssessmentScoreTemplateForm {
  catalogs: AssessmentScoreTemplateFormCatalog[];
}

export interface AssessmentScoreTemplateFormCatalog {
  _id: string;
  name: string;
  weight: number;
  items: (AssessmentScoreTemplateFormItem & Partial<AssessmentScoreTemplateFormType>)[];
}

export interface AssessmentScoreTemplateFormItem {
  _id: string;
  max_score: number;
  name: string;
  hint?: string;
}

export interface AssessmentQuestionSetting {
  id: number;
  name: string;
  flag: string;
  form: TaTemplateFormItem;
  config_form: {
    [key: string]: TaTemplateFormItem;
  };
}

export interface AssessmentQuestionSheetFormItem {
  _id: string;
  name: string;
  type: string;
  config: AssessmentQuestionSheetFormItemConfig;
}

export interface AssessmentQuestionSheetFormItemConfig {
  [key: string]: {
    select?: TaTemplateFormSelect[];
    placeholder?: string;
    unit?: string;
    required?: boolean;
    condition_target?: string;
    condition_field?: TaTemplateFormItem;
  };
}

export interface AssessmentQuestionSheet {
  id: number;
  cover_image: { files: VObject[] };
  form?: { fields: AssessmentQuestionSheetFormItem[] };
  effective_at: string;
  invalid_at: string;
  model_flag: string;
  desc?: string;
  type: string;
}

export interface AssessmentAnswerSheet {
  id: number;
  created_at: string;
  question_sheet: AssessmentQuestionSheet;
  payload?: any;
  score: number;
  state: string;
  question_sheet_id?: number;
  question_sheet_form?: TaTemplateFormItem;
}
export interface AssessmentEvaluation {
  id: number;
}

export interface AssessmentQuestionBank {
  id: number;
}
export interface AssessmentQuestion {
  id: number;
  kind: string;
}
export interface AssessmentQuestionPaper {
  id: number;
  arrange_mode: string;
}
