export default [
  {
    path: '/assessment/user/activities',
    name: 'assessmentUserActivitiesIndex',
    component: () =>
      import(
        /* webpackChunkName: "assessmentUserActivitiesIndex" */ '@/engines/assessment/views/assessment/user/activities/Index.vue'
      ),
    meta: {
      title: '考核',
    },
  },
  {
    path: '/assessment/user/activities/:activityId',
    name: 'assessmentUserActivitiesShow',
    component: () =>
      import(
        /* webpackChunkName: "assessmentUserActivitiesShow" */ '@/engines/assessment/views/assessment/user/activities/Show.vue'
      ),
    meta: {
      title: '考核',
      menuKey: '/assessment/user/activities',
    },
  },
];
