export default [
  {
    path: '/assessment/user/question_sheets',
    name: 'assessmentUserQuestionSheetsIndex',
    component: () =>
      import(
        /* webpackChunkName: "assessmentUserQuestionSheetsIndex" */ '@/engines/assessment/views/assessment/user/question_sheets/Index.vue'
      ),
    meta: {
      title: '问卷列表',
    },
  },
  {
    path: '/assessment/user/question_sheets/:questionSheetId',
    name: 'assessmentUserQuestionSheetsShow',
    component: () =>
      import(
        /* webpackChunkName: "assessmentUserQuestionSheetsShow" */ '@/engines/assessment/views/assessment/user/question_sheets/Show.vue'
      ),
    meta: {
      title: '答题',
      menuKey: '/assessment/user/question_sheets',
    },
  },
];
