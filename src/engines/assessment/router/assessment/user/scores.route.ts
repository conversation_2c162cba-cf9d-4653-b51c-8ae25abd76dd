export default [
  {
    path: '/assessment/user/activities/:activityId/scores',
    name: 'assessmentUserScoresIndex',
    component: () =>
      import(
        /* webpackChunkName: "assessmentUserScoresIndex" */ '@/engines/assessment/views/assessment/user/scores/Index.vue'
      ),
    meta: {
      title: 'assessmentUserScoresIndex',
    },
  },
  // {
  //   path: '/assessment/user/activities/:activityId/scores/:scoreId',
  //   name: 'assessmentUserScoresShow',
  //   component: () => import(/* webpackChunkName: "assessmentUserScoresShow" */ '@/engines/assessment/views/assessment/user/scores/Show.vue'),
  //   meta: {
  //     title: 'assessmentUserScoresShow',
  //   },
  // },
];
