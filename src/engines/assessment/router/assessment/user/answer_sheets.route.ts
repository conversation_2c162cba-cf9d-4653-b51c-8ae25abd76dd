export default [
  {
    path: '/assessment/user/answer_sheets',
    name: 'assessmentUserAnswerSheetsIndex',
    component: () =>
      import(
        /* webpackChunkName: "assessmentUserAnswerSheetsIndex" */ '@/engines/assessment/views/assessment/user/answer_sheets/Index.vue'
      ),
    meta: {
      title: '问卷调查答卷',
    },
  },
  {
    path: '/assessment/user/answer_sheets/:answerSheetId',
    name: 'assessmentUserAnswerSheetsShow',
    component: () =>
      import(
        /* webpackChunkName: "assessmentUserAnswerSheetsShow" */ '@/engines/assessment/views/assessment/user/answer_sheets/Show.vue'
      ),
    meta: {
      title: '问卷调查答卷',
      menuKey: '/assessment/user/answer_sheets',
    },
  },
];
