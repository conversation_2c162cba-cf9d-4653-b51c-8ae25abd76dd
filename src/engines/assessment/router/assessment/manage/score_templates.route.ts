export default [
  {
    path: '/assessment/manage/score_templates',
    name: 'assessmentManageScoreTemplatesIndex',
    component: () =>
      import(
        /* webpackChunkName: "assessmentManageScoreTemplatesIndex" */ '@/engines/assessment/views/assessment/manage/score_templates/Index.vue'
      ),
    meta: {
      title: '考核评分表',
    },
  },
  {
    path: '/assessment/manage/score_templates/:score_templateId',
    name: 'assessmentManageScoreTemplatesShow',
    component: () =>
      import(
        /* webpackChunkName: "assessmentManageScoreTemplatesShow" */ '@/engines/assessment/views/assessment/manage/score_templates/Show.vue'
      ),
    meta: {
      title: '考核评分表',
      menuKey: '/assessment/manage/score_templates',
    },
  },
];
