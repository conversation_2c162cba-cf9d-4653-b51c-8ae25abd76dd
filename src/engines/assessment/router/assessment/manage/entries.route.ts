export default [
  {
    path: '/assessment/manage/activities/:activityId/entries',
    name: 'assessmentManageEntriesIndex',
    component: () =>
      import(
        /* webpackChunkName: "assessmentManageEntriesIndex" */ '@/engines/assessment/views/assessment/manage/entries/Index.vue'
      ),
    meta: {
      title: 'assessmentManageEntriesIndex',
    },
  },
  {
    path: '/assessment/manage/activities/:activityId/entries/:entryId',
    name: 'assessmentManageEntriesShow',
    component: () =>
      import(
        /* webpackChunkName: "assessmentManageEntriesShow" */ '@/engines/assessment/views/assessment/manage/entries/Show.vue'
      ),
    meta: {
      title: 'assessmentManageEntriesShow',
    },
  },
];
