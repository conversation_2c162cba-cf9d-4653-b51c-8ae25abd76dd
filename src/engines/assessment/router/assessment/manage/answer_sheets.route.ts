export default [
  {
    path: '/assessment/manage/question_sheets/:questionSheetId/answer_sheets',
    name: 'assessmentManageAnswerSheetsIndex',
    component: () =>
      import(
        /* webpackChunkName: "assessmentManageAnswerSheetsIndex" */ '@/engines/assessment/views/assessment/manage/answer_sheets/Index.vue'
      ),
    meta: {
      title: '问卷管理',
    },
  },
  {
    path: '/assessment/manage/question_sheets/:questionSheetId/answer_sheets/:answerSheetId',
    name: 'assessmentManageAnswerSheetsShow',
    component: () =>
      import(
        /* webpackChunkName: "assessmentManageAnswerSheetsShow" */ '@/engines/assessment/views/assessment/manage/answer_sheets/Show.vue'
      ),
    meta: {
      title: '问卷管理',
      menuKey: '/assessment/manage/question_sheets/:questionSheetId/answer_sheets',
    },
  },
];
