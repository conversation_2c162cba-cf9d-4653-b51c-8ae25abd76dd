export default [
  {
    path: '/assessment/manage/question_sheet_templates',
    name: 'assessmentManageQuestionSheetTemplatesIndex',
    component: () =>
      import(
        /* webpackChunkName: "assessmentManageQuestionSheetTemplatesIndex" */ '@/engines/assessment/views/assessment/manage/question_sheet_templates/Index.vue'
      ),
    meta: {
      title: '问卷模板列表',
    },
  },
  {
    path: '/assessment/manage/question_sheet_templates/:questionSheetId',
    name: 'assessmentManageQuestionSheetTemplatesShow',
    component: () =>
      import(
        /* webpackChunkName: "assessmentManageQuestionSheetTemplatesShow" */ '@/engines/assessment/views/assessment/manage/question_sheet_templates/Show.vue'
      ),
    meta: {
      title: '问卷模板详情',
      menuKey: '/assessment/manage/question_sheet_templates',
    },
  },
];
