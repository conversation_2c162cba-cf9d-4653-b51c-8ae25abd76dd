export default [
  {
    path: '/assessment/manage/activities/:activityId/catalogs',
    name: 'assessmentManageCatalogsIndex',
    component: () =>
      import(
        /* webpackChunkName: "assessmentManageCatalogsIndex" */ '@/engines/assessment/views/assessment/manage/catalogs/Index.vue'
      ),
    meta: {
      title: 'assessmentManageCatalogsIndex',
    },
  },
  {
    path: '/assessment/manage/activities/:activityId/catalogs/:catalogId',
    name: 'assessmentManageCatalogsShow',
    component: () =>
      import(
        /* webpackChunkName: "assessmentManageCatalogsShow" */ '@/engines/assessment/views/assessment/manage/catalogs/Show.vue'
      ),
    meta: {
      title: 'assessmentManageCatalogsShow',
    },
  },
];
