export default [
  {
    path: '/assessment/manage/question_banks',
    name: 'assessmentManageQuestionBanksIndex',
    component: () =>
      import(
        /* webpackChunkName: "assessmentManageQuestionBanksIndex" */ '@/engines/assessment/views/assessment/manage/question_banks/Index.vue'
      ),
    meta: {
      title: '题库管理',
    },
  },
  {
    path: '/assessment/manage/question_banks/:questionBankId',
    name: 'assessmentManageQuestionBanksShow',
    component: () =>
      import(
        /* webpackChunkName: "assessmentManageQuestionBanksShow" */ '@/engines/assessment/views/assessment/manage/question_banks/Show.vue'
      ),
    meta: {
      menuKey: '/assessment/manage/question_banks',
      title: '题库详情',
    },
  },
];
