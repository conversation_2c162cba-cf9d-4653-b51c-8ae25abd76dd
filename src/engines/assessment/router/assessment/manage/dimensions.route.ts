export default [
  {
    path: '/assessment/manage/activities/:activityId/dimensions',
    name: 'assessmentManageDimensionsIndex',
    component: () =>
      import(
        /* webpackChunkName: "assessmentManageDimensionsIndex" */ '@/engines/assessment/views/assessment/manage/dimensions/Index.vue'
      ),
    meta: {
      title: 'assessmentManageDimensionsIndex',
    },
  },
  {
    path: '/assessment/manage/activities/:activityId/dimensions/:dimensionId',
    name: 'assessmentManageDimensionsShow',
    component: () =>
      import(
        /* webpackChunkName: "assessmentManageDimensionsShow" */ '@/engines/assessment/views/assessment/manage/dimensions/Show.vue'
      ),
    meta: {
      title: 'assessmentManageDimensionsShow',
    },
  },
];
