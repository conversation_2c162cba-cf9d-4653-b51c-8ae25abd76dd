export default [
  {
    path: '/assessment/manage/question_sheets',
    name: 'assessmentManageQuestionSheetsIndex',
    component: () =>
      import(
        /* webpackChunkName: "assessmentManageQuestionSheetsIndex" */ '@/engines/assessment/views/assessment/manage/question_sheets/Index.vue'
      ),
    meta: {
      title: '问卷列表',
    },
  },
  {
    path: '/assessment/manage/question_sheets/:questionSheetId',
    name: 'assessmentManageQuestionSheetsShow',
    component: () =>
      import(
        /* webpackChunkName: "assessmentManageQuestionSheetsShow" */ '@/engines/assessment/views/assessment/manage/question_sheets/Show.vue'
      ),
    meta: {
      title: '问卷详情',
      menuKey: '/assessment/manage/question_sheets',
    },
  },
  {
    path: '/assessment/manage/question_sheets/:questionSheetId/dashboard',
    name: 'assessmentManageQuestionSheetsDashboard',
    component: () =>
      import(
        /* webpackChunkName: "assessmentManageQuestionSheetsDashboard" */ '@/engines/assessment/views/assessment/manage/question_sheets/Dashboard.vue'
      ),
    meta: {
      title: '问卷统计',
      menuKey: '/assessment/manage/question_sheets',
    },
  },
];
