export default [
  {
    path: '/assessment/manage/activities/:activityId/scores',
    name: 'assessmentManageScoresIndex',
    component: () =>
      import(
        /* webpackChunkName: "assessmentManageScoresIndex" */ '@/engines/assessment/views/assessment/manage/scores/Index.vue'
      ),
    meta: {
      title: 'assessmentManageScoresIndex',
    },
  },
  {
    path: '/assessment/manage/activities/:activityId/scores/:scoreId',
    name: 'assessmentManageScoresShow',
    component: () =>
      import(
        /* webpackChunkName: "assessmentManageScoresShow" */ '@/engines/assessment/views/assessment/manage/scores/Show.vue'
      ),
    meta: {
      title: 'assessmentManageScoresShow',
    },
  },
];
