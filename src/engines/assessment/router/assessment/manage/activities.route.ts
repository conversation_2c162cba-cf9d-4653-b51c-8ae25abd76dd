export default [
  {
    path: '/assessment/manage/activities',
    name: '考核活动管理',
    component: () =>
      import(
        /* webpackChunkName: "assessmentManageActivitiesIndex" */ '@/engines/assessment/views/assessment/manage/activities/Index.vue'
      ),
    meta: {
      title: '考核活动管理',
    },
  },
  {
    path: '/assessment/manage/activities/:activityId',
    name: 'assessmentManageActivitiesShow',
    component: () =>
      import(
        /* webpackChunkName: "assessmentManageActivitiesShow" */ '@/engines/assessment/views/assessment/manage/activities/Show.vue'
      ),
    meta: {
      title: '考核活动管理',
      menuKey: '/assessment/manage/activities',
    },
  },
];
