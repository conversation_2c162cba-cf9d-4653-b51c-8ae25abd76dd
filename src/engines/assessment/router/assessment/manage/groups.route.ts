export default [
  {
    path: '/assessment/manage/activities/:activityId/groups',
    name: 'assessmentManageGroupsIndex',
    component: () =>
      import(
        /* webpackChunkName: "assessmentManageGroupsIndex" */ '@/engines/assessment/views/assessment/manage/groups/Index.vue'
      ),
    meta: {
      title: 'assessmentManageGroupsIndex',
    },
  },
  {
    path: '/assessment/manage/activities/:activityId/groups/:groupId',
    name: 'assessmentManageGroupsShow',
    component: () =>
      import(
        /* webpackChunkName: "assessmentManageGroupsShow" */ '@/engines/assessment/views/assessment/manage/groups/Show.vue'
      ),
    meta: {
      title: 'assessmentManageGroupsShow',
    },
  },
];
