<script lang="ts">
import { AssessmentManageQuestionSheetsApi } from '@/engines/assessment/apis/assessment/manage/question_sheets.api';
import { AssessmentQuestionSheetModel } from '@/engines/assessment/assessment-core/models/assessment/question_sheets';
import { AssessmentQuestionSheet } from '@/engines/assessment/assessment-core/types/model';
import ComAssessmentQuestionSheetsIndex from '@/engines/assessment/components/assessment/question_sheets/ComAssessmentQuestionSheetsIndex.vue';
import { VStore } from '@/lib/vails';
import { defineComponent } from '@vue/runtime-core';
import { useRouter, useRoute } from 'vue-router';

const AssessmentManageQuestionSheetsIndex = defineComponent({
  name: 'AssessmentManageQuestionSheetsIndex',
  components: {
    ComAssessmentQuestionSheetsIndex,
  },
  setup() {
    const store = new VStore(new AssessmentManageQuestionSheetsApi(), AssessmentQuestionSheetModel);

    const route = useRoute();
    const router = useRouter();

    const type = route.query.type || 'Assessment::QuestionSheetRecord';

    const onShow = (record: AssessmentQuestionSheet) => {
      router.push(`/assessment/manage/question_sheets/${record.id}/dashboard?type=${type}`);
    };

    const onDetail = (record: AssessmentQuestionSheet) => {
      router.push(`/assessment/manage/question_sheets/${record.id}?type=${type}`);
    };

    return {
      store,
      onShow,
      onDetail,
      type,
    };
  },
});

export default AssessmentManageQuestionSheetsIndex;
</script>

<template lang="pug">
.assessment-manage-question-sheets-index
  ComAssessmentQuestionSheetsIndex(:store='store', :type='type', @onShow='onShow', @onDetail='onDetail')
</template>

<style lang="stylus" scoped>
.assessment-manage-question-sheets-index
  height 100%
  width 100%
</style>
