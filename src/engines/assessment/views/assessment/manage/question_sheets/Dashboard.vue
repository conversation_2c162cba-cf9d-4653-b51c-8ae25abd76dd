<script lang="ts">
import { AssessmentManageAnswerSheetsApi } from '@/engines/assessment/apis/assessment/manage/answer_sheets.api';
import { AssessmentManageQuestionSettingsApi } from '@/engines/assessment/apis/assessment/manage/question_settings.api';
import { AssessmentManageQuestionSheetsApi } from '@/engines/assessment/apis/assessment/manage/question_sheets.api';
import { AssessmentAnswerSheetModel } from '@/engines/assessment/assessment-core/models/assessment/answer_sheets';
import { AssessmentQuestionSettingModel } from '@/engines/assessment/assessment-core/models/assessment/question_settings';
import { AssessmentQuestionSheetModel } from '@/engines/assessment/assessment-core/models/assessment/question_sheets';
import ComAssessmentQuestionSheetsDashboard from '@/engines/assessment/components/assessment/question_sheets/ComAssessmentQuestionSheetsDashboard.vue';
import { VStore } from '@/lib/vails';
import { computed, defineComponent, onMounted, toRefs } from 'vue';
import { useRoute } from 'vue-router';

const AssessmentManageQuestionSheetsShow = defineComponent({
  name: 'AssessmentManageQuestionSheetsShow',
  components: {
    ComAssessmentQuestionSheetsDashboard,
  },
  setup(props) {
    const route = useRoute();

    const store = new VStore(new AssessmentManageQuestionSheetsApi(), AssessmentQuestionSheetModel);
    const answerSheetStore = new VStore(
      new AssessmentManageAnswerSheetsApi({
        parents: [{ type: 'question_sheets', id: store.record.value.id }],
      }),
      AssessmentAnswerSheetModel,
    );

    const questionSettingStore = new VStore(
      new AssessmentManageQuestionSettingsApi(),
      AssessmentQuestionSettingModel,
    );

    const breadcrumbs = computed(() => [
      {
        label: '调查问卷管理',
        route: `/assessment/manage/question_sheets?type=${route.query.type}`,
      },
    ]);

    onMounted(async () => {
      await store.find(Number(route.params.questionSheetId));
      answerSheetStore.api = new AssessmentManageAnswerSheetsApi({
        parents: [{ type: 'question_sheets', id: store.record.value.id }],
      });
    });

    return {
      ...toRefs(props),
      store,
      record: store.record,
      breadcrumbs,
      answerSheetStore,
      questionSettingStore,
    };
  },
});

export default AssessmentManageQuestionSheetsShow;
</script>

<template lang="pug">
.assessment-manage-question-sheets-show
  ComAssessmentQuestionSheetsDashboard(
    v-if='record.id',
    :store='store',
    :breadcrumbs='breadcrumbs',
    :answerSheetStore='answerSheetStore',
    :questionSettingStore='questionSettingStore'
  )
</template>

<style lang="stylus" scoped>
.assessment-manage-question-sheets-show
  height 100%
  width 100%
  .breadcrumbs
    padding 0 24px
</style>
