<script lang="ts">
import ComAssessmentQuestionSheetsShow from '@/engines/assessment/components/assessment/question_sheets/ComAssessmentQuestionSheetsShow.vue';
import { defineComponent, toRefs, onMounted, computed } from 'vue';
import { AssessmentManageQuestionSheetsApi } from '@/engines/assessment/apis/assessment/manage/question_sheets.api';
import { AssessmentQuestionSheetModel } from '@/engines/assessment/assessment-core/models/assessment/question_sheets';
import { VStore } from '@/lib/vails';
import { useRoute } from 'vue-router';

const AssessmentManageQuestionSheetsShow = defineComponent({
  name: 'AssessmentManageQuestionSheetsShow',
  components: {
    ComAssessmentQuestionSheetsShow,
  },
  setup(props) {
    const route = useRoute();

    const store = new VStore(new AssessmentManageQuestionSheetsApi(), AssessmentQuestionSheetModel);

    const breadcrumbs = computed(() => [
      { label: '问卷', route: `/assessment/manage/question_sheets?type=${route.query.type}` },
    ]);

    onMounted(() => {
      store.find(Number(route.params.questionSheetId));
    });

    return {
      ...toRefs(props),
      store,
      record: store.record,
      breadcrumbs,
    };
  },
});

export default AssessmentManageQuestionSheetsShow;
</script>

<template lang="pug">
.assessment-manage-question-sheets-show
  ComAssessmentQuestionSheetsShow(v-if='record.id', :store='store', :breadcrumbs='breadcrumbs')
</template>

<style lang="stylus" scoped>
.assessment-manage-question-sheets-show
  height 100%
  width 100%
  .breadcrumbs
    padding 0 24px
</style>
