<script lang="ts">
import ComAssessmentQuestionSheetTemplatesIndex from '@/engines/assessment/components/assessment/question_sheets/ComAssessmentQuestionSheetTemplatesIndex.vue';
import { defineComponent } from '@vue/runtime-core';
import { AssessmentManageQuestionSheetsApi } from '@/engines/assessment/apis/assessment/manage/question_sheets.api';
import { AssessmentQuestionSheetModel } from '@/engines/assessment/assessment-core/models/assessment/question_sheets';
import { VStore } from '@/lib/vails';

const AssessmentManageQuestionSheetsIndex = defineComponent({
  name: 'AssessmentManageQuestionSheetsIndex',
  components: {
    ComAssessmentQuestionSheetTemplatesIndex,
  },
  setup() {
    const store = new VStore(new AssessmentManageQuestionSheetsApi(), AssessmentQuestionSheetModel);

    return {
      store,
    };
  },
});

export default AssessmentManageQuestionSheetsIndex;
</script>

<template lang="pug">
.assessment-manage-question-sheet-templates-index
  ComAssessmentQuestionSheetTemplatesIndex(:store='store')
</template>

<style lang="stylus" scoped>
.assessment-manage-question-sheet-templates-index
  height 100%
  width 100%
</style>
