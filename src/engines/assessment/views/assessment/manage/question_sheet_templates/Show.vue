<script lang="ts">
import ComAssessmentQuestionSheetsShow from '@/engines/assessment/components/assessment/question_sheets/ComAssessmentQuestionSheetsShow.vue';
import { defineComponent, toRefs, onMounted, computed } from 'vue';
import { AssessmentManageQuestionSheetsApi } from '@/engines/assessment/apis/assessment/manage/question_sheets.api';
import { AssessmentQuestionSheetModel } from '@/engines/assessment/assessment-core/models/assessment/question_sheets';
import { VStore } from '@/lib/vails';
import { useRoute } from 'vue-router';

const AssessmentManageQuestionSheetTemplatesShow = defineComponent({
  name: 'AssessmentManageQuestionSheetTemplatesShow',
  components: {
    ComAssessmentQuestionSheetsShow,
  },
  setup(props) {
    const route = useRoute();

    const store = new VStore(
      new AssessmentManageQuestionSheetsApi({
        params: { q: { type_eq: 'Assessment::QuestionSheetTemplate' } },
      }),
      AssessmentQuestionSheetModel,
    );

    const breadcrumbs = computed(() => [
      { label: '问卷模板', route: '/assessment/manage/question_sheet_templates' },
    ]);

    onMounted(() => {
      store.find(Number(route.params.questionSheetId));
    });

    return {
      ...toRefs(props),
      store,
      record: store.record,
      breadcrumbs,
    };
  },
});

export default AssessmentManageQuestionSheetTemplatesShow;
</script>

<template lang="pug">
.assessment-manage-question-sheet-templates-show
  ComAssessmentQuestionSheetsShow(
    v-if='record.id',
    :store='store',
    :breadcrumbs='breadcrumbs',
    template='assessment_question_sheet#template'
  )
</template>

<style lang="stylus" scoped>
.assessment-manage-question-sheet-templates-show
  height 100%
  width 100%
  .breadcrumbs
    padding 0 24px
</style>
