<script lang="ts">
import ComAssessmentScoreTemplatesIndex from '@/engines/assessment/components/assessment/score_templates/ComAssessmentScoreTemplatesIndex.vue';
import { defineComponent } from '@vue/runtime-core';
import { AssessmentManageScoreTemplatesApi } from '@/engines/assessment/apis/assessment/manage/score_templates.api';
import { VStore } from '@/lib/vails';

const AssessmentManageScoreTemplatesIndex = defineComponent({
  name: 'AssessmentManageScoreTemplatesIndex',
  components: {
    ComAssessmentScoreTemplatesIndex,
  },
  setup() {
    const store = new VStore(new AssessmentManageScoreTemplatesApi());

    return {
      store,
    };
  },
});

export default AssessmentManageScoreTemplatesIndex;
</script>

<template lang="pug">
.assessment-manage-score-templates-index
  ComAssessmentScoreTemplatesIndex(:store='store')
</template>

<style lang="stylus" scoped>
.assessment-manage-score-templates-index
  height 100%
  width 100%
</style>
