<script lang="ts">
import ComAssessmentScoreTemplatesShow from '@/engines/assessment/components/assessment/score_templates/ComAssessmentScoreTemplatesShow.vue';
import { defineComponent, toRefs, onMounted, computed } from 'vue';
import { AssessmentManageScoreTemplatesApi } from '@/engines/assessment/apis/assessment/manage/score_templates.api';
import { VStore } from '@/lib/vails';
import { useRoute } from 'vue-router';

const AssessmentManageScoreTemplatesShow = defineComponent({
  name: 'AssessmentManageScoreTemplatesShow',
  components: {
    ComAssessmentScoreTemplatesShow,
  },
  setup(props) {
    const route = useRoute();

    const store = new VStore(new AssessmentManageScoreTemplatesApi());

    const breadcrumbs = computed(() => [
      { label: '测评表管理', route: '/assessment/manage/score_templates' },
    ]);

    onMounted(() => {
      store.find(Number(route.params.score_templateId));
    });

    return {
      ...toRefs(props),
      store,
      record: store.record,
      breadcrumbs,
    };
  },
});

export default AssessmentManageScoreTemplatesShow;
</script>

<template lang="pug">
.assessment-manage-score-templates-show
  ComAssessmentScoreTemplatesShow(v-if='record.id', :store='store', :breadcrumbs='breadcrumbs')
</template>

<style lang="stylus" scoped>
.assessment-manage-score-templates-show
  height 100%
  width 100%
  .breadcrumbs
    padding 0 24px
</style>
