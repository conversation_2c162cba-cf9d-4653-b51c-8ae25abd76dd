<script lang="ts">
import ComAssessmentEntriesIndex from '@/engines/assessment/components/assessment/entries/ComAssessmentEntriesIndex.vue';
import { defineComponent } from '@vue/runtime-core';
import { AssessmentManageEntriesApi } from '@/engines/assessment/apis/assessment/manage/entries.api';
import { VStore } from '@/lib/vails';
import { useRoute } from 'vue-router';

const AssessmentManageEntriesIndex = defineComponent({
  name: 'AssessmentManageEntriesIndex',
  components: {
    ComAssessmentEntriesIndex,
  },
  setup() {
    const routes = useRoute();
    const activityId = Number(routes.params.activityId);

    const store = new VStore(
      new AssessmentManageEntriesApi({
        parents: [{ type: 'activities', id: activityId }],
      }),
    );

    return {
      store,
      activityId,
    };
  },
});

export default AssessmentManageEntriesIndex;
</script>

<template lang="pug">
.assessment-manage-entries-index
  ComAssessmentEntriesIndex(:store='store', :activityId='activityId' )
</template>

<style lang="stylus" scoped>
.assessment-manage-entries-index
  height 100%
  width 100%
</style>
