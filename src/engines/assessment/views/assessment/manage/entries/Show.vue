<script lang="ts">
import ComAssessmentEntriesShow from '@/engines/assessment/components/assessment/entries/ComAssessmentEntriesShow.vue';
import { defineComponent, toRefs, onMounted, computed } from 'vue';
import { AssessmentManageEntriesApi } from '@/engines/assessment/apis/assessment/manage/entries.api';
import { VStore } from '@/lib/vails';
import { useRoute } from 'vue-router';

const AssessmentManageEntriesShow = defineComponent({
  name: 'AssessmentManageEntriesShow',
  components: {
    ComAssessmentEntriesShow,
  },
  setup(props) {
    const route = useRoute();
    const routes = useRoute();
    const activityId = Number(routes.params.activityId);
    const store = new VStore(
      new AssessmentManageEntriesApi({
        parents: [{ type: 'activities', id: activityId }],
      }),
    );

    const breadcrumbs = computed(() => [
      { label: '', route: `/assessment/manage/activities/:activityId/entries` },
    ]);

    onMounted(() => {
      store.find(Number(route.params.entryId));
    });

    return {
      ...toRefs(props),
      store,
      record: store.record,
      breadcrumbs,
    };
  },
});

export default AssessmentManageEntriesShow;
</script>

<template lang="pug">
.assessment-manage-entries-show
  ComAssessmentEntriesShow(v-if='record.id', :store='store', :breadcrumbs='breadcrumbs')
</template>

<style lang="stylus" scoped>
.assessment-manage-entries-show
  height 100%
  width 100%
  .breadcrumbs
    padding 0 24px
</style>
