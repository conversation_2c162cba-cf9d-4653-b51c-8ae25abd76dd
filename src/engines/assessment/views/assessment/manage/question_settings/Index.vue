<script lang="ts">
import ComAssessmentQuestionSettingsIndex from '@/engines/assessment/components/assessment/question_settings/ComAssessmentQuestionSettingsIndex.vue';
import { defineComponent } from '@vue/runtime-core';
import { AssessmentManageQuestionSettingsApi } from '@/engines/assessment/apis/assessment/manage/question_settings.api';
import { AssessmentQuestionSettingModel } from '@/engines/assessment/assessment-core/models/assessment/question_settings';
import { VStore } from '@/lib/vails';

const AssessmentManageQuestionSettingsIndex = defineComponent({
  name: 'AssessmentManageQuestionSettingsIndex',
  components: {
    ComAssessmentQuestionSettingsIndex,
  },
  setup() {
    const store = new VStore(
      new AssessmentManageQuestionSettingsApi(),
      AssessmentQuestionSettingModel,
    );

    return {
      store,
    };
  },
});

export default AssessmentManageQuestionSettingsIndex;
</script>

<template lang="pug">
.assessment-manage-question-settings-index
  ComAssessmentQuestionSettingsIndex(:store='store')
</template>

<style lang="stylus" scoped>
.assessment-manage-question-settings-index
  height 100%
  width 100%
</style>
