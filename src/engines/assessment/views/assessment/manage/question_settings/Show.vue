<script lang="ts">
import ComAssessmentQuestionSettingsShow from '@/engines/assessment/components/assessment/question_settings/ComAssessmentQuestionSettingsShow.vue';
import { defineComponent, toRefs, onMounted, computed } from 'vue';
import { AssessmentManageQuestionSettingsApi } from '@/engines/assessment/apis/assessment/manage/question_settings.api';
import { AssessmentQuestionSettingModel } from '@/engines/assessment/assessment-core/models/assessment/question_settings';
import { VStore } from '@/lib/vails';
import { useRoute } from 'vue-router';

const AssessmentManageQuestionSettingsShow = defineComponent({
  name: 'AssessmentManageQuestionSettingsShow',
  components: {
    ComAssessmentQuestionSettingsShow,
  },
  setup(props) {
    const route = useRoute();

    const store = new VStore(
      new AssessmentManageQuestionSettingsApi(),
      AssessmentQuestionSettingModel,
    );

    const breadcrumbs = computed(() => [
      { label: '', route: '/assessment/manage/question_settings' },
    ]);

    onMounted(() => {
      store.find(Number(route.params.question_settingId));
    });

    return {
      ...toRefs(props),
      store,
      record: store.record,
      breadcrumbs,
    };
  },
});

export default AssessmentManageQuestionSettingsShow;
</script>

<template lang="pug">
.assessment-manage-question-settings-show
  ComAssessmentQuestionSettingsShow(v-if='record.id', :store='store', :breadcrumbs='breadcrumbs')
</template>

<style lang="stylus" scoped>
.assessment-manage-question-settings-show
  height 100%
  width 100%
  .breadcrumbs
    padding 0 24px
</style>
