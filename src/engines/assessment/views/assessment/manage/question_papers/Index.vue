<script lang="ts">
import { defineComponent } from 'vue';
import ComAssessmentManageQuestionPaperIndex from '@/engines/assessment/components/assessment/manage/question_papers/ComAssessmentManageQuestionPaperIndex.vue';
import { VStore } from '@/lib/vails';
import { AssessmentManageQuestionPapersApi } from '@/engines/assessment/apis/assessment/manage/question_papers.api';
import { AssessmentQuestionPaperModel } from '@/engines/assessment/assessment-core/models/assessment/question_papers';

export default defineComponent({
  name: 'AssessmentManageQuestionPaperIndex',
  components: { ComAssessmentManageQuestionPaperIndex },
  setup() {
    const store = new VStore(new AssessmentManageQuestionPapersApi(), AssessmentQuestionPaperModel);

    return {
      store,
    };
  },
});
</script>

<template lang="pug">
.assessment-manage-question-paper-index.w-full.h-full
  ComAssessmentManageQuestionPaperIndex(:store='store')
</template>
