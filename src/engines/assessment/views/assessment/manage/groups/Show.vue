<script lang="ts">
import ComAssessmentGroupsShow from '@/engines/assessment/components/assessment/groups/ComAssessmentGroupsShow.vue';
import { defineComponent, toRefs, onMounted, computed } from 'vue';
import { AssessmentManageGroupsApi } from '@/engines/assessment/apis/assessment/manage/groups.api';
import { VStore } from '@/lib/vails';
import { useRoute } from 'vue-router';

const AssessmentManageGroupsShow = defineComponent({
  name: 'AssessmentManageGroupsShow',
  components: {
    ComAssessmentGroupsShow,
  },
  setup(props) {
    const route = useRoute();
    const routes = useRoute();
    const activityId = Number(routes.params.activityId);
    const store = new VStore(
      new AssessmentManageGroupsApi({
        parents: [{ type: 'activities', id: activityId }],
      }),
    );

    const breadcrumbs = computed(() => [
      { label: '', route: `/assessment/manage/activities/:activityId/groups` },
    ]);

    onMounted(() => {
      store.find(Number(route.params.groupId));
    });

    return {
      ...toRefs(props),
      store,
      record: store.record,
      breadcrumbs,
    };
  },
});

export default AssessmentManageGroupsShow;
</script>

<template lang="pug">
.assessment-manage-groups-show
  ComAssessmentGroupsShow(v-if='record.id', :store='store', :breadcrumbs='breadcrumbs')
</template>

<style lang="stylus" scoped>
.assessment-manage-groups-show
  height 100%
  width 100%
  .breadcrumbs
    padding 0 24px
</style>
