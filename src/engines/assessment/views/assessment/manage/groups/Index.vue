<script lang="ts">
import ComAssessmentGroupsIndex from '@/engines/assessment/components/assessment/groups/ComAssessmentGroupsIndex.vue';
import { defineComponent } from '@vue/runtime-core';
import { AssessmentManageGroupsApi } from '@/engines/assessment/apis/assessment/manage/groups.api';
import { VStore } from '@/lib/vails';
import { useRoute } from 'vue-router';

const AssessmentManageGroupsIndex = defineComponent({
  name: 'AssessmentManageGroupsIndex',
  components: {
    ComAssessmentGroupsIndex,
  },
  setup() {
    const routes = useRoute();
    const activityId = Number(routes.params.activityId);

    const store = new VStore(
      new AssessmentManageGroupsApi({
        parents: [{ type: 'activities', id: activityId }],
      }),
    );

    return {
      store,
      activityId,
    };
  },
});

export default AssessmentManageGroupsIndex;
</script>

<template lang="pug">
.assessment-manage-groups-index
  ComAssessmentGroupsIndex(:store='store', :activityId='activityId' )
</template>

<style lang="stylus" scoped>
.assessment-manage-groups-index
  height 100%
  width 100%
</style>
