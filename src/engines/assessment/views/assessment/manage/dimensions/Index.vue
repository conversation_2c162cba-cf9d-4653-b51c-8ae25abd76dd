<script lang="ts">
import ComAssessmentDimensionsIndex from '@/engines/assessment/components/assessment/dimensions/ComAssessmentDimensionsIndex.vue';
import { defineComponent } from '@vue/runtime-core';
import { AssessmentManageDimensionsApi } from '@/engines/assessment/apis/assessment/manage/dimensions.api';
import { VStore } from '@/lib/vails';
import { useRoute } from 'vue-router';

const AssessmentManageDimensionsIndex = defineComponent({
  name: 'AssessmentManageDimensionsIndex',
  components: {
    ComAssessmentDimensionsIndex,
  },
  setup() {
    const routes = useRoute();
    const activityId = Number(routes.params.activityId);

    const store = new VStore(
      new AssessmentManageDimensionsApi({
        parents: [{ type: 'activities', id: activityId }],
      }),
    );

    return {
      store,
      activityId,
    };
  },
});

export default AssessmentManageDimensionsIndex;
</script>

<template lang="pug">
.assessment-manage-dimensions-index
  ComAssessmentDimensionsIndex(:store='store', :activityId='activityId' )
</template>

<style lang="stylus" scoped>
.assessment-manage-dimensions-index
  height 100%
  width 100%
</style>
