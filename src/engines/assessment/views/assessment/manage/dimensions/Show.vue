<script lang="ts">
import ComAssessmentDimensionsShow from '@/engines/assessment/components/assessment/dimensions/ComAssessmentDimensionsShow.vue';
import { defineComponent, toRefs, onMounted, computed } from 'vue';
import { AssessmentManageDimensionsApi } from '@/engines/assessment/apis/assessment/manage/dimensions.api';
import { VStore } from '@/lib/vails';
import { useRoute } from 'vue-router';

const AssessmentManageDimensionsShow = defineComponent({
  name: 'AssessmentManageDimensionsShow',
  components: {
    ComAssessmentDimensionsShow,
  },
  setup(props) {
    const route = useRoute();
    const routes = useRoute();
    const activityId = Number(routes.params.activityId);
    const store = new VStore(
      new AssessmentManageDimensionsApi({
        parents: [{ type: 'activities', id: activityId }],
      }),
    );

    const breadcrumbs = computed(() => [
      { label: '', route: `/assessment/manage/activities/:activityId/dimensions` },
    ]);

    onMounted(() => {
      store.find(Number(route.params.dimensionId));
    });

    return {
      ...toRefs(props),
      store,
      record: store.record,
      breadcrumbs,
    };
  },
});

export default AssessmentManageDimensionsShow;
</script>

<template lang="pug">
.assessment-manage-dimensions-show
  ComAssessmentDimensionsShow(v-if='record.id', :store='store', :breadcrumbs='breadcrumbs')
</template>

<style lang="stylus" scoped>
.assessment-manage-dimensions-show
  height 100%
  width 100%
  .breadcrumbs
    padding 0 24px
</style>
