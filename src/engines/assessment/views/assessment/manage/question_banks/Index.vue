<script lang="ts">
import { defineComponent } from 'vue';
import ComAssessmentManageQuestionBankIndex from '@/engines/assessment/components/assessment/manage/question_banks/ComAssessmentManageQuestionBankIndex.vue';
import { VStore } from '@/lib/vails';
import { AssessmentManageQuestionBanksApi } from '@/engines/assessment/apis/assessment/manage/question_banks.api';
import { AssessmentQuestionBankModel } from '@/engines/assessment/assessment-core/models/assessment/question_banks';

export default defineComponent({
  name: 'AssessmentManageQuestionBankIndex',
  components: { ComAssessmentManageQuestionBankIndex },
  setup() {
    const store = new VStore(new AssessmentManageQuestionBanksApi(), AssessmentQuestionBankModel);

    return {
      store,
    };
  },
});
</script>

<template lang="pug">
.assessment-manage-question-bank-index.w-full.h-full
  ComAssessmentManageQuestionBankIndex(:store='store')
</template>
