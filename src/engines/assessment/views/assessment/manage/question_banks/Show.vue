<script lang="ts">
import { defineComponent } from 'vue';
import ComAssessmentManageQuestionBankShow from '@/engines/assessment/components/assessment/manage/question_banks/ComAssessmentManageQuestionBankShow.vue';
import { VStore } from '@/lib/vails';
import { AssessmentManageQuestionBanksApi } from '@/engines/assessment/apis/assessment/manage/question_banks.api';
import { AssessmentQuestionBankModel } from '@/engines/assessment/assessment-core/models/assessment/question_banks';
import { useRoute } from 'vue-router';

export default defineComponent({
  name: 'AssessmentManageQuestionBankShow',
  components: { ComAssessmentManageQuestionBankShow },
  setup() {
    const store = new VStore(new AssessmentManageQuestionBanksApi(), AssessmentQuestionBankModel);

    const route = useRoute();

    store.find(Number(route.params.questionBankId));

    return {
      store,
      record: store.record,
    };
  },
});
</script>

<template lang="pug">
.assessment-manage-question-bank-index.w-full.h-full
  ComAssessmentManageQuestionBankShow(:store='store', v-if='record.id')
</template>
