<script lang="ts">
import ComAssessmentManageAnswerSheetsShow from '@/engines/assessment/components/assessment/answer_sheets/ComAssessmentManageAnswerSheetsShow.vue';
import { defineComponent, toRefs, onMounted, computed } from 'vue';
import { AssessmentManageAnswerSheetsApi } from '@/engines/assessment/apis/assessment/manage/answer_sheets.api';
import { AssessmentAnswerSheetModel } from '@/engines/assessment/assessment-core/models/assessment/answer_sheets';
import { VStore } from '@/lib/vails';
import { useRoute } from 'vue-router';
import { AssessmentManageQuestionSheetsApi } from '@/engines/assessment/apis/assessment/manage/question_sheets.api';
import { AssessmentQuestionSheetModel } from '@/engines/assessment/assessment-core/models/assessment/question_sheets';

const AssessmentManageAnswerSheetsShow = defineComponent({
  name: 'AssessmentManageAnswerSheetsShow',
  components: {
    ComAssessmentManageAnswerSheetsShow,
  },
  setup(props) {
    const route = useRoute();

    const store = new VStore(
      new AssessmentManageAnswerSheetsApi({
        parents: [{ type: 'question_sheets', id: Number(route.params.questionSheetId) }],
      }),
      AssessmentAnswerSheetModel,
    );

    const questionSheetStore = new VStore(
      new AssessmentManageQuestionSheetsApi(),
      AssessmentQuestionSheetModel,
    );

    const breadcrumbs = computed(() => [
      {
        label: questionSheetStore.record.value.name || '问卷列表',
        route: `/assessment/manage/question_sheets/${route.params.questionSheetId}/answer_sheets`,
      },
    ]);

    onMounted(() => {
      store.find(Number(route.params.answerSheetId)).then(() => {
        questionSheetStore.find(store.record.value.question_sheet_id);
        // if (store.record.value.question_sheet?.name) {
        //   updateTitle(store.record.value.question_sheet?.name, route.fullPath);
        // }
      });
    });

    return {
      ...toRefs(props),
      store,
      record: store.record,
      breadcrumbs,
      questionSheetStore,
      questionSheet: questionSheetStore.record,
    };
  },
});

export default AssessmentManageAnswerSheetsShow;
</script>

<template lang="pug">
.assessment-manage-answer-sheets-show
  ComAssessmentManageAnswerSheetsShow(
    v-if='record.id && questionSheet.id',
    :store='store',
    :breadcrumbs='breadcrumbs',
    :questionSheetStore='questionSheetStore'
  )
</template>

<style lang="stylus" scoped>
.assessment-manage-answer-sheets-show
  height 100%
  width 100%
  .breadcrumbs
    padding 0 24px
</style>
