<script lang="ts">
import { AssessmentManageAnswerSheetsApi } from '@/engines/assessment/apis/assessment/manage/answer_sheets.api';
import { AssessmentAnswerSheetModel } from '@/engines/assessment/assessment-core/models/assessment/answer_sheets';
import { VStore } from '@/lib/vails';
import { defineComponent } from '@vue/runtime-core';
import { useRoute } from 'vue-router';
import ComAssessmentManageAnswerSheetsIndex from '../../../../components/assessment/answer_sheets/ComAssessmentManageAnswerSheetsIndex.vue';

const AssessmentManageAnswerSheetsIndex = defineComponent({
  name: 'AssessmentManageAnswerSheetsIndex',
  components: {
    ComAssessmentManageAnswerSheetsIndex,
  },
  setup() {
    const route = useRoute();
    const store = new VStore(
      new AssessmentManageAnswerSheetsApi({
        parents: [{ type: 'question_sheets', id: Number(route.params.questionSheetId) }],
      }),
      AssessmentAnswerSheetModel,
    );

    return {
      store,
    };
  },
});

export default AssessmentManageAnswerSheetsIndex;
</script>

<template lang="pug">
.assessment-manage-answer-sheets-index
  ComAssessmentManageAnswerSheetsIndex(:store='store')
</template>

<style lang="stylus" scoped>
.assessment-manage-answer-sheets-index
  height 100%
  width 100%
</style>
