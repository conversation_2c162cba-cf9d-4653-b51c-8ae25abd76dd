<script lang="ts">
import ComAssessmentScoresShow from '@/engines/assessment/components/assessment/scores/ComAssessmentScoresShow.vue';
import { defineComponent, toRefs, onMounted, computed } from 'vue';
import { AssessmentManageScoresApi } from '@/engines/assessment/apis/assessment/manage/scores.api';
import { VStore } from '@/lib/vails';
import { useRoute } from 'vue-router';

const AssessmentManageScoresShow = defineComponent({
  name: 'AssessmentManageScoresShow',
  components: {
    ComAssessmentScoresShow,
  },
  setup(props) {
    const route = useRoute();
    const routes = useRoute();
    const activityId = Number(routes.params.activityId);
    const store = new VStore(
      new AssessmentManageScoresApi({
        parents: [{ type: 'activities', id: activityId }],
      }),
    );

    const breadcrumbs = computed(() => [
      { label: '', route: `/assessment/manage/activities/:activityId/scores` },
    ]);

    onMounted(() => {
      store.find(Number(route.params.scoreId));
    });

    return {
      ...toRefs(props),
      store,
      record: store.record,
      breadcrumbs,
    };
  },
});

export default AssessmentManageScoresShow;
</script>

<template lang="pug">
.assessment-manage-scores-show
  ComAssessmentScoresShow(v-if='record.id', :store='store', :breadcrumbs='breadcrumbs')
</template>

<style lang="stylus" scoped>
.assessment-manage-scores-show
  height 100%
  width 100%
  .breadcrumbs
    padding 0 24px
</style>
