<script lang="ts">
import ComAssessmentScoresIndex from '@/engines/assessment/components/assessment/scores/ComAssessmentScoresIndex.vue';
import { defineComponent } from '@vue/runtime-core';
import { AssessmentManageScoresApi } from '@/engines/assessment/apis/assessment/manage/scores.api';
import { VStore } from '@/lib/vails';
import { useRoute } from 'vue-router';

const AssessmentManageScoresIndex = defineComponent({
  name: 'AssessmentManageScoresIndex',
  components: {
    ComAssessmentScoresIndex,
  },
  setup() {
    const routes = useRoute();
    const activityId = Number(routes.params.activityId);

    const store = new VStore(
      new AssessmentManageScoresApi({
        parents: [{ type: 'activities', id: activityId }],
      }),
    );

    return {
      store,
      activityId,
    };
  },
});

export default AssessmentManageScoresIndex;
</script>

<template lang="pug">
.assessment-manage-scores-index
  ComAssessmentScoresIndex(:store='store', :activityId='activityId' )
</template>

<style lang="stylus" scoped>
.assessment-manage-scores-index
  height 100%
  width 100%
</style>
