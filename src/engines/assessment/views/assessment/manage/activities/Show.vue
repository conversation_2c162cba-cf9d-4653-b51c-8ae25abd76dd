<script lang="ts">
import ComAssessmentActivitiesShow from '@/engines/assessment/components/assessment/activities/ComAssessmentActivitiesShow.vue';
import { defineComponent, toRefs, onMounted, computed } from 'vue';
import { AssessmentManageActivitiesApi } from '@/engines/assessment/apis/assessment/manage/activities.api';
import { VStore } from '@/lib/vails';
import { useRoute } from 'vue-router';

const AssessmentManageActivitiesShow = defineComponent({
  name: 'AssessmentManageActivitiesShow',
  components: {
    ComAssessmentActivitiesShow,
  },
  setup(props) {
    const route = useRoute();

    const store = new VStore(new AssessmentManageActivitiesApi());

    const breadcrumbs = computed(() => [
      { label: '考核管理', route: '/assessment/manage/activities' },
    ]);

    onMounted(() => {
      store.find(Number(route.params.activityId));
    });

    return {
      ...toRefs(props),
      store,
      record: store.record,
      breadcrumbs,
    };
  },
});

export default AssessmentManageActivitiesShow;
</script>

<template lang="pug">
.assessment-manage-activities-show
  ComAssessmentActivitiesShow(v-if='record.id', :store='store', :breadcrumbs='breadcrumbs')
</template>

<style lang="stylus" scoped>
.assessment-manage-activities-show
  height 100%
  width 100%
  .breadcrumbs
    padding 0 24px
</style>
