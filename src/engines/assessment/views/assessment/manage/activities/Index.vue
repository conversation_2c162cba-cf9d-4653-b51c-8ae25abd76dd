<script lang="ts">
import ComAssessmentActivitiesIndex from '@/engines/assessment/components/assessment/activities/ComAssessmentActivitiesIndex.vue';
import { defineComponent } from '@vue/runtime-core';
import { AssessmentManageActivitiesApi } from '@/engines/assessment/apis/assessment/manage/activities.api';
import { VStore } from '@/lib/vails';

const AssessmentManageActivitiesIndex = defineComponent({
  name: 'AssessmentManageActivitiesIndex',
  components: {
    ComAssessmentActivitiesIndex,
  },
  setup() {
    const store = new VStore(
      new AssessmentManageActivitiesApi({
        params: { group_keys: 'state' },
      }),
    );

    return {
      store,
    };
  },
});

export default AssessmentManageActivitiesIndex;
</script>

<template lang="pug">
.assessment-manage-activities-index
  ComAssessmentActivitiesIndex(:store='store')
</template>

<style lang="stylus" scoped>
.assessment-manage-activities-index
  height 100%
  width 100%
</style>
