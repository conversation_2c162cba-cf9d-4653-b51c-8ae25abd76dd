<script lang="ts">
import ComAssessmentCatalogsShow from '@/engines/assessment/components/assessment/catalogs/ComAssessmentCatalogsShow.vue';
import { defineComponent, toRefs, onMounted, computed } from 'vue';
import { AssessmentManageCatalogsApi } from '@/engines/assessment/apis/assessment/manage/catalogs.api';
import { VStore } from '@/lib/vails';
import { useRoute } from 'vue-router';

const AssessmentManageCatalogsShow = defineComponent({
  name: 'AssessmentManageCatalogsShow',
  components: {
    ComAssessmentCatalogsShow,
  },
  setup(props) {
    const route = useRoute();
    const activityId = Number(route.params.activityId);
    const store = new VStore(
      new AssessmentManageCatalogsApi({
        parents: [{ type: 'activities', id: activityId }],
      }),
    );

    const breadcrumbs = computed(() => [
      { label: '', route: `/assessment/manage/activities/:activityId/catalogs` },
    ]);

    onMounted(() => {
      store.find(Number(route.params.catalogId));
    });

    return {
      ...toRefs(props),
      store,
      record: store.record,
      breadcrumbs,
    };
  },
});

export default AssessmentManageCatalogsShow;
</script>

<template lang="pug">
.assessment-manage-catalogs-show
  ComAssessmentCatalogsShow(v-if='record.id', :store='store', :breadcrumbs='breadcrumbs')
</template>

<style lang="stylus" scoped>
.assessment-manage-catalogs-show
  height 100%
  width 100%
  .breadcrumbs
    padding 0 24px
</style>
