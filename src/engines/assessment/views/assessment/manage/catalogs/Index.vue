<script lang="ts">
import ComAssessmentCatalogsIndex from '@/engines/assessment/components/assessment/catalogs/ComAssessmentCatalogsIndex.vue';
import { defineComponent } from '@vue/runtime-core';
import { AssessmentManageCatalogsApi } from '@/engines/assessment/apis/assessment/manage/catalogs.api';
import { VStore } from '@/lib/vails';
import { useRoute } from 'vue-router';

const AssessmentManageCatalogsIndex = defineComponent({
  name: 'AssessmentManageCatalogsIndex',
  components: {
    ComAssessmentCatalogsIndex,
  },
  setup() {
    const routes = useRoute();
    const activityId = Number(routes.params.activityId);

    const store = new VStore(
      new AssessmentManageCatalogsApi({
        parents: [{ type: 'activities', id: activityId }],
      }),
    );

    return {
      store,
      activityId,
    };
  },
});

export default AssessmentManageCatalogsIndex;
</script>

<template lang="pug">
.assessment-manage-catalogs-index
  ComAssessmentCatalogsIndex(:store='store', :activityId='activityId' )
</template>

<style lang="stylus" scoped>
.assessment-manage-catalogs-index
  height 100%
  width 100%
</style>
