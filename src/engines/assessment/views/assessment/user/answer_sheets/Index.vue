<script lang="ts">
import ComAssessmentUserAnswerSheetsIndex from '@/engines/assessment/components/assessment/answer_sheets/ComAssessmentUserAnswerSheetsIndex.vue';
import { AssessmentAnswerSheetModel } from '@/engines/assessment/models/assessment/user/answer_sheets';
import { VStore } from '@/lib/vails';
import { defineComponent } from '@vue/runtime-core';

// import usePolicy from '@/components/global/ta-component/ta-template-form-core/usePolicy';
import { AssessmentManageQuestionSettingsApi } from '@/engines/assessment/apis/assessment/manage/question_settings.api';
import { AssessmentUserAnswerSheetsApi } from '@/engines/assessment/assessment-core/apis/assessment/user/answer_sheets.api';
import { AssessmentQuestionSettingModel } from '@/engines/assessment/assessment-core/models/assessment/question_settings';

const AssessmentUserAnswerSheetsIndex = defineComponent({
  name: 'AssessmentUserAnswerSheetsIndex',
  components: {
    ComAssessmentUserAnswerSheetsIndex,
  },
  setup() {
    // usePolicy();

    const store = new VStore(new AssessmentUserAnswerSheetsApi(), AssessmentAnswerSheetModel);
    const questionSettingStore = new VStore(
      new AssessmentManageQuestionSettingsApi(),
      AssessmentQuestionSettingModel,
    );

    return {
      store,
      questionSettingStore,
    };
  },
});

export default AssessmentUserAnswerSheetsIndex;
</script>

<template lang="pug">
.assessment-user-answer-sheets-index
  ComAssessmentUserAnswerSheetsIndex(:store='store')
</template>

<style lang="stylus" scoped>
.assessment-user-answer-sheets-index
  height 100%
  width 100%
</style>
