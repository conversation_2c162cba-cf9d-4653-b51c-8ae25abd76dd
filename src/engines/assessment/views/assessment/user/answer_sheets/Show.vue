<script lang="ts">
import { AssessmentUserAnswerSheetsApi } from '@/engines/assessment/assessment-core/apis/assessment/user/answer_sheets.api';
import ComAssessmentUserAnswerSheetsShow from '@/engines/assessment/components/assessment/answer_sheets/ComAssessmentUserAnswerSheetsShow.vue';
import { AssessmentAnswerSheetModel } from '@/engines/assessment/models/assessment/user/answer_sheets';
import { VStore } from '@/lib/vails';
import { computed, defineComponent, onMounted, toRefs } from 'vue';
import { useRoute } from 'vue-router';
import { AssessmentUserQuestionSheetsApi } from '@/engines/assessment/assessment-core/apis/assessment/user/question_sheets.api';
import { AssessmentQuestionSheetModel } from '@/engines/assessment/assessment-core/models/assessment/question_sheets';
import useNavigateTab from '@/components/global/ta-component/useNavigateTab';

const AssessmentUserAnswerSheetsShow = defineComponent({
  name: 'AssessmentUserAnswerSheetsShow',
  components: {
    ComAssessmentUserAnswerSheetsShow,
  },
  setup(props) {
    const route = useRoute();

    const store = new VStore(new AssessmentUserAnswerSheetsApi(), AssessmentAnswerSheetModel);
    const questionSheetStore = new VStore(
      new AssessmentUserQuestionSheetsApi(),
      AssessmentQuestionSheetModel,
    );
    const { updateTitle } = useNavigateTab();
    const breadcrumbs = computed(() => [{ label: '', route: '/assessment/user/answer_sheets' }]);

    onMounted(() => {
      store.find(Number(route.params.answerSheetId)).then(() => {
        questionSheetStore.find(store.record.value.question_sheet_id);
        if (store.record.value.question_sheet?.name) {
          updateTitle(store.record.value.question_sheet?.name, route.fullPath);
        }
      });
    });

    return {
      ...toRefs(props),
      store,
      record: store.record,
      breadcrumbs,
      questionSheetStore,
      questionSheet: questionSheetStore.record,
    };
  },
});

export default AssessmentUserAnswerSheetsShow;
</script>

<template lang="pug">
.assessment-user-answer-sheets-show
  ComAssessmentUserAnswerSheetsShow(
    v-if='record.id && questionSheet.id',
    :store='store',
    :questionSheetStore='questionSheetStore',
    :breadcrumbs='breadcrumbs'
  )
</template>

<style lang="stylus" scoped>
.assessment-user-answer-sheets-show
  height 100%
  width 100%
  .breadcrumbs
    padding 0 24px
</style>
