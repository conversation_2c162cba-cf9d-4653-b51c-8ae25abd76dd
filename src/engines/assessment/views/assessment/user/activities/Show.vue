<script lang="ts">
import ComAssessmentActivitiesUserShow from '@/engines/assessment/components/assessment/activities/ComAssessmentActivitiesUserShow.vue';
import { defineComponent, toRefs, onMounted, computed } from 'vue';
import { AssessmentUserActivitiesApi } from '@/engines/assessment/assessment-core/apis/assessment/user/activities.api';
import { VStore } from '@/lib/vails';
import { useRoute } from 'vue-router';

const AssessmentUserActivitiesShow = defineComponent({
  name: 'AssessmentUserActivitiesShow',
  components: {
    ComAssessmentActivitiesUserShow,
  },
  setup(props) {
    const route = useRoute();

    const store = new VStore(new AssessmentUserActivitiesApi());

    const breadcrumbs = computed(() => [
      { label: '考核列表', route: '/assessment/user/activities' },
    ]);

    onMounted(() => {
      store.find(Number(route.params.activityId));
    });

    return {
      ...toRefs(props),
      store,
      record: store.record,
      breadcrumbs,
    };
  },
});

export default AssessmentUserActivitiesShow;
</script>

<template lang="pug">
.assessment-user-activities-show
  ComAssessmentActivitiesUserShow(v-if='record.id', :store='store', :breadcrumbs='breadcrumbs')
</template>

<style lang="stylus" scoped>
.assessment-user-activities-show
  height 100%
  width 100%
  .breadcrumbs
    padding 0 24px
</style>
