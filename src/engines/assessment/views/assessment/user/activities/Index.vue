<script lang="ts">
import ComAssessmentActivitiesUserIndex from '@/engines/assessment/components/assessment/activities/ComAssessmentActivitiesUserIndex.vue';
import { defineComponent } from '@vue/runtime-core';
import { AssessmentUserActivitiesApi } from '@/engines/assessment/assessment-core/apis/assessment/user/activities.api';
import { VStore } from '@/lib/vails';

const AssessmentUserActivitiesIndex = defineComponent({
  name: 'AssessmentUserActivitiesIndex',
  components: {
    ComAssessmentActivitiesUserIndex,
  },
  setup() {
    const store = new VStore(
      new AssessmentUserActivitiesApi({
        params: { group_keys: 'state' },
      }),
    );

    return {
      store,
    };
  },
});

export default AssessmentUserActivitiesIndex;
</script>

<template lang="pug">
.assessment-user-activities-index
  ComAssessmentActivitiesUserIndex(:store='store')
</template>

<style lang="stylus" scoped>
.assessment-user-activities-index
  height 100%
  width 100%
</style>
