<script lang="ts">
import ComAssessmentScoresUserIndex from '@/engines/assessment/components/assessment/scores/ComAssessmentScoresUserIndex.vue';
import { defineComponent } from '@vue/runtime-core';
import { VStore } from '@/lib/vails';
import { useRoute } from 'vue-router';
import { AssessmentUserScoredScoresApi } from '@/engines/assessment/assessment-core/apis/assessment/user/scored/scores.api';
import { AssessmentUserActivitiesApi } from '@/engines/assessment/assessment-core/apis/assessment/user/activities.api';
import { AssessmentScoreModel } from '@/engines/assessment/assessment-core/models/assessment/scores';

const AssessmentUserScoresIndex = defineComponent({
  name: 'AssessmentUserScoresIndex',
  components: {
    ComAssessmentScoresUserIndex,
  },
  setup() {
    const routes = useRoute();
    const activityId = Number(routes.params.activityId);

    const store = new VStore(
      new AssessmentUserScoredScoresApi({
        params: { q: { activity_id_eq: activityId } },
      }),
      AssessmentScoreModel,
    );

    const activityStore = new VStore(new AssessmentUserActivitiesApi({}));

    activityStore.find(activityId);

    return {
      store,
      activityId,
      activity: activityStore.record,
    };
  },
});

export default AssessmentUserScoresIndex;
</script>

<template lang="pug">
.assessment-user-scores-index
  ComAssessmentScoresUserIndex(v-if='activity.id', :store='store', :activity='activity')
</template>

<style lang="stylus" scoped>
.assessment-user-scores-index
  height 100%
  width 100%
</style>
