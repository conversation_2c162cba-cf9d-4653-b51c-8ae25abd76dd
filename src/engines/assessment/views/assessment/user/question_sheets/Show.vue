<script lang="ts">
import { defineComponent, toRefs, onMounted, computed } from 'vue';
import { AssessmentQuestionSheetModel } from '@/engines/assessment/assessment-core/models/assessment/question_sheets';
import { VStore } from '@/lib/vails';
import { useRoute } from 'vue-router';
import ComAssessmentUserQuestionSheetsShow from '@/engines/assessment/components/assessment/question_sheets/ComAssessmentUserQuestionSheetsShow.vue';
import { AssessmentUserQuestionSheetsApi } from '@/engines/assessment/assessment-core/apis/assessment/user/question_sheets.api';

const AssessmentUserQuestionSheetsShow = defineComponent({
  name: 'AssessmentUserQuestionSheetsShow',
  components: {
    ComAssessmentUserQuestionSheetsShow,
  },
  setup(props) {
    const route = useRoute();

    const store = new VStore(new AssessmentUserQuestionSheetsApi(), AssessmentQuestionSheetModel);

    const breadcrumbs = computed(() => [
      { label: '问卷', route: '/assessment/user/question_sheets' },
    ]);

    onMounted(() => {
      store.find(Number(route.params.questionSheetId));
    });

    return {
      ...toRefs(props),
      store,
      record: store.record,
      breadcrumbs,
    };
  },
});

export default AssessmentUserQuestionSheetsShow;
</script>

<template lang="pug">
.assessment-user-question-sheets-show
  ComAssessmentUserQuestionSheetsShow(v-if='record.id', :store='store', :breadcrumbs='breadcrumbs')
</template>

<style lang="stylus" scoped>
.assessment-user-question-sheets-show
  height 100%
  width 100%
  .breadcrumbs
    padding 0 24px
</style>
