<script lang="ts">
import { AssessmentQuestionSheetModel } from '@/engines/assessment/assessment-core/models/assessment/question_sheets';
import { AssessmentQuestionSheet } from '@/engines/assessment/assessment-core/types/model';
import ComAssessmentUserQuestionSheetsIndex from '@/engines/assessment/components/assessment/question_sheets/ComAssessmentUserQuestionSheetsIndex.vue';
import { VStore } from '@/lib/vails';
import { defineComponent } from '@vue/runtime-core';
import { useRouter } from 'vue-router';
import { AssessmentUserQuestionSheetsApi } from '@/engines/assessment/assessment-core/apis/assessment/user/question_sheets.api';

const AssessmentUserQuestionSheetsIndex = defineComponent({
  name: 'AssessmentUserQuestionSheetsIndex',
  components: {
    ComAssessmentUserQuestionSheetsIndex,
  },
  setup() {
    const store = new VStore(new AssessmentUserQuestionSheetsApi(), AssessmentQuestionSheetModel);

    const router = useRouter();
    const onShow = (record: AssessmentQuestionSheet) => {
      router.push(`/assessment/user/question_sheets/${record.id}`);
    };

    return {
      store,
      onShow,
    };
  },
});

export default AssessmentUserQuestionSheetsIndex;
</script>

<template lang="pug">
.assessment-user-question-sheets-index
  ComAssessmentUserQuestionSheetsIndex(:store='store', @onShow='onShow')
</template>

<style lang="stylus" scoped>
.assessment-user-question-sheets-index
  height 100%
  width 100%
</style>
