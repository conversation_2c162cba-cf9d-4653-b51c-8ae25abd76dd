<script lang="ts">
import ComAssessmentEvaluationsIndex from '@/engines/assessment/components/assessment/evaluations/ComAssessmentEvaluationsIndex.vue';
import { defineComponent } from '@vue/runtime-core';
import { AssessmentUserEvaluationsApi } from '@/engines/assessment/assessment-core/apis/assessment/user/evaluations.api';
import { AssessmentEvaluationModel } from '@/engines/assessment/assessment-core/models/assessment/user/evaluations';
import { VStore } from '@/lib/vails';

import usePolicy from '@/components/global/ta-component/ta-template-form-core/usePolicy';

const AssessmentUserEvaluationsIndex = defineComponent({
  name: 'AssessmentUserEvaluationsIndex',
  components: {
    ComAssessmentEvaluationsIndex,
  },
  setup() {
    usePolicy();

    const store = new VStore(new AssessmentUserEvaluationsApi(), AssessmentEvaluationModel as any);

    return {
      store,
    };
  },
});

export default AssessmentUserEvaluationsIndex;
</script>

<template lang="pug">
.assessment-user-evaluations-index
  ComAssessmentEvaluationsIndex(:store='store' )
</template>

<style lang="stylus" scoped>
.assessment-user-evaluations-index
  height 100%
  width 100%
</style>
