<script lang="ts">
import ComAssessmentEvaluationsShow from '@/engines/assessment/components/assessment/evaluations/ComAssessmentEvaluationsShow.vue';
import { defineComponent, toRefs, onMounted, computed } from 'vue';
import { AssessmentUserEvaluationsApi } from '@/engines/assessment/assessment-core/apis/assessment/user/evaluations.api';
import { AssessmentEvaluationModel } from '@/engines/assessment/assessment-core/models/assessment/user/evaluations';
import { VStore } from '@/lib/vails';
import { useRoute } from 'vue-router';

const AssessmentUserEvaluationsShow = defineComponent({
  name: 'AssessmentUserEvaluationsShow',
  components: {
    ComAssessmentEvaluationsShow,
  },
  setup(props) {
    const route = useRoute();

    const store = new VStore(new AssessmentUserEvaluationsApi(), AssessmentEvaluationModel as any);

    const breadcrumbs = computed(() => [{ label: '', route: '/assessment/user/evaluations' }]);

    onMounted(() => {
      store.find(Number(route.params.evaluationId));
    });

    return {
      ...toRefs(props),
      store,
      record: store.record,
      breadcrumbs,
    };
  },
});

export default AssessmentUserEvaluationsShow;
</script>

<template lang="pug">
.assessment-user-evaluations-show
  ComAssessmentEvaluationsShow(v-if='record.id', :store='store', :breadcrumbs='breadcrumbs')
</template>

<style lang="stylus" scoped>
.assessment-user-evaluations-show
  height 100%
  width 100%
  .breadcrumbs
    padding 0 24px
</style>
