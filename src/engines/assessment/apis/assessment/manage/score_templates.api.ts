import { VApiConfig } from '@/lib/vails/api';
import { MyApi } from '@/apis/MyApi';
import { AssessmentScoreTemplate } from '@/engines/assessment/assessment-core/types/model';

export class AssessmentManageScoreTemplatesApi extends MyApi<AssessmentScoreTemplate> {
  constructor(config?: VApiConfig) {
    super({
      namespace: '/assessment/manage',
      name: 'score_template',
      ...config,
    });
  }
}
