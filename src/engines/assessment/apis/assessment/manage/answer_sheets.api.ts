import { VApiConfig } from '@/lib/vails/api';
import { MyApi } from '@/apis/MyApi';
import { AssessmentAnswerSheet } from '@/engines/assessment/assessment-core/types/model';

export class AssessmentManageAnswerSheetsApi extends MyApi<AssessmentAnswerSheet> {
  constructor(config?: VApiConfig) {
    super({
      namespace: '/assessment/manage',
      name: 'answer_sheet',
      ...config,
    });
  }
}
