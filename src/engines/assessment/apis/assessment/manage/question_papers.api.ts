import { VApiConfig } from '@/lib/vails/api';
import { MyApi } from '@/apis/MyApi';
import { AssessmentQuestionPaper } from '@/engines/assessment/assessment-core/types/model';

export class AssessmentManageQuestionPapersApi extends MyApi<AssessmentQuestionPaper> {
  constructor(config?: VApiConfig) {
    super({
      namespace: '/assessment/manage',
      name: 'question_paper',
      ...config,
    });
  }
}
