import { VApiConfig } from '@/lib/vails/api';
import { MyApi } from '@/apis/MyApi';
import { AssessmentQuestionBank } from '@/engines/assessment/assessment-core/types/model';

export class AssessmentManageQuestionBanksApi extends MyApi<AssessmentQuestionBank> {
  constructor(config?: VApiConfig) {
    super({
      namespace: '/assessment/manage',
      name: 'question_bank',
      ...config,
    });
  }
}
