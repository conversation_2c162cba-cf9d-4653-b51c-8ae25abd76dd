import { VApiConfig } from '@/lib/vails/api';
import { MyApi } from '@/apis/MyApi';
import { AssessmentQuestionSetting } from '@/engines/assessment/assessment-core/types/model';

export class AssessmentManageQuestionSettingsApi extends MyApi<AssessmentQuestionSetting> {
  constructor(config?: VApiConfig) {
    super({
      namespace: '/assessment/manage',
      name: 'question_setting',
      ...config,
    });
  }
}
