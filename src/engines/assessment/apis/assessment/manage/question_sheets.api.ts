import { MyApi } from '@/apis/MyApi';
import { AssessmentQuestionSheet } from '@/engines/assessment/assessment-core/types/model';
import { VApiConfig } from '@/lib/vails/api';

export class AssessmentManageQuestionSheetsApi extends MyApi<AssessmentQuestionSheet> {
  constructor(config?: VApiConfig) {
    super({
      namespace: '/assessment/manage',
      name: 'question_sheet',
      actions: [
        { name: 'statistics', method: 'post', on: 'member' },
        { name: 'export_headers', method: 'post', on: 'collection' },
        { name: 'export', method: 'post', on: 'collection' },
      ],
      ...config,
    });
  }
}
