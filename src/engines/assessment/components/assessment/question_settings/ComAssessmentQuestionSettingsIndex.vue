<script lang="ts">
import { defineComponent, computed, toRefs } from 'vue';
import ComAssessmentQuestionSettingsShow from './ComAssessmentQuestionSettingsShow.vue';

const ComAssessmentQuestionSettingsIndex = defineComponent({
  name: 'ComAssessmentQuestionSettingsIndex',
  components: {
    ComAssessmentQuestionSettingsShow,
  },
  props: {
    store: { type: Object, required: true },
  },
  setup(props) {
    const config = computed(() => ({
      recordName: '题目模板',
      store: props.store,
      // pagination: {
      //   perPage: 15,
      //   showPageSizeChanger: false,
      //   hideOnSinglePage: false,
      //   showSizeChanger: false,
      // }
      template: 'assessment_question_setting',
      // detail: {
      //   mode: 'auto',
      //   mode: 'drawer',
      //   width: '1100px',
      // },
      mode: 'table',
      // editApi:
      // showCount: true,
      // actions: [
      //  { key: 'create', enabled: true },
      //  { key: 'update', enabled: true },
      //  { key: 'delete', enabled: true },
      //  { key: 'import', enabled: true },
      //  { key: 'export', enabled: true },
      // ],
      // table: {
      //   scroll: { y: '70vh' },
      //   // columns: [{ dataIndex: 'id', title: 'ID' }],
      // },
      // selection: {
      //   showByDefault: false;
      //   showSwitch: false;
      // };
      // searcherSimpleOptions: [{key: 'title', label: '标题', type: 'string' }],
    }));

    return {
      ...toRefs(props),
      config,
    };
  },
});

export default ComAssessmentQuestionSettingsIndex;
</script>

<template lang="pug">
.com-assessment-manage-question-settings-index
  TaIndexView(:config='config')
    //- template(#card='{ record }')
    //- template(#table)
    //-    a-table-column(:autoHeight='true' title='名称' dataIndex='record')
    //- template(#detail='{ record, onClose }')
    //-   ComAssessmentQuestionSettingsShow(
    //-     v-if='record.id'
    //-     :store='store'
    //-     :extendRoute='`/assessment/manage/question_settings/${record.id}`'
    //-     :editable='editable'
    //-     @afterDelete='onClose'
    //-     @afterExtend='onClose'
    //-   )

</template>

<style lang="stylus" scoped>
.com-assessment-manage-question-settings-index
  height 100%
  width 100%
</style>
