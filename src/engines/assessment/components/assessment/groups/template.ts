export const assessmentGroupManageTemplate = {
  type: 'layout',
  model: {},
  fields: [
    {
      key: 'input_95386509597',
      name: '名称',
      type: 'input',
      model: { summary: true, attr_type: 'string' },
      fields: [],
      options: { span: 24 },
      model_key: 'name',
      model_key_prefix: '',
    },
  ],
  actions: [
    { key: 'create', enabled: true },
    { key: 'update', enabled: true },
    { key: 'delete', enabled: true },
    { key: 'import', enabled: false },
    { key: 'export', enabled: false },
  ],
  options: {
    label: {},
    theme: { card: {}, form: {}, background: {} },
    create_text: '提交',
    update_text: '提交',
  },
  model_key: 'layout_1990581881',
  column_attributes: [{ _id: 'input_95386509597', title: ['名称'], dataIndex: 'name' }],
};
