<script lang="ts">
import { defineComponent, computed, toRefs } from 'vue';
import ComAssessmentGroupsShow from './ComAssessmentGroupsShow.vue';
import { assessmentGroupManageTemplate } from './template';

const ComAssessmentGroupsIndex = defineComponent({
  name: 'ComAssessmentGroupsIndex',
  components: {
    ComAssessmentGroupsShow,
  },
  props: {
    store: { type: Object, required: true },
    activityId: { type: Number, required: true },
    scrollY: { type: String, default: '70vh' },
  },
  setup(props) {
    const config = computed(() => ({
      recordName: '考核分组',
      store: props.store,
      // pagination: {
      //   perPage: 15,
      //   showPageSizeChanger: false,
      //   hideOnSinglePage: false,
      //   showSizeChanger: false,
      // }
      template: assessmentGroupManageTemplate,
      // detail: {
      //   mode: 'auto',
      //   mode: 'drawer',
      //   width: '1100px',
      // },
      mode: 'table',
      // editApi:
      // showCount: true,
      // actions: [
      //  { key: 'create', enabled: true },
      //  { key: 'update', enabled: true },
      //  { key: 'delete', enabled: true },
      //  { key: 'import', enabled: true },
      //  { key: 'export', enabled: true },
      // ],
      table: {
        scroll: { y: props.scrollY },
        // columns: [{ dataIndex: 'id', title: 'ID' }],
      },
      // selection: {
      //   showByDefault: false;
      //   showSwitch: false;
      // };
      // searcherSimpleOptions: [{key: 'title', label: '标题', type: 'string' }],
    }));

    return {
      ...toRefs(props),
      config,
    };
  },
});

export default ComAssessmentGroupsIndex;
</script>

<template lang="pug">
.com-assessment-manage-groups-index
  TaIndexView(:config='config' )
    //- template(#card='{ record }')
    //- template(#table)
    //-    a-table-column(:autoHeight='true' title='名称' dataIndex='record')
    //- template(#detail='{ record, onClose }')
    //-   ComAssessmentGroupsShow(
    //-     v-if='record.id'
    //-     :store='store'
    //-     :extendRoute='`/assessment/manage/activities/${activityId}/groups/${record.id}`'
    //-     :editable='editable'
    //-     @afterDelete='onClose'
    //-     @afterExtend='onClose'
    //-   )

</template>

<style lang="stylus" scoped>
.com-assessment-manage-groups-index
  height 100%
  width 100%
</style>
