<script lang="ts">
import { TaBreadcrumbInterface } from '@/components/global/ta-component/TaBreadcrumb.vue';
import {
  insertJsonata,
  jsonataGet,
} from '@/components/global/ta-component/ta-template-form-core/useJsonata';
import { PropType, defineComponent, ref, toRefs } from 'vue';
import ComAssessmentAnswerSheetsIndex from '../answer_sheets/ComAssessmentAnswerSheetsIndex.vue';
import ComAssessmentQuestionSheetsStatistics from './ComAssessmentQuestionSheetsStatistics.vue';

const ComAssessmentQuestionSheetsDashboard = defineComponent({
  name: 'ComAssessmentQuestionSheetsDashboard',
  components: {
    ComAssessmentQuestionSheetsStatistics,
    ComAssessmentAnswerSheetsIndex,
  },
  props: {
    breadcrumbs: { type: Array as PropType<TaBreadcrumbInterface[]>, default: () => [] },
    store: { type: Object, required: true },
    answerSheetStore: { type: Object, required: true },
    questionSettingStore: { type: Object, required: true },
  },
  setup(props) {
    const tabs = [
      { key: 'statistics', label: '调查表统计' },
      { key: 'answers', label: '调查表列表' },
    ];

    const activeTabKey = ref(tabs[0].key);

    const statisticsTabs = [
      { key: 'table', label: '看表格' },
      { key: 'chart', label: '看图表' },
    ];

    const activeStatisticsTab = ref(statisticsTabs[0]);

    const insertQuestionSheet = (name: string) => {
      return insertJsonata(name, (dataKey: string) =>
        jsonataGet({ questionSheet: props.store.record.value }, dataKey),
      );
    };

    return {
      ...toRefs(props),
      record: props.store.record,
      tabs,
      activeTabKey,

      statisticsTabs,
      activeStatisticsTab,
      insertQuestionSheet,
    };
  },
});
export default ComAssessmentQuestionSheetsDashboard;
</script>

<template lang="pug">
.com-assessment-manage-question-sheets-dashboard.flex.flex-col.h-full
  TaBreadcrumbWithTitle.breadcrumbs.flex-shrink-0(
    v-if='breadcrumbs.length > 0',
    :breadcrumbs='breadcrumbs',
    :title='insertQuestionSheet(record.name)'
  )
  .header.flex-shrink-0.my-3.flex.justify-between
    TaTab.tabs(v-model:value='activeTabKey', :tabs='tabs')
    .actions
      a-popover(v-if='activeTabKey === "statistics"', placement='bottom')
        template(#content)
          .item.space-x-10.p-5.flex.items-center.space-between.text-sm.font-medium.cursor-pointer(
            v-for='item in statisticsTabs',
            :class='{ "text-primary": activeStatisticsTab.key == item.key }',
            @click='() => (activeStatisticsTab = item)'
          )
            .flex-shrink-0 {{ item.label }}
            TaIcon.h-4.w-4(v-if='activeStatisticsTab.key == item.key', type='CheckOutlined')
        .flex.items-center.space-x-2.cursor-pointer
          .text-sm.font-medium.flex-shrink-0 {{ activeStatisticsTab.label }}
          TaIcon.h-4.w-4(type='solid/chevron-down')
  .flex-grow.h-0
    ComAssessmentQuestionSheetsStatistics(
      v-show='activeTabKey === "statistics"',
      :store='store',
      :mode='activeStatisticsTab.key'
    )
    ComAssessmentAnswerSheetsIndex(
      v-show='activeTabKey === "answers"',
      :store='answerSheetStore',
      :questionSettingStore='questionSettingStore'
    )
</template>

<style lang="stylus" scoped>
.com-assessment-manage-question-sheets-dashboard
  .tabs
    >>> .tab-active
      .tab-label
        color $primary-color
</style>
