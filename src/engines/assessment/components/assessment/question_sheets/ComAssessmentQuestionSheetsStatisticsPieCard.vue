<script lang="ts">
import { defineComponent, toRefs, computed } from 'vue';
import { VObject } from '@/lib/vails';

// let TaScreenEchartBase: any = null;
// try {
//   TaScreenEchartBase = require('@/engines/screen/components/global/screen/components/TaScreenEchartBase.vue');
// } catch {}

const ComAssessmentQuestionSheetsStatisticsPieCard = defineComponent({
  name: 'ComAssessmentQuestionSheetsStatisticsPieCard',
  components: {
    // TaScreenEchartBase,
  },
  props: {
    stat: { type: Object, required: true },
    insertQuestionSheet: { type: Function, required: true },
  },
  setup(props) {
    const options = computed(() => ({
      // color: ['#06B6D4', '#22D3EE', '#67E8F9 ', '#A5F3FC'],
      color: ['#5470C6', '#91CC75', '#FAC858', '#EE6666 ', '#73C0DE'],
      legend: {
        bottom: -5,
        left: 'center',
        itemGap: 30,
        icon: 'circle',
      },
      series: [
        {
          type: 'pie',
          radius: ['33.6%', '56%'],
          avoidLabelOverlap: true,
          label: {
            show: true,
            position: 'outside',
            formatter: '{c}票 | {d}%',
            color: '#737373',
            textStyle: {
              fontSize: 14,
            },
          },
          labelLine: {
            show: true,
          },
          data: props.stat.stat.map((i: VObject) => ({
            name: i.name,
            value: i.count,
          })),
        },
      ],
    }));

    return {
      ...toRefs(props),
      options,
    };
  },
});
export default ComAssessmentQuestionSheetsStatisticsPieCard;
</script>

<template lang="pug">
.com-assessment-manage-question-sheets-statistics-pie-card.w-full.h-full.border.border-gray-300.rounded.flex.flex-col
  .border.border-transparent.border-b-gray-300.py-4.px-5.flex-shrink-0
    a-tooltip(:title='insertQuestionSheet(stat.name)')
      .truncate {{ stat.index + 1 }}. {{ insertQuestionSheet(stat.name) }}
  .chart.w-full.h-66.py-5.flex-shrink-0
    TaScreenEchartBase(:options='options')

</template>

<style lang="stylus" scoped>
.com-assessment-manage-question-sheets-statistics-pie-card
  height fit-content
</style>
