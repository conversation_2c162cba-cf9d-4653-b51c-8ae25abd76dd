import { WritableComputedRef, Ref } from 'vue';
import { VObject } from '@/lib/vails/model/index';
import Excel from 'exceljs';
import { saveAs } from 'file-saver';
import { TaTemplateFormSelect } from '@/components/global/ta-component/ta-template-form-core/types';
import { message } from 'ant-design-vue';
import { AssessmentQuestionSheetItemInterface } from '@/engines/assessment/assessment-core/useAssessmentQuestionSheet';

export const useAssessmentQuestionSheetsFormEditorImportExport = (
  fields: WritableComputedRef<AssessmentQuestionSheetItemInterface[]>,
  typeOptions: Ref<TaTemplateFormSelect[]>,
  generateKeyFn: () => string,
) => {
  const onImportTemplateDownload = () => {
    const workbook = new Excel.Workbook();
    const worksheet = workbook.addWorksheet('Question');

    worksheet.columns = [
      { header: '题目', key: 'name', width: 100, outlineLevel: 1 },
      {
        header: '题目类型（单选题、多选题、填空题、简答题）',
        key: 'type',
        width: 20,
        outlineLevel: 1,
      },
      { header: '必填（是/否））', key: 'required', width: 50, outlineLevel: 1 },
      { header: '选项（回车分隔）', key: 'options', width: 100, outlineLevel: 1 },
      { header: '答案', key: 'answer', width: 100, outlineLevel: 1 },
      { header: '分数', key: 'score', width: 100, outlineLevel: 1 },
    ];

    const row1 = worksheet.addRow({
      name: '问题一',
      type: '单选题',
      required: '是',
      options: 'A.选项一\nB.选项二\nC.选项三',
      answer: 'A',
    });
    row1.height = 50;

    const row2 = worksheet.addRow({
      name: '问题二',
      type: '多选题',
      required: '否',
      options: 'A.选项一\nB.选项二\nC.选项三',
      answer: 'AB',
    });
    row2.height = 50;

    worksheet.addRow({ name: '问题三', type: '填空题', required: '是', options: '' });
    worksheet.addRow({ name: '问题四', type: '简答题', required: '是', options: '' });

    workbook.xlsx.writeBuffer().then((buffer: any) => {
      saveAs(new Blob([buffer]), `导入模板.xlsx`);
    });
  };

  const onImport = () => {
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.accept = '.xlsx,.xls';
    fileInput.onchange = () => {
      const file = fileInput.files?.[0];
      if (!file) {
        return;
      }

      const reader = new FileReader();
      reader.onload = () => {
        try {
          const fileData = reader.result;
          if (!fileData) {
            return;
          }

          const workbook = new Excel.Workbook();
          workbook.xlsx.load(fileData as Buffer).then(() => {
            const worksheet = workbook.getWorksheet(1)!;
            const rows = worksheet.getRows(2, worksheet.rowCount - 1);
            const newFields: AssessmentQuestionSheetItemInterface[] = [];
            if (!rows) return;

            rows.forEach((row: any) => {
              const name = row.getCell('A').value?.toString();
              const typeName = row.getCell('B').value?.toString();
              const required = row.getCell('C').value?.toString() === '是' ? true : false;
              const type = typeOptions.value.find(item => item.label === typeName)?.value;

              const options = row.getCell('D').value?.richText
                ? row
                    .getCell('D')
                    .value.richText.map((i: VObject) => i.text)
                    .join()
                : row.getCell('D').value?.toString();

              const select = options ? options.split('\n').map((label: string) => ({ label })) : [];

              const answerStr = row.getCell('E').value?.toString();
              const score = row.getCell('F').value?.toString();

              if (!name || !type) {
                return;
              }

              const config: VObject = {};
              const answer: VObject = {};
              let enable_answer = false;

              switch (typeName) {
                case '简答题':
                  config.textarea = { required };
                  answer.text = answerStr;
                  if (answerStr) enable_answer = true;
                  break;
                case '填空题':
                  config.textarea = { required };
                  answer.text = answerStr;
                  if (answerStr) enable_answer = true;

                  break;
                case '单选题':
                  config.radio = { required, select };
                  answer.options = select.find((opt: VObject) =>
                    opt.label.startsWith(answerStr),
                  )?.label;
                  if (answer.options) enable_answer = true;

                  break;
                case '多选题':
                  config.checkbox = { required, select };
                  answer.options = answerStr
                    .split('')
                    .map(
                      (str: string) =>
                        select.find((opt: VObject) => opt.label.startsWith(str))?.label,
                    )
                    .filter((i?: string) => i);
                  if (answer.options.length > 0) enable_answer = true;

                  break;
                default:
                  break;
              }

              newFields.push({
                _id: generateKeyFn(),
                name,
                type,
                config,
                answer,
                enable_answer,
                score: score ? Number(score) : undefined,
              });
            });

            fields.value = [...fields.value, ...newFields];
          });
          message.success('导入成功');
        } catch {
          message.error('导入失败');
        }
      };
      reader.readAsArrayBuffer(file);
    };
    fileInput.click();
  };

  const onExport = () => {
    const workbook = new Excel.Workbook();
    const worksheet = workbook.addWorksheet('Question');

    worksheet.columns = [
      { header: '题目', key: 'name', width: 100, outlineLevel: 1 },
      {
        header: '题目类型（单选题、多选题、填空题、简答题）',
        key: 'type',
        width: 20,
        outlineLevel: 1,
      },
      { header: '必填（是/否））', key: 'required', width: 50, outlineLevel: 1 },
      { header: '选项（回车分隔）', key: 'options', width: 100, outlineLevel: 1 },
      { header: '答案', key: 'answer', width: 100, outlineLevel: 1 },
    ];

    fields.value.forEach(field => {
      const { name, type, config } = field;
      const typeName = typeOptions.value.find(item => item.value === type)?.label;
      const required =
        config?.textarea?.required || config?.radio?.required || config?.checkbox?.required;
      const select = config?.radio?.select || config?.checkbox?.select;
      const options = select ? select.map((item: { label: string }) => item.label).join('\n') : '';
      const answer = parseAnswer(typeName, field.answer);
      const row = worksheet.addRow({
        name,
        type: typeName,
        required: required ? '是' : '否',
        options,
        answer,
      });
      row.height = 50;
    });

    workbook.xlsx.writeBuffer().then((buffer: any) => {
      saveAs(new Blob([buffer]), `${Date.now()}导出.xlsx`);
    });
  };

  const parseAnswer = (typeName: string, answer?: VObject) => {
    switch (typeName) {
      case '简答题':
        return answer?.text;
      case '填空题':
        return answer?.text;
      case '单选题':
        return answer?.options?.[0];
      case '多选题':
        return answer?.options?.map((o: string) => o[0]).join('');
      default:
        break;
    }
  };

  return {
    onImport,
    onExport,
    onImportTemplateDownload,
  };
};
