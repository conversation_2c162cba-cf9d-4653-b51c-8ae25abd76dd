<script lang="ts">
import { defineComponent, toRefs, computed, watch, ref } from 'vue';
import { message } from 'ant-design-vue';

const ComAssessmentQuestionSheetsHintOptions = defineComponent({
  name: 'ComAssessmentQuestionSheetsHintOptions',
  components: {},
  props: {
    record: { type: Object, required: true },
  },
  setup(props, { emit }) {
    const checked = ref(false);
    const localRecord = computed({
      get: () => props.record,
      set: val => emit('update:record', val),
    });

    watch(
      () => true,
      () => {
        if (!localRecord.value.formData?.hint_options) localRecord.value.formData.hint_options = {};
        if (!localRecord.value.formData.hint_options?.content)
          localRecord.value.formData.hint_options.content = '';
        if (!localRecord.value.formData.hint_options?.expired)
          localRecord.value.formData.hint_options.expired = 0;
        if (!localRecord.value.formData.hint_options?.switch)
          localRecord.value.formData.hint_options.switch = false;
      },
      { immediate: true },
    );
    const switchState = () => {
      if (checked.value) {
        localRecord.value.formData.hint_options.switch = true;
      } else {
        localRecord.value.formData.hint_options.switch = false;
      }
      console.log(
        'localRecord.value.formData.hint_options.switch: ',
        localRecord.value.formData.hint_options.switch,
      );
    };
    const onSave = () => {
      props.record
        .save()
        .then(() => {
          message.success('保存成功');
        })
        .catch(() => {
          message.error('保存失败');
        });
    };

    return {
      ...toRefs(props),
      localRecord,
      checked,
      switchState,
      onSave,
    };
  },
});
export default ComAssessmentQuestionSheetsHintOptions;
</script>

<template lang="pug">
.com-assessment-manage-question-sheets-hint-options.w-full.h-full
  .overview.flex-shrink-0.py-6.px-4
    .editor.flex-grow.p-2.space-y-2.flex.flex-col
      .actions.w-full.flex.flex-end.items-center.space-x-2.flex-shrink-0
        a-switch(
          type='primary',
          v-model:checked='checked',
          checked-children='开启',
          un-checked-children='关闭',
          @change='switchState'
        )
        a-button(type='primary', @click='onSave') 保存
    .m-4.space-y-2(v-if='localRecord.formData.hint_options')
      a-input(
        v-model:value='localRecord.formData.hint_options.title',
        prefix='标题',
        placeholder='请输入通知标题'
      )
      TaRichEditor.rich-text(
        ref='fieldRef',
        v-model:value='localRecord.formData.hint_options.content'
      )
      .flex.items-center.space-x-2(v-if='localRecord.formData.hint_options')
        .text-center.flex-shrink-0.text-gray-700 倒计时
        a-input-number(
          v-model:value='localRecord.formData.hint_options.expired',
          :min=0,
          :defaultValue=0
        )
          template(#addonAfter) 秒
</template>

<style lang="stylus" scoped></style>
