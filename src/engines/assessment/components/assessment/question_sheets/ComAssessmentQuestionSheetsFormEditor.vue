<script lang="ts">
import { ref, defineComponent, toRefs, computed, watch, getCurrentInstance, PropType } from 'vue';
import { VObject } from '@/lib/vails';
import { AssessmentQuestionSheet } from '@/engines/assessment/assessment-core/types/model';
import draggable from 'vuedraggable';
import { message } from 'ant-design-vue';
import { cloneDeep, merge } from 'lodash';
import { useAssessmentQuestionSheetsFormEditorImportExport } from './useAssessmentQuestionSheetsFormEditorImportExport';
import { AssessmentQuestionSheetModel } from '@/engines/assessment/assessment-core/models/assessment/question_sheets';
import {
  assessmentQuestionSheetCatalogTemplate,
  assessmentQuestionSheetTemplateCatalogTemplate,
} from './template';
import {
  AssessmentQuestionSheetItemInterface,
  useAssessmentQuestionSheet,
  AssessmentQuestionSheetCatalogInterface,
} from '../../../assessment-core/useAssessmentQuestionSheet';

let sign = 0;

const ComAssessmentQuestionSheetsFormEditor = defineComponent({
  name: 'ComAssessmentQuestionSheetsFormEditor',
  components: { draggable },
  props: {
    record: {
      type: Object as PropType<AssessmentQuestionSheet & AssessmentQuestionSheetModel>,
      required: true,
    },
  },
  setup(props, { emit }) {
    const generateKey = () => {
      return `${Date.now()}_${sign++}`;
    };

    const localRecord = computed<AssessmentQuestionSheet & AssessmentQuestionSheetModel>({
      get: () => props.record,
      set: val => emit('update:record', val),
    });

    const activeQuestion = ref<AssessmentQuestionSheetItemInterface>({
      _id: '',
      name: '',
      type: '',
      materials: [],
      enable_answer: false,
      answer: {},
      config: {} as VObject,
    });

    if (!localRecord.value.form) localRecord.value.form = { fields: [] };

    watch(
      () => true,
      () => {
        if (!localRecord.value.formData.form) localRecord.value.formData.form = {};
        if (!localRecord.value.formData.form.fields) localRecord.value.formData.form.fields = [];
        if (!localRecord.value.formData.form.catalogs)
          localRecord.value.formData.form.catalogs = [];
      },
      { immediate: true },
    );

    const {
      insertQuestionSheet,
      // descAry,
      // noCatalogFields: noCatalogFieldsReadonly,
      noCatalog,
      getTemplate,
      isRequired,
      questionSettingOptions,
      questionSettingFlag2Record,
      // loading,
      fetchQuestionSettings,
      calcIndexAry,
    } = useAssessmentQuestionSheet(localRecord);

    fetchQuestionSettings();

    const dataCatalogs = computed<AssessmentQuestionSheetCatalogInterface[]>({
      get: () => {
        return localRecord.value.formData?.form?.catalogs;
      },
      set: val => {
        localRecord.value.formData.form.catalogs = val;
      },
    });

    const catalogs = computed<AssessmentQuestionSheetCatalogInterface[]>(() => [
      noCatalog.value,
      ...dataCatalogs.value,
    ]);

    const fields = computed<AssessmentQuestionSheetItemInterface[]>({
      get: () => {
        return activeCatalog.value._id !== noCatalog.value._id
          ? activeCatalog.value.items
          : noCatalogFields.value;
      },
      set: val => {
        return activeCatalog.value._id !== noCatalog.value._id
          ? (activeCatalog.value.items = val)
          : (noCatalogFields.value = val);
      },
    });

    const noCatalogFields = computed<AssessmentQuestionSheetItemInterface[]>({
      get: () => {
        return localRecord.value.formData?.form?.fields;
      },
      set: val => {
        localRecord.value.formData.form.fields = val;
      },
    });

    const activeCatalog = ref<AssessmentQuestionSheetCatalogInterface>(noCatalog.value);

    const onCreateCatalog = () => {
      const newCatalog: AssessmentQuestionSheetCatalogInterface = {
        _id: generateKey(),
        weight: 1,
        name: `组别${dataCatalogs.value.length + 1}`,
        items: [],
      };

      activeCatalog.value = newCatalog;
      dataCatalogs.value.push(activeCatalog.value);
    };

    const onDeleteCatalog = (index: number) => {
      //  const index = fields.value.findIndex((i: VObject) => i._id === activeQuestion.value._id);
      if (index > -1) {
        dataCatalogs.value.splice(index, 1);
        activeCatalog.value = catalogs.value[fields.value.length - 1] || {};
        proxy.$forceUpdate();
      }
    };

    const visibleEditCatalog = ref(false);
    const catalogEditForm = ref<any>(null);

    const catalogFormData = ref<AssessmentQuestionSheetCatalogInterface | null>(null);

    const onEditCatalog = (catalog: AssessmentQuestionSheetCatalogInterface) => {
      catalogFormData.value = cloneDeep(catalog);
      visibleEditCatalog.value = true;
    };

    const onEditCatalogSubmit = () => {
      catalogEditForm.value?.validate().then(() => {
        const val = catalogFormData.value;
        if (val) {
          const index = dataCatalogs.value.findIndex(
            (catalog: AssessmentQuestionSheetCatalogInterface) => catalog._id === val._id,
          );
          dataCatalogs.value.splice(index, 1, val);
          visibleEditCatalog.value = false;
          catalogFormData.value = null;
        }
      });
    };

    const { proxy } = getCurrentInstance() as any;

    const onCreateQuestion = (payload?: AssessmentQuestionSheetItemInterface) => {
      const newQuestion: AssessmentQuestionSheetItemInterface = payload
        ? merge(cloneDeep(payload), {
            _id: generateKey(),
          })
        : {
            _id: generateKey(),
            name: '',
            type: '',
            enable_answer: false,
            depth: 0,
            materials: [],
            answer: {},
            config: {} as VObject,
          };
      activeQuestion.value = newQuestion;
      fields.value.push(activeQuestion.value);
    };

    const onDeleteQuestion = () => {
      const index = fields.value.findIndex((i: VObject) => i._id === activeQuestion.value._id);
      if (index > -1) {
        fields.value.splice(index, 1);
        activeQuestion.value = fields.value[fields.value.length - 1] || {};
        proxy.$forceUpdate();
      }
    };

    const onSave = () => {
      props.record
        .save()
        .then(() => {
          message.success('保存成功');
        })
        .catch(() => {
          message.error('保存失败');
        });
    };

    const onCopyQuestion = () => {
      onCreateQuestion(activeQuestion.value);
    };

    const onFormTypeChange = (value: string) => {
      activeQuestion.value.config = Object.keys(
        questionSettingFlag2Record.value[activeQuestion.value.type]?.config_form || {},
      ).reduce((out: VObject, key: string) => {
        out[key] = {};
        return out;
      }, {});
      activeQuestion.value.type = value;
      activeQuestion.value.answer = {};
    };

    const triggerDefaultValue = (key: string) => {
      if (!activeQuestion.value.config[key]) activeQuestion.value.config[key] = {};
      if (!activeQuestion.value.answer) activeQuestion.value.answer = {};

      return true;
    };

    const { onImport, onExport, onImportTemplateDownload } =
      useAssessmentQuestionSheetsFormEditorImportExport(
        fields,
        questionSettingOptions,
        generateKey,
      );

    const catalogId2Score = computed(() => {
      const noCatalogScore = noCatalogFields.value.reduce(
        (out: number, item: AssessmentQuestionSheetItemInterface) => out + (item.score || 0),
        0,
      );
      return dataCatalogs.value.reduce(
        (out: Record<string, number>, catalog: AssessmentQuestionSheetCatalogInterface) => {
          out[catalog._id] = catalog.items.reduce(
            (out: number, item: AssessmentQuestionSheetItemInterface) => out + (item.score || 0),
            0,
          );
          return out;
        },
        { [noCatalog.value._id]: noCatalogScore },
      );
    });

    const isAssessmentQuestionSheetTemplate = computed(() => {
      // return localRecord.value.type === 'Assessment::QuestionSheetTemplate';
      return true;
    });

    return {
      ...toRefs(props),
      dataCatalogs,
      fields,

      questionSettingOptions,
      questionSettingFlag2Record,

      activeCatalog,
      onCreateCatalog,
      onDeleteCatalog,
      onEditCatalog,
      noCatalog,
      onEditCatalogSubmit,
      visibleEditCatalog,
      catalogFormData,
      catalogEditForm,
      assessmentQuestionSheetCatalogTemplate,

      activeQuestion,
      onCreateQuestion,
      onDeleteQuestion,

      onSave,
      onCopyQuestion,
      triggerDefaultValue,

      getTemplate,
      onFormTypeChange,
      isRequired,

      insertQuestionSheet,

      onImport,
      onExport,
      onImportTemplateDownload,

      catalogId2Score,

      localRecord,
      isAssessmentQuestionSheetTemplate,
      assessmentQuestionSheetTemplateCatalogTemplate,

      calcIndexAry,
    };
  },
});
export default ComAssessmentQuestionSheetsFormEditor;
</script>

<template lang="pug">
.com-assessment-manage-question-sheets-form-editor.w-full.h-full.overflow-auto.flex.space-x-4(
  v-if='localRecord.formData.form'
)
  .catalogs.flex-shrink-0.bg-white.rounded-lg.min-w-45.flex.flex-col.overflow-hidden
    .title.flex.justify-between
      | 分类
      .rounded-full.w-5.h-5.bg-white.text-primary.flex.justify-center.items-center.cursor-pointer(
        @click='onCreateCatalog'
      )
        | +

    .catalogs.py-2.h-full.overflow-auto
      .no-catalog-item.relative.space-y-2.px-2.border-l-4.border-transparent.cursor-not-allowed.py-2(
        :class='{ "active-catalog": noCatalog._id === activeCatalog?._id }',
        @click='() => (activeCatalog = noCatalog)'
      )
        .text-sm.font-medium.text-gray-400.transition-all
          | {{ noCatalog.name }}

        .catalog-score.text-gray-700.text-xs.flex.items-center
          | 总分：
          .catalog-score-value
            | {{ catalogId2Score[noCatalog._id] }}

      draggable.w-full.space-y-2.py-2(:list='dataCatalogs', item-key='_id')
        template(#item='{ element: catalog, index }')
          .catalog-item.editable-catalog.relative.space-y-2.px-2.border-l-4.border-transparent.cursor-pointer.py-2(
            :class='{ "active-catalog": catalog._id === activeCatalog?._id }',
            @click='() => (activeCatalog = catalog)'
          )
            .catalog-text.text-sm.text-gray-700.transition-all.flex.items-center.justify-between
              a-tooltip(:title='catalog.name')
                .flex-grow.w-0.font-medium.truncate {{ catalog.name }}
              .catalog-weight.text-gray-400
                | ({{ (catalog.weight * 100).toFixed(1) }}%)
              .catalog-actions.flex.item-centers.space-x-2
                TaIconTooltip(
                  icon='EditOutlined',
                  tooltip='编辑分类',
                  @click='() => onEditCatalog(catalog)'
                )
                TaPopoverConfirm(title='确定删除该分类吗？', @confirm='() => onDeleteCatalog(index)')
                  TaIconTooltip(icon='DeleteOutlined', tooltip='删除分类')
            .catalog-score.text-gray-700.text-xs.flex.items-center
              | 总分：
              .catalog-score-value.mr-2
                | {{ catalog.random_count ? (catalogId2Score[catalog._id] * (catalog.random_count / catalog.items.length)).toFixed(1) : catalogId2Score[catalog._id] }}
            .catalog-random-count.text-xs(
              v-if='catalog.random_count && isAssessmentQuestionSheetTemplate'
            )
              | (从 {{ catalog.items.length }} 题中随机 {{ catalog.random_count }} 题)

  .overview.w-120.flex-shrink-0.bg-white.rounded-lg.overflow.flex.flex-col.overflow-hidden
    .title.flex.justify-between
      | 题目
      .rounded-full.w-5.h-5.bg-white.text-primary.flex.justify-center.items-center.cursor-pointer(
        @click='() => onCreateQuestion()'
      )
        | +
    draggable.h-full.space-y-4.overflow-auto.py-4(:list='fields', item-key='_id')
      template(#item='{ element: question, index }')
        .question-item.relative.space-y-2.pb-2.px-2.border-l-4.border-transparent(
          :class='{ "active-question": question._id === activeQuestion._id }'
        )
          .flex(v-if='question.materials?.length > 0')
            .no.mt-2.mr-2 （{{ calcIndexAry(question, fields[index - 1]).splice(0, calcIndexAry(question, fields[index - 1]).length - 1).join('.') }}）
            TaContentField.materials(:value='question.materials', :disabled='true')
          .text-sm.font-medium.text-gray-700.transition-all
            span.text-red-600(v-if='isRequired(question)') *
            span.no （{{ calcIndexAry(question, fields[index - 1]).join('.') }}）
            span.name.whitespace-pre-wrap {{ insertQuestionSheet(question.name) }}
            span(v-if='question.score') &nbsp({{ question.score }}分)
          TaTemplateForm.w-full(
            v-if='question.type && questionSettingFlag2Record[question.type]',
            :modelValue='{ questionSheet: record }',
            :template='getTemplate(question)'
          )
          .absolute.z-10.w-full.h-full.top-0.left-0.cursor-pointer(
            @click='() => (activeQuestion = question)'
          )

  .editor.space-y-2.flex.flex-col.bg-white.rounded-lg.flex-grow.min-w-110.overflow-hidden
    .title.flex.justify-between.whitespace-nowrap.space-x-2.overflow-auto
      | 配置
      .actions.flex.flex-end.items-center.space-x-2
        .button(type='primary', @click='onSave') 保存问卷
        a-popover
          .button(type='primary') 导入导出
          template(#content)
            .py-2.px-4.cursor-pointer(@click='onImportTemplateDownload') 导出模板
            .py-2.px-4.cursor-pointer(@click='onImport') 导入题目
            .py-2.px-4.cursor-pointer(@click='onExport') 导出问卷
        //- .button(type='primary', @click='() => onCreateQuestion()') 创建题目
        .button(v-if='activeQuestion._id', type='warning', @click='onDeleteQuestion') 删除题目
        .button(v-if='activeQuestion._id', @click='onCopyQuestion') 复制题目

    .editor-container.space-y-4.overflow-auto.flex-grow.h-0.py-4.px-6(v-if='activeQuestion._id')
      .name.flex.space-y-2.flex-col
        .text-sm.font-medium.text-gray-700.flex-shrink-0.flex.justify-between.items-center
          | 题目：
          .w-30.flex.items-center
            a-input-number(v-model:value='activeQuestion.score', :min='0')
            .ml-2 分
        a-radio-group(v-model:value='activeQuestion.depth')
          a-radio(:value='1') 一级序号
          a-radio(:value='2') 二级序号
          a-radio(:value='3') 三级序号
        a-textarea.h-40(v-model:value='activeQuestion.name')

      .flag.flex.flex-col.space-y-2
        .text-sm.font-medium.text-gray-700.flex-shrink-0 类型：
        TaSelect(
          :value='activeQuestion.type',
          :options='questionSettingOptions',
          @update:value='onFormTypeChange'
        )

      .config.space-y-2
        .text-sm.font-medium.text-gray-700.flex-shrink-0.flex.justify-between.items-center
          | 答案：
          a-switch(v-model:checked='activeQuestion.enable_answer')

        .config-item(v-if='activeQuestion.enable_answer')
          TaTemplateForm.w-full(
            v-if='activeQuestion.type && questionSettingFlag2Record[activeQuestion.type]',
            :modelValue='activeQuestion.answer',
            :template='getTemplate(activeQuestion)'
          )

      .config.space-y-2(v-if='questionSettingFlag2Record[activeQuestion.type]?.config_form')
        .text-sm.font-medium.text-gray-700.flex-shrink-0 配置：
        .config-item(
          v-for='(template, key) in questionSettingFlag2Record[activeQuestion.type]?.config_form'
        )
          TaTemplateForm(
            v-if='triggerDefaultValue(key)',
            v-model:modelValue='activeQuestion.config[key]',
            :template='template'
          )
      .config.space-y-2
        .text-sm.font-medium.text-gray-700.flex-shrink-0 阅读材料：
        TaContentField.config(
          v-model:value='activeQuestion.materials'
        )

    .editor-empty.flex.h-full.items-center.text-gray-400.text-lg.items-center.justify-center(
      v-else
    )
      | 未选择题目

  a-modal(v-model:visible='visibleEditCatalog', @ok='onEditCatalogSubmit')
    TaTemplateForm(
      v-if='visibleEditCatalog',
      ref='catalogEditForm',
      v-model:modelValue='catalogFormData',
      :template='isAssessmentQuestionSheetTemplate ? assessmentQuestionSheetTemplateCatalogTemplate : assessmentQuestionSheetCatalogTemplate'
    )
</template>

<style lang="stylus" scoped>
>>> .ta-radio-group
  @apply pt-4;
.active-question, .active-catalog
  border-color $primary-color
  @apply rounded border-l-4;
.bg-primary
  background-color $primary-color
.title
  @apply text-sm font-medium text-white bg-primary px-4 h-12 flex items-center flex-shrink-0;
.button
  color $primary-color
  @apply rounded-lg bg-white py-1 px-4 text-sm cursor-pointer;
.catalog-text
  .catalog-actions
    display none
  &:hover
    .catalog-actions
      display flex
    .catalog-weight
      display none
</style>

<style lang="stylus">
.content:has(.com-assessment-manage-question-sheets-form-editor)
  background transparent !important
</style>
