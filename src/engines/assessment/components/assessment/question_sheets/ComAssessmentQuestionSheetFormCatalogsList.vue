<script lang="ts">
import { defineComponent, toRefs } from 'vue';

const ComAssessmentQuestionSheetFormCatalogsList = defineComponent({
  name: 'ComAssessmentQuestionSheetFormCatalogsList',
  components: {},
  props: {
    record: { type: Object, default: () => ({}) }, // question sheet
    noCatalog: { type: Object, default: () => ({}) },
  },
  setup(props, { emit }) {
    const onClickQuestion = (question: any) => {
      emit('clickQuestion', question);
    };

    return {
      ...toRefs(props),
      onClickQuestion,
    };
  },
});
export default ComAssessmentQuestionSheetFormCatalogsList;
</script>

<template lang="pug">
.assessment-question-sheet-form-catalogs-list.w-70.px-4.space-y-4.flex.flex-col.h-full(v-if='record.form?.catalogs?.length > 0')
  .container-title.flex-shrink-0 题目列表
  .container-content.flex-grow.h-0.overflow-auto.space-y-2
    template(v-if='noCatalog.items?.length > 0')
      .catalog.flex.justify-between.items-center
        a-tooltip(:title='noCatalog.name')
          .truncate {{ noCatalog.name }}
        slot(name='catalog-right', :catalog='noCatalog')
      .catalog-item.cursor-pointer(v-for='(question, index) in noCatalog.items', @click='onClickQuestion(question)')
        | 第&nbsp{{ index + 1 }}&nbsp题
        slot(name='question-right', :question='question')

    template(v-for='catalog in record.form?.catalogs?.filter(catalog => catalog.items?.length > 0)')
      .catalog.flex.justify-between.items-center
        a-tooltip(:title='catalog.name')
          .truncate {{ catalog.name }}
        slot(name='catalog-right', :catalog='catalog')

      .catalog-item.cursor-pointer(v-for='(question, index) in catalog.items', @click='onClickQuestion(question)')
        | 第&nbsp{{ index + 1 }}&nbsp题
        slot(name='question-right', :question='question')
</template>

<style lang="stylus" scoped>
.catalog
  @apply bg-gray-100 text-gray-700 font-medium py-2 px-4

.active-catalog
  background lighten($primary-color, 20%)
  color $primary-color

.container-title
  @apply font-medium text-base text-gray-900 py-2 border-b border-gray-200

.catalog-item
  @apply pl-8 pr-4 py-1 text-sm text-gray-700 flex items-center justify-between
</style>
