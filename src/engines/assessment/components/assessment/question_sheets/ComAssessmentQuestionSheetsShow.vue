<script lang="ts">
import { defineComponent, toRefs, PropType } from 'vue';
import { TaBreadcrumbInterface } from '@/components/global/ta-component/TaBreadcrumb.vue';
import ComAssessmentQuestionSheetsFormEditor from './ComAssessmentQuestionSheetsFormEditor.vue';
import ComAssessmentQuestionSheetsHintOptions from './ComAssessmentQuestionSheetsHintOptions.vue';
import {
  insertJsonata,
  jsonataGet,
} from '@/components/global/ta-component/ta-template-form-core/useJsonata';

const ComAssessmentQuestionSheetsShow = defineComponent({
  name: 'ComAssessmentQuestionSheetsShow',
  components: {
    ComAssessmentQuestionSheetsFormEditor,
    ComAssessmentQuestionSheetsHintOptions,
  },
  props: {
    store: { type: Object, required: true },
    extendRoute: { type: String, default: '' },
    editable: { type: Boolean, default: true },
    breadcrumbs: { type: Array as PropType<TaBreadcrumbInterface[]>, default: () => [] },
    template: { type: String, default: 'assessment_question_sheet' },
  },
  emits: ['afterDelete', 'afterExtend'],
  setup(props, { emit }) {
    const tabs = [
      {
        key: 'info',
        label: '基本信息',
      },
      {
        key: 'form',
        label: '表单',
      },
      {
        key: 'hint',
        label: '弹窗提示',
      },
    ];

    const afterDelete = () => {
      emit('afterDelete');
    };

    const afterExtend = () => {
      emit('afterExtend');
    };

    const insertQuestionSheet = (name: string) => {
      return insertJsonata(name, (dataKey: string) =>
        jsonataGet({ questionSheet: props.store.record.value }, dataKey),
      );
    };

    return {
      ...toRefs(props),
      tabs,
      record: props.store.record,
      afterDelete,
      afterExtend,
      insertQuestionSheet,
    };
  },
});
export default ComAssessmentQuestionSheetsShow;
</script>

<template lang="pug">
.com-assessment-manage-question-sheets-show
  TaShowLayout(
    :tabs='tabs',
    :title='insertQuestionSheet(record.name)',
    :store='store',
    :editable='editable',
    :breadcrumbs='breadcrumbs',
    :template='template',
    @afterDelete='afterDelete',
    @afterExtend='afterExtend'
  )
    template(#form_tab)
      ComAssessmentQuestionSheetsFormEditor(v-model:record='record')
    template(#hint_tab)
      ComAssessmentQuestionSheetsHintOptions(v-model:record='record')
    template(#right='{ actions }')
      ComIconText(icon='EditOutlined', text='编辑', @click='actions.onEdit')
      TaPopoverConfirm(title='删除', content='您确认删除该数据吗？', @confirm='actions.onDelete')
        ComIconText(icon='DeleteOutlined', text='删除')
</template>

<style lang="stylus" scoped>
.com-assessment-manage-question-sheets-show
  height 100%
</style>
