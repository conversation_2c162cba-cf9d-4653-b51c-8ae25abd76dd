<script lang="ts">
import { VObject } from '@/lib/vails/model';
import { merge } from 'lodash';
import { computed, defineComponent, ref, toRefs } from 'vue';

const ComAssessmentQuestionSheetsIndex = defineComponent({
  name: 'ComAssessmentQuestionSheetsIndex',
  components: {},
  props: {
    store: { type: Object, required: true },
    type: { type: String, default: 'Assessment::QuestionSheetRecord' },
  },
  setup(props, { emit }) {
    const taIndexView = ref<any>(null);

    const config = computed(() => ({
      recordName: '问卷管理',
      store: props.store,
      // pagination: {
      //   perPage: 15,
      //   showPageSizeChanger: false,
      //   hideOnSinglePage: false,
      //   showSizeChanger: false,
      // }
      template: 'assessment_question_sheet',
      detail: {
        // mode: 'route',
      },
      mode: 'table',
      // editApi:
      // showCount: true,
      // actions: [
      //  { key: 'create', enabled: true },
      //  { key: 'update', enabled: true },
      //  { key: 'delete', enabled: true },
      //  { key: 'import', enabled: true },
      //  { key: 'export', enabled: true },
      // ],
      table: {
        // scroll: { y: '70vh' },
        // columns: [{ dataIndex: 'id', title: 'ID' }],
        pushColumns: [{ title: '操作', width: '10px', fixed: 'right' }],
      },
      // selection: {
      //   showByDefault: false;
      //   showSwitch: false;
      // };
      // searcherSimpleOptions: [{key: 'title', label: '标题', type: 'string' }],
      formDataEncode: (payload: VObject) => {
        return merge(payload, { type: props.type });
      },
      params: {
        q: { type_eq: props.type },
      },
    }));
    const onDetail = (record: VObject) => {
      emit('onDetail', record);
    };
    const onShow = (record: VObject) => {
      emit('onShow', record);
    };

    return {
      ...toRefs(props),
      taIndexView,
      config,
      onDetail,
      onShow,
    };
  },
});

export default ComAssessmentQuestionSheetsIndex;
</script>

<template lang="pug">
.com-assessment-question-sheets-index
  TaIndexView(ref='taIndexView', :config='config', @onShow='onShow')
    template(#bodyCell='{ record, column, text, index }')
      template(v-if='column.title === "操作"')
        .actions.flex.items-center.space-x-3.cursor-pointer
          TaIconTooltip.icon(icon='EditOutlined', tips='详情', @click='onDetail(record)')
</template>

<style lang="stylus" scoped>
.com-assessment-question-sheets-index
  height 100%
  width 100%
</style>
