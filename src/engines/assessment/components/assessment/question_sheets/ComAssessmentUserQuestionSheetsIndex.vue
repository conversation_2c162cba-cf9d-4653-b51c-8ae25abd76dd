<script lang="ts">
import { VObject } from '@/lib/vails/model';
import { computed, defineComponent, ref, toRefs } from 'vue';

const ComAssessmentUserQuestionSheetsIndex = defineComponent({
  name: 'ComAssessmentUserQuestionSheetsIndex',
  components: {},
  props: {
    store: { type: Object, required: true },
  },
  emits: ['onShow'],
  setup(props, { emit }) {
    const taIndexView = ref<any>(null);

    const config = computed(() => ({
      recordName: '问卷',
      store: props.store,
      // pagination: {
      //   perPage: 15,
      //   showPageSizeChanger: false,
      //   hideOnSinglePage: false,
      //   showSizeChanger: false,
      // }
      template: 'assessment_question_sheet',
      detail: {
        // mode: 'route',
      },
      mode: 'table',
      // editApi:
      // showCount: true,
      actions: [
        { key: 'create', enabled: false },
        { key: 'update', enabled: false },
        { key: 'delete', enabled: false },
        { key: 'import', enabled: false },
        { key: 'export', enabled: false },
      ],
    }));

    const onShow = (record: VObject) => {
      emit('onShow', record);
    };

    return {
      ...toRefs(props),
      taIndexView,
      config,
      onShow,
    };
  },
});

export default ComAssessmentUserQuestionSheetsIndex;
</script>

<template lang="pug">
.com-assessment-user-question-sheets-index
  TaIndexView(ref='taIndexView', :config='config', @onShow='onShow')
</template>

<style lang="stylus" scoped>
.com-assessment-user-question-sheets-index
  height 100%
  width 100%
</style>
