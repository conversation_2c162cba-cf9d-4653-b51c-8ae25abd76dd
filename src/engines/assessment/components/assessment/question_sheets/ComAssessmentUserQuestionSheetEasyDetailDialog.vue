<script lang="ts">
import { defineComponent, toRefs, computed } from 'vue';
import ComAssessmentUserQuestionSheetEasyDetail from './ComAssessmentUserQuestionSheetEasyDetail.vue';

const ComAssessmentUserQuestionSheetEasyDetailDialog = defineComponent({
  name: 'ComAssessmentUserQuestionSheetEasyDetailDialog',
  components: {
    ComAssessmentUserQuestionSheetEasyDetail,
  },
  props: {
    visible: { type: Boolean, default: false },
    title: { type: String, default: '详情' },
    questionSheetId: { type: Number, default: 0 },
    answerSheetId: { type: Number, default: undefined },
    backUrl: { type: String, default: '/assessment/user/answer_sheets' },
    onSuccess: { type: Function, default: undefined },
    needAnswer: { type: Boolean, default: true },
    needSave: { type: Boolean, default: true },
    needTemporaryStorage: { type: Boolean, default: false },
    disabled: { type: Boolean, default: false },
    autoSyncValue: { type: Boolean, default: false },
  },
  setup(props, { emit }) {
    const localVisible = computed({
      get: () => props.visible,
      set: value => {
        if (!value) {
          emit('update:visible', value);
        }
      },
    });

    return {
      ...toRefs(props),
      localVisible,
    };
  },
});
export default ComAssessmentUserQuestionSheetEasyDetailDialog;
</script>

<template lang="pug">
TaNoPaddingModal.com-assessment-user-question-sheet-easy-detail-dialog(
  v-model:visible='localVisible'
  :title='title',
  :footer='null',
  width='80vw'
)
  ComAssessmentUserQuestionSheetEasyDetail.h-70vh.w-full.p-4(
    v-bind='$props',
  )
</template>

<style lang="stylus" scoped></style>
