<script lang="ts">
import { defineComponent, toRefs, onMounted, ref, computed, watch, onBeforeUnmount } from 'vue';
import { VStore, VObject } from '@/lib/vails';
import { AssessmentUserAnswerSheetsApi } from '../../../assessment-core/apis/assessment/user/answer_sheets.api';
import { AssessmentAnswerSheetModel } from '../../../assessment-core/models/assessment/answer_sheets';
import { useRoute } from 'vue-router';
import ls from '@/utils/local-storage';
import { message, Modal } from 'ant-design-vue';
import { debounce, merge, set } from 'lodash';
import useNavigateTab from '@/components/global/ta-component/useNavigateTab';
import ComAssessmentQuestionSheetFormCatalogsList from './ComAssessmentQuestionSheetFormCatalogsList.vue';
import ComAssessmentQuestionSheetFormQuestionItem from './ComAssessmentQuestionSheetFormQuestionItem.vue';
import scrollIntoView from 'smooth-scroll-into-view-if-needed';
import { useContextProvide } from '@/components/global/ta-component/ta-template-form-core/useContext';
import { TaTemplateFormItem } from '../../../../../components/global/ta-component/ta-template-form-core/types';
import {
  useAssessmentQuestionSheet,
  AssessmentQuestionSheetItemInterface,
  AssessmentQuestionSheetCatalogInterface,
} from '../../../assessment-core/useAssessmentQuestionSheet';

const KEEP_KEY = 'QUESTION_SHEET_DONE_IDS';

const storageQuestionSheetId = (id: number) => {
  const existsAry = (ls.get(KEEP_KEY) || '').split(',');
  existsAry.push(String(id));
  ls.set(KEEP_KEY, existsAry.join(','));
};

const checkQuestionSheetId = (_id: number) => {
  // TODO: 当前功能被禁用，参数 _id 暂时未使用
  // const existsAry = (ls.get(KEEP_KEY) || '').split(',');
  // return existsAry.filter((i: string) => i === String(_id)).length >= 1;
  void _id; // 明确标记参数已知但未使用
  return false;
};

const ComAssessmentUserQuestionSheetsShow = defineComponent({
  name: 'ComAssessmentUserQuestionSheetsShow',
  components: {
    ComAssessmentQuestionSheetFormCatalogsList,
    ComAssessmentQuestionSheetFormQuestionItem,
  },
  props: {
    store: { type: Object, required: true },
    answerSheetRecord: { type: Object, default: undefined }, // 传入就 update
    backUrl: { type: String, default: '' },
    onSuccess: { type: Function, default: undefined },
    needAnswer: { type: Boolean, default: true },
    needSave: { type: Boolean, default: true },
    needItemScore: { type: Boolean, default: true },
    needTemporaryStorage: { type: Boolean, default: false },
    disabled: { type: Boolean, default: false },
    autoSyncValue: { type: Boolean, default: false },
    send_emit: { type: Boolean, default: false },
  },
  emits: ['saveSuccess'],
  setup(props, { emit }) {
    useContextProvide(ref({}) as any, {}); // 避免 warning

    const route = useRoute();
    const passwd = route?.query?.passwd;

    const visibleNotice = ref(false);
    const content = ref<null | string>(null);
    const countdown = ref<number>(0);
    console.log(props.store.record, 'props.store.record');

    const {
      insertQuestionSheet,
      descAry,
      noCatalogFields,
      noCatalog,
      getTemplate,
      isRequired,
      questionSettingFlag2Record,
      loading,
      fetchQuestionSettings,
      calcIndexAry,
    } = useAssessmentQuestionSheet(props.store.record);

    const answerStore = new VStore(
      new AssessmentUserAnswerSheetsApi({
        parents: [{ type: 'question_sheets', id: props.store.record.value.id }],
      }),
      AssessmentAnswerSheetModel,
    );

    const answerRecord = ref<any>(
      props.answerSheetRecord?.id
        ? props.answerSheetRecord
        : answerStore.new({
            payload: {},
          }),
    );

    watch(
      () => props.answerSheetRecord,
      newVal => {
        if (newVal) {
          answerRecord.value = newVal;
          if (!answerRecord.value.formData?.payload) {
            answerRecord.value.formData = {
              payload: {},
            };
          }
        }
      },
    );

    const triggerDefaultValue = (key: string) => {
      if (!answerRecord.value.formData.payload[key]) answerRecord.value.formData.payload[key] = {};
      return true;
    };

    const questionRefMap = ref<Record<string, any>>({});
    const { closeCurrentTabAndNavigateTo } = useNavigateTab();

    const onSaveWithConfirm = () => {
      Modal.confirm({
        title: '提示',
        content: '是否确认提交？',
        zIndex: 100000,
        onOk: () => {
          onSave();
        },
      });
    };

    const onTemporaryStorage = () => {
      onSave(true);
    };

    const form = computed(() => {
      return answerRecord.value.question_sheet_form || props.store.record.value.form;
    });

    const onSave = (temporaryStorage = false) => {
      if (checkQuestionSheetId(props.store.record.value.id)) {
        message.warning('请勿重复提交');
        return;
      }

      (temporaryStorage
        ? Promise.resolve()
        : Promise.all(
            Object.keys(questionRefMap.value).map((_id: string) => {
              if (questionRefMap.value[_id]) {
                return questionRefMap.value[_id].form.validate();
              }
            }),
          )
      )
        .then(() => {
          answerRecord.value.formData.passwd = passwd;
          answerRecord.value.formData.state = temporaryStorage ? 'todo' : 'done';
          answerRecord.value
            .save()
            .then(() => {
              if (temporaryStorage) {
                message.success('暂存成功');
              } else {
                message.success('提交成功');
                storageQuestionSheetId(props.store.record.value.id);
                if (props.onSuccess) {
                  props.onSuccess(answerRecord.value);
                } else if (props.send_emit) {
                  emit('saveSuccess');
                } else {
                  closeCurrentTabAndNavigateTo(props.backUrl || `/assessment/user/answer_sheets`);
                }
              }
            })
            .catch(() => {
              message.error('提交失败');
            });
        })
        .catch(async () => {
          let errorCount = 0;
          await Promise.all(
            Object.keys(questionRefMap.value).map((_id: string) => {
              if (questionRefMap.value[_id]) {
                return questionRefMap.value[_id].form.validate().catch(() => errorCount++);
              }
            }),
          );
          message.error(`有题目未完成${errorCount}道`);
        });
    };

    onMounted(() => {
      if (checkQuestionSheetId(props.store.record.value.id)) {
        loading.value = false;
        message.warning('请勿重复提交');
        return;
      }
      // if (props.store.record.value) {
      //   //  当有弹窗开关且打开的时候
      //   visibleNotice.value = props.store.record.value.hint_options?.switch || false;
      //   if (visibleNotice.value) {
      //     content.value = props.store.record.value.hint_options?.content || '';
      //     // 设置弹窗关闭的时间
      //     countdown.value = props.store.record.value.hint_options?.expired || 0;
      //     // 当有设定关闭倒计时的时候，开启一个定时器
      //     if (countdown.value > 0) {
      //       const timer = setInterval(() => {
      //         countdown.value -= 1;
      //         if (countdown.value <= 0) {
      //           clearInterval(timer);
      //         }
      //       }, 1000);
      //     }
      //   }
      // }
      fetchQuestionSettings();
    });

    // 弹窗关闭方法
    // const onNoticeConfirm = () => {
    //   if (countdown.value <= 0) {
    //     visibleNotice.value = false;
    //   }
    // };

    const questionId2Filled = ref<Record<string, boolean>>({});

    let unmounted = false;
    onBeforeUnmount(() => {
      unmounted = true;
    });

    const validateWithoutRaiseError = async () => {
      if (unmounted) return;

      const promiseResult = await Promise.all(
        Object.keys(questionRefMap.value).map((id: string) => {
          return questionRefMap.value[id].form?.validateWithoutRaiseError?.() || Promise.resolve();
        }),
      );

      questionId2Filled.value = Object.keys(questionRefMap.value).reduce(
        (out: VObject, id: string, index: number) => {
          out[id] = promiseResult[index];
          return out;
        },
        {},
      );
    };

    const debounceValidateWithoutRaiseError = debounce(validateWithoutRaiseError, 500);

    watch(
      () => answerRecord.value.formData.payload,
      () => debounceValidateWithoutRaiseError(),
      { deep: true, immediate: true },
    );

    const isQuestionFilled = (question: AssessmentQuestionSheetItemInterface) => {
      return questionId2Filled.value[question._id];
    };

    const catalogId2Stat = computed(() => {
      const noCatalogStat = `${
        noCatalog.value.items.filter(
          (question: AssessmentQuestionSheetItemInterface) => questionId2Filled.value[question._id],
        ).length
      } / ${noCatalogFields.value.length}`;

      return (form.value?.catalogs || []).reduce(
        (out: VObject, catalog: AssessmentQuestionSheetCatalogInterface) => {
          out[catalog._id] = `${
            catalog.items.filter(
              (question: AssessmentQuestionSheetItemInterface) =>
                questionId2Filled.value[question._id],
            ).length
          } / ${catalog.items.length}`;
          return out;
        },
        { [noCatalog.value._id]: noCatalogStat },
      );
    });

    const catalogId2Collapsed = ref<Record<string, boolean>>({});
    const showAnswer = ref(false);

    const totalScoreStat = computed(() => {
      const result: {
        total: number;
        catalogs: Record<string, { score: number; maxScore: number; weight: number }>;
      } = {
        total: 0,
        catalogs: {},
      };
      Object.values(questionRefMap.value).forEach((questionRef: any) => {
        result.catalogs[questionRef.catalog._id] = result.catalogs[questionRef.catalog._id] || {
          score: 0,
          maxScore: 0,
          weight: questionRef.catalog.weight || 1,
        };
        result.catalogs[questionRef.catalog._id].score += questionRef.score;
        result.catalogs[questionRef.catalog._id].maxScore += questionRef.question.score || 0;
      }, 0);

      result.total = Object.values(result.catalogs).reduce(
        (out: number, item: { score: number; weight: number }) => {
          return out + item.score * item.weight;
        },
        0,
      );

      return result;
    });

    // 计算分数，现逻辑移到后端
    // const itemPayloadResult = computed(() => {
    //   return Object.keys(questionRefMap.value).reduce((out: VObject, id: string) => {
    //     out[id] = questionRefMap.value[id].score;
    //     return out;
    //   }, {});
    // });

    const onClickQuestion = (question: AssessmentQuestionSheetItemInterface) => {
      const node = questionRefMap.value[question._id].$el;
      if (node) {
        scrollIntoView(node, {
          scrollMode: 'if-needed',
          block: 'nearest',
          behavior: 'smooth',
        });
      }
    };

    const syncTimer = ref<any>(null);
    const syncCache = ref<VObject>({});

    const onSyncValue = async (_: TaTemplateFormItem, name: string[], value: any, key: string) => {
      if (props.autoSyncValue) {
        const payload = set({}, [key, name.join('.')].join('.'), value);
        const trigger = async () => {
          if (answerRecord.value?.id) {
            // 不用 save 防止刷新页面
            await answerRecord.value.update({
              payload: syncCache.value,
            });
          } else {
            answerRecord.value.formData.payload = syncCache.value;
            await answerRecord.value.save();
          }
          syncCache.value = {};
        };

        if (syncTimer.value) {
          clearTimeout(syncTimer.value);
        }
        syncCache.value = merge(syncCache.value, payload);
        syncTimer.value = setTimeout(trigger, 200);
      }
    };

    const catalogScoreAllTheSame = (catalog: AssessmentQuestionSheetCatalogInterface) => {
      return catalog.items.every(
        (question: AssessmentQuestionSheetItemInterface) =>
          question.score === catalog.items[0]?.score,
      );
    };

    return {
      ...toRefs(props),
      record: props.store.record,

      questionSettingFlag2Record,
      loading,
      getTemplate,
      isRequired,
      answerRecord,
      triggerDefaultValue,

      questionRefMap,
      onSave,
      insertQuestionSheet,
      descAry,
      visibleNotice,
      content,
      // onNoticeConfirm,
      countdown,
      noCatalogFields,
      noCatalog,

      isQuestionFilled,
      catalogId2Collapsed,
      catalogId2Stat,
      showAnswer,
      totalScoreStat,
      onClickQuestion,
      onSaveWithConfirm,
      onTemporaryStorage,
      onSyncValue,

      form,
      catalogScoreAllTheSame,
      calcIndexAry,
    };
  },
});
export default ComAssessmentUserQuestionSheetsShow;
</script>

<template lang="pug">
.com-assessment-user-question-sheets-show.flex.flex-col
  .flex.space-x-4.flex-grow.h-0.overflow-hidden
    ComAssessmentQuestionSheetFormCatalogsList.overflow-auto(
      :record='record',
      :noCatalog='noCatalog',
      @clickQuestion='onClickQuestion'
    )
      template(#catalog-right='{ catalog }')
        .stat {{ catalogId2Stat[catalog._id] }}
      template(#question-right='{ question }')
        TaIcon(
          :type='isQuestionFilled(question) ? `CheckCircleFilled` : `InfoCircleFilled`',
          :class='isQuestionFilled(question) ? "!text-green-500" : "!text-gray-500"'
        )

    .question-sheet-form-items.flex-grow.w-0.h-full.flex.flex-col.overflow-auto
      .cover-image.flex-shrink-0.h-20.rounded-xl.flex.flex-col.justify-center.items-start.px-3.mb-3(
        v-if='record.coverImageUrl',
        :style='{ background: `url(${record.coverImageUrl}) center / cover no-repeat` }'
      )
        .text-base.font-medium.text-white.mb-1 {{ insertQuestionSheet(record.name) }}
        .text-xs.font-medium.text-white {{ record.model_payload?.副标题 ? insertQuestionSheet(record.model_payload?.副标题) : '调查问卷' }}
      .desc.text-base.mb-3.flex-shrink-0(v-if='descAry.length > 0')
        p(v-for='t in descAry') {{ t }}

      .total-score.text-base.font-medium.text-gray-900(v-if='showAnswer')
        .flex.items-center
          .prefix.w-2.h-8.bg-primary.mr-2
          | 总分：{{ totalScoreStat.total.toFixed(2) }}分

      .questions.flex-grow.h-0(v-if='!loading')
        ComAssessmentQuestionSheetFormQuestionItem.question-item.py-4(
          v-for='(question, index) in form?.fields',
          :ref='el => (questionRefMap[question._id] = el)',
          v-model:value='answerRecord.formData.payload[question._id]',
          :catalog='noCatalog',
          :question='question',
          :noAry='calcIndexAry(question, form.fields[index - 1])',
          :showAnswer='showAnswer',
          :insertQuestionSheet='insertQuestionSheet',
          :isRequired='isRequired',
          :getTemplate='getTemplate',
          :triggerDefaultValue='triggerDefaultValue',
          :disabled='disabled',
          @syncValue='onSyncValue'
        )

        template(v-for='catalog in form?.catalogs')
          .catalog-in-content.flex.items-center.justify-between
            .flex.items-center
              .prefix.w-2.h-8.bg-primary.mr-2
              .text-gray-900.font-medium.text-base {{ catalog.name }}
              .text-gray-900.text-base(v-if='needItemScore')
                | （{{ catalog.items.length }}题，
                span(v-if='catalogScoreAllTheSame(catalog)') 每题{{ catalog.items[0]?.score }}分，
                | 共{{ totalScoreStat.catalogs?.[catalog._id]?.maxScore }}分）
              .text-gray-900.text-base(v-else)
                | （{{ catalog.items.length }}题）
            .px-4.text-sm.text-gray-600(v-if='catalog.weight && catalog.weight !== 1')
              div(v-if='!showAnswer') 权重：{{ (catalog.weight * 100).toFixed(2) }}%
              div(v-else) 分数 {{ totalScoreStat.catalogs?.[catalog._id]?.score }} * {{ catalog.weight * 100 }}% = {{ (totalScoreStat.catalogs?.[catalog._id]?.score * catalog.weight).toFixed(2) }}分
          //- ComBpmInstanceCollapse(
          //-   v-model:collapsed='catalogId2Collapsed[catalog._id]'
          //-   :header='catalog.name',
          //-   :defaultCollapsed='false',
          //-   :closeClone='true'
          //- )
          ComAssessmentQuestionSheetFormQuestionItem.question-item.py-4(
            v-for='(question, index) in catalog.items',
            :ref='el => (questionRefMap[question._id] = el)',
            v-model:value='answerRecord.formData.payload[question._id]',
            :question='question',
            :catalog='catalog',
            :noAry='calcIndexAry(question, catalog.items[index - 1])',
            :showAnswer='showAnswer',
            :insertQuestionSheet='insertQuestionSheet',
            :isRequired='isRequired',
            :getTemplate='getTemplate',
            :triggerDefaultValue='triggerDefaultValue',
            :disabled='disabled',
            @syncValue='onSyncValue'
          )

  .actions.py-2.px-4.border-t.bg-white.border-gray-200.flex.justify-end.w-full.flex-shrink-0.space-x-2(
    v-if='!loading'
  )
    slot(name='footer-extra')
    template(v-if='needAnswer')
      a-button(v-if='!showAnswer', @click='() => (showAnswer = true)')
        | 查看答案
      a-button(v-else, @click='() => (showAnswer = false)')
        | 隐藏答案
    template(v-if='needTemporaryStorage')
      a-button(@click='onTemporaryStorage')
        | 暂存
    template(v-if='!disabled && needSave')
      a-button(type='primary', @click='onSaveWithConfirm')
        | 提交

//- .modal-container.h-screen.w-screen(v-if='visibleNotice')
//-   .mark.bg-gray-900.opacity-40.fixed.top-0.left-0.w-screen.h-screen.z-10
//-   .modal.p-2.absolute.z-20.bg-white(@click.stop='')
//-     .title.flex.justify-center.text-xl.font-bold.my-4 {{ store.record.value.hint_options?.title || '' }}
//-     TaRichEditor.text-xs.mx-2.font-medium(:disabled='true', :value='content')
//-     .line.bg-gray-200.w-full
//-     .flex.justify-center.py-2
//-       .rounded.bg-gray-200.text-white.py-1.px-3.text-base(
//-         @click.stop='onNoticeConfirm',
//-         :class='{ "bg-primary": countdown <= 0 }'
//-       )
//-         .flex
//-           span(v-if='countdown <= 0') 确定
//-           span(v-else) {{ countdown }}秒
</template>

<style lang="stylus">
.com-assessment-user-question-sheets-show
  // padding-bottom 20px
  height 100%
  .bg-primary
    background $primary-color
  .modal
    top 50%
    left 50%
    width 100%
    transform translate(-50%, -50%)
  .desc
    text-indent 1.75rem
    // font-family STKaitiSC-Regular, STKaitiSC
    white-space break-spaces
</style>
