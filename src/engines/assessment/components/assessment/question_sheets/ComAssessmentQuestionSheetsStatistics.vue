<script lang="ts">
import { ref, defineComponent, toRefs, PropType } from 'vue';
import { VObject } from '@/lib/vails';
import ComAssessmentQuestionSheetsStatisticsPieCard from './ComAssessmentQuestionSheetsStatisticsPieCard.vue';
import scrollIntoView from 'smooth-scroll-into-view-if-needed';
import {
  insertJsonata,
  jsonataGet,
} from '@/components/global/ta-component/ta-template-form-core/useJsonata';

const ComAssessmentQuestionSheetsStatistics = defineComponent({
  name: 'ComAssessmentQuestionSheetsStatistics',
  components: {
    ComAssessmentQuestionSheetsStatisticsPieCard,
  },
  props: {
    store: { type: Object, required: true },
    mode: { type: String as PropType<'chart' | 'table'>, default: 'chart' },
  },
  setup(props) {
    const activeCollapseKey = ref('');

    const statistics = ref<VObject>({});

    props.store
      .sendMemberAction({ id: props.store.record.value.id, action: 'statistics' })
      .then((res: any) => {
        statistics.value = res.data;
        onOpenAllCollapse();
      });

    const getStatData = (stat: VObject) => {
      return [...stat.stat, { name: '本题有效填写人次', count: stat.count }];
    };

    const onCollapseChange = (key: string) => {
      if (typeof key === 'string') {
        const iterator = document.evaluate(
          `//div[text()="${Number(key) + 1}"][contains(@class, "collapse-header-no")]`,
          document,
          null,
          XPathResult.ANY_TYPE,
          null,
        );
        const node = iterator.iterateNext() as Element;
        setTimeout(() => {
          if (node) {
            scrollIntoView(node, {
              scrollMode: 'if-needed',
              block: 'center',
              behavior: 'smooth',
            });
          }
        }, 200);
      }
    };

    const onOpenAllCollapse = () => {
      activeCollapseKey.value = statistics.value.map((stat: VObject) => stat.index);
    };

    const insertQuestionSheet = (name: string) => {
      return insertJsonata(name, (dataKey: string) =>
        jsonataGet({ questionSheet: props.store.record.value }, dataKey),
      );
    };

    return {
      ...toRefs(props),
      activeCollapseKey,
      statistics,
      getStatData,
      onCollapseChange,

      onOpenAllCollapse,

      insertQuestionSheet,
    };
  },
});
export default ComAssessmentQuestionSheetsStatistics;
</script>

<template lang="pug">
.com-assessment-manage-question-sheets-statistics.w-full.h-full
  .w-full.h-full.grid.grid-cols-2.gap-6.overflow-auto(v-show='mode === "chart"')
    ComAssessmentQuestionSheetsStatisticsPieCard(
      v-for='stat in statistics',
      :stat='stat',
      :insertQuestionSheet='insertQuestionSheet',
    )

  .w-full.h-full.flex.flex-col(v-show='mode === "table"')
    .flex.item-centers.justify-between.w-full.text-sm.font-medium.py-5.bg-gray-100.flex-shrink-0
      .title.flex.flex-shrink-0.flex-grow
        div(class='w-[40px]')
        .w-15 序号
        .w-0.flex-grow.flex-shrink-0 题目
      .actions.px-6
        TaTextButton(icon='DownOutlined', @click='onOpenAllCollapse') 全部展开
        TaTextButton(icon='UpOutlined', @click='() => activeCollapseKey = []') 全部收起
    a-collapse.collapse.h-0.flex-grow.overflow-auto(
      v-model:activeKey="activeCollapseKey",
      @change='onCollapseChange'
    )
      a-collapse-panel(
        v-for='stat in statistics',
        :key='stat.index',
      )
        template(#header)
          .flex.items-center.w-full
            .collapse-header-no.w-15 {{ stat.index + 1 }}
            .flex-grow.w-0 {{ insertQuestionSheet(stat.name) }}
        template(#default)
          a-table(:dataSource='getStatData(stat)', :pagination='{ hideOnSinglePage: true }')
            a-table-column(:autoHeight='true' title='选项', dataIndex='name', align='center')
            a-table-column(:autoHeight='true' title='小计', dataIndex='count', align='center')
            a-table-column(:autoHeight='true' title='比例', align='center', :width='400')
              template(#default='{ record, index }')
                .pr-6
                  a-progress(v-if='index < stat.stat.length', :percent='((record.count / stat.count || 0) * 100).toFixed(1)')
</template>

<style lang="stylus" scoped>
.com-assessment-manage-question-sheets-statistics
  .collapse-header-no
    scroll-margin 20px
  .collapse
    >>> .ant-collapse-content-box
      padding 0
    >>> .ant-collapse-header
      padding 1.25rem 16px
</style>
