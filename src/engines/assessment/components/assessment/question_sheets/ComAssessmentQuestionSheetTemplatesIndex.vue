<script lang="ts">
import { defineComponent, computed, toRefs } from 'vue';
import { VObject } from '@/lib/vails/model';
import { merge } from 'lodash';

const ComAssessmentQuestionSheetTemplatesIndex = defineComponent({
  name: 'ComAssessmentQuestionSheetTemplatesIndex',
  components: {},
  props: {
    store: { type: Object, required: true },
  },
  setup(props) {
    const config = computed(() => ({
      recordName: '问卷模板',
      store: props.store,
      // pagination: {
      //   perPage: 15,
      //   showPageSizeChanger: false,
      //   hideOnSinglePage: false,
      //   showSizeChanger: false,
      // }
      template: 'assessment_question_sheet#template',
      detail: {
        mode: 'route',
      },
      mode: 'table',
      // editApi:
      // showCount: true,
      // actions: [
      //  { key: 'create', enabled: true },
      //  { key: 'update', enabled: true },
      //  { key: 'delete', enabled: true },
      //  { key: 'import', enabled: true },
      //  { key: 'export', enabled: true },
      // ],
      // table: {
      //   scroll: { y: '70vh' },
      //   // columns: [{ dataIndex: 'id', title: 'ID' }],
      // },
      // selection: {
      //   showByDefault: false;
      //   showSwitch: false;
      // };
      // searcherSimpleOptions: [{key: 'title', label: '标题', type: 'string' }],
      formDataEncode: (payload: VObject) => {
        return merge(payload, { type: 'Assessment::QuestionSheetTemplate' });
      },
      params: {
        q: { type_eq: 'Assessment::QuestionSheetTemplate' },
      },
    }));

    return {
      ...toRefs(props),
      config,
    };
  },
});

export default ComAssessmentQuestionSheetTemplatesIndex;
</script>

<template lang="pug">
.com-assessment-manage-question-sheets-templates-index
  TaIndexView(:config='config')
    //- template(#card='{ record }')
    //- template(#table)
    //-    a-table-column(:autoHeight='true' title='名称' dataIndex='record')
    //- template(#detail='{ record, onClose }')
    //-     v-if='record.id'
    //-     :store='store'
    //-     :extendRoute='`/assessment/manage/question_sheets/${record.id}`'
    //-     :editable='editable'
    //-     @afterDelete='onClose'
    //-     @afterExtend='onClose'
    //-   )
</template>

<style lang="stylus" scoped>
.com-assessment-manage-question-sheets-templates-index
  height 100%
  width 100%
</style>
