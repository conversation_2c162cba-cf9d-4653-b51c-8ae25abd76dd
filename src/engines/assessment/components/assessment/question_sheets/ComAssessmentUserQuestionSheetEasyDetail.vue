<script lang="ts">
import { defineComponent, toRefs, watch } from 'vue';
import { AssessmentUserQuestionSheetsApi } from '@/engines/assessment/assessment-core/apis/assessment/user/question_sheets.api';
import ComAssessmentUserQuestionSheetsShow from './ComAssessmentUserQuestionSheetsShow.vue';
import { VStore } from '@/lib/vails';
import { AssessmentQuestionSheetModel } from '@/engines/assessment/assessment-core/models/assessment/question_sheets';
import { AssessmentUserAnswerSheetsApi } from '@/engines/assessment/assessment-core/apis/assessment/user/answer_sheets.api';
import { AssessmentAnswerSheetModel } from '@/engines/assessment/assessment-core/models/assessment/answer_sheets';

const ComAssessmentUserQuestionSheetEasyDetail = defineComponent({
  name: 'ComAssessmentUserQuestionSheetEasyDetail',
  components: {
    ComAssessmentUserQuestionSheetsShow,
  },
  props: {
    questionSheetId: { type: Number, default: 0 },
    answerSheetId: { type: Number, default: undefined },
    backUrl: { type: String, default: '/assessment/user/answer_sheets' },
    onSuccess: { type: Function, default: undefined },
    needAnswer: { type: Boolean, default: true },
    needSave: { type: Boolean, default: true },
    needTemporaryStorage: { type: Boolean, default: false },
    disabled: { type: Boolean, default: false },
    autoSyncValue: { type: Boolean, default: false },
  },
  setup(props) {
    const questionSheetStore = new VStore(
      new AssessmentUserQuestionSheetsApi(),
      AssessmentQuestionSheetModel,
    );

    const answerSheetStore = new VStore(
      new AssessmentUserAnswerSheetsApi(),
      AssessmentAnswerSheetModel,
    );

    watch(
      () => props.questionSheetId,
      questionSheetId => {
        if (questionSheetId) questionSheetStore.find(questionSheetId);
      },
      { immediate: true },
    );

    watch(
      () => props.answerSheetId,
      answerSheetId => {
        if (answerSheetId)
          answerSheetStore.find(answerSheetId).then(() => {
            const questionId = answerSheetStore.record.value.question_sheet_id;
            if (questionId && questionId != props.questionSheetId)
              questionSheetStore.find(answerSheetStore.record.value.question_sheet_id);
          });
      },
      { immediate: true },
    );

    return {
      ...toRefs(props),
      questionSheetStore,
      answerSheetStore,
      answerSheet: answerSheetStore.record,
      questionSheet: questionSheetStore.record,
    };
  },
});
export default ComAssessmentUserQuestionSheetEasyDetail;
</script>

<template lang="pug">
.com-assessment-user-question-sheet-easy-detail.h-full.w-full
  ComAssessmentUserQuestionSheetsShow.h-full.w-full(
    v-if='questionSheet?.id',
    v-bind='$props',
    :store='questionSheetStore',
    :answerSheetRecord='answerSheet',
    :backUrl='backUrl',
    :onSuccess='onSuccess',
  )
</template>

<style lang="stylus" scoped></style>
