<script lang="ts">
import { ref, defineComponent, toRefs, computed, PropType } from 'vue';
import { TaTemplateFormItem } from '../../../../../components/global/ta-component/ta-template-form-core/types';
import { isAnswerEqual } from '../../../assessment-core/useAssessmentQuestionSheet';
import {
  AssessmentQuestionSheetItemInterface,
  AssessmentQuestionSheetCatalogInterface,
} from '../../../assessment-core/useAssessmentQuestionSheet';

const ComAssessmentQuestionSheetFormQuestionItem = defineComponent({
  name: 'ComAssessmentQuestionSheetFormQuestionItem',
  components: {},
  props: {
    question: { type: Object as PropType<AssessmentQuestionSheetItemInterface>, required: true },
    catalog: { type: Object as PropType<AssessmentQuestionSheetCatalogInterface>, required: true },
    noAry: { type: Array, required: true },
    value: { type: Object, required: true },
    disabled: { type: <PERSON>olean, default: false },
    showAnswer: { type: Boolean, default: false },
    insertQuestionSheet: { type: Function, default: (val: string) => val },
    isRequired: { type: Function, default: () => true },
    getTemplate: { type: Function, default: () => '' },
    triggerDefaultValue: { type: Function, default: () => true },
  },
  setup(props, { emit }) {
    const localValue = computed({
      get() {
        return props.value;
      },
      set(val) {
        emit('update:value', val);
      },
    });

    const form = ref<any>(null);

    const isAnswerCorrect = computed(() => isAnswerEqual(localValue.value, props.question));
    const score = computed(() => (isAnswerCorrect.value ? props.question.score || 0 : 0));

    const onSyncValue = (item: TaTemplateFormItem, name: string[], value: any) => {
      emit('syncValue', item, name, value, props.question._id);
    };

    return {
      ...toRefs(props),
      localValue,
      isAnswerEqual,
      form,
      isAnswerCorrect,
      score,
      onSyncValue,
    };
  },
});
export default ComAssessmentQuestionSheetFormQuestionItem;
</script>

<template lang="pug">
.com-assessment-question-sheet-form-question
  .flex(v-if='question.materials?.length > 0')
    .no.mt-2.mr-2 {{ [...noAry].splice(0, noAry.length - 1).join('.') }}.
    TaContentField.materials(:value='question.materials', :disabled='true')
  .text-base.font-regular.text-gray-700.transition-all.mb-2.flex.justify-between.items-center
    .name
      span.text-red-600(v-if='isRequired(question)') *&nbsp
      span.no {{ noAry.join('.') }}.&nbsp
      span.name.whitespace-pre-wrap {{ insertQuestionSheet(question.name) }}
      span(v-if='question.score') &nbsp({{ question.score }}分)

    slot(name='name-right', :question='question', :catalog='catalog', :index='index')

  component.w-full.question-form(
    :is='disabled ? `TaTemplateFormViewer` : `TaTemplateForm`',
    v-if='triggerDefaultValue(question._id)',
    ref='form',
    v-model:modelValue='localValue',
    :template='getTemplate(question)',
    @syncValue='onSyncValue'
  )

  .answer.mt-4.rounded.px-4.py-2.border.border-green-500.text-green-500.bg-green-100(
    v-if='question.enable_answer && showAnswer',
    :class='{ "wrong-answer": !isAnswerCorrect }'
  )
    .line.flex.items-center.text-sm
      TaIcon(:type='isAnswerCorrect ? `CheckCircleFilled` : `CloseCircleFilled`')
      .px-2.text-sm.font-medium {{ isAnswerCorrect ? `回答正确` : `回答不一致，参考答案：` }}
    TaTemplateFormViewer.w-full.answer-viewer.mt-2(
      v-if='!isAnswerCorrect',
      v-model:modelValue='question.answer',
      :template='getTemplate(question)'
    )
</template>

<style lang="stylus" scoped>
.question-form
  >>> .ant-radio-wrapper
    margin 8px
.wrong-answer
  @apply border-red-500 text-red-500 bg-red-50
</style>
