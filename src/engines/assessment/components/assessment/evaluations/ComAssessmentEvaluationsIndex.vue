<script lang="ts">
import { defineComponent, computed, ref, toRefs, nextTick } from 'vue';
import { TaIndexViewTabInterface } from '@/components/global/ta-component/TaIndexView/types';
import { VObject } from '@/lib/vails/model';
import ComAssessmentEvaluationsShow from './ComAssessmentEvaluationsShow.vue';
import ComAssessmentEvaluationDrawer from './ComAssessmentEvaluationDrawer.vue';

const ComAssessmentEvaluationsIndex = defineComponent({
  name: 'ComAssessmentEvaluationsIndex',
  components: {
    ComAssessmentEvaluationsShow,
    ComAssessmentEvaluationDrawer,
  },
  props: {
    store: { type: Object, required: true },
  },
  setup(props) {
    const config = computed(() => ({
      recordName: '评分列表',
      store: props.store,
      params: {
        group_keys: ['state'],
      },
      pagination: {
        // perPage: 15,
        // showPageSizeChanger: false,
        hideOnSinglePage: true,
        // showSizeChanger: false,
      },
      template: 'assessment_evaluation',
      // detail: {
      //   mode: 'auto',
      //   mode: 'drawer',
      //   width: '1100px',
      // },
      mode: 'table',
      // editApi:
      showCount: true,
      actions: [
        { key: 'create', enabled: false },
        { key: 'update', enabled: false },
        { key: 'delete', enabled: false },
        { key: 'import', enabled: false },
        { key: 'export', enabled: false },
      ],
      table: {
        scroll: { y: 'auto' },
        // columns: [{ dataIndex: 'id', title: 'ID' }],
      },
      list: {
        scroll: { y: 'auto' },
      },
      // selection: {
      //   showByDefault: false;
      //   showSwitch: false;
      // };
      // searcherSimpleOptions: [{key: 'title', label: '标题', type: 'string' }],
    }));

    const statistics = ref({
      todo: 0,
      done: 0,
    });

    const tabs = computed<TaIndexViewTabInterface[]>(() => [
      {
        key: 'todo',
        label: '待评',
        num: statistics.value.todo,
        query: { state_eq: 'todo' },
      },
      {
        key: 'done',
        label: '已评',
        num: statistics.value.done,
        query: { state_eq: 'done' },
      },
    ]);

    const onIndex = (data: VObject) => {
      statistics.value = data.statistics;
    };

    const activeEvaluation = ref<VObject | null>(null);
    const visibleShow = ref(false);
    const disabledShow = ref(false);

    const onShow = async (record: VObject) => {
      activeEvaluation.value = record;
      await activeEvaluation.value?.fetch();
      disabledShow.value = true;
      nextTick(() => {
        visibleShow.value = true;
      });
    };

    const onEdit = async (record: VObject) => {
      activeEvaluation.value = record;
      await activeEvaluation.value?.fetch();
      disabledShow.value = false;
      nextTick(() => {
        visibleShow.value = true;
      });
    };

    return {
      ...toRefs(props),
      config,
      tabs,
      onIndex,
      onShow,
      onEdit,
      disabledShow,
      visibleShow,
      activeEvaluation,
    };
  },
});

export default ComAssessmentEvaluationsIndex;
</script>

<template lang="pug">
.com-assessment-user-evaluations-index
  TaIndexView(:config='config' :tabs='tabs' @onIndex='onIndex')
    //- template(#table='{ actions }')
    //-   a-table-column(:autoHeight='true' title='名称', dataIndex='name')
    //-   a-table-column(:autoHeight='true' title='得分', dataIndex='score')
    //-   a-table-column(:autoHeight='true' title='状态', dataIndex='getState')
    //-   a-table-column(:autoHeight='true' title='操作')
    //-     template(#default='{ record }')
    //-       .flex.operates
    //-         a-button(v-if='record.state === "done"', type='primary', @click.stop='onShow(record)') 查看
    //-         a-button(v-else, type='primary', @click.stop='onEdit(record)') 评分

  ComAssessmentEvaluationDrawer(
    v-model:visible='visibleShow',
    v-model:record='activeEvaluation',
    :disabled='disabledShow',
  )
    //- template(#card='{ record }')
    //- template(#table)
    //-    a-table-column(:autoHeight='true' title='名称' dataIndex='record')
    //- template(#detail='{ record, onClose }')
    //-   ComAssessmentEvaluationsShow(
    //-     v-if='record.id'
    //-     :store='store'
    //-     :extendRoute='`/assessment/user/evaluations/${record.id}`'
    //-     :editable='editable'
    //-     @afterDelete='onClose'
    //-     @afterExtend='onClose'
    //-   )

</template>

<style lang="stylus" scoped>
.com-assessment-user-evaluations-index
  height 100%
  width 100%
</style>
