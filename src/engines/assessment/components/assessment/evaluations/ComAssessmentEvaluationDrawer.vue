<script lang="ts">
import { ref, defineComponent, toRefs, computed, nextTick, watch } from 'vue';
import ComAssessmentScoreForm from '@/engines/assessment/components/assessment/scores/ComAssessmentScoreForm.vue';
import { message } from 'ant-design-vue';

const ComAssessmentEvaluationDrawer = defineComponent({
  name: 'ComAssessmentEvaluationDrawer',
  components: {
    ComAssessmentScoreForm,
  },
  props: {
    visible: { type: Boolean, default: false },
    record: { type: Object, default: () => ({}) },
    disabled: { type: Boolean, default: false },
    onOk: { type: Function, default: undefined },
  },
  setup(props, { emit }) {
    const localRecord = computed({
      get: () => props.record,
      set: val => emit('update:record', val),
    });
    const localVisible = computed({
      get: () => props.visible,
      set: val => emit('update:visible', val),
    });

    const form = ref<any>(null);
    const moreForm = ref<any>(null);

    const onSubmit = async (skipValidate = false) => {
      if (
        skipValidate ||
        (form.value?.validate() && (moreForm.value ? await moreForm.value.validate() : true))
      ) {
        if (props.onOk) {
          props.onOk().then(() => {
            localVisible.value = false;
          });
        } else {
          form.value?.onSubmit();
        }
      } else {
        message.error('请完成所有评分项');
      }
    };

    watch(
      localVisible,
      () => {
        if (localVisible.value) {
          localRecord.value.payload = localRecord.value.payload || {};
          console.log(localRecord.value, 'localRecord.value');

          nextTick(() => {
            form.value?.onStart();
          });
        }
      },
      { immediate: true },
    );

    return {
      ...toRefs(props),
      localVisible,
      // loading,
      form,
      moreForm,
      onSubmit,
      localRecord,
    };
  },
});
export default ComAssessmentEvaluationDrawer;
</script>

<template lang="pug">
a-drawer.com-assessment-evaluation-drawer(
  v-model:visible='localVisible'
  width='60vw'
)
  .content.flex.flex-col.h-full
    slot
    .spinning.h-full
      .flex.flex-col.w-full.h-full
        .flex-grow.h-0.overflow-auto
          ComAssessmentScoreForm(
            ref='form',
            :visibleEdit='!disabled',
            :editable='!disabled',
            :needActions='false',
            v-model:score='localRecord',
            @afterSave='() => localVisible = false'
          )
          TaTemplateForm(
            ref='moreForm',
            v-if='record.score_template?.score_form && !disabled'
            v-model:modelValue='localRecord.formData.payload',
            :template='record.score_template.score_form'
          )
          TaTemplateFormViewer(
            v-if='record.score_template?.score_form && disabled'
            :modelValue='localRecord.payload',
            :template='record.score_template.score_form'
          )

        .flex-shrink-0.py-2.px-4.flex.justify-end.space-x-2(v-if='!disabled')
          slot(name='footer', :actions='{ onSubmit, onCancel: () => localVisible = false }')
            a-button(
              type='default'
              @click='() => localVisible = false'
            ) 取消
            a-button(
              type='primary'
              @click='onSubmit'
            ) 提交

</template>

<style lang="stylus" scoped>
.spinning
  >>> .ant-spin-nested-loading
    height: 100%
  >>> .ant-spin-container
    height: 100%
</style>
