<script lang="ts">
import { defineComponent, toRefs } from 'vue';
import dayjs from 'dayjs';

const ComAssessmentActivityCard = defineComponent({
  name: 'ComAssessmentActivityCard',
  components: {},
  props: {
    activity: { type: Object, required: true },
  },
  setup(props) {
    return {
      ...toRefs(props),
      dayjs,
    };
  },
});
export default ComAssessmentActivityCard;
</script>

<template lang="pug">
.com-assessment-activity-card.clickable
  .card
    .card-top
      .name {{ activity.name }}
      slot(name='right-top', :activity='activity')
    .card-middle
      .cell
        .key 考核时间
        .value {{ activity.start_on ? dayjs(activity.start_on).format('YYYY-MM-DD') : '空' }}
          |&nbsp;~ {{ activity.end_on ? dayjs(activity.end_on).format('YYYY-MM-DD') : '空' }}
      .cell
        .key 被考核
        .value {{ activity.entry_count || 0 }} 人
      .cell
        .key 考核内容
        .value {{ activity.content }}
      .cell(v-if='activity.catalog_enabled')
        .key 考核分类
        .value(v-if='typeof activity.catalogs === "object"') {{ activity.catalogs.map(i => i.name).join('、') }}
      .cell(v-if='activity.group_enabled')
        .key 考核分组
        .value {{ activity.group_count }}

      //- .steps
        a-steps(:current="currentIndex" size="small", )
          a-step(
            v-for="(step, key) in activity.stages.stages"
            :key="key"
            :title="step.name"
            style="width: 240px"
          )
            p(slot="description" style="width: 240px")
              span(v-if="step.start_at && step.end_at") {{ step.start_at }} 至 {{ step.end_at }}
              span(v-else) 无
    .card-bottom(v-if='$slots.bottom')
      slot(name='bottom', :activity='activity')
</template>

<style lang="stylus" scoped>
.com-assessment-activity-card
  .card
    margin-bottom 12px
    padding 16px 16px 0px
    width 100%
    border 1px solid rgba(0, 0, 0, 0.08)
    border-radius 2px
    .card-top
      display flex
      justify-content space-between
      align-items center
      width 100%
      .name
        color #383838
        font-weight 500
        font-size 14px
        line-height 20px
    .card-middle
      padding 6px 0px 16px
      width 100%
      .cell
        display flex
        padding 2px 0px
        width 100%
        font-size 14px
        line-height 22px
        .key
          min-width 90px
          color #a6a6a6
        .value
          color #808080
      .steps
        margin-top 10px
        width 100%
    .card-bottom
      padding 8px 0px
      border-top 1px #E8E8E8 solid
    &:hover
      box-shadow 0 2px 8px 2px rgba(239, 243, 253, 1)
      .name
        color $primary-color
</style>
