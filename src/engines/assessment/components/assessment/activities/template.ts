export const assessmentActivityManageTemplate = {
  type: 'layout',
  model: {},
  fields: [
    {
      key: 'input_81544787492',
      name: '考核活动名称',
      type: 'input',
      model: { attr_type: 'string' },
      rules: [
        {
          type: 'string',
          message: '请填写正确的考核活动名称',
          required: true,
          rule_type: 'required',
        },
      ],
      fields: [],
      options: { span: 24 },
      model_key: 'name',
      model_key_prefix: '',
    },
    {
      key: 'date_range_1645255530968_0',
      name: '日期范围',
      type: 'date_range',
      model: { attr_type: 'array' },
      rules: [],
      fields: [],
      options: { span: 24, props: { model_key: { type: 'array', length: 2 } } },
      model_key: ['start_on', 'end_on'],
      conditions: [],
      model_key_prefix: '',
    },
    {
      key: 'textarea_86933925271',
      name: '考核活动内容',
      type: 'textarea',
      model: { attr_type: 'text' },
      fields: [],
      options: { span: 24 },
      model_key: 'content',
      model_key_prefix: '',
    },
    {
      key: 'radio_1645256867566_3',
      name: '状态',
      type: 'radio',
      model: { attr_type: 'string' },
      rules: [
        { type: 'string', message: '请填写正确的状态', required: true, rule_type: 'required' },
      ],
      fields: [],
      options: {
        span: 24,
        select: [
          { label: '已发布', value: 'published' },
          { label: '草稿箱', value: 'draft' },
          { label: '已完成', value: 'completed' },
        ],
        multiple: false,
        defaultValue: 'published',
      },
      model_key: 'state',
      conditions: [],
      model_key_prefix: '',
    },
    {
      key: 'key_layout_1645256916818_4',
      name: 'attachments',
      type: 'key_layout',
      fields: [
        {
          key: 'file_1645256919950_5',
          name: '附件',
          type: 'file',
          model: { attr_type: 'array' },
          rules: [],
          fields: [],
          options: { span: 24, multiple: true },
          model_key: 'files',
          conditions: [],
          model_key_prefix: 'attachments',
        },
      ],
      options: { span: 24 },
      model_key: 'attachments',
      conditions: [],
      model_key_prefix: '',
    },
    {
      name: '评分表',
      type: 'api_single',
      rules: [],
      model: {
        attr_type: 'number',
      },
      options: {
        span: 24,
        multiple: false,
        table_items: [
          {
            name: '名称',
            data_index: 'name',
            search: true,
            type: 'string',
          },
        ],
        display_configurable_form: {},
        import_export_headers: [
          {
            _id: '1645335812463_4',
          },
        ],
        path: '/assessment/manage/score_templates',
        display: 'tag',
      },
      key: 'api_single_1645335812335_1',
      model_key: 'score_template_id',
      fields: [],
      conditions: [],
      model_key_prefix: '',
    },
    {
      name: '开启分类',
      type: 'switch',
      rules: [],
      model: { attr_type: 'boolean' },
      options: { span: 24 },
      key: 'switch_1645325368364_1',
      model_key: 'catalog_enabled',
      fields: [],
      conditions: [],
      model_key_prefix: '',
    },
    {
      name: '开启分组',
      type: 'switch',
      rules: [],
      model: { attr_type: 'boolean' },
      options: { span: 24 },
      key: 'switch_1645325368747_2',
      model_key: 'group_enabled',
      fields: [],
      conditions: [],
      model_key_prefix: '',
    },
    {
      name: '自评表',
      type: 'form_designer',
      rules: [],
      model: {
        attr_type: 'object',
      },
      options: {
        span: 24,
      },
      key: 'form_designer_1672298296162_1',
      model_key: 'entry_form',
      fields: [],
      conditions: [],
      model_key_prefix: '',
    },
    {
      name: '阶段设置',
      type: 'list',
      fields: [
        {
          name: '阶段名称',
          type: 'input',
          rules: [],
          model: {
            attr_type: 'string',
          },
          options: {
            span: 24,
          },
          key: 'input_1672624484678_2',
          model_key: 'name',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
        {
          name: '开始时间',
          type: 'datetime',
          rules: [],
          model: {
            attr_type: 'date',
          },
          options: {
            span: 12,
          },
          key: 'datetime_1672624553876_4',
          model_key: 'effective_at',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
        {
          name: '结束时间',
          type: 'datetime',
          rules: [],
          model: {
            attr_type: 'date',
          },
          options: {
            span: 12,
          },
          key: 'datetime_1672624555746_5',
          model_key: 'invalid_at',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
        {
          name: '开启',
          type: 'switch',
          rules: [],
          model: {
            attr_type: 'boolean',
          },
          options: {
            span: 4,
            defaultValue: true,
          },
          key: 'switch_1672624531510_3',
          model_key: 'enable',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
        {
          name: '类型',
          type: 'radio',
          rules: [
            {
              type: 'string',
              message: '请填写正确的考核阶段类型',
              required: true,
              rule_type: 'required',
            },
          ],
          model: {
            attr_type: 'string',
          },
          options: {
            select: [
              {
                label: '自评阶段',
                value: 'submit',
              },
              {
                label: '评价阶段',
                value: 'evaluate',
              },
              {
                label: '统计阶段',
                value: 'statistic',
              },
            ],
            multiple: false,
            span: 20,
            table_items: [],
            display_configurable_form: {},
            import_export_headers: [
              {
                _id: '1672624650000_0',
              },
            ],
          },
          key: 'radio_1672624641577_7',
          model_key: 'type',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
      ],
      rules: [],
      model: {
        attr_type: 'array',
      },
      options: {
        span: 24,
        disabled_actions: {},
        display: 'normal',
        edit_directly: true,
        defaultValue: [
          { _id: 1, enable: true, type: 'submit' },
          { _id: 2, enable: true, type: 'evaluate' },
          { _id: 3, enable: true, type: 'statistic' },
        ],
      },
      key: 'list_1672624482130_1',
      model_key: 'stage_infos',
      conditions: [],
      model_key_prefix: '',
    },
  ],
  actions: [
    { key: 'create', enabled: true },
    { key: 'update', enabled: true },
    { key: 'delete', enabled: true },
    { key: 'import', enabled: false },
    { key: 'export', enabled: false },
  ],
  options: {
    label: {},
    theme: { card: {}, form: {}, background: {} },
    create_text: '提交',
    update_text: '提交',
  },
  model_key: 'layout_25352172436',
  state: 'published',
};
