<script lang="ts">
import { defineComponent, toRefs, onMounted, ref, computed } from 'vue';
import dayjs from 'dayjs';
import { VObject } from '@/lib/vails/model';
import { AssessmentUserScoredScoresApi } from '@/engines/assessment/assessment-core/apis/assessment/user/scored/scores.api';
import { AssessmentUserEntriedEntriesApi } from '@/engines/assessment/assessment-core/apis/assessment/user/entried/entries.api';
import ComAssessmentCard from '@/engines/assessment/components/assessment/activities/ComAssessmentCard.vue';
import { useRoute } from 'vue-router';
import ComEchartsPie from './ComEchartsPie.vue';

const ComAssessmentActivityStatistics = defineComponent({
  name: 'ComAssessmentActivityStatistics',
  components: { ComEchartsPie, ComAssessmentCard },
  props: {
    record: { type: Object, required: true },
  },
  setup(props) {
    const colorPie = [
      'rgb(112, 42, 67, 0.4)',
      'rgb(29, 100, 151, 0.3)',
      'rgb(129, 121, 51, 0.2)',
      'rgb(31, 161, 255,0.25)',
    ];
    const pieSeriesData = ref([
      { value: 1, name: '例1' },
      { value: 2, name: '例2' },
      { value: 3, name: '例3' },
    ]);
    const rank_scores = ref([{ rank: 0, average_score: 0, score: 0 }]);
    const radius = ['30%', '60%'];
    const lineLength = [10, 40];
    const data = ref({});
    const route = useRoute();
    const activityId = String(route.params.activityId);
    const get_statistics = () => {
      new AssessmentUserScoredScoresApi()
        .sendCollectionAction('statistics', { params: { q: { activity_id_eq: activityId } } })
        .then((res: any) => {
          data.value = res.data;
          let temp_arr: { value: any; name: any }[] = [];
          const scores = res.data.score_range_group;
          const total = scores.reduce((sum: number, e: VObject) => (sum += Number(e.count)), 0);
          scores.map((item: VObject) => {
            let name = (item.label || '空').concat(
              '(',
              Math.round((item.count / total) * 100),
              '%)',
            );
            temp_arr.push({ value: item.count, name: name });
          });
          pieSeriesData.value = temp_arr;
        });
    };
    const entries_index = () => {
      new AssessmentUserEntriedEntriesApi({ params: { q: { activity_id_eq: activityId } } })
        .index()
        .then((res: any) => {
          // 结束后，查找个人详情信息
          if (state_show.value) {
            rank_scores.value = [];
            res.data.records.map((item: any) => {
              entries_show(item.id);
            });
          }
        });
    };
    const entries_show = (id: number) => {
      const activityId = String(route.params.activityId);
      new AssessmentUserEntriedEntriesApi({ params: { q: { activity_id_eq: activityId } } })
        .find(id)
        .then((res: any) => {
          rank_scores.value.push({
            rank: res.data.rank,
            score: res.data.score,
            average_score: res.data.average_score,
          });
        });
    };
    const entried_show = computed(() => {
      return props.record.activity_relate_state.entried;
    });
    const scored_show = computed(() => {
      return props.record.activity_relate_state.scored;
    });
    const state_show = computed(() => {
      if (props.record.state === 'completed') {
        return true;
      }
      return false;
    });

    onMounted(() => {
      // 有打分的权限后，显示得分统计信息
      if (scored_show.value) {
        get_statistics();
      }
      // 参与了考核且考核结束后，获取分数和名次
      if (entried_show.value && state_show) {
        entries_index();
      }
    });

    return {
      ...toRefs(props),
      dayjs,
      colorPie,
      pieSeriesData,
      radius,
      lineLength,
      rank_scores,
      data,
      entried_show,
      scored_show,
      state_show,
    };
  },
});
export default ComAssessmentActivityStatistics;
</script>

<template lang="pug">
.com-assessment-activity-statistics
  .card-1(v-if='scored_show')
    .card-top
      .name 考核统计
    .card-middle
      .box
        .title-box
          .title 打分统计
        .content-box
          .score-box
            .score-box-title 已评
            .score-box-content
              .score {{ data?.state_group?.["done"] || 0 }}
              .unit 位
          .score-box
            .score-box-title 未评
            .score-box-content
              .score {{ data?.state_group?.["todo"] || 0 }}
              .unit 位
      .box
        .title-box
          .title 分数分布
        ComEchartsPie.echart-pie(
          keyId='overviewPie',
          :colors='colorPie',
          :seriesData='pieSeriesData',
          :radius='radius',
          width='400px',
          height='300px',
          :lineLength='lineLength'
        )
  .card-2(v-if='entried_show && state_show')
    .card-title 我的成绩
    .card-box(v-for='rank_score in rank_scores')
      ComAssessmentCard(:num='rank_score.score')
      ComAssessmentCard(
        icon='https://stiei-obs2.obs.myhuaweicloud.com/party-build-web/rank.png',
        title='平均分',
        :num='rank_score.average_score',
        background_picture='https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/party-build-web/%E5%8D%A1%E7%89%87-%E7%BB%BF%E8%89%B2.png'
      )
  .card-none(v-else-if='!entried_show')
    .none
  .card-else(v-else)
    img.img(src='@/engines/assessment/assets/icons/no_result.png')
    .tips 考核暂未结束，请在考核结束后查看考核成绩
</template>

<style lang="stylus" scoped>
.com-assessment-activity-statistics
  .card-1
    margin-bottom 12px
    padding 16px 16px 0px
    width 100%
    border-radius 2px
    .card-top
      display flex
      justify-content space-between
      align-items center
      width 100%
      .name
        color #383838
        font-weight 500
        font-size 14px
        line-height 20px
    .card-middle
      padding 6px 0px 16px
      width 100%
      display flex
      align-items center
      justify-content space-between
      .box
        border 1px solid #E8E8E8
        width 49%
        .title-box
          height 48px
          display flex
          align-items center
          border-bottom 1px solid #E8E8E8
          .title
            margin-left 20px
            font-size 14px
            font-family PingFangSC-Regular, PingFang SC
            font-weight 400
            color #383838
            line-height 20px
        .content-box
          width 100%
          height 300px
          display flex
          align-items center
          justify-content space-around
          .score-box
            width 96px
            height 132px
            .score-box-title
              font-size 24px
              font-family PingFangSC-Medium, PingFang SC
              font-weight 500
              color #A6A6A6
              line-height 24px
            .score-box-content
              display flex
              justify-content space-between
              .score
                font-size 88px
                font-family DINCond-Medium, DINCond
                font-weight 500
                color #383838
                line-height 88px
              .unit
                align-self flex-end
        .echart-pie
          display flex
          justify-content center
      .cell
        display flex
        padding 2px 0px
        width 100%
        font-size 14px
        line-height 22px
        .key
          min-width 90px
          color #a6a6a6
        .value
          color #808080
      .steps
        margin-top 10px
        width 100%
    &:hover
      box-shadow 0 2px 8px 2px rgba(239, 243, 253, 1)
      .name
        color $primary-color
  .card-2
    margin-top 12px
    margin-bottom 12px
    padding 0 16px 0px
    width 100%
    .card-title
      padding 0 0 16px
      border-bottom 1px solid #E8E8E8
      margin-bottom 16px
    .card-box
      display grid
      grid-template-columns 1fr 1fr
      gap 20px
    &:hover
      box-shadow 0 2px 8px 2px rgba(239, 243, 253, 1)
      .card-title
        color $primary-color
  .card-else
    display block
    text-align center
    margin-top 32px
    margin-bottom 32px
    .img
      width 200px
      height 200px
      margin 0 auto
    .tips
      font-size 14px
      font-family PingFangSC-Regular, PingFang SC
      font-weight 400
      color rgba(38, 38, 38, 0.85)
</style>
