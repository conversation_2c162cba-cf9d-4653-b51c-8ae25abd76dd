<script lang="ts">
import { defineComponent, toRefs, PropType } from 'vue';
import { TaBreadcrumbInterface } from '@/components/global/ta-component/TaBreadcrumb.vue';
import ComAssessmentActivitiesConfiguration from './ComAssessmentActivitiesConfiguration.vue';
import ComAssessmentEntriesStatsIndex from '../entries/ComAssessmentEntriesStatsIndex.vue';
import { VStore } from '@/lib/vails';
import { AssessmentManageEntriesApi } from '@/engines/assessment/apis/assessment/manage/entries.api';

const ComAssessmentActivitiesShow = defineComponent({
  name: 'ComAssessmentActivitiesShow',
  components: {
    ComAssessmentActivitiesConfiguration,
    ComAssessmentEntriesStatsIndex,
  },
  props: {
    store: { type: Object, required: true },
    extendRoute: { type: String, default: '' },
    editable: { type: Boolean, default: true },
    breadcrumbs: { type: Array as PropType<TaBreadcrumbInterface[]>, default: () => [] },
  },
  emits: ['afterDelete', 'afterExtend'],
  setup(props, { emit }) {
    const tabs = [
      {
        key: 'configuration',
        label: '考核配置',
      },
      {
        key: 'entries',
        label: '测评数据',
      },
    ];

    const entriesStore = new VStore(
      new AssessmentManageEntriesApi({
        parents: [{ type: 'activities', id: props.store.record.value.id }],
        params: { q: { s: ['score desc'] } },
      }),
    );

    const afterDelete = () => {
      emit('afterDelete');
    };

    const afterExtend = () => {
      emit('afterExtend');
    };

    return {
      ...toRefs(props),
      tabs,
      record: props.store.record,
      afterDelete,
      afterExtend,
      entriesStore,
    };
  },
});
export default ComAssessmentActivitiesShow;
</script>

<template lang="pug">
.com-assessment-manage-activities-show
  TaShowLayout.show-layout(
    :tabs='tabs',
    :title='record.name',
    :store='store',
    :extendRoute='extendRoute',
    :editable='editable',
    :breadcrumbs='breadcrumbs',
    :showInfo='false',
    @afterDelete='afterDelete',
    @afterExtend='afterExtend'
  )
    template(#configuration_tab)
      ComAssessmentActivitiesConfiguration(:activity='record')
    template(#entries_tab)
      ComAssessmentEntriesStatsIndex(:activity='record', :store='entriesStore', :activityId='record.id')
</template>

<style lang="stylus" scoped>
.com-assessment-manage-activities-show
  height 100%
  .show-layout
    >>> .content
      padding 0
      display flex
      flex-direction column
</style>
