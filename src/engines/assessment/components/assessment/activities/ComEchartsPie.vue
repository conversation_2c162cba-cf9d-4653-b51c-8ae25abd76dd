<script lang="ts">
import { ref, defineComponent, toRefs, watch, onMounted, computed } from 'vue';
import echarts from 'echarts';

const ComEchartsPie = defineComponent({
  name: 'ComEchartsPie',
  components: {},
  props: {
    keyId: { type: String, required: true },
    seriesData: { type: Array, required: true },
    width: { type: String, default: '500px' },
    height: { type: String, default: '200px' },
    radius: { type: Array, default: () => ['40%', '70%'] }, //圆环大小
    colors: { type: Array, default: () => ['#EEF0F0', '#FFC82C', '#78A4FA', '#FF4949', '#FFC82C'] },
    lineLength: { type: Array, default: () => [10, 10] },
    legend_line_position: { type: String, default: 'center' },
    legend_column_position: { type: String, default: 'bottom' },
  },
  setup(props) {
    const pie = ref<any>(null);
    const option = computed(() => ({
      tooltip: {
        trigger: 'item',
      },
      color: props.colors,
      legend: {
        show: true,
        orient: 'horizontal',
        icon: 'circle',
        left: props.legend_line_position,
        top: props.legend_column_position,
      },
      series: [
        {
          type: 'pie',
          radius: props.radius,
          avoidLabelOverlap: true,
          label: {
            show: true,
            fontSize: 12,
          },
          emphasis: {
            label: {
              show: true,
              fontSize: '20',
              fontWeight: 'bold',
            },
          },
          labelLine: {
            show: true,
            // smooth: 0.1,
            length: props.lineLength[0],
            length2: props.lineLength[1],
            maxSurfaceAngle: 30,
            // normal: {
            //   position: 'inner',
            // },
          },

          data: props.seriesData,
        },
      ],
    }));
    const createChart = () => {
      let domId = document.getElementById(`pie${props.keyId}`) as HTMLDivElement;
      pie.value = echarts.init(domId);
      pie.value.setOption(option.value);
    };
    watch(
      () => {
        props.seriesData;
      },
      () => {
        pie.value.setOption(option.value);
      },
      { deep: true },
    );
    onMounted(() => {
      createChart();
    });
    return {
      ...toRefs(props),
    };
  },
});
export default ComEchartsPie;
</script>

<template lang="pug">
.com-echarts-pie
  .pie(
    v-if='keyId',
    :id='`pie${keyId}`',
    :style='`width: ${width};height: ${height};left: ${shiftLeft}`'
  )
</template>

<style lang="stylus" scoped></style>
