<script lang="ts">
import { defineComponent, computed, toRefs, ref } from 'vue';
import { TaIndexViewTabInterface } from '@/components/global/ta-component/TaIndexView/types';
import { VObject } from '@/lib/vails/model';
import ComAssessmentActivityCard from './ComAssessmentActivityCard.vue';

const ComAssessmentActivitiesUserIndex = defineComponent({
  name: 'ComAssessmentActivitiesUserIndex',
  components: {
    ComAssessmentActivityCard,
  },
  props: {
    store: { type: Object, required: true },
  },
  setup(props) {
    const config = computed(() => ({
      recordName: '考核活动',
      store: props.store,
      // pagination: {
      //   perPage: 15,
      //   showPageSizeChanger: false,
      //   hideOnSinglePage: false,
      //   showSizeChanger: false,
      // }
      // template: assessmentActivityManageTemplate,
      detail: {
        mode: 'route',
      },
      // editApi:
      showCount: true,
      // actions: [
      //  { key: 'create', enabled: true },
      //  { key: 'update', enabled: true },
      //  { key: 'delete', enabled: true },
      //  { key: 'import', enabled: true },
      //  { key: 'export', enabled: true },
      // ],
      table: {
        scroll: { y: '70vh' },
        // columns: [{ dataIndex: 'id', title: 'ID' }],
      },
      // selection: {
      //   showByDefault: false;
      //   showSwitch: false;
      // };
      // searcherSimpleOptions: [{key: 'title', label: '标题', type: 'string' }],
    }));

    const statistics = ref({
      // doing: 0,
      // ended: 0,
      published: 0,
      draft: 0,
      completed: 0,
    });

    const tabs = computed<TaIndexViewTabInterface[]>(() => [
      {
        key: 'published',
        label: '已发布',
        num: statistics.value.published,
        query: { state_eq: 'published' },
      },
      // {
      //   key: 'ended',
      //   label: '已结束',
      //   num: statistics.value.ended,
      //   query: { state_eq: 'rejected' },
      // },
      {
        key: 'draft',
        label: '草稿箱',
        num: statistics.value.draft,
        query: { state_eq: 'rejected' },
      },
      {
        key: 'completed',
        label: '已完成',
        num: statistics.value.completed,
        query: { state_eq: 'completed' },
      },
    ]);

    const onIndex = (data: VObject) => {
      statistics.value = data.statistics;
    };

    return {
      ...toRefs(props),
      config,
      tabs,
      onIndex,
    };
  },
});

export default ComAssessmentActivitiesUserIndex;
</script>

<template lang="pug">
.com-assessment-manage-activities-user-index
  TaIndexView(:config='config', :tabs='tabs', @onIndex='onIndex')
    template(#card='{ record }')
      ComAssessmentActivityCard(:activity='record')
        template(#right-top='{ activity }')
          .tags.flex
            TaTag(
              v-if='activity.activity_score_count?.score_count?.todo',
              icon='ExclamationCircleFilled',
              color='#3da8f5',
              background='rgba(240, 251, 254)'
            )
              | 待考核{{ activity.activity_score_count.score_count.todo }}人
            TaTag.tag(
              v-if='activity.activity_score_count?.score_count?.done',
              icon='CheckCircleFilled',
              color='rgba(74, 198, 117)',
              background='rgba(238, 249, 241)'
            )
              | 已考核{{ activity.activity_score_count.score_count.done }}人

        template(#bottom='{ activity }')
          .bottom
            TaTag(v-if='activity.activity_relate_state.scored', icon='TeamOutlined')
              | 考核他人
            TaTag(v-if='activity.activity_relate_state.entried', icon='UserOutlined')
              | 被考核人
</template>

<style lang="stylus" scoped>
.com-assessment-manage-activities-user-index
  height 100%
  width 100%
.bottom
  padding 10px 0
  display flex
  justify-content flex-end
.tag
  margin-left 12px
</style>
