<script lang="ts">
import { defineComponent, toRefs } from 'vue';

const ComAssessmentCard = defineComponent({
  name: 'ComAssessmentCard',
  components: {},
  props: {
    icon: {
      type: String,
      default: 'https://stiei-obs2.obs.myhuaweicloud.com/party-build-web/score.png',
    },
    title: { type: String, default: '我的得分' },
    num: { type: Number, default: 0 },
    background_picture: {
      type: String,
      default:
        'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/party-build-web/%E5%8D%A1%E7%89%87-%E9%BB%84%E8%89%B2.png',
    },
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
export default ComAssessmentCard;
</script>

<template lang="pug">
.com-assessment-card(
  :style='{ background: `url(${background_picture})`, "background-size": "cover" }'
)
  .top
    img.icon(:src='icon')
    .title {{ title }}
  .middle
    .num {{ num }}
</template>

<style lang="stylus" scoped>
.com-assessment-card
  width 100%
  .top
    margin 18px
    display flex
    align-items center
    .icon
      width 30px
      height 30px
    .title
      margin-left 7px
      font-size 18px
      font-family PingFangSC-Medium, PingFang SC
      font-weight 500
      color #FFFFFF
      line-height 25px
  .middle
    font-size 80px
    font-family DINCondensed-Bold, DINCondensed
    font-weight bold
    color #FFFFFF
    line-height 80px
    .num
      font-size 80px
      font-family DINCondensed-Bold, DINCondensed
      font-weight bold
      color #FFFFFF
      line-height 80px
      margin 0 158px 0 auto
      position relative
      left 60%
</style>
