<script lang="ts">
import { defineComponent, toRefs } from 'vue';
import { ref, computed } from '@vue/reactivity';
import { VObject } from '@/lib/vails/model';
import ComAssessmentEntriesIndex from '../entries/ComAssessmentEntriesIndex.vue';
import ComAssessmentGroupsIndex from '../groups/ComAssessmentGroupsIndex.vue';
import ComAssessmentDimensionsIndex from '../dimensions/ComAssessmentDimensionsIndex.vue';
import ComAssessmentCatalogsIndex from '../catalogs/ComAssessmentCatalogsIndex.vue';
import { VStore } from '@/lib/vails';
import { AssessmentManageCatalogsApi } from '@/engines/assessment/apis/assessment/manage/catalogs.api';
import { AssessmentManageEntriesApi } from '@/engines/assessment/apis/assessment/manage/entries.api';
import { AssessmentManageGroupsApi } from '@/engines/assessment/apis/assessment/manage/groups.api';
import { AssessmentManageDimensionsApi } from '@/engines/assessment/apis/assessment/manage/dimensions.api';
import ComAssessmentActivitiesConfigurationDetail from './ComAssessmentActivitiesConfigurationDetail.vue';

const ComAssessmentActivitiesConfiguration = defineComponent({
  name: 'ComAssessmentActivitiesConfiguration',
  components: {
    ComAssessmentEntriesIndex,
    ComAssessmentDimensionsIndex,
    ComAssessmentCatalogsIndex,
    ComAssessmentGroupsIndex,
    ComAssessmentActivitiesConfigurationDetail,
  },
  props: {
    activity: { type: Object, required: true },
  },
  emits: ['afterDelete', 'afterExtend'],
  setup(props) {
    const catalogsStore = new VStore(
      new AssessmentManageCatalogsApi({
        parents: [{ type: 'activities', id: props.activity.id }],
      }),
    );

    const entriesStore = new VStore(
      new AssessmentManageEntriesApi({
        parents: [{ type: 'activities', id: props.activity.id }],
        params: { q: { s: ['position asc'] } },
      }),
    );

    const groupsStore = new VStore(
      new AssessmentManageGroupsApi({
        parents: [{ type: 'activities', id: props.activity.id }],
      }),
    );

    const dimensionsStore = new VStore(
      new AssessmentManageDimensionsApi({
        parents: [{ type: 'activities', id: props.activity.id }],
        params: { q: { s: ['position asc'] } },
      }),
    );

    const tabs = computed(() => {
      const result = [
        {
          icon: 'HomeOutlined',
          key: 'info',
          label: '基本信息',
          desc: '配置活动基本信息',
          detailComponent: 'ComAssessmentActivitiesConfigurationDetail',
        },
        {
          icon: 'HomeOutlined',
          key: 'entries',
          label: '人员配置',
          desc: '创删改查被考核人员（单位）信息',
          detailComponent: 'ComAssessmentEntriesIndex',
          store: entriesStore as any,
        },
      ];
      if (props.activity.catalog_enabled) {
        result.push({
          icon: 'HomeOutlined',
          key: 'catalogs',
          label: '分类配置',
          desc: '配置考核分类，如：岗位、工作性质等',
          detailComponent: 'ComAssessmentCatalogsIndex',
          store: catalogsStore as any,
        });
      } else {
        result.push({
          icon: 'HomeOutlined',
          key: 'dimensions',
          label: '维度管理',
          desc: '配置考核维度',
          detailComponent: 'ComAssessmentDimensionsIndex',
          store: dimensionsStore as any,
        });
      }

      if (props.activity.group_enabled) {
        result.push({
          icon: 'HomeOutlined',
          key: 'groups',
          label: '分组管理',
          desc: '为被考核人分组，可在小组间进行互评',
          detailComponent: 'ComAssessmentGroupsIndex',
          store: groupsStore,
        });
      }
      return result;
    });

    const activeTab = ref<VObject>(tabs.value[0] || {});

    return {
      ...toRefs(props),
      tabs,
      activeTab,
    };
  },
});
export default ComAssessmentActivitiesConfiguration;
</script>

<template lang="pug">
.com-assessment-manage-activities-configuration.flex
  .left
    .line
    .title.flex 考核配置
    .tabs
      .tab.clickable(
        v-for='tab in tabs',
        :class='{ "active-tab": tab.key === activeTab?.key }',
        @click='() => (activeTab = tab)'
      )
        .label.flex
          TaIcon.icon(:type='tab.icon')
          | {{ tab.label }}
        .desc
          | {{ tab.desc }}
  .right.w-0.overflow-y-auto
    component.dynamic-component(
      :is='activeTab.detailComponent',
      :activity='activity',
      :activityId='activity.id',
      :store='tabs.find(tab => tab.key === activeTab.key)?.store'
    )
</template>

<style lang="stylus" scoped>
.com-assessment-manage-activities-configuration
  height 100%
  width 100%
  position relative
  flex-grow 1
  .left
    width 280px
    border-right 1px solid #F0F0F0
    flex-shrink 0
    .line
      width 100%
      z-index 5
      border-bottom 1px solid #F0F0F0
      position absolute
      top 56px
    .title
      height 56px
      padding 0 20px
      align-items center
    .tabs
      .tab
        border-left 4px solid white
        padding 12px 18px
        .label
          margin-bottom 8px
          color #262626
          font-weight 500
          font-size 14px
          align-items center
          .icon
            margin-right 6px
            color #A6A6A6
        .desc
          color #A6A6A6
          font-size 12px
          line-height 16px
  .right
    position relative
    padding 4px 20px 0
    flex-grow 1
.active-tab
  border-left 4px solid $primary-color !important
  .label
    color $primary-color !important
    .icon
      color $primary-color !important
  .desc
    color $primary-color !important
.dynamic-component
  >>> .ta-index-view-header
    margin-bottom 20px
  >>> .ta-table-component__table
    padding 0
  >>> .table-header__title
    font-size 14px !important
</style>
