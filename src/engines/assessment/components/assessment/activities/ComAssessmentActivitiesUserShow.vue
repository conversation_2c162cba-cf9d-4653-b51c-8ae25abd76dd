<script lang="ts">
import { defineComponent, toRefs, PropType, computed } from 'vue';
import { TaBreadcrumbInterface } from '@/components/global/ta-component/TaBreadcrumb.vue';
import { VStore } from '@/lib/vails';
import ComAssessmentScoresUserIndex from '@/engines/assessment/components/assessment/scores/ComAssessmentScoresUserIndex.vue';
import ComAssessmentActivityStatistics from '@/engines/assessment/components/assessment/activities/ComAssessmentActivityStatistics.vue';
import { AssessmentUserScoredScoresApi } from '@/engines/assessment/assessment-core/apis/assessment/user/scored/scores.api';
import ComAssessmentActivitySelfEntryForm from '../entries/ComAssessmentActivitySelfEntryForm.vue';
import { AssessmentScoreModel } from '../../../assessment-core/models/assessment/scores';

const ComAssessmentActivitiesUserShow = defineComponent({
  name: 'ComAssessmentActivitiesUserShow',
  components: {
    ComAssessmentScoresUserIndex,
    ComAssessmentActivityStatistics,
    ComAssessmentActivitySelfEntryForm,
  },
  props: {
    store: { type: Object, required: true },
    extendRoute: { type: String, default: '' },
    editable: { type: Boolean, default: true },
    breadcrumbs: { type: Array as PropType<TaBreadcrumbInterface[]>, default: () => [] },
  },
  emits: ['afterDelete', 'afterExtend'],
  setup(props, { emit }) {
    const tabs = computed(() => [
      {
        key: 'scores',
        label: '测评',
      },
      {
        key: 'info',
        label: '详细信息',
      },
      ...(props.store.record.value.activity_relate_state.entried &&
      props.store.record.value?.entry_form?.fields?.length > 0
        ? [{ key: 'entry', label: '自评' }]
        : []),
    ]);

    const scoresStore = new VStore(
      new AssessmentUserScoredScoresApi({
        params: {
          q: {
            activity_id_eq: props.store.record.value.id,
            s: ['entry_position asc'],
            refs: 'entry',
          },
        },
      }),
      AssessmentScoreModel,
    );

    const afterDelete = () => {
      emit('afterDelete');
    };

    const afterExtend = () => {
      emit('afterExtend');
    };

    return {
      ...toRefs(props),
      tabs,
      record: props.store.record,
      afterDelete,
      afterExtend,
      scoresStore,
    };
  },
});
export default ComAssessmentActivitiesUserShow;
</script>

<template lang="pug">
.com-assessment-activities-user-show
  TaShowLayout.show-layout(
    :tabs='tabs',
    :title='record.name',
    :store='store',
    :extendRoute='extendRoute',
    :editable='editable',
    :breadcrumbs='breadcrumbs',
    @afterDelete='afterDelete',
    @afterExtend='afterExtend'
  )
    template(#scores_tab)
      ComAssessmentScoresUserIndex(:store='scoresStore', :activity='record')
    template(#info_tab)
      ComAssessmentActivityStatistics(:record='record')
    template(#entry_tab)
      ComAssessmentActivitySelfEntryForm(:activity='record')
</template>

<style lang="stylus" scoped>
.com-assessment-activities-user-show
  height 100%
  .show-layout
    >>> .content
      padding 0
      display flex
      flex-direction column
</style>
