<script lang="ts">
import { defineComponent, computed, toRefs, onActivated, ref } from 'vue';
import { TaIndexViewTabInterface } from '@/components/global/ta-component/TaIndexView/types';
import { VObject } from '@/lib/vails/model';
import { assessmentActivityManageTemplate } from './template';
import ComAssessmentActivityCard from './ComAssessmentActivityCard.vue';

const ComAssessmentActivitiesIndex = defineComponent({
  name: 'ComAssessmentActivitiesIndex',
  components: {
    ComAssessmentActivityCard,
  },
  props: {
    store: { type: Object, required: true },
  },
  setup(props) {
    const config = computed(() => ({
      recordName: '考核活动',
      store: props.store,
      // pagination: {
      //   perPage: 15,
      //   showPageSizeChanger: false,
      //   hideOnSinglePage: false,
      //   showSizeChanger: false,
      // }
      template: assessmentActivityManageTemplate,
      detail: {
        mode: 'route',
      },
      // editApi:
      showCount: true,
      // actions: [
      //  { key: 'create', enabled: true },
      //  { key: 'update', enabled: true },
      //  { key: 'delete', enabled: true },
      //  { key: 'import', enabled: true },
      //  { key: 'export', enabled: true },
      // ],
      table: {
        scroll: { y: '70vh' },
        // columns: [{ dataIndex: 'id', title: 'ID' }],
      },
      // selection: {
      //   showByDefault: false;
      //   showSwitch: false;
      // };
      // searcherSimpleOptions: [{key: 'title', label: '标题', type: 'string' }],
    }));

    const statistics = ref({
      // doing: 0,
      completed: 0,
      published: 0,
      draft: 0,
    });

    const tabs = computed<TaIndexViewTabInterface[]>(() => [
      {
        key: 'published',
        label: '已发布',
        num: statistics.value.published,
        query: { state_eq: 'published' },
      },
      // {
      //   key: 'ended',
      //   label: '已结束',
      //   num: statistics.value.ended,
      //   query: { state_eq: 'rejected' },
      // },
      {
        key: 'draft',
        label: '草稿箱',
        num: statistics.value.draft,
        query: { state_eq: 'draft' },
      },
      {
        key: 'completed',
        label: '已完成',
        num: statistics.value.completed,
        query: { state_eq: 'completed' },
      },
    ]);

    const onIndex = (data: VObject) => {
      statistics.value = data.statistics;
    };

    const taIndexView = ref<any>(null);

    onActivated(() => {
      taIndexView.value?.silenceRefresh();
    });

    return {
      ...toRefs(props),
      config,
      tabs,
      onIndex,
      taIndexView,
    };
  },
});

export default ComAssessmentActivitiesIndex;
</script>

<template lang="pug">
.com-assessment-manage-activities-index
  TaIndexView(ref='taIndexView', :config='config', :tabs='tabs', @onIndex='onIndex')
    template(#card='{ record }')
      ComAssessmentActivityCard(:activity='record')
</template>

<style lang="stylus" scoped>
.com-assessment-manage-activities-index
  height 100%
  width 100%
</style>
