<script lang="ts">
import { ref, defineComponent, toRefs } from 'vue';
import { assessmentActivityManageTemplate } from './template';
import useNavigateTab from '@/components/global/ta-component/useNavigateTab';

const ComAssessmentActivitiesConfigurationDetail = defineComponent({
  name: 'ComAssessmentActivitiesConfigurationDetail',
  components: {},
  props: {
    activity: { type: Object, required: true },
  },
  setup(props) {
    const visibleForm = ref(false);

    const { closeCurrentTabAndNavigateTo } = useNavigateTab();

    const onDelete = () => {
      props.activity.delete().then(() => {
        closeCurrentTabAndNavigateTo('/assessment/manage/activities');
      });
    };
    return {
      ...toRefs(props),
      assessmentActivityManageTemplate,
      visibleForm,
      onDelete,
    };
  },
});
export default ComAssessmentActivitiesConfigurationDetail;
</script>

<template lang="pug">
.com-assessment-activities-configuration-detail
  .header.flex-between
    .title.flex 考核详情
    .actions.flex
      TaPopoverConfirm(title='删除', content='您确认删除该活动吗？', @confirm='onDelete()')
        TaTextButton(icon='DeleteOutlined') 删除
      TaTextButton.ml-12px(icon='EditOutlined', @click='() => (visibleForm = true)') 编辑
  .content
    TaTemplateFormViewer(:record='activity', :template='assessmentActivityManageTemplate')
  TaTemplateFormWithActionsDrawer(
    v-model:visible='visibleForm',
    title='编辑考核活动',
    :record='activity',
    :template='assessmentActivityManageTemplate',
    @afterSave='() => (visibleForm = false)'
  )
</template>

<style lang="stylus" scoped>
.com-assessment-activities-configuration-detail
  .header
    margin-bottom 25px
    .title
      align-items center
      color #383838
      height 50px
      font-weight 500
      font-size 14px
      line-height 20px
      align-items center
    .actions
      align-items center
  .content
    overflow-y auto
    // height 55vh
.ml-12px
  margin-left 12px
</style>
