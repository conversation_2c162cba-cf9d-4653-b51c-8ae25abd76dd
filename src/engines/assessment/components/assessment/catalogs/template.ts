export const assessmentCatalogManageTemplate = {
  type: 'layout',
  model: {},
  fields: [
    {
      key: 'input_69910242363',
      name: '分类名称',
      type: 'input',
      model: { summary: true, attr_type: 'string' },
      fields: [],
      options: { span: 24 },
      model_key: 'name',
      model_key_prefix: '',
    },
    {
      name: '评分表',
      icon: 'FolderOutlined',
      type: 'api_single',
      rules: [],
      model: {
        attr_type: 'number',
      },
      options: {
        span: 24,
        multiple: false,
        table_items: [
          {
            name: '名称',
            data_index: 'name',
            search: true,
            type: 'string',
          },
        ],
        display_configurable_form: {},
        import_export_headers: [
          {
            _id: '1645335812463_4',
          },
        ],
        path: '/assessment/manage/score_templates',
        display: 'tag',
      },
      key: 'api_single_1645335812335_1',
      model_key: 'score_template_id',
      fields: [],
      conditions: [],
      model_key_prefix: '',
    },
  ],
  actions: [
    { key: 'create', enabled: true },
    { key: 'update', enabled: true },
    { key: 'delete', enabled: true },
    { key: 'import', enabled: false },
    { key: 'export', enabled: false },
  ],
  options: {
    label: {},
    theme: { card: {}, form: {}, background: {} },
    create_text: '提交',
    update_text: '提交',
  },
  model_key: 'layout_64129026269',
  column_attributes: [{ _id: 'input_69910242363', title: ['分类名称'], dataIndex: 'name' }],
};
