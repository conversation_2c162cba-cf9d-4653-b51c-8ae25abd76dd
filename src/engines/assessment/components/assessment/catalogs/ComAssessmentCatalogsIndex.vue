<script lang="ts">
import { defineComponent, computed, toRefs, watch } from 'vue';
import { assessmentCatalogManageTemplate } from './template';
import ComAssessmentDimensionsIndex from '../dimensions/ComAssessmentDimensionsIndex.vue';
import { VStore, VObject } from '@/lib/vails';
import { AssessmentManageDimensionsApi } from '@/engines/assessment/apis/assessment/manage/dimensions.api';

const ComAssessmentCatalogsIndex = defineComponent({
  name: 'ComAssessmentCatalogsIndex',
  components: {
    ComAssessmentDimensionsIndex,
  },
  props: {
    store: { type: Object, required: true },
    activityId: { type: Number, required: true },
    scrollY: { type: String, default: '70vh' },
  },
  setup(props) {
    const config = computed(() => ({
      recordName: '考核分类',
      store: props.store,
      // pagination: {
      //   perPage: 15,
      //   showPageSizeChanger: false,
      //   hideOnSinglePage: false,
      //   showSizeChanger: false,
      // }
      template: assessmentCatalogManageTemplate,
      detail: {
        // mode: 'auto',
        mode: 'drawer',
        width: '1100px',
      },
      mode: 'table',
      // editApi:
      // showCount: true,
      // actions: [
      //  { key: 'create', enabled: true },
      //  { key: 'update', enabled: true },
      //  { key: 'delete', enabled: true },
      //  { key: 'import', enabled: true },
      //  { key: 'export', enabled: true },
      // ],
      table: {
        scroll: { y: props.scrollY },
        // columns: [{ dataIndex: 'id', title: 'ID' }],
      },
      // selection: {
      //   showByDefault: false;
      //   showSwitch: false;
      // };
      // searcherSimpleOptions: [{key: 'title', label: '标题', type: 'string' }],
    }));

    const dimensionsStore = new VStore(new AssessmentManageDimensionsApi());

    watch(
      () => props.store.record.value.id,
      () => {
        dimensionsStore.api = new AssessmentManageDimensionsApi({
          parents: [{ type: 'activities', id: props.activityId }],
          params: { q: { catalog_id_eq: props.store.record.value.id } },
        });
      },
      {
        immediate: true,
      },
    );

    const dimensionFormDataEncode = (val: VObject) => {
      return Object.assign(val, { catalog_id: props.store.record.value.id });
    };

    const totalWeight = computed(() =>
      dimensionsStore.records.value
        .map((r: VObject) => r.weight)
        .reduce((a: number, b: number) => a + b, 0)
        .toFixed(2),
    );

    return {
      ...toRefs(props),
      config,
      dimensionsStore,
      dimensionFormDataEncode,
      totalWeight,
    };
  },
});

export default ComAssessmentCatalogsIndex;
</script>

<template lang="pug">
.com-assessment-manage-catalogs-index
  TaIndexView(:config='config')
    //- template(#card='{ record }')
    //- template(#table)
    //-    a-table-column(:autoHeight='true' title='名称' dataIndex='record')
    template(#detail='{ record, onClose }')
      ComAssessmentDimensionsIndex.px-6.py-4(
        v-if='record.id'
        :store='dimensionsStore'
        :formDataEncode='dimensionFormDataEncode'
      )
        template(#header)
          .flex.items-center.mb-2
            .text-lg.text-gray-900.font-bold 考核纬度
            .ml-2.text-blue-400(
              :class='{ "!text-red-500": totalWeight > 1 }'
            ) 总权重：{{ totalWeight }}

</template>

<style lang="stylus" scoped>
.com-assessment-manage-catalogs-index
  height 100%
  width 100%
</style>
