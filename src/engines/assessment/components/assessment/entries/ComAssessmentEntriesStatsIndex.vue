<script lang="ts">
import { defineComponent, computed, ref, toRefs, onMounted } from 'vue';
import ComAssessmentEntriesShow from './ComAssessmentEntriesShow.vue';
import { TaIndexViewTabInterface } from '@/components/global/ta-component/TaIndexView/types';
import { VObject } from '@/lib/vails/model';
import ComAssessmentEntriesDetail from './ComAssessmentEntriesDetail.vue';
import ComEntriesDropdown from './ComEntriesDropdown.vue';
import { AssessmentManageCatalogsApi } from '@/engines/assessment/apis/assessment/manage/catalogs.api';
import { VStore } from '@/lib/vails';
import { AssessmentManageGroupsApi } from '@/engines/assessment/apis/assessment/manage/groups.api';

const ComAssessmentEntriesStatsIndex = defineComponent({
  name: 'ComAssessmentEntriesStatsIndex',
  components: {
    ComAssessmentEntriesShow,
    ComAssessmentEntriesDetail,
    ComEntriesDropdown,
  },
  props: {
    activity: { type: Object, required: true },
    store: { type: Object, required: true },
    activityId: { type: Number, required: true },
  },
  setup(props) {
    const catalogsStore = new VStore(
      new AssessmentManageCatalogsApi({
        parents: [{ type: 'activities', id: props.activityId }],
      }),
    );
    const groupsStore = new VStore(
      new AssessmentManageGroupsApi({
        parents: [{ type: 'activities', id: props.activityId }],
      }),
    );
    const catalogsArr = ref<any[]>([]); //分组 tabs
    const groupsArr = ref<any[]>([]); //分类 tabs
    const chosenCatalogsArr = ref<any[]>([]); //分组选中tabs
    const chosenGroupsArr = ref<any[]>([]); //分类选中tabs
    const params = computed(() => {
      let chosenCatalogIds = chosenCatalogsArr.value.map((item: any) => item.id);
      let chosenGroupIds = chosenGroupsArr.value.map((item: any) => item.id);
      return {
        q: {
          catalog_id_in: chosenCatalogIds,
          group_id_in: chosenGroupIds,
        },
      };
    });
    //数据转换
    const dropdownDataConversion = () => {
      catalogsStore.index({ per_page: 9999 }).then(res => {
        catalogsArr.value = res.records.map((item: any) => {
          return {
            label: item.name,
            id: item.id,
          };
        });
        // catalogsArr.value.unshift({
        //   label: '全部',
        // });
      });
      groupsStore.index({ per_page: 9999 }).then(res => {
        groupsArr.value = res.records.map((item: any) => {
          return {
            label: item.name,
            id: item.id,
          };
        });
        // groupsArr.value.unshift({
        //   label: '全部',
        // });
      });
    };

    const config = computed(() => ({
      recordName: '考核人员',
      store: props.store,
      params: params.value,
      // pagination: {
      //   perPage: 15,
      //   showPageSizeChanger: false,
      //   hideOnSinglePage: false,
      //   showSizeChanger: false,
      // }
      // template: assessmentEntryManageTemplateFunc(
      //   props.store.record.catalog_enabled,
      //   props.store.record.group_enabled,
      // ),
      detail: {
        // mode: 'auto',
        mode: 'drawer',
        width: '50vw',
      },
      // draggable: true,
      mode: 'table',
      // editApi:
      // showCount: true,
      actions: [
        // { key: 'create', enabled: true },
        //  { key: 'update', enabled: true },
        //  { key: 'delete', enabled: true },
        //  { key: 'import', enabled: true },
        { key: 'export', enabled: true },
      ],
      table: {
        scroll: { y: 'calc(100vh - 400px)' },
        // columns: [{ dataIndex: 'id', title: 'ID' }],
      },
      // selection: {
      //   showByDefault: false;
      //   showSwitch: false;
      // };
      searcherSimpleOptions: [
        { key: 'user_of_User_type_name', label: '考核人员', type: 'string' },
        { key: 'user_of_User_type_departments_name', label: '考核人员部门', type: 'string' },
        { key: 'user_of_Department_type_name', label: '考核部门', type: 'string' },
      ],
    }));

    const statistics = ref({
      all: 0,
    });

    const tabs = computed<TaIndexViewTabInterface[]>(() => [
      {
        key: 'all',
        label: '全部',
        num: statistics.value?.all,
        query: {},
      },
    ]);

    const onIndex = (data: VObject) => {
      statistics.value = data.statistics;
    };
    onMounted(() => {
      dropdownDataConversion();
    });

    return {
      ...toRefs(props),
      config,
      tabs,
      onIndex,
      catalogsArr,
      groupsArr,
      chosenCatalogsArr,
      chosenGroupsArr,
    };
  },
});

export default ComAssessmentEntriesStatsIndex;
</script>

<template lang="pug">
.com-assessment-manage-entries-stats-index
  TaIndexView(:config='config', @onIndex='onIndex')
    template(#header)
      .w-full.flex
        ComEntriesDropdown(
          v-if='catalogsArr.length > 0',
          :tabs='catalogsArr',
          v-model:chosenTabs='chosenCatalogsArr'
        )
          template(#label='{ arrString, open, visible, chosenArr }')
            .flex.items-center.mr-4
              .px-1.cursor-pointer.rounded-lg.text-white.truncate.mr-2(
                class='bg-[#1890ff]',
                v-if='chosenArr.length > 0',
                v-for='item in chosenArr'
              ) {{ item.label }}
              .cursor-pointer.mr-2(v-else) {{ arrString || "分类筛选" }}
              TaIcon.transition-all.duration-200.transform(
                :class='visible ? "rotate-0" : "rotate-180"',
                type='CaretUpOutlined',
                size='14',
                :style='`color:${visible ? "#1890ff" : "#808080"} `'
              )

        ComEntriesDropdown(
          v-if='groupsArr.length > 0',
          :tabs='groupsArr',
          v-model:chosenTabs='chosenGroupsArr'
        )
          template(#label='{ arrString, open, visible, chosenArr }')
            .flex.items-center
              .max-w-14.px-1.cursor-pointer.rounded-lg.bg-gray-400.text-white.truncate.mr-2(
                class='bg-[#1890ff]',
                v-if='chosenArr.length > 0',
                v-for='item in chosenArr'
              ) {{ item.label }}
              .cursor-pointer.mr-2(v-else) {{ arrString || "分组筛选" }}
              TaIcon.transition-all.duration-200.transform(
                :class='visible ? "rotate-0" : "rotate-180"',
                type='CaretUpOutlined',
                size='14',
                :style='`color:${visible ? "#1890ff" : "#808080"}`'
              )

    template(#table)
      a-table-column(:autoHeight='true' title='被考核人信息', :width='170')
        template(#default='{ record }')
          .assessment
            p
              span.label 名字:
              span {{ record.assessment_user_info.name }}
            p(v-if='record.assessment_user_info?.org_name')
              span.label 单位:
              span {{ record.assessment_user_info.org_name }}
            p(v-if='record.assessment_user_info.department_names')
              span.label 部门:
              span {{ record.assessment_user_info.department_names.join(" ") }}
            p(v-if='record.assessment_user_info.department_name')
              span.label 部门:
              span {{ record.assessment_user_info.department_name }}

      a-table-column(:autoHeight='true' title='各项维度分数', dataIndex='dimension_stat', :width='270')
        template(#default='{ value }')
          .assessment
            p(v-for='item in value')
              span.dimension_label {{ item.name }}:
              span.dimension
                span {{ ` ${item.score || 0}分` }}，
                span {{ `已评分: ${item.score_stat.done || 0}人` }}，
                span {{ `待评分: ${item.score_stat.todo || 0}人` }}，
      a-table-column(:autoHeight='true' v-if='activity.catalog_enabled', title='分类', :width='100')
        template(#default='{ record }')
          .assessment {{ record.catalog.name }}
      a-table-column(:autoHeight='true' title='总分', :width='170')
        template(#default='{ record }')
          .assessment
            .content
              p {{ record.score || 0 }}分
              p
                span {{ `已评分: ${record.score_stat?.done || 0}人` }}，
                span {{ `待评分: ${record.score_stat?.todo || 0}人` }}；
    template(#detail='{ record }')
      ComAssessmentEntriesDetail(:entriesRecord='record', :activityId='activityId')
</template>

<style lang="stylus" scoped>
.com-assessment-manage-entries-stats-index
  height 100%
  width 100%
  padding 10px 20px
  .assessment
    display flex
    flex-direction column
    // padding-left 12px
    overflow hidden
    text-overflow ellipsis
    white-space nowrap
    .label
      margin-right 5px
</style>
