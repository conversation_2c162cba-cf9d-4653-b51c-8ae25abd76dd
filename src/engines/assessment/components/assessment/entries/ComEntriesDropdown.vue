<script lang="ts">
import { ref, defineComponent, toRefs, computed, onMounted } from 'vue';

const ComEntriesDropdown = defineComponent({
  name: 'ComEntriesDropdown',
  components: {},
  props: {
    tabs: { type: Array, default: () => [] },
    chosenTabs: { type: Array, default: () => [] },
    singleChoice: { type: Boolean, default: false },
  },
  setup(props, { emit }) {
    const visible = ref(false);

    const chosenArr = computed({
      get: () => props.chosenTabs,
      set: val => emit('update:chosenTabs', val),
    });
    //数组转字符串
    const arrString = computed(() => {
      let text = '';
      chosenArr.value.forEach((item: any, index: number) => {
        if (index < 3) {
          if (index == 0) {
            text = text + item?.label;
          } else {
            text = text + ',' + item?.label;
          }
        } else {
          text = text + item?.label + '等';
        }
      });
      return text;
    });
    //获取数组下标
    const checkAdult = (tab: any) => {
      let index = chosenArr.value.findIndex((item: any) => {
        return item.id == tab.id;
      });
      return index;
    };

    //选择事件
    const onChosen = (tab: any) => {
      let index = checkAdult(tab);
      //判断单选或多选
      if (props.singleChoice) {
        chosenArr.value[0] = tab;
      } else {
        if (index >= 0) {
          chosenArr.value.splice(index, 1);
        } else {
          chosenArr.value.push(tab);
        }
      }
    };
    //展开弹窗
    const open = () => {
      visible.value = true;
    };
    //收起弹窗
    const visibleChange = (data: any) => {
      visible.value = data;
    };
    onMounted(() => {
      // if (chosenArr.value.length == 0) {
      //   chosenArr.value.push(props.tabs[0]);
      //   console.log(chosenArr.value, 'chosenArr.value');
      // }
    });

    return {
      ...toRefs(props),
      arrString,
      chosenArr,
      onChosen,
      checkAdult,
      open,
      visible,
      visibleChange,
    };
  },
});
export default ComEntriesDropdown;
</script>

<template lang="pug">
.com-entries-dropdown
  a-dropdown(v-model:visible="visible",trigger="click",@visibleChange="visibleChange")
    slot(name="label",:arrString="arrString",:chosenArr="chosenArr",:open="open",:visible="visible")
      .flex.items-center
        div.cursor-pointer {{arrString || '请选择'}}
        TaIcon.transition-all.duration-200.transform(:class="visible ? 'rotate-0':'rotate-180'" type="CaretUpOutlined",size="14",:style="`color: #1890ff`")
    template(#overlay)
      slot(name="overlay",:tabs="tabs")
        .bg-white.shadow-xl
          div(
            v-for="tab in tabs"
            @click="onChosen(tab)"
            )
            slot(name="tab",:tab="tab",:isTab="checkAdult(tab)>=0")
              .cursor-pointer.px-3.py-3.flex.items-center(
                class="hover:text-[#1890ff] text-[#808080]"
                :style="`color: ${checkAdult(tab) >=0 ? '#1890ff':''} `") {{tab.label}}

</template>

<style lang="stylus" scoped></style>
