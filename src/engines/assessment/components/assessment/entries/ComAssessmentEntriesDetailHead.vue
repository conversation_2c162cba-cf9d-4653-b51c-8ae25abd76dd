<script lang="ts">
import { defineComponent, ref, toRefs } from 'vue';

const ComAssessmentEntriesDetailHead = defineComponent({
  name: 'ComAssessmentEntriesDetailHead',
  components: {},
  props: {
    record: { type: Object, default: () => {} },
  },
  setup(props) {
    const arrString = (arr: any[]) => {
      let text = '';
      arr.forEach((item: any, index: number) => {
        if (index == 0) {
          text = text + item;
        } else {
          text = text + ' , ' + item;
        }
      });
      return text;
    };
    const tabs = ref([
      {
        label: '部门',
        data: arrString(props.record.assessment_user_info.department_names),
      },
      {
        label: '岗位',
        data: arrString(props.record.assessment_user_info.duty_names),
      },
    ]);
    return {
      ...toRefs(props),
      tabs,
    };
  },
});
export default ComAssessmentEntriesDetailHead;
</script>

<template lang="pug">
.com-assessment-entries-detail-head.rounded.px-4.py-3(class='bg-[#38a7f4]')
  .flex.justify-between.items-center
    .text-white.text-base.font-medium {{ record?.assessment_user_info?.name }}
  .flex.items-center.flex-wrap.mt-2
    .flex.items-center(class='min-w-[150px]', v-for='item in tabs')
      .text-white.text-sm {{ item.label }}：
      .text-white.text-sm {{ item.data }}
</template>

<style lang="stylus" scoped></style>
