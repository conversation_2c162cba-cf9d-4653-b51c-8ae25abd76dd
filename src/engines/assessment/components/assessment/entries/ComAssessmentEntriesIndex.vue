<script lang="ts">
import { defineComponent, computed, ref, toRefs } from 'vue';
import ComAssessmentEntriesShow from './ComAssessmentEntriesShow.vue';
import { assessmentEntryManageTemplateFunc } from './template';
import { VObject } from '@/lib/vails/model';
import {
  AssessmentEntry,
  AssessmentCatalog,
} from '@/engines/assessment/assessment-core/types/model';
import { VStore } from '@/lib/vails';
import { AssessmentManageCatalogsApi } from '@/engines/assessment/apis/assessment/manage/catalogs.api';

const ComAssessmentEntriesIndex = defineComponent({
  name: 'ComAssessmentEntriesIndex',
  components: {
    ComAssessmentEntriesShow,
  },
  props: {
    store: { type: Object, required: true },
    activity: { type: Object, required: true },
    activityId: { type: Number, required: true },
    scrollY: { type: String, default: '70vh' },
  },
  setup(props) {
    const config = computed(() => ({
      recordName: '考核人员',
      store: props.store,
      // pagination: {
      //   perPage: 15,
      //   showPageSizeChanger: false,
      //   hideOnSinglePage: false,
      //   showSizeChanger: false,
      // }
      template: assessmentEntryManageTemplateFunc(
        props.activity?.catalog_enabled,
        props.activity?.group_enabled,
      ),
      // detail: {
      //   mode: 'auto',
      //   mode: 'drawer',
      //   width: '1100px',
      // },
      draggable: true,
      mode: 'table',
      // editApi:
      // showCount: true,
      // actions: [
      //  { key: 'create', enabled: true },
      //  { key: 'update', enabled: true },
      //  { key: 'delete', enabled: true },
      //  { key: 'import', enabled: true },
      //  { key: 'export', enabled: true },
      // ],
      table: {
        scroll: { y: props.scrollY },
        // columns: [{ dataIndex: 'id', title: 'ID' }],
      },
      selection: {
        showByDefault: true,
        // showSwitch: true,
      },
      searcherSimpleOptions: [
        { key: 'user_of_Department_type_name', label: '考核人员', type: 'string' },
        { key: 'user_of_User_type_name', label: '考核部门', type: 'string' },
        ...(props.activity?.group_enabled
          ? [{ key: 'group_name', label: '考核分组', type: 'string' }]
          : []),
        ...(props.activity?.catalog_enabled
          ? [{ key: 'catalog_name', label: '考核分类', type: 'string' }]
          : []),
      ],
    }));

    const taIndexView = ref<any>(null);

    const fields = ref<VObject>({
      department: null,
      user: null,
      catalog: null,
    });

    const onBatchCreate = (type: 'department' | 'user') => {
      fields.value[type]?.open?.();
    };

    const userIds = ref<number[]>([]);

    const userTableItems = [
      {
        name: '用户名',
        type: 'string',
        search: true,
        data_index: 'name',
      },
      {
        name: '用户账号',
        type: 'string',
        search: true,
        data_index: 'account',
      },
    ];

    const onUserSelectOk = () => {
      props.activity
        .update({
          entries_attributes: userIds.value.map((id: number) => ({
            user_id: id,
            user_type: 'User',
          })),
        })
        .then(() => {
          taIndexView.value.silenceRefresh();
        });
    };

    const departmentIds = ref<number[]>([]);

    const departmentTableItems = [
      {
        name: '名称',
        type: 'string',
        search: true,
        data_index: 'name',
      },
      {
        name: 'code',
        type: 'string',
        search: true,
        data_index: 'code',
      },
      {
        name: '简称',
        type: 'string',
        search: true,
        data_index: 'short_name',
      },
    ];

    const onDepartmentSelectOk = () => {
      props.activity
        .update({
          entries_attributes: departmentIds.value.map((id: number) => ({
            user_id: id,
            user_type: 'Department',
          })),
        })
        .then(() => {
          taIndexView.value.silenceRefresh();
        });
    };

    const selectedEntries = ref<AssessmentEntry[]>([]);
    const catalogId = ref<number>();

    const catalogTableItems = [
      {
        name: '名称',
        type: 'string',
        search: true,
        data_index: 'name',
      },
    ];

    const onBatchEditCatalog = () => fields.value.catalog?.open?.();

    const onBatchEditCatalogOk = () => {
      props.activity
        .update({
          entries_attributes: selectedEntries.value.map((entry: AssessmentEntry) => ({
            catalog_id: catalogId.value,
            id: entry.id,
          })),
        })
        .then(() => {
          taIndexView.value.silenceRefresh();
        });
    };

    const catalogStoreForTabs = new VStore(
      new AssessmentManageCatalogsApi({
        parents: [{ type: 'activities', id: props.activityId }],
      }),
    );

    catalogStoreForTabs.index({ per_page: 999999 });

    const tabs = computed(() => [
      {
        key: 'all',
        label: '全部',
        query: {},
      },
      ...catalogStoreForTabs.records.value.map((record: AssessmentCatalog) => ({
        key: record.id,
        label: record.name,
        query: {
          catalog_id_eq: record.id,
        },
      })),
    ]);

    return {
      ...toRefs(props),
      config,
      onBatchCreate,
      userIds,
      userTableItems,
      onUserSelectOk,
      departmentIds,
      departmentTableItems,
      onDepartmentSelectOk,
      fields,
      taIndexView,
      selectedEntries,
      onBatchEditCatalog,
      onBatchEditCatalogOk,
      catalogTableItems,
      catalogId,
      tabs,
    };
  },
});

export default ComAssessmentEntriesIndex;
</script>

<template lang="pug">
.com-assessment-manage-entries-index.w-full
  TaIndexView.w-full(
    ref='taIndexView',
    v-model:selectedRecords='selectedEntries',
    :config='config',
    :tabs='tabs'
  )
    template(#right-actions)
      TaTextButton.ml-2(
        v-if='selectedEntries.length > 0',
        icon='EditOutlined',
        @click='onBatchEditCatalog'
      ) 批量修改分类

      a-dropdown(trigger=["click", "hover"])
        TaTextButton(icon='PlusCircleOutlined') 批量创建
        template(#overlay)
          a-menu
            a-menu-item(@click='onBatchCreate("department")')
              .clickable 单位
            a-menu-item(@click='onBatchCreate("user")')
              .clickable 用户

  TaApiNoDisplayField(
    :ref='el => (fields["user"] = el)',
    v-model:value='userIds',
    :item='{ options: { use_book: true } }',
    path='/res/member/users',
    recordName='考核用户',
    :multiple='true',
    :tableItems='userTableItems',
    @ok='onUserSelectOk'
  )

  TaApiNoDisplayField(
    :ref='el => (fields["department"] = el)',
    v-model:value='departmentIds',
    :item='{ options: { use_book: true } }',
    path='/res/member/departments',
    recordName='考核用户',
    :multiple='true',
    :tableItems='departmentTableItems',
    @ok='onDepartmentSelectOk'
  )

  TaApiNoDisplaySingleField(
    :ref='el => (fields["catalog"] = el)',
    v-model:value='catalogId',
    :path='`/assessment/manage/activities/${activityId}/catalogs`',
    recordName='选择考核分类',
    :multiple='false',
    :tableItems='catalogTableItems',
    @ok='onBatchEditCatalogOk'
  )
</template>

<style lang="stylus" scoped>
.com-assessment-manage-entries-index
  height 100%
  width 100%
</style>
