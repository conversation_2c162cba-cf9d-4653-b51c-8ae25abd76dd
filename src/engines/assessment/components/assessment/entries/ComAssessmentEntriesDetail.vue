<script lang="ts">
import { defineComponent, toRefs, computed } from 'vue';
import { AssessmentManageScoresApi } from '@/engines/assessment/apis/assessment/manage/scores.api';
import { VStore } from '@/lib/vails';
import ComAssessmentEntriesDetailHead from './ComAssessmentEntriesDetailHead.vue';
import ComAssessmentScoresDetailIndex from '../scores/ComAssessmentScoresDetailIndex.vue';
import ComAssessmentScoresIndex from '../scores/ComAssessmentScoresIndex.vue';
const ComAssessmentEntriesDetail = defineComponent({
  name: 'ComAssessmentEntriesDetail',
  components: {
    ComAssessmentEntriesDetailHead,
    ComAssessmentScoresDetailIndex,
    ComAssessmentScoresIndex,
  },
  props: {
    entriesRecord: { type: Object, default: () => {} },
    activityId: { type: Number, required: true },
  },
  setup(props) {
    const store = new VStore(
      new AssessmentManageScoresApi({
        parents: [{ type: 'activities', id: props.activityId }],
      }),
    );
    // entry_id
    const params = computed(() => ({
      q: {
        entry_id_eq: props.entriesRecord.id,
      },
    }));
    return {
      ...toRefs(props),
      store,
      params,
    };
  },
});
export default ComAssessmentEntriesDetail;
</script>

<template lang="pug">
.com-assessment-entries-detail.px-5
  ComAssessmentScoresDetailIndex(:store="store",:params="params")
    template(#header)
      ComAssessmentEntriesDetailHead.mt-2(:record="entriesRecord")

  //- ComAssessmentScoresIndex(:store="store",:activityId="activityId")
</template>

<style lang="stylus" scoped></style>
