import { IFile } from '../../../../../components/global/ta-component/file/servers/types';
export const assessmentEntryManageTemplateFunc = (
  catalogEnabled: boolean,
  groupEnabled: boolean,
) => ({
  type: 'layout',
  model: {},
  fields: [
    ...(groupEnabled
      ? [
          {
            key: 'api_single_1645322665148_6',
            name: '考核分组',
            type: 'api_single',
            model: { attr_type: 'number' },
            rules: [
              {
                rule_type: 'required',
                type: 'number',
                required: true,
                message: '请填写正确的考核分组',
              },
            ],
            fields: [],
            options: {
              path: '/assessment/manage/activities/${activityId}/groups',
              span: 24,
              display: 'tag',
              ransack: '',
              multiple: false,
              table_items: [{ name: '分组名称', type: 'string', search: true, data_index: 'name' }],
              import_export_headers: [{ _id: '1645322665249_0' }],
              display_configurable_form: {},
            },
            model_key: 'group_id',
            conditions: [],
            model_key_prefix: '',
          },
        ]
      : []),
    ...(catalogEnabled
      ? [
          {
            key: 'api_single_1645322770405_8',
            name: '考核分类',
            type: 'api_single',
            model: { attr_type: 'number' },
            rules: [
              {
                rule_type: 'required',
                type: 'number',
                required: true,
                message: '请填写正确的考核分类',
              },
            ],
            fields: [],
            options: {
              path: '/assessment/manage/activities/${activityId}/catalogs',
              span: 24,
              display: 'tag',
              ransack: '',
              multiple: false,
              table_items: [{ name: '分组名称', type: 'string', search: true, data_index: 'name' }],
              import_export_headers: [{ _id: '1645322665249_0' }],
              display_configurable_form: {},
            },
            model_key: 'catalog_id',
            conditions: [],
            model_key_prefix: '',
          },
        ]
      : []),
    {
      key: 'radio_1645322834418_9',
      name: '考核人员类型',
      type: 'radio',
      model: { attr_type: 'string' },
      rules: [
        {
          type: 'string',
          message: '请填写正确的考核人员类型',
          required: true,
          rule_type: 'required',
        },
      ],
      fields: [],
      options: {
        span: 24,
        select: [
          { label: '单位', value: 'Department' },
          { label: '用户', value: 'User' },
        ],
        multiple: false,
      },
      model_key: 'user_type',
      conditions: [],
      model_key_prefix: '',
    },
    {
      key: 'condition_1645322875824_10',
      name: '条件块',
      type: 'condition',
      fields: [],
      options: { span: 24 },
      model_key: 'condition_1645322875824_10',
      conditions: [
        {
          opt: '==',
          val: 'Department',
          name: '单位',
          type: 'simple',
          fields: [
            {
              key: 'api_single_1645322995914_11',
              name: '考核单位',
              type: 'api_single',
              model: { attr_type: 'number' },
              rules: [
                {
                  type: 'number',
                  message: '请填写正确的考核单位',
                  required: true,
                  rule_type: 'required',
                },
              ],
              fields: [],
              options: {
                path: '/res/member/departments',
                span: 24,
                display: 'tag',
                multiple: false,
                table_items: [
                  { name: '部门名称', type: 'string', search: true, data_index: 'name' },
                  { name: '上级部门', type: 'string', data_index: 'ancestor_names' },
                ],
                import_export_headers: [{ _id: '1645322997858_1' }],
                display_configurable_form: {},
              },
              model_key: 'user_id',
              conditions: [],
              model_key_prefix: '',
            },
          ],
          model_key: 'user_type',
          complex_condition: { groups: [] },
        },
        {
          opt: '==',
          val: 'User',
          name: '用户',
          type: 'simple',
          fields: [
            {
              key: 'api_single_1645323053560_12',
              name: '考核用户',
              type: 'api_single',
              model: { attr_type: 'number' },
              rules: [
                {
                  type: 'number',
                  message: '请填写正确的考核用户',
                  required: true,
                  rule_type: 'required',
                },
              ],
              fields: [],
              options: {
                path: '/res/member/users',
                span: 24,
                display: 'tag',
                ransack: '',
                multiple: false,
                table_items: [{ name: '名称', type: 'string', search: true, data_index: 'name' }],
                import_export_headers: [{ _id: '1645323055453_2' }],
                display_configurable_form: {},
              },
              model_key: 'user_id',
              conditions: [],
              model_key_prefix: '',
            },
          ],
          model_key: 'user_type',
          complex_condition: { groups: [] },
        },
      ],
      model_key_prefix: '',
    },
    {
      name: '自定义显示名称',
      icon: 'FolderOutlined',
      type: 'input',
      rules: [],
      model: {
        attr_type: 'string',
      },
      options: {
        span: 24,
      },
      key: 'input_1661165988229_2',
      model_key: 'user_name',
      fields: [],
      conditions: [],
      model_key_prefix: '',
    },
    {
      key: 'api_single_1644655768117_5',
      name: '部门',
      type: 'api_single',
      model: {
        attr_type: 'number',
      },
      rules: [],
      fields: [],
      options: {
        path: '/res/member/departments',
        span: 24,
        attrs: ['short_name'],
        display: 'tag',
        ransack: '{ "org_id_eq": ${org_id} }',
        multiple: false,
        table_items: [
          {
            name: '部门名称',
            type: 'string',
            search: true,
            data_index: 'name',
          },
          {
            name: '部门简称',
            type: 'string',
            search: true,
            data_index: 'short_name',
          },
        ],
        import_export_headers: [
          {
            _id: '1648017145242_6',
          },
        ],
        display_configurable_form: {},
      },
      model_key: 'model_payload.department_id',
      conditions: [],
      model_key_prefix: '',
    },
    {
      name: 'model_payload',
      type: 'key_layout',
      options: {
        span: 24,
      },
      key: 'key_layout_1645523516938_0',
      model_key: 'model_payload',
      fields: [
        {
          name: '文件',
          type: 'file',
          rules: [],
          model: {
            attr_type: 'array',
          },
          options: {
            span: 24,
            multiple: true,
          },
          key: 'file_1645523555048_1',
          model_key: 'attachments',
          fields: [],
          conditions: [],
          model_key_prefix: 'model_payload',
        },
      ],
      conditions: [],
      model_key_prefix: '',
    },
  ],
  actions: [
    { key: 'create', enabled: true },
    { key: 'update', enabled: true },
    { key: 'delete', enabled: true },
    { key: 'import', enabled: false },
    { key: 'export', enabled: false },
  ],
  options: {
    label: {},
    theme: { card: {}, form: {}, background: {} },
    layout: 'horizontal',
    create_text: '提交',
    update_text: '提交',
  },
  column_attributes: [
    { _id: 'input_1645323889985_15', title: ['序号'], dataIndex: 'position' },
    { _id: 'input_1645323889985_16', title: ['考核对象'], dataIndex: 'assessment_user_info.name' },
    {
      _id: 'input_1645323889985_17',
      title: ['部门'],
      dataIndex: 'assessment_user_info.department_names',
    },
    { _id: 'input_1645323889985_18', title: ['组织'], dataIndex: 'assessment_user_info.org_names' },
    ...(catalogEnabled
      ? [{ _id: 'input_1645323889985_20', title: ['考核分类'], dataIndex: 'catalog.name' }]
      : []),
    ...(groupEnabled
      ? [{ _id: 'input_1645323889985_21', title: ['考核分组'], dataIndex: 'group.name' }]
      : []),
    {
      _id: 'input_1645323889985_19',
      title: ['附件'],
      dataIndex: 'model_payload.attachments',
      render: (value: IFile[]) => value?.length || 0,
    },
  ],
  model_key: 'layout_45694184066',
});
