<script lang="ts">
import { ref, defineComponent, toRefs } from 'vue';
import { VStore, VModel } from '@/lib/vails';
import { AssessmentUserEntriedEntriesApi } from '@/engines/assessment/assessment-core/apis/assessment/user/entried/entries.api';

const ComAssessmentActivitySelfEntryForm = defineComponent({
  name: 'ComAssessmentActivitySelfEntryForm',
  components: {},
  props: {
    activity: { type: Object, required: true },
  },
  setup(props) {
    const entryStore = new VStore(
      new AssessmentUserEntriedEntriesApi({ params: { q: { activity_id_eq: props.activity.id } } }),
    );

    const editRecord = ref(new VModel(props.activity.entry, entryStore));

    if (!editRecord.value.formData.payload) {
      editRecord.value.formData.payload = {};
    }

    const enabledRef = ref<any>(null);

    return {
      ...toRefs(props),
      editRecord,
      enabledRef,
      entryStore,
    };
  },
});
export default ComAssessmentActivitySelfEntryForm;
</script>

<template lang="pug">
.com-assessment-activity-self-entry-form
  TaTemplateFormWithActions(
    :template='activity.entry_form',
    v-model:modelValue='editRecord.formData.payload',
    :record='editRecord',
    :disabled='!enabledRef'
    @afterSave='() => activity.fetch()'
  )

  TaPolicyResource(
    :store='entryStore',
    :resource_id='editRecord.id',
    actionKey='update',
  )
    .enabled(ref='enabledRef')
</template>

<style lang="stylus" scoped>
.com-assessment-activity-self-entry-form
  >>> .cancel
    display none
</style>
