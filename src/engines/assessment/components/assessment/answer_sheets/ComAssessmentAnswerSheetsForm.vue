<script lang="ts">
import { ref, defineComponent, toRefs, computed, watch } from 'vue';
import { AssessmentQuestionSheetFormItem } from '@/engines/assessment/assessment-core/types/model';
import { AssessmentQuestionSettingModel } from '@/engines/assessment/assessment-core/models/assessment/question_settings';
import { VObject } from '@/lib/vails';
import { cloneDeep, isObjectLike, forOwn } from 'lodash';
import ComAssessmentUserQuestionSheetsShow from '../question_sheets/ComAssessmentUserQuestionSheetsShow.vue';
import {
  insertJsonata,
  jsonataGet,
} from '@/components/global/ta-component/ta-template-form-core/useJsonata';

const ComAssessmentAnswerSheetsForm = defineComponent({
  name: 'ComAssessmentAnswerSheetsForm',
  components: { ComAssessmentUserQuestionSheetsShow },
  props: {
    store: { type: Object, required: true },
    questionSettingFlag2Record: { type: Object, required: true },
    disabled: { type: Bo<PERSON>an, default: false },
  },
  setup(props) {
    const editRecord = ref<VObject>({});

    const deepFreeze = (object: VObject) => {
      const result = cloneDeep(object);
      if (!isObjectLike(result)) {
        return;
      }

      Object.freeze(result);

      forOwn(result, function (value) {
        if (!isObjectLike(value) || Object.isFrozen(value)) {
          return;
        }

        deepFreeze(value);
      });

      return result;
    };

    watch(
      () => props.store.record.value.id,
      () => {
        if (props.store.record.value.id) {
          editRecord.value = props.store.record.value;
        } else {
          editRecord.value = props.store.new({});
        }
      },
      { immediate: true },
    );

    const getTemplate = (question: AssessmentQuestionSheetFormItem) => {
      return AssessmentQuestionSettingModel.insertConfigToTemplate(
        props.questionSettingFlag2Record[question.type],
        question.config,
        question.name,
      );
    };

    const insertQuestionName = (name: string) => {
      return insertJsonata(name, (dataKey: string) =>
        jsonataGet({ questionSheet: props.store.record.value.question_sheet }, dataKey),
      );
    };

    const isRequired = (question: AssessmentQuestionSheetFormItem) => {
      return AssessmentQuestionSettingModel.isRequired(question.config);
    };

    const triggerDefaultValue = (key: string) => {
      if (!editRecord.value.formData.payload) editRecord.value.formData.payload = {};
      if (!editRecord.value.formData.payload[key]) editRecord.value.formData.payload[key] = {};
      return true;
    };

    const formRefMap = ref<VObject>({});

    const questionSheetFakeStore = computed(() => ({
      record: ref(props.store.record.value.questionSheetModel),
    }));

    return {
      ...toRefs(props),
      getTemplate,
      isRequired,
      triggerDefaultValue,
      editRecord,
      formRefMap,
      deepFreeze,
      insertQuestionName,

      questionSheetFakeStore,
      record: props.store.record,
    };
  },
});
export default ComAssessmentAnswerSheetsForm;
</script>

<template lang="pug">
.com-assessment-answer-sheets-form.h-full
  ComAssessmentUserQuestionSheetsShow.h-full(
    :store='questionSheetFakeStore',
    :answerSheetRecord='record',
    :needAnswer='false',
    :disabled='true',
  )
  //- .questions.px-6
  //-   .question-item.relative.py-2(
  //-     v-for='(question, index) in editRecord.questionSheetModel.form?.fields'
  //-   )
  //-     .text-sm.font-regular.text-gray-700.transition-all.py-1
  //-       span.text-red-600(v-if='isRequired(question)') *&nbsp
  //-       span.no {{ index + 1 }}.&nbsp
  //-       span.name {{ insertQuestionName(question.name) }}
  //-     template(v-if='triggerDefaultValue(question._id)',)
  //-       TaTemplateForm.w-full.question-form(
  //-         v-if='disabled'
  //-         :modelValue='deepFreeze(editRecord.formData.payload[question._id])',
  //-         :template='getTemplate(question)'
  //-       )
  //-       TaTemplateForm.w-full.question-form(
  //-         v-else,
  //-         :ref='(el) => (formRefMap[question._id] = el)',
  //-         v-model:modelValue='editRecord.formData.payload[question._id]',
  //-         :template='getTemplate(question)'
  //-       )

</template>

<style lang="stylus" scoped></style>
