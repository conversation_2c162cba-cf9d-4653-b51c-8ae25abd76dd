<script lang="ts">
import { defineComponent, toRefs, PropType, computed, ref } from 'vue';
import { TaBreadcrumbInterface } from '@/components/global/ta-component/TaBreadcrumb.vue';

import ComAssessmentQuestionSheetFormCatalogsList from '../question_sheets/ComAssessmentQuestionSheetFormCatalogsList.vue';
import ComAssessmentQuestionSheetFormQuestionItem from '../question_sheets/ComAssessmentQuestionSheetFormQuestionItem.vue';
import { VObject } from '../../../../../lib/vails/model/index';
import scrollIntoView from 'smooth-scroll-into-view-if-needed';
import { message } from 'ant-design-vue';
import {
  useAssessmentQuestionSheet,
  AssessmentQuestionSheetItemInterface,
  AssessmentQuestionSheetCatalogInterface,
  isAnswerEqual,
} from '../../../assessment-core/useAssessmentQuestionSheet';

const ComAssessmentManageAnswerSheetsShow = defineComponent({
  name: 'ComAssessmentManageAnswerSheetsShow',
  components: {
    ComAssessmentQuestionSheetFormCatalogsList,
    ComAssessmentQuestionSheetFormQuestionItem,
  },
  props: {
    store: { type: Object, required: true },
    questionSheetStore: { type: Object, required: true },
    breadcrumbs: { type: Array as PropType<TaBreadcrumbInterface[]>, default: () => [] },
  },
  emits: [],
  setup(props) {
    const tabs = [
      {
        key: 'form',
        label: '评分',
      },
    ];

    const {
      insertQuestionSheet,
      descAry,
      noCatalogFields,
      noCatalog,
      getTemplate,
      isRequired,
      questionSettingFlag2Record,
      loading,
      fetchQuestionSettings,
      calcIndexAry,
    } = useAssessmentQuestionSheet(props.questionSheetStore.record);

    fetchQuestionSettings();

    const totalScoreStatByItemPayload = computed(() => {
      const result: {
        score: number;
        catalogs: Record<string, { score: number; weight: number }>;
      } = {
        score: 0,
        catalogs: {},
      };

      props.questionSheetStore.record.value.formData.form?.fields.forEach(
        (field: AssessmentQuestionSheetItemInterface) => {
          if (field._id in props.store.record.value.formData.item_payload) {
            result.score += props.store.record.value.formData.item_payload[field._id];
          }
        },
      );

      props.questionSheetStore.record.value.formData.form?.catalogs?.forEach(
        (catalog: AssessmentQuestionSheetCatalogInterface) => {
          const catalogScore = catalog.items?.reduce(
            (out: number, question: AssessmentQuestionSheetItemInterface) => {
              if (question._id in props.store.record.value.formData.item_payload) {
                return out + props.store.record.value.formData.item_payload[question._id];
              } else {
                return out;
              }
            },
            0,
          );

          const catalogWeight = catalog.weight || 1;

          result.catalogs[catalog._id] = {
            score: catalogScore,
            weight: catalogWeight,
          };

          result.score += catalogScore * catalogWeight;
        },
      );

      return result;
    });

    const questionRefMap = ref<VObject>({});

    const onClickQuestion = (question: AssessmentQuestionSheetItemInterface) => {
      const node = questionRefMap.value[question._id].$el;
      if (node) {
        scrollIntoView(node, {
          scrollMode: 'if-needed',
          block: 'nearest',
          behavior: 'smooth',
        });
      }
    };

    const onSave = () => {
      props.store.record.value
        .save()
        .then(() => {
          message.success('调整分数成功');
        })
        .catch(() => {
          message.error('调整分数失败');
        });
    };

    return {
      ...toRefs(props),
      record: props.store.record,
      questionSheet: props.questionSheetStore.record,
      tabs,
      isAnswerEqual,
      insertQuestionSheet,
      descAry,
      noCatalogFields,
      noCatalog,
      getTemplate,
      isRequired,
      questionSettingFlag2Record,
      loading,
      fetchQuestionSettings,
      totalScoreStatByItemPayload,
      questionRefMap,
      onClickQuestion,
      onSave,
      calcIndexAry,
    };
  },
});
export default ComAssessmentManageAnswerSheetsShow;
</script>

<template lang="pug">
.com-assessment-manage-answer-sheets-show.h-full.w-full
  TaShowLayout(
    :tabs='tabs',
    :title='record.user?.name',
    :store='store',
    :breadcrumbs='breadcrumbs',
  )
    template(#form_tab)
      .com-assessment-user-question-sheets-show.h-full.w-full.flex.flex-col
        .flex.space-x-4.flex-grow.h-0.overflow-hidden
          ComAssessmentQuestionSheetFormCatalogsList.overflow-auto(
            :record='questionSheet',
            :noCatalog='noCatalog',
            @clickQuestion='onClickQuestion'
          )
            template(#catalog-right='{ catalog }')
              //- .stat {{ catalogId2Stat[noCatalog._id] }}
            template(#question-right='{ question }')
              TaIcon.text-red-500(
                :type='isAnswerEqual(record.payload[question._id], question) ? `CheckCircleFilled` : `CloseCircleFilled`'
                :class='{ "text-green-500": isAnswerEqual(record.payload[question._id], question) }'
              )

          .question-sheet-form-items.flex-grow.w-0.h-full.flex.flex-col.overflow-auto
            .cover-image.flex-shrink-0.h-20.rounded-xl.flex.flex-col.justify-center.items-start.px-3.mb-3(
              :style='{ background: `url(${questionSheet.coverImageUrl}) center / cover no-repeat` }'
            )
              .text-base.font-medium.text-white.mb-1 {{ insertQuestionSheet(questionSheet.name) }}
              .text-xs.font-medium.text-white {{ questionSheet.model_payload?.副标题 ? insertQuestionSheet(questionSheet.model_payload?.副标题) : '调查问卷' }}
            .desc.text-base.mb-3.flex-shrink-0(v-if='descAry.length > 0')
              p(v-for='t in descAry') {{ t }}

            .total-score.text-base.font-medium.text-gray-900
              .flex.items-center
                .prefix.w-2.h-8.bg-primary.mr-2
                | 总分：{{ totalScoreStatByItemPayload.score.toFixed(2) }}分

            .questions.flex-grow.h-0(v-if='!loading')
              ComAssessmentQuestionSheetFormQuestionItem.question-item.py-4(
                v-for='(question, index) in questionSheet.form?.fields'
                :ref='(el) => (questionRefMap[question._id] = el)',
                :value='record.formData.payload[question._id]'
                :catalog='noCatalog',
                :question='question',
                :noAry='calcIndexAry(question, questionsheet.form.fields[index - 1])',
                :disabled='true',
                :showAnswer='true',
                :insertQuestionSheet='insertQuestionSheet',
                :isRequired='isRequired',
                :getTemplate='getTemplate',
              )
                template(#name-right='{ question }')
                  .flex.items-center
                    a-input-number.w-20(v-model:value='record.formData.item_payload[question._id]', :min='0', :max='question.score')
                    | &nbsp/&nbsp
                    span {{ question.score }}分

              template(v-for='catalog in questionSheet.form?.catalogs')
                .catalog-in-content.flex.items-center.justify-between
                  .flex.items-center
                    .prefix.w-2.h-8.bg-primary.mr-2
                    .text-gray-900.font-medium.text-base {{ catalog.name }}
                  .px-4.text-sm.text-gray-600(v-if='catalog.weight && catalog.weight !== 1')
                    //- div(v-if='!showAnswer') 权重：{{ (catalog.weight * 100).toFixed(2) }}%
                    div 分数 {{ totalScoreStatByItemPayload.catalogs?.[catalog._id]?.score?.toFixed(2) }} * {{ catalog.weight * 100 }}% = {{ (totalScoreStatByItemPayload.catalogs?.[catalog._id]?.score * catalog.weight).toFixed(2) }}分
                //- ComBpmInstanceCollapse(
                //-   v-model:collapsed='catalogId2Collapsed[catalog._id]'
                //-   :header='catalog.name',
                //-   :defaultCollapsed='false',
                //-   :closeClone='true'
                //- )
                ComAssessmentQuestionSheetFormQuestionItem.question-item.py-4(
                  v-for='(question, index) in catalog.items'
                  :ref='(el) => (questionRefMap[question._id] = el)',
                  :value='record.formData.payload[question._id]'
                  :question='question',
                  :catalog='catalog',
                  :noAry='calcIndexAry(question, catalog.items[index - 1])',
                  :showAnswer='true',
                  :disabled='true',
                  :insertQuestionSheet='insertQuestionSheet',
                  :isRequired='isRequired',
                  :getTemplate='getTemplate',
                )
                  template(#name-right='{ question }')
                    .flex.items-center
                      a-input-number.w-20(v-model:value='record.formData.item_payload[question._id]', :min='0', :max='question.score')
                      | &nbsp/&nbsp
                      span {{ question.score }}分
        .actions.flex-shrink-0.px-3.py-2.flex.item-center.justify-end
          a-button(
            type='primary',
            :loading='loading',
            @click='onSave'
          ) 提交分数调整
</template>

<style lang="stylus" scoped>
.com-assessment-manage-answer-sheets-show
  height 100%
  .bg-primary
    background-color $primary-color
</style>
