<script lang="ts">
import { computed, defineComponent, toRefs } from 'vue';

const ComAssessmentUserAnswerSheetsIndex = defineComponent({
  name: 'ComAssessmentUserAnswerSheetsIndex',
  components: {},
  props: {
    store: { type: Object, required: true },
    questionSettingStore: { type: Object, required: true },
  },
  setup(props) {
    const config = computed(() => ({
      recordName: '我的答卷',
      store: props.store,
      // pagination: {
      //   // perPage: 999999,
      //   hideOnSinglePage: true,
      // },
      template: 'assessment_answer_sheet',
      detail: {
        mode: 'route',
      },
      mode: 'table',
      // editApi:
      // showCount: true,
      // actions: [
      //  { key: 'create', enabled: true },
      //  { key: 'update', enabled: true },
      //  { key: 'delete', enabled: true },
      //  { key: 'import', enabled: true },
      //  { key: 'export', enabled: true },
      // ],
      // table: {
      //   scroll: { y: '70vh' },
      //   // columns: [{ dataIndex: 'id', title: 'ID' }],
      // },
      // selection: {
      //   showByDefault: false;
      //   showSwitch: false;
      // };
      // searcherSimpleOptions: [{key: 'title', label: '标题', type: 'string' }],
    }));

    return {
      ...toRefs(props),
      config,
    };
  },
});

export default ComAssessmentUserAnswerSheetsIndex;
</script>

<template lang="pug">
.com-assessment-user-answer-sheets-index
  TaIndexView(:config='config')
</template>

<style lang="stylus" scoped>
.com-assessment-user-answer-sheets-index
  height 100%
  width 100%
</style>
