<script lang="ts">
import { defineComponent, toRefs, PropType } from 'vue';
import { TaBreadcrumbInterface } from '@/components/global/ta-component/TaBreadcrumb.vue';
import ComAssessmentUserQuestionSheetsShow from '../question_sheets/ComAssessmentUserQuestionSheetsShow.vue';

const ComAssessmentUserAnswerSheetsShow = defineComponent({
  name: 'ComAssessmentUserAnswerSheetsShow',
  components: {
    ComAssessmentUserQuestionSheetsShow,
  },
  props: {
    store: { type: Object, required: true },
    questionSheetStore: { type: Object, required: true },
    answerSheetRecord: { type: Object, default: () => ({}) },
    breadcrumbs: { type: Array as PropType<TaBreadcrumbInterface[]>, default: () => [] },
  },
  emits: ['afterDelete', 'afterExtend'],
  setup(props) {
    return {
      ...toRefs(props),
      record: props.store.record,
    };
  },
});
export default ComAssessmentUserAnswerSheetsShow;
</script>

<template lang="pug">
.com-assessment-user-answer-sheets-show
  ComAssessmentUserQuestionSheetsShow(
    :store='questionSheetStore',
    :answerSheetRecord='record',
    :disabled="true",
    backUrl='/assessment/user/answer_sheets',
  )
</template>

<style lang="stylus" scoped>
.com-assessment-user-answer-sheets-show
  height 100%
</style>
