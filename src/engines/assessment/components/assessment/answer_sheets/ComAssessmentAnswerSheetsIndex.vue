<script lang="ts">
import { AssessmentQuestionSetting } from '@/engines/assessment/assessment-core/types/model';
import { VObject } from '@/lib/vails/model';
import { computed, defineComponent, ref, toRefs } from 'vue';
import ComAssessmentAnswerSheetsForm from './ComAssessmentAnswerSheetsForm.vue';
import ComAssessmentAnswerSheetsShow from './ComAssessmentAnswerSheetsShow.vue';

const ComAssessmentAnswerSheetsIndex = defineComponent({
  name: 'ComAssessmentAnswerSheetsIndex',
  components: {
    ComAssessmentAnswerSheetsShow,
    ComAssessmentAnswerSheetsForm,
  },
  props: {
    store: { type: Object, required: true },
    questionSettingStore: { type: Object, required: true },
  },
  setup(props) {
    const config = computed(() => ({
      recordName: '答卷',
      store: props.store,
      // pagination: {
      //   // perPage: 999999,
      //   hideOnSinglePage: true,
      // },
      template: 'assessment_answer_sheet',
      detail: {
        mode: 'drawer',
      },
      mode: 'table',
      // editApi:
      // showCount: true,
      actions: [
        { key: 'create', enabled: false },
        { key: 'update', enabled: false },
        { key: 'delete', enabled: false },
        { key: 'import', enabled: false },
        { key: 'export', enabled: true },
      ],
      // table: {
      //   scroll: { y: '70vh' },
      //   // columns: [{ dataIndex: 'id', title: 'ID' }],
      // },
      // selection: {
      //   showByDefault: false;
      //   showSwitch: false;
      // };
      // searcherSimpleOptions: [{key: 'title', label: '标题', type: 'string' }],
    }));

    const questionSettingFlag2Record = ref<VObject>({});

    const loading = ref(true);
    props.questionSettingStore.index({ per_page: 15 }).then(() => {
      props.questionSettingStore.records.value.forEach(
        (questionSetting: AssessmentQuestionSetting) => {
          questionSettingFlag2Record.value[questionSetting.flag] = questionSetting;
        },
      );
      loading.value = false;
    });

    return {
      ...toRefs(props),
      config,
      questionSettingFlag2Record,
      loading,
    };
  },
});

export default ComAssessmentAnswerSheetsIndex;
</script>

<template lang="pug">
.com-assessment-manage-answer-sheets-index
  TaIndexView(:config='config')
    template(#header)
      .empty
    //- template(#table)
    //-   a-table-column(:autoHeight='true' title='届' dataIndex='')
    //-   a-table-column(:autoHeight='true' title='轮', dataIndex='')
    //-   a-table-column(:autoHeight='true' title='被巡单位', dataIndex='')
    //-   a-table-column(:autoHeight='true' title='开始结束时间')
    //-     template(#default='{ record }')
    //-       | {{ record.questionSheetModel.timeRangeStr }}
    //-   a-table-column(:autoHeight='true' title='问卷名称', :dataIndex='["question_sheet", "name"]')
    //-     template(#default='{ text, record }')
    //-       ComAssessmentQuestionSheetInserted(:value='text', :record='record.question_sheet')
    //-   a-table-column(:autoHeight='true' title='关联问卷', dataIndex='')
    //-   a-table-column(:autoHeight='true' title='填写时间', dataIndex='createdAtStr')
    template(#detail='{ record }')
      .detail.h-full.flex.flex-col
        .created-at.p-6.flex.items-center.space-x-2
          .point.bg-primary.h-2.w-2.rounded-full
          .text-sm.font-regular.text-gray-800 提交时间：{{ record.createdAtStr }}

        ComAssessmentAnswerSheetsForm.flex-grow.h-0(
          v-if='!loading',
          :store='store',
          :questionSettingFlag2Record='questionSettingFlag2Record',
          :disabled='true'
        )
</template>

<style lang="stylus" scoped>
.com-assessment-manage-answer-sheets-index
  height 100%
  width 100%
</style>
