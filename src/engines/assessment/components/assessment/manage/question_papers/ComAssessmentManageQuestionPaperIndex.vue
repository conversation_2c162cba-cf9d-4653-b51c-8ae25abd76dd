<script lang="ts">
import { defineComponent, computed, toRefs } from 'vue';

export default defineComponent({
  name: 'ComAssessmentManageQuestionPaperIndex',
  components: {},
  props: {
    store: { type: Object, required: true },
  },
  setup(props) {
    const config = computed(() => ({
      recordName: '试卷管理',
      store: props.store,
      template: 'assessment_question_paper',
      detail: {
        mode: 'drawer',
      },
      mode: 'table',
      // editApi:
      // showCount: true,
      // actions: [
      //  { key: 'create', enabled: true },
      //  { key: 'update', enabled: true },
      //  { key: 'delete', enabled: true },
      //  { key: 'import', enabled: true },
      //  { key: 'export', enabled: true },
      // ],
      table: {
        scroll: { y: 'auto' },
        // columns: [{ dataIndex: 'id', title: 'ID' }],
      },
      // selection: {
      //   showByDefault: false;
      //   showSwitch: false;
      // };
      // searcherSimpleOptions: [{key: 'title', label: '标题', type: 'string' }],
    }));

    return {
      ...toRefs(props),
      config,
    };
  },
});
</script>

<template lang="pug">
.com-assessment-manage-question-paper-index.w-full.h-full
  TaIndexView(:config='config')
</template>
