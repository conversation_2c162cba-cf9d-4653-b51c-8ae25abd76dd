<script lang="ts">
import { defineComponent, toRefs, PropType } from 'vue';
import { TaBreadcrumbInterface } from '@/components/global/ta-component/TaBreadcrumb.vue';
import ComAssessmentManageQuestionIndex from '../questions/ComAssessmentManageQuestionIndex.vue';
import { VStore } from '@/lib/vails';
import { AssessmentManageQuestionsApi } from '@/engines/assessment/apis/assessment/manage/questions.api';
import { AssessmentQuestionModel } from '@/engines/assessment/assessment-core/models/assessment/questions';

const ComAssessmentManageQuestionBankShow = defineComponent({
  name: 'ComAssessmentManageQuestionBankShow',
  components: { ComAssessmentManageQuestionIndex },
  props: {
    store: { type: Object, required: true },
    extendRoute: { type: String, default: '' },
    editable: { type: Boolean, default: true },
    breadcrumbs: { type: Array as PropType<TaBreadcrumbInterface[]>, default: () => [] },
  },
  emits: ['afterDelete', 'afterExtend'],
  setup(props, { emit }) {
    const tabs = [
      {
        key: 'info',
        label: '基本信息',
      },
      {
        key: 'questions',
        label: '题目',
      },
    ];

    const afterDelete = () => {
      emit('afterDelete');
    };

    const afterExtend = () => {
      emit('afterExtend');
    };

    const questionStore = new VStore(
      new AssessmentManageQuestionsApi({
        parents: [{ type: 'question_banks', id: props.store.record.value?.id }],
        params: { q: { parent_id_null: 1 } },
      }),
      AssessmentQuestionModel,
    );

    return {
      ...toRefs(props),
      tabs,
      record: props.store.record,
      afterDelete,
      afterExtend,

      questionStore,
    };
  },
});
export default ComAssessmentManageQuestionBankShow;
</script>

<template lang="pug">
.com-assessment-manage-question-bank-show
  TaShowLayout(
    :tabs='tabs',
    :title='record.name',
    :store='store',
    :extendRoute='extendRoute',
    :editable='editable',
    :breadcrumbs='breadcrumbs',
    template='assessment_question_bank',
    @afterDelete='afterDelete',
    @afterExtend='afterExtend'
  )
    template(#questions_tab)
      ComAssessmentManageQuestionIndex(:store='questionStore', :questionBank='record')
</template>

<style lang="stylus" scoped>
.com-assessment-manage-question-bank-show
  height 100%
</style>
