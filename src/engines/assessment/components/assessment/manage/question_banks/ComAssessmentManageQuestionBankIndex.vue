<script lang="ts">
import { defineComponent, computed, toRefs } from 'vue';
import ComAssessmentManageQuestionBanksShow from './ComAssessmentManageQuestionBankShow.vue';

export default defineComponent({
  name: 'ComAssessmentManageQuestionBankIndex',
  components: { ComAssessmentManageQuestionBanksShow },
  props: {
    store: { type: Object, required: true },
  },
  setup(props) {
    const config = computed(() => ({
      recordName: '题库管理',
      store: props.store,
      template: 'assessment_question_bank',
      detail: {
        mode: 'route',
      },
      mode: 'table',
      // editApi:
      // showCount: true,
      // actions: [
      //  { key: 'create', enabled: true },
      //  { key: 'update', enabled: true },
      //  { key: 'delete', enabled: true },
      //  { key: 'import', enabled: true },
      //  { key: 'export', enabled: true },
      // ],
      table: {
        scroll: { y: 'auto' },
        // columns: [{ dataIndex: 'id', title: 'ID' }],
      },
      // selection: {
      //   showByDefault: false;
      //   showSwitch: false;
      // };
      // searcherSimpleOptions: [{key: 'title', label: '标题', type: 'string' }],
    }));

    return {
      ...toRefs(props),
      config,
    };
  },
});
</script>

<template lang="pug">
.com-assessment-manage-question-bank-index.w-full.h-full
  TaIndexView(:config='config')
    template(#detail='{ record, onClose }')
      ComAssessmentManageQuestionBanksShow(
        v-if='record.id'
        :store='store'
        :extendRoute='`/assessment/manage/question_banks/${record.id}`'
        :editable='editable'
        @afterDelete='onClose'
        @afterExtend='onClose'
      )

</template>
