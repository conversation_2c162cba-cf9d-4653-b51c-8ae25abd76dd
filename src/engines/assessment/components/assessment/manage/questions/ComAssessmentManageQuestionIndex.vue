<script lang="ts">
import { VObject } from '@/lib/vails';
import { defineComponent, computed, toRefs } from 'vue';
import ComAssessmentManageQuestionShow from './ComAssessmentManageQuestionShow.vue';

export default defineComponent({
  name: 'ComAssessmentManageQuestionIndex',
  components: { ComAssessmentManageQuestionShow },
  props: {
    store: { type: Object, required: true },
    questionBank: { type: Object, required: true },
  },
  setup(props) {
    const config = computed(() => ({
      recordName: '题目管理',
      store: props.store,
      template: 'assessment_question',
      formDataEncode: (payload: VObject) => {
        return { ...payload, question_bank_id: props.questionBank.id };
      },
      detail: {
        mode: 'drawer',
      },
      mode: 'table',
      // editApi:
      // showCount: true,
      // actions: [
      //  { key: 'create', enabled: true },
      //  { key: 'update', enabled: true },
      //  { key: 'delete', enabled: true },
      //  { key: 'import', enabled: true },
      //  { key: 'export', enabled: true },
      // ],
      table: {
        scroll: { y: 'auto' },
        // columns: [{ dataIndex: 'id', title: 'ID' }],
      },
      // selection: {
      //   showByDefault: false;
      //   showSwitch: false;
      // };
      searcherSimpleOptions: [{ key: 'name', label: '题干', type: 'string' }],
    }));

    return {
      ...toRefs(props),
      config,
    };
  },
});
</script>

<template lang="pug">
.com-assessment-manage-question-index.w-full.h-full
  TaIndexView(:config='config')
    template(#detail='{ record, onClose }')
      ComAssessmentManageQuestionShow(
        v-if='record.id'
        :store='store'
        :editable='editable'
        @afterDelete='onClose'
        @afterExtend='onClose'
      )
</template>
