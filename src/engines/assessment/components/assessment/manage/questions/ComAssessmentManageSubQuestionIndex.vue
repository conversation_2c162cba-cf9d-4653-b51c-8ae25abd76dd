<script lang="ts">
import { VObject } from '@/lib/vails';
import { defineComponent, computed, toRefs } from 'vue';

export default defineComponent({
  name: 'ComAssessmentManageSubQuestionIndex',
  props: {
    store: { type: Object, required: true },
    parent: { type: Object, required: true },
  },
  setup(props) {
    const config = computed(() => ({
      recordName: '子题目管理',
      store: props.store,
      template: 'assessment_question#children',
      formDataEncode: (payload: VObject) => {
        return {
          ...payload,
          question_bank_id: props.parent.question_bank_id,
          parent_id: props.parent.id,
        };
      },
      detail: {
        mode: 'drawer',
      },
      mode: 'table',
      // editApi:
      // showCount: true,
      // actions: [
      //  { key: 'create', enabled: true },
      //  { key: 'update', enabled: true },
      //  { key: 'delete', enabled: true },
      //  { key: 'import', enabled: true },
      //  { key: 'export', enabled: true },
      // ],
      table: {
        scroll: { y: 'auto' },
        // columns: [{ dataIndex: 'id', title: 'ID' }],
      },
      // selection: {
      //   showByDefault: false;
      //   showSwitch: false;
      // };
      searcherSimpleOptions: [{ key: 'name', label: '题干', type: 'string' }],
    }));

    return {
      ...toRefs(props),
      config,
    };
  },
});
</script>

<template lang="pug">
.com-assessment-manage-sub-question-index.w-full.h-full
  TaIndexView(:config='config')
</template>
