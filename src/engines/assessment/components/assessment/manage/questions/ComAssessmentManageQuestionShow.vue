<script lang="ts">
import { defineComponent, toRefs, PropType, computed } from 'vue';
import { TaBreadcrumbInterface } from '@/components/global/ta-component/TaBreadcrumb.vue';
import ComAssessmentManageSubQuestionIndex from '../questions/ComAssessmentManageSubQuestionIndex.vue';
import { VStore } from '@/lib/vails';
import { AssessmentManageQuestionsApi } from '@/engines/assessment/apis/assessment/manage/questions.api';
import { AssessmentQuestionModel } from '@/engines/assessment/assessment-core/models/assessment/questions';

const ComAssessmentManageQuestionShow = defineComponent({
  name: 'ComAssessmentManageQuestionShow',
  components: { ComAssessmentManageSubQuestionIndex },
  props: {
    store: { type: Object, required: true },
    extendRoute: { type: String, default: '' },
    editable: { type: Boolean, default: true },
    breadcrumbs: { type: Array as PropType<TaBreadcrumbInterface[]>, default: () => [] },
  },
  emits: ['afterDelete', 'afterExtend'],
  setup(props, { emit }) {
    const tabs = computed(() => [
      {
        key: 'info',
        label: '基本信息',
      },
      ...(props.store.record.value?.kind == 'Assessment::Attr::Material'
        ? [
            {
              key: 'children',
              label: '子题目',
            },
          ]
        : []),
    ]);

    const afterDelete = () => {
      emit('afterDelete');
    };

    const afterExtend = () => {
      emit('afterExtend');
    };

    const questionStore = new VStore(
      new AssessmentManageQuestionsApi({
        parents: [{ type: 'question_banks', id: props.store.record.value?.question_bank_id }],
        params: { q: { parent_id_eq: props.store.record.value?.id } },
      }),
      AssessmentQuestionModel,
    );

    return {
      ...toRefs(props),
      tabs,
      record: props.store.record,
      afterDelete,
      afterExtend,

      questionStore,
    };
  },
});
export default ComAssessmentManageQuestionShow;
</script>

<template lang="pug">
.com-assessment-manage-question-show
  TaShowLayout(
    :tabs='tabs',
    :title='record.name',
    :store='store',
    :extendRoute='extendRoute',
    :editable='editable',
    :breadcrumbs='breadcrumbs',
    template='assessment_question',
    @afterDelete='afterDelete',
    @afterExtend='afterExtend'
  )
    template(#children_tab)
      ComAssessmentManageSubQuestionIndex(:store='questionStore', :parent='record')
</template>

<style lang="stylus" scoped>
.com-assessment-manage-question-show
  height 100%
</style>
