<script lang="ts">
import { VObject } from '@/lib/vails';
import { defineComponent, toRefs } from 'vue';
import { useRouter } from 'vue-router';

const ComAssessmentScoreUserInfo = defineComponent({
  name: 'ComAssessmentScoreUserInfo',
  components: {},
  props: {
    score: { type: Object, required: true },
  },
  setup(props) {
    const router = useRouter();
    const hideInfo = process.env.VUE_APP_HIDE_ASSESSMENT_ENTRY_INFO || false;
    const linkTo = (score: VObject) => {
      router.push({
        path: '/plan/dalies/mine',
        query: {
          userId: score.entry.assessment_user_info.id,
          userName: score.entry.assessment_user_info.name,
          period: 'year',
        },
      });
    };
    return {
      ...toRefs(props),
      linkTo,
      hideInfo,
    };
  },
});
export default ComAssessmentScoreUserInfo;
</script>

<template lang="pug">
.com-assessment-score-user-info(v-if='score?.assessment_user_info')
  .name(v-if='score.entry.user_name') {{ score.entry.user_name }}
  .info(v-else)
    .flex.items-center.space-x-2
      .name {{ score.entry.assessment_user_info.name }}
      svg.w-6.h-6.cursor-pointer(
        v-if='!hideInfo',
        xmlns='http://www.w3.org/2000/svg',
        fill='none',
        viewBox='0 0 24 24',
        stroke-width='1.5',
        stroke='currentColor',
        @click.stop='linkTo(score)'
      )
        path(
          stroke-linecap='round',
          stroke-linejoin='round',
          d='M12 9v3.75m9-.75a9 9 0 11-18 0 9 9 0 0118 0zm-9 3.75h.008v.008H12v-.008z'
        )

    .info-item(v-if='score?.entry?.assessment_user_info?.duty_names') 岗位: {{ score.entry.assessment_user_info.duty_names?.join(" ") }}
</template>

<style lang="stylus" scoped>
.com-assessment-score-user-info
  background $primary-color
  padding 18px 16px
  color white
  border-radius 4px
  margin-bottom 16px
  .flex
    margin-bottom 26px
    .name
      font-size 16px
      font-weight 500
      color #FFFFFF
      line-height 20px
  .info-item
    margin-right 20px
</style>
