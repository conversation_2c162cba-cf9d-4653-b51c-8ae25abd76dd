<script lang="ts">
import { defineComponent, computed, toRefs, ref } from 'vue';
import { VObject } from '@/lib/vails/model';
import ComAssessmentScoresShow from './ComAssessmentScoresShow.vue';
import ComAssessmentScoreCard from './ComAssessmentScoreCard.vue';
import ComAssessmentScoreForm from './ComAssessmentScoreForm.vue';
import ComAssessmentScoreUserInfo from './ComAssessmentScoreUserInfo.vue';
import { message } from 'ant-design-vue';
import { AssessmentScore } from '@/engines/assessment/assessment-core/types/model';
import usePolicy from '@/components/global/ta-component/ta-template-form-core/usePolicy';

const ComAssessmentScoresUserIndex = defineComponent({
  name: 'ComAssessmentScoresUserIndex',
  components: {
    ComAssessmentScoresShow,
    ComAssessmentScoreCard,
    ComAssessmentScoreForm,
    ComAssessmentScoreUserInfo,
  },
  props: {
    store: { type: Object, required: true },
    activity: { type: Object, required: true },
  },
  emits: ['update:store'],
  setup(props, { emit }) {
    const { reset: resetPolicy } = usePolicy();
    const localStore = computed({
      get: () => props.store,
      set: val => emit('update:store', val),
    });

    const config = computed(() => ({
      recordName: 'recordName',
      store: props.store,
      pagination: {
        perPage: 999999,
        // showPageSizeChanger: false,
        hideOnSinglePage: true,
        // showSizeChanger: false,
      },
      list: {
        scroll: { y: 'auto' },
      },
      // template: 'assessment_score',
      // detail: {
      //   mode: 'auto',
      //   mode: 'drawer',
      //   width: '1100px',
      // },
      // mode: 'table',
      // editApi:
      // showCount: true,
      // actions: [
      //  { key: 'create', enabled: true },
      //  { key: 'update', enabled: true },
      //  { key: 'delete', enabled: true },
      //  { key: 'import', enabled: true },
      //  { key: 'export', enabled: true },
      // ],
      // table: {
      //   scroll: { y: '70vh' },
      //   // columns: [{ dataIndex: 'id', title: 'ID' }],
      // },
      // selection: {
      //   showByDefault: false;
      //   showSwitch: false;
      // };
      // searcherSimpleOptions: [{key: 'title', label: '标题', type: 'string' }],
    }));
    const score_stat_by_template_data = ref({});
    const set_score_stat_by_template = () => {
      score_stat_by_template_data.value =
        props.activity.activity_score_count?.score_stat_by_template.filter(
          (item: { label: string }) => item.label !== null,
        );
    };
    const onIndex = () => {
      set_score_stat_by_template();
      if (props.store.records.value[0]) {
        onShow(props.store.records.value[0]);
      }
    };
    const filters = [
      {
        label: '全部',
        value: 'all',
        query: {},
      },
      {
        label: '已评',
        value: 'score',
        query: { state_eq: 'done' },
      },
      {
        label: '待评',
        value: 'noscore',
        query: { state_eq: 'todo' },
      },
    ];

    const activeFilterOption = ref<VObject>(filters[0]);
    const searcherQuery = ref<VObject>({});

    const taIndexViewTemporaryQuery = computed(() => ({
      ...activeFilterOption.value.query,
      ...searcherQuery.value,
    }));

    const activeScore = ref<Partial<AssessmentScore>>({});

    const onShow = (record: AssessmentScore, customVisibleEdit = false) => {
      resetPolicy();
      return props.store.find(record.id).then(() => {
        if (!props.store.record.value.formData.item_payload) {
          localStore.value.record.value.formData.item_payload = {};
        }
        activeScore.value = props.store.record.value;
        visibleEdit.value = customVisibleEdit || activeScore.value.state === 'todo';
      });
    };

    const visibleEdit = ref(true);

    const scoreForm = ref<any>(null);

    const nextRecord = computed(() => {
      const currentIndex = props.store.records.value.findIndex(
        (record: AssessmentScore) => record.id === activeScore.value.id,
      );

      if (currentIndex === -1) return null;
      return props.store.records.value[currentIndex + 1];
    });

    const onNext = () => {
      if (nextRecord.value) {
        onShow(nextRecord.value, true);
      } else {
        message.warning('已经是最后一个了哦');
      }
    };

    const visibleAttachments = ref(false);

    const reloadActivity = () => props.activity.fetch().then(() => set_score_stat_by_template());

    const visibleEntryForm = ref(false);

    return {
      ...toRefs(props),
      config,
      onIndex,
      onShow,
      searcherQuery,
      filters,
      activeFilterOption,
      activeScore,
      taIndexViewTemporaryQuery,
      visibleEdit,
      scoreForm,
      onNext,
      nextRecord,
      visibleAttachments,
      reloadActivity,
      visibleEntryForm,
      score_stat_by_template_data,
    };
  },
});

export default ComAssessmentScoresUserIndex;
</script>

<template lang="pug">
TaFourCellLayout.com-assessment-manage-scores-index(leftWidth='280px')
  template(#left-header)
    a-dropdown
      .filter.flex.clickable
        | {{ activeFilterOption.label }}
        TaIcon.icon(type='DownOutlined')
      template(#overlay)
        a-menu.filter-menu
          a-menu-item.filter-menu-item(
            v-for='(filter, key) in filters',
            :key='key + 1',
            :style='{ borderTop: key === 3 ? "1px #E8E8E8 solid" : "" }',
            @click='() => (activeFilterOption = filter)'
          )
            .flex-between
              span {{ filter.label }}
              TaIcon.icon(v-if='activeFilterOption.value === filter.value', type='CheckOutlined')

  template(#left-content)
    TaIndexView(
      :config='config',
      :temporaryQuery='taIndexViewTemporaryQuery',
      @onIndex='onIndex',
      @onShow='onShow'
    )
      template(#header)
        .empty
      template(#card='{ record }')
        ComAssessmentScoreCard(:score='record', :isActive='record.id === activeScore.id')

  template(#right-header)
    .left-header.flex.items-center.flex-shrink-0.space-x-2
      a-button(
        v-if='activeScore.entry_form && activeScore.entry_payload',
        @click='() => (visibleEntryForm = true)'
      )
        | 查看自评

      .tags.flex
        TaTag.ml-8(
          v-if='activity.activity_score_count?.score_count?.todo',
          icon='ExclamationCircleFilled',
          color='#3da8f5',
          background='rgba(240, 251, 254)'
        )
          | 待考核{{ activity.activity_score_count.score_count.todo }}人
        TaTag.ml-8(
          v-if='activity.activity_score_count?.score_count?.done',
          icon='CheckCircleFilled',
          color='rgba(74, 198, 117)',
          background='rgba(238, 249, 241)'
        )
          | 已考核{{ activity.activity_score_count.score_count.done }}人
      .score_label.flex.items-center(v-for="item in score_stat_by_template_data")
        .label {{ item.label }}
        .count :{{item.count}}
        .percentage ({{item.percentage}}%)
    .right-header.flex-end
      a-tooltip(
        title='查看自评附件',
        v-if='activeScore.entry?.model_payload?.attachments && activeScore.entry.model_payload.attachments.length > 0'
      )
        .attachment.clickable(@click='() => (visibleAttachments = true)')
          TaIcon.mr-4(type='LinkOutlined')
          span 附件{{ activeScore.entry.model_payload.attachments.length }}

  template(#right-content)
    .score-content.h-full.flex.flex-col.overflow-y-auto
      .info.flex-shrink-0
        ComAssessmentScoreUserInfo(:score='activeScore')
        TaAttachments.attachments(
          v-if='activeScore.entry?.model_payload?.attachments && activeScore.entry.model_payload.attachments.length > 0',
          :attachments='activeScore.entry?.model_payload?.attachments',
          :showActions='false'
        )
        .flex
          .dimension {{ activeScore.dimension?.name }}
          .sum 总计： {{ scoreForm?.sumScore }} 分

      ComAssessmentScoreForm.score-form.flex-grow.h-0.overflow-y-auto(
        ref='scoreForm',
        v-model:visibleEdit='visibleEdit',
        :score='activeScore',
        :needNext='!!nextRecord',
        :editable='activity.state !== "completed"',
        @next='onNext',
        @afterSave='reloadActivity'
      )
a-modal(v-model:visible='visibleAttachments', title='自评附件', :footer='null')
  TaAttachments(:attachments='activeScore.entry?.model_payload?.attachments', :showActions='false')

TaTemplateFormModal(
  v-if='visibleEntryForm',
  v-model:visible='visibleEntryForm',
  :template='activeScore.entry_form',
  :modelValue='activeScore.entry_payload',
  :disabled='true'
)
</template>

<style lang="stylus" scoped>
.com-assessment-manage-scores-index
  height 100%
  width 100%
  flex-grow 1
  >>> .right-content
    padding 0 !important
  .score-content
    .score-form, .info
      padding 20px 20px 0
.filter
  align-items center
  height 65px
  font-size 14px
  font-weight 400
  color rgba(38, 38, 38, 0.85)
  line-height 20px
  .icon
    margin-left 12px
.filter-menu
  width 200px
  padding 4px 0px
  .filter-menu-item
    padding 12px 20px
    .icon
      color #A6A6A6
.sum
  margin-left 12px
.right-header
  width 100%
  font-size 14px
.ml-8
  margin-left 8px
.mr-4
  margin-right 4px
.attachment
  margin-right 10px
.attachments
  margin-bottom 18px
</style>
