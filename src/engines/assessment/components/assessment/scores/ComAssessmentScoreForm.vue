<script lang="ts">
import { ref, defineComponent, toRefs, computed } from 'vue';
import { VObject } from '@/lib/vails/model';
import { AssessmentScoreTemplateFormCatalog } from '@/engines/assessment/assessment-core/types/model';
import { message } from 'ant-design-vue';
import { AssessmentScoreModel } from '../../../assessment-core/models/assessment/scores';

const ComAssessmentScoreForm = defineComponent({
  name: 'ComAssessmentScoreForm',
  components: {},
  props: {
    visibleEdit: { type: Boolean, default: false },
    editable: { type: Boolean, default: true },
    score: { type: Object, required: true },
    needNext: { type: Boolean, default: true },
    needActions: { type: Boolean, default: true },
    showHintDirectly: { type: Boolean, default: false },
    draftState: { type: String, default: 'todo' },
  },
  emits: ['update:visibleEdit', 'update:score', 'next', 'afterSave'],
  setup(props, { emit }) {
    const localVisibleEdit = computed({
      get: () => props.visibleEdit,
      set: val => emit('update:visibleEdit', val),
    });

    const localScore = computed({
      get: () => props.score,
      set: val => emit('update:score', val),
    });

    const validate = () => {
      let requiredAllConfirm = true;
      (localScore.value.score_template?.form?.catalogs || []).forEach(
        (catalog: AssessmentScoreTemplateFormCatalog) => {
          if (getCatalogVisible(catalog._id)) {
            (catalog.items || []).forEach((item: VObject) => {
              if (
                !localScore.value.formData.item_payload?.[item._id] &&
                localScore.value.formData.item_payload?.[item._id] !== 0
              ) {
                requiredAllConfirm = false;
              }
            });
          }
        },
      );

      return requiredAllConfirm;
    };

    const allFinished = computed(() => {
      if (typeof props.score.allFinished === 'boolean') {
        return props.score.allFinished;
      } else {
        return props.score.allFinished.value;
      }
    });

    const onSubmit = (customVisibleEdit = false) => {
      if (!allFinished.value || validate()) {
        localVisibleEdit.value = customVisibleEdit;
        localScore.value.formData.state = allFinished.value ? 'done' : props.draftState;
        return localScore.value.save().then(() => {
          emit('afterSave');
        });
      } else {
        message.error('请完成所有评分项');
        return Promise.reject('请完成所有评分项');
      }
    };

    const onSubmitAndNext = () => {
      onSubmit(true).then(() => emit('next'));
    };

    const reloadPayload = () => {
      localScore.value.reload();
      // reload 的对象内部变化，不会触发 computed 更新，
      // 所以手动将 _dirty 设为 true，强制更新
      (localScore as any)._dirty = true;
      if (!localScore.value.formData.item_payload) {
        localScore.value.formData.item_payload = {};
      }
    };

    const onCancel = () => {
      localVisibleEdit.value = false;
      reloadPayload();
    };

    const onStart = () => {
      localVisibleEdit.value = true;
      reloadPayload();
    };

    const sumScore = computed(() =>
      (props.score.score_template?.form?.catalogs || []).reduce((sum: number, catalog: VObject) => {
        return (
          catalog.weight *
          catalog.items
            .map((item: VObject) => props.score.formData.item_payload[item._id] || 0)
            .reduce((o: number, n: number) => o + n, 0)
        );
      }, 0),
    );

    const actions = ref<any>(null);

    const getCatalogVisible = (catalogId: string) => {
      if (props.score.template_conf?.catalog_ids && props.score.template_conf.type === 'specify') {
        return props.score.template_conf.catalog_ids.includes(catalogId);
      }

      return true;
    };

    return {
      ...toRefs(props),
      localVisibleEdit,
      onSubmit,
      onSubmitAndNext,
      onCancel,
      onStart,
      sumScore,
      actions,
      getCatalogVisible,
      validate,
      AssessmentScoreModel,
      allFinished,
    };
  },
});
export default ComAssessmentScoreForm;
</script>

<template lang="pug">
.com-assessment-score-form.flex.flex-col(v-if='score && score.formData?.item_payload')
  .collapse.flex-grow.h-0
    template(v-for='(catalog, index) in score.score_template?.form?.catalogs')
      .collapse-panel(v-if='getCatalogVisible(catalog._id)')
        .panel-header.flex-between
          span {{ catalog.name }}
          span
            | {{ catalog.items.filter(item => Object.keys(score.formData.item_payload).includes(item._id) && (!!score.formData.item_payload[item._id] || score.formData.item_payload[item._id] === 0) ).length }}&nbsp/&nbsp
            | {{ catalog.items.length }}
        .panel-middle
          .question-item(v-for='(item, index) in catalog.items', :key='item._id')
            .question-header.flex.items-center
              div(style='color: #ff4f3e') *&nbsp
              div.flex-grow.whitespace-pre-wrap.w-0 {{ item.name }}
              a-tooltip(
                v-if='item.hint && !showHintDirectly',
                :title='item.hint',
                :overlayStyle='{ "max-width": "1000px" }',
                overlayClassName='whitespace-pre'
              )
                TaIcon.ml-1.cursor-pointer(
                  type='InfoCircleFilled',
                  class='!text-gray-400'
                )
              div.flex-shrink-0(v-if='item.max_score')
                | （最高分：{{ item.max_score }}）
            .question-hint.mb-2.text-gray-600(v-if='showHintDirectly && item.hint')
              | {{ item.hint }}
            .question-middle
              template(v-if='!(editable && localVisibleEdit && (actions || !needActions))')
                strong.text-primary(v-if='score.getItemComponentType(item) === "input"')
                  | 分值：{{ score.formData.item_payload[item._id] || "-" }} 分
                strong.text-primary(v-else-if='score.getItemComponentType(item) === "star"')
                  TaEvaluateField(
                    :value='score.formData.item_payload[item._id]',
                    :disabled='true',
                    :length='item.max_score || 5'
                  )
                strong.text-primary(v-else)
                  | 选项：{{ score.getItemLabel(item) || "-" }}
              template(v-else)
                a-input-number.number-input(
                  v-if='score.getItemComponentType(item) === "input"',
                  v-model:value='score.formData.item_payload[item._id]',
                  size='large',
                  :min='0',
                  :max='item.max_score',
                  :placeholder='`分值， 满分${item.max_score}`'
                )
                TaEvaluateField(
                  v-else-if='score.getItemComponentType(item) === "star"',
                  v-model:value='score.formData.item_payload[item._id]',
                  :length='item.max_score || 5'
                )
                TaRadioGroup(
                  v-else,
                  v-model:value='score.formData.item_payload[item._id]',
                  :item='{ options: { select: score.getItemComponentSelect(item) } }'
                )
  slot
  .submit.flex-shrink-0(v-if='needActions')
    TaPolicyResource(:store='score.store', :resource_id='score.id', actionKey='update')
      .actions(ref='actions')
        template(v-if='localVisibleEdit')
          a-button.button(
            v-if='alreadyEdited && score.state !== "todo"',
            size='large',
            @click='onCancel'
          ) 取消修改
          a-button.button.next-button(
            v-if='needNext',
            :disabled='!score.alreadyEdited',
            size='large',
            @click='onSubmitAndNext'
          )
            | {{ allFinished ? "提交并跳至下一个" : "暂存并跳至下一个" }}
          a-button.button(
            type='primary',
            size='large',
            :disabled='!score.alreadyEdited',
            @click='onSubmit(false)'
          )
            | {{ allFinished ? "提交" : "暂存" }}
        template(v-else-if='editable')
          a-button.button(type='primary', size='large', @click='onStart') 修改考核
</template>

<style lang="stylus" scoped>
.com-assessment-score-form
  // height 100%
  position relative
  .collapse
    overflow auto
    width 100%
    height 100%
    padding 0 20px
    .collapse-panel
      margin-top 20px
      width 100%
      // height 100%
      .panel-header
        padding 12px 16px
        width 100%
        background #EDF7FF
        background $background-color
        color #3DA8F5
        color $primary-color
        font-size 14px
        line-height 20px
      .panel-middle
        background #fff
        padding-bottom 40px
        .question-item
          width 100%
          padding 0 4px
          .question-header
            padding 20px 0px 10px
            color rgba(38, 38, 38, 0.85)
            font-weight 500
            font-size 14px
            line-height 20px
          .question-middle
            margin-bottom 20px
            color #A6A6A6
            font-weight 500
            font-size 14px
            line-height 25px
  .submit
    // position absolute
    position sticky
    bottom 0px
    // left 200px
    z-index 1
    display flex
    justify-content flex-end
    box-sizing border-box
    padding 10px 16px
    width 100%
    border-top 1px #E5E5E5 solid
    background #fff
    .submit-instance
      margin-right 12px
      .button
        color #3DA8F5
        border-color #3DA8F5
.number-input
  width 100%
.button
  margin-left 12px
.next-button
  border-color $primary-color
  color $primary-color
</style>
