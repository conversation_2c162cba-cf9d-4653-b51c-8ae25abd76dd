<script lang="ts">
import { defineComponent, toRefs, computed } from 'vue';

const ComAssessmentScoresDetailIndex = defineComponent({
  name: 'ComAssessmentScoresDetailIndex',
  components: {},
  props: {
    store: { type: Object, required: true },
    params: { type: Object, required: true },
  },
  setup(props) {
    const config = computed(() => ({
      store: props.store,
      params: props.params,
      mode: 'table',
      template: 'assessment_score_template',
      pagination: {
        perPage: 12,
      },
      table: {
        columns: [
          {
            title: '工号',
            dataIndex: 'assessment_user_info.account',
            align: 'align',
          },
          {
            title: '考核人',
            dataIndex: 'assessment_user_info.name',
            align: 'align',
          },
          {
            title: '部门',
            dataIndex: 'assessment_user_info.duty_names',
            align: 'align',
          },
          {
            title: '考核进度',
            dataIndex: 'state',
            align: 'align',
          },
          {
            title: '总分',
            dataIndex: 'score',
            align: 'align',
          },
        ],
      },
    }));
    return {
      ...toRefs(props),
      config,
    };
  },
});
export default ComAssessmentScoresDetailIndex;
</script>

<template lang="pug">
.com-assessment-scores-detail-index
  TaIndexView(
    :config='config',
  )
    template(#header)
      slot(name="header")
    template(#bodyCell="{text,record,index,column}")
      div(v-if="column.title == '考核进度'") {{text == 'done' ? '已完成':'进行中'  }}
      div(v-else-if="column.title == '部门'")  {{text.toString()}}
      div(v-else) {{text || '-'}}
</template>

<style lang="stylus" scoped></style>
