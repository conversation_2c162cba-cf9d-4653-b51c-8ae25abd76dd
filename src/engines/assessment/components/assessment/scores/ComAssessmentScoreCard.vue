<script lang="ts">
import { defineComponent, toRefs } from 'vue';

const ComAssessmentScoreCard = defineComponent({
  name: 'ComAssessmentScoreCard',
  components: {},
  props: {
    score: { type: Object, required: true },
    isActive: { type: Boolean, default: false },
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
export default ComAssessmentScoreCard;
</script>

<template lang="pug">
.com-assessment-score-card.clickable(:class='{ "active": isActive }')
  .name {{ score.entry.user_name||score.entry.assessment_user_info.name }}
  .state
    .todo(v-if='score.state === "todo"') 待评
    .done.flex(v-else)
      | 已评&nbsp
      .score | {{ score.score }} 分
</template>

<style lang="stylus" scoped>
.com-assessment-score-card
  // height 65px
  width 270px
  padding 12px 20px
  border-bottom 1px solid #E5E5E5
  border-left 4px solid white
  .name
    font-size 14px
    font-weight 400
    color #383838
    line-height 20px
    margin-bottom 8px
  .state
    font-size 12px
    .todo
      color #3DA8F5
    .done
      color #6DC37D
      .score
        color #A6A6A6
.active
  border-left 4px solid $primary-color
</style>
