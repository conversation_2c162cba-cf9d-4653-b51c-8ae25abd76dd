<script lang="ts">
import { defineComponent, toRefs, PropType } from 'vue';
import { TaBreadcrumbInterface } from '@/components/global/ta-component/TaBreadcrumb.vue';
import ComScoreTemplateEditor from './ComScoreTemplateEditor.vue';

const ComAssessmentScoreTemplatesShow = defineComponent({
  name: 'ComAssessmentScoreTemplatesShow',
  components: {
    ComScoreTemplateEditor,
  },
  props: {
    store: { type: Object, required: true },
    extendRoute: { type: String, default: '' },
    editable: { type: Boolean, default: true },
    breadcrumbs: { type: Array as PropType<TaBreadcrumbInterface[]>, default: () => [] },
  },
  emits: ['afterDelete', 'afterExtend'],
  setup(props, { emit }) {
    const tabs = [
      {
        key: 'edit',
        label: '测评表内容',
      },
      {
        key: 'info',
        label: '基本信息',
      },
    ];

    const afterDelete = () => {
      emit('afterDelete');
    };

    const afterExtend = () => {
      emit('afterExtend');
    };

    return {
      ...toRefs(props),
      tabs,
      record: props.store.record,
      afterDelete,
      afterExtend,
    };
  },
});
export default ComAssessmentScoreTemplatesShow;
</script>

<template lang="pug">
.com-assessment-manage-score-templates-show
  TaShowLayout.show-layout(
    :tabs='tabs',
    :title='record.name',
    :store='store',
    :extendRoute='extendRoute',
    :editable='editable',
    :breadcrumbs='breadcrumbs',
    template='assessment_score_template#model',
    @afterDelete='afterDelete',
    @afterExtend='afterExtend'
  )
    template(#edit_tab)
      ComScoreTemplateEditor(v-model:scoreTemplate='record')
</template>

<style lang="stylus" scoped>
.com-assessment-manage-score-templates-show
  height 100%
  .show-layout
    >>> .content
      padding 0
</style>
