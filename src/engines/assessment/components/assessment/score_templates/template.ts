export const assessmentScoreTemplateCatalogTemplate = {
  type: 'layout',
  model: {},
  key: 'layout_1645604005890_0',
  model_key: 'layout_1645604005890_0',
  fields: [
    {
      name: '考核项名称',
      type: 'input',
      rules: [
        {
          type: 'string',
          message: '请填写正确的考核项名称',
          required: true,
          rule_type: 'required',
        },
      ],
      model: { attr_type: 'string' },
      options: { span: 24 },
      key: 'input_1645604018826_2',
      model_key: 'name',
      fields: [],
      conditions: [],
      model_key_prefix: '',
    },
    {
      name: '权重',
      type: 'percent',
      rules: [
        {
          type: 'number',
          message: '请填写正确的权重',
          required: true,
          rule_type: 'required',
          min: 0,
          max: 1,
        },
      ],
      model: { attr_type: 'number' },
      options: { span: 24, min: 0, max: 1 },
      key: 'percent_1645604014128_1',
      model_key: 'weight',
      fields: [],
      conditions: [],
      model_key_prefix: '',
    },
  ],
  conditions: [],
  options: {
    label: {},
    disabled_actions: {},
    theme: 'none',
    create_text: '提交',
    update_text: '提交',
  },
  actions: [
    { key: 'create', enabled: true },
    { key: 'update', enabled: true },
    { key: 'delete', enabled: true },
    { key: 'import', enabled: true },
    { key: 'export', enabled: true },
  ],
};
