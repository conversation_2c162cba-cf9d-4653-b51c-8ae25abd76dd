<script lang="ts">
import {
  AssessmentScoreTemplateFormCatalog,
  AssessmentScoreTemplateFormItem,
} from '@/engines/assessment/assessment-core/types/model';
import { cloneDeep } from 'lodash';
import { computed, defineComponent, ref, toRefs, watch } from 'vue';
import draggable from 'vuedraggable';
import { assessmentScoreTemplateCatalogTemplate } from './template';

const ComScoreTemplateEditor = defineComponent({
  name: 'ComScoreTemplateEditor',
  components: {
    draggable,
  },
  props: {
    scoreTemplate: { type: Object, default: () => ({}) },
  },
  emits: ['update:scoreTemplate'],
  setup(props, { emit }) {
    const localScoreTemplate = computed({
      get: () => props.scoreTemplate,
      set: val => emit('update:scoreTemplate', val),
    });

    const visibleCatalogCreateForm = ref(false);
    const visibleCatalogEditForm = ref(false);

    const catalogFormData = ref<Partial<AssessmentScoreTemplateFormCatalog>>({});
    const activeCatalog = ref<Partial<AssessmentScoreTemplateFormCatalog>>({});
    const activeItem = ref<Partial<AssessmentScoreTemplateFormItem>>({});

    watch(
      () => localScoreTemplate.value.id,
      () => {
        if (!localScoreTemplate.value.formData.form) {
          localScoreTemplate.value.formData.form = {};
        }
        activeCatalog.value = (localScoreTemplate.value.formData.form?.catalogs || [])[0] || {};
      },
      { immediate: true },
    );

    const hasEmptyItems = computed(() => {
      return (
        activeCatalog.value.items?.filter((i: AssessmentScoreTemplateFormItem) => !i.name)
          .length !== 0
      );
    });

    const keyGenerator = (prefix: string) => {
      return `${prefix}_${Date.now()}`;
    };

    const onCatalogCreateSubmit = () => {
      catalogCreateForm.value?.validate().then(() => {
        if (!localScoreTemplate.value.formData.form.catalogs) {
          localScoreTemplate.value.formData.form.catalogs = [];
        }
        localScoreTemplate.value.formData.form.catalogs.push({
          ...cloneDeep(catalogFormData.value),
          _id: keyGenerator('catalog'),
        });
        catalogFormData.value = {};
        visibleCatalogCreateForm.value = false;
        update();
      });
    };

    const addItem = () => {
      if (!activeCatalog.value.items) {
        activeCatalog.value.items = [];
      }
      activeCatalog.value.items?.push({
        _id: keyGenerator('item'),
        name: '',
        max_score: 0,
      });
      activeItem.value = (activeCatalog.value.items || [])[
        (activeCatalog.value.items?.length || 0) - 1
      ];
    };

    const onCatalogSelect = (catalog: AssessmentScoreTemplateFormCatalog) => {
      activeCatalog.value = catalog;
    };

    const onDeleteCatalog = (index: number) => {
      localScoreTemplate.value.formData.form.catalogs.splice(index, 1);
      if (JSON.stringify(activeCatalog.value) === '{}') {
        update();
      } else {
        activeCatalog.value = {};
      }
    };

    const onEditCatalog = (catalog: AssessmentScoreTemplateFormCatalog) => {
      visibleCatalogEditForm.value = true;
      catalogFormData.value = cloneDeep(catalog);
    };

    const onCatalogEditSubmit = () => {
      catalogEditForm.value?.validate().then(() => {
        const val = catalogFormData.value;

        const index = localScoreTemplate.value.formData.form.catalogs.findIndex(
          (catalog: AssessmentScoreTemplateFormCatalog) => catalog._id === val._id,
        );
        localScoreTemplate.value.formData.form.catalogs.splice(index, 1, val);
        visibleCatalogEditForm.value = false;
        catalogFormData.value = {};
        update();
      });
    };

    const onCatalogMove = (
      catalog: AssessmentScoreTemplateFormCatalog,
      index: number,
      change: number,
    ) => {
      const target = localScoreTemplate.value.formData.form.catalogs[index + change];
      localScoreTemplate.value.formData.form.catalogs[index + change] = catalog;
      localScoreTemplate.value.formData.form.catalogs[index] = target;
      update();
    };

    const onDeleteItem = (index: number) => {
      activeCatalog.value.items?.splice(index, 1);
      if (JSON.stringify(activeItem.value) === '{}') {
        update();
      } else {
        activeItem.value = {};
      }
    };

    const onEditItem = (item: AssessmentScoreTemplateFormItem) => {
      activeItem.value = item;
    };

    const onItemSubmit = () => {
      activeItem.value = {};
      update();
    };

    const update = () => {
      const catalogs = localScoreTemplate.value.formData.form.catalogs.map(
        (catalog: AssessmentScoreTemplateFormCatalog) => {
          return {
            ...catalog,
            items:
              catalog.items?.filter((item: AssessmentScoreTemplateFormItem) => item.name) || [],
          };
        },
      );

      localScoreTemplate.value.formData.form.catalogs = catalogs;

      localScoreTemplate.value.formData.form.timestamps = Date.now();
      localScoreTemplate.value.save().then(() => {
        // NOTE: 保持原有引用
        activeCatalog.value = localScoreTemplate.value.formData.form.catalogs.find(
          (catalog: AssessmentScoreTemplateFormCatalog) => catalog._id === activeCatalog.value._id,
        );
      });
    };

    const catalogCreateForm = ref<any>(null);
    const catalogEditForm = ref<any>(null);

    const visibleHintEditor = ref(false);
    const hintCache = ref('');

    const onShowHintEditor = () => {
      hintCache.value = activeItem.value.hint || '';
      visibleHintEditor.value = true;
    };

    const onShowHint = (item: AssessmentScoreTemplateFormItem) => {
      hintCache.value = item.hint || '';
      visibleHintEditor.value = true;
    };

    const onHintEditorOk = () => {
      activeItem.value.hint = hintCache.value;
      visibleHintEditor.value = false;
      update();
    };

    return {
      ...toRefs(props),
      localScoreTemplate,
      visibleCatalogCreateForm,
      visibleCatalogEditForm,
      catalogFormData,
      activeCatalog,
      activeItem,
      onCatalogCreateSubmit,
      onCatalogEditSubmit,
      addItem,
      onCatalogSelect,
      onDeleteCatalog,
      onEditCatalog,
      onCatalogMove,
      onDeleteItem,
      onEditItem,
      onItemSubmit,
      assessmentScoreTemplateCatalogTemplate,
      catalogCreateForm,
      catalogEditForm,
      hasEmptyItems,
      update,
      visibleHintEditor,
      hintCache,
      onShowHintEditor,
      onShowHint,
      onHintEditorOk,
    };
  },
});
export default ComScoreTemplateEditor;
</script>

<template lang="pug">
TaFourCellLayout.com-score-template-editor
  template(#left-header)
    .left-header 考核项
  template(#left-content)
    draggable.catalogs(
      :list='scoreTemplate.formData.form?.catalogs || []',
      item-key='id',
      @end='update'
    )
      template(#item='{ element: catalog, index }')
        .catalog.moveable(
          :class='{ "active-catalog": catalog.id === activeCatalog.id }',
          @click='onCatalogSelect(catalog)'
        )
          .name {{ catalog.name }}（{{ Math.round(catalog.weight * 100) }}%）
          .more
            a-dropdown(placement='bottom')
              .icon
                TaIcon(type='EllipsisOutlined')
              template(#overlay)
                a-menu.dropdown-menu
                  a-menu-item.dropdown-menu-item(
                    :disabled='index === 0',
                    @click='onCatalogMove(catalog, index, -1)'
                  )
                    TaIcon.dropdown-icon(type='DragOutlined')
                    span 选项上移
                  a-menu-item.dropdown-menu-item(
                    :disabled='index === scoreTemplate.form.catalogs.length - 1',
                    @click='onCatalogMove(catalog, index, +1)'
                  )
                    TaIcon.dropdown-icon(type='DragOutlined')
                    span 选项下移
                  a-menu-item.dropdown-menu-item(@click='onEditCatalog(catalog)')
                    TaIcon.dropdown-icon(type='EditOutlined')
                    span 编辑
                  a-menu-item.dropdown-menu-item(@click='onDeleteCatalog(index)')
                    TaIcon.dropdown-icon(type='DeleteOutlined')
                    span 删除
    .catalog.clickable(@click='() => (visibleCatalogCreateForm = true)')
      .name
        TaIcon.icon(type='PlusCircleFilled')
        | 创建考核项

  template(#right-header)
    .right-header.flex-between
      .flex-start 评分项
      TaTextButton(
        icon='PlusCircleFilled',
        :disabled='localScoreTemplate.form?.catalogs?.length === 0 || hasEmptyItems',
        @click='addItem'
      ) 添加评分项

  template(#right-content)
    .item-content
      TaEmpty(v-if='(activeCatalog.items || []).length === 0')
      draggable(:list='activeCatalog.items || []', item-key='_id', @end='update')
        template(#item='{ element: item, index }')
          .item.moveable
            .top
              .title.flex.items-center
                | 内容{{ index + 1 }}：

                TaTextButton(
                  v-if='activeItem._id === item._id'
                  @click='onShowHintEditor(item)'
                ) 编辑提示词

                a-tooltip(
                  v-else-if='item.hint',
                  :title='item.hint',
                  :overlayStyle='{ "max-width": "1000px" }',
                  overlayClassName='whitespace-pre'
                )
                  TaIcon.text-gray-400.cursor-pointer(
                    type='InfoCircleFilled',
                  )
              .right-path
                .score-edit(v-if='activeItem._id === item._id')
                  p 最高分：
                  a-input.score-input.ant-input(
                    v-model:value='activeItem.max_score',
                    type='number'
                  )
                .score-show(v-else)
                  span 最高分：{{ item.max_score }}
                  .more
                    a-dropdown(placement='bottom')
                      .icon
                        TaIcon(type='EllipsisOutlined')
                      template(#overlay)
                        a-menu.dropdown-menu
                          //- a-menu-item.dropdown-menu-item(:disabled='index === 0')
                          //-   TaIcon.dropdown-icon(type='DragOutlined')
                          //-   span 选项上移
                          //- a-menu-item.dropdown-menu-item(:disabled='index === (activeCatalog.items || []).length')
                          //-   TaIcon.dropdown-icon(type='DragOutlined')
                          //-   span 选项下移
                          a-menu-item.dropdown-menu-item(
                            :disabled='hasEmptyItems',
                            @click='onEditItem(item)'
                          )
                            TaIcon.dropdown-icon(type='EditOutlined')
                            span 编辑
                          a-menu-item.dropdown-menu-item(@click='onDeleteItem(index)')
                            TaIcon.dropdown-icon(type='DeleteOutlined')
                            span 删除
            .content
              a-textarea(
                v-model:value='item.name',
                v-if='activeItem._id === item._id',
                size='large',
                :autoSize='{ minRows: 2, maxRows: 8 }',
                placeholder='请输入'
              )
              span(v-else) {{ item.name }}
            .bottom(v-if='activeItem._id === item._id')
              a-button.button(type='primary', @click='onItemSubmit(item)', :disabled='!item.name') {{ item.id ? "更新" : "创建" }}
              a-button(@click='onDeleteItem(index)') 删除

a-modal(v-model:visible='visibleCatalogCreateForm', @ok='onCatalogCreateSubmit')
  TaTemplateForm(
    ref='catalogCreateForm',
    v-model:visible='visibleCatalogCreateForm',
    v-model:modelValue='catalogFormData',
    :template='assessmentScoreTemplateCatalogTemplate'
  )

a-modal(v-model:visible='visibleCatalogEditForm', @ok='onCatalogEditSubmit')
  TaTemplateForm(
    ref='catalogEditForm',
    v-model:visible='visibleCatalogCreateForm',
    v-model:modelValue='catalogFormData',
    :template='assessmentScoreTemplateCatalogTemplate'
  )

a-modal(v-model:visible='visibleHintEditor', title='提示语', width='70vw', @ok='onHintEditorOk')
  a-textarea(
    v-model:value='hintCache',
    :autoSize='{ minRows: 6, maxRows: 12 }',
  )
</template>

<style lang="stylus" scoped>
.com-score-template-editor
  height 100%
  flex-grow 1
.catalog
  position relative
  padding 18px 48px 18px 20px
  border-bottom 1px #E5E5E5 solid
  border-left 4px #fff solid
  color #383838
  cursor pointer
  &:hover
    opacity 0.8
  .name
    font-size 14px
    line-height 20px
    .icon
      margin-right 12px
  .more
    position absolute
    top 0px
    right 0px
    display flex
    justify-content center
    align-items center
    width 48px
    height 100%
.catalog-active
  border-left 4px $primary-color solid
  color $primary-color
.dropdown-menu
  width 200px
  padding 4px 0px
  .dropdown-menu-item
    &:last-child
      border-top 1px solid #E8E8E8
    .dropdown-icon
      color #A6A6A6
      margin-right 6px
.right-header
  width 100%
.item
  margin-bottom 12px
  padding 0px 16px
  border 1px solid #E8E8E8
  border-radius 4px
  background #FAFAFA
  .top
    position relative
    display flex
    padding 14px 0 4px
    justify-content space-between
    .right-path
      .score-edit, .score-show
        display flex
        p
          color #808080
          font-size 14px
          line-height 20px
          flex-shrink 0
        .score-input
          margin-top -7px
          min-width 20px
          max-width 100px
          height 30px
        .more
          width 48px
          text-align center
  .content
    margin-bottom 13px
  .bottom
    display flex
    justify-content flex-end
    padding-bottom 14px
    width 100%
    .button
      margin-right 12px
.moveable
  cursor move
</style>
