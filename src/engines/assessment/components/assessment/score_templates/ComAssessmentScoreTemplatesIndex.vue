<script lang="ts">
import { defineComponent, computed, toRefs } from 'vue';
import ComAssessmentScoreTemplatesShow from './ComAssessmentScoreTemplatesShow.vue';

const ComAssessmentScoreTemplatesIndex = defineComponent({
  name: 'ComAssessmentScoreTemplatesIndex',
  components: {
    ComAssessmentScoreTemplatesShow,
  },
  props: {
    store: { type: Object, required: true },
  },
  setup(props) {
    const config = computed(() => ({
      recordName: '评分表',
      store: props.store,
      // pagination: {
      //   perPage: 15,
      //   showPageSizeChanger: false,
      //   hideOnSinglePage: false,
      //   showSizeChanger: false,
      // }
      template: 'assessment_score_template#model',
      detail: {
        mode: 'route',
        //   mode: 'auto',
        //   mode: 'drawer',
        //   width: '1100px',
      },
      mode: 'table',
      // editApi:
      // showCount: true,
      // actions: [
      //  { key: 'create', enabled: true },
      //  { key: 'update', enabled: true },
      //  { key: 'delete', enabled: true },
      //  { key: 'import', enabled: true },
      //  { key: 'export', enabled: true },
      // ],
      table: {
        scroll: { y: '70vh' },
        // columns: [{ dataIndex: 'id', title: 'ID' }],
      },
      // selection: {
      //   showByDefault: false;
      //   showSwitch: false;
      // };
      searcherSimpleOptions: [{ key: 'name', label: '名称', type: 'string' }],
    }));

    return {
      ...toRefs(props),
      config,
    };
  },
});

export default ComAssessmentScoreTemplatesIndex;
</script>

<template lang="pug">
.com-assessment-manage-score-templates-index
  TaIndexView(:config='config')
    //- template(#card='{ record }')
    //- template(#table)
    //-    a-table-column(:autoHeight='true' title='名称' dataIndex='record')
    //- template(#detail='{ record, onClose }')
    //-   ComAssessmentScoreTemplatesShow(
    //-     v-if='record.id'
    //-     :store='store'
    //-     :extendRoute='`/assessment/manage/score_templates/${record.id}`'
    //-     :editable='editable'
    //-     @afterDelete='onClose'
    //-     @afterExtend='onClose'
    //-   )

</template>

<style lang="stylus" scoped>
.com-assessment-manage-score-templates-index
  height 100%
  width 100%
</style>
