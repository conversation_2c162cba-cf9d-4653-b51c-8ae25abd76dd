export const assessmentDimensionManageTemplateFunc = (catalogRequired: boolean) => ({
  type: 'layout',
  model: {},
  fields: [
    {
      key: 'input_1645323889985_16',
      name: '名称',
      type: 'input',
      model: { summary: true, attr_type: 'string' },
      rules: [
        { type: 'string', message: '请填写正确的单行输入', required: true, rule_type: 'required' },
      ],
      fields: [],
      options: { span: 24 },
      model_key: 'name',
      conditions: [],
      model_key_prefix: '',
    },
    {
      key: 'number_68288549811',
      name: '权重',
      type: 'number',
      model: { summary: true, attr_type: 'float' },
      rules: [
        { type: 'number', message: '请填写正确的权重', required: true, rule_type: 'required' },
      ],
      fields: [],
      options: { span: 24, max: 1, min: 0 },
      model_key: 'weight',
      model_key_prefix: '',
    },
    {
      name: '评分表',
      icon: 'FolderOutlined',
      type: 'api_single',
      rules: [],
      model: {
        attr_type: 'number',
      },
      options: {
        span: 24,
        multiple: false,
        table_items: [
          {
            name: '名称',
            data_index: 'name',
            search: true,
            type: 'string',
          },
        ],
        display_configurable_form: {},
        import_export_headers: [
          {
            _id: '1645335812463_4',
          },
        ],
        path: '/assessment/manage/score_templates',
        display: 'tag',
      },
      key: 'api_single_1645335812335_1',
      model_key: 'score_template_id',
      fields: [],
      conditions: [],
      model_key_prefix: '',
    },
    ...(catalogRequired
      ? [
          {
            key: 'api_single_1645323994247_19',
            name: '考核分类',
            type: 'api_single',
            model: { attr_type: 'number' },
            rules: [
              {
                rule_type: 'required',
                type: 'number',
                required: true,
                message: '请填写正确的考核分类',
              },
            ],
            fields: [],
            options: {
              path: '/assessment/manage/catalog',
              span: 24,
              display: 'tag',
              ransack: '{ "activity_id_eq": ${activityId} }}',
              multiple: false,
              table_items: [{ name: '名称', type: 'string', search: true, data_index: 'name' }],
              import_export_headers: [{ _id: '1645323994344_3' }],
              display_configurable_form: {},
            },
            model_key: 'catalog_id',
            conditions: [],
            model_key_prefix: '',
          },
        ]
      : []),
    {
      key: 'radio_1645323805712_15',
      name: '维度类型',
      type: 'radio',
      model: { summary: true, attr_type: 'string' },
      rules: [
        { type: 'string', message: '请填写正确的单项选择', required: true, rule_type: 'required' },
      ],
      fields: [],
      options: {
        span: 24,
        select: [
          { label: '自评', value: 'Assessment::Dimensions::SelfUser' },
          { label: '互评', value: 'Assessment::Dimensions::Mutual' },
          { label: '指定考核对象', value: 'Assessment::Dimensions::SpecifyUser' },
          { label: '指定组织管理', value: 'Assessment::Dimensions::Membership' },
        ],
        multiple: false,
      },
      model_key: 'type',
      conditions: [],
      model_key_prefix: '',
    },
    {
      name: 'options',
      icon: 'FolderOutlined',
      type: 'key_layout',
      options: { span: 24 },
      key: 'key_layout_1645328120117_1',
      model_key: 'options',
      fields: [
        {
          name: '条件块',
          icon: 'FolderOutlined',
          type: 'condition',
          conditions: [
            {
              name: '互评',
              model_key: 'type',
              val: 'Assessment::Dimensions::Mutual',
              fields: [],
              type: 'simple',
              complex_condition: { groups: [] },
              opt: '==',
            },
            {
              name: '指定考核对象',
              type: 'simple',
              model_key: 'type',
              val: 'Assessment::Dimensions::SpecifyUser',
              fields: [
                {
                  name: '考核对象',
                  icon: 'FolderOutlined',
                  type: 'list',
                  fields: [
                    {
                      name: '考核对象类型',
                      icon: 'FolderOutlined',
                      type: 'radio',
                      rules: [
                        {
                          rule_type: 'required',
                          type: 'string',
                          required: true,
                          message: '请填写正确的考核对象类型',
                        },
                      ],
                      model: { attr_type: 'string' },
                      options: {
                        select: [
                          { label: '部门', value: 'Department' },
                          { label: '用户', value: 'User' },
                        ],
                        multiple: false,
                        span: 12,
                      },
                      key: 'radio_1645328234892_4',
                      model_key: 'user_type',
                      fields: [],
                      conditions: [],
                      model_key_prefix: '',
                    },
                    {
                      name: '条件块',
                      icon: 'FolderOutlined',
                      type: 'condition',
                      conditions: [
                        {
                          name: '部门',
                          model_key: 'user_type',
                          val: 'Department',
                          fields: [
                            {
                              name: '考核部门',
                              icon: 'FolderOutlined',
                              type: 'api_single',
                              rules: [
                                {
                                  rule_type: 'required',
                                  type: 'number',
                                  required: true,
                                  message: '请填写正确的考核部门',
                                },
                              ],
                              model: { attr_type: 'number' },
                              options: {
                                span: 12,
                                multiple: false,
                                table_items: [
                                  {
                                    name: '部门名称',
                                    data_index: 'name',
                                    search: true,
                                    type: 'string',
                                  },
                                  {
                                    name: '所属单位',
                                    type: 'string',
                                    search: false,
                                    data_index: 'org_name',
                                  },
                                ],
                                display_configurable_form: {},
                                import_export_headers: [{ _id: '1645328317623_0' }],
                                path: '/res/member/departments',
                                display: 'tag',
                              },
                              key: 'api_single_1645328315926_6',
                              model_key: 'user_id',
                              fields: [],
                              conditions: [],
                              model_key_prefix: '',
                            },
                          ],
                          type: 'simple',
                          complex_condition: { groups: [] },
                          opt: '==',
                        },
                        {
                          name: '用户',
                          model_key: 'user_type',
                          val: 'User',
                          fields: [
                            {
                              name: '考核用户',
                              icon: 'FolderOutlined',
                              type: 'api_single',
                              rules: [
                                {
                                  rule_type: 'required',
                                  type: 'number',
                                  required: true,
                                  message: '请填写正确的考核用户',
                                },
                              ],
                              model: { attr_type: 'number' },
                              options: {
                                span: 12,
                                multiple: false,
                                table_items: [
                                  {
                                    name: '名称',
                                    data_index: 'name',
                                    search: true,
                                    type: 'string',
                                  },
                                  {
                                    name: '岗位',
                                    data_index: 'duty_names',
                                    search: false,
                                    type: 'string',
                                  },
                                  {
                                    name: '所属单位',
                                    type: 'string',
                                    search: false,
                                    data_index: 'org_names',
                                  },
                                  {
                                    name: '手机号',
                                    type: 'string',
                                    search: false,
                                    data_index: 'mobile',
                                  },
                                ],
                                display_configurable_form: {},
                                import_export_headers: [{ _id: '1645328381183_1' }],
                                path: '/res/member/users',
                                display: 'tag',
                              },
                              key: 'api_single_1645328379711_8',
                              model_key: 'user_id',
                              fields: [],
                              conditions: [],
                              model_key_prefix: '',
                            },
                          ],
                          type: 'simple',
                          opt: '==',
                          complex_condition: { groups: [] },
                        },
                      ],
                      options: { span: 12 },
                      key: 'condition_1645328272627_5',
                      model_key: 'condition_1645328272627_5',
                      fields: [],
                      model_key_prefix: '',
                    },
                  ],
                  rules: [],
                  model: { attr_type: 'array' },
                  options: { span: 24, disabled_actions: {}, edit_directly: true },
                  key: 'list_1645328169539_2',
                  model_key: 'users',
                  conditions: [],
                  model_key_prefix: 'options',
                },
              ],
              complex_condition: { groups: [] },
              opt: '==',
            },
            {
              name: '指定考核对象',
              type: 'simple',
              model_key: 'type',
              val: 'Assessment::Dimensions::Membership',
              fields: [
                {
                  name: '类型',
                  icon: 'FolderOutlined',
                  type: 'radio',
                  rules: [
                    {
                      rule_type: 'required',
                      type: 'string',
                      required: true,
                      message: '请填写正确的类型',
                    },
                  ],
                  model: {
                    attr_type: 'string',
                  },
                  options: {
                    select: [
                      {
                        label: '所在组织',
                        value: 'self_org',
                      },
                      {
                        label: '所在部门',
                        value: 'self_department',
                      },
                      {
                        label: '其他',
                        value: '其他',
                      },
                    ],
                    multiple: false,
                    span: 24,
                    table_items: [],
                    display_configurable_form: {},
                    import_export_headers: [
                      {
                        _id: '1658326017002_0',
                      },
                    ],
                  },
                  key: 'radio_1658326016891_3',
                  model_key: 'options.membership.mode',
                  fields: [],
                  conditions: [],
                  model_key_prefix: '',
                },
                {
                  name: '条件块',
                  icon: 'FolderOutlined',
                  type: 'condition',
                  conditions: [
                    {
                      name: '所在组织',
                      model_key: 'options.membership.mode',
                      val: 'self_org',
                      fields: [],
                      type: 'simple',
                      complex_condition: {
                        groups: [
                          {
                            items: [],
                          },
                        ],
                      },
                      opt: '==',
                    },
                    {
                      name: '所在部门',
                      model_key: 'options.membership.mode',
                      val: 'self_department',
                      fields: [],
                      type: 'simple',
                      complex_condition: {
                        groups: [
                          {
                            items: [],
                          },
                        ],
                      },
                    },
                    {
                      name: '其他',
                      model_key: 'options.membership.mode',
                      val: '其他',
                      fields: [
                        {
                          name: '部门',
                          icon: 'FolderOutlined',
                          type: 'api',
                          rules: [
                            // {
                            //   rule_type: 'required',
                            //   type: 'array',
                            //   required: true,
                            //   message: '请填写正确的部门',
                            // },
                          ],
                          model: {
                            attr_type: 'array',
                          },
                          options: {
                            span: 24,
                            multiple: true,
                            table_items: [
                              {
                                name: '部门名',
                                data_index: 'name',
                                search: true,
                                type: 'string',
                              },
                            ],
                            display_configurable_form: {},
                            import_export_headers: [
                              {
                                _id: '1658326665928_2',
                              },
                            ],
                            path: '/res/member/departments',
                          },
                          key: 'api_store_1658326664592_5',
                          model_key: 'options.membership.department_ids',
                          fields: [],
                          conditions: [],
                          model_key_prefix: '',
                        },
                        {
                          name: '组织',
                          icon: 'FolderOutlined',
                          type: 'api',
                          rules: [
                            // {
                            //   rule_type: 'required',
                            //   type: 'array',
                            //   required: true,
                            //   message: '请填写正确的组织',
                            // },
                          ],
                          model: {
                            attr_type: 'array',
                          },
                          options: {
                            span: 24,
                            multiple: true,
                            table_items: [
                              {
                                name: '组织名',
                                data_index: 'name',
                                search: true,
                                type: 'string',
                              },
                            ],
                            display_configurable_form: {},
                            import_export_headers: [
                              {
                                _id: '1658326575770_1',
                              },
                            ],
                            path: '/res/member/orgs',
                          },
                          key: 'api_store_1658326573826_4',
                          model_key: 'options.membership.org_ids',
                          fields: [],
                          conditions: [],
                          model_key_prefix: '',
                        },
                      ],
                      type: 'simple',
                      complex_condition: {
                        groups: [
                          {
                            items: [],
                          },
                        ],
                      },
                    },
                  ],
                  options: {
                    span: 24,
                  },
                  key: 'condition_1658326008163_1',
                  model_key: 'condition_1658326008163_1',
                  fields: [],
                  model_key_prefix: '',
                },
                {
                  name: '职位',
                  icon: 'FolderOutlined',
                  type: 'api',
                  rules: [],
                  model: {
                    attr_type: 'array',
                  },
                  options: {
                    span: 24,
                    multiple: true,
                    table_items: [
                      {
                        name: '职称名',
                        data_index: 'name',
                        search: true,
                        type: 'string',
                      },
                    ],
                    display_configurable_form: {},
                    import_export_headers: [
                      {
                        _id: '1658326808248_3',
                      },
                    ],
                    path: '/res/member/duties',
                  },
                  key: 'api_1658326801350_7',
                  model_key: 'options.membership.duty_ids',
                  fields: [],
                  conditions: [],
                  model_key_prefix: '',
                },
              ],
              complex_condition: { groups: [] },
              opt: '==',
            },
          ],
          options: { span: 24 },
          key: 'condition_1645328114451_0',
          model_key: 'condition_1645328114451_0',
          fields: [],
          model_key_prefix: 'options',
        },
      ],
      conditions: [],
      model_key_prefix: '',
    },
    {
      name: '采用模式',
      icon: 'FolderOutlined',
      type: 'radio',
      rules: [],
      model: {
        attr_type: 'string',
      },
      options: {
        select: [
          {
            label: '百分比采用',
            value: 'ratio',
          },
          {
            label: '总数采用',
            value: 'count',
          },
        ],
        multiple: false,
        span: 24,
        table_items: [],
        display_configurable_form: {},
        import_export_headers: [
          {
            _id: '1658330267832_0',
          },
        ],
      },
      key: 'radio_1658330267728_2',
      model_key: 'options.sample_option.sample_mode',
      fields: [],
      conditions: [],
      model_key_prefix: '',
    },
    {
      name: '条件块',
      icon: 'FolderOutlined',
      type: 'condition',
      conditions: [
        {
          name: '总数采用',
          model_key: 'options.sample_option.sample_mode',
          val: 'count',
          fields: [
            {
              name: '参加个数',
              icon: 'FolderOutlined',
              type: 'number',
              rules: [],
              model: {
                attr_type: 'number',
              },
              options: {
                span: 24,
                precision: 0,
              },
              key: 'number_1658330706639_6',
              model_key: 'options.sample_option.value',
              fields: [],
              conditions: [],
              model_key_prefix: '',
            },
          ],
          opt: '==',
          type: 'simple',
          complex_condition: {
            groups: [
              {
                items: [],
              },
            ],
          },
        },
        {
          name: '百分比采用',
          model_key: 'options.sample_option.sample_mode',
          val: 'ratio',
          fields: [
            {
              name: '采用百分比',
              icon: 'FolderOutlined',
              type: 'percent',
              rules: [],
              model: {
                attr_type: 'number',
              },
              options: {
                span: 24,
              },
              key: 'percent_1658330640906_5',
              model_key: 'options.sample_option.value',
              fields: [],
              conditions: [],
              model_key_prefix: '',
            },
          ],
          type: 'simple',
          opt: '==',
          complex_condition: {
            groups: [
              {
                items: [],
              },
            ],
          },
        },
      ],
      options: {
        span: 24,
      },
      key: 'condition_1658330592385_3',
      model_key: 'condition_1658330592385_3',
      fields: [],
      model_key_prefix: '',
    },
  ],
  actions: [
    { key: 'create', enabled: true },
    { key: 'update', enabled: true },
    { key: 'delete', enabled: true },
    { key: 'import', enabled: false },
    { key: 'export', enabled: false },
  ],
  options: {
    label: {},
    theme: { card: {}, form: {}, background: {} },
    create_text: '提交',
    update_text: '提交',
  },
  model_key: 'layout_94328008518',
  column_attributes: [
    { _id: 'input_1645323889985_16', title: ['名称'], dataIndex: 'name' },
    { _id: 'radio_1645323805712_15', title: ['维度类型'], dataIndex: 'type' },
    { _id: 'number_68288549811', title: ['权重'], dataIndex: 'weight' },
  ],
});
