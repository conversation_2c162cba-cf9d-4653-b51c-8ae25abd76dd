<script lang="ts">
import { defineComponent, computed, toRefs } from 'vue';
import { VObject } from '@/lib/vails/model';
import ComAssessmentDimensionsShow from './ComAssessmentDimensionsShow.vue';
import { assessmentDimensionManageTemplateFunc } from './template';

const ComAssessmentDimensionsIndex = defineComponent({
  name: 'ComAssessmentDimensionsIndex',
  components: {
    ComAssessmentDimensionsShow,
  },
  props: {
    store: { type: Object, required: true },
    // activityId: { type: Number, required: true },
    scrollY: { type: String, default: '70vh' },
    formDataEncode: { type: Function, default: (val: VObject) => val },
  },
  setup(props) {
    const config = computed(() => ({
      recordName: '考核维度',
      store: props.store,
      // pagination: {
      //   perPage: 15,
      //   showPageSizeChanger: false,
      //   hideOnSinglePage: false,
      //   showSizeChanger: false,
      // }
      template: assessmentDimensionManageTemplateFunc(props.store.record.catalog_enabled),
      // detail: {
      //   mode: 'auto',
      //   mode: 'drawer',
      //   width: '1100px',
      // },
      mode: 'table',
      draggable: true,
      // editApi:
      // showCount: true,
      // actions: [
      //  { key: 'create', enabled: true },
      //  { key: 'update', enabled: true },
      //  { key: 'delete', enabled: true },
      //  { key: 'import', enabled: true },
      //  { key: 'export', enabled: true },
      // ],
      table: {
        scroll: { y: props.scrollY },
        // columns: [{ dataIndex: 'id', title: 'ID' }],
      },
      // selection: {
      //   showByDefault: false;
      //   showSwitch: false;
      // };
      // searcherSimpleOptions: [{key: 'title', label: '标题', type: 'string' }],
      formDataEncode: props.formDataEncode,
    }));

    return {
      ...toRefs(props),
      config,
    };
  },
});

export default ComAssessmentDimensionsIndex;
</script>

<template lang="pug">
.com-assessment-manage-dimensions-index
  TaIndexView(:config='config')
    template(#header)
      slot(name='header')
    //- template(#card='{ record }')
    //- template(#table)
    //-    a-table-column(:autoHeight='true' title='名称' dataIndex='record')
    //- template(#detail='{ record, onClose }')
    //-   ComAssessmentDimensionsShow(
    //-     v-if='record.id'
    //-     :store='store'
    //-     :extendRoute='`/assessment/manage/activities/${activityId}/dimensions/${record.id}`'
    //-     :editable='editable'
    //-     @afterDelete='onClose'
    //-     @afterExtend='onClose'
    //-   )

</template>

<style lang="stylus" scoped>
.com-assessment-manage-dimensions-index
  height 100%
  width 100%
</style>
