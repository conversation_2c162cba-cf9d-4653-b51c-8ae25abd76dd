<script lang="ts">
import { ref, defineComponent, toRefs, computed, watch } from 'vue';
import { AssessmentEvaluationModel } from '../../assessment-core/models/assessment/user/evaluations';
import { AssessmentUserEvaluationsApi } from '../../assessment-core/apis/assessment/user/evaluations.api';
import { VStore } from '@/lib/vails';
import { message } from 'ant-design-vue';
import ComAssessmentEvaluationDrawer from '../assessment/evaluations/ComAssessmentEvaluationDrawer.vue';
import { AssessmentUserScoreTemplatesApi } from '../../assessment-core/apis/assessment/user/score_templates.api';
import { cloneDeep } from 'lodash';
import { AuthSessionApi } from '@/engines/login/apis/auth/session.api';

const ComBpmTokenSourceAssessmentEvaluationDrawer = defineComponent({
  name: 'ComBpmTokenSourceAssessmentEvaluationDrawer',
  components: {
    ComAssessmentEvaluationDrawer,
  },
  props: {
    visible: { type: Boolean, default: false },
    token: { type: Object, default: () => ({}) },
    formRecord: { type: Object, default: () => ({}) },
    disabled: { type: Boolean, default: false },
  },
  emits: ['update:visible', 'update:formRecord', 'refresh'],
  setup(props, { emit }) {
    const store: any = new VStore(new AssessmentUserEvaluationsApi());
    const localRecord = ref();

    watch(
      props.token.token_source,
      () => {
        if (!props.token.token_source?.id) return;
        localRecord.value = new AssessmentEvaluationModel(
          cloneDeep(props.token.token_source || {}),
          store,
        );
        if (localRecord.value.score_template_id) {
          new AssessmentUserScoreTemplatesApi()
            .find(localRecord.value.score_template_id)
            .then(res => {
              localRecord.value.score_template = res.data;
            });
        } else {
          message.error('配置错误');
        }
      },
      { immediate: true },
    );

    const localVisible = computed({
      get: () => props.visible,
      set: val => emit('update:visible', val),
    });

    const localFormRecord = computed({
      get: () => props.formRecord,
      set: val => emit('update:formRecord', val),
    });

    const onOk = async () => {
      if (!localFormRecord.value.formData.token_source.item_payload) {
        localFormRecord.value.formData.token_source.item_payload = {};
      }

      localFormRecord.value.formData.token_source.item_payload =
        localRecord.value.formData.item_payload;

      localFormRecord.value.formData.token_source.payload = localRecord.value.formData.payload;

      if (!isTemporaryStorage.value) {
        localFormRecord.value.formData.token_source.state = 'done';
      }

      if (adminCanEdit.value && !isTemporaryStorage.value) {
        localFormRecord.value.formData.token_source.bpm_token_state = 'completed';
      }
      await localFormRecord.value.save().then(() => {
        emit('refresh');
      });
    };

    const isTemporaryStorage = ref(false);

    const onTemporaryStorage = (callback: (skipValidate: boolean) => void) => {
      isTemporaryStorage.value = true;
      callback(true);
    };

    const onSave = (callback: () => void) => {
      isTemporaryStorage.value = false;
      callback();
    };

    const adminCanEdit = computed(
      () =>
        AuthSessionApi.currentUser().roles_name.includes('bpm_admin') &&
        localFormRecord.value.token_source?.bpm_token_state === 'processing' &&
        props.formRecord?.id,
    );

    return {
      ...toRefs(props),
      localVisible,
      localRecord,
      onOk,
      adminCanEdit,
      localFormRecord,
      onTemporaryStorage,
      onSave,
    };
  },
});
export default ComBpmTokenSourceAssessmentEvaluationDrawer;
</script>

<template lang="pug">
ComAssessmentEvaluationDrawer.com-bpm-token-source-assessment-evaluation-drawer(
  v-model:visible='localVisible',
  v-model:record='localRecord',
  :disabled='disabled && !(adminCanEdit)',
  :onOk='onOk',
)
  .extra.flex-shrink-0.flex-between
    .user-name.flex-grow(v-if='adminCanEdit && localFormRecord.formData?.token_source')
      .label 专家姓名
      TaInput(v-model:value='localFormRecord.formData.token_source.user_name')
    .user-name.flex-grow(v-else-if='localFormRecord?.formData?.token_source?.user_name')
      .label 专家姓名
      | {{ localFormRecord.formData?.token_source?.user_name }}

    .weight.flex-grow(v-if='adminCanEdit && localFormRecord.formData?.token_source')
      .label 权重
      .flex.space-x-2(v-if='typeof localFormRecord.formData.token_source.weight === "number"')
        TaInputNumberPercent.flex-grow(
          v-model:value='localFormRecord.formData.token_source.weight'
        )
        a-button(type='primary', @click='localFormRecord.formData.token_source.weight = null') 清除权重
      a-button(v-else, type='primary', @click='localFormRecord.formData.token_source.weight = 0') 设置权重
    .weight.flex-grow(v-else-if='typeof localFormRecord.formData?.token_source?.weight === "number"')
      .label 权重
      | {{ (localFormRecord.formData.token_source.weight * 100).toFixed(2) }}%
  template(#footer='{ actions }')
    a-button(
      type='default'
      @click='actions.onCancel'
    ) 取消

    //- a-button(
    //-   v-if="adminCanEdit",
    //-   type='default'
    //-   @click='onTemporaryStorage(actions.onSubmit)'
    //- ) 暂存

    a-button(
      type='primary'
      @click='onSave(actions.onSubmit)'
    ) 提交

</template>

<style lang="stylus" scoped>
.spinning
  >>> .ant-spin-nested-loading
    height: 100%
  >>> .ant-spin-container
    height: 100%
.com-bpm-token-source-assessment-evaluation-drawer
  .label
    font-weight 500
    font-size 14px
    line-height 20px
    margin-bottom 10px
  .user-name
    padding 10px 20px
</style>
