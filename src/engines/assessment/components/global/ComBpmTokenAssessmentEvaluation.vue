<script lang="ts">
import { ref, defineComponent, toRefs, computed, watch, nextTick } from 'vue';
import { AssessmentEvaluationModel } from '../../assessment-core/models/assessment/user/evaluations';
import { AssessmentUserEvaluationsApi } from '../../assessment-core/apis/assessment/user/evaluations.api';
import { VStore } from '@/lib/vails';
import { message } from 'ant-design-vue';
import { AssessmentUserScoreTemplatesApi } from '../../assessment-core/apis/assessment/user/score_templates.api';
import { cloneDeep } from 'lodash';
import ComAssessmentScoreForm from '@/engines/assessment/components/assessment/scores/ComAssessmentScoreForm.vue';
import { AuthSessionApi } from '../../../login/apis/auth/session.api';

// 新 data_form 版本
const ComBpmTokenAssessmentEvaluation = defineComponent({
  name: 'ComBpmTokenAssessmentEvaluation',
  components: {
    ComAssessmentScoreForm,
  },
  props: {
    modelValue: { type: Object, default: () => ({}) },
    disabled: { type: Boolean, default: false },
  },
  emits: ['update:value'],
  setup(props, { emit }) {
    const store: any = new VStore(new AssessmentUserEvaluationsApi());
    const localRecord = ref();

    const localValue = computed({
      get: () => props.modelValue,
      set: val => emit('update:value', val),
    });

    watch(
      localValue.value.score_template_id,
      () => {
        localValue.value.item_payload || (localValue.value.item_payload = {});
        localValue.value.payload || (localValue.value.payload = {});
        nextTick(() => {
          // console.log(cloneDeep(localValue.value), 'localValue.value');
          localRecord.value = new AssessmentEvaluationModel(cloneDeep(localValue.value), store);

          localRecord.value.formData = localValue.value;
          if (localValue.value.score_template_id) {
            new AssessmentUserScoreTemplatesApi()
              .find(localValue.value.score_template_id)
              .then(res => {
                localRecord.value.score_template = res.data;
              });
          } else {
            message.error('配置错误');
          }
        });
      },
      { immediate: true },
    );

    const form = ref<any>(null);
    const moreForm = ref<any>(null);

    const validate = () => {
      return Promise.all([
        new Promise((resolve, reject) => (form.value?.validate() ? resolve(true) : reject())),
        moreForm.value ? moreForm.value.validate() : Promise.resolve(),
      ]);
    };

    const adminCanEdit = computed(() =>
      AuthSessionApi.currentUser().roles_name.includes('bpm_admin'),
    );

    const totalScore = computed(() => {
      console.log(localValue.value, 'localValue.value.payload');

      if (localValue.value.item_payload) {
        return Object.values(localValue.value.item_payload).reduce(
          (a: any, b: any) => Number(a) + Number(b),
          0,
        );
      }
      return 0;
    });

    return {
      ...toRefs(props),
      localValue,
      localRecord,
      validate,
      form,
      moreForm,
      adminCanEdit,
      totalScore,
    };
  },
});
export default ComBpmTokenAssessmentEvaluation;
</script>

<template lang="pug">
.com-bpm-token-source-assessment-evaluation.flex.flex-col.w-full
  .px-6
    .mb-4
      .user-name.flex-grow(v-if='!disabled && adminCanEdit')
        .label 专家姓名
        TaInput(v-model:value='localValue.user_name')
      .user-name.flex-grow(v-else-if='localValue.user_name')
        .label 专家姓名
        | {{ localValue.user_name }}

    .weight.flex-grow(v-if='!disabled && adminCanEdit')
      .label 权重
      .flex.space-x-2(v-if='typeof localValue.weight === "number"')
        TaInputNumberPercent.flex-grow(
          v-model:value='localValue.weight'
        )
        a-button(type='primary', @click='localValue.weight = null') 清除权重
      a-button(v-else, type='primary', @click='localValue.weight = 0') 设置权重
    .weight.flex-grow(v-else-if='typeof localValue.weight === "number"')
      .label 权重
      | {{ (localValue.weight * 100).toFixed(2) }}%
  ComAssessmentScoreForm(
    ref='form',
    :visibleEdit='!disabled',
    :editable='!disabled',
    :needActions='false',
    v-model:score='localRecord',
  )
  //- @afterSave='() => localVisible = false'
  .flex.justify-end.items-center.mt-4
    .rounded-l.bg-primary.text-white.px-4.py-1 总分
    .rounded-r.bg-primary-100.text-primary.px-4.py-1 {{ totalScore }}
  TaTemplateForm(
    ref='moreForm',
    v-if='localValue.score_template?.score_form && !disabled'
    v-model:modelValue='localValue.payload',
    :template='localValue.score_template.score_form'
  )
  TaTemplateFormViewer(
    v-if='localValue.score_template?.score_form && disabled'
    :modelValue='localValue.payload',
    :template='localValue.score_template.score_form'
  )
</template>

<style lang="stylus" scoped>
.spinning
  >>> .ant-spin-nested-loading
    height: 100%
  >>> .ant-spin-container
    height: 100%
.com-bpm-token-source-assessment-evaluation
  .label
    font-weight 500
    font-size 14px
    line-height 20px
    margin-bottom 10px
  .text-primary
    color $primary-color
  .bg-primary
    background $primary-color

  .bg-primary-100
    background lighten($primary-color, 80%)
</style>
