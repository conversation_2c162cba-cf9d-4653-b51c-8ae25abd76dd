<script lang="ts">
import {
  AssessmentScoreTemplateFormCatalog,
  AssessmentScoreTemplateFormItem,
} from '@/engines/assessment/assessment-core/types/model';
import { Token } from '@/engines/bpm/bpm-core/types';
import { VObject, VStore } from '@/lib/vails';
import { mean, sum } from 'lodash';
import { computed, defineComponent, nextTick, ref, toRefs } from 'vue';
import { AssessmentUserScoreTemplatesApi } from '../../assessment-core/apis/assessment/user/score_templates.api';

// 旧版，非 data form 的 token_source 版本
const ComBpmTokenSourceAssessmentEvaluationStatisticsTable = defineComponent({
  name: 'ComBpmTokenSourceAssessmentEvaluationStatisticsTable',
  components: {},
  props: {
    tokenPlace: { type: Object, default: () => ({}) },
    showTotal: { type: Boolean, default: false },
    showAverage: { type: Boolean, default: true },
  },
  setup(props) {
    const activeTab = ref('看分类');
    // const config = computed(() => {
    //   if (activeTab.value === '看分类') {
    //     return {
    //       headers: [
    //         {
    //           text: '分类',
    //           align: 'start',
    //           sortable: false,
    //           value: 'name',
    //         },
    //         { text: '总分', value: 'count' },
    //         { text: '专家', value: 'percent' },
    //         { text: '打分', value: 'percent' },
    //         { text: '得分', value: 'percent' },
    //       ],
    //     };
    //   } else {
    //     return {
    //       headers: [
    //         {
    //           text: '姓名',
    //           align: 'start',
    //           sortable: false,
    //           value: 'name',
    //         },
    //         ...[],
    //         { text: '得分', value: 'count' },
    //         { text: '操作', value: 'percent' },
    //       ],
    //     };
    //   }
    // });

    const scoreTemplateStore = new VStore(new AssessmentUserScoreTemplatesApi());

    const scoreTemplateId = props.tokenPlace?.tokens?.filter(
      (token: Token) => token.token_source,
    )?.[0]?.token_source?.score_template_id;

    if (scoreTemplateId) {
      scoreTemplateStore.find(scoreTemplateId);
    }

    const processScore = (score: number | undefined) => {
      return score == 0 ? 0 : score?.toFixed(2);
    };

    const byCatalogData = computed(() => {
      const result: {
        name: string;
        maxScore: number;
        users: { name: string; score: number }[];
      }[] = [];
      if (scoreTemplateStore.record.value?.form) {
        scoreTemplateStore.record.value.form.catalogs?.forEach(
          (catalog: AssessmentScoreTemplateFormCatalog) => {
            result.push({
              name: catalog.name,
              maxScore:
                (catalog.items || [])
                  .map((item: AssessmentScoreTemplateFormItem) => item.max_score)
                  .reduce((a, b) => a + b, 0) * catalog.weight,
              users: tokens.value
                .filter((token: Token) => {
                  return token.token_source?.template_conf?.type === 'specify' &&
                    token.token_source?.template_conf?.catalog_ids?.length > 0
                    ? token.token_source.template_conf.catalog_ids.includes(catalog._id)
                    : Object.keys(token.token_source?.catalog_payload || {}).includes(catalog._id);
                })
                .map((token: Token) => {
                  return {
                    token_id: token.id,
                    name: token.token_source?.user_name || token.token_source?.user?.name,
                    score: processScore((token.token_source?.catalog_payload || {})[catalog._id]),
                  };
                }),
            });
          },
        );
      }
      return result;
    });

    const visibleEvaluation = ref(false);
    const activeToken = ref<Token>();
    const onShowTokenEvaluation = (token: Token) => {
      activeToken.value = token;
      nextTick(() => {
        visibleEvaluation.value = true;
      });
    };

    const tokens = computed(() => {
      return props.tokenPlace.tokens.filter((token: Token) => token.token_source);
    });

    const id2Weight = computed(() =>
      tokens.value.reduce((acc: VObject, token: Token) => {
        acc[token.id] = token.token_source?.weight;
        return acc;
      }, {}),
    );

    const id2Total = computed(() =>
      tokens.value.reduce((acc: VObject, token: Token) => {
        acc[token.id] = Object.values(token.token_source?.catalog_payload || {}).reduce(
          (a: any, b: any) => a + b,
          0,
        );
        return acc;
      }, {}),
    );

    const tokenWithoutWeightCount = computed(() => {
      return Object.values(id2Weight.value).filter((w: any) => typeof w !== 'number').length;
    });

    const defaultWeight = computed(() => {
      return hasWeight.value
        ? (1 - sum(Object.values(id2Weight.value))) / tokenWithoutWeightCount.value
        : 1;
    });

    const averageTotalScore = computed(() => {
      const result = mean(
        tokens.value.map((token: Token) => {
          return id2Total.value[token.id] * getWeightByTokenId(token.id);
        }),
      );

      return result.toFixed(2);
    });

    const sumTotalScore = computed(() => {
      const result = sum(
        tokens.value.map((token: Token) => {
          return id2Total.value[token.id] * getWeightByTokenId(token.id);
        }),
      );

      return result.toFixed(2);
    });

    const hasWeight = computed(() => tokenWithoutWeightCount.value !== tokens.value.length);

    const getWeightByTokenId = (id: number) => {
      return typeof id2Weight.value[id] === 'number' ? id2Weight.value[id] : defaultWeight.value;
    };

    return {
      ...toRefs(props),
      activeTab,
      scoreTemplate: scoreTemplateStore.record,
      byCatalogData,
      mean,
      sum,
      processScore,
      onShowTokenEvaluation,
      visibleEvaluation,
      activeToken,
      averageTotalScore,
      sumTotalScore,
      tokens,
      id2Weight,
      defaultWeight,
      tokenWithoutWeightCount,
      hasWeight,
      getWeightByTokenId,
    };
  },
});
export default ComBpmTokenSourceAssessmentEvaluationStatisticsTable;
</script>

<template lang="pug">
.assessment-evaluation-flowable.relative.w-full(v-if='scoreTemplate?.form')
  .switch.absolute.right-4.text-sm.font-medium.flex
    .tab.rounded-l.border-r-0(
      :class='{ "active-tab": activeTab === "看分类" }',
      @click.stop='() => (activeTab = "看分类")'
    ) 看分类
    .tab.rounded-r(
      :class='{ "active-tab": activeTab === "看人员" }',
      @click.stop='() => (activeTab = "看人员")'
    ) 看人员

  .overflow-hidden.rounded-xl.border.border-gray-200.w-full(v-if='activeTab === "看人员"')
    table.-m-1px.border-gray-200.table-fixed(style='width: calc(100% + 2px)')
      thead.bg-gray-100
        tr
          th.p-4.text-xs.font-medium.text-left.text-gray-500.uppercase
            .flex-center
              | 姓名
          th.p-4.text-xs.font-medium.text-left.text-gray-500.uppercase(
            v-for='catalog in scoreTemplate.form.catalogs'
          )
            .flex-center
              | {{ catalog.name }}
          th.p-4.text-xs.font-medium.text-left.text-gray-500.uppercase
            .flex-center
              | 得分
          th.p-4.text-xs.font-medium.text-left.text-gray-500.uppercase(v-if='hasWeight')
            .flex-center
              | 权重
          th.p-4.text-xs.font-medium.text-left.text-gray-500.uppercase
            .flex-center
              | 操作
      tbody.bg-white
        tr(v-for='token in tokens')
          td.p-4
            .flex-center {{ token.token_source?.user_name || token.token_source?.user?.name }}
          td.p-4(v-for='catalog in scoreTemplate.form.catalogs')
            .flex-center.min-h-6
              .blue-tag(
                v-if='processScore(token.token_source?.catalog_payload?.[catalog._id]) !== undefined'
              )
                | {{ processScore(token.token_source?.catalog_payload?.[catalog._id]) }}
          td.p-4
            .flex-center.min-h-6
              .blue-tag {{ processScore(Object.values(token.token_source?.catalog_payload || {}).reduce((a, b) => a + b, 0)) }}
          td.p-4(v-if='hasWeight')
            .flex-center.min-h-6
              | {{ (getWeightByTokenId(token.id) * 100).toFixed(2) }}%
          td.p-4
            .flex-center
              TaTextButton(@click='onShowTokenEvaluation(token)')
                | 查看评分表

        tr(v-if='showAverage')
          td.p-4(:colSpan='4 + scoreTemplate.form.catalogs.length - 2 + (hasWeight ? 1 : 0)')
            .flex-center 平均得分
          td.p-4(:colSpan='1')
            .flex-center
              .green-tag
                | {{ averageTotalScore }}

        tr(v-if='showTotal')
          td.p-4(:colSpan='4 + scoreTemplate.form.catalogs.length - 2 + (hasWeight ? 1 : 0)')
            .flex-center 总得分
          td.p-4(:colSpan='1')
            .flex-center
              .green-tag
                | {{ sumTotalScore }}

  .overflow-hidden.rounded-xl.border.border-gray-200.w-full(v-if='activeTab === "看分类"')
    table.-m-1px.border-gray-200.table-fixed(style='width: calc(100% + 2px)')
      thead.bg-gray-100
        tr
          th.p-4.text-xs.font-medium.text-left.text-gray-500.uppercase
            .flex-center 分类
          th.p-4.text-xs.font-medium.text-left.text-gray-500.uppercase
            .flex-center 总分
          th.p-4.text-xs.font-medium.text-left.text-gray-500.uppercase
            .flex-center 专家
          th.p-4.text-xs.font-medium.text-left.text-gray-500.uppercase
            .flex-center 打分
          th.p-4.text-xs.font-medium.text-left.text-gray-500.uppercase(v-if='hasWeight')
            .flex-center 权重
          th.p-4.text-xs.font-medium.text-left.text-gray-500.uppercase
            .flex-center 得分
      tbody.bg-white
        template(v-for='item in byCatalogData')
          tr(v-for='(user, index) in item.users')
            td.p-4(v-if='index === 0', :rowSpan='item.users.length')
              .flex-center {{ item.name }}
            td.p-4(v-if='index === 0', :rowSpan='item.users.length')
              .flex-center
                .blue-tag {{ item.maxScore }}
            td.p-4 {{ user.name }}
            td.p-4
              .flex-center.min-h-6
                .blue-tag(v-if='user.score !== undefined')
                  | {{ user.score }}
            td.p-4(v-if='hasWeight')
              .flex-center.min-h-6
                | {{ (getWeightByTokenId(user.token_id) * 100).toFixed(2) }}%
            td.p-4(v-if='index === 0', :rowSpan='item.users.length')
              .flex-center
                .blue-tag {{ processScore(sum(item.users.map(u => Number(u.score) * getWeightByTokenId(u.token_id)))) }}
        tr(v-if='showAverage')
          td.p-4(:colSpan='4 + (hasWeight ? 1 : 0)')
            .flex-center 平均得分
          td.p-4(:colSpan='1')
            .flex-center
              .green-tag
                | {{ averageTotalScore }}

        tr(v-if='showTotal')
          td.p-4(:colSpan='4 + (hasWeight ? 1 : 0)')
            .flex-center 总得分
          td.p-4(:colSpan='1')
            .flex-center
              .green-tag
                | {{ sumTotalScore }}


  ComBpmTokenSourceAssessmentEvaluationDrawer(
    v-if='visibleEvaluation',
    v-model:visible='visibleEvaluation',
    :token='activeToken',
    :disabled='true'
  )
</template>

<style lang="stylus" scoped>
table
  border-collapse collapse
  @apply rounded-xl
th, td
  @apply border-gray-200 border
.switch
  top -3.1rem
.tab
  @apply py-1 px-3 text-gray-500 border-gray-300 border cursor-pointer
.active-tab
  color #1C64F2
  background #E1EFFE
.blue-tag
  color #1C64F2
  background #E1EFFE
  @apply py-2px px-3 rounded-lg text-sm font-medium
.green-tag
  color #1E429F
  @apply bg-green-100 py-2px px-3 rounded-lg text-sm font-medium
.flex-center
  @apply w-full h-full flex items-center justify-center
</style>
