<script lang="ts">
import { ref, defineComponent, toRefs, computed, onMounted, PropType } from 'vue';
import { FormsResourceInfos } from '@/components/global/ta-component/ta-template-form-core/apis/forms/resource_infos';
import { VObject, VStore } from '@/lib/vails';
import { AssessmentUserScoreTemplatesApi } from '../../assessment-core/apis/assessment/user/score_templates.api';
import { cloneDeep } from 'lodash';

const ComTokenSourceAssessmentEvaluationTemplateConfEditor = defineComponent({
  name: 'ComTokenSourceAssessmentEvaluationTemplateConfEditor',
  components: {},
  props: {
    value: { type: Array as PropType<VObject[]>, default: () => [] }, // template_confs
    payload: { type: Object, default: () => ({}) }, // localNodeCopy
  },
  emits: ['update:value'],
  setup(props, { emit }) {
    const localValue = computed<VObject[]>({
      get: () => props.value,
      set: val => emit('update:value', val),
    });

    // const localPayload = computed({
    //   get: () => props.payload,
    //   set: val => emit('update:payload', val),
    // });

    const users = ref<VObject[]>([]);

    const scoreTemplateStore = new VStore(new AssessmentUserScoreTemplatesApi());

    onMounted(async () => {
      const scoreTemplateId = props.payload.token_source_options?.default_values?.score_template_id;

      if (!scoreTemplateId) return;

      await scoreTemplateStore.find(scoreTemplateId);

      if (
        props.payload.options?.user_options?.type === 'Bpm::Attr::UserList' &&
        (props.payload.options.user_options.user_ids || []).length > 0
      ) {
        const { data } = await new FormsResourceInfos().sendCollectionAction('find_by_ids', {
          data: {
            resource_info: {
              path: 'res/user/users',
              ids: props.payload.options.user_options.user_ids,
              attrs: ['id', 'name'],
            },
          },
        });
        users.value = data.records;
      }
    });

    const columns = computed(() => {
      const userColumn = {
        title: '用户',
        dataIndex: 'name',
        key: 'user',
        align: 'center',
        fixed: 'left',
      };

      const catalogColumns = (scoreTemplateStore.record.value?.form?.catalogs || []).map(
        (catalog: any) => ({
          title: catalog.name,
          dataIndex: catalog._id,
          align: 'center',
          key: catalog._id,
        }),
      );

      return [userColumn, ...catalogColumns];
    });

    const onUpdateChecked = (
      val: boolean,
      column: { dataIndex: string },
      record: { id: number },
    ) => {
      let cache = cloneDeep(localValue.value);
      if (!Array.isArray(cache)) cache = [];

      let existsItem = cache.find((item: VObject) => item.id === record.id);
      if (!existsItem) {
        existsItem = { id: record.id, catalog_ids: [] };
        cache.push(existsItem);
      }

      const existsCatalog = existsItem?.catalog_ids?.find(
        (catalog: string) => catalog === column.dataIndex,
      );

      if (existsCatalog && !val) {
        existsItem.catalog_ids = existsItem.catalog_ids.filter(
          (catalogId: string) => catalogId !== column.dataIndex,
        );
      } else if (!existsCatalog && val) {
        existsItem.catalog_ids.push(column.dataIndex);
      }

      localValue.value = cache;
    };

    const getChecked = (column: { dataIndex: string }, record: { id: number }) => {
      return (
        typeof localValue.value.find === 'function' &&
        localValue.value
          .find((item: VObject) => item.id === record.id)
          ?.catalog_ids?.includes(column.dataIndex)
      );
    };
    return {
      ...toRefs(props),
      localValue,
      users,
      scoreTemplate: scoreTemplateStore.record,
      columns,
      // localPayload,
      onUpdateChecked,
      getChecked,
    };
  },
});
export default ComTokenSourceAssessmentEvaluationTemplateConfEditor;
</script>

<template lang="pug">
.com-assessment-evaluation-template-conf-editor(v-if='scoreTemplate?.id')
  template(v-if='payload.options?.user_options?.type === "Bpm::Attr::UserList"')
    a-table(
      :columns='columns'
      :dataSource='users'
      :pagination='false'
    )
      template(#bodyCell='{ record, column, text }')
        .name(v-if='column.key === "user"')
          | {{ record.name }}

        .switch.flex.items-center.justify-center(v-else)
          a-switch(
            :checked='getChecked(column, record)',
            @update:checked='(val) => onUpdateChecked(val, column, record)'
          )
.com-assessment-evaluation-template-conf-editor.w-full.py-4.flex.justify-center(v-else)
  .text-gray-400 暂未配置评分表
</template>

<style lang="stylus" scoped></style>
