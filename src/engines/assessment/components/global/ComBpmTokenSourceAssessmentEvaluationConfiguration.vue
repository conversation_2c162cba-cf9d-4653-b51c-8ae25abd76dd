<script lang="ts">
import { defineComponent, toRefs, computed } from 'vue';
import { ComBpmTokenSourceAssessmentEvaluationConfigurationTemplate } from './template';

const ComBpmTokenSourceAssessmentEvaluationConfiguration = defineComponent({
  name: 'ComBpmTokenSourceAssessmentEvaluationConfiguration',
  components: {},
  props: {
    value: { type: Object, default: () => ({}) },
  },
  setup(props, { emit }) {
    const localValue = computed({
      get: () => props.value,
      set: val => emit('update:value', val),
    });
    return {
      ...toRefs(props),
      localValue,
      template: ComBpmTokenSourceAssessmentEvaluationConfigurationTemplate,
    };
  },
});
export default ComBpmTokenSourceAssessmentEvaluationConfiguration;
</script>

<template lang="pug">
.com-bpm-token-source-assessment-evaluation-configuration.pt-4.bg-gray-50
  TaTemplateForm(v-model:modelValue='localValue', :template='template')
</template>

<style lang="stylus" scoped></style>
