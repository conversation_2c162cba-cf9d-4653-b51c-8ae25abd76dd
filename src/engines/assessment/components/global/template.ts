import { VObject } from '@/lib/vails';

export const ComBpmTokenSourceAssessmentEvaluationConfigurationTemplate: VObject = {
  options: {
    label: {},
    disabled_actions: {},
    theme: { card: { content: '<p><br></p>' }, background: {} },
    create_text: '提交',
    update_text: '提交',
    searcher: [],
  },
  model: {},
  column_attributes: [],
  actions: [
    { key: 'create', enabled: true },
    { key: 'update', enabled: true },
    { key: 'delete', enabled: true },
    { key: 'import', enabled: true },
    { key: 'export', enabled: true },
  ],
  type: 'container_layout',
  key: 'container_layout_1687678210265_0',
  model_key: 'container_layout_1687678210265_0',
  fields: [
    {
      key: 'TaTemplateFormDesignerKey-1',
      type: 'layout',
      fields: [
        {
          name: '题目模板',
          icon: 'FolderOutlined',
          type: 'api_single',
          rules: [],
          model: { attr_type: 'number' },
          options: {
            span: 24,
            multiple: false,
            table_items: [{ name: '名称', data_index: 'name', search: true, type: 'string' }],
            display_configurable_form: {},
            import_export_headers: [{ _id: '1687678334400_0' }],
            path: '/assessment/user/score_templates',
          },
          key: 'api_single_1687678332393_3',
          model_key: 'token_source_options.default_values.score_template_id',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
        {
          name: '统计组件',
          type: 'radio',
          rules: [],
          model: {
            attr_type: 'string',
          },
          options: {
            select: [
              {
                label: '平均分表格',
                value: 'ComBpmTokenSourceAssessmentEvaluationStatisticsTable',
              },
              {
                label: '总分表格',
                value: 'ComBpmTokenSourceAssessmentEvaluationStatisticsTotalTable',
              },
            ],
            defaultValue: 'ComBpmTokenSourceAssessmentEvaluationStatisticsTable',
            multiple: false,
            span: 24,
            table_items: [],
          },
          key: 'select_1662017442300_1',
          model_key: 'token_source_options.options.component',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
        {
          name: '可见性配置',
          icon: 'FolderOutlined',
          type: 'dynamic_component',
          rules: [],
          model: {},
          options: {
            span: 24,
            dynamic_component: 'ComTokenSourceAssessmentEvaluationTemplateConfEditor',
          },
          key: 'dynamic_component_1687678299780_1',
          model_key: 'token_source_options.default_values.template_confs',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
      ],
      options: {},
      model: {},
      model_key_prefix: '',
    },
  ],
  conditions: [],
};
