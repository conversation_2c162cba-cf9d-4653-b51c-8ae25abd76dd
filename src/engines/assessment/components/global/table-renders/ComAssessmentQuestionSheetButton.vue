<script lang="ts">
import { ref, defineComponent, toRefs } from 'vue';
import { VStore } from '../../../../../lib/vails/store/index';
import { AssessmentQuestionSheetModel } from '../../../assessment-core/models/assessment/question_sheets';
import { AssessmentManageQuestionSheetsApi } from '../../../apis/assessment/manage/question_sheets.api';
import ComAssessmentUserQuestionSheetsShow from '@/engines/assessment/components/assessment/question_sheets/ComAssessmentUserQuestionSheetsShow.vue';

const ComAssessmentQuestionSheetButton = defineComponent({
  name: 'ComAssessmentQuestionSheetButton',
  components: {
    ComAssessmentUserQuestionSheetsShow,
  },
  props: {
    value: { type: String, required: true },
    record: { type: Object, required: true },
  },
  setup(props) {
    const visible = ref(false);

    const store = new VStore(new AssessmentManageQuestionSheetsApi(), AssessmentQuestionSheetModel);

    const onShowQuestionSheet = async () => {
      await store.find(props.value);
      visible.value = true;
    };

    return {
      ...toRefs(props),
      visible,
      onShowQuestionSheet,
      store,
      questionSheet: store.record,
    };
  },
});
export default ComAssessmentQuestionSheetButton;
</script>

<template lang="pug">
.com-assessment-question-sheet-button.flex.items-center
  a-button(type='primary', @click='onShowQuestionSheet') 查看试卷

  a-drawer(
    v-if='visible',
    v-model:visible="visible",
    width="70vw"
    title='查看试卷',
  )
    ComAssessmentUserQuestionSheetsShow(
      v-if='questionSheet.id',
      :store='store',
      :needSave='false',
      :needTemporaryStorage='false',
    )


</template>

<style lang="stylus" scoped></style>
