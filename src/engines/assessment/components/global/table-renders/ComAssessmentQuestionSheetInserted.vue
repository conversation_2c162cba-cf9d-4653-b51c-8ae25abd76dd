<script lang="ts">
import { defineComponent, toRefs } from 'vue';
import {
  insertJsonata,
  jsonataGet,
} from '@/components/global/ta-component/ta-template-form-core/useJsonata';

const ComAssessmentQuestionSheetInserted = defineComponent({
  name: 'ComAssessmentQuestionSheetInserted',
  components: {},
  props: {
    value: { type: String, required: true },
    record: { type: Object, required: true },
  },
  setup(props) {
    const insertQuestionSheet = (name: string) => {
      return insertJsonata(name, (dataKey: string) =>
        jsonataGet({ questionSheet: props.record }, dataKey),
      );
    };

    return {
      ...toRefs(props),
      insertQuestionSheet,
    };
  },
});
export default ComAssessmentQuestionSheetInserted;
</script>

<template lang="pug">
.com-assessment-question-sheet-inserted
  | {{ insertQuestionSheet(value) }}
</template>

<style lang="stylus" scoped></style>
