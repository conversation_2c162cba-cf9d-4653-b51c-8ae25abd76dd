<script lang="ts">
import { defineComponent } from 'vue';
import ComBpmTokenPlaceAssessmentEvaluationStatisticsTable from './ComBpmTokenPlaceAssessmentEvaluationStatisticsTable.vue';

const ComBpmTokenSourceAssessmentEvaluationStatisticsTotalTable = defineComponent({
  name: 'ComBpmTokenSourceAssessmentEvaluationStatisticsTotalTable',
  components: {
    ComBpmTokenPlaceAssessmentEvaluationStatisticsTable,
  },
  props: {
    tokenPlace: { type: Object, default: () => ({}) },
  },
  setup() {
    return {};
  },
});
export default ComBpmTokenSourceAssessmentEvaluationStatisticsTotalTable;
</script>

<template lang="pug">
ComBpmTokenPlaceAssessmentEvaluationStatisticsTable(
  :tokenPlace='tokenPlace',
  :showTotal='true',
  :showAverage='false',
)
</template>

<style lang="stylus" scoped></style>
