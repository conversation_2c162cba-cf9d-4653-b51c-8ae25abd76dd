<script lang="ts">
import { defineComponent } from 'vue';
import ComBpmTokenSourceAssessmentEvaluationStatisticsTable from './ComBpmTokenSourceAssessmentEvaluationStatisticsTable.vue';

const ComBpmTokenSourceAssessmentEvaluationStatisticsTotalTable = defineComponent({
  name: 'ComBpmTokenSourceAssessmentEvaluationStatisticsTotalTable',
  components: {
    ComBpmTokenSourceAssessmentEvaluationStatisticsTable,
  },
  props: {
    tokenPlace: { type: Object, default: () => ({}) },
  },
  setup() {
    return {};
  },
});
export default ComBpmTokenSourceAssessmentEvaluationStatisticsTotalTable;
</script>

<template lang="pug">
ComBpmTokenSourceAssessmentEvaluationStatisticsTable(
  :tokenPlace='tokenPlace',
  :showTotal='true',
  :showAverage='false',
)
</template>

<style lang="stylus" scoped></style>
