<script lang="ts">
import { defineComponent, computed, ref, watch } from 'vue';

export default defineComponent({
  name: 'ComAssessmentQuestionAnswerConfig',
  props: {
    value: { type: Object, default: () => ({}) },
    disabled: { type: Boolean, default: false },
  },
  setup(props, { emit }) {
    const localValue = computed({
      get: () => props.value,
      set: val => emit('update:value', val),
    });

    const item = computed(() => {
      return {
        options: {
          select: localValue.value.config?.select_options || [],
        },
      };
    });

    const answer = ref<any>(localValue.value.config?.answer?.options);

    watch(
      () => answer.value,
      n => {
        localValue.value.config.answer = { options: n };
      },
    );

    return {
      localValue,
      item,
      answer,
    };
  },
});
</script>

<template lang="pug">
.com-assessment-question-answer-config.p-3
  .flex.items-center.h-8
    .ml-3.font-medium 答案
  .pl-3
    TaRadioGroup(
      v-if='localValue.kind == "Assessment::Attr::SingleChoice"'
      v-model:value='answer',
      :item='item'
      :disabled='disabled'
    )
    TaCheckGroup(
      v-else-if='localValue.kind == "Assessment::Attr::MultipleChoice"'
      v-model:value='answer',
      :item='item',
      :disabled='disabled'
      :multiple='true',
    )
</template>
