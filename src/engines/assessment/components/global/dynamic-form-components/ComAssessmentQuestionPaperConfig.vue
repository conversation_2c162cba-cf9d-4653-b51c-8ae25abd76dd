<script lang="ts">
import { AssessmentManageQuestionsApi } from '@/engines/assessment/apis/assessment/manage/questions.api';
import { AssessmentManageQuestionBanksApi } from '@/engines/assessment/apis/assessment/manage/question_banks.api';
import { AssessmentQuestionModel } from '@/engines/assessment/assessment-core/models/assessment/questions';
import { VStore } from '@/lib/vails';
import { compact, uniq } from 'lodash';
import { defineComponent, toRefs, computed, ref, watch } from 'vue';

export default defineComponent({
  name: 'ComAssessmentQuestionPaperConfig',
  props: {
    value: { type: Object, default: () => ({}) },
    disabled: { type: Boolean, default: false },
  },
  setup(props, { emit }) {
    const localValue = computed({
      get: () => props.value,
      set: val => emit('update:value', val),
    });
    localValue.value.config ||= {};
    localValue.value.arrange_mode ||= 'random';

    const defaultConfig = () => ({
      question_bank_id: null,
      question_type: null,
      question_count: null,
      question_score: null,
      key: Date.now(),
    });

    const configs = ref(localValue.value.config?.questions || [defaultConfig()]);
    const addConfig = () => {
      configs.value.push(defaultConfig());
    };

    const removeConfig = (config: any) => {
      let index = configs.value.indexOf(config);
      if (index !== -1) {
        configs.value.splice(index, 1);
      }
    };

    watch(
      () => configs.value,
      n => (localValue.value.config.questions = n),
      {
        deep: true,
      },
    );

    const validate = () => {
      return new Promise<void>((resolve, reject) => {
        if (!localValue.value.arrange_mode) {
          reject('请填写正确的配置信息');
        } else if (
          !localValue.value.config?.questions ||
          localValue.value.config.questions.length == 0
        ) {
          reject('请填写正确的配置信息');
        } else if (localValue.value.config.questions.length > 0) {
          localValue.value.config.questions.forEach((config: any) => {
            if (
              !config.question_bank_id ||
              !config.question_type ||
              (localValue.value.arrange_mode == 'random' && !config.question_count) ||
              (localValue.value.arrange_mode == 'fixed' &&
                (!config.question_ids || config.question_ids.length == 0)) ||
              !config.question_score
            ) {
              reject('请填写正确的配置信息');
            }
          });
          resolve();
        } else {
          resolve();
        }
      });
    };

    const questionBanksStat = ref<any>({});

    const fetchDefaultQuestionBankStat = async () => {
      if (localValue.value.config?.questions) {
        const questionBankIds = compact(
          uniq(localValue.value.config.questions.map((config: any) => config.question_bank_id)),
        );
        const store = new VStore(new AssessmentManageQuestionBanksApi());
        const { data } = await store.sendCollectionAction({
          action: 'list_index',
          config: {
            data: {
              q: { id_in: questionBankIds, mode: 'viewable' },
              per_page: 1000,
              resource_stat_condition: {
                refs: [
                  {
                    relations: ['questions'],
                    item: {
                      key: 'questions',
                      caculator: {
                        type: 'caculation',
                        filter: {
                          parent_id_null: 1,
                        },
                        caculations: [{ name: 'kind', method: 'count', group_attr: 'kind' }],
                      },
                    },
                  },
                ],
              },
            },
          },
        });
        data.records.forEach((record: any) => {
          questionBanksStat.value[record.id] = record.ta_statistics.questions?.kind || {};
        });
      }
    };
    fetchDefaultQuestionBankStat();

    const onSyncQuestionBanksStat = async (item: any, val: any) => {
      const { data } = await new AssessmentManageQuestionsApi({
        params: {
          q: { parent_id_null: 1, mode: 'viewable', question_bank_id_eq: val },
          group_keys: 'kind',
        },
      }).index({ per_page: 1 });
      questionBanksStat.value[val] = { ...data.statistics };
    };

    const questionTypeOptions = () => {
      return Object.keys(AssessmentQuestionModel.kingMapping()).map((k: any) => ({
        label: AssessmentQuestionModel.kingMapping()[k]?.label,
        value: k,
      }));
    };

    const questionTableItems = [
      {
        name: 'ID',
        type: 'string',
        search: true,
        data_index: 'id',
      },
      {
        name: '题目',
        type: 'string',
        search: true,
        data_index: 'name',
      },
    ];

    const arrangeModeItem = {
      options: {
        select: [
          { label: '抽取题目', value: 'random' },
          { label: '固定题目', value: 'fixed' },
        ],
      },
    };

    return {
      ...toRefs(props),
      validate,
      localValue,
      configs,

      addConfig,
      removeConfig,
      onSyncQuestionBanksStat,
      questionBanksStat,

      questionTypeOptions,

      questionTableItems,
      arrangeModeItem,
    };
  },
});
</script>

<template lang="pug">
.com-assessment-question-paper-arrange-config.w-full.w-full
  .flex.items-center.justify-between.p-3
    .flex.items-center
      .ant-form-item-required
      .ml-3.mr-5.font-medium 排题方式
      TaRadioGroup(
        v-model:value='localValue.arrange_mode',
        :item='arrangeModeItem'
        :disabled='disabled'
      )
    .flex.items-center.cursor-pointer(@click='addConfig()' v-if='!disabled')
      TaIcon.w-4.h-4.mr-1(type='outline/plus-circle')
      div 新增配置
  .flex.items-center.w-full(v-for='config in configs', :key='config.key')
    .p-3(class='w-1/4')
      .flex.items-center.h-8
        .ant-form-item-required
        .ml-3.font-medium 题库
      TaSelect(
        :disabled='disabled'
        :item="{ options: { placeholder: '请选择题库' } }"
        path='/assessment/manage/question_banks',
        :ransackStr='JSON.stringify({ mode: "viewable" })',
        v-model:value='config.question_bank_id',
        @syncValue='onSyncQuestionBanksStat'
      )
    .p-3(class='w-1/4')
      .flex.items-center.h-8
        .ant-form-item-required
        .ml-3.font-medium 题型
      TaSelect(
        :disabled='disabled'
        :item="{ options: { placeholder: '请选择题型' } }"
        :options='questionTypeOptions()',
        v-model:value='config.question_type',
      )
    template(v-if='localValue.arrange_mode == "random"')
      .p-3(class='w-1/4' )
        .flex.items-center.h-8
          .ant-form-item-required
          .ml-3.font-medium 抽取数量（可抽取：{{questionBanksStat[config.question_bank_id]?.[config.question_type] || 0}}）
        TaInputNumber(
          :disabled='disabled',
          :min='0',
          :item="{ options: { placeholder: '请输入抽数量' } }"
          :max='questionBanksStat[config.question_bank_id]?.[config.question_type] || 0',
          v-model:value='config.question_count'
        )
      .p-3(class='w-1/4')
        .flex.items-center.h-8
          .ant-form-item-required
          .ml-3.font-medium {{ config.question_type == 'Assessment::Attr::Material' ? '每小题分数' : '每题分数' }}
        .flex.items-center.justify-between
          TaInputNumber(
            :disabled='disabled',
            :min='0',
            :item="{ options: { placeholder: config.question_type == 'Assessment::Attr::Material' ? '请输入每小题分数' : '请输入每题分数' } }"
            v-model:value='config.question_score'
          )
          TaIcon.w-5.h-5.cursor-pointer.ml-4(
            type='outline/trash',
            v-if='configs.length > 1 && !disabled'
            @click='removeConfig(config)',
          )
    template(v-else)
      .p-3(class='w-1/4')
        .flex.items-center.h-8
          .ant-form-item-required
          .ml-3.font-medium {{ config.question_type == 'Assessment::Attr::Material' ? '每小题分数' : '每题分数' }}
        TaInputNumber(
          :disabled='disabled',
          :min='0',
          :item="{ options: { placeholder: config.question_type == 'Assessment::Attr::Material' ? '请输入每小题分数' : '请输入每题分数' } }"
          v-model:value='config.question_score'
        )
      .p-3(class='w-1/4')
        .flex.items-center.h-8
          .ml-3.font-medium 
        .flex.items-center.justify-between
          TaApiField(
            :disabled='disabled',
            v-model:value='config.question_ids',
            :path='`/assessment/manage/questions`',
            :multiple='true',
            :tableItems='questionTableItems',
            :ransackStr='JSON.stringify({ question_bank_id_eq: config.question_bank_id || 0, kind_eq: config.question_type, parent_id_null: 1, mode: "viewable" })',
          )
            template(#display='{ open }')
              .ant-btn.ant-btn-dashed(v-if='disabled') {{ `已选（${config.question_ids.length }）` }}
              .ant-btn.ant-btn-primary.ant-btn-background-ghost(
                v-else
                @click.stop='open()'
              )
                .flex.items-center 
                  TaIcon.w-4.h-4.mr-1(type='outline/plus' v-if='!config.question_ids || config.question_ids.length == 0')
                  div {{ config.question_ids && config.question_ids.length > 0 ? `已选（${config.question_ids.length }）` : '选择题目'  }}
          TaIcon.w-5.h-5.cursor-pointer.ml-4(
            type='outline/trash',
            v-if='configs.length > 1 && !disabled'
            @click='removeConfig(config)',
          )
</template>

<style lang="stylus" scoped>
.com-assessment-question-paper-arrange-config
  >>> .ant-select-single.ant-select-lg:not(.ant-select-customize-input .ant-select-selector)
    height: 44px;
  >>> .ant-select-single.ant-select-lg:not(.ant-select-customize-input .ant-select-selector)
    padding: 2px 11px;
  >>> .ant-select-single .ant-select-selector .ant-select-selection-search-input
    margin-top: 2px;
  >>> .ta-radio-group .ant-radio-wrapper
    margin-bottom: 0
  .ant-form-item-required::before
    display: inline-block;
    color: #ff4d4f;
    font-size: 14px;
    font-family: SimSun, sans-serif;
    line-height: 1;
    content: '*';
</style>
