import { AssessmentAnswerSheet } from '@/engines/assessment/assessment-core/types/model';
import { VModel } from '@/lib/vails';

export class AssessmentAnswerSheetModel extends VModel<AssessmentAnswerSheet> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return AssessmentAnswerSheetModel.stateMapping()[this.reactiveRecord.state];
  // });
  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}
