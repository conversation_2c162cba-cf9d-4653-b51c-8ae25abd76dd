export const themeTemplate = {
  type: 'layout',
  model: {},
  key: 'layout_1638101654509_6',
  model_key: 'layout_1638101654509_6',
  fields: [
    {
      name: '普通布局',
      type: 'layout',
      fields: [
        {
          name: '类型',
          type: 'input',
          rules: [],
          model: { attr_type: 'string' },
          options: { span: 24, defaultValue: 'svr' },
          key: 'input_1638101667033_8',
          model_key: 'model_flag',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
        {
          name: '名称',
          type: 'input',
          rules: [],
          model: { attr_type: 'string' },
          options: { span: 24 },
          key: 'input_1638101667033_8',
          model_key: 'name',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
        {
          name: 'conf',
          type: 'key_layout',
          options: { span: 24 },
          key: 'key_layout_1638101674951_10',
          model_key: 'conf',
          fields: [
            {
              name: '普通布局',
              type: 'layout',
              fields: [
                {
                  name: '主题色',
                  type: 'color',
                  rules: [],
                  model: { attr_type: 'string' },
                  options: { span: 24 },
                  key: 'color_1638101905720_14',
                  model_key: '主题色',
                  fields: [],
                  conditions: [],
                  model_key_prefix: 'conf',
                },
                {
                  name: '主题字体颜色',
                  type: 'color',
                  rules: [],
                  model: { attr_type: 'string' },
                  options: { span: 24 },
                  key: 'color_1638101905720_14',
                  model_key: '主题字体颜色',
                  fields: [],
                  conditions: [],
                  model_key_prefix: 'conf',
                },
                {
                  name: 'Tab背景颜色',
                  type: 'color',
                  rules: [],
                  model: { attr_type: 'string' },
                  options: { span: 24 },
                  key: 'color_1638101985502_18',
                  model_key: 'Tab背景颜色',
                  fields: [],
                  conditions: [],
                  model_key_prefix: 'conf',
                },
                {
                  name: 'Tab字体颜色',
                  type: 'color',
                  rules: [],
                  model: { attr_type: 'string' },
                  options: { span: 24 },
                  key: 'color_1638101985502_18',
                  model_key: 'Tab字体颜色',
                  fields: [],
                  conditions: [],
                  model_key_prefix: 'conf',
                },
                {
                  name: '背景色',
                  type: 'color',
                  rules: [],
                  model: { attr_type: 'string' },
                  options: { span: 24 },
                  key: 'color_1638101908482_15',
                  model_key: '背景色',
                  fields: [],
                  conditions: [],
                  model_key_prefix: 'conf',
                },
                {
                  name: '卡片颜色',
                  type: 'color',
                  rules: [],
                  model: { attr_type: 'string' },
                  options: { span: 24 },
                  key: 'color_1638101936485_16',
                  model_key: '卡片颜色',
                  fields: [],
                  conditions: [],
                  model_key_prefix: 'conf',
                },
                {
                  name: '卡片字体颜色',
                  type: 'color',
                  rules: [],
                  model: { attr_type: 'string' },
                  options: { span: 24 },
                  key: 'color_1638101936485_16',
                  model_key: '卡片字体颜色',
                  fields: [],
                  conditions: [],
                  model_key_prefix: 'conf',
                },
                {
                  name: '标题标签色',
                  type: 'color',
                  rules: [],
                  model: { attr_type: 'string' },
                  options: { span: 24 },
                  key: 'color_1638101965717_17',
                  model_key: '标题标签色',
                  fields: [],
                  conditions: [],
                  model_key_prefix: 'conf',
                },
              ],
              options: { span: 24, label: { width: 80, align: 'left' } },
              key: 'layout_1638101685615_11',
              model_key: 'layout_1638101685615_11',
              conditions: [],
              model_key_prefix: 'conf',
            },
          ],
          conditions: [],
          model_key_prefix: '',
        },
      ],
      options: { span: 24, label: { width: 80, align: 'left' } },
      key: 'layout_1638101658678_7',
      model_key: 'layout_1638101658678_7',
      conditions: [],
      model_key_prefix: '',
    },
  ],
  conditions: [],
  options: { label: {}, disabled_actions: {}, create_text: '提交', update_text: '提交' },
  actions: [
    { key: 'create', enabled: true },
    { key: 'update', enabled: true },
    { key: 'delete', enabled: true },
    { key: 'import', enabled: false },
    { key: 'export', enabled: false },
  ],
  column_attributes: [
    {
      _id: 'column_attributes_1638103243423_20',
      dataIndex: 'name',
      title: '名称',
      render: 'TableRendersAuto',
    },
  ],
};

export const formThemeTemplate = {
  type: 'layout',
  model: {},
  key: 'layout_1642767937192_2',
  model_key: 'layout_1642767937192_2',
  fields: [
    {
      name: '类型',
      type: 'input',
      rules: [],
      model: { attr_type: 'string' },
      options: { span: 24, defaultValue: 'form' },
      key: 'input_1642768075827_10',
      model_key: 'model_flag',
      fields: [],
      conditions: [],
      model_key_prefix: '',
    },
    {
      name: '名称',
      type: 'input',
      rules: [],
      model: { attr_type: 'string' },
      options: { span: 24 },
      key: 'input_1642768809991_11',
      model_key: 'name',
      fields: [],
      conditions: [],
      model_key_prefix: '',
    },
    {
      name: '嵌套对象',
      type: 'key_layout',
      options: { span: 24 },
      key: 'key_layout_1642767957873_4',
      model_key: 'model_payload',
      fields: [
        {
          name: '嵌套对象',
          type: 'key_layout',
          options: { span: 24 },
          key: 'key_layout_1642768053544_9',
          model_key: 'value',
          fields: [
            {
              name: '圆角',
              type: 'switch',
              rules: [],
              model: { attr_type: 'boolean' },
              options: { span: 24 },
              key: 'switch_1642768827390_12',
              model_key: 'radius',
              fields: [],
              conditions: [],
              model_key_prefix: 'model_payload.value',
            },
            {
              name: '嵌套对象',
              type: 'key_layout',
              options: { span: 24 },
              key: 'key_layout_1642767968861_6',
              model_key: 'background',
              fields: [
                {
                  name: '背景色',
                  type: 'input',
                  rules: [],
                  model: { attr_type: 'string' },
                  options: { span: 24 },
                  key: 'input_1642767959593_5',
                  model_key: 'color',
                  fields: [],
                  conditions: [],
                  model_key_prefix: 'model_payload.value.background',
                },
              ],
              conditions: [],
              model_key_prefix: 'model_payload.value',
            },
            {
              name: '嵌套对象',
              type: 'key_layout',
              options: { span: 24 },
              key: 'key_layout_1642768003644_7',
              model_key: 'card',
              fields: [
                {
                  name: '图片',
                  type: 'image',
                  rules: [],
                  model: { attr_type: 'array' },
                  options: { span: 24, multiple: true },
                  key: 'image_1642768021642_8',
                  model_key: 'image',
                  fields: [],
                  conditions: [],
                  model_key_prefix: 'model_payload.value.card',
                },
              ],
              conditions: [],
              model_key_prefix: 'model_payload.value',
            },
          ],
          conditions: [],
          model_key_prefix: 'model_payload',
        },
      ],
      conditions: [],
      model_key_prefix: '',
    },
  ],
  conditions: [],
  options: {
    label: {},
    disabled_actions: {},
    theme: {
      card: {
        image: [
          {
            file: {},
            exist: true,
            width: '1400',
            cdnTag: 0,
            chunks: true,
            height: '300',
            loaded: '124571',
            status: 'done',
            chunkId: 1,
            fileKey: '57930ac77f56c40c414a8fe0484333f4',
            percent: 100,
            version: '2',
            duration: null,
            fileName: '模版:夜景.png',
            fileSize: '124571',
            fileType: 'png',
            mimeType: 'image/png',
            chunkSize: 4194304,
            transcoding: null,
            downloadPath: '/party/2020/image/57/93/57930ac77f56c40c414a8fe0484333f4.png',
            fileCategory: 'image',
            category_type: 'image',
          },
        ],
      },
      background: { color: '#99A9DA' },
      radius: true,
    },
    create_text: '提交',
    update_text: '提交',
  },
  actions: [
    { key: 'create', enabled: true },
    { key: 'update', enabled: true },
    { key: 'delete', enabled: true },
    { key: 'import', enabled: false },
    { key: 'export', enabled: false },
  ],
  model_flag: 'form',
  column_attributes: [
    {
      _id: 'column_attributes_1638103243423_20',
      dataIndex: 'name',
      title: '名称',
      render: 'TableRendersAuto',
    },
  ],
};
