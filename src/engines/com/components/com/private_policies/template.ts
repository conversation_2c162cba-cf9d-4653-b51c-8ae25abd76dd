export const PrivatePolicyTemplate = {
  type: 'layout',
  model: {},
  key: 'layout_1649683844758_0',
  model_key: 'layout_1649683844758_0',
  fields: [
    {
      name: '名称',
      icon: 'FolderOutlined',
      type: 'input',
      rules: [],
      model: { attr_type: 'string' },
      options: { span: 24 },
      key: 'input_1649683847435_1',
      model_key: 'name',
      fields: [],
      conditions: [],
      model_key_prefix: '',
    },
    {
      name: '关键字',
      icon: 'FolderOutlined',
      type: 'input',
      rules: [],
      model: { attr_type: 'string' },
      options: { span: 24 },
      key: 'input_1649683847768_2',
      model_key: 'key',
      fields: [],
      conditions: [],
      model_key_prefix: '',
    },
    {
      name: '内容',
      icon: 'FolderOutlined',
      type: 'content',
      rules: [],
      model: { attr_type: 'array' },
      options: { span: 24 },
      key: 'content_1649683873468_3',
      model_key: 'content.modules',
      fields: [],
      conditions: [],
      model_key_prefix: '',
    },
  ],
  conditions: [],
  options: {
    label: {},
    disabled_actions: {},
    theme: { card: {}, background: {} },
    create_text: '提交',
    update_text: '提交',
  },
  actions: [
    { key: 'create', enabled: true },
    { key: 'update', enabled: true },
    { key: 'delete', enabled: true },
    { key: 'import', enabled: false },
    { key: 'export', enabled: false },
  ],
  column_attributes: [
    {
      _id: 'column_attributes_1638094304115_3',
      dataIndex: 'name',
      title: '名称',
      render: 'TableRendersAuto',
    },
    {
      _id: 'column_attributes_1638094312112_4',
      dataIndex: 'key',
      title: '关键字',
      render: 'TableRendersAuto',
    },
  ],
};
