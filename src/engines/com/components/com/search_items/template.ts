export const comAdminSearchItemTemplate = {
  type: 'layout',
  model: {},
  key: 'layout_1647929614848_0',
  model_key: 'layout_1647929614848_0',
  fields: [
    {
      name: '名称',
      icon: 'FolderOutlined',
      type: 'input',
      rules: [],
      model: { attr_type: 'string', summary: true },
      options: { span: 24 },
      key: 'input_1647929617779_1',
      model_key: 'name',
      fields: [],
      conditions: [],
      model_key_prefix: '',
    },
    {
      name: '开启',
      icon: 'FolderOutlined',
      type: 'switch',
      rules: [],
      model: { attr_type: 'boolean', summary: true },
      options: { span: 24 },
      key: 'switch_1647929662395_2',
      model_key: 'enabled',
      fields: [],
      conditions: [],
      model_key_prefix: '',
    },
    {
      name: '分组',
      icon: 'FolderOutlined',
      type: 'input',
      rules: [],
      model: { attr_type: 'string', summary: true },
      options: { span: 24 },
      key: 'input_1647929674562_3',
      model_key: 'group_name',
      fields: [],
      conditions: [],
      model_key_prefix: '',
    },
    {
      name: '嵌套对象',
      type: 'key_layout',
      options: {
        span: 24,
      },
      key: 'key_layout_1648016093027_3',
      model_key: 'conditions',
      fields: [
        {
          name: '搜索条件',
          icon: 'FolderOutlined',
          type: 'list',
          fields: [
            {
              name: 'ransack key',
              icon: 'FolderOutlined',
              type: 'input',
              rules: [],
              model: { attr_type: 'string' },
              options: { span: 24 },
              key: 'input_1647929775199_6',
              model_key: 'key',
              fields: [],
              conditions: [],
              model_key_prefix: '',
            },
            {
              name: 'value',
              icon: 'FolderOutlined',
              type: 'input',
              rules: [],
              model: { attr_type: 'string' },
              options: { span: 24 },
              key: 'input_1647929773450_5',
              model_key: 'value',
              fields: [],
              conditions: [],
              model_key_prefix: '',
            },
          ],
          rules: [],
          model: { attr_type: 'array' },
          options: { span: 24, disabled_actions: {}, edit_directly: true },
          key: 'list_1647929690211_4',
          model_key: 'items',
          conditions: [],
          model_key_prefix: 'conditions',
        },
      ],
      conditions: [],
      model_key_prefix: '',
    },
  ],
  conditions: [],
  options: {
    label: {},
    disabled_actions: {},
    theme: { card: {}, background: {} },
    create_text: '提交',
    update_text: '提交',
  },
  actions: [
    { key: 'create', enabled: true },
    { key: 'update', enabled: true },
    { key: 'delete', enabled: true },
    { key: 'import', enabled: false },
    { key: 'export', enabled: false },
  ],
  column_attributes: [
    {
      _id: 'column_attributes_1638103243423_20',
      dataIndex: 'name',
      title: '名称',
      render: 'TableRendersAuto',
    },
    {
      _id: 'column_attributes_1638103243423_21',
      dataIndex: 'enabled',
      title: '开启',
      render: 'TableRendersAuto',
    },
    {
      _id: 'column_attributes_1638103243423_22',
      dataIndex: 'group_name',
      title: '分组',
      render: 'TableRendersAuto',
    },
  ],
};
