<script lang="ts">
import { computed, defineComponent, toRefs } from 'vue';
// import { TaIndexViewTabInterface } from '@/components/global/ta-component/TaIndexView/types';
// import { VObject } from '@/lib/vails/model';
// import ComBpmRulesShow from './ComBpmRulesShow.vue';
import { bpmRuleTemplate } from './template';

const ComBpmAdminRulesIndex = defineComponent({
  name: 'ComBpmAdminRulesIndex',
  components: {
    // ComBpmRulesShow,
  },
  props: {
    store: { type: Object, required: true },
  },
  setup(props) {
    const config = computed(() => ({
      recordName: '流速规则',
      store: props.store,
      // pagination: {
      //   perPage: 15,
      //   showPageSizeChanger: false,
      //   hideOnSinglePage: false,
      //   showSizeChanger: false,
      // }
      template: bpmRuleTemplate,
      // detail: {
      //   mode: 'auto',
      //   mode: 'drawer',
      //   width: '1100px',
      // },
      mode: 'table',
      // editApi:
      // showCount: true,
      // actions: [
      //  { key: 'create', enabled: true },
      //  { key: 'update', enabled: true },
      //  { key: 'delete', enabled: true },
      //  { key: 'import', enabled: true },
      //  { key: 'export', enabled: true },
      // ],
      table: {
        scroll: { y: 'auto' },
        // columns: [{ dataIndex: 'id', title: 'ID' }],
      },
      list: {
        scroll: { y: 'auto' },
      },
      // selection: {
      //   showByDefault: false;
      //   showSwitch: false;
      // };
      // searcherSimpleOptions: [{key: 'title', label: '标题', type: 'string' }],
    }));

    // const statistics = ref({
    //   key1: 0,
    //   key2: 0,
    // });

    // const tabs = computed<TaIndexViewTabInterface[]>(() => [
    //   {
    //     key: 'key1',
    //     label: '标签1',
    //     num: statistics.value.key1,
    //     query: {},
    //   },
    //   {
    //     key: 'key2',
    //     label: '标签2',
    //     num: statistics.value.key2,
    //     query: {},
    //   },
    // ]);

    // const onIndex = (data: VObject) => {
    //   statistics.value = data.statistics;
    // };

    return {
      ...toRefs(props),
      config,
      // tabs,
      // onIndex,
    };
  },
});

export default ComBpmAdminRulesIndex;
</script>

<template lang="pug">
.com-bpm-admin-rules-index
  TaIndexView(:config='config')
    //- template(#card='{ record }')
    //- template(#table)
    //-    a-table-column(:autoHeight='true' title='名称' dataIndex='record')
    //- template(#detail='{ record, onClose }')
    //-   ComBpmRulesShow(
    //-     v-if='record.id'
    //-     :store='store'
    //-     :extendRoute='`/bpm/admin/rules/${record.id}`'
    //-     :editable='editable'
    //-     @afterDelete='onClose'
    //-     @afterExtend='onClose'
    //-   )
</template>

<style lang="stylus" scoped>
.com-bpm-admin-rules-index
  height 100%
  width 100%
</style>
