export const bpmRuleTemplate = {
  options: {
    label: {},
    disabled_actions: {},
    theme: { card: { content: '<p><br></p>' }, background: {}, form: {} },
    create_text: '提交',
    update_text: '提交',
    searcher: [],
  },
  model: {},
  column_attributes: [
    {
      _id: 'column_attributes_1636030960498_0',
      title: ['名称'],
      render: 'TableRendersAuto',
      dataIndex: 'name',
    },
    {
      _id: 'column_attributes_1636030960498_1',
      title: ['时限'],
      render: 'ComBpmTimeInSecondView',
      dataIndex: 'time_in_second',
    },
  ],
  actions: [
    { key: 'create', enabled: true },
    { key: 'update', enabled: true },
    { key: 'delete', enabled: true },
    { key: 'import', enabled: true },
    { key: 'export', enabled: true },
  ],
  type: 'layout',
  key: 'layout_1683513857921_0',
  model_key: 'layout_1683513857921_0',
  fields: [
    {
      name: '名称',
      icon: 'FolderOutlined',
      type: 'input',
      rules: [],
      model: { attr_type: 'string' },
      options: { span: 24 },
      key: 'input_1683513876601_1',
      model_key: 'name',
      fields: [],
      conditions: [],
      model_key_prefix: '',
    },
    {
      name: '类型',
      icon: 'FolderOutlined',
      type: 'radio',
      rules: [],
      model: {
        attr_type: 'string',
      },
      options: {
        select: [
          {
            label: '按流程',
            value: 'Bpm::RuleByInstance',
          },
          {
            label: '按规则',
            value: 'Bpm::RuleByToken',
          },
        ],
        multiple: false,
        span: 24,
        table_items: [],
        display_configurable_form: {},
        import_export_headers: [
          {
            _id: '1683516473144_0',
          },
        ],
      },
      key: 'radio_1683516472981_1',
      model_key: 'type',
      fields: [],
      conditions: [],
      model_key_prefix: '',
    },
    {
      name: '节点范围',
      icon: 'FolderOutlined',
      type: 'api',
      rules: [],
      model: {
        attr_type: 'array',
      },
      options: {
        span: 24,
        multiple: true,
        table_items: [],
        display_configurable_form: {},
        import_export_headers: [
          {
            _id: '1700306613140_1',
          },
        ],
        path: 'bpm/admin/workflows/${workflowId}/places',
      },
      key: 'api_1700306589982_1',
      model_key: 'options.place_ids',
      fields: [],
      conditions: [],
      model_key_prefix: '',
    },
    {
      name: '流速时限',
      icon: 'FolderOutlined',
      type: 'dynamic_component',
      rules: [],
      model: {},
      options: { span: 24, dynamic_component: 'ComBpmTimeInSecond' },
      key: 'dynamic_component_1683513893547_2',
      model_key: 'time_in_second',
      fields: [],
      conditions: [],
      model_key_prefix: '',
    },
  ],
  conditions: [],
};
