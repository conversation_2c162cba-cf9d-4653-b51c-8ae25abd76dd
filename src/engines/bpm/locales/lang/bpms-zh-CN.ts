export default {
  bpm: {
    user: {
      workflow: {
        desc: '流程描述',
      },
    },
    stat: {
      workflow: {
        recordName: '流速分析',
      },
    },
    ComBpmInstancesIndex: {
      card: {
        unred: '未读',
        wait: '待我处理',
        initiated: '我发起的',
        processed: '我已处理',
        carbon: '抄送我的',
      },
      modes: {
        talbe: '表格',
        list: '列表',
        card: '卡片',
      },
      filterfileds: {
        status: '状态',
        inprogress: '进行中',
        waitsubmitted: '待提交',
        completed: '已完成',
        terminated: '已终止',
        initiator: '发起人',
        currentstate: '当前阶段',
        initiationtime: '发起时间',
      },
      sortfilter: {
        levelformtoptobottom: '按优先级从高到低',
        initiationfromtoold: '按发起时间从近到远',
        initiationoldtofrom: '按发起时间从远到近',
        lasttimefromtoold: '按最后处理时间从近到远',
        lastimeoldtofrom: '按最后处理时间从远到近',
      },
      search: '搜索',
      placeholder: '搜索编号、部门、发起人姓名、发起人账号、其他信息',
      sort: '排序',
      expert: '导出',
    },
    ComBpmInstanceFilters: {
      in: '包含',
      notin: '不包含',
      isnull: '为空',
      notnull: '不为空',
      lt: '早于',
      gt: '晚于',
      eq: '等于',
      noteq: '不等于',
      messagewaring: '最多支持5个筛选条件',
      and: '所有',
      or: '任意',
      picktime: '具体时间',
      today: '今天',
      yesterday: '昨天',
      week: '本周',
      month: '本月',
      lastmonth: '上月',
      sevendaysago: '过去7天',
      thirtydaysago: '过去30天',
      filters: '筛选',
      setfilters: '设置筛选条件',
      meetconditions: '符合以下',
      conditions: '条件',
      addconditions: '添加条件',
    },
    ComBpmInstanceIndexSidebar: {
      process: '流程',
      classification: '流程分类',
      clear: '清除',
    },
    ComBpmInstanceIndexTable: {
      name: '审批',
      title: '标题',
      time: '发起时间',
      lasttoken: '当前阶段',
      operator: '操作者',
      lastProcessAt: '最后处理时间',
      other: '额外信息',
      state: '状态',
    },
    ComBpmInstanceIndexList: {
      name: '审批',
    },
    ComBpmInstanceCard: {
      seq: '编号',
      creator: '申请人',
      lasttoken: '当前阶段',
      operator: '操作者',
      establish: '发起人',
      establishTime: '发起时间',
    },
    ComBpmInstanceIndexFlowChart: {
      processing: '进行中',
      completed: '已完成',
      terminated: '已终止',
      created: '待提交',
    },
    BpmUserInstancesIndex: {
      title: '动态',
    },
    route: {
      BpmUserInstancesIndex: '业务流程',
    },
    state: {
      all: '全部',
      created: '待提交',
      preparing: '待处理',
      processing: '进行中',
      completed: '已完成',
      checked: '审核通过',
      received: '已收件',
      rejected: '已驳回',
      failed: '已退回',
      canceled: '已取消',
      terminated: '已终止',
      been_fail: '被退回',
      been_reject: '被驳回',
    },
  },
};
