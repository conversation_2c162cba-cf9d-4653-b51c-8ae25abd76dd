export default {
  bpm: {
    user: {
      workflow: {
        desc: 'Process description',
      },
    },
    stat: {
      workflow: {
        recordName: 'Flow rate analysis',
      },
    },
    ComBpmInstancesIndex: {
      card: {
        unred: 'Unread',
        wait: 'Pending My Action',
        initiated: 'Initiated by Me',
        processed: 'Processed by Me',
        carbon: 'CCed to Me',
      },
      modes: {
        talbe: 'Table',
        list: 'List',
        card: 'Card',
      },
      filterfileds: {
        status: 'Status',
        inprogress: 'In Progress',
        waitsubmitted: 'Pending Submission',
        completed: 'Completed',
        terminated: 'Terminated',
        initiator: 'Initiator',
        currentstate: 'Current Stage',
        initiationtime: 'Initiation Time',
      },
      sortfilter: {
        initiationfromtoold: 'By Initiation Time: Newest First',
        initiationoldtofrom: 'By Initiation Time: Oldest First',
        lasttimefromtoold: 'By Last Processing Time: Newest First',
        lastimeoldtofrom: 'By Last Processing Time: Oldest First',
      },
      search: 'Search',
      placeholder: 'Search by ID, Department, Initiator Name, Initiator Account, Other Information',
      sort: 'Sort',
      expert: 'Export',
    },
    ComBpmInstanceFilters: {
      in: 'Includes',
      notin: 'Does Not Include',
      isnull: 'Is Null',
      notnull: 'Is Not Null',
      lt: 'Earlier Than',
      gt: 'Later Than',
      eq: 'Equals',
      noteq: 'Does Not Equal',
      messagewaring: 'Up to 5 filter conditions are supported',
      and: 'All',
      or: 'Any',
      picktime: 'Specific Time',
      today: 'Today',
      yesterday: 'Yesterday',
      week: 'This Week',
      month: 'This Month',
      lastmonth: 'Last Month',
      sevendaysago: 'Last 7 Days',
      thirtydaysago: 'Last 30 Days',
      filters: 'Filters',
      setfilters: 'Set Filters',
      meetconditions: 'Meet the Following',
      conditions: 'Conditions',
      addconditions: 'Add Condition',
    },
    ComBpmInstanceIndexSidebar: {
      process: 'Process',
      classification: 'Process Classification',
      clear: 'Clear',
    },
    ComBpmInstanceIndexTable: {
      name: 'Approval',
      title: 'Title',
      time: 'Initiation Time',
      lasttoken: 'Current Stage',
      operator: 'Operator',
      lastProcessAt: 'Last Processing Time',
      other: 'Additional Information',
      state: 'Status',
    },
    ComBpmInstanceIndexList: {
      name: 'Approval',
    },
    ComBpmInstanceCard: {
      seq: 'ID',
      creator: 'Applicant',
      lasttoken: 'Current Stage',
      operator: 'Operator',
      establish: 'Initiator',
      establishTime: 'Initiation Time',
    },
    ComBpmInstanceIndexFlowChart: {
      processing: 'In Progress',
      completed: 'Completed',
      terminated: 'Terminated',
      created: 'Pending Submission',
    },
    BpmUserInstancesIndex: {
      title: 'Trend',
    },
    route: {
      BpmUserInstancesIndex: 'Business Process',
    },
    state: {
      all: 'All',
      created: 'Pending Submission',
      preparing: 'Pending Processing',
      processing: 'In Progress',
      completed: 'Completed',
      checked: 'Approved',
      received: 'Received',
      rejected: 'Rejected',
      failed: 'Returned',
      canceled: 'Canceled',
      terminated: 'Terminated',
    },
  },
};
