export default [
  // {
  //   path: '/iest/ai/chat/user/conversations',
  //   name: 'iestAiChatUserConversationsIndex',
  //   component: () =>
  //     import(
  //       /* webpackChunkName: "iestAiChatUserConversationsIndex" */ '@/engines/iest/views/iest/ai/chat/user/conversations/Index.vue'
  //     ),
  //   meta: {
  //     title: '对话',
  //   },
  // },
  {
    name: 'iestAiChatUserConversations',
    path: '/iest/ai/chat/user/',
    component: () =>
      import(
        /* webpackChunkName: "iestAiChatUserConversations" */
        '@/engines/iest/components/iest/conversations/ComIestConversationLayout.vue'
      ),
    children: [
      {
        path: '/iest/ai/chat/user/conversations/:conversationId',
        name: 'iestAiChatUserConversationsShow',
        component: () =>
          import(
            /* webpackChunkName: "iestAiChatUserConversationsShow" */ '@/engines/iest/views/iest/ai/chat/user/conversations/Show.vue'
          ),
        meta: {
          title: 'AI',
        },
      },
      {
        path: '/iest/ai/chat/user/conversations/new',
        name: 'iestAiChatUserConversationsNew',
        component: () =>
          import(
            /* webpackChunkName: "iestAiChatUserConversationsNew" */ '@/engines/iest/views/iest/ai/chat/user/conversations/New.vue'
          ),
        meta: {
          title: 'AI',
        },
      },
    ],
  },
];
