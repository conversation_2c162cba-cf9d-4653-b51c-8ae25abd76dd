export default [
  {
    path: '/serve/user/messages',
    name: 'serveUserMessagesIndex',
    component: () =>
      import(
        /* webpackChunkName: "serveUserMessagesIndex" */ '@/engines/iest/views/serve/user/messages/Index.vue'
      ),
    meta: {
      title: '消息管理',
    },
  },
  {
    path: '/serve/user/messages/:messageId',
    name: 'serveUserMessagesShow',
    component: () =>
      import(
        /* webpackChunkName: "serveUserMessagesShow" */ '@/engines/iest/views/serve/user/messages/Show.vue'
      ),
    meta: {
      title: '消息详情',
    },
  },
];
