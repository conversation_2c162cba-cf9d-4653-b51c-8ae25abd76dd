export default [
  {
    path: '/serve/manage/ai_message_templates',
    name: 'serveManageAiMessageTemplatesIndex',
    component: () =>
      import(
        /* webpackChunkName: "serveManageAiMessageTemplatesIndex" */ '@/engines/iest/views/serve/manage/ai_message_templates/Index.vue'
      ),
    meta: {
      title: 'AI消息模板',
      layout: 'message',
    },
  },
  {
    path: '/serve/manage/ai_message_templates/:ai_message_templateId',
    name: 'serveManageAiMessageTemplatesShow',
    component: () =>
      import(
        /* webpackChunkName: "serveManageAiMessageTemplatesShow" */ '@/engines/iest/views/serve/manage/ai_message_templates/Show.vue'
      ),
    meta: {
      title: 'AI消息模板详情',
      layout: 'message',
    },
  },
];
