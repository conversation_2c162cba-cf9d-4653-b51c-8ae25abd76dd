import { My<PERSON><PERSON> } from '@/apis/MyApi';
import { ServeActivity } from '@/engines/iest/serve-core/types/model';
import { VApiConfig } from '@/lib/vails/api';

export class ServeManageActivitiesApi extends MyApi<ServeActivity> {
  constructor(config?: VApiConfig) {
    super({
      namespace: '/serve/manage',
      name: 'activity',
      actions: [
        { name: 'crawl_wechat_article', method: 'post', on: 'collection' },
        { name: 'batch_crawl_wechat_articles', method: 'post', on: 'collection' },
        { name: 'crawl_task_status', method: 'get', on: 'collection' },
        { name: 'crawl_tasks_list', method: 'get', on: 'collection' },
        { name: 'cleanup_crawl_tasks', method: 'delete', on: 'collection' },
        { name: 'delete_crawl_task', method: 'delete', on: 'collection' },
      ],
      ...config,
    });
  }

  // 查询微信抓取任务状态
  async getCrawlTaskStatus(taskId: string) {
    const url = this.getCollectionPath();
    return this.request.get(`${url}/crawl_task_status/${taskId}`);
  }

  // 获取所有微信抓取任务列表
  async getCrawlTasksList() {
    return this.sendCollectionAction('crawl_tasks_list');
  }

  // 清理已完成的任务
  async cleanupCrawlTasks() {
    return this.sendCollectionAction('cleanup_crawl_tasks', { method: 'delete' });
  }

  // 删除单个任务
  async deleteCrawlTask(taskId: string) {
    const url = this.getCollectionPath();
    return this.request.delete(`${url}/delete_crawl_task/${taskId}`);
  }
}
