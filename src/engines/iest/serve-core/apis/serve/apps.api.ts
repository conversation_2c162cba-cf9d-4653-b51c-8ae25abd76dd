import { VApiConfig } from '@/lib/vails/api';
import { MyApi } from '@/apis/MyApi';
import { ServeApp } from '@/engines/serve-mobile/serve-core/types/model';

export class ServeAppsApi extends MyApi<ServeApp> {
  constructor(config?: VApiConfig) {
    super({
      namespace: '/serve',
      name: 'app',
      actions:[{name:'ta_resource_statistic',method:'post',on:'member'}],
      ...config,
    });
  }
}
