import { MyApi } from '@/apis/MyApi';
import { VApiConfig } from '@/lib/vails/api';
import { ServePack } from '../../../types/model';

export class ServeUserPacksApi extends MyApi<ServePack> {
  constructor(config?: VApiConfig) {
    super({
      namespace: '/serve/user',
      name: 'pack',
      actions: [{ name: 'refresh_contents_by_rule', method: 'post', on: 'member' }],
      ...config,
    });
  }
}
