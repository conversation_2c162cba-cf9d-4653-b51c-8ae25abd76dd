import { VApiConfig } from '@/lib/vails/api';
import { MyApi } from '@/apis/MyApi';
import { ServeActivity } from '../../../../serve-core/types/model';

export class ServeUserActivitiesApi extends MyApi<ServeActivity> {
  constructor(config?: VApiConfig) {
    super({
      namespace: '/serve/user',
      name: 'activity',
      actions: [
        { name: 'mark', method: 'post', on: 'member' },
        { name: 'unmark', method: 'post', on: 'member' },
      ],
      ...config,
    });
  }
}
