import { VApiConfig } from '@/lib/vails/api';
import { MyApi } from '@/apis/MyApi';
import { ServeRule } from '@/engines/iest/serve-core/types/model';

export class ServeUserRulesApi extends MyApi<ServeRule> {
  constructor(config?: VApiConfig) {
    super({
      namespace: '/serve/user',
      name: 'rule',
      actions: [
        {
          name: 'generate_content_by_template_prompt',
          method: 'post',
          on: 'member',
        },
      ],
      ...config,
    });
  }
}
