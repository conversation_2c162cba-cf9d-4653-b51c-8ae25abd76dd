import { VApiConfig } from '@/lib/vails/api';
import { MyApi } from '@/apis/MyApi';
import { ServeRule } from '../../../types/model';

export class ServeManageRulesApi extends MyApi<ServeRule> {
  constructor(config?: VApiConfig) {
    super({
      namespace: '/serve/manage',
      name: 'rule',
      actions: [
        {
          name: 'generate_content_by_template_prompt',
          on: 'member',
          method: 'post',
        },
      ],
      ...config,
    });
  }
}
