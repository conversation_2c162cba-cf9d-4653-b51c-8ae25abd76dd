import { MyApi } from '@/apis/MyApi';
import { VApiConfig } from '@/lib/vails/api';
import { ServeAiMessageSquare } from '../../../types/model';

export class ServeManageAiMessageSquaresApi extends MyApi<ServeAiMessageSquare> {
  constructor(config?: VApiConfig) {
    super({
      namespace: '/serve/manage',
      name: 'ai_message_square',
      actions: [
        { name: 'clone_to_template', method: 'post', on: 'member' },
        { name: 'mark', method: 'post', on: 'member' },
        { name: 'unmark', method: 'post', on: 'member' },
      ],
      ...config,
    });
  }
}
