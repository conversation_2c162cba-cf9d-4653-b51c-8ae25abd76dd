import { VModel, VObject } from '@/lib/vails';
import { ServeEntry } from '@/engines/serve/serve-core/types/model';

export enum ServeEntryType {
  Exp = 'Serve::ExpEntry',
  Bpm = 'Serve::BpmEntry',
  Oper = 'Serve::OperEntry',
  Talent='Serve::TalentEntry',
  Coupon='Serve::CouponEntry',
}

export class ServeEntryModel extends VModel<ServeEntry> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return ServeEntryModel.stateMapping()[this.reactiveRecord.state];
  // });
  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}
