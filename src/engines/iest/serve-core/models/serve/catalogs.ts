import { VModel, VObject } from '@/lib/vails';
import { ServeCatalog } from '@/engines/serve/serve-core/types/model';

export class ServeCatalogModel extends VModel<ServeCatalog> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return ServeCatalogModel.stateMapping()[this.reactiveRecord.state];
  // });

  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}
