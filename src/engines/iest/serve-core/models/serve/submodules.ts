import { VModel, VObject } from '@/lib/vails';
import { ServeSubmodule } from '@/engines/serve/serve-core/types/model';

export class ServeSubmoduleModel extends VModel<ServeSubmodule> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return ServeSubmoduleModel.stateMapping()[this.reactiveRecord.state];
  // });
  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}
