import { VModel } from '@/lib/vails';
import { ServeAiMessage } from '../../../types/model';

export class ServeAiMessageModel extends VModel<ServeAiMessage> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return ServeAiMessageModel.stateMapping()[this.reactiveRecord.state];
  // });
  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}
