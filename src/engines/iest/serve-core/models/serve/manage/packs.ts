import { VModel, VObject, VStore } from '@/lib/vails';
import { ServePack } from '@/engines/iest/serve-core/types/model';
import { ServeManageMessagesApi } from '../../../apis/serve/manage/messages.api';
import { ServeMessageModel } from './messages';

export class ServePackModel extends VModel<ServePack> {
  stateConfig = this.computedAttr('stateConfig', () => {
    return ServePackModel.stateMapping()[this.reactiveRecord.state];
  });

  static createMessageStore(params = {}) {
    return new VStore(new ServeManageMessagesApi(params), ServeMessageModel);
  }

  static stateMapping(): VObject {
    return {
      pending: { label: '待审核', color: 'blue' },
      sending: { label: '待发送', color: 'yellow' },
      finished: { label: '已发送', color: 'green' },
      terminated: { label: '已终止', color: 'gray' },
    };
  }
}
