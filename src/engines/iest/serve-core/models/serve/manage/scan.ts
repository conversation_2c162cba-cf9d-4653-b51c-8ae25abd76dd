import { VModel, VObject } from '@/lib/vails';
import { ServeScan } from '@/engines/serve-mobile/serve-core/types/model';

export class ServeScanModel extends VModel<ServeScan> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return ServeScanModel.stateMapping()[this.reactiveRecord.state];
  // });

  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}
