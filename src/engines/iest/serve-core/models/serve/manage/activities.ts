import { VModel, VObject } from '@/lib/vails';
import { ServeActivity } from '../../../types/model';
import FileServer from '@/components/global/ta-component/file/servers';

export class ServeActivityModel extends VModel<ServeActivity> {
  coverImageUrl = this.computedAttr('coverImageUrl', () => {
    return (
      this.reactiveRecord.cover_image?.files?.[0]?.url ||
      new FileServer().getDownloadUrl(this.reactiveRecord.cover_image?.files?.[0] || { url: '' })
    );
  });
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return ServeActivityModel.stateMapping()[this.reactiveRecord.state];
  // });
  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}
