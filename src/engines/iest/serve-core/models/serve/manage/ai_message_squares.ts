import { VModel } from '@/lib/vails';
import { ServeAiMessageSquare } from '../../../types/model';

export class ServeAiMessageSquareModel extends VModel<ServeAiMessageSquare> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return ServeAiMessageSquareModel.stateMapping()[this.reactiveRecord.state];
  // });

  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}
