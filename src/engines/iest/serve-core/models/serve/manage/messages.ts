import { VModel, VObject } from '@/lib/vails';
import { ServeMessage } from '@/engines/iest/serve-core/types/model';

export class ServeMessageModel extends VModel<ServeMessage> {
  flag = 'msg';
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return ServeMessageModel.stateMapping()[this.reactiveRecord.state];
  // });

  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}
