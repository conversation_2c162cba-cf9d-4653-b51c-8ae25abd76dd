import { VModel, VObject } from '@/lib/vails';
import { ServeEntry } from '@/engines/serve-mobile/serve-core/types/model';

export class ServeEntryModel extends VModel<ServeEntry> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return ServeEntryModel.stateMapping()[this.reactiveRecord.state];
  // });

  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}
