import { ServeAiMessageTemplate } from '@/engines/iest/serve-core/types/model';
import { VModel } from '@/lib/vails';

export class ServeAiMessageTemplateModel extends VModel<ServeAiMessageTemplate> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return ServeAiMessageTemplateModel.stateMapping()[this.reactiveRecord.state];
  // });
  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}
