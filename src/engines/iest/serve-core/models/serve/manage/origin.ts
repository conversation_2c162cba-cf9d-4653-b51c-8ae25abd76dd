import { VModel, VObject } from '@/lib/vails';
import { ServeOrigin } from '../../../types/model';

export class ServeOriginModel extends VModel<ServeOrigin> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return ServeOriginModel.stateMapping()[this.reactiveRecord.state];
  // });
  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}
