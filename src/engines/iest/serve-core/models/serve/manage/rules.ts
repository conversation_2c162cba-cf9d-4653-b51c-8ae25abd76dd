import { VModel, VObject, VStore } from '@/lib/vails';
import { ServeRule } from '../../../types/model';
import { ServePackModel } from './packs';
import { ServeManagePacksApi } from '../../../apis/serve/manage/packs.api';

export class ServeRuleModel extends VModel<ServeRule> {
  stateConfig = this.computedAttr('stateConfig', () => {
    return ServeRuleModel.stateMapping()[this.reactiveRecord.state];
  });

  static createPackStore(params = {}) {
    return new VStore(new ServeManagePacksApi(params), ServePackModel);
  }

  static stateMapping(): VObject {
    return {
      draft: { label: '未应用', color: 'yellow' },
      used: { label: '已应用', color: 'blue' },
      closed: { label: '已关闭', color: 'gray' },
    };
  }
}
