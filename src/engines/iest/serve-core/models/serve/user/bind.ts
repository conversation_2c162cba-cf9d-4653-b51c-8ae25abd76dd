import { VModel, VObject } from '@/lib/vails';
import { ServeBind } from '@/engines/serve-mobile/serve-core/types/model';

export class ServeBindModel extends VModel<ServeBind> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return ServeBindModel.stateMapping()[this.reactiveRecord.state];
  // });

  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}
