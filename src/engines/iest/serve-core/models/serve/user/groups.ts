import { VModel, VObject } from '@/lib/vails';
import { ServeGroup } from '@/engines/serve/serve-core/types/model';

export class ServeGroupModel extends VModel<ServeGroup> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return ServeGroupModel.stateMapping()[this.reactiveRecord.state];
  // });

  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}
