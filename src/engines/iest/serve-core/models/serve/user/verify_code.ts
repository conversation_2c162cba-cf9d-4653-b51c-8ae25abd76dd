import { VModel, VObject } from '@/lib/vails';
import { ServeVerifyCode } from '@/engines/serve-mobile/serve-core/types/model';

export class ServeVerifyCodeModel extends VModel<ServeVerifyCode> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return ServeVerifyCodeModel.stateMapping()[this.reactiveRecord.state];
  // });

  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}
