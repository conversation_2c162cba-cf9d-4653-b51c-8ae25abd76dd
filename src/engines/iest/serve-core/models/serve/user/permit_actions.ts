import { VModel, VObject } from '@/lib/vails';
import { ServePermitAction } from '@/engines/serve-mobile/serve-core/types/model';

export class ServePermitActionModel extends VModel<ServePermitAction> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return ServePermitActionModel.stateMapping()[this.reactiveRecord.state];
  // });

  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}
