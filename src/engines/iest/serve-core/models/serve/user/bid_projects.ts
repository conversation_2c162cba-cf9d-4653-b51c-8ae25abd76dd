import { VModel, VObject } from '@/lib/vails';
import { ServeBidProject } from '@/engines/iest/serve-core/types/model';

export class ServeBidProjectModel extends VModel<ServeBidProject> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return ServeBidProjectModel.stateMapping()[this.reactiveRecord.state];
  // });

  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}
