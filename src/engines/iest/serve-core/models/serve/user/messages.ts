import { VModel } from '@/lib/vails';
import { ServeMessage } from '../../../types/model';

export class ServeMessageModel extends VModel<ServeMessage> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return ServeMessageModel.stateMapping()[this.reactiveRecord.state];
  // });
  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}
