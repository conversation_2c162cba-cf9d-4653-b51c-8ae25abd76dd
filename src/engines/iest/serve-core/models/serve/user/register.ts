import { VModel, VObject } from '@/lib/vails';
import { ServeRegister } from '@/engines/serve-mobile/serve-core/types/model';

export class ServeRegisterModel extends VModel<ServeRegister> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return ServeRegisterModel.stateMapping()[this.reactiveRecord.state];
  // });

  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}
