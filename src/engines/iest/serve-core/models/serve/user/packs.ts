import { VModel } from '@/lib/vails';
import { ServePack } from '../../../types/model';

export class ServePackModel extends VModel<ServePack> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return ServePackModel.stateMapping()[this.reactiveRecord.state];
  // });
  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}
