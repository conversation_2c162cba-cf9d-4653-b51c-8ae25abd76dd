import { VModel, VObject } from '@/lib/vails';
import { ServeActivity } from '@/engines/serve-mobile/serve-core/types/model';

export class ServeActivityModel extends VModel<ServeActivity> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return ServeActivityModel.stateMapping()[this.reactiveRecord.state];
  // });

  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
  coverImageUrl =this.computedAttr('coverImageUrl', () => {
    return this.reactiveRecord.cover_image?.files?.[0]?.url || '#';
  });
}
