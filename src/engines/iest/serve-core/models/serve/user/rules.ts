import { VModel, VObject } from '@/lib/vails';
import { ServeRule } from '@/engines/iest/serve-core/types/model';

export class ServeRuleModel extends VModel<ServeRule> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return ServeRuleModel.stateMapping()[this.reactiveRecord.state];
  // });

  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}
