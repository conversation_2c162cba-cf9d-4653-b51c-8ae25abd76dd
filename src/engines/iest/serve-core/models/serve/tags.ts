import { VModel, VObject } from '@/lib/vails';
import { ServeTag } from '@/engines/serve/serve-core/types/model';

export class ServeTagModel extends VModel<ServeTag> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return ServeTagModel.stateMapping()[this.reactiveRecord.state];
  // });

  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}
