import { VModel, VObject } from '@/lib/vails';
import { ServeBanner } from '@/engines/serve/serve-core/types/model';

export class ServeBannerModel extends VModel<ServeBanner> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return ServeBannerModel.stateMapping()[this.reactiveRecord.state];
  // });

  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}
