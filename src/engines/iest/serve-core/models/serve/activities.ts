import FileServer from '@/components/global/ta-component/file/servers';
import { WorkflowModel } from '@/engines/bpm/bpm-core/apis/admin/workflow.api';
import { BpmUserWorkflows } from '@/engines/bpm/bpm-core/apis/user/workflowl.api';
import { ExpUserActivitiesApi } from '@/engines/exp/exp-core/apis/exp/user/activities.api';
import { ExpActivityModel } from '@/engines/exp/exp-core/models/exp/activities';
import { ServeActivity } from '@/engines/iest/serve-core/types/model';
import { VModel, VObject, VStore } from '@/lib/vails';
import { ServeEntryType } from './entries';

export enum ServeActivityType {
  Exp = 'Serve::ExpActivity',
  Bpm = 'Serve::BpmActivity',
  Oper = 'Serve::OperActivity',
  Coupon = 'Serve::CouponActivity',
  Talent = 'Serve::TalentActivity',
  Wechat = 'Serve::WechatActivity',
  Article = 'Serve::ArticleActivity',
}

export class ServeActivityModel extends VModel<ServeActivity> {
  coverImageUrl = this.computedAttr('coverImageUrl', () => {
    return (
      this.reactiveRecord.cover_image?.files?.[0]?.url ||
      new FileServer().getDownloadUrl(this.reactiveRecord.cover_image?.files?.[0] || { url: '' })
    );
  });

  banners = this.computedAttr('banners', () => {
    return (
      this.reactiveRecord.model_payload?.images ||
      this.reactiveRecord.source_info?.detail?.images ||
      this.reactiveRecord.cover_image?.files ||
      []
    );
  });

  bpmInstanceProcessing = this.computedAttr('bpmInstanceProcessing', () => {
    if (
      this.reactiveRecord.entry?.source_info &&
      this.reactiveRecord.type === ServeActivityType.Bpm &&
      this.reactiveRecord.entry?.source_info?.state === 'processing'
    ) {
      return true;
    }
    return false;
  });

  worklfowModel = this.computedAttr('worklfowModel', () => {
    if (this.reactiveRecord.source_info && this.reactiveRecord.type === ServeActivityType.Bpm)
      return this.returnNestedModel(
        new WorkflowModel(
          this.reactiveRecord.source_info,
          new VStore(new BpmUserWorkflows(), WorkflowModel),
        ),
      );

    return null;
  });

  expActivityModel = this.computedAttr('expActivityModel', () => {
    if (this.reactiveRecord.source_info && this.reactiveRecord.type === ServeActivityType.Exp) {
      return this.returnNestedModel(
        new ExpActivityModel(
          this.reactiveRecord.source_info,
          new VStore(new ExpUserActivitiesApi(), ExpActivityModel),
        ),
      );
    }

    return null;
  });

  entrySteps = this.computedAttr('entrySteps', () => {
    const result: VObject[] = [];
    if (this.worklfowModel.value?.formSetting) {
      this.worklfowModel.value?.formSetting.steps.forEach((step: VObject, index: number) => {
        result.push({
          title: '流程',
          key: `workflow-${index}`,
          type: 'form',
          form: step.form,
          modelKeyPrefix: ['source_info', step.modelKeyPrefix].join('.'),
        });
      });
    }

    if (this.expActivityModel.value?.id) {
      result.push({
        title: '用户固定表单',
        key: 'exp-1',
        type: 'form',
        form: 'exp_entry#user_fixed',
        modelKeyPrefix: 'source_info',
      });

      if (this.reactiveRecord.source_info?.entry_form) {
        result.push({
          title: '活动表单',
          key: 'exp-2',
          type: 'form',
          form: this.reactiveRecord.source_info.entry_form,
          modelKeyPrefix: 'source_info.payload',
        });
      }
    }

    if (this.reactiveRecord.type === ServeActivityType.Oper) {
      result.push({
        title: '活动表单',
        key: 'oper-2',
        type: 'form',
        form: 'oper_entry',
        modelKeyPrefix: 'source_info',
      });
    }

    if (this.reactiveRecord.type === ServeActivityType.Talent) {
      result.push({
        title: '人才房摇号',
        type: 'form',
        form: this.reactiveRecord.source_info?.form,
        modelKeyPrefix: 'source_info.model_payload',
      });
    }

    return result;
  });

  entryType = this.computedAttr('entryType', () => {
    const typeMap = {
      [ServeActivityType.Exp]: ServeEntryType.Exp,
      [ServeActivityType.Bpm]: ServeEntryType.Bpm,
      [ServeActivityType.Oper]: ServeEntryType.Oper,
      [ServeActivityType.Talent]: ServeEntryType.Talent,
    };
    return this.reactiveRecord.type
      ? typeMap[this.reactiveRecord.type as keyof typeof typeMap]
      : undefined;
  });
}
