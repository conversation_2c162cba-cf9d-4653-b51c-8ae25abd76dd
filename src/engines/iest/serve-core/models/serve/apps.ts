import { VModel, VObject } from '@/lib/vails';
import { ServeApp } from '@/engines/serve-mobile/serve-core/types/model';

export class ServeAppModel extends VModel<ServeApp> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return ServeAppModel.stateMapping()[this.reactiveRecord.state];
  // });

  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}
