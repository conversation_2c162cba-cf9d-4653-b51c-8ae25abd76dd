<script lang='ts'>
import { ref, defineComponent, toRefs, PropType, computed, ComputedRef } from 'vue';
interface DataItem {
  name: string;
  num: number;
  color?: string;
}

interface ProgressItem extends DataItem {
  percent: number;
}

const ComIestProgress = defineComponent({
  name: 'ComIestProgress',
  components: {},
  props: {
    data: {
      type: Array as PropType<ProgressItem[]>,
      default: () => []
    },
    colors: {
      type: Array as PropType<string[]>,
      default: () => ['#A4CAFE', '#F8B4B4', '#FDBA8C', '#FCE96A', '#84E1BC', '#7EDCE2', '#B4C6FC', '#CABFFD', '#F8B4D9']
    }
  },
  setup(props) {
    const totalNum = computed(() => props.data.reduce((acc, cur) => acc + cur.num, 0))

    const progressData: ComputedRef<ProgressItem[]> = computed(() => props.data.map((item: DataItem) => ({
      ...item,
      percent: item.num / totalNum.value * 100,
    })))

    const computedLinearGradient = computed(() => {
      const arr: string[] = []
      const endArr: number[] = []
      progressData.value.forEach((item: ProgressItem, index: number) => {
        const colorFmt = `${item.color || props.colors[index % props.colors.length]}`
        const start = index === 0 ? 0 : endArr[index - 1]
        const end = start + item.percent
        endArr.push(end)
        arr.push(`${colorFmt} ${start}% ${end}%`)
      })

      return `linear-gradient(to right, ${arr.join(',')})`
    })
    return {
      ...toRefs(props),
      progressData,
      computedLinearGradient,
    };
  },
});
export default ComIestProgress;
</script>

<template lang="pug">
.com-iest-progress.rounded-lg.w-full.bg-gray-200
</template>

<style lang="stylus" scoped>
.com-iest-progress
  background-image v-bind(computedLinearGradient)
</style>
