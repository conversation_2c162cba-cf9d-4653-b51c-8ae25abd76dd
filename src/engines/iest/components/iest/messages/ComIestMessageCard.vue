<script lang="ts">
import { computed, defineComponent, toRefs } from 'vue';
import { IestMessageType, IestMention } from '../../../types/types';

const ComIestMessageCard = defineComponent({
  name: 'ComIestMessageCard',
  components: {},
  props: {
    record: { type: Object, default: () => ({}) },
  },
  emits: ['clickMention'],
  setup(props, { emit }) {
    const isSystemMessage = computed(() => props.record.type === IestMessageType.System);
    const AI_CHAT_AVATAR = 'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iest/ai.png';
    const onClickedMention = (mention: IestMention) => {
      emit('clickMention', mention, props.record);
    };

    return {
      ...toRefs(props),
      IestMessageType,
      isSystemMessage,
      AI_CHAT_AVATAR,
      onClickedMention,
    };
  },
});
export default ComIestMessageCard;
</script>

<template lang="pug">
.com-iest-message-card.w-full.text-base
  .flex.items-stretch(:class='{ "user-message": !isSystemMessage }')
    .avatar.mr-2(v-if='isSystemMessage')
      a-avatar(:src='AI_CHAT_AVATAR')
    .dots__wrapper.flex.flex-col.justify-center.mr-2.cursor-pointer(v-else)
      .dots.space-y-1(style='width:min-content;')
        //- .dot.bg-gray-500.rounded-full(v-for='i in 3' class='w-0.5 h-0.5')
    .content.bg-white.p-4.space-y-4.rounded-2xl(class='dark:bg-primary-800')
      .message.text-gray-900.text-sm(class='dark:text-white') {{ record.content }}
      .flex.flex-wrap(v-if='record.actions?.length > 0')
        .action.mr-2(v-for='action in record.actions')
          a-tag.cursor-pointer(
            :color='action.color || "blue"',
            @click.stop='action.callback'
          ) {{ action.name }}
      .flex.flex-wrap.space-x-2(
        v-if='record.mentions?.length > 0',
        class='dark:text-white',
      )
        .mention-card.flex.rounded-lg.overflow-hidden.text-sm.cursor-pointer(
          v-for='mention in record.mentions',
          :key='mention.id'
          @click.stop='onClickedMention(mention)'
        )
          .bg-gray-100.p-2.pl-3.flex.items-center(class='dark:bg-primary-700') {{ "</>" }}
          .bg-gray-50.mention.p-2.pr-3(class='dark:bg-primary-900')
            .name {{ mention.name }}
            .notice.text-xs.text-gray-400(class='dark:text-primary-300') 点击查看详情

</template>

<style lang="stylus" scoped>
.com-iest-message-card
  .user-message
    @apply justify-end;
    .content
      border-top-right-radius 0
      border-top-left-radius 8px
  .content
    border-top-left-radius 0
</style>
