<script lang="ts">
import { defineComponent, computed, ref, Ref, toRefs, reactive } from 'vue';
import { TaIndexViewTabInterface } from '@/components/global/ta-component/TaIndexView/types';
import { VObject } from '@/lib/vails/model';
import ComIestOrgsShow from './ComIestOrgsShow.vue'
import '@/engines/iest/views/table.styl';
import ComColorfulLabel from '@/engines/iest/components/global/ComColorfulLabel.vue';
import ComServePackShowDrawer from '../../serve/packs/ComServePackShowDrawer.vue';
import ComServeRuleIndexDrawer from '../../serve/rules/ComServeRuleIndexDrawer.vue';

const ComIestOrgsIndexCopy = defineComponent({
  name: 'ComIestOrgsIndexCopy',
  components: {
    ComIestOrgsShow,
    ComColorfulLabel,
    ComServePackShowDrawer,
    ComServeRuleIndexDrawer,
  },
  props: {
    store: { type: Object, required: true },
  },
  setup(props, { emit }) {
    const ruleStore = props.store.model.createRuleStore()
    const config = computed(() => ({
      recordName: '区域管理',
      store: props.store,
      template: 'org#iest',
      detail: {
        mode: 'route',
        url: `/iest/manage/orgs`
      },
      mode: 'table',
      actions: [
        { key: 'update', enabled: true },
      ],
      table: {
        scroll: { y: 'auto' },
      },
      searcherSimpleOptions: [
        { key: 'name', label: '名称', type: 'string' },
        { key: 'short_name', label: '简称', type: 'string' },
        { key: 'province', label: '省', type: 'string' },
        { key: 'city', label: '市', type: 'string' },
      ],
    }));

    const visible = reactive({
      messagesVisible: false,
      unreadmessagesVisible: false,
      rulesVisible: false,
    })

    const params = ref({})
    const activeRecord = ref<VObject>({})
    const onDrawerOpen = (record: VObject, key: string) => {
      key = key.split('_').join('').replace('count', '') as any;
      activeRecord.value = record;

      switch (key) {
        case 'rules':
          params.value = {
            q: {
              orgs_id_eq: record.id,
            }
          }
          break;
        case 'messages':
          params.value = {
            q: {
              pack_org_id_eq: record.id,
            }
          }
          break;
        case 'unreadmessages':
          params.value = {
            q: {
              pack_org_id_eq: record.id,
              is_read_false: 1
            }
          }
          break;
      }

      (visible as any)[key + 'Visible'] = true;

    };
    return {
      ...toRefs(props),
      config,
      onDrawerOpen,
      visible,
      params,
      activeRecord,
      ruleStore,
    };
  },
});

export default ComIestOrgsIndexCopy;
</script>

<template lang="pug">
.com-iest-manage-orgs-index.iest__table__skin.flex.flex-col
  .px-4.pt-4.bg-white.flex-grow.h-0.rounded-lg
    TaIndexView.ta-index-view-skin(:config='config')
      template(#bodyCell='{ record,column }')
        .text-sm(v-if='column.dataIndex[0] === "path_names"') {{ record.path_names?.toReversed()?.join('>') }}
        template(v-else-if='column.dataIndex[0].includes("count") && column.dataIndex[0] !=="children_count"')
          .flex.w-full.justify-center.items-center
            ComColorfulLabel.px-10px.py-2px.cursor-pointer(
              :label='text || 0',
              color='blue',
              @click.stop='onDrawerOpen(record,column.dataIndex[0])'
            )
      template(#detail='{ record, onClose }')
        ComIestOrgsShow(
          v-if='record.id'
          :store='store'
          :extendRoute='`/iest/manage/orgs/${record.id}`'
          :editable='editable'
          @afterDelete='onClose'
          @afterExtend='onClose'
        )
    ComServePackShowDrawer(
      v-if='visible.messagesVisible'
      v-model:visible='visible.messagesVisible'
      :packStore='store'
      :title='`${activeRecord.name}全部消息`'
      :params='params'
      width='900'
    )
    ComServePackShowDrawer(
      v-if='visible.unreadmessagesVisible'
      v-model:visible='visible.unreadmessagesVisible'
      :packStore='store'
      :title='`${activeRecord.name}未读消息`'
      :params='params'
      width='900'
    )
    ComServeRuleIndexDrawer(
      v-if='visible.rulesVisible'
      :title='`${activeRecord.name} | 规则`'
      v-model:visible='visible.rulesVisible'
      :store='ruleStore',
      :initForm='{ org_ids: [activeRecord.id] }'
      :params='params',
      width='900',
    )

</template>

<style lang="stylus" scoped>
.com-iest-manage-orgs-index
  height 100%
  width 100%
</style>
