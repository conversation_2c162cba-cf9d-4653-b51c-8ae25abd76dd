<script lang="ts">
import { defineComponent, toRefs, PropType } from 'vue';
import { TaBreadcrumbInterface } from '@/components/global/ta-component/TaBreadcrumb.vue';
import ComResUserIndex from '../../../../../components/res/ComResUserIndex.vue';
import ComServeRulesIndex from '../../serve/rules/ComServeRulesIndex.vue';
import ComServePacksIndex from '../../serve/packs/ComServePacksIndex.vue';
import ComServeMessagesIndex from '../../serve/messages/ComServeMessagesIndex.vue';
import ComIestOrgsIndexCopy from './ComIestOrgsIndexCopy.vue';

const ComIestOrgsShow: any = defineComponent({
  name: 'ComIestOrgsShow',
  components: {
    ComResUserIndex,
    ComServeRulesIndex,
    ComServePacksIndex,
    ComIestOrgsIndexCopy,
    ComServeMessagesIndex,
  },
  props: {
    store: { type: Object, required: true },
    userStore: { type: Object, required: true },
    ruleStore: { type: Object, required: true },
    packStore: { type: Object, required: true },
    catalogStore: { type: Object, required: true },
    messageStore: { type: Object, required: true },
    subOrgStore: { type: Object, required: true },
    extendRoute: { type: String, default: '' },
    editable: { type: Boolean, default: true },
    breadcrumbs: { type: Array as PropType<TaBreadcrumbInterface[]>, default: () => [] },
  },
  emits: ['afterDelete', 'afterExtend'],
  setup(props, { emit }) {
    const tabs = [
      {
        key: 'official',
        label: '公职人员',
      },
      {
        key: 'rule',
        label: '规则',
      },
      {
        key: 'pack',
        label: '消息批次',
      },
      {
        key: 'message',
        label: '消息',
      },
      {
        key: 'sub',
        label: '下级组织',
      },
    ];

    const afterDelete = () => {
      emit('afterDelete');
    };

    const afterExtend = () => {
      emit('afterExtend');
    };

    return {
      ...toRefs(props),
      tabs,
      record: props.store.record,
      afterDelete,
      afterExtend,
    };
  },
});
export default ComIestOrgsShow;
</script>

<template lang="pug">
.com-iest-manage-orgs-show.bg-gray-100.p-4
  TaShowLayout.ta-show-layout(
    :tabs='tabs',
    :title='record.name',
    :store='store',
    :extendRoute='extendRoute',
    :editable='editable',
    :breadcrumbs='breadcrumbs',
    template='org#iest',
    @afterDelete='afterDelete',
    @afterExtend='afterExtend'
  )
    template(#official_tab)
      ComResUserIndex(:store='userStore')
    template(#rule_tab)
      ComServeRulesIndex(
        :store='ruleStore',
        :catalogStore='catalogStore'
      )
    template(#pack_tab)
      ComServePacksIndex(
        :store='packStore'
      )
    template(#message_tab)
      ComServeMessagesIndex(
        :store='messageStore'
      )
    template(#sub_tab)
      ComIestOrgsIndexCopy(:store='subOrgStore')
</template>

<style lang="stylus" scoped>
.com-iest-manage-orgs-show
  height 100%
  .ta-show-layout:deep(.background)
    padding 0
    .content
      @apply rounded-lg;
</style>
