<script lang='ts'>
import { ref, defineComponent, toRefs } from 'vue';
import ComIestStatisticsCardContainer from './ComIestStatisticsCardContainer.vue';
import ComHomeIestOfficialCard from '@/components/home/<USER>';
import '@/engines/iest/views/table.styl';

const ComIestStatisticsOfficial = defineComponent({
  name: 'ComIestStatisticsOfficial',
  components: { ComIestStatisticsCardContainer, ComHomeIestOfficialCard },
  props: {
    officialData: {
      type: Object,
      default: () => ({})
    }
  },
  setup(props) {
    const config = {
      columns: [
        {
          title: '区域',
          dataIndex: ['name'],
        },
        {
          title: '人数',
          dataIndex: ['num'],
        },
      ]
    }

    const paginationConfig = {
      hide: true,
    }
    return {
      ...toRefs(props),
      config,
      paginationConfig,
    };
  },
});
export default ComIestStatisticsOfficial;
</script>

<template lang="pug">
ComIestStatisticsCardContainer.iest__table__skin
  .card__wrapper.grid.grid-cols-2.gap-4.pt-4
    .left
      ComHomeIestOfficialCard(
        :item='officialData.official',
        class='!bg-white !border-none'
      )
      TaIndexTable.ta-index-view-skin(
        :data='officialData.user.slice(0,7)',
        :config='config',
        :paginationConfig='paginationConfig'
      )
    .right
      ComHomeIestOfficialCard(
        :item='officialData.tag'
        class='!bg-white !border-none'
      )
      TaIndexTable.ta-index-view-skin(
        :data='officialData.user.slice(7,14)',
        :config='config',
        :paginationConfig='paginationConfig'
      )

</template>

<style lang="stylus" scoped></style>
