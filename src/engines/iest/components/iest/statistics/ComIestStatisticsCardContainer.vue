<script lang='ts'>
import { defineComponent, toRefs } from 'vue';
const ComIestStatisticsCardContainer = defineComponent({
  name: 'ComIestStatisticsCardContainer',
  components: {},
  props: {
    title: { type: String, default: '' },
    url: { type: String, default: '' },
    showHeader: { type: Boolean, default: true }
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
export default ComIestStatisticsCardContainer;
</script>

<template lang="pug">
.com-iest-statistics-card-container.iest-card-container.flex.flex-col
  header.flex.items-center.border-b-1.border-gray-200.pb-4(v-if="showHeader")
    .text-base.font-medium.text-gray-700 {{ title }}
    TaLink.ml-auto(:to='url')
      .flex.items-center.text-primary-600
        .text-sm 查看更多
        TaIcon(type='outline/chevron-right' class='!w-3 !h-3')
  section
    slot
</template>

<style lang="stylus" scoped></style>
