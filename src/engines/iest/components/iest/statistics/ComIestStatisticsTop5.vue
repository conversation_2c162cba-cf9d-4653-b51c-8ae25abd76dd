<script lang='ts'>
import { ref, defineComponent, toRefs } from 'vue';
import ComIestProgress from '../../ComIestProgress.vue';
import ComIestStatisticsCardContainer from './ComIestStatisticsCardContainer.vue';


const ComIestStatisticsTop5 = defineComponent({
  name: 'ComIestStatisticsTop5',
  components: {
    ComIestProgress,
    ComIestStatisticsCardContainer
  },
  props: {
    data: {
      type: Array,
      default: () => [
        ['name', 'num']
      ]
    },
    total: {
      type: Number,
      default: 1
    }
  },
  setup(props) {
    const colors = ['#1C64F2', '#16BDCA', '#FDBA8C', '#E74694', '#9061F9']
    const getPercentage = (num: number) => {
      num = num || 0
      return (num / props.total * 100).toFixed(1)
    }
    return {
      ...toRefs(props),
      colors,
      getPercentage,
    };
  },
});
export default ComIestStatisticsTop5;
</script>

<template lang="pug">
ComIestStatisticsCardContainer
  .card__wrapper.pt-4.space-y-4
    .flex.items-center(v-for='(item, index) in data')
      .text-base.text-gray-900.mr-auto.flex-shrink-0 {{ item[0] }}·{{item[1]}}
      .w-40.mr-8
        a-progress.w-full(
          :percent='getPercentage(item[1])',
          :stroke-width='20',
          stroke-linecap='round',
          :showInfo='false',
          :stroke-color="colors[index]"
        )
      .text-sm.w-15 {{ getPercentage(item[1]) }}%
</template>

<style lang="stylus" scoped>
// .card__wrapper
//   :deep(.ant-progress-text)
//     margin-left 32px
</style>
