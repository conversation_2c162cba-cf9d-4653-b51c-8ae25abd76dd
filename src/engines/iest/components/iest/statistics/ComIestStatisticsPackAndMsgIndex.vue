<script lang='ts'>
import { ServeManageMessagesApi } from '@/engines/iest/serve-core/apis/serve/manage/messages.api';
import { ServeManagePacksApi } from '@/engines/iest/serve-core/apis/serve/manage/packs.api';
import { ServeMessageModel } from '@/engines/iest/serve-core/models/serve/manage/messages';
import { ServePackModel } from '@/engines/iest/serve-core/models/serve/manage/packs';
import { VObject, VStore } from '@/lib/vails';
import { ref, defineComponent, toRefs, computed } from 'vue';
import ComServePacksCard from '../../serve/packs/ComServePacksCard.vue';
import ComColorfulLabel from '../../global/ComColorfulLabel.vue';
import '@/engines/iest/views/table.styl';
import { cloneDeep } from 'lodash';
import ComBpmInstanceDetailDialog from '@/engines/bpm/components/ComBpmInstanceDetailDialog.vue';

const ComIestStatisticsPackAndMsgIndex = defineComponent({
  name: 'ComIestStatisticsPackAndMsgIndex',
  components: { ComServePacksCard, ComColorfulLabel, ComBpmInstanceDetailDialog },
  setup(props) {
    const packStore = new VStore(new ServeManagePacksApi(), ServePackModel)
    const msgStore = new VStore(new ServeManageMessagesApi(), ServeMessageModel)
    const msgStoreForDrawer = cloneDeep(msgStore)

    const visible = ref(false)
    const instanceVisible = ref(false)
    const activeRecord = ref({})
    const msgDrawerParams = ref({})
    const onCheck = (record: VObject, params: any) => {
      msgDrawerParams.value = {
        q: {
          pack_id_eq: record.id,
          ...params
        }
      }
      visible.value = true
      activeRecord.value = record
    }

    const config = computed(() => ({
      store: packStore,
      mode: 'list',
      actions: [
        { key: 'create', enabled: false },
        { key: 'update', enabled: false },
        { key: 'delete', enabled: false },
        { key: 'import', enabled: false },
        { key: 'export', enabled: false },
      ],
      pagination: {
        hide: true,
        perPage: 5,
      },
      table: {
        scroll: { y: 'auto' }
      },
      list: {
        gap: 8,
        scroll: { y: 'auto' }
      }
    }))

    const tabs = computed(() => [
      {
        label: '消息批次',
        key: 'pack',
      },
      {
        label: '消息记录',
        key: 'msg',
        mode: 'table',
        store: msgStore,
        template: 'serve_message#home',
      },
    ])

    const onShow = (record: VObject) => {
      if (record.flag === 'msg') {
        return
      }
      activeRecord.value = record
      instanceVisible.value = true
    }
    return {
      ...toRefs(props),
      config,
      tabs,
      msgStoreForDrawer,
      visible,
      instanceVisible,
      activeRecord,
      msgDrawerParams,
      onCheck,
      onShow,
    };
  },
});
export default ComIestStatisticsPackAndMsgIndex;
</script>

<template lang="pug">
.com-iest-statistics-pack-and-msg-index.iest__table__skin
  ComMsgWithPackIdDrawer(
      v-model:visible='visible',
      :msgStore='msgStoreForDrawer',
      :params='msgDrawerParams',
      :record='activeRecord',
    )
  ComBpmInstanceDetailDialog(
      v-if='instanceVisible'
      v-model:visible='instanceVisible',
      :instanceId='activeRecord.create_instance_id'
    )
  TaIndexView.ta-index-view-skin(
    :config='config'
    :tabs='tabs'
    :showHeader='false',
    @onShow='onShow'
  )
    template(#card='{record}')
      ComServePacksCard.cursor-pointer(
        :record='record',
        @check='(params)=>onCheck(record, params)'
      )
    template(#bodyCell='{record, column}')
      ComColorfulLabel.px-10px.py-2px(
        v-if='column.dataIndex[0] === "is_read"'
        :label='record.is_read? "已读" : "未读"'
        :color='record.is_read? "green" : "red"'
      )
</template>

<style lang="stylus" scoped>
.com-iest-statistics-pack-and-msg-index
  :deep(.list-view__pagination_placeholder)
    display none
</style>
