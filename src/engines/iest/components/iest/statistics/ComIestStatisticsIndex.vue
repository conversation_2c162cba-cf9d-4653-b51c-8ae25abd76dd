<script lang="ts">
import { ref, defineComponent, toRefs, computed } from 'vue';
import ComIestStatisticsCard from './ComIestStatisticsCard.vue';
import ComIestStatisticsCardContainer from './ComIestStatisticsCardContainer.vue';
import ComIestStatisticsCustomLineBar from './ComIestStatisticsCustomLineBar.vue';
import ComIestStatisticsPackAndMsgIndex from './ComIestStatisticsPackAndMsgIndex.vue';
import ComIestStatisticsOfficial from './ComIestStatisticsOfficial.vue';
import ComIestStatisticsTop5 from './ComIestStatisticsTop5.vue';
import ComIestStatisticsRuleCard from './ComIestStatisticsRuleCard.vue';
import ComHomeIestRule from '../ComHomeIestRule.vue';
import { ServeManageRulesApi } from '@/engines/iest/serve-core/apis/serve/manage/rules.api';
import { ServeRuleModel } from '@/engines/iest/serve-core/models/serve/manage/rules';
import { VStore } from '@/lib/vails';
import { ServeManageRuleGroupsApi } from '@/engines/iest/apis/serve/manage/rule_groups.api';
import { ServeRuleGroupModel } from '@/engines/iest/models/serve/manage/rule_groups';

const ComIestStatisticsIndex = defineComponent({
  name: 'ComIestStatisticsIndex',
  components: {
    ComIestStatisticsCard,
    ComIestStatisticsCardContainer,
    ComIestStatisticsCustomLineBar,
    ComIestStatisticsPackAndMsgIndex,
    ComIestStatisticsOfficial,
    ComIestStatisticsTop5,
    ComIestStatisticsRuleCard,
    ComHomeIestRule,
  },
  props: {
    data: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props) {
    const packData = computed(() => [
      {
        label: '待审核',
        num: props.data?.pack?.pending,
      },
      {
        label: '待发送',
        num: props.data?.pack?.sending,
      },
      {
        label: '已发送',
        num: props.data?.pack?.finished,
      },
    ]);

    const msgData = computed(() => [
      {
        label: '已发送',
        num: props.data?.msg?.total,
      },
      {
        label: '已读',
        num: props.data?.msg?.read,
      },
      {
        label: '未读',
        num: props.data?.msg?.unread,
      },
    ]);

    const actData = computed(() => [
      {
        label: '来源',
        num: props.data?.origin?.total,
      },
      {
        label: '标签',
        num: props.data?.tag?.total,
      },
      {
        label: '素材总数',
        num: props.data?.act?.total,
      },
    ]);

    const officialData = computed(() => ({
      official: {
        name: '公职人员',
        num: props.data?.user?.official || 0,
        icon: 'users-group',
      },
      tag: {
        name: '标签',
        num: props.data?.res_tag?.count || 0,
        icon: 'tag',
      },
      user: Object.keys(props.data?.user || {}).reduce((acc: any, key: string) => {
        if (key !== 'official') {
          acc.push({
            name: key,
            num: props.data?.user?.[key] || 0,
          });
        }
        return acc;
      }, []),
    }));

    const ruleData = computed(() => ({
      total: props.data?.rule?.total || 0,
    }));

    const lineData = computed(() => {
      return props.data?.pack?.trend?.map((item: any) => item.pack_count).reverse();
    });
    const barData = computed(() => {
      return props.data?.msg?.trend?.map((item: any) => item.pack_count).reverse();
    });

    const xData = computed(() => {
      return props.data?.pack?.trend?.map((item: any) => item.start_day).reverse();
    });

    const ruleStore = new VStore(new ServeManageRulesApi(), ServeRuleModel);
    const catalogStore = new VStore(new ServeManageRuleGroupsApi(), ServeRuleGroupModel);

    const resTagTop5Data = computed(() =>
      props.data?.res_tag?.top5?.map((item: any) => [item.name, item.users_count]),
    );
    return {
      ...toRefs(props),
      packData,
      msgData,
      actData,
      officialData,
      ruleData,
      lineData,
      xData,
      barData,
      ruleStore,
      catalogStore,
      resTagTop5Data,
    };
  },
});
export default ComIestStatisticsIndex;
</script>

<template lang="pug">
.com-iest-statistics-index.space-y-4
  header.iest-card-container
    .grid.grid-cols-3.gap-2.grid__wrapper
      TaLink.card(to='/serve/manage/packs')
        ComIestStatisticsCard(
          title='消息批次',
          intro='（单位：次）',
          :data='packData',
        )
      TaLink.card(to='/serve/manage/messages')
        ComIestStatisticsCard(
          title='消息统计'
          intro='（单位：条）',
          :data='msgData',
        )
      TaLink.card(to='/serve/manage/activities')
        ComIestStatisticsCard(title='素材统计',:data='actData')
  section
    ComIestStatisticsCardContainer(title='消息统计' url='/serve/manage/messages')
      .flex.h-350px.justify-between.space-x-6.pt-3
        ComIestStatisticsCustomLineBar(
          class='basis-4/7'
          :lineData='lineData',
          :xData='xData',
          :barData='barData',
        )
        .w-1px.bg-gray-200.h-full
        ComIestStatisticsPackAndMsgIndex(class='basis-3/7')

  ComHomeIestRule(
    title='规则统计'
    url='/serve/manage/rules',
    :store='ruleStore',
    :catalogStore='catalogStore'
  )

  section.grid.grid-cols-2.gap-4
    .left.space-y-4
      ComIestStatisticsOfficial(
        title='公职人员统计',
        url='/res/user',
        :officialData='officialData'
      )

    .right.space-y-4
      //- .iest-card-container
      ComIestStatisticsTop5(
        title='素材标签Top5'
        url='/serve/manage/tags'
        :data='data?.tag?.top',
        :total='data?.tag?.total_activities'
      )
      ComIestStatisticsTop5(
        title='人员标签Top5'
        url='/res/user',
        :data='resTagTop5Data',
        :total='data?.user?.official'
      )

</template>

<style lang="stylus" scoped>
.com-iest-statistics-index
  .grid__wrapper
    .card:first-child
      background-image linear-gradient(270deg, #FFF4F4 0%, #FFFAFA 100%)
    .card:nth-child(2)
      background-image linear-gradient(270deg, #F2F8FF 0%, #F7FBFF 100%)
    .card:nth-child(3)
      background-image linear-gradient(270deg, #F6F5FF 0%, #FAF9FF 100%)
</style>
