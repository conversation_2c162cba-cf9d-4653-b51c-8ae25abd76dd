<script lang='ts'>
import { ref, defineComponent, toRefs, computed } from 'vue';
import * as echarts from 'echarts';
const ComIestStatisticsCustomLineBar = defineComponent({
  name: 'ComIestStatisticsCustomLineBar',
  components: {},
  props: {
    lineData: {
      type: Array, default: () => []
    },
    barData: {
      type: Array, default: () => []
    },
    xData: {
      type: Array, default: () => []
    }
  },
  setup(props) {
    const echartsRef = ref<any>(null);
    const lineBarOption = computed(() => ({
      grid: {
        left: '10%',
        right: '10%',
        bottom: '16%',
        top: '12%',
      },
      legend: {
        textStyle: {
          color: '#4B5563'
        },
        bottom: 0,
        data: ['消息数量', '消息批次']
      },
      xAxis: [
        {
          type: 'category',
          axisLine: {
            lineStyle: {
              color: '#D1D5DB'
            }
          },
          axisLabel: {
            formatter: '{value}'
          },
          axisPointer: {
            type: 'shadow'
          },
          data: props.xData,
        }
      ],
      yAxis: [
        {
          type: 'value',
          min: 0,
          max: Math.max(...(props.barData as any)),
          name: '消息数量(条)',
          nameTextStyle: {
            color: '#4B5563'
          },
          axisLabel: {
            color: '#4B5563'
          },
          splitLine: {
            lineStyle: {
              type: 'dashed'
            }

          }
        },
        {
          type: 'value',
          min: 0,
          max: Math.max(...(props.lineData as any)),
          name: '消息批次(次)',
          nameTextStyle: {
            color: '#4B5563'
          },
          axisLabel: {
            color: '#4B5563'
          },
          splitLine: {
            show: false,
          }
        }
      ],
      series: [
        {
          name: '消息数量',
          type: 'bar',
          itemStyle: {
            color: '#3F83F8'
          },
          data: props.barData
        },
        {
          name: '消息批次',
          type: 'line',
          symbol: 'none',
          lineStyle: {
            color: '#16BDCA'
          },
          data: props.lineData
        },
      ]
    }))
    return {
      ...toRefs(props),
      echartsRef,
      lineBarOption,
    };
  },
});
export default ComIestStatisticsCustomLineBar;
</script>

<template lang="pug">
.w-full.h-full
  TaScreenEchartBase(:options='lineBarOption' ref='echartsRef')
</template>

<style lang="stylus" scoped></style>
