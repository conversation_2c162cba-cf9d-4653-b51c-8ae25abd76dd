<script lang='ts'>
import { ref, defineComponent, toRefs } from 'vue';

const ComIestStatisticsCard = defineComponent({
  name: 'ComIestStatisticsCard',
  components: {},
  props: {
    title: { type: String, default: '' },
    intro: { type: String, default: '' },
    data: {
      type: Object,
      default: () => [{ num: 0, label: '' }]
    }
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
export default ComIestStatisticsCard;
</script>

<template lang="pug">
.com-iest-statistics-card.p-4.rounded-lg
  header.flex.items-center.mb-2
    .text-base.font-medium.text-primary-900 {{ title }}
    .text-xs.font-normal.opacity-50 {{ intro }}
  section.grid.grid-cols-3
    template(v-for="(item, index) in data")
      .item.py-3.px-2.flex.flex-col.items-center
        .num.text-2xl.font-bold {{ item.num || 0 }}
        .label.text-sm {{ item.label }}

</template>

<style lang="stylus" scoped>
.com-iest-statistics-card section .num
  color #25396F
  font-family "DIN Alternate"
.com-iest-statistics-card section .label
  color rgba(35, 56, 118, 0.80)
  font-family "PingFang SC"

</style>
