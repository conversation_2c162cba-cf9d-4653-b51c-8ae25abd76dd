<script lang='ts'>
import { ref, defineComponent, toRefs } from 'vue';
import ComIestStatisticsCardContainer from './ComIestStatisticsCardContainer.vue';

const ComIestStatisticsRuleCard = defineComponent({
  name: 'ComIestStatisticsRuleCard',
  components: { ComIestStatisticsCardContainer },
  props: {
    topData: {
      type: Array,
      default: () => [{ name: '规则1', value: 80 }]
    },
    data: {
      type: Array,
      default: () => []
    }
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
export default ComIestStatisticsRuleCard;
</script>

<template lang="pug">
ComIestStatisticsCardContainer.com-iest-statistics-rule-card
  .card__wrapper.pt-4.space-y-4
    .grid.grid-cols-2.gap-3
      .p-4.rounded-lg.bg-green-50.flex.items-center
        .w-12.h-12.rounded-full.bg-white.flex.items-center.justify-center.mr-4
          TaIcon.text-green-500(type='flowbite-v2-solid/rule-combined' class='w-6 h-6')
        .num__and__value
          .text-base.text-gray-500 规则总数
          .text-xl.text-primary-900.font-semibold {{ data.total }}
      .p-4.rounded-lg.bg-red-50.flex.items-center
        .w-12.h-12.rounded-full.bg-white.flex.items-center.justify-center.mr-4
          TaIcon.text-red-500(type='flowbite-v2-solid/paper-clip' class='w-6 h-6')
        .num__and__value
          .text-base.text-gray-500 已发消息规则
          .text-xl.text-primary-900.font-semibold 0
    .progress(v-for='(item,index) in topData')
      .flex.justify-between.items-center
        .text-sm.text-gray-500 Top{{ index+1 }} {{item.name}}
        .text-xs.text-gray-500 {{ item.value }}%
      a-progress(
        :percent='item.value',
        stroke-linecap='round',
        :showInfo='false',
        stroke-color="#1A56DB"
      )
</template>

<style lang="stylus" scoped></style>
