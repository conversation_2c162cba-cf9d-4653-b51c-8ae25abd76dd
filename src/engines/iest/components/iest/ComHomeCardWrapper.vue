<script lang='ts'>
import { defineComponent, toRefs } from 'vue';
const ComHomeCardWrapper = defineComponent({
  name: 'ComHomeCardWrapper',
  components: {},
  props: {
    title: { type: String, default: '' },
    url: { type: String, default: '' },
    showHeader: { type: Boolean, default: true }
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
export default ComHomeCardWrapper;
</script>

<template lang="pug">
.com-home-card-wrapper.iest-card-container.flex.flex-col
  header.flex.items-center(v-if="showHeader")
    .text-base.font-medium.text-gray-700 {{ title }}
    TaLink.ml-auto(:to='url')
      .flex.items-center.text-gray-400
        .text-sm 查看更多
        TaIcon(type='outline/chevron-right' class='!w-3 !h-3')
  section
    slot
</template>

<style lang="stylus" scoped></style>
