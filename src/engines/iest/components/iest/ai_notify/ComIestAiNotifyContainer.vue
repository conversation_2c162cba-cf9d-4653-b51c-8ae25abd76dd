<script lang="ts">
import { ref, defineComponent, toRefs, nextTick, computed } from 'vue';
import ComIestAiNotifyChatContainer from './ComIestAiNotifyChatContainer.vue';
import { IestAiNotifyMessage } from './types';
import { IestAiNotifyManageMessagesApi } from '../../../apis/iest/ai_notify/manage/tags.api';
import { VObject } from '../../../../../lib/vails/model/index';
import { useAiNotifyMessage } from './useAiNotifyMessage';
import ComIestAiNotifyUsersSelector from './ComIestAiNotifyUsersSelector.vue';
import ComIestAiNotifyServeActivitiesSelector from './ComIestAiNotifyServeActivitiesSelector.vue';
import ComIestAiNotifyBasicForm from './ComIestAiNotifyBasicForm.vue';

const ComIestAiNotifyContainer = defineComponent({
  name: 'ComIestAiNotifyContainer',
  components: {
    ComIestAiNotifyChatContainer,
  },
  props: {},
  setup(props) {
    const messages = ref<IestAiNotifyMessage[]>([]);
    const payload = ref<VObject>({});

    const { loading, onCreate } = useAiNotifyMessage(messages, payload);

    const isManuallyCreate = ref(false);

    const cardsShow = computed(() => {
      return (
        isManuallyCreate.value ||
        payload.value.user_ids?.length > 0 ||
        payload.value.serve_activities?.length > 0
      );
    });

    const cards = computed(() => {
      return cardsShow.value
        ? [
            {
              name: '发送任务',
              key: 'form',
              value: payload.value,
              component: ComIestAiNotifyBasicForm,
            },
            {
              name: '文章',
              key: 'serve_activities',
              value: payload.value.serve_activities,
              component: ComIestAiNotifyServeActivitiesSelector,
            },
            {
              name: '用户',
              key: 'user_ids',
              value: payload.value.user_ids,
              component: ComIestAiNotifyUsersSelector,
            },
          ]
        : [];
    });

    return {
      ...toRefs(props),
      messages,
      cards,
      onCreate,
      loading,
    };
  },
});
export default ComIestAiNotifyContainer;
</script>

<template lang="pug">
.com-iest-ai-notify-container.w-full.h-full.flex.flex-col
  //- .line
  //-   a-button(@click='() => cards.push(1)') 123
  .flex.justify-center.w-full.h-0.flex-grow
    ComIestAiNotifyChatContainer.h-full.transition-all(
      v-model:messages='messages',
      :class='cards.length > 0 ? "w-1/3" : "w-3/4"',
      :loading='loading',
      @send='onCreate'
    )
    .info-cards.h-full.w-0.p-4.bg-gray-200.ml-4.rounded-lg.space-y-4.overflow-y-auto(:class='{ hidden: cards.length === 0, "flex-grow": cards.length > 0 }')
      .card.bg-white.p-4.px-6.rounded-lg(v-for='card in cards', :key='card.key')
        .text-gray-900.font-medium.mb-2.flex.items-center
          TaIcon.mr-2(type='RightOutlined')
          | {{ card.name }}
        component(:is='card.component', v-model:payload='payload', v-model:value='card.value')
</template>

<style lang="stylus" scoped></style>
