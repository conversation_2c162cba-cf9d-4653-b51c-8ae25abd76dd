<script lang="ts">
import { defineComponent, toRefs } from 'vue';

const ComIestAiNotifyMessage = defineComponent({
  name: 'ComIestAiNotifyMessage',
  components: {},
  props: {
    record: { type: Object, default: () => ({}) },
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
export default ComIestAiNotifyMessage;
</script>

<template lang="pug">
.com-iest-ai-notify-message.w-full
  .flex.w-full
    .avatar.mr-2(v-if='record.role === "system"')
      a-avatar 廉
    .content.bg-white.w-0.flex-grow.rounded-lg.px-4.py-2.space-y-2
      .message {{ record.message }}
      .flex.flex-wrap(v-if='record.actions?.length > 0')
        .action.mr-2(v-for='action in record.actions')
          a-tag.cursor-pointer(
            :color='action.color || "blue"',
            @click.stop='action.callback'
          ) {{ action.name }}

</template>

<style lang="stylus" scoped></style>
