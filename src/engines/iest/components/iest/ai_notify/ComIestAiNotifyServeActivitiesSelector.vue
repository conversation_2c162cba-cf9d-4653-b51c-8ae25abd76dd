<script lang="ts">
import { defineComponent, toRefs, computed, ref, watch } from 'vue';

const ComIestAiNotifyServeActivitiesSelector = defineComponent({
  name: 'ComIestAiNotifyServeActivitiesSelector',
  components: {},
  props: {
    value: { type: Number, default: undefined },
    payload: { type: Object, default: () => ({}) },
    disabled: { type: Boolean, default: false },
  },
  setup(props, { emit }) {
    const localValue = computed({
      get: () => props.value,
      set: val => {
        emit('update:value', val);
      },
    });

    const localPayload = computed({
      get: () => props.payload,
      set: val => {
        emit('update:payload', val);
      },
    });

    if (!localPayload.value.payload) {
      localPayload.value.payload = {};
    }

    const tableItems = [
      {
        name: '标题',
        type: 'string',
        search: true,
        data_index: 'name',
      },
      // {
      //   name: '标签',
      //   type: 'string',
      //   data_index: 'tag_names',
      // },
    ];

    const tagTableItems = [
      {
        name: '名称',
        type: 'string',
        search: true,
        data_index: 'name',
      },
    ];

    const ransack = computed(() => {
      return {
        tags_id_in: localPayload.value.payload?.serve_tag_ids || [],
      };
    });

    // const activityForm = {
    //   key: 'container_layout_1719131190252_12',
    //   type: 'container_layout',
    //   model: {
    //     create_default_value: {},
    //     update_default_value: {},
    //   },
    //   fields: [
    //     {
    //       key: 'TaTemplateFormDesignerKey-16',
    //       type: 'layout',
    //       model: {},
    //       fields: [
    //         {
    //           key: 'input_1719135920218_16',
    //           icon: 'FolderOutlined',
    //           name: '名称',
    //           type: 'input',
    //           model: {
    //             attr_type: 'string',
    //           },
    //           rules: [],
    //           fields: [],
    //           options: {
    //             span: 24,
    //             formDisabled: true,
    //           },
    //           model_key: 'name',
    //           conditions: [],
    //           model_key_prefix: '',
    //           model_key_configuration: [],
    //         },
    //         // {
    //         //   key: 'image_1719131463273_14',
    //         //   icon: 'FolderOutlined',
    //         //   name: '封面',
    //         //   type: 'image',
    //         //   model: {
    //         //     attr_type: 'array',
    //         //   },
    //         //   rules: [],
    //         //   fields: [],
    //         //   options: {
    //         //     span: 24,
    //         //     multiple: true,
    //         //     formDisabled: true,
    //         //   },
    //         //   model_key: 'cover_image.files',
    //         //   conditions: [],
    //         //   model_key_prefix: '',
    //         //   model_key_configuration: [],
    //         // },
    //         {
    //           key: 'dynamic_component_1719146169309_1',
    //           icon: 'FolderOutlined',
    //           name: '详情',
    //           type: 'dynamic_component',
    //           model: {},
    //           rules: [],
    //           fields: [],
    //           options: {
    //             span: 24,
    //             formDisabled: true,
    //             dynamic_component: 'ComTaContentFieldDyna',
    //           },
    //           model_key: 'content.content',
    //           conditions: [],
    //           model_key_prefix: '',
    //           model_key_configuration: [],
    //         },
    //       ],
    //       options: {},
    //       model_key_prefix: '',
    //     },
    //   ],
    //   content: {
    //     content: '<p><br></p>',
    //   },
    //   options: {
    //     label: {},
    //     theme: {
    //       card: {},
    //       form: {},
    //       background: {},
    //     },
    //     disabled_actions: {},
    //   },
    //   model_key: 'container_layout_1719131190252_12',
    //   conditions: [],
    //   model_key_configuration: [],
    //   rich_text_1719131196487_13: '<p><br></p>',
    // };

    const remainServeActivityIds = ref<number[]>([]);
    const usedServeActivityIds = ref<number[]>([]);

    watch(localValue, (val: any) => {
      if (val && !usedServeActivityIds.value.includes(val)) {
        usedServeActivityIds.value.push(val);
      }
    });

    watch(
      () => localPayload.value.article_content,
      () => {
        remainServeActivityIds.value = [];
        // onChangeArticle();
      },
    );

    // const onChangeArticle = async () => {
    //   const id = remainServeActivityIds.value.shift();
    //   if (id) {
    //     localValue.value = id;
    //   } else {
    //     const { data } = await new NoticeTasksApi().sendCollectionAction(
    //       'ai_match_serve_article_ids',
    //       {
    //         data: {
    //           keyword: localPayload.value.article_content,
    //           exclude_ids: usedServeActivityIds.value,
    //           serve_content_type_tag_ids: localPayload.value.serve_content_type_tag_ids,
    //         },
    //       },
    //     );

    //     remainServeActivityIds.value = data.serve_activity_ids;
    //     const id = remainServeActivityIds.value.shift();
    //     localValue.value = id;
    //   }
    // };

    return {
      ...toRefs(props),
      localValue,
      tableItems,
      tagTableItems,
      ransack,
      localPayload,
      // activityForm,
      // onChangeArticle,
    };
  },
});
export default ComIestAiNotifyServeActivitiesSelector;
</script>

<template lang="pug">
.com-iest-ai-notify-serve-activities-selector
  .flex.items-center
    TaApiField.field(
      v-if='localPayload.payload',
      recordName='过滤标签',
      v-model:value='localPayload.payload.serve_tag_ids',
      path='/serve/user/submodules/1/tags',
      :multiple='true',
      :disabled='true',
      :tableItems='tagTableItems'
    )
  //- h1 {{ localValue}}
  TaApiStoreField(
    v-model:value='localValue',
    recordName='文章',
    path='/serve/user/submodules/1/activities',
    display='table',
    :attrs='["name", "cover_image", "content"]',
    :disabled='disabled',
    :tableItems='tableItems',
    :ransackStr='JSON.stringify(ransack)',
    :displayConfigurableForm='activityForm'
  )
    template(#display-extra)
      a-button(
        v-if='localPayload.article_content',
        type='primary',
        @click='onChangeArticle'
      ) 换一篇

    template(#modal-header-left)
      .flex.items-center
        .text-gray-900.text-sm.mb-2.font-medium 过滤标签：
        TaApiField.field(
          v-if='localPayload.payload',
          v-model:value='localPayload.payload.serve_tag_ids',
          recordName='过滤标签',
          path='/serve/user/submodules/1/tags',
          :multiple='true',
          :tableItems='tagTableItems'
        )
</template>

<style lang="stylus" scoped>
.field
  >>> .display-layout
    flex-direction row !important
    align-items center !important
    .tag-display
      @apply mb-2
</style>
