<script lang="ts">
import { defineComponent, toRefs, computed, ref } from 'vue';

const ComIestAiNotifyUsersSelector = defineComponent({
  name: 'ComIestAiNotifyUsersSelector',
  components: {},
  props: {
    value: { type: Array, default: () => [] },
    payload: { type: Object, default: () => ({}) },
    disabled: { type: Boolean, default: false },
  },
  setup(props, { emit }) {
    const localValue = computed({
      get: () => props.value,
      set: val => {
        emit('update:value', val);
      },
    });

    const localPayload = computed({
      get: () => props.payload,
      set: val => {
        emit('update:payload', val);
      },
    });

    if (!localPayload.value.payload) {
      localPayload.value.payload = {};
    }

    const userTableItems = [
      {
        name: '姓名',
        type: 'string',
        search: true,
        data_index: 'name',
      },
      {
        name: '账号',
        type: 'string',
        search: true,
        data_index: 'account',
      },
      {
        name: '岗位',
        type: 'string',
        data_index: 'duty_names',
      },
      {
        name: '部门',
        type: 'string',
        data_index: 'org_names',
      },
    ];

    const tableItems = [
      {
        name: '名称',
        type: 'string',
        search: true,
        data_index: 'name',
      },
      {
        name: '',
        type: 'string',
        data_index: 'path_names',
      },
    ];

    const tagTableItems = [
      {
        name: '名称',
        type: 'string',
        search: true,
        data_index: 'name',
      },
    ];

    const ransack = computed(() => {
      return {
        res_tags_id_in: localPayload.value.payload?.res_tag_ids || [],
        orgs_id_in: localPayload.value.payload?.org_ids || [],
      };
    });

    const currentPage = ref(1);

    return {
      ...toRefs(props),
      localValue,
      tableItems,
      tagTableItems,
      userTableItems,
      ransack,
      localPayload,
      currentPage,
    };
  },
});
export default ComIestAiNotifyUsersSelector;
</script>

<template lang="pug">
.com-iest-ai-notify-users-selector
  .flex.flex-col(v-if='localPayload')
    TaApiField.field(
      recordName='过滤部门',
      v-model:value='localPayload.org_ids',
      path='/res/member/orgs',
      :multiple='true',
      :disabled='true',
      :tableItems='tableItems'
    )
      template(#tags='{ records }')
        .tag-display.flex.items-center.flex-wrap.gap-2.mr-2.mb-2
          template(v-for='tag in records', :key='tag.id')
            ComTag(:text='tag.name', title='组织')
    TaApiField.field(
      recordName='过滤标签',
      v-model:value='localPayload.res_tag_ids',
      path='/res/member/tags',
      :multiple='true',
      :disabled='true',
      :tableItems='tagTableItems'
    )
      template(#tags='{ records }')
        .tag-display.flex.items-center.flex-wrap.gap-2
          template(v-for='tag in records', :key='tag.id')
            ComTag(:text='tag.name', title='标签')

  TaApiField(
    v-model:value='localValue',
    recordName='接收人',
    display='table',
    path='/res/member/users',
    :tableItems='userTableItems',
    :attrs='["name", "account", "mobile", "duty_names", "org_names"]',
    :multiple='true',
    :disabled='disabled',
    :ransackStr='JSON.stringify(ransack)'
  )
    template(#table='{ records, actions, searchKeyword }')
      .title.flex.justify-between.items-center.mb-2
        .text-gray-900.text-sm.mb-2.font-medium.flex-shrink-0(
          class='dark:text-white'
        ) 发送人员：

        .actions.flex.items-center.space-x-2
          //- a-input-search(
          //-   :value='searchKeyword'
          //-   style='width: 150px; margin-bottom: 8px'
          //-   @update:value='(val) => { actions.updateSearchKeyword(val); currentPage = 1 }'
          //- )
          .mb-2
            a-button(
              v-if='!disabled',
              type='primary',
              @click.stop='actions.openSelector'
            ) 重新选择
      .grid.grid-cols-2.gap-2
        .user__card.bg-white.rounded.p-2.relative(
          v-for='(user, index) in records.slice((currentPage - 1) * 15, (currentPage - 1) * 15 + 15)',
          :key='user.id',
          class='dark:bg-primary-900'
        )
          .text-xs.text-gray-900.font-medium.leading-none.mb-1(
            class='dark:text-white',
          ) {{ user.name }}
          .text-xs.text-gray-700.font-normal(
            class='dark:text-white',
          ) {{ user.org_names?.join(',') || '暂无组织' }}
          .absolute.top-1.right-1.text-gray-500.z-2(
            v-if='!disabled',
            class='dark:text-white',
            @click.stop='actions.removeRecord(index)'
          )
            TaIcon.cursor-pointer(type='outline/x' class='!w-4 !h-4')
      .mt-2.flex.justify-end
        a-pagination(
          v-model:current='currentPage',
          :total='records.length',
          :pageSize='15',
          :showSizeChanger='false',
          :hideOnSinglePage='true',
          @change='actions.onTableChange'
        )
    template(#modal-header-left)
      div
        .flex.items-center(v-if='localPayload')
          .text-gray-900.text-sm.mb-2.font-medium 过滤部门：
          TaApiField.field(
            recordName='过滤部门',
            v-model:value='localPayload.org_ids',
            path='/res/member/orgs',
            :multiple='true',
            :tableItems='tableItems'
          )
        .flex.items-center(v-if='localPayload')
          .text-gray-900.text-sm.mb-2.font-medium 过滤标签：
          TaApiField.field(
            recordName='过滤标签',
            v-model:value='localPayload.res_tag_ids',
            path='/res/member/tags',
            :multiple='true',
            :tableItems='tagTableItems'
          )
</template>

<style lang="stylus" scoped>
.field
  >>> .display-layout
    flex-direction row !important
    align-items center !important
</style>
