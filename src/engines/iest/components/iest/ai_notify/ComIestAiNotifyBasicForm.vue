<script lang="ts">
import { defineComponent, toRefs, computed } from 'vue';

const ComIestAiNotifyBasicForm = defineComponent({
  name: 'ComIestAiNotifyBasicForm',
  components: {},
  props: {
    value: { type: Object, default: () => ({}) },
    disabled: { type: Boolean, default: false },
  },
  setup(props, { emit }) {
    const localValue = computed({
      get: () => props.value,
      set: val => {
        emit('update:value', val);
      },
    });

    const useCustom = computed({
      get: () => (localValue.value.payload?.use_custom ? true : null),
      set: val => {
        if (!localValue.value.payload) {
          localValue.value.payload = {};
        }
        localValue.value.payload.use_custom = val ? true : false;
      },
    });

    return {
      ...toRefs(props),
      localValue,
      useCustom,
    };
  },
});
export default ComIestAiNotifyBasicForm;
</script>

<template lang="pug">
.com-iest-ai-notify-basic-form
  .field.flex.w-full.items-center
    .text-gray-900.label.whitespace-nowrap.font-medium(class='dark:text-white') 发送时间：
    TaDateTimePicker.value(
      v-model:value='localValue.send_at',
      :disabled='disabled',
      :item='{ options: { placeholder: "请选择发送时间" } }'
    )

  .field.flex.w-full.items-center.mt-4
    .text-gray-900.label.whitespace-nowrap.font-medium(class='dark:text-white') 内容类型：
    TaRadioGroup.value(
      v-model:value='useCustom',
      :disabled='disabled',
      :item='{ options: {}}',
      :options='[{ label: "自定义内容", value: true }, { label: "素材库素材", value: null } ]'
    )


</template>

<style lang="stylus" scoped>
.field
  >>> .display-layout
    flex-direction row !important
    align-items center !important
  >>> .picker
    @apply "!bg-primary-900 text-white border-none";
  >>> .ant-picker-input input
    @apply "!text-white";
</style>
