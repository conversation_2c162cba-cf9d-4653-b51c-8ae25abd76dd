import { computed, ref, Ref } from 'vue';
import { IestAiNotifyMessage } from './types';
import { IestAiNotifyManageMessagesApi } from '@/engines/iest/apis/iest/ai_notify/manage/tags.api';
import { VObject } from '@/lib/vails';

let uid = 0;

export const useAiNotifyMessage = (
  messages: Ref<IestAiNotifyMessage[]>,
  payload: Ref<VObject>,
  intentType = 'create_notify',
) => {
  const history = computed(() =>
    messages.value.filter(msg => msg.role === 'user').map(msg => msg.message),
  );

  const generateId = () => {
    return `${new Date().getTime()}-${uid++}`;
  };

  const generateMessage = (
    msg: string,
    role: 'system' | 'user' = 'system',
    extra: any = {},
  ): IestAiNotifyMessage => {
    return {
      id: generateId(),
      message: msg,
      role,
      ...extra,
    };
  };

  const createNotifyInitMessages = (): IestAiNotifyMessage[] => [
    generateMessage('请告诉我您要发送的消息任务', 'system', {
      actions: [
        {
          name: '国庆前一天给一把手发送国庆廉洁提醒',
          callback: () => {
            onCreate('国庆前一天给一把手发送国庆廉洁提醒');
          },
        },
        {
          name: '明天下午六点给张三发送招投标廉洁提醒',
          callback: () => {
            onCreate('明天下午六点给张三发送招投标廉洁提醒');
          },
        },
      ],
    }),
  ];

  switch (intentType) {
    case 'create_notify':
      createNotifyInitMessages().forEach(msg => messages.value.push(msg));
      break;

    case 'search_user':
      break;

    case 'search_article':
      break;
    default:
      break;
  }

  const api = new IestAiNotifyManageMessagesApi();
  const loading = ref(false);

  const onCreate = async (msg: string) => {
    if (loading.value) return;

    loading.value = true;

    messages.value.push({
      id: generateId(),
      message: msg,
      role: 'user',
    });

    messages.value.push({
      id: generateId(),
      message: '正在发送中...',
      role: 'system',
    });

    api
      .create({
        sentence: msg,
        history: history.value,
        meta: payload.value,
      })
      .then(res => {
        // const res = {
        //   data: {
        //     action: 'create_notify_done',
        //     message: '已为您填写消息发送任务',
        //     meta: {
        //       send_at: '2024-09-30 00:00:00',
        //       user_ids: [
        //         1373, 5359, 5658, 5738, 6117, 7160, 7371, 7424, 8861, 1460, 1655, 2631, 6160, 6480,
        //         7287, 7288, 6239, 2288, 2293, 6685, 2299, 7032, 2582, 7976, 6405, 6731, 2289, 7377,
        //         8863, 7403, 2285, 238, 239, 2600, 7414, 2380, 240, 1669, 7993, 2260, 2645, 1668, 1657,
        //         4172, 2396, 7114, 2287, 4674, 1683, 4800, 2262,
        //       ],
        //       res_tag_ids: [19],
        //       serve_content_type_tag_ids: [1],
        //       serve_activities: [
        //         {
        //           id: 8012,
        //           name: '清风丨清正在德 廉洁在志',
        //         },
        //         {
        //           id: 6399,
        //           name: '图解纪法丨处分担任两个以上职务违纪党员的审批程序',
        //         },
        //         {
        //           id: 8645,
        //           name: '《中国共产党纪律处分条例》解读微视频丨如何理解免予处分、不予处分、不追究党纪责任情形',
        //         },
        //         {
        //           id: 9143,
        //           name: '解读微视频丨如何理解对在网络空间有不当言行的处分规定',
        //         },
        //         {
        //           id: 10443,
        //           name: '春城热线 | 明天上午9:00昆明高新区将做客直播节目《春城热线》',
        //         },
        //       ],
        //     },
        //   },
        // };
        messages.value.splice(-1, 1);
        setTimeout(() => {
          processResponse(res.data);
          loading.value = false;
        }, 500);
      });
  };

  const processResponse = (data: VObject) => {
    switch (true) {
      case ['change_article'].includes(data.action):
        messages.value.push(generateMessage(data.message, 'system'));
        // Object.assign(payload.value, data.meta);
        break;
      // case ['create_notify_done', 'create_notify_empty', 'search_user_done'].includes(data.action):
      case !!data.message:
        messages.value.push(generateMessage(data.message, 'system'));
        Object.assign(payload.value, data.meta);
        break;

      default:
        break;
    }
  };

  return {
    onCreate,
    loading,
    generateId,
  };
};
