<script lang="ts">
import { ref, defineComponent, toRefs, computed, watch, nextTick } from 'vue';
import ComIestAiNotifyMessage from './ComIestAiNotifyMessage.vue';
import { message } from 'ant-design-vue';
import scrollIntoView from 'smooth-scroll-into-view-if-needed';

const ComIestAiNotifyChatContainer = defineComponent({
  name: 'ComIestAiNotifyChatContainer',
  components: {
    ComIestAiNotifyMessage,
  },
  props: {
    messages: { type: Array, default: () => [] },
    loading: { type: Boolean, default: false },
  },
  emits: ['update:messages', 'update:value', 'send'],
  setup(props, { emit }) {
    const localMessages = computed({
      get: () => props.messages,
      set: value => emit('update:messages', value),
    });

    const value = ref('');
    // const localValue = computed({
    //   get: () => props.value,
    //   set: value => emit('update:value', value),
    // });

    const onSend = () => {
      if (!value.value) {
        message.warning('请输入内容');
        return;
      }
      emit('send', value.value);
      value.value = '';
    };

    const chatBottomAnchor = ref<any>(null);
    const showIcon = ref(true);

    watch(
      () => props.loading,
      () => {
        // if (!props.loading) {
        nextTick(() => {
          scrollIntoView(chatBottomAnchor.value);
        });
        // }

        if (!props.loading) {
          setTimeout(() => {
            showIcon.value = true;
          }, 500);
        } else {
          showIcon.value = false;
        }
      },
    );

    return {
      ...toRefs(props),
      localMessages,
      chatBottomAnchor,
      // localValue,
      value,
      showIcon,
      onSend,
    };
  },
});
export default ComIestAiNotifyChatContainer;
</script>

<template lang="pug">
.com-iest-ai-notify-chat-container.mp-12.h-full.flex.flex-col
  .flex-grow.h-0.bg-gray-200.rounded-lg.p-4.flex-col.flex
    .space-y-4.flex-grow.h-0.overflow-y-auto.overflow-x-hidden.mb-4
      transition-group(name='list')
        ComIestAiNotifyMessage(
          v-for='message in localMessages', :record='message', :key='message.id'
        )
        .chat-bottom-anchor(ref='chatBottomAnchor')

    .flex.space-x-2
      a-textarea(v-model:value='value', :autoSize='{ minRows: 1, maxRows: 4 }')
      a-button(:loading='loading', style='height: 100%', @click='onSend')
        .flex.justify-center.items-center
          TaIcon(v-if='!loading && showIcon', type='SendOutlined')

</template>

<style lang="stylus" scoped>
.list-enter-active,
.list-leave-active {
  transition: all 0.5s ease;
}
.list-enter-from,
.list-leave-to {
  opacity: 0;
  transform: translateX(30px);
}
</style>
