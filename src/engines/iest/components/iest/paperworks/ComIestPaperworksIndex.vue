<script lang="ts">
import { defineComponent, computed, ref, Ref, toRefs, reactive, PropType } from 'vue';
import { TaIndexViewTabInterface } from '@/components/global/ta-component/TaIndexView/types';
import { VObject } from '@/lib/vails/model';
import ComIestPaperworksShow from './ComIestPaperworksShow.vue';
import { useCable } from '@/engines/base/channels/useCable';
import { VStore } from '@/lib/vails';
import ComIestPaperworkState from './ComIestPaperworkState.vue';

const ComIestPaperworksIndex = defineComponent({
  name: 'ComIestPaperworksIndex',
  components: {
    ComIestPaperworksShow,
    ComIestPaperworkState,
  },
  props: {
    store: { type: Object as PropType<VStore>, required: true },
  },
  setup(props, { emit }) {
    const config = computed(() => ({
      recordName: '风险预警',
      store: props.store,
      // pagination: {
      //   perPage: 15,
      //   showPageSizeChanger: false,
      //   hideOnSinglePage: false,
      //   showSizeChanger: false,
      // }
      template: 'iest_paperwork',
      detail: {
        //   mode: 'auto',
        mode: 'dialog',
        width: '1600px',
      },
      mode: 'table',
      // editApi:
      // showCount: true,
      // actions: [
      //  { key: 'create', enabled: true },
      //  { key: 'update', enabled: true },
      //  { key: 'delete', enabled: true },
      //  { key: 'import', enabled: true },
      //  { key: 'export', enabled: true },
      // ],
      table: {
        scroll: { y: 'auto' },
        // columns: [{ dataIndex: 'id', title: 'ID' }],
      },
      list: {
        scroll: { y: 'auto' },
      },
      // selection: {
      //   showByDefault: false;
      //   showSwitch: false;
      // };
      // searcherSimpleOptions: [{key: 'title', label: '标题', type: 'string' }],
    }));

    const statistics = ref({
      all: 0,
      uploading: 0,
      success: 0,
    });

    const tabs = computed<TaIndexViewTabInterface[]>(() => [
      {
        key: 'success',
        label: '已成功',
        num: statistics.value.success,
        query: {
          state_eq: 'success',
        },
      },
      {
        key: 'uploading',
        label: '处理中',
        num: statistics.value.uploading,
        query: {
          state_eq: 'uploading',
        },
      },
      {
        key: 'all',
        label: '全部',
        num: statistics.value.all,
        query: {},
      },
    ]);

    const onIndex = (data: VObject) => {
      statistics.value = data.statistics;
    };

    const taIndexView = ref<any>(null);

    props.store.extra.cable_key = 'iest_paperworks';
    useCable(props.store, { taIndexViewRef: taIndexView });

    return {
      ...toRefs(props),
      config,
      tabs,
      onIndex,
      taIndexView,
    };
  },
});

export default ComIestPaperworksIndex;
</script>

<template lang="pug">
.com-iest-manage-paperworks-index
  TaIndexView(ref='taIndexView', :config='config' :tabs='tabs' @onIndex='onIndex')
    template(#bodyCell='{ record, column }')
      template(v-if='column.dataIndex[0] === "state"')
        ComIestPaperworkState(:record='record')
    //- template(#card='{ record }')
    //- template(#table)
    //-    a-table-column(title='名称' dataIndex='record')
    template(#detail='{ record, onClose }')
      .h-85vh.overflow-y-auto
        ComIestPaperworksShow(
          v-if='record.id',
          :store='store',
        )

</template>

<style lang="stylus" scoped>
.com-iest-manage-paperworks-index
  height 100%
  width 100%
</style>
