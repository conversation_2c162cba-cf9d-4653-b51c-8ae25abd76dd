import { message } from 'ant-design-vue';
export default function useAnchor() {
  let seq = 1;

  // 跳转到指定锚点
  function toAnchor(id: string) {
    const el = document.getElementById(id);
    if (el) el.scrollIntoView({ behavior: 'smooth', block: 'start' });
  }

  /**
   * 先遍历p元素，如未找到，则遍历p元素的子元素，如未找到，则提示未找到
   * @param selector
   * @param targetValue
   */
  function pAnchor(selector: string, targetValue: string) {
    //获取全部p元素
    const ps = document.querySelectorAll(selector);
    const psArr = Array.from(ps);
    const targetP: any = psArr.find((x: any) => x.innerText.includes(targetValue));

    psArr.forEach((p: any) => {
      p.style.backgroundColor = 'transparent';
    });

    if (targetP) {
      targetP.setAttribute('id', `${seq}`);
      targetP.style.backgroundColor = '#FDE8E8';
      toAnchor(`${seq++}`);
    } else {
      //获取全部p元素的子元素
      const children = psArr.map((y: any) => y.children);
      const target = children.find(x => {
        return (Array.from(x) as HTMLAllCollection[])
          .reduce((acc: any, cur: any) => acc + cur.innerText, '')
          ?.replaceAll(' ', '')
          ?.includes(targetValue);
      });
      //获取符合条件的父元素p
      const parent = target?.[0]?.parentElement;
      if (parent) {
        parent.setAttribute('id', `${seq}`);
        parent.style.backgroundColor = '#FDE8E8';
        toAnchor(`${seq++}`);
      } else {
        message.warning('未找到对应处');
      }
    }
  }

  function tableAnchor(selector: string, targetValue: string) {
    const tables = document.querySelectorAll(selector);
    const tablesArr = Array.from(tables);
    if (!tablesArr || tablesArr.length === 0) {
      message.warning('未找到对应处');
      return;
    }
    switch (tablesArr.length) {
      case 1:
        const targetTable: any = tablesArr[0];
        targetTable.style.backgroundColor = '#FDE8E8';

        //用table锚点会导致偏移，所以改用td锚点
        const firstTd = document.querySelector(`${selector} td`);
        if (firstTd) {
          firstTd.setAttribute('id', `${seq}`);
          toAnchor(`${seq++}`);
        }
        break;
      default:
        break;
    }
  }

  function canvasPageAnchor(pageNum: number) {
    const canvas = document.querySelectorAll('canvas');
    const canvasArr = Array.from(canvas);

    //清空标记的border
    canvasArr.map((c: any) => (c.style.border = 'none'));

    const targetCanvas: any = canvasArr?.[pageNum - 1];
    if (targetCanvas) {
      targetCanvas.setAttribute('id', `${seq}`);
      targetCanvas.style.border = '2px solid red';
      toAnchor(`${seq++}`);
    } else {
      message.warning('未找到对应处');
    }
  }

  return {
    toAnchor,
    pAnchor,
    tableAnchor,
    canvasPageAnchor,
  };
}
