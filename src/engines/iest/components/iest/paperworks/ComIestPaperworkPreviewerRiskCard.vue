<script setup lang="ts">
import { computed, ref } from 'vue';

enum RiskLevel {
  Normal = '一般风险',
  High = '重大风险',
}
interface Props {
  level: RiskLevel;
  content: string;
  ruleName: string;
  tags: Array<any>;
}
const props = defineProps<Props>();
const emit = defineEmits(['anchor']);

const pages = computed(() => {
  return props.tags?.length || 0;
});
const isShowContent = ref(false);
const changeContentDisplay = () => {
  isShowContent.value = !isShowContent.value;
};

//处理返回的```json{}````
const handleContent = computed(() => {
  return props.content; // JSON.parse(props.content.substring(7, props.content.length - 4).trim());
});
</script>

<template lang="pug">
.com-iest-paperwork-previewer-risk-card.cursor-pointer.relative
  .absolute.risk__level.left-0.top-0.w-1.h-full(
    :class='{ normal: level === RiskLevel.Normal, high: level === RiskLevel.High }'
  )
  .flex.items-center.risk__wrapper
    TaIcon.w-4.h-4.mr-2.show__icon(
      type='outline/chevron-right',
      color='#999999',
      :class='{down:isShowContent}',
      @click.stop='changeContentDisplay'
    )
    a-tooltip(:title='ruleName')
      .text-sm.leading-normal.text-black(class='dark:text-white') {{ ruleName }}
    //- .flex.ml-auto.items-center
    //-   TaIcon.page__change.w-14px.h-14px(
    //-     type='outline/chevron-left',
    //-     color='#999999',
    //-     @click='changePage("last")'
    //-   )
    //-   span {{idx}}/{{ pages }}
    //-   TaIcon.page__change.w-14px.h-14px(
    //-     type='outline/chevron-right',
    //-     color='#999999',
    //-     @click='changePage("next")'
    //-   )
  .risk__content.pt-4(v-if='isShowContent')
    template(
      v-for='(value,key,i) in handleContent'
    )
      p.text-sm.font-normal.text-black.px-5 {{i+1}}.{{ key }}：
        br
      p.w-full.whitespace-pre-wrap.pl-3 {{ value }}

</template>

<style lang="stylus">
.com-iest-paperwork-previewer-risk-card
  border-radius 4px
  border 1px solid #e4e5e7
  padding 10px
  .risk__level
    border-radius 4px 0 0 4px
  .normal
    background orange
  .high
    background red
  .risk__wrapper
    .show__icon
      transition transform 150ms
    .down
      transform rotate(90deg)
      transition transform 200ms
    .page__change:hover
      @apply text-blue-500;
</style>
