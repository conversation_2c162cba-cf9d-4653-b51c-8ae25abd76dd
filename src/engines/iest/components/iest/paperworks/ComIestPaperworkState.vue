<script lang="ts">
import { ref, defineComponent, toRefs } from 'vue';

const ComIestPaperworkState = defineComponent({
  name: 'ComIestPaperworkState',
  components: {},
  props: {
    record: { type: Object, default: () => ({}) },
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
export default ComIestPaperworkState;
</script>

<template lang="pug">
template(v-if='record.state === "success"')
  a-tag(color='green') 已完成
template(v-else-if='record.state === "uploading"')
  a-tag(color='blue') 处理中
template(v-else-if='record.state === "failed"')
  a-tag 失败
template(v-else)
  a-tag 未知
</template>

<style lang="stylus" scoped></style>
