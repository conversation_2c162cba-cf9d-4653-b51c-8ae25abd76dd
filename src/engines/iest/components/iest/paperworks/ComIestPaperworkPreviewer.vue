<script lang="ts">
import { ref, defineComponent, toRefs, reactive, watch } from 'vue';
import VueOfficeDocx from '@vue-office/docx';
import VueOfficePdf from '@vue-office/pdf';
import '@vue-office/docx/lib/index.css';
import useAnchor from './useAnchor';
import { VObject } from '@/lib/vails/model/index';
import ComIestPaperworkPreviewerRiskCard from './ComIestPaperworkPreviewerRiskCard.vue';
import { message } from 'ant-design-vue';
import useNavigateTab from '@/components/global/ta-component/useNavigateTab';
import { useRoute } from 'vue-router';

enum FileType {
  PDF = 'pdf',
  Doc = 'doc',
  Docx = 'docx',
}

const ComIestPaperworkPreviewer = defineComponent({
  name: 'ComIestPaperworkPreviewer',
  components: {
    VueOfficeDocx,
    VueOfficePdf,
    ComIestPaperworkPreviewerRiskCard,
  },
  props: {
    record: { type: Object, default: () => ({}) },
  },
  setup(props) {
    // const { updateTitle } = useNavigateTab();
    // const route = useRoute();

    // watch(
    //   () => props.record?.name,
    //   () => {
    //     if (props.record.name) updateTitle(props.record.name || '合同审查', route.fullPath);
    //   },
    //   { immediate: true },
    // );

    const { pAnchor } = useAnchor();

    const MAIN_BG_URL =
      'url(https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/demo/background.png)';
    const RES_BG_URL = 'url(https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/demo/bg2.png)';
    const fileIconMap = {
      [FileType.PDF]:
        'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/demo/Type%3D%E6%96%87%E4%BB%B6%E7%B1%BB%E5%9E%8B-%E6%A0%87%E5%87%86%E5%9B%BE-PDF%E6%96%87%E6%A1%A3.png',
      [FileType.Doc]:
        'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/demo/Type%3D%E6%96%87%E4%BB%B6%E7%B1%BB%E5%9E%8B-%E6%A0%87%E5%87%86%E5%9B%BE-Word%E6%96%87%E6%A1%A3.png',
      [FileType.Docx]:
        'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/demo/Type%3D%E6%96%87%E4%BB%B6%E7%B1%BB%E5%9E%8B-%E6%A0%87%E5%87%86%E5%9B%BE-Word%E6%96%87%E6%A1%A3.png',
    };

    const getFileIcon = (fname: string) => {
      if (fname.includes(FileType.PDF)) {
        return fileIconMap[FileType.PDF];
      }

      return fileIconMap[FileType.Docx];
    };

    const file = reactive({
      fileName: props.record.attachment?.files?.[0]?.fileName,
      fileURL: props.record.attachment?.files?.[0]?.url,
      id: 0,
    });
    const isPlaneLoading = ref(true);
    const isResultLoading = ref(true);
    const changeLoading = (mode: boolean) => {
      isPlaneLoading.value = mode;
      isResultLoading.value = mode;
    };

    const renderedHandler = (e: any) => {
      changeLoading(false);
    };

    const riskTabs = [
      {
        label: '全部',
        key: 'all',
        color: '#2468f2',
      },
      {
        label: '重大风险',
        key: 'high',
        color: 'rgb(243, 62, 62)',
      },
      {
        label: '一般风险',
        key: 'normal',
        color: 'rgb(255, 147, 38)',
      },
    ];

    const riskItems = ref({
      all: [] as VObject[],
      high: [] as VObject[],
      normal: [] as VObject[],
    });

    const fetchResult = async (refresh = false) => {
      // if (refresh) await props.record.fetch();
      // (
      //   props.record.response?.result?.风险点列表 ||
      //   props.record.response?.result?.风险点 ||
      //   props.record.response?.result ||
      //   []
      // ).forEach((item: VObject) => {
      //   if (item.风险严重程度评分 >= 7) {
      //     riskItems.value.high.push({ riskName: '重大风险', ...item });
      //     riskItems.value.all.push({ riskName: '重大风险', ...item });
      //   } else if (item.风险严重程度评分 >= 5) {
      //     riskItems.value.normal.push({ riskName: '一般风险', ...item });
      //     riskItems.value.all.push({ riskName: '一般风险', ...item });
      //   }
      // });
    };

    fetchResult();

    const activeRiskTabKey = ref('all');
    const changeRiskTab = (tab: any) => {
      activeRiskTabKey.value = tab.key;
    };

    const marks: any[] = [];
    const findAnchor = (item: VObject, rects?: number[][]) => {
      console.log(rects, 'anchor content');

      marks.forEach((el: any) => {
        el.remove();
      });
      if (file.fileName?.includes('.pdf')) {
        const canvas = document.querySelectorAll('canvas');
        const canvasAry = Array.from(canvas);
        const scale = canvasAry[0].clientWidth / 600;
        if (!rects) {
          message.warning('未找到对应处');
          return;
        }
        rects?.forEach(rect => {
          const [pageNumber, x, y, x1, y1] = rect;

          const mark = document.createElement('div');
          mark.style.position = 'absolute';
          mark.style.left = `${x * scale}px`;
          mark.style.top = `${y * scale +
            30 +
            (pageNumber - 1 === 0 ? -20 * scale : canvasAry[pageNumber - 1].offsetHeight)
            }px`;
          mark.style.width = `${(x1 - x) * scale}px`;
          mark.style.height = `${(y1 - y) * scale}px`;
          mark.style.backgroundColor = 'rgba(255, 0, 0, 0.5)';
          mark.style.zIndex = '999';
          mark.style.pointerEvents = 'none';
          // 都是同一个 parent
          canvasAry[0].parentNode?.appendChild(mark);
          marks.push(mark);
        });
      } else {
        pAnchor('.docx article > p', item.raw);
      }

      if (marks.length > 0) {
        marks[0].scrollIntoView({ behavior: 'smooth', block: 'center' });
      }
    };

    return {
      ...toRefs(props),
      MAIN_BG_URL,
      RES_BG_URL,
      getFileIcon,
      fileIconMap,
      file,
      isPlaneLoading,
      isResultLoading,
      renderedHandler,
      riskTabs,
      riskItems,
      activeRiskTabKey,
      changeRiskTab,
      findAnchor,
    };
  },
});
export default ComIestPaperworkPreviewer;
</script>

<template lang="pug">
.com-iest-paperwork-previewer.bg-cover.p-4.flex.flex-col.h-full(
  class='dark:bg-[#13325b]'
)
  .demo__wrapper.flex.flex-grow.gap-x-4.overflow-hidden.h-full
    .plane.flex-grow.px-6.pb-6.flex.flex-col.relative.h-full(
      class='dark:bg-[#09183F] dark:rounded-lg'
    )
      div.mask.absolute.w-full.h-full.bg-white.z-999.left-0.opacity-80(
        v-if='isPlaneLoading'
      )
      .plane__header.pt-4.h-64px.flex
        img.w-6.h-6.mr-2(
          v-if='file.fileName?.length > 1'
          :src='getFileIcon(file.fileName)'
        )
        .text-base.font-medium.mr-auto {{ file.fileName }}【{{ record.name }}】
        //- a-button(v-if='!contractId' type='primary', @click='visible = true') 上传合同
        //- .ml-2
        //-   a-button(type='primary',@click='toCopy(file.fileURL)') 下载结果
      .plane__content.flex-grow.overflow-auto.h-0
        vue-office-docx(
          v-if='file.fileURL && file.fileName?.includes(".docx")',
          :src='file.fileURL',
          @rendered="renderedHandler",
          @error="errorHandler",
        )
        vue-office-pdf(
          v-else-if='file.fileURL && file.fileName?.includes(".pdf")',
          :src='file.fileURL',
          @rendered="renderedHandler",
          @error="errorHandler",
        )
        .h-full.w-full.flex.flex-col.items-center.justify-center(v-else)
          .empty__icon.w-40.h-40.bg-cover.mb-2
          .text-sm.text-gray-500 暂无文件
    .result.bg-cover.px-4.py-1.w-100.flex.flex-col.overflow-hidden.flex-shrink-0.relative
      div.mask.absolute.w-full.h-full.bg-white.z-999.left-0.opacity-80(
        v-if='isResultLoading'
      )
      .result__header.h-46px.flex-shrink-0
        .result__block.h-full.flex.items-center.cursor-pointer
          TaIcon.w-4.h-4.mr-1(type='MenuFoldOutlined',color='#1C64F2')
          .text-blue-600.text-sm.leading-normal.font-medium(class='dark:text-[#1E429F)]') 审查结果
      .risk__tabs__wrapper.w-full.flex.items-center.py-4.flex-shrink-0(v-if='riskItems["all"].length > 0')
        .text-sm.font-semibold.text-black.mr-4 风险等级
        .risk__tabs.flex.gap-x-2
          .risk__tab.text-xs.cursor-pointer(
            v-for='tab in riskTabs',
            :style='`--c:${tab.color}`',
            :class='{acitveRiskTab: activeRiskTabKey === tab.key}',
            @click='activeRiskTabKey = tab.key',
          ) {{ tab.label }} {{ riskItems[tab.key].length }}
      .result__content.w-full.flex-grow.overflow-y-auto
        .result__content__wrapper.w-full.flex.flex-col.gap-y-2(v-if='record?.id')
          ComIestPaperworkPreviewerRiskCard.w-full(
            class='dark:bg-[#09183F]'
            v-for="item in record.paperwork_results"
            :level='`一般风险`',
            :ruleName='item.name',
            :content='{ "原文": item.raw }',
            @click='() => findAnchor(item, item.rects)'
          )
          //- :tags='item.tags',
        .h-full.w-full.flex.flex-col.items-center.justify-center(v-else)
          .empty__icon.w-40.h-40.bg-cover.mb-2
          .text-sm.text-gray-500 暂无文件
</template>

<style lang="stylus" scoped></style>
