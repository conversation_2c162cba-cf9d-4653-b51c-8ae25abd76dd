<script lang='ts'>
import { ref, defineComponent, toRefs, computed, watch } from 'vue';
import * as echarts from 'echarts';
const ComHomeRuleMsgPie = defineComponent({
  name: 'ComHomeRuleMsgPie',
  components: {},
  props: {
    data: {
      type: Array, default: () => [
        { value: 0, name: '暂无数据' }
      ]
    }
  },
  setup(props) {
    const echartsRef = ref<any>(null);
    const pieOption = computed(() => ({
      color: ['#F05252', '#3F83F8'],
      grid: {
        top: '25%',
        left: '1%'
      },
      legend: {
        bottom: 'center',
        right: 'right',
        icon: 'circle',
        itemWidth: 10,
        textStyle: {
          color: '#4B5563'
        },
        formatter: function (name: string) {
          const thisItem: any = props.data.find((item: any) => item.name === name)
          const v = thisItem ? thisItem.value : 0
          return `${name}·${v}`
        }
      },
      series: [
        {
          name: 'pie',
          silent: true,
          type: 'pie',
          radius: '89%',
          center: ['30%', '50%'],
          itemStyle: {
            borderColor: 'white',
            borderWidth: 1,
          },
          label: {
            show: false,
          },
          data: Array.isArray(props.data) ? props.data : [props.data],

        }
      ]
    }))


    return {
      ...toRefs(props),
      pieOption,
      echartsRef,
    };
  },
});
export default ComHomeRuleMsgPie;
</script>

<template lang="pug">
.w-full.h-full
  TaScreenEchartBase(:options='pieOption' ref='echartsRef')
</template>

<style lang="stylus" scoped></style>
