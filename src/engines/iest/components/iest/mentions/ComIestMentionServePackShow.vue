<script lang="ts">
import {
  ref,
  defineComponent,
  toRefs,
  computed,
  onMounted,
  Ref,
  inject,
  onUnmounted,
  watch,
} from 'vue';
import ComIestAiNotifyBasicForm from '../ai_notify/ComIestAiNotifyBasicForm.vue';
import ComIestMentionServeActivitiesSelector from './ComIestMentionServeActivitiesSelector.vue';
import ComIestAiNotifyUsersSelector from '../ai_notify/ComIestAiNotifyUsersSelector.vue';
import { message } from 'ant-design-vue';
import ComServeMessagesIndex from '../../serve/messages/ComServeMessagesIndex.vue';
import { VStore } from '../../../../../lib/vails/store/index';
import { ServeManageMessagesApi } from '../../../serve-core/apis/serve/manage/messages.api';
import { ServeMessageModel } from '../../../serve-core/models/serve/manage/messages';
import ComServePackShowCard from '../../serve/packs/ComServePackShowCard.vue';
import ComBpmInstanceDetailDialog from '@/engines/bpm/components/ComBpmInstanceDetailDialog.vue';
import { VObject } from '../../../../../lib/vails/model/index';
import { ServeManagePacksApi } from '../../../serve-core/apis/serve/manage/packs.api';
import { useCable } from '../../../../base/channels/useCable';
import { ComChatConversationLayoutCacheCacheKey } from '@/engines/chat/components/chat/conversations/ComChatConversationLayout.vue';

const ComIestMentionServePackShow = defineComponent({
  name: 'ComIestMentionServePackShow',
  components: {
    ComIestAiNotifyBasicForm,
    ComIestMentionServeActivitiesSelector,
    ComIestAiNotifyUsersSelector,
    ComServeMessagesIndex,
    ComServePackShowCard,
    ComBpmInstanceDetailDialog,
  },
  props: {
    record: { type: Object, default: () => ({}) },
    disabled: { type: Boolean, default: false },
  },
  emits: ['update:record', 'close', 'back'],
  setup(props, { emit }) {
    const localRecord = computed({
      get: () => props.record,
      set: val => {
        emit('update:record', val);
      },
    });

    const onConfirm = () => {
      localRecord.value.formData.aasm_state_event_later = 'finish';
      localRecord.value
        .save()
        .then(() => {
          message.success('发送成功');
          serveMessageParams.value = { q: { pack_id_eq: localRecord.value.mentionable_id } };
          cache.value.mentionActions = [];
        })
        .catch(() => {
          message.error('发送失败');
        })
        .finally(() => {
          localRecord.value.formData.aasm_state_event_later = undefined;
        });
    };

    const serveMessageStore = new VStore(new ServeManageMessagesApi(), ServeMessageModel);
    const servePackStore = new VStore(new ServeManagePacksApi(), ServeMessageModel);

    servePackStore.extra.cable_key = 'serve_packs';
    useCable(servePackStore);

    watch(
      () => props.record.mentionable_id,
      () => {
        if (props.record.mentionable_id) {
          servePackStore.find(props.record.mentionable_id);
        }
      },
      { immediate: true },
    );

    const serveMessageParams = ref({ q: { pack_id_eq: localRecord.value.mentionable_id } });

    const articleOffset = ref(6);
    const onChangeArticle = () => {
      localRecord.value.formData.renew_version = { offset: articleOffset.value };
      localRecord.value
        .save()
        .then(() => {
          message.success('文章更新成功');
          localRecord.value.formData.payload.activity_id =
            localRecord.value.formData.payload.payload.serve_activity_ids?.[0];
          articleOffset.value += 5;
        })
        .catch(() => {
          message.error('哎呀出错了');
        });
    };

    const instanceVisible = ref(false);

    const footerActions = [
      {
        key: 'finish',
        type: 'primary',
        name: '确认发送',
        callback: onConfirm,
        confirm: true,
      },
    ];

    const cache = inject<Ref<VObject>>(ComChatConversationLayoutCacheCacheKey)!;

    onMounted(() => {
      if (cache?.value && !props.disabled && !props.record.mentionable_id) {
        cache.value.mentionActions = footerActions;
      }
    });

    onUnmounted(() => {
      if (cache?.value) {
        cache.value.mentionActions = [];
      }
    });

    const fetchPackLater = () => {
      // setTimeout(() => {
      servePackStore.record.value.fetch();
      // }, 500);
    };

    return {
      ...toRefs(props),
      localRecord,
      onConfirm,
      serveMessageStore,
      serveMessageParams,
      onChangeArticle,
      instanceVisible,
      servePackStore,
      pack: servePackStore.record,
      fetchPackLater,
    };
  },
});

export default ComIestMentionServePackShow;
</script>

<template lang="pug">
.com-iest-mention-serve-pack-show.h-full.bg-white(
  class='dark:bg-screen-transparent dark:text-white'
)
  .flex.flex-col.h-full(v-if='!record.mentionable_id')
    //- header.flex.justify-between.items-center.mb-2.p-4.border-b.border-gray-200
    //-   TaIcon.cursor-pointer(type='outline/arrow-left' class='!w-5 !h-5' @click.stop='$emit("back")')
    //-   .text-base.text-gray-800.font-semibold 消息发送详情
    //-   TaIcon.cursor-pointer(type='outline/x' class='!w-5 !h-5' @click.stop='$emit("close")')
    .overflow-y-auto.space-y-4.flex-grow.h-0.p-4.py-2
      .bg-gray-100.p-4.rounded-lg(class='dark:bg-primary-800')
        ComIestAiNotifyBasicForm(
          v-model:value='localRecord.formData.payload',
          :disabled='disabled',
        )

      .bg-gray-100.p-4.rounded-lg(
        v-if='localRecord.formData.payload.payload?.use_custom'
        class='dark:bg-primary-800',
      )
        .text-gray-900.label.whitespace-nowrap.mb-2.font-medium(class='dark:text-white') 自定义内容：
        TaTextarea(
          v-model:value='localRecord.formData.payload.content',
          :disabled='disabled',
          class='dark:!text-white',
        )


      .bg-gray-100.p-4.rounded-lg(
        v-else,
        class='dark:bg-primary-800',
      )
        ComIestMentionServeActivitiesSelector(
          v-model:value='localRecord.formData.payload.activity_id',
          v-model:payload='localRecord.formData.payload.payload'
          :disabled='disabled',
          @changeArticle='onChangeArticle',
        )
      .bg-gray-100.p-4.rounded-lg(class='dark:bg-primary-800')
        ComIestAiNotifyUsersSelector(
          v-if='localRecord.formData.payload.payload',
          v-model:value='localRecord.formData.payload.payload.user_ids',
          v-model:payload='localRecord.formData.payload.payload'
          :disabled='disabled',
        )
    //- .footer.flex.rounded-b-xl.justify-end.items-center.bg-white.px-4.py-2.border-t.border-gary-200(v-if='!disabled')
    //-   TaPopoverConfirm(
    //-     tips='您确认发送改消息么？',
    //-     @confirm='onConfirm',
    //-   )
    //-     a-button(type='primary') 确认发送

  .flex.flex-col.h-full(v-else)
    //- header.flex.justify-between.items-center.mb-2.border-b.border-gray-200.p-4
    //-   .text-base.text-gray-800.font-semibold 消息发送详情
    //-   TaIcon.cursor-pointer(type='outline/x' class='!w-5 !h-5' @click.stop='$emit("close")')
    .px-4.py-2
      ComServePackShowCard.cursor-pointer(
        :record='pack',
        :msgStore='serveMessageStore',
        @click.stop='instanceVisible = true'
      )
    ComServeMessagesIndex(
      :store='serveMessageStore',
      :params='serveMessageParams',
      :showHeader='false',
      template='serve_message#ai',
      @afterUpdate='fetchPackLater',
    )
      //- @afterIndex='fetchPackLater'
    ComBpmInstanceDetailDialog(
      v-if='instanceVisible'
      v-model:visible='instanceVisible',
      :instanceId='localRecord.mentionable?.create_instance_id'
    )
    //- 详情

</template>

<style lang="stylus" scoped>
.dark .com-iest-mention-serve-pack-show
  >>> .ant-table-thead
    @apply bg-transparent;
  >>> .ant-table-cell
    @apply bg-transparent text-white;
  >>> .ant-table-placeholder:hover
    .ant-table-cell
      @apply "!bg-transparent text-white";
  >>> .ant-table, .ant-table-thead
    @apply "!bg-transparent !text-white"
</style>
