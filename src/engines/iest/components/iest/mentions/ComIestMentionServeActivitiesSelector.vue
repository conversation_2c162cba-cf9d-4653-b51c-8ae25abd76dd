<script lang="ts">
import { defineComponent, toRefs, computed, ref, watch, onMounted } from 'vue';
import ComServeActivitiesCardForAi from '../../serve/activities/ComServeActivitiesCardForAi.vue';
import { RetweetOutlined } from '@ant-design/icons-vue';

const ComIestAiNotifyServeActivitiesSelector = defineComponent({
  name: 'ComIestAiNotifyServeActivitiesSelector',
  components: { ComServeActivitiesCardForAi, RetweetOutlined },
  props: {
    value: { type: Number, default: undefined },
    payload: { type: Object, default: () => ({}) },
    disabled: { type: Boolean, default: false },
  },
  setup(props, { emit }) {
    const localValue = computed({
      get: () => props.value,
      set: val => {
        emit('update:value', val);
      },
    });

    const localPayload = computed({
      get: () => props.payload,
      set: val => {
        emit('update:payload', val);
      },
    });

    if (!localPayload.value.payload) {
      localPayload.value.payload = {};
    }

    const tableItems = [
      {
        name: '标题',
        type: 'string',
        search: true,
        data_index: 'name',
      },
      // {
      //   name: '标签',
      //   type: 'string',
      //   data_index: 'tag_names',
      // },
    ];

    const tagTableItems = [
      {
        name: '名称',
        type: 'string',
        search: true,
        data_index: 'name',
      },
    ];

    const ransack = computed(() => {
      return {
        tags_id_in: localPayload.value.payload?.serve_tag_ids || [],
      };
    });

    const activityField = ref<any>(null);

    const openField = () => {
      activityField.value?.open();
    };

    onMounted(() => {
      if (!localValue.value) {
        localValue.value = localPayload.value.serve_activity_ids?.[0];
      }
    });

    const onChangeArticle = () => {
      emit('changeArticle');
    };

    return {
      ...toRefs(props),
      localValue,
      tableItems,
      tagTableItems,
      ransack,
      localPayload,
      openField,
      activityField,
      onChangeArticle,
    };
  },
});
export default ComIestAiNotifyServeActivitiesSelector;
</script>

<template lang="pug">
.com-iest-ai-notify-serve-activities-selector
  TaApiField(
    v-model:value='localPayload.serve_activity_ids',
    recordName='文章',
    path='/serve/user/submodules/1/activities',
    display='tag',
    :attrs='["name","tags","content"]',
    :disabled='true',
    :tableItems='tableItems',
    :ransackStr='JSON.stringify(ransack)',
    :displayConfigurableForm='activityForm'
  )
    template(#tags='{records, actions}')
      .wrapper
        .extra.flex.items-center.mb-2.space-x-2.justify-between.mb-2
          .text-sm.text-gray-900.font-semibold.mr-auto(
            class='dark:text-white'
          ) 发送文章
          .actions.space-x-2.flex.items-center
            a-button(
              v-if='localPayload.serve_activity_ids && !disabled',
              type='default',
              @click='onChangeArticle'
            ) 换一批
              template(#icon)
                RetweetOutlined
            a-button(
              v-if='!disabled',
              type='primary',
              @click.stop='openField'
            ) 重新选择

        .space-y-2
          ComServeActivitiesCardForAi(
            v-for='record in records' :record='record',
            :disabled='disabled',
            :checked='localValue === record.id',
            @update:checked='localValue = record.id'
          )

  TaApiNoDisplaySingleField(
    ref='activityField',
    v-model:value='localValue',
    recordName='文章',
    path='/serve/user/submodules/1/activities',
    display='tag',
    :attrs='["name"]',
    :disabled='true',
    :tableItems='tableItems',
    :ransackStr='JSON.stringify(ransack)',
    :displayConfigurableForm='activityForm'
  )
    template(#modal-header-left)
      .flex.items-center
        .text-gray-900.text-sm.mb-2.font-medium 过滤标签：
        TaApiField.field(
          v-if='localPayload.payload',
          v-model:value='localPayload.payload.serve_tag_ids',
          recordName='过滤标签',
          path='/serve/user/submodules/1/tags',
          :multiple='true',
          :tableItems='tagTableItems'
        )
</template>

<style lang="stylus" scoped>
.field
  >>> .display-layout
    flex-direction row !important
    align-items center !important
    .tag-display
      @apply mb-2;
.com-iest-ai-notify-serve-activities-selector
  :deep(.ant-btn)
    display flex
    align-items center
</style>
