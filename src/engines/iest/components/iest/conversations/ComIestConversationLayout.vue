<script lang="ts">
import { defineComponent, toRefs, ref, provide, onBeforeUnmount } from 'vue';
import { VObject } from '../../../../../lib/vails/model/index';
import { VStore } from '../../../../../lib/vails/store/index';
import { IestAiChatUserConversationsApi } from '@/engines/iest/apis/iest/ai/chat/user/conversations.api';
import { useCable } from '@/engines/base/channels/useCable';
import { AuthSessionApi } from '../../../../login/apis/auth/session.api';
import TaAvatar from '../../../../../components/global/ta-component/TaAvatar.vue';
import { useRoute } from 'vue-router';

export const ComChatConversationLayoutCacheCacheKey = 'ComIestConversationLayoutCache';

const ComIestConversationLayout = defineComponent({
  name: 'ComIestConversationLayout',
  components: {},
  props: {
    initCache: {
      type: Object,
      default: () => ({ expended: false, routeBase: '/iest/ai/chat/user/conversations/' }),
    },
  },
  setup(props) {
    const cache = ref<VObject>(props.initCache);

    provide(ComChatConversationLayoutCacheCacheKey, cache);

    const store = new VStore(new IestAiChatUserConversationsApi());

    store.index();

    store.extra.cable_key = 'iest_ai_chat_conversations';
    useCable(store, { callback: { afterCreate: () => store.index() } });

    return {
      ...toRefs(props),
      conversations: store.records,
      cache,
      currentUser: AuthSessionApi.currentUser(),
      title: process.env.VUE_APP_AI_TITLE || 'AI廉说风险预警提醒平台',
    };
  },
});
export default ComIestConversationLayout;
</script>

<template lang="pug">
.com-iest-conversation-layout.w-full.h-full.flex(class='bg-[#f3f4f6] dark:bg-screen drak:text-white')
  .conversation-list.flex-shrink-0.overflow-hidden.transition-all(
    :class='cache.expended ? "w-60" : "w-0"',
  )
    .bg-white.w-full.h-full.flex.flex-col.text-lg.rounded-lg.transform.w-60(
      :class='cache.expended ? "dark:bg-screen-transparent dark:text-white" : "dark:bg-screen-transparent -translate-x-40"',
    )
      .conversation-list-header.p-4
        .header.flex.justify-between.items-center
          router-link(:to='`${cache.routeBase}new`')
            .text-sm.font-bold.flex-grow.text-black.underline.decoration-blue-500.underline-offset-10(
              class='dark:text-white'
            ) {{ title }}
          .cursor-pointer(
            @click='() => cache.expended = !cache.expended',
          )
            TaIcon(type='VerticalRightOutlined', class='!w-5 !h-5')
        router-link(:to='`${cache.routeBase}/new`')
          .conversation-create-btn.flex.items-center.text-orange-500.mt-4.cursor-pointer
            TaIcon.mr-2(type='solid/plus-circle' class='!w-5 !h-5')
            .text-sm.font-semibold 开启新会话
          //-   .mr-2.mb-1
        //-     TaIcon(type='LeftOutlined')

      .conversation-list-body.overflow-y-auto.flex-grow.h-0
        .conversation-list-item.flex.items-center.p-2(v-for='record in conversations')
          router-link(:to='`${cache.routeBase}${record.id}`')
            .conversation-list-item-content.flex-grow.ml-2.text-sm
              .conversation-list-item-title.text-lg.font-bold
              .conversation-list-item-last-message.text-gray-500(
                class='dark:text-white'
              ) {{ record.name || '-' }}
      .user.p-4.pb-0.border-t.border-gray-200(
        class='dark:border-none'
      )
        .flex.mb-4
          .flex.items-center
            TaAvatar(:nameOfUser='currentUser.name')
          .text-sm.ml-2
            .font-bold {{ currentUser.name }}
            .text-xs {{ currentUser.account }}

  .conversation-mini-list.flex-shrink-0.overflow-hidden.transition-all.flex.items-center(
    :class='cache.expended ? "w-0" : "w-fit"',
  )
    .bg-white.w-full.rounded-xl.p-4(
      class='dark:bg-screen-transparent'
    )
      .bg-white.pb-4(
        class='dark:bg-transparent'
        @click='() => cache.expended = !cache.expended',
      )
        .icon.bg-primary.p-2.text-white.font-bold.rounded-xl.px-3.text-base 廉
      .line.h-1px.bg-gray-200.w-full.my-4
      .bg-white.pb-4.mb-4.flex.flex-col.items-center(
        class='dark:bg-transparent'
      )
        router-link(:to='`${cache.routeBase}new`')
          a-tooltip(title='开启新会话', placement='right')
            <svg class="dark:hidden" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
              <path d="M5.26316 19.1654C4.98398 19.1654 4.71624 19.0517 4.51884 18.8495C4.32143 18.6473 4.21053 18.373 4.21053 18.0869V14.8516H2.10526C1.54691 14.8516 1.01143 14.6244 0.616617 14.2199C0.221804 13.8154 0 13.2668 0 12.6948V2.98889C0 2.41686 0.221804 1.86825 0.616617 1.46376C1.01143 1.05927 1.54691 0.832031 2.10526 0.832031H17.8947C18.4531 0.832031 18.9886 1.05927 19.3834 1.46376C19.7782 1.86825 20 2.41686 20 2.98889V12.6948C20 13.2668 19.7782 13.8154 19.3834 14.2199C18.9886 14.6244 18.4531 14.8516 17.8947 14.8516H10.8958L5.92105 18.9292C5.7343 19.0822 5.50227 19.1655 5.26316 19.1654ZM2.10526 2.98889V12.6948H5.26316C5.54233 12.6948 5.81007 12.8084 6.00748 13.0106C6.20489 13.2129 6.31579 13.4872 6.31579 13.7732V15.8427L9.86842 12.931C10.0552 12.778 10.2872 12.6947 10.5263 12.6948H17.8947V2.98889H2.10526Z" fill="#6B7280"/>
              <path d="M5.78947 8.92027C6.37083 8.92027 6.84211 8.43744 6.84211 7.84184C6.84211 7.24623 6.37083 6.7634 5.78947 6.7634C5.20812 6.7634 4.73684 7.24623 4.73684 7.84184C4.73684 8.43744 5.20812 8.92027 5.78947 8.92027Z" fill="#6B7280"/>
              <path d="M10 8.92027C10.5814 8.92027 11.0526 8.43744 11.0526 7.84184C11.0526 7.24623 10.5814 6.7634 10 6.7634C9.41865 6.7634 8.94737 7.24623 8.94737 7.84184C8.94737 8.43744 9.41865 8.92027 10 8.92027Z" fill="#6B7280"/>
              <path d="M14.2105 8.92027C14.7919 8.92027 15.2632 8.43744 15.2632 7.84184C15.2632 7.24623 14.7919 6.7634 14.2105 6.7634C13.6292 6.7634 13.1579 7.24623 13.1579 7.84184C13.1579 8.43744 13.6292 8.92027 14.2105 8.92027Z" fill="#6B7280"/>
            </svg>
            <svg class="hidden dark:block" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
              <path d="M5.26316 19.1654C4.98398 19.1654 4.71624 19.0517 4.51884 18.8495C4.32143 18.6473 4.21053 18.373 4.21053 18.0869V14.8516H2.10526C1.54691 14.8516 1.01143 14.6244 0.616617 14.2199C0.221804 13.8154 0 13.2668 0 12.6948V2.98889C0 2.41686 0.221804 1.86825 0.616617 1.46376C1.01143 1.05927 1.54691 0.832031 2.10526 0.832031H17.8947C18.4531 0.832031 18.9886 1.05927 19.3834 1.46376C19.7782 1.86825 20 2.41686 20 2.98889V12.6948C20 13.2668 19.7782 13.8154 19.3834 14.2199C18.9886 14.6244 18.4531 14.8516 17.8947 14.8516H10.8958L5.92105 18.9292C5.7343 19.0822 5.50227 19.1655 5.26316 19.1654ZM2.10526 2.98889V12.6948H5.26316C5.54233 12.6948 5.81007 12.8084 6.00748 13.0106C6.20489 13.2129 6.31579 13.4872 6.31579 13.7732V15.8427L9.86842 12.931C10.0552 12.778 10.2872 12.6947 10.5263 12.6948H17.8947V2.98889H2.10526Z" fill="url(#paint0_linear_985_23426)"/>
              <path d="M5.78947 8.92027C6.37083 8.92027 6.84211 8.43744 6.84211 7.84184C6.84211 7.24623 6.37083 6.7634 5.78947 6.7634C5.20812 6.7634 4.73684 7.24623 4.73684 7.84184C4.73684 8.43744 5.20812 8.92027 5.78947 8.92027Z" fill="url(#paint1_linear_985_23426)"/>
              <path d="M10 8.92027C10.5814 8.92027 11.0526 8.43744 11.0526 7.84184C11.0526 7.24623 10.5814 6.7634 10 6.7634C9.41865 6.7634 8.94737 7.24623 8.94737 7.84184C8.94737 8.43744 9.41865 8.92027 10 8.92027Z" fill="url(#paint2_linear_985_23426)"/>
              <path d="M14.2105 8.92027C14.7919 8.92027 15.2632 8.43744 15.2632 7.84184C15.2632 7.24623 14.7919 6.7634 14.2105 6.7634C13.6292 6.7634 13.1579 7.24623 13.1579 7.84184C13.1579 8.43744 13.6292 8.92027 14.2105 8.92027Z" fill="url(#paint3_linear_985_23426)"/>
              <defs>
                <linearGradient id="paint0_linear_985_23426" x1="9.7561" y1="2.57806" x2="10.5351" y2="14.5472" gradientUnits="userSpaceOnUse">
                  <stop stop-color="#28D9FF"/>
                  <stop offset="1" stop-color="white"/>
                </linearGradient>
                <linearGradient id="paint1_linear_985_23426" x1="9.7561" y1="2.57806" x2="10.5351" y2="14.5472" gradientUnits="userSpaceOnUse">
                  <stop stop-color="#28D9FF"/>
                  <stop offset="1" stop-color="white"/>
                </linearGradient>
                <linearGradient id="paint2_linear_985_23426" x1="9.7561" y1="2.57806" x2="10.5351" y2="14.5472" gradientUnits="userSpaceOnUse">
                  <stop stop-color="#28D9FF"/>
                  <stop offset="1" stop-color="white"/>
                </linearGradient>
                <linearGradient id="paint3_linear_985_23426" x1="9.7561" y1="2.57806" x2="10.5351" y2="14.5472" gradientUnits="userSpaceOnUse">
                  <stop stop-color="#28D9FF"/>
                  <stop offset="1" stop-color="white"/>
                </linearGradient>
              </defs>
            </svg>
      .bg-white.pb-4.flex.flex-col.items-center.cursor-pointer(
        class='dark:bg-transparent'
        @click.stop='() => cache.expended = !cache.expended',
      )
        a-tooltip(title='打开列表', placement='right')
          <svg class="dark:hidden" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
            <g clip-path="url(#clip0_1040_21803)">
              <path d="M17.1322 1H13.7935C13.6162 0.696959 13.3607 0.445042 13.0529 0.269523C12.7451 0.0940032 12.3956 0.00105394 12.0396 0H7.96557C7.60958 0.00105394 7.26014 0.0940032 6.9523 0.269523C6.64446 0.445042 6.38904 0.696959 6.21168 1H2.87297C2.33272 1 1.81459 1.21071 1.43257 1.58579C1.05055 1.96086 0.835938 2.46957 0.835938 3V18C0.835938 18.5304 1.05055 19.0391 1.43257 19.4142C1.81459 19.7893 2.33272 20 2.87297 20H17.1322C17.6725 20 18.1906 19.7893 18.5726 19.4142C18.9547 19.0391 19.1693 18.5304 19.1693 18V3C19.1693 2.46957 18.9547 1.96086 18.5726 1.58579C18.1906 1.21071 17.6725 1 17.1322 1ZM12.0396 2V4H7.96557V2H12.0396ZM17.1322 18H2.87297V3H5.92853V4C5.6584 4 5.39934 4.10536 5.20833 4.29289C5.01732 4.48043 4.91001 4.73478 4.91001 5C4.91001 5.26522 5.01732 5.51957 5.20833 5.70711C5.39934 5.89464 5.6584 6 5.92853 6H14.0767C14.3468 6 14.6059 5.89464 14.7969 5.70711C14.9879 5.51957 15.0952 5.26522 15.0952 5C15.0952 4.73478 14.9879 4.48043 14.7969 4.29289C14.6059 4.10536 14.3468 4 14.0767 4V3H17.1322V18Z" fill="#6B7280"/>
              <path d="M14.0767 9H8.98409C8.71396 9 8.45489 9.10536 8.26389 9.29289C8.07288 9.48043 7.96557 9.73478 7.96557 10C7.96557 10.2652 8.07288 10.5196 8.26389 10.7071C8.45489 10.8946 8.71396 11 8.98409 11H14.0767C14.3468 11 14.6059 10.8946 14.7969 10.7071C14.9879 10.5196 15.0952 10.2652 15.0952 10C15.0952 9.73478 14.9879 9.48043 14.7969 9.29289C14.6059 9.10536 14.3468 9 14.0767 9Z" fill="#6B7280"/>
              <path d="M14.0767 13H8.98409C8.71396 13 8.45489 13.1054 8.26389 13.2929C8.07288 13.4804 7.96557 13.7348 7.96557 14C7.96557 14.2652 8.07288 14.5196 8.26389 14.7071C8.45489 14.8946 8.71396 15 8.98409 15H14.0767C14.3468 15 14.6059 14.8946 14.7969 14.7071C14.9879 14.5196 15.0952 14.2652 15.0952 14C15.0952 13.7348 14.9879 13.4804 14.7969 13.2929C14.6059 13.1054 14.3468 13 14.0767 13Z" fill="#6B7280"/>
              <path d="M5.92853 11C6.49104 11 6.94705 10.5523 6.94705 10C6.94705 9.44771 6.49104 9 5.92853 9C5.36602 9 4.91001 9.44771 4.91001 10C4.91001 10.5523 5.36602 11 5.92853 11Z" fill="#6B7280"/>
              <path d="M5.92853 15C6.49104 15 6.94705 14.5523 6.94705 14C6.94705 13.4477 6.49104 13 5.92853 13C5.36602 13 4.91001 13.4477 4.91001 14C4.91001 14.5523 5.36602 15 5.92853 15Z" fill="#6B7280"/>
            </g>
            <defs>
              <clipPath id="clip0_1040_21803">
                <rect width="20" height="20" fill="white"/>
              </clipPath>
            </defs>
          </svg>
          <svg class="hidden dark:block" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 20 20" fill="none">
            <g clip-path="url(#clip0_985_23428)">
              <path d="M17.1322 1H13.7935C13.6162 0.696959 13.3607 0.445042 13.0529 0.269523C12.7451 0.0940032 12.3956 0.00105394 12.0396 0H7.96557C7.60958 0.00105394 7.26014 0.0940032 6.9523 0.269523C6.64446 0.445042 6.38904 0.696959 6.21168 1H2.87297C2.33272 1 1.81459 1.21071 1.43257 1.58579C1.05055 1.96086 0.835938 2.46957 0.835938 3V18C0.835938 18.5304 1.05055 19.0391 1.43257 19.4142C1.81459 19.7893 2.33272 20 2.87297 20H17.1322C17.6725 20 18.1906 19.7893 18.5726 19.4142C18.9547 19.0391 19.1693 18.5304 19.1693 18V3C19.1693 2.46957 18.9547 1.96086 18.5726 1.58579C18.1906 1.21071 17.6725 1 17.1322 1ZM12.0396 2V4H7.96557V2H12.0396ZM17.1322 18H2.87297V3H5.92853V4C5.6584 4 5.39934 4.10536 5.20833 4.29289C5.01732 4.48043 4.91001 4.73478 4.91001 5C4.91001 5.26522 5.01732 5.51957 5.20833 5.70711C5.39934 5.89464 5.6584 6 5.92853 6H14.0767C14.3468 6 14.6059 5.89464 14.7969 5.70711C14.9879 5.51957 15.0952 5.26522 15.0952 5C15.0952 4.73478 14.9879 4.48043 14.7969 4.29289C14.6059 4.10536 14.3468 4 14.0767 4V3H17.1322V18Z" fill="url(#paint0_linear_985_23428)"/>
              <path d="M14.0767 9H8.98409C8.71396 9 8.45489 9.10536 8.26389 9.29289C8.07288 9.48043 7.96557 9.73478 7.96557 10C7.96557 10.2652 8.07288 10.5196 8.26389 10.7071C8.45489 10.8946 8.71396 11 8.98409 11H14.0767C14.3468 11 14.6059 10.8946 14.7969 10.7071C14.9879 10.5196 15.0952 10.2652 15.0952 10C15.0952 9.73478 14.9879 9.48043 14.7969 9.29289C14.6059 9.10536 14.3468 9 14.0767 9Z" fill="url(#paint1_linear_985_23428)"/>
              <path d="M14.0767 13H8.98409C8.71396 13 8.45489 13.1054 8.26389 13.2929C8.07288 13.4804 7.96557 13.7348 7.96557 14C7.96557 14.2652 8.07288 14.5196 8.26389 14.7071C8.45489 14.8946 8.71396 15 8.98409 15H14.0767C14.3468 15 14.6059 14.8946 14.7969 14.7071C14.9879 14.5196 15.0952 14.2652 15.0952 14C15.0952 13.7348 14.9879 13.4804 14.7969 13.2929C14.6059 13.1054 14.3468 13 14.0767 13Z" fill="url(#paint2_linear_985_23428)"/>
              <path d="M5.92853 11C6.49104 11 6.94705 10.5523 6.94705 10C6.94705 9.44771 6.49104 9 5.92853 9C5.36602 9 4.91001 9.44771 4.91001 10C4.91001 10.5523 5.36602 11 5.92853 11Z" fill="url(#paint3_linear_985_23428)"/>
              <path d="M5.92853 15C6.49104 15 6.94705 14.5523 6.94705 14C6.94705 13.4477 6.49104 13 5.92853 13C5.36602 13 4.91001 13.4477 4.91001 14C4.91001 14.5523 5.36602 15 5.92853 15Z" fill="url(#paint4_linear_985_23428)"/>
            </g>
            <defs>
              <linearGradient id="paint0_linear_985_23428" x1="9.77903" y1="1.90476" x2="10.7886" y2="14.9391" gradientUnits="userSpaceOnUse">
                <stop stop-color="#28D9FF"/>
                <stop offset="1" stop-color="white"/>
              </linearGradient>
              <linearGradient id="paint1_linear_985_23428" x1="9.77903" y1="1.90476" x2="10.7886" y2="14.9391" gradientUnits="userSpaceOnUse">
                <stop stop-color="#28D9FF"/>
                <stop offset="1" stop-color="white"/>
              </linearGradient>
              <linearGradient id="paint2_linear_985_23428" x1="9.77903" y1="1.90476" x2="10.7886" y2="14.9391" gradientUnits="userSpaceOnUse">
                <stop stop-color="#28D9FF"/>
                <stop offset="1" stop-color="white"/>
              </linearGradient>
              <linearGradient id="paint3_linear_985_23428" x1="9.77903" y1="1.90476" x2="10.7886" y2="14.9391" gradientUnits="userSpaceOnUse">
                <stop stop-color="#28D9FF"/>
                <stop offset="1" stop-color="white"/>
              </linearGradient>
              <linearGradient id="paint4_linear_985_23428" x1="9.77903" y1="1.90476" x2="10.7886" y2="14.9391" gradientUnits="userSpaceOnUse">
                <stop stop-color="#28D9FF"/>
                <stop offset="1" stop-color="white"/>
              </linearGradient>
              <clipPath id="clip0_985_23428">
                <rect width="20" height="20" fill="white"/>
              </clipPath>
            </defs>
          </svg>
      .line.h-1px.bg-gray-200.w-full.my-4
      .bg-white.flex.flex-col.items-center.space-y-4(
        class='dark:bg-transparent'
      )
        TaAvatar(:nameOfUser='currentUser.name')
        //- .cursor-pointer(
        //-   @click='() => cache.expended = !cache.expended',
        //- )
        //-   <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16" fill="none">
        //-     <path d="M11.384 6.52943L5.68307 0.949836C5.28007 0.555932 4.64608 0.576884 4.27009 1.00012C3.89309 1.42336 3.91409 2.0865 4.31709 2.48145L9.95802 8.00133L4.31709 13.5212C3.91409 13.9162 3.89209 14.5793 4.26909 15.0025C4.46608 15.2236 4.73208 15.3346 5.00008 15.3346C5.24507 15.3346 5.49007 15.2414 5.68307 15.0528L11.384 9.47323C11.781 9.08352 12 8.56181 12 8.00133C12 7.44086 11.781 6.91915 11.384 6.52943Z" fill="#6B7280"/>
        //-   </svg>


  .router-view.flex-grow.w-0
    router-view(:key='$route.fullPath')
</template>

<style lang="stylus" scoped>
.dark .com-iest-conversation-layout
  .icon, .line
    border-radius var(--rounded-lg, 0.5rem)
    background linear-gradient(90deg, #00E1FF 0%, #2A5BFD 100%)
.w-fit
  width fit-content
</style>
