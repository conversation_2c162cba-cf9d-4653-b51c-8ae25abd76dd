<script lang="ts">
import {
  defineComponent,
  toRefs,
  ref,
  inject,
  Ref,
  onMounted,
  computed,
  defineAsyncComponent,
} from 'vue';
import { ComChatConversationLayoutCacheCacheKey } from './ComIestConversationLayout.vue';
import { VObject } from '../../../../../lib/vails/model/index';
import { useConversationMessage } from './useConversationMessage';
import ComIestConversationContainer from './ComIestConversationContainer.vue';
import { IestMessage, IestMentionType, IestMention } from '../../../types/types';
import { useRouter } from 'vue-router';

export interface IestMentionAction {
  key: string;
  name: string;
  type: string; // primary
  callback: () => void;
  confirm?: boolean;
}

const ComIestConversationsShow = defineComponent({
  name: 'ComIestConversationsShow',
  components: {
    ComIestConversationContainer,
  },
  props: {
    store: { type: Object, required: true },
    messageStore: { type: Object, required: true },
    mentionStore: { type: Object, required: true },
    mentionVersionStore: { type: Object, required: true },
  },
  emits: [],
  setup(props, { emit }) {
    const cache = inject<Ref<VObject>>(ComChatConversationLayoutCacheCacheKey)!;
    const loading = ref(false);

    const { responseToMessage } = useConversationMessage(props.messageStore, loading);

    onMounted(async () => {
      if (cache.value.newMessage) {
        await responseToMessage(cache.value.newMessage);
        await showMentionIfExists();
        // 获取更新后的名称
        props.store.record.value.fetch();
        cache.value.newMessage = '';
      }
    });

    const onCreate = async (content: string) => {
      if (loading.value) return;
      await responseToMessage(content);
      await showMentionIfExists();
      loading.value = false;
    };

    const showMentionIfExists = async () => {
      const lastMessage =
        props.messageStore.records.value[props.messageStore.records.value.length - 1];

      if (lastMessage?.mentions?.length > 0) {
        await onClickMention(lastMessage.mentions[0], lastMessage);
      }
    };

    const activeMessage = ref<IestMessage | null>(null);

    const onClickMention = async (mention: IestMention, message: IestMessage | null) => {
      await props.mentionStore.find(mention.id);

      activeMessage.value = message;

      if (message) {
        const matchVersionIndex = versions.value.findIndex(
          (version: VObject) => version.message_id === message.id,
        );

        if (matchVersionIndex !== -1 && matchVersionIndex !== versions.value.length - 1) {
          await onClickVersion(versions.value[matchVersionIndex]);
        } else if (matchVersionIndex === versions.value.length - 1) {
          // 最后一个版本
          visibleVersion.value = false;
        }
      } else {
        visibleVersion.value = false;
      }

      visibleMentionList.value = false;
    };

    const mentionTypeComponent = computed(() => {
      if (props.mentionStore.record.value.type === IestMentionType.ServePack) {
        return defineAsyncComponent(() => import('./../mentions/ComIestMentionServePackShow.vue'));
      } else if (props.mentionStore.record.value.type === IestMentionType.SearchBpmInstance) {
        return defineAsyncComponent(
          () => import('./../mentions/ComIestMentionSearchBpmInstanceIndex.vue'),
        );
      }
      return '';
    });

    const versions = computed(() => props.mentionStore.record.value.mention_versions || []);

    const visibleVersion = ref(false);

    const onClickVersion = async (version: IestMention) => {
      await props.mentionVersionStore.find(version.id);
      visibleVersion.value = true;
    };

    const onCloseMention = () => {
      const record = props.mentionStore.record;
      record.value = {};
    };

    const router = useRouter();

    const onAfterDelete = () => {
      router.replace('/iest/ai/chat/user/conversations/new');
    };

    const visibleMentionList = ref(false);

    const onShowMentionList = () => {
      visibleMentionList.value = true;
    };

    const onCloseMentionList = () => {
      visibleMentionList.value = false;
      visibleVersion.value = false;
      onCloseMention();
    };

    const mentionWidth = computed(() => {
      if (cache.value.mentionWidth) {
        return `${cache.value.mentionWidth}`;
      }
      return '340px';
    });

    return {
      ...toRefs(props),
      cache,
      record: props.store.record,
      messages: props.messageStore.records,
      onCreate,
      loading,
      onClickMention,
      mention: props.mentionStore.record,
      mentions: props.mentionStore.records,
      mentionTypeComponent,
      activeMessage,
      versions,
      visibleVersion,
      onClickVersion,
      mentionVersion: props.mentionVersionStore.record,
      onCloseMention,
      onAfterDelete,
      visibleMentionList,
      onShowMentionList,
      onCloseMentionList,
      mentionWidth,
    };
  },
});
export default ComIestConversationsShow;
</script>

<template lang="pug">
.com-iest-ai-chat-user-conversations-show.w-full.h-full.flex.flex-col
  .flex.justify-center.w-full.h-0.flex-grow.flex
    //- :class='mention?.id ? "-translate-x-0" : ""'
    .transform.flex-grow.w-0.transition-all.flex.justify-center.py-4
      ComIestConversationContainer.h-full.transition-all.transform.min-w-343px.max-w-780px(
        v-model:messages='messages',
        :record='record',
        :class='mention?.id ? "w-full" : "w-3/4"',
        :loading='loading',
        @confirm='onCreate'
        @clickMention='onClickMention'
        @afterDelete='onAfterDelete',
      )


    .h-full.flex.flex-col.relative
      .actions.flex.items-center.justify-space.px-4.pt-4.justify-end
        .w-5.h-5.cursor-pointer.flex.items-center.justify-center.rounded(
          :class='visibleMentionList ? "bg-gray-300" : "hover:bg-gray-300"',
          @click.stop='() => visibleMentionList ? onCloseMentionList() : onShowMentionList()',
        )
          TaIcon(type='AppstoreFilled')
      .content.h-full.flex.flex-col.transition-all.ease-linear.overflow-hidden.transform(
        :style='{ width: mention?.id || visibleMentionList ? mentionWidth : "0" }',
      )
        transition-group(name='list')
          .mention-list.h-full.flex.flex-col.p-4.w-310px(v-if='visibleMentionList')
            .bg-white.rounded-xl.overflow-hidden.w-full.h-full.flex.flex-col(
              class='dark:bg-screen-transparent dark:text-white',
            )
              header.flex.justify-between.items-center.p-4.border-b.border-gray-200(
                class='dark:border-none'
              )
                .text-base.text-gray-800.font-semibold(class='dark:text-white') 关联内容
                TaIcon.cursor-pointer(type='outline/x' class='!w-5 !h-5' @click.stop='onCloseMentionList')
              .flex-grow.h-0.p-4
                .rounded.bg-gray-100.flex.overflow-hidden.cursor-pointer.mb-2(
                  class='dark:bg-primary-900'
                  v-for='record in mentions',
                  @click='onClickMention(record, null)',
                )
                  .bg-gray-200.p-2(class='dark:bg-primary-700') {{ '</>' }}
                  .p-2 {{ record.name }}
          .mention-show.transform.transition-all.overflow-hidden.h-0.flex-grow(
            v-if='mention?.id && !visibleMentionList',
            class='dark:text-white'
          )
            .p-4.flex.flex-col.h-full(
              :style='{ width: mentionWidth }'
            )
              header.bg-white.rounded-t-xl.flex.justify-between.items-center.p-4.border-b.border-gray-200(
                class='dark:bg-primary-900 dark:border-none'
              )
                TaIcon.cursor-pointer(type='outline/arrow-left' class='!w-5 !h-5' @click.stop='() => visibleMentionList = true')
                .text-base.text-gray-800.font-semibold(
                  class='dark:text-white'
                ) {{ mention.name }}详情
                TaIcon.cursor-pointer(type='outline/x' class='!w-5 !h-5' @click.stop='onCloseMention')

              .w-full.bg-white.h-full.flex.flex-col(class='dark:bg-screen-transparent')
                component(
                  v-if='mention?.id && !visibleVersion && !visibleMentionList'
                  v-model:record='mention'
                  :is='mentionTypeComponent',
                )

                component.w-full.h-0.flex-grow(
                  v-else-if='mention?.id && visibleVersion && !visibleMentionList'
                  :disabled='true',
                  :record='mentionVersion'
                  :is='mentionTypeComponent',
                  @close='onCloseMention',
                )

              .footer.flex.rounded-b-xl.justify-end.items-center.bg-white.px-4.py-2.border-t.border-gary-200(
                v-if='cache.mentionActions?.length > 0 || versions.length > 1'
                class='dark:bg-primary-900 dark:border-none',
              )
                .versions.w-0.flex-grow.flex.items-center.overflow-x-auto
                  a-tag.cursor-pointer(
                    v-for='(version, index) in versions.slice(0, versions.length - 1)',
                    :key='version.id',
                    :color='visibleVersion && version.id === mentionVersion?.id ? "blue" : undefined',
                    @click.stop='onClickVersion(version)'
                  )
                    | 版本{{ index + 1 }}
                  a-tag.cursor-pointer(
                    :color='visibleVersion ? undefined : "blue"',
                    @click.stop='visibleVersion = false'
                  )
                    | 当前版本
                .actions.flex.space-x-2
                template(v-for='action in cache.mentionActions')
                  TaPopoverConfirm(
                    v-if='action.confirm',
                    :key='action.key',
                    :title='`确定${action.name}吗？`',
                    @click.stop='',
                    @confirm='action.callback'
                  )
                    a-button(:type='action.type') {{ action.name }}
                  a-button(
                    v-else,
                    :key='action.key',
                    :type='action.type',
                    @click='action.callback',
                  ) {{ action.name }}

          //- TaPopoverConfirm(
          //-   tips='您确认发送改消息么？',
          //-   @confirm='onConfirm',
          //- )
          //-   a-button(type='primary') 确认发送



    //- TaNoPaddingModal(
  //-   v-model:visible='visibleVersion',
  //-   :footer='null',
  //-   :title='`历史版本`',
  //- )
  //-   component.w-full.h-full(
  //-     v-if='mention?.id'
  //-     :disabled='true',
  //-     :record='mentionVersion'
  //-     :is='mentionTypeComponent',
  //-   )
</template>

<style lang="stylus" scoped>
.com-iest-ai-chat-user-conversations-show
  height 100%

.list-enter-active,
.list-leave-active {
  transition: all 0.5s ease-in-out;
}
.list-enter-from,
.list-leave-to {
  opacity: 0;
  transform: translateX(300px);
}
</style>
