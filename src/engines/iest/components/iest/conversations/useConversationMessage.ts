import { IestMessageType } from '@/engines/iest/types/types';
import { message } from 'ant-design-vue';
import { nextTick, Ref } from 'vue';

let seq = 0;

export const useConversationMessage = (store: any, loading: Ref<boolean>) => {
  const loadingMessage = store.new({
    id: 'loading',
    content: 'AI处理中...',
    type: IestMessageType.System,
  });

  const responseToMessage = async (content: string) => {
    if (loading.value) return;

    loading.value = true;

    try {
      const userMessage = store.new({
        content,
        type: IestMessageType.User,
      });

      await userMessage.save({
        params: {
          limit_identifier: seq++,
        },
      });

      store.records.value.push(loadingMessage);

      const systemMessage = store.new({
        previous_content: content,
        type: IestMessageType.System,
      });

      await systemMessage.save({
        params: {
          limit_identifier: seq++,
        },
      });
      nextTick(() => {
        store.records.value.splice(store.records.value.length - 2, 1);
      });
    } catch (error) {
      console.error(error);
      message.error('发送消息失败');
    }
    loading.value = false;
  };

  return {
    responseToMessage,
  };
};
