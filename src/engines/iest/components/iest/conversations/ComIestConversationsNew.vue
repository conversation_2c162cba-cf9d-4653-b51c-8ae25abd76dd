<script lang="ts">
import { message } from 'ant-design-vue';
import { defineComponent, inject, ref, Ref, toRefs } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { VObject } from '../../../../../lib/vails/model/index';
import { AuthSessionApi } from '../../../../login/apis/auth/session.api';
import ComIestConversationInput from './ComIestConversationInput.vue';
import { ComChatConversationLayoutCacheCacheKey } from './ComIestConversationLayout.vue';
import ComIestConversationTag from './ComIestConversationTag.vue';

const ComIestConversationsNew = defineComponent({
  name: 'ComIestConversationsNew',
  components: { ComIestConversationInput, ComIestConversationTag },
  props: {
    store: { type: Object, required: true },
  },
  emits: [],
  setup(props, { emit }) {
    const AI_BG_URL = 'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iest/aibg.webp';
    const loading = ref(false);
    const route = useRoute();
    const router = useRouter();
    const suggestionType = String(route.query.type);

    const suggestionOptionMap: VObject = {
      search_user: {
        placeholder: '请告诉我您要搜索的用户~',
        suggestions: [{ name: '一把手' }, { name: '年轻干部' }],
      },
      search_serve_activity: {
        placeholder: '请告诉我您要搜索的文章~',
        suggestions: [{ name: '国庆相关' }, { name: '中秋相关' }],
      },
      create_serve_pack: {
        placeholder: '请告诉我您要发送的消息~',
        suggestions: [
          { name: '国庆前一天给年轻干部发一条国庆廉洁提醒' },
          { name: '明天给张三发一条招投标廉洁提醒' },
          { name: '我有多少待办' },
          { name: '库里有多少人员' },
          { name: '消息统计' },
          { name: '规则配置情况' },
          { name: '请告诉我现在有多少素材' },
        ],
      },
    };

    const suggestionOptions =
      suggestionOptionMap[suggestionType] || suggestionOptionMap.create_serve_pack;

    const value = ref('');
    const cache = inject<Ref<VObject>>(ComChatConversationLayoutCacheCacheKey);

    const onCreate = (content: string) => {
      if (loading.value) return;

      loading.value = true;
      const record = props.store.new({});

      record
        .save()
        .then(() => {
          cache!.value.newMessage = content;
          router.push(`${cache?.value?.routeBase}${record.id}`);
        })
        .catch(() => {
          message.error('创建会话失败');
        })
        .finally(() => {
          loading.value = false;
        });
    };

    let timeDesc = '上午';

    switch (true) {
      case new Date().getHours() >= 12 && new Date().getHours() < 18:
        timeDesc = '下午';
        break;
      case new Date().getHours() >= 18:
        timeDesc = '晚上';
        break;
    }

    return {
      ...toRefs(props),
      value,
      onCreate,
      currentUser: AuthSessionApi.currentUser(),
      timeDesc,
      suggestionOptions,
      loading,
      AI_BG_URL,
    };
  },
});
export default ComIestConversationsNew;
</script>

<template lang="pug">
.com-iest-ai-chat-user-conversations-new.w-full.h-full.flex.flex-col.items-center
  .flex.flex-col.items-center.space-y-6.h-full.pt-13(class='w-2/3')
    .w-72.h-72.relative.mb-6
      img.logo.w-full.h-full.object-cover(:src='AI_BG_URL')
      .absolute.text-2xl.font-semibold.text-black.bottom-0.left-0.right-0.mx-auto.underline.underline-offset-16.decoration-blue-500.whitespace-nowrap(
        class='dark:text-white'
      ) 滨江区廉洁教育智能工具箱AI
    //- .logo.text-4xl.text-primary.font-bold.mb-4
    //-   | {{ timeDesc }}好，{{ currentUser.name }}
    ComIestConversationInput.h-fit.w-full(
      v-model:value='value',
      :loading='loading',
      :minRows='4',
      :maxRows='5',
      :isSuffix='true',
      :placeholder='suggestionOptions.placeholder',
      @confirm='onCreate'
    )
    .suggestions.w-full.flex.justify-center
      .flex.flex-wrap
        ComIestConversationTag.mr-2.mb-2.cursor-pointer.px-10px.py-2.bg-white.rounded-lg(
          v-for='suggestion in suggestionOptions.suggestions',
          :value='suggestion.name',
          color='blue',
          @click.stop='onCreate(suggestion.name)'
        )
          | {{ suggestion.name }}
</template>

<style lang="stylus" scoped>
.com-iest-ai-chat-user-conversations-new
  height 100%
  .h-fit
    height fit-content
</style>
