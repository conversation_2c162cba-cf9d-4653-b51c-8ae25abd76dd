<script lang="ts">
import { ref, defineComponent, toRefs, computed, watch } from 'vue';
import { message } from 'ant-design-vue';

import { DirectiveBinding } from 'vue';

const enterHandler = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    const textarea = el.querySelector('textarea') || el;

    if (!textarea) return;

    textarea.addEventListener('keydown', (e: KeyboardEvent) => {
      if (e.key === 'Enter') {
        if (e.shiftKey) {
          // Shift+Enter: 允许换行，不做任何特殊处理
          return;
        } else {
          // 仅 Enter: 阻止默认行为并触发自定义处理函数
          console.log('//////');

          e.preventDefault();
          if (typeof binding.value === 'function') {
            binding.value(e);
          }
        }
      }
    });
  },
};

const ComIestConversationInput = defineComponent({
  name: 'ComIestConversationInput',
  components: {},
  directives: { enterHandler },
  props: {
    value: { type: String, default: '' },
    loading: { type: Boolean, default: false },
    placeholder: { type: String, default: '请输入内容' },
    minRows: { type: Number, default: 1 },
    maxRows: { type: Number, default: 4 },
    isSuffix: { type: Boolean, default: false },
  },
  emits: ['update:value', 'confirm'],
  setup(props, { emit }) {
    const localValue = computed({
      get: () => props.value,
      set: value => emit('update:value', value),
    });

    const onConfirm = () => {
      if (!localValue.value) {
        message.warning('请输入内容');
        return;
      }
      emit('confirm', localValue.value);
      localValue.value = '';
    };

    const showIcon = ref(true);

    watch(
      () => props.loading,
      () => {
        if (!props.loading) {
          setTimeout(() => {
            showIcon.value = true;
          }, 500);
        } else {
          showIcon.value = false;
        }
      },
    );

    return {
      ...toRefs(props),
      localValue,
      showIcon,
      onConfirm,
    };
  },
});
export default ComIestConversationInput;
</script>

<template lang="pug">
.com-iest-conversation-input.mp-12.h-full.flex.space-x-2.relative
  a-textarea(
    v-model:value='localValue',
    :autoSize='{ minRows, maxRows }',
    :placeholder='placeholder',
    size='large'
    v-enter-handler='onConfirm'
    allow-clear
  )
  a-button(
    v-if='!isSuffix'
    :disabled='loading',
    style='height: 100%',
    class='!text-blue-500'
    type='text'
    @click='onConfirm'
  )
    .flex.justify-center.items-center
      TaIcon(v-if='!loading && showIcon', type='SendOutlined', size='1rem')
      TaIcon(v-else, type='LoadingOutlined', size='1rem')
  a-button.right-2.bottom-2.z-99(
    v-else
    type='text'
    :disabled='loading',
    class='!absolute !text-blue-500'
    @click='onConfirm'
  )
    .flex.justify-center.items-center.w-full.h-full
      TaIcon(v-if='!loading && showIcon', type='SendOutlined', size='1rem')
      TaIcon(v-else, type='LoadingOutlined', size='1rem')

</template>

<style lang="stylus" scoped>
.dark .com-iest-conversation-input
  >>> textarea
    @apply border-none bg-primary-900 rounded-lg text-white;
  >>> .ant-btn
    @apply bg-primary-800;
// .com-iest-conversation-input
</style>
