<script lang="ts">
import { ref, defineComponent, toRefs, computed, watch, nextTick, onMounted } from 'vue';
import { message } from 'ant-design-vue';
import scrollIntoView from 'smooth-scroll-into-view-if-needed';
import ComIestMessageCard from '../messages/ComIestMessageCard.vue';
import ComIestConversationInput from './ComIestConversationInput.vue';
import { IestMention, IestMessage } from '../../../types/types';
import { VObject } from '../../../../../lib/vails/model/index';

const ComIestConversationContainer = defineComponent({
  name: 'ComIestConversationContainer',
  components: {
    ComIestMessageCard,
    ComIestConversationInput,
  },
  props: {
    messages: { type: Array, default: () => [] },
    record: { type: Object, default: () => ({}) },
    loading: { type: <PERSON>olean, default: false },
  },
  emits: ['update:messages', 'confirm', 'clickMention', 'afterDelete'],
  setup(props, { emit }) {
    const localMessages = computed({
      get: () => props.messages,
      set: value => emit('update:messages', value),
    });

    const value = ref('');

    const onConfirm = () => {
      if (!value.value) {
        message.warning('请输入内容');
        return;
      }
      emit('confirm', value.value);
      value.value = '';
    };

    const chatBottomAnchor = ref<any>(null);
    const showIcon = ref(true);

    onMounted(() => {
      setTimeout(() => {
        scrollIntoView(chatBottomAnchor.value);
      }, 500);
    });

    watch(
      () => props.loading,
      () => {
        // if (!props.loading) {
        nextTick(() => {
          scrollIntoView(chatBottomAnchor.value);
        });
        // }

        if (!props.loading) {
          setTimeout(() => {
            showIcon.value = true;
          }, 500);
        } else {
          showIcon.value = false;
        }
      },
    );

    watch(
      () => localMessages.value.length,
      () => {
        nextTick(() => {
          scrollIntoView(chatBottomAnchor.value);
        });
      },
    );

    const onClickedMention = (mention: IestMention, message: IestMessage) => {
      emit('clickMention', mention, message);
    };

    const visibleEditName = ref(false);
    const formData = ref<VObject>({});
    const operMap = {
      edit: {
        label: '修改名称',
        icon: 'EditOutlined',
        color: 'rgba(107, 114, 128, 1)',
        action: () => {
          formData.value.name = props.record.name;
          visibleEditName.value = true;
        },
      },
      delete: {
        label: '删除会话',
        icon: 'DeleteOutlined',
        color: 'rgba(224, 36, 36, 1)',
        action: () => {
          props.record
            .delete()
            .then(() => {
              emit('afterDelete');
              message.success('删除成功');
            })
            .catch(() => {
              message.error('删除失败');
            });
        },
      },
    };

    const loadingChangeName = ref(false);
    const onSave = () => {
      if (!formData.value.name) {
        message.warning('请输入名称');
        return;
      }

      loadingChangeName.value = true;
      props.record
        .update({
          name: formData.value.name,
        })
        .then(() => {
          visibleEditName.value = false;
          message.success('修改成功');
          // props.record.name = formData.value.name;
        })
        .catch(() => {
          message.error('修改失败');
        })
        .finally(() => {
          loadingChangeName.value = false;
        });
    };

    return {
      ...toRefs(props),
      localMessages,
      chatBottomAnchor,
      // localValue,
      value,
      showIcon,
      onConfirm,
      onClickedMention,
      operMap,
      visibleEditName,
      formData,
      onSave,
      loadingChangeName,
    };
  },
});
export default ComIestConversationContainer;
</script>

<template lang="pug">
.com-iest-ai-notify-chat-container.mp-12.h-full.flex.flex-col
  .flex.justify-center.items-center(class='dark:text-white')
    a-dropdown(trigger='click' placement='bottomRight')
      .title__wrapper.flex.items-center.px-2.py-1.rounded.cursor-pointer(
        class='hover:bg-gray-200 dark:hover:bg-primary-900'
        style='width:fit-content'
      )
        .name.flex.items-center.space-x-2
          .flex.items-center
            TaIcon(type='MessageOutlined')
          .text-sm.text-gray-700.pr-1(
            class='dark:text-white'
          ) {{ record.name || '-' }}

        TaIcon(type='DownOutlined' :size='12')
      template(#overlay)
        a-menu
          a-menu-item(v-for='(oper,key) in operMap' :key='key')
            .flex.items-center(
              :style='`color:${oper.color}`',v-if='key !== "delete"'
              @click='oper.action'
            )
              TaIcon.mr-3(
                :type='oper.icon',
                :size='14'
              )
              .text-sm.font-normal {{oper.label}}
            TaPopoverConfirm(
              v-else
              title='确定删除吗？',
              okText='确定',
              cancelText='取消',
              @click.stop='',
              @confirm='operMap["delete"].action()'
            )
              .flex.items-center(:style='`color:${oper.color}`')
                TaIcon.mr-3(
                  :type='oper.icon',
                  :size='14'
                )
                .text-sm.font-normal {{oper.label}}
  a-modal(
    v-model:visible='visibleEditName',
    title='修改名称',
    :confirmLoading='loadingChangeName',
    @ok='onSave',
  )
    .flex.items-center.justify-center
      a-input(v-model:value='formData.name', :maxLength='20', :autoFoucs='true')

  .container__bg.flex-grow.h-0.rounded-lg.flex-col.flex.py-4(
    class='bg-[#f3f4f6] dark:bg-transparent'
  )
    .space-y-4.flex-grow.h-0.overflow-y-auto.overflow-x-hidden.mb-4.flex-col.flex.px-4
      transition-group(name='list')
        ComIestMessageCard(
          v-for='message in localMessages',
          :record='message',
          :key='message.id',
          @clickMention='onClickedMention',
        )
        .chat-bottom-anchor(ref='chatBottomAnchor')

    ComIestConversationInput.flex-grow-0.h-fit(
      v-model:value='value',
      @confirm='onConfirm',
    )

</template>

<style lang="stylus" scoped>
.list-enter-active,
.list-leave-active {
  transition: all 0.5s ease;
}
.list-enter-from,
.list-leave-to {
  opacity: 0;
  transform: translateX(30px);
}

.h-fit
  height fit-content
.com-iest-ai-notify-chat-container
  // .container__bg
  //   background-color #f3f4f6
</style>
