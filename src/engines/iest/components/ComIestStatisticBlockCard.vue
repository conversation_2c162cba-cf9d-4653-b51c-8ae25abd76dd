<script lang='ts'>
import { ref, defineComponent, toRefs } from 'vue';
import useColor from '../../../components/global/ta-component/ta-template-form-core/useColor';
const ComIestStatisticBlockCard = defineComponent({
  name: 'ComIestStatisticBlockCard',
  components: {},
  props: {
    value: { type: [String, Number], default: '0' },
    label: { type: [String, Number], default: '总数' },
    flexDirection: { type: String, default: 'column' },
    color: { type: String, default: 'yellow' },
    icon: { type: String, default: 'file-circle-plus' },
    defaultTextColorWeight: { type: Number, default: 700 },
    defaultBgWeight: { type: Number, default: 100 },
  },
  setup(props) {
    const { computedColor, computedBg } = useColor(
      props,
      props.defaultTextColorWeight,
      props.defaultBgWeight,
    );
    return {
      ...toRefs(props),
      computedColor,
      computedBg,
    };
  },
});
export default ComIestStatisticBlockCard;
</script>

<template lang="pug">
.com-iest-statistic-block-card.iest-card-container.flex.items-center(class='!p-4')
  .w-12.h-12.rounded-lg.p-3.flex.items-center.justify-center.mr-4.flex-shrink-0(:style='`background-color:${computedBg}`')
    TaIcon(
      class='!w-6 !h-6'
      :type='`flowbite-v2-solid/${icon}`'
      :style='`color:${computedColor}`'
    )
  .right
    .text-2xl.text-gray-900.font-bold(class='mb-0.5') {{ value }}
    .text-base.text-gray-500 {{ label }}
</template>

<style lang="stylus" scoped></style>
