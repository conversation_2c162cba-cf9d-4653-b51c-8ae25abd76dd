<script lang='ts'>
import { ref, defineComponent, toRefs, PropType, computed } from 'vue';
const ComIestIndexModeSwitch = defineComponent({
  name: 'ComIestIndexModeSwitch',
  components: {},
  props: {
    indexMode: {
      type: String as PropType<'table' | 'list'>,
      default: 'table',
    }
  },
  setup(props, { emit }) {
    const localMode = computed({
      get() {
        return props.indexMode
      }, set(val) {
        emit('update:indexMode', val)
      }
    })
    return {
      ...toRefs(props),
      localMode
    };
  },
});
export default ComIestIndexModeSwitch;
</script>

<template lang="pug">
.com-iest-indexmode-switch.flex.items-center.cursor-pointer.text-gray-500
  .w-7.h-7.rounded.flex.items-center.justify-center.mr-1(
    :class='{"active-mode": localMode === "table"}',
    @click='localMode = "table"'
  )
    TaIcon(type='outline/menu' class='!w-5 !h-5')
  .w-7.h-7.rounded.flex.items-center.justify-center(
    :class='{"active-mode": localMode === "list"}',
    @click='localMode = "list"'
  )
    TaIcon(type='outline/view-grid' class='!w-5 !h-5')
</template>

<style lang="stylus" scoped>
.com-iest-indexmode-switch
  .active-mode
    @apply bg-gray-100 text-gray-900;

</style>
