<script lang='ts'>
import { VObject, VStore } from '@/lib/vails';
import { ref, defineComponent, toRefs, computed } from 'vue';
import '@/engines/iest/components/global/screen/screen_table.styl';
import { useContextInject } from '@/engines/bpm/bpm-core/ta-template-form-core/useContext';
import { usePreviewClickable } from '@/components/global/ta-component/TaBuilder/builder/usePreview';
import { merge } from 'lodash';
import { ServeManageActivitiesApi } from '@/engines/iest/apis/serve/manage/activities.api';
import { ServeActivityModel } from '@/engines/iest/models/serve/activities';
import ComServeRelativedTags from '../../serve/tags/ComServeRelativedTags.vue';

const ComIestScreenActivityIndexInModal = defineComponent({
  name: 'ComIestScreenActivityIndexInModal',
  components: {
    ComServeRelativedTags
  },
  setup(props) {
    const store = new VStore(new ServeManageActivitiesApi({
      parents: [{ type: 'submodules', id: 1 }]
    }), ServeActivityModel)
    const { context } = useContextInject();
    const params = computed(() => context._form.params)
    const config = computed(() => ({
      store,
      template: 'serve_activity',
      actions: [
        { key: 'create', enabled: false },
        { key: 'update', enabled: false },
        { key: 'delete', enabled: false },
      ],
      params: {
        q: merge({ state_eq: 'published', s: ['created_at desc'] }, params.value),
      },
      mode: 'table',
      table: {
        scroll: { y: "auto" }
      }
    }))

    const { onClick } = usePreviewClickable(props)


    const openActivityShowScreenModal = (record: VObject) => {
      const event = { mode: 'page_setting' } as { mode: 'page_setting' }
      const eventExtra = {
        page_setting_id: 16
      }
      onClick(
        event,
        {
          params: {
            activityId: record.id
          }
        },
        eventExtra
      )
    }
    return {
      ...toRefs(props),
      config,
      openActivityShowScreenModal,
    };
  },
});
export default ComIestScreenActivityIndexInModal;
</script>

<template lang="pug">
.com-iest-screen-rule-index-in-modal.iest__screen__table
  TaIndexView.ta-index-view-skin(:config='config' :showHeader='false',@onShow='openActivityShowScreenModal')
    template(#bodyCell='{record,column,text}')
      .text-sm(v-if='column.dataIndex[0] === "orgs"') {{ record.orgs?.map(org => org.name).join(',') }}
      ComServeRelativedTags(
          v-else-if='column.dataIndex[0] === "tag_ids"'
          :tags='record.tags'
        )
</template>

<style lang="stylus" scoped></style>
