<script lang="ts">
import { usePreviewClickable } from '@/components/global/ta-component/TaBuilder/builder/usePreview';
import { useContextInject } from '@/engines/bpm/bpm-core/ta-template-form-core/useContext';
import '@/engines/iest/components/global/screen/screen_table.styl';
import { ServeMessageModel } from '@/engines/iest/serve-core/models/serve/manage/messages';
import { VObject, VStore } from '@/lib/vails';
import { computed, defineComponent, toRefs } from 'vue';
import { ServeManageMessagesApi } from '../../../serve-core/apis/serve/manage/messages.api';
import ComColorfulLabel from '../ComColorfulLabel.vue';

const ComIestScreenMsgIndexInModal = defineComponent({
  name: 'ComIestScreenMsgIndexInModal',
  components: { ComColorfulLabel },
  props: {},
  setup(props) {
    const store = new VStore(new ServeManageMessagesApi(), ServeMessageModel);
    const { context } = useContextInject();
    const params = computed(() => context._form?.params);

    const config = computed(() => ({
      store,
      template: 'serve_message',
      mode: 'table',
      params: {
        q: params.value,
      },
      table: {
        scroll: { y: 'auto' },
      },
    }));

    const { onClick } = usePreviewClickable(props);
    const openBidProjectScreenModal = (record: VObject) => {
      const event = { mode: 'page_setting' } as { mode: 'page_setting' };
      const eventExtra = {
        page_setting_id: 14,
      };
      onClick(
        event,
        {
          params: {
            messageId: record.id,
          },
        },
        eventExtra,
      );
    };
    return {
      ...toRefs(props),
      config,
      openBidProjectScreenModal,
    };
  },
});
export default ComIestScreenMsgIndexInModal;
</script>

<template lang="pug">
.com-iest-screen-msg-index-in-modal.iest__screen__table
  TaIndexView.ta-index-view-skin(
    :config="config" :showHeader='false'
    @onShow='openBidProjectScreenModal'
  )
    template(#bodyCell='{ record, column, text }')
      .flex.w-full.justify-center(v-if='column.dataIndex[0] === "is_read"')
        span {{ record.is_read ? "已读" : "未读" }}
</template>

<style lang="stylus" scoped></style>
