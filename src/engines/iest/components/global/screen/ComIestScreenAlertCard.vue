<script lang='ts'>
import { ref, defineComponent, toRefs } from 'vue';
import ComColorfulLabel from '@/engines/iest/components/global/ComColorfulLabel.vue';

const ComIestScreenAlertCard = defineComponent({
  name: 'ComIestScreenAlertCard',
  components: { ComColorfulLabel },
  props: {
    record: { type: Object, default: () => ({}) },
    stateColor: { type: String, default: '' },
    stateLabel: { type: String, default: '' },
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
export default ComIestScreenAlertCard;
</script>

<template lang="pug">
.com-iest-screen-alert-card.rounded-lg.p-3
  .key.mb-2 创建人
    span.value.pl-2 {{ record?.user?.name }}
  .grid.grid-cols-3.mb-2
    .item
      .key.mb-1 区域
      .value {{ record?.org?.name }}
    .item
      .key.mb-1 风险数量
      .value {{ record?.paperwork_result_count }}
    .item
      .key.mb-1 状态
      ComColorfulLabel.px-10px.py-2px.text-sm(
        :label='stateLabel'
        :color='stateColor'
        :defaultTextColorWeight='400',
        :defaultBgWeight='900'
      )
  .bg-primary-900.rounded.shadow.p-2.flex.items-center
    .w-5.h-5.mr-1.bg-cover.flex-shrink-0(style='background-image:url(https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iest/filefile.png)')
    .value.flex-grow.truncate {{record?.attachment?.files?.[0]?.fileName}}
</template>

<style lang="stylus" scoped>
.com-iest-screen-alert-card
  background #09183F
  .key
    @apply text-sm text-primary-300;
  .value
    @apply text-white text-sm;
</style>
