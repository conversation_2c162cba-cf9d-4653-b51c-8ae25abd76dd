<script lang="ts">
import { ref, defineComponent, toRefs, computed, onMounted } from 'vue';
import { ServeUserBidProjectsApi } from '../../../serve-core/apis/serve/user/bid_projects.api';
import { ServeBidProjectModel } from '@/engines/iest/serve-core/models/serve/user/bid_projects';
import { VStore } from '@/lib/vails';
import { useContextInject } from '@/engines/bpm/bpm-core/ta-template-form-core/useContext';
import dayjs from 'dayjs';

const ComIestScreenBiddingProjectShow = defineComponent({
  name: 'ComIestScreenBiddingProjectShow',
  components: {},
  props: {},
  setup(props) {
    const rightBgUrl =
      'url(https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iest/%E5%9F%BA%E6%9C%AC%E4%BF%A1%E6%81%AFbg%20%281%29.png)';
    const leftHeaderUrl =
      'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iest/Group%20%283%29.png';
    const store = new VStore(new ServeUserBidProjectsApi(), ServeBidProjectModel);
    const { context } = useContextInject();
    const bidId = computed(() => context._form?.params?.bidId);
    onMounted(() => {
      store.find(bidId.value);
    });

    const dateFormat = (date: string) => {
      if (!date) return '-';
      return dayjs(date).format('YYYY-MM-DD HH:mm:ss');
    };

    const infoShowInOneLine = computed(() => ({
      项目负责人名称: store.record?.value?.manage_name,
      项目负责人联系方式: store.record?.value?.phone,
      统一信用代码: store.record?.value?.unit_code,
      // 项目负责人证件号码
    }));

    const infoShowInTwoLine = computed(() => ({
      项目建立时间: dateFormat(store.record?.value?.setup_at),
      // 招标范围: store.record?.value?.content?.range,
      项目金额: store.record?.value?.amount,
      项目编号: store.record?.value?.code,
      // 报名开始时间: dateFormat(store.record?.value?.start_at),
      // 报名截止时间: dateFormat(store.record?.value?.end_at),
      // 开标日期: dateFormat(store.record?.value?.open_at)
    }));
    return {
      ...toRefs(props),
      rightBgUrl,
      leftHeaderUrl,
      infoShowInOneLine,
      infoShowInTwoLine,
      record: store.record,
    };
  },
});
export default ComIestScreenBiddingProjectShow;
</script>

<template lang="pug">
.com-iest-screen-bidding-project-show.flex
  .left.mr-6.space-y-6.flex.flex-col(class='basis-2/3')
    header.w-493px.h-30px
      img.w-full.h-full(:src='leftHeaderUrl')
    .overflow-auto.flex-grow.h-0
      .form__item.font-medium.text-base.mb-6
        .text-primary-300.mb-6px 标题
        .text-white {{ record?.name }}
      .form__item.font-medium.text-base
        .text-primary-300.mb-6px 内容
        .text-white {{ record?.content?.body }}
  .right.flex-grow.flex.flex-col
    header.text-lg.text-white.font-semibold.mb-2 {{ record.unit }}
    .h-1px.w-full.mb-2(
      style='background: linear-gradient(90deg, #fff 0%, rgba(255, 255, 255, 0) 100%)'
    )
    .content__wrapper.flex-grow.h-0.overflow-auto.space-y-4
      .one-line-wrapper.space-y-2.text-sm.text-white
        .form__item(v-for='(value, key) in infoShowInOneLine') {{ key }}: {{ value }}
      .two-line-wrapper.text-sm.grid.grid-cols-2.gap-4
        .grid__item(v-for='(value, key) in infoShowInTwoLine')
          .text-primary-300.mb-6px {{ key }}
          .text-white {{ value }}
      TaAttachments(:attachments='record?.attachments')
</template>

<style lang="stylus" scoped>
.com-iest-screen-bidding-project-show .left
  background #09183F
.com-iest-screen-bidding-project-show .right
  background-image v-bind(rightBgUrl)
  background-size cover
  background-repeat no-repeat
.com-iest-screen-bidding-project-show .left, .com-iest-screen-bidding-project-show .right
  @apply rounded-2xl p-6
</style>
