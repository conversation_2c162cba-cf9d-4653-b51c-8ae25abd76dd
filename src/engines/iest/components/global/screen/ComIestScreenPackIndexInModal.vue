<script lang="ts">
import { usePreviewClickable } from '@/components/global/ta-component/TaBuilder/builder/usePreview';
import { useContextInject } from '@/engines/bpm/bpm-core/ta-template-form-core/useContext';
import ComBpmInstanceDetailDialog from '@/engines/bpm/components/ComBpmInstanceDetailDialog.vue';
import ComColorfulLabel from '@/engines/iest/components/global/ComColorfulLabel.vue';
import '@/engines/iest/components/global/screen/screen_table.styl';
import { ServeManagePacksApi } from '@/engines/iest/serve-core/apis/serve/manage/packs.api';
import { ServePackModel } from '@/engines/iest/serve-core/models/serve/manage/packs';
import { VObject, VStore } from '@/lib/vails';
import { computed, defineComponent, reactive, ref, toRefs } from 'vue';
import ComIestStatisticBlockCard from '../../ComIestStatisticBlockCard.vue';

const ComIestScreenPackIndexInModal = defineComponent({
  name: 'ComIestScreenPackIndexInModal',
  components: { ComIestStatisticBlockCard, ComColorfulLabel, ComBpmInstanceDetailDialog },
  setup(props) {
    const store = new VStore(new ServeManagePacksApi({}), ServePackModel);
    const { context } = useContextInject();
    const params = computed(() => context._form.params);

    const config = computed(() => ({
      store,
      template: 'serve_pack',
      actions: [
        { key: 'create', enabled: false },
        { key: 'update', enabled: false },
        { key: 'delete', enabled: false },
      ],
      mode: 'table',
      params: {
        q: params.value,
      },
      table: {
        scroll: { y: 'auto' },
      },
    }));

    const state = reactive({
      total: 0,
      pending: 0,
      sending: 0,
      finished: 0,
    });
    const blocks = computed(() => [
      {
        label: '批次总数',
        value: state.total,
        color: 'blue',
        icon: 'clock',
      },
      {
        label: '待审核',
        value: state.pending,
        color: 'purple',
        icon: 'info',
      },
      {
        label: '待发送',
        value: state.sending,
        color: 'yellow',
        icon: 'arrows-repeat',
      },
      {
        label: '已发送',
        value: state.finished,
        color: 'green',
        icon: 'user-headset',
      },
    ]);

    const fetchData = () => {
      store
        .sendCollectionAction({
          action: 'list_index',
          config: {
            data: {
              collection_stat_condition: {
                items: [
                  {
                    key: 'state',
                    caculator: {
                      type: 'caculation',
                      caculations: [
                        { name: 'total', method: 'count' },
                        { name: 'pending', filter: { state_eq: 'pending' }, method: 'count' },
                        { name: 'sending', filter: { state_eq: 'sending' }, method: 'count' },
                        { name: 'finished', filter: { state_eq: 'finished' }, method: 'count' },
                      ],
                    },
                  },
                ],
              },
            },
          },
        })
        .then((res: any) => {
          try {
            const { pending, sending, finished, total } = res.data.statistics.state;
            state.pending = pending;
            state.sending = sending;
            state.finished = finished;
            state.total = total;
          } catch (e) {
            console.error(e);
          }
        });
    };
    const onIndex = () => {
      fetchData();
    };

    const { onClick } = usePreviewClickable(props);
    const openMsgIndexScreenModal = (record: VObject, type: string) => {
      type = type.includes('unread') ? 'is_read_false' : 'is_read_true';
      const event = { mode: 'page_setting' } as { mode: 'page_setting' };
      const eventExtra = {
        page_setting_id: 12,
      };
      onClick(
        event,
        {
          params: {
            pack_id_eq: record.id,
            [type]: 1,
          },
        },
        eventExtra,
      );
    };

    const instanceVisible = ref(false);
    const activeRecord = ref<any>({});
    const onShow = (record: VObject) => {
      activeRecord.value = record;
      instanceVisible.value = true;
    };
    const openActivityShowScreenModal = (record: VObject) => {
      const event = { mode: 'page_setting' } as { mode: 'page_setting' };
      const eventExtra = {
        page_setting_id: 16,
      };
      onClick(
        event,
        {
          params: {
            activityId: record.id,
          },
        },
        eventExtra,
      );
    };
    const calcReachPercent = computed(() => (unread: number, read: number) => {
      unread = Number(unread || 0);
      read = Number(read || 0);
      const total = unread + read;
      if (total === 0) return 0;
      return (read / total) * 100;
    });
    return {
      ...toRefs(props),
      config,
      blocks,
      onIndex,
      openMsgIndexScreenModal,
      onShow,
      activeRecord,
      instanceVisible,
      openActivityShowScreenModal,
      calcReachPercent,
    };
  },
});
export default ComIestScreenPackIndexInModal;
</script>

<template lang="pug">
.com-iest-screen-pack-index-in-modal.iest__screen__table.flex.flex-col
  .grid.grid-cols-4.gap-4.mb-4
    template(v-for='block in blocks')
      ComIestStatisticBlockCard.block__card(
        :label='block.label',
        :value='block.value',
        :color='block.color',
        :icon='block.icon',
        style='background-color:#072342'
        :defaultTextColorWeight='400',
        :defaultBgWeight='900'
      )

  ComBpmInstanceDetailDialog.screen-bpm-instance-detail-dialog(
    v-if='instanceVisible && activeRecord.create_instance_id'
    v-model:visible='instanceVisible',
    :instanceId='activeRecord.create_instance_id'
  )

  TaIndexView.ta-index-view-skin.flex-grow.h-0(
    :config='config'
    :showHeader='false'
    @onIndex='onIndex'
    @onShow='onShow'
  )
    template(#bodyCell='{record,column,text}')
      .text-base.truncate(
        v-if='column.dataIndex?.join(".") === "activity.name"'
        @click.stop='openActivityShowScreenModal(record.activity)'
      ) {{ text }}
      .text-sm(
        v-else-if='column.dataIndex[0] === "reach_percent"'
        :class='{ "text-red-500": calcReachPercent(record.unread_count, record.read_count) > 80, "text-red-500": calcReachPercent(record.unread_count, record.read_count) <= 20 }'
      ) {{ `${calcReachPercent(record.unread_count, record.read_count).toFixed(1)}%` }}
</template>

<style lang="stylus" scoped>
.com-iest-screen-pack-index-in-modal
  .block__card
    :deep(.right > div):first-child
      color white
</style>
