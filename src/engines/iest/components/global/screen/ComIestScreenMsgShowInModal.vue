<script lang="ts">
import { useContextInject } from '@/engines/bpm/bpm-core/ta-template-form-core/useContext';
import { ServeManageMessagesApi } from '@/engines/iest/serve-core/apis/serve/manage/messages.api';
import { ServeMessageModel } from '@/engines/iest/serve-core/models/serve/manage/messages';
import { VStore } from '@/lib/vails';
import dayjs from 'dayjs';
import MarkdownIt from 'markdown-it';
import { computed, defineComponent, toRefs } from 'vue';

const ComIestScreenMsgShowInModal = defineComponent({
  name: 'ComIestScreenMsgShowInModal',
  components: {},
  props: {},
  setup(props) {
    const { context } = useContextInject();
    const messageId = computed(() => context._form?.params?.messageId);

    const store = new VStore(new ServeManageMessagesApi(), ServeMessageModel);
    store.find(messageId.value).then(() => {
      context._form.params.bidId = store.record?.value?.pack?.source?.id;
    });

    const dateFormat = (date: string) => {
      if (!date) return '-';
      return dayjs(date).format('YYYY-MM-DD HH:mm:ss');
    };

    const msgInfo = computed(() => ({
      区域: store.record?.value?.org_name,
      发送人: store.record?.value?.sender?.name,
      发送时间: dateFormat(store.record?.value?.send_at),
      是否已读: store.record?.value?.is_read ? '是' : '否',
      已读时间: dateFormat(store.record?.value?.read_at),
    }));

    function getTextFromHTML(htmlString: string) {
      const parser = new DOMParser();
      const doc = parser.parseFromString(htmlString, 'text/html');
      return doc.body.textContent || '';
    }
    const md = new MarkdownIt({
      html: false,
      linkify: true,
      typographer: true,
      breaks: true,
    });
    const msgContentAndActivityContent = (msg: string, activityContent?: Array<any>) => {
      let markdownMsg = msg || '';
      let htmlString = (activityContent || []).map((item: any) => item.body).join('\n');
      let markdownAll = markdownMsg + (htmlString ? '\n' + htmlString : '');
      let html = md.render(markdownAll);
      return html;
    };
    const receiver = computed(() => {
      const user_name = store.record?.value?.user?.name;
      const duty_name = store.record?.value?.user_duty_names || '';
      const department_name = store.record?.value?.user_department_names?.join('-') || '';
      return `${department_name}${duty_name}${user_name}`;
    });
    const project_name = computed(() => {
      const content = store.record?.value?.payload?.card?.content;
      if (!content) return '';
      const matches = content.match(/【([^】]*)】/);
      return matches ? matches[1] : content;
    });
    return {
      ...toRefs(props),
      record: store.record,
      msgInfo,
      msgContentAndActivityContent,
      receiver,
      project_name,
    };
  },
});
export default ComIestScreenMsgShowInModal;
</script>

<template lang="pug">
.com-iest-screen-msg-show-in-modal.overflow-y-auto
  .space-y-4.text-2xl.pt-8
    .text-blue 项目名称
      span.pl-2.text-white {{ project_name }}
    //- .text-blue 关联规则
      span.pl-2.text-white {{ record?.pack?.rule_name || 'AI发送' }}
    .text-blue 接收人
      span.pl-2.text-white {{ receiver }}
    .flex.space-x-4.overflow-x-auto
      .item.flex-grow(v-for='(value, key) in msgInfo')
        .text-blue.mb-2 {{ key }}
        .text-white {{ value }}
    ComIestScreenBiddingProjectShow.min-h-300px(v-if='record.type === "Serve::BidRule"')
    .flex.w-full.space-x-5(style='align-items: flex-start')
      .left.flex.flex-col.items-center(class='w-3/5')
        ComIestScreenBiddingProjectShow.min-h-300px(v-if='record.type === "Serve::BidRule"')
        p.text-3xl.text-white.mb-2(
        v-if='record.payload?.content?.content',style='text-indent: 2rem')
          span(v-html="msgContentAndActivityContent(record.payload?.content?.content)")
        p.text-3xl.text-white.pt-2(
          v-else-if='record?.content',
          style='text-indent: 2rem; line-height: 2'
          )
          span(v-html="msgContentAndActivityContent(record.content, record.activity?.content?.content)")
      .right.flex.justify-center(class='w-2/5')
        img(v-if='record.payload?.image?.url', :src='record.payload.image.url', style='object-fit: contain; height: auto; max-height: 100%')
</template>

<style lang="stylus" scoped>
.text-blue
  color: #1ba2ff

.text-3xl
  line-height: 1.8
</style>
