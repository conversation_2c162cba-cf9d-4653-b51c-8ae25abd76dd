<script lang="ts">
import { ref, defineComponent, toRefs, computed } from 'vue';

const ComIestScreenOfficialUserBarStatistic = defineComponent({
  name: 'ComIestScreenOfficialUserBarStatistic',
  components: {},
  props: {
    data: {
      type: Array,
      default: () => [],
    },
  },
  setup(props) {
    const echartsRef = ref<any>(null);
    const yData = computed(() => props.data.map((item: any) => item.name));

    const barOption = computed(() => ({
      grid: {
        top: '3%',
        left: '6%',
        right: '5%',
        bottom: '4%',
      },
      yAxis: {
        type: 'category',
        data: yData.value,
        axisLabel: {
          textStyle: {
            color: '#D1D5DB',
          },
        },
        axisPointer: {
          type: 'shadow',
        },
      },
      xAxis: {
        type: 'value',
        axisLabel: {
          color: 'white',
        },
        splitLine: {
          lineStyle: {
            type: 'dashed',
          },
        },
      },
      series: [
        {
          type: 'bar',
          data: props.data.map((item: any) => item.value),
        },
      ],
    }));
    return {
      ...toRefs(props),
      echartsRef,
      barOption,
    };
  },
});
export default ComIestScreenOfficialUserBarStatistic;
</script>

<template lang="pug">
.com-iest-screen-bar.w-full.h-full
  TaScreenEchartBase(:options='barOption' ref='echartsRef')
</template>

<style lang="stylus" scoped></style>
