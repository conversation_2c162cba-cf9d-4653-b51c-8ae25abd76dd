<script lang='ts'>
import { VObject, VStore } from '@/lib/vails';
import { ref, defineComponent, toRefs, computed } from 'vue';
import ComIestScreenPackInstanceCard from './ComIestScreenPackInstanceCard.vue';
import { useContextInject } from '@/engines/bpm/bpm-core/ta-template-form-core/useContext';
import { InstanceModel } from '@/engines/bpm/bpm-core/apis/user/instance.api';
import { BpmAdminInstances } from '@/engines/bpm/bpm-core/apis/admin/instance.api';
import ComBpmInstanceDetailDialog from '@/engines/bpm/components/ComBpmInstanceDetailDialog.vue';

const ComIestScreenPackInstanceInModal = defineComponent({
  name: 'ComIestScreenPackInstanceInModal',
  components: { ComIestScreenPackInstanceCard, ComBpmInstanceDetailDialog },
  setup(props) {
    const store = new VStore(new BpmAdminInstances(), InstanceModel as any)
    const { context } = useContextInject();
    const params = computed(() => context._form.params)
    const config = computed(() => ({
      store,
      actions: [
        { key: 'create', enabled: false },
        { key: 'update', enabled: false },
        { key: 'delete', enabled: false },
      ],
      mode: 'list',
      params: {
        q: params.value
      },
      list: {
        scroll: { y: "auto" },
        gap: 16,
      }
    }))

    const activeRecord = ref<VObject>({})
    const instanceVisible = ref(false)
    const onShow = (record: VObject) => {
      activeRecord.value = record;
      instanceVisible.value = true;
    }
    return {
      ...toRefs(props),
      config,
      onShow,
      activeRecord,
      instanceVisible
    };
  },
});
export default ComIestScreenPackInstanceInModal;
</script>

<template lang="pug">
.com-iest-screen-pack-instance-in-modal.iest__screen__table
  TaIndexView.ta-index-view-skin(:config='config' :showHeader='false' @onShow='onShow')
    template(#card='{record}')
      ComIestScreenPackInstanceCard.cursor-pointer(:record='record')
  ComBpmInstanceDetailDialog.screen-bpm-instance-detail-dialog(
    v-if='instanceVisible'
    v-model:visible='instanceVisible',
    :instanceId='activeRecord.id'
  )
</template>

<style lang="stylus" scoped>
</style>
