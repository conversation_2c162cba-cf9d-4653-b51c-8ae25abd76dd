<script lang='ts'>
import { ref, defineComponent, toRefs, computed } from 'vue';
import '@/engines/iest/components/global/screen/screen_table.styl';
import { VObject, VStore } from '@/lib/vails';
import { useContextInject } from '@/engines/bpm/bpm-core/ta-template-form-core/useContext';
import { IestManagePaperworksApi } from '@/engines/iest/apis/iest/manage/paperworks.api';
import { IestPaperworkModel } from '@/engines/iest/models/iest/manage/paperworks';
import { merge } from 'lodash';
import ComColorfulLabel from '../ComColorfulLabel.vue';
import ComIestPaperworksShow from '../../iest/paperworks/ComIestPaperworksShow.vue';
import ComIestScreenAlertCard from './ComIestScreenAlertCard.vue';

const ComIestScreenAlertIndexModal = defineComponent({
  name: 'ComIestScreenAlertIndexModal',
  components: { ComColorfulLabel, ComIestPaperworksShow, ComIestScreenAlertCard },
  props: {

  },
  setup(props) {
    const store = new VStore(
      new IestManagePaperworksApi({
        params: {
          group_keys: ['state'],
        },
      }),
      IestPaperworkModel,
    );
    const { context } = useContextInject();
    const params = computed(() => context._form.params)

    const mode = ref<'table' | 'list'>('table')

    const config = computed(() => ({
      store,
      template: 'iest_paperwork',
      detail: {
        mode: 'dialog',
        width: '1600px'
      },
      actions: [
        { key: 'create', enabled: false },
        { key: 'update', enabled: false },
        { key: 'delete', enabled: false },
      ],
      params: {
        q: merge({}, params.value),
      },
      mode: mode.value,
      table: {
        scroll: { y: "auto" }
      },
      list: {
        scroll: { y: "auto" },
        gap: 8,
        splitCount: 3
      }
    }))

    const stateColorMap = {
      success: 'green',
      uploading: 'blue',
      failed: 'red',
    }
    const stateNameMap = {
      success: '已完成',
      uploading: '处理中',
      failed: '失败',
    }

    return {
      ...toRefs(props),
      config,
      stateColorMap,
      stateNameMap,
      store,
      mode,
    };
  },
});
export default ComIestScreenAlertIndexModal;
</script>

<template lang="pug">
.com-iest-screen-alert-index-modal.iest__screen__table.flex.flex-col
  .border-1.rounded-md.border-primary-700.flex.mb-2.ml-auto.cursor-pointer(style='width:fit-content')
    .px-3.py-2.bg-primary-800(:class='{selected:mode==="table"}',@click.stop='mode = "table"')
      TaIcon.text-white(class='w-3 h-3' type='flowbite-v2-solid/chart-bars-3-from-left')
    .px-3.py-2.bg-primary-800(:class='{selected:mode==="list"}',@click.stop='mode = "list"')
      TaIcon.text-white(class='w-3 h-3' type='flowbite-v2-solid/grid')
  TaIndexView.ta-index-view-skin.flex-grow.h-0(
    :config='config'
    :showHeader='false',
  )
    template(#card='{ record }')
      ComIestScreenAlertCard.cursor-pointer(
        :record='record',
        :stateColor='stateColorMap[record.state]',
        :stateLabel='stateNameMap[record.state]',
      )
    template(#bodyCell='{record,column,text}')
      ComColorfulLabel.px-10px.py-2px.text-sm(
        v-if='column.dataIndex[0] === "state"'
        :label='stateNameMap[text]',
        :color='stateColorMap[text]',
        :defaultTextColorWeight='400',
        :defaultBgWeight='900'
      )
      .text-sm(v-else-if='column.dataIndex[0] === "attachment"') {{ text?.[0]?.fileName}}
    template(#detail='{ record, onClose }')
      .h-85vh.overflow-y-auto
        ComIestPaperworksShow(
          v-if='record.id',
          :store='store',
        )

</template>

<style lang="stylus" scoped>
.com-iest-screen-alert-index-modal
  .selected
    @apply bg-primary-700;
</style>
