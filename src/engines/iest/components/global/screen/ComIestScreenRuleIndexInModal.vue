<script lang="ts">
import { usePreviewClickable } from '@/components/global/ta-component/TaBuilder/builder/usePreview';
import { useContextInject } from '@/engines/bpm/bpm-core/ta-template-form-core/useContext';
import '@/engines/iest/components/global/screen/screen_table.styl';
import { ServeManageRulesApi } from '@/engines/iest/serve-core/apis/serve/manage/rules.api';
import { ServeRuleModel } from '@/engines/iest/serve-core/models/serve/manage/rules';
import { VObject, VStore } from '@/lib/vails';
import { merge } from 'lodash';
import { computed, defineComponent, toRefs } from 'vue';

const ComIestScreenRuleIndexInModal = defineComponent({
  name: 'ComIestScreenRuleIndexInModal',
  components: {},
  setup(props) {
    const store = new VStore(new ServeManageRulesApi({}), ServeRuleModel);
    const { context } = useContextInject();
    const params = computed(() => context._form.params);
    const config = computed(() => ({
      store,
      template: 'serve_rule#big_screen',
      actions: [
        { key: 'create', enabled: false },
        { key: 'update', enabled: false },
        { key: 'delete', enabled: false },
      ],
      params: {
        q: merge({}, params.value, { s: ['position asc'] }),
      },
      mode: 'table',
      table: {
        scroll: { y: 'auto' },
      },
    }));

    const { onClick } = usePreviewClickable(props);
    const openPackInstanceScreenModal = (record: VObject) => {
      const event = { mode: 'page_setting' } as { mode: 'page_setting' };
      const eventExtra = {
        page_setting_id: 5,
      };
      onClick(
        event,
        {
          params: {
            'flowable_of_Serve::Pack_type_rule_id_eq': record.id,
            state_in: ['created', 'preparing', 'processing'],
          },
        },
        eventExtra,
      );
    };

    const openPackIndexScreenModal = (record: VObject) => {
      const event = { mode: 'page_setting' } as { mode: 'page_setting' };
      const eventExtra = {
        page_setting_id: 10,
      };
      onClick(
        event,
        {
          params: {
            rule_id_eq: record.id,
          },
        },
        eventExtra,
      );
    };
    return {
      ...toRefs(props),
      config,
      openPackInstanceScreenModal,
      openPackIndexScreenModal,
    };
  },
});
export default ComIestScreenRuleIndexInModal;
</script>

<template lang="pug">
.com-iest-screen-rule-index-in-modal.iest__screen__table
  TaIndexView.ta-index-view-skin(:config='config' :showHeader='false')
    template(#bodyCell='{record,column,text}')
      .text-sm(v-if='column.dataIndex[0] === "orgs"') {{ record.orgs?.map(org => org.name).join(',') }}
      .text-sm.cursor-pointer.clickable(
        v-else-if='column.dataIndex[0] === "total_packs_count"'
        @click.stop='openPackIndexScreenModal(record)'
      ) {{text}}
      .text-sm.cursor-pointer.clickable(
        v-else-if='column.dataIndex[0] === "processing_packs_count"',
        @click.stop='openPackInstanceScreenModal(record)'
      ) {{ text }}
</template>

<style lang="stylus" scoped></style>
