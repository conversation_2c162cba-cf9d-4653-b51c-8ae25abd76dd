<script lang="ts">
import { usePreviewClickable } from '@/components/global/ta-component/TaBuilder/builder/usePreview';
import { IestManageOrgsApi } from '@/engines/iest/apis/iest/manage/orgs.api';
import { IestOrgModel } from '@/engines/iest/models/iest/manage/orgs';
import { ResAdminUsersApi } from '@/engines/res/res-core/apis/res/admin/users.api';
import { ResUserModel } from '@/engines/res/res-core/models/res/user';
import { VObject, VStore } from '@/lib/vails';
import { computed, defineComponent, onMounted, toRefs } from 'vue';
import ComColorfulLabel from '../ComColorfulLabel.vue';
import ComIestScreenOfficialUserBarStatistic from './ComIestScreenOfficialUserBarStatistic.vue';

const ComIestBiddingScreenOfficialUserIndexAndStatisticInModal = defineComponent({
  name: 'ComIestBiddingScreenOfficialUserIndexAndStatisticInModal',
  components: { ComIestScreenOfficialUserBarStatistic, ComColorfulLabel },
  props: {},
  setup(props) {
    const store = new VStore(
      new ResAdminUsersApi({
        params: {
          q: {
            scopes: 'find_public_official_users',
          },
        },
      }),
      ResUserModel,
    );
    const orgBaseStore = new VStore(
      new IestManageOrgsApi({
        params: {
          q: { scopes: { find_all_by_generation: 0 } },
        },
      }),
      IestOrgModel,
    );
    const orgStore = new VStore(
      new IestManageOrgsApi({
        params: {
          q: { scopes: { find_all_by_generation: 1 } },
        },
      }),
      IestOrgModel,
    );
    const config = computed(() => ({
      store,
      template: 'user#index',
      mode: 'table',
      table: {
        scroll: { y: 'auto' },
      },
      searcherSimpleOptions: [
        { key: 'name', label: '姓名', type: 'string' },
        { key: 'account', label: '手机号', type: 'string' },
      ],
    }));

    const computedData = computed(() => {
      return (orgStore.records?.value || [])
        .filter((record: any) => record.parent_id > 0)
        .map((record: any, index: number) => ({
          name: record.name,
          value: record.public_official_users_count,
        }));
    });

    const parentData = computed(() => {
      return orgBaseStore?.records?.value || [];
    });

    const fetchData = () => {
      Promise.all([orgBaseStore.index({ per_page: 1 }), orgStore.index({ per_page: 20 })]);
    };
    onMounted(() => {
      fetchData();
    });

    const onIndex = async () => {
      await fetchData();
    };

    const { onClick } = usePreviewClickable(props);
    const openMsgIndexScreenModal = (record: VObject, type: string) => {
      type = type.includes('unread') ? 'is_read_false' : 'is_read_true';
      const event = { mode: 'page_setting' } as { mode: 'page_setting' };
      const eventExtra = {
        page_setting_id: 12,
      };
      onClick(
        event,
        {
          params: {
            pack_id_eq: record.id,
            [type]: 1,
          },
        },
        eventExtra,
      );
    };
    return {
      ...toRefs(props),
      config,
      computedData,
      orgStore,
      orgBaseStore,
      onIndex,
      parentData,
      openMsgIndexScreenModal,
    };
  },
});
export default ComIestBiddingScreenOfficialUserIndexAndStatisticInModal;
</script>

<template lang="pug">
.com-iest-bidding-screen-official-user-index-and-statistic-in-modal.grid.grid-cols-2.gap-4.iest__screen__table
  .statistic.flex.flex-col
    a-spin(:spinning='orgBaseStore.loading.value || orgStore.loading.value')
      .grid.grid-cols-6.gap-2.mb-4
        .statistic__item.rounded-lg.px-4.py-6.h-31(style="background-color: #0E2A55;")
          .text-lg.text-white.opacity-80.mb-2 {{ parentData?.[0]?.name || "本级"}}
          .num {{ parentData?.[0]?.public_official_users_count || 0 }}
            span.text-sm.font-normal 人
        .statistic__item.rounded-lg.px-4.py-6.h-31(
          v-for='item in computedData'
          style="background-color: #0E2A55;"
        )
          .text-lg.text-white.opacity-80.mb-2 {{ item.name}}
          .num {{ item.value }}
            span.text-sm.font-normal 人
    .p-8.rounded-lg.flex-grow.h-0(style="background-color: #0E2A55;")
      ComIestScreenOfficialUserBarStatistic(:data='computedData')
  TaIndexView.ta-index-view-skin(:config='config' :showHeader='false' @onIndex='onIndex')
    template(#bodyCell='{ record, column }')
      .text-sm(v-if='column.dataIndex.includes("names")') {{ record[column.dataIndex[0]]?.join('、') }}
      template(v-else-if='column.dataIndex[0].includes("count")')
        .flex.w-full.justify-center
          .cursor-pointer(@click.stop='openMsgIndexScreenModal(record,column.dataIndex[0])') {{ text || 0 }}
      .text-sm(v-else-if='column.dataIndex[0] === "res_tags"') {{record.res_tags?.map(tag => tag.name).join('、')}}
</template>

<style lang="stylus" scoped>
.com-iest-bidding-screen-official-user-index-and-statistic-in-modal
  .num
    color #FFF
    font-family "DIN Alternate"
    font-size 36px
    font-style normal
    font-weight 700
    line-height normal
</style>
