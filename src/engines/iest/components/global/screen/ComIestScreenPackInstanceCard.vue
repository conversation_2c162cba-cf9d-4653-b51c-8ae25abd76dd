<script lang='ts'>
import { ref, defineComponent, toRefs, computed } from 'vue';

const ComIestScreenPackInstanceCard = defineComponent({
  name: 'ComIestScreenPackInstanceCard',
  components: {},
  props: {
    record: {
      type: Object,
      required: true,
    }
  },
  setup(props) {
    const instanceInfo = computed(() => ({
      发起人: props.record?.creator_name,
      编号: props.record?.seq,
      发起时间: props.record?.createdStr,
      当前阶段: props.record?.last_token?.name,
      操作人: props.record?.last_token?.operator_name,
    }))
    const relativeInfo = computed(() => ({
      规则名称: '',
      素材名称: '',
      所属区域: props.record?.flowable_info?.org_name,
    }))
    return {
      ...toRefs(props),
      instanceInfo,
      relativeInfo,
    };
  },
});
export default ComIestScreenPackInstanceCard;
</script>

<template lang="pug">
.com-iest-screen-pack-instance-card.text-sm.rounded-2xl.p-4
  header.flex.items-center.justify-between.mb-2
    .text-base.text-white.font-medium {{ record.workflow_name }}
    .label.px-10px.py-2px.rounded.text-xs.text-white.border-1(
      :class='{"bg-green-700 border-green-400":false, "bg-red-700 border-red-400":false, "bg-blue-700 border-blue-400":true}'
    ) 进行中
  section.space-x-6.flex.items-center
    .key.text-gray-400(v-for='(value,key) in instanceInfo') {{ key}}：
      span.value.text-white {{ value }}
  hr.my-1
  section.space-x-6.flex.items-center
    .key.text-gray-400(v-for='(value,key) in relativeInfo') {{ key}}：
      span.value.text-white {{ value }}
</template>

<style lang="stylus" scoped>
.com-iest-screen-pack-instance-card
  background-color #09183f
  hr
    border-color #264D7A

</style>
