<script lang='ts'>
import { useContextInject } from '@/engines/bpm/bpm-core/ta-template-form-core/useContext';
import { ServeManageActivitiesApi } from '@/engines/iest/apis/serve/manage/activities.api';
import { ServeActivityModel } from '@/engines/iest/models/serve/activities';
import { VStore } from '@/lib/vails';
import { ref, defineComponent, toRefs, computed, onMounted } from 'vue';
import dayjs from 'dayjs';
import ComLabel from '../ComLabel.vue';

const ComIestScreenActivityShowInModal = defineComponent({
  name: 'ComIestScreenActivityShowInModal',
  components: { ComLabel },
  setup(props) {
    const { context } = useContextInject();
    const activityId = computed(() => context._form?.params?.activityId)

    const store = new VStore(new ServeManageActivitiesApi({
      parents: [{ type: 'submodules', id: 1 }]
    }), ServeActivityModel)
    onMounted(() => {
      store.find(activityId.value)
    })

    return {
      ...toRefs(props),
      record: store.record,
      dayjs,
    };
  },
});
export default ComIestScreenActivityShowInModal;
</script>

<template lang="pug">
.com-iest-screen-activity-show-in-modal.overflow-y-auto
  article.py-4.px-6.bg-white.rounded-lg(v-if='record?.id')
    header.mb-4
      .text-base.text-gray-900.mb-1 {{ record.name }}
      .flex.items-center
        .text-xs.text-gray-400.pr-4 {{ dayjs(record.created_at).format('YYYY-MM-DD HH:mm') }}
        .tags__wrapper.flex.flex-wrap.flex-grow.w-0.space-x-1
          ComLabel.px-10px.py-2px(
            v-for='tag in record.tags',
            :label='tag.name',
            :bg='tag.color || "#1890ff"',
            color='white'
          )
    section.pt-2(v-if='record.content')
      TaContentField(
        :disabled='true',
        :value='record.content.content',
        v-if='Array.isArray(record.content.content)'
      )
      TaContentField(:disabled='true', :value='[{ body: record.content.content }]', v-else)
    section.pt-4.flex.justify-center(v-if='record.attachments')
      TaAttachments(:attachments='record.attachments.files', :disabled='true')
</template>

<style lang="stylus" scoped>
.com-iest-screen-activity-show-in-modal
  section
    :deep(img)
      margin auto
    :deep(video)
      margin auto
</style>
