<script lang="ts">
import { computed, defineComponent, toRefs } from 'vue';

const ComIestScreenTop = defineComponent({
  name: 'ComIestScreenTop',
  components: {},
  props: {
    records: {
      type: Array,
      default: () => [],
    },
  },
  setup(props) {
    const BG_URL =
      'url(https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iest/bid-guard/%E7%9F%A9%E5%BD%A2%206%20%281%29.png)';
    const colors = ['#D96050', '#DCA24D', '#207EE5', '#207EE5', '#3F73AC', '#3F73AC'];
    const data = computed(() => {
      return (props.records || [])
        .sort((a: any, b: any) => b.total_packs_count - a.total_packs_count)
        .slice(0, 6)
        .map((record: any) => ({
          name: record.name,
          value: record.total_packs_count,
        }));
    });
    return {
      ...toRefs(props),
      BG_URL,
      colors,
      data,
    };
  },
});
export default ComIestScreenTop;
</script>

<template lang="pug">
.com-iest-screen-top.grid.grid-cols-2.gap-2
  .item.w-full.h-32px.relative.bg-cover.flex.justify-around.items-center(v-for='(item,index) in data')
    .empty.px-8
    .rule__name.text-base.truncate.w-58 {{ item.name }}
    .rule__value.text-base {{ item.value }}
    .topN.absolute.top-0.left-0.h-full.w-16.flex.items-center.justify-center(
      :style='`background-color:${colors[index]}`'
    )
      .text-base.text-white.pl-2  {{ index + 1 }}
</template>

<style lang="stylus" scoped>
.com-iest-screen-top
  grid-auto-flow column
  grid-template-rows 32px 32px 32px
.com-iest-screen-top .item
  background-image v-bind(BG_URL)
  .topN
    clip-path polygon(9px 0,100% 0,calc(100% - 8px) 100%,17px 100%)
  .rule__name
    color #7DA7B7
  .rule__value
    color #AFE1EF
</style>
