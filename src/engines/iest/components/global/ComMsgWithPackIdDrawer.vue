<script lang="ts">
import { computed, defineComponent, ref, toRefs } from 'vue';
import ComServeMessageContent from '../serve/messages/ComServeMessageContent.vue';
import ComIestServePackInfoCard from '../serve/packs/ComIestServePackInfoCard.vue';
import ComColorfulLabel from './ComColorfulLabel.vue';

const ComMsgWithPackIdDrawer = defineComponent({
  name: 'ComMsgWithPackIdDrawer',
  components: {
    ComIestServePackInfoCard,
    ComColorfulLabel,
    ComServeMessageContent,
  },
  props: {
    title: { type: String, default: '新入职' },
    width: { type: String, default: '900' },
    visible: { type: Boolean, default: false },
    msgStore: { type: Object, required: true },
    record: { type: Object, required: true },
    params: {
      type: Object,
      default: () => {},
    },
  },
  setup(props, { emit }) {
    const localVisible = computed({
      get() {
        return props.visible;
      },
      set(val) {
        emit('update:visible', val);
      },
    });

    const config = computed(() => ({
      store: props.msgStore,
      template: 'serve_message',
      params: props.params,
      mode: 'table',
      table: {
        scroll: { y: 'auto' },
      },
    }));

    // 详情弹窗相关
    const showDetailModal = ref(false);
    const currentRecord = ref(null);
    function handleRowClick(record: any) {
      currentRecord.value = record;
      showDetailModal.value = true;
    }
    function handleModalClose() {
      showDetailModal.value = false;
    }

    return {
      ...toRefs(props),
      localVisible,
      config,
      showDetailModal,
      currentRecord,
      handleRowClick,
      handleModalClose,
    };
  },
});
export default ComMsgWithPackIdDrawer;
</script>

<template lang="pug">
TaNoPaddingDrawer(
  v-if='localVisible',
  v-model:visible='localVisible'
  :title='title',
  :width='width',
)
  .h-full.flex.flex-col.iest__table__skin.p-3
    ComIestServePackInfoCard.mb-4(:record='record')
    TaIndexView.flex-grow.h-0.ta-index-view-skin(
      :config='config',
      :showHeader='false',
      @onShow='handleRowClick'
    )
      template(#bodyCell='{record, column}')
        ComColorfulLabel.px-10px.py-2px(
          v-if='column.dataIndex[0] === "is_read"'
          :label='record.is_read? "已读" : "未读"'
          :color='record.is_read? "green" : "red"'
        )
  a-modal(
    :visible='showDetailModal',
    :width='1100',
    @cancel='handleModalClose',
    :destroyOnClose='true',
    :title='currentRecord?.name || "消息详情"',
    :footer='null',
    centered
  )
    .message-modal-content(v-if='showDetailModal && currentRecord')
      ComServeMessageContent(:record='currentRecord')
</template>

<style lang="stylus" scoped></style>
