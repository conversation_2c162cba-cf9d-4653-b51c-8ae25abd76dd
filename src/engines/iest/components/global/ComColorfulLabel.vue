<script setup lang="ts">
import ComLabel from './ComLabel.vue';
import useColor from '../../../../components/global/ta-component/ta-template-form-core/useColor';
interface Props {
  label: string;
  color: string;
  defaultTextColorWeight: number;
  defaultBgWeight: number;
}
const props = withDefaults(defineProps<Props>(), {
  defaultTextColorWeight: 800,
  defaultBgWeight: 100,
});

const { computedColor, computedBg } = useColor(
  props,
  props.defaultTextColorWeight,
  props.defaultBgWeight,
);

defineExpose({
  name: 'ComColorfulLabel',
})
defineOptions({
  inheritAttrs: false
})
</script>

<template lang="pug">
ComLabel(
  :style='$attrs["style"]',
  :class='$attrs["class"]',
  :label='label',
  :color='computedColor',
  :bg='computedBg',
  @click='$attrs["onClick"]'
)
  slot
</template>

<style lang="stylus"></style>
