<script lang='ts'>
import { BpmAdminInstances } from '@/engines/bpm/bpm-core/apis/admin/instance.api';
import { InstanceModel } from '@/engines/bpm/bpm-core/apis/user/instance.api';
import { VObject, VStore } from '@/lib/vails';
import { ref, defineComponent, toRefs, computed } from 'vue';
import ComBpmInstanceCard from '../../../bpm/components/ComBpmInstanceCardNew.vue';


const ComInstanceDrawer = defineComponent({
  name: 'ComInstanceDrawer',
  components: { ComBpmInstanceCard },
  props: {
    visible: { type: Boolean, default: false },

    params: { type: Object, default: () => ({}) }
  },
  setup(props, { emit }) {
    const store = new VStore(
      new BpmAdminInstances({}),
      InstanceModel,
    )

    const localVisible = computed({
      get() {
        return props.visible
      },
      set(val) {
        emit('update:visible', val)
      }
    })

    const config = computed(() => ({
      store,
      mode: "list",
      params: props.params,
      list: {
        scroll: { y: "auto" },
        gap: 12,
      }
    }))

    const addRuleNameForPackInstance = (record: VObject = {}) => {
      const ruleName = record.payload?.rule_name

      if (!record.summaryAry) {
        record.summaryAry = []
      }

      if (ruleName && !record.summaryAry.includes(`规则：${ruleName}`)) {
        record.summaryAry.push(`规则：${ruleName}`)
      }
      return true
    }
    return {
      ...toRefs(props),
      localVisible,
      config,
      addRuleNameForPackInstance,
    };
  },
});
export default ComInstanceDrawer;
</script>

<template lang="pug">
TaNoPaddingDrawer(
  v-if='localVisible'
  v-model:visible="localVisible"
)
  .h-full.flex.flex-col.iest__table__skin.p-3
    slot(name='drawer-header')
    TaIndexView.flex-grow.h-0.ta-index-view-skin(
      :config='config',
      :showHeader='false'
    )
      template(#card='{record,column,text}')
        ComBpmInstanceCard(
          :record='addRuleNameForPackInstance(record) && record'
          class='border-1 border-gray-200'
        )
</template>

<style lang="stylus" scoped></style>
