<script lang="ts">
import { ServeManageActivitiesApi } from '@/engines/iest/apis/serve/manage/activities.api';
import {
  CheckCircleOutlined,
  ClockCircleOutlined,
  CloseCircleOutlined,
  DeleteOutlined,
  LoadingOutlined,
  PauseCircleOutlined,
  PlayCircleOutlined,
  ReloadOutlined,
} from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import { computed, defineComponent, onMounted, onUnmounted, reactive, ref } from 'vue';

// 任务类型定义
interface TaskData {
  activity_name?: string;
  activity_id?: string;
  author?: string;
  error?: string;
}

interface Task {
  task_id: string;
  message: string;
  updated_at: string;
  status: string;
  data?: TaskData;
}

export default defineComponent({
  name: 'ComWechatCrawlTaskManager',
  components: {
    ClockCircleOutlined,
    CheckCircleOutlined,
    CloseCircleOutlined,
    DeleteOutlined,
    LoadingOutlined,
    PauseCircleOutlined,
    PlayCircleOutlined,
    ReloadOutlined,
  },
  setup() {
    // 响应式数据
    const loading = ref(false);
    const cleanupLoading = ref(false);
    const autoRefresh = ref(false);
    const refreshTimer = ref<any>(null);

    // 任务数据
    const tasks = reactive({
      pending: [] as Task[],
      processing: [] as Task[],
      completed: [] as Task[],
      failed: [] as Task[],
    });

    const stats = reactive({
      total: 0,
      pending: 0,
      processing: 0,
      completed: 0,
      failed: 0,
    });

    // API实例
    const api = new ServeManageActivitiesApi({
      parents: [{ type: 'submodules', id: 1 }],
    });

    // 计算属性：所有任务
    const allTasks = computed(() => {
      return [
        ...tasks.processing.map(t => ({ ...t, status: 'processing' })),
        ...tasks.pending.map(t => ({ ...t, status: 'pending' })),
        ...tasks.completed.map(t => ({ ...t, status: 'completed' })),
        ...tasks.failed.map(t => ({ ...t, status: 'failed' })),
      ].sort((a, b) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime());
    });

    // 获取任务列表
    const refreshTasks = async () => {
      if (loading.value) return;

      loading.value = true;
      try {
        console.log('正在获取任务列表...');
        const response = await api.getCrawlTasksList();

        if (response.data) {
          // 更新任务数据
          Object.assign(
            tasks,
            response.data.tasks || {
              pending: [],
              processing: [],
              completed: [],
              failed: [],
            },
          );

          // 更新统计数据
          Object.assign(
            stats,
            response.data.stats || {
              total: 0,
              pending: 0,
              processing: 0,
              completed: 0,
              failed: 0,
            },
          );

          console.log('任务列表获取成功:', response.data);
        }
      } catch (error: any) {
        console.error('获取任务列表失败:', error);
        message.error(`获取任务列表失败: ${error.message || '网络错误'}`);

        // 失败时清空数据
        Object.assign(tasks, {
          pending: [],
          processing: [],
          completed: [],
          failed: [],
        });
        Object.assign(stats, {
          total: 0,
          pending: 0,
          processing: 0,
          completed: 0,
          failed: 0,
        });
      } finally {
        loading.value = false;
      }
    };

    // 清理超过过期任务
    const cleanupTasks = async () => {
      cleanupLoading.value = true;
      try {
        console.log('清理超过24小时的过期任务...');
        const response = await api.cleanupCrawlTasks();

        if (response.data) {
          message.success(`已清理 ${response.data.cleaned_count || 0} 个过期任务`);
          await refreshTasks();
        } else {
          message.success('清理任务完成');
          await refreshTasks();
        }
      } catch (error: any) {
        console.error('清理任务失败:', error);
        message.error(`清理任务失败: ${error.message || '网络错误'}`);
      } finally {
        cleanupLoading.value = false;
      }
    };

    // 切换自动刷新
    const toggleAutoRefresh = () => {
      autoRefresh.value = !autoRefresh.value;

      if (autoRefresh.value) {
        startAutoRefresh();
        message.info('已开启自动刷新（每5秒）');
      } else {
        stopAutoRefresh();
        message.info('已停止自动刷新');
      }
    };

    // 开始自动刷新
    const startAutoRefresh = () => {
      if (refreshTimer.value) {
        clearInterval(refreshTimer.value);
      }

      refreshTimer.value = setInterval(() => {
        refreshTasks();
      }, 5000);
    };

    // 停止自动刷新
    const stopAutoRefresh = () => {
      if (refreshTimer.value) {
        clearInterval(refreshTimer.value);
        refreshTimer.value = null;
      }
    };

    // 删除任务
    const deleteTask = async (taskId: string) => {
      try {
        console.log('正在删除任务:', taskId);
        const response = await api.deleteCrawlTask(taskId);

        if (response.data) {
          message.success(`任务删除成功: ${taskId}`);
          await refreshTasks();
        } else {
          message.success('任务删除成功');
          await refreshTasks();
        }
      } catch (error: any) {
        console.error('删除任务失败:', error);
        message.error(`删除任务失败: ${error.message || '网络错误'}`);
      }
    };

    // 格式化时间
    const formatTime = (timeStr: string) => {
      if (!timeStr) return '';

      const time = new Date(timeStr);
      const now = new Date();
      const diff = now.getTime() - time.getTime();

      if (diff < 60000) {
        return '刚刚';
      } else if (diff < 3600000) {
        return `${Math.floor(diff / 60000)}分钟前`;
      } else if (diff < 86400000) {
        return `${Math.floor(diff / 3600000)}小时前`;
      } else {
        return time.toLocaleDateString() + ' ' + time.toLocaleTimeString();
      }
    };

    // 获取状态颜色
    const getStatusColor = (status: string) => {
      const colors: Record<string, string> = {
        pending: 'blue',
        processing: 'orange',
        completed: 'green',
        failed: 'red',
      };
      return colors[status] || 'default';
    };

    // 计算属性：是否有活跃任务
    const hasActiveTasks = computed(() => {
      return stats.pending > 0 || stats.processing > 0;
    });

    // 生命周期
    onMounted(() => {
      refreshTasks();

      if (hasActiveTasks.value) {
        autoRefresh.value = true;
        startAutoRefresh();
      }
    });

    onUnmounted(() => {
      stopAutoRefresh();
    });

    return {
      loading,
      cleanupLoading,
      autoRefresh,
      tasks,
      allTasks,
      stats,
      hasActiveTasks,
      refreshTasks,
      cleanupTasks,
      toggleAutoRefresh,
      deleteTask,
      getStatusColor,
      formatTime,
    };
  },
});
</script>

<template lang="pug">
.wechat-crawl-task-manager
  //- 任务统计卡片
  .task-stats.mb-4
    a-row(:gutter='16')
      a-col(:span='6')
        a-card.stat-card.pending
          .stat-content
            .stat-number {{ stats.pending }}
            .stat-label 排队中
      a-col(:span='6')
        a-card.stat-card.processing
          .stat-content
            .stat-number {{ stats.processing }}
            .stat-label 处理中
      a-col(:span='6')
        a-card.stat-card.completed
          .stat-content
            .stat-number {{ stats.completed }}
            .stat-label 已完成
      a-col(:span='6')
        a-card.stat-card.failed
          .stat-content
            .stat-number {{ stats.failed }}
            .stat-label 失败

  //- 操作按钮
  .task-actions.mb-4
    a-space
      a-button(type='primary', @click='refreshTasks', :loading='loading')
        template(#icon)
          ReloadOutlined
        | 刷新任务
      a-button(type='default', @click='toggleAutoRefresh')
        template(#icon)
          PlayCircleOutlined(v-if='!autoRefresh')
          PauseCircleOutlined(v-else)
        | {{ autoRefresh ? '停止自动刷新' : '开启自动刷新' }}
      a-button(type='danger', @click='cleanupTasks', :loading='cleanupLoading')
        template(#icon)
          DeleteOutlined
        | 清理过期任务

  //- 任务列表
  .task-list.overflow-auto.h-100
    a-empty(v-if='allTasks.length === 0', description='暂无任务')

    .task-item(v-for='task in allTasks', :key='task.task_id', :class='`status-${task.status}`')
      a-card.task-card
        template(#title)
          .task-header
            .task-id
              span.task-id-label 任务ID:
              a-tooltip(:title='task.task_id || "N/A"')
                span.task-id-value {{ task.task_id ? task.task_id : 'N/A' }}
            .task-actions
              .task-time {{ formatTime(task.updated_at) }}
              a-button(
                v-if='task.status === "completed" || task.status === "failed"',
                type='text',
                size='small',
                danger,
                @click.stop='deleteTask(task.task_id)'
              )
                template(#icon)
                  DeleteOutlined
                | 删除

        .task-content
          .task-message
            a-tag(:color='getStatusColor(task.status)')
              template(#icon)
                LoadingOutlined(v-if='task.status === "processing"')
                ClockCircleOutlined(v-if='task.status === "pending"')
                CheckCircleOutlined(v-if='task.status === "completed"')
                CloseCircleOutlined(v-if='task.status === "failed"')
              | {{ task.message }}

          //- 任务详情
          .task-details(v-if='task.data && Object.keys(task.data).length > 0')
            .detail-item(v-if='task.data.activity_name')
              strong 文章标题:
              span {{ task.data.activity_name }}
            .detail-item(v-if='task.data.activity_id')
              strong 活动ID:
              span {{ task.data.activity_id }}
            .detail-item(v-if='task.data.author')
              strong 作者:
              span {{ task.data.author }}
            .detail-item(v-if='task.data.error')
              strong 错误信息:
              span.error-text {{ task.data.error }}
</template>

<style scoped lang="stylus">
.wechat-crawl-task-manager
  .task-stats
    .stat-card
      text-align center
      &.pending .stat-number
        color #1890ff
      &.processing .stat-number
        color #fa8c16
      &.completed .stat-number
        color #52c41a
      &.failed .stat-number
        color #ff4d4f
      .stat-content
        .stat-number
          font-size 24px
          font-weight bold
          display block
        .stat-label
          font-size 12px
          color #666
          margin-top 4px
  .task-list
    .task-item
      margin-bottom 12px
      .task-header
        display flex
        justify-content space-between
        align-items center
        .task-id
          display flex
          align-items center
          .task-id-label
            font-size 12px
            color #666
            margin-right 4px
          .task-id-value
            font-family monospace
            font-size 12px
            color #1890ff
            cursor pointer
            &:hover
              color #40a9ff
        .task-actions
          display flex
          align-items center
          gap 8px
          .task-time
            font-size 12px
            color #999
      .task-card
        border-left 4px solid #d9d9d9
        .task-header
          display flex
          justify-content space-between
          align-items center
          .task-id
            display flex
            align-items center
            .task-id-label
              font-size 12px
              color #666
              margin-right 4px
            .task-id-value
              font-family monospace
              font-size 12px
              color #1890ff
              cursor pointer
              &:hover
                color #40a9ff
          .task-time
            font-size 12px
            color #999
        .task-content
          .task-message
            margin-bottom 12px
          .task-details
            .detail-item
              margin-bottom 4px
              font-size 13px
              strong
                color #333
              .error-text
                color #ff4d4f
      &.status-pending .task-card
        border-left-color #1890ff
      &.status-processing .task-card
        border-left-color #fa8c16
      &.status-completed .task-card
        border-left-color #52c41a
      &.status-failed .task-card
        border-left-color #ff4d4f
</style>
