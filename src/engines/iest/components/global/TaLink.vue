<template lang='pug'>
RouterLink(v-bind='$props',v-on='$listeners',ref='rl')
  slot
</template>

<script lang="ts">
import { RouteLocationRaw, useRouter } from 'vue-router';
import { PropType, onMounted, ref, onBeforeUnmount } from 'vue';
import { preloadRouteComponents } from '../../hooks/usePreload';
import { RouterLink } from 'vue-router';
import { toRefs } from '@vue/reactivity';

//requestIdleCallback polyfill for safari
const requestIdleCallback = window.requestIdleCallback ||
  function (cb: any) {
    const start = Date.now()
    return setTimeout(function () {
      cb({
        didTimeout: false,
        timeRemaining: () => Math.max(0, 50 - (Date.now() - start))
      })
    }, 1)
  }

const cancelIdleCallback = window.cancelIdleCallback || function (id: number) {
  clearTimeout(id)
}

//observer
type ObserveFn = (element: any, callback: () => void) => () => void
function useObserver(): { observe: ObserveFn } | undefined {

  let observer: IntersectionObserver | null = null
  const callbacks = new Map<Element, () => void>()

  const observe: ObserveFn = (element, callback: () => void) => {
    if (!observer) {
      observer = new IntersectionObserver((entries) => {
        for (const entry of entries) {
          const callback = callbacks.get(entry.target)
          const isVisible = entry.isIntersecting || entry.intersectionRatio > 0
          if (isVisible && callback) { callback() }
        }
      })
    }

    callbacks.set(element, callback)
    observer.observe(element)

    return () => {
      callbacks.delete(element)
      observer!.unobserve(element)

      if (callbacks.size === 0) {
        observer!.disconnect()
        observer = null
      }
    }
  }

  //todo: window的兼容性处理
  const _observer = (window as any)._observer = { observe }
  return _observer
}

export default {
  name: 'TaLink',
  components: { RouterLink },
  props: {
    //Router
    to: {
      type: String as PropType<RouteLocationRaw>,
      default: undefined
    },
    href: {
      type: String as PropType<RouteLocationRaw>,
      default: undefined
    },

    //...

    //prefetch
    prefetch: {
      type: Boolean,
      default: true,
    },
    noPrefetch: {
      type: Boolean,
      default: false,
    }
  },
  setup(props: any) {
    let idleId: any = null
    let unobserve: (() => void) | null = null
    const router = useRouter()

    const rl = ref<any>(null)

    onMounted(() => {
      const observer = useObserver()
      idleId = requestIdleCallback(() => {
        unobserve = observer!.observe(rl.value?.$el, async () => {
          unobserve?.()
          unobserve = null
          if (props.prefetch && !props.noPrefetch) {
            await preloadRouteComponents(props.to || props.href || '', router)
          }
        })

      })

    })

    onBeforeUnmount(() => {
      if (idleId) {
        cancelIdleCallback(idleId)
      }

      unobserve?.()
      unobserve = null

    })

    return {
      ...toRefs(props),
      rl
    }
  }
}
</script>

<style scoped></style>
