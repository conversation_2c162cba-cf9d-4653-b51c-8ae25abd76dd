<script setup lang="ts">
import { computed } from 'vue';
interface Props {
  label: string;
  color: string;
  bg: string;
}
const props = defineProps<Props>();

const computedStyle = computed(() => {
  return {
    color: props.color,
    backgroundColor: props.bg,
  };
});
</script>

<template lang="pug">
.com-iest-tag.rounded-md.flex.items-center.w-fit(:style='computedStyle')
  slot
    .label.text-xs.w-fit {{ label }}
</template>

<style lang="stylus" scoped>
.w-fit
  width fit-content
</style>
