<script lang="ts">
import { AuthSessionApi } from '@/engines/login/apis/auth/session.api';
import { computed, defineComponent, PropType, toRefs } from 'vue';

const ComServeRuleCatalogCard = defineComponent({
  name: 'ComServeRuleCatalogCard',
  components: {},
  props: {
    record: { type: Object, required: true },
    actions: { type: Array as PropType<any>, required: true },
    ids: { type: Array, default: () => [] },
  },
  setup(props, { emit }) {
    const user = computed(() => AuthSessionApi.currentUser());
    const hasPermit = computed(() => user.value.roles_name?.includes('serve_admin'));

    const checked = computed(() => props.ids.includes(props.record.id));
    const onEdit = () => {
      (props.actions as any).onEdit(props.record);
    };
    const onDelete = () => {
      (props.actions as any).onDelete(props.record);
    };
    return {
      ...toRefs(props),
      checked,
      onDelete,
      onEdit,
      hasPermit,
    };
  },
});
export default ComServeRuleCatalogCard;
</script>

<template lang="pug">
.com-serve-rule-catalog-card.p-4.bg-white.relative(:class='{ selected: checked }')
  .flex.mb-1.items-center
    .title {{ record.name }} · {{ record.statistic.rules_count || 0 }}
    .ml-auto.flex(v-if='hasPermit')
      .action(@click.stop='onEdit')
        TaIcon.ta__icon.text-gray-900(type='solid/pencil')
      .action(@click.stop='')
        a-popconfirm(title='确认删除吗？', ok-text='确认', cancel-text='取消', @confirm='onDelete')
          TaIcon.ta__icon.text-gray-900(type='solid/trash')
  .content {{ record.model_payload?.desc }}
</template>

<style lang="stylus" scoped>
.com-serve-rule-catalog-card
  border-bottom 1px solid #E5E7EB
  user-select none
  .title
    @apply text-sm font-medium leading-normal text-gray-700
  .content
    @apply text-xs font-normal leading-normal text-gray-500
  .action
    width 21px
    height @width
    border-radius 4px
    display none
    &:hover
      background-color #F3F4F6
    .ta__icon
      width 16px
      height @width
.com-serve-rule-catalog-card.selected
  .title
    @apply text-primary-700
  &::before
    content ''
    top 0
    left 0
    height 100%
    width 4px
    position absolute
    background-color $primary-color-700
.com-serve-rule-catalog-card:hover
  .action
    display flex
    align-items center
    justify-content center
</style>
