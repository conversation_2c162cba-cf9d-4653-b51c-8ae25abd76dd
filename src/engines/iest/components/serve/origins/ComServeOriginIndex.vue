<script lang="ts">
import ComColorfulLabel from '@/engines/iest/components/global/ComColorfulLabel.vue';
import '@/engines/iest/views/table.styl';
import { cloneDeep } from 'lodash-es';
import { computed, defineComponent, reactive, ref, toRefs } from 'vue';
import { VObject } from '../../../../../lib/vails/model/index';
import ComIestProgress from '../../ComIestProgress.vue';
import ComServeRelativeActivitiesDrawer from '../activities/ComServeRelativeActivitiesDrawer.vue';
import ComServeOriginShow from './ComServeOriginShow.vue';

const ComServeOriginIndex = defineComponent({
  name: 'ComServeOriginIndex',
  components: {
    ComServeOriginShow,
    ComIestProgress,
    ComColorfulLabel,
    ComServeRelativeActivitiesDrawer,
  },
  props: {
    store: { type: Object, required: true },
    activityStore: { type: Object, required: true },
    needStatistic: { type: Boolean, default: false },
  },
  setup(props, { emit }) {
    const visible = ref(false);
    const activeRecord = ref<any>({});
    const checkRelativeActivities = (record: VObject) => {
      activeRecord.value = record;
      visible.value = true;
    };
    const statisticActivityStore = cloneDeep(props.activityStore);
    const state = reactive({
      totalCount: props.store.totalCount,
      text: 0,
      image: 0,
      video: 0,
    });

    const statisticData = computed(() => [
      {
        name: '图文类',
        num: state.image || 0,
        color: '#FDF6B2',
        bg: 'rgba(142, 75, 16, 1)',
        icon: 'image',
      },
      {
        name: '视频类',
        num: state.video || 0,
        color: 'rgba(222, 247, 236, 1)',
        bg: 'rgba(4, 108, 78, 1)',
        icon: 'videocamera',
      },
      {
        name: '文本类',
        num: state.text || 0,
        color: 'rgba(225, 239, 254, 1)',
        bg: 'rgba(26, 86, 219, 1)',
        icon: 'file-word',
      },
    ]);

    const fetchStatisticData = () => {
      statisticActivityStore
        .sendCollectionAction({
          action: 'list_index',
          config: {
            data: {
              collection_stat_condition: {
                items: [
                  {
                    key: 'type',
                    caculator: {
                      type: 'caculation',
                      caculations: [
                        {
                          name: 'image',
                          filter: { content_type_tags_name_eq: '图片' },
                          method: 'count',
                        },
                        {
                          name: 'video',
                          filter: { content_type_tags_name_eq: '视频' },
                          method: 'count',
                        },
                        {
                          name: 'text',
                          filter: { content_type_tags_name_eq: '文字' },
                          method: 'count',
                        },
                      ],
                    },
                  },
                ],
              },
            },
          },
        })
        .then((res: any) => {
          const data = res.data?.statistics?.type;
          if (data) {
            const { image, video, text } = data;
            state.image = image || 0;
            state.video = video || 0;
            state.text = text || 0;
          }
        });
    };

    const config = computed(() => ({
      recordName: '来源管理',
      store: props.store,
      template: 'serve_origin',
      detail: {
        mode: 'drawer',
        width: '900px',
      },
      mode: 'table',
      actions: [
        { key: 'create', enabled: true },
        { key: 'update', enabled: true },
        { key: 'delete', enabled: true },
        { key: 'import', enabled: true },
        { key: 'export', enabled: true },
      ],
      draggable: true,
      table: {
        scroll: { y: 'auto' },
        // columns: [{ dataIndex: 'id', title: 'ID' }],
      },
      list: {
        scroll: { y: 'auto' },
      },
      searcherSimpleOptions: [
        { key: 'name', label: '来源名称', type: 'string' },
        { key: 'code', label: '分类', type: 'string' },
      ],
    }));

    // const statistics = ref({
    //   key1: 0,
    //   key2: 0
    // })

    // const tabs = computed<TaIndexViewTabInterface[]>(() => [
    //   {
    //     key: '',
    //     label: '标签1',
    //     query: {},
    //   },
    //   {
    //     key: 'key2',
    //     label: '标签2',
    //     query: {},
    //   },
    // ]);

    const onIndex = (data: VObject) => {
      console.log('data: ', data);
      if (props.needStatistic) {
        fetchStatisticData();
      }
    };

    return {
      ...toRefs(props),
      config,
      visible,
      activeRecord,
      checkRelativeActivities,
      // tabs,
      onIndex,
      statisticData,
      state,
    };
  },
});

export default ComServeOriginIndex;
</script>

<template lang="pug">
.com-serve-manage-origin-index.flex.flex-col.iest__table__skin
  ComServeRelativeActivitiesDrawer(
    v-model:visible='visible',
    :params='{q:{origin_id_eq:activeRecord?.id}}',
    :activityStore='activityStore',
    :title='`${activeRecord.code} | ${activeRecord.name}`'
  )

  .dashboard.rounded-lg.bg-white.p-4.flex.items-center.mb-4(v-if='needStatistic')
    .total__item.flex.items-center.px-5
      .bg-primary-100.rounded-lg.w-12.h-12.flex.items-center.justify-center.mr-3
        TaIcon.text-primary-700(type='flowbite-v2-solid/swatchbook' class='w-6 h-6')
      .name__and__value
        .text-2xl.leading-tight.font-semibold.text-gray-900.mb-1 {{ state.totalCount }}
        .text-sm.leading-tight.text-gray-500 来源总数
    .flex-grow.w-0
      .flex.items-center.w-full.justify-around.mb-4
        .item.flex.items-center(v-for='item in statisticData')
          .rounded-lg.w-8.h-8.flex.items-center.justify-center.mr-3(:style='`background-color: ${item.color}; color: ${item.bg};`')
            TaIcon(:type='`flowbite-v2-solid/${item.icon}`' class='!w-4 !h-4')
          .name__and__value
            .text-2xl.leading-tight.font-semibold.text-gray-900.mb-1 {{ item.num }}
            .text-sm.leading-tight.text-gray-500 {{ item.name }}
      ComIestProgress.h-3(:data='statisticData')

  //- .iest-card-container.mb-4
  //-   .rule__statistic.pb-4.flex.justify-between.items-center
  //-     .left.info
  //-       .text-3xl.text-gray-900.font-bold.leading-none {{ store.totalCount.value > 0? store.totalCount.value : 0 }}
  //-       .text-base.text-gray-500 来源总数
  //-     .right.legend.space-y-2
  //-       .flex.items-center(v-for='legend in legends')
  //-         .circle.mr-1(:style='{backgroundColor: legend.color}')
  //-         .text-xs.text-gray-500 {{ legend.name }}
  //-   ComIestProgress.h-26px(:data='legends')

  .px-4.pt-4.bg-white.flex-grow.h-0.rounded-lg
    TaIndexView.ta-index-view-skin(:config='config',@onIndex='onIndex')
      template(#bodyCell='{record,column,text}')
        ComColorfulLabel.py-2px.px-10px.cursor-pointer(
          v-if='column.dataIndex[0] === "activities_count"'
          :label='text || 0',
          color='blue',
          @click.stop='checkRelativeActivities(record)'
        )

    //- template(#card='{ record }')
    //- template(#table)
    //-    a-table-column(title='名称' dataIndex='record')
    //- template(#detail='{ record, onClose }')
    //-   ComServeOriginShow(
    //-     v-if='record.id'
    //-     :store='store'
    //-     :extendRoute='`/serve/manage/origins/${record.id}`'
    //-     :editable='editable'
    //-     @afterDelete='onClose'
    //-     @afterExtend='onClose'
    //-   )

</template>

<style lang="stylus" scoped>
.com-serve-manage-origin-index
  height 100%
  width 100%
</style>
