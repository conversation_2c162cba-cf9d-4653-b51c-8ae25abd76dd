<script lang="ts">
import { VObject } from '@/lib/vails/model';
import { computed, defineComponent, ref } from 'vue';
import ComServeMessageCard from '../common/ComServeMessageCard.vue';
import ComServeAiMessageTemplatesShow from './ComServeAiMessageTemplatesShow.vue';

const ComServeAiMessageTemplatesIndex = defineComponent({
  name: 'ComServeAiMessageTemplatesIndex',
  components: {
    ComServeAiMessageTemplatesShow,
    ComServeMessageCard,
  },
  props: {
    store: { type: Object, required: true },
  },
  setup(props) {
    const config = computed(() => ({
      recordName: 'AI消息模板',
      store: props.store,
      template: 'serve_ai_message_template',
      mode: 'list',
      list: {
        scroll: { y: 'auto' },
        gap: 12,
        splitCount: 2,
      },
      table: {
        scroll: { y: 'auto' },
      },
      searcherSimpleOptions: [{ key: 'name', label: '名称', type: 'string' }],
    }));

    const statistics = ref({
      published: 0,
      draft: 0,
      closed: 0,
    });

    const onIndex = (data: VObject) => {
      statistics.value = data.statistics || {};
      console.log('模板数据加载', data.records?.length || 0, '条记录');
    };

    return {
      config,
      onIndex,
    };
  },
});

export default ComServeAiMessageTemplatesIndex;
</script>

<template lang="pug">
.com-serve-manage-ai-message-templates-index
  TaIndexView(:config='config', @onIndex='onIndex')
    template(#header)
      .page-header.px-6.py-6.border-b.border-gray-200
        .header-content
          h1.text-2xl.font-bold.text-gray-900.mb-1 消息模板
          p.text-sm.text-gray-600 浏览和使用AI消息模板

    template(#card='{ record }')
      ComServeMessageCard(
        :record='{ ...record, store: store }',
        :showActions='true',
        :canLike='false',
        :canCollect='false',
        :showCounts='true',
        :showEdit='true',
        :showDelete='true'
      )
</template>

<style lang="stylus" scoped>
.com-serve-manage-ai-message-templates-index
  height 100%
  width 100%
  display flex
  flex-direction column
:deep(.ta-index-view)
  height 100%
  display flex
  flex-direction column
  .ta-index-view-content
    flex 1
    min-height 0
    overflow-y auto
    padding 16px 24px
  .ant-pagination
    flex-shrink 0
    margin 16px 0
    padding 0 24px
    text-align center
</style>
