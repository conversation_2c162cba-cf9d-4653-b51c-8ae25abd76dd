<script lang="ts">
import { defineComponent, toRefs, computed, ref } from 'vue';
import ComServeRelativedTags from '../tags/ComServeRelativedTags.vue';
import ComLabel from '../../global/ComLabel.vue';

const ComServeActivitiesCardForAi = defineComponent({
  name: 'ComServeActivitiesCardForAi',
  components: { ComServeRelativedTags, ComLabel },
  props: {
    record: {
      type: Object,
      default: () => ({}),
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    checked: {
      type: Boolean,
      default: false,
    },
  },
  setup(props, { emit }) {
    const drawerVisible = ref(false);
    const localChecked = computed({
      get() {
        return props.checked;
      },
      set(val) {
        emit('update:checked', val);
      },
    });
    return {
      ...toRefs(props),
      localChecked,
      drawerVisible,
    };
  },
});
export default ComServeActivitiesCardForAi;
</script>

<template lang="pug">
.com-serve-activities-card.bg-white.rounded-lg.flex.items-center.p-3.cursor-pointer(
  class='dark:bg-primary-900',
  @click.stop='() => { disabled ? null : localChecked = !localChecked } '
)
  a-radio.mr-2(v-if='!disabled', :disabled='disabled' v-model:checked='localChecked')
  .wrapper.flex-grow.w-0
    section.block
      .text-base.text-gray-900.font-bold.truncate(
        class='dark:text-white'
      ) {{ record.name }}
      .intro.text-sm.text-gray-400.truncate {{ record.model_payload?.intro }}
    footer.pt-2.flex.justify-between.w-full
      div(v-if='record.tags?.length > 0')
        ComServeRelativedTags(
          :tags='record.tags'
        )
      ComLabel.rounded-md.px-6px.py-2px.cursor-pointer(
        label='预览',
        color='white',
        bg='#3F83F8',
        @click.stop='drawerVisible = true'
      )
  TaNoPaddingDrawer(
    v-if='drawerVisible'
    v-model:visible='drawerVisible',
    :title='record.name',
    width='900',
  )
    article.py-4.px-6
      section(v-if='record.content')
        TaContentField(
          :disabled='true',
          :value='record.content.content',
          v-if='Array.isArray(record.content.content)'
        )
        TaContentField(:disabled='true', :value='[{ body: record.content.content }]', v-else)
          section.pt-4.flex.justify-center(v-if='record.attachments')
            TaAttachments(:attachments='record.attachments.files', :disabled='true')
</template>

<style lang="stylus" scoped></style>
