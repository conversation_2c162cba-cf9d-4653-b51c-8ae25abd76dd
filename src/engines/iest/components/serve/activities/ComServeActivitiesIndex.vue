<script lang="ts">
import '@/engines/iest/views/table.styl';
import { AuthSessionApi } from '@/engines/login/apis/auth/session.api';

import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import { computed, defineComponent, onUnmounted, reactive, ref, toRefs } from 'vue';
import { ServeManageActivitiesApi } from '../../../apis/serve/manage/activities.api';
import { ServeManageTagsApi } from '../../../apis/serve/manage/tags.api';
import ComIestIndexModeSwitch from '../../ComIestIndexModeSwitch.vue';
import ComIestProgress from '../../ComIestProgress.vue';
import ComLabel from '../../global/ComLabel.vue';
import ComWechatCrawlTaskManager from '../../global/ComWechatCrawlTaskManager.vue';
import ComServeRelativedTags from '../tags/ComServeRelativedTags.vue';
import ComServeActivitiesCard from './ComServeActivitiesCard.vue';
import ComServeActivitiesShow from './ComServeActivitiesShow.vue';

const ComServeActivitiesIndex = defineComponent({
  name: 'ComServeActivitiesIndex',
  components: {
    ComServeActivitiesShow,
    ComIestIndexModeSwitch,
    ComServeRelativedTags,
    ComServeActivitiesCard,
    ComLabel,
    ComIestProgress,
    ComWechatCrawlTaskManager,
  },
  props: {
    store: { type: Object, required: true },
    params: { type: Object, default: () => ({ q: { s: ['id desc'] } }) },
    needStatistic: { type: Boolean, default: false },
  },
  setup(props) {
    const indexMode = ref<'table' | 'list'>('table');
    const taindexview = ref<any>(null);
    const taskManager = ref<any>(null); // 新增：任务管理器引用

    // 微信文章抓取相关状态
    const wechatCrawlVisible = ref(false);
    const crawlModalTab = ref('new'); // 新增：标签页状态
    const wechatUrl = ref('');
    const crawlLoading = ref(false);
    const selectedTagIds = ref<number[]>([]);
    const tagOptions = ref<any[]>([]);
    const selectedState = ref<string>('published');

    // 状态选项
    const stateOptions = [
      { label: '已发布', value: 'published' },
      { label: '待发布', value: 'pending' },
    ];

    // 权限检查
    const info = computed(() => AuthSessionApi.currentUser());
    const hasAdminPermission = computed(
      () => info.value?.roles_name?.includes('serve_admin') || false,
    );
    const state = reactive({
      totalCount: props.store.totalCount,
      text: 0,
      image: 0,
      video: 0,
    });

    const statisticData = computed(() => [
      {
        name: '图文类',
        num: state.image || 0,
        color: '#FDF6B2',
        bg: 'rgba(142, 75, 16, 1)',
        icon: 'image',
      },
      {
        name: '视频类',
        num: state.video || 0,
        color: 'rgba(222, 247, 236, 1)',
        bg: 'rgba(4, 108, 78, 1)',
        icon: 'videocamera',
      },
      {
        name: '文本类',
        num: state.text || 0,
        color: 'rgba(225, 239, 254, 1)',
        bg: 'rgba(26, 86, 219, 1)',
        icon: 'file-word',
      },
    ]);

    const fetchStatisticData = () => {
      props.store
        .sendCollectionAction({
          action: 'list_index',
          config: {
            data: {
              collection_stat_condition: {
                items: [
                  {
                    key: 'type',
                    caculator: {
                      type: 'caculation',
                      caculations: [
                        {
                          name: 'image',
                          filter: { content_type_tags_name_eq: '图片' },
                          method: 'count',
                        },
                        {
                          name: 'video',
                          filter: { content_type_tags_name_eq: '视频' },
                          method: 'count',
                        },
                        {
                          name: 'text',
                          filter: { content_type_tags_name_eq: '文字' },
                          method: 'count',
                        },
                      ],
                    },
                  },
                ],
              },
            },
          },
        })
        .then((res: any) => {
          const data = res.data?.statistics?.type;
          if (data) {
            const { image, video, text } = data;
            state.image = image || 0;
            state.video = video || 0;
            state.text = text || 0;
          }
        });
    };

    const config = computed(() => ({
      recordName: '素材管理',
      store: props.store,
      template: 'serve_activity',
      detail: {
        mode: 'drawer',
        width: '1100px',
      },
      params: props.params,
      mode: indexMode.value,
      // editApi:
      // showCount: true,
      actions: [
        { key: 'create', enabled: true },
        { key: 'update', enabled: true },
        { key: 'delete', enabled: true },
        { key: 'import', enabled: true },
        { key: 'export', enabled: true },
        ...(hasAdminPermission.value
          ? [
              {
                key: 'wechat_crawl',
                label: '微信抓取',
                enabled: true,
                icon: 'WechatOutlined',
                action_type: 'collection',
                callback: showWechatCrawlModal,
              },
            ]
          : []),
      ],
      table: {
        scroll: { y: 'auto' },
      },
      list: {
        splitCount: 3,
        gap: 16,
        scroll: { y: 'auto' },
      },
      searcherAccordionUseDrawer: true,
      searcherComplicatedOptions: [
        {
          key: 'name',
          label: '素材标题',
          type: 'string',
        },
        {
          key: 'created_at',
          label: '创建时间',
          type: 'time',
        },
        // {
        //   key: 'tags_id',
        //   label: '标签',
        //   type: 'select',
        //   path: '/serve/manage/submodules/1/tags',
        //   multiple: true,
        // },
        {
          label: '标签',
          type: 'dynamicComponent',
          component: 'ComTagGroupField',
        },
      ],
      searcherSimpleOptions: [{ key: 'name', label: '素材名称', type: 'string' }],
    }));

    // const statistics = ref({
    //   key1: 0,
    //   key2: 0
    // })

    // const tabs = computed<TaIndexViewTabInterface[]>(() => [
    //   {
    //     key: 'key1',
    //     label: '标签1',
    //     num: statistics.value.key1,
    //     query: {},
    //   },
    //   {
    //     key: 'key2',
    //     label: '标签2',
    //     num: statistics.value.key2,
    //     query: {},
    //   },
    // ]);

    const onIndex = () => {
      // statistics.value = data.statistics
      if (props.needStatistic) {
        fetchStatisticData();
      }
    };

    const onShow = async (record: any) => {
      await taindexview.value.syncEditRecord(record);
      taindexview.value.editable = false;
      taindexview.value.visibleDrawer = true;
    };

    // 加载标签列表
    const loadTagOptions = async () => {
      try {
        const tagApi = new ServeManageTagsApi({
          parents: [{ type: 'submodules', id: 1 }],
        });
        const response = await tagApi.index({ per_page: 100 });
        tagOptions.value = response.data.records.map((tag: any) => ({
          label: tag.name,
          value: tag.id,
          tag: tag,
        }));
      } catch (error) {
        console.error('加载标签列表失败:', error);
      }
    };

    // 微信文章抓取功能
    const showWechatCrawlModal = async () => {
      wechatCrawlVisible.value = true;
      wechatUrl.value = '';
      selectedTagIds.value = [];
      selectedState.value = 'published';
      // 加载标签选项
      await loadTagOptions();
    };

    // 任务状态轮询相关
    const taskPollingTimer = ref<any>(null);
    const currentTaskId = ref<string>('');
    const taskStatus = ref<string>('');
    const taskMessage = ref<string>('');

    const handleWechatCrawl = async () => {
      if (!wechatUrl.value.trim()) {
        message.error('请输入微信文章URL');
        return;
      }

      if (!wechatUrl.value.includes('mp.weixin.qq.com')) {
        message.error('请输入有效的微信公众号文章URL');
        return;
      }

      crawlLoading.value = true;

      try {
        const api = new ServeManageActivitiesApi({
          parents: [{ type: 'submodules', id: 1 }],
        });

        const response = await api.sendCollectionAction('crawl_wechat_article', {
          data: {
            url: wechatUrl.value.trim(),
            tag_ids: selectedTagIds.value,
            state: selectedState.value,
          },
        });

        if (response.data && response.data.task_id) {
          // 异步任务已提交，开始轮询状态
          currentTaskId.value = response.data.task_id;
          taskStatus.value = response.data.status;
          taskMessage.value = response.data.message || '任务已提交，正在处理...';

          message.info('微信文章抓取任务已提交，正在后台处理...');

          // 不自动切换到任务管理标签页，保持在当前标签页
          // crawlModalTab.value = 'tasks';

          // 刷新任务管理器
          setTimeout(() => {
            taskManager.value?.refreshTasks();
          }, 500);

          // 开始轮询任务状态
          startTaskPolling();
        } else if (response.data) {
          // 兼容旧版本同步响应
          message.success('微信文章抓取成功！');
          wechatCrawlVisible.value = false;
          wechatUrl.value = '';
          taindexview.value?.silenceRefresh();
        }
      } catch (error: any) {
        console.error('微信文章抓取失败:', error);
        console.log('错误详情:', {
          status: error.response?.status,
          data: error.response?.data,
          message: error.message,
        });

        // 根据不同的错误类型显示不同的错误信息
        let errorMessage = '抓取失败，请稍后重试';

        if (error.response) {
          const status = error.response.status;
          const data = error.response.data;

          switch (status) {
            case 403:
              errorMessage = '权限不足，请联系管理员开通权限';
              break;
            case 400:
              errorMessage = data.error || '请求参数错误，请检查URL格式';
              break;
            case 404:
              errorMessage = '接口不存在，请检查系统配置';
              break;
            case 500:
              errorMessage = data.error || '服务器内部错误，请稍后重试';
              break;
            default:
              errorMessage = data.error || `请求失败 (状态码: ${status})`;
          }
        } else if (error.message) {
          errorMessage = `网络错误: ${error.message}`;
        }

        message.error(errorMessage);
      } finally {
        crawlLoading.value = false;
      }
    };

    // 开始任务状态轮询
    const startTaskPolling = () => {
      if (taskPollingTimer.value) {
        clearInterval(taskPollingTimer.value);
      }

      taskPollingTimer.value = setInterval(async () => {
        await checkTaskStatus();
      }, 3000); // 每3秒轮询一次

      // 立即检查一次
      checkTaskStatus();
    };

    // 检查任务状态
    const checkTaskStatus = async () => {
      if (!currentTaskId.value) return;

      try {
        const api = new ServeManageActivitiesApi({
          parents: [{ type: 'submodules', id: 1 }],
        });

        const response = await api.getCrawlTaskStatus(currentTaskId.value);

        if (response.data) {
          const { status, message: statusMessage, data } = response.data;

          taskStatus.value = status;
          taskMessage.value = statusMessage || '';

          if (status === 'completed') {
            // 任务完成
            stopTaskPolling();
            message.success('微信文章抓取成功！');
            wechatCrawlVisible.value = false;
            wechatUrl.value = '';
            crawlLoading.value = false;

            // 刷新列表
            taindexview.value?.silenceRefresh();

            // 显示抓取结果信息
            if (data && data.activity_name) {
              message.info(`已成功抓取文章：${data.activity_name}`);
            }
          } else if (status === 'failed') {
            // 任务失败
            stopTaskPolling();
            crawlLoading.value = false;
            message.error(statusMessage || '微信文章抓取失败');
          } else if (status === 'processing') {
            // 任务进行中，更新状态消息
            // 可以在这里显示进度信息
          }
        }
      } catch (error: any) {
        console.error('查询任务状态失败:', error);

        // 如果是404错误，说明任务不存在或已过期
        if (error.response?.status === 404) {
          stopTaskPolling();
          crawlLoading.value = false;
          message.error('任务已过期或不存在');
        }
      }
    };

    // 停止任务轮询
    const stopTaskPolling = () => {
      if (taskPollingTimer.value) {
        clearInterval(taskPollingTimer.value);
        taskPollingTimer.value = null;
      }
      currentTaskId.value = '';
      taskStatus.value = '';
      taskMessage.value = '';
    };

    // 组件卸载时清理定时器
    onUnmounted(() => {
      stopTaskPolling();
    });

    const AI_SRC = 'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iest/ai_summary.png';

    return {
      ...toRefs(props),
      config,
      taindexview,
      taskManager,
      // tabs,
      onIndex,
      indexMode,
      onShow,
      dayjs,
      state,
      statisticData,
      AI_SRC,
      // 微信抓取相关
      wechatCrawlVisible,
      crawlModalTab,
      wechatUrl,
      crawlLoading,
      selectedTagIds,
      tagOptions,
      selectedState,
      stateOptions,
      showWechatCrawlModal,
      handleWechatCrawl,
      hasAdminPermission,
      // 任务状态相关
      currentTaskId,
      taskStatus,
      taskMessage,
      startTaskPolling,
      checkTaskStatus,
      stopTaskPolling,
    };
  },
});

export default ComServeActivitiesIndex;
</script>

<template lang="pug">
.com-serve-manage-activities-index.iest__table__skin.flex.flex-col
  //- 微信文章抓取模态框
  a-modal(
    v-model:visible='wechatCrawlVisible',
    title='微信文章抓取管理',
    width='1000px',
    :footer='null',
    :maskClosable='false'
  )
    a-tabs(v-model:activeKey="crawlModalTab", type="card")
      //- 新建抓取任务标签页
      a-tab-pane(key="new", tab="新建抓取")
        .mb-4
          .mb-2 请输入微信公众号文章URL：
          a-input(
            v-model:value='wechatUrl',
            placeholder='https://mp.weixin.qq.com/s?__biz=...',
            :disabled='crawlLoading'
          )
        .mb-4
          .mb-2 选择标签（可选）：
          a-select(
            v-model:value='selectedTagIds',
            mode='multiple',
            placeholder='选择相关标签',
            :options='tagOptions',
            :disabled='crawlLoading',
            allow-clear,
            show-search,
            style='width: 100%',
            :filter-option='(input, option) => option.label.toLowerCase().includes(input.toLowerCase())'
          )
        .mb-4
          .mb-2 设置发布状态：
          a-select(
            v-model:value='selectedState',
            placeholder='选择发布状态',
            :options='stateOptions',
            :disabled='crawlLoading',
            style='width: 100%'
          )
        //- 任务状态显示
        .mb-4(v-if='currentTaskId && taskStatus')
          .mb-2 任务状态：
          .flex.items-center
            a-spin(v-if="taskStatus === 'pending' || taskStatus === 'processing'", size='small')
            .ml-2
          .text-sm.font-medium(
            :class="{ 'text-blue-600': taskStatus === 'pending' || taskStatus === 'processing', 'text-green-600': taskStatus === 'completed', 'text-red-600': taskStatus === 'failed' }"
          ) {{ taskMessage || '处理中...' }}
          .text-xs.text-gray-500(v-if='currentTaskId') 任务ID: {{ currentTaskId }}
        .text-gray-500.text-sm.mb-4
          | 支持微信公众号文章链接，系统将自动提取文章内容并保存到素材库中。任务提交后将在后台异步处理，不会阻塞界面操作。

        //- 操作按钮
        .flex.justify-end
          a-space
            a-button(@click='wechatCrawlVisible = false') 取消
            a-button(
              type='primary',
              :loading='crawlLoading',
              @click='handleWechatCrawl',
              :disabled='!wechatUrl'
            ) 开始抓取

      //- 任务管理标签页
      a-tab-pane(key="tasks", tab="任务管理")
        ComWechatCrawlTaskManager(ref="taskManager")
  .dashboard.rounded-lg.bg-white.p-4.flex.items-center.mb-4(v-if='needStatistic')
    .total__item.flex.items-center.px-5
      .bg-primary-100.rounded-lg.w-12.h-12.flex.items-center.justify-center.mr-3
        TaIcon.text-primary-700.w-6.h-6(type='flowbite-v2-solid/swatchbook')
      .name__and__value
        .text-2xl.leading-tight.font-semibold.text-gray-900.mb-1 {{ state.totalCount }}
        .text-sm.leading-tight.text-gray-500 素材总数
    .flex-grow.w-0
      .flex.items-center.w-full.justify-around.mb-4
        .item.flex.items-center(v-for='item in statisticData')
          .rounded-lg.w-8.h-8.flex.items-center.justify-center.mr-3(
            :style='`background-color: ${item.color}; color: ${item.bg};`'
          )
            TaIcon(:type='`flowbite-v2-solid/${item.icon}`', class='!w-4 !h-4')
          .name__and__value
            .text-2xl.leading-tight.font-semibold.text-gray-900.mb-1 {{ item.num }}
            .text-sm.leading-tight.text-gray-500 {{ item.name }}
      ComIestProgress.h-3(:data='statisticData')

  .px-4.pt-4.bg-white.flex-grow.h-0.rounded-lg
    TaIndexView.ta-index-view-skin(ref='taindexview', :config='config', @onIndex='onIndex')
      template(#card='{ record }')
        ComServeActivitiesCard.cursor-pointer(:record='record', @onShow='onShow(record)')
      template(#bodyCell='{ record, column, text }')
        ComServeRelativedTags(v-if='column.dataIndex[0] === "tag_ids"', :tags='record.tags')
        .flex.items-center(v-else-if='column.dataIndex[0] === "name"')
          a-tooltip.mr-1.flex-shrink-0(:title='record.ai_summary', v-if='record.ai_summary')
            .w-5.h-5
              img.w-full.h-full.rounded-full(:src='AI_SRC', alt='AI', width='20', height='20')
          span.text-sm {{ text }}
      template(#right-actions)
        ComIestIndexModeSwitch(v-model:indexMode='indexMode')
      template(#detail='{ record, onClose }')
        article.py-4.px-6
          header.mb-4
            .text-base.text-gray-900.mb-1 {{ record.name }}
            .flex.items-center
              .text-xs.text-gray-400.pr-4 {{ dayjs(record.created_at).format('YYYY-MM-DD HH:mm') }}
              .tags__wrapper.flex.flex-wrap.flex-grow.w-0.space-x-1
                ComLabel.px-10px.py-2px(
                  v-for='tag in record.tags',
                  :label='tag.name',
                  :bg='tag.color || "#1890ff"',
                  color='white'
                )
          section.pt-2(v-if='record.content')
            TaContentField(
              :disabled='true',
              :value='record.content.content',
              v-if='Array.isArray(record.content.content)'
            )
            TaContentField(:disabled='true', :value='[{ body: record.content.content }]', v-else)
          section.pt-4.flex.justify-center(v-if='record.attachments')
            TaAttachments(:attachments='record.attachments.files', :disabled='true')

    //- template(#card='{ record }')
    //- template(#table)
    //-    a-table-column(title='名称' dataIndex='record')
    //- template(#detail='{ record, onClose }')
    //-   ComServeActivitiesShow(
    //-     v-if='record.id'
    //-     :store='store'
    //-     :extendRoute='`/serve/manage/activities/${record.id}`'
    //-     :editable='editable'
    //-     @afterDelete='onClose'
    //-     @afterExtend='onClose'
    //-   )
</template>

<style lang="stylus" scoped>
.com-serve-manage-activities-index
  height 100%
  width 100%
  section
    :deep(img)
      margin auto
    :deep(video)
      margin auto
</style>
