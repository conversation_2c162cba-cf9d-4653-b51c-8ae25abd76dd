<script lang='ts'>
import { ref, defineComponent, toRefs, computed } from 'vue';
import ComServeRelativedTags from '../tags/ComServeRelativedTags.vue';
import dayjs from 'dayjs';
const ComServeActivitiesCard = defineComponent({
  name: 'ComServeActivitiesCard',
  components: { ComServeRelativedTags },
  props: {
    record: {
      type: Object,
      default: () => ({})
    }
  },
  setup(props) {
    const AI_SRC = 'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iest/ai_summary.png'
    return {
      ...toRefs(props),
      dayjs,
      AI_SRC,
    };
  },
});
export default ComServeActivitiesCard;
</script>

<template lang="pug">
.com-serve-activities-card.iest-card-container.overflow-hidden(class='!p-0')

  section.px-4.py-3
    .flex.items-center.mb-2.justify-between
      .text-xs.text-gray-500 {{ record.origin?.name}}
      a-tooltip(:title='record.ai_summary',v-if='record.ai_summary')
        .w-5.h-5
          img.w-full.h-full(:src='AI_SRC' alt='AI' class='rounded-full' width='24' height='24')
    .text-base.text-gray-900.font-bold.truncate {{ record.name }}
    .intro.text-xs.text-gray-500 {{ dayjs(record.created_at).format('YYYY-MM-DD HH:mm:ss') }}
    footer.flex.justify-between.items-end.pt-1
      ComServeRelativedTags(:tags='record.tags')
      a-button(ghost type='primary',@click.stop='$emit("onShow")' class='!rounded-lg') 查看
</template>

<style lang="stylus" scoped>
.com-serve-activities-card
  border 1px solid #E5E7EB
  section
    .intro
      overflow  hidden
      text-overflow ellipsis
      display -webkit-box
      -webkit-line-clamp 2
      -webkit-box-orient vertical
</style>
