<script lang="ts">
import { defineComponent, toRefs, ref } from 'vue';
import dayjs from 'dayjs';
const ComServeHomeHotIndex = defineComponent({
  name: 'ComServeHomeHotIndex',
  components: {},
  props: {
    store: {
      type: Object,
      required: true,
    },
    skipFunction: {
      type: Function,
      required: true,
    },
  },
  setup(props) {
    const IconMap = {
      0: 'url(https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/discipline-inspection/%E7%83%AD%E9%97%A81.png)',
      1: 'url(https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/discipline-inspection/%E7%83%AD%E9%97%A82.png)',
      2: 'url(https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/discipline-inspection/%E7%83%AD%E9%97%A83.png)',
      3: 'url(https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/discipline-inspection/%E5%8F%8C%E7%AE%AD%E5%A4%B4.png)',
      4: 'url(https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/discipline-inspection/%E5%8F%8C%E7%AE%AD%E5%A4%B4.png)',
    };
    const currentIndex = ref(0);
    const handleChange = (current: any) => {
      currentIndex.value = current;
    };
    return {
      ...toRefs(props),
      IconMap,
      dayjs,
      records: props.store.records,
      handleChange,
      currentIndex,
    };
  },
});
export default ComServeHomeHotIndex;
</script>

<template lang="pug">
.com-serve-home-hot-index.flex.items-center.w-full.h-298px
  a-carousel.w-529px.h-298px.banners.flex-shrink-0(autoplay, :afterChange='handleChange')
    img.w-529px.h-298px.cursor-pointer(
      v-for='record in records',
      @click='onClickActivity(record)',
      :src='record?.cover_image?.files?.[0]?.url || "https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iest/Group-17-%E6%81%A2%E5%A4%8D%E7%9A%84.png"'
    )

  .flex-grow.ml-6.h-full
    header.h-35px.relative.mb-5
      img.absolute.left-3.w-90px.h-8.bottom-2(src='@/assets/images/hot.png', alt='热点阅读')

    section.hot__card.px-1.py-3.flex.items-center.cursor-pointer(
      v-for='(record, index) in records',
      :class='{ cur: currentIndex === index }',
      @click='skipFunction(record)'
    )
      .icon.w-4.h-4(v-if='index > 2', :style='`background-image:${IconMap[index]}`')
      .icon.w-6.h-6(v-else, :style='`background-image:${IconMap[index]}`')
      .text-sm.text-gray-700.font-medium.ml-1.mr-auto.truncate.w-0.flex-grow {{ record.name }}
      .text-xs.text-gray-500.font-normal.flex-shrink-0 {{ dayjs(record.created_at).format('YYYY-MM-DD') }}
</template>

<style lang="stylus" scoped>
.com-serve-home-hot-index
  .banners img
    display block
    object-fit cover
    object-position center
  header
    border-top 1px solid #1A56DB
    background linear-gradient(
      0deg,
      rgba(255, 255, 255, 0.19) 0%,
      rgba(255, 255, 255, 0.19) 100%
    ), #C3DDFD
    img
      object-fit cover
  .cur
    >div
      @apply text-blue-400
  .hot__card
    border-bottom 1px solid #E5E7EB
    &:hover
      >div
        @apply text-blue-600
    .icon
      @apply bg-cover
</style>
