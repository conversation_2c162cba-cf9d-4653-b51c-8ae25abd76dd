<script lang='ts'>
import { ref, defineComponent, toRefs, reactive, computed } from 'vue';
import ComColorfulLabel from '../../global/ComColorfulLabel.vue';
import dayjs from 'dayjs';

const ComServeRuleGrpInfoCard = defineComponent({
  name: 'ComServeRuleGrpInfoCard',
  components: { ComColorfulLabel },
  props: {
    record: {
      type: Object,
      required: true,
    }
  },
  setup(props) {
    const state = computed(() => ({
      待审核: props.record?.statistic?.processing_packs_count,
      发送数量: props.record?.statistic?.finished_packs_count,
      规则数量: props.record?.statistic?.rules_count,
    }))

    const data = computed(() => ({
      描述: props.record?.payload?.desc,
      创建时间: dayjs(props.record.created_at).format('YYYY-MM-DD HH:mm:ss'),
      最新发送时间: props.record.latest_send_at ? dayjs(props.record.latest_send_at).format('YYYY-MM-DD HH:mm:ss') : '暂无',
    }))
    return {
      ...toRefs(props),
      state,
      data,
    };
  },
});
export default ComServeRuleGrpInfoCard;
</script>

<template lang="pug">
.com-serve-rule-info-card.p-3.rounded-lg.bg-gray-50
  header.mb-2
    .text-sm.text-gray-900.font-semibold {{record.name}}
  section.flex.justify-between.items-end
    .info.mr-4
      .item.text-xs.text-gray-500.mb-1(v-for='(value,key) in data') {{ key }}
        span.pl-3 {{ value }}
    .batch__statistic.px-4.py-3.flex
      .name__and__num.w-16.px-2.flex.flex-col.items-center(v-for='(value,key) in state')
        .num.text-base.font-bold  {{ value }}
        .name.text-xs.font-normal {{ key }}
</template>

<style lang="stylus" scoped>
.com-serve-rule-info-card
  .batch__statistic
    background-image linear-gradient(270deg, #FFF4F4 0%, #FFFAFA 100%)
    .num
      color #25396F
      font-family "DIN Alternate"
    .name
      color rgba(35, 56, 118, 0.80)
</style>
