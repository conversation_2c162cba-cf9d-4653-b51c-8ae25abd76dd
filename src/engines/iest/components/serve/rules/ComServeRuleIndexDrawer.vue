<script lang='ts'>
import { ref, defineComponent, toRefs, computed } from 'vue';
import ComServeRulesIndex from './ComServeRulesIndex.vue';
import usePolicy from '@/components/global/ta-component/ta-template-form-core/usePolicy';
import ComColorfulLabel from '../../global/ComColorfulLabel.vue';
import ComServeRuleInfoCard from './ComServeRuleInfoCard.vue';
import ComServeRuleShowDrawer from './ComServeRuleShowDrawer.vue';
import { VObject } from '@/lib/vails';

const ComServeRuleIndexDrawer = defineComponent({
  name: 'ComServeRuleIndexDrawer',
  components: {
    ComServeRulesIndex,
    ComColorfulLabel,
    ComServeRuleInfoCard,
    ComServeRuleShowDrawer,
  },
  props: {
    visible: { type: Boolean, default: false },
    store: { type: Object, required: true },
    params: { type: Object, default: () => ({}) },
    initForm: { type: Object, default: () => ({}) }
  },
  setup(props, { emit, attrs }) {
    usePolicy()
    const workflowId = props.store.model.packWorkflowId
    const localVisible = computed({
      get: () => props.visible,
      set: (val: any) => {
        emit('update:visible', val);
      }
    })
    const config = computed(() => ({
      recordName: `${attrs.title}`,
      store: props.store,
      template: 'serve_rule',
      mode: 'table',
      params: props.params,
      table: {
        scroll: { y: "auto" }
      },
    }))

    const visible = ref(false);
    const activeRecord = ref<VObject>({});
    const params = ref<VObject>({})
    const onOpenDrawer = (record: VObject) => {
      visible.value = true
      activeRecord.value = record
      params.value = {
        q: {
          'flowable_of_Serve::Pack_type_rule_id_eq': record.id,
          state_not_in: ['completed', 'terminated']
        }
      }
    }

    const drawerVisible = ref(false)
    const onOpenDrawer2 = (record: VObject) => {
      drawerVisible.value = true
      activeRecord.value = record;
      params.value = {
        q: {
          rule_id_eq: record.id,
        }
      }
    }
    return {
      ...toRefs(props),
      localVisible,
      config,
      workflowId,
      visible,
      activeRecord,
      params,
      onOpenDrawer,
      onOpenDrawer2,
      drawerVisible,
    };
  },
});
export default ComServeRuleIndexDrawer;
</script>

<template lang="pug">
TaNoPaddingDrawer(
  v-if='localVisible'
  v-model:visible="localVisible"
)
  .p-4.h-full.iest__table__skin
    ComInstanceDrawer(
      v-model:visible='visible',
      title='待审核',
      :workflowId='workflowId',
      :params='params'
      width='900'
    )
      template(#drawer-header)
        ComServeRuleInfoCard(:record='activeRecord')

    ComServeRuleShowDrawer(
      v-model:visible='drawerVisible',
      :title='`${activeRecord.name} | 已发送批次`',
      width='900',
      :params='params',
      :ruleStore='store'
    )
      template(#drawer-header)
        ComServeRuleInfoCard.mb-4(:record='activeRecord')

    TaIndexView.ta-index-view-skin(
      :config='config'
    )
      template(#bodyCell='{ record,column,text }')
        template(v-if='column.dataIndex[0] === "processing_packs_count"')
          ComColorfulLabel.px-10px.py-2px.cursor-pointer(
            :label='text',
            color='blue',
            @click.stop='onOpenDrawer(record)'
          )
        template(v-else-if='column.dataIndex[0] === "total_packs_count"')
          ComColorfulLabel.px-10px.py-2px.cursor-pointer(
            :label='text',
            color='blue',
            @click.stop='onOpenDrawer2(record)'
          )
        template(v-else-if='column.dataIndex[0] === "orgs"')
          .text-sm {{record.orgs?.map((org)=>org.name)?.join(',')}}
</template>

<style lang="stylus" scoped></style>
