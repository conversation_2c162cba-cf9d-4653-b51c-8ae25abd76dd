<script lang="ts">
import '@/engines/iest/views/table.styl';
import { AuthSessionApi } from '@/engines/login/apis/auth/session.api';
import { VObject } from '@/lib/vails/model';
import { computed, defineComponent, ref, toRefs } from 'vue';
import ComColorfulLabel from '../../global/ComColorfulLabel.vue';
import ComServeRuleCatalogCard from '../catalogs/ComServeRuleCatalogCard.vue';
import ComServeRuleInfoCard from './ComServeRuleInfoCard.vue';
import ComServeRuleShowDrawer from './ComServeRuleShowDrawer.vue';
import ComServeRulesShow from './ComServeRulesShow.vue';

const ComServeRulesIndex = defineComponent({
  name: 'ComServeRulesIndex',
  components: {
    ComServeRulesShow,
    ComColorfulLabel,
    ComServeRuleInfoCard,
    ComServeRuleCatalogCard,
    ComServeRuleShowDrawer,
  },
  props: {
    store: { type: Object, required: true },
    catalogStore: { type: Object, required: true },
    sidebarWidth: { type: Number, default: 250 },
  },
  setup(props, { emit }) {
    const catalogTaIndexView = ref<any>(null);
    const currentUser = computed(() => AuthSessionApi.currentUser());
    console.log('currentUser: ', currentUser);
    const isAdmin = computed(() => currentUser.value.roles_name.includes('serve_admin'));
    const config = computed(() => ({
      recordName: '规则管理',
      store: props.store,
      template: 'serve_rule',
      params: {
        q: {
          rule_group_id_in: catalogIds.value,
          s: ['position asc'],
        },
      },
      mode: 'table',
      actions: [
        { key: 'create', enabled: isAdmin.value },
        { key: 'update', enabled: isAdmin.value },
        { key: 'delete', enabled: isAdmin.value },
        { key: 'import', enabled: isAdmin.value },
        { key: 'export', enabled: isAdmin.value },
      ],
      table: {
        scroll: { y: 'auto' },
      },
      draggable: isAdmin.value,
      formDataEncode: (formData: any) => {
        return {
          ...formData,
          rule_group_id: formData.rule_group_id || catalogIds.value[0],
        };
      },
      searcherAccordionUseDrawer: true,
      searcherComplicatedOptions: [
        {
          key: 'effective_at',
          label: '开始时间',
          type: 'time',
        },
        {
          key: 'invalid_at',
          label: '结束时间',
          type: 'time',
        },
      ],
      searcherSimpleOptions: [{ key: 'name', label: '规则名称', type: 'string' }],
    }));

    const catalogIdSet = new Set<number>();
    const catalogIds = ref<number[]>([]);
    const catalogConfig = computed(() => ({
      recordName: '规则分类',
      store: props.catalogStore,
      template: 'serve_rule_group',
      mode: 'list',
      params: {
        q: {
          s: ['position asc'],
        },
      },
      pagination: {
        perPage: 999,
        hide: true,
      },
      actions: [
        // { key: 'create', enabled: true },
        // { key: 'update', enabled: true },
        // { key: 'delete', enabled: true },
      ],
      list: {
        scroll: { y: 'auto' },
      },
    }));

    const onClear = () => {
      catalogIdSet.clear();
      catalogIds.value = [];
    };
    const onCatalogShow = (record: VObject) => {
      if (!catalogIdSet.has(record.id)) {
        //多选则注释该行
        catalogIdSet.clear();

        catalogIdSet.add(record.id);
      } else {
        catalogIdSet.delete(record.id);
      }

      catalogIds.value = Array.from(catalogIdSet);
    };

    const visible = ref(false);
    const activeRecord = ref<VObject>({});
    const params = ref<VObject>({});
    const onOpenDrawer = (record: VObject) => {
      visible.value = true;
      activeRecord.value = record;
      params.value = {
        q: {
          'flowable_of_Serve::Pack_type_rule_id_eq': record.id,
          state_in: ['processing', 'created'],
        },
      };
    };

    const drawerVisible = ref(false);
    const onOpenDrawer2 = (record: VObject) => {
      drawerVisible.value = true;
      activeRecord.value = record;
      params.value = {
        q: {
          rule_id_eq: record.id,
        },
      };
    };

    const onOpenN8n = (record: VObject) => {
      if (record.model_payload?.n8n_workflow)
        window.open(
          `${process.env.VUE_APP_PATH}/n8n/workflow/${record.model_payload.n8n_workflow}`,
          '_blank',
        );
    };

    return {
      ...toRefs(props),
      config,
      onOpenDrawer,
      visible,
      activeRecord,
      params,
      catalogConfig,
      catalogTaIndexView,
      onCatalogShow,
      catalogIds,
      onClear,
      onOpenDrawer2,
      drawerVisible,
      // tabs,
      // onIndex,
      onOpenN8n,
    };
  },
});

export default ComServeRulesIndex;
</script>

<template lang="pug">
.com-serve-manage-rules-index.iest__table__skin.flex
  .left.px-4.pt-4.rounded-lg.bg-white.mr-4
    TaIndexView.mr-4.flex-shrink-0.ta-index-view(
      ref='catalogTaIndexView',
      :config='catalogConfig',
      @onShow='onCatalogShow',
      :style='`width:${sidebarWidth}px`'
    )
      template(#card='{ record, actions }')
        ComServeRuleCatalogCard.cursor-pointer(
          :record='record',
          :actions='actions',
          :ids='catalogIds'
        )
      template(#header)
        .flex.py-4.items-center
          .text-base.font-medium.text-black.mr-1 {{ catalogConfig.recordName }}
          .refresh.rounded-full.bg-primary-100.flex.items-center.justify-center.cursor-pointer(
            :class='{ refreshDisabled: catalogIds.length === 0 }',
            @click='onClear'
          )
            TaIcon.w-14px.h-14px.text-primary-600(type='outline/refresh')

  .right.px-4.pt-4.rounded-lg.bg-white.flex-grow
    TaIndexView.ta-index-view-skin(:config='config')
      template(#bodyCell='{ record, column, text }')
        template(v-if='column.dataIndex[0] === "processing_packs_count"')
          ComColorfulLabel.px-10px.py-2px.cursor-pointer(
            :label='text',
            color='blue',
            @click.stop='onOpenDrawer(record)'
          )
        template(v-else-if='column.dataIndex[0] === "total_packs_count"')
          ComColorfulLabel.px-10px.py-2px.cursor-pointer(
            :label='text',
            color='blue',
            @click.stop='onOpenDrawer2(record)'
          )
        template(v-else-if='column.dataIndex[0] === "orgs"')
          .text-sm {{ record.orgs?.map(org => org.name)?.join(',') }}
        template(v-else-if='column.dataIndex[0] === "n8n"')
          TaIconTooltip.icon(
            v-if='record.model_payload?.n8n_workflow',
            icon='SlidersOutlined',
            tips='Agent 工作流'
            @click.stop='onOpenN8n(record)',
          )
          .text-sm(v-else) {{ ' ' }}
    ComInstanceDrawer(v-model:visible='visible', title='待审核', :params='params', width='900')
      template(#drawer-header)
        ComServeRuleInfoCard(:record='activeRecord')
    ComServeRuleShowDrawer(
      v-if='drawerVisible',
      v-model:visible='drawerVisible',
      :title='`${activeRecord.name} | 已发送批次`',
      width='1100',
      :params='params',
      :ruleStore='store'
    )
      template(#drawer-header)
        ComServeRuleInfoCard.mb-4(:record='activeRecord')
</template>

<style lang="stylus" scoped>
.com-serve-manage-rules-index
  height 100%
  width 100%
  .refresh
    width 18px
    height @width
  .refreshDisabled
    background transparent
    >div
      color #111928
      animation rotate 1s linear
</style>
