<script lang='ts'>
import { ref, defineComponent, toRefs, computed } from 'vue';
import ComServePacksIndex from '../packs/ComServePacksIndex.vue';

const ComServeRuleShowDrawer = defineComponent({
  name: 'ComServeRuleShowDrawer',
  components: { ComServePacksIndex },
  props: {
    visible: { type: Boolean, default: false },
    ruleStore: { type: Object, required: true },
    params: { type: Object, default: () => ({}) },
  },
  setup(props, { emit }) {
    const localVisible = computed({
      get() {
        return props.visible;
      }, set(val: boolean) {
        emit('update:visible', val)
      }
    })

    const packStore = props.ruleStore.model.createPackStore(props.params)
    return {
      ...toRefs(props),
      localVisible,
      packStore,
    };
  },
});
export default ComServeRuleShowDrawer;
</script>

<template lang="pug">
TaNoPaddingDrawer(
  v-if='localVisible'
  v-model:visible="localVisible"
)
  .h-full.flex.flex-col.iest__table__skin.p-3.bg-gray-100
    slot(name='drawer-header')
    ComServePacksIndex(
      :store='packStore',
      :params='params',
      :showHeader='false'
    )

</template>

<style lang="stylus" scoped></style>
