<script lang='ts'>
import { ref, defineComponent, toRefs } from 'vue';
import ComLabel from '../../global/ComLabel.vue';
import ComColorfulLabel from '../../global/ComColorfulLabel.vue';
const ComServeRelativedTags = defineComponent({
  name: 'ComServeRelativedTags',
  components: { ComLabel, ComColorfulLabel },
  props: {
    tags: { type: Array, default: () => [] }
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
export default ComServeRelativedTags;
</script>

<template lang="pug">
a-popover
  .com-serve-relatived-tags.flex.items-center.space-x-1
    component.px-10px.py-2px(
      v-for='tag in tags.slice(0,2)',
      :is='tag.color ? "ComLabel" : "ComColorfulLabel"'
      style='width:max-content;'
      :label='tag.name',
      :color='tag.color?"white":"blue"',
      :bg='tag.color'
    )
  template(#content)
    .grid.grid-cols-5.gap-2(v-if='tags.length > 2')
      component.px-10px.py-2px(
        class='!w-full justify-center'
        :is='tag.color ? "ComLabel" : "ComColorfulLabel"'
        v-for='tag in tags',
        :label='tag.name',
        :color='tag.color?"white":"blue"',
        :bg='tag.color'
      )

</template>

<style lang="stylus" scoped></style>
