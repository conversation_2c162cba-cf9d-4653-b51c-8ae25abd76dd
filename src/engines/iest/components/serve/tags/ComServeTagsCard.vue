<script lang='ts'>
import { ref, defineComponent, toRefs, computed } from 'vue';
import dayjs from 'dayjs';

const ComServeTagsCard = defineComponent({
  name: 'ComServeTagsCard',
  components: {},
  props: {
    record: {
      type: Object,
      required: true
    }
  },
  setup(props) {
    const rgba = (color: string, opacity = 0.5) => {
      color = color || '#1890ff'
      return `rgba(${parseInt(color.slice(1, 3), 16)}, ${parseInt(color.slice(3, 5), 16)}, ${parseInt(color.slice(5, 7), 16)}, ${opacity})`
    }
    const computedStyle = computed(() => ({
      backgroundColor: rgba(props.record.color, 0.2),
      borderColor: rgba(props.record.color, 1),
    }))

    const data = computed(() => ({
      标签名称: props.record.name,
      关联素材: 0,
    }))

    const createdAt = computed(() => {
      return dayjs(props.record.created_at).format('YYYY.MM.DD')
    })
    return {
      ...toRefs(props),
      computedStyle,
      data,
      createdAt,
    };
  },
});
export default ComServeTagsCard;
</script>

<template lang="pug">
.com-serve-tags-card.p-3.rounded-lg.border-1(
  :style='computedStyle'
)
  .flex.items-center.text-sm.mb-2(v-for='(value,key) in data')
    .text-gray-900.mr-4.flex-shirnk-0 {{ key }}
    .text-gray-700 {{ value }}
  .text-xs 创建时间:{{ createdAt }}

</template>

<style lang="stylus" scoped>
.com-serve-tags-card
  box-shadow 5px -5px 3px 0 v-bind('computedStyle.backgroundColor')
</style>
