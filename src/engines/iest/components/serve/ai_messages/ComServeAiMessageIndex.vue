<script lang="ts">
import { TaIndexViewTabInterface } from '@/components/global/ta-component/TaIndexView/types';
import { VObject } from '@/lib/vails/model';
import { computed, defineComponent, ref, toRefs } from 'vue';
import ComServeAiMessageShow from './ComServeAiMessageShow.vue';

const ComServeAiMessageIndex = defineComponent({
  name: 'ComServeAiMessageIndex',
  components: {
    ComServeAiMessageShow,
  },
  props: {
    store: { type: Object, required: true },
  },
  setup(props, { emit }) {
    const config = computed(() => ({
      recordName: 'AI消息',
      store: props.store,
      // pagination: {
      //   perPage: 15,
      //   showPageSizeChanger: false,
      //   hideOnSinglePage: false,
      //   showSizeChanger: false,
      // }
      template: 'serve_ai_message',
      detail: {
        mode: 'drawer',
        width: '100%',
      },
      mode: 'table',
      // editApi:
      // showCount: true,
      actions: [
        //  { key: 'create', enabled: true },
        //  { key: 'update', enabled: true },
        //  { key: 'delete', enabled: true },
        //  { key: 'import', enabled: true },
        //  { key: 'export', enabled: true },
      ],
      table: {
        scroll: { y: 'auto' },
        // columns: [{ dataIndex: 'id', title: 'ID' }],
      },
      list: {
        scroll: { y: 'auto' },
      },
      // selection: {
      //   showByDefault: false;
      //   showSwitch: false;
      // };
      searcherSimpleOptions: [
        { key: 'name', label: '名称', type: 'string' },
        { key: 'content', label: '内容', type: 'string' },
      ],
    }));

    const statistics = ref({
      key1: 0,
      key2: 0,
    });

    const tabs = computed<TaIndexViewTabInterface[]>(() => [
      {
        key: 'key1',
        label: '标签1',
        num: statistics.value.key1,
        query: {},
      },
      {
        key: 'key2',
        label: '标签2',
        num: statistics.value.key2,
        query: {},
      },
    ]);

    const onIndex = (data: VObject) => {
      statistics.value = data.statistics;
    };

    return {
      ...toRefs(props),
      config,
      tabs,
      onIndex,
    };
  },
});

export default ComServeAiMessageIndex;
</script>

<template lang="pug">
.com-serve-manage-ai-message-index
  TaIndexView(:config='config', @onIndex='onIndex')
    //- template(#card='{ record }')
    //- template(#table)
    //-    a-table-column(title='名称' dataIndex='record')
    //- template(#detail='{ record, onClose }')
    //-   ComServeAiMessageShow(
    //-     v-if='record.id'
    //-     :store='store'
    //-     :extendRoute='`/serve/manage/ai_messages/${record.id}`'
    //-     :editable='editable'
    //-     @afterDelete='onClose'
    //-     @afterExtend='onClose'
    //-   )
</template>

<style lang="stylus" scoped>
.com-serve-manage-ai-message-index
  height 100%
  width 100%
</style>
