<script lang="ts">
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import { defineComponent, ref } from 'vue';

const ComServeMessageCard = defineComponent({
  name: 'ComServeMessageCard',
  props: {
    record: { type: Object, required: true },
    showActions: { type: Boolean, default: true },
    canLike: { type: Boolean, default: true },
    canCollect: { type: Boolean, default: true },
    showTags: { type: Boolean, default: true },
    showCounts: { type: Boolean, default: true },
    showEdit: { type: Boolean, default: false }, // 是否显示编辑按钮
    showDelete: { type: Boolean, default: false }, // 是否显示删除按钮
  },
  emits: ['like', 'collect', 'edit', 'delete'],
  setup(props, { emit }) {
    // 编辑弹窗相关状态
    const editModalVisible = ref(false);
    const editForm = ref({
      name: '',
      content: '',
      description: '',
    });

    // 格式化日期
    const formatDate = (date: string) => {
      if (!date) return '';
      return dayjs(date).format('YYYY-MM-DD HH:mm');
    };

    // 处理点赞事件
    const handleLike = (record: any) => {
      emit('like', record);
    };

    // 处理收藏事件
    const handleCollect = (record: any) => {
      emit('collect', record);
    };

    // 打开编辑弹窗
    const openEditModal = (record: any) => {
      editForm.value = {
        name: record.name || '',
        content: record.content || '',
        description: record.description || '',
      };
      editModalVisible.value = true;
    };

    // 关闭编辑弹窗
    const closeEditModal = () => {
      editModalVisible.value = false;
      editForm.value = {
        name: '',
        content: '',
        description: '',
      };
    };

    // 处理编辑事件
    const handleEdit = async (record: any) => {
      if (record.store) {
        try {
          openEditModal(record);
        } catch (error) {
          message.error('编辑失败');
        }
      } else {
        emit('edit', record);
      }
    };

    // 更新内容
    const handleUpdate = async () => {
      if (!props.record.store) return;

      try {
        // 构建完整的更新数据，包含id
        const updateData = {
          id: props.record.id,
          ...editForm.value,
        };
        await props.record.store.update(updateData);
        message.success('更新成功');
        closeEditModal();
      } catch (error) {
        message.error('更新失败');
      }
    };

    // 处理删除事件
    const handleDelete = async (record: any) => {
      if (record.store) {
        try {
          await record.store.delete(record.id);
          message.success('删除成功');
          // 刷新列表
          record.store.index();
        } catch (error) {
          message.error('删除失败');
        }
      } else {
        emit('delete', record);
      }
    };

    return {
      formatDate,
      handleLike,
      handleCollect,
      handleEdit,
      handleDelete,
      editModalVisible,
      editForm,
      handleUpdate,
      closeEditModal,
    };
  },
});

export default ComServeMessageCard;
</script>

<template lang="pug">
.message-card.bg-white.rounded-lg.p-6.border.border-gray-200.mb-3.transition-all.duration-300.hover_shadow-md
  .message-header.mb-4
    .message-title-row.flex.items-start.mb-3
      .title-tags.flex.gap-2.mr-3(
        v-if='showTags && record.payload?.tags && record.payload.tags.length > 0'
      )
        TaTag.title-tag.bg-purple-50.text-purple-700.border-purple-700(
          v-for='tag in (record.payload?.tags || [])',
          :key='tag'
        ) {{ tag }}
      .message-title.text-base.font-medium.flex-1.leading-relaxed {{ record.name }}

      //- 编辑和删除按钮组
      .action-buttons.flex.space-x-2.ml-2(v-if='showEdit || showDelete')
        a-tooltip(title='编辑内容', v-if='showEdit')
          button.edit-btn.text-gray-500.hover_text-purple-700.transition-colors(
            @click.stop='handleEdit(record)'
          )
            TaIcon(type='EditOutlined')

        a-tooltip(title='删除', v-if='showDelete')
          TaPopoverConfirm(title='删除', content='确认删除该内容？', @confirm='() => handleDelete(record)')
            button.delete-btn.text-gray-500.hover_text-red-500.transition-colors(@click.stop='')
              TaIcon(type='DeleteOutlined')

  .message-content.text-gray-500.overflow-auto.h-20.leading-relaxed.mb-4.pb-4.border-b.border-gray-100 {{ record.content }}
  .message-footer.flex.justify-between.items-center.pt-2
    .creator-info.text-xs.text-gray-400
      span.creator-name {{ record.creator?.name || '系统' }}
      span.separator.mx-2(v-if='record.creator?.name') ·
      span.department(v-if='record.department_names && record.department_names.length > 0') {{ record.department_names.join(',') }}
      span.separator.mx-2(v-if='record.department_names && record.department_names.length > 0') ·
      span.date {{ formatDate(record.created_at) }}
    .action-buttons.flex.gap-6.text-xs(v-if='showActions')
      //- 可点击的收藏按钮
      .collection.flex.items-center.gap-2.cursor-pointer.transition-colors.duration-300(
        v-if='canCollect',
        :class='record.has_star ? "text-purple-700" : "text-gray-400 hover_text-purple-700"',
        @click='handleCollect(record)'
      )
        TaIcon(:type='record.has_star ? "BookFilled" : "BookOutlined"')
        span {{ record.stars_count || 0 }}

      //- 可点击的点赞按钮
      .likes.flex.items-center.gap-2.cursor-pointer.transition-colors.duration-300(
        v-if='canLike',
        :class='record.has_like ? "text-purple-700" : "text-gray-400 hover_text-purple-700"',
        @click='handleLike(record)'
      )
        TaIcon(:type='record.has_like ? "LikeFilled" : "LikeOutlined"')
        span {{ record.likes_count || 0 }}

      //- 使用次数显示（始终显示）
      .usage.flex.items-center.gap-2.text-gray-400(
        v-if='showCounts'
      )
        TaIcon(type='MessageOutlined')
        span {{ record.used_count || 0 }}

      //- 只读的收藏计数
      .collection.flex.items-center.gap-2.text-gray-400(
        v-if='!canCollect && showCounts && record.stars_count > 0'
      )
        TaIcon(type='BookOutlined')
        span {{ record.stars_count || 0 }}

      //- 只读的点赞计数
      .likes.flex.items-center.gap-2.text-gray-400(
        v-if='!canLike && showCounts && record.likes_count > 0'
      )
        TaIcon(type='LikeOutlined')
        span {{ record.likes_count || 0 }}

  //- 编辑弹窗
  a-modal(
    v-model:visible='editModalVisible',
    title='编辑内容',
    @ok='handleUpdate',
    @cancel='closeEditModal',
    :width='800'
  )
    .edit-form.p-4
      .form-item.mb-4
        label.block.text-sm.font-medium.text-gray-700.mb-1 名称
        a-input(v-model:value='editForm.name', placeholder='请输入名称')

      .form-item.mb-4
        label.block.text-sm.font-medium.text-gray-700.mb-1 内容
        a-textarea(v-model:value='editForm.content', placeholder='请输入内容', :rows='6')
</template>

<style lang="stylus" scoped></style>
