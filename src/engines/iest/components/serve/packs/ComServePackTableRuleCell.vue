<script lang="ts">
import { ref, defineComponent, toRefs, computed } from 'vue';

const ComServePackTableRuleCell = defineComponent({
  name: 'ComServePackTableRuleCell',
  components: {},
  props: {
    rule: {
      type: Object,
      default: () => ({}),
    },
    source: {
      type: Object,
      default: () => ({}),
    },
  },
  setup(props) {
    enum RuleType {
      'Serve::BidRule' = '招投标',
    }
    const ruleType = computed(() => {
      return RuleType[props.rule.type as keyof typeof RuleType];
    });
    return {
      ...toRefs(props),
      ruleType,
      RuleType,
    };
  },
});
export default ComServePackTableRuleCell;
</script>

<template lang="pug">
.com-serve-pack-table-rule-cell.text-sm.rounded-lg.border-1.border-gray-100.bg-gray-50.p-6px
  template(v-if='ruleType === RuleType["Serve::BidRule"]')
    .flex.items-center.mb-1
      .text-xs.text-gray-500 规则：
      .text-gray-900.flex-grow.w-0.truncate(v-tooltip='{ title: rule.name }') {{ rule.name }}
    .rounded-md.bg-primary-50.border-1.border-primary-300.flex.items-center
      .text-primary-800.leading-none.border-r-1.border-r-primary-300.p-1(
        style='width: min-content',
        v-if='ruleType.length <= 3'
      ) {{ ruleType }}
      .text-primary-800.leading-none.border-r-1.border-r-primary-300.p-1(
        v-else,
        style='width: 2.7em'
      ) {{ ruleType }}
      .text-gray-900.flex-grow.w-0.px-1.ellipsis-2(v-tooltip='{ title: source.name }') {{ source?.name }}
  template(v-else)
    .text-xs.text-gray-500.mb-1 规则：
    .text-gray-900.ellipsis-2 {{ rule.name }}
</template>

<style lang="stylus" scoped>
.com-serve-pack-table-rule-cell
  .ellipsis-2
    overflow hidden
    text-overflow ellipsis
    display -webkit-box
    -webkit-line-clamp 2
    -webkit-box-orient vertical
</style>
