<script lang="ts">
import { computed, defineComponent, toRefs } from 'vue';
import ComServeMessagesIndex from '../messages/ComServeMessagesIndex.vue';

const ComServePackShowDrawer = defineComponent({
  name: 'ComServePackShowDrawer',
  components: { ComServeMessagesIndex },
  props: {
    visible: { type: Boolean, default: false },
    packStore: { type: Object, required: true },
    params: { type: Object, default: () => ({}) },
  },
  setup(props, { emit }) {
    const localVisible = computed({
      get() {
        return props.visible;
      },
      set(val: boolean) {
        emit('update:visible', val);
      },
    });
    const msgStore = props.packStore.model.createMessageStore(props.params);
    return {
      ...toRefs(props),
      localVisible,
      msgStore,
    };
  },
});
export default ComServePackShowDrawer;
</script>

<template lang="pug">
TaNoPaddingDrawer(v-if='localVisible', v-model:visible='localVisible')
  .h-full.flex.flex-col.iest__table__skin.p-3.bg-gray-100
    slot(name='drawer-header')
    ComServeMessagesIndex(:store='msgStore', :params='params')
</template>

<style lang="stylus" scoped></style>
