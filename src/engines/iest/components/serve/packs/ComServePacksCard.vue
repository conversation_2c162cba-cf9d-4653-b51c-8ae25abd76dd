<script lang='ts'>
import { ref, defineComponent, toRefs, computed } from 'vue';
import ComColorfulLabel from '@/engines/iest/components/global/ComColorfulLabel.vue';
import dayjs from 'dayjs';

const ComServePacksCard = defineComponent({
  name: 'ComServePacksCard',
  components: { ComColorfulLabel },
  props: {
    record: {
      type: Object,
      required: true,
    }
  },
  emits: ['check'],
  setup(props) {
    const sendAt = computed(() => props.record?.send_at ? dayjs(props.record?.send_at).format('YYYY-MM-DD HH:mm') : '-')

    return {
      ...toRefs(props),
      sendAt,
    };
  },
});
export default ComServePacksCard;
</script>

<template lang="pug">
.com-home-packs-card.p-2.bg-gray-50.rounded-lg
  header.flex.items-center.mb-1
    ComColorfulLabel.px-10px.py-2px.flex-shrink-0.mr-2(
      :label='record.stateConfig?.label',
      :color='record.stateConfig?.color'
    )
    .text-gray-900.text-sm.truncate {{ record.activity?.name}}
  section.flex.justify-between.items-center
    .left__info.text-xs.text-gray-400
      div.mb-1 {{record.org_name}} | {{ sendAt }}
      div.pr-1 关联: {{ record.rule?.name || 'AI发送' }} | {{ record.creator?.name}}
    .right.flex.flex-shrink-0(v-if='record.state === "finished"')
      .px-4.rounded-lg.bg-green-50.flex.flex-col.items-center.mr-2(
        @click.stop='$emit("check",{is_read_true:1})'
      )
        .text-xs(style='color:rgba(35, 56, 118, 0.50)') 已读
        .text-sm(style='font-family:DIN Alternate') {{ record.read_count }}
      .px-4.rounded-lg.bg-red-50.flex.flex-col.items-center(
        @click.stop='$emit("check",{is_read_false:1})'
      )
        .text-xs(style='color:rgba(35, 56, 118, 0.50)') 未读
        .text-sm(style='font-family:DIN Alternate') {{ record.unread_count }}

</template>

<style lang="stylus" scoped></style>
