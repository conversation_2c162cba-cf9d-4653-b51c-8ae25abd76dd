<script lang="ts">
import ComBpmInstanceDetailDialog from '@/engines/bpm/components/ComBpmInstanceDetailDialog.vue';
import '@/engines/iest/views/table.styl';
import { VObject } from '@/lib/vails/model';
import { merge } from 'lodash';
import { computed, defineComponent, onMounted, reactive, ref, toRefs } from 'vue';
import ComIestStatisticBlockCard from '../../ComIestStatisticBlockCard.vue';
import ComColorfulLabel from '../../global/ComColorfulLabel.vue';
import ComServePackShowDrawer from './ComServePackShowDrawer.vue';
import ComServePacksShow from './ComServePacksShow.vue';
import ComServePackTableRuleCell from './ComServePackTableRuleCell.vue';

const ComServePacksIndex = defineComponent({
  name: 'ComServePacksIndex',
  components: {
    ComServePacksShow,
    ComIestStatisticBlockCard,
    ComColorfulLabel,
    ComBpmInstanceDetailDialog,
    ComServePackShowDrawer,
    ComServePackTableRuleCell,
  },
  props: {
    store: { type: Object, required: true },
    params: { type: Object, default: () => ({}) },
    showHeader: { type: Boolean, default: true },
  },
  setup(props, { emit }) {
    const state = reactive({
      totalCount: 0,
      pending: 0,
      sending: 0,
      finished: 0,
    });

    const statisticData = computed(() => [
      {
        name: '批次总数',
        value: state.totalCount,
        color: 'gray',
        icon: 'info',
        params: {},
      },
      {
        name: '待审核',
        value: state.pending,
        color: 'blue',
        icon: 'arrows-repeat',
        params: {
          q: {
            state_eq: 'pending',
          },
        },
      },
      {
        name: '待发送',
        value: state.sending,
        color: 'yellow',
        icon: 'clock',
        params: {
          q: {
            state_eq: 'sending',
          },
        },
      },
      {
        name: '已发送',
        value: state.finished,
        color: 'green',
        icon: 'file-check',
        params: {
          q: {
            state_eq: 'finished',
          },
        },
      },
    ]);

    const config = computed(() => ({
      recordName: '消息批次',
      store: props.store,
      template: 'serve_pack',
      params: merge({ q: { s: ['created_at desc'] } }, props.params),
      // detail: {
      //   mode: 'auto',
      //   mode: 'drawer',
      //   width: '1100px',
      // },
      mode: 'table',
      actions: [
        // { key: 'create', enabled: false },
        { key: 'update', enabled: true },
        { key: 'delete', enabled: true },
        // { key: 'import', enabled: true },
        // { key: 'export', enabled: true },
      ],
      table: {
        scroll: { y: 'auto' },
      },
      searcherSimpleOptions: [
        { key: 'activity_name', label: '素材名称', type: 'string' },
        { key: 'org_name', label: '区域', type: 'string' },
        {
          key: 'rule_name',
          label: '关联名称',
          type: 'string',
        },
      ],
      searcherAccordionUseDrawer: true,
      searcherComplicatedOptions: [
        {
          key: 'activity_name',
          label: '素材名称',
          type: 'string',
        },
        {
          key: 'rule_name',
          label: '关联名称',
          type: 'string',
        },
        {
          key: 'send_at',
          label: '发送时间',
          type: 'time',
        },
        {
          key: 'state',
          label: '状态',
          type: 'string',
          hide_custom: true,
          options: [
            {
              label: '待审核',
              query: { state_eq: 'pending' },
            },
            {
              label: '待发送',
              query: { state_eq: 'sending' },
            },
            {
              label: '已发送',
              query: { state_eq: 'finished' },
            },
            {
              label: '已终止',
              query: { state_eq: 'terminated' },
            },
          ],
        },
      ],
    }));

    const fetchStatisticData = () => {
      if (!props.showHeader) return;
      props.store
        .sendCollectionAction({
          action: 'list_index',
          config: {
            data: {
              collection_stat_condition: {
                items: [
                  {
                    key: 'state',
                    caculator: {
                      type: 'caculation',
                      caculations: [
                        { name: 'pending', filter: { state_eq: 'pending' }, method: 'count' },
                        { name: 'sending', filter: { state_eq: 'sending' }, method: 'count' },
                        { name: 'finished', filter: { state_eq: 'finished' }, method: 'count' },
                      ],
                    },
                  },
                ],
              },
            },
          },
        })
        .then((res: any) => {
          try {
            const { pending, sending, finished } = res.data.statistics.state;
            state.pending = pending;
            state.sending = sending;
            state.finished = finished;
          } catch (e) {
            console.error(e);
          }
        });
    };

    onMounted(fetchStatisticData);
    const onIndex = (data: VObject) => {
      state.totalCount = props.store.totalCount.value > 0 ? props.store.totalCount.value : 0;
      fetchStatisticData();
    };

    const instanceVisible = ref(false);
    const activeRecord = ref<any>({});
    const onShow = (record: any) => {
      activeRecord.value = record;
      instanceVisible.value = true;
    };

    const params = ref({});
    const drawerVisible = ref(false);
    const onDrawerOpen = (record: any, type: string) => {
      if (type.includes('read')) {
        type = type.includes('unread') ? 'is_read_false' : 'is_read_true';
        params.value = {
          q: {
            pack_id_eq: record.id,
            [type]: 1,
          },
        };
      } else if (type === 'failed_count') {
        params.value = {
          q: {
            pack_id_eq: record.id,
            state_eq: 'failed',
          },
        };
      } else {
        params.value = {
          q: {
            pack_id_eq: record.id,
          },
        };
      }

      activeRecord.value = record;
      drawerVisible.value = true;
    };

    const calcReachPercent = computed(() => (unread: number, read: number) => {
      unread = Number(unread || 0);
      read = Number(read || 0);
      const total = unread + read;
      if (total === 0) return 0;
      return (read / total) * 100;
    });
    return {
      ...toRefs(props),
      config,
      onIndex,
      statisticData,
      instanceVisible,
      onShow,
      activeRecord,
      params,
      onDrawerOpen,
      drawerVisible,
      calcReachPercent,
    };
  },
});

export default ComServePacksIndex;
</script>

<template lang="pug">
.com-serve-manage-packs-index.iest__table__skin.flex.flex-col
  .statistics.grid.grid-cols-4.gap-4.mb-4(v-if='showHeader')
    ComIestStatisticBlockCard(
      v-for='item in statisticData',
      :label='item.name',
      :value='item.value',
      :color='item.color',
      :icon='item.icon'
    )
  .px-4.pt-4.bg-white.flex-grow.h-0.rounded-lg
    TaIndexView.ta-index-view-skin(:config='config', @onIndex='onIndex', @onShow='onShow')
      template(#bodyCell='{ record, column, text }')
        ComColorfulLabel.px-10px.py-2px.flex-shrink-0(
          v-if='column.dataIndex[0] === "state"',
          :label='record.stateConfig?.label',
          :color='record.stateConfig?.color'
        )
        template(v-else-if='column.dataIndex[0] === "send_users_count"')
          .grid.gap-1
            ComColorfulLabel.px-10px.py-2px.cursor-pointer.whitespace-nowrap(
              :label='`发送：${text || 0}`',
              color='gray',
              @click.stop='onDrawerOpen(record, "send_users_count")'
            )
            ComColorfulLabel.px-10px.py-2px.cursor-pointer.whitespace-nowrap(
              v-if='record.failed_count',
              :label='`失败：${record.failed_count}`',
              color='red',
              @click.stop='onDrawerOpen(record, "failed_count")'
            )
            ComColorfulLabel.px-10px.py-2px.cursor-pointer.whitespace-nowrap(
              :label='`已读：${record.read_count || 0}`',
              color='blue',
              @click.stop='onDrawerOpen(record, "read_count")'
            )
            ComColorfulLabel.px-10px.py-2px.cursor-pointer.whitespace-nowrap(
              :label='`未读：${record.unread_count || 0}`',
              color='yellow',
              @click.stop='onDrawerOpen(record, "unread_count")'
            )
        ComServePackTableRuleCell.w-48(
          v-else-if='column.dataIndex.join(".") === "rule.name"',
          :rule='record.rule',
          :source='record.source'
        )
        .text-sm(
          v-else-if='column.dataIndex[0] === "reach_percent"',
          :class='{ "text-red-500": calcReachPercent(record.unread_count, record.read_count) > 80, "text-red-500": calcReachPercent(record.unread_count, record.read_count) <= 20 }'
        ) {{ `${calcReachPercent(record.unread_count, record.read_count).toFixed(1)}%` }}
    ComBpmInstanceDetailDialog(
      v-if='instanceVisible && activeRecord.create_instance_id',
      v-model:visible='instanceVisible',
      :instanceId='activeRecord.create_instance_id'
    )
    ComServePackShowDrawer(
      v-if='drawerVisible',
      v-model:visible='drawerVisible',
      :packStore='store',
      :title='activeRecord.name',
      width='900',
      :params='params'
    )
</template>

<style lang="stylus" scoped>
.com-serve-manage-packs-index
  height 100%
  width 100%
</style>
