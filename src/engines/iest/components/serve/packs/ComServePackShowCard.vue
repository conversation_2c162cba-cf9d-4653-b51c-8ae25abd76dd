<script lang="ts">
import { ref, defineComponent, toRefs, computed, watch } from 'vue';
import ComColorfulLabel from '@/engines/iest/components/global/ComColorfulLabel.vue';
import { ServePackModel } from '@/engines/iest/serve-core/models/serve/manage/packs';
import ComServeActivitiesCardForAi from '../activities/ComServeActivitiesCardForAi.vue';

const ComServePackShowCard = defineComponent({
  name: 'ComServePackShowCard',
  components: { ComColorfulLabel, ComServeActivitiesCardForAi },
  props: {
    record: {
      type: Object,
      required: true,
    },
    msgStore: {
      type: Object,
      required: true,
    },
  },
  setup(props) {
    const stateConfig = computed(() => ServePackModel.stateMapping()[props.record?.state]);

    return {
      ...toRefs(props),
      stateConfig,
    };
  },
});
export default ComServePackShowCard;
</script>

<template lang="pug">
.com-serve-pack-show-card.rounded-lg.bg-gray-100.border-gray-200.border-1.p-3(
  class='dark:bg-primary-800 border-none'
)
  ComColorfulLabel.px-10px.py-2px.mb-1(
    :label='stateConfig?.label',
    :color='stateConfig?.color'
  )
  .text__wrapper.space-y-1.mb-6px
    .text-sm.text-gray-900.font-semibold(class='dark:text-white') {{ record.name }}
    .text-xs.text-gray-700(class='dark:text-white') {{record.org?.name}}
    .text-xs.text-gray-500(class='dark:text-white')
      span.pr-3 关联
      span.underline {{ record.rule?.name || 'AI发送' }}

  ComServeActivitiesCardForAi.mb-6px(
    v-if='record.activity?.id',
    :record='record.activity',
    :disabled='true',
  )

  .w-full.flex.justify-center
    .grid.grid-cols-3.gap-2(style='width:fit-content')
      .item.flex.flex-col.items-center
        .text-base.text-gray-500.font-semibold(
          class='dark:text-white'
        )
          | {{ record.send_users_count}}
        .text-xs.text-gray-400 发送数量
      .item.flex.flex-col.items-center
        .text-base.text-gray-500.font-semibold(
          class='dark:text-white'
        )
          | {{ record.read_count}}
        .text-xs.text-gray-400 已读
      .item.flex.flex-col.items-center
        .text-base.text-gray-500.font-semibold(
          class='dark:text-white'
        )
          | {{ record.unread_count}}
        .text-xs.text-gray-400 未读

</template>

<style lang="stylus" scoped>
.com-serve-pack-show-card
  .item > div
    font-family "DIN Alternate"
</style>
