<script lang="ts">
import TaAttachments from '@/components/global/ta-component/file/TaAttachments.vue';
import { useCable } from '@/engines/base/channels/useCable';
import ComColorfulLabel from '@/engines/iest/components/global/ComColorfulLabel.vue';
import { ServeManageRulesApi } from '@/engines/iest/serve-core/apis/serve/manage/rules.api';
import { ServeRuleModel } from '@/engines/iest/serve-core/models/serve/manage/rules';
import '@/engines/iest/views/table.styl';
import { VStore } from '@/lib/vails/store/index';
import dayjs from 'dayjs';
import { computed, defineComponent, onMounted, PropType, reactive, ref, toRefs } from 'vue';
import ComServeMessageContent from './ComServeMessageContent.vue';
import ComServeMessagesShow from './ComServeMessagesShow.vue';

const ComServeMessagesIndex = defineComponent({
  name: 'ComServeMessagesIndex',
  components: {
    ComServeMessagesShow,
    ComColorfulLabel,
    TaAttachments,
    ComServeMessageContent,
  },
  props: {
    store: { type: Object as PropType<VStore>, required: true },
    showHeader: { type: Boolean, default: true },
    params: { type: Object, default: () => ({}) },
    template: { type: String, default: 'serve_message' },
    needStatistic: { type: Boolean, default: false },
  },
  setup(props, { emit }) {
    const taIndexView = ref<any>(null);
    const showDetailModal = ref(false);
    const currentRecord = ref(null);
    const ruleOptions = ref<any[]>([]);
    const ruleStore = new VStore(new ServeManageRulesApi(), ServeRuleModel);

    // 获取规则列表用于筛选
    onMounted(() => {
      ruleStore.index({ per_page: 100, q: { s: ['position asc'] } }).then(() => {
        const rules = ruleStore.records.value || [];
        ruleOptions.value = rules.map((rule: any) => ({
          label: rule.name,
          query: { rule_id_eq: rule.id },
        }));
      });
    });

    const extra = props.store.extra;
    extra.cable_key = 'serve_messages';
    useCable(props.store, {
      taIndexViewRef: taIndexView,
      callback: {
        afterUpdate: () => emit('afterUpdate'),
      },
    });

    const state = reactive({
      total_count: props.store.totalCount,
      read_count: 0,
      unread_count: 0,
      this_week_count: 0,
      last_week_count: 0,
      this_month_count: 0,
      last_month_count: 0,
    });
    // 规则筛选选项
    const ruleFilterOption = computed(() => {
      // 如果规则列表为空，则不显示规则筛选选项
      if (ruleOptions.value.length === 0) {
        return null;
      }

      return {
        label: '规则筛选',
        type: 'select',
        key: 'rule_id',
        group: '基础筛选',
        options: ruleOptions.value,
      };
    });

    // 复杂筛选选项
    const complicatedOptions = computed(() => {
      const options: any[] = [
        {
          label: '阅读状态',
          type: 'select',
          key: 'is_read',
          group: '基础筛选',
          options: [
            {
              label: '已读',
              query: { is_read_eq: true },
            },
            {
              label: '未读',
              query: { is_read_eq: false },
            },
          ],
        },
      ];

      // 如果规则筛选选项存在，则添加到选项列表中
      if (ruleFilterOption.value) {
        options.push(ruleFilterOption.value);
      }

      // 添加其他筛选选项
      options.push(
        {
          label: '发送时间',
          type: 'time',
          key: 'send_at',
          group: '基础筛选',
        } as any,
        {
          label: '创建时间',
          type: 'time',
          key: 'created_at',
          group: '基础筛选',
        } as any,
        {
          label: '发送状态',
          type: 'select',
          key: 'state',
          group: '基础筛选',
          options: [
            {
              label: '发送成功',
              query: { state_eq: 'successed' },
            },
            {
              label: '发送失败',
              query: { state_eq: 'failed' },
            },
          ],
        },
      );

      return options;
    });

    const config = computed(() => ({
      recordName: '消息管理',
      store: props.store,
      template: props.template,
      params: props.params,
      detail: {
        // mode: 'drawer',
        // width: '1100px',
      },
      mode: 'table',
      actions: [
        { key: 'create', enabled: false },
        { key: 'update', enabled: false },
        { key: 'delete', enabled: false },
        { key: 'import', enabled: true },
        { key: 'export', enabled: true },
      ],
      table: {
        scroll: { y: 'auto' },
        onRow: (record: any) => {
          return {
            onClick: () => handleRowClick(record),
          };
        },
      },
      searcherAccordionUseDrawer: true,
      searcherSimpleOptions: [
        { key: 'pack_creator_name', label: '发送人', type: 'string' },
        { key: 'pack_org_name', label: '区域', type: 'string' },
        { key: 'pack_rule_name', label: '关联', type: 'string' },
        { key: 'user_name', label: '接收人', type: 'string' },
        { key: 'user_account', label: '接收人账号', type: 'string' },
      ],
      searcherComplicatedOptions: complicatedOptions.value,
    }));

    const handleRowClick = (record: any) => {
      currentRecord.value = record;
      showDetailModal.value = true;

      // 加载详细信息
      if (record.id) {
        props.store.find(record.id);
      }
    };

    const handleModalClose = () => {
      showDetailModal.value = false;
    };

    const fetchStatisticData = () => {
      props.store
        .sendCollectionAction({
          action: 'list_index',
          config: {
            data: {
              collection_stat_condition: {
                items: [
                  {
                    key: 'msg',
                    caculator: {
                      type: 'caculation',
                      caculations: [
                        {
                          name: 'this_week',
                          filter: {
                            send_at_gteq: dayjs().startOf('week'),
                            send_at_lt: dayjs().endOf('week'),
                          },
                          method: 'count',
                        },
                        {
                          name: 'last_week',
                          filter: {
                            send_at_gteq: dayjs().subtract(1, 'week').startOf('week'),
                            send_at_lt: dayjs().subtract(1, 'week').endOf('week'),
                          },
                          method: 'count',
                        },
                        {
                          name: 'this_month',
                          filter: {
                            send_at_gteq: dayjs().startOf('month'),
                            send_at_lt: dayjs().endOf('month'),
                          },
                          method: 'count',
                        },
                        {
                          name: 'last_month',
                          filter: {
                            send_at_gteq: dayjs().subtract(1, 'month').startOf('month'),
                            send_at_lt: dayjs().subtract(1, 'month').endOf('month'),
                          },
                          method: 'count',
                        },
                        { name: 'read_count', filter: { is_read_true: 1 }, method: 'count' },
                        { name: 'unread_count', filter: { is_read_false: 1 }, method: 'count' },
                      ],
                    },
                  },
                ],
              },
            },
          },
        })
        .then((res: any) => {
          const statistic = res.data?.statistics?.msg;
          if (statistic) {
            const { this_week, last_week, this_month, last_month, read_count, unread_count } =
              statistic;
            state.this_week_count = this_week;
            state.last_week_count = last_week;
            state.this_month_count = this_month;
            state.last_month_count = last_month;
            state.read_count = read_count;
            state.unread_count = unread_count;
          }
        });
    };

    const onIndex = () => {
      if (props.needStatistic) {
        fetchStatisticData();
      }
      // emit('afterIndex');
    };

    const calcDeltaPercent = (current: number, last: number) => {
      if (last === 0) {
        return {
          value: 0,
          fmt: `↑0%`,
        };
      }
      const delta = current - last;
      const percent = (delta / last) * 100;

      return {
        value: percent,
        fmt: percent >= 0 ? `↑${percent.toFixed(1)}%` : `↓${percent.toFixed(1)}%`,
      };
    };

    return {
      ...toRefs(props),
      config,
      onIndex,
      state,
      calcDeltaPercent,
      taIndexView,
      showDetailModal,
      currentRecord,
      handleModalClose,
      handleRowClick,
      ruleOptions,
    };
  },
});

export default ComServeMessagesIndex;
</script>

<template lang="pug">
.com-serve-manage-messages-index.iest__table__skin.flex.flex-col
  .dashboard.rounded-lg.bg-white.p-4.flex.items-center.justify-between.mb-4(
    v-if='needStatistic',
    class='dark:bg-transparent dark:text-white'
  )
    .item.px-4.flex.items-center.flex-grow
      .left.mr-auto
        .text-base.leading-tight.text-gray-500.mb-1 消息发送总数
        .text-2xl.leading-tight.text-gray-900.font-semibold {{ state.total_count }}
      .right.w-136px.h-57px
        img.w-full.h-full(src='https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iest/Chart.png')
    .item.px-4.flex-grow.flex.flex-col.items-center
      .text-sm.text-gray-500 本周发送
      .flex.mb-6px.items-center
        .text-2xl.leading-tight.text-gray-900.font-semibold.mr-2 {{ state.this_week_count }}
        ComColorfulLabel.px-10px.py-2px(
          :label='calcDeltaPercent(state.this_week_count, state.last_week_count).fmt',
          :color='calcDeltaPercent(state.this_week_count, state.last_week_count).value >= 0 ? "green" : "red"'
        )
      .text-sm.text-gray-500 与上周相比
    .item.px-4.flex-grow.flex.flex-col.items-center
      .text-sm.text-gray-500 本月发送
        .flex.mb-6px.items-center
          .text-2xl.leading-tight.text-gray-900.font-semibold.mr-2 {{ state.this_month_count }}
          ComColorfulLabel.px-10px.py-2px(
            :label='calcDeltaPercent(state.this_month_count, state.last_month_count).fmt',
            :color='calcDeltaPercent(state.this_month_count, state.last_month_count).value >= 0 ? "green" : "red"'
          )
      .text-sm.text-gray-500 与上月相比
    .item.px-4.flex-grow
      .text-base.text-gray-900.font-medium.mb-1 读取情况
      .legends.flex.items-center.space-x-4.mb-1
        .legend.flex.items-center
          .w-2.h-2.rounded-full.bg-blue-500.mr-1
          .text-sm.text-gray-500.mr-1 已读
          .text-sm.text-gray-900.font-semibold(style='font-family: DIN Alternate') {{ state.read_count }}
        .legend.flex.items-center
          .w-2.h-2.rounded-full.bg-gray-200.mr-1
          .text-sm.text-gray-500.mr-1 未读
          .text-sm.text-gray-900.font-semibold(style='font-family: DIN Alternate') {{ state.unread_count }}
      a-progress(
        :percent='!state.total_count || Math.floor((state.read_count / state.total_count) * 100)',
        strokeColor='#3b82f6',
        trailColor='#e5e7eb'
      )
  .px-4.pt-4.bg-white.flex-grow.h-0.rounded-lg(class='dark:bg-transparent dark:text-white')
    TaIndexView.ta-index-view-skin(
      ref='taIndexView',
      :config='config',
      :showHeader='showHeader',
      @onIndex='onIndex',
      @onShow='handleRowClick'
    )
      template(#bodyCell='{ record, column, text }')
        ComColorfulLabel.px-10px.py-2px(
          v-if='column.dataIndex[0] === "is_read"',
          :label='record.is_read ? "已读" : "未读"',
          :color='record.is_read ? "green" : "red"'
        )
        .text-sm(v-else-if='column.dataIndex[0] === "sender"') {{ record.sender?.name || '系统' }}

  a-modal(
    :visible='showDetailModal',
    :width='1100',
    @cancel='handleModalClose',
    :destroyOnClose='true',
    :title='currentRecord?.name || "消息详情"',
    :footer='null',
    centered
  )
    .message-modal-content(v-if='showDetailModal && currentRecord')
      ComServeMessageContent(:record='currentRecord')
</template>

<style lang="stylus" scoped>
.com-serve-manage-messages-index
  height 100%
  width 100%
  .dashboard > .item + .item
    border-left 1px solid #E5E7EB
.message-modal-content
  padding 20px
  max-height 80vh
  overflow-y auto
</style>
