<script lang="ts">
import { AuthSessionApi } from '@/engines/login/apis/auth/session.api';
import { VObject } from '@/lib/vails/model';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import { computed, defineComponent, onMounted, ref, watch } from 'vue';
import ComServeMessageCard from '../common/ComServeMessageCard.vue';

// 标签数据
const tags = [
  { id: '1', name: '节日提醒', count: 0 },
  { id: '2', name: '廉洁教育', count: 0 },
  { id: '3', name: '风险防控', count: 0 },
  { id: '4', name: '警示教育', count: 0 },
];

const ComServeAiMessageSquaresIndex = defineComponent({
  name: 'ComServeAiMessageSquaresIndex',
  components: {
    ComServeMessageCard,
  },
  props: {
    store: { type: Object, required: true },
    orgsStore: { type: Object, required: true },
    ruleGroupsStore: { type: Object, required: true },
  },
  setup(props) {
    const searchQuery = ref('');
    const selectedDistricts = ref<string[]>([]);
    const selectedRuleGroups = ref<string[]>([]);
    const selectedTags = ref<string[]>([]);
    const activeFilterTab = ref('all');
    const sortBy = ref('latest');
    // 修改为响应式数据，而不是常量
    const districts = ref<any[]>([]);
    const ruleGroups = ref<any[]>([]);
    const isLoadingDistricts = ref(false);
    const isLoadingRuleGroups = ref(false);
    const info = computed(() => AuthSessionApi.currentUser());
    const filterTabs = [
      { key: 'all', label: '所有消息', icon: 'TagOutlined' },
      { key: 'monthly', label: '本月新增', icon: 'CalendarOutlined' },
      { key: 'collection', label: '我收藏的', icon: 'BookOutlined' },
      { key: 'liked', label: '已赞', icon: 'LikeOutlined' },
    ];

    const records = ref<any[]>([]);

    const config = computed(() => ({
      // 标题名称，显示在页面和提示中
      recordName: '消息广场',

      // 数据存储对象，必填
      store: props.store,

      // 分页配置
      pagination: {
        perPage: 15,
        showPageSizeChanger: false,
        hideOnSinglePage: false,
        showSizeChanger: false,
        position: 'bottom',
      },

      // 模板名称，用于获取表单配置
      template: 'serve_ai_message_square',

      // 显示模式：card-卡片模式 / table-表格模式 / tag-标签模式 / tree-树形模式 / custom-自定义模式
      mode: 'card',

      // 布局配置
      layout: {
        type: 'custom',
        cardGap: 12, // 设置卡片之间的间距为12px
        cardStyle: {
          marginBottom: '12px', // 卡片下方间距
        },
      },

      // 搜索配置：simple-简单搜索 / false-高级搜索
      searcher: {
        simple: false,
      },

      // 表格滚动配置
      tableConfig: {
        scroll: { y: 'calc(100vh - 300px)' },
      },
    }));

    // 获取区县数据
    const fetchDistricts = async () => {
      try {
        isLoadingDistricts.value = true;

        // 使用 orgsStore 获取组织数据，检查需要哪些参数
        const response = await props.orgsStore.index({
          q: { parent_name_eq: '杭州市' },
        });

        // 获取最终要使用的records
        const recordsToUse =
          response && response.records ? response.records : props.orgsStore.records;

        // 转换为所需格式并保存到districts
        if (recordsToUse && Array.isArray(recordsToUse)) {
          districts.value = recordsToUse.map((org: any) => {
            const messagesCount = Number(org.serve_ai_message_squares_count || 0);

            return {
              id: org.id,
              name: org.name || '未知区县',
              count: messagesCount, // 确保是数字类型
              org,
            };
          });
        } else {
          console.error('获取区县数据格式不正确:', recordsToUse);
          // 如果接口返回格式不对，使用默认数据
          initDefaultDistricts();
        }
      } catch (error) {
        console.error('获取区县数据出错:', error);
        // 出错时使用默认数据
        initDefaultDistricts();
      } finally {
        isLoadingDistricts.value = false;
      }
    };

    // 初始化默认区县数据（当API请求失败时使用）
    const initDefaultDistricts = () => {
      districts.value = [
        { id: '1', name: '上城区', count: 0 },
        { id: '2', name: '下城区', count: 0 },
        { id: '3', name: '江干区', count: 0 },
        { id: '4', name: '拱墅区', count: 0 },
        { id: '5', name: '西湖区', count: 0 },
        { id: '6', name: '滨江区', count: 0 },
        { id: '7', name: '余杭区', count: 0 },
        { id: '8', name: '富阳区', count: 0 },
        { id: '9', name: '临安区', count: 0 },
        { id: '10', name: '钱塘区', count: 0 },
        { id: '11', name: '桐庐县', count: 0 },
        { id: '12', name: '建德市', count: 0 },
        { id: '13', name: '淳安县', count: 0 },
        { id: '14', name: '萧山区', count: 0 },
        { id: '15', name: '临平区', count: 0 },
      ];
    };

    const onIndex = (data: VObject) => {
      records.value = data.records || [];
      // 更新标签计数
      updateTagsCount(data.records);
      // 不再需要更新区县计数，因为已经从API获取
    };

    const updateTagsCount = (records: any[]) => {
      const tagCount = new Map();
      records.forEach(record => {
        record.tags?.forEach((tag: string) => {
          tagCount.set(tag, (tagCount.get(tag) || 0) + 1);
        });
      });
      tags.forEach(tag => {
        tag.count = tagCount.get(tag.name) || 0;
      });
    };

    const handleSearch = () => {
      // ransack 查询参数构建
      const q: any = {
        // 搜索框搜索 name 或 content
        name_or_content_cont: searchQuery.value,
        // 根据创建时间排序
        s: 'created_at desc',
      };

      // 根据排序方式调整
      if (sortBy.value === 'latest') {
        q.s = 'created_at desc';
      } else if (sortBy.value === 'popular') {
        q.s = 'likes_count desc';
      } else if (sortBy.value === 'most_sent') {
        q.s = 'stars_count desc';
      }

      // 区县筛选 - 修改为支持多选
      if (selectedDistricts.value.length > 0) {
        // 使用 org_id_in 进行多选筛选
        q.org_id_in = selectedDistricts.value;
      }

      // 规则组筛选 - 修改为支持多选
      if (selectedRuleGroups.value.length > 0) {
        // 使用 rule_rule_group_id_in 进行多选筛选
        q.rule_rule_group_id_in = selectedRuleGroups.value;
      }

      // 标签筛选
      if (selectedTags.value.length > 0) {
        const selectedTagNames = selectedTags.value
          .map(id => tags.find(t => t.id === id)?.name)
          .filter(Boolean);

        if (selectedTagNames.length > 0) {
          // 用payload_cont传递标签条件
          q.payload_cont = JSON.stringify({
            tags: selectedTagNames,
          });
        }
      }

      // 根据 tab 筛选
      if (activeFilterTab.value === 'monthly') {
        const startOfMonth = new Date();
        startOfMonth.setDate(1);
        startOfMonth.setHours(0, 0, 0, 0);
        q.created_at_gteq = startOfMonth.toISOString();
      } else if (activeFilterTab.value === 'collection') {
        q.star_mark_actions_target_id_eq = info.value.id;
      } else if (activeFilterTab.value === 'liked') {
        q.like_mark_actions_target_id_eq = info.value.id;
      }

      // 调用 store 的 index 方法
      props.store.index({ q });
    };

    watch(
      [searchQuery, activeFilterTab, sortBy, selectedDistricts, selectedRuleGroups, selectedTags],
      () => {
        handleSearch();
      },
      // 添加 { deep: true } 选项以确保能监听到数组内部变化，虽然对 ref<string[]> 可能不是必需的，但更保险
      { deep: true },
    );

    onMounted(() => {
      // 页面加载时获取区县数据和规则组数据
      fetchDistricts();
      fetchRuleGroups();
      // 等数据加载后再查询消息数据
      handleSearch();
    });

    const handleTagClick = (tagId: string) => {
      const index = selectedTags.value.indexOf(tagId);
      if (index > -1) {
        selectedTags.value.splice(index, 1);
      } else {
        selectedTags.value.push(tagId);
      }
    };

    const handleDistrictClick = (districtId: string, count: number) => {
      // 如果区县没有消息，则不进行筛选
      if (count === 0) {
        console.log(`区县 ID=${districtId} 没有消息，跳过筛选`);
        return;
      }

      // 修改为数组操作，支持多选
      const index = selectedDistricts.value.indexOf(districtId);
      if (index > -1) {
        // 如果已选中，则移除
        selectedDistricts.value.splice(index, 1);
      } else {
        // 如果未选中，则添加
        selectedDistricts.value.push(districtId);
      }
    };

    const sortOptions = [
      { value: 'latest', label: '最新发布' },
      { value: 'popular', label: '最多点赞' },
      { value: 'most_sent', label: '最多收藏' },
    ];

    const handleSortChange = (value: string) => {
      sortBy.value = value;
    };

    const formatDate = (date: string) => {
      return dayjs(date).format('YYYY-MM-DD HH:mm');
    };

    // 获取规则组数据
    const fetchRuleGroups = async () => {
      try {
        isLoadingRuleGroups.value = true;

        // 使用 ruleGroupsStore 获取规则组数据
        const response = await props.ruleGroupsStore.index();

        // 获取最终要使用的records
        const recordsToUse =
          response && response.records ? response.records : props.ruleGroupsStore.records;

        // 转换为所需格式并保存到ruleGroups
        if (recordsToUse && Array.isArray(recordsToUse)) {
          ruleGroups.value = recordsToUse.map((ruleGroup: any) => {
            const messagesCount = Number(ruleGroup.statistic?.serve_ai_message_squares_count || 0);

            return {
              id: ruleGroup.id,
              name: ruleGroup.name || '未知规则组',
              count: messagesCount, // 从statistic中获取消息数量
              ruleGroup, // 保存完整对象
            };
          });
        } else {
          console.error('获取规则组数据格式不正确:', recordsToUse);
          // 如果返回格式不对，设置空数组
          ruleGroups.value = [];
        }
      } catch (error) {
        console.error('获取规则组数据出错:', error);
        // 出错时设置空数组
        ruleGroups.value = [];
      } finally {
        isLoadingRuleGroups.value = false;
      }
    };

    const handleRuleGroupClick = (ruleGroupId: string, count: number) => {
      // 如果规则组没有消息，则不进行筛选
      if (count === 0) {
        console.log(`规则组 ID=${ruleGroupId} 没有消息，跳过筛选`);
        return;
      }

      // 修改为数组操作，支持多选
      const index = selectedRuleGroups.value.indexOf(ruleGroupId);

      if (index > -1) {
        // 如果已选中，则移除
        selectedRuleGroups.value.splice(index, 1);
      } else {
        // 如果未选中，则添加
        selectedRuleGroups.value.push(ruleGroupId);
      }
    };

    // 添加点赞操作
    const handleLike = async (record: any) => {
      try {
        // 如果已经点赞，则取消点赞
        if (record.has_like) {
          await props.store.sendMemberAction({
            id: record.id,
            action: 'unmark',
            config: {
              data: {
                flag: 'like',
              },
            },
          });
          // 更新本地状态
          record.has_like = false;
          record.likes_count = (record.likes_count || 0) - 1;
        } else {
          // 否则执行点赞操作
          await props.store.sendMemberAction({
            id: record.id,
            action: 'mark',
            config: {
              data: {
                flag: 'like',
              },
            },
          });
          // 更新本地状态
          record.has_like = true;
          record.likes_count = (record.likes_count || 0) + 1;
        }
      } catch (error) {
        console.error('点赞操作失败:', error);
        message.error('点赞操作失败，请稍后重试');
      }
    };

    // 添加收藏操作
    const handleCollect = async (record: any) => {
      try {
        // 如果已经收藏，则取消收藏
        if (record.has_star) {
          await props.store.sendMemberAction({
            id: record.id,
            action: 'unmark',
            config: {
              data: {
                flag: 'star',
              },
            },
          });
          // 更新本地状态
          record.has_star = false;
          record.stars_count = (record.stars_count || 0) - 1;
        } else {
          // 否则执行收藏操作
          await props.store.sendMemberAction({
            id: record.id,
            action: 'mark',
            config: {
              data: {
                flag: 'star',
              },
            },
          });
          // 更新本地状态
          record.has_star = true;
          record.stars_count = (record.stars_count || 0) + 1;
        }
      } catch (error) {
        console.error('收藏操作失败:', error);
        message.error('收藏操作失败，请稍后重试');
      }
    };

    return {
      searchQuery,
      selectedDistricts,
      selectedRuleGroups,
      selectedTags,
      activeFilterTab,
      filterTabs,
      config,
      records,
      districts,
      ruleGroups,
      isLoadingDistricts,
      isLoadingRuleGroups,
      tags,
      sortBy,
      sortOptions,
      handleSearch,
      handleTagClick,
      handleDistrictClick,
      handleSortChange,
      onIndex,
      formatDate,
      handleRuleGroupClick,
      handleLike,
      handleCollect,
    };
  },
});

export default ComServeAiMessageSquaresIndex;
</script>

<template lang="pug">
.com-serve-manage-ai-message-squares-index.flex.flex-col.h-screen.w-full(class="bg-[#f5f5f5]")
  .page-header.flex-shrink-0.sticky.top-0.z-10.bg-white.px-6.py-6.border-b.border-gray-200
    .header-content
      h1.text-2xl.font-bold.text-gray-900.mb-1 消息广场
      p.text-sm.text-gray-600 浏览和点赞已发送的消息
  .page-container.flex.flex-1.gap-4.px-6.py-4.min-h-0.overflow-y-auto(class="h-[calc(100vh-100px)]")
    .filter-sidebar.w-80.flex-shrink-0.overflow-y-auto.flex.flex-col.gap-4
      .filter-section.bg-white.rounded-lg.p-4
        .section-title.flex.items-center.mb-4
          TaIcon.mr-2.text-base(type='SearchOutlined')
          span.text-sm.font-medium 搜索消息
        .search-box
          TaInput.search-input.w-full(
            v-model:value='searchQuery',
            placeholder='搜索消息...',
            @press-enter='handleSearch'
          )
            template(#prefix)
              TaIcon(type='SearchOutlined')
            template(#suffix)
              .clear-btn.cursor-pointer.text-gray-400.hover_text-gray-600(v-if='searchQuery', @click='searchQuery = ""')
                TaIcon(type='CloseOutlined')

      .filter-section.bg-white.rounded-lg.p-4
        .section-title.flex.items-center.mb-4
          TaIcon.mr-2.text-base(type='SortAscendingOutlined')
          span.text-sm.font-medium 排序方式
        .sort-select
          a-select.select-with-icon(
            v-model:value='sortBy',
            style='width: 100%',
            @change='handleSortChange'
          )
            a-select-option(
              v-for='option in sortOptions',
              :key='option.value',
              :value='option.value'
            ) {{ option.label }}

      .filter-section.bg-white.rounded-lg.p-4
        .section-title.flex.items-center.mb-4
          TaIcon.mr-2.text-base(type='TagsOutlined')
          span.text-sm.font-medium 规则组
        .tag-list.flex.items-center.justify-center(v-if="isLoadingRuleGroups")
          TaSpin
        .tag-list.grid.grid-cols-2.gap-2(v-else)
          TaTag.cursor-pointer.m-0.px-2.py-1.rounded-full.text-center.truncate(
            v-for='ruleGroup in ruleGroups',
            :key='ruleGroup.id',
            :class="{ 'ant-tag-purple': selectedRuleGroups.includes(ruleGroup.id), 'disabled-tag': ruleGroup.count === 0 }"
            @click='() => handleRuleGroupClick(ruleGroup.id, ruleGroup.count)'
          ) {{ ruleGroup.name }} ({{ ruleGroup.count }})

      .filter-section.bg-white.rounded-lg.p-4
        .section-title.flex.items-center.mb-4
          TaIcon.mr-2.text-base(type='EnvironmentOutlined')
          span.text-sm.font-medium 所属区县
        .tag-list.flex.items-center.justify-center(v-if="isLoadingDistricts")
          TaSpin
        .tag-list.grid.grid-cols-2.gap-2(v-else)
          TaTag.cursor-pointer.m-0.px-2.py-1.rounded-full.text-center.truncate(
            v-for='district in districts',
            :key='district.id',
            :class="{ 'ant-tag-purple': selectedDistricts.includes(district.id), 'disabled-tag': district.count === 0 }"
            @click='() => handleDistrictClick(district.id, district.count)'
          ) {{ district.name }} ({{ district.count }})

    .content-container.flex-1.min-w-0.flex.flex-col.bg-white.rounded-lg.overflow-y-auto
      TaIndexView.index-view.h-full.flex.flex-col(:config='config', @onIndex='onIndex')
        template(#header)
          span
        template(#virtual_layout_tabs)
          .filter-tabs-container.px-6.py-4.border-b.border-gray-200.flex.justify-between.items-center.bg-white
            .left-tabs.flex.gap-2
              TaButton.tab-button.flex.items-center.gap-1.px-4.py-2.rounded-full.text-sm.border.border-transparent(
                v-for='tab in filterTabs',
                :key='tab.key',
                :class="{ 'text-purple-700 bg-purple-50 border-purple-700': activeFilterTab === tab.key, 'text-gray-500 hover_text-purple-700 hover_bg-purple-50': activeFilterTab !== tab.key }",
                @click='activeFilterTab = tab.key',
                type='text'
              )
                TaIcon(:type='tab.icon')
                span {{ tab.label }}
            //- .right-buttons
              TaButton.hot-button(type='primary')
                TaIcon(type='RiseOutlined')
                span 热门消息

        template(#card='{ record }')
          ComServeMessageCard(
            :record="record"
            :showActions="true"
            :canLike="true"
            :canCollect="true"
            :showEdit="false"
            :showDelete="false"
            @like="handleLike"
            @collect="handleCollect"
          )
</template>

<style lang="stylus" scoped>
/* 保留一些复杂或特定的样式，使用Tailwind难以实现的部分 */
.disabled-tag
  color #999
  background #f5f5f5
  border-color #e8e8e8
  cursor not-allowed
  &:hover
    color #999
    border-color #e8e8e8

/* antd相关样式覆盖，保留使用stylus */
:deep(.ant-tag-purple)
  color #722ED1
  background #F9F0FF
  border-color #722ED1

/* 修复hover语法，在pug中将hover:class改为hover_class */
.hover_text-purple-700:hover
  color #722ED1

.hover_bg-purple-50:hover
  background #F9F0FF

.hover_shadow-md:hover
  box-shadow 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)

:deep(.ta-index-view)
  height 100%
  display flex
  flex-direction column
  .ta-index-view-content
    flex 1
    min-height 0
    overflow-y auto
    padding 16px 24px
  .ant-pagination
    flex-shrink 0
    margin 16px 0
    padding 0 24px
    text-align center
</style>
