<script lang="ts">
import { ref, defineComponent, toRefs, inject, Ref, onMounted, onUnmounted, computed } from 'vue';
import { VObject } from '@/lib/vails/model/index';
import { VStore } from '@/lib/vails/store/index';
import { ServeElasticBidProjectsApi } from '@/engines/iest/apis/serve/elastic/bid_projects.api';
import { ServeBidProjectModel } from '@/engines/iest/serve-core/models/serve/user/bid_projects';
import { ComChatConversationLayoutCacheCacheKey } from '@/engines/chat/components/chat/conversations/ComChatConversationLayout.vue';

const ComServeBidProjectsMentionIndex = defineComponent({
  name: 'ComServeBidProjectsMentionIndex',
  components: {},
  props: {
    record: { type: Object, default: () => ({}) },
  },
  setup(props) {
    const cache = inject<Ref<VObject>>(ComChatConversationLayoutCacheCacheKey)!;

    const api = new ServeElasticBidProjectsApi();
    const store = new VStore(api, ServeBidProjectModel);

    const config = computed(() => ({
      recordName: '招投标项目',
      store,
      mode: 'list',
      params: props.record.payload || {},
      pagination: {
        hideOnSinglePage: true,
      },
      list: {
        scroll: {
          y: 'auto',
        },
      },
    }));

    const visibleMap = ref<VObject>({});

    const onShow = (record: VObject) => {
      visibleMap.value[`project-${record.id}`] = true;
    };

    onMounted(() => {
      cache.value.mentionWidth = '580px';
    });

    onUnmounted(() => {
      cache.value.mentionWidth = undefined;
    });

    return {
      ...toRefs(props),
      config,
      visibleMap,
      onShow,
    };
  },
});
export default ComServeBidProjectsMentionIndex;
</script>

<template lang="pug">
.com-serve-bid-projects-mention-index.px-4.h-full
  TaIndexView.h-full(:config='config', @onShow='onShow')
    //- template(#header)
    //-   .empty
    template(#card='{ record, actions }')
      h1 {{ record.name }}
      //-ComBpmprojectCard(:record='record', @dialogClose='actions.silenceRefresh')
      //-ComBpmprojectDetailEasyDialogFromIndex(
        v-if='visibleMap[`project-${record.id}`]',
        v-model:visible='visibleMap[`project-${record.id}`]',
        :project='record',
        @close='actions.silenceRefresh'
      //-)
</template>

<style lang="stylus" scoped>
.dark .com-serve-bid-projects-mention-index
  >>> .ta-index-view-header
    @apply bg-transparent;
  >>> .table-header__title
    @apply text-white;
</style>
