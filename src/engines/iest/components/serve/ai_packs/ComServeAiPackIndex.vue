<script lang="ts">
import { TaIndexViewTabInterface } from '@/components/global/ta-component/TaIndexView/types';
import ComBpmInstanceDetailDialog from '@/engines/bpm/components/ComBpmInstanceDetailDialog.vue';
import { VObject } from '@/lib/vails/model';
import { computed, defineComponent, nextTick, ref, toRefs } from 'vue';
import ComColorfulLabel from '../../global/ComColorfulLabel.vue';
import ComServePackShowDrawer from '../packs/ComServePackShowDrawer.vue';
import ComServePackTableRuleCell from '../packs/ComServePackTableRuleCell.vue';
import ComServeAiPackShow from './ComServeAiPackShow.vue';

const ComServeAiPackIndex = defineComponent({
  name: 'ComServeAiPackIndex',
  components: {
    ComServeAiPackShow,
    ComColorfulLabel,
    ComBpmInstanceDetailDialog,
    ComServePackShowDrawer,
    ComServePackTableRuleCell,
  },
  props: {
    store: { type: Object, required: true },
  },
  setup(props) {
    const config = computed(() => ({
      recordName: '消息发送',
      store: props.store,
      template: 'serve_ai_pack',
      mode: 'table',
      actions: [
        { key: 'create', enabled: true },
        { key: 'update', enabled: true },
        { key: 'delete', enabled: true },
      ],
      detail: {
        width: '100%',
      },
      table: {
        scroll: { y: 'auto' },
      },
      searcherSimpleOptions: [
        { key: 'name', label: '名称', type: 'string' },
        { key: 'rule_name', label: '关联规则名称', type: 'string' },
      ],
    }));

    const statistics = ref({
      finished: 0,
      pending: 0,
      terminated: 0,
      sending: 0,
    });

    const tabs = computed<TaIndexViewTabInterface[]>(() => [
      {
        key: 'pending',
        label: '待审核',
        num: statistics.value?.pending || 0,
        query: { state_eq: 'pending' },
      },
      {
        key: 'sending',
        label: '发送中',
        num: statistics.value?.sending || 0,
        query: { state_eq: 'sending' },
      },
      {
        key: 'finished',
        label: '已完成',
        num: statistics.value?.finished || 0,
        query: { state_eq: 'finished' },
      },
      {
        key: 'terminated',
        label: '已终止',
        num: statistics.value?.terminated || 0,
        query: { state_eq: 'terminated' },
      },
    ]);

    const instanceVisible = ref(false);
    const activeRecord = ref<any>({});
    const onShow = (record: any) => {
      activeRecord.value = record;
      instanceVisible.value = true;
    };

    const params = ref({});
    const drawerVisible = ref(false);

    const onDrawerOpen = (record: any, type: string) => {
      // 清除上一次的参数
      params.value = {};
      nextTick(() => {
        if (type.includes('read')) {
          type = type.includes('unread') ? 'is_read_false' : 'is_read_true';
          params.value = {
            q: {
              pack_id_eq: record.id,
              [type]: 1,
            },
          };
        } else if (type === 'failed_count') {
          params.value = {
            q: {
              pack_id_eq: record.id,
              state_eq: 'failed',
            },
          };
        } else {
          params.value = {
            q: {
              pack_id_eq: record.id,
            },
          };
        }

        activeRecord.value = record;
        drawerVisible.value = true;
      });
    };

    const calcReachPercent = computed(() => (unread: number, read: number) => {
      unread = Number(unread || 0);
      read = Number(read || 0);
      const total = unread + read;
      if (total === 0) return 0;
      return (read / total) * 100;
    });

    const taIndexViewRef = ref();
    const onIndex = (data: VObject) => {
      statistics.value = data.statistics;
    };
    const refresh = () => {
      taIndexViewRef.value?.silenceRefresh?.();
    };
    return {
      ...toRefs(props),
      config,
      tabs,
      onIndex,
      instanceVisible,
      onShow,
      activeRecord,
      params,
      onDrawerOpen,
      drawerVisible,
      calcReachPercent,
      refresh,
      taIndexViewRef,
    };
  },
});

export default ComServeAiPackIndex;
</script>

<template lang="pug">
.com-serve-manage-ai-pack-index.iest__table__skin
  TaIndexView.ta-index-view-skin(
    ref="taIndexViewRef"
    :config='config',
    :tabs='tabs',
    @onIndex='onIndex',
    @onShow='onShow'
  )
    template(#bodyCell='{ record, column, text }')
      ComColorfulLabel.px-10px.py-2px.flex-shrink-0(
        v-if='column.dataIndex[0] === "state"',
        :label='record.stateConfig?.label',
        :color='record.stateConfig?.color'
      )
      template(v-else-if='column.dataIndex[0] === "send_users_count"')
        .grid.gap-1
          ComColorfulLabel.px-10px.py-2px.cursor-pointer.whitespace-nowrap(
            :label='`发送：${text || 0}`',
            color='gray',
            @click.stop='onDrawerOpen(record, column.dataIndex[0])'
          )
          ComColorfulLabel.px-10px.py-2px.cursor-pointer.whitespace-nowrap(
            v-if='record.failed_count',
            :label='`失败：${record.failed_count}`',
            color='red',
            @click.stop='onDrawerOpen(record, "failed_count")'
          )
          ComColorfulLabel.px-10px.py-2px.cursor-pointer.whitespace-nowrap(
            :label='`已读：${record.read_count || 0}`',
            color='blue',
            @click.stop='onDrawerOpen(record, "read_count")'
          )
          ComColorfulLabel.px-10px.py-2px.cursor-pointer.whitespace-nowrap(
            :label='`未读：${record.unread_count || 0}`',
            color='yellow',
            @click.stop='onDrawerOpen(record, "unread_count")'
          )
      ComServePackTableRuleCell.w-48(
        v-else-if='column.dataIndex.join(".") === "rule.name"',
        :rule='record.rule',
        :source='record.source'
      )
      .text-sm(
        v-else-if='column.dataIndex[0] === "reach_percent"',
        :class='{ "text-red-500": calcReachPercent(record.unread_count, record.read_count) > 80, "text-gray-500": calcReachPercent(record.unread_count, record.read_count) <= 20 }'
      ) {{ `${calcReachPercent(record.unread_count, record.read_count).toFixed(1)}%` }}
  ComBpmInstanceDetailDialog(
    v-if='instanceVisible && activeRecord.create_instance_id',
    v-model:visible='instanceVisible',
    :instanceId='activeRecord.create_instance_id',
    @close='refresh'
  )
  ComServePackShowDrawer(
    v-if='drawerVisible',
    v-model:visible='drawerVisible',
    :packStore='store',
    :title='activeRecord.name',
    :width='900',
    :params='params'
  )
</template>

<style lang="stylus" scoped>
.com-serve-manage-ai-pack-index
  height 100%
  width 100%
</style>
