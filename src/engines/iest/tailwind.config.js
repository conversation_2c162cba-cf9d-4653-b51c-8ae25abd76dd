const plugin = require('windicss/plugin');
const colors = require('windicss/colors');

module.exports = {
  antdvThemeDarkVariables: {
    'text-color': 'fade(@white, 65%)',
    'disabled-color': '#A4CAFE',
    'component-background': 'rgba(30, 66, 159, 1)',
    'body-background': '#233876',
    'modal-content-bg': 'white',
    'modal-header-bg': 'white',
    'timeline-dot-bg': 'white',
    'timeline-color': 'white',
    'ropdown-menu-bg': 'rgba(30, 66, 159, 1)',
    'table-bg': 'white',
    'table-body-sort-bg': 'white',
    'table-row-hover-bg': 'white',
    'drawer-bg': 'white',
  },
  darkMode: ['selector', '[data-mode="dark"]'],
  theme: {
    colors: {
      ...colors,
      primary: {
        900: '#233876',
        800: 'rgba(30, 66, 159, 1)',
        700: '#1A56DB',
        300: '#A4CAFE',
        200: '#C3DDFD',
      },
      screen: '#011848',
      'screen-transparent': 'rgba(255, 255, 255, 0.10)',
    },
  },
  plugins: [
    plugin(function ({ addComponents, theme }) {
      addComponents({
        '.iest-card-container': {
          boxShadow: theme('boxShadow.sm'),
          borderRadius: theme('borderRadius.lg'),
          backgroundColor: theme('colors.white'),
          padding: theme('padding.4'),
        },
      });
    }),
  ],
};
