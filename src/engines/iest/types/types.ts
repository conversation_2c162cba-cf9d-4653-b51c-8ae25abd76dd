export interface IestConversation {
  id: number;
}

export interface IestMessage {
  id: number;
}

export interface IestMention {
  id: number;
}

export interface IestOrg {
  id: number;
}

export enum IestMessageType {
  User = 'Iest::Ai::Chat::Messages::User',
  System = 'Iest::Ai::Chat::Messages::System',
}

export enum IestMentionType {
  ServePack = 'Iest::Ai::Chat::Mentions::ServePack',
  SearchBpmInstance = 'Iest::Ai::Chat::Mentions::SearchBpmInstance',
}
