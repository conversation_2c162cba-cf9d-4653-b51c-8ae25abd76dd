import { ServeActivity } from '@/engines/iest/serve-core/types/model';
import { VModel } from '@/lib/vails';

export class ServeActivityModel extends VModel<ServeActivity> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return ServeActivityModel.stateMapping()[this.reactiveRecord.state];
  // });
  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}
