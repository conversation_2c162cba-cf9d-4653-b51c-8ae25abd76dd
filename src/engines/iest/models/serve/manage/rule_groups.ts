import { VModel, VObject } from '@/lib/vails';
import { ServeRuleGroup } from '@/engines/iest/types/model';

export class ServeRuleGroupModel extends VModel<ServeRuleGroup> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return ServeRuleGroupModel.stateMapping()[this.reactiveRecord.state];
  // });

  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}
