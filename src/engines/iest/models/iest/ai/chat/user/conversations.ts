import { VModel, VObject } from '@/lib/vails';
import { IestConversation } from '@/engines/iest/types/types';

export class IestConversationModel extends VModel<IestConversation> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return IestConversationModel.stateMapping()[this.reactiveRecord.state];
  // });
  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}
