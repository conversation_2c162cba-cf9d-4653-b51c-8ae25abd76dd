import { VModel, VObject } from '@/lib/vails';
import { IestTag } from '@/engines/iest/types/model';

export class IestTagModel extends VModel<IestTag> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return IestTagModel.stateMapping()[this.reactiveRecord.state];
  // });

  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}
