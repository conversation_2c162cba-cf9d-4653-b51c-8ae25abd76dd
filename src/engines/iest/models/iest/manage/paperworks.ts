import { VModel } from '@/lib/vails';
import { IestPaperwork } from '@/engines/iest/types/model';

export class IestPaperworkModel extends VModel<IestPaperwork> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return IestPaperworkModel.stateMapping()[this.reactiveRecord.state];
  // });
  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}
