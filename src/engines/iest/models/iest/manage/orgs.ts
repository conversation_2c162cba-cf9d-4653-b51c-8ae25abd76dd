import { VModel, VObject, VStore } from '@/lib/vails';
import { IestOrg } from '@/engines/iest/types/types';
import { ServeManageMessagesApi } from '@/engines/iest/serve-core/apis/serve/manage/messages.api';
import { ServeMessageModel } from '@/engines/iest/serve-core/models/serve/manage/messages';
import { ServeManageRulesApi } from '@/engines/iest/serve-core/apis/serve/manage/rules.api';
import { ServeRuleModel } from '@/engines/iest/serve-core/models/serve/manage/rules';

export class IestOrgModel extends VModel<IestOrg> {
  static createMessageStore(params = {}) {
    return new VStore(new ServeManageMessagesApi(params), ServeMessageModel);
  }
  static createRuleStore(params = {}) {
    return new VStore(new ServeManageRulesApi(params), ServeRuleModel);
  }
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return IestOrgModel.stateMapping()[this.reactiveRecord.state];
  // });

  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}
