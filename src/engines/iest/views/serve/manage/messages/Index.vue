<script lang="ts">
import ComServeMessagesIndex from '@/engines/iest/components/serve/messages/ComServeMessagesIndex.vue';
import { ServeManageMessagesApi } from '@/engines/iest/serve-core/apis/serve/manage/messages.api';
import { ServeMessageModel } from '@/engines/iest/serve-core/models/serve/manage/messages';
import { VStore } from '@/lib/vails';
import { defineComponent } from '@vue/runtime-core';

import usePolicy from '@/components/global/ta-component/ta-template-form-core/usePolicy';

const ServeManageMessagesIndex = defineComponent({
  name: 'ServeManageMessagesIndex',
  components: {
    ComServeMessagesIndex,
  },
  setup() {
    usePolicy();

    const store = new VStore(new ServeManageMessagesApi(), ServeMessageModel);

    return {
      store,
    };
  },
});

export default ServeManageMessagesIndex;
</script>

<template lang="pug">
.serve-manage-messages-index.bg-gray-100
  .h-full.pt-4
    ComServeMessagesIndex(:store='store', needStatistic)
</template>

<style lang="stylus" scoped>
.serve-manage-messages-index
  height 100%
  width 100%
</style>
