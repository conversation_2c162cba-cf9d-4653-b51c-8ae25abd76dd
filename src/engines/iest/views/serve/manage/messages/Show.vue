<script lang="ts">
  import ComServeMessagesShow from '@/engines/iest/components/serve/messages/ComServeMessagesShow.vue';
  import { defineComponent, toRefs, onMounted, computed } from 'vue';
  import { ServeManageMessagesApi } from '@/engines/iest/serve-core/apis/serve/manage/messages.api';
  import { ServeMessageModel } from '@/engines/iest/serve-core/models/serve/manage/messages';
  import { VStore } from '@/lib/vails';
  import { useRoute } from 'vue-router';

  const ServeManageMessagesShow = defineComponent({
    name: 'ServeManageMessagesShow',
    components: {
    ComServeMessagesShow,
  },
    setup(props) {
      const route = useRoute();
    
    const store = new VStore(new ServeManageMessagesApi(), ServeMessageModel);
    
    const breadcrumbs = computed(() => [
      
    { label: '', route: '/serve/manage/messages' },
          ]);

  onMounted(() => {
    store.find(Number(route.params.messageId));
  });

  return {
    ...toRefs(props),
    store,
    record: store.record,
    breadcrumbs,
  };
  },
});

  export default ServeManageMessagesShow;
</script>

<template lang="pug">
.serve-manage-messages-show
  ComServeMessagesShow(v-if='record.id', :store='store', :breadcrumbs='breadcrumbs')
</template>

<style lang="stylus" scoped>
.serve-manage-messages-show
  height 100%
  width 100%
  .breadcrumbs
    padding 0 24px
</style>
