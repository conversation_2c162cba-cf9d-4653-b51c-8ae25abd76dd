<script lang="ts">
import ComServeOriginIndex from '@/engines/iest/components/serve/origins/ComServeOriginIndex.vue';
import { ServeManageOriginApi } from '@/engines/iest/serve-core/apis/serve/manage/origin.api';
import { ServeOriginModel } from '@/engines/iest/serve-core/models/serve/manage/origin';
import { VStore } from '@/lib/vails';
import { defineComponent } from '@vue/runtime-core';

import usePolicy from '@/components/global/ta-component/ta-template-form-core/usePolicy';
import { ServeManageActivitiesApi } from '../../../../apis/serve/manage/activities.api';
import { ServeActivityModel } from '../../../../serve-core/models/serve/manage/activities';

const ServeManageOriginIndex = defineComponent({
  name: 'ServeManageOriginIndex',
  components: {
    ComServeOriginIndex,
  },
  setup() {
    usePolicy();

    const store = new VStore(new ServeManageOriginApi(), ServeOriginModel);
    const activityStore = new VStore(
      new ServeManageActivitiesApi({
        parents: [{ type: 'submodules', id: 1 }],
        params: { q: { s: ['position asc'] } },
      }),
      ServeActivityModel,
    );

    return {
      store,
      activityStore,
    };
  },
});

export default ServeManageOriginIndex;
</script>

<template lang="pug">
.serve-manage-origin-index.bg-gray-100
  .pt-4.h-full
    ComServeOriginIndex(:store='store' :activityStore='activityStore' needStatistic)
</template>

<style lang="stylus" scoped>
.serve-manage-origin-index
  height 100%
  width 100%
</style>
