<script lang="ts">
  import ComServeOriginShow from '@/engines/iest/components/serve/origins/ComServeOriginShow.vue';
  import { defineComponent, toRefs, onMounted, computed } from 'vue';
  import { ServeManageOriginApi } from '@/engines/iest/serve-core/apis/serve/manage/origin.api';
  import { ServeOriginModel } from '@/engines/iest/serve-core/models/serve/manage/origin';
  import { VStore } from '@/lib/vails';
  import { useRoute } from 'vue-router';

  const ServeManageOriginShow = defineComponent({
    name: 'ServeManageOriginShow',
    components: {
    ComServeOriginShow,
  },
    setup(props) {
      const route = useRoute();
    
    const store = new VStore(new ServeManageOriginApi(), ServeOriginModel);
    
    const breadcrumbs = computed(() => [
      
    { label: '', route: '/serve/manage/origins' },
          ]);

  onMounted(() => {
    store.find(Number(route.params.originId));
  });

  return {
    ...toRefs(props),
    store,
    record: store.record,
    breadcrumbs,
  };
  },
});

  export default ServeManageOriginShow;
</script>

<template lang="pug">
.serve-manage-origin-show
  ComServeOriginShow(v-if='record.id', :store='store', :breadcrumbs='breadcrumbs')
</template>

<style lang="stylus" scoped>
.serve-manage-origin-show
  height 100%
  width 100%
  .breadcrumbs
    padding 0 24px
</style>
