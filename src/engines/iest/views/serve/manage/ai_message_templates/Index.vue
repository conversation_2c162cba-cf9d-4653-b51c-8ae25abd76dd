<script lang="ts">
import ComServeAiMessageTemplatesIndex from '@/engines/iest/components/serve/ai_message_templates/ComServeAiMessageTemplatesIndex.vue';
import { ServeManageAiMessageTemplatesApi } from '@/engines/iest/serve-core/apis/serve/manage/ai_message_templates.api';
import { ServeAiMessageTemplateModel } from '@/engines/iest/serve-core/models/serve/manage/ai_message_templates';
import { VStore } from '@/lib/vails';
import { defineComponent } from '@vue/runtime-core';

import usePolicy from '@/components/global/ta-component/ta-template-form-core/usePolicy';

const ServeManageAiMessageTemplatesIndex = defineComponent({
  name: 'ServeManageAiMessageTemplatesIndex',
  components: {
    ComServeAiMessageTemplatesIndex,
  },
  setup() {
    usePolicy();

    const store = new VStore(new ServeManageAiMessageTemplatesApi(), ServeAiMessageTemplateModel);

    return {
      store,
    };
  },
});

export default ServeManageAiMessageTemplatesIndex;
</script>

<template lang="pug">
.serve-manage-ai-message-templates-index
  ComServeAiMessageTemplatesIndex(:store='store' )
</template>

<style lang="stylus" scoped>
.serve-manage-ai-message-templates-index
  height 100%
  width 100%
</style>
