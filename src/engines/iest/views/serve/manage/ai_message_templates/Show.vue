<script lang="ts">
import ComServeAiMessageTemplatesShow from '@/engines/iest/components/serve/ai_message_templates/ComServeAiMessageTemplatesShow.vue';
import { ServeManageAiMessageTemplatesApi } from '@/engines/iest/serve-core/apis/serve/manage/ai_message_templates.api';
import { ServeAiMessageTemplateModel } from '@/engines/iest/serve-core/models/serve/manage/ai_message_templates';
import { VStore } from '@/lib/vails';
import { computed, defineComponent, onMounted, toRefs } from 'vue';
import { useRoute } from 'vue-router';

const ServeManageAiMessageTemplatesShow = defineComponent({
  name: 'ServeManageAiMessageTemplatesShow',
  components: {
    ComServeAiMessageTemplatesShow,
  },
  setup(props) {
    const route = useRoute();

    const store = new VStore(new ServeManageAiMessageTemplatesApi(), ServeAiMessageTemplateModel);

    const breadcrumbs = computed(() => [
      { label: '', route: '/serve/manage/ai_message_templates' },
    ]);

    onMounted(() => {
      store.find(Number(route.params.ai_message_templateId));
    });

    return {
      ...toRefs(props),
      store,
      record: store.record,
      breadcrumbs,
    };
  },
});

export default ServeManageAiMessageTemplatesShow;
</script>

<template lang="pug">
.serve-manage-ai-message-templates-show
  ComServeAiMessageTemplatesShow(v-if='record.id', :store='store', :breadcrumbs='breadcrumbs')
</template>

<style lang="stylus" scoped>
.serve-manage-ai-message-templates-show
  height 100%
  width 100%
  .breadcrumbs
    padding 0 24px
</style>
