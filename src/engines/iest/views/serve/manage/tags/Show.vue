<script lang="ts">
import ComServeTagsShow from '@/engines/iest/components/serve/tags/ComServeTagsShow.vue';
import { defineComponent, toRefs, onMounted, computed } from 'vue';
import { ServeManageTagsApi } from '@/engines/iest/apis/serve/manage/tags.api';
import { ServeTagModel } from '@/engines/iest/serve-core/models/serve/manage/tags';
import { VStore } from '@/lib/vails';
import { useRoute } from 'vue-router';

const ServeManageTagsShow = defineComponent({
  name: 'ServeManageTagsShow',
  components: {
    ComServeTagsShow,
  },
  setup(props) {
    const route = useRoute();

    const store = new VStore(new ServeManageTagsApi(), ServeTagModel);

    const breadcrumbs = computed(() => [

      { label: '', route: '/serve/manage/tags' },
    ]);

    onMounted(() => {
      store.find(Number(route.params.tagId));
    });

    return {
      ...toRefs(props),
      store,
      record: store.record,
      breadcrumbs,
    };
  },
});

export default ServeManageTagsShow;
</script>

<template lang="pug">
.serve-manage-tags-show
  ComServeTagsShow(v-if='record.id', :store='store', :breadcrumbs='breadcrumbs')
</template>

<style lang="stylus" scoped>
.serve-manage-tags-show
  height 100%
  width 100%
  .breadcrumbs
    padding 0 24px
</style>
