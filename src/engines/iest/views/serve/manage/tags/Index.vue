<script lang="ts">
import ComServeTagsIndex from '@/engines/iest/components/serve/tags/ComServeTagsIndex.vue';
import { defineComponent } from '@vue/runtime-core';
import { ServeManageTagsApi } from '@/engines/iest/apis/serve/manage/tags.api';
import { ServeTagModel } from '@/engines/iest/serve-core/models/serve/manage/tags';
import { VStore } from '@/lib/vails';

import usePolicy from '@/components/global/ta-component/ta-template-form-core/usePolicy';
import { ServeManageActivitiesApi } from '../../../../apis/serve/manage/activities.api';
import { ServeActivityModel } from '../../../../serve-core/models/serve/manage/activities';

const ServeManageTagsIndex = defineComponent({
  name: 'ServeManageTagsIndex',
  components: {
    ComServeTagsIndex,
  },
  setup() {
    usePolicy();




    const store = new VStore(new ServeManageTagsApi({
      parents: [{ type: 'submodules', id: 1 }]
    }), ServeTagModel);

    const activityStore = new VStore(new ServeManageActivitiesApi({
      parents: [{ type: 'submodules', id: 1 }]
    }), ServeActivityModel);

    return {
      store,
      activityStore
    };
  },
});

export default ServeManageTagsIndex;
</script>

<template lang="pug">
.serve-manage-tags-index.bg-gray-100
  .pt-4.h-full
    ComServeTagsIndex(:store='store' :activityStore='activityStore')
</template>

<style lang="stylus" scoped>
.serve-manage-tags-index
  height 100%
  width 100%
</style>
