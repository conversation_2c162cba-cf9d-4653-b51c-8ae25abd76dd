<script lang="ts">
import usePolicy from '@/components/global/ta-component/ta-template-form-core/usePolicy';
import ComServeAiPackIndex from '@/engines/iest/components/serve/ai_packs/ComServeAiPackIndex.vue';
import { ServeManagePacksApi } from '@/engines/iest/serve-core/apis/serve/manage/packs.api';
import { ServePackModel } from '@/engines/iest/serve-core/models/serve/manage/packs';
import { VStore } from '@/lib/vails';
import { defineComponent } from '@vue/runtime-core';

const ServeManageAiPackIndex = defineComponent({
  name: 'ServeManageAiPackIndex',
  components: {
    ComServeAiPackIndex,
  },
  setup() {
    usePolicy();

    const store = new VStore(
      new ServeManagePacksApi({ params: { q: { type_eq: 'Serve::AiPack' } } }),
      ServePackModel,
    );

    return {
      store,
    };
  },
});

export default ServeManageAiPackIndex;
</script>

<template lang="pug">
.serve-manage-ai-pack-index
  ComServeAiPackIndex(:store='store' )
</template>

<style lang="stylus" scoped>
.serve-manage-ai-pack-index
  height 100%
  width 100%
</style>
