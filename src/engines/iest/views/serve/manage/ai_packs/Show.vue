<script lang="ts">
import ComServeAiPackShow from '@/engines/iest/components/serve/ai_packs/ComServeAiPackShow.vue';
import { ServeManagePacksApi } from '@/engines/iest/serve-core/apis/serve/manage/packs.api';
import { ServePackModel } from '@/engines/iest/serve-core/models/serve/manage/packs';
import { VStore } from '@/lib/vails';
import { computed, defineComponent, onMounted, toRefs } from 'vue';
import { useRoute } from 'vue-router';

const ServeManageAiPackShow = defineComponent({
  name: 'ServeManageAiPackShow',
  components: {
    ComServeAiPackShow,
  },
  setup(props) {
    const route = useRoute();

    const store = new VStore(new ServeManagePacksApi(), ServePackModel);

    const breadcrumbs = computed(() => [{ label: '', route: '/serve/manage/ai_packs' }]);

    onMounted(() => {
      store.find(Number(route.params.ai_packId));
    });

    return {
      ...toRefs(props),
      store,
      record: store.record,
      breadcrumbs,
    };
  },
});

export default ServeManageAiPackShow;
</script>

<template lang="pug">
.serve-manage-ai-pack-show
  ComServeAiPackShow(v-if='record.id', :store='store', :breadcrumbs='breadcrumbs')
</template>

<style lang="stylus" scoped>
.serve-manage-ai-pack-show
  height 100%
  width 100%
  .breadcrumbs
    padding 0 24px
</style>
