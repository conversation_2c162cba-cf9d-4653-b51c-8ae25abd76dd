<script lang="ts">
import ComServeRulesIndex from '@/engines/iest/components/serve/rules/ComServeRulesIndex.vue';
import { defineComponent } from '@vue/runtime-core';
import { ServeManageRulesApi } from '@/engines/iest/serve-core/apis/serve/manage/rules.api';
import { ServeRuleModel } from '@/engines/iest/serve-core/models/serve/manage/rules';
import { VStore } from '@/lib/vails';

import usePolicy from '@/components/global/ta-component/ta-template-form-core/usePolicy';
import { ServeManageRuleGroupsApi } from '@/engines/iest/apis/serve/manage/rule_groups.api';
import { ServeRuleGroupModel } from '@/engines/iest/models/serve/manage/rule_groups';

const ServeManageRulesIndex = defineComponent({
  name: 'ServeManageRulesIndex',
  components: {
    ComServeRulesIndex,
  },
  setup() {
    usePolicy();

    const store = new VStore(new ServeManageRulesApi(), ServeRuleModel);
    const catalogStore = new VStore(new ServeManageRuleGroupsApi({}), ServeRuleGroupModel);

    return {
      store,
      catalogStore,
    };
  },
});

export default ServeManageRulesIndex;
</script>

<template lang="pug">
.serve-manage-rules-index.bg-gray-100
  .pt-4.h-full
    ComServeRulesIndex(:store='store', :catalogStore='catalogStore')
</template>

<style lang="stylus" scoped>
.serve-manage-rules-index
  height 100%
  width 100%
</style>
