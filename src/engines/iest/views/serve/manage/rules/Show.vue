<script lang="ts">
  import ComServeRulesShow from '@/engines/iest/components/serve/rules/ComServeRulesShow.vue';
  import { defineComponent, toRefs, onMounted, computed } from 'vue';
  import { ServeManageRulesApi } from '@/engines/iest/serve-core/apis/serve/manage/rules.api';
  import { ServeRuleModel } from '@/engines/iest/serve-core/models/serve/manage/rules';
  import { VStore } from '@/lib/vails';
  import { useRoute } from 'vue-router';

  const ServeManageRulesShow = defineComponent({
    name: 'ServeManageRulesShow',
    components: {
    ComServeRulesShow,
  },
    setup(props) {
      const route = useRoute();
    
    const store = new VStore(new ServeManageRulesApi(), ServeRuleModel);
    
    const breadcrumbs = computed(() => [
      
    { label: '', route: '/serve/manage/rules' },
          ]);

  onMounted(() => {
    store.find(Number(route.params.ruleId));
  });

  return {
    ...toRefs(props),
    store,
    record: store.record,
    breadcrumbs,
  };
  },
});

  export default ServeManageRulesShow;
</script>

<template lang="pug">
.serve-manage-rules-show
  ComServeRulesShow(v-if='record.id', :store='store', :breadcrumbs='breadcrumbs')
</template>

<style lang="stylus" scoped>
.serve-manage-rules-show
  height 100%
  width 100%
  .breadcrumbs
    padding 0 24px
</style>
