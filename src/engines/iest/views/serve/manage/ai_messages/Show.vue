<script lang="ts">
import ComServeAiMessageShow from '@/engines/iest/components/serve/ai_messages/ComServeAiMessageShow.vue';
import { ServeManageAiMessageApi } from '@/engines/iest/serve-core/apis/serve/manage/ai_message.api';
import { ServeAiMessageModel } from '@/engines/iest/serve-core/models/serve/manage/ai_message';
import { VStore } from '@/lib/vails';
import { computed, defineComponent, onMounted, toRefs } from 'vue';
import { useRoute } from 'vue-router';

const ServeManageAiMessageShow = defineComponent({
  name: 'ServeManageAiMessageShow',
  components: {
    ComServeAiMessageShow,
  },
  setup(props) {
    const route = useRoute();

    const store = new VStore(new ServeManageAiMessageApi(), ServeAiMessageModel);

    const breadcrumbs = computed(() => [{ label: '', route: '/serve/manage/ai_messages' }]);

    onMounted(() => {
      store.find(Number(route.params.ai_messageId));
    });

    return {
      ...toRefs(props),
      store,
      record: store.record,
      breadcrumbs,
    };
  },
});

export default ServeManageAiMessageShow;
</script>

<template lang="pug">
.serve-manage-ai-message-show
  ComServeAiMessageShow(v-if='record.id', :store='store', :breadcrumbs='breadcrumbs')
</template>

<style lang="stylus" scoped>
.serve-manage-ai-message-show
  height 100%
  width 100%
  .breadcrumbs
    padding 0 24px
</style>
