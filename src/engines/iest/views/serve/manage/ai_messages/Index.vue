<script lang="ts">
import ComServeAiMessageIndex from '@/engines/iest/components/serve/ai_messages/ComServeAiMessageIndex.vue';
import { ServeManageAiMessageApi } from '@/engines/iest/serve-core/apis/serve/manage/ai_message.api';
import { ServeAiMessageModel } from '@/engines/iest/serve-core/models/serve/manage/ai_message';
import { VStore } from '@/lib/vails';
import { defineComponent } from '@vue/runtime-core';

import usePolicy from '@/components/global/ta-component/ta-template-form-core/usePolicy';

const ServeManageAiMessageIndex = defineComponent({
  name: 'ServeManageAiMessageIndex',
  components: {
    ComServeAiMessageIndex,
  },
  setup() {
    usePolicy();

    const store = new VStore(new ServeManageAiMessageApi(), ServeAiMessageModel);

    return {
      store,
    };
  },
});

export default ServeManageAiMessageIndex;
</script>

<template lang="pug">
.serve-manage-ai-message-index
  ComServeAiMessageIndex(:store='store' )
</template>

<style lang="stylus" scoped>
.serve-manage-ai-message-index
  height 100%
  width 100%
</style>
