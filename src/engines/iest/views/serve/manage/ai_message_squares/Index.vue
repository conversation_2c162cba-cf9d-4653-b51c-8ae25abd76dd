<script lang="ts">
import { ServeManageRuleGroupsApi } from '@/engines/iest/apis/serve/manage/rule_groups.api';
import ComServeAiMessageSquaresIndex from '@/engines/iest/components/serve/ai_message_squares/ComServeAiMessageSquaresIndex.vue';
import { ServeManageAiMessageSquaresApi } from '@/engines/iest/serve-core/apis/serve/manage/ai_message_squares.api';
import { ServeManageOrgsApi } from '@/engines/iest/serve-core/apis/serve/manage/orgs.api';
import { ServeAiMessageSquareModel } from '@/engines/iest/serve-core/models/serve/manage/ai_message_squares';
import { VStore } from '@/lib/vails';
import { defineComponent } from '@vue/runtime-core';

import usePolicy from '@/components/global/ta-component/ta-template-form-core/usePolicy';

const ServeManageAiMessageSquaresIndex = defineComponent({
  name: 'ServeManageAiMessageSquaresIndex',
  components: {
    ComServeAiMessageSquaresIndex,
  },
  setup() {
    usePolicy();

    const store = new VStore(new ServeManageAiMessageSquaresApi(), ServeAiMessageSquareModel);
    // 初始化orgs的store用于获取组织数据
    const orgsStore = new VStore(new ServeManageOrgsApi());
    // 初始化rule_groups的store用于获取规则组数据
    const ruleGroupsStore = new VStore(new ServeManageRuleGroupsApi());

    return {
      store,
      orgsStore,
      ruleGroupsStore,
    };
  },
});

export default ServeManageAiMessageSquaresIndex;
</script>

<template lang="pug">
.serve-manage-ai-message-squares-index
  ComServeAiMessageSquaresIndex(:store='store' :orgs-store='orgsStore' :rule-groups-store='ruleGroupsStore')
</template>

<style lang="stylus" scoped>
.serve-manage-ai-message-squares-index
  height 100%
  width 100%
</style>
