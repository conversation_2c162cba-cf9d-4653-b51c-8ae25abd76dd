<script lang="ts">
import ComServeAiMessageSquaresShow from '@/engines/iest/components/serve/ai_message_squares/ComServeAiMessageSquaresShow.vue';
import { ServeManageAiMessageSquaresApi } from '@/engines/iest/serve-core/apis/serve/manage/ai_message_squares.api';
import { ServeAiMessageSquareModel } from '@/engines/iest/serve-core/models/serve/manage/ai_message_squares';
import { VStore } from '@/lib/vails';
import { computed, defineComponent, onMounted, toRefs } from 'vue';
import { useRoute } from 'vue-router';

const ServeManageAiMessageSquaresShow = defineComponent({
  name: 'ServeManageAiMessageSquaresShow',
  components: {
    ComServeAiMessageSquaresShow,
  },
  setup(props) {
    const route = useRoute();

    const store = new VStore(new ServeManageAiMessageSquaresApi(), ServeAiMessageSquareModel);

    const breadcrumbs = computed(() => [{ label: '', route: '/serve/manage/ai_message_squares' }]);

    onMounted(() => {
      store.find(Number(route.params.ai_message_squareId));
    });

    return {
      ...toRefs(props),
      store,
      record: store.record,
      breadcrumbs,
    };
  },
});

export default ServeManageAiMessageSquaresShow;
</script>

<template lang="pug">
.serve-manage-ai-message-squares-show
  ComServeAiMessageSquaresShow(v-if='record.id', :store='store', :breadcrumbs='breadcrumbs')
</template>

<style lang="stylus" scoped>
.serve-manage-ai-message-squares-show
  height 100%
  width 100%
  .breadcrumbs
    padding 0 24px
</style>
