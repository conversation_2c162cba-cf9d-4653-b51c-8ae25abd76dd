<script lang="ts">
import ComServeActivitiesShow from '@/engines/iest/components/serve/activities/ComServeActivitiesShow.vue';
import { defineComponent, toRefs, onMounted, computed } from 'vue';
import { ServeManageActivitiesApi } from '@/engines/iest/apis/serve/manage/activities.api';
import { ServeActivityModel } from '@/engines/iest/serve-core/models/serve/manage/activities';
import { VStore } from '@/lib/vails';
import { useRoute } from 'vue-router';

const ServeManageActivitiesShow = defineComponent({
  name: 'ServeManageActivitiesShow',
  components: {
    ComServeActivitiesShow,
  },
  setup(props) {
    const route = useRoute();

    const store = new VStore(new ServeManageActivitiesApi(), ServeActivityModel);

    const breadcrumbs = computed(() => [

      { label: '', route: '/serve/manage/activities' },
    ]);

    onMounted(() => {
      store.find(Number(route.params.activityId));
    });

    return {
      ...toRefs(props),
      store,
      record: store.record,
      breadcrumbs,
    };
  },
});

export default ServeManageActivitiesShow;
</script>

<template lang="pug">
.serve-manage-activities-show
  ComServeActivitiesShow(v-if='record.id', :store='store', :breadcrumbs='breadcrumbs')
</template>

<style lang="stylus" scoped>
.serve-manage-activities-show
  height 100%
  width 100%
  .breadcrumbs
    padding 0 24px
</style>
