<script lang="ts">
import ComServeActivitiesIndex from '@/engines/iest/components/serve/activities/ComServeActivitiesIndex.vue';
import { defineComponent } from '@vue/runtime-core';
import { ServeManageActivitiesApi } from '@/engines/iest/apis/serve/manage/activities.api';
import { ServeActivityModel } from '@/engines/iest/serve-core/models/serve/manage/activities';
import { VStore } from '@/lib/vails';

import usePolicy from '@/components/global/ta-component/ta-template-form-core/usePolicy';

const ServeManageActivitiesIndex = defineComponent({
  name: 'ServeManageActivitiesIndex',
  components: {
    ComServeActivitiesIndex,
  },
  setup() {
    usePolicy();

    const store = new VStore(
      new ServeManageActivitiesApi({
        parents: [{ type: 'submodules', id: 1 }],
      }),
      ServeActivityModel,
    );
    const params = { q: { s: ['published_at desc'] } };
    return {
      store,
      params,
    };
  },
});

export default ServeManageActivitiesIndex;
</script>

<template lang="pug">
.serve-manage-activities-index.bg-gray-100
  .pt-4.h-full
    ComServeActivitiesIndex(:store='store', :params='params', needStatistic)
</template>

<style lang="stylus" scoped>
.serve-manage-activities-index
  height 100%
  width 100%
</style>
