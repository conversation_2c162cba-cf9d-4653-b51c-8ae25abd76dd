<script lang="ts">
import ComServePacksIndex from '@/engines/iest/components/serve/packs/ComServePacksIndex.vue';
import { defineComponent } from '@vue/runtime-core';
import { ServeManagePacksApi } from '@/engines/iest/serve-core/apis/serve/manage/packs.api';
import { ServePackModel } from '@/engines/iest/serve-core/models/serve/manage/packs';
import { VStore } from '@/lib/vails';

import usePolicy from '@/components/global/ta-component/ta-template-form-core/usePolicy';

const ServeManagePacksIndex = defineComponent({
  name: 'ServeManagePacksIndex',
  components: {
    ComServePacksIndex,
  },
  setup() {
    usePolicy();




    const store = new VStore(new ServeManagePacksApi(), ServePackModel);


    return {
      store,

    };
  },
});

export default ServeManagePacksIndex;
</script>

<template lang="pug">
.serve-manage-packs-index.bg-gray-100
  .pt-4.h-full
    ComServePacksIndex(:store='store' )
</template>

<style lang="stylus" scoped>
.serve-manage-packs-index
  height 100%
  width 100%
</style>
