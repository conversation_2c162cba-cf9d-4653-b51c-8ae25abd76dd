<script lang="ts">
  import ComServePacksShow from '@/engines/iest/components/serve/packs/ComServePacksShow.vue';
  import { defineComponent, toRefs, onMounted, computed } from 'vue';
  import { ServeManagePacksApi } from '@/engines/iest/serve-core/apis/serve/manage/packs.api';
  import { ServePackModel } from '@/engines/iest/serve-core/models/serve/manage/packs';
  import { VStore } from '@/lib/vails';
  import { useRoute } from 'vue-router';

  const ServeManagePacksShow = defineComponent({
    name: 'ServeManagePacksShow',
    components: {
    ComServePacksShow,
  },
    setup(props) {
      const route = useRoute();
    
    const store = new VStore(new ServeManagePacksApi(), ServePackModel);
    
    const breadcrumbs = computed(() => [
      
    { label: '', route: '/serve/manage/packs' },
          ]);

  onMounted(() => {
    store.find(Number(route.params.packId));
  });

  return {
    ...toRefs(props),
    store,
    record: store.record,
    breadcrumbs,
  };
  },
});

  export default ServeManagePacksShow;
</script>

<template lang="pug">
.serve-manage-packs-show
  ComServePacksShow(v-if='record.id', :store='store', :breadcrumbs='breadcrumbs')
</template>

<style lang="stylus" scoped>
.serve-manage-packs-show
  height 100%
  width 100%
  .breadcrumbs
    padding 0 24px
</style>
