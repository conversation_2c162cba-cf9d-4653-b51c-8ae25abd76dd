<script lang="ts">
import { ServeUserMessagesApi } from '@/engines/iest/apis/serve/user/messages.api';
import ComServeMessagesShow from '@/engines/iest/components/serve/messages/ComServeMessagesShow.vue';
import { ServeMessageModel } from '@/engines/iest/serve-core/models/serve/user/messages';
import { VStore } from '@/lib/vails';
import { computed, defineComponent, onMounted, toRefs } from 'vue';
import { useRoute } from 'vue-router';

const ServeUserMessagesShow = defineComponent({
  name: 'ServeUserMessagesShow',
  components: {
    ComServeMessagesShow,
  },
  setup(props) {
    const route = useRoute();

    const store = new VStore(new ServeUserMessagesApi(), ServeMessageModel);

    const breadcrumbs = computed(() => [{ label: '', route: '/serve/user/messages' }]);

    onMounted(() => {
      store.find(Number(route.params.messageId));
    });

    return {
      ...toRefs(props),
      store,
      record: store.record,
      breadcrumbs,
    };
  },
});

export default ServeUserMessagesShow;
</script>

<template lang="pug">
.serve-user-messages-show
  ComServeMessagesShow(v-if='record.id', :store='store', :breadcrumbs='breadcrumbs')
</template>

<style lang="stylus" scoped>
.serve-user-messages-show
  height 100%
  width 100%
  .breadcrumbs
    padding 0 24px
</style>
