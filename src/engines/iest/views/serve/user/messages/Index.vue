<script lang="ts">
import { ServeUserMessagesApi } from '@/engines/iest/apis/serve/user/messages.api';
import ComServeMessagesIndex from '@/engines/iest/components/serve/messages/ComServeMessagesIndex.vue';
import { ServeMessageModel } from '@/engines/iest/serve-core/models/serve/user/messages';
import { VStore } from '@/lib/vails';
import { defineComponent } from '@vue/runtime-core';

import usePolicy from '@/components/global/ta-component/ta-template-form-core/usePolicy';

const ServeUserMessagesIndex = defineComponent({
  name: 'ServeUserMessagesIndex',
  components: {
    ComServeMessagesIndex,
  },
  setup() {
    usePolicy();

    const store = new VStore(new ServeUserMessagesApi(), ServeMessageModel);

    return {
      store,
    };
  },
});

export default ServeUserMessagesIndex;
</script>

<template lang="pug">
.serve-user-messages-index
  ComServeMessagesIndex(:store='store' )
</template>

<style lang="stylus" scoped>
.serve-user-messages-index
  height 100%
  width 100%
</style>
