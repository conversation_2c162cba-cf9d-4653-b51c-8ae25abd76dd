<script lang="ts">
import ComIestConversationsShow from '@/engines/iest/components/iest/conversations/ComIestConversationsShow.vue';
import { defineComponent, toRefs, onMounted, computed } from 'vue';
import { IestAiChatUserConversationsApi } from '@/engines/iest/apis/iest/ai/chat/user/conversations.api';
import { IestConversationModel } from '@/engines/iest/models/iest/ai/chat/user/conversations';
import { VStore } from '@/lib/vails';
import { useRoute } from 'vue-router';
import { IestAiChatUserMessagesApi } from '../../../../../../apis/iest/ai/chat/user/messages.api';
import { message } from 'ant-design-vue';
import { IestAiChatUserMentionsApi } from '../../../../../../apis/iest/ai/chat/user/mentions.api';
import { IestAiChatUserMentionVersionsApi } from '../../../../../../apis/iest/ai/chat/user/mention_versions.api';
import { useCable } from '../../../../../../../base/channels/useCable';

const IestAiChatUserConversationsShow = defineComponent({
  name: 'IestAiChatUserConversationsShow',
  components: {
    ComIestConversationsShow,
  },
  setup(props) {
    const route = useRoute();
    const conversationId = Number(route.params.conversationId);

    const store = new VStore(new IestAiChatUserConversationsApi(), IestConversationModel);
    const messageStore = new VStore(
      new IestAiChatUserMessagesApi({
        parents: [{ type: 'conversations', id: conversationId }],
        params: { q: { s: ['id asc'] } },
      }),
    );

    const mentionStore = new VStore(
      new IestAiChatUserMentionsApi({
        parents: [{ type: 'conversations', id: conversationId }],
        params: { q: { s: ['id asc'] } },
      }),
    );

    const mentionVersionStore = new VStore(
      new IestAiChatUserMentionVersionsApi({
        parents: [{ type: 'conversations', id: conversationId }],
        params: { q: { s: ['id asc'] } },
      }),
    );

    onMounted(() => {
      store.find(conversationId);
      messageStore.index({ per_page: 999999 });
      mentionStore.index({ per_page: 999999 });
    });

    mentionStore.extra.cable_key = 'iest_ai_chat_mentions';
    useCable(mentionStore, {
      callback: { afterCreate: () => mentionStore.index({ per_page: 999999 }) },
    });

    return {
      ...toRefs(props),
      store,
      record: store.record,
      messageStore,
      mentionStore,
      mentionVersionStore,
    };
  },
});

export default IestAiChatUserConversationsShow;
</script>

<template lang="pug">
.iest-ai-chat-user-conversations-show
  ComIestConversationsShow(
    v-if='record.id',
    :store='store',
    :messageStore='messageStore',
    :mentionStore='mentionStore',
    :mentionVersionStore='mentionVersionStore',
  )
</template>

<style lang="stylus" scoped>
.iest-ai-chat-user-conversations-show
  height 100%
  width 100%
  .breadcrumbs
    padding 0 24px
</style>
