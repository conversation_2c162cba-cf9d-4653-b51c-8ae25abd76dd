<script lang="ts">
import ComIestConversationsIndex from '@/engines/iest/components/iest/conversations/ComIestConversationsIndex.vue';
import { defineComponent } from '@vue/runtime-core';
import { IestAiChatUserConversationsApi } from '@/engines/iest/apis/iest/ai/chat/user/conversations.api';
import { IestConversationModel } from '@/engines/iest/models/iest/ai/chat/user/conversations';
import { VStore } from '@/lib/vails';

import usePolicy from '@/components/global/ta-component/ta-template-form-core/usePolicy';

const IestAiChatUserConversationsIndex = defineComponent({
  name: 'IestAiChatUserConversationsIndex',
  components: {
    ComIestConversationsIndex,
  },
  setup() {
    usePolicy();

    const store = new VStore(new IestAiChatUserConversationsApi(), IestConversationModel);

    return {
      store,
    };
  },
});

export default IestAiChatUserConversationsIndex;
</script>

<template lang="pug">
.iest-ai-chat-user-conversations-index
  ComIestConversationsIndex(:store='store')
</template>

<style lang="stylus" scoped>
.iest-ai-chat-user-conversations-index
  height 100%
  width 100%
</style>
