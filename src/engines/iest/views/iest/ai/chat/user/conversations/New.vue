<script lang="ts">
import ComIestConversationsNew from '@/engines/iest/components/iest/conversations/ComIestConversationsNew.vue';
import { defineComponent, toRefs, onMounted } from 'vue';
import { IestAiChatUserConversationsApi } from '@/engines/iest/apis/iest/ai/chat/user/conversations.api';
import { IestConversationModel } from '@/engines/iest/models/iest/ai/chat/user/conversations';
import { VStore } from '@/lib/vails';

const IestAiChatUserConversationsNew = defineComponent({
  name: 'IestAiChatUserConversationsNew',
  components: {
    ComIestConversationsNew,
  },
  setup(props) {
    const store = new VStore(new IestAiChatUserConversationsApi(), IestConversationModel);

    onMounted(() => {
      // store.find(Number(route.params.conversationId));
    });

    return {
      ...toRefs(props),
      store,
      record: store.record,
    };
  },
});

export default IestAiChatUserConversationsNew;
</script>

<template lang="pug">
.iest-ai-chat-user-conversations-new
  ComIestConversationsNew(:store='store')
</template>

<style lang="stylus" scoped>
.iest-ai-chat-user-conversations-new
  height 100%
  width 100%
  .breadcrumbs
    padding 0 24px
</style>
