<script lang="ts">
import { defineComponent, toRefs } from 'vue';
import ComIestAiNotifyContainer from '../../../../../components/iest/ai_notify/ComIestAiNotifyContainer.vue';

const IestAiNotifyManageMessagesIndex = defineComponent({
  name: 'IestAiNotifyManageMessagesIndex',
  components: {
    ComIestAiNotifyContainer,
  },
  props: {},
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
export default IestAiNotifyManageMessagesIndex;
</script>

<template lang="pug">
.iest-ai-notify-manage-messages-index.h-full.w-full
  ComIestAiNotifyContainer
</template>

<style lang="stylus" scoped></style>
