<script lang="ts">
import { ref, defineComponent, toRefs } from 'vue';
import { useRoute } from 'vue-router';
import EnginesScreenPagesShow from '../../../../screen/views/screen/user/page_settings/Show.vue';

const IestScreenDashboard = defineComponent({
  name: 'IestScreenDashboard',
  components: {
    EnginesScreenPagesShow,
  },
  props: {},
  setup(props) {
    const route = useRoute();

    const screenId = route.params.screenId;

    return {
      ...toRefs(props),
      screenId,
    };
  },
});
export default IestScreenDashboard;
</script>

<template lang="pug">
.iest-screen-dashboard.h-full.w-full
  EnginesScreenPagesShow(:pageSettingId='screenId', :byWidth='true')
</template>

<style lang="stylus" scoped></style>
