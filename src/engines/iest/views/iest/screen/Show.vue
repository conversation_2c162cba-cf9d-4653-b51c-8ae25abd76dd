<script lang="ts">
import ComScreenClock from '@/engines/iest/components/ComScreenClock.vue';
import ComIestStatisticTabChange from '@/engines/iest/components/ComIestStatisticTabChange.vue';
import { ref, defineComponent, toRefs, onBeforeUnmount, watch, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useDraggable } from '@vueuse/core';

const IestScreenShow = defineComponent({
  name: 'IestScreenShow',
  components: {
    ComScreenClock,
    ComIestStatisticTabChange,
  },
  props: {},
  setup(props) {
    const route = useRoute();

    document.getElementsByTagName('html')[0].setAttribute('data-pro-theme', 'antdv-pro-theme-dark');
    document.documentElement.classList.toggle('dark', true);

    onBeforeUnmount(() => {
      document
        .getElementsByTagName('html')[0]
        .setAttribute('data-pro-theme', 'antdv-pro-theme-custom');

      document.documentElement.classList.toggle('dark', false);
    });

    const screenId = route.params.screenId;

    const iestScreenIds = [2, 17];
    const bidScreenId = [4]
    const noTitleScreen = [19]

    const screenKindMap = new Proxy({}, {
      get(target, key) {
        key = key.toString()
        if (iestScreenIds.includes(+key)) {
          return 'IEST'
        }
        if (bidScreenId.includes(+key)) {
          return 'BID'
        }
        if (noTitleScreen.includes(+key)) {
          return 'NO_TITLE_BID'
        }
      }
    })

    const showBgMap = computed(
      () => {
        const bgMap = {
          IEST: 'url(https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iest/7B44A81A-AE34-4C34-8788-1338C6C8BF73.png)',
          BID: 'url(https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iest/bid-guard/%E6%95%B0%E6%8D%AE%E7%9C%8B%E6%9D%BF%E6%8B%9B%E6%8A%95%E6%A0%87%20-%207.png)',
          'NO_TITLE_BID': 'center / cover url(https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iest/bid-guard/%E6%95%B0%E6%8D%AE%E7%9C%8B%E6%9D%BF%E6%8B%9B%E6%8A%95%E6%A0%87%20-%207.png)',
        }
        return (bgMap as any)[(screenKindMap as any)[screenId as string]]
      }
    )

    const buttons = [
      {
        name: '驾驶舱',
        key: 'dashboard',
        img: 'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iest/screen_button.svg',
        activeImg:
          'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iest/screen_button_active.svg',
        route: `/iest/screen/${screenId}/dashboard`,
      },
      {
        name: 'AI 助手',
        key: 'ai',
        img: 'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iest/ai_button.svg',
        activeImg: 'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iest/ai_button_active.svg',
        route: `/iest/screen/${screenId}/ai`,
      },
    ];

    const ai = ref<HTMLDivElement>();
    const { x, y, style, isDragging } = useDraggable(ai, {
      initialValue: { x: window.innerWidth - 150, y: 100 },
    });

    const router = useRouter();
    const isAiPage = computed(() => route.path.startsWith(`/iest/screen/${screenId}/ai`));
    const floatBtnBg = computed(() => !isAiPage.value ? 'url(https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iest/bid-guard/Group%201321314616.png)' : 'url(https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iest/bid-guard/Group%201321314615.png)');

    let lastDragTime = 0;
    const isDragged = ref(false);
    watch(isDragging, (newVal: boolean, oldVal: boolean) => {
      if (!newVal && oldVal) {
        lastDragTime = Date.now();
      }
    });
    const onClickAiIcon = () => {
      const cur = Date.now();
      if (isDragging.value) {
        return;
      }

      if (cur - lastDragTime < 50 && isDragged.value) {
        return (isDragged.value = false);
      }

      return isAiPage.value ? router.replace(`/iest/screen/${screenId}/dashboard`) : router.replace(`/iest/screen/${screenId}/ai`)
    };

    const onDragStart = (event: DragEvent) => {
      isDragged.value = true;

      if (event?.dataTransfer) {
        event.dataTransfer.effectAllowed = 'move';
        ai.value!.style.visibility = 'hidden';
      }
    };

    const onDragEnd = (event: DragEvent) => {
      ai.value!.style.visibility = 'visible';

    };


    return {
      ...toRefs(props),
      buttons,
      screenId,
      screenKindMap,
      showBgMap,
      ai,
      style,
      isDragging,
      x,
      y,
      onDragStart,
      onDragEnd,
      onClickAiIcon,
      floatBtnBg,
      title: process.env.VUE_APP_AI_TITLE,
    };
  },
});
export default IestScreenShow;
</script>

<template lang="pug">
.iest-screen-show.w-screen.h-screen(class='bg-[#011848]')
  .dot-net.h-full.w-full.flex.flex-col
    .flex.justify-center.items-center.flex-shrink-0.relative.bg-cover(
      v-if='screenKindMap[screenId] === "IEST"', class='h-1/16 min-h-12'
      style='background-image: url(https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iest/Top.svg)'
    )
      .ai__icon.absolute.w-20.h-20.z-800.bg-cover.cursor-pointer.rounded-full.select-none.shadow-lg(
          @click.stop='onClickAiIcon'
          :draggable='true',
          ref='ai',
          :class='{ "cursor-move": isDragging,"cursor-pointer":!isDragging }'
          style="touch-action:none;"
          :style="style",
          @dragstart='onDragStart',
          @dragend='onDragEnd',
        )
      .wrapper.pl-4(class='basis-1/3')
        ComScreenClock.w-50
      .w-full.flex.justify-center.items-center(class='basis-1/3')
        .text-white.text-xl.font-bold {{ title }}
      .wrapper.flex.justify-end.pr-4(class='basis-1/3')
        ComIestStatisticTabChange.w-80.h-8(
          @toAi='() => $route.path.startsWith(`/iest/screen/${screenId}/ai`) || $router.replace(`/iest/screen/${screenId}/ai`)'
          @toDashboard='() => $route.path.startsWith(`/iest/screen/${screenId}/dashboard`) || $router.replace(`/iest/screen/${screenId}/dashboard`)'
        )
    .flex.justify-center.relative.flex-shrink-0(v-else-if='screenKindMap[screenId] === "BID"')
      img.absolute.top-0.left-0(class='w-2/3', src='https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iest/%E6%A0%87%E9%A2%98.svg')
      .buttons.flex.space-x-8.py-4.z-10
        template(v-for='button in buttons')
          .button.cursor-pointer(@click='() => $route.path.startsWith(button.route) || $router.replace(button.route)')
            img(
              :src='$route.path.startsWith(button.route) ? button.activeImg : button.img',
              class='w-20',
            )
    .empty.w-full.relative(v-else)
      .ai__icon.absolute.w-20.h-20.z-800.bg-cover.cursor-pointer.rounded-full.select-none.shadow-lg.top-10.left-10(
        @click.stop='onClickAiIcon'
        :draggable='false',
        ref='ai',
      )
    .flex-grow.h-0.w-full
      router-view(v-slot='{ Component }')
        keep-alive
          component(:is='Component')
</template>

<style lang="stylus">
.iest-screen-show
  .ai__icon
    background-image v-bind(floatBtnBg)
.dark .com-bpm-instance-timeline
  .ant-timeline-item-head
    @apply "!bg-white";
.dark .com-bpm-instance-detail, .dark .ta-no-padding-modal__body
  .ant-table
    @apply "!bg-transparent";
    .ant-table-thead >tr th,.ant-table-tbody >tr td
      border-bottom 1px solid #f0f0f0
    .ant-table-tbody>tr.ant-table-row-selected>td
      @apply "!bg-gray-100";
  .ant-steps-item-title
    @apply "!text-gray-900";
  .ant-table-pagination *
    @apply "!text-gray-900";
  .ta-data-form-viewer .value
    @apply "!text-gray-900";
  .ant-input:not(.ant-input-xxx)
    @apply text-gray-900;

.dark .ant-modal
  .ant-btn:not(.ant-btn-primary):not(:hover)
    color rgba(0, 0, 0, .65);

</style>

<style lang="stylus" scoped>
.dot-net
  background v-bind(showBgMap)
  // background-image url(https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iest/bid-guard/261730884461_.pic.jpg)
  //-background url(https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iest/7B44A81A-AE34-4C34-8788-1338C6C8BF73.png) repeat
</style>
