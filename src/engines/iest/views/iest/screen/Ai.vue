<script lang="ts">
import ComChatConversationLayout from '@/engines/chat/components/chat/conversations/ComChatConversationLayout.vue';
import { defineComponent, toRefs } from 'vue';
import { useRoute } from 'vue-router';

const IestScreenAi = defineComponent({
  name: 'IestScreenAi',
  components: {
    ComChatConversationLayout,
  },
  props: {},
  setup(props) {
    const route = useRoute();

    const screenId = route.params.screenId;
    const initCache = {
      routeBase: `/iest/screen/${screenId}/ai/conversations/`,
    };
    return {
      ...toRefs(props),
      screenId,
      initCache,
    };
  },
});
export default IestScreenAi;
</script>

<template lang="pug">
.iest-screen-ai.h-full.w-full
  ComChatConversationLayout(:initCache='initCache')
</template>

<style lang="stylus" scoped></style>
