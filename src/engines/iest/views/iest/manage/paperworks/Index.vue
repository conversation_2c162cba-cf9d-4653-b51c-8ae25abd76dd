<script lang="ts">
import ComIestPaperworksIndex from '@/engines/iest/components/iest/paperworks/ComIestPaperworksIndex.vue';
import { defineComponent } from '@vue/runtime-core';
import { IestManagePaperworksApi } from '@/engines/iest/apis/iest/manage/paperworks.api';
import { IestPaperworkModel } from '@/engines/iest/models/iest/manage/paperworks';
import { VStore } from '@/lib/vails';

import usePolicy from '@/components/global/ta-component/ta-template-form-core/usePolicy';

const IestManagePaperworkIndex = defineComponent({
  name: 'IestManagePaperworkIndex',
  components: {
    ComIestPaperworksIndex,
  },
  setup() {
    usePolicy();

    const store = new VStore(
      new IestManagePaperworksApi({
        params: {
          group_keys: ['state'],
        },
      }),
      IestPaperworkModel,
    );

    return {
      store,
    };
  },
});

export default IestManagePaperworkIndex;
</script>

<template lang="pug">
.iest-manage-paperwork-index
  ComIestPaperworksIndex(:store='store')
</template>

<style lang="stylus" scoped>
.iest-manage-paperwork-index
  height 100%
  width 100%
</style>
