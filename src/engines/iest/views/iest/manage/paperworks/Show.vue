<script lang="ts">
import ComIestPaperworkShow from '@/engines/iest/components/iest/paperworks/ComIestPaperworksShow.vue';
import { defineComponent, toRefs, onMounted, computed } from 'vue';
import { IestManagePaperworksApi } from '@/engines/iest/apis/iest/manage/paperworks.api';
import { IestPaperworkModel } from '@/engines/iest/models/iest/manage/paperworks';
import { VStore } from '@/lib/vails';
import { useRoute } from 'vue-router';

const IestManagePaperworkShow = defineComponent({
  name: 'IestManagePaperworkShow',
  components: {
    ComIestPaperworkShow,
  },
  setup(props) {
    const route = useRoute();

    const store = new VStore(new IestManagePaperworksApi(), IestPaperworkModel);

    const breadcrumbs = computed(() => [{ label: '', route: '/iest/manage/paperworks' }]);

    onMounted(() => {
      store.find(Number(route.params.paperworkId));
    });

    return {
      ...toRefs(props),
      store,
      record: store.record,
      breadcrumbs,
    };
  },
});

export default IestManagePaperworkShow;
</script>

<template lang="pug">
.iest-manage-paperwork-show
  ComIestPaperworkShow(v-if='record.id', :store='store', :breadcrumbs='breadcrumbs')
</template>

<style lang="stylus" scoped>
.iest-manage-paperwork-show
  height 100%
  width 100%
  .breadcrumbs
    padding 0 24px
</style>
