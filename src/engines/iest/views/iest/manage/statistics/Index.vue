<script lang="ts">
import { ref, defineComponent, toRefs, onMounted } from 'vue';
import ComIestStatisticsIndex from '@/engines/iest/components/iest/statistics/ComIestStatisticsIndex.vue';
import { VStore } from '@/lib/vails';
import { IestAppsApi } from '@/engines/iest/apis/iest/apps.api';
import { method } from 'lodash';

const IestStatisticsIndex = defineComponent({
  name: 'IestStatisticsIndex',
  components: {
    ComIestStatisticsIndex,
  },
  setup(props) {
    const appStore = new VStore(new IestAppsApi({}));
    const state = ref({});
    const fetchStatisticsData = () => {
      appStore
        .sendMemberAction({
          id: 1,
          action: 'ta_resource_statistic',
          config: {
            data: {
              stat_condition: {
                refs: [
                  {
                    relations: ['serve_packs'],
                    item: {
                      key: 'pack',
                      caculator: {
                        type: 'caculation',
                        caculations: [
                          { name: 'pending', method: 'count', filter: { state_eq: 'pending' } },
                          { name: 'sending', method: 'count', filter: { state_eq: 'sending' } },
                          { name: 'finished', method: 'count', filter: { state_eq: 'finished' } },
                          { name: 'total', method: 'count' },
                          {
                            name: 'trend',
                            scopes: { sreen_by_period: [7, 'day'] },
                            method: 'clone',
                          },
                        ],
                      },
                    },
                  },
                  {
                    relations: ['serve_messages'],
                    item: {
                      key: 'msg',
                      caculator: {
                        type: 'caculation',
                        caculations: [
                          { name: 'read', method: 'count', filter: { is_read_true: 1 } },
                          { name: 'unread', method: 'count', filter: { is_read_false: 1 } },
                          { name: 'total', method: 'count' },
                          {
                            name: 'trend',
                            scopes: { sreen_by_period: [7, 'day'] },
                            method: 'clone',
                          },
                        ],
                      },
                    },
                  },
                  {
                    relations: ['serve_activities'],
                    item: {
                      key: 'act',
                      caculator: {
                        type: 'caculation',
                        caculations: [{ name: 'total', method: 'count' }],
                      },
                    },
                  },
                  {
                    relations: ['serve_rules'],
                    item: {
                      key: 'rule',
                      caculator: {
                        type: 'caculation',
                        caculations: [{ name: 'total', method: 'count' }],
                      },
                    },
                  },
                  {
                    relations: ['serve_tags'],
                    item: {
                      key: 'tag',
                      caculator: {
                        type: 'caculation',
                        caculations: [
                          { name: 'total', method: 'count' },
                          { name: 'total_activities', method: 'sum', attr: 'activity_count' },
                          {
                            name: 'top',
                            order: 'activity_count desc',
                            filter: { serve_tag_group_id_null: 0 },
                            attr: 'name, activity_count',
                            method: 'pluck',
                            limit: 5,
                          },
                        ],
                      },
                    },
                  },
                  {
                    relations: ['serve_origins'],
                    item: {
                      key: 'origin',
                      caculator: {
                        type: 'caculation',
                        caculations: [{ name: 'total', method: 'count' }],
                      },
                    },
                  },
                  {
                    relations: ['users'],
                    item: {
                      key: 'user',
                      scopes: ['find_public_official_users'],
                      caculator: {
                        type: 'caculation',
                        caculations: [
                          { name: 'official', method: 'count', distinct: true },
                          {
                            name: '杭州市',
                            scopes: { in_org_and_descendants: '杭州市' },
                            method: 'count',
                            distinct: true,
                          },
                          {
                            name: '上城区',
                            scopes: { in_org_and_descendants: '上城区' },
                            method: 'count',
                            distinct: true,
                          },
                          {
                            name: '拱墅区',
                            scopes: { in_org_and_descendants: '拱墅区' },
                            method: 'count',
                            distinct: true,
                          },
                          {
                            name: '西湖区',
                            scopes: { in_org_and_descendants: '西湖区' },
                            method: 'count',
                            distinct: true,
                          },
                          {
                            name: '滨江区',
                            scopes: { in_org_and_descendants: '滨江区' },
                            method: 'count',
                            distinct: true,
                          },
                          {
                            name: '萧山区',
                            scopes: { in_org_and_descendants: '萧山区' },
                            method: 'count',
                            distinct: true,
                          },
                          {
                            name: '余杭区',
                            scopes: { in_org_and_descendants: '余杭区' },
                            method: 'count',
                            distinct: true,
                          },
                          {
                            name: '富阳区',
                            scopes: { in_org_and_descendants: '富阳区' },
                            method: 'count',
                            distinct: true,
                          },
                          {
                            name: '临安区',
                            scopes: { in_org_and_descendants: '临安区' },
                            method: 'count',
                            distinct: true,
                          },
                          {
                            name: '临平区',
                            scopes: { in_org_and_descendants: '临平区' },
                            method: 'count',
                            distinct: true,
                          },
                          {
                            name: '钱塘区',
                            scopes: { in_org_and_descendants: '钱塘区' },
                            method: 'count',
                            distinct: true,
                          },
                          {
                            name: '桐庐县',
                            scopes: { in_org_and_descendants: '桐庐县' },
                            method: 'count',
                            distinct: true,
                          },
                          {
                            name: '萧山区',
                            scopes: { in_org_and_descendants: '萧山区' },
                            method: 'count',
                            distinct: true,
                          },
                          {
                            name: '淳安县',
                            scopes: { in_org_and_descendants: '淳安县' },
                            method: 'count',
                            distinct: true,
                          },
                          {
                            name: '建德市',
                            scopes: { in_org_and_descendants: '建德市' },
                            method: 'count',
                            distinct: true,
                          },
                        ],
                      },
                    },
                  },
                  {
                    relations: ['res_tags'],
                    item: {
                      key: 'res_tag',
                      caculator: {
                        type: 'caculation',
                        caculations: [
                          { name: 'count', method: 'count' },
                          { name: 'top5', scopes: 'screen_order_user', method: 'clone' },
                        ],
                      },
                    },
                  },
                ],
              },
            },
          },
        })
        .then((res: any) => {
          state.value = res.data;
        });
    };

    onMounted(() => {
      fetchStatisticsData();
    });
    return {
      ...toRefs(props),
      appStore,
      fetchStatisticsData,
      state,
    };
  },
});
export default IestStatisticsIndex;
</script>

<template lang="pug">
.iest-statistics-index.bg-gray-100.min-h-full
  .pt-4.h-full
    ComIestStatisticsIndex(:data='state')
</template>

<style lang="stylus" scoped>
.iest-statistics-index
  width 100%
</style>
