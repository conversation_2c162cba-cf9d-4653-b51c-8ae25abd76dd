<script lang="ts">
import ComIestOrgsIndex from '@/engines/iest/components/iest/orgs/ComIestOrgsIndex.vue';
import { defineComponent } from '@vue/runtime-core';
import { IestManageOrgsApi } from '@/engines/iest/apis/iest/manage/orgs.api';
import { IestOrgModel } from '@/engines/iest/models/iest/manage/orgs';
import { VStore } from '@/lib/vails';

import usePolicy from '@/components/global/ta-component/ta-template-form-core/usePolicy';

const IestManageOrgsIndex = defineComponent({
  name: 'IestManageOrgsIndex',
  components: {
    ComIestOrgsIndex,
  },
  setup() {
    usePolicy();




    const store = new VStore(new IestManageOrgsApi({
      params: {
        q: {
          org_identity_id_eq: 3
        }
      }
    }), IestOrgModel);


    return {
      store,

    };
  },
});

export default IestManageOrgsIndex;
</script>

<template lang="pug">
.iest-manage-orgs-index.bg-gray-100
  .h-full.pt-4
    ComIestOrgsIndex(:store='store' )
</template>

<style lang="stylus" scoped>
.iest-manage-orgs-index
  height 100%
  width 100%
</style>
