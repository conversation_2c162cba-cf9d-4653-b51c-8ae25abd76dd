<script lang="ts">
import ComIestOrgsShow from '@/engines/iest/components/iest/orgs/ComIestOrgsShow.vue';
import { defineComponent, toRefs, onMounted, computed } from 'vue';
import { IestManageOrgsApi } from '@/engines/iest/apis/iest/manage/orgs.api';
import { IestOrgModel } from '@/engines/iest/models/iest/manage/orgs';
import { VStore } from '@/lib/vails';
import { useRoute } from 'vue-router';
import { ResAdminUsersApi } from '@/engines/res/res-core/apis/res/admin/users.api';
import { ResUserModel } from '@/engines/res/res-core/models/res/user';
import { ServeManageRulesApi } from '@/engines/iest/serve-core/apis/serve/manage/rules.api';
import { ServeRuleModel } from '@/engines/iest/serve-core/models/serve/manage/rules';
import { ServeManagePacksApi } from '@/engines/iest/serve-core/apis/serve/manage/packs.api';
import { ServePackModel } from '@/engines/iest/serve-core/models/serve/manage/packs';
import { ServeManageMessagesApi } from '@/engines/iest/serve-core/apis/serve/manage/messages.api';
import { ServeMessageModel } from '@/engines/iest/serve-core/models/serve/manage/messages';
import { ServeManageRuleGroupsApi } from '@/engines/iest/apis/serve/manage/rule_groups.api';
import { ServeRuleGroupModel } from '@/engines/iest/models/serve/manage/rule_groups';

const IestManageOrgsShow = defineComponent({
  name: 'IestManageOrgsShow',
  components: {
    ComIestOrgsShow,
  },
  setup(props) {
    const route = useRoute();

    const getParams = (key: string = 'orgs_id_eq') => {
      return {
        q: {
          [key]: route.params.orgId
        }
      }
    }

    const store = new VStore(new IestManageOrgsApi(), IestOrgModel);
    const subOrgStore = new VStore(new IestManageOrgsApi({
      params: getParams('parent_id_eq')
    }), IestOrgModel);
    const userStore = new VStore(new ResAdminUsersApi({
      params: getParams()
    }), ResUserModel)
    const ruleStore = new VStore(new ServeManageRulesApi({
      params: getParams()
    }), ServeRuleModel)
    const catalogStore = new VStore(new ServeManageRuleGroupsApi({
      params: getParams('rules_orgs_id_eq')
    }), ServeRuleGroupModel)
    const packStore = new VStore(new ServeManagePacksApi({
      params: getParams('org_id_eq')
    }), ServePackModel)
    const messageStore = new VStore(new ServeManageMessagesApi({
      params: getParams('pack_org_id_eq')
    }), ServeMessageModel)

    const breadcrumbs = computed(() => [

      { label: '区域管理', route: '/iest/manage/orgs' },
    ]);

    onMounted(() => {
      store.find(Number(route.params.orgId));
    });

    return {
      ...toRefs(props),
      store,
      userStore,
      ruleStore,
      catalogStore,
      packStore,
      messageStore,
      subOrgStore,
      record: store.record,
      breadcrumbs,
    };
  },
});

export default IestManageOrgsShow;
</script>

<template lang="pug">
.iest-manage-orgs-show
  ComIestOrgsShow(
    v-if='record.id',
    :store='store',
    :userStore='userStore',
    :ruleStore='ruleStore',
    :packStore='packStore',
    :messageStore='messageStore',
    :subOrgStore='subOrgStore'
    :catalogStore='catalogStore'
    :breadcrumbs='breadcrumbs'
  )
</template>

<style lang="stylus" scoped>
.iest-manage-orgs-show
  height 100%
  width 100%
  .breadcrumbs
    padding 0 24px
</style>
