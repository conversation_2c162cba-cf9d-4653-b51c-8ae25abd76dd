import { RouteLocationRaw, useRouter, Router } from 'vue-router';
export async function preloadRouteComponents(
  to: RouteLocationRaw,
  router: Router & {
    _routePreloaded?: Set<string>;
    _preloadPromises?: Array<Promise<unknown>>;
  } = useRouter(),
): Promise<void> {
  const { path, matched } = router.resolve(to);

  if (!matched.length) return;

  if (!router._routePreloaded) {
    router._routePreloaded = new Set();
  }
  if (router._routePreloaded.has(path)) return;

  const promises = (router._preloadPromises = router._preloadPromises || []);

  if (promises.length > 4) {
    // Defer adding new preload requests until the existing ones have resolved
    return Promise.all(promises).then(() => preloadRouteComponents(to, router));
  }

  router._routePreloaded.add(path);

  const components = matched
    .map(component => component.components?.default)
    .filter(component => typeof component === 'function');

  for (const component of components) {
    const promise = Promise.resolve((component as () => unknown)())
      .catch(() => {})
      .finally(() => promises.splice(promises.indexOf(promise)));
    promises.push(promise);
  }

  await Promise.all(promises);
}
