import { VModel } from '@/lib/vails';
import { ChatConversation } from '@/engines/chat/types/types';

export class ChatConversationModel extends VModel<ChatConversation> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return ChatConversationModel.stateMapping()[this.reactiveRecord.state];
  // });
  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}
