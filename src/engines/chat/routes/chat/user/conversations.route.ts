export default [
  {
    name: 'ChatUserConversations',
    path: '/chat/user/',
    component: () =>
      import(
        /* webpackChunkName: "ChatUserConversations" */
        '@/engines/chat/components/chat/conversations/ComChatConversationLayout.vue'
      ),
    children: [
      {
        path: '/chat/user/conversations/:conversationId',
        name: 'ChatUserConversationsShow',
        component: () =>
          import(
            /* webpackChunkName: "ChatUserConversationsShow" */ '@/engines/chat/views/chat/user/conversations/Show.vue'
          ),
        meta: {
          title: 'AI',
        },
      },
      {
        path: '/chat/user/conversations/new',
        name: 'ChatUserConversationsNew',
        component: () =>
          import(
            /* webpackChunkName: "ChatUserConversationsNew" */ '@/engines/chat/views/chat/user/conversations/New.vue'
          ),
        meta: {
          title: 'AI',
        },
      },
    ],
  },
];
