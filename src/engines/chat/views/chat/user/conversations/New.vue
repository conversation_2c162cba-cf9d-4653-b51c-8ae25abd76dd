<script lang="ts">
import ComChatConversationsNew from '@/engines/chat/components/chat/conversations/ComChatConversationsNew.vue';
import { defineComponent, toRefs, onMounted } from 'vue';
import { ChatUserConversationsApi } from '@/engines/chat/apis/chat/user/conversations.api';
import { ChatConversationModel } from '@/engines/chat/models/chat/conversations';
import { VStore } from '@/lib/vails';

const ChatUserConversationsNew = defineComponent({
  name: 'ChatUserConversationsNew',
  components: {
    ComChatConversationsNew,
  },
  setup(props) {
    const store = new VStore(new ChatUserConversationsApi(), ChatConversationModel);

    onMounted(() => {
      // store.find(Number(route.params.conversationId));
    });

    return {
      ...toRefs(props),
      store,
      record: store.record,
    };
  },
});

export default ChatUserConversationsNew;
</script>

<template lang="pug">
.iest-ai-chat-user-conversations-new
  ComChatConversationsNew(:store='store')
</template>

<style lang="stylus" scoped>
.iest-ai-chat-user-conversations-new
  height 100%
  width 100%
  .breadcrumbs
    padding 0 24px
</style>
