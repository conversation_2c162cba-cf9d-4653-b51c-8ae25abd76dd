<script lang="ts">
import ComChatConversationsIndex from '@/engines/chat/components/chat/conversations/ComChatConversationsIndex.vue';
import { defineComponent } from '@vue/runtime-core';
import { ChatUserConversationsApi } from '@/engines/chat/apis/chat/user/conversations.api';
import { ChatConversationModel } from '@/engines/chat/models/chat/conversations';
import { VStore } from '@/lib/vails';

import usePolicy from '@/components/global/ta-component/ta-template-form-core/usePolicy';

const ChatUserConversationsIndex = defineComponent({
  name: 'ChatUserConversationsIndex',
  components: {
    ComChatConversationsIndex,
  },
  setup() {
    usePolicy();

    const store = new VStore(new ChatUserConversationsApi(), ChatConversationModel);

    return {
      store,
    };
  },
});

export default ChatUserConversationsIndex;
</script>

<template lang="pug">
.iest-ai-chat-user-conversations-index
  ComChatConversationsIndex(:store='store')
</template>

<style lang="stylus" scoped>
.iest-ai-chat-user-conversations-index
  height 100%
  width 100%
</style>
