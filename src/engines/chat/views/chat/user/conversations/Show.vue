<script lang="ts">
import ComChatConversationsShow from '@/engines/chat/components/chat/conversations/ComChatConversationsShow.vue';
import { defineComponent, toRefs, onMounted } from 'vue';
import { ChatUserConversationsApi } from '@/engines/chat/apis/chat/user/conversations.api';
import { ChatConversationModel } from '@/engines/chat/models/chat/conversations';
import { VStore } from '@/lib/vails';
import { useRoute } from 'vue-router';
import { ChatUserMessagesApi } from '@/engines/chat/apis/chat/user/messages.api';
import { ChatUserMentionsApi } from '@/engines/chat/apis/chat/user/mentions.api';
import { ChatUserMentionVersionsApi } from '@/engines/chat/apis/chat/user/mention_versions.api';
import { useCable } from '@/engines/base/channels/useCable';

const ChatUserConversationsShow = defineComponent({
  name: 'ChatUserConversationsShow',
  components: {
    ComChatConversationsShow,
  },
  setup(props) {
    const route = useRoute();
    const conversationId = Number(route.params.conversationId);

    const store = new VStore(new ChatUserConversationsApi(), ChatConversationModel);
    const messageStore = new VStore(
      new ChatUserMessagesApi({
        parents: [{ type: 'conversations', id: conversationId }],
        params: { q: { s: ['id asc'] } },
      }),
    );

    const mentionStore = new VStore(
      new ChatUserMentionsApi({
        parents: [{ type: 'conversations', id: conversationId }],
        params: { q: { s: ['id asc'] } },
      }),
    );

    const mentionVersionStore = new VStore(
      new ChatUserMentionVersionsApi({
        parents: [{ type: 'conversations', id: conversationId }],
        params: { q: { s: ['id asc'] } },
      }),
    );

    onMounted(() => {
      store.find(conversationId);
      messageStore.index({ per_page: 999999 });
      mentionStore.index({ per_page: 999999 });
    });

    mentionStore.extra.cable_key = 'chat_mentions';
    useCable(mentionStore, {
      callback: { afterCreate: () => mentionStore.index({ per_page: 999999 }) },
    });

    return {
      ...toRefs(props),
      store,
      record: store.record,
      messageStore,
      mentionStore,
      mentionVersionStore,
    };
  },
});

export default ChatUserConversationsShow;
</script>

<template lang="pug">
.iest-ai-chat-user-conversations-show
  ComChatConversationsShow(
    v-if='record.id',
    :store='store',
    :messageStore='messageStore',
    :mentionStore='mentionStore',
    :mentionVersionStore='mentionVersionStore',
  )
</template>

<style lang="stylus" scoped>
.iest-ai-chat-user-conversations-show
  height 100%
  width 100%
  .breadcrumbs
    padding 0 24px
</style>
