<script lang="ts">
import { computed, defineComponent, toRefs } from 'vue';
import { ChatMessageType, ChatMention } from '../../../types/types';
import ComChatConversationTag from '../conversations/ComChatConversationTag.vue';

const ComChatMessageCard = defineComponent({
  name: 'ComChatMessageCard',
  components: {
    ComChatConversationTag,
  },
  props: {
    record: { type: Object, default: () => ({}) },
  },
  emits: ['clickMention', 'createUserMessage'],
  setup(props, { emit }) {
    const isSystemMessage = computed(() => props.record.type === ChatMessageType.System);
    const AI_CHAT_AVATAR = 'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iest/ai.png';
    const onClickedMention = (mention: ChatMention) => {
      emit('clickMention', mention, props.record);
    };

    const onCreateUserMessage = (content: string) => {
      emit('createUserMessage', content);
    };

    return {
      ...toRefs(props),
      ChatMessageType,
      isSystemMessage,
      AI_CHAT_AVATAR,
      onClickedMention,
      onCreateUserMessage,
    };
  },
});
export default ComChatMessageCard;
</script>

<template lang="pug">
.com-iest-message-card.w-full.text-base
  .flex.items-stretch(:class='{ "user-message": !isSystemMessage }')
    .avatar.mr-2(v-if='isSystemMessage')
      a-avatar(:src='AI_CHAT_AVATAR')
    .dots__wrapper.flex.flex-col.justify-center.mr-2.cursor-pointer(v-else)
      .dots.space-y-1(style='width:min-content;')
        //- .dot.bg-gray-500.rounded-full(v-for='i in 3' class='w-0.5 h-0.5')
    .content-with-suggestions
      .content.bg-white.p-4.space-y-4.rounded-2xl(class='dark:bg-primary-800')
        .message.text-gray-900.text-sm(class='dark:text-white') {{ record.content }}
        .flex.flex-wrap(v-if='record.actions?.length > 0')
          .action.mr-2(v-for='action in record.actions')
            a-tag.cursor-pointer(
              :color='action.color || "blue"',
              @click.stop='action.callback'
            ) {{ action.name }}
        .flex.flex-wrap.space-x-2(
          v-if='record.mentions?.length > 0',
          class='dark:text-white',
        )
          .mention-card.flex.rounded-lg.overflow-hidden.text-sm.cursor-pointer(
            v-for='mention in record.mentions',
            :key='mention.id'
            @click.stop='onClickedMention(mention)'
          )
            .bg-gray-100.p-2.pl-3.flex.items-center(class='dark:bg-primary-700') {{ "</>" }}
            .bg-gray-50.mention.p-2.pr-3(class='dark:bg-primary-900')
              .name {{ mention.name }}
              .notice.text-xs.text-gray-400(class='dark:text-primary-300') 点击查看详情
        TaAttachments.min-w-100(
          v-if='record.attachment?.files?.length > 0',
          :attachments='record.attachment?.files',
        )
      .flex.flex-col.mt-2(v-if='record.suggestions?.filter(s => s.action_type == "question")?.length > 0')
        ComChatConversationTag.w-fit.mr-2.mb-2.cursor-pointer.px-10px.py-2.bg-white.rounded-lg(
          v-for='suggestion in record.suggestions?.filter(s => s.action_type == "question")',
          :value='suggestion.name',
          color='blue',
          @click.stop='onCreateUserMessage(suggestion.name)'
        )
</template>

<style lang="stylus" scoped>
.com-iest-message-card
  .user-message
    @apply justify-end;
    .content
      border-top-right-radius 0
      border-top-left-radius 8px
  .content
    border-top-left-radius 0
.w-fit
  width fit-content
</style>
