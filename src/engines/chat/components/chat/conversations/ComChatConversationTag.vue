<script lang="ts">
import { ref, defineComponent, toRefs } from 'vue';

const ComChatConversationTag = defineComponent({
  name: 'ComChatConversationTag',
  components: {},
  props: {
    value: { type: String, default: '' },
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
export default ComChatConversationTag;
</script>

<template lang="pug">
.com-iest-conversation-tag.flex.items-center
  .rounded-lg.w-6.h-6.bg-yellow-100.flex.items-center.justify-center.mr-1(
    class='dark:bg-yellow-900'
  )
    TaIcon.text-yellow-800(
      type='flowbite-v2-solid/fire' class='!w-3 !h-3 dark:text-yellow-400'
    )
  .text-sm.text-gray-500(
    class='hover:text-blue-500 dark:text-white'
  ) {{value}}
</template>

<style lang="stylus" scoped>
.dark .com-iest-conversation-tag
  border-radius: var(--rounded-lg, 0.5rem);
  background: linear-gradient(90deg, rgba(46, 84, 138, 0.80) 0%, rgba(46, 84, 138, 0.00) 102.36%);
</style>
