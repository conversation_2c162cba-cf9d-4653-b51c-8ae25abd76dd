<script lang="ts">
import { defineComponent, toRefs, ref, inject, Ref } from 'vue';
import { AuthSessionApi } from '../../../../login/apis/auth/session.api';
import ComChatConversationInput from './ComChatConversationInput.vue';
import { useRoute, useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import { ComChatConversationLayoutCacheCacheKey } from './ComChatConversationLayout.vue';
import { VObject } from '../../../../../lib/vails/model/index';
import ComChatConversationTag from './ComChatConversationTag.vue';
import { ChatNewSuggtionsFn } from '@/components/chatConfig';

const ComChatConversationsNew = defineComponent({
  name: 'ComChatConversationsNew',
  components: { ComChatConversationInput, ComChatConversationTag },
  props: {
    store: { type: Object, required: true },
    type: { type: String, default: '' },
  },
  emits: [],
  setup(props, { emit }) {
    const AI_BG_URL =
      'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/iest/hangzhoumainicon.png';
    const loading = ref(false);
    const route = useRoute();
    const router = useRouter();
    const suggestionType = String(route.query.type || route.meta?.type || props.type);

    const suggestionOptionMap: VObject = {
      normal: {
        placeholder: '请告诉我您要发送的消息~',
        suggestions: ChatNewSuggtionsFn(suggestionType),
      },
    };

    const suggestionOptions = suggestionOptionMap[suggestionType] || suggestionOptionMap.normal;

    const value = ref('');
    const cache = inject<Ref<VObject>>(ComChatConversationLayoutCacheCacheKey);

    const onCreate = (content: string, files = []) => {
      if (loading.value) return;

      loading.value = true;
      const record = props.store.new({});

      record
        .save()
        .then(() => {
          cache!.value.newMessage = content;
          cache!.value.newMessageFiles = files;
          router.push(`${cache?.value?.routeBase}${record.id}`);
        })
        .catch(() => {
          message.error('创建会话失败');
        })
        .finally(() => {
          loading.value = false;
        });
    };

    let timeDesc = '上午';

    switch (true) {
      case new Date().getHours() >= 12 && new Date().getHours() < 18:
        timeDesc = '下午';
        break;
      case new Date().getHours() >= 18:
        timeDesc = '晚上';
        break;
    }

    return {
      ...toRefs(props),
      value,
      onCreate,
      currentUser: AuthSessionApi.currentUser(),
      timeDesc,
      suggestionOptions,
      loading,
      AI_BG_URL,
      title: process.env.VUE_APP_AI_TITLE || '智能助手',
    };
  },
});
export default ComChatConversationsNew;
</script>

<template lang="pug">
.com-chat-user-conversations-new.w-full.h-full.flex.flex-col.items-center
  .flex.flex-col.items-center.space-y-6.h-full.pt-13(class='w-2/3')
    .w-72.h-72.relative.mb-6
      img.logo.w-full.h-full.object-cover(:src='AI_BG_URL')
      .absolute.text-2xl.font-semibold.text-black.bottom-0.left-0.right-0.mx-auto.underline.underline-offset-16.decoration-blue-500.whitespace-nowrap.w-full.text-center(
        class='dark:text-white'
      ) {{ title }}
    //- .logo.text-4xl.text-primary.font-bold.mb-4
    //-   | {{ timeDesc }}好，{{ currentUser.name }}
    ComChatConversationInput.h-fit.w-full(
      v-model:value='value',
      :loading='loading',
      :minRows='4',
      :maxRows='5',
      :isSuffix='true',
      :placeholder='suggestionOptions.placeholder',
      @confirm='onCreate'
    )
    .suggestions.w-full.flex.justify-center
      .flex.flex-wrap
        ComChatConversationTag.mr-2.mb-2.cursor-pointer.px-10px.py-2.bg-white.rounded-lg(
          v-for='suggestion in suggestionOptions.suggestions',
          :value='suggestion.name',
          color='blue',
          @click.stop='onCreate(suggestion.name)'
        )
          | {{ suggestion.name }}
</template>

<style lang="stylus" scoped>
.com-chat-user-conversations-new
  height 100%
  .h-fit
    height fit-content
</style>
