import { ChatMessageType } from '@/engines/chat/types/types';
import { VObject } from '@/lib/vails';
import { message } from 'ant-design-vue';
import { inject, nextTick, onBeforeUnmount, Ref, watch } from 'vue';
import { ComChatConversationLayoutCacheCacheKey } from './ComChatConversationLayout.vue';
import { AudioStreamPlayer } from '../AudioStreamPlayer';

let seq = 0;

export const useConversationMessage = (store: any, loading: Ref<boolean>) => {
  const cache = inject<Ref<VObject>>(ComChatConversationLayoutCacheCacheKey)!;
  let player: AudioStreamPlayer | null = null;

  const loadingMessage = store.new({
    id: 'loading',
    content: 'AI处理中...',
    type: ChatMessageType.System,
  });

  // 发送消息到服务器并处理响应
  const responseToMessage = async (content: string, files: VObject[] = []) => {
    if (loading.value) return;

    loading.value = true;

    try {
      // 创建并保存用户消息
      const userMessage = store.new({
        content,
        attachment: { files },
        type: ChatMessageType.User,
      });

      await userMessage.save({
        params: {
          limit_identifier: seq++,
        },
      });

      store.records.value.push(loadingMessage);

      // 创建并保存系统消息
      const systemMessage = store.new({
        previous_content: content,
        type: ChatMessageType.System,
      });

      await systemMessage.save({
        params: {
          limit_identifier: seq++,
        },
      });

      // 如果启用了音频功能，处理音频播放
      if (cache && cache.value.audioLoading) {
        try {
          // 初始化音频播放器
          if (player) {
            player.dispose(); // 清理之前的实例
          }
          player = new AudioStreamPlayer();

          // 连接WebSocket
          const wsUrl = `${
            process.env.VUE_APP_AUDIO_STREAM_BASE_URL || 'ws://localhost:3000/chat/user/speech'
          }/generate.json`;

          await player.connect(wsUrl);

          // 发送合成请求
          if (player.ws && player.ws.readyState === WebSocket.OPEN) {
            const request = {
              text: systemMessage.content,
              options: {
                voice: 'yina',
                format: 'pcm',
                speech_rate: -125,
              },
            };
            player.ws.send(JSON.stringify(request));
          } else {
            console.error('WebSocket未连接');
          }
        } catch (error) {
          console.error('音频处理失败:', error);
          message.error('音频处理失败');
        }
      }

      // 移除加载消息
      nextTick(() => {
        store.records.value.splice(store.records.value.length - 2, 1);
      });
    } catch (error) {
      console.error('发送消息失败:', error);
      message.error('发送消息失败');
    }
    loading.value = false;
  };

  // 监听音频开关状态
  watch(
    () => cache.value.audioLoading,
    value => {
      if (!value && player) {
        player.dispose();
        player = null;
      }
    },
  );

  // 组件卸载时清理资源
  onBeforeUnmount(() => {
    if (player) {
      player.dispose();
      player = null;
    }
  });

  return {
    responseToMessage,
  };
};
