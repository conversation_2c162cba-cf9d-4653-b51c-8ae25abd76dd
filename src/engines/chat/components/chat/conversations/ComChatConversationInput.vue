<script lang="ts">
import { ref, defineComponent, toRefs, computed, watch, onBeforeUnmount, inject, Ref } from 'vue';
import { message } from 'ant-design-vue';

import { DirectiveBinding } from 'vue';
import { VObject } from '@/lib/vails';
import { AudioStreamClient } from '../AudioStreamClient';
import { ComChatConversationLayoutCacheCacheKey } from './ComChatConversationLayout.vue';
import AudioEventCenter from '../AudioEventCenter';

const enterHandler = {
  mounted(el: HTMLElement, binding: DirectiveBinding) {
    const textarea = el.querySelector('textarea') || el;

    if (!textarea) return;

    textarea.addEventListener('keydown', (e: KeyboardEvent) => {
      if (e.key === 'Enter') {
        if (e.shiftKey) {
          // Shift+Enter: 允许换行，不做任何特殊处理
          return;
        } else {
          // 仅 Enter: 阻止默认行为并触发自定义处理函数
          console.log('//////');

          e.preventDefault();
          if (typeof binding.value === 'function') {
            binding.value(e);
          }
        }
      }
    });
  },
};

const ComChatConversationInput = defineComponent({
  name: 'ComChatConversationInput',
  components: {},
  directives: { enterHandler },
  props: {
    value: { type: String, default: '' },
    loading: { type: Boolean, default: false },
    placeholder: { type: String, default: '请输入内容' },
    minRows: { type: Number, default: 1 },
    maxRows: { type: Number, default: 4 },
    isSuffix: { type: Boolean, default: false },
  },
  emits: ['update:value', 'confirm'],
  setup(props, { emit }) {
    const localValue = computed({
      get: () => props.value,
      set: value => {
        emit('update:value', value)
        if (!value) {
          textCacheMap.clear();
        }
      },
    });

    const files = ref<VObject[]>([]);
    const fileUploader = ref<any>(null);
    const fileAllSettled = ref(true);

    const onFilesClick = () => {
      fileUploader.value.openFile();
    };

    const onConfirm = () => {
      if (!localValue.value) {
        message.warning('请输入内容');
        return;
      }

      if (!fileAllSettled.value) {
        message.warning('文件上传中，请稍后');
        return;
      }

      emit('confirm', localValue.value, files.value);
      localValue.value = '';
      files.value = [];
      textCacheMap.clear()
    };

    const showIcon = ref(true);

    watch(
      () => props.loading,
      () => {
        if (!props.loading) {
          setTimeout(() => {
            showIcon.value = true;
          }, 500);
        } else {
          showIcon.value = false;
        }
      },
    );

    const cache = inject<Ref<VObject>>(ComChatConversationLayoutCacheCacheKey)!;
    const audioLoading = ref(!!cache.value.audioLoading);

    // / 创建音频流客户端实例
    const audioClient = new AudioStreamClient(
      String(
        `${process.env.VUE_APP_AUDIO_STREAM_BASE_URL || 'ws://localhost:3000/chat/user/speech'
        }/recognize.json`,
      ),
    );

    const textCacheMap = new Map()
    // 设置回调函数
    audioClient.setCallbacks({
      onStart: () => {
        console.log('开始录音');
      },
      onStop: () => {
        console.log('停止录音');
      },
      onResult: (result: any) => {
        console.log('收到识别结果:', result);

        if (
          result.header.status === 20000000 &&
          result.header.name === 'TranscriptionResultChanged'
        ) {
          const text = result.payload.result;
          const index = result.payload.index;
          textCacheMap.set(index, text)

          localValue.value = Array.from(textCacheMap.values()).join('');
          waitingCount = 0;
        }
      },
      onError: (error: any) => {
        console.error('发生错误:', error);
        audioLoading.value = false;
      },
      onClose: () => {
        console.log('连接已关闭');
        audioLoading.value = false;
      },
    });

    onBeforeUnmount(() => {
      audioLoading.value = false;
    });

    const onAudioClick = () => {
      if (audioLoading.value) {
        audioLoading.value = false;
      } else {
        audioLoading.value = true;
      }
    };

    let audioMessageTimer: any = null;
    let waitingCount = 0;

    const confirmAudioMessageLater = () => {
      if (audioMessageTimer) {
        clearTimeout(audioMessageTimer);
        audioMessageTimer = null;
      }

      waitingCount += 1;

      if (waitingCount >= 4) {
        // 4 秒未变化，自动发送
        if (localValue.value && !props.loading) {
          onConfirm();
        } else {
          waitingCount = 0;
        }
      }
      // 1 秒 1 循环
      audioMessageTimer = setTimeout(() => {
        confirmAudioMessageLater();
      }, 1000);
    };

    const clearAudioMessageTimer = () => {
      if (audioMessageTimer) {
        clearTimeout(audioMessageTimer);
        audioMessageTimer = null;
      }
    };


    AudioEventCenter.on('audioPlaying', (isPlaying: boolean) => {
      if (isPlaying) {
        audioClient.suspend()
      } else {
        audioClient.resume()
      }
    });

    watch(
      audioLoading,
      (value: boolean) => {
        if (value) {
          audioClient.startRecording();
          confirmAudioMessageLater();
          cache.value.audioLoading = true;
        } else {
          audioClient.stopRecording();
          clearAudioMessageTimer();
          cache.value.audioLoading = false;
        }
      },
      { immediate: true },
    );

    return {
      ...toRefs(props),
      localValue,
      showIcon,
      onConfirm,
      fileUploader,
      onFilesClick,
      files,
      fileAllSettled,
      onAudioClick,
      audioLoading,
    };
  },
});
export default ComChatConversationInput;
</script>

<template lang="pug">
.com-iest-conversation-input.h-full
  .mp-12.flex.space-x-2.relative
    a-textarea(
      v-model:value='localValue',
      :autoSize='{ minRows, maxRows }',
      :placeholder='placeholder',
      size='large'
      v-enter-handler='onConfirm'
      allow-clear
    )
    .flex.items-center(v-if='!isSuffix')
      a-button(
        :class='{ "audio-loading": audioLoading }'
        type='text'
        class='!text-blue-500'
        @click='onAudioClick'
      )
        .flex.justify-center.items-center.relative
          .spot-shell.absolute.-left-3.-top-3.w-10.h-10(v-if='audioLoading')
            .breathing-light
          TaIcon.z-10(type='AudioOutlined', size='1rem')
      a-button(
        :disabled='loading',
        style='height: 100%',
        class='!text-blue-500'
        type='text'
        @click='onFilesClick'
      )
        .flex.justify-center.items-center
          TaIcon(v-if='!loading && showIcon', type='FileTextOutlined', size='1rem')
          TaIcon(v-else, type='LoadingOutlined', size='1rem')
      a-button(
        v-if='!isSuffix'
        :disabled='loading',
        style='height: 100%',
        class='!text-blue-500'
        type='text'
        @click='onConfirm'
      )
        .flex.justify-center.items-center
          TaIcon(v-if='!loading && showIcon', type='SendOutlined', size='1rem')
          TaIcon(v-else, type='LoadingOutlined', size='1rem')


    .absolute.right-2.bottom-2.z-99.flex(v-else)
      a-button(
        :class='{ "audio-loading": audioLoading }'
        type='text'
        class='!text-blue-500'
        @click='onAudioClick'
      )
        .flex.justify-center.items-center.relative
          .spot-shell.absolute.-left-3.-top-3.w-10.h-10(v-if='audioLoading')
            .breathing-light
          TaIcon.z-10(type='AudioOutlined', size='1rem')
      a-button(
        type='text'
        :disabled='loading',
        class='!text-blue-500'
        @click='onFilesClick'
      )
        .flex.justify-center.items-center.w-full.h-full
          TaIcon(v-if='!loading && showIcon', type='FileTextOutlined', size='1rem')
          TaIcon(v-else, type='LoadingOutlined', size='1rem')
      a-button(
        type='text'
        :disabled='loading',
        class='!text-blue-500'
        @click='onConfirm'
      )
        .flex.justify-center.items-center.w-full.h-full
          TaIcon(v-if='!loading && showIcon', type='SendOutlined', size='1rem')
          TaIcon(v-else, type='LoadingOutlined', size='1rem')
  TaFileUploader(
    ref='fileUploader',
    v-model:isAllSettled='fileAllSettled',
    v-model:value='files',
    :multiple='false',
    class='!block',
  )
    .empty
</template>

<style lang="stylus" scoped>
.com-iest-conversation-input
  >>> .trigger-box
    @apply block;
  >>> .file-list
    @apply p-0;
  >>> .attachment
    @apply bg-white mt-2 py-1;
  .audio-loading
    @apply animate-bubble !bg-primary-200;

.breathing-light
  width 2.5rem
  height 2.5rem
  background-color $primary-color-200
  border-radius 50%
  animation breathing 1s ease-in-out infinite
  box-shadow 0 0 20px $primary-color-200

@keyframes breathing
  0%
    opacity 0.4
    // box-shadow 0 0 20px $primary-color-400
    transform scale(0.8)
  50%
    opacity 1
    // box-shadow 0 0 40px $primary-color-100
    transform scale(1)
  100%
    opacity 0.4
    // box-shadow 0 0 20px $primary-color-400
    transform scale(0.8)
//
.dark .com-iest-conversation-input
  >>> textarea
    @apply border-none bg-primary-900 rounded-lg text-white;
  >>> .ant-btn
    @apply bg-primary-800;
  >>> .attachment-wrapper
    @apply text-white;
// .com-iest-conversation-input
</style>
