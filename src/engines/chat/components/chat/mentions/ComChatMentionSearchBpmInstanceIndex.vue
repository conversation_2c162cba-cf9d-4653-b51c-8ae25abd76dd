<script lang="ts">
import { ref, defineComponent, toRefs, inject, Ref, onMounted, onUnmounted, computed } from 'vue';
import { VObject } from '../../../../../lib/vails/model/index';
import { ComChatConversationLayoutCacheCacheKey } from '../conversations/ComChatConversationLayout.vue';
import { VStore } from '../../../../../lib/vails/store/index';
import { BpmUserUnreadInstances } from '../../../../bpm/bpm-core/apis/user/unread/instance.api';
import { InstanceModel } from '../../../../bpm/bpm-core/apis/user/instance.api';
import ComBpmInstanceCard from '@/engines/bpm/components/ComBpmInstanceCard.vue';

const ComChatMentionSearchBpmInstanceShow = defineComponent({
  name: 'ComChatMentionSearchBpmInstanceShow',
  components: { ComBpmInstanceCard },
  props: {
    record: { type: Object, default: () => ({}) },
  },
  setup(props) {
    const cache = inject<Ref<VObject>>(ComChatConversationLayoutCacheCacheKey)!;

    const api = new BpmUserUnreadInstances();
    api.namespace = `/bpm/user/${props.record.payload?.mode || 'approving'}`;
    const store = new VStore(api, InstanceModel);

    const config = computed(() => ({
      recordName: props.record.payload?.mode_zh || '待办',
      store,
      mode: 'list',
      params: props.record.payload?.params || {},
      pagination: {
        hideOnSinglePage: true,
      },
      list: {
        scroll: {
          y: 'auto',
        },
      },
    }));

    const visibleMap = ref<VObject>({});

    const onShow = (record: VObject) => {
      visibleMap.value[`instance-${record.id}`] = true;
    };

    onMounted(() => {
      cache.value.mentionWidth = '580px';
    });

    onUnmounted(() => {
      cache.value.mentionWidth = undefined;
    });

    return {
      ...toRefs(props),
      config,
      visibleMap,
      onShow,
    };
  },
});
export default ComChatMentionSearchBpmInstanceShow;
</script>

<template lang="pug">
.com-iest-mention-search-bpm-instance-show.px-4.h-full
  TaIndexView(:config='config', @onShow='onShow')
    //- template(#header)
    //-   .empty
    template(#card='{ record, actions }')
      ComBpmInstanceCard(:record='record',:hideSeq='true', @dialogClose='actions.silenceRefresh')
      ComBpmInstanceDetailEasyDialogFromIndex(
        v-if='visibleMap[`instance-${record.id}`]',
        v-model:visible='visibleMap[`instance-${record.id}`]',
        :instance='record',
        @close='actions.silenceRefresh'
      )
</template>

<style lang="stylus" scoped>
.dark .com-iest-mention-search-bpm-instance-show
  >>> .ta-index-view-header
    @apply bg-transparent;
  >>> .table-header__title
    @apply text-white;
</style>
