<script lang="ts">
import { ref, defineComponent, toRefs, inject, Ref, onMounted, onUnmounted, computed } from 'vue';
import { VObject } from '../../../../../lib/vails/model/index';
import { ComChatConversationLayoutCacheCacheKey } from '../conversations/ComChatConversationLayout.vue';
import { VStore } from '../../../../../lib/vails/store/index';
import { ResAdminUsersApi } from '../../../../../engines/res/res-core/apis/res/admin/users.api';
import { ResUserModel } from '../../../../../engines/res/res-core/models/res/user';

const ComChatMentionUserListShow = defineComponent({
  name: 'ComChatMentionUserListShow',
  components: {},
  props: {
    record: { type: Object, default: () => ({}) },
  },
  setup(props) {
    const cache = inject<Ref<VObject>>(ComChatConversationLayoutCacheCacheKey)!;

    // 用户列表数据管理
    const api = new ResAdminUsersApi();
    const store = new VStore(api, ResUserModel);

    // 组件状态
    const selectedUsers = ref<VObject[]>([]);
    const messageContent = ref('');
    const sendTime = ref('');
    const loading = ref(false);

    // 配置用户列表显示
    const config = computed(() => ({
      recordName: '用户列表',
      store,
      mode: 'list',
      params: props.record.payload?.params || {},
      pagination: {
        hideOnSinglePage: true,
        pageSize: 20,
      },
      list: {
        scroll: {
          y: 'auto',
        },
      },
      searcherSimpleOptions: [
        { key: 'name', label: '姓名', type: 'string' },
        { key: 'account', label: '账号', type: 'string' },
      ],
    }));

    // 用户选择处理
    const onSelectUser = (user: VObject) => {
      const index = selectedUsers.value.findIndex(u => u.id === user.id);
      if (index > -1) {
        selectedUsers.value.splice(index, 1);
      } else {
        selectedUsers.value.push(user);
      }
    };

    const isUserSelected = (user: VObject) => {
      return selectedUsers.value.some(u => u.id === user.id);
    };

    // 消息发送处理
    const onSendMessage = async () => {
      if (!messageContent.value.trim()) {
        // 显示错误提示
        return;
      }

      if (selectedUsers.value.length === 0) {
        // 显示错误提示：请选择接收用户
        return;
      }

      loading.value = true;

      try {
        // 构建消息发送数据
        const messageData = {
          content: messageContent.value,
          send_at: sendTime.value || new Date().toISOString(),
          user_ids: selectedUsers.value.map(u => u.id),
          type: 'user_message',
        };

        // 调用消息发送API
        // 这里需要根据实际的API接口进行调用
        console.log('发送消息数据:', messageData);

        // 发送成功后的处理
        messageContent.value = '';
        selectedUsers.value = [];
        sendTime.value = '';

        // 显示成功提示
      } catch (error) {
        console.error('消息发送失败:', error);
        // 显示错误提示
      } finally {
        loading.value = false;
      }
    };

    // 清空选择
    const onClearSelection = () => {
      selectedUsers.value = [];
    };

    // 预设消息模板
    const messageTemplates = ref([
      { name: '廉洁提醒', content: '请注意廉洁自律，严格遵守相关规定。' },
      { name: '会议通知', content: '请按时参加会议，准备相关材料。' },
      { name: '工作提醒', content: '请及时完成相关工作任务。' },
    ]);

    const onSelectTemplate = (template: VObject) => {
      messageContent.value = template.content;
    };

    // 组件生命周期
    onMounted(() => {
      cache.value.mentionWidth = '680px';
      // 加载用户列表
      store.index({ per_page: 50 });
    });

    onUnmounted(() => {
      cache.value.mentionWidth = undefined;
    });

    return {
      ...toRefs(props),
      config,
      selectedUsers,
      messageContent,
      sendTime,
      loading,
      onSelectUser,
      isUserSelected,
      onSendMessage,
      onClearSelection,
      messageTemplates,
      onSelectTemplate,
      users: store.records,
    };
  },
});

export default ComChatMentionUserListShow;
</script>

<template lang="pug">
.com-chat-mention-user-list-show.px-4.h-full.flex.flex-col
  .header.mb-4.border-b.pb-4
    .title.text-lg.font-semibold.mb-2 用户消息发送
    .selected-count.text-sm.text-gray-600
      | 已选择 {{ selectedUsers.length }} 个用户
      a-button.ml-2(v-if='selectedUsers.length > 0', size='small', @click='onClearSelection') 清空选择

  .content.flex-1.flex.gap-4
    //- 左侧：用户列表
    .user-list.flex-1.border.rounded.p-4
      .list-header.mb-3
        .title.font-medium 选择用户
        .search.mt-2
          a-input(placeholder='搜索用户姓名、部门...', size='small')

      .user-items.space-y-2.max-h-400.overflow-y-auto
        .user-item.flex.items-center.p-2.border.rounded.cursor-pointer(
          class='hover:bg-gray-50',
          v-for='user in users',
          :key='user.id',
          :class='{ "bg-blue-50 border-blue-300": isUserSelected(user) }',
          @click='onSelectUser(user)'
        )
          a-checkbox.mr-3(:checked='isUserSelected(user)', @click.stop='onSelectUser(user)')
          .user-info.flex-1
            .name.font-medium {{ user.name }}
            .department.text-sm.text-gray-500 {{ user.departmentNames }}
            .org.text-xs.text-gray-400 {{ user.orgNames }}

    //- 右侧：消息编辑
    .message-editor.w-80.border.rounded.p-4
      .editor-header.mb-3
        .title.font-medium 编辑消息

      //- 消息模板
      .templates.mb-4
        .template-title.text-sm.text-gray-600.mb-2 快速模板
        .template-buttons.space-x-2
          a-button.text-xs(
            v-for='template in messageTemplates',
            :key='template.name',
            size='small',
            @click='onSelectTemplate(template)'
          ) {{ template.name }}

      //- 消息内容
      .message-content.mb-4
        .label.text-sm.text-gray-600.mb-2 消息内容
        a-textarea(
          v-model:value='messageContent',
          placeholder='请输入要发送的消息内容...',
          :rows='6',
          :maxlength='500',
          show-count
        )

      //- 发送时间
      .send-time.mb-4
        .label.text-sm.text-gray-600.mb-2 发送时间
        a-date-picker(
          v-model:value='sendTime',
          show-time,
          placeholder='选择发送时间（可选）',
          style='width: 100%',
          size='small'
        )

      //- 发送按钮
      .actions
        a-button.w-full(
          type='primary',
          :loading='loading',
          :disabled='!messageContent.trim() || selectedUsers.length === 0',
          @click='onSendMessage'
        ) 发送消息 ({{ selectedUsers.length }})

  //- 已选用户列表
  .selected-users.mt-4.border-t.pt-4(v-if='selectedUsers.length > 0')
    .title.text-sm.text-gray-600.mb-2 已选择的用户
    .selected-list.flex.flex-wrap.gap-2
      a-tag.cursor-pointer(
        v-for='user in selectedUsers',
        :key='user.id',
        closable,
        @close='onSelectUser(user)'
      ) {{ user.name }} ({{ user.departmentNames }})
</template>

<style lang="stylus" scoped>
.com-chat-mention-user-list-show
  .user-item
    transition all 0.2s ease
    &:hover
      transform translateY(-1px)
      box-shadow 0 2px 4px rgba(0, 0, 0, 0.1)
  .message-editor
    .ant-textarea
      resize none
    .ant-btn[disabled]
      opacity 0.5
      cursor not-allowed
.dark .com-chat-mention-user-list-show
  .user-item
    @apply border-gray-600 hover
    :bg-gray-800, &.selected
      @apply bg-blue-900 border-blue-500
  .message-editor
    @apply border-gray-600 bg-gray-800
</style>
