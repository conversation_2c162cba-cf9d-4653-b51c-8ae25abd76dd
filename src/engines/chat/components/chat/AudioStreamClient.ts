import { message } from 'ant-design-vue';

export class AudioStreamClient {
  websocketUrl = '';
  websocket: any;
  mediaRecorder: any = null;
  audioContext: any = null;
  audioInput: any = null;
  processor: any = null;
  isRecording: any = null;
  stream: any = null;
  private isStreamEnded: boolean = false;

  onResult: null | ((result: string) => void) = null;
  onStart: null | (() => void) = null;
  onStop: null | (() => void) = null;
  onError: null | ((error: any) => void) = null;
  onClose: null | (() => void) = null;

  // 音频配置
  config = {
    sampleRate: 16000, // 采样率
    sampleBits: 16, // 采样位数
    channelCount: 1, // 声道数
    bufferSize: 4096, // 缓冲区大小
  };

  constructor(websocketUrl: string) {
    this.websocketUrl = websocketUrl;
    this.websocket = null;
  }

  // 初始化WebSocket连接
  initWebSocket() {
    if (this.websocket) {
      this.websocket.close();
    }

    this.websocket = new WebSocket(this.websocketUrl);

    this.websocket.onopen = () => {
      console.log('WebSocket连接已建立');
      message.success('请开始说话');
    };

    this.websocket.onmessage = (event: any) => {
      const result = JSON.parse(event.data);
      // 处理识别结果
      if (result.header) {
        // console.log('识别结果:', result);
        // 触发结果事件
        this.onResult && this.onResult(result);
      }
    };

    this.websocket.onerror = (error: any) => {
      console.error('WebSocket错误:', error);
      this.onError && this.onError(error);
    };

    this.websocket.onclose = () => {
      console.log('WebSocket连接已关闭');
      message.warning('识别结束');

      this.onClose && this.onClose();
    };
  }

  // 开始录音
  async startRecording() {
    if (this.isRecording) return;
    message.loading('识别开启中..');

    try {
      // 请求麦克风权限
      this.stream = await navigator.mediaDevices.getUserMedia({
        audio: {
          sampleRate: this.config.sampleRate,
          channelCount: this.config.channelCount,
          echoCancellation: true,
          // noiseSuppression: true,
        },
      });

      this.isRecording = true;
      this.audioContext = new (window.AudioContext || (window as any).webkitAudioContext)({
        sampleRate: this.config.sampleRate,
      });

      // 创建音频处理节点
      this.audioInput = this.audioContext.createMediaStreamSource(this.stream);
      this.processor = this.audioContext.createScriptProcessor(
        this.config.bufferSize,
        this.config.channelCount,
        this.config.channelCount,
      );

      // 连接节点
      this.audioInput.connect(this.processor);
      this.processor.connect(this.audioContext.destination);

      // 初始化WebSocket连接
      this.initWebSocket();

      // 处理音频数据
      this.processor.onaudioprocess = this.processAudioData.bind(this);

      this.onStart && this.onStart();
    } catch (error) {
      console.error('启动录音失败:', error);
      this.onError && this.onError(error);
    }
  }

  private handleStreamEnd(finalData: any) {
    if (this.isRecording && this.websocket && this.websocket.readyState === WebSocket.OPEN) {
      // 处理最后的音频数据
      this.websocket.send(finalData.buffer);
      console.log('最后的音频数据已发送');
    }
  }

  // 处理音频数据
  processAudioData(audioProcessingEvent: any) {
    if (!this.isRecording || !this.websocket || this.websocket.readyState !== WebSocket.OPEN) {
      return;
    }

    const inputBuffer = audioProcessingEvent.inputBuffer;
    const inputData = inputBuffer.getChannelData(0);

    // 将Float32Array转换为Int16Array
    const pcmData = new Int16Array(inputData.length);
    for (let i = 0; i < inputData.length; i++) {
      pcmData[i] = inputData[i] * 0x7fff;
    }

    // 发送音频数据
    if (this.websocket.readyState === WebSocket.OPEN) {
      console.log('sended');

      this.websocket.send(pcmData.buffer);
    }

    if (this.isStreamEnded && pcmData.length > 0) {
      this.handleStreamEnd(pcmData.at(-1));
    }
  }

  suspend() {
    this.stream.getTracks().forEach((track: any) => (track.enabled = false));
  }

  resume() {
    this.stream.getTracks().forEach((track: any) => (track.enabled = true));
  }

  // 停止录音
  stopRecording() {
    if (!this.isRecording) return;

    this.isRecording = false;

    // 断开音频处理节点
    if (this.processor && this.audioInput) {
      this.audioInput.disconnect(this.processor);
      this.processor.disconnect(this.audioContext.destination);
      this.processor = null;
      this.audioInput = null;
      this.stream.getTracks().forEach((track: any) => track.stop());
    }

    // 关闭音频上下文
    if (this.audioContext) {
      this.audioContext.close();
      this.audioContext = null;
    }

    // 关闭WebSocket连接
    if (this.websocket) {
      this.websocket.close();
      this.websocket = null;
    }

    this.onStop && this.onStop();
  }

  // 设置回调函数
  setCallbacks({ onStart, onStop, onResult, onError, onClose }: any) {
    this.onStart = onStart;
    this.onStop = onStop;
    this.onResult = onResult;
    this.onError = onError;
    this.onClose = onClose;
  }
}
