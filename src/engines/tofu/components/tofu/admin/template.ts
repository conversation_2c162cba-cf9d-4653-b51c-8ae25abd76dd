import { TaTemplateFormItem } from '../../../../../components/global/ta-component/ta-template-form-core/types';
import { permitTemplate } from '../../../../res/permitTemplate';

export const entryTemplate: TaTemplateFormItem = {
  name: '普通布局',
  type: 'layout',
  fields: [
    {
      name: '导航信息',
      type: 'label',
      rules: [],
      model: { attr_type: 'string' },
      options: { span: 24 },
      key: 'label_1620136832367_7',
      model_key: 'label_1620136832367_7',
      fields: [],
      conditions: [],
      model_key_prefix: '',
    },
    {
      name: '导航名称',
      type: 'input',
      rules: [{ rule_type: 'required', required: true, message: '请填写正确的导航名称' }],
      model: { attr_type: 'string' },
      options: { span: 24 },
      key: 'input_1620136715081_6',
      model_key: 'name',
      fields: [],
      conditions: [],
      model_key_prefix: '',
    },
    {
      name: '类型',
      type: 'radio',
      rules: [{ rule_type: 'required', required: true, message: '请选择模块类型' }],
      model: {
        attr_type: 'string',
      },
      options: {
        select: [
          {
            label: '菜单',
            value: 'Tofu::Menu',
          },
          {
            label: '方块',
            value: 'Tofu::Node',
          },
          {
            label: '分割线',
            value: 'Tofu::Split',
          },
        ],
        multiple: false,
        span: 24,
      },
      key: 'radio_1635477492299_1',
      model_key: 'type',
      fields: [],
      conditions: [],
      model_key_prefix: '',
    },
    {
      name: '布局',
      type: 'input',
      rules: [],
      model: { attr_type: 'string' },
      options: { span: 24 },
      key: 'input_1620136715081_7',
      model_key: 'layout',
      fields: [],
      conditions: [],
      model_key_prefix: '',
    },
    {
      name: '描述',
      type: 'input',
      rules: [],
      model: { attr_type: 'string' },
      options: { span: 24 },
      key: 'input_1620136715081_7',
      model_key: 'desc',
      fields: [],
      conditions: [],
      model_key_prefix: '',
    },
    {
      name: '上级导航',
      type: 'tree',
      rules: [],
      model: { attr_type: 'string' },
      options: { span: 24, treeData: [] },
      key: 'input_1620136712681_5',
      model_key: 'parent_id',
      fields: [],
      conditions: [],
      model_key_prefix: '',
    },
    {
      name: 'antd图标 或 图片',
      type: 'input',
      rules: [],
      model: { attr_type: 'string' },
      options: { span: 24 },
      key: 'input_1620136712681_5',
      model_key: 'icon',
      fields: [],
      conditions: [],
      model_key_prefix: '',
    },
    {
      name: 'PC端链接',
      type: 'input',
      rules: [],
      model: { attr_type: 'string' },
      options: { span: 24 },
      key: 'input_1620136705564_4',
      model_key: 'url',
      fields: [],
      conditions: [],
      model_key_prefix: '',
    },
    {
      name: '打开方式',
      type: 'input',
      rules: [],
      model: { attr_type: 'string' },
      options: { span: 24 },
      key: 'input_1620136703104_3',
      model_key: 'open_mode',
      fields: [],
      conditions: [],
      model_key_prefix: '',
    },
    permitTemplate,
  ],
  options: { span: 24, label: { width: 80, align: 'left' }, theme: 'none' },
  key: 'layout_1620136674208_1',
  model_key: 'layout_1620136674208_1',
  conditions: [],
  model_key_prefix: '',
};
