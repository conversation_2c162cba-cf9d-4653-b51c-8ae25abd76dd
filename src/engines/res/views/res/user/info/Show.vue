<script lang="ts">
import { AuthSessionApi } from '@/engines/login/apis/auth/session.api';
import { GET_INFO } from '@/engines/login/store/user/actions';
import { ResUserInfoApi } from '@/engines/res/res-core/apis/res/user/info.api';
import { SoaAuthAuthPasswordApi } from '@/engines/res/res-core/apis/soa_auth/auth/password.api';
import store from '@/store';
import { message } from 'ant-design-vue';
import { computed, defineComponent, reactive, ref, toRefs } from 'vue';
import { useRoute } from 'vue-router';

const ResUserInfoShow = defineComponent({
  name: 'ResUserInfoShow',
  components: {},
  props: {
    showMobile: { type: Boolean, default: true },
    MobileEdit: { type: Boolean, default: true },
    background: { type: String, default: () => '#F7F7F7' },
  },
  setup(props) {
    const route = useRoute();
    const info = computed(() => store.state.user.currentUser);

    const editing = ref(false);
    const editInfo = reactive<{ [key: string]: string | any }>({
      mobile: '',
      email: '',
      name: '',
      avatar: {
        files: [],
      },
    });

    // 单字段编辑状态管理
    const fieldEditing = reactive({
      avatar: false,
      name: false,
      email: false,
      mobile: false,
    });

    // 单字段编辑数据
    const fieldEditData = reactive({
      avatar: { files: [] },
      name: '',
      email: '',
      mobile: '',
    });

    // 当前密码
    const currentPassword = ref('');
    // 新密码
    const newPassword = ref('');
    // 验证密码
    const verifyPassword = ref('');

    const isSubmit = computed(() => {
      if (
        newPassword.value === verifyPassword.value &&
        newPassword.value !== '' &&
        currentPassword.value !== ''
      ) {
        return true;
      } else {
        return false;
      }
    });

    const submit = () => {
      if (!isSubmit.value) return message.error('请检查密码');
      const reg =
        /((^(?=.*[a-z])(?=.*[A-Z])(?=.*\W)[\da-zA-Z\W]{8,16}$)|(^(?=.*\d)(?=.*[A-Z])(?=.*\W)[\da-zA-Z\W]{8,16}$)|(^(?=.*\d)(?=.*[a-z])(?=.*\W)[\da-zA-Z\W]{8,16}$)|(^(?=.*\d)(?=.*[a-z])(?=.*[A-Z])[\da-zA-Z\W]{8,}$))/;
      // const phoneReg = /^1[3456789]\d{9}$/;
      // if (!phoneReg.test(editInfo.mobile) && props.MobileEdit) {
      //   return message.error('请输入正确的手机号');
      // }

      if (!reg.test(newPassword.value)) {
        message.error('密码长度不小于8位,密码必须由大小写字母、数字、特殊字符三种以上组合');
      } else {
        new SoaAuthAuthPasswordApi({ encrypt: true, wrapParams: false })
          .update({
            old_password: currentPassword.value,
            new_password: newPassword.value,
          })
          .then(() => {
            message.success('修改密码成功');
            if (route.query?.login_again === 'true') {
              message.success('请重新登录');
              logout();
            }
          });
      }
    };

    const onEdit = () => {
      editInfo.mobile = info.value.mobile;
      editInfo.email = info.value.email;
      editInfo.name = info.value.name;
      editInfo.avatar.files = info.value.avatar?.files;
      editing.value = true;
    };

    const checkEmail = (v: string) => {
      const reg = /^[a-zA-Z0-9_.-]+@[a-zA-Z0-9-]+(\.[a-zA-Z0-9-]+)*\.[a-zA-Z0-9]{2,6}$/;
      return reg.test(v);
    };

    const validaEmail = (v: string) => {
      if (checkEmail(v)) {
        return true;
      } else {
        message.error('请填写正确的邮箱号');
        return false;
      }
    };

    const checkPhone = (v: string) => {
      const reg = /^1[3456789]\d{9}$/;
      const reg2 = /^0\d{2,3}-?\d{7,8}$/;
      return reg.test(v) || reg2.test(v);
    };
    const validaPhone = (v: string) => {
      if (v && v.length === 11) {
        if (checkPhone(v)) {
          return true;
        }
        message.error('请填写正确的手机号');
        return false;
      } else {
        message.error('请填写正确的手机号');
        return false;
      }
    };

    const onUpdate = () => {
      let res = true;
      if (editInfo.email) {
        res = validaEmail(editInfo.email);
      }
      if (!res) return;
      if (props.MobileEdit && editing) {
        res = validaPhone(editInfo.mobile);
      }
      if (!res) return;
      new ResUserInfoApi()
        .update(editInfo)
        .then(() => {
          message.success('修改成功');
          store.dispatch(`user/${GET_INFO}`);
          editing.value = false;
        })
        .catch(() => {
          message.error('修改失败');
        });
    };
    const logout = async () => {
      await new AuthSessionApi().logout();
      // 使用 location 跳转来彻底清空所有 store
      window.location.href = process.env?.VUE_APP_LOGIN_HREF || '/login';
    };
    const stateClick = () => {
      console.log('stateClick', store.state.user);
    };

    const passChange = (e: Event) => {
      const value = (e.target as HTMLInputElement).value
        .trim()
        .replace(/[\u4e00-\u9fa5]|(^\s+)|(\s+$)/gi, '')
        .replace(/[ ]/g, '');
      newPassword.value = value;
    };
    const passVerChange = (e: Event) => {
      const value = (e.target as HTMLInputElement).value
        .trim()
        .replace(/[\u4e00-\u9fa5]|(^\s+)|(\s+$)/gi, '')
        .replace(/[ ]/g, '');
      verifyPassword.value = value;
    };

    const phoneChange = (e: Event) => {
      const value = (e.target as HTMLInputElement).value.trim().replace(/[^\d]/g, '');
      editInfo.mobile = value;
    };

    const emailChange = (e: Event) => {
      const value = (e.target as HTMLInputElement).value
        .trim()
        .replace(/[\u4e00-\u9fa5]|(^\s+)|(\s+$)/gi, '')
        .replace(/[ ]/g, '');
      editInfo.email = value;
    };

    // 单字段编辑方法
    const startFieldEdit = (field: keyof typeof fieldEditing) => {
      fieldEditing[field] = true;
      // 初始化编辑数据
      if (field === 'avatar') {
        fieldEditData[field] = { files: info.value.avatar?.files || [] };
      } else {
        fieldEditData[field] = info.value[field] || '';
      }
    };

    const cancelFieldEdit = (field: keyof typeof fieldEditing) => {
      fieldEditing[field] = false;
      // 重置编辑数据
      if (field === 'avatar') {
        fieldEditData[field] = { files: [] };
      } else {
        fieldEditData[field] = '';
      }
    };

    const saveField = async (field: keyof typeof fieldEditing) => {
      let isValid = true;
      const value = fieldEditData[field];

      // 字段验证
      if (field === 'email' && value) {
        isValid = validaEmail(value as string);
      } else if (field === 'mobile' && value && props.MobileEdit) {
        isValid = validaPhone(value as string);
      }

      if (!isValid) return;

      try {
        // 构建只包含当前字段的更新数据
        const updateData: any = {
          id: info.value.id, // 必须包含ID用于API路由
        };
        if (field === 'avatar') {
          updateData.avatar = value;
        } else {
          updateData[field] = value;
        }

        await new ResUserInfoApi().update(updateData);
        message.success(`${getFieldLabel(field)}修改成功`);

        // 刷新用户信息
        await store.dispatch(`user/${GET_INFO}`);

        // 退出编辑状态
        fieldEditing[field] = false;
      } catch (error) {
        message.error(`${getFieldLabel(field)}修改失败`);
      }
    };

    const getFieldLabel = (field: string) => {
      const labels: { [key: string]: string } = {
        avatar: '头像',
        name: '姓名',
        email: '邮箱',
        mobile: '手机号',
      };
      return labels[field] || field;
    };

    // 单字段输入处理
    const handleFieldInput = (field: keyof typeof fieldEditData, e: Event) => {
      const value = (e.target as HTMLInputElement).value;
      if (field === 'mobile') {
        fieldEditData[field] = value.trim().replace(/[^\d]/g, '');
      } else if (field === 'email') {
        fieldEditData[field] = value
          .trim()
          .replace(/[\u4e00-\u9fa5]|(^\s+)|(\s+$)/gi, '')
          .replace(/[ ]/g, '');
      } else if (field === 'name') {
        fieldEditData[field] = value;
      }
    };

    return {
      ...toRefs(props),
      submit,
      stateClick,
      logout,
      info,
      currentPassword,
      newPassword,
      verifyPassword,
      isSubmit,
      editing,
      editInfo,
      onEdit,
      onUpdate,

      passVerChange,
      passChange,
      phoneChange,
      emailChange,

      // 单字段编辑相关
      fieldEditing,
      fieldEditData,
      startFieldEdit,
      cancelFieldEdit,
      saveField,
      handleFieldInput,
    };
  },
});
export default ResUserInfoShow;
</script>

<template lang="pug">
.user-info
  .left-box(@click='stateClick')
    .user-avatar.item-card.flex
      TaAvatar(:user='info', :size='60')
      .info
        .account {{ info.account }}
        .mobile {{ info.mobile || '暂无号码' }}
    .write-off-box.item-card
      .write-header 个人资料
      .write-off.cursor-pointer(@click='logout') 注销登录
  .right-box.h-full.overflow-y-auto
    .information-box.item-card
      .item-card-header 个人资料
      .form-item.flex.items-center
        .label.flex.items-center 头像
        template(v-if='!fieldEditing.avatar')
          .field-hover-container.avatar-field.relative.flex.items-center(class="group")
            .avatar-content(@dblclick='startFieldEdit("avatar")')
              TaAvatar(:user='info', :size='100')
            .field-edit-icon.absolute.opacity-0.transition-opacity.duration-200.bg-blue-50.rounded.w-6.h-6.flex.items-center.justify-center.cursor-pointer.text-blue-500(
              @click='startFieldEdit("avatar")',
              class="group-hover:opacity-100 hover:bg-blue-100",
              style='left: calc(100% + 12px); top: 50%; transform: translateY(-50%);'
            )
              TaIcon(type='EditOutlined', :size='16')
            .edit-tip.absolute.opacity-0.transition-opacity.duration-200.bg-gray-800.text-white.text-xs.px-2.py-1.rounded.pointer-events-none(
              class="group-hover:opacity-100",
              style='left: calc(100% + 12px); top: calc(50% + 32px); transform: translateY(-50%); white-space: nowrap;'
            ) 双击编辑
        template(v-else)
          .flex.items-center.gap-4
            .w-40.h-40
              TaImageSingleUploader(v-model:value='fieldEditData.avatar.files')
            .flex.gap-2
              a-button(type='primary', size='small', @click='saveField("avatar")') 保存
              a-button(size='small', @click='cancelFieldEdit("avatar")') 取消
      .form-item.flex.items-center
        .label.flex.items-center 姓名
        template(v-if='!fieldEditing.name')
          .field-hover-container.input-field.relative.flex.items-center(class="group")
            .input-box.input-read.select-text(@dblclick='startFieldEdit("name")') {{ info.name || '暂无' }}
            .field-edit-icon.absolute.opacity-0.transition-opacity.duration-200.bg-blue-50.rounded.w-6.h-6.flex.items-center.justify-center.cursor-pointer.text-blue-500(
              @click='startFieldEdit("name")',
              class="group-hover:opacity-100 hover:bg-blue-100",
              style='left: calc(100% + 12px); top: 50%; transform: translateY(-50%);'
            )
              TaIcon(type='EditOutlined', :size='16')
            .edit-tip.absolute.opacity-0.transition-opacity.duration-200.bg-gray-800.text-white.text-xs.px-2.py-1.rounded.pointer-events-none(
              class="group-hover:opacity-100",
              style='left: calc(100% + 12px); top: calc(50% + 32px); transform: translateY(-50%); white-space: nowrap;'
            ) 双击编辑
        template(v-else)
          .flex.items-center.gap-4
            a-input.input-box.input-read(
              v-model:value='fieldEditData.name',
              style='color: #000',
              :maxLength='20',
              @input='handleFieldInput("name", $event)'
            )
            .flex.gap-2
              a-button(type='primary', size='small', @click='saveField("name")') 保存
              a-button(size='small', @click='cancelFieldEdit("name")') 取消
      .form-item.flex.items-center
        .label.flex.items-center 邮箱
        template(v-if='!fieldEditing.email')
          .field-hover-container.input-field.relative.flex.items-center(class="group")
            .input-box.input-read.select-text(@dblclick='startFieldEdit("email")') {{ info.email || '暂无' }}
            .field-edit-icon.absolute.opacity-0.transition-opacity.duration-200.bg-blue-50.rounded.w-6.h-6.flex.items-center.justify-center.cursor-pointer.text-blue-500(
              @click='startFieldEdit("email")',
              class="group-hover:opacity-100 hover:bg-blue-100",
              style='left: calc(100% + 12px); top: 50%; transform: translateY(-50%);'
            )
              TaIcon(type='EditOutlined', :size='16')
            .edit-tip.absolute.opacity-0.transition-opacity.duration-200.bg-gray-800.text-white.text-xs.px-2.py-1.rounded.pointer-events-none(
              class="group-hover:opacity-100",
              style='left: calc(100% + 12px); top: calc(50% + 32px); transform: translateY(-50%); white-space: nowrap;'
            ) 双击编辑
        template(v-else)
          .flex.items-center.gap-4
            a-input.input-box.input-read(
              v-model:value='fieldEditData.email',
              style='color: #000',
              :maxLength='320',
              @input='handleFieldInput("email", $event)'
            )
            .flex.gap-2
              a-button(type='primary', size='small', @click='saveField("email")') 保存
              a-button(size='small', @click='cancelFieldEdit("email")') 取消
      .form-item.flex.items-center(v-if='showMobile')
        .label.flex.items-center 手机号
        template(v-if='!fieldEditing.mobile')
          .field-hover-container.input-field.relative.flex.items-center(class="group")
            .input-box.input-read.select-text(@dblclick='MobileEdit ? startFieldEdit("mobile") : null') {{ info.mobile || '暂无号码' }}
            .field-edit-icon.absolute.opacity-0.transition-opacity.duration-200.bg-blue-50.rounded.w-6.h-6.flex.items-center.justify-center.cursor-pointer.text-blue-500(
              v-if='MobileEdit',
              @click='startFieldEdit("mobile")',
              class="group-hover:opacity-100 hover:bg-blue-100",
              style='left: calc(100% + 12px); top: 50%; transform: translateY(-50%);'
            )
              TaIcon(type='EditOutlined', :size='16')
            .edit-tip.absolute.opacity-0.transition-opacity.duration-200.bg-gray-800.text-white.text-xs.px-2.py-1.rounded.pointer-events-none(
              v-if='MobileEdit',
              class="group-hover:opacity-100",
              style='left: calc(100% + 12px); top: calc(50% + 32px); transform: translateY(-50%); white-space: nowrap;'
            ) 双击编辑
        template(v-else)
          .flex.items-center.gap-4
            a-input.input-box.input-read(
              v-model:value='fieldEditData.mobile',
              style='color: #000',
              :maxLength='11',
              @input='handleFieldInput("mobile", $event)'
            )
            .flex.gap-2
              a-button(type='primary', size='small', @click='saveField("mobile")') 保存
              a-button(size='small', @click='cancelFieldEdit("mobile")') 取消
    .password-box.item-card
      .item-card-header 密码
      .form-item.flex.items-center
        .label.flex.items-center 当前密码
        a-input-password.input-box.input-write(
          type='password',
          v-model:value='currentPassword',
          placeholder='请输入当前密码'
        )

      .form-item.flex
        .label.flex.items-center 新密码
        a-input-password.input-box.input-write(
          type='password',
          v-model:value='newPassword',
          placeholder='请输入新密码'
          @input='passChange',
          :maxLength='20'
        )
      .form-item.flex
        .label.flex.items-center 验证密码
        a-input-password.input-box.input-write(
          type='password',
          v-model:value='verifyPassword',
          placeholder='请输入验证密码'
          @input='passVerChange',
          :maxLength='20'
        )
      .submit-btn(@click='submit', :class='isSubmit ? "change-submit-btn" : ""') 保存
</template>

<style lang="stylus" scoped>
input:focus
  outline none
.user-info
  display flex
  align-items flex-start
  justify-content center
  background v-bind(background)
  padding-top 20px
  .user-avatar
    padding 19px 31px
  .item-card
    margin-bottom 20px
    width 100%
    background #fff
    border-radius 4px
    min-height 40px
    .info
      margin-left 16px
      .account
        color #262626
        font-size 18px
        margin-bottom 8px
        line-height 20px
        font-weight 600
      .mobile
        font-size 14px
        color #8c8c8c
        line-height 20px
  .write-off-box
    .write-header
      font-size 18px
      padding 13px 20px
      color #262626
      font-weight 600
      border-bottom 1px solid #E5E5E5
    .write-off
      font-size 18px
      padding 13px 20px
      color #8c8c8c
      font-weight 600
  .left-box
    margin-right 20px
    width 300px
  .right-box
    width 640px
    .information-box
      padding 14px 20px
      .item-card-header
        font-size 18px
        color #262626
        font-weight 600
      .form-item
        margin-bottom 20px
        .input-read
          width 300px
          font-size 14px
          color #bfbfbf
          font-width 600
          padding 12px 8px
          background #FFFFFF
          border-radius 8px
          border 1px solid #D9D9D9
        .label
          width 106px
          color #8c8c8c
          font-size 14px
          font-weight 600
        .update-btn
          border 1px solid #3da8f5
          border-radius 8px
          padding 8px 15px
          color #3DA8F5
          font-size 14px
          margin-left 69px
          font-weight 600px
    .password-box
      padding 14px 20px
      .item-card-header
        font-size 18px
        font-weight 600
        color #262626
      .form-item
        margin 20px 0
        align-items center
        .label
          width 106px
          font-size 14px
          font-weight 600
          color #8C8C8C
        .input-box
          width 300px
          padding 12px 8px
          font-size 14px
          font-width 600
          color #595959
          background #FFFFFF
          border-radius 8px
          border 1px solid #D9D9D9
        .ant-btn
          height 32px
          border-radius 4px
          font-size 12px
        .ant-btn-primary
          background #3DA8F5
          border-color #3DA8F5
        .flex.gap-2
          gap 8px
        .flex.flex-col.gap-2
          gap 8px
          flex-direction column
        // 悬停编辑样式 - 使用WindCSS类名，这里只保留必要的自定义样式
        .field-hover-container
          &:hover .field-edit-icon
            opacity 1 !important
            background rgba(59, 130, 246, 0.1) !important
          .field-edit-icon:hover
            background rgba(59, 130, 246, 0.2) !important

        // 文字选择样式
        .select-text
          user-select text
          cursor text
          &:hover
            cursor text

        // 头像双击区域
        .avatar-content
          cursor pointer
          &:hover
            opacity 0.8

        // 编辑提示样式
        .edit-tip
          z-index 20
          font-size 11px
          line-height 1.2
// 保存按钮
.submit-btn
  width 120px
  margin 0 auto
  height 40px
  border-radius 4px
  background #e5e5e5
  color #fff
  font-size 14px
  line-height 40px
  text-align center
.change-submit-btn
  background #3DA8F5
  cursor pointer
</style>
