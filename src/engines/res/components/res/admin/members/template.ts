import {
  TaTemplateFormColumnAttribute,
  TaTemplateFormItem,
  TaTemplateFormSelect,
} from '../../../../../../components/global/ta-component/ta-template-form-core/types';

export const memberTemplate = (
  identityOptions: TaTemplateFormSelect[],
  create?: boolean,
  extraColumnAttrs?: TaTemplateFormColumnAttribute[],
) => ({
  type: 'layout',
  model: {},
  key: 'layout_1636876922491_0',
  model_key: 'layout_1636876922491_0',
  fields: [
    {
      name: '普通布局',
      type: 'layout',
      fields: [
        {
          name: '姓名',
          type: 'input',
          rules: [],
          model: { attr_type: 'string', summary: true },
          options: { span: 24 },
          key: 'input_1636877039168_2',
          model_key: 'name',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
        {
          name: '账号',
          type: 'input',
          rules: [],
          model: { attr_type: 'string', summary: true },
          options: { span: 24, formDisabled: !create },
          key: 'input_1636877039168_21',
          model_key: 'account',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
        {
          name: '手机号',
          type: 'input',
          rules: [],
          model: { attr_type: 'string', summary: true },
          options: { span: 24 },
          key: 'input_1636877040019_3',
          model_key: 'mobile',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
        {
          name: '密码',
          type: 'password',
          rules: [],
          model: { attr_type: 'string', summary: true },
          options: { span: 24 },
          key: 'password_1636877040019_3',
          model_key: 'password_raw',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
        {
          name: '身份',
          type: 'checkbox',
          rules: [],
          model: { attr_type: 'string' },
          options: { select: identityOptions, multiple: true, span: 24 },
          key: 'checkbox_1636877074583_5',
          model_key: 'member_identity_ids',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
      ],
      options: { span: 24, label: { width: 50, align: 'left' } },
      key: 'layout_1636877037914_1',
      model_key: 'layout_1636877037914_1',
      conditions: [],
      model_key_prefix: '',
    },
  ],
  conditions: [],
  options: { label: {}, disabled_actions: {} },
  actions: [
    { key: 'create', enabled: true },
    { key: 'update', enabled: true },
    { key: 'delete', enabled: true },
  ],
  column_attributes: [
    { title: '姓名', _id: 'input_1636877039168_2', dataIndex: 'name' },
    { title: '账号', _id: 'input_1636877041900_4', dataIndex: 'account' },
    // { title: '手机号', _id: 'input_1636877040019_3', dataIndex: 'mobile' },
    ...(extraColumnAttrs || []),
  ],
});

export const memberMembershipsWithInvalidTemplate: TaTemplateFormItem = {
  key: 'layout_1640250013581_0',
  type: 'layout',
  model: {},
  fields: [
    {
      key: 'nested_attributes_1640252109259_3',
      name: '岗位',
      type: 'nested_attributes',
      model: { attr_type: 'array' },
      rules: [],
      fields: [
        {
          key: 'layout_1640252206171_7',
          name: '普通布局',
          type: 'layout',
          fields: [
            {
              key: 'api_single_1640315583870_3',
              name: '组织',
              type: 'api_single',
              model: { attr_type: 'number' },
              rules: [],
              fields: [],
              options: {
                path: '/res/admin/orgs',
                span: 24,
                display: 'tag',
                multiple: false,
                table_items: [
                  { name: '名称', type: 'string', search: true, data_index: 'name' },
                  { name: '', type: 'string', search: false, data_index: 'path_names' },
                ],
                import_export_headers: [],
                display_configurable_form: {},
              },
              model_key: 'org_id',
              conditions: [],
              model_key_prefix: '',
            },
            {
              key: 'api_single_1640252150929_4',
              name: '岗位',
              type: 'api_single',
              model: { attr_type: 'number' },
              rules: [],
              fields: [],
              options: {
                path: '/res/admin/duties',
                span: 24,
                display: 'tag',
                multiple: false,
                table_items: [
                  { name: '名称', type: 'string', search: true, data_index: 'name' },
                  {
                    name: '部门',
                    type: 'string',
                    search: false,
                    data_index: 'department.path_names',
                  },
                  {
                    name: '分组',
                    data_index: 'duty_group_name',
                    search: true,
                    type: 'string',
                  },
                ],
                import_export_headers: [],
                display_configurable_form: {},
                // ransack: '{ "department_org_id_eq": ${org_id} }',
              },
              model_key: 'duty_id',
              conditions: [],
              model_key_prefix: '',
            },
            {
              key: 'condition_1640315749135_5',
              name: '条件块',
              type: 'condition',
              fields: [],
              options: { span: 24 },
              model_key: 'condition_1640315749135_5',
              conditions: [
                {
                  val: '',
                  name: '条件1',
                  type: 'complex',
                  fields: [
                    {
                      name: '普通布局',
                      type: 'layout',
                      fields: [
                        {
                          key: 'api_single_1640315595289_4',
                          name: '部门',
                          type: 'api_single',
                          model: { attr_type: 'number' },
                          rules: [],
                          fields: [],
                          options: {
                            path: '/res/admin/orgs/${org_id}/departments',
                            span: 24,
                            display: 'tag',
                            multiple: false,
                            table_items: [
                              {
                                name: '名称',
                                type: 'string',
                                search: true,
                                data_index: 'name',
                              },
                              {
                                name: '',
                                type: 'string',
                                search: true,
                                data_index: 'short_name',
                              },
                            ],
                            import_export_headers: [],
                            display_configurable_form: {},
                          },
                          model_key: 'department_id',
                          conditions: [],
                          model_key_prefix: '',
                        },
                      ],
                      options: { span: 24, label: { width: 80, align: 'left' } },
                      key: 'layout_1640317877172_8',
                      model_key: 'layout_1640317877172_8',
                      conditions: [],
                      model_key_prefix: '',
                    },
                  ],
                  model_key: '',
                  complex_condition: {
                    groups: [
                      {
                        _id: '1640315760181_2',
                        items: [
                          {
                            _id: '1640315760207_3',
                            desc: {
                              name: '组织',
                              optZh: '大于',
                              template: {
                                key: 'key',
                                type: 'layout',
                                fields: [
                                  {
                                    key: 'number_1632802020086_16',
                                    name: '值',
                                    type: 'number',
                                    model: { attr_type: 'number' },
                                    rules: [],
                                    fields: [],
                                    options: { span: 24 },
                                    model_key: 'val',
                                    conditions: [],
                                    model_key_prefix: 'rule',
                                  },
                                ],
                              },
                              modelValue: { rule: { val: 0 } },
                            },
                            rule: {
                              key: 'org_id',
                              opt: '>',
                              val: 0,
                              type: 'Com::Attr::ConditionRules::Number',
                              key_name: '组织',
                            },
                          },
                        ],
                      },
                    ],
                  },
                },
              ],
              model_key_prefix: '',
            },
            {
              key: 'datetime_1640252158406_6',
              name: '启用时间',
              type: 'datetime',
              model: { attr_type: 'date' },
              rules: [],
              fields: [],
              options: { span: 24 },
              model_key: 'effective_at',
              conditions: [],
              model_key_prefix: '',
            },
            {
              key: 'datetime_1640252156736_5',
              name: '停用时间',
              type: 'datetime',
              model: { attr_type: 'date' },
              rules: [],
              fields: [],
              options: { span: 24, placeholder: '' },
              model_key: 'invalid_at',
              conditions: [],
              model_key_prefix: '',
            },
          ],
          options: { span: 24, label: { align: 'left', width: 80 } },
          model_key: 'layout_1640252206171_7',
          conditions: [],
          model_key_prefix: '',
        },
      ],
      options: { span: 24, disabled_actions: {}, display: 'table' },
      model_key: 'memberships_with_invalid',
      conditions: [],
      model_key_prefix: '',
    },
  ],
  actions: [
    { key: 'create', enabled: true },
    { key: 'update', enabled: true },
    { key: 'delete', enabled: true },
    { key: 'import', enabled: true },
    { key: 'export', enabled: true },
  ],
  options: { label: {}, create_text: '提交', update_text: '提交', disabled_actions: {} },
  model_key: 'layout_1640250013581_0',
  conditions: [],
};

export const memberIdentityTemplate = {
  type: 'layout',
  model: {},
  key: 'layout_1636893091603_0',
  model_key: 'layout_1636893091603_0',
  fields: [
    {
      name: '普通布局',
      type: 'layout',
      fields: [
        {
          name: '名称',
          type: 'input',
          rules: [],
          model: { attr_type: 'string' },
          options: { span: 24 },
          key: 'input_1636893096855_3',
          model_key: 'name',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
        {
          name: '角色类型',
          type: 'input',
          rules: [],
          model: { attr_type: 'string' },
          options: { span: 24 },
          key: 'input_1636893095907_2',
          model_key: 'member_type',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
        {
          name: '表单',
          type: 'form_designer',
          rules: [],
          model: { attr_type: 'array' },
          options: { span: 24 },
          key: 'form_designer_1636893273912_4',
          model_key: 'form',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
      ],
      options: { span: 24, label: { width: 50, align: 'left' } },
      key: 'layout_1636893094756_1',
      model_key: 'layout_1636893094756_1',
      conditions: [],
      model_key_prefix: '',
    },
  ],
  conditions: [],
  options: { label: {}, disabled_actions: {} },
  actions: [
    // { key: 'create', enabled: true },
    { key: 'update', enabled: true },
    { key: 'delete', enabled: true },
  ],
};
