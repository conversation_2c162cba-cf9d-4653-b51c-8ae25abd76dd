export const orgTemplate = {
  type: 'layout',
  model: {},
  key: 'layout_1636893091603_0',
  model_key: 'layout_1636893091603_0',
  fields: [
    {
      name: '普通布局',
      type: 'layout',
      fields: [
        {
          name: '名称',
          type: 'input',
          rules: [],
          model: { attr_type: 'string' },
          options: { span: 24 },
          key: 'input_1636893096855_3',
          model_key: 'name',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
        {
          name: '标识',
          type: 'input',
          rules: [],
          model: { attr_type: 'string' },
          options: { span: 24 },
          key: 'input_1636893095907_2',
          model_key: 'code',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
        {
          name: '简称',
          type: 'input',
          rules: [],
          model: { attr_type: 'string' },
          options: { span: 24 },
          key: 'input_1636893095907_3',
          model_key: 'short_name',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
        {
          name: '上级组织',
          type: 'api_single',
          rules: [],
          model: { attr_type: 'number' },
          options: {
            span: 24,
            multiple: false,
            table_items: [
              { name: '名称', data_index: 'name', search: true, type: 'string' },
              { name: '类型', data_index: 'org_identity_name', search: true, type: 'string' },
            ],
            path: '/res/member/orgs',
            display: 'tag',
          },
          key: 'api_single_1638094168811_1',
          model_key: 'parent_id',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
      ],
      options: { span: 24, label: { width: 50, align: 'left' } },
      key: 'layout_1636893094756_1',
      model_key: 'layout_1636893094756_1',
      conditions: [],
      model_key_prefix: '',
    },
  ],
  conditions: [],
  options: { label: {}, disabled_actions: {}, create_text: '提交', update_text: '提交' },
  actions: [
    { key: 'update', enabled: true },
    { key: 'delete', enabled: true },
  ],
  column_attributes: [
    {
      _id: 'column_attributes_1638094304115_1',
      dataIndex: 'position',
      title: '排序',
      render: 'TableRendersAuto',
    },
    {
      _id: 'column_attributes_1638094304115_3',
      dataIndex: 'code',
      title: '标识',
      render: 'TableRendersAuto',
    },
    {
      _id: 'column_attributes_1638094312112_4',
      dataIndex: 'name',
      title: '名称',
      render: 'TableRendersAuto',
    },
    {
      _id: 'column_attributes_1638094317775_5',
      dataIndex: 'short_name',
      title: '简称',
      render: 'TableRendersAuto',
    },
    {
      _id: 'column_attributes_1638094325412_6',
      dataIndex: 'path_names',
      title: '路径',
      render: 'TableRendersAuto',
    },
  ],
};

export const orgIdentityTemplate = {
  type: 'layout',
  model: {},
  key: 'layout_1636893091603_0',
  model_key: 'layout_1636893091603_0',
  fields: [
    {
      name: '普通布局',
      type: 'layout',
      fields: [
        {
          name: '名称',
          type: 'input',
          rules: [],
          model: { attr_type: 'string' },
          options: { span: 24 },
          key: 'input_1636893096855_3',
          model_key: 'name',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
        {
          name: '组织类型',
          type: 'input',
          rules: [],
          model: { attr_type: 'string' },
          options: { span: 24 },
          key: 'input_1636893095907_2',
          model_key: 'org_type',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
        {
          name: '表单定义',
          type: 'form_designer',
          rules: [],
          model: { attr_type: 'string' },
          options: { span: 24 },
          key: 'input_1636893095908_2',
          model_key: 'form',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
      ],
      options: { span: 24, label: { width: 100, align: 'left' } },
      key: 'layout_1636893094756_1',
      model_key: 'layout_1636893094756_1',
      conditions: [],
      model_key_prefix: '',
    },
  ],
  conditions: [],
  options: { label: {}, disabled_actions: {} },
  actions: [
    // { key: 'create', enabled: true },
    { key: 'update', enabled: true },
    { key: 'delete', enabled: true },
  ],
};
