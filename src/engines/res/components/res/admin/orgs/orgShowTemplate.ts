import {
  TaTemplateFormItem,
  TaTemplateFormSelect,
} from '@/components/global/ta-component/ta-template-form-core/types';
import { VObject } from '@/lib/vails/model/index';

export const orgShowMemberTemplate = {
  column_attributes: [
    { title: '姓名', dataIndex: 'name' },
    { title: '账号', dataIndex: 'account' },
    { title: '部门', dataIndex: 'department_names' },
    { title: '职务', dataIndex: 'duty_names' },
    // { title: '手机号', dataIndex: 'phone' },
  ],
};

export const orgShowOrgTemplate: TaTemplateFormItem = {
  type: 'layout',
  model: {},
  key: 'layout_1636893091603_0',
  model_key: 'layout_1636893091603_0',
  fields: [
    {
      name: '普通布局',
      type: 'layout',
      fields: [
        {
          name: '名称',
          type: 'input',
          rules: [],
          model: { attr_type: 'string' },
          options: { span: 24 },
          key: 'input_1636893096855_3',
          model_key: 'name',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
        {
          name: '标识',
          type: 'input',
          rules: [],
          model: { attr_type: 'string' },
          options: { span: 24 },
          key: 'input_1636893095907_2',
          model_key: 'code',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
        {
          name: '简称',
          type: 'input',
          rules: [],
          model: { attr_type: 'string' },
          options: { span: 24 },
          key: 'input_1636893095907_3',
          model_key: 'short_name',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
      ],
      options: { span: 24, label: { width: 150, align: 'left' } },
      key: 'layout_1636893094756_1',
      model_key: 'layout_1636893094756_1',
      conditions: [],
      model_key_prefix: '',
    },
  ],
  conditions: [],
  options: { label: {}, disabled_actions: {} },
  column_attributes: [
    { title: '名称', dataIndex: 'name' },
    { title: '代号', dataIndex: 'code' },
    { title: '简称', dataIndex: 'short_name' },
  ],
};

export const orgShowDepartmentTemplateFunc = (
  departmentSelect: TaTemplateFormSelect[],
  defaultParentDepartmentId?: number,
): TaTemplateFormItem => ({
  type: 'layout',
  model: {},
  key: 'layout_1636893091603_0',
  model_key: 'layout_1636893091603_0',
  fields: [
    {
      name: '普通布局',
      type: 'layout',
      fields: [
        {
          name: '名称',
          type: 'input',
          rules: [],
          model: { attr_type: 'string' },
          options: { span: 24 },
          key: 'input_1636893096855_3',
          model_key: 'name',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
        {
          name: '标识',
          type: 'input',
          rules: [],
          model: { attr_type: 'string' },
          options: { span: 24 },
          key: 'input_1636893095907_2',
          model_key: 'code',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
        {
          name: '简称',
          type: 'input',
          rules: [],
          model: { attr_type: 'string' },
          options: { span: 24 },
          key: 'input_1636893095907_3',
          model_key: 'short_name',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
        {
          name: '上级部门',
          type: 'select',
          rules: [],
          model: {
            attr_type: 'number',
          },
          options: {
            span: 24,
            select: departmentSelect,
            defaultValue: defaultParentDepartmentId,
          },
          key: 'tree_1637046543442_9',
          model_key: 'parent_id',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
        {
          name: '上级部门【更多部门】',
          type: 'api_single',
          rules: [],
          model: {
            attr_type: 'number',
          },
          options: {
            layout: 'vertical',
            span: 24,
            multiple: false,
            table_items: [
              { name: '组织', data_index: 'org_name', search: true, type: 'string' },
              { name: '名称', data_index: 'name', search: true, type: 'string' },
            ],
            display_configurable_form: {},
            path: '/res/admin/departments',
            display: 'tag',
            attrs: ['org_name', 'name'],
          },
          key: 'api_single_1637046543442_9',
          model_key: 'parent_id',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
      ],
      options: { span: 24, label: { width: 150, align: 'left' } },
      key: 'layout_1636893094756_1',
      model_key: 'layout_1636893094756_1',
      conditions: [],
      model_key_prefix: '',
    },
  ],
  conditions: [],
  options: { label: {}, disabled_actions: {} },
  column_attributes: [
    { title: '名称', dataIndex: 'name' },
    { title: '简称', dataIndex: 'short_name' },
    { title: '代码', dataIndex: 'code' },
    { title: '路径', dataIndex: 'path_names' },
    // { title: '手机号', _id: 'input_1636877040019_3', dataIndex: 'phone' },
  ],
});

export const orgShowMembershipTemplateFunc = (
  org: VObject,
  departmentSelect: TaTemplateFormSelect[],
): TaTemplateFormItem => ({
  key: 'layout_1637045146744_0',
  type: 'layout',
  model: {},
  fields: [
    {
      key: 'layout_1637045152365_2',
      name: '普通布局',
      type: 'layout',
      fields: [
        {
          key: 'input_1637045588623_3',
          name: '组织',
          type: 'input',
          model: { attr_type: 'string' },
          rules: [],
          fields: [],
          options: { span: 24, formDisabled: true, defaultValue: org.name },
          model_key: 'org_name',
          conditions: [],
          model_key_prefix: '',
        },
        {
          name: '人员',
          type: 'api_single',
          rules: [
            { rule_type: 'required', type: 'number', required: true, message: '请填写正确的人员' },
          ],
          model: { attr_type: 'number' },
          options: {
            span: 24,
            multiple: false,
            table_items: [
              { name: '名称', data_index: 'user_name', search: true, type: 'string' },
              { name: '账号', data_index: 'user_account', search: true, type: 'string' },
            ],
            path: '/res/admin/members',
            ransack: '',
            display: 'tag',
          },
          key: 'api_1637061050417_1',
          model_key: 'member_id',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
        {
          name: '岗位',
          type: 'api_single',
          rules: [
            // { rule_type: 'required', type: 'number', required: true, message: '请填写正确的岗位' },
          ],
          model: { attr_type: 'number' },
          options: {
            span: 24,
            multiple: false,
            table_items: [
              { name: '名称', data_index: 'name', search: true, type: 'string' },
              { name: '部门', data_index: 'department.path_names', search: false, type: 'string' },
              {
                name: '分组',
                data_index: 'duty_group_name',
                search: true,
                type: 'string',
              },
            ],
            path: '/res/admin/duties',
            display: 'tag',
          },
          key: 'api_single_1637060881064_0',
          model_key: 'duty_id',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
        {
          name: '部门',
          type: 'select',
          rules: [],
          model: { attr_type: 'string' },
          options: {
            span: 24,
            select: departmentSelect,
          },
          key: 'tree_1637061219603_4',
          model_key: 'department_id',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
      ],
      options: { span: 24, label: { align: 'left', width: 150 } },
      model_key: 'layout_1637045152365_2',
      conditions: [],
      model_key_prefix: '',
    },
  ],
  actions: [
    { key: 'create', enabled: true },
    { key: 'update', enabled: true },
    { key: 'delete', enabled: true },
    { key: 'import', enabled: true },
    { key: 'export', enabled: true },
  ],
  options: { label: {}, disabled_actions: {} },
  model_key: 'layout_1637045146744_0',
  conditions: [],
  column_attributes: [
    { title: '用户名', dataIndex: 'member.name' },
    { title: '账号', dataIndex: 'member.account' },
    { title: '角色', dataIndex: 'member.member_identity_name' },
    { title: '岗位', dataIndex: 'duty.name' },
    { title: '部门', dataIndex: 'department.name' },
  ],
  index_attributes: [],
});

export const orgShowMembershipCreateTemplateFunc = (
  org: VObject,
  memberIdentitySelect: TaTemplateFormSelect[],
  departmentSelect: TaTemplateFormSelect[],
  defaultParentDepartmentId?: number,
): TaTemplateFormItem => ({
  key: 'layout_1637045146744_0',
  type: 'layout',
  model: {},
  fields: [
    {
      key: 'layout_1637045152365_2',
      name: '普通布局',
      type: 'layout',
      fields: [
        {
          key: 'input_1637045588623_3',
          name: '组织',
          type: 'input',
          model: { attr_type: 'string' },
          rules: [],
          fields: [],
          options: { span: 24, formDisabled: true, defaultValue: org.name },
          model_key: 'org_name',
          conditions: [],
          model_key_prefix: '',
        },
        {
          key: 'checkbox_1637045668469_5',
          name: '角色',
          type: 'radio',
          model: { attr_type: 'string' },
          rules: [],
          fields: [],
          options: { span: 24, select: memberIdentitySelect, multiple: false },
          model_key: 'member_identity_id',
          conditions: [],
          model_key_prefix: '',
        },
        {
          name: '人员',
          type: 'api',
          rules: [
            { rule_type: 'required', type: 'array', required: true, message: '请填写正确的人员' },
          ],
          model: { attr_type: 'array' },
          options: {
            span: 24,
            multiple: true,
            table_items: [
              { name: '名称', data_index: 'user_name', search: true, type: 'string' },
              { name: '账号', data_index: 'user_account', search: true, type: 'string' },
            ],
            path: '/res/admin/members',
            ransack: '{ "member_identity_id_eq": ${member_identity_id} }',
            display: 'tag',
          },
          key: 'api_1637061050417_1',
          model_key: 'member_ids',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
        {
          name: '岗位',
          type: 'api_single',
          rules: [
            // { rule_type: 'required', type: 'number', required: true, message: '请填写正确的岗位' },
          ],
          model: { attr_type: 'number' },
          options: {
            span: 24,
            multiple: false,
            table_items: [
              { name: '名称', data_index: 'name', search: true, type: 'string' },
              { name: '部门', data_index: 'department.path_names', search: false, type: 'string' },
              {
                name: '分组',
                data_index: 'duty_group_name',
                search: true,
                type: 'string',
              },
            ],
            path: '/res/admin/duties',
            display: 'tag',
          },
          key: 'api_single_1637060881064_0',
          model_key: 'duty_id',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
        {
          name: '部门',
          type: 'select',
          rules: [],
          model: { attr_type: 'string' },
          options: {
            span: 24,
            select: departmentSelect,
            defaultValue: defaultParentDepartmentId,
          },
          key: 'tree_1637061219603_4',
          model_key: 'department_id',
          fields: [],
          conditions: [],
          model_key_prefix: '',
        },
      ],
      options: { span: 24, label: { align: 'left', width: 150 } },
      model_key: 'layout_1637045152365_2',
      conditions: [],
      model_key_prefix: '',
    },
  ],
  actions: [
    { key: 'create', enabled: true },
    { key: 'update', enabled: true },
    { key: 'delete', enabled: true },
    { key: 'import', enabled: true },
    { key: 'export', enabled: true },
  ],
  options: { label: {}, disabled_actions: {} },
  model_key: 'layout_1637045146744_0',
  conditions: [],
  column_attributes: [
    { title: '用户名', dataIndex: 'member.name' },
    { title: '账号', dataIndex: 'member.account' },
    { title: '角色', dataIndex: 'member.member_identity_name' },
    { title: '岗位', dataIndex: 'duty.name' },
    { title: '部门', dataIndex: 'department.name' },
  ],
  index_attributes: [],
});
