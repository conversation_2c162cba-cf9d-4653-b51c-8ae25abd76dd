export const ResBookTemplate = {
  key: 'layout_1647259312044_0',
  type: 'layout',
  model: {},
  fields: [
    {
      key: 'input_1647259329205_1',
      name: '名称',
      type: 'input',
      model: { attr_type: 'string' },
      rules: [],
      fields: [],
      options: { span: 24 },
      model_key: 'name',
      conditions: [],
      model_key_prefix: '',
    },
    {
      key: 'textarea_1647259338575_2',
      name: '详情',
      type: 'textarea',
      model: { attr_type: 'string' },
      rules: [],
      fields: [],
      options: { span: 24 },
      model_key: 'desc',
      conditions: [],
      model_key_prefix: '',
    },
    {
      key: 'radio_1647259353151_3',
      name: '通讯录类型',
      type: 'radio',
      model: { attr_type: 'string' },
      rules: [
        {
          type: 'string',
          message: '请填写正确的通讯录类型',
          required: true,
          rule_type: 'required',
        },
      ],
      fields: [],
      options: {
        span: 24,
        select: [
          { label: '单位', value: 'Org' },
          { label: '条线', value: 'Department' },
          { label: '用户', value: 'User' },
        ],
        multiple: false,
      },
      model_key: 'relation_type',
      conditions: [],
      model_key_prefix: '',
    },
    {
      name: '条件块',
      icon: 'FolderOutlined',
      type: 'condition',
      conditions: [
        {
          name: '单位',
          model_key: 'relation_type',
          val: 'Org',
          fields: [
            {
              name: '选择单位',
              type: 'nested_attributes_polymorphic',
              rules: [],
              model: { attr_type: 'array' },
              options: {
                span: 24,
                multiple: true,
                display_configurable_form: {},
                attrs: ['name', 'source.name'],
                nested_attributes_polymorphic_tabs: [
                  {
                    key: 'org',
                    label: '单位',
                    path: '/res/member/orgs',
                    table_items: [
                      { name: '名称', type: 'string', search: true, data_index: 'name' },
                      { name: '简称', type: 'string', search: true, data_index: 'short_name' },
                      {
                        name: '身份类型',
                        type: 'string',
                        search: false,
                        data_index: 'org_identity_name',
                      },
                    ],
                    ransackStr: '',
                    polymorphicType: 'Org',
                  },
                ],
                display: 'tag',
              },
              key: 'api_store_1647261893571_0',
              model_key: 'book_relations',
              fields: [],
              conditions: [],
              model_key_prefix: '',
            },
          ],
          type: 'simple',
          complex_condition: { groups: [] },
          opt: '==',
        },
        {
          name: '用户',
          model_key: 'relation_type',
          val: 'User',
          fields: [
            {
              name: '选择用户',
              type: 'nested_attributes_polymorphic',
              rules: [],
              model: { attr_type: 'array' },
              options: {
                span: 24,
                multiple: true,
                display_configurable_form: {},
                attrs: ['name', 'source.name'],
                nested_attributes_polymorphic_tabs: [
                  {
                    key: 'user',
                    label: '用户',
                    path: '/res/member/users',
                    table_items: [
                      { name: '名称', type: 'string', search: true, data_index: 'name' },
                      { name: '岗位职务', type: 'string', search: false, data_index: 'duty_names' },
                    ],
                    ransackStr: '',
                    polymorphicType: 'User',
                  },
                ],
                display: 'tag',
              },
              key: 'api_store_1647261893571_0',
              model_key: 'book_relations',
              fields: [],
              conditions: [],
              model_key_prefix: '',
            },
          ],
          type: 'simple',
          complex_condition: { groups: [] },
          opt: '==',
        },
        {
          name: '条线',
          model_key: 'relation_type',
          val: 'Department',
          fields: [
            {
              name: '选择条线',
              type: 'nested_attributes_polymorphic',
              rules: [],
              model: { attr_type: 'array' },
              options: {
                span: 24,
                multiple: true,
                display_configurable_form: {},
                attrs: ['name', 'source.name'],
                nested_attributes_polymorphic_tabs: [
                  {
                    key: 'department',
                    label: '条线',
                    path: '/res/member/departments',
                    table_items: [
                      { name: '名称', type: 'string', search: true, data_index: 'name' },
                      { name: 'code', type: 'string', search: true, data_index: 'code' },
                      { name: '', type: 'string', search: true, data_index: 'short_name' },
                    ],
                    ransackStr: '',
                    polymorphicType: 'Department',
                  },
                ],
                display: 'tag',
              },
              key: 'api_store_1647261893571_0',
              model_key: 'book_relations',
              fields: [],
              conditions: [],
              model_key_prefix: '',
            },
          ],
          type: 'simple',
          complex_condition: { groups: [] },
          opt: '==',
        },
      ],
      options: { span: 24 },
      key: 'condition_1647309985498_2',
      model_key: 'condition_1647309985498_2',
      fields: [],
      model_key_prefix: '',
    },
  ],
  actions: [
    { key: 'create', enabled: true },
    { key: 'update', enabled: true },
    { key: 'delete', enabled: true },
    { key: 'import', enabled: false },
    { key: 'export', enabled: false },
  ],
  options: {
    label: {},
    theme: { card: {}, background: {} },
    create_text: '提交',
    update_text: '提交',
    disabled_actions: {},
  },
  model_key: 'layout_1647259312044_0',
  conditions: [],
  column_attributes: [
    {
      _id: 'column_attributes_1638094304115_3',
      dataIndex: 'name',
      title: '名称',
      render: 'TableRendersAuto',
    },
    {
      _id: 'column_attributes_1638094312112_4',
      dataIndex: 'relation_type',
      title: '类型',
      render: 'TableRendersAuto',
    },
  ],
};
