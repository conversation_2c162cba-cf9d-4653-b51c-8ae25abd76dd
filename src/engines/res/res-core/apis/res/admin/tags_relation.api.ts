import { MyApi } from '@/apis/MyApi';
import { VApiConfig } from '@/lib/vails/api';
import { ResTagsRelation } from '../../../types/model';

export class ResAdminTagsRelationApi extends MyApi<ResTagsRelation> {
  constructor(config?: VApiConfig) {
    super({
      namespace: '/res/admin',
      name: 'tags_relation',
      actions: [{ name: 'destroy_relation', method: 'delete', on: 'collection' }],
      ...config,
    });
  }
}
