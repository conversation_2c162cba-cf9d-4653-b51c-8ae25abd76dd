<script lang='ts'>
import { defineComponent, PropType, ref, toRefs } from 'vue';
import scrollIntoView from 'smooth-scroll-into-view-if-needed';
interface ITab {
  key: string;
  label: string;
  comp: string;
}
const ComVerticalTabs = defineComponent({
  name: 'ComVerticalTabs',
  components: {},
  props: {
    tabs: { type: Array as PropType<ITab[]>, default: () => [] },
  },
  setup(props, { emit }) {
    const activeTabKey = ref(props.tabs[0].key);
    const tabChange = (key: string) => {
      activeTabKey.value = key;
      const node = document.querySelector(`#${key}`)
      console.dir(node, 'node')
      if (node) {
        scrollIntoView(node, {
          // scrollMode: node,
          block: 'start',
          behavior: 'smooth',
        });
      }

      emit('tabChange', key)
    }
    return {
      ...toRefs(props),
      activeTabKey,
      tabChange,
    };
  },
});
export default ComVerticalTabs;
</script>

<template lang="pug">
.com-vertical-tabs.grid.text-gray-700
  .tab.px-4.py-2.cursor-pointer.relative.z-2(
    v-for='tab in tabs',
    :class='{"tab__active": tab.key === activeTabKey}'
    @click = 'tabChange(tab.key)'
  ) {{ tab.label }}
</template>

<style lang="stylus" scoped>
.com-vertical-tabs .tab__active
  color white
.com-vertical-tabs .tab__active::before
  content ''
  position absolute
  inset 0
  width 100%
  height 100%
  z-index -1
  border-radius 8px
  background-color $primary-color-500
</style>
