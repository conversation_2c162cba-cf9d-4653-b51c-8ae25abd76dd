<script lang='ts'>
import { ref, defineComponent, toRefs, computed } from 'vue';
import ComBpmInstanceDetailEasyDialogFromIndex from '@/engines/bpm/components/ComBpmInstanceDetailEasyDialogFromIndex.vue';
import ComOpmWorkplaneBpmCard from './ComOpmWorkplaneBpmCard.vue';
import { VObject } from '@/lib/vails';

const ComOpmWorkplaneApproved = defineComponent({
  name: 'ComOpmWorkplaneApproved',
  components: {
    ComBpmInstanceDetailEasyDialogFromIndex,
    ComOpmWorkplaneBpmCard
  },
  props: {
    title: { type: String, default: '' },
    url: { type: String, default: '' },
    approvingStore: { type: Object, default: () => { } },
    createdStore: { type: Object, default: () => { } },
    notifiedStore: { type: Object, default: () => { } },
  },
  setup(props) {
    const config = computed(() => ({
      store: props.approvingStore,
      mode: 'list',
      pagination: {
        perPage: 5,
        hide: true,
      },
    }))

    const tabs = computed(() => [
      {
        label: '待我处理',
        key: 'approving'
      },
      {
        label: '抄送我的',
        key: 'notified',
        store: props.notifiedStore,
      },
      {
        label: '我发起的',
        key: 'created',
        store: props.createdStore,
      },
    ])

    const visible = ref<Record<string, boolean>>({})
    const onShow = (record: VObject) => {
      if (!record.flowable_user_info || record.flowable_user_info?.mode === 'instance') {
        visible.value[`instance-${record.id}`] = true;
      } else {
        visible.value[`token-${record.id}`] = true;
      }
    };
    return {
      ...toRefs(props),
      config,
      tabs,
      onShow,
      visible,
    };
  },
});
export default ComOpmWorkplaneApproved;
</script>

<template lang="pug">
ComOpmCardContainer(:title='title' class='!px-4')
  template(#actions)
    router-link.ml-auto(:to='url')
      .flex.items-center.text-gray-500
        .text-sm 查看更多
        TaIcon(type='outline/chevron-right' class='!w-3 !h-3')
  TaIndexView(
    :config='config',
    :tabs='tabs',
    :showHeader='false',
    @onShow='onShow'
  )
    template(#card='{record,actions}')
      ComOpmWorkplaneBpmCard.cursor-pointer(:record='record')
      ComBpmInstanceDetailEasyDialogFromIndex(
        v-if='visible[`token-${record.id}`]',
        v-model:visible='visible[`token-${record.id}`]',
        :instance='record',
        @close='actions.silenceRefresh'
      )

      ComBpmInstanceDetailEasyDialogFromIndex(
        v-if='visible[`instance-${record.id}`]',
        v-model:visible='visible[`instance-${record.id}`]',
        :instance='record',
        @close='actions.silenceRefresh'
      )
</template>

<style lang="stylus" scoped></style>
