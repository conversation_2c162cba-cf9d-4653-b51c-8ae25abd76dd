<script lang='ts'>
import { ref, defineComponent, toRefs } from 'vue';
const ComOpmWorkplaneBpmCard = defineComponent({
  name: 'ComOpmWorkplaneBpmCard',
  components: {},
  props: {
    record: { type: Object, default: () => ({}) }
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
export default ComOpmWorkplaneBpmCard;
</script>

<template lang="pug">
.com-home-bpm-card.py-4
  header.text-sm.text-gray-700.font-medium.mb-1 {{ record.workflow_name }}
  .flex.items-center.text-gray-500
    ComColorfulTag.py-2px.px-10px.mr-4.text-xs(
      text='审批',
      color='red',
    )
    TaIcon.mr-1(type='outline/calendar' class='!w-4 h-4')
    .text-xs {{ record.createdStr }}
</template>

<style lang="stylus" scoped>
.com-home-bpm-card
  border-bottom 1px solid #F3F4F6;

</style>
