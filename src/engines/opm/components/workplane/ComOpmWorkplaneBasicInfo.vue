<script lang='ts'>
import { ref, defineComponent, toRefs } from 'vue';

const ComOpmWorkplaneBasicInfo = defineComponent({
  name: 'ComOpmWorkplaneBasicInfo',
  components: {},
  props: {
    user: {
      type: Object,
      required: true,
    },
    member: {
      type: Object,
      required: true,
    }
  },
  setup(props) {

    return {
      ...toRefs(props),
    };
  },
});
export default ComOpmWorkplaneBasicInfo;
</script>

<template lang="pug">
.com-opm-workplane-basic-info.p-4.rounded-lg.bg-white
  .flex.items-center
    TaAvatar.mr-4(:size='80' :user='user')
    .info
      .text-lg.text-gray-900.font-medium.mb-1 {{ user.name }}
      .text-sm.text-gray-500.mb-1 {{ user.mobile }}
      section.flex.items-center.text-sm.space-x-2(v-if='member?.mainPosition')
        ComColorfulTag.text-xs.flex.items-center.flex-shrink-0(
          color='#3F83F8'
          class='!px-1 !py-[2px]'
        )
          TaIcon.w-4.h-4.text-primary-500.mr-1(type='solid/briefcase')
          .text-xs 主岗
        .info__value__color {{ `${member.mainPosition?.department_name || '暂无部门'} - ${member.mainPosition.duty_name || '暂无岗位'}` }}
      //- section.flex.items-center.text-sm.space-x-2(v-if='record.mainPosition')
      //-   ComColorfulTag.text-xs.flex.items-center(
      //-     color='#3F83F8'
      //-     class='!px-1 !py-[2px]'
      //-   )
      //-     TaIcon.w-4.h-4.text-primary-500.mr-1(type='solid/briefcase')
      //-     .text-xs 主岗
      //-   .info__value__color {{ `${record.mainPosition?.department_name || '暂无部门'} - ${record.mainPosition.duty_name || '暂无岗位'}` }}


</template>

<style lang="stylus" scoped>
.com-opm-workplane-basic-info:deep(.ant-avatar-circle)
  border-radius 8px
</style>
