<script lang='ts'>
import { ref, defineComponent, toRefs, computed, onMounted, nextTick } from 'vue';
import ComOpmWorkplaneCommon from './ComOpmWorkplaneCommon.vue';
import ComOpmWorkplaneWorkflows from './ComOpmWorkplaneWorkflows.vue';
import { BpmUserWorkflows } from '@/engines/bpm/bpm-core/apis/user/workflowl.api';
import { BpmWorkflowModel } from '@/engines/bpm/models/bpm/rule_stat/workflows';
import { VStore } from '@/lib/vails';
import { BpmUserApprovingInstances } from '@/engines/bpm/bpm-core/apis/user/approving/instance.api';
import { BpmUserCreatedInstances } from '@/engines/bpm/bpm-core/apis/user/created/instance.api';
import { BpmUserNotifiedInstances } from '@/engines/bpm/bpm-core/apis/user/notified/instance.api';
import { InstanceModel } from '@/engines/bpm/bpm-core/apis/user/instance.api';
import ComOpmWorkplaneApproved from './ComOpmWorkplaneApproved.vue';
import ComOpmWorkplaneBasicInfo from './ComOpmWorkplaneBasicInfo.vue';
import store from '@/store';
import { OpmUserOwnershipsApi } from '../../opm-core/apis/opm/user/ownerships.api';
import { OpmOwnershipModel } from '../../opm-core/models/opm/ownerships';
import { OpmOwnMemberApi } from '../../opm-core/apis/opm/own/members.api';
import { OpmMemberModel } from '../../opm-core/models/opm/members';

const ComOpmWorkplaneIndex = defineComponent({
  name: 'ComOpmWorkplaneIndex',
  components: {
    ComOpmWorkplaneCommon,
    ComOpmWorkplaneWorkflows,
    ComOpmWorkplaneApproved,
    ComOpmWorkplaneBasicInfo
  },
  props: {},
  setup(props) {
    const user = computed(() => store.state.user.currentUser);
    const member = ref({})
    const workflowStore = new VStore(new BpmUserWorkflows({
      params: {
        q: {
          // classify_eq: 'direct',
          s: [
            'position asc'
          ]
        }
      }
    }), BpmWorkflowModel)
    const approvingStore = new VStore(new BpmUserApprovingInstances(), InstanceModel);
    const createdStore = new VStore(new BpmUserCreatedInstances(), InstanceModel);
    const notifiedStore = new VStore(new BpmUserNotifiedInstances(), InstanceModel)
    const ownershipStore = new VStore(new OpmUserOwnershipsApi(), OpmOwnershipModel)
    const ownMemberStore = new VStore(new OpmOwnMemberApi(), OpmMemberModel)

    const userOwnerships = ref<any>([])
    const activeOwnerShip = ref<any>({})

    const fetchData = async (index: number = 0) => {
      await ownershipStore.index().then(() => {
        try {
          userOwnerships.value = ownershipStore.records.value
          activeOwnerShip.value = userOwnerships.value[index]
          if (activeOwnerShip.value?.id) {
            ownMemberStore.api = new OpmOwnMemberApi({
              parents: [{ type: 'ownerships', id: activeOwnerShip.value.id }]
            })
            ownMemberStore.find(0).then(() => {
              member.value = ownMemberStore.record.value
            })
          }

        } catch (e) {
          console.error(e)
        }
      })
    }

    onMounted(async () => {
      await fetchData()
    })

    return {
      ...toRefs(props),
      workflowStore,
      approvingStore,
      createdStore,
      notifiedStore,
      user,
      activeOwnerShip,
      member,
    };
  },
});
export default ComOpmWorkplaneIndex;
</script>

<template lang="pug">
.com-opm-workplane-index.pb-4
  .wrapper.w-full.h-full.pt-4.flex.flex-col
    header.flex.items-center.justify-between.mb-4.flex-shrink-0
      .title.text-xl.font-semibold 工作台

    section.flex
      .left.space-y-4.mr-4.flex-grow
        ComOpmWorkplaneCommon
        slot(name='left-middle')
          .empty
        ComOpmWorkplaneWorkflows(:store="workflowStore")
      .right.space-y-4.flex-shrink-0(style='flex-basis:30%;')
        ComOpmWorkplaneBasicInfo.cursor-pointer(
          :user='user',
          :member='member'
          @click='$router.push(`/opm/own/ownerships/${activeOwnerShip.id}/member`)'
        )
        ComOpmWorkplaneApproved(
          title='我的待办',
          :approvingStore='approvingStore'
          :createdStore='createdStore'
          :notifiedStore='notifiedStore'
          url='/bpm/user/instances'
        )
        slot(name='right-bottom')


</template>

<style lang="stylus" scoped>
.com-opm-workplane-index .wrapper:deep(.com-opm-card-container) .title
  @apply text-xl;
</style>
