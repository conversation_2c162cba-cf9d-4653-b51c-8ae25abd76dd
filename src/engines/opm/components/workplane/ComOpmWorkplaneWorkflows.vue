<script lang='ts'>
import { VObject } from '@/lib/vails';
import { merge } from 'lodash';
import { ref, defineComponent, toRefs, onMounted, nextTick, computed } from 'vue';
import ComBpmInstanceCreator from '@/engines/bpm/components/ComBpmInstanceCreator.vue';

const ComOpmWorkplaneWorkflows = defineComponent({
  name: 'ComOpmWorkplaneWorkflows',
  components: { ComBpmInstanceCreator },
  props: {
    store: {
      type: Object,
      required: true,
    }
  },
  setup(props) {
    const tabs = [
      {
        label: '流程发起',
        key: 'direct',
        params: {}
      },
      {
        label: '人事相关',
        key: 'opm',
        params: {
          q: { catalog_name_eq: "人事流程" }
        }
      },
      {
        label: '业务相关',
        key: 'other',
        params: {
          q: { mod_id_not_eq: 1 }
        }
      }
    ]


    const fetchData = (params: any = {}, page: number = 1) => {
      props.store.index(merge({ page }, params))
    }

    const activeTabKey = ref('direct')
    const activeTab = computed(() => {
      return tabs.find(tab => tab.key === activeTabKey.value)
    })
    const onChangedTab = (tab: any) => {
      activeTabKey.value = tab.key
      fetchData(tab.params)
    }
    onMounted(() => {
      if (activeTab.value) {
        fetchData(activeTab.value.params)
      }
    })

    const selectedWorkflowId = ref(0);
    const creator = ref<any>(null);
    const onCreateInstance = async (workflow: VObject) => {
      selectedWorkflowId.value = workflow.id;
      nextTick(() => creator.value.onCreateInstance());
    };

    const currentPage = computed(() => {
      return props.store.currentPage?.value
    })
    const totalPages = computed(() => {
      return props.store.totalPages?.value
    })
    return {
      ...toRefs(props),
      tabs,
      activeTabKey,
      onChangedTab,
      workflows: props.store.records,
      onCreateInstance,
      selectedWorkflowId,
      creator,
      totalPages,
      currentPage,
      activeTab,
    };
  },
});
export default ComOpmWorkplaneWorkflows;
</script>

<template lang="pug">
ComOpmCardContainer.com-opm-workplane-workflows(title='所有流程')
  .workflow__cards__wrapper
    ComBpmInstanceCreator(
      ref='creator',
      :workflowId='selectedWorkflowId',
      :canSaveAndCreateNext='true'
    )
    .tabs.grid.grid-cols-3.gap-6.mb-4(style='width:fit-content;')
      template(v-for='tab in tabs')
        .py-1.px-5.rounded-lg.text-gray-900.text-sm.border-1.border-gray-200.cursor-pointer(
          :class='{ "!text-white bg-primary-700 !border-primary-700": activeTabKey === tab.key }'
          @click='onChangedTab(tab)'
        ) {{ tab.label }}

    .direct.grid.grid-cols-3.gap-5(v-if='activeTabKey === "direct"')
      template(v-for='workflow in workflows')
        .p-5.rounded-xl.flex.items-center.cursor-pointer(
          style='box-shadow:0 0 20px -1px rgba(56, 64, 91, 0.08), 0 2px 12px -2px rgba(56, 64, 91, 0.05);'
          @click='onCreateInstance(workflow)'
        )
          .w-8.h-8.rounded-lg.bg-primary-500.grid.mr-2.flex-shrink-0
            TaIcon.w-6.h-6.text-white.place-self-center(type='solid/document-text')
          .text-gray-900.text-base.font-medium.truncate.w-0.flex-grow {{ workflow.name }}

    .opm.grid.grid-cols-3.gap-5(v-if='activeTabKey === "opm"')
      template(v-for='workflow in workflows')
        .p-5.rounded-xl.flex.items-center.cursor-pointer(
          style='box-shadow:0 0 20px -1px rgba(56, 64, 91, 0.08), 0 2px 12px -2px rgba(56, 64, 91, 0.05);'
          @click='onCreateInstance(workflow)'
        )
          .w-8.h-8.rounded-lg.bg-primary-500.grid.mr-2.flex-shrink-0
            TaIcon.w-6.h-6.text-white.place-self-center(type='solid/document-text')
          .text-gray-900.text-base.font-medium.truncate.w-0.flex-grow {{ workflow.name }}

    .pagination.flex.items-center.justify-center.gap-x-2.pt-2(v-if='totalPages > 1')
      template(v-for='(dot,index) in totalPages')
        .dot.w-2.h-2.bg-gray-100.rounded-full.cursor-pointer(
          :class='{ "!bg-blue-500": currentPage === index + 1 }',
          @click='fetchData(activeTab.params, index + 1)'
        )
</template>

<style lang="stylus" scoped>
</style>
