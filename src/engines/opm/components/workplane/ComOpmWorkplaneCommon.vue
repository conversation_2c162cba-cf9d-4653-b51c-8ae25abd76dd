<script lang='ts'>
import { BpmUserDashboardApi } from '@/engines/bpm/apis/bpm/user/dashboard.api';
import { ref, defineComponent, toRefs, onMounted, nextTick } from 'vue';
import ComBpmInstanceCreator from '@/engines/bpm/components/ComBpmInstanceCreator.vue';
import { VObject } from '@/lib/vails';

const ComOpmWorkplaneCommon = defineComponent({
  name: 'ComOpmWorkplaneCommon',
  components: { ComBpmInstanceCreator },
  props: {},
  setup(props) {
    const dashboard = ref<any>({});
    const creator = ref<any>(null);
    const selectedWorkflowId = ref<number>(0);
    const onCreateInstance = async (workflow: VObject) => {
      selectedWorkflowId.value = workflow.id;
      nextTick(() => creator.value.onCreateInstance());
    };
    onMounted(() => {
      new BpmUserDashboardApi().find().then((res: any) => {
        dashboard.value = res.data;
      });
    })


    return {
      ...toRefs(props),
      dashboard,
      onCreateInstance,
      selectedWorkflowId,
      creator
    };
  },
});
export default ComOpmWorkplaneCommon;
</script>

<template lang="pug">
ComOpmCardContainer.com-opm-workplane-common(title='常用功能')
  .flex.overflow-x-auto
    .grid__item.px-4.pt-3.place-self-center.flex.flex-col.items-center.cursor-pointer(
      v-for='item in dashboard.created_workflows'
      @click='onCreateInstance(item)'
    )
      TaColorfulIcon.icon.mb-3(:config='{ icon: "HomeFilled" }')
      .text-gray-900.text-base.font-medium {{ item.name }}
    ComBpmInstanceCreator(
      ref='creator',
      :workflowId='selectedWorkflowId',
      :canSaveAndCreateNext='true'
    )
</template>

<style lang="stylus" scoped>

</style>
