<script lang="ts">
import { computed, defineComponent, toRefs } from 'vue';

export default defineComponent({
  name: 'ComOpmMemberUserGender',
  props: {
    record: { type: Object, required: true },
  },
  setup(props) {
    const genderMapping: any = {
      男: {
        class: 'text-blue-500',
        icon: 'solid/male',
      },
      女: {
        class: 'text-red-400',
        icon: 'solid/female',
      },
    };
    const genderConfig = computed(() => {
      return (
        genderMapping[props.record.user?.gender] || { icon: 'solid/male', class: 'text-gray-400' }
      );
    });

    return {
      ...toRefs(props),
      genderConfig,
    };
  },
});
</script>

<template lang="pug">
.com-opm-member-user-gender
  .flex.items-center
    TaIcon.w-4.h-4.mr-1(:type='genderConfig.icon', :class='genderConfig.class')
    .text-sm(class='text-["#3F3F46"]') {{ record.user?.gender || '-' }}
</template>
