<script lang="ts">
import { defineComponent, toRefs, computed } from 'vue';

const ComOpmDepartmentLabel = defineComponent({
  name: 'ComOpmDepartmentLabel',
  components: {},
  props: {
    record: {
      type: Object,
      required: true,
    },
  },
  setup(props) {
    const style = computed(() => {
      if (props.record.dataType == 'org') {
        return {
          backgroundColor: '#3F83F8',
        };
      }

      return { backgroundColor: props.record.department_identity_color || '#31C48D' };
    });

    const typeName = computed(() => {
      if (props.record.dataType == 'org') {
        return '组织';
      }
      return props.record.department_identity_name || '部门';
    });
    return {
      ...toRefs(props),
      style,
      typeName,
    };
  },
});
export default ComOpmDepartmentLabel;
</script>

<template lang="pug">
.com-opm-department-label.text-white(
  :style='style'
) {{ typeName}}
</template>
