<script lang="ts">
import { defineComponent, toRefs } from 'vue';

export default defineComponent({
  name: 'ComOpmtRelocateToDepartment',
  props: {
    record: { type: Object, required: true },
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
</script>

<template lang="pug">
.com-opm-relocate-to-department
  .flex.items-center(v-if='record.to_department?.id')
    ComOpmDepartmentLabel.mr-1.px-1.rounded-sm(:record='record.to_department')
    .text-sm {{ record.to_department?.name }}
  span(v-else) -
</template>
