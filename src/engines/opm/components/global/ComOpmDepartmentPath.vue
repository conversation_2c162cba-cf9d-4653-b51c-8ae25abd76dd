<script lang="ts">
import { defineComponent, toRefs, computed } from 'vue';

const ComOpmDepartmentPath = defineComponent({
  name: 'ComOpmDepartmentPath',
  props: {
    record: { type: Object, required: true },
  },
  setup(props) {
    const computedStyle = computed(() => {
      return `background-color:${props.record.department_identity_color || '#31C48D'}; `;
    });

    const pathNames = computed(() => {
      return [...props.record.path_names].reverse().join(' / ');
    });

    return {
      ...toRefs(props),
      computedStyle,
      pathNames,
    };
  },
});
export default ComOpmDepartmentPath;
</script>

<template lang="pug">
.com-opm-department-path.flex.items-center
  .inline.text-white.tag.rounded.mr-2(
    :style='computedStyle'
  ) {{ record.department_identity_name || "部门" }}
  .text-sm.text-gray-900 {{ pathNames }}
</template>

<style lang="stylus" scoped>
.com-opm-department-path
  .tag
    padding: 0 4px;
</style>
