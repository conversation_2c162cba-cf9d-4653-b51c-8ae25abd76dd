<script lang="ts">
import { defineComponent, toRefs } from 'vue';

export default defineComponent({
  name: 'ComOpmMemberDepartmentLevels',
  props: { record: { type: Object, required: true } },
  setup(props) {
    const styleConfig: any = {
      本级: { background: 'rgba(63, 131, 248, 0.2)', color: '#3F83F8' },
      下级: { background: 'rgba(22, 189, 202, 0.2)', color: '#16BDCA' },
    };

    return {
      ...toRefs(props),
      styleConfig,
    };
  },
});
</script>

<template lang="pug">
.com-opm-member-department-levels
  .flex.items-center.gap-x-1
    .inline.tag.rounded.text-xs(
      v-for='level in record.departmentLevels'
      :style='styleConfig[level]'
    ) {{ level }}
</template>

<style lang="stylus" scoped>
.com-opm-member-department-levels
  .tag
    padding: 2px 10px;
</style>
