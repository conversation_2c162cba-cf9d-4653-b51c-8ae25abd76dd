<script lang="ts">
import { defineComponent, ref, computed } from 'vue';

export default defineComponent({
  name: 'ComOpmPhoneMask',
  props: {
    value: { type: String, default: '' },
  },
  setup() {
    const isShowPhoneNumber = ref(false);
    const encryptPhoneNumber = computed(() => (phoneNumber: string) => {
      if (!phoneNumber) return '暂无电话';
      if (isShowPhoneNumber.value) return phoneNumber;
      return phoneNumber.slice(0, 3) + '****' + phoneNumber.slice(7);
    });

    return {
      isShowPhoneNumber,
      encryptPhoneNumber,
    };
  },
});
</script>

<template lang="pug">
.phone__info.flex.items-center
  TaIcon.w-4.h-4.text-gray-400.mr-1(type='solid/device-tablet')
  .info__value__color.mr-4 {{encryptPhoneNumber(value|| "")}}
  TaIcon.w-4.h-4.text-gray-400.mr-1.cursor-pointer(
    :type='`outline/${isShowPhoneNumber ? "eye" : "eye-off"}`'
    @click='isShowPhoneNumber = !isShowPhoneNumber'
  )
</template>
