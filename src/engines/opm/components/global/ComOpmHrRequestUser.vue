<script lang="ts">
import { toRefs } from '@vueuse/core';
import { computed, defineComponent } from 'vue';

export default defineComponent({
  name: 'ComOpmHrRequestUser',
  props: {
    record: { type: Object, required: true },
  },
  setup(props) {
    const memberRecord = computed(() => {
      return props.record.member?.id ? props.record.member : { user: props.record.user };
    });

    return {
      ...toRefs(props),
      memberRecord,
    };
  },
});
</script>

<template lang="pug">
.com-opm-hr-request-user
  ComOpmMemberUserAvatar(:record='memberRecord')
</template>
