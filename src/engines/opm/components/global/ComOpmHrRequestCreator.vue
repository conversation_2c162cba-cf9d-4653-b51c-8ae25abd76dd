<script lang="ts">
import { toRefs } from '@vueuse/core';
import { defineComponent } from 'vue';

export default defineComponent({
  name: 'ComOpmHrRequestCreator',
  props: {
    record: { type: Object, required: true },
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
</script>

<template lang="pug">
.com-opm-hr-request-creator
  ComOpmMemberUserAvatar(:record='{ user: record.creator }')
</template>
