<script lang='ts'>
import { computed } from 'vue';
import { ref, defineComponent, toRefs } from 'vue';
import ComOpmJobTitlesCard from '../../../opm/job_titles/ComOpmJobTitlesCard.vue';
import { VStore } from '@/lib/vails';
const ComOpmMemberJobTitleList = defineComponent({
  name: 'ComOpmMemberJobTitleList',
  components: { ComOpmJobTitlesCard },
  props: {
    store: { type: Object, required: true },
    record: { type: Object, required: true },
  },
  setup(props) {
    const config = computed(() => ({
      store: props.store,
      mode: 'list',
      pagination: {
        hideOnSinglePage: true,
      },
      list: {
        gap: 16
      }
    }))
    return {
      ...toRefs(props),
      config,
    };
  },
});
export default ComOpmMemberJobTitleList;
</script>

<template lang="pug">
.com-opm-member-job-title-list
  TaIndexView.ta-index-view(:config='config' :showHeader='false')
    template(#card='{record}')
      ComOpmJobTitlesCard(:record='record')
</template>

<style lang="stylus" scoped>
.com-opm-member-job-title-list .ta-index-view :deep(.draggable)
  grid-template-columns repeat(auto-fill, minmax(220px, 1fr)) !important
.com-opm-member-job-title-list .ta-index-view :deep(.list-view__pagination)
  display none
.com-opm-member-job-title-list .ta-index-view :deep(.empty-placeholder)
  padding-top 0
  padding-bottom 0
</style>
