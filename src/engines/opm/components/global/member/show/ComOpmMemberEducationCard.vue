<script lang="ts">
import { defineComponent, toRefs, computed } from 'vue';
import dayjs from 'dayjs';
const ComOpmMemberEducationCard = defineComponent({
  name: 'ComOpmMemberEducationCard',
  components: {},
  props: {
    data: { type: Array, default: () => [] },
  },
  setup(props) {
    //TODO mockData -> props.data
    const mockData: any[] = [
      { school: '首都医科大学', time: '2017-09-10', qualification: '本科', major: '医学' },
      // { school: '首都医科大学', time: '2017-09-10', qualification: '本科', major: '医学' },
      // { school: '首都医科大学', time: '2017-09-10', qualification: '本科', major: '医学' },
      // { school: '首都医科大学', time: '2017-09-10', qualification: '本科', major: '医学' },
    ];

    const config = computed(() => ({
      columns: [
        { title: '序号', dataIndex: ['no'], render: 'No' },
        { title: '学校', dataIndex: ['school'] },
        {
          title: '时间',
          dataIndex: ['time'],
          render: (row: any) => dayjs(row.time).format('YYYY-MM-DD'),
        },
        { title: '学历', dataIndex: ['qualification'] },
        { title: '专业', dataIndex: ['major'] },
      ],
    }));

    const paginationConfig = {
      perPage: 5,
      hideOnSinglePage: true,
    };
    return {
      ...toRefs(props),
      mockData,
      config,
      paginationConfig,
    };
  },
});
export default ComOpmMemberEducationCard;
</script>

<template lang="pug">
.com-opm-member-education-card
  TaIndexTable.ta-index-table(
    :data='mockData',
    :config='config',
    :paginationConfig='paginationConfig'
  )
</template>

<style lang="stylus" scoped>
.com-opm-member-education-card
  .ta-index-table
    :deep(table)
      border 1px solid #e4e5e7
      border-radius 8px
      overflow hidden
    :deep(table) thead tr
      background-color #F3F4F6

    //-防止空数据时出现神奇的bug
    :deep(.ant-table-expanded-row-fixed)
      width 100% !important
</style>
