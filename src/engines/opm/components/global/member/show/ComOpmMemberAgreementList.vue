<script lang="ts">
import { defineComponent, toRefs } from 'vue';
import ComOpmMemberAgreementCard from '../../../opm/members/ComOpmMemberAgreementCard.vue';

const ComOpmMemberAgreementList = defineComponent({
  name: 'ComOpmMemberAgreementList',
  components: {
    ComOpmMemberAgreementCard,
  },
  props: {
    data: {
      type: Array,
      default: () => [],
    },
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
export default ComOpmMemberAgreementList;
</script>

<template lang="pug">
.com-opm-member-agreement-list.grid.grid-cols-2.gap-2
  ComOpmUserAgreementCard
</template>

<style lang="stylus" scoped></style>
