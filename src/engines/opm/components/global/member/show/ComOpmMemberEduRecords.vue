<script lang='ts'>
import { computed } from 'vue';
import { ref, defineComponent, toRefs } from 'vue';
const ComOpmMemberEduRecords = defineComponent({
  name: 'ComOpmMemberEduRecords',
  components: {},
  props: {
    store: {
      type: Object,
      required: true
    },
    //member record
    record: {
      type: Object,
      required: true
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  setup(props) {
    const config = computed(() => ({
      store: props.store,
      mode: 'table',
      pagination: {
        hideOnSinglePage: true,
      },
      actions: [
        { key: 'update', enabled: !props.disabled },
        { key: 'delete', enabled: !props.disabled },
      ],
      template: 'opm_edu_record'
    }))
    return {
      ...toRefs(props),
      config,
    };
  },
});
export default ComOpmMemberEduRecords;
</script>

<template lang="pug">
.com-opm-member-edu-records
  TaIndexView(:config='config' :showHeader='false')
</template>

<style lang="stylus" scoped></style>
