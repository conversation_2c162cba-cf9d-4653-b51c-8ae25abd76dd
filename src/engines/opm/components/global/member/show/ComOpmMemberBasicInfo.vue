<script lang="ts">
import { defineComponent, ref, toRefs } from 'vue';
import ComOpmMemberUserGender from '../../ComOpmMemberUserGender.vue';
import ComOpmCreateIncrPositionRequest from '../../../opm/incr_position_requests/ComOpmCreateIncrPositionRequest.vue';
import ComOpmCreateRevokePositionRequest from '../../../opm/revoke_position_requests/ComOpmCreateRevokePositionRequest.vue';
import ComOpmCreateRelocatePositionRequest from '../../../opm/relocate_position_requests/ComOpmCreateRelocatePositionRequest.vue';
import ComOpmCreateResignationRequest from '../../../opm/resignation_requests/ComOpmCreateResignationRequest.vue';
import { VStore } from '@/lib/vails';
import { OpmManageTagsApi } from '@/engines/opm/opm-core/apis/opm/manage/tags.api';

const ComOpmMemberBasicInfo = defineComponent({
  name: 'ComOpmMemberBasicInfo',
  components: {
    ComOpmMemberUserGender,
    ComOpmCreateIncrPositionRequest,
    ComOpmCreateRevokePositionRequest,
    ComOpmCreateRelocatePositionRequest,
    ComOpmCreateResignationRequest,
  },
  props: {
    store: { type: Object, required: true },
    record: { type: Object, required: true },
    ownershipRecord: { type: Object, required: true },
    disabled: { type: Boolean, default: false },
    tagStore: { type: Object },
  },
  setup(props, { emit }) {
    const visible = ref({
      incPositionModal: false,
      revokePositionModal: false,
      relocatePositionModal: false,
      resignationModel: false,
      editDrawer: false,
    });

    const editRecord = ref({});

    const actions = [
      {
        name: '编辑',
        icon: 'pencil',
        color: '#3F83F8',
        action: () => {
          props.store.find(props.record.id).then((res: any) => {
            const data = res.data;
            editRecord.value = props.store.new(data);
            visible.value.editDrawer = true;
          });
        },
      },
      {
        name: '岗位',
        icon: 'clipboard-copy',
        color: '#31C48D',
        wrapper: 'a-dropdown',
        action: () => {},
      },
      {
        name: '离职',
        icon: 'logout',
        color: '#F05252',
        action: () => {
          visible.value.resignationModel = true;
        },
      },
    ];

    const operMap = {
      create: {
        label: '新增',
        color: '#374151',
        icon: 'eye',
        action: () => {
          visible.value.incPositionModal = true;
        },
      },
      edit: {
        label: '转岗',
        color: '#374151',
        icon: 'download',
        action: () => {
          visible.value.relocatePositionModal = true;
        },
      },
      delete: {
        label: '离岗',
        color: '#374151',
        icon: 'pencil-alt',
        action: () => {
          visible.value.revokePositionModal = true;
        },
      },
    };

    const handleClick = (el: { key: keyof typeof operMap }) => {
      if (el.key) {
        operMap[el.key].action();
      }
    };

    const tagStore =
      props.tagStore ||
      new VStore(
        new OpmManageTagsApi({
          parents: [{ type: 'ownerships', id: props.ownershipRecord.id }],
        }),
      );

    tagStore.index({ q: { members_id_eq: props.record.id }, per_page: 100 });

    const onRefresh = () => {
      emit('refresh');
    };

    return {
      ...toRefs(props),
      actions,
      operMap,
      visible,
      handleClick,
      onRefresh,

      editRecord,
      tagRecords: tagStore.records,
    };
  },
});
export default ComOpmMemberBasicInfo;
</script>

<template lang="pug">
.com-opm-member-basic-info
  .flex.items-start
    TaAvatar.mr-4(:size='80' :user='record.user')
    .info.flex-shrink-0
      section.flex.items-center.mb-1
        .name.text-lg.font-medium.text-primary-900.mr-2 {{ record.user.name }}
        .flex.items-center.space-x-3.text-xs(style='padding-left:1em')
          ComRankTag(:rank='record.mainPosition?.duty_rank')
          //- ComColorfulTag(color='#1C64F2' text='在职')
          //- ComColorfulTag(color='#FF7300' text='试用期')
          //- .text-gray-400 有效期至：
          //-   span.text-gray-900 2021-12-31
      section.flex.items-center.text-sm.space-x-4.mb-1
        ComOpmMemberUserGender(:record='record')
        template(v-if='record.user?.mobile')
          .text-gray-200 |
          ComOpmPhoneMask(:value='record.user.mobile')
        .text-gray-200(v-if='record.user.email') |
        .email__info.flex.items-center(v-if='record.user.email')
          TaIcon.w-4.h-4.text-gray-400.mr-1(type='solid/mail')
          .info__value__color {{ record.user.email }}
      section.flex.items-center.text-sm.space-x-2(v-if='record.mainPosition')
        ComColorfulTag.text-xs.flex.items-center(
          color='#3F83F8'
          class='!px-1 !py-[2px]'
        )
          TaIcon.w-4.h-4.text-primary-500.mr-1(type='solid/briefcase')
          .text-xs 主岗
        .info__value__color {{ `${record.mainPosition?.department_name || '暂无部门'} - ${record.mainPosition.duty_name || '暂无岗位'}` }}

    //- 操作
    .actions.flex.space-x-2.ml-auto.flex-shrink-0(v-if='!disabled')
      template(v-for='(action, index) in actions')
        component(:is='action.wrapper || "div"')
          .rounded-md.text-white.px-4.py-2.flex.items-center.cursor-pointer(
            :style='`background-color: ${action.color};`',
            @click='action.action()'
          )
            TaIcon.w-4.h-4.mr-1(:type='`outline/${action.icon}`')
            span {{ action.name }}
          template(#overlay)
            a-menu(@click='(el)=>handleClick(el)')
              a-menu-item(v-for='(oper, operKey) in operMap' :key='operKey')
                .flex.items-center(:style='`color:${oper.color}`')
                  TaIcon.mr-3(
                    :type='`outline/${oper.icon}`',
                    style='width:14px;height:14px'
                  )
                  .text-sm.font-normal {{oper.label}}

  .flex.items-center.flex-wrap.gap-2.text-xs.mt-3(v-if='tagRecords.length > 0')
    .text-gray-400 标签：
    ComColorfulTag(v-for='tag in tagRecords', :text='tag.name', :color='tag.color')

  ComOpmCreateIncrPositionRequest(
    :memberRecord='record',
    :ownershipRecord='ownershipRecord'
    v-model:visible='visible.incPositionModal',
    @refresh='onRefresh'
  )
  ComOpmCreateRevokePositionRequest(
    :memberRecord='record',
    :ownershipRecord='ownershipRecord',
    v-model:visible='visible.revokePositionModal',
    @refresh='onRefresh'
  )
  ComOpmCreateRelocatePositionRequest(
    :memberRecord='record',
    :ownershipRecord='ownershipRecord',
    v-model:visible='visible.relocatePositionModal',
    @refresh='onRefresh'
  )

  ComOpmCreateResignationRequest(
    :memberRecord='record',
    :ownershipRecord='ownershipRecord',
    v-model:visible='visible.resignationModel',
    @refresh='onRefresh'
  )

  TaTemplateFormWithActionsDrawer(
    v-model:visible='visible.editDrawer',
    :record='editRecord',
    :template='ownershipRecord.member_identity.form'
    title='编辑人员信息',
  )
</template>

<style lang="stylus" scoped>
.com-opm-member-basic-info :deep(.ant-avatar-circle)
  border-radius 8px
.info__value__color
  color #3F3F46
</style>
