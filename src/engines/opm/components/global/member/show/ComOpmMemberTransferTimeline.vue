<script lang="ts">
import { defineComponent, onMounted, ref, toRefs } from 'vue';
import ComOpmMemberTransferTimelineItem from '../../../opm/members/ComOpmMemberTransferTimelineItem.vue';
import { VStore } from '@/lib/vails';
import { OpmManageHrTransfersApi } from '@/engines/opm/opm-core/apis/opm/manage/hr_transfers.api';
import { OpmHrTransferModel } from '@/engines/opm/opm-core/models/opm/hr_transfers';

const ComOpmMemberTransferTimeline = defineComponent({
  name: 'ComOpmMemberTransferTimeline',
  components: { ComOpmMemberTransferTimelineItem },
  props: {
    record: { type: Object, required: true },
    ownershipRecord: { type: Object, required: true },
  },
  setup(props) {
    const transferStore = new VStore(
      new OpmManageHrTransfersApi({
        parents: [{ type: 'ownerships', id: props.ownershipRecord.id }],
        params: {
          q: { member_id_eq: props.record.id },
        },
      }),
      OpmHrTransferModel,
    );

    const fetchTransfers = async () => {
      await transferStore.index({ per_page: 99999, q: { s: 'id asc' } });
    };

    onMounted(() => {
      fetchTransfers();
    });

    return {
      ...toRefs(props),
      fetchTransfers,
      records: transferStore.records,
    };
  },
});
export default ComOpmMemberTransferTimeline;
</script>

<template lang="pug">
.com-opm-member-transfer-timeline.pt-2
  a-timeline
    ComOpmMemberTransferTimelineItem(
      v-for='record in records',
      :key='record.id',
      :record='record',
    )

</template>

<style lang="stylus" scoped></style>
