<script lang="ts">
import { computed, defineComponent, toRefs, ref } from 'vue';
import ComOpmItemCard from '@/engines/opm/components/workplane/ComOpmItemCard.vue';
import ComOpmItemInfo from '@/engines/opm/components/opm/items/ComOpmItemInfo.vue';
import ComOpmWinItemCard from '@/engines/opm/components/opm/win_items/ComOpmWinItemCard.vue';
import ComOpmWinItemInfo from '@/engines/opm/components/opm/win_items/ComOpmWinItemInfo.vue';
import ComOpmTechItemCard from '@/engines/opm/components/opm/tech_items/ComOpmTechItemCard.vue';
import ComOpmTechItemInfo from '@/engines/opm/components/opm/tech_items/ComOpmTechItemInfo.vue';
import ComOpmTeachingItemCard from '@/engines/opm/components/opm/teaching_items/ComOpmTeachingItemCard.vue';
import ComOpmTeachingItemInfo from '@/engines/opm/components/opm/teaching_items/ComOpmTeachingItemInfo.vue';

const ComOpmMemberTrainList = defineComponent({
  name: 'ComOpmMemberTrainList',
  components: {
    ComOpmItemCard,
    ComOpmItemInfo,
    ComOpmWinItemCard,
    ComOpmWinItemInfo,
    ComOpmTechItemCard,
    ComOpmTechItemInfo,
    ComOpmTeachingItemCard,
    ComOpmTeachingItemInfo,
  },
  props: {
    ownershipRecord: {
      type: Object,
      required: true,
    },
    record: {
      type: Object,
      required: true,
    },
    store: {
      type: Object,
      required: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    template: {
      type: String,
      default: 'opm_item',
    },
  },
  setup(props) {
    const config = computed(() => ({
      recordName: '',
      store: props.store,
      detail: {
        mode: 'drawer',
        width: '900px',
      },
      template: props.template,
      pagination: {
        hideOnSinglePage: true,
      },
      mode: 'list',
      list: {
        gap: 16,
        splitCount: 2,
      },
    }));
    const taindexview = ref<any>(null);
    const onCreate = () => {
      taindexview.value?.slotActions?.onCreate();
    };
    return {
      ...toRefs(props),
      config,
      taindexview,
      onCreate,
    };
  },
});
export default ComOpmMemberTrainList;
</script>

<template lang="pug">
.com-opm-member-train-list(
  :style='`--list-view__pagination-placeholder-height:${store?.records?.value?.length > 0 ? "48px" : "0"}`'
)
  TaIndexView.ta-index-view(:config='config', :showHeader='false', ref='taindexview')
    template(#card='{ record }')
      component(:is='record.cardCompName', :record='record')
    template(#detail='{ record, onClose }')
      .com-opm-item-detail__wrapper.px-6.py-4.h-full.bg-gray-100
        component.mb-4(:is='record.cardCompName', :record='record')
        component(:is='record.detailCompName', :record='record')
    template(#empty)
      .flex.justify-center.items-center(v-if='!disabled')
        a-button(type='primary', @click='onCreate') 添加
</template>

<style lang="stylus" scoped>
.com-opm-member-train-list :deep(.list-view__pagination) {
  margin-bottom: 0 !important;
}

.com-opm-member-train-list :deep(.empty-placeholder) {
  padding-top: 0;
  padding-bottom: 0;
}

.com-opm-member-train-list .ta-index-view :deep(.list-view__pagination_placeholder) {
  height: var(--list-view__pagination-placeholder-height, 0) !important;
}
</style>
