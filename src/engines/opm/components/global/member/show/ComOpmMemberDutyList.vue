<script lang="ts">
import { defineComponent, toRefs } from 'vue';
import ComOpmMemberDutyCard from '../../../opm/members/ComOpmMemberDutyCard.vue';

const ComOpmMemberDutyList = defineComponent({
  name: 'ComOpmMemberDutyList',
  components: { ComOpmMemberDutyCard },
  props: {
    record: { type: Object, required: true },
    store: { type: Object, required: true },
    disabled: { type: Boolean, default: false },
  },
  setup(props) {
    const onChangePriority = async (item: any) => {
      await props.store.sendMemberAction({
        id: props.record.id,
        action: 'main_position',
        config: {
          data: {
            ...item,
          },
        },
      });
      props.record.fetch();
    };

    return {
      ...toRefs(props),
      onChangePriority,
    };
  },
});
export default ComOpmMemberDutyList;
</script>

<template lang="pug">
.com-opm-user-duty-list.grid.gap-4
  ComOpmMemberDutyCard.cursor-pointer(
    v-for='item in record.departmentsDuties',
    :item='item'
    :disabled='disabled'
    @changePriority='onChangePriority'
  )
</template>

<style lang="stylus" scoped>
.com-opm-user-duty-list
  grid-template-columns repeat(auto-fill, minmax(230px, 1fr))
</style>
