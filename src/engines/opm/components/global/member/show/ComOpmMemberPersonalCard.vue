<script lang="ts">
import { VObject } from '@/lib/vails';
import { get } from 'lodash';
import { computed, defineComponent, toRefs } from 'vue';

const ComOpmMemberPersonalCard = defineComponent({
  name: 'ComOpmMemberPersonalCard',
  components: {},
  props: {
    record: { type: Object, required: true },
  },
  setup(props) {
    const memberIdentityForm = computed(() => props.record.member_identity_form);
    const data = computed(() => {
      const fields = memberIdentityForm.value?.fields?.[0]?.layout
        ? memberIdentityForm.value?.fields?.[0]?.fields || []
        : memberIdentityForm.value?.fields || [];

      return fields.map((field: VObject) => ({
        label: field.name,
        value: get(props.record, field.model_key) || '',
        span: Math.floor(field.options.span / 8),
      }));
    });

    const groupedData = computed(() => {
      const result: any[] = [];
      let tempGroup: any[] = [];
      data.value.forEach((item: any) => {
        if (item.span === 3) {
          if (tempGroup.length) {
            result.push(tempGroup);
            tempGroup = [];
          }
          result.push([item]);
        } else {
          tempGroup.push(item);
          if (tempGroup.length === 2) {
            result.push(tempGroup);
            tempGroup = [];
          }
        }
      });

      return result;
    });

    return {
      ...toRefs(props),
      groupedData,
    };
  },
});
export default ComOpmMemberPersonalCard;
</script>

<template lang="pug">
.com-opm-member-personal-card
  table.info-table.text-sm.rounded-lg
    tr(v-for='grp in groupedData')
      template(v-for='item in grp')
        td.label {{ item.label }}
        td.value(:colspan='item.span') {{ item.value }}
</template>

<style lang="stylus" scoped>
.com-opm-member-personal-card {
  .info-table, .info-table td {
    border: 1px solid #E5E7EB;
  }

  .info-table {
    width: 100%;
    border-radius: 8px;
    border-collapse: collapse;
    border-style: hidden;
    overflow: hidden;
    box-shadow: 0 0 0 1px #E5E7EB;
  }

  .info-table td {
    padding: 12px;
  }

  .info-table .label {
    width: 15%;
    background-color: #F1F4F9;
    color: #384C9C;
  }

  .info-table .value {
    width: 35%;
    @apply: text-gray-900;
  }

  .info-table .long-text {
    word-break: break-all;
  }
}
</style>
