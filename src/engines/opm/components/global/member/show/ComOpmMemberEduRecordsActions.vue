<script lang='ts'>
import { ref, defineComponent, toRefs } from 'vue';
const ComOpmMemberEduRecordsActions = defineComponent({
  name: 'ComOpmMemberEduRecordsActions',
  components: {},
  props: {
    store: { type: Object, required: true },
  },
  setup(props) {
    const template = 'opm_edu_record'
    const visibleDrawer = ref(false)
    const editRecord = ref({})
    const onCreate = () => {
      visibleDrawer.value = true
      editRecord.value = props.store.new({})
    }
    return {
      ...toRefs(props),
      visibleDrawer,
      onCreate,
      template,
      editRecord,
    };
  },
});
export default ComOpmMemberEduRecordsActions;
</script>

<template lang="pug">
.com-opm-member-edu-records-actions
  TaIcon.w-4.h-4.text-primary-700.cursor-pointer(type='outline/plus',@click='onCreate')

  TaTemplateFormWithActionsDrawer(
    v-if='visibleDrawer',
    :template='template',
    :record='editRecord',
    title='添加教育经历',
    v-model:visible='visibleDrawer',
  )
</template>

<style lang="stylus" scoped></style>
