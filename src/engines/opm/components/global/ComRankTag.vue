<script lang="ts">
import { defineComponent, toRefs } from 'vue';

const ComRankTag = defineComponent({
  name: 'ComRankTag',
  components: {},
  props: {
    rank: { type: String, default: 'employee' },
  },
  setup(props) {
    const staffIcon =
      'url(https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/jetset/%E5%91%98%E5%B7%A5.png)';
    const leaderIcon =
      'url(https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/jetset/%E9%A2%86%E5%AF%BC.png)';

    const rankMap = {
      manage: {
        icon: leaderIcon,
        color: '#3F83F8',
        text: '主管',
      },
      charge: {
        icon: leaderIcon,
        color: '#3F83F8',
        text: '分管',
      },
      employee: {
        icon: staffIcon,
        color: '#16BDCA',
        text: '员工',
      },
    };
    return {
      ...toRefs(props),
      rankMap,
    };
  },
});
export default ComRankTag;
</script>

<template lang="pug">
.relative(v-if='rankMap[rank]')
  ComColorfulTag.inline.text-xs(:color='rankMap[rank]?.color' :text='rankMap[rank]?.text')
  .absolute.top-0.h-full.flex.items-center(style='left:-1em')
    .w-5.h-5.bg-cover(
      :style='`background-image:${rankMap[rank]?.icon};`'
    )
</template>

<style lang="stylus" scoped></style>
