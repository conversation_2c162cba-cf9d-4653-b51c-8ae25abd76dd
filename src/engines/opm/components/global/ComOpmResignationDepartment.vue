<script lang="ts">
import { defineComponent, toRefs } from 'vue';

export default defineComponent({
  name: 'ComOpmtResignationDepartment',
  props: {
    record: { type: Object, required: true },
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
</script>

<template lang="pug">
.com-opm-resignation-department
  .flex.items-center(v-if='record.department?.name')
    ComOpmDepartmentLabel.mr-1.px-1.rounded-sm(:record='record.department')
    .text-sm {{ record.department?.name }}
  span(v-else) -
</template>
