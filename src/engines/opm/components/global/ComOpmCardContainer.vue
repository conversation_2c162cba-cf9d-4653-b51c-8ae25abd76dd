<script lang='ts'>
import { ref, defineComponent, toRefs } from 'vue';
const ComOpmCardContainer = defineComponent({
  name: 'ComOpmCardContainer',
  components: {},
  props: {
    title: {
      type: String,
      default: ''
    }
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
export default ComOpmCardContainer;
</script>

<template lang="pug">
.com-opm-card-container.rounded-lg.bg-white.py-4.px-6
  header.text-primary-900.font-medium.text-base.mb-4.flex.items-center
    .title(v-if='title') {{ title }}
    .ml-auto
      slot(name='actions')
  slot
</template>

<style lang="stylus" scoped></style>
