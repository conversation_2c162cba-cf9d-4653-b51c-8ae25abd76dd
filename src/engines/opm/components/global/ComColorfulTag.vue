<script lang="ts">
import { ref, defineComponent, toRefs, computed } from 'vue';
import useColor from '../../utils/hooks/useColor';
import colors from 'windicss/colors';

const cantUseColors = ['transparent', 'inherit', 'current', 'black', 'white'];

const ComColorfulTag = defineComponent({
  name: 'ComColorfulTag',
  components: {},
  props: {
    text: {
      type: String,
      default: '',
    },
    color: {
      type: String,
      default: '#05B7C5',
    },
    isCustom: {
      type: Boolean,
      default: false,
    },
  },
  setup(props) {
    const { isValidColor, hexToRgb } = useColor();
    const isSupportColor = computed(() => {
      return !cantUseColors.includes(props.color) && props.color in colors;
    });

    const fmtRgb2Rgba = (rgbArr: number[], alpha: number = 1) => {
      return `rgba(${rgbArr.join(' ')} / ${alpha})`;
    };

    const computedStyle = computed(() => {
      let textColor, bgColor;
      if (props.isCustom) return '';

      if (isValidColor(props.color)) {
        textColor = fmtRgb2Rgba(hexToRgb(props.color));
        bgColor = fmtRgb2Rgba(hexToRgb(props.color), 0.1);
      } else if (isSupportColor.value) {
        textColor = (colors as any)[props.color][800];
        bgColor = (colors as any)[props.color][100];
      } else {
        textColor = 'rgb(31 41 55)';
        bgColor = 'rgb(243 244 246)';
      }

      return `color:${textColor};background-color:${bgColor};`;
    });
    return {
      ...toRefs(props),
      computedStyle,
    };
  },
});
export default ComColorfulTag;
</script>

<template lang="pug">
div.com-colorful-tag(:style='computedStyle')
  slot
    | {{ text }}
</template>

<style lang="stylus" scoped>
:where(.com-colorful-tag)
  padding 2px 10px
  border-radius 4px
</style>
