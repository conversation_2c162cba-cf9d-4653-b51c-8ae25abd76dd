<script lang="ts">
import { defineComponent, toRefs, computed } from 'vue';

const ComOpmEmployRequestDepartment = defineComponent({
  name: 'ComOpmEmployRequestDepartment',
  props: {
    record: { type: Object, required: true },
  },
  setup(props) {
    const department = computed(() => {
      return props.record.employ_invite?.department || {};
    });
    const computedStyle = computed(() => {
      return `background-color:${department.value.department_identity_color || '#31C48D'}; `;
    });

    return {
      ...toRefs(props),
      computedStyle,
      department,
    };
  },
});
export default ComOpmEmployRequestDepartment;
</script>

<template lang="pug">
.com-opm-employ-request-department.flex.items-center
  .inline.text-white.tag.rounded.mr-2(
    :style='computedStyle'
  ) {{ record.department_identity_name || "部门" }}
  .text-sm.text-gray-900 {{ department?.name }}
</template>

<style lang="stylus" scoped>
.com-opm-employ-request-department
  .tag
    padding: 0 4px;
</style>
