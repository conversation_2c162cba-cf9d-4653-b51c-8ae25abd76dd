<script lang="ts">
import { toRefs } from '@vueuse/core';
import { defineComponent } from 'vue';

export default defineComponent({
  name: 'ComOpmEmployRequestInviter',
  props: {
    record: { type: Object, required: true },
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
</script>

<template lang="pug">
.com-opm-employ-request-inviter
  ComOpmMemberUserAvatar(:record='{ user: record.inviter}')
</template>
