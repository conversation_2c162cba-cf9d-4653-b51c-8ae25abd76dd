<script lang="ts">
import { defineComponent, toRefs } from 'vue';

export default defineComponent({
  name: 'ComOpmtRelocateFromDepartment',
  props: {
    record: { type: Object, required: true },
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
</script>

<template lang="pug">
.com-opm-relocate-from-department
  .flex.items-center(v-if='record.from_department?.id')
    ComOpmDepartmentLabel.mr-1.px-1.rounded-sm(:record='record.from_department')
    .text-sm {{ record.from_department?.name }}
  span(v-else) -
</template>
