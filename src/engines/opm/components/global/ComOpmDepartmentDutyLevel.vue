<script lang="ts">
import { defineComponent, toRefs, computed } from 'vue';

const ComOpmDepartmentDutyLevel = defineComponent({
  name: 'ComOpmDepartmentDutyLevel',
  components: {},
  props: {
    record: { type: Object, required: true },
  },
  setup(props) {
    const computedStyle = computed(() => {
      return styleConfig[props.record.level];
    });

    const styleConfig: any = {
      本级: { background: 'rgba(63, 131, 248, 0.2)', color: '#3F83F8' },
      下级: { background: 'rgba(22, 189, 202, 0.2)', color: '#16BDCA' },
    };
    return {
      ...toRefs(props),
      computedStyle,
    };
  },
});
export default ComOpmDepartmentDutyLevel;
</script>

<template lang="pug">
.com-opm-department-duty-level
  .inline.tag.rounded.text-xs(
    :style='computedStyle'
  ) {{ record.level }}
</template>

<style lang="stylus" scoped>
.com-opm-department-duty-level
  .tag
    padding: 2px 10px;
</style>
