<script lang="ts">
import { computed, defineComponent, toRefs } from 'vue';
import ComOpmMemberAvatarCard from '../../opm/members/ComOpmMemberAvatarCard.vue';

export default defineComponent({
  name: 'ComOpmMemberCard',
  components: { ComOpmMemberAvatarCard },
  props: {
    value: { type: Object, required: true },
  },
  setup(props) {
    const member = computed(() => {
      return props.value.member || {};
    });
    return {
      ...toRefs(props),
      member,
    };
  },
});
</script>

<template lang="pug">
.pt-3
  ComOpmMemberAvatarCard(:record='member')
</template>
