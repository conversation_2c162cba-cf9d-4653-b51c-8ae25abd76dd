<script lang="ts">
import { defineComponent, computed, ref } from 'vue';
import { debounce } from 'lodash';
import { message, Modal } from 'ant-design-vue';
import { OpmManageMembersApi } from '../../../opm-core/apis/opm/manage/members.api';

const ComOpmMemberAutoComplete = defineComponent({
  name: 'ComOpmMemberAutoComplete',
  props: {
    value: { type: Object, default: () => ({}) },
    disabled: { type: Boolean, default: false },
  },
  setup(props, { emit }) {
    const localValue = computed({
      get: () => props.value,
      set: val => emit('update:value', val),
    });

    const api = new OpmManageMembersApi({
      parents: [{ type: 'ownerships', id: localValue.value.ownership_id }],
    });

    const key = 'search';
    const loading = ref(false);

    const onSearch = () => {
      if (loading.value || !localValue.value.account) return;

      loading.value = true;
      const search = message.loading({ content: '正在搜索账号', key });
      api
        .sendCollectionAction('search', { params: { account: localValue.value.account } })
        .then(({ data }) => {
          loading.value = false;
          if (data.records[0]) {
            search();
            const record = data.records[0];
            if (!record.user) return;
            Modal.info({
              title: `检测到账号：${record.user.name}`,
              content: `账号：${record.user.account}、姓名：${record.user.name}、手机号：${record.user.mobile}，是否添加此账号？`,
              okText: '确定',
              cancelText: '取消',
              onOk() {
                localValue.value.user = record.user;
                localValue.value.payload = record.payload;
                localValue.value.model_payload = record.model_payload;
              },
            });
          } else {
            localValue.value.user = { account: localValue.value.account };
            message.warning({ content: '未搜索到该账号，你可以手动创建', key });
          }
        });
    };

    const debounceInput: any = debounce(onSearch, 1000);

    const validate = () => {
      return new Promise<void>((resolve, reject) => {
        if (!localValue.value.account) {
          reject(new Error('请填写正确的账号'));
        } else {
          resolve();
        }
      });
    };

    return {
      localValue,
      onSearch,
      loading,
      validate,
      debounceInput,
    };
  },
});

export default ComOpmMemberAutoComplete;
</script>
<template lang="pug">
.com-opm-member-auto-complete
  .value(v-if='disabled || localValue.id') {{ localValue.user?.account }}
  TaInput.form-input-item(
    v-else
    v-model:value='localValue.account',
    placeholder='请输入帐号（支持自动搜索）',
    @input='debounceInput',
    :loading='loading'
    :disabled='localValue.id'
  )
</template>

<stype></stype>
