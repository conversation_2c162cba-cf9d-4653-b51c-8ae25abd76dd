<script lang="ts">
import { defineComponent, toRefs } from 'vue';
import ComOpmMemberInfoCard from '../opm/members/ComOpmMemberInfoCard.vue';
export default defineComponent({
  name: 'ComOpmMemberUserAvatar',
  components: { ComOpmMemberInfoCard },
  props: { record: { type: Object, required: true } },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
</script>

<template lang="pug">
.com-opm-member-user-avatar
  a-popover(trigger='hover')
    template(#content)
      ComOpmMemberInfoCard(:record='record' :isPopoverContent='true')
    .flex.items-center
      TaAvatar.mr-2(:user='record.user', :size='28')
      .text-sm.text-gray-900 {{ record.user?.name || record.user?.account }}

</template>


<style lang='stylus' scoped>
.com-opm-member-user-avatar:deep(.ant-popover-inner-content)
  background-color transparent !important
</style>
