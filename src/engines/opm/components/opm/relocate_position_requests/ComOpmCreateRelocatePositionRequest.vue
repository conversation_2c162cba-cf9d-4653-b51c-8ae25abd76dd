<script lang="ts">
import { defineComponent, computed, ref, toRefs } from 'vue';
import ComBpmInstanceDetailDialog from '@/engines/bpm/components/ComBpmInstanceDetailDialog.vue';
import ComOpmMemberAvatarCard from '../members/ComOpmMemberAvatarCard.vue';
import { VStore } from '@/lib/vails';
import { OpmManageRelocatePositionRequestsApi } from '@/engines/opm/opm-core/apis/opm/manage/relocate_position_requests.api';
import { OpmRelocatePositionRequestModel } from '@/engines/opm/opm-core/models/opm/relocate_position_requests';
import { message } from 'ant-design-vue';

export default defineComponent({
  name: 'ComOpmRelocatePositionRequest',
  components: {
    ComBpmInstanceDetailDialog,
    ComOpmMemberAvatarCard,
  },
  props: {
    visible: { type: Boolean, default: false },
    memberRecord: { type: Object, required: true },
    ownershipRecord: { type: Object, required: true },
  },
  setup(props, { emit }) {
    const localVisible = computed({
      get: () => props.visible,
      set: val => emit('update:visible', val),
    });

    const selectedRecord = ref<any>({});
    const onSelect = (record: any) => {
      selectedRecord.value = record;
    };

    const editRecord = ref<any>({
      org: {
        name: props.ownershipRecord.org?.name,
      },
      member: props.memberRecord,
      ownership_id: props.ownershipRecord.id,
    });

    const visibleInstance = ref(false);
    const activeInstanceId = ref();

    const requestStore = new VStore(
      new OpmManageRelocatePositionRequestsApi({
        parents: [{ type: 'ownerships', id: props.ownershipRecord.id }],
      }),
      OpmRelocatePositionRequestModel,
    );

    const formRef = ref();

    const onSubmit = () => {
      if (!selectedRecord.value?.duty_id && !selectedRecord.value?.department_id) {
        message.warning('请选择岗位');
        return;
      }
      formRef.value.validate().then(() => {
        const submitValue = {
          payload: {
            from_duty_id: selectedRecord.value.duty_id,
            from_department_id: selectedRecord.value.department_id,
            from_duty_name: selectedRecord.value.duty_name,
            from_department_name: selectedRecord.value.department_name,
            from_duty_rank: selectedRecord.value.duty_rank,
            from_priority: selectedRecord.value.priority,
            to_duty_id: editRecord.value.duty_id,
            to_department_id: editRecord.value.department_id,
            to_duty_rank: editRecord.value.duty_rank,
            to_priority: editRecord.value.priority,
          },
          member_id: props.memberRecord.id,
        };

        requestStore.create(submitValue as any).then((res: any) => {
          localVisible.value = false;
          if (res.flowable_instance_infos?.[0]?.id) {
            visibleInstance.value = true;
            activeInstanceId.value = res.flowable_instance_infos?.[0]?.id;
          }
          emit('refresh');
        });
      });
    };

    const onDialogClose = () => {
      emit('refresh');
    };

    return {
      ...toRefs(props),
      localVisible,
      selectedRecord,
      onSelect,
      editRecord,
      visibleInstance,
      activeInstanceId,
      onSubmit,
      formRef,
      onDialogClose,
    };
  },
});
</script>

<template lang="pug">
.com-opm-create-relocate-position-request
  ComBpmInstanceDetailDialog(v-model:visible='visibleInstance', :instanceId='activeInstanceId', @close='onDialogClose')
  a-modal(
    v-model:visible='localVisible',
    title='转岗',
    width='640px'
  )
    .relocate-poistion-form-container
      .px-5
        ComOpmMemberAvatarCard(:record='memberRecord')
        .grid.grid-cols-2.gap-2.mt-4
          .bg-gray-50.rounded.p-3.card.border.border-gray-50.cursor-pointer.relative(
            :class='{ "selected": selectedRecord.department_id == dd.department_id && selectedRecord.duty_id == dd.duty_id }',
            @click='onSelect(dd)'
            v-for='dd in memberRecord.departmentsDuties'
          )
            .checked-icon.rounded-tl.rounded-br.overflow-hidden.absolute.bg-primary(
              v-show='selectedRecord.department_id == dd.department_id && selectedRecord.duty_id == dd.duty_id'
            )
              TaIcon.w-2.h-2.text-white(type='outline/check')
            .pl-4.mb-2
              ComRankTag(:type='dd.duty_rank')
            .flex.items-center.mb-1
              TaIcon.w-4.h-4.mr-1.text-gray-400(type='solid/briefcase')
              .text-sm(class='text-[#3F3F46]') {{  dd.department_name || "暂无部门" }}
            .flex.items-center
              TaIcon.w-4.h-4.mr-1.text-gray-400(type='solid/user')
              .text-sm(class='text-[#3F3F46]') {{  dd.duty_name || "暂无岗位" }}
      TaTemplateForm(
        ref='formRef',
        :modelValue='editRecord',
        template='opm_relocate_position_request#new_request'
      )
    template(#footer)
      .flex.items-center.justify-end
        .px-5.py-2.bg-white.border.text-sm.font-medium.cursor-pointer.rounded.mr-2(
          class='text-[#1F2A37] border-[#E5E7EB]',
          @click='() => localVisible = false'
        ) 取消
        .px-5.py-2.text-white.text-sm.font-medium.cursor-pointer.rounded.bg-primary(
          @click='onSubmit()'
        ) 发起
</template>

<style lang="stylus" scoped>
.relocate-poistion-form-container
  .card
    &.selected, &:hover
      border: 1px solid $primary-color;
      background: white;
    .checked-icon
      bottom: -1px;
      right: -1px;
  .bg-primary
    background: $primary-color;
  >>>.is-parent-container
    border-radius: 0;
    margin: 0;
</style>
