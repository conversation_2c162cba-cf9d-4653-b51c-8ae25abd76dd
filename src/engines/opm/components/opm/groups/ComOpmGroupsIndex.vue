<script lang="ts">
import { defineComponent, computed, ref, Ref, toRefs, reactive } from 'vue';
import { TaIndexViewTabInterface } from '@/components/global/ta-component/TaIndexView/types';
import { VObject } from '@/lib/vails/model';
import ComOpmGroupsShow from './ComOpmGroupsShow.vue'

const ComOpmGroupsIndex = defineComponent({
  name: 'ComOpmGroupsIndex',
  components: {
    ComOpmGroupsShow,
  },
  props: {
    store: { type: Object, required: true },
    selectedGroupId: { type: Number, required: true },
  },
  setup(props, { emit }) {
    const taindexview = ref<any>(null)
    const onCreate = () => {
      taindexview.value?.slotActions?.onCreate()
    }
    const config = computed(() => ({
      recordName: '职称管理',
      store: props.store,
      pagination: {
        perPage: 30,
        hideOnSinglePage: true,
      },
      template: 'opm_group',
      mode: 'list',
      // detail: {
      //   mode: 'auto',
      //   mode: 'drawer',
      //   width: '1100px',
      // },
      // editApi:
      // showCount: true,
      actions: [
        { key: 'create', enabled: true },
        { key: 'update', enabled: true },
        { key: 'delete', enabled: true },
        { key: 'import', enabled: true },
        { key: 'export', enabled: true },
      ],
      list: {
        scroll: { y: 'auto' },
      },
      // selection: {
      //   showByDefault: false;
      //   showSwitch: false;
      // };
      searcherSimpleOptions: [{ key: 'name', label: '职称分组', type: 'string' }],
    }));


    const operMap = {
      edit: {
        label: '编辑',
        color: '#111928',
        icon: 'pencil-alt',
        action: async (record: VObject) => {
          taindexview.value?.slotActions?.onEdit(record)
        },
      },
      delete: {
        label: '删除',
        color: '#F05252',
        icon: 'trash',
        action: async (record: VObject) => {
          taindexview.value?.slotActions?.onDelete(record)
        },
      },
    }
    const handleClick = (el: { key: keyof typeof operMap }, record: any) => {
      if (el.key) {
        operMap[el.key].action(record);
      }
    }

    const _selectedGroupId = computed({
      get: () => props.selectedGroupId,
      set: (value) => {
        if (value === props.selectedGroupId) {
          emit('update:selectedGroupId', 0)
        } else {
          emit('update:selectedGroupId', value)
        }
      },
    })
    const onShow = (record: VObject) => {
      _selectedGroupId.value = record.id
    }
    return {
      ...toRefs(props),
      config,
      taindexview,
      onCreate,
      onShow,
      operMap,
      handleClick,
    };
  },
});

export default ComOpmGroupsIndex;
</script>

<template lang="pug">
.com-opm-admin-groups-index.flex.flex-col
  .actions__wrapper.flex.w-full.mb-4.items-center
    .text-base.text-primary-900.font-medium.mr-auto 职称序列
    .btn.bg-primary-500.cursor-pointer(@click='onCreate' v-if='!disabled')
      TaIcon.w-4.h-4(type='outline/plus')
      span.pl-1.text-xs 新建
  TaIndexView.flex-grow.h-0(
    ref='taindexview'
    :config='config'
    :showHeader='false'
    @onShow='onShow'
  )
    template(#card='{record}')
      .group__card.rounded-md.p-2.flex.items-center(
        :class='{"hover:bg-gray-50":true,"bg-primary-50": record.id === selectedGroupId}'
      )
        .text-sm.text-gray-900.mr-auto(style='line-height:24px;') {{ record.name }}
        .text-xs.text-gray-400 {{ record.job_titles_count }}
        .dots.h-6.w-6.rounded-md.grid.ml-2.hidden(
          class='hover:bg-[rgba(0,0,0,0.05)]'
          @click.stop=''
        )
          a-dropdown(trigger="click" placement='bottom')
            TaIcon.place-self-center.text-gray-600.cursor-pointer(
              class='w-6 h-6'
              type='solid/dots-horizontal2'
            )
            template(#overlay)
              a-menu(@click='(el)=>handleClick(el,record)')
                a-menu-item(v-for='(oper,actionKey) in operMap',:key='actionKey')
                  .flex.items-center(:style='`color:${oper.color}`')
                    TaIcon.mr-3(
                      :type='`outline/${oper.icon}`',
                      style='width:14px;height:14px'
                    )
                    .text-sm.font-normal {{oper.label}}

    //- template(#table)
    //-    a-table-column(title='名称' dataIndex='record')
    //- template(#detail='{ record, onClose }')
    //-   ComOpmGroupsShow(
    //-     v-if='record.id'
    //-     :store='store'
    //-     :extendRoute='`/opm/admin/groups/${record.id}`'
    //-     :editable='editable'
    //-     @afterDelete='onClose'
    //-     @afterExtend='onClose'
    //-   )

</template>

<style lang="stylus" scoped>
.group__card:hover .dots
  display block
.com-opm-admin-groups-index
  height 100%
  width 100%
  .btn
    @apply px-3 py-6px rounded text-sm text-white font-medium flex items-center;
</style>
