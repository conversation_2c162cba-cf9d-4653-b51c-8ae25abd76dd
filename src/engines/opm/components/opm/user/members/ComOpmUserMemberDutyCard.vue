<script lang="ts">
import { defineComponent, toRefs } from 'vue';

const ComOpmUserMemberDutyCard = defineComponent({
  name: 'ComOpmUserMemberDutyCard',
  props: {
    item: { type: Object, required: true },
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
export default ComOpmUserMemberDutyCard;
</script>

<template lang="pug">
.com-opm-user-member-duty-card.rounded-md.bg-gray-50.p-3.relative(
  :class='{"checked": item.priority}'
)
  header.flex.items-center.mb-2.h-6(style='padding-left:1em')
    ComRankTag(:rank='item.duty_rank')
    .main__duty.ml-auto
      ComColorfulTag.text-xs.flex.items-center(
        v-if='item.priority'
        color='#3F83F8'
        class='!px-1 !py-[2px]'
      )
        TaIcon.w-4.h-4.text-primary-500.mr-1(type='solid/briefcase')
        .text-xs 主岗
  section.flex.items-center.mb-1
    TaIcon.text-gray-400.w-4.h-4(type='solid/briefcase')
    .label.text-sm.pl-1 {{ item.department_name || '暂无部门' }}
  section.flex.items-center
    TaIcon.text-gray-400.w-4.h-4(type='solid/user')
    .label.text-sm.pl-1 {{ item.duty_name || '暂无无岗位' }}
</template>

<style lang="stylus" scoped>
.com-opm-user-member-duty-card
  border 1px solid #F9FAFB
.com-opm-user-member-duty-card section .label
 color #3F3F46
.com-opm-user-member-duty-card
  &:hover, &.checked
    border-color $primary-color-500
</style>
