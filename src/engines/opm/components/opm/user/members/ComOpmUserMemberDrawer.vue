<script lang='ts'>
import { ref, defineComponent, toRefs, computed } from 'vue';
import ComOpmCardContainer from '../../../global/ComOpmCardContainer.vue';

const ComOpmUserMemberDrawer = defineComponent({
  name: 'ComOpmUserMemberDrawer',
  components: {},
  props: {
    visible: {
      type: Boolean,
      default: false,
    },
    store: {
      type: Object,
      required: true
    },
    record: {
      type: Object,
      required: true
    },
    ownershipRecord: {
      type: Object,
      required: true
    },
    tagStore: {
      type: Object
    }
  },
  setup(props, { emit }) {
    const _visible = computed({
      get() {
        return props.visible;
      },
      set(val) {
        emit('update:visible', val);
      }

    })
    return {
      ...toRefs(props),
      _visible,
    };
  },
});
export default ComOpmUserMemberDrawer;
</script>

<template lang="pug">
TaNoPaddingDrawer(
  v-model:visible='_visible',
  title='基本资料'
)
  .px-6.py-4.h-full.bg-gray-100
    .p-4.rounded-lg.bg-white.mb-4
      ComOpmMemberBasicInfo(
        :record='record'
        :ownershipRecord='ownershipRecord'
        :store='store'
        :disabled='true',
        :tagStore='tagStore'
      )
    ComOpmCardContainer(title='岗位信息')
      ComOpmMemberDutyList(
        style='grid-template-columns:initial !important;'
        :record='record'
        :store='store',
        :disabled='true'
      )
</template>

<style lang="stylus" scoped></style>
