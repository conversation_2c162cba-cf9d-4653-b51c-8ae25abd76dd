<script lang="ts">
import { defineComponent, toRefs, computed, ref } from 'vue';
import ComOpmUserMemberDrawer from './ComOpmUserMemberDrawer.vue';
import { VObject, VStore } from '@/lib/vails';
import { AuthSessionApi } from '@/engines/login/apis/auth/session.api';
import { OpmUserTagsApi } from '@/engines/opm/opm-core/apis/opm/user/tags.api';
import { OpmTagModel } from '@/engines/opm/opm-core/models/opm/manage/tags';

const ComOpmUserMembersIndex = defineComponent({
  name: 'ComOpmUserMembersIndex',
  components: { ComOpmUserMemberDrawer },
  props: {
    store: { type: Object, required: true },
    departmentId: { type: Number, default: null },
    dutyId: { type: Number, default: null },
    ownershipRecord: { type: Object, default: () => { } },
    template: { type: String, default: 'member#opm' },
  },
  setup(props) {
    const modes = [
      {
        text: '看本级',
        key: 'all_level',
      },
      {
        text: '看全部',
        key: 'self_level',
      },
    ];

    const currentMode = ref(modes[0]);
    const changeMode = (mode: any) => {
      currentMode.value = mode;
    };

    const params = computed(() => {
      if (props.departmentId && props.dutyId) {
        return {
          q: { departments_id_eq: props.departmentId, duties_id_eq: props.dutyId },
        };
      }

      if (currentMode.value.key == 'all_level') {
        return {
          q: {
            departments_ancestor_id_eq: props.departmentId,
          },
        };
      }

      return {
        q: {
          departments_id_eq: props.departmentId,
        },
      };
    });

    const config = computed(() => ({
      store: props.store,
      mode: 'table',
      template: props.template,
      params: params.value,
      table: {
        scroll: { y: 'auto' },
      },
      searcherSimpleOptions: [{ key: 'user_name', label: '姓称', type: 'string' }],
    }));

    const drawerVisible = ref(false);
    const activeRecord = ref<any>({})
    const onShow = (record: VObject) => {
      const isMe = record.user.id === AuthSessionApi.currentUser().id
      if (isMe) return window.open(
        `${process.env.VUE_APP_DOMAIN}/opm/own/ownerships/${props.ownershipRecord.id}/member`,
      );

      activeRecord.value = record
      drawerVisible.value = true;
    };


    const tagStore = new VStore(new OpmUserTagsApi({
      parents: [{
        type: 'ownerships',
        id: props.ownershipRecord.id
      }]
    }), OpmTagModel)
    return {
      ...toRefs(props),
      config,
      modes,
      changeMode,
      currentMode,
      onShow,
      drawerVisible,
      activeRecord,
      tagStore,
    };
  },
});
export default ComOpmUserMembersIndex;
</script>

<template lang="pug">
.com-opm-user-members-index.w-full.h-full
  TaIndexView(:config='config', @onShow='onShow')
    template(#right-actions)
      .cursor-pointer(v-for='mode in modes', @click='changeMode(mode)', v-if='departmentId && !dutyId')
        .flex.items-center(v-if='mode.key != currentMode.key')
          TaIcon.mr-1.w-4.h-4.text-primary(type='outline/org')
          .text-xs.mr-1.text-primary {{ mode.text }}
          TaIcon.w-4.h-4.text-gray-400(type='outline/switch-horizontal')
  ComOpmUserMemberDrawer(
    v-if='drawerVisible'
    width='600'
    v-model:visible='drawerVisible',
    :store='store',
    :ownershipRecord='ownershipRecord',
    :record='activeRecord'
    :tagStore='tagStore'
  )
</template>

<style lang="stylus" scoped>
.com-opm-user-members-index
  .text-primary
    color: $primary-color;
  .bg-primary
    background: $primary-color;
</style>
