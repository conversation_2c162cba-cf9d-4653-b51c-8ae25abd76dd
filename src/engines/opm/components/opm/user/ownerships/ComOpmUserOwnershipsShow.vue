<script lang="ts">
import { defineComponent } from '@vue/runtime-core';
import { VStore } from '@/lib/vails';
import { ref } from 'vue';
import { OpmDepartmentTreeModel } from '@/engines/opm/opm-core/models/opm/departments';
import { OpmMemberModel } from '@/engines/opm/opm-core/models/opm/members';
import { OpmUserDepartmentsApi } from '@/engines/opm/opm-core/apis/opm/user/departments.api';
import { OpmUserDepartmentDutiesApi } from '@/engines/opm/opm-core/apis/opm/user/department_duties.api';
import { OpmUserMembersApi } from '@/engines/opm/opm-core/apis/opm/user/members.api';
import ComOpmDepartmentsTree from '../../departments/ComOpmDepartmentsTree.vue';
import ComOpmUserDepartmentsShow from '../departments/ComOpmUserDepartmentShow.vue';
import ComOpmUserDepartmentAndDutyContainer from './ComOpmUserDepartmentAndDutyContainer.vue';

const ComOpmUserOwnershipsShow = defineComponent({
  name: 'ComOpmUserOwnershipsShow',
  components: {
    ComOpmDepartmentsTree,
    ComOpmUserDepartmentAndDutyContainer,
  },
  props: {
    record: { type: Object, required: true },
  },
  setup(props) {
    const treeStore = new VStore(
      new OpmUserDepartmentsApi({
        parents: [{ type: 'ownerships', id: props.record.id }],
      }),
      OpmDepartmentTreeModel,
    );

    const dutyStore = new VStore(
      new OpmUserDepartmentDutiesApi({
        parents: [{ type: 'ownerships', id: props.record.id }],
      }),
    );
    const userStore = new VStore(
      new OpmUserMembersApi({
        parents: [{ type: 'ownerships', id: props.record.id }],
      }),
      OpmMemberModel,
    );

    const activeSwitchKey = ref<'tree' | 'table'>('tree');

    const selectedDepartmentId = ref<any>(null);
    const path = ref<string[]>([props.record.org?.name]);

    const handleTreeSelect = (node: any, pathArr: string[]) => {
      selectedDepartmentId.value = node.dataType == 'org' ? null : node.key;
      path.value = pathArr;
    };

    return {
      treeStore,
      dutyStore,
      userStore,
      activeSwitchKey,
      selectedDepartmentId,
      path,
      handleTreeSelect,
    };
  },
});

export default ComOpmUserOwnershipsShow;
</script>

<template lang="pug">
.com-opm-user-ownerships-index.w-full.h-full
  .wrapper.w-full.h-full.pt-4.flex.flex-col
    header.flex.items-center.justify-between.mb-4.flex-shrink-0
      .title.text-xl.font-semibold 组织结构
    section.flex-grow.h-0.flex.gap-x-4(v-if='activeSwitchKey === "tree"')
      .left__tree.px-2.py-3.bg-white.rounded-lg.flex-shrink-0(class='basis-70')
        ComOpmDepartmentsTree.h-full(
          :store='treeStore'
          :defaultRoot='record.org',
          :ownershipRecord='record',
          :editable='false',
          @select='handleTreeSelect',
        )
      .right__index.flex-grow
        ComOpmUserDepartmentAndDutyContainer.h-full(
          :dutyStore='dutyStore',
          :userStore='userStore'
          :departmentId='selectedDepartmentId',
          :path='path',
          :ownershipRecord='record',
        )
</template>

<style lang="stylus" scoped>
.com-opm-user-ownerships-index
  height 100%
  width 100%
  background #F0F2FC
  .wrapper header .title
    color #25396F
</style>
