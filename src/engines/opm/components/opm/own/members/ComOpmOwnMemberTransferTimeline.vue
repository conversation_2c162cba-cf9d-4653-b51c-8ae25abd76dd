<script lang="ts">
import { defineComponent, onMounted, toRefs } from 'vue';
import ComOpmMemberTransferTimelineItem from '../../../opm/members/ComOpmMemberTransferTimelineItem.vue';
import { VStore } from '@/lib/vails';
import { OpmHrTransferModel } from '@/engines/opm/opm-core/models/opm/hr_transfers';
import { OpmOwnHrTransfersApi } from '@/engines/opm/opm-core/apis/opm/own/hr_transfers.api';

const ComOpmOwnMemberTransferTimeline = defineComponent({
  name: 'ComOpmOwnMemberTransferTimeline',
  components: { ComOpmMemberTransferTimelineItem },
  props: {
    record: { type: Object, required: true },
    ownershipRecord: { type: Object, required: true },
  },
  setup(props) {
    const transferStore = new VStore(
      new OpmOwnHrTransfersApi({
        parents: [{ type: 'ownerships', id: props.ownershipRecord.id }],
      }),
      OpmHrTransferModel,
    );

    const fetchTransfers = async () => {
      await transferStore.index({ per_page: 99999, q: { s: 'id asc' } });
    };

    onMounted(() => {
      fetchTransfers();
    });

    return {
      ...toRefs(props),
      records: transferStore.records,
    };
  },
});
export default ComOpmOwnMemberTransferTimeline;
</script>

<template lang="pug">
.com-opm-member-transfer-timeline.pt-2
  a-timeline
    ComOpmMemberTransferTimelineItem(
      v-for='record in records',
      :key='record.id',
      :record='record',
    )

</template>

<style lang="stylus" scoped></style>
