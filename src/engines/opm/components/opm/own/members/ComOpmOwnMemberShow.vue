<script lang="ts">
import { defineComponent, toRefs, PropType, computed } from 'vue';
import ComOpmOwnMemberDuties from './ComOpmOwnMemberDuties.vue';
import ComOpmOwnMemberBasic from './ComOpmOwnMemberBasic.vue';
import ComVerticalTabs from '../../../ComVerticalTabs.vue';
import ComOpmOwnMemberTransferTimeline from './ComOpmOwnMemberTransferTimeline.vue';

const ComOpmOwnMemberShow = defineComponent({
  name: 'ComOpmOwnMemberShow',
  components: {
    ComVerticalTabs,
    ComOpmOwnMemberDuties,
    ComOpmOwnMemberBasic,
    ComOpmOwnMemberTransferTimeline,
  },
  props: {
    store: { type: Object, required: true },
    record: { type: Object, required: true },
    eduItemStore: { type: Object, default: () => ({}) },
    winItemStore: { type: Object, default: () => ({}) },
    techItemStore: { type: Object, default: () => ({}) },
    eduRecordStore: { type: Object, default: () => ({}) },
    ownershipRecord: { type: Object, required: true },
    handleTabMethod: { type: [String, Array] as PropType<string | string[]>, default: '' },
    handleTabPayload: { type: [Object, Array], default: null },
    memberTitleStore: { type: Object, required: true },
  },
  setup(props) {
    const baseTabs = [
      {
        label: '基本信息',
        key: 'basic',
        comp: 'ComOpmOwnMemberBasic',
        store: props.store,
        record: props.record,
        ownershipRecord: props.ownershipRecord,
      },
      {
        label: '岗位信息',
        key: 'duty',
        comp: 'ComOpmOwnMemberDuties',
        record: props.record,
        store: props.store,
        ownershipRecord: props.ownershipRecord,
      },

      {
        label: '个人信息',
        key: 'personal',
        comp: 'ComOpmMemberPersonalCard',
        record: props.record,
        ownershipRecord: props.ownershipRecord,
      },
      {
        label: '职称信息',
        key: 'title',
        comp: 'ComOpmMemberJobTitleList',
        store: props.memberTitleStore,
        record: props.record,
      },
      {
        label: '教育信息',
        key: 'education',
        comp: 'ComOpmMemberEduRecords',
        store: props.eduRecordStore,
        record: props.record,
        actionComp: 'ComOpmMemberEduRecordsActions',
        disabled: false,
      },
      {
        label: '培训记录',
        key: 'train',
        comp: 'ComOpmMemberTrainList',
        ownershipRecord: props.ownershipRecord,
        store: props.eduItemStore,
        record: props.record,
        template: 'opm_edu_item',
      },
      {
        label: '获奖记录',
        key: 'win',
        comp: 'ComOpmMemberTrainList',
        ownershipRecord: props.ownershipRecord,
        store: props.winItemStore,
        record: props.record,
        template: 'opm_win_item'
      },
      {
        label: '技能证书',
        key: 'tech',
        comp: 'ComOpmMemberTrainList',
        ownershipRecord: props.ownershipRecord,
        store: props.techItemStore,
        record: props.record,
        template: 'opm_tech_item'
      },
      {
        label: '人事调动',
        key: 'transfer',
        comp: 'ComOpmOwnMemberTransferTimeline',
        record: props.record,
        ownershipRecord: props.ownershipRecord,
      },
    ];

    const handleTabs = (method: string | string[], payload: any) => {
      if (!method) return baseTabs;

      const result: any = [];
      //TODO:待完善 补充push,insert,remove等方法 来代替插槽
      return result;
    };

    const tabs = computed(() => handleTabs(props.handleTabMethod, props.handleTabPayload));

    return {
      ...toRefs(props),
      tabs,
    };
  },
});
export default ComOpmOwnMemberShow;
</script>

<template lang="pug">
.com-opm-own-member-show.grid
  .left__tab.px-4.pt-5
    ComVerticalTabs(:tabs='tabs')
  .overflow-auto.py-5
    .right__content.w-full.grid.gap-4
      template(v-for='(tab,index) in tabs')
        ComOpmCardContainer(:title='index && tab.label' :id='tab.key' style='min-width:700px')
          component(
            v-if='tab.comp'
            :is='tab.comp'
            :record='tab.record',
            :store='tab.store',
            :ownershipRecord='ownershipRecord'
            :disabled='tab.disabled'
            :template='tab.template'
          )
          template(#actions)
            component(
              v-if='tab.actionComp',
              :is='tab.actionComp',
              :record='tab.record',
              :store='tab.store',
            )
</template>

<style lang="stylus" scoped>
.com-opm-own-member-show
  height 100%
  grid-template-columns 180px 1fr
</style>
