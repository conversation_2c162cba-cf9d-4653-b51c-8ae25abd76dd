<script lang="ts">
import { defineComponent, ref, toRefs } from 'vue';
import { VStore } from '@/lib/vails';
import { OpmOwnTagsApi } from '@/engines/opm/opm-core/apis/opm/own/tags.api';

const ComOpmOwnMemberBasic = defineComponent({
  name: 'ComOpmOwnMemberBasic',
  components: {},
  props: {
    store: { type: Object, required: true },
    record: { type: Object, required: true },
    ownershipRecord: { type: Object, required: true },
  },
  setup(props) {
    const visible = ref({
      editDrawer: false,
    });

    const editRecord = ref({});

    const actions = [
      {
        name: '编辑',
        icon: 'pencil',
        color: '#3F83F8',
        action: () => {
          props.store.find(props.record.id).then((res: any) => {
            const data = res.data;
            editRecord.value = props.store.new(data);
            visible.value.editDrawer = true;
          });
        },
      },
    ];

    const tagStore = new VStore(
      new OpmOwnTagsApi({
        parents: [{ type: 'ownerships', id: props.ownershipRecord.id }],
      }),
    );
    tagStore.index({ q: { members_id_eq: props.record.id }, per_page: 100 });

    const afterSave = () => {
      props.record.fetch();
    };

    return {
      ...toRefs(props),
      actions,
      visible,

      editRecord,
      tagRecords: tagStore.records,

      afterSave,
    };
  },
});
export default ComOpmOwnMemberBasic;
</script>

<template lang="pug">
.com-opm-own-member-basic
  .flex.mb-3.items-start
    TaAvatar.mr-4(:size='80' :user='record.user')
    .info.flex-shrink-0
      section.flex.items-center.mb-1
        .name.text-lg.font-medium.text-primary-900.mr-2 {{ record.user.name }}
        .flex.items-center.space-x-3.text-xs(style='padding-left:1em')
          ComRankTag(:rank='record.mainPosition?.duty_rank')
          //- ComColorfulTag(color='#1C64F2' text='在职')
          //- ComColorfulTag(color='#FF7300' text='试用期')
          //- .text-gray-400 有效期至：
          //-   span.text-gray-900 2021-12-31
      section.flex.items-center.text-sm.space-x-4.mb-1
        ComOpmMemberUserGender(:record='record')
        .text-gray-200 |

        ComOpmPhonMask(:value='record.user.mobile')
        .text-gray-200(v-if='record.user.email') |
        .email__info.flex.items-center(v-if='record.user.email')
          TaIcon.w-4.h-4.text-gray-400.mr-1(type='solid/mail')
          .info__value__color {{ record.user.email }}
      section.flex.items-center.text-sm.space-x-2(v-if='record.mainPosition')
        ComColorfulTag.text-xs.flex.items-center(
          color='#3F83F8'
          class='!px-1 !py-[2px]'
        )
          TaIcon.w-4.h-4.text-primary-500.mr-1(type='solid/briefcase')
          .text-xs 主岗
        .info__value__color {{ `${record.mainPosition?.department_name || '暂无部门'} - ${record.mainPosition.duty_name || '暂无岗位'}` }}

    //- 操作
    .actions.flex.space-x-2.ml-auto.flex-shrink-0
      template(v-for='(action, index) in actions')
        component(:is='action.wrapper || "div"')
          .rounded-md.text-white.px-4.py-2.flex.items-center.cursor-pointer(
            :style='`background-color: ${action.color};`',
            @click='action.action()'
          )
            TaIcon.w-4.h-4.mr-1(:type='`outline/${action.icon}`')
            span {{ action.name }}

  .flex.items-center.flex-wrap.gap-2.text-xs(v-if='tagRecords.length > 0')
    .text-gray-400 标签：
    ComColorfulTag(v-for='tag in tagRecords', :text='tag.name', :color='tag.color')

  TaTemplateFormWithActionsDrawer(
    v-model:visible='visible.editDrawer',
    :record='editRecord',
    :template='record.member_identity_form'
    title='编辑个人信息',
    @afterSave='afterSave'
  )
</template>

<style lang="stylus" scoped>
.com-opm-own-member-basic:deep(.ant-avatar-circle)
  border-radius 8px
.info__value__color
  color #3F3F46
</style>
