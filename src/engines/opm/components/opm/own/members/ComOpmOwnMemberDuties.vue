<script lang="ts">
import { defineComponent, toRefs } from 'vue';
import ComOpmUserMemberDutyCard from '../../user/members/ComOpmUserMemberDutyCard.vue';

const ComOpmOwnMemberDuties = defineComponent({
  name: 'ComOpmOwnMemberDuties',
  components: { ComOpmUserMemberDutyCard },
  props: {
    record: { type: Object, required: true },
    store: { type: Object, required: true },
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
export default ComOpmOwnMemberDuties;
</script>

<template lang="pug">
.com-opm-own-member-duties.grid.gap-4
  ComOpmUserMemberDutyCard.cursor-pointer(
    v-for='item in record.departmentsDuties',
    :item='item'
  )
</template>

<style lang="stylus" scoped>
.com-opm-own-member-duties
  grid-template-columns repeat(auto-fill, minmax(230px, 1fr))
</style>
