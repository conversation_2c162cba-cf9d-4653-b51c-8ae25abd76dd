<script lang="ts">
import { OpmUserEmployRequestsApi } from '@/engines/opm/opm-core/apis/opm/user/employ_requests.api';
import { VStore } from '@/lib/vails';
import { computed, defineComponent, ref, toRefs } from 'vue';
import ComBpmInstanceDetailDialog from '@/engines/bpm/components/ComBpmInstanceDetailDialog.vue';

export default defineComponent({
  name: 'ComOpmEmployInviteShow',
  components: {
    ComBpmInstanceDetailDialog,
  },
  props: {
    record: { type: Object, required: true },
  },
  setup(props) {
    const template = computed(() => {
      const memberIdentityForm = props.record.member_identity?.form;
      if (memberIdentityForm && Object.keys(memberIdentityForm).length > 0) {
        if (memberIdentityForm.setting?.confs?.[0].conf) {
          return memberIdentityForm.setting.confs[0].conf.form;
        }
        return memberIdentityForm;
      }
      return {};
    });

    const store = new VStore(
      new OpmUserEmployRequestsApi({
        parents: [{ type: 'employ_invites', id: props.record.id }],
      }),
    );

    const editRecord = ref<any>(props.record.latest_request || {});

    const visibleInstance = ref(false);
    const activeInstanceId = ref();

    const formRef = ref<any>();

    const onSubmit = () => {
      if (!props.record.userCanRequest) return;

      formRef.value.validate().then(() => {
        store.create(editRecord.value as any).then((res: any) => {
          if (res.flowable_instance_infos?.[0]?.id) {
            visibleInstance.value = true;
            activeInstanceId.value = res.flowable_instance_infos?.[0]?.id;
          }
          props.record.fetch();
        });
      });
    };

    return {
      ...toRefs(props),
      template,
      editRecord,
      onSubmit,
      visibleInstance,
      activeInstanceId,
      formRef,
    };
  },
});
</script>

<template lang="pug">
.com-opm-employ-invite-show
  ComBpmInstanceDetailDialog(v-model:visible='visibleInstance', :instanceId='activeInstanceId')
  .w-194.mx-auto.py-10
    .bg-white.rounded-lg.overflow-hidden
      img.w-full(src='https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/opm/invite-form.png')
      .pt-11.pb-9.text-center.text-lg.font-medim.text-gray-900 {{ record.org?.name }}入职审批

    .form(v-if='record.userCanRequest')
      TaTemplateForm(
        ref="formRef"
        :template='template',
        :modelValue="editRecord"
      )
      .bg-white.rounded-lg.py-3.flex.justify-center
        .bg-blue-600.py-2.px-6.rounded.text-white.text-base.font-medim.cursor-pointer(@click='onSubmit()') 提交
    TaTemplateFormViewer(
      :template='template',
      :modelValue='editRecord'
      v-else
    )
</template>

<style lang="stylus" scoped>
.com-opm-employ-invite-show
  >>>.is-parent-container
    margin-left: 0;
    margin-right: 0;
</style>
