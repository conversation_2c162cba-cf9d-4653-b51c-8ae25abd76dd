<script lang="ts">
import { defineComponent, toRefs, ref, onMounted } from 'vue';
import { VStore } from '@/lib/vails';
import { OpmManageEmployInvitesApi } from '@/engines/opm/opm-core/apis/opm/manage/employ_invites.api';
import { OpmEmployInviteModel } from '@/engines/opm/opm-core/models/opm/employ_invites';
import QrCode from 'qrcode';
import Clipboard from 'clipboard';
import { message } from 'ant-design-vue';
import { AuthSessionApi } from '@/engines/login/apis/auth/session.api';

export default defineComponent({
  name: 'ComOpmCreateEmployInvite',
  props: {
    ownershipRecord: { type: Object, required: true },
    departmentId: { type: Number, default: null },
    dutyId: { type: Number, default: null },
  },
  setup(props) {
    const user = AuthSessionApi.currentUser();

    const inviteVisibleDrawer = ref(false);
    const inviteStore = new VStore(
      new OpmManageEmployInvitesApi({
        parents: [{ type: 'ownerships', id: props.ownershipRecord.id }],
      }),
      OpmEmployInviteModel,
    );
    const inviteRecord = ref<any>({});
    const onInvite = () => {
      inviteVisibleDrawer.value = true;
      inviteRecord.value = inviteStore.new({
        org_name: props.ownershipRecord.org?.name,
        department_id: props.departmentId,
        duty_id: props.dutyId,
        ownership_id: props.ownershipRecord.id,
      });
    };

    const qrCodeURL = ref('');
    const qrModalVisible = ref();
    const qrLink = ref('');
    const qrMobileLink = ref('');
    const afterInviteCreate = async (record: any) => {
      qrLink.value = `${process.env.VUE_APP_DOMAIN}/opm/user/employ_invites/${record.id}`;
      qrMobileLink.value = `${process.env.VUE_APP_MOBILE_DOMAIN}/#/engines/opm/pages/opm/user/employ_invites/form/index?employInviteId=${record.id}`
      qrCodeURL.value = await QrCode.toDataURL(qrMobileLink.value);
      inviteRecord.value = record;
      qrModalVisible.value = true;
    };



    const downloadImgByBase64 = (url: string) => {
      const img = new Image();


      img.onload = () => {
        const bg = new Image();
        bg.src = 'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/opm/tunghai/%E5%88%86%E4%BA%AB%E4%BA%8C%E7%BB%B4%E7%A0%81.png';
        bg.setAttribute('crossOrigin', 'Anonymous');


        bg.onload = () => {
          const canvas = document.createElement('canvas');
          const dpr = window.devicePixelRatio;
          const clientWidth = 375;
          const clientHeight = 595;
          canvas.width = Math.round(clientWidth * dpr);
          canvas.height = Math.round(clientHeight * dpr);

          const ctx = canvas.getContext('2d')!;
          ctx.scale(dpr, dpr);
          const _textcenter = (text: string, y: number) => {
            ctx.fillText(text, (clientWidth - ctx.measureText(text).width) / 2, y);
          }
          const _drawRoundedRect = (
            ctx: CanvasRenderingContext2D,
            x: number,
            y: number,
            width: number,
            height: number,
            radius: number,
            type: 'fill' | 'stroke' = 'fill'
          ) => {
            ctx.beginPath();
            ctx.moveTo(x + radius, y);
            ctx.lineTo(x + width - radius, y);
            ctx.arcTo(x + width, y, x + width, y + radius, radius);
            ctx.lineTo(x + width, y + height - radius);
            ctx.arcTo(x + width, y + height, x + width - radius, y + height, radius);
            ctx.lineTo(x + radius, y + height);
            ctx.arcTo(x, y + height, x, y + height - radius, radius);
            ctx.lineTo(x, y + radius);
            ctx.arcTo(x, y, x + radius, y, radius);
            ctx.closePath();
            ctx[type]();
          };


          ctx.drawImage(bg, 0, 0, clientWidth, clientHeight);
          // Add avatar image (placeholder circle for now)

          ctx.beginPath();
          ctx.fillStyle = '#1890ff';
          _drawRoundedRect(ctx, 36, 48, 36, 36, 4);
          ctx.closePath();
          ctx.fill();

          ctx.font = '500 14px PingFang SC';
          ctx.fillStyle = '#ffffff';
          const nameSlice = user.name.slice(0, 2)
          ctx.fillText(nameSlice, (36 - ctx.measureText(nameSlice).width) / 2 + 36, 72);

          // Add name text
          ctx.font = '500 16px PingFang SC';
          ctx.fillStyle = '#233876';
          ctx.fillText(user.name || '', 88, 72)

          ctx.font = '500 16px PingFang SC';
          ctx.fillStyle = '#233876';
          const companyText = inviteRecord.value.org_name;
          _textcenter(companyText, 176);

          ctx.font = '600 14px PingFang SC';
          ctx.fillStyle = '#233876';

          if (inviteRecord.value.department?.name && inviteRecord.value.duty?.name) {
            _textcenter(`${inviteRecord.value?.department?.name} - ${inviteRecord.value?.duty?.name}`, 208);
          } else if (inviteRecord.value.department?.name) {
            _textcenter(inviteRecord.value.department?.name, 208);
          } else if (inviteRecord.value.duty?.name) {
            _textcenter(inviteRecord.value.duty?.name, 208);
          }


          // Draw QR code
          const qrSize = 212;
          const qrX = (clientWidth - qrSize) / 2;
          const qrY = 261;
          ctx.drawImage(img, qrX, qrY, qrSize, qrSize);

          const base64 = canvas.toDataURL('image/png');
          const a = document.createElement('a');
          a.href = base64;
          a.download = '邀请二维码.png';
          a.click();
          // Convert to base64 and download

        }

      };
      img.src = url;

      img.setAttribute('crossOrigin', 'Anonymous');
    };

    const copy = () => {
      const clipboard = new Clipboard('.copy-link');
      clipboard.on('success', () => {
        message.success('复制成功');
        clipboard.destroy();
      });
      clipboard.on('error', () => {
        console.log('该浏览器不支持自动复制');
        clipboard.destroy();
      });
    };
    return {
      ...toRefs(props),

      onInvite,
      inviteVisibleDrawer,
      inviteStore,
      inviteRecord,

      qrCodeURL,
      afterInviteCreate,
      qrModalVisible,
      qrLink,
      downloadImgByBase64,
      copy,
    };
  },
});
</script>

<template lang="pug">
.com-opm-create-employ-invite
  slot(name='button', :onInvite='onInvite')
    .h-9.px-4.text-white.font-medium.rounded.cursor-pointer.bg-teal-400(@click='onInvite()')
      .flex.items-center.h-full
        TaIcon.w-4.h-4.mr-1(type='outline/user-add')
        .text 邀请

  TaTemplateFormWithActionsModal(
    v-model:visible='inviteVisibleDrawer',
    template='opm_employ_invite',
    :record='inviteRecord',
    title='二维码邀请',
    width='640px',
    :footer='false',
    @afterSave='afterInviteCreate'
  )

  a-modal(
    v-model:visible='qrModalVisible',
    width='640px',
    title='二维码邀请'
    :maskClosable='false',
  )
    .wrapper.text-center
      .text-blue-900.text-base.font-medium.mb-3 {{ inviteRecord.org?.name }}
      .text-blue-900.text-sm.font-medium
        span {{ inviteRecord.department?.name }}
        span.px-2(v-if="inviteRecord.department?.name && inviteRecord.duty?.name") -
        span {{ inviteRecord.duty?.name }}
      img.w-9.mx-auto.mt-2.mb-1(src='https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/opm/Union.png')
      .invite-qr-img.w-55.h-55.mx-auto.rounded-lg.p-1
        img.w-full.h-full(:src='qrCodeURL')
      .text-gray-900.font-medium.text-sm.mt-8.mb-4 分享链接邀请

      .copy-link.bg-gray-100.rounded-lg.w-96.mx-auto.flex.items-center.justify-between.py-1.pl-4.pr-1(:data-clipboard-text='qrLink', @click='copy()')
        .text-gray-900.text-sm.w-64.truncate {{ qrLink }}
        .px-5.py-2.bg-primary.text-white.text-sm.font-medium.cursor-pointer.rounded 复制链接
    template(#footer)
      .flex.items-center.justify-center.py-2
        .copy-link.px-5.py-2.bg-white.border.text-sm.font-medium.cursor-pointer.rounded.mr-2(
          :data-clipboard-text='qrLink'
          class='text-[#1F2A37] border-[#E5E7EB]',
          @click='copy()'
        ) 复制链接
        .px-5.py-2.bg-primary.text-white.text-sm.font-medium.cursor-pointer.rounded(@click='downloadImgByBase64(qrCodeURL)') 下载二维码
</template>

<style lang="stylus" scoped>
.invite-qr-img
  background: linear-gradient(180deg, #E0F5FF 0%, #E3EEFF 100%);
  overflow: hidden;
</style>
