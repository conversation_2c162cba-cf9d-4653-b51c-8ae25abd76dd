<script lang='ts'>
import { ref, defineComponent, toRefs, computed } from 'vue';
import dayjs from 'dayjs';
const ComOpmItemInfo = defineComponent({
  name: 'ComOpmItemInfo',
  components: {},
  props: {
    record: {
      type: Object,
      default: () => ({})
    }
  },
  setup(props) {
    const data = computed(() => ({
      培训分类: props.record?.mode,
      参与者: props.record?.member?.name,
      培训来源: props.record?.origin,
      培训时间: `${dayjs(props.record.effective_at).format('YYYY-MM-DD')}-${dayjs(props.record.invalid_at).format('YYYY-MM-DD')}`,
      培训时长: props.record?.payload?.days,
      是否有培训证书: props.record?.payload?.has_attachment ? '是' : '否',
      补充说明: props.record?.payload?.extra,
      相关附件: props.record?.payload?.attachments?.files
    }))
    return {
      ...toRefs(props),
      data
    };
  },
});
export default ComOpmItemInfo;
</script>

<template lang="pug">
.com-opm-item-info.p-4.rounded-lg.bg-white.text-gray-900
  header.text-base.font-medium.mb-4 培训信息

  .space-y-3
    template(v-for='(value,key) in data')
      .text-sm.text-gray-500(v-if='key !== "相关附件"') {{ key }}：
        span.pl-1.text-sm {{ value }}
      .wrapper(v-else)
        .text-sm.text-gray-500 {{key}}
        TaAttachments(:attachments='value')
</template>

<style lang="stylus" scoped></style>
