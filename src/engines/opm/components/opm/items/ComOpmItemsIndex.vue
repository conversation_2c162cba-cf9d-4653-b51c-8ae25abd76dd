<script lang="ts">
import { defineComponent, computed, ref, Ref, toRefs, reactive } from 'vue';
import { TaIndexViewTabInterface } from '@/components/global/ta-component/TaIndexView/types';
import { VObject } from '@/lib/vails/model';
import ComOpmItemsShow from './ComOpmItemsShow.vue'
import ComOpmItemInfo from './ComOpmItemInfo.vue';
import ComOpmItemCard from '../../workplane/ComOpmItemCard.vue';
import ComOpmWinItemInfo from '../win_items/ComOpmWinItemInfo.vue';
import ComOpmWinItemCard from '../win_items/ComOpmWinItemCard.vue';
import ComOpmTechItemCard from '../tech_items/ComOpmTechItemCard.vue';
import ComOpmTechItemInfo from '../tech_items/ComOpmTechItemInfo.vue';
import ComBpmInstanceDetail from '@/engines/bpm/components/ComBpmInstanceDetail.vue';


const ComOpmItemsIndex = defineComponent({
  name: 'ComOpmItemsIndex',
  components: {
    ComOpmItemsShow,
    ComOpmItemCard,
    ComOpmItemInfo,
    ComOpmWinItemCard,
    ComOpmWinItemInfo,
    ComOpmTechItemCard,
    ComOpmTechItemInfo,
    ComBpmInstanceDetail
  },
  props: {
    store: { type: Object, required: true },
    disabled: { type: Boolean, default: false },
    title: { type: String, default: '培训记录' },
    template: { type: String, default: 'opm_edu_item' },
  },
  setup(props, { emit }) {
    const activeMode = ref<'table' | 'list'>('table')
    const toggleMode = () => {
      activeMode.value = activeMode.value === 'table' ? 'list' : 'table'
    }

    const modeMap = {
      table: '列表视图',
      list: '卡片视图',
    }
    const config = computed(() => ({
      recordName: props.title,
      store: props.store,
      template: props.template,
      detail: {
        mode: 'drawer',
        width: '900px',
      },
      mode: activeMode.value,
      actions: [
        { key: 'update', enabled: !props.disabled },
        { key: 'delete', enabled: !props.disabled },
      ],
      table: {
        scroll: { y: 'auto' },
        // columns: [{ dataIndex: 'id', title: 'ID' }],
      },
      list: {
        scroll: { y: 'auto' },
        gap: 8
      },
      searcherSimpleOptions: [{ key: 'name', label: '名称', type: 'string' }],
    }));


    const taindexview = ref<any>(null)
    const onCreate = () => {
      taindexview.value?.slotActions?.onCreate()
    }
    const onExport = () => {
      taindexview.value?.slotActions?.export()
    }

    const fmtStateName = (state: string) => {
      switch (state) {
        case 'completed':
          return '已完成'
        case 'terminated':
          return '已终止'
        default:
          return '进行中'
      }
    }

    const onClose = () => {
      taindexview.value?.silenceRefresh()
    }

    return {
      ...toRefs(props),
      config,
      taindexview,
      onCreate,
      modeMap,
      activeMode,
      toggleMode,
      onExport,
      fmtStateName,
      onClose,
    };
  },
});

export default ComOpmItemsIndex;
</script>

<template lang="pug">
.com-opm-admin-items-index.flex.flex-col
  TaIndexView.flex-grow.h-0.ta-index-view(
    ref='taindexview'
    :config='config'
    :tabs='tabs'
    @onIndex='onIndex'
  )
    template(#header)
      .export.h-full.flex.pb-4
        .btn.cursor-pointer.bg-orange-400.mr-2(@click='onExport')
          TaIcon.w-4.h-4(type='outline/upload')
          span.pl-1 导出
        .btn.bg-primary-500.cursor-pointer(@click='onCreate' v-if='!disabled')
          TaIcon.w-4.h-4(type='outline/plus')
          span.pl-1 新建
    template(#right-actions)
      .actions__wrapper.flex.space-x-2.w-full.mb-4.h-full.items-center

        .btn.shadow.bg-primary-700.cursor-pointer(@click='toggleMode') {{ modeMap[activeMode] }}
    template(#card='{ record }')
      component(:is='record.cardCompName' :record='record')
    template(#bodyCell='{record,column}')
      template(v-if='column.dataIndex[0] === "create_instance_state"')
        | {{ fmtStateName(record.create_instance_state) }}
    template(#detail='{ record, onClose }')
      ComBpmInstanceDetail(
        v-if='record.create_instance_id && (record.create_instance_state !== "completed")',
        :instanceId='record.create_instance_id',
        @close='onClose',
      )
      .com-opm-item-detail__wrapper.px-6.py-4.h-full.bg-gray-100(v-else)
        component.mb-4(:is='record.cardCompName' :record='record')
        component(:is='record.detailCompName' :record='record')
    //- template(#table)
    //-    a-table-column(title='名称' dataIndex='record')
    //- template(#detail='{ record, onClose }')
    //-   ComOpmItemsShow(
    //-     v-if='record.id'
    //-     :store='store'
    //-     :extendRoute='`/opm/admin/items/${record.id}`'
    //-     :editable='editable'
    //-     @afterDelete='onClose'
    //-     @afterExtend='onClose'
    //-   )

</template>

<style lang="stylus" scoped>
.com-opm-admin-items-index
  height 100%
  width 100%
  .btn
    @apply px-4 py-2 rounded text-sm text-white font-medium flex items-center;

.com-opm-admin-items-index .ta-index-view :deep(.search)
  padding-bottom 16px

</style>
