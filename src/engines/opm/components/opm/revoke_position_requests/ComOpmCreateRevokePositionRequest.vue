<script lang="ts">
import { defineComponent, computed, ref } from 'vue';
import ComBpmInstanceDetailDialog from '@/engines/bpm/components/ComBpmInstanceDetailDialog.vue';
import ComOpmMemberAvatarCard from '../members/ComOpmMemberAvatarCard.vue';
import { VStore } from '@/lib/vails';
import { OpmManageRevokePositionRequestsApi } from '@/engines/opm/opm-core/apis/opm/manage/revoke_position_requests.api';
import { OpmRevokePositionRequestModel } from '@/engines/opm/opm-core/models/opm/revoke_position_requests';
import { message } from 'ant-design-vue';

export default defineComponent({
  name: 'ComOpmCreateRevokePositionRequest',
  components: {
    ComBpmInstanceDetailDialog,
    ComOpmMemberAvatarCard,
  },
  props: {
    visible: { type: Boolean, default: false },
    memberRecord: { type: Object, required: true },
    ownershipRecord: { type: Object, required: true },
  },
  setup(props, { emit }) {
    const localVisible = computed({
      get: () => props.visible,
      set: val => emit('update:visible', val),
    });

    const selectedRecord = ref<any>({});
    const onSelect = (record: any) => {
      selectedRecord.value = record;
    };

    const requestStore = new VStore(
      new OpmManageRevokePositionRequestsApi({
        parents: [{ type: 'ownerships', id: props.ownershipRecord.id }],
      }),
      OpmRevokePositionRequestModel,
    );

    const visibleInstance = ref(false);
    const activeInstanceId = ref();

    const onSubmit = () => {
      if (selectedRecord.value?.department_id || selectedRecord.value?.duty_id) {
        requestStore
          .create({
            payload: selectedRecord.value,
            member_id: props.memberRecord.id,
          } as any)
          .then((res: any) => {
            localVisible.value = false;
            if (res.flowable_instance_infos?.[0]?.id) {
              visibleInstance.value = true;
              activeInstanceId.value = res.flowable_instance_infos?.[0]?.id;
            }
            emit('refresh');
          });
      } else {
        message.warning('请选择需要移除的岗位');
      }
    };

    const onDialogClose = () => {
      emit('refresh');
    };

    return {
      localVisible,
      onSubmit,
      onSelect,
      selectedRecord,
      visibleInstance,
      activeInstanceId,
      onDialogClose,
    };
  },
});
</script>

<template lang="pug">
.com-opm-create-revoke-position-request
  ComBpmInstanceDetailDialog(
    v-model:visible='visibleInstance',
    :instanceId='activeInstanceId',
    @close='onDialogClose'
  )

  a-modal(
    v-model:visible='localVisible'
    title='移除岗位',
     width='640px',
  )
    .revoke-poistion-form-container
      ComOpmMemberAvatarCard(:record='memberRecord')
      .grid.grid-cols-2.gap-2.mt-4
        .bg-gray-50.rounded.p-3.card.border.border-gray-50.cursor-pointer.relative(
          :class='{ "selected": selectedRecord.department_id == dd.department_id && selectedRecord.duty_id == dd.duty_id }',
          @click='onSelect(dd)'
          v-for='dd in memberRecord.departmentsDuties'
        )
          .checked-icon.rounded-tl.rounded-br.overflow-hidden.absolute.bg-primary(
            v-show='selectedRecord.department_id == dd.department_id && selectedRecord.duty_id == dd.duty_id'
          )
            TaIcon.w-2.h-2.text-white(type='outline/check')
          .pl-4.mb-2
            ComRankTag(:type='dd.duty_rank')
          .flex.items-center.mb-1
            TaIcon.w-4.h-4.mr-1.text-gray-400(type='solid/briefcase')
            .text-sm(class='text-[#3F3F46]') {{  dd.department_name || "暂无部门" }}
          .flex.items-center
            TaIcon.w-4.h-4.mr-1.text-gray-400(type='solid/user')
            .text-sm(class='text-[#3F3F46]') {{  dd.duty_name || "暂无岗位" }}
    template(#footer)
      .flex.items-center.justify-end
        .px-5.py-2.bg-white.border.text-sm.font-medium.cursor-pointer.rounded.mr-2(
          class='text-[#1F2A37] border-[#E5E7EB]',
          @click='() => localVisible = false'
        ) 取消
        .px-5.py-2.text-white.text-sm.font-medium.cursor-pointer.rounded(
          class='bg-[#C81E1E]'
          @click='onSubmit()'
        ) 移除
</template>

<style lang="stylus" scoped>
.revoke-poistion-form-container
  .card
    &.selected, &:hover
      border: 1px solid $primary-color;
      background: white;
    .checked-icon
      bottom: -1px;
      right: -1px;
  .bg-primary
    background: $primary-color;
</style>
