<script lang="ts">
import { TaIndexViewTabInterface } from '@/components/global/ta-component/TaIndexView/types';
import ComBpmInstanceDetailDialog from '@/engines/bpm/components/ComBpmInstanceDetailDialog.vue';
import { VObject } from '@/lib/vails/model';
import { computed, defineComponent, ref, toRefs } from 'vue';
import ComOpmTransactionsShow from './ComOpmTransactionsShow.vue';

const ComOpmTransactionsIndex = defineComponent({
  name: 'ComOpmTransactionsIndex',
  components: {
    ComOpmTransactionsShow,
    ComBpmInstanceDetailDialog,
  },
  props: {
    store: { type: Object, required: true },
  },
  setup(props) {
    const visible = ref<boolean>(false);
    const instance_id = ref<number>(0);
    const config = computed(() => ({
      recordName: '加班请假记录',
      store: props.store,
      // pagination: {
      //   perPage: 15,
      //   showPageSizeChanger: false,
      //   hideOnSinglePage: false,
      //   showSizeChanger: false,
      // }
      template: 'opm_transaction',
      // detail: {
      //   mode: 'auto',
      //   mode: 'drawer',
      //   width: '1100px',
      // },
      mode: 'table',
      // editApi:
      // showCount: true,
      actions: [
        //  { key: 'create', enabled: true },
        //  { key: 'update', enabled: true },
        //  { key: 'delete', enabled: true },
        //  { key: 'import', enabled: true },
        //  { key: 'export', enabled: true },
      ],
      table: {
        scroll: { y: 'auto' },
        // columns: [{ dataIndex: 'id', title: 'ID' }],
      },
      list: {
        scroll: { y: 'auto' },
      },
      // selection: {
      //   showByDefault: false;
      //   showSwitch: false;
      // };
      searcherSimpleOptions: [{ key: 'reason', label: '原因', type: 'string' }],
    }));

    const statistics = ref({
      key1: 0,
      key2: 0,
    });

    const tabs = computed<TaIndexViewTabInterface[]>(() => [
      {
        key: 'key1',
        label: '标签1',
        num: statistics.value.key1,
        query: {},
      },
      {
        key: 'key2',
        label: '标签2',
        num: statistics.value.key2,
        query: {},
      },
    ]);

    const onIndex = (data: VObject) => {
      statistics.value = data.statistics;
    };

    const onShow = (record: VObject) => {
      console.log(record);
      instance_id.value = record.details?.flowable_id;
      if (instance_id.value) {
        visible.value = true;
      }
    };

    const onDialogClose = () => {
      // 可以在这里添加刷新数据的逻辑
      visible.value = false;
    };

    return {
      ...toRefs(props),
      config,
      tabs,
      onIndex,
      onShow,
      visible,
      onDialogClose,
      instance_id,
    };
  },
});

export default ComOpmTransactionsIndex;
</script>

<template lang="pug">
.com-opm-user-transactions-index
  TaIndexView(:config='config' , @onIndex='onIndex',@onShow='onShow')
    //- template(#card='{ record }')
    //- template(#table)
    //-    a-table-column(title='名称' dataIndex='record')
    //- template(#detail='{ record, onClose }')
    //-   ComOpmTransactionsShow(
    //-     v-if='record.id'
    //-     :store='store'
    //-     :extendRoute='`/opm/user/transactions/${record.id}`'
    //-     :editable='editable'
    //-     @afterDelete='onClose'
    //-     @afterExtend='onClose'
    //-   )

  ComBpmInstanceDetailDialog(
    v-if='visible',
    v-model:visible='visible',
    :instanceId='instance_id',
    @close='onDialogClose'
  )
</template>

<style lang="stylus" scoped>
.com-opm-user-transactions-index
  height 100%
  width 100%
</style>
