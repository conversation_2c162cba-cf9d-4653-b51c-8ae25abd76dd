<script lang="ts">
import { OpmManageMembersApi } from '@/engines/opm/opm-core/apis/opm/manage/members.api';
import { OpmMemberModel } from '@/engines/opm/opm-core/models/opm/members';
import { VStore } from '@/lib/vails';
import { defineComponent, ref, toRefs } from 'vue';

export default defineComponent({
  name: 'ComOpmHrRequestMemberSelect',
  props: {
    ownershipRecord: { type: Object, required: true },
  },
  setup(props, { emit }) {
    const taApiRef = ref<any>();
    const selectedMemberId = ref();
    const openMemberSelection = () => {
      taApiRef.value?.open();
    };

    const memberRecord = ref({});
    const memberStore = new VStore(
      new OpmManageMembersApi({
        parents: [{ type: 'ownerships', id: props.ownershipRecord.id }],
      }),
      OpmMemberModel,
    );

    const onOk = async () => {
      if (selectedMemberId.value) {
        await memberStore.find(selectedMemberId.value);
        memberRecord.value = memberStore.record.value;
      } else {
        memberRecord.value = {};
      }
      emit('onOk', memberRecord.value);
    };

    const tableItems = [
      {
        name: '姓名',
        type: 'string',
        search: true,
        data_index: 'user.name',
      },
      {
        name: '账号',
        type: 'string',
        search: true,
        data_index: 'user.account',
      },
    ];

    return {
      ...toRefs(props),
      taApiRef,
      selectedMemberId,
      openMemberSelection,
      onOk,
      memberStore,
      memberRecord,
      tableItems,
    };
  },
});
</script>

<template lang="pug">
.com-opm-hr-request-member-select
  TaApiNoDisplayField(
    ref='taApiRef',
    v-model:value='selectedMemberId'
    :path='memberStore.api.indexPath',
    :attrs='["name"]',
    :tableItems='tableItems',
    :multiple='false',
    @ok='onOk()',
  )
  .flex.items-cener
    .flex.items-center.py-2.px-4.rounded.bg-primary-500.text-white.cursor-pointer(
      @click='openMemberSelection()',
    )
      TaIcon.w-4.h-4(type='outline/plus')
      span.pl-1 添加
</template>
