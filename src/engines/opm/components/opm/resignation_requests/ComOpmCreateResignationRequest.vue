<script lang="ts">
import { defineComponent, computed, ref, toRefs } from 'vue';
import ComBpmInstanceDetailDialog from '@/engines/bpm/components/ComBpmInstanceDetailDialog.vue';
import { VStore } from '@/lib/vails';
import { OpmManageResignationRequestsApi } from '@/engines/opm/opm-core/apis/opm/manage/resignation_requests.api';
import { OpmResignationRequestModel } from '@/engines/opm/opm-core/models/opm/resignation_requests';

export default defineComponent({
  name: 'ComOpmCreateResignationRequest',
  components: {
    ComBpmInstanceDetailDialog,
  },
  props: {
    memberRecord: { type: Object, required: true },
    ownershipRecord: { type: Object, required: true },
    visible: { type: Boolean, default: false },
  },
  setup(props, { emit }) {
    const localVisible = computed({
      get: () => props.visible,
      set: val => emit('update:visible', val),
    });

    const editRecord = ref<any>({
      member: props.memberRecord,
    });

    const requestStore = new VStore(
      new OpmManageResignationRequestsApi({
        parents: [{ type: 'ownerships', id: props.ownershipRecord.id }],
      }),
      OpmResignationRequestModel,
    );

    const visibleInstance = ref(false);
    const activeInstanceId = ref();

    const onSubmit = () => {
      const submitValue = {
        payload: editRecord.value.payload,
        member_id: props.memberRecord.id,
      };

      requestStore.create(submitValue as any).then((res: any) => {
        localVisible.value = false;
        if (res.flowable_instance_infos?.[0]?.id) {
          visibleInstance.value = true;
          activeInstanceId.value = res.flowable_instance_infos?.[0]?.id;
        }
        emit('refresh');
      });
    };

    const onDialogClose = () => {
      emit('refresh');
    };

    return {
      ...toRefs(props),
      localVisible,
      editRecord,
      onSubmit,
      visibleInstance,
      activeInstanceId,
      onDialogClose,
    };
  },
});
</script>

<template lang="pug">
.com-opm-create-resignation-request
  ComBpmInstanceDetailDialog(v-model:visible='visibleInstance', :instanceId='activeInstanceId', @close='onDialogClose')

  a-modal(
    v-model:visible='localVisible',
    title='离职',
    width='640px'
  )
    .resignation-form-container
      TaTemplateForm(
        ref='form',
        :modelValue='editRecord',
        template='opm_resignation_request'
      )
    template(#footer)
      .flex.items-center.justify-end
        .px-5.py-2.bg-white.border.text-sm.font-medium.cursor-pointer.rounded.mr-2(
          class='text-[#1F2A37] border-[#E5E7EB]',
          @click='() => localVisible = false'
        ) 取消
        .px-5.py-2.bg-primary.text-white.text-sm.font-medium.cursor-pointer.rounded(@click='onSubmit()') 发起
</template>

<style lang="stylus" scoped>
.resignation-form-container
  >>>.is-parent-container
    border-radius: 0;
    margin: 0;
</style>
