<script lang='ts'>
import { defineComponent, toRefs } from 'vue';
import dayjs from 'dayjs';
const ComOpmTeachingItemCard = defineComponent({
  name: 'ComOpmTechItemCard',
  components: {},
  props: {
    record: { type: Object, default: () => ({}) },
  },
  setup(props) {
    return {
      ...toRefs(props),
      dayjs,
    };
  },
});
export default ComOpmTeachingItemCard;
</script>

<template lang="pug">
.com-opm-teaching-item-card.p-4.rounded-lg.bg-white.border-1.border-gray-200
  .flex.items-center
    .w-12.h-12.rounded-lg.grid.bg-primary-50.mr-4
      TaIcon.w-8.h-8.place-self-center.text-primary-600(type='solid/academic-cap')
    .info.flex-grow.w-0
      .flex.items-center.justify-between.w-full
        .text-gray-500.text-sm.font-medium.mb-1 高教证书
        .text-xs {{`${dayjs(record.effective_at).format('YYYY.MM.DD')}`}}
      .text-base.text-gray-900.font-medium {{ record.name }}
</template>

<style lang="stylus" scoped></style>
