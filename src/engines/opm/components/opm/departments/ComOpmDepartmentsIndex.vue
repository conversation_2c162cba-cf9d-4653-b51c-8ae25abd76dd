<script lang="ts">
import { defineComponent, computed, toRefs, watch, ref } from 'vue';
import { VObject } from '@/lib/vails/model';
import { VStore } from '@/lib/vails';
import { OpmDepartmentModel } from '@/engines/opm/opm-core/models/opm/departments';
import { useRouter } from 'vue-router';

const ComOpmDepartmentsIndex = defineComponent({
  name: 'ComOpmDepartmentsIndex',
  components: {},
  props: {
    store: { type: Object, required: true },
    ownershipRecord: { type: Object, required: true },
  },
  setup(props) {
    const isSearching = ref(false);
    const cloneStore = new VStore(
      new props.store.api.constructor({
        parents: props.store.api.parents,
      }),
      OpmDepartmentModel,
    );
    const params = computed(() => ({
      q: isSearching.value ? {} : { parent_id_null: 1 },
    }));

    const config = computed(() => ({
      recordName: '',
      store: isSearching.value ? cloneStore : props.store,
      formDataEncode: (payload: VObject) => {
        return { ...payload, ownership_id: props.ownershipRecord.id };
      },
      pagination: {
        perPage: 15,
        showPageSizeChanger: false,
        hideOnSinglePage: false,
        showSizeChanger: false,
      },
      template: 'department#opm',
      params: params.value,
      mode: 'table',
      table: {
        scroll: { y: 'auto' },
        pushColumns: [{ title: '操作', width: '120px' }],
        childrenColumnName: 'tableChildren',
      },
      searcherSimpleOptions: [{ key: 'name', label: '部门名称', type: 'string' }],
    }));

    const findContAnyKeyHasValue = (query: VObject) => {
      query = query || {};
      return Object.keys(query).find(
        (key: any) => key.includes('cont_any') && query[key] && query[key].length > 0,
      );
    };

    watch(
      () => [props.store, cloneStore],
      () => {
        const currentQuery = isSearching.value ? cloneStore.query.value : props.store.query.value;

        isSearching.value = !!findContAnyKeyHasValue(currentQuery);
      },
      { deep: true },
    );

    const onExpand = (expanded: boolean, record: VObject) => {
      if (!expanded) return;
      if (record.tableChildren && record.tableChildren.length > 0) return;
      if (record.children_count > 0) {
        //clone store
        const childrenStore = new VStore(
          new props.store.api.constructor({
            parents: props.store.api.parents,
          }),
          props.store.model,
        );
        childrenStore.index({ q: { parent_id_eq: record.id } }).then(() => {
          record.tableChildren = childrenStore.records.value;
        });
      }
    };

    const router = useRouter();

    const onShow = (record: any) => {
      router.push(`/opm/manage/ownerships/${props.ownershipRecord.id}/departments/${record.id}`);
    };

    return {
      ...toRefs(props),
      config,
      onExpand,

      onShow,
    };
  },
});

export default ComOpmDepartmentsIndex;
</script>

<template lang="pug">
.com-opm-admin-departments-index
  TaIndexView(:config='config' @onExpand='onExpand' )
    template(#header='{ actions }')
      .flex.items-center
        .h-9.px-4.text-white.font-medium.rounded.cursor-pointer.bg-primary.mr-3(@click='actions.onCreate()')
          .flex.items-center.h-full
            TaIcon.w-4.h-4.mr-1(type='outline/plus')
            .text-sm 创建
    template(#bodyCell='{ record, column, text, index  }')
      template(v-if='column.title === "操作"')
        .flex.items-center
          .text-sm.cursor-pointer.text-primary(@click.stop='onShow(record)') 查看
</template>

<style lang="stylus" scoped>
.com-opm-admin-departments-index
  height 100%
  width 100%
  >>>.ta-cell-value-formatter
    color: #111928
</style>
