<script lang="ts">
import { ref, defineComponent, toRefs, computed, reactive } from 'vue';
import ComOpmDepartmentDutiesIndex from '../duties/ComOpmDepartmentDutiesIndex.vue';
import ComOpmMembersIndex from '../members/ComOpmMembersIndex.vue';
import ComOpmDepartmentStatisticBlocks from '../departments/ComOpmDepartmentStatisticBlocks.vue';
import { useRoute, useRouter } from 'vue-router';

const ComOpmDepartmentsShow = defineComponent({
  name: 'ComOpmDepartmentsShow',
  components: {
    ComOpmDepartmentDutiesIndex,
    ComOpmMembersIndex,
    ComOpmDepartmentStatisticBlocks,
  },
  props: {
    memberStore: { type: Object, required: true },
    dutyStore: { type: Object, required: true },
    record: { type: Object, required: true },
    ownershipRecord: { type: Object, default: () => {} },
  },
  setup(props) {
    const activeKey = ref<'user' | 'duty'>('user');
    const tabs = computed(() => [
      { key: 'user', label: '人员' },
      { key: 'duty', label: '部门详情' },
    ]);
    const dutyParams = computed(() => ({
      q: { department_id_eq: props.record.id },
    }));
    const memberParams = computed(() => ({
      q: { departments_ancestor_id_eq: props.record.id },
    }));

    const statistic = reactive({
      userTotalCount: 0,
      dutyTotalCount: 0,
      expiringUserCount: 0,
    });

    const statisticData = computed(() => [
      {
        label: '人员总数',
        color: '#9694FF',
        src: 'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/opm/users.svg',
        num: statistic.userTotalCount || 0,
      },
      {
        label: '岗位总数',
        color: '#57CAEB',
        src: 'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/opm/briefcase%20%281%29.svg',
        num: statistic.dutyTotalCount || 0,
      },
      {
        label: '即将到期',
        color: '#659EFF',
        src: 'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/opm/clock%20%281%29.svg',
        num: statistic.expiringUserCount || 0,
      },
    ]);

    const router = useRouter();

    const onCrumbClick = (crumb: any) => {
      if (crumb.type == 'org') {
        router.push(`/opm/manage/ownerships/${props.ownershipRecord.id}/org`);
      } else if (crumb.type == 'department') {
        router.push(`/opm/manage/ownerships/${props.ownershipRecord.id}/departments/${crumb.id}`);
      }
    };

    return {
      ...toRefs(props),
      activeKey,
      dutyParams,
      memberParams,
      tabs,
      statisticData,

      onCrumbClick,
    };
  },
});
export default ComOpmDepartmentsShow;
</script>

<template lang="pug">
.com-opm-departments-show.flex.flex-col.w-full.h-full
  .tabs.bg-white.rounded-lg.mb-4
    .breadcrumb__path.flex.items-center.pl-4.pt-4.text-gray-500
      template(v-for='(node,i) in record.crumbs')
        .text-sm.cursor-pointer(
          :class='{"text-gray-900": i === record.crumbs.length - 1}',
          @click.stop='onCrumbClick(node)'
        ) {{ node.name }}
        TaIcon.w-4.h-4(type='outline/chevron-right' v-if='i < record.crumbs.length - 1')

    a-tabs(v-model:activeKey='activeKey')
      a-tab-pane(v-for='tab in tabs',:key='tab.key',:tab='tab.label')
      template(#renderTabBar='{ DefaultTabBar,...props}')
        component(:is='DefaultTabBar', v-bind='props' :style='{ height:"40px" }')

  section.flex-grow.flex.flex-col
    template(v-if='activeKey === "duty"')
      ComOpmDepartmentStatisticBlocks.mb-4.gap-4(:data='statisticData')
      .px-4.rounded-lg.bg-white.flex-grow.h-0
        ComOpmDepartmentDutiesIndex(
          :store='dutyStore',
          :departmentId='record.id',
          :ownershipRecord='ownershipRecord'
        )

    template(v-else-if='activeKey === "user"')
      .p-4.rounded-lg.bg-white.flex-grow.h-0
        ComOpmMembersIndex(
          :store='memberStore',
          :departmentId='record.id',
          :ownershipRecord='ownershipRecord',
        )

</template>

<style lang="stylus" scoped>
.com-opm-departments-show
  :deep(.ant-tabs-tab):first-child
    margin-left 16px
</style>
