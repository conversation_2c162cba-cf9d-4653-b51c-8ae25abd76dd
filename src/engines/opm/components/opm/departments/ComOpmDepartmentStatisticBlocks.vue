<script lang="ts">
import { defineComponent, toRefs } from 'vue';
const ComOpmDepartmentStatisticBlocks = defineComponent({
  name: 'ComOpmDepartmentStatisticBlocks',
  components: {},
  props: {
    data: { type: Object, default: () => [] },
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
export default ComOpmDepartmentStatisticBlocks;
</script>

<template lang="pug">
.com-opm-department-statistic-blocks.grid(:style='`--count: ${data.length};`')
  .grid__item__block.bg-white.rounded-lg.p-6.flex.items-center(v-for='(item, index) in data')
    .left__box.w-14.h-14.rounded-2xl.grid.mr-5.flex-shrink-0(:style='`background-color: ${item.color};`')
      .icon.w-7.h-7.place-self-center
        img.w-full.h-full(:src='item.src')
    .right__info
      .text-sm.text-gray-500 {{ item.label }}
      .num.text-2xl.text-primary-900.font-bold {{ item.num }}
</template>

<style lang="stylus" scoped>
.com-opm-department-statistic-blocks
  grid-template-columns repeat(var(--count,1), minmax(0, 1fr))
.com-opm-department-statistic-blocks .right__info .num
  font-family "DIN Alternate"
</style>
