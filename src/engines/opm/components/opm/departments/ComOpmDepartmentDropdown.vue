<script lang="ts">
import { VObject } from '@/lib/vails';
import { defineComponent, toRefs, ref, computed } from 'vue';

const ComOpmDepartmentDropdown = defineComponent({
  name: 'ComOpmDepartmentDropdown',
  components: {},
  props: {
    record: { type: Object, required: true },
    store: { type: Object, required: true },
    ownershipRecord: { type: Object, required: true },
  },
  setup(props, { emit }) {
    const formVisible = ref(false);
    const editRecord = ref<any>({});
    const formTitle = ref('');

    const actions = computed(() => {
      if (props.record.dataType == 'org') {
        return ['createDepartment'];
      } else {
        return ['create', 'edit', 'delete'];
      }
    });

    const flag = ref<string>('');

    const operMap = {
      createDepartment: {
        label: '新建部门',
        color: '#111928',
        icon: 'plus',
        action: () => {
          formTitle.value = '新建部门';
          editRecord.value = props.store.new({
            ownership_id: props.ownershipRecord.id,
          });
          formVisible.value = true;
        },
      },
      create: {
        label: '新建子部门',
        color: '#111928',
        icon: 'plus',
        action: (record: VObject) => {
          formTitle.value = '新建部门';
          editRecord.value = props.store.new({
            parent_id: record.key.toString().startsWith('org') ? null : record.id,
            ownership_id: props.ownershipRecord.id,
          });
          formVisible.value = true;
        },
      },
      edit: {
        label: '编辑',
        color: '#111928',
        icon: 'pencil-alt',
        action: (record: VObject) => {
          formTitle.value = '编辑部门';
          editRecord.value = record;
          editRecord.value.formData.ownership_id = props.ownershipRecord.id;
          editRecord.value.origin_parent_id = record.parent_id;
          formVisible.value = true;
        },
      },
      delete: {
        label: '删除',
        color: '#F05252',
        icon: 'trash',
        action: async (record: VObject) => {
          await props.store.delete(record.id);
          emit('delete', record, record.parent_id);
        },
      },
    };

    const handleClick = (el: { key: keyof typeof operMap }, record: any) => {
      if (el.key) {
        flag.value = el.key;
        operMap[el.key].action(record);
      }
    };

    const onSuccess = () => {
      if (flag.value === 'create' || flag.value === 'createDepartment') {
        const node = editRecord.value as any;
        emit('create', node, node.parent_id);
      }

      if (flag.value === 'edit') {
        if (editRecord.value.parent_id === editRecord.value.origin_parent_id) return;

        const node = editRecord.value as any;
        emit('edit', node, node.origin_parent_id, node.parent_id);
      }
    };
    return {
      ...toRefs(props),
      operMap,
      flag,
      handleClick,

      actions,
      formVisible,
      formTitle,
      editRecord,
      onSuccess,
    };
  },
});
export default ComOpmDepartmentDropdown;
</script>

<template lang="pug">
.com-opm-department-dropdown.h-6.w-6.rounded-md(
  class='hover:bg-[rgba(0,0,0,0.05)]'
  @click.stop=''
)
  TaTemplateFormWithActionsModal(
    v-model:visible='formVisible',
    template='department#opm',
    :canSaveAndCreateNext='true',
    :record='editRecord',
    :title='formTitle',
    width='700px',
    :footer='null',
    @success='onSuccess'
  )
  a-dropdown(
    trigger='click'
  )
    TaIcon.place-self-center.text-gray-600.cursor-pointer(
      class='w-6 h-6'
      type='solid/dots-horizontal2'
    )
    template(#overlay)
      a-menu(@click='(el)=>handleClick(el,record)')
        a-menu-item(v-for='actionKey in actions',:key='actionKey')
          .flex.items-center(:style='`color:${operMap[actionKey].color}`',v-if='actionKey !== "delete"')
            TaIcon.mr-3(
              :type='`outline/${operMap[actionKey].icon}`',
              style='width:14px;height:14px'
            )
            .text-sm.font-normal {{operMap[actionKey].label}}
          TaPopoverConfirm(
            v-else
            title='确定删除吗？',
            okText='确定',
            cancelText='取消',
            @click.stop='',
            @confirm='operMap[actionKey].action(record)'
          )
            .flex.items-center(:style='`color:${operMap[actionKey].color}`')
              TaIcon.mr-3(
                :type='`outline/${operMap[actionKey].icon}`',
                style='width:14px;height:14px'
              )
              .text-sm.font-normal {{operMap[actionKey].label}}

</template>

<style lang="stylus" scoped></style>
