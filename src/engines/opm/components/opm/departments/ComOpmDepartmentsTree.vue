<script lang="ts">
import Tree from '@/engines/opm/utils/hooks/useTree';
import { VObject } from '@/lib/vails';
import { CaretDownOutlined, CaretRightOutlined } from '@ant-design/icons-vue';
import type { TreeProps } from 'ant-design-vue';
import { defineComponent, onMounted, ref, toRefs } from 'vue';
import ComOpmDepartmentDropdown from './ComOpmDepartmentDropdown.vue';

const ComOpmDepartmentsTree = defineComponent({
  name: 'ComOpmDepartmentsTree',
  components: {
    CaretRightOutlined,
    CaretDownOutlined,
    ComOpmDepartmentDropdown,
  },
  props: {
    store: { type: Object, required: true },
    ownershipRecord: { type: Object, required: true },
    defaultRoot: { type: Object, default: () => ({}) },
    editable: { type: Boolean, default: true },
  },
  emits: ['select'],
  setup(props, { emit }) {
    const tree = new Tree();
    const expandedKeys = ref<string[]>([]);
    const selectedKeys = ref<string[]>([]);

    const _calcSelectedNodePath = (node: VObject, isFirst = true) => {
      const result: any = [];
      result.push(isFirst ? node : node.node);
      node = node.parent;
      if (node) {
        result.push(..._calcSelectedNodePath(node, false));
      }
      return result;
    };

    onMounted(() => {
      props.store.addRecord = (data: any) => {};
      tree.reloadData = reloadData;
      Tree.isRoot = (node: VObject) => node.dataType === 'org';

      initData();
    });

    const initData = () => {
      props.store.index({ q: { parent_id_null: 1, s: ['id asc'] }, per_page: 99 }).then(() => {
        const records = props.store.records?.value || [];
        tree.data.value = [
          {
            ...props.defaultRoot,
            title: props.defaultRoot.name || '全部',
            dataType: 'org',
            key: `org-${props.defaultRoot.id}`,
            children: records,
          },
        ];
      });
    };

    const reloadData = () => {
      props.store.index({ q: { parent_id_null: 1 }, per_page: 99 });
    };

    const onLoadData: TreeProps['loadData'] = treeNode => {
      if (treeNode.dataRef?.children && treeNode.dataRef.children.length > 0) {
        return Promise.resolve();
      }

      return props.store.index({ q: { parent_id_eq: treeNode.dataRef?.id } }).then(() => {
        const records = props.store.records?.value || [];
        treeNode.dataRef!.children = records;
        tree.data.value = [...tree.data.value];
      });
    };

    const handleSelect = (_selectedKeys: VObject, e: VObject) => {
      const path = _calcSelectedNodePath(e.node)
        .map((n: any) => n.title)
        .reverse();
      emit('select', e.node, path);
    };

    const handleCreateTreeNode = (node: VObject, parentKey: string | number) => {
      tree.create(node, parentKey);
    };
    const handleDeleteTreeNode = (target: VObject, parentKey: string | number) => {
      tree.delete(target, parentKey);
    };

    const handleTransferTreeNode = (
      node: VObject,
      originParentKey: string | number,
      targetParentKey: string | number,
    ) => {
      tree.transfer(node, originParentKey, targetParentKey);
    };

    return {
      ...toRefs(props),
      treeData: tree.data,
      tree,
      expandedKeys,
      selectedKeys,
      onLoadData,
      handleSelect,
      handleCreateTreeNode,
      handleDeleteTreeNode,
      handleTransferTreeNode,
      reloadData,
    };
  },
});
export default ComOpmDepartmentsTree;
</script>

<template lang="pug">
.com-opm-departments-tree.flex.flex-col#department
  section.tree.flex-grow.h-0.overflow-y-auto.pb-4
    a-directory-tree(
      blockNode
      v-model:expandedKeys="expandedKeys"
      v-model:selectedKeys="selectedKeys"
      :tree-data="treeData",
      :load-data="onLoadData",
      @select='handleSelect',
      @expand='handleExpand',
      expand-action='dblclick'
    )
      template(#icon='treeNode')
        //- .grid.w-full.h-full.px-1
        //-   ComOpmDepartmentLabel.px-1.rounded-sm.text-xs.place-self-center(:record='treeNode.dataRef')
      template(#switcherIcon='{ dataRef,expanded }')
        CaretRightOutlined(v-if='!expanded' style='color:#9CA3AF')
        CaretDownOutlined(v-else style='color:#9CA3AF')

      template(#title='{ key: treeKey, title, dataRef }')
        .tree-title__custom.flex.items-center.gap-x-2
          ComOpmDepartmentLabel.px-1.rounded-sm.text-xs.place-self-center.whitespace-nowrap(:record='dataRef')
          .text-sm.text-gray-900.mr-auto.truncate {{ title }}
          .text-xs.text-gray-400(v-if='dataRef.children_count') {{ dataRef.children_count}}
          ComOpmDepartmentDropdown.action.hidden.flex-shrink-0(
            v-if='editable',
            :record='dataRef',
            :store='store',
            :ownershipRecord='ownershipRecord',
            @refresh='reloadData()',
            @create='handleCreateTreeNode',
            @delete='handleDeleteTreeNode',
            @edit='handleTransferTreeNode'
          )

</template>

<style lang="stylus" scoped>
.com-opm-departments-tree#department
  :deep(.ant-tree-node-content-wrapper)
    display flex
    align-items center
    &>span:first-child
      flex-shrink 0
  :deep(.ant-tree-iconEle)
    width fit-content
  :deep(.ant-tree-title)
    width 100%
  :deep(.ant-tree-treenode)
    padding-top 6px
    padding-bottom @padding-top
    &:hover::before
      border-radius 4px
    &:hover .action
      display grid
  :deep(.ant-tree-node-content-wrapper)
    flex-grow 1
    width 0
  :deep(.ant-tree-treenode-selected)::before
    border-radius 4px
    background-color $primary-color-50
  >>>.ant-tree-switcher
    margin-top -3px
  >>>.ant-tree.ant-tree-directory .ant-tree-treenode::before
    bottom 0
</style>
