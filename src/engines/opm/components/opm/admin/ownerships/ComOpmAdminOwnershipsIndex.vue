<script lang="ts">
import { computed, defineComponent, toRefs } from 'vue';

export default defineComponent({
  name: 'ComOpmAdminOwnershipsIndex',
  props: {
    store: { type: Object, required: true },
  },
  setup(props) {
    const config = computed(() => ({
      recordName: '组织权限',
      store: props.store,
      template: 'opm_ownership',
      mode: 'table',
      draggable: true,
      actions: [
        { key: 'create', enabled: true },
        { key: 'update', enabled: true },
        { key: 'delete', enabled: true },
      ],
      table: {
        scroll: { y: 'auto' },
      },
    }));
    return {
      ...toRefs(props),
      config,
    };
  },
});
</script>

<template lang="pug">
.com-opm-admin-ownerships-index.w-full.h-full
  TaIndexView(:config='config')
</template>
