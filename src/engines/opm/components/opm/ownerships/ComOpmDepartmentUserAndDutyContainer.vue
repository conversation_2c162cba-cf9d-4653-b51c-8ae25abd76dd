<script lang="ts">
import { ref, defineComponent, toRefs, computed, reactive, watch } from 'vue';
import ComOpmDepartmentDutiesIndex from '../duties/ComOpmDepartmentDutiesIndex.vue';
import ComOpmMembersIndex from '../members/ComOpmMembersIndex.vue';
import ComOpmDepartmentStatisticBlocks from '../departments/ComOpmDepartmentStatisticBlocks.vue';
import { VStore } from '@/lib/vails';
import { OpmManageDepartmentsApi } from '@/engines/opm/opm-core/apis/opm/manage/departments.api';
import { OpmDepartmentModel } from '@/engines/opm/opm-core/models/opm/departments';
import { useRoute, useRouter } from 'vue-router';

const ComOpmDepartmentUserAndDutyContainer = defineComponent({
  name: 'ComOpmDepartmentUserAndDutyContainer',
  components: {
    ComOpmDepartmentDutiesIndex,
    ComOpmMembersIndex,
    ComOpmDepartmentStatisticBlocks,
  },
  props: {
    userStore: { type: Object, required: true },
    dutyStore: { type: Object, required: true },
    departmentId: { type: [Number, String], default: null },
    path: { type: [String, Array], default: () => [] },
    ownershipRecord: { type: Object, default: () => {} },
  },
  setup(props) {
    const activeKey = ref<'user' | 'duty'>('user');
    const tabs = computed(() => [
      { key: 'user', label: '人员' },
      { key: 'duty', label: props.departmentId ? '部门详情' : '岗位' },
    ]);
    const dutyParams = computed(() => ({
      q: props.departmentId ? { department_id_eq: props.departmentId } : {},
    }));
    const memberParams = computed(() => ({
      q: props.departmentId ? { departments_ancestor_id_eq: props.departmentId } : {},
    }));

    const statistic = ref<any>({
      member_count: 0,
      duty_count: 0,
      due_member_count: 0,
    });

    const statisticData = computed(() => [
      {
        label: '人员总数',
        color: '#9694FF',
        src: 'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/opm/users.svg',
        num: statistic.value?.member_count || 0,
      },
      {
        label: '岗位总数',
        color: '#57CAEB',
        src: 'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/opm/briefcase%20%281%29.svg',
        num: statistic.value?.duty_count || 0,
      },
      {
        label: '即将到期',
        color: '#659EFF',
        src: 'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/opm/clock%20%281%29.svg',
        num: statistic.value?.due_member_count || 0,
      },
    ]);

    const departmentStore = new VStore(
      new OpmManageDepartmentsApi({
        parents: [{ type: 'ownerships', id: props.ownershipRecord.id }],
      }),
      OpmDepartmentModel,
    );

    const fetchStatistic = (departmentId: any) => {
      departmentStore.find(departmentId).then(() => {
        statistic.value = departmentStore.record.value?.statistic || {};
      });
    };

    watch(
      () => props.departmentId,
      () => {
        if (props.departmentId) fetchStatistic(props.departmentId);
      },
    );

    const router = useRouter();

    const onShowDepartment = () => {
      router.push(
        `/opm/manage/ownerships/${props.ownershipRecord.id}/departments/${props.departmentId}`,
      );
    };

    return {
      ...toRefs(props),
      activeKey,
      dutyParams,
      memberParams,
      tabs,
      statisticData,
      onShowDepartment,
    };
  },
});
export default ComOpmDepartmentUserAndDutyContainer;
</script>

<template lang="pug">
.com-opm-department-user-and-duty-container.flex.flex-col
  .tabs.bg-white.rounded-lg.mb-4
    .flex.justify-between.items-center.px-4.pt-4
      .breadcrumb__path.flex.items-center.text-gray-500
        template(v-for='(node,i) in path')
          .text-sm(:class='{"text-gray-900": i === path.length - 1}') {{ node }}
          TaIcon.w-4.h-4(type='outline/chevron-right' v-if='i < path.length - 1')
      TaIcon.w-4.h-4.text-gray-400.cursor-pointer(
        v-if='departmentId',
        type='outline/arrows-expand',
        @click='onShowDepartment()'
      )

    a-tabs(v-model:activeKey='activeKey')
      a-tab-pane(v-for='tab in tabs',:key='tab.key',:tab='tab.label')
      template(#renderTabBar='{ DefaultTabBar,...props}')
        component(:is='DefaultTabBar', v-bind='props' :style='{ height:"40px" }')

  section.flex-grow.h-0.flex.flex-col
    template(v-if='activeKey === "duty"')
      ComOpmDepartmentStatisticBlocks.mb-4.gap-4(:data='statisticData',v-if='departmentId')
      .px-4.rounded-lg.bg-white.flex-grow.h-0
        ComOpmDepartmentDutiesIndex(
          :store='dutyStore',
          :departmentId='departmentId',
          :ownershipRecord='ownershipRecord',
        )

    template(v-else-if='activeKey === "user"')
      .p-4.rounded-lg.bg-white.flex-grow.h-0
        ComOpmMembersIndex(
          :store='userStore',
          :departmentId='departmentId',
          :ownershipRecord='ownershipRecord'
        )

</template>

<style lang="stylus" scoped>
.com-opm-department-user-and-duty-container
  :deep(.ant-tabs-tab):first-child
    margin-left 16px
</style>
