<script lang="ts">
import { defineComponent } from '@vue/runtime-core';
import { VStore } from '@/lib/vails';
import ComOpmDepartmentsTree from '../departments/ComOpmDepartmentsTree.vue';
import ComOpmDepartmentUserAndDutyContainer from './ComOpmDepartmentUserAndDutyContainer.vue';
import ComViewSwitch from '../../ComViewSwitch.vue';
import { ref } from 'vue';
import { OpmManageDepartmentsApi } from '@/engines/opm/opm-core/apis/opm/manage/departments.api';
import { OpmDepartmentTreeModel } from '@/engines/opm/opm-core/models/opm/departments';
import { OpmManageMembersApi } from '@/engines/opm/opm-core/apis/opm/manage/members.api';
import { OpmMemberModel } from '@/engines/opm/opm-core/models/opm/members';
import { OpmManageDepartmentDutiesApi } from '@/engines/opm/opm-core/apis/opm/manage/department_duties.api';
import ComOpmDepartmentsIndex from '../departments/ComOpmDepartmentsIndex.vue';
import ComOpmDepartmentsShow from '../departments/ComOpmDepartmentsShow.vue';

const ComOpmOwnershipsShow = defineComponent({
  name: 'ComOpmOwnershipsShow',
  components: {
    ComOpmDepartmentsIndex,
    ComOpmDepartmentsTree,
    ComViewSwitch,
    ComOpmDepartmentsShow,
    ComOpmDepartmentUserAndDutyContainer,
  },
  props: {
    record: { type: Object, required: true },
  },
  setup(props) {
    // 如果要同步两个视图的状态的话，可能要在ComResDepartmentsIndex里重写index
    const store = new VStore(
      new OpmManageDepartmentsApi({
        parents: [{ type: 'ownerships', id: props.record.id }],
      }),
      OpmDepartmentTreeModel,
    );
    const treeStore = new VStore(
      new OpmManageDepartmentsApi({
        parents: [{ type: 'ownerships', id: props.record.id }],
      }),
      OpmDepartmentTreeModel,
    );

    const dutyStore = new VStore(
      new OpmManageDepartmentDutiesApi({
        parents: [{ type: 'ownerships', id: props.record.id }],
      }),
    );
    const userStore = new VStore(
      new OpmManageMembersApi({
        parents: [{ type: 'ownerships', id: props.record.id }],
      }),
      OpmMemberModel,
    );

    const activeSwitchKey = ref<'tree' | 'table'>('tree');

    const selectedDepartmentId = ref<any>(null);
    const path = ref<string[]>([props.record.org?.name]);

    const handleTreeSelect = (node: any, pathArr: string[]) => {
      selectedDepartmentId.value = node.dataType == 'org' ? null : node.key;
      path.value = pathArr;
    };

    return {
      store,
      treeStore,
      dutyStore,
      userStore,
      activeSwitchKey,
      selectedDepartmentId,
      path,
      handleTreeSelect,
    };
  },
});

export default ComOpmOwnershipsShow;
</script>

<template lang="pug">
.com-opm-ownerships-index
  .wrapper.w-full.h-full.pt-4.flex.flex-col

    header.flex.items-center.justify-between.mb-4.flex-shrink-0
      .title.text-xl.font-semibold 组织结构
      ComViewSwitch(v-model:activeSwitchKey='activeSwitchKey')

    section.flex-grow.h-0.flex.gap-x-4(v-if='activeSwitchKey === "tree"')
      .left__tree.px-2.py-3.bg-white.rounded-lg.flex-shrink-0(class='basis-70')
        ComOpmDepartmentsTree.h-full(
          :store='treeStore'
          :defaultRoot='record.org',
          :ownershipRecord='record',
          @select='handleTreeSelect'
        )
      .right__index.flex-grow
        ComOpmDepartmentUserAndDutyContainer.h-full(
          :dutyStore='dutyStore',
          :userStore='userStore'
          :departmentId='selectedDepartmentId',
          :path='path',
          :ownershipRecord='record',
        )

    section.flex-grow.h-0(v-else-if='activeSwitchKey === "table"')
      .p-4.bg-white.rounded-lg.h-full
        ComOpmDepartmentsIndex(:store='store', :ownershipRecord='record')
</template>

<style lang="stylus" scoped>
.com-opm-ownerships-index
  height 100%
  width 100%
  background #F0F2FC
  .wrapper header .title
    color #25396F
</style>
