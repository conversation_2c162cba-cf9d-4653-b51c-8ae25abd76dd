<script lang="ts">
import { defineComponent, computed, ref, Ref, toRefs, reactive } from 'vue';
import { TaIndexViewTabInterface } from '@/components/global/ta-component/TaIndexView/types';
import { VObject } from '@/lib/vails/model';
import ComOpmJobTitlesShow from './ComOpmJobTitlesShow.vue'
import ComOpmMembersIndex from '../members/ComOpmMembersIndex.vue';
import TaNoPaddingModal from '../../../../../components/global/ta-component/TaNoPaddingModal.vue';
import TaApiNoDisplaySingleField from '../../../../../components/global/ta-component/TaApiNoDisplaySingleField.vue';
import { message } from 'ant-design-vue';

const ComOpmJobTitlesIndex = defineComponent({
  name: 'ComOpmJobTitlesIndex',
  components: {
    ComOpmJobTitlesShow,
    ComOpmMembersIndex,
  },
  props: {
    store: { type: Object, required: true },
    disabled: { type: Boolean, default: false },
    memberStore: { type: Object, required: true },
    ownershipRecord: { type: Object, required: true },
    selectedGroupId: { type: Number, required: true },
    //记录
    recordsStore: { type: Object, default: () => ({}) },
  },
  setup(props, { emit }) {
    const params = computed(() => {
      return props.selectedGroupId ? { q: { group_id_eq: props.selectedGroupId } } : {}
    });
    const config = computed(() => ({
      recordName: '职称',
      store: props.store,
      // pagination: {
      //   perPage: 15,
      //   showPageSizeChanger: false,
      //   hideOnSinglePage: false,
      //   showSizeChanger: false,
      // }
      template: 'opm_job_title',
      params: params.value,
      detail: {
        mode: 'drawer',
        width: '1100px',
      },
      mode: 'table',
      // editApi:
      // showCount: true,
      actions: [
        { key: 'create', enabled: true },
        { key: 'update', enabled: true },
        { key: 'delete', enabled: true },
        { key: 'import', enabled: true },
        { key: 'export', enabled: true },
      ],
      formDataEncode: (formData: any) => {
        return {
          ...formData,
          group_id: formData.group_id || props.selectedGroupId
        };
      },
      table: {
        scroll: { y: 'auto' },
        // columns: [{ dataIndex: 'id', title: 'ID' }],
      },
      searcherSimpleOptions: [{ key: 'name', label: '职称名称', type: 'string' }],
    }));

    const taindexview = ref<any>(null)
    const onCreate = () => {
      taindexview.value?.slotActions?.onCreate({ group_id: props.selectedGroupId })
    }


    const memberSelector = ref<any>(null)
    const selectedMemberId = ref<number>()
    const activeJobTitleId = ref<number>(0)
    const tableItems = [
      {
        name: '姓名',
        type: 'string',
        search: true,
        data_index: 'user.name',
      },
      {
        name: '性别',
        type: 'string',
        search: true,
        data_index: 'user.gender',
      },
      {
        name: '手机号',
        type: 'string',
        search: true,
        data_index: 'user.mobile',
      },
    ]
    const openMemberSelector = (title: VObject) => {
      memberSelector.value?.open()
      activeJobTitleId.value = title.id
    }

    const refreshFlag = ref<boolean>(false)
    const addMemberInTitle = () => {
      props.recordsStore.sendCollectionAction({
        action: 'insert',
        config: {
          data: {
            member_id: selectedMemberId.value,
            source_id: activeJobTitleId.value,
            source_type: 'Opm::JobTitle',
            type: 'Opm::JobTitleRecord'
          }
        }
      }).then(() => {
        message.success('添加成功')
        refreshFlag.value = !refreshFlag.value
        selectedMemberId.value = 0
      })
    }

    const removeMemberFromTitle = (member: VObject, record: VObject) => {
      props.recordsStore.sendCollectionAction({
        action: 'remove',
        config: {
          data: {
            member_id: member.id,
            source_id: record.id,
            source_type: 'Opm::JobTitle',
            type: 'Opm::JobTitleRecord'
          }
        }
      }).then(() => {
        message.success('移除成功')
        refreshFlag.value = !refreshFlag.value
      })
    }

    return {
      ...toRefs(props),
      config,
      onCreate,
      taindexview,
      openMemberSelector,
      memberSelector,
      selectedMemberId,
      addMemberInTitle,
      tableItems,
      refreshFlag,
      removeMemberFromTitle,
    };
  },
});

export default ComOpmJobTitlesIndex;
</script>

<template lang="pug">
.com-opm-admin-job-titles-index.flex.flex-col
  .actions__wrapper.flex.w-full.mb-4.items-center
    .text-base.text-primary-900.font-medium.mr-auto 职称列表
    .btn.bg-primary-500.cursor-pointer(@click='onCreate' v-if='!disabled')
      TaIcon.w-4.h-4(type='outline/plus')
      span.pl-1.text-xs 新建

  TaApiNoDisplaySingleField(
    :ref='el => memberSelector = el',
    v-model:value='selectedMemberId',
    :path='`opm/manage/ownerships/${ownershipRecord.id}/members`',
    :attrs='["name"]',
    recordName='选择人员',
    :multiple='false',
    :tableItems='tableItems'
    @ok='addMemberInTitle'
  )


  TaIndexView.flex-grow.h-0(
    ref='taindexview' :config='config' :showHeader='false'
    @onShow='onShow'
  )
    template(#detail='{ record, onClose }')
      ComOpmMembersIndex.p-4(
        :store='memberStore',
        :ownershipRecord='ownershipRecord'
        :extraParams='{opm_job_titles_id_eq: record.id,refresh_flag:refreshFlag}'
      )
        template(#header-actions)
          .btn.bg-primary-500.cursor-pointer(
            style='width:fit-content'
            @click='openMemberSelector(record)'
          )
            TaIcon.w-4.h-4(type='outline/plus')
            span.pl-1.text-xs 添加人员
        template(#oper='{ record:member }')
          .flex.items-center
            TaPopoverConfirm(
              title='移除',
              :content='`确认移除?`',
              @confirm='() => removeMemberFromTitle(member,record)'
            )
              .text-primary.text-sm.cursor-pointer(@click.stop='') 移除
    //- template(#card='{ record }')
    //- template(#table)
    //-    a-table-column(title='名称' dataIndex='record')
    //- template(#detail='{ record, onClose }')
    //-   ComOpmJobTitlesShow(
    //-     v-if='record.id'
    //-     :store='store'
    //-     :extendRoute='`/opm/admin/job_titles/${record.id}`'
    //-     :editable='editable'
    //-     @afterDelete='onClose'
    //-     @afterExtend='onClose'
    //-   )

</template>

<style lang="stylus" scoped>
.com-opm-admin-job-titles-index
  height 100%
  width 100%
.btn
  @apply px-4 py-2 rounded text-sm text-white font-medium flex items-center;
</style>
