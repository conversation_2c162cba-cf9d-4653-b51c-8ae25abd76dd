<script lang='ts'>
import { ref, defineComponent, toRefs } from 'vue';
import ComColorfulTag from '../../global/ComColorfulTag.vue';
const ComOpmJobTitlesCard = defineComponent({
  name: 'ComOpmJobTitlesCard',
  components: {},
  props: {
    record: { type: Object, default: () => ({}) }
  },
  setup(props) {
    return {
      ...toRefs(props),
    };
  },
});
export default ComOpmJobTitlesCard;
</script>

<template lang="pug">
.com-opm-job-titles-card.bg-gray-50.rounded-lg.p-3.space-y-2
  ComColorfulTag(
    :text='record.group_name',
    color='indigo'
    style='width:fit-content'
  )

  section.flex.items-center
    TaIcon.w-3.h-3.text-gray-400.mr-1(type='solid/user')
    .info {{ record.name }}
  section.flex.items-center
    TaIcon.w-3.h-3.text-gray-400.mr-1(type='solid/calendar')
    .info {{ record.date }}
</template>

<style lang="stylus" scoped>
.com-opm-job-titles-card .info
  color #3F3F46;
  @apply text-sm font-light;
</style>
