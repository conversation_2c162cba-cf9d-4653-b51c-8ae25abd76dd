<script lang='ts'>
import { ref, defineComponent, toRefs, computed } from 'vue';
import dayjs from 'dayjs';
const ComOpmTechItemInfo = defineComponent({
  name: 'ComOpmTechItemInfo',
  components: {},
  props: {
    record: {
      type: Object,
      default: () => ({})
    }
  },
  setup(props) {
    const data = computed(() => ({
      持证人: props.record?.member?.name,
      拿证日期: `${dayjs(props.record.effective_at).format('YYYY-MM-DD')}`,
      是否有证书: props.record?.payload?.has_attachment ? '是' : '否',
      补充说明: props.record?.payload?.extra,
      相关附件: props.record?.payload?.attachments?.files
    }))
    return {
      ...toRefs(props),
      data
    };
  },
});
export default ComOpmTechItemInfo;
</script>

<template lang="pug">
.com-opm-tech-item-info.p-4.rounded-lg.bg-white.text-gray-900
  header.text-base.font-medium.mb-4 证书信息

  .space-y-3
    template(v-for='(value,key) in data')
      .text-sm.text-gray-500(v-if='key !== "相关附件"') {{ key }}：
        span.pl-1.text-sm {{ value }}
      .wrapper(v-else)
        .text-sm.text-gray-500 {{key}}
        TaAttachments(:attachments='value')
</template>

<style lang="stylus" scoped></style>
