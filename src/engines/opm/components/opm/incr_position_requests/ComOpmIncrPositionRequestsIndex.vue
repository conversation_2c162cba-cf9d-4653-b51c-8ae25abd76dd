<script lang="ts">
import { defineComponent, computed, toRefs, ref } from 'vue';
import ComOpmDepartmentStatisticBlocks from '../departments/ComOpmDepartmentStatisticBlocks.vue';
import ComBpmInstanceDetailDialog from '@/engines/bpm/components/ComBpmInstanceDetailDialog.vue';
import { TaIndexViewTabInterface } from '@/components/global/ta-component/TaIndexView/types';
import ComOpmHrRequestMemberSelect from '../hr_requests/ComOpmHrRequestMemberSelect.vue';
// import ComOpmCreateRelocatePositionRequest from './ComOpmCreateRelocatePositionRequest.vue';
import ComOpmCreateIncrPositionRequest from './ComOpmCreateIncrPositionRequest.vue';

export default defineComponent({
  name: 'ComOpmIncrPositionRequestsIndex',
  components: {
    ComOpmDepartmentStatisticBlocks,
    ComBpmInstanceDetailDialog,
    ComOpmHrRequestMemberSelect,
    ComOpmCreateIncrPositionRequest,
  },
  props: {
    store: { type: Object, required: true },
    ownershipRecord: { type: Object, required: true },
  },
  setup(props) {
    const config = computed(() => ({
      store: props.store,
      mode: 'table',
      showCount: true,
      template: 'opm_incr_position_request',
      table: {
        scroll: { y: 'auto' },
        pushColumns: [
          {
            title: '操作',
            width: '80px',
          },
        ],
      },
      searcherSimpleOptions: [
        { key: 'user_name', label: '申请人姓名', type: 'string' },
        { key: 'user_account', label: '申请人账号', type: 'string' },
      ],
    }));

    const blockStat = ref<any>({});
    const statisticData = computed(() => [
      {
        label: '人员总数',
        color: '#9694FF',
        src: 'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/opm/users.svg',
        num: blockStat.value?.user_count || 0,
      },
      {
        label: '近30天新增',
        color: '#659EFF',
        src: 'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/opm/clock%20%281%29.svg',
        num: blockStat.value?.month_add_count || 0,
      },
      {
        label: '待审批',
        color: '#57CAEB',
        src: 'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/opm/briefcase%20%281%29.svg',
        num: blockStat.value?.processing_count || 0,
      },
    ]);

    const statistics = ref<any>({});

    const tabs = computed<TaIndexViewTabInterface[]>(() => [
      {
        key: 'processing',
        label: '待处理',
        num: statistics.value?.processing || 0,
        query: {
          create_instance_state_eq: 'processing',
        },
      },
      {
        key: 'completed',
        label: '已处理',
        num: statistics.value?.completed || 0,
        query: {
          create_instance_state_eq: 'completed',
        },
      },
      {
        key: 'terminated',
        label: '已终止',
        num: statistics.value?.terminated || 0,
        query: {
          create_instance_state_eq: 'terminated',
        },
      },
    ]);

    const onIndex = (data: any) => {
      blockStat.value = data.total_statistics;
      statistics.value = data.statistics;
    };

    const statVisible = ref(true);
    const toggleStatistic = () => {
      statVisible.value = !statVisible.value;
    };

    const visibleInstance = ref(false);
    const activeInstanceId = ref();

    const onShowInstance = (record: any) => {
      if (record.flowable_instance_infos?.[0]?.id) {
        activeInstanceId.value = record.flowable_instance_infos?.[0]?.id;
        visibleInstance.value = true;
      }
    };

    const memberRecord = ref<any>({});
    const modalVisible = ref(false);

    const onMemberSelected = (record: any) => {
      memberRecord.value = record;
      if (memberRecord.value.id) {
        modalVisible.value = true;
      }
    };

    const taIndexView = ref<any>(null);
    const refresh = () => {
      taIndexView.value?.silenceRefresh();
    };

    const exporter = ref<any>(null);
    const onExport = () => {
      exporter.value.onHeaders();
    };

    return {
      ...toRefs(props),
      config,
      onIndex,
      onShowInstance,
      statisticData,
      toggleStatistic,
      statVisible,
      visibleInstance,
      activeInstanceId,

      tabs,

      memberRecord,
      modalVisible,
      onMemberSelected,

      refresh,
      taIndexView,

      exporter,
      onExport,
    };
  },
});
</script>

<template lang="pug">
.com-opm-incr-position-requests-index.w-full.h-full
  .w-full.h-full.pt-4.flex.flex-col
    .flex.items-center.justify-between.mb-4.flex-shrink-0
      .title.text-xl.font-semibold 增岗管理
      .bg-white.py-2.px-4.rounded.flex.items-center.cursor-pointer(@click='toggleStatistic()')
        TaIcon.w-4.h-4.mr-2.text-primary(type='outline/presentation-chart-bar')
        .text-gray-900.text-sm {{ statVisible ? '隐藏' : '显示' }}统计
    
    ComOpmDepartmentStatisticBlocks.mb-4.gap-4(:data='statisticData', v-show='statVisible')
    .flex.flex-grow.flex-col.bg-white.p-4.rounded
      TaIndexView(ref='taIndexView', :config='config', @onIndex='onIndex', :tabs='tabs')
        template(#header)
          .flex.items-center
            ComOpmHrRequestMemberSelect.mr-3(
              :ownershipRecord='ownershipRecord',
              @onOk='onMemberSelected'
            )
            .flex.items-center.py-2.px-4.text-white.font-medium.rounded.cursor-pointer.bg-orange-400(
              class='hover:bg-orange-500',
              @click='onExport()'
            )
              TaIcon.w-4.h-4(type='outline/upload')
              span.pl-1 导出列表
        template(#bodyCell='{ record, column, text, index  }')
          template(v-if='column.title === "操作"')
            .text-primary.text-sm.cursor-pointer(@click.stop='onShowInstance(record)') 查看

  ComBpmInstanceDetailDialog(v-model:visible='visibleInstance', :instanceId='activeInstanceId', @close='refresh()')

  ComOpmCreateIncrPositionRequest(
    v-model:visible='modalVisible',
    :ownershipRecord='ownershipRecord',
    :memberRecord='memberRecord',
    @refresh='refresh()'
    v-if='memberRecord.id'
  )

  .hidden
    TaExport(
      ref='exporter',
      :store='store',
      :template='taIndexView?.localTemplate',
    )
</template>
