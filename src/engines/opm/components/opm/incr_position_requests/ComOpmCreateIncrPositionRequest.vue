<script lang="ts">
import { defineComponent, toRefs, computed, ref } from 'vue';
import { VStore } from '@/lib/vails';
import { OpmManageIncrPositionRequestsApi } from '@/engines/opm/opm-core/apis/opm/manage/incr_position_requests.api';
import { OpmIncrPositionRequestModel } from '@/engines/opm/opm-core/models/opm/incr_position_requests';
import ComBpmInstanceDetailDialog from '@/engines/bpm/components/ComBpmInstanceDetailDialog.vue';

export default defineComponent({
  name: 'ComOpmCreateIncrPositionRequest',
  components: { ComBpmInstanceDetailDialog },
  props: {
    memberRecord: { type: Object, required: true },
    ownershipRecord: { type: Object, required: true },
    visible: { type: Boolean, default: false },
  },
  setup(props, { emit }) {
    const localVisible = computed({
      get: () => props.visible,
      set: val => emit('update:visible', val),
    });

    const editRecord = ref<any>({
      org: {
        name: props.ownershipRecord.org?.name,
      },
      member: props.memberRecord,
      ownership_id: props.ownershipRecord.id,
    });

    const requestStore = new VStore(
      new OpmManageIncrPositionRequestsApi({
        parents: [{ type: 'ownerships', id: props.ownershipRecord.id }],
      }),
      OpmIncrPositionRequestModel,
    );

    const visibleInstance = ref(false);
    const activeInstanceId = ref();

    const onSubmit = () => {
      const submitValue = {
        payload: editRecord.value.payload,
        member_id: props.memberRecord.id,
      };

      requestStore.create(submitValue as any).then((res: any) => {
        localVisible.value = false;
        if (res.flowable_instance_infos?.[0]?.id) {
          visibleInstance.value = true;
          activeInstanceId.value = res.flowable_instance_infos?.[0]?.id;
        }
        emit('refresh');
      });
    };

    const onDialogClose = () => {
      emit('refresh');
    };

    return {
      ...toRefs(props),
      localVisible,
      editRecord,
      onSubmit,
      visibleInstance,
      activeInstanceId,
      onDialogClose,
    };
  },
});
</script>

<template lang="pug">
.com-opm-create-incr-position-request
  ComBpmInstanceDetailDialog(v-model:visible='visibleInstance', :instanceId='activeInstanceId', @close='onDialogClose')

  a-modal(
    v-model:visible='localVisible',
    title='新增岗位',
    width='640px'
  )
    .incr-poistion-form-container
      TaTemplateForm(
        ref='form',
        :modelValue='editRecord',
        template='opm_incr_position_request'
      )
    template(#footer)
      .flex.items-center.justify-end
        .px-5.py-2.bg-white.border.text-sm.font-medium.cursor-pointer.rounded.mr-2(
          class='text-[#1F2A37] border-[#E5E7EB]',
          @click='() => localVisible = false'
        ) 取消
        .px-5.py-2.bg-primary.text-white.text-sm.font-medium.cursor-pointer.rounded(@click='onSubmit()') 提交
</template>

<style lang="stylus" scoped>
.incr-poistion-form-container
  >>>.is-parent-container
    border-radius: 0;
    margin: 0;
</style>
