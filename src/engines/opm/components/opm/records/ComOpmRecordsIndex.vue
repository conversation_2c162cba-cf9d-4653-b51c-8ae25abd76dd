<script lang="ts">
import { defineComponent, computed, ref, Ref, toRefs, reactive } from 'vue';
import { TaIndexViewTabInterface } from '@/components/global/ta-component/TaIndexView/types';
import { VObject } from '@/lib/vails/model';
import ComOpmRecordsShow from './ComOpmRecordsShow.vue'

const ComOpmRecordsIndex = defineComponent({
  name: 'ComOpmRecordsIndex',
  components: {
    ComOpmRecordsShow,
  },
  props: {
    store: { type: Object, required: true },

  },
  setup(props, { emit }) {
    const config = computed(() => ({
      recordName: '教育信息',
      store: props.store,
      // pagination: {
      //   perPage: 15,
      //   showPageSizeChanger: false,
      //   hideOnSinglePage: false,
      //   showSizeChanger: false,
      // }
      template: 'opm_edu_record',
      // detail: {
      //   mode: 'auto',
      //   mode: 'drawer',
      //   width: '1100px',
      // },
      mode: 'table',
      // actions: [
      //  { key: 'create', enabled: true },
      //  { key: 'update', enabled: true },
      //  { key: 'delete', enabled: true },
      //  { key: 'import', enabled: true },
      //  { key: 'export', enabled: true },
      // ],
      table: {
        scroll: { y: 'auto' },
        // columns: [{ dataIndex: 'id', title: 'ID' }],
      },
      searcherSimpleOptions: [
        { key: 'origin', label: '学校', type: 'string' },
        { key: 'level', label: '学历', type: 'string' },
      ],
    }));

    const taindexview = ref<any>(null)
    const onCreate = () => {
      taindexview.value?.slotActions?.onCreate()
    }
    const onExport = () => {
      taindexview.value?.slotActions?.export()
    }

    return {
      ...toRefs(props),
      config,
      onCreate,
      taindexview,
      onExport,
    };
  },
});

export default ComOpmRecordsIndex;
</script>

<template lang="pug">
.com-opm-admin-records-index
  TaIndexView.ta-index-view(:config='config' ref='taindexview')
    template(#header)
      .export.h-full.flex.pb-4
        .btn.cursor-pointer.bg-orange-400.mr-2(@click='onExport')
          TaIcon.w-4.h-4(type='outline/upload')
          span.pl-1 导出
        .btn.bg-primary-500.cursor-pointer(@click='onCreate' v-if='!disabled')
          TaIcon.w-4.h-4(type='outline/plus')
          span.pl-1 新建
    //- template(#card='{ record }')
    //- template(#table)
    //-    a-table-column(title='名称' dataIndex='record')
    //- template(#detail='{ record, onClose }')
    //-   ComOpmRecordsShow(
    //-     v-if='record.id'
    //-     :store='store'
    //-     :extendRoute='`/opm/admin/records/${record.id}`'
    //-     :editable='editable'
    //-     @afterDelete='onClose'
    //-     @afterExtend='onClose'
    //-   )

</template>

<style lang="stylus" scoped>
.com-opm-admin-records-index
  height 100%
  width 100%
  .btn
    @apply px-4 py-2 rounded text-sm text-white font-medium flex items-center;

.com-opm-admin-records-index .ta-index-view :deep(.search)
  padding-bottom 16px
</style>
