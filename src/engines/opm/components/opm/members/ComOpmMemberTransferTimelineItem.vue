<script lang="ts">
import { ref, defineComponent, toRefs } from 'vue';
import dayjs from 'dayjs';
import { get } from 'lodash';
const ComOpmMemberTransferTimelineItem = defineComponent({
  name: 'ComOpmMemberTransferTimelineItem',
  components: {},
  props: {
    record: {
      type: Object,
      required: true,
    },
  },
  setup(props) {
    return {
      ...toRefs(props),
      dayjs,
      get,
    };
  },
});
export default ComOpmMemberTransferTimelineItem;
</script>

<template lang="pug">
a-timeline-item.com-opm-member-transfer-timeline-item
  template(#dot)
    .w-7.h-7.rounded-full.bg-primary-100.grid
      img.w-14px.h-14px.place-self-center(
        src='https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/jetset/timelineNode.png'
      )

  article
    header.flex.items-center.mb-1
      .text-lg.text-gray-900.font-semibold.mr-2 {{ record?.typeConfig?.label }}
      ComColorfulTag.text-white.text-xs(isCustom :style='`background-color:${record?.typeConfig?.color}`' :text='record?.typeConfig?.tag')
    .text-sm.text-gray-500.mb-2 {{dayjs(record?.created_at).format('YYYY-MM-DD HH:mm:ss')}}

    section.text-base.mb-2.flex.items-center.flex-wrap
      .text-gray-400.mr-9(v-for='(value,key) in record?.typeConfig?.payload') {{ key }}：
        span.text-gray-900 {{get(record,value)}}

    //- section.text-base.text-gray-700 内容文案内容文案内容文案内容文案内容内容文案内容文案内容文案内容文案内容内容文案内容文案内容文案内容文案内容内容文案内容文案内容文案内容
</template>

<style lang="stylus" scoped></style>
