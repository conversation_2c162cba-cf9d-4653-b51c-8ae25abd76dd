<script lang="ts">
import { OpmMemberModel } from '@/engines/opm/opm-core/models/opm/members';
import { VApi, VStore } from '@/lib/vails';
import { computed, defineComponent, toRefs } from 'vue';
const ComOpmMemberInfoCard = defineComponent({
  name: 'ComOpmMemberInfoCard',
  components: {},
  props: {
    record: { type: Object, required: true },
    isPopoverContent: { type: Boolean, default: false },
  },
  setup(props) {
    const mainPosition = computed(() => {
      return (
        props.record.departments_duties?.filter((dd: any) => dd.priority)?.[0] ||
        props.record.departments_duties?.[0]
      );
    });
    return {
      ...toRefs(props),
      mainPosition,
    };
  },
});
export default ComOpmMemberInfoCard;
</script>

<template lang="pug">
.com-opm-member-info-card(:class='{"p-4 bg-white rounded-lg shadow-md":!isPopoverContent}')
  .flex
    TaAvatar.mr-4(:user='record.user', :size='80')
    .info
      header.flex.items-center.mb-2
        .text-lg.text-gray-900.font-semibold.mr-3 {{ record.user?.name }}
        .flex.items-center.space-x-3.text-xs(style='padding-left:1em')
          ComRankTag(:rank='mainPosition?.duty_rank')
          //- ComColorfulTag(color='#1C64F2' text='在职')
      .flex.items-center.mb-1
        TaIcon.w-4.h-4.mr-1.text-gray-400(type='outline/briefcase')
        .text-sm {{ mainPosition?.department_name || '暂无岗位' }} | {{ mainPosition?.duty_name || '暂无部门' }}
      .flex.items-center
        TaIcon.w-4.h-4.mr-1.text-gray-400(type='outline/mail')
        .text-sm {{ record.user?.email || '暂无邮箱' }}
</template>

<style lang="stylus" scoped>
.com-opm-member-info-card:deep(.avatar)
  @apply rounded-lg overflow-hidden;
.com-opm-member-info-card .info
  font-family Lexend
</style>
