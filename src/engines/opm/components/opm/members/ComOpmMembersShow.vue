<script lang="ts">
import { truncate } from 'lodash';
import { defineComponent, toRefs, PropType, computed } from 'vue';
import ComVerticalTabs from '../../ComVerticalTabs.vue';
import treasuriesRoute from '@/engines/res/router/res/admin/treasuries.route';

const ComOpmMembersShow = defineComponent({
  name: 'ComOpmMembersShow',
  components: { ComVerticalTabs },
  props: {
    store: { type: Object, required: true },
    record: { type: Object, required: true },
    ownershipRecord: { type: Object, required: true },
    eduItemStore: { type: Object, default: () => ({}) },
    winItemStore: { type: Object, default: () => ({}) },
    teachingItemStore: { type: Object, default: () => ({}) },
    techItemStore: { type: Object, default: () => ({}) },
    eduRecordStore: { type: Object, default: () => ({}) },
    memberTitleStore: { type: Object, required: true },
    handleTabMethod: { type: [String, Array] as PropType<string | string[]>, default: '' },
    handleTabPayload: { type: [Object, Array], default: null },
  },
  setup(props) {
    const enableTabs = JSON.parse(process.env.VUE_APP_OPM_ENABLE_TABS || '[]');
    const baseTabs = [
      {
        label: '基本信息',
        key: 'basic',
        comp: 'ComOpmMemberBasicInfo',
        store: props.store,
        record: props.record,
        ownershipRecord: props.ownershipRecord,
        visible: true,
      },
      {
        label: '岗位信息',
        key: 'duty',
        comp: 'ComOpmMemberDutyList',
        record: props.record,
        store: props.store,
        visible: true,
      },
      {
        label: '个人信息',
        key: 'personal',
        comp: 'ComOpmMemberPersonalCard',
        record: props.record,
        ownershipRecord: props.ownershipRecord,
        visible: true,
      },
      {
        label: '职称信息',
        key: 'title',
        comp: 'ComOpmMemberJobTitleList',
        store: props.memberTitleStore,
        record: props.record,
        visible: enableTabs.includes('title'),
      },
      {
        label: '教育信息',
        key: 'education',
        comp: 'ComOpmMemberEduRecords',
        record: props.record,
        store: props.eduRecordStore,
        disabled: true,
        visible: enableTabs.includes('education'),
      },
      {
        label: '培训记录',
        key: 'train',
        comp: 'ComOpmMemberTrainList',
        ownershipRecord: props.ownershipRecord,
        record: props.record,
        store: props.eduItemStore,
        template: 'opm_edu_item',
        disabled: true,
        visible: enableTabs.includes('train'),
      },
      {
        label: '获奖记录',
        key: 'win',
        comp: 'ComOpmMemberTrainList',
        ownershipRecord: props.ownershipRecord,
        record: props.record,
        store: props.winItemStore,
        template: 'opm_win_item',
        disabled: true,
        visible: enableTabs.includes('win'),
      },
      {
        label: '高教证书',
        key: 'teaching',
        comp: 'ComOpmMemberTrainList',
        ownershipRecord: props.ownershipRecord,
        record: props.record,
        store: props.teachingItemStore,
        template: 'opm_teaching_item',
        disabled: true,
        visible: enableTabs.includes('teaching'),
      },
      {
        label: '技能证书',
        key: 'tech',
        comp: 'ComOpmMemberTrainList',
        ownershipRecord: props.ownershipRecord,
        record: props.record,
        store: props.techItemStore,
        template: 'opm_tech_item',
        disabled: true,
        visible: enableTabs.includes('tech'),
      },
      {
        label: '合同信息',
        key: 'agreement',
        comp: 'ComOpmAgreementList',
        record: props.record,
        visible: enableTabs.includes('agreement'),
      },
      {
        label: '人事调动',
        key: 'transfer',
        comp: 'ComOpmMemberTransferTimeline',
        record: props.record,
        ownershipRecord: props.ownershipRecord,
        visible: enableTabs.includes('transfer'),
      },
    ];

    const handleTabs = (method: string | string[], payload: any) => {
      if (!method) return baseTabs.filter(tab => tab.visible);

      const result: any = [];
      //TODO:待完善 补充push,insert,remove等方法 来代替插槽
      return result;
    };

    const tabs = computed(() => handleTabs(props.handleTabMethod, props.handleTabPayload));

    return {
      ...toRefs(props),
      tabs,
    };
  },
});
export default ComOpmMembersShow;
</script>

<template lang="pug">
.com-opm-members-show.grid
  .left__tab.px-4.pt-5
    ComVerticalTabs(:tabs='tabs')
  .overflow-y-auto.py-5
    .right__content.w-full.grid.gap-4
      template(v-for='(tab,index) in tabs')
        ComOpmCardContainer(:title='index && tab.label' :id='tab.key' style='min-width:700px')
          component(
            v-if='tab.comp'
            :is='tab.comp'
            :record='tab.record',
            :store='tab.store',
            :ownershipRecord='ownershipRecord'
            :disabled='tab.disabled'
            :template='tab.template'
          )
          template(#actions)
            component(
              v-if='tab.actionComp',
              :is='tab.actionComp',
              :record='tab.record',
              :store='tab.store',
            )
</template>

<style lang="stylus" scoped>
.com-opm-members-show
  height 100%
  grid-template-columns 180px 1fr
</style>
