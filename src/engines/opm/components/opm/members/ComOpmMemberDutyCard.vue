<script lang="ts">
import { defineComponent, toRefs } from 'vue';

const ComOpmMemberDutyCard = defineComponent({
  name: 'ComOpmMemberDutyCard',
  components: {},
  props: {
    item: { type: Object, required: true },
    disabled: { type: Boolean, default: false },
  },
  setup(props, { emit }) {
    const onClick = () => {
      if (!props.item.priority && !props.disabled) {
        emit('changePriority', props.item);
      }
    };
    return {
      ...toRefs(props),
      onClick,
    };
  },
});
export default ComOpmMemberDutyCard;
</script>

<template lang="pug">
.com-opm-member-duty-card.rounded-md.bg-gray-50.p-3.relative(
  :class='{"checked": item.priority}',
  @click='onClick()'
)
  header.flex.items-center.mb-2.h-6(style='padding-left:1em')
    ComRankTag(:rank='item.duty_rank')
    .main__duty.ml-auto
      ComColorfulTag.text-xs.flex.items-center(
        v-if='item.priority'
        color='#3F83F8'
        class='!px-1 !py-[2px]'
      )
        TaIcon.w-4.h-4.text-primary-500.mr-1(type='solid/briefcase')
        .text-xs 主岗
      .action__btn.hidden(v-else-if='!item.priority && !disabled')
        a-tag.cursor-pointer(color='blue') 设为主岗
  section.flex.items-center.mb-1
    TaIcon.text-gray-400.w-4.h-4(type='solid/briefcase')
    .label.text-sm.pl-1 {{ item.department_name || '暂无部门' }}
  section.flex.items-center
    TaIcon.text-gray-400.w-4.h-4(type='solid/user')
    .label.text-sm.pl-1 {{ item.duty_name || '暂无岗位' }}
</template>

<style lang="stylus" scoped>
.com-opm-member-duty-card
  border 1px solid #F9FAFB
.com-opm-member-duty-cardsection .label
 color #3F3F46
.com-opm-member-duty-card
  &:hover, &.checked
    border-color $primary-color-500
.com-opm-member-duty-card:hover
  .action__btn
    display block
.com-opm-member-duty-card input[type='checkbox']
  border none
  outline none
</style>
