<script lang="ts">
import { OpmMemberModel } from '@/engines/opm/opm-core/models/opm/members';
import { VApi, VStore } from '@/lib/vails';
import { computed, defineComponent, toRefs } from 'vue';

export default defineComponent({
  name: 'ComOpmMemberAvatarCard',
  props: {
    record: { type: Object, required: true },
  },
  setup(props) {
    const data = computed(() => {
      return new OpmMemberModel(props.record, new VStore(new VApi()));
    });

    return {
      ...toRefs(props),
      data,
    };
  },
});
</script>

<template lang="pug">
.com-opm-member-avatar-card.flex.items-center
  TaAvatar(:user='record.user', :size='80')
  .ml-4
    .text-blue-900.text-lg.font-medium.mb-1 {{ record.user?.name || record.user?.account }}
    ComOpmPhonMask.mb-1(:value='record.user.mobile' v-if='record.user?.mobile')
    .flex.items-center
      TaIcon.w-4.h-4.text-gray-400.mr-1(type='solid/briefcase')
      .text-sm(class='text-[#3F3F46]') {{ data.mainPosition?.department_name || "暂无部门" }}
      span.mx-2 |
      .text-sm(class='text-[#3F3F46]') {{ data.mainPosition?.duty_name || "暂无岗位" }}
</template>
