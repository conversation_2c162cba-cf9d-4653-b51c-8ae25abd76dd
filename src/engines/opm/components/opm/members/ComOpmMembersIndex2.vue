<script lang="ts">
import { defineComponent, computed, ref, onMounted, reactive } from 'vue';
import ComOpmMembersIndex from './ComOpmMembersIndex.vue';
import ComOpmDepartmentStatisticBlocks from '../departments/ComOpmDepartmentStatisticBlocks.vue';

const ComOpmMembersIndex2 = defineComponent({
  name: 'ComOpmMembersIndex2',
  components: { ComOpmMembersIndex, ComOpmDepartmentStatisticBlocks },
  props: {
    store: { type: Object, required: true },
    ownershipRecord: { type: Object, required: true },
  },
  setup(props) {
    const state = reactive({
      total: 0,
      effective: 0,
      expired: 0,
      expired30: 0,
    });
    const statisticData = computed(() => [
      {
        label: '人员总数',
        color: '#9694FF',
        src: 'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/opm/users.svg',
        num: state.total || 0,
      },
      {
        label: '生效中',
        color: '#57CAEB',
        src: 'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/opm/briefcase%20%281%29.svg',
        num: state.effective || 0,
      },
      {
        label: '近30天失效',
        color: '#659EFF',
        src: 'https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/opm/clock%20%281%29.svg',
        num: state.expired30 || 0,
      },
    ]);

    const fetchStatisticData = () => {
      props.store
        .sendCollectionAction({
          action: 'list_index',
          config: {
            data: {
              collection_stat_condition: {
                items: [
                  {
                    key: 'stat',
                    caculator: {
                      type: 'caculation',
                      caculations: [
                        { name: 'total', method: 'count' },
                        { name: 'effective', method: 'count' },
                      ],
                    },
                  },
                ],
              },
            },
          },
        })
        .then((res: any) => {
          state.total = res.data.statistics?.stat?.total;
          state.effective = res.data.statistics?.stat?.effective;
        });
    };

    onMounted(() => {
      fetchStatisticData();
    });

    const statVisible = ref(true);
    const toggleStatistic = () => {
      statVisible.value = !statVisible.value;
    };

    return {
      statisticData,
      toggleStatistic,
      statVisible,
    };
  },
});
export default ComOpmMembersIndex2;
</script>

<template lang="pug">
.com-opm-ownerships-index.w-full.h-full
  .w-full.h-full.pt-4.flex.flex-col
    .flex.items-center.justify-between.mb-4.flex-shrink-0
      .title.text-xl.font-semibold 人员列表
      .bg-white.py-2.px-4.rounded.flex.items-center.cursor-pointer(@click='toggleStatistic()')
        TaIcon.w-4.h-4.mr-2.text-primary(type='outline/presentation-chart-bar')
        .text-gray-900.text-sm {{ statVisible ? '隐藏' : '显示' }}统计

    ComOpmDepartmentStatisticBlocks.mb-4.gap-4(:data='statisticData', v-show='statVisible')
    .flex.flex-grow.flex-col.bg-white.p-4.rounded
      ComOpmMembersIndex(
        :store='store',
        :ownershipRecord='ownershipRecord',
        template='member#opm_members'
      )
</template>

<style lang="stylus" scoped>
.com-opm-ownerships-index
  .text-primary
    color: $primary-color
</style>
