<script lang="ts">
import { ref, defineComponent, toRefs, computed } from 'vue';
import dayjs from 'dayjs';

const ComOpmMemberAgreementCard = defineComponent({
  name: 'ComOpmMemberAgreementCard',
  components: {},
  props: {},
  setup(props) {
    const SIGNED_BG = 'url(https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/jetset/signed.png)';
    const SIGNING_BG = 'url(https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/jetset/signing.png)';
    const EXPIRED_BG = 'url(https://stiei-obs2.obs.cn-east-2.myhuaweicloud.com/jetset/expired.png)';
    const computedBg = computed(() => {
      return SIGNED_BG;
    });

    const safeDayjs = (date: string) => {
      if (!date) return '-';
      return dayjs(date).format('YYYY-MM-DD');
    };

    const info = computed(() => ({
      合同编号: '1234567654321',
      合同类型: '固定期限劳动合同',
      合同日期: '',
      合同期限: '',
      签订日期: '',
      是否到期提醒: false,
    }));
    return {
      ...toRefs(props),
      info,
      computedBg,
    };
  },
});
export default ComOpmMemberAgreementCard;
</script>

<template lang="pug">
.com-opm-member-agreement-card.rounded-lg.p-4.bg-cover
  header.text-gray-900.text-base.mb-3 固定期限劳动合同
  section.info.text-sm.mb-3.grid.grid-cols-2.gap-y-2
    .text-gray-500.flex.items-center(v-for='(value,key) in info') {{ key }}：
      TaSwitch(
        v-if='typeof value === "boolean"'
        :value='value'
        trueLabel="是"
        falseLabel="否"
      )
      span.text-gray-900(v-else) {{ value }}
  section.text-gray-500
    .text-sm 合同附件：
    //- TaAttachments
</template>

<style lang="stylus" scoped>
.com-opm-member-agreement-card
  background-image v-bind(computedBg)
</style>
