<script lang="ts">
import useProcessFields from '@/components/global/ta-component/ta-template-form-core/useProcessFields';
import { defineComponent, toRefs, computed, ref } from 'vue';
import { FormsUserSetableTemplateApi } from '@/components/global/ta-component/ta-template-form-core/templates.api';
import ComOpmCreateEmployInvite from '../employ_invites/ComOpmCreateEmployInvite.vue';
import { useRoute, useRouter } from 'vue-router';

const ComOpmMembersIndex = defineComponent({
  name: 'ComOpmMembersIndex',
  components: {
    ComOpmCreateEmployInvite,
  },
  props: {
    store: { type: Object, required: true },
    departmentId: { type: Number, default: null },
    dutyId: { type: Number, default: null },
    ownershipRecord: { type: Object, default: () => {} },
    template: { type: String, default: 'member#opm' },
    extraParams: { type: Object, default: () => ({}) },
  },
  setup(props) {
    const modes = [
      {
        text: '看本级',
        key: 'all_level',
      },
      {
        text: '看全部',
        key: 'self_level',
      },
    ];

    const currentMode = ref(modes[0]);
    const changeMode = (mode: any) => {
      currentMode.value = mode;
    };

    const params = computed(() => {
      if (props.departmentId && props.dutyId) {
        return {
          q: {
            departments_id_eq: props.departmentId,
            duties_id_eq: props.dutyId,
          },
        };
      }

      if (currentMode.value.key == 'all_level') {
        return {
          q: {
            departments_ancestor_id_eq: props.departmentId,
          },
        };
      }

      return {
        q: {
          departments_id_eq: props.departmentId,
        },
      };
    });

    const config = computed(() => ({
      store: props.store,
      mode: 'table',
      template: props.template,
      params: {
        q: {
          ...params.value.q,
          ...props.extraParams,
        },
      },
      table: {
        scroll: { y: 'auto' },
        pushColumns: [
          {
            title: '操作',
            width: '80px',
          },
        ],
      },
      searcherSimpleOptions: [
        { key: 'user_name', label: '姓名', type: 'string' },
        { key: 'user_account', label: '账号', type: 'string' },
        { key: 'user_mobile', label: '手机号', type: 'string' },
      ],
      searcherAccordionUseDrawer: true,
      searcherComplicatedOptions: [
        {
          key: 'user_res_tags_id',
          label: '标签',
          type: 'select',
          path: `/opm/manage/ownerships/${props.ownershipRecord.id}/tags`,
          multiple: true,
        },
        {
          key: 'payload.出生日期',
          label: '出生日期',
          type: 'time',
        },
        {
          key: 'payload.籍贯',
          label: '籍贯',
          type: 'string',
        },
        {
          key: 'payload.政治面貌',
          label: '政治面貌',
          type: 'select',
          options: [
            {
              label: '政治党员（正式）',
              query: {
                'payload.政治面貌_eq': '政治党员（正式）',
              },
            },
            {
              label: '政治党员（预备）',
              query: {
                'payload.政治面貌_eq': '政治党员（预备）',
              },
            },
            {
              label: '民革',
              query: {
                'payload.政治面貌_eq': '民革',
              },
            },
            {
              label: '民盟',
              query: {
                'payload.政治面貌_eq': '民盟',
              },
            },
            {
              label: '民建',
              query: {
                'payload.政治面貌_eq': '民建',
              },
            },
            {
              label: '农工党',
              query: {
                'payload.政治面貌_eq': '农工党',
              },
            },
            {
              label: '致公党',
              query: {
                'payload.政治面貌_eq': '致公党',
              },
            },
            {
              label: '九三学社',
              query: {
                'payload.政治面貌_eq': '九三学社',
              },
            },
            {
              label: '无党派（经认定）',
              query: {
                'payload.政治面貌_eq': '无党派（经认定）',
              },
            },
            {
              label: '共青团员',
              query: {
                'payload.政治面貌_eq': '共青团员',
              },
            },
            {
              label: '群众',
              query: {
                'payload.政治面貌_eq': '群众',
              },
            },
          ],
        },
      ],
    }));

    const taIndexView = ref<any>(null);

    const remove = async (record: any) => {
      const data: any = {};

      if (props.departmentId) {
        data.department_ids =
          record.departmentIds.length > 1 ? record.departmentIds : [props.departmentId];
      }
      if (props.dutyId) {
        data.duty_ids = [props.dutyId];
      }

      await props.store.sendMemberAction({
        id: record.id,
        action: 'remove',
        config: {
          data,
        },
      });
      taIndexView.value?.silenceRefresh();
    };

    const memberTemplate = ref<any>({ form: {} });
    const fetchTemplates = async () => {
      const { data } = await new FormsUserSetableTemplateApi({
        parents: [{ type: 'member', id: 'opm' }],
      }).find();
      memberTemplate.value = data;
    };
    fetchTemplates();

    const { mergeFormItem } = useProcessFields();
    const mergedMemberTemplate = computed(() => {
      const memberIdentityForm = props.ownershipRecord?.member_identity?.form;
      if (memberIdentityForm && Object.keys(memberIdentityForm).length > 0) {
        if (memberIdentityForm.setting?.confs?.[0].conf) {
          return mergeFormItem([memberTemplate.value, memberIdentityForm.setting.confs[0].conf]);
        }
        return mergeFormItem([memberTemplate.value, { form: { ...memberIdentityForm } }]);
      }
      return mergeFormItem([memberTemplate.value]);
    });

    const editRecord = ref<any>({});
    const visibleDrawer = ref(false);

    const onCreate = () => {
      editRecord.value = props.store.new({
        department_id: props.departmentId,
        duty_id: props.dutyId,
        ownership_id: props.ownershipRecord?.id,
      });
      visibleDrawer.value = true;
    };

    const afterSave = () => {
      taIndexView.value?.silenceRefresh();
    };

    const route = useRoute();
    const router = useRouter();
    const onShow = (record: any) => {
      if (route.meta.memberOpen == 'tab') {
        router.push(`/opm/manage/ownerships/${props.ownershipRecord.id}/members/${record.id}`);
      } else {
        window.open(
          `${process.env.VUE_APP_DOMAIN}/opm/manage/ownerships/${props.ownershipRecord.id}/members/${record.id}`,
        );
      }
    };

    const importer = ref<any>(null);
    const onImport = () => {
      importer.value?.onShow();
    };
    const afterImport = () => {
      taIndexView.value?.silenceRefresh();
    };

    const exporter = ref<any>(null);
    const onExport = () => {
      exporter.value.onHeaders();
    };

    return {
      ...toRefs(props),
      config,
      modes,
      changeMode,
      currentMode,
      remove,
      taIndexView,

      mergedMemberTemplate,
      editRecord,
      visibleDrawer,
      onCreate,
      afterSave,

      onShow,
      exporter,
      onExport,

      importer,
      onImport,
      afterImport,
    };
  },
});
export default ComOpmMembersIndex;
</script>

<template lang="pug">
.com-opm-members-index.w-full.h-full
  TaIndexView(:config='config', ref='taIndexView', @onShow='onShow')
    template(#header)
      slot(name='header-actions')
        .flex.items-center
          .h-9.px-4.text-white.font-medium.rounded.cursor-pointer.bg-primary.mr-3(@click='onCreate()')
            .flex.items-center.h-full
              TaIcon.w-4.h-4.mr-1(type='outline/plus')
              .text-sm 添加人员
          ComOpmCreateEmployInvite.mr-3(:ownershipRecord='ownershipRecord', :departmentId='departmentId', :dutyId='dutyId')
          .flex.items-center.h-9.px-4.text-white.font-medium.rounded.cursor-pointer.bg-teal-400.mr-3(
            @click='onImport()'
            class='hover:bg-teal-500'
          )
            TaIcon.w-4.h-4(type='outline/save')
            span.pl-1 批量导入
          .flex.items-center.py-2.px-4.text-white.font-medium.rounded.cursor-pointer.bg-orange-400(
            class='hover:bg-orange-500',
            @click='onExport()'
          )
            TaIcon.w-4.h-4(type='outline/upload')
            span.pl-1 导出列表
    template(#right-actions)
      .cursor-pointer(v-for='mode in modes', @click='changeMode(mode)', v-if='departmentId && !dutyId')
        .flex.items-center(v-if='mode.key != currentMode.key')
          TaIcon.mr-1.w-4.h-4.text-primary(type='outline/org')
          .text-xs.mr-1.text-primary {{ mode.text }}
          TaIcon.w-4.h-4.text-gray-400(type='outline/switch-horizontal')
    template(#bodyCell='{ record, column, text, index  }')
      template(v-if='column.title === "操作"')
        slot(name='oper',:record='record')
          .flex.items-center
            TaPopoverConfirm(
              title='移除',
              :content='`确认将${record.user?.name || record.user?.account}移除?`',
              @confirm='() => remove(record)'
            )
              .text-primary.text-sm.cursor-pointer(@click.stop='') 移除
  TaTemplateFormWithActionsDrawer(
    v-model:visible='visibleDrawer',
    :closable='false',
    :template='mergedMemberTemplate',
    :record='editRecord',
    title='添加人员',
    width='1100px',
    @afterSave='afterSave',
  )
  .hidden
    TaExport(
      ref='exporter',
      :store='store',
      :template='ownershipRecord.member_form',
    )
    TaImport(
      ref='importer',
      :store='store'
      :template='ownershipRecord.member_form',
      @success='afterImport()'
    )

</template>

<style lang="stylus" scoped>
.com-opm-members-index
  .text-primary
    color: $primary-color;
  .bg-primary
    background: $primary-color;

  .tag__actions
    .btn
      @apply px-4 py-2 rounded-md text-sm text-white font-medium flex items-center;
</style>
