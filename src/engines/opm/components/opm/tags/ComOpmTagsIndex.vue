<script lang="ts">
import { defineComponent, computed, ref, toRefs } from 'vue';
import ComOpmTagsShow from './ComOpmTagsShow.vue';
import ComOpmMembersIndex from '../members/ComOpmMembersIndex.vue';

const ComOpmTagsIndex = defineComponent({
  name: 'ComOpmTagsIndex',
  components: {
    ComOpmTagsShow,
    ComOpmMembersIndex,
  },
  props: {
    store: { type: Object, required: true },
    memberStore: { type: Object, required: true },
    ownershipRecord: { type: Object, required: true },
  },
  setup(props) {
    const taindexview = ref<any>(null);

    const config = computed(() => ({
      recordName: '标签',
      store: props.store,
      pagination: {
        perPage: 15,
        hideOnSinglePage: false,
      },
      template: 'res_tag#opm',
      detail: {
        mode: 'drawer',
        width: '1100px',
      },
      mode: 'table',
      actions: [
        { key: 'create', enabled: false },
        { key: 'export', enabled: false },
        { key: 'import', enabled: false },
        { key: 'update', enabled: false },
        { key: 'delete', enabled: false },
      ],
      table: {
        scroll: { y: 'auto' },
        pushColumns: [{ dataIndex: 'oper', title: '操作' }],
      },
      searcherSimpleOptions: [{ key: 'name', label: '标签名称', type: 'string' }],
    }));

    const dispatchTaIndexView = (action: string, payload?: any) => {
      switch (action) {
        case 'create':
          taindexview.value?.slotActions?.onCreate?.();
          break;
        case 'delete':
          taindexview.value?.slotActions?.onDelete?.(payload);
          break;
        case 'edit':
          taindexview.value?.slotActions?.onEdit?.(payload);
          break;
        case 'detail':
          taindexview.value?.onRowClick?.(payload);
          break;
      }
    };

    const importer = ref<any>(null);
    const onImport = () => {
      importer.value?.onShow();
    };
    const afterImport = () => {
      taindexview.value?.silenceRefresh();
    };

    const exporter = ref<any>(null);
    const onExport = () => {
      exporter.value.onHeaders();
    };

    return {
      ...toRefs(props),
      config,
      taindexview,
      dispatchTaIndexView,

      onImport,
      importer,
      afterImport,
      exporter,
      onExport,
    };
  },
});

export default ComOpmTagsIndex;
</script>

<template lang="pug">
.com-opm-manage-tags-index
  TaIndexView(
    ref='taindexview',
    :config='config'
  )
    template(#header)
      .tag__actions.flex.items-center.space-x-2
        .btn.bg-primary-500.cursor-pointer.transition-all(
          @click='dispatchTaIndexView("create")',
        )
          TaIcon.w-4.h-4(type='outline/plus')
          span.pl-1 新建
        .btn.bg-teal-400.cursor-pointer.transition-all(
          @click='onImport()'
          class='hover:bg-teal-500'
        )
          TaIcon.w-4.h-4(type='outline/save')
          span.pl-1 批量导入
        .btn.bg-orange-400.cursor-pointer.transition-all(
          class='hover:bg-orange-500',
          @click='onExport()'
        )
          TaIcon.w-4.h-4(type='outline/upload')
          span.pl-1 导出列表
    template(#bodyCell='{record,column,text}')
      template(v-if='column.dataIndex[0] === "name"')
        .flex.items-center
          ComColorfulTag.mr-2.text-xs(:color='record.color', :text='text')
          span {{ text }}
      template(v-else-if='column.dataIndex[0] === "oper"')
        .flex.items-center.gap-x-3
          .text-primary-500.cursor-pointer(@click.stop='dispatchTaIndexView("edit",record)') 编辑
          .text-gray-200 |
          .text-primary-500.cursor-pointer(@click.stop='dispatchTaIndexView("detail",record)') 查看
          .text-gray-200 |
          TaPopoverConfirm(
            title='删除',
            content='您确认删除该数据吗',
            @confirm='dispatchTaIndexView("delete",record)'
            @click.stop=''
          )
            .text-primary-500.cursor-pointer 删除
    template(#detail='{ record, onClose }')
      .p-4.bg-gray-100.h-full
        .p-4.bg-white.rounded-lg.h-full
          ComOpmTagsShow(
            :store='store',
            :record='record',
            :memberStore='memberStore',
            :ownershipRecord='ownershipRecord'
          )

  .hidden
    TaImport(
      ref='importer',
      :store='store'
      :template='taindexview?.localTemplate',
      @success='afterImport()'
    )
    TaExport(
      ref='exporter',
      :store='store',
      :template='taindexview?.localTemplate',
    )
</template>

<style lang="stylus" scoped>
.com-opm-manage-tags-index
  height 100%
  width 100%
  .tag__actions
    .btn
      @apply px-4 py-2 rounded text-sm text-white font-medium flex items-center;
</style>
