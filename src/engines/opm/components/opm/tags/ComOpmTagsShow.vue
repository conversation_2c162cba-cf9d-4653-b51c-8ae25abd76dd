<script lang="ts">
import { VObject } from '@/lib/vails';
import { message } from 'ant-design-vue';
import { computed, defineComponent, ref, toRefs } from 'vue';

const ComOpmTagsShow = defineComponent({
  name: 'ComOpmTagsShow',
  components: {},
  props: {
    store: { type: Object, required: true },
    record: { type: Object, required: true },
    memberStore: { type: Object, required: true },
    ownershipRecord: { type: Object, required: true },
  },
  setup(props) {
    const api = ref<any>(null);
    const taindexview = ref<any>(null);
    const selectedMemberIds = ref<number[]>([]);

    const config = computed(() => ({
      store: props.memberStore,
      template: 'member#opm_members',
      mode: 'table',
      params: {
        q: {
          res_tags_id_eq: props.record.id,
        },
      },
      table: {
        scroll: { y: 'auto' },
        pushColumns: [
          {
            title: '操作',
            width: '80px',
          },
        ],
      },
      searcherSimpleOptions: [{ key: 'user.name', label: '姓名', type: 'string' }],
    }));

    const removeMembers = (member: VObject) => {
      props.store
        .sendMemberAction({
          id: props.record.id,
          action: 'remove_members',
          config: {
            data: {
              member_ids: [member.id],
            },
          },
        })
        .then(() => {
          message.success('移除成功');
          taindexview.value.silenceRefresh();
        })
        .catch((e: any) => {
          message.error('移除失败');
          console.log(e);
        });
    };

    const addMembers = (memberIds: number[]) => {
      props.store
        .sendMemberAction({
          id: props.record.id,
          action: 'add_members',
          config: {
            data: {
              member_ids: memberIds,
            },
          },
        })
        .then(() => {
          taindexview.value?.silenceRefresh();
          message.success('新增成功');
        })
        .catch((e: any) => {
          message.error('新增失败');
          console.log(e);
        });
    };
    const addTagMembers = () => {
      api.value?.open?.();
    };

    const path = computed(() => props.memberStore.api.indexPath);
    const tableItems = [
      {
        name: '姓名',
        type: 'string',
        search: true,
        data_index: 'user.name',
      },
    ];

    const onShow = (record: any) => {
      window.open(
        `${process.env.VUE_APP_DOMAIN}/opm/manage/ownerships/${props.ownershipRecord.id}/members/${record.id}`,
        '_blank',
      );
    };

    const exporter = ref<any>(null);
    const onExport = () => {
      exporter.value.onHeaders();
    };

    const importer = ref<any>(null);
    const onImport = () => {
      importer.value?.onShow();
    };
    const afterImport = () => {
      // taindexview.value?.silenceRefresh();
    };

    return {
      ...toRefs(props),
      config,
      removeMembers,
      addMembers,
      api,
      path,
      taindexview,
      addTagMembers,
      selectedMemberIds,
      tableItems,
      onShow,
      exporter,
      onExport,
      importer,
      onImport,
      afterImport,
    };
  },
});
export default ComOpmTagsShow;
</script>

<template lang="pug">
.com-opm-manage-tags-show
  TaApiNoDisplayField(
    ref='api',
    v-model:value='selectedMemberIds'
    :path='path',
    :attrs='["name"]',
    :tableItems='tableItems',
    :multiple='true',
    :ransackStr='JSON.stringify({ res_tags_id_not_eq:record.id })',
    @ok='addMembers(selectedMemberIds)',
  )
  TaIndexView(:config='config' ref='taindexview', @onShow='onShow')
    template(#bodyCell='{ record, column, text, index  }')
      template(v-if='column.title === "操作"')
        .flex.items-center
          TaPopoverConfirm(
            title='移除',
            :content='`确认将${record.user?.name || record.user?.account}移除?`',
            @confirm='removeMembers(record)'
            @click.stop=''
          )
            .text-primary.text-sm.cursor-pointer 移除
    template(#header)
      .tag__actions.flex.items-center.space-x-2
        .btn.bg-primary-500.cursor-pointer.transition-all(@click.stop='addTagMembers')
          TaIcon.w-4.h-4(type='outline/plus')
          span.pl-1 添加人员
        .btn.bg-orange-400.cursor-pointer.transition-all(
          class='hover:bg-orange-500',
          @click='onExport()'
        )
          TaIcon.w-4.h-4(type='outline/save')
          span.pl-1 导出列表
  .hidden
    TaExport(
      ref='exporter',
      :store='memberStore',
      :template='ownershipRecord.member_form',
    )
    TaImport(
      ref='importer',
      :store='memberStore'
      :template='ownershipRecord.member_form',
      @success='afterImport()'
    )
</template>

<style lang="stylus" scoped>
.com-opm-manage-tags-show
  height 100%
  .tag__actions
    .btn
      @apply px-4 py-2 rounded-md text-sm text-white font-medium flex items-center;
</style>
