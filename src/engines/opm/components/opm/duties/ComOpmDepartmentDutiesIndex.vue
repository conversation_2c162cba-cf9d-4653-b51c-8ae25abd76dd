<script lang="ts">
import { defineComponent, toRefs, computed, ref } from 'vue';
import ComOpmMembersIndex from '../members/ComOpmMembersIndex.vue';
import { VStore } from '@/lib/vails';
import { OpmManageMembersApi } from '@/engines/opm/opm-core/apis/opm/manage/members.api';
import { OpmMemberModel } from '@/engines/opm/opm-core/models/opm/members';

const ComOpmDepartmentDutiesIndex = defineComponent({
  name: 'ComOpmDepartmentDutiesIndex',
  components: {
    ComOpmMembersIndex,
  },
  props: {
    store: { type: Object, required: true },
    departmentId: { type: Number, default: null },
    params: { type: Object, default: () => ({}) },
    ownershipRecord: { type: Object, default: () => {} },
  },
  setup(props) {
    const modes = [
      {
        text: '看本级',
        key: 'all_level',
      },
      {
        text: '看全部',
        key: 'self_level',
      },
    ];

    const currentMode = ref(modes[0]);
    const changeMode = (mode: any) => {
      currentMode.value = mode;
    };

    const params = computed(() => {
      if (currentMode.value.key == 'all_level') {
        return {
          q: {
            department_ancestor_id_eq: props.departmentId,
          },
        };
      }

      return {
        q: {
          department_id_eq: props.departmentId,
        },
      };
    });

    const config = computed(() => ({
      store: props.store,
      mode: 'table',
      template: 'duty#opm_department',
      params: params.value,
      table: {
        scroll: { y: 'auto' },
        pushColumns: [{ title: '操作', width: '80px' }],
      },
      // searcherSimpleOptions: [{ key: 'name', label: '岗位名称', type: 'string' }],
    }));

    const drawerVisible = ref(false);

    const memberStore = new VStore(
      new OpmManageMembersApi({
        parents: props.store.api.parents,
      }),
      OpmMemberModel,
    );

    const currentRecord = ref({});
    const onShow = (record: any) => {
      currentRecord.value = record;
      drawerVisible.value = true;
    };

    return {
      ...toRefs(props),
      config,
      modes,
      changeMode,
      currentMode,

      drawerVisible,
      memberStore,
      onShow,
      currentRecord,
    };
  },
});
export default ComOpmDepartmentDutiesIndex;
</script>

<template lang="pug">
.com-opm-department-duties-index.w-full.h-full
  TaIndexView(:config='config')
    template(#right-actions)
      .cursor-pointer(v-for='mode in modes', @click='changeMode(mode)', v-if='departmentId')
        .flex.items-center(v-if='mode.key != currentMode.key')
          TaIcon.mr-1.w-4.h-4.text-primary(type='outline/org')
          .text-xs.mr-1.text-primary {{ mode.text }}
          TaIcon.w-4.h-4.text-gray-400(type='outline/switch-horizontal')
    template(#bodyCell='{ record, column, text, index  }')
      template(v-if='column.title === "操作"')
        .flex.items-center(@click.stop='onShow(record)')
          .text-primary.text-sm.cursor-pointer 查看
a-drawer(
  v-model:visible='drawerVisible',
  title='岗位人员',
  :closable='false'
  width='1100px',
  :bodyStyle='{background: "#F3F4F6"}'
)
  .bg-white.p-4.rounded-lg.h-full
    ComOpmMembersIndex(
      v-if='drawerVisible'
      :store='memberStore',
      :departmentId='currentRecord.department_id',
      :dutyId='currentRecord.duty_id',
      :ownershipRecord='ownershipRecord'
    )
</template>

<style lang="stylus" scoped>
.com-opm-department-duties-index
  .text-primary
    color: $primary-color;
</style>
