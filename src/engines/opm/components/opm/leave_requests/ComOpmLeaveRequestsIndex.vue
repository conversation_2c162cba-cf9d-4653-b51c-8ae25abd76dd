<script lang="ts">
import { TaIndexViewTabInterface } from '@/components/global/ta-component/TaIndexView/types';
import { VObject } from '@/lib/vails/model';
import { computed, defineComponent, ref, toRefs } from 'vue';
import ComOpmLeaveRequestsShow from './ComOpmLeaveRequestsShow.vue';

const ComOpmLeaveRequestsIndex = defineComponent({
  name: 'ComOpmLeaveRequestsIndex',
  components: {
    ComOpmLeaveRequestsShow,
  },
  props: {
    store: { type: Object, required: true },
    balanceStore: { type: Object, required: true },
  },
  setup(props, { emit }) {
    const config = computed(() => ({
      recordName: '加班调休假期管理',
      store: props.store,
      // pagination: {
      //   perPage: 15,
      //   showPageSizeChanger: false,
      //   hideOnSinglePage: false,
      //   showSizeChanger: false,
      // }
      template: 'opm_leave_request',
      detail: {
        mode: 'auto',
        //   mode: 'drawer',
        width: '1100px',
      },
      mode: 'table',
      // editApi:
      // showCount: true,
      actions: [
        //  { key: 'create', enabled: true },
        //  { key: 'update', enabled: true },
        //  { key: 'delete', enabled: true },
        //  { key: 'import', enabled: true },
        //  { key: 'export', enabled: true },
      ],
      table: {
        scroll: { y: 'auto' },
        // columns: [{ dataIndex: 'id', title: 'ID' }],
      },
      list: {
        scroll: { y: 'auto' },
      },
      // selection: {
      //   showByDefault: false;
      //   showSwitch: false;
      // };
      // searcherSimpleOptions: [{key: 'title', label: '标题', type: 'string' }],
    }));

    const statistics = ref({
      overtime: 0,
      comp_time: 0,
      annual_leave: 0,
      all: 0,
    });

    const tabs = computed<TaIndexViewTabInterface[]>(() => [
      {
        key: 'all',
        label: '全部',
        num: statistics.value.all,
        query: {},
        balance_query: { category: 'all' },
      },
      {
        key: 'overtime',
        label: '加班',
        num: statistics.value.overtime,
        query: { type_eq: 'Opm::OvertimeRequest' },
        balance_query: { category: 'comp_time' },
      },
      {
        key: 'comp_time',
        label: '调休',
        num: statistics.value.comp_time,
        query: { type_eq: 'Opm::TimeOffRequest' },
        balance_query: { category: 'comp_time' },
      },
      // {
      //   key: 'annual_leave',
      //   label: '年假',
      //   num: statistics.value.annual_leave,
      //   query: { type_eq: 'Opm::AnnualLeaveRequest' },
      //   balance_query: { category: 'annual_leave' },
      // },
    ]);
    const query = ref({});
    const onIndex = (data: VObject) => {
      statistics.value = data.statistics;
    };
    const tabChange = (data: VObject) => {
      query.value = data.balance_query;
      // fetchBalance();
    };
    const fetchBalance = () => {
      props.balanceStore
        .sendCollectionAction({
          action: 'summary',
          config: {
            sub_q: {
              ...query.value,
            },
          },
        })
        .then((res: any) => {
          console.log('res: ', res);
        });
    };
    return {
      ...toRefs(props),
      config,
      tabs,
      onIndex,
      tabChange,
    };
  },
});

export default ComOpmLeaveRequestsIndex;
</script>

<template lang="pug">
.com-opm-admin-leave-requests-index
  TaIndexView(:config='config', :tabs='tabs', @onIndex='onIndex', @tabChange='tabChange')
    //- template(#card='{ record }')
    //- template(#table)
    //-    a-table-column(title='名称' dataIndex='record')
    //- template(#detail='{ record, onClose }')
    //-   ComOpmLeaveRequestsShow(
    //-     v-if='record.id'
    //-     :store='store'
    //-     :extendRoute='`/opm/admin/leave_requests/${record.id}`'
    //-     :editable='editable'
    //-     @afterDelete='onClose'
    //-     @afterExtend='onClose'
    //-   )
</template>

<style lang="stylus" scoped>
.com-opm-admin-leave-requests-index
  height 100%
  width 100%
</style>
