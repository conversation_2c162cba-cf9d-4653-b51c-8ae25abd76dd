<script lang="ts">
import { ref, defineComponent, toRefs, computed } from 'vue';
const ComViewSwitch = defineComponent({
  name: 'ComViewSwitch',
  components: {},
  props: {
    activeSwitchKey: {
      type: String,
      default: 'table',
    },
  },
  setup(props, { emit }) {
    const switchMap = {
      tree: {
        name: '树形视图',
        icon: 'tree',
      },
      table: {
        name: '表格视图',
        icon: 'table',
      },
    };
    const _activeSwitchKey = computed({
      get() {
        return props.activeSwitchKey as keyof typeof switchMap;
      },
      set(val: keyof typeof switchMap) {
        emit('update:activeSwitchKey', val);
      },
    });

    const toggleSwitch = () => {
      _activeSwitchKey.value = _activeSwitchKey.value === 'table' ? 'tree' : 'table';
    };
    return {
      ...toRefs(props),
      switchMap,
      _activeSwitchKey,
      toggleSwitch,
    };
  },
});
export default ComViewSwitch;
</script>

<template lang="pug">
.com-view-switch.px-4.py-2.rounded.bg-white.flex.items-center.space-x-2.cursor-pointer(@click.stop='toggleSwitch')
  //- .w-4.h-4
  //-   img.w-full.h-full(src='@/assets/table.svg',v-if='_activeSwitchKey === "table"')
  //-   img.w-full.h-full(src='@/assets/tree.svg', v-else)
  TaIcon.w-4.h-4.text-primary(type='outline/org-table', v-if='_activeSwitchKey === "table"')
  TaIcon.w-4.h-4.text-primary(type='outline/org-tree', v-else)
  .text-sm.text-gray-900 {{ switchMap[_activeSwitchKey].name }}
  TaIcon.w-4.h-4(type='outline/switch-horizontal')
</template>

<style lang="stylus" scoped></style>
