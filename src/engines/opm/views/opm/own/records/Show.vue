<script lang="ts">
  import ComOpmRecordsShow from '@/engines/opm/components/opm/records/ComOpmRecordsShow.vue';
  import { defineComponent, toRefs, onMounted, computed } from 'vue';
  import { OpmOwnRecordsApi } from '@/engines/opm/opm-core/apis/opm/own/records.api';
  import { OpmRecordModel } from '@/engines/opm/opm-core/models/opm/own/records';
  import { VStore } from '@/lib/vails';
  import { useRoute } from 'vue-router';

  const OpmOwnRecordsShow = defineComponent({
    name: 'OpmOwnRecordsShow',
    components: {
    ComOpmRecordsShow,
  },
    setup(props) {
      const route = useRoute();
    
    const store = new VStore(new OpmOwnRecordsApi(), OpmRecordModel);
    
    const breadcrumbs = computed(() => [
      
    { label: '', route: '/opm/own/records' },
          ]);

  onMounted(() => {
    store.find(Number(route.params.recordId));
  });

  return {
    ...toRefs(props),
    store,
    record: store.record,
    breadcrumbs,
  };
  },
});

  export default OpmOwnRecordsShow;
</script>

<template lang="pug">
.opm-own-records-show
  ComOpmRecordsShow(v-if='record.id', :store='store', :breadcrumbs='breadcrumbs')
</template>

<style lang="stylus" scoped>
.opm-own-records-show
  height 100%
  width 100%
  .breadcrumbs
    padding 0 24px
</style>
