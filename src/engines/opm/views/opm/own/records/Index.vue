<script lang="ts">
import ComOpmRecordsIndex from '@/engines/opm/components/opm/records/ComOpmRecordsIndex.vue';
import { defineComponent } from '@vue/runtime-core';
import { OpmOwnRecordsApi } from '@/engines/opm/opm-core/apis/opm/own/records.api';
import { OpmRecordModel } from '@/engines/opm/opm-core/models/opm/own/records';
import { VStore } from '@/lib/vails';

import usePolicy from '@/components/global/ta-component/ta-template-form-core/usePolicy';
import { useRoute } from 'vue-router';

const OpmOwnRecordsIndex = defineComponent({
  name: 'OpmOwnRecordsIndex',
  components: {
    ComOpmRecordsIndex,
  },
  setup() {
    usePolicy();



    const ownershipId = Number(useRoute().params.ownershipId);
    const store = new VStore(new OpmOwnRecordsApi({
      parents: [{ type: 'ownerships', id: ownershipId }],
      params: {
        q: {
          type_eq: 'Opm::EduRecord'
        }
      }
    }), OpmRecordModel);


    return {
      store,

    };
  },
});

export default OpmOwnRecordsIndex;
</script>

<template lang="pug">
.opm-own-records-index.flex.flex-col(class='bg-[#f0f2fc]')
  header.flex.items-center.justify-between.mb-4.flex-shrink-0.pt-4
    .title.text-xl.font-semibold.text-primary-900 教育信息
  .flex-grow.h-0.p-4.rounded-lg.bg-white
    ComOpmRecordsIndex(:store='store')
</template>

<style lang="stylus" scoped>
.opm-own-records-index
  height 100%
  width 100%
</style>
