<script lang="ts">
import ComOpmItemsIndex from '@/engines/opm/components/opm/items/ComOpmItemsIndex.vue';
import { defineComponent } from '@vue/runtime-core';
import { OpmOwnItemsApi } from '@/engines/opm/opm-core/apis/opm/own/items.api';
import { OpmItemModel } from '@/engines/opm/opm-core/models/opm/own/items';
import { VStore } from '@/lib/vails';

import usePolicy from '@/components/global/ta-component/ta-template-form-core/usePolicy';
import { OpmTechItemModel } from '@/engines/opm/opm-core/models/opm/manage/items';

const OpmOwnItemsIndex = defineComponent({
  name: 'OpmOwnItemsIndex',
  components: {
    ComOpmItemsIndex,
  },
  setup() {
    usePolicy();




    const store = new VStore(new OpmOwnItemsApi({
      params: {
        q: {
          type_eq: 'Opm::TechItem'
        }
      }
    }), OpmTechItemModel);

    const title = '我的证书'
    const template = 'opm_tech_item'

    return {
      store,
      title,
      template,
    };
  },
});

export default OpmOwnItemsIndex;
</script>

<template lang="pug">
.opm-own-items-index.flex.flex-col(class='bg-[#f0f2fc]')
  header.flex.items-center.justify-between.mb-4.flex-shrink-0.pt-4
    .title.text-xl.font-semibold.text-primary-900 {{ title }}
  .flex-grow.h-0.p-4.rounded-lg.bg-white
    ComOpmItemsIndex.h-full(:store='store' :title='title' :template='template')
</template>

<style lang="stylus" scoped>
.opm-own-items-index
  height 100%
  width 100%
</style>
