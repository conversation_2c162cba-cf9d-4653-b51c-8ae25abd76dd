<script lang="ts">
import ComOpmJobTitlesIndex from '@/engines/opm/components/opm/job_titles/ComOpmJobTitlesIndex.vue';
import { defineComponent } from '@vue/runtime-core';
import { OpmOwnJobTitlesApi } from '@/engines/opm/opm-core/apis/opm/own/job_titles.api';
import { OpmJobTitleModel } from '@/engines/opm/opm-core/models/opm/own/job_titles';
import { VStore } from '@/lib/vails';

import usePolicy from '@/components/global/ta-component/ta-template-form-core/usePolicy';

const OpmOwnJobTitlesIndex = defineComponent({
  name: 'OpmOwnJobTitlesIndex',
  components: {
    ComOpmJobTitlesIndex,
  },
  setup() {
    usePolicy();

    

    
    const store = new VStore(new OpmOwnJobTitlesApi(), OpmJobTitleModel);
    

    return {
      store,
      
    };
  },
});

export default OpmOwnJobTitlesIndex;
</script>

<template lang="pug">
.opm-own-job-titles-index
  ComOpmJobTitlesIndex(:store='store' )
</template>

<style lang="stylus" scoped>
.opm-own-job-titles-index
  height 100%
  width 100%
</style>
