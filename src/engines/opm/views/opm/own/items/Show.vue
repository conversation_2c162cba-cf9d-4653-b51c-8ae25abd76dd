<script lang="ts">
  import ComOpmItemsShow from '@/engines/opm/components/opm/items/ComOpmItemsShow.vue';
  import { defineComponent, toRefs, onMounted, computed } from 'vue';
  import { OpmOwnItemsApi } from '@/engines/opm/opm-core/apis/opm/own/items.api';
  import { OpmItemModel } from '@/engines/opm/opm-core/models/opm/own/items';
  import { VStore } from '@/lib/vails';
  import { useRoute } from 'vue-router';

  const OpmOwnItemsShow = defineComponent({
    name: 'OpmOwnItemsShow',
    components: {
    ComOpmItemsShow,
  },
    setup(props) {
      const route = useRoute();
    
    const store = new VStore(new OpmOwnItemsApi(), OpmItemModel);
    
    const breadcrumbs = computed(() => [
      
    { label: '', route: '/opm/own/items' },
          ]);

  onMounted(() => {
    store.find(Number(route.params.itemId));
  });

  return {
    ...toRefs(props),
    store,
    record: store.record,
    breadcrumbs,
  };
  },
});

  export default OpmOwnItemsShow;
</script>

<template lang="pug">
.opm-own-items-show
  ComOpmItemsShow(v-if='record.id', :store='store', :breadcrumbs='breadcrumbs')
</template>

<style lang="stylus" scoped>
.opm-own-items-show
  height 100%
  width 100%
  .breadcrumbs
    padding 0 24px
</style>
