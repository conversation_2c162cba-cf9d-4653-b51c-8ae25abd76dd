<script lang="ts">
import { defineComponent } from 'vue';
import ComOpmOwnMemberShow from '@/engines/opm/components/opm/own/members/ComOpmOwnMemberShow.vue';
import { useRoute } from 'vue-router';
import { VStore } from '@/lib/vails';
import { OpmOwnMemberApi } from '@/engines/opm/opm-core/apis/opm/own/members.api';
import { OpmMemberModel } from '@/engines/opm/opm-core/models/opm/members';
import { OpmOwnOwnershipsApi } from '@/engines/opm/opm-core/apis/opm/own/ownerships.api';
import { OpmOwnershipModel } from '@/engines/opm/opm-core/models/opm/ownerships';
import { OpmOwnItemsApi } from '@/engines/opm/opm-core/apis/opm/own/items.api';
import { OpmItemModel } from '@/engines/opm/opm-core/models/opm/own/items';
import { OpmOwnJobTitlesApi } from '@/engines/opm/opm-core/apis/opm/own/job_titles.api';
import { OpmJobTitleModel } from '@/engines/opm/opm-core/models/opm/admin/job_titles';
import { OpmEduItemModel, OpmTechItemModel, OpmWinItemModel } from '@/engines/opm/opm-core/models/opm/manage/items';
import { OpmRecordModel } from '@/engines/opm/opm-core/models/opm/admin/records';
import { OpmOwnRecordsApi } from '@/engines/opm/opm-core/apis/opm/own/records.api';

export default defineComponent({
  name: 'OpmOwnMemberShow',
  components: { ComOpmOwnMemberShow },
  setup() {
    const route = useRoute();
    const ownershipId = Number(route.params.ownershipId);

    const ownershipStore = new VStore(new OpmOwnOwnershipsApi(), OpmOwnershipModel);
    ownershipStore.find(ownershipId);

    const store = new VStore(
      new OpmOwnMemberApi({
        parents: [
          {
            type: 'ownerships',
            id: ownershipId,
          },
        ],
      }),
      OpmMemberModel,
    );
    store.find(0);

    const memberTitleStore = new VStore(new OpmOwnJobTitlesApi({
      parents: [{ type: 'ownerships', id: ownershipId }, { type: 'member' }],
    }), OpmJobTitleModel);

    const eduItemStore = new VStore(new OpmOwnItemsApi({
      params: {
        q: {
          type_eq: 'Opm::EduItem',
          create_instance_state_eq: 'completed',
        }
      }
    }), OpmEduItemModel);
    const winItemStore = new VStore(new OpmOwnItemsApi({
      params: {
        q: {
          type_eq: 'Opm::WinItem',
          create_instance_state_eq: 'completed',
        }
      }
    }), OpmWinItemModel);
    const techItemStore = new VStore(new OpmOwnItemsApi({
      params: {
        q: {
          type_eq: 'Opm::TechItem',
          create_instance_state_eq: 'completed',
        }
      }
    }), OpmTechItemModel);

    const eduRecordStore = new VStore(new OpmOwnRecordsApi({
      parents: [{ type: 'ownerships', id: ownershipId }],
      params: {
        q: {
          type_eq: 'Opm::EduRecord',
          create_instance_state_eq: 'completed',
        }
      }
    }), OpmRecordModel);

    return {
      ownershipRecord: ownershipStore.record,
      record: store.record,
      store,
      eduItemStore,
      memberTitleStore,
      winItemStore,
      techItemStore,
      eduRecordStore,
    };
  },
});
</script>

<template lang="pug">
.opm-own-member-show.w-full.h-full.bg-primary-50
  ComOpmOwnMemberShow(
    :store='store',
    :record='record',
    :ownershipRecord='ownershipRecord',
    :eduItemStore='eduItemStore',
    :winItemStore='winItemStore'
    :memberTitleStore='memberTitleStore',
    :techItemStore='techItemStore'
    :eduRecordStore='eduRecordStore'
    v-if='record.id && ownershipRecord.id'
  )
</template>
