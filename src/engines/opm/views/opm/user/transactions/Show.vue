<script lang="ts">
import ComOpmTransactionsShow from '@/engines/opm/components/opm/transactions/ComOpmTransactionsShow.vue';
import { OpmUserTransactionsApi } from '@/engines/opm/opm-core/apis/opm/user/transactions.api';
import { OpmTransactionModel } from '@/engines/opm/opm-core/models/opm/user/transactions';
import { VStore } from '@/lib/vails';
import { computed, defineComponent, onMounted, toRefs } from 'vue';
import { useRoute } from 'vue-router';

const OpmUserTransactionsShow = defineComponent({
  name: 'OpmUserTransactionsShow',
  components: {
    ComOpmTransactionsShow,
  },
  setup(props) {
    const route = useRoute();

    const store = new VStore(new OpmUserTransactionsApi(), OpmTransactionModel);

    const breadcrumbs = computed(() => [{ label: '', route: '/opm/user/transactions' }]);

    onMounted(() => {
      store.find(Number(route.params.transactionId));
    });

    return {
      ...toRefs(props),
      store,
      record: store.record,
      breadcrumbs,
    };
  },
});

export default OpmUserTransactionsShow;
</script>

<template lang="pug">
.opm-user-transactions-show
  ComOpmTransactionsShow(v-if='record.id', :store='store', :breadcrumbs='breadcrumbs')
</template>

<style lang="stylus" scoped>
.opm-user-transactions-show
  height 100%
  width 100%
  .breadcrumbs
    padding 0 24px
</style>
