<script lang="ts">
import ComOpmTransactionsIndex from '@/engines/opm/components/opm/transactions/ComOpmTransactionsIndex.vue';
import { OpmUserTransactionsApi } from '@/engines/opm/opm-core/apis/opm/user/transactions.api';
import { OpmTransactionModel } from '@/engines/opm/opm-core/models/opm/user/transactions';
import { VStore } from '@/lib/vails';
import { defineComponent } from '@vue/runtime-core';

import usePolicy from '@/components/global/ta-component/ta-template-form-core/usePolicy';

const OpmUserTransactionsIndex = defineComponent({
  name: 'OpmUserTransactionsIndex',
  components: {
    ComOpmTransactionsIndex,
  },
  setup() {
    usePolicy();

    const store = new VStore(new OpmUserTransactionsApi(), OpmTransactionModel);

    return {
      store,
    };
  },
});

export default OpmUserTransactionsIndex;
</script>

<template lang="pug">
.opm-user-transactions-index
  ComOpmTransactionsIndex(:store='store' )
</template>

<style lang="stylus" scoped>
.opm-user-transactions-index
  height 100%
  width 100%
</style>
