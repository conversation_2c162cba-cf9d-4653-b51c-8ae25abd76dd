<script lang="ts">
  import ComOpmTagsShow from '@/engines/opm/components/opm/tags/ComOpmTagsShow.vue';
  import { defineComponent, toRefs, onMounted, computed } from 'vue';
  import { OpmUserTagsApi } from '@/engines/opm/opm-core/apis/opm/user/tags.api';
  import { OpmTagModel } from '@/engines/opm/opm-core/models/opm/user/tags';
  import { VStore } from '@/lib/vails';
  import { useRoute } from 'vue-router';

  const OpmUserTagsShow = defineComponent({
    name: 'OpmUserTagsShow',
    components: {
    ComOpmTagsShow,
  },
    setup(props) {
      const route = useRoute();
    
    const store = new VStore(new OpmUserTagsApi(), OpmTagModel);
    
    const breadcrumbs = computed(() => [
      
    { label: '', route: '/opm/user/tags' },
          ]);

  onMounted(() => {
    store.find(Number(route.params.tagId));
  });

  return {
    ...toRefs(props),
    store,
    record: store.record,
    breadcrumbs,
  };
  },
});

  export default OpmUserTagsShow;
</script>

<template lang="pug">
.opm-user-tags-show
  ComOpmTagsShow(v-if='record.id', :store='store', :breadcrumbs='breadcrumbs')
</template>

<style lang="stylus" scoped>
.opm-user-tags-show
  height 100%
  width 100%
  .breadcrumbs
    padding 0 24px
</style>
