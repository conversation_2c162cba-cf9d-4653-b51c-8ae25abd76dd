<script lang="ts">
import ComOpmTagsIndex from '@/engines/opm/components/opm/tags/ComOpmTagsIndex.vue';
import { defineComponent } from '@vue/runtime-core';
import { OpmUserTagsApi } from '@/engines/opm/opm-core/apis/opm/user/tags.api';
import { OpmTagModel } from '@/engines/opm/opm-core/models/opm/user/tags';
import { VStore } from '@/lib/vails';

import usePolicy from '@/components/global/ta-component/ta-template-form-core/usePolicy';
import { useRoute } from 'vue-router';
import { OpmUserOwnershipsApi } from '@/engines/opm/opm-core/apis/opm/user/ownerships.api';
import { OpmOwnershipModel } from '@/engines/opm/opm-core/models/opm/ownerships';
import { OpmOwnMemberApi } from '@/engines/opm/opm-core/apis/opm/own/members.api';
import { OpmMemberModel } from '@/engines/opm/opm-core/models/opm/members';

const OpmUserTagsIndex = defineComponent({
  name: 'OpmUserTagsIndex',
  components: {
    ComOpmTagsIndex,
  },
  setup() {
    usePolicy();




    const route = useRoute();
    const ownershipId = Number(route.params.ownershipId);

    const ownershipStore = new VStore(new OpmUserOwnershipsApi(), OpmOwnershipModel);
    ownershipStore.find(ownershipId);

    const store = new VStore(
      new OpmUserTagsApi({
        parents: [{ type: 'ownerships', id: ownershipId }],
      }),
      OpmTagModel,
    );

    const memberStore = new VStore(
      new OpmOwnMemberApi({
        parents: [{ type: 'ownerships', id: ownershipId }],
      }),
      OpmMemberModel,
    );

    return {
      store,
      memberStore,
      ownershipRecord: ownershipStore.record,
    };
  },
});

export default OpmUserTagsIndex;
</script>

<template lang="pug">
.opm-user-tags-index.w-full.h-full(class='bg-[#f0f2fc]')
  .pt-4.h-full.flex.flex-col
    header.text-primary-900.text-xl.font-semibold.mb-5 个人标签
    .p-4.flex-grow.h-0.bg-white.rounded-lg
      ComOpmTagsIndex(
        :store='store',
        :memberStore='memberStore',
        :ownershipRecord='ownershipRecord',
        v-if='ownershipRecord.id'
      )
</template>

<style lang="stylus" scoped>
.opm-user-tags-index
  height 100%
  width 100%
</style>
