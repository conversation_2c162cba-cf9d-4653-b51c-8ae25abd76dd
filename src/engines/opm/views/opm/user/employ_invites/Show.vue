<script lang="ts">
import { defineComponent } from 'vue';
import ComOpmEmployInviteShow from '@/engines/opm/components/opm/employ_invites/ComOpmEmployInviteShow.vue';
import { useRoute } from 'vue-router';
import { VStore } from '@/lib/vails';
import { OpmUserEmployInvitesApi } from '@/engines/opm/opm-core/apis/opm/user/employ_invites.api';
import { OpmEmployInviteModel } from '@/engines/opm/opm-core/models/opm/employ_invites';

export default defineComponent({
  name: 'OpmUserEmployInvitesShow',
  components: {
    ComOpmEmployInviteShow,
  },
  setup() {
    const route = useRoute();
    const employInviteId = Number(route.params.employInviteId);

    const store = new VStore(new OpmUserEmployInvitesApi(), OpmEmployInviteModel);
    store.find(employInviteId);

    return {
      record: store.record,
    };
  },
});
</script>

<template lang="pug">
.opm-user-employ-invites-show.w-full.min-h-full.bg-gray-100
  ComOpmEmployInviteShow(:record='record', v-if='record.id')
</template>
