<script lang="ts">
import ComOpmLeaveRequestsShow from '@/engines/opm/components/opm/leave_requests/ComOpmLeaveRequestsShow.vue';
import { defineComponent, toRefs, onMounted, computed } from 'vue';
import { OpmUserLeaveRequestsApi } from '@/engines/opm/opm-core/apis/opm/user/leave_requests.api';
import { OpmLeaveRequestModel } from '@/engines/opm/models/opm/user/leave_requests';
import { VStore } from '@/lib/vails';
import { useRoute } from 'vue-router';

const OpmUserLeaveRequestsShow = defineComponent({
  name: 'OpmUserLeaveRequestsShow',
  components: {
    ComOpmLeaveRequestsShow,
  },
  setup(props) {
    const route = useRoute();

    const store = new VStore(new OpmUserLeaveRequestsApi(), OpmLeaveRequestModel);

    const breadcrumbs = computed(() => [{ label: '', route: '/opm/user/leave_requests' }]);

    onMounted(() => {
      store.find(Number(route.params.leave_requestId));
    });

    return {
      ...toRefs(props),
      store,
      record: store.record,
      breadcrumbs,
    };
  },
});

export default OpmUserLeaveRequestsShow;
</script>

<template lang="pug">
.opm-user-leave-requests-show
  ComOpmLeaveRequestsShow(v-if='record.id', :store='store', :breadcrumbs='breadcrumbs')
</template>

<style lang="stylus" scoped>
.opm-user-leave-requests-show
  height 100%
  width 100%
  .breadcrumbs
    padding 0 24px
</style>
