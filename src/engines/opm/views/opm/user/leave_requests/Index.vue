<script lang="ts">
import ComOpmLeaveRequestsIndex from '@/engines/opm/components/opm/leave_requests/ComOpmLeaveRequestsIndex.vue';
import { defineComponent } from '@vue/runtime-core';
import { OpmUserLeaveRequestsApi } from '@/engines/opm/opm-core/apis/opm/user/leave_requests.api';
import { OpmLeaveRequestModel } from '@/engines/opm/models/opm/user/leave_requests';
import { OpmBalanceModel } from '@/engines/opm/opm-core/models/opm/admin/balances';
import { OpmUserBalancesApi } from '@/engines/opm/opm-core/apis/opm/user/balances.api';
import { VStore } from '@/lib/vails';
import usePolicy from '@/components/global/ta-component/ta-template-form-core/usePolicy';

const OpmUserLeaveRequestsIndex = defineComponent({
  name: 'OpmUserLeaveRequestsIndex',
  components: {
    ComOpmLeaveRequestsIndex,
  },
  setup() {
    usePolicy();

    const store = new VStore(new OpmUserLeaveRequestsApi(), OpmLeaveRequestModel);
    const balanceStore = new VStore(new OpmUserBalancesApi(), OpmBalanceModel);

    return {
      store,
      balanceStore,
    };
  },
});

export default OpmUserLeaveRequestsIndex;
</script>

<template lang="pug">
.opm-user-leave-requests-index
  ComOpmLeaveRequestsIndex(:store='store' ,:balanceStore='balanceStore')
</template>

<style lang="stylus" scoped>
.opm-user-leave-requests-index
  height 100%
  width 100%
</style>
