<script lang="ts">
import ComOpmBalancesIndex from '@/engines/opm/components/opm/balances/ComOpmBalancesIndex.vue';
import { defineComponent } from '@vue/runtime-core';
import { OpmUserBalancesApi } from '@/engines/opm/opm-core/apis/opm/user/balances.api';
import { OpmBalanceModel } from '@/engines/opm/opm-core/models/opm/admin/balances';
import { VStore } from '@/lib/vails';
import usePolicy from '@/components/global/ta-component/ta-template-form-core/usePolicy';

const OpmUserBalancesIndex = defineComponent({
  name: 'OpmUserBalancesIndex',
  components: {
    ComOpmBalancesIndex,
  },
  setup() {
    usePolicy();

    const store = new VStore(new OpmUserBalancesApi(), OpmBalanceModel);

    return {
      store,
    };
  },
});

export default OpmUserBalancesIndex;
</script>

<template lang="pug">
.opm-user-balances-index
  ComOpmBalancesIndex(:store='store' )
</template>

<style lang="stylus" scoped>
.opm-user-balances-index
  height 100%
  width 100%
</style>
