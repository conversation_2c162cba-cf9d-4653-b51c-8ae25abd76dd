<script lang="ts">
import ComOpmBalancesShow from '@/engines/opm/components/opm/balances/ComOpmBalancesShow.vue';
import { defineComponent, toRefs, onMounted, computed } from 'vue';
import { OpmUserBalancesApi } from '@/engines/opm/opm-core/apis/opm/user/balances.api';
import { VStore } from '@/lib/vails';
import { useRoute } from 'vue-router';
import { OpmBalanceModel } from '@/engines/opm/opm-core/models/opm/admin/balances';

const OpmUserBalancesShow = defineComponent({
  name: 'OpmUserBalancesShow',
  components: {
    ComOpmBalancesShow,
  },
  setup(props) {
    const route = useRoute();

    const store = new VStore(new OpmUserBalancesApi(), OpmBalanceModel);

    const breadcrumbs = computed(() => [{ label: '', route: '/opm/user/balances' }]);

    onMounted(() => {
      store.find(Number(route.params.balanceId));
    });

    return {
      ...toRefs(props),
      store,
      record: store.record,
      breadcrumbs,
    };
  },
});

export default OpmUserBalancesShow;
</script>

<template lang="pug">
.opm-user-balances-show
  ComOpmBalancesShow(v-if='record.id', :store='store', :breadcrumbs='breadcrumbs')
</template>

<style lang="stylus" scoped>
.opm-user-balances-show
  height 100%
  width 100%
  .breadcrumbs
    padding 0 24px
</style>
