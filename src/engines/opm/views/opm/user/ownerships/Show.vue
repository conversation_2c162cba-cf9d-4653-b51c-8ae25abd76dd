<script lang="ts">
import { defineComponent } from 'vue';
import ComOpmUserOwnershipsShow from '@/engines/opm/components/opm/user/ownerships/ComOpmUserOwnershipsShow.vue';
import { VStore } from '@/lib/vails';
import { OpmUserOwnershipsApi } from '@/engines/opm/opm-core/apis/opm/user/ownerships.api';
import { OpmOwnershipModel } from '@/engines/opm/opm-core/models/opm/ownerships';
import { useRoute } from 'vue-router';

export default defineComponent({
  name: 'OpmUserOwnershipsShow',
  components: {
    ComOpmUserOwnershipsShow,
  },
  setup() {
    const store = new VStore(new OpmUserOwnershipsApi(), OpmOwnershipModel);

    const route = useRoute();
    const ownershipId = Number(route.params.ownershipId);

    store.find(ownershipId);

    return {
      store,
      record: store.record,
    };
  },
});
</script>
<template lang="pug">
.opm-user-ownerships-show.w-full.h-full(class='bg-[#f0f2fc]')
  ComOpmUserOwnershipsShow(:record='record', v-if='record.id')
</template>
