<script lang="ts">
import { defineComponent } from 'vue';
import ComOpmOwnershipsShow from '@/engines/opm/components/opm/ownerships/ComOpmOwnershipsShow.vue';
import { VStore } from '@/lib/vails';
import { OpmManageOwnershipsApi } from '@/engines/opm/opm-core/apis/opm/manage/ownerships.api';
import { OpmOwnershipModel } from '@/engines/opm/opm-core/models/opm/ownerships';
import { useRoute } from 'vue-router';

export default defineComponent({
  name: 'OpmManageOwnershipsShow',
  components: { ComOpmOwnershipsShow },
  setup() {
    const store = new VStore(new OpmManageOwnershipsApi(), OpmOwnershipModel);

    const route = useRoute();
    const ownershipId = Number(route.params.ownershipId);

    store.find(ownershipId);

    return {
      store,
      record: store.record,
    };
  },
});
</script>

<template lang="pug">
.opm-manage-ownerships-show.w-full.h-full(class='bg-[#f0f2fc]')
  ComOpmOwnershipsShow(:store='store', :record='record', v-if='record.id')
</template>
