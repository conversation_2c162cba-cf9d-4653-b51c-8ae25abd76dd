<script lang="ts">
import { defineComponent } from 'vue';
import ComOpmMembersShow from '@/engines/opm/components/opm/members/ComOpmMembersShow.vue';
import { VStore } from '@/lib/vails';
import { OpmManageMembersApi } from '@/engines/opm/opm-core/apis/opm/manage/members.api';
import { OpmMemberModel } from '@/engines/opm/opm-core/models/opm/members';
import { useRoute } from 'vue-router';
import { OpmManageOwnershipsApi } from '@/engines/opm/opm-core/apis/opm/manage/ownerships.api';
import { OpmOwnershipModel } from '@/engines/opm/opm-core/models/opm/ownerships';
import { OpmAdminJobTitlesApi } from '@/engines/opm/opm-core/apis/opm/admin/job_titles.api';
import { OpmJobTitleModel } from '@/engines/opm/opm-core/models/opm/admin/job_titles';
import { OpmManageItemsApi } from '@/engines/opm/opm-core/apis/opm/manage/items.api';
import { OpmItemModel } from '@/engines/opm/opm-core/models/opm/admin/items';
// import { OpmAdminGroupsApi } from '@/engines/opm/opm-core/apis/opm/admin/groups.api';
// import { OpmGroupModel } from '@/engines/opm/opm-core/models/opm/admin/groups';
import {
  OpmTechItemModel,
  OpmWinItemModel,
  OpmTeachingItemModel,
} from '@/engines/opm/opm-core/models/opm/manage/items';
import { OpmRecordModel } from '@/engines/opm/opm-core/models/opm/admin/records';
import { OpmAdminRecordsApi } from '@/engines/opm/opm-core/apis/opm/admin/records.api';

export default defineComponent({
  name: 'OpmManageMemberShow',
  components: {
    ComOpmMembersShow,
  },
  setup() {
    const route = useRoute();
    const ownershipId = Number(route.params.ownershipId);
    const memberId = Number(route.params.memberId);

    const ownershipStore = new VStore(new OpmManageOwnershipsApi(), OpmOwnershipModel);
    ownershipStore.find(ownershipId);
    const store = new VStore(
      new OpmManageMembersApi({
        parents: [{ type: 'ownerships', id: ownershipId }],
      }),
      OpmMemberModel,
    );

    const memberTitleStore = new VStore(
      new OpmAdminJobTitlesApi({
        parents: [{ type: 'members', id: memberId }],
      }),
      OpmJobTitleModel,
    );

    store.find(memberId);

    const eduItemStore = new VStore(
      new OpmManageItemsApi({
        parents: [{ type: 'ownerships', id: ownershipId }],
        params: {
          q: {
            type_eq: 'Opm::EduItem',
            create_instance_state_eq: 'completed',
            member_id_eq: memberId,
          },
        },
      }),
      OpmItemModel,
    );
    const winItemStore = new VStore(
      new OpmManageItemsApi({
        parents: [{ type: 'ownerships', id: ownershipId }],
        params: {
          q: {
            type_eq: 'Opm::winItem',
            create_instance_state_eq: 'completed',
            member_id_eq: memberId,
          },
        },
      }),
      OpmWinItemModel,
    );
    const techItemStore = new VStore(
      new OpmManageItemsApi({
        parents: [{ type: 'ownerships', id: ownershipId }],
        params: {
          q: {
            type_eq: 'Opm::TechItem',
            create_instance_state_eq: 'completed',
            member_id_eq: memberId,
          },
        },
      }),
      OpmTechItemModel,
    );

    const teachingItemStore = new VStore(
      new OpmManageItemsApi({
        parents: [{ type: 'ownerships', id: ownershipId }],
        params: {
          q: {
            type_eq: 'Opm::TeachingItem',
            create_instance_state_eq: 'completed',
            member_id_eq: memberId,
          },
        },
      }),
      OpmTeachingItemModel,
    );

    const eduRecordStore = new VStore(
      new OpmAdminRecordsApi({
        params: {
          q: {
            member_id_eq: memberId,
            type_eq: 'Opm::EduRecord',
          },
        },
      }),
      OpmRecordModel,
    );
    return {
      store,
      record: store.record,
      ownershipRecord: ownershipStore.record,
      memberTitleStore,
      eduItemStore,
      winItemStore,
      techItemStore,
      teachingItemStore,
      eduRecordStore,
    };
  },
});
</script>

<template lang="pug">
.opm-manage-member-show.bg-primary-50.h-full
  ComOpmMembersShow(
    :store='store',
    :memberTitleStore='memberTitleStore',
    :record='record',
    :ownershipRecord='ownershipRecord',
    :eduItemStore='eduItemStore',
    :winItemStore='winItemStore',
    :teachingItemStore='teachingItemStore',
    :techItemStore='techItemStore',
    :eduRecordStore='eduRecordStore',
    v-if='record.id && ownershipRecord.id'
  )
</template>
