<script lang="ts">
import { defineComponent } from 'vue';
import ComOpmMembersIndex2 from '@/engines/opm/components/opm/members/ComOpmMembersIndex2.vue';
import { useRoute } from 'vue-router';
import { VStore } from '@/lib/vails';
import { OpmManageOwnershipsApi } from '@/engines/opm/opm-core/apis/opm/manage/ownerships.api';
import { OpmOwnershipModel } from '@/engines/opm/opm-core/models/opm/ownerships';
import { OpmManageMembersApi } from '@/engines/opm/opm-core/apis/opm/manage/members.api';
import { OpmMemberModel } from '@/engines/opm/opm-core/models/opm/members';

export default defineComponent({
  name: 'OpmMangeMembersIndex',
  components: {
    ComOpmMembersIndex2,
  },
  setup() {
    const route = useRoute();
    const ownershipId = Number(route.params.ownershipId);

    const ownershipStore = new VStore(new OpmManageOwnershipsApi(), OpmOwnershipModel);
    ownershipStore.find(ownershipId);

    const store = new VStore(
      new OpmManageMembersApi({
        parents: [{ type: 'ownerships', id: ownershipId }],
      }),
      OpmMemberModel,
    );

    return {
      store,
      ownershipRecord: ownershipStore.record,
    };
  },
});
</script>

<template lang="pug">
.opm-manage-members-index.w-full.h-full(class='bg-[#F0F2FC]')
  ComOpmMembersIndex2(
    :store='store',
    :ownershipRecord='ownershipRecord',
    v-if='ownershipRecord.id'
  )
</template>
