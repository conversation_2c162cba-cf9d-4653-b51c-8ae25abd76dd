<script lang="ts">
import { defineComponent } from 'vue';
import { useRoute } from 'vue-router';
import { OpmManageOwnershipsApi } from '@/engines/opm/opm-core/apis/opm/manage/ownerships.api';
import { OpmOwnershipModel } from '@/engines/opm/opm-core/models/opm/ownerships';
import { VStore } from '@/lib/vails';
import ComOpmIncrPositionRequestsIndex from '@/engines/opm/components/opm/incr_position_requests/ComOpmIncrPositionRequestsIndex.vue';
import { OpmManageIncrPositionRequestsApi } from '@/engines/opm/opm-core/apis/opm/manage/incr_position_requests.api';
import { OpmIncrPositionRequestModel } from '@/engines/opm/opm-core/models/opm/incr_position_requests';

export default defineComponent({
  name: 'OpmIncrPositionRequestsIndex',
  components: { ComOpmIncrPositionRequestsIndex },
  setup() {
    const route = useRoute();
    const ownershipId = Number(route.params.ownershipId);

    const ownershipStore = new VStore(new OpmManageOwnershipsApi(), OpmOwnershipModel);
    ownershipStore.find(ownershipId);

    const store = new VStore(
      new OpmManageIncrPositionRequestsApi({
        parents: [{ type: 'ownerships', id: ownershipId }],
        params: {
          group_keys: 'create_instance_state',
        },
      }),
      OpmIncrPositionRequestModel,
    );

    return {
      ownershipRecord: ownershipStore.record,
      store,
    };
  },
});
</script>

<template lang="pug">
.opm-incr-position-requests-index.w-full.h-full(class='bg-[#F0F2FC]')
  ComOpmIncrPositionRequestsIndex(
    :store='store',
    :ownershipRecord='ownershipRecord',
    v-if='ownershipRecord.id'
  )
</template>
