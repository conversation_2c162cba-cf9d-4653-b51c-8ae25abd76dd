<script lang="ts">
import { defineComponent } from 'vue';
import ComOpmRelocatePostionRequestsIndex from '@/engines/opm/components/opm/relocate_position_requests/ComOpmRelocatePostionRequestsIndex.vue';
import { useRoute } from 'vue-router';
import { OpmManageOwnershipsApi } from '@/engines/opm/opm-core/apis/opm/manage/ownerships.api';
import { OpmOwnershipModel } from '@/engines/opm/opm-core/models/opm/ownerships';
import { VStore } from '@/lib/vails';
import { OpmManageRelocatePositionRequestsApi } from '@/engines/opm/opm-core/apis/opm/manage/relocate_position_requests.api';
import { OpmRelocatePositionRequestModel } from '@/engines/opm/opm-core/models/opm/relocate_position_requests';

export default defineComponent({
  name: 'OpmRelocatePositionRequestsIndex',
  components: { ComOpmRelocatePostionRequestsIndex },
  setup() {
    const route = useRoute();
    const ownershipId = Number(route.params.ownershipId);

    const ownershipStore = new VStore(new OpmManageOwnershipsApi(), OpmOwnershipModel);
    ownershipStore.find(ownershipId);

    const store = new VStore(
      new OpmManageRelocatePositionRequestsApi({
        parents: [{ type: 'ownerships', id: ownershipId }],
        params: {
          group_keys: 'create_instance_state',
        },
      }),
      OpmRelocatePositionRequestModel,
    );

    return {
      ownershipRecord: ownershipStore.record,
      store,
    };
  },
});
</script>

<template lang="pug">
.opm-relocate-position-requests-index.w-full.h-full(class='bg-[#F0F2FC]')
  ComOpmRelocatePostionRequestsIndex(
    :store='store',
    :ownershipRecord='ownershipRecord',
    v-if='ownershipRecord.id'
  )
</template>
