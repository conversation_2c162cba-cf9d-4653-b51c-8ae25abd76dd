<script lang="ts">
import { defineComponent } from 'vue';
import ComOpmDepartmentsShow from '@/engines/opm/components/opm/departments/ComOpmDepartmentsShow.vue';
import { useRoute } from 'vue-router';
import { VStore } from '@/lib/vails';
import { OpmManageOwnershipsApi } from '@/engines/opm/opm-core/apis/opm/manage/ownerships.api';
import { OpmOwnershipModel } from '@/engines/opm/opm-core/models/opm/ownerships';
import { OpmManageDepartmentsApi } from '@/engines/opm/opm-core/apis/opm/manage/departments.api';
import { OpmDepartmentModel } from '@/engines/opm/opm-core/models/opm/departments';
import { OpmManageDepartmentDutiesApi } from '@/engines/opm/opm-core/apis/opm/manage/department_duties.api';
import { OpmManageMembersApi } from '@/engines/opm/opm-core/apis/opm/manage/members.api';
import { OpmMemberModel } from '@/engines/opm/opm-core/models/opm/members';

export default defineComponent({
  name: 'OpmDepartmentsShow',
  components: { ComOpmDepartmentsShow },
  setup() {
    const route = useRoute();

    const ownershipId = Number(route.params.ownershipId);
    const departmentId = Number(route.params.departmentId);

    const ownershipStore = new VStore(new OpmManageOwnershipsApi(), OpmOwnershipModel);
    ownershipStore.find(ownershipId);

    const departmentStore = new VStore(
      new OpmManageDepartmentsApi({
        parents: [{ type: 'ownerships', id: ownershipId }],
      }),
      OpmDepartmentModel,
    );
    departmentStore.find(departmentId);

    const dutyStore = new VStore(
      new OpmManageDepartmentDutiesApi({
        parents: [{ type: 'ownerships', id: ownershipId }],
      }),
    );

    const memberStore = new VStore(
      new OpmManageMembersApi({
        parents: [{ type: 'ownerships', id: ownershipId }],
      }),
      OpmMemberModel,
    );

    return {
      departmentRecord: departmentStore.record,
      ownershipRecord: ownershipStore.record,
      dutyStore,
      memberStore,
    };
  },
});
</script>

<template lang="pug">
.opm-manage-departments-show.w-full.h-full(class='bg-[#f0f2fc]')
  ComOpmDepartmentsShow(
    :memberStore='memberStore',
    :dutyStore='dutyStore',
    :record='departmentRecord',
    :ownershipRecord='ownershipRecord',
    v-if='departmentRecord.id'
  )
</template>

<style lang="stylus" scoped>
.opm-manage-departments-show
  padding-top: 16px !important;
</style>
