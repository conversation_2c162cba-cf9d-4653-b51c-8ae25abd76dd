<script lang='ts'>
import { OpmManageItemsApi } from '@/engines/opm/opm-core/apis/opm/manage/items.api';
import { OpmTechItemModel } from '@/engines/opm/opm-core/models/opm/manage/items';
import { VStore } from '@/lib/vails';
import ComOpmItemsIndex from '@/engines/opm/components/opm/items/ComOpmItemsIndex.vue';
import { ref, defineComponent, toRefs } from 'vue';
import { useRoute } from 'vue-router';
const OpmManageTechItemIndex = defineComponent({
  name: 'OpmManageTechItemIndex',
  components: {
    ComOpmItemsIndex,
  },
  props: {},
  setup(props) {
    const route = useRoute();
    const ownershipId = route.params.ownershipId as string;

    const store = new VStore(new OpmManageItemsApi({
      parents: [{ type: 'ownerships', id: ownershipId }],
      params: {
        q: {
          type_eq: 'Opm::TechItem'
        }
      }
    }), OpmTechItemModel);

    const title = '技术证书记录';
    const template = 'opm_tech_item';
    return {
      ...toRefs(props),
      store,
      title,
      template,
    };
  },
});
export default OpmManageTechItemIndex;
</script>

<template lang="pug">
.opm-manage-tech-item-index.flex.flex-col(class='bg-[#f0f2fc]')
  header.flex.items-center.justify-between.mb-4.flex-shrink-0.pt-4
    .title.text-xl.font-semibold.text-primary-900 {{ title }}
  .flex-grow.h-0.p-4.rounded-lg.bg-white
    ComOpmItemsIndex(
      :store='store'
      :disabled='true'
      :title='title'
      :template='template'
    )
</template>

<style lang="stylus" scoped>
.opm-manage-tech-item-index
  height 100%
  width 100%
</style>
