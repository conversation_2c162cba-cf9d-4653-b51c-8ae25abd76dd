<script lang="ts">
import { defineComponent } from 'vue';
import ComOpmEmployRequestsIndex from '@/engines/opm/components/opm/employ_requests/ComOpmEmployRequestsIndex.vue';
import { useRoute } from 'vue-router';
import { OpmManageOwnershipsApi } from '@/engines/opm/opm-core/apis/opm/manage/ownerships.api';
import { OpmOwnershipModel } from '@/engines/opm/opm-core/models/opm/ownerships';
import { VStore } from '@/lib/vails';
import { OpmMangeEmployRequestsApi } from '@/engines/opm/opm-core/apis/opm/manage/employ_requests.api';
import { OpmEmployRequestModel } from '@/engines/opm/opm-core/models/opm/employ_requests';

export default defineComponent({
  name: 'OpmEmployRequestsIndex',
  components: { ComOpmEmployRequestsIndex },
  setup() {
    const route = useRoute();
    const ownershipId = Number(route.params.ownershipId);

    const ownershipStore = new VStore(new OpmManageOwnershipsApi(), OpmOwnershipModel);
    ownershipStore.find(ownershipId);

    const store = new VStore(
      new OpmMangeEmployRequestsApi({
        parents: [{ type: 'ownerships', id: ownershipId }],
        params: {
          group_keys: 'create_instance_state',
        },
      }),
      OpmEmployRequestModel,
    );

    return {
      ownershipRecord: ownershipStore.record,
      store,
    };
  },
});
</script>

<template lang="pug">
.opm-employ-requests-index.w-full.h-full(class='bg-[#F0F2FC]')
  ComOpmEmployRequestsIndex(
    :store='store',
    :ownershipRecord='ownershipRecord',
    v-if='ownershipRecord.id'
  )
</template>
