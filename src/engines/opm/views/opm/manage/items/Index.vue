<script lang='ts'>
import { OpmManageItemsApi } from '@/engines/opm/opm-core/apis/opm/manage/items.api';
import { OpmEduItemModel } from '@/engines/opm/opm-core/models/opm/manage/items';
import { VStore } from '@/lib/vails';
import ComOpmItemsIndex from '@/engines/opm/components/opm/items/ComOpmItemsIndex.vue';
import { ref, defineComponent, toRefs } from 'vue';
import { useRoute } from 'vue-router';
const OpmManageItemIndex = defineComponent({
  name: 'OpmManageItemIndex',
  components: {
    ComOpmItemsIndex,
  },
  props: {},
  setup(props) {
    const route = useRoute();
    const ownershipId = route.params.ownershipId as string;

    const store = new VStore(new OpmManageItemsApi({
      parents: [{ type: 'ownerships', id: ownershipId }],
      params: {
        q: {
          type_eq: 'Opm::EduItem'
        }
      }
    }), OpmEduItemModel);
    return {
      ...toRefs(props),
      store,
    };
  },
});
export default OpmManageItemIndex;
</script>

<template lang="pug">
.opm-manage-item-index.flex.flex-col(class='bg-[#f0f2fc]')
  header.flex.items-center.justify-between.mb-4.flex-shrink-0.pt-4
    .title.text-xl.font-semibold.text-primary-900 培训管理
  .flex-grow.h-0.p-4.rounded-lg.bg-white
    ComOpmItemsIndex(:store='store' :disabled='true')
</template>

<style lang="stylus" scoped>
.opm-manage-item-index
  height 100%
  width 100%
</style>
