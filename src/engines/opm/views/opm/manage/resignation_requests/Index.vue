<script lang="ts">
import { defineComponent } from 'vue';
import ComOpmResignationRequestIndex from '@/engines/opm/components/opm/resignation_requests/ComOpmResignationRequestIndex.vue';
import { useRoute } from 'vue-router';
import { OpmManageOwnershipsApi } from '@/engines/opm/opm-core/apis/opm/manage/ownerships.api';
import { OpmOwnershipModel } from '@/engines/opm/opm-core/models/opm/ownerships';
import { VStore } from '@/lib/vails';
import { OpmManageResignationRequestsApi } from '@/engines/opm/opm-core/apis/opm/manage/resignation_requests.api';
import { OpmResignationRequestModel } from '@/engines/opm/opm-core/models/opm/resignation_requests';

export default defineComponent({
  name: 'OpmEmployRequestsIndex',
  components: { ComOpmResignationRequestIndex },
  setup() {
    const route = useRoute();
    const ownershipId = Number(route.params.ownershipId);

    const ownershipStore = new VStore(new OpmManageOwnershipsApi(), OpmOwnershipModel);
    ownershipStore.find(ownershipId);

    const store = new VStore(
      new OpmManageResignationRequestsApi({
        parents: [{ type: 'ownerships', id: ownershipId }],
        params: {
          group_keys: 'create_instance_state',
        },
      }),
      OpmResignationRequestModel,
    );

    return {
      ownershipRecord: ownershipStore.record,
      store,
    };
  },
});
</script>

<template lang="pug">
.opm-resignation-requests-index.w-full.h-full(class='bg-[#F0F2FC]')
  ComOpmResignationRequestIndex(
    :store='store',
    :ownershipRecord='ownershipRecord',
    v-if='ownershipRecord.id'
  )
</template>
