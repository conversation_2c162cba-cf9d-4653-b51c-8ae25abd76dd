<script lang="ts">
import { defineComponent } from 'vue';
import ComOpmRevokePositionRequestIndex from '@/engines/opm/components/opm/revoke_position_requests/ComOpmRevokePositionRequestIndex.vue';
import { useRoute } from 'vue-router';
import { OpmManageOwnershipsApi } from '@/engines/opm/opm-core/apis/opm/manage/ownerships.api';
import { OpmOwnershipModel } from '@/engines/opm/opm-core/models/opm/ownerships';
import { VStore } from '@/lib/vails';
import { OpmManageRevokePositionRequestsApi } from '@/engines/opm/opm-core/apis/opm/manage/revoke_position_requests.api';
import { OpmRevokePositionRequestModel } from '@/engines/opm/opm-core/models/opm/revoke_position_requests';

export default defineComponent({
  name: 'OpmRevokePositionRequestsIndex',
  components: { ComOpmRevokePositionRequestIndex },
  setup() {
    const route = useRoute();
    const ownershipId = Number(route.params.ownershipId);

    const ownershipStore = new VStore(new OpmManageOwnershipsApi(), OpmOwnershipModel);
    ownershipStore.find(ownershipId);

    const store = new VStore(
      new OpmManageRevokePositionRequestsApi({
        parents: [{ type: 'ownerships', id: ownershipId }],
        params: {
          group_keys: 'create_instance_state',
        },
      }),
      OpmRevokePositionRequestModel,
    );

    return {
      ownershipRecord: ownershipStore.record,
      store,
    };
  },
});
</script>

<template lang="pug">
.opm-revoke-position-requests-index.w-full.h-full(class='bg-[#F0F2FC]')
  ComOpmRevokePositionRequestIndex(
    :store='store',
    :ownershipRecord='ownershipRecord',
    v-if='ownershipRecord.id'
  )
</template>
