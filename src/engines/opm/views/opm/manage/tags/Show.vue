<script lang="ts">
  import ComOpmTagsShow from '@/engines/opm/components/opm/tags/ComOpmTagsShow.vue';
  import { defineComponent, toRefs, onMounted, computed } from 'vue';
  import { OpmManageTagsApi } from '@/engines/opm/opm-core/apis/opm/manage/tags.api';
  import { OpmTagModel } from '@/engines/opm/opm-core/models/opm/manage/tags';
  import { VStore } from '@/lib/vails';
  import { useRoute } from 'vue-router';

  const OpmManageTagsShow = defineComponent({
    name: 'OpmManageTagsShow',
    components: {
    ComOpmTagsShow,
  },
    setup(props) {
      const route = useRoute();
    
    const store = new VStore(new OpmManageTagsApi(), OpmTagModel);
    
    const breadcrumbs = computed(() => [
      
    { label: '', route: '/opm/manage/tags' },
          ]);

  onMounted(() => {
    store.find(Number(route.params.tagId));
  });

  return {
    ...toRefs(props),
    store,
    record: store.record,
    breadcrumbs,
  };
  },
});

  export default OpmManageTagsShow;
</script>

<template lang="pug">
.opm-manage-tags-show
  ComOpmTagsShow(v-if='record.id', :store='store', :breadcrumbs='breadcrumbs')
</template>

<style lang="stylus" scoped>
.opm-manage-tags-show
  height 100%
  width 100%
  .breadcrumbs
    padding 0 24px
</style>
