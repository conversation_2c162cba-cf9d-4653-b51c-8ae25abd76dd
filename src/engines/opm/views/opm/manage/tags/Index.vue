<script lang="ts">
import ComOpmTagsIndex from '@/engines/opm/components/opm/tags/ComOpmTagsIndex.vue';
import { defineComponent } from '@vue/runtime-core';
import { OpmManageTagsApi } from '@/engines/opm/opm-core/apis/opm/manage/tags.api';
import { OpmTagModel } from '@/engines/opm/opm-core/models/opm/manage/tags';
import { VStore } from '@/lib/vails';
import { useRoute } from 'vue-router';
import { OpmManageMembersApi } from '@/engines/opm/opm-core/apis/opm/manage/members.api';
import { OpmMemberModel } from '@/engines/opm/opm-core/models/opm/members';
import { OpmManageOwnershipsApi } from '@/engines/opm/opm-core/apis/opm/manage/ownerships.api';
import { OpmOwnershipModel } from '@/engines/opm/opm-core/models/opm/ownerships';

const OpmManageTagsIndex = defineComponent({
  name: 'OpmManageTagsIndex',
  components: {
    ComOpmTagsIndex,
  },
  setup() {
    const route = useRoute();
    const ownershipId = Number(route.params.ownershipId);

    const ownershipStore = new VStore(new OpmManageOwnershipsApi(), OpmOwnershipModel);
    ownershipStore.find(ownershipId);

    const store = new VStore(
      new OpmManageTagsApi({
        parents: [{ type: 'ownerships', id: ownershipId }],
      }),
      OpmTagModel,
    );

    const memberStore = new VStore(
      new OpmManageMembersApi({
        parents: [{ type: 'ownerships', id: ownershipId }],
      }),
      OpmMemberModel,
    );

    return {
      store,
      memberStore,
      ownershipRecord: ownershipStore.record,
    };
  },
});

export default OpmManageTagsIndex;
</script>

<template lang="pug">
.opm-manage-tags-index.w-full.h-full(class='bg-[#f0f2fc]')
  .pt-4.h-full.flex.flex-col
    header.text-primary-900.text-xl.font-semibold.mb-5 组织标签
    .p-4.flex-grow.h-0.bg-white.rounded-lg
      ComOpmTagsIndex(
        :store='store',
        :memberStore='memberStore',
        :ownershipRecord='ownershipRecord',
        v-if='ownershipRecord.id'
      )
</template>

<style lang="stylus" scoped>
.opm-manage-tags-index
  height 100%
  width 100%
</style>
