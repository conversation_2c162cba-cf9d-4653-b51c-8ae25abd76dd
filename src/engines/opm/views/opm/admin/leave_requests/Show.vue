<script lang="ts">
import ComOpmLeaveRequestsShow from '@/engines/opm/components/opm/leave_requests/ComOpmLeaveRequestsShow.vue';
import { defineComponent, toRefs, onMounted, computed } from 'vue';
import { OpmAdminLeaveRequestsApi } from '@/engines/opm/opm-core/apis/opm/admin/leave_requests.api';
import { OpmLeaveRequestModel } from '@/engines/opm/opm-core/models/opm/admin/leave_requests';
import { VStore } from '@/lib/vails';
import { useRoute } from 'vue-router';

const OpmAdminLeaveRequestsShow = defineComponent({
  name: 'OpmAdminLeaveRequestsShow',
  components: {
    ComOpmLeaveRequestsShow,
  },
  setup(props) {
    const route = useRoute();

    const store = new VStore(new OpmAdminLeaveRequestsApi(), OpmLeaveRequestModel);

    const breadcrumbs = computed(() => [{ label: '', route: '/opm/admin/leave_requests' }]);

    onMounted(() => {
      store.find(Number(route.params.leave_requestId));
    });

    return {
      ...toRefs(props),
      store,
      record: store.record,
      breadcrumbs,
    };
  },
});

export default OpmAdminLeaveRequestsShow;
</script>

<template lang="pug">
.opm-admin-leave-requests-show
  ComOpmLeaveRequestsShow(v-if='record.id', :store='store', :breadcrumbs='breadcrumbs')
</template>

<style lang="stylus" scoped>
.opm-admin-leave-requests-show
  height 100%
  width 100%
  .breadcrumbs
    padding 0 24px
</style>
