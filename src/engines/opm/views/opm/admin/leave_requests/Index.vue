<script lang="ts">
import ComOpmLeaveRequestsIndex from '@/engines/opm/components/opm/leave_requests/ComOpmLeaveRequestsIndex.vue';
import { defineComponent } from '@vue/runtime-core';
import { OpmAdminLeaveRequestsApi } from '@/engines/opm/opm-core/apis/opm/admin/leave_requests.api';
import { OpmLeaveRequestModel } from '@/engines/opm/opm-core/models/opm/admin/leave_requests';
import { VStore } from '@/lib/vails';

import usePolicy from '@/components/global/ta-component/ta-template-form-core/usePolicy';
import { OpmAdminBalancesApi } from '@/engines/opm/opm-core/apis/opm/admin/balances.api';
import { OpmBalanceModel } from '@/engines/opm/opm-core/models/opm/admin/balances';

const OpmAdminLeaveRequestsIndex = defineComponent({
  name: 'OpmAdminLeaveRequestsIndex',
  components: {
    ComOpmLeaveRequestsIndex,
  },
  setup() {
    usePolicy();

    const store = new VStore(new OpmAdminLeaveRequestsApi(), OpmLeaveRequestModel);
    const balanceStore = new VStore(new OpmAdminBalancesApi(), OpmBalanceModel);

    return {
      store,
      balanceStore,
    };
  },
});

export default OpmAdminLeaveRequestsIndex;
</script>

<template lang="pug">
.opm-admin-leave-requests-index
  ComOpmLeaveRequestsIndex(:store='store',:balanceStore='balanceStore' )
</template>

<style lang="stylus" scoped>
.opm-admin-leave-requests-index
  height 100%
  width 100%
</style>
