<script lang="ts">
  import ComOpmGroupsShow from '@/engines/opm/components/opm/groups/ComOpmGroupsShow.vue';
  import { defineComponent, toRefs, onMounted, computed } from 'vue';
  import { OpmAdminGroupsApi } from '@/engines/opm/opm-core/apis/opm/admin/groups.api';
  import { OpmGroupModel } from '@/engines/opm/opm-core/models/opm/admin/groups';
  import { VStore } from '@/lib/vails';
  import { useRoute } from 'vue-router';

  const OpmAdminGroupsShow = defineComponent({
    name: 'OpmAdminGroupsShow',
    components: {
    ComOpmGroupsShow,
  },
    setup(props) {
      const route = useRoute();
    
    const store = new VStore(new OpmAdminGroupsApi(), OpmGroupModel);
    
    const breadcrumbs = computed(() => [
      
    { label: '', route: '/opm/admin/groups' },
          ]);

  onMounted(() => {
    store.find(Number(route.params.groupId));
  });

  return {
    ...toRefs(props),
    store,
    record: store.record,
    breadcrumbs,
  };
  },
});

  export default OpmAdminGroupsShow;
</script>

<template lang="pug">
.opm-admin-groups-show
  ComOpmGroupsShow(v-if='record.id', :store='store', :breadcrumbs='breadcrumbs')
</template>

<style lang="stylus" scoped>
.opm-admin-groups-show
  height 100%
  width 100%
  .breadcrumbs
    padding 0 24px
</style>
