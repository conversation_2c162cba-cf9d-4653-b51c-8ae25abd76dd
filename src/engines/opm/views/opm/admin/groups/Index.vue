<script lang="ts">
import ComOpmGroupsIndex from '@/engines/opm/components/opm/groups/ComOpmGroupsIndex.vue';
import { defineComponent, onMounted, ref } from '@vue/runtime-core';
import { OpmAdminGroupsApi } from '@/engines/opm/opm-core/apis/opm/admin/groups.api';
import { OpmGroupModel } from '@/engines/opm/opm-core/models/opm/admin/groups';
import { VStore } from '@/lib/vails';

import usePolicy from '@/components/global/ta-component/ta-template-form-core/usePolicy';
import ComOpmJobTitlesIndex from '@/engines/opm/components/opm/job_titles/ComOpmJobTitlesIndex.vue';
import { OpmAdminJobTitlesApi } from '@/engines/opm/opm-core/apis/opm/admin/job_titles.api';
import { OpmJobTitleModel } from '@/engines/opm/opm-core/models/opm/admin/job_titles';
import { useRoute } from 'vue-router';
import { OpmManageMembersApi } from '@/engines/opm/opm-core/apis/opm/manage/members.api';
import { OpmMemberModel } from '@/engines/opm/opm-core/models/opm/members';
import { OpmManageOwnershipsApi } from '@/engines/opm/opm-core/apis/opm/manage/ownerships.api';
import { OpmOwnershipModel } from '@/engines/opm/opm-core/models/opm/ownerships';
import { OpmAdminRecordsApi } from '@/engines/opm/opm-core/apis/opm/admin/records.api';
import { OpmRecordModel } from '@/engines/opm/opm-core/models/opm/admin/records';

const OpmAdminGroupsIndex = defineComponent({
  name: 'OpmAdminGroupsIndex',
  components: {
    ComOpmGroupsIndex,
    ComOpmJobTitlesIndex
  },
  setup() {
    usePolicy();


    const route = useRoute();
    const ownershipId = Number(route.params.ownershipId);

    const store = new VStore(new OpmAdminGroupsApi(), OpmGroupModel);
    const titleStore = new VStore(new OpmAdminJobTitlesApi(), OpmJobTitleModel);
    const memberStore = new VStore(new OpmManageMembersApi({
      parents: [{ type: 'ownerships', id: ownershipId }]
    }), OpmMemberModel);
    const ownershipStore = new VStore(new OpmManageOwnershipsApi(), OpmOwnershipModel);
    const recordsStore = new VStore(new OpmAdminRecordsApi(), OpmRecordModel);

    const selectedGroupId = ref<number>(0)

    onMounted(() => {
      ownershipStore.find(ownershipId);
    })

    return {
      store,
      titleStore,
      memberStore,
      selectedGroupId,
      recordsStore,
      ownershipRecord: ownershipStore.record,
    };
  },
});

export default OpmAdminGroupsIndex;
</script>

<template lang="pug">
.opm-admin-groups-index.flex.flex-col(class='bg-[#f0f2fc]')
  header.flex.items-center.justify-between.mb-4.flex-shrink-0.pt-4
    .title.text-xl.font-semibold.text-primary-900 职称管理
  section.flex.flex-grow.h-0.gap-x-4
    .left__groups.px-2.py-3.rounded-lg.bg-white(class='basis-[240px]')
      ComOpmGroupsIndex(
        :store='store'
        v-model:selectedGroupId='selectedGroupId'
      )
    .right__titles.rounded-lg.bg-white.flex-grow.p-4
      ComOpmJobTitlesIndex(
        :store='titleStore',
        :selectedGroupId='selectedGroupId',
        :ownershipRecord='ownershipRecord',
        :memberStore='memberStore',
        :recordsStore='recordsStore'
      )
</template>

<style lang="stylus" scoped>
.opm-admin-groups-index
  height 100%
  width 100%
</style>
