<script lang="ts">
import ComOpmItemsIndex from '@/engines/opm/components/opm/items/ComOpmItemsIndex.vue';
import { defineComponent } from '@vue/runtime-core';
import { OpmAdminItemsApi } from '@/engines/opm/opm-core/apis/opm/admin/items.api';
import { OpmItemModel } from '@/engines/opm/opm-core/models/opm/admin/items';
import { VStore } from '@/lib/vails';

import usePolicy from '@/components/global/ta-component/ta-template-form-core/usePolicy';

const OpmAdminItemsIndex = defineComponent({
  name: 'OpmAdminItemsIndex',
  components: {
    ComOpmItemsIndex,
  },
  setup() {
    usePolicy();

    

    
    const store = new VStore(new OpmAdminItemsApi(), OpmItemModel);
    

    return {
      store,
      
    };
  },
});

export default OpmAdminItemsIndex;
</script>

<template lang="pug">
.opm-admin-items-index
  ComOpmItemsIndex(:store='store' )
</template>

<style lang="stylus" scoped>
.opm-admin-items-index
  height 100%
  width 100%
</style>
