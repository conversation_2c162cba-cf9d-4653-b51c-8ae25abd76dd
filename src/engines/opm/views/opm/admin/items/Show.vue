<script lang="ts">
  import ComOpmItemsShow from '@/engines/opm/components/opm/items/ComOpmItemsShow.vue';
  import { defineComponent, toRefs, onMounted, computed } from 'vue';
  import { OpmAdminItemsApi } from '@/engines/opm/opm-core/apis/opm/admin/items.api';
  import { OpmItemModel } from '@/engines/opm/opm-core/models/opm/admin/items';
  import { VStore } from '@/lib/vails';
  import { useRoute } from 'vue-router';

  const OpmAdminItemsShow = defineComponent({
    name: 'OpmAdminItemsShow',
    components: {
    ComOpmItemsShow,
  },
    setup(props) {
      const route = useRoute();
    
    const store = new VStore(new OpmAdminItemsApi(), OpmItemModel);
    
    const breadcrumbs = computed(() => [
      
    { label: '', route: '/opm/admin/items' },
          ]);

  onMounted(() => {
    store.find(Number(route.params.itemId));
  });

  return {
    ...toRefs(props),
    store,
    record: store.record,
    breadcrumbs,
  };
  },
});

  export default OpmAdminItemsShow;
</script>

<template lang="pug">
.opm-admin-items-show
  ComOpmItemsShow(v-if='record.id', :store='store', :breadcrumbs='breadcrumbs')
</template>

<style lang="stylus" scoped>
.opm-admin-items-show
  height 100%
  width 100%
  .breadcrumbs
    padding 0 24px
</style>
