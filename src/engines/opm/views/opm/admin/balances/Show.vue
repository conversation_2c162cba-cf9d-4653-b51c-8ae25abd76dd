<script lang="ts">
import ComOpmBalancesShow from '@/engines/opm/components/opm/balances/ComOpmBalancesShow.vue';
import { defineComponent, toRefs, onMounted, computed } from 'vue';
import { OpmAdminBalancesApi } from '@/engines/opm/opm-core/apis/opm/admin/balances.api';
import { OpmBalanceModel } from '@/engines/opm/opm-core/models/opm/admin/balances';
import { VStore } from '@/lib/vails';
import { useRoute } from 'vue-router';

const OpmAdminBalancesShow = defineComponent({
  name: 'OpmAdminBalancesShow',
  components: {
    ComOpmBalancesShow,
  },
  setup(props) {
    const route = useRoute();

    const store = new VStore(new OpmAdminBalancesApi(), OpmBalanceModel);

    const breadcrumbs = computed(() => [{ label: '', route: '/opm/admin/balances' }]);

    onMounted(() => {
      store.find(Number(route.params.balanceId));
    });

    return {
      ...toRefs(props),
      store,
      record: store.record,
      breadcrumbs,
    };
  },
});

export default OpmAdminBalancesShow;
</script>

<template lang="pug">
.opm-admin-balances-show
  ComOpmBalancesShow(v-if='record.id', :store='store', :breadcrumbs='breadcrumbs')
</template>

<style lang="stylus" scoped>
.opm-admin-balances-show
  height 100%
  width 100%
  .breadcrumbs
    padding 0 24px
</style>
