<script lang="ts">
import ComOpmBalancesIndex from '@/engines/opm/components/opm/balances/ComOpmBalancesIndex.vue';
import { defineComponent } from '@vue/runtime-core';
import { OpmAdminBalancesApi } from '@/engines/opm/opm-core/apis/opm/admin/balances.api';
import { OpmBalanceModel } from '@/engines/opm/opm-core/models/opm/admin/balances';
import { VStore } from '@/lib/vails';

import usePolicy from '@/components/global/ta-component/ta-template-form-core/usePolicy';

const OpmAdminBalancesIndex = defineComponent({
  name: 'OpmAdminBalancesIndex',
  components: {
    ComOpmBalancesIndex,
  },
  setup() {
    usePolicy();

    const store = new VStore(new OpmAdminBalancesApi(), OpmBalanceModel);

    return {
      store,
    };
  },
});

export default OpmAdminBalancesIndex;
</script>

<template lang="pug">
.opm-admin-balances-index
  ComOpmBalancesIndex(:store='store' )
</template>

<style lang="stylus" scoped>
.opm-admin-balances-index
  height 100%
  width 100%
</style>
