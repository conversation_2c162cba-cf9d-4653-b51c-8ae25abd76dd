<script lang="ts">
import ComOpmRecordsIndex from '@/engines/opm/components/opm/records/ComOpmRecordsIndex.vue';
import { defineComponent } from '@vue/runtime-core';
import { OpmAdminRecordsApi } from '@/engines/opm/opm-core/apis/opm/admin/records.api';
import { OpmRecordModel } from '@/engines/opm/opm-core/models/opm/admin/records';
import { VStore } from '@/lib/vails';

import usePolicy from '@/components/global/ta-component/ta-template-form-core/usePolicy';

const OpmAdminRecordsIndex = defineComponent({
  name: 'OpmAdminRecordsIndex',
  components: {
    ComOpmRecordsIndex,
  },
  setup() {
    usePolicy();

    

    
    const store = new VStore(new OpmAdminRecordsApi(), OpmRecordModel);
    

    return {
      store,
      
    };
  },
});

export default OpmAdminRecordsIndex;
</script>

<template lang="pug">
.opm-admin-records-index
  ComOpmRecordsIndex(:store='store' )
</template>

<style lang="stylus" scoped>
.opm-admin-records-index
  height 100%
  width 100%
</style>
