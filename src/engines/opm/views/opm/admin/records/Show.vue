<script lang="ts">
  import ComOpmRecordsShow from '@/engines/opm/components/opm/records/ComOpmRecordsShow.vue';
  import { defineComponent, toRefs, onMounted, computed } from 'vue';
  import { OpmAdminRecordsApi } from '@/engines/opm/opm-core/apis/opm/admin/records.api';
  import { OpmRecordModel } from '@/engines/opm/opm-core/models/opm/admin/records';
  import { VStore } from '@/lib/vails';
  import { useRoute } from 'vue-router';

  const OpmAdminRecordsShow = defineComponent({
    name: 'OpmAdminRecordsShow',
    components: {
    ComOpmRecordsShow,
  },
    setup(props) {
      const route = useRoute();
    
    const store = new VStore(new OpmAdminRecordsApi(), OpmRecordModel);
    
    const breadcrumbs = computed(() => [
      
    { label: '', route: '/opm/admin/records' },
          ]);

  onMounted(() => {
    store.find(Number(route.params.recordId));
  });

  return {
    ...toRefs(props),
    store,
    record: store.record,
    breadcrumbs,
  };
  },
});

  export default OpmAdminRecordsShow;
</script>

<template lang="pug">
.opm-admin-records-show
  ComOpmRecordsShow(v-if='record.id', :store='store', :breadcrumbs='breadcrumbs')
</template>

<style lang="stylus" scoped>
.opm-admin-records-show
  height 100%
  width 100%
  .breadcrumbs
    padding 0 24px
</style>
