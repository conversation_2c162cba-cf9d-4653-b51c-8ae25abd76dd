<script lang="ts">
import { defineComponent } from 'vue';
import ComOpmAdminOwnershipsIndex from '@/engines/opm/components/opm/admin/ownerships/ComOpmAdminOwnershipsIndex.vue';
import { VStore } from '@/lib/vails';
import { OpmAdminOwnershipsApi } from '@/engines/opm/opm-core/apis/opm/admin/ownerships.api';
import { OpmOwnershipModel } from '@/engines/opm/opm-core/models/opm/ownerships';

export default defineComponent({
  name: 'OpmAdminOwnershipsIndex',
  components: { ComOpmAdminOwnershipsIndex },
  setup() {
    const store = new VStore(new OpmAdminOwnershipsApi(), OpmOwnershipModel);

    return {
      store,
    };
  },
});
</script>

<template lang="pug">
.opm-admin-ownerships-index.w-full.h-full
  ComOpmAdminOwnershipsIndex(:store='store')
</template>
