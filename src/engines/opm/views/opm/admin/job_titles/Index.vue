<script lang="ts">
import ComOpmJobTitlesIndex from '@/engines/opm/components/opm/job_titles/ComOpmJobTitlesIndex.vue';
import { defineComponent } from '@vue/runtime-core';
import { OpmAdminJobTitlesApi } from '@/engines/opm/opm-core/apis/opm/admin/job_titles.api';
import { OpmJobTitleModel } from '@/engines/opm/opm-core/models/opm/admin/job_titles';
import { VStore } from '@/lib/vails';

import usePolicy from '@/components/global/ta-component/ta-template-form-core/usePolicy';

const OpmAdminJobTitlesIndex = defineComponent({
  name: 'OpmAdminJobTitlesIndex',
  components: {
    ComOpmJobTitlesIndex,
  },
  setup() {
    usePolicy();

    

    
    const store = new VStore(new OpmAdminJobTitlesApi(), OpmJobTitleModel);
    

    return {
      store,
      
    };
  },
});

export default OpmAdminJobTitlesIndex;
</script>

<template lang="pug">
.opm-admin-job-titles-index
  ComOpmJobTitlesIndex(:store='store' )
</template>

<style lang="stylus" scoped>
.opm-admin-job-titles-index
  height 100%
  width 100%
</style>
