<script lang="ts">
  import ComOpmJobTitlesShow from '@/engines/opm/components/opm/job_titles/ComOpmJobTitlesShow.vue';
  import { defineComponent, toRefs, onMounted, computed } from 'vue';
  import { OpmAdminJobTitlesApi } from '@/engines/opm/opm-core/apis/opm/admin/job_titles.api';
  import { OpmJobTitleModel } from '@/engines/opm/opm-core/models/opm/admin/job_titles';
  import { VStore } from '@/lib/vails';
  import { useRoute } from 'vue-router';

  const OpmAdminJobTitlesShow = defineComponent({
    name: 'OpmAdminJobTitlesShow',
    components: {
    ComOpmJobTitlesShow,
  },
    setup(props) {
      const route = useRoute();
    
    const store = new VStore(new OpmAdminJobTitlesApi(), OpmJobTitleModel);
    
    const breadcrumbs = computed(() => [
      
    { label: '', route: '/opm/admin/job_titles' },
          ]);

  onMounted(() => {
    store.find(Number(route.params.job_titleId));
  });

  return {
    ...toRefs(props),
    store,
    record: store.record,
    breadcrumbs,
  };
  },
});

  export default OpmAdminJobTitlesShow;
</script>

<template lang="pug">
.opm-admin-job-titles-show
  ComOpmJobTitlesShow(v-if='record.id', :store='store', :breadcrumbs='breadcrumbs')
</template>

<style lang="stylus" scoped>
.opm-admin-job-titles-show
  height 100%
  width 100%
  .breadcrumbs
    padding 0 24px
</style>
