export default [
  {
    path: '/opm/admin/records',
    name: 'opmAdminRecordsIndex',
    component: () => import(/* webpackChunkName: "opmAdminRecordsIndex" */ '@/engines/opm/views/opm/admin/records/Index.vue'),
    meta: {
      title: 'opmAdminRecordsIndex',
    },
  },
  {
    path: '/opm/admin/records/:recordId',
    name: 'opmAdminRecordsShow',
    component: () => import(/* webpackChunkName: "opmAdminRecordsShow" */ '@/engines/opm/views/opm/admin/records/Show.vue'),
    meta: {
      title: 'opmAdminRecordsShow',
    },
  },
];
