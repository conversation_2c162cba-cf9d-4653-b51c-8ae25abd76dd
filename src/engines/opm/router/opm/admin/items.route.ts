export default [
  {
    path: '/opm/admin/items',
    name: 'opmAdminItemsIndex',
    component: () => import(/* webpackChunkName: "opmAdminItemsIndex" */ '@/engines/opm/views/opm/admin/items/Index.vue'),
    meta: {
      title: 'opmAdminItemsIndex',
    },
  },
  {
    path: '/opm/admin/items/:itemId',
    name: 'opmAdminItemsShow',
    component: () => import(/* webpackChunkName: "opmAdminItemsShow" */ '@/engines/opm/views/opm/admin/items/Show.vue'),
    meta: {
      title: 'opmAdminItemsShow',
    },
  },
];
