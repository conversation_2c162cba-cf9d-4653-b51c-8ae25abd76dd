export default [
  {
    path: '/opm/manage/ownerships/:ownershipId/members',
    name: 'opmManageMembersIndex',
    component: () =>
      import(
        /* webpackChunkName: "opmManageMembersIndex" */ '@/engines/opm/views/opm/manage/members/Index.vue'
      ),
    meta: {
      title: '人员管理',
    },
  },
  {
    path: '/opm/manage/ownerships/:ownershipId/members/:memberId',
    name: 'opmManageMembersShow',
    component: () =>
      import(
        /* webpackChunkName: "opmManageMembersShow" */ '@/engines/opm/views/opm/manage/members/Show.vue'
      ),
    meta: {
      title: '人员详情',
      layout: 'simpleOpm',
    },
  },
];
