export default [
  {
    path: '/opm/manage/ownerships/:ownershipId/employ_requests',
    name: 'opmManageEmployRequestsIndex',
    component: () =>
      import(
        /* webpackChunkName: "opmManageEmployRequestsIndex" */ '@/engines/opm/views/opm/manage/employ_requests/Index.vue'
      ),
    meta: {
      title: '入职管理',
    },
  },
  {
    path: '/opm/manage/ownerships/:ownershipId/resignation_requests',
    name: 'opmManageResignationRequestsIndex',
    component: () =>
      import(
        /* webpackChunkName: "opmManageResignationRequestsIndex" */ '@/engines/opm/views/opm/manage/resignation_requests/Index.vue'
      ),
    meta: {
      title: '离职管理',
    },
  },
  {
    path: '/opm/manage/ownerships/:ownershipId/revoke_position_requests',
    name: 'opmManageRevokePositionRequestsIndex',
    component: () =>
      import(
        /* webpackChunkName: "opmManageRevokePositionRequestsIndex" */ '@/engines/opm/views/opm/manage/revoke_position_requests/Index.vue'
      ),
    meta: {
      title: '离岗管理',
    },
  },
  {
    path: '/opm/manage/ownerships/:ownershipId/relocate_position_requests',
    name: 'opmManageRelocatePositionRequestsIndex',
    component: () =>
      import(
        /* webpackChunkName: "opmManageRelocatePositionRequestsIndex" */ '@/engines/opm/views/opm/manage/relocate_position_requests/Index.vue'
      ),
    meta: {
      title: '转岗管理',
    },
  },
  {
    path: '/opm/manage/ownerships/:ownershipId/incr_position_requests',
    name: 'opmManageIncrPositionRequestsIndex',
    component: () =>
      import(
        /* webpackChunkName: "opmManageIncrPositionRequestsIndex" */ '@/engines/opm/views/opm/manage/incr_position_requests/Index.vue'
      ),
    meta: {
      title: '增岗管理',
    },
  },
];
