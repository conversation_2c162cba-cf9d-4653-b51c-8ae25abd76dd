export default [
  {
    path: '/opm/manage/ownerships/:ownershipId/items',
    name: 'opmManageItemsIndex',
    component: () =>
      import(
        /* webpackChunkName: "opmManageItemsIndex" */ '@/engines/opm/views/opm/manage/items/Index.vue'
      ),
    meta: {
      title: '培训管理',
    },
  },
  {
    path: '/opm/manage/ownerships/:ownershipId/win_items',
    name: 'opmManageWinItemsIndex',
    component: () =>
      import(
        /* webpackChunkName: "opmManageWinItemsIndex" */ '@/engines/opm/views/opm/manage/win_items/Index.vue'
      ),
    meta: {
      title: '获奖记录',
    },
  },
  {
    path: '/opm/manage/ownerships/:ownershipId/tech_items',
    name: 'opmManageTechItemsIndex',
    component: () =>
      import(
        /* webpackChunkName: "opmManageTechItemsIndex" */ '@/engines/opm/views/opm/manage/tech_items/Index.vue'
      ),
    meta: {
      title: '技术证书记录',
    },
  },
];
