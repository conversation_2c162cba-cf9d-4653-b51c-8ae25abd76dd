export default [
  {
    path: '/opm/manage/ownerships/:ownershipId/tags',
    name: 'opmManageTagsIndex',
    component: () =>
      import(
        /* webpackChunkName: "opmManageTagsIndex" */ '@/engines/opm/views/opm/manage/tags/Index.vue'
      ),
    meta: {
      title: '组织标签',
    },
  },
  {
    path: '/opm/manage/tags/:tagId',
    name: 'opmManageTagsShow',
    component: () =>
      import(
        /* webpackChunkName: "opmManageTagsShow" */ '@/engines/opm/views/opm/manage/tags/Show.vue'
      ),
    meta: {
      title: '组织标签',
    },
  },
];
