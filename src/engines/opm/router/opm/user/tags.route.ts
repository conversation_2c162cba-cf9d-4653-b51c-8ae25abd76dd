export default [
  {
    path: '/opm/user/ownerships/:ownershipId/tags',
    name: 'opmUserTagsIndex',
    component: () =>
      import(
        /* webpackChunkName: "opmUserTagsIndex" */ '@/engines/opm/views/opm/user/tags/Index.vue'
      ),
    meta: {
      title: '个人标签',
    },
  },
  {
    path: '/opm/user/tags/:tagId',
    name: 'opmUserTagsShow',
    component: () =>
      import(
        /* webpackChunkName: "opmUserTagsShow" */ '@/engines/opm/views/opm/user/tags/Show.vue'
      ),
    meta: {
      title: 'opmUserTagsShow',
    },
  },
];
