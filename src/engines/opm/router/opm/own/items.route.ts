export default [
  {
    path: '/opm/own/items',
    name: 'opmOwnItemsIndex',
    component: () =>
      import(
        /* webpackChunkName: "opmOwnItemsIndex" */ '@/engines/opm/views/opm/own/items/Index.vue'
      ),
    meta: {
      title: '培训记录',
    },
  },
  {
    path: '/opm/own/win_items',
    name: 'opmOwnWinItemsIndex',
    component: () =>
      import(
        /* webpackChunkName: "opmOwnWinItemsIndex" */ '@/engines/opm/views/opm/own/win_items/Index.vue'
      ),
    meta: {
      title: '获奖记录',
    },
  },
  {
    path: '/opm/own/tech_items',
    name: 'opmOwnTechItemsIndex',
    component: () =>
      import(
        /* webpackChunkName: "opmOwnTechItemsIndex" */ '@/engines/opm/views/opm/own/tech_items/Index.vue'
      ),
    meta: {
      title: '技术培训记录',
    },
  },
  {
    path: '/opm/own/items/:itemId',
    name: 'opmOwnItemsShow',
    component: () =>
      import(
        /* webpackChunkName: "opmOwnItemsShow" */ '@/engines/opm/views/opm/own/items/Show.vue'
      ),
    meta: {
      title: '培训记录',
    },
  },
];
