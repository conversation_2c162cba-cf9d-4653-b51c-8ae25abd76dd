import { VObject } from '@/lib/vails';
import { ref } from 'vue';

export default class Tree {
  data = ref<any>([]);

  static isRoot(node: any): boolean {
    return false;
  }
  reloadData() {}

  findTreeNode(key: string | number, payload = this.data.value): any {
    if (key === null) return payload[0];

    for (const node of payload) {
      if (Reflect.has(node, 'key') && node.key === key) {
        return node;
      }
      const foundNode = this.findTreeNode(key, node.children);
      if (foundNode) {
        return foundNode;
      }
    }
    return undefined;
  }

  createTreeNode(node: any, parentNode: any) {
    parentNode.children = [...(parentNode.children || []), node];
    !Tree.isRoot(parentNode) && parentNode.children_count++;
  }

  create(node: any, parentKey: string | number) {
    const parentNode = this.findTreeNode(parentKey);
    if (parentNode) {
      this.createTreeNode(node, parentNode);
    }
  }

  deleteTreeNode(target: VObject, parentNode: any) {
    parentNode.children = parentNode.children.filter((node: any) => node.key !== target.key);
    !Tree.isRoot(parentNode) && parentNode.children_count--;
  }

  delete(target: VObject, parentKey: string | number) {
    const parentNode = this.findTreeNode(parentKey);
    if (parentNode) {
      this.deleteTreeNode(target, parentNode);
      if (target.children.length > 0) {
        const root = this.data.value[0];
        root.children = root.children.concat(target.children);
        this.reloadData();
        return;
      }

      if (target.children_count > 0) {
        this.reloadData();
        return;
      }
    }
  }

  transfer(node: VObject, originParentKey: string | number, targetParentKey: string | number) {
    const originParentNode = this.findTreeNode(originParentKey);
    const targetParentNode = this.findTreeNode(targetParentKey);
    if (originParentNode && targetParentNode) {
      this.deleteTreeNode(node, originParentNode);
      this.createTreeNode(node, targetParentNode);
      this.reloadData();
    }
  }
}
