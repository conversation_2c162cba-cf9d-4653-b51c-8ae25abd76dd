export type Color = `#${string}`;

export default function useColor() {
  function isValidColor(color: string): color is Color {
    const colorPattern = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
    return colorPattern.test(color);
  }

  function isDarkColor(color: Color) {
    const [r, g, b] = hexToRgb(color);
    const grayLevel = (r * 299 + g * 587 + b * 114) / 1000;
    if (grayLevel === 0) return false;
    return grayLevel < 192;
  }

  function hexToRgb(
    color: Color,
    defaultRgb: [number, number, number] = [0, 0, 0],
  ): [number, number, number] {
    if (!isValidColor(color)) {
      return defaultRgb;
    }

    let r: number, g: number, b: number;

    if (color.length === 4) {
      r = parseInt(color[1] + color[1], 16);
      g = parseInt(color[2] + color[2], 16);
      b = parseInt(color[3] + color[3], 16);
    } else {
      r = parseInt(color.slice(1, 3), 16);
      g = parseInt(color.slice(3, 5), 16);
      b = parseInt(color.slice(5, 7), 16);
    }

    return [r, g, b];
  }

  return {
    isValidColor,
    isDarkColor,
    hexToRgb,
  };
}
