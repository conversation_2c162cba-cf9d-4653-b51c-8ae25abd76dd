import { VModel } from '@/lib/vails';
import { OpmOwnership } from '@/engines/opm/opm-core/types/model';

export class OpmOwnershipModel extends VModel<OpmOwnership> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return ResDepartmentModel.stateMapping()[this.reactiveRecord.state];
  // });
  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}
