import { VModel, VObject } from '@/lib/vails';
import { OpmLeaveRequest } from '../../../types/model';

export class OpmLeaveRequestModel extends VModel<OpmLeaveRequest> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return OpmLeaveRequestModel.stateMapping()[this.reactiveRecord.state];
  // });
  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}
