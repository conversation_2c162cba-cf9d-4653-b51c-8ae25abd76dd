import { VModel, VObject } from '@/lib/vails';
import { OpmItem } from '@/engines/opm/opm-core/types/model';

export class OpmItemModel extends VModel<OpmItem> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return OpmItemModel.stateMapping()[this.reactiveRecord.state];
  // });

  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}
