import { VModel } from '@/lib/vails';
import { OpmBalance } from '../../../types/model';

export class OpmBalanceModel extends VModel<OpmBalance> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return OpmBalanceModel.stateMapping()[this.reactiveRecord.state];
  // });
  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}
