import { VModel, VObject } from '@/lib/vails';
import { OpmRecord } from '@/engines/opm/opm-core/types/model';

export class OpmRecordModel extends VModel<OpmRecord> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return OpmRecordModel.stateMapping()[this.reactiveRecord.state];
  // });

  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}
