import { VModel, VObject } from '@/lib/vails';
import { OpmGroup } from '@/engines/opm/opm-core/types/model';

export class OpmGroupModel extends VModel<OpmGroup> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return OpmGroupModel.stateMapping()[this.reactiveRecord.state];
  // });

  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}
