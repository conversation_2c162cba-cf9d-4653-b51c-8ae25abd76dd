import { VModel } from '@/lib/vails';
import { OpmResignationRequest } from '@/engines/opm/opm-core/types/model';

export class OpmResignationRequestModel extends VModel<OpmResignationRequest> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return ResDepartmentModel.stateMapping()[this.reactiveRecord.state];
  // });
  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
  mainPosition = this.computedAttr('mainPosition', () => {
    const departmentsDuties: any = this.reactiveRecord.departments_duties || [];
    return (
      departmentsDuties.filter((rec: any) => !!rec.duty_priority)?.[0] || departmentsDuties?.[0]
    );
  });

  department = this.computedAttr('department', () => {
    return {
      name: this.mainPosition.value?.department_name,
      department_identity_color: this.mainPosition.value?.department_identity_color,
      department_identity_name: this.mainPosition.value?.department_identity_name,
    };
  });
}
