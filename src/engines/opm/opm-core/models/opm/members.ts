import { VModel } from '@/lib/vails';
import { OpmMember } from '@/engines/opm/opm-core/types/model';
import { uniq } from 'lodash';

export class OpmMemberModel extends VModel<OpmMember> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return ResDepartmentModel.stateMapping()[this.reactiveRecord.state];
  // });
  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }

  dutyNames = this.computedAttr('dutyNames', () => {
    return uniq(this.reactiveRecord.departments_duties?.map((d: any) => d.duty_name) || []).join(
      '、',
    );
  });

  departmentNames = this.computedAttr('departmentNames', () => {
    return uniq(
      this.reactiveRecord.departments_duties?.map((d: any) => d.department_name) || [],
    )?.join('、');
  });

  departmentIds = this.computedAttr('departmentIds', () => {
    return uniq(
      this.reactiveRecord.departments_duties?.map((d: any) => d.department_id) || [],
    )?.join('、');
  });

  departmentLevels = this.computedAttr('departmentLevels', () => {
    return uniq(this.reactiveRecord.departments?.map((department: any) => department.level) || []);
  });

  mainPosition = this.computedAttr('mainPosition', () => {
    return (
      this.reactiveRecord.departments_duties?.filter((rec: any) => !!rec.priority)?.[0] ||
      (this.reactiveRecord.departments_duties as any)?.[0]
    );
  });

  departmentsDuties = this.computedAttr('departmentsDuties', () => {
    return this.reactiveRecord.departments_duties || [];
  });

  dutyRanks = this.computedAttr('dutyRanks', () => {
    const mapping: any = { charge: '分管', manage: '主管', employee: '员工' };

    return uniq(this.reactiveRecord.duty_ranks || [])
      .map((rank: any) => {
        return mapping[rank] || '员工';
      })
      .join('、');
  });

  dutyPriorities = this.computedAttr('dutyPriorities', () => {
    return uniq(
      (this.reactiveRecord.priorities || []).map((priority: any) => {
        return priority ? '主岗' : '副岗';
      }),
    );
  });
}
