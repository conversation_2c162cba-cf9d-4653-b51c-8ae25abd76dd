import { VModel } from '@/lib/vails';
import { OpmRelocatePositionRequest } from '@/engines/opm/opm-core/types/model';

export class OpmRelocatePositionRequestModel extends VModel<OpmRelocatePositionRequest> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return ResDepartmentModel.stateMapping()[this.reactiveRecord.state];
  // });
  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}
