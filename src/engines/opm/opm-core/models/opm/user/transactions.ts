import { VModel } from '@/lib/vails';
import { OpmTransaction } from '../../../types/model';

export class OpmTransactionModel extends VModel<OpmTransaction> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return OpmTransactionModel.stateMapping()[this.reactiveRecord.state];
  // });
  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}
