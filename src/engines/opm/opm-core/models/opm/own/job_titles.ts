import { VModel, VObject } from '@/lib/vails';
import { OpmJobTitle } from '@/engines/opm/opm-core/types/model';

export class OpmJobTitleModel extends VModel<OpmJobTitle> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return OpmJobTitleModel.stateMapping()[this.reactiveRecord.state];
  // });

  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}
