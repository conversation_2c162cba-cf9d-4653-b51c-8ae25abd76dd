import { VModel } from '@/lib/vails';
import { OpmEmployRequest } from '@/engines/opm/opm-core/types/model';

export class OpmEmployRequestModel extends VModel<OpmEmployRequest> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return ResDepartmentModel.stateMapping()[this.reactiveRecord.state];
  // });
  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
  inviter = this.computedAttr('inviter', () => {
    return this.reactiveRecord.employ_invite?.creator || {};
  });
}
