import { VModel, VObject } from '@/lib/vails';
import { OpmTag } from '@/engines/opm/opm-core/types/model';

export class OpmTagModel extends VModel<OpmTag> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return OpmTagModel.stateMapping()[this.reactiveRecord.state];
  // });

  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}
