import { VModel, VObject } from '@/lib/vails';
import { OpmItem } from '@/engines/opm/opm-core/types/model';

export class OpmItemModel extends VModel<OpmItem> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return OpmItemModel.stateMapping()[this.reactiveRecord.state];
  // });
  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}

export class OpmEduItemModel extends OpmItemModel {
  cardCompName = 'ComOpmItemCard';
  detailCompName = 'ComOpmItemInfo';
}

export class OpmWinItemModel extends OpmItemModel {
  cardCompName = 'ComOpmWinItemCard';
  detailCompName = 'ComOpmWinItemInfo';
}

export class OpmTeachingItemModel extends OpmItemModel {
  cardCompName = 'ComOpmTeachingItemCard';
  detailCompName = 'ComOpmTeachingItemInfo';
}

export class OpmTechItemModel extends OpmItemModel {
  cardCompName = 'ComOpmTechItemCard';
  detailCompName = 'ComOpmTechItemInfo';
}
