import { VModel } from '@/lib/vails';
import { OpmHrTransfer } from '@/engines/opm/opm-core/types/model';

const typeMap: any = {
  'Opm::IncrPositionTransfer': {
    label: '增岗',
    tag: '增岗',
    color: '#3F83F8',
    payload: {
      部门: 'payload.department.name',
      岗位: 'payload.duty.name',
    },
  },
  'Opm::RelocatePositionTransfer': {
    label: '转岗',
    tag: '调动',
    color: '#31C48D',
    payload: {
      原部门: 'payload.from_department.name',
      原岗位: 'payload.from_duty.name',
      新部门: 'payload.to_department.name',
      新岗位: 'payload.to_duty.name',
    },
  },

  'Opm::RevokePositionTransfer': {
    label: '撤岗',
    tag: '调动',
    color: '#F05252',
    payload: {
      部门: 'payload.department.name',
      岗位: 'payload.duty.name',
    },
  },
  'Opm::EmployTransfer': {
    label: '入职',
    tag: '入职',
    color: '#31C48D',
    payload: {
      部门: 'payload.department_name',
      岗位: 'payload.duty_name',
    },
  },
  'Opm::ResignationTransfer': {
    label: '离职',
    tag: '离职',
    color: '#F05252',
    payload: {
      部门: 'payload.departments_duties[0].department_name',
      岗位: 'payload.departments_duties[0].duty_name',
      离职原因: 'payload.leave_reason',
      离职时间: 'payload.leave_date',
    },
  },
};
export class OpmHrTransferModel extends VModel<OpmHrTransfer> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return ResDepartmentModel.stateMapping()[this.reactiveRecord.state];
  // });
  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }

  typeConfig = this.computedAttr('typeConfig', () => {
    if (this.reactiveRecord.type && this.reactiveRecord.type in typeMap) {
      return typeMap[this.reactiveRecord.type];
    }

    return {};
  });
}
