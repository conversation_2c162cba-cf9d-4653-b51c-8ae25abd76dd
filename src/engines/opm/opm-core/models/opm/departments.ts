import { VModel } from '@/lib/vails';
import { OpmDepartment } from '@/engines/opm/opm-core/types/model';
import { ref } from 'vue';

export class OpmDepartmentModel extends VModel<OpmDepartment> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return ResDepartmentModel.stateMapping()[this.reactiveRecord.state];
  // });
  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }

  pathNames = this.computedAttr('pathNames', () => {
    return (this.reactiveRecord.path_names || []).reverse();
  });
}

export class OpmDepartmentTreeModel extends OpmDepartmentModel {
  children = ref([]);
  tableChildren = this.reactiveRecord.children_count > 0 ? [] : null;

  isLeaf = this.computedAttr('isLeaf', () => {
    return (
      (!this.children.value || this.children.value.length === 0) &&
      this.reactiveRecord.children_count === 0
    );
  });

  key = this.computedAttr('key', () => {
    return this.reactiveRecord.id;
  });
  title = this.computedAttr('title', () => {
    return this.reactiveRecord.name;
  });
}
