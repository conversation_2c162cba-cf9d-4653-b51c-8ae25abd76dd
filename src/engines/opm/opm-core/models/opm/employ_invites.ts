import { VModel } from '@/lib/vails';
import { OpmEmployInvite } from '@/engines/opm/opm-core/types/model';

export class OpmEmployInviteModel extends VModel<OpmEmployInvite> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return ResDepartmentModel.stateMapping()[this.reactiveRecord.state];
  // });
  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }

  userCanRequest = this.computedAttr('userCanRequest', () => {
    return this.reactiveRecord.can_request;
  });
}
