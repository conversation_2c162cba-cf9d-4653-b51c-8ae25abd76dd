export interface ResDepartment {
  id: number;
  name: string;
  children_count: number;
}

export interface OpmDepartment {
  id: number;
  name: string;
  children_count: number;
  path_names: [];
}

export interface OpmDuty {
  id: number;
}

export interface OpmMember {
  id: number;
  duties: [];
  departments: [];
  departments_duties: [];
  duty_ranks: [];
  priorities: [];
}

export interface OpmOwnership {
  id: number;
}

export interface OpmDepartmentDuty {
  duty_id: number;
  duty_name: string;
  department_id: number;
  department_name: string;
  member_count: number;
}

export interface OpmEmployInvite {
  id: number;
  org_name: string;
  department_id: number;
  duty_id: number;
  ownership_id: number;
  can_request: boolean;
  creator: any;
}

export interface OpmHrTransfer {
  id: number;
  type: string;
  payload: any;
}

export interface OpmEmployRequest {
  id: number;
  employ_invite?: OpmEmployInvite;
}

export interface OpmIncrPositionRequest {
  id: number;
}

export interface OpmRevokePositionRequest {
  id: number;
  create_instance_state: string;
  updated_at: string;
}

export interface OpmRelocatePositionRequest {
  id: number;
}

export interface OpmResignationRequest {
  id: number;
  departments_duties?: [];
}
export interface OpmTag {
  id: number;
}
export interface OpmItem {
  id: number;
}
export interface OpmTag {
  id: number;
}
export interface OpmItem {
  id: number;
}
export interface OpmItem {
  id: number;
}
export interface OpmGroup {
  id: number;
}
export interface OpmJobTitle {
  id: number;
}
export interface OpmJobTitle {
  id: number;
}
export interface OpmRecord {
  id: number;
}
export interface OpmRecord {
  id: number;
}
export interface OpmLeaveRequest {
  id: number;
}
export interface OpmBalance {
  id: number;
}
export interface OpmTransaction {
  id: number;
}
