import { VApiConfig } from '@/lib/vails/api';
import { MyApi } from '@/apis/MyApi';
import { OpmTag } from '@/engines/opm/opm-core/types/model';

export class OpmManageTagsApi extends MyApi<OpmTag> {
  constructor(config?: VApiConfig) {
    super({
      namespace: '/opm/manage',
      name: 'tag',
      actions: [
        { name: 'add_members', method: 'post', on: 'member' },
        { name: 'remove_members', method: 'post', on: 'member' },
      ],
      ...config,
    });
  }
}
