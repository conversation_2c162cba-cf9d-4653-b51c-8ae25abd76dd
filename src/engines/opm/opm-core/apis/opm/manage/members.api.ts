import { VApiConfig } from '@/lib/vails/api';
import { MyApi } from '@/apis/MyApi';
import { OpmMember } from '@/engines/opm/opm-core/types/model';

export class OpmManageMembersApi extends MyApi<OpmMember> {
  constructor(config?: VApiConfig) {
    super({
      namespace: '/opm/manage',
      name: 'member',
      ...config,
      actions: [
        { name: 'remove', method: 'post', on: 'member' },
        { name: 'search', method: 'get', on: 'collection' },
        { name: 'main_position', method: 'post', on: 'member' },
      ],
    });
  }
}
