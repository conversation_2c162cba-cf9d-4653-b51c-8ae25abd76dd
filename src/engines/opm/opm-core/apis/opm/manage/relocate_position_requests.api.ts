import { VApiConfig } from '@/lib/vails/api';
import { MyApi } from '@/apis/MyApi';
import { OpmRelocatePositionRequest } from '@/engines/opm/opm-core/types/model';

export class OpmManageRelocatePositionRequestsApi extends MyApi<OpmRelocatePositionRequest> {
  constructor(config?: VApiConfig) {
    super({
      namespace: '/opm/manage',
      name: 'relocate_position_request',
      ...config,
    });
  }
}
