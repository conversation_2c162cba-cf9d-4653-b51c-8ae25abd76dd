import { VApiConfig } from '@/lib/vails/api';
import { MyApi } from '@/apis/MyApi';
import { OpmRevokePositionRequest } from '@/engines/opm/opm-core/types/model';

export class OpmManageRevokePositionRequestsApi extends MyApi<OpmRevokePositionRequest> {
  constructor(config?: VApiConfig) {
    super({
      namespace: '/opm/manage',
      name: 'revoke_position_request',
      ...config,
    });
  }
}
