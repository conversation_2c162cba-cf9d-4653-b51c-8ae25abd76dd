import { MyApi } from '@/apis/MyApi';
import { VApiConfig } from '@/lib/vails/api';
import { OpmBalance } from '../../../types/model';

export class OpmUserBalancesApi extends MyApi<OpmBalance> {
  constructor(config?: VApiConfig) {
    super({
      namespace: '/opm/user',
      name: 'balance',
      actions: [
        { name: 'summary', method: 'get', on: 'collection' },
        { name: 'detailed_balance', method: 'get', on: 'collection' },
      ],
      ...config,
    });
  }
}
