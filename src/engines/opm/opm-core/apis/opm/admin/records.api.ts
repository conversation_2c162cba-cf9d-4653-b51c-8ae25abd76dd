import { VApiConfig } from '@/lib/vails/api';
import { MyApi } from '@/apis/MyApi';
import { OpmRecord } from '@/engines/opm/opm-core/types/model';

export class OpmAdminRecordsApi extends MyApi<OpmRecord> {
  constructor(config?: VApiConfig) {
    super({
      namespace: '/opm/admin',
      name: 'record',
      actions: [
        { name: 'insert', method: 'post', on: 'collection' },
        { name: 'remove', method: 'post', on: 'collection' },
      ],
      ...config,
    });
  }
}
