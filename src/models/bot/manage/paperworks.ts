import { VModel, VObject } from '@/lib/vails';
import { BotPaperwork } from '@/types/model';

export class BotPaperworkModel extends VModel<BotPaperwork> {
  // stateConfig = this.computedAttr('stateConfig', () => {
  //  return BotPaperworkModel.stateMapping()[this.reactiveRecord.state];
  // });

  // static stateMapping(): VObject {
  //   return {
  //     pending: { label: '待提交', color: '#FBBF24' },
  //   };
  // }
}
