
/*
* Licensed to the Apache Software Foundation (ASF) under one
* or more contributor license agreements.  See the NOTICE file
* distributed with this work for additional information
* regarding copyright ownership.  The ASF licenses this file
* to you under the Apache License, Version 2.0 (the
* "License"); you may not use this file except in compliance
* with the License.  You may obtain a copy of the License at
*
*   http://www.apache.org/licenses/LICENSE-2.0
*
* Unless required by applicable law or agreed to in writing,
* software distributed under the License is distributed on an
* "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
* KIND, either express or implied.  See the License for the
* specific language governing permissions and limitations
* under the License.
*/

!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e((t="undefined"!=typeof globalThis?globalThis:t||self).echarts={})}(this,(function(t){"use strict";
/*! *****************************************************************************
    Copyright (c) Microsoft Corporation.

    Permission to use, copy, modify, and/or distribute this software for any
    purpose with or without fee is hereby granted.

    THE SOFTWARE IS PROVIDED "AS IS" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH
    REGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY
    AND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,
    INDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM
    LOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR
    OTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR
    PERFORMANCE OF THIS SOFTWARE.
    ***************************************************************************** */var e=function(t,n){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])},e(t,n)};function n(t,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function i(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(i.prototype=n.prototype,new i)}var i=function(){this.firefox=!1,this.ie=!1,this.edge=!1,this.newEdge=!1,this.weChat=!1},r=new function(){this.browser=new i,this.node=!1,this.wxa=!1,this.worker=!1,this.svgSupported=!1,this.touchEventsSupported=!1,this.pointerEventsSupported=!1,this.domSupported=!1,this.transformSupported=!1,this.transform3dSupported=!1,this.hasGlobalWindow="undefined"!=typeof window};"object"==typeof wx&&"function"==typeof wx.getSystemInfoSync?(r.wxa=!0,r.touchEventsSupported=!0):"undefined"==typeof document&&"undefined"!=typeof self?r.worker=!0:!r.hasGlobalWindow||"Deno"in window?(r.node=!0,r.svgSupported=!0):function(t,e){var n=e.browser,i=t.match(/Firefox\/([\d.]+)/),r=t.match(/MSIE\s([\d.]+)/)||t.match(/Trident\/.+?rv:(([\d.]+))/),o=t.match(/Edge?\/([\d.]+)/),a=/micromessenger/i.test(t);i&&(n.firefox=!0,n.version=i[1]);r&&(n.ie=!0,n.version=r[1]);o&&(n.edge=!0,n.version=o[1],n.newEdge=+o[1].split(".")[0]>18);a&&(n.weChat=!0);e.svgSupported="undefined"!=typeof SVGRect,e.touchEventsSupported="ontouchstart"in window&&!n.ie&&!n.edge,e.pointerEventsSupported="onpointerdown"in window&&(n.edge||n.ie&&+n.version>=11),e.domSupported="undefined"!=typeof document;var s=document.documentElement.style;e.transform3dSupported=(n.ie&&"transition"in s||n.edge||"WebKitCSSMatrix"in window&&"m11"in new WebKitCSSMatrix||"MozPerspective"in s)&&!("OTransition"in s),e.transformSupported=e.transform3dSupported||n.ie&&+n.version>=9}(navigator.userAgent,r);var o="12px sans-serif";var a,s,l=function(t){var e={};if("undefined"==typeof JSON)return e;for(var n=0;n<t.length;n++){var i=String.fromCharCode(n+32),r=(t.charCodeAt(n)-20)/100;e[i]=r}return e}("007LLmW'55;N0500LLLLLLLLLL00NNNLzWW\\\\WQb\\0FWLg\\bWb\\WQ\\WrWWQ000CL5LLFLL0LL**F*gLLLL5F0LF\\FFF5.5N"),u={createCanvas:function(){return"undefined"!=typeof document&&document.createElement("canvas")},measureText:function(t,e){if(!a){var n=u.createCanvas();a=n&&n.getContext("2d")}if(a)return s!==e&&(s=a.font=e||o),a.measureText(t);t=t||"";var i=/((?:\d+)?\.?\d*)px/.exec(e=e||o),r=i&&+i[1]||12,h=0;if(e.indexOf("mono")>=0)h=r*t.length;else for(var c=0;c<t.length;c++){var p=l[t[c]];h+=null==p?r:p*r}return{width:h}},loadImage:function(t,e,n){var i=new Image;return i.onload=e,i.onerror=n,i.src=t,i}};function h(t){for(var e in u)t[e]&&(u[e]=t[e])}var c=E(["Function","RegExp","Date","Error","CanvasGradient","CanvasPattern","Image","Canvas"],(function(t,e){return t["[object "+e+"]"]=!0,t}),{}),p=E(["Int8","Uint8","Uint8Clamped","Int16","Uint16","Int32","Uint32","Float32","Float64"],(function(t,e){return t["[object "+e+"Array]"]=!0,t}),{}),f=Object.prototype.toString,d=Array.prototype,g=d.forEach,y=d.filter,v=d.slice,m=d.map,_=function(){}.constructor,x=_?_.prototype:null,w="__proto__",b=2311;function S(){return b++}function M(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];"undefined"!=typeof console&&console.error.apply(console,t)}function T(t){if(null==t||"object"!=typeof t)return t;var e=t,n=f.call(t);if("[object Array]"===n){if(!ut(t)){e=[];for(var i=0,r=t.length;i<r;i++)e[i]=T(t[i])}}else if(p[n]){if(!ut(t)){var o=t.constructor;if(o.from)e=o.from(t);else{e=new o(t.length);for(i=0,r=t.length;i<r;i++)e[i]=t[i]}}}else if(!c[n]&&!ut(t)&&!j(t))for(var a in e={},t)t.hasOwnProperty(a)&&a!==w&&(e[a]=T(t[a]));return e}function C(t,e,n){if(!Y(e)||!Y(t))return n?T(e):t;for(var i in e)if(e.hasOwnProperty(i)&&i!==w){var r=t[i],o=e[i];!Y(o)||!Y(r)||H(o)||H(r)||j(o)||j(r)||q(o)||q(r)||ut(o)||ut(r)?!n&&i in t||(t[i]=T(e[i])):C(r,o,n)}return t}function D(t,e){if(Object.assign)Object.assign(t,e);else for(var n in e)e.hasOwnProperty(n)&&n!==w&&(t[n]=e[n]);return t}function I(t,e,n){for(var i=B(e),r=0,o=i.length;r<o;r++){var a=i[r];(n?null!=e[a]:null==t[a])&&(t[a]=e[a])}return t}var k=u.createCanvas;function A(t,e){if(t){if(t.indexOf)return t.indexOf(e);for(var n=0,i=t.length;n<i;n++)if(t[n]===e)return n}return-1}function L(t,e){var n=t.prototype;function i(){}for(var r in i.prototype=e.prototype,t.prototype=new i,n)n.hasOwnProperty(r)&&(t.prototype[r]=n[r]);t.prototype.constructor=t,t.superClass=e}function P(t,e,n){if(t="prototype"in t?t.prototype:t,e="prototype"in e?e.prototype:e,Object.getOwnPropertyNames)for(var i=Object.getOwnPropertyNames(e),r=0;r<i.length;r++){var o=i[r];"constructor"!==o&&(n?null!=e[o]:null==t[o])&&(t[o]=e[o])}else I(t,e,n)}function O(t){return!!t&&("string"!=typeof t&&"number"==typeof t.length)}function R(t,e,n){if(t&&e)if(t.forEach&&t.forEach===g)t.forEach(e,n);else if(t.length===+t.length)for(var i=0,r=t.length;i<r;i++)e.call(n,t[i],i,t);else for(var o in t)t.hasOwnProperty(o)&&e.call(n,t[o],o,t)}function N(t,e,n){if(!t)return[];if(!e)return it(t);if(t.map&&t.map===m)return t.map(e,n);for(var i=[],r=0,o=t.length;r<o;r++)i.push(e.call(n,t[r],r,t));return i}function E(t,e,n,i){if(t&&e){for(var r=0,o=t.length;r<o;r++)n=e.call(i,n,t[r],r,t);return n}}function z(t,e,n){if(!t)return[];if(!e)return it(t);if(t.filter&&t.filter===y)return t.filter(e,n);for(var i=[],r=0,o=t.length;r<o;r++)e.call(n,t[r],r,t)&&i.push(t[r]);return i}function B(t){if(!t)return[];if(Object.keys)return Object.keys(t);var e=[];for(var n in t)t.hasOwnProperty(n)&&e.push(n);return e}var F=x&&W(x.bind)?x.call.bind(x.bind):function(t,e){for(var n=[],i=2;i<arguments.length;i++)n[i-2]=arguments[i];return function(){return t.apply(e,n.concat(v.call(arguments)))}};function V(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];return function(){return t.apply(this,e.concat(v.call(arguments)))}}function H(t){return Array.isArray?Array.isArray(t):"[object Array]"===f.call(t)}function W(t){return"function"==typeof t}function G(t){return"string"==typeof t}function U(t){return"[object String]"===f.call(t)}function X(t){return"number"==typeof t}function Y(t){var e=typeof t;return"function"===e||!!t&&"object"===e}function q(t){return!!c[f.call(t)]}function Z(t){return!!p[f.call(t)]}function j(t){return"object"==typeof t&&"number"==typeof t.nodeType&&"object"==typeof t.ownerDocument}function K(t){return null!=t.colorStops}function $(t){return null!=t.image}function Q(t){return"[object RegExp]"===f.call(t)}function J(t){return t!=t}function tt(){for(var t=[],e=0;e<arguments.length;e++)t[e]=arguments[e];for(var n=0,i=t.length;n<i;n++)if(null!=t[n])return t[n]}function et(t,e){return null!=t?t:e}function nt(t,e,n){return null!=t?t:null!=e?e:n}function it(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];return v.apply(t,e)}function rt(t){if("number"==typeof t)return[t,t,t,t];var e=t.length;return 2===e?[t[0],t[1],t[0],t[1]]:3===e?[t[0],t[1],t[2],t[1]]:t}function ot(t,e){if(!t)throw new Error(e)}function at(t){return null==t?null:"function"==typeof t.trim?t.trim():t.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"")}var st="__ec_primitive__";function lt(t){t[st]=!0}function ut(t){return t[st]}var ht=function(){function t(){this.data={}}return t.prototype.delete=function(t){var e=this.has(t);return e&&delete this.data[t],e},t.prototype.has=function(t){return this.data.hasOwnProperty(t)},t.prototype.get=function(t){return this.data[t]},t.prototype.set=function(t,e){return this.data[t]=e,this},t.prototype.keys=function(){return B(this.data)},t.prototype.forEach=function(t){var e=this.data;for(var n in e)e.hasOwnProperty(n)&&t(e[n],n)},t}(),ct="function"==typeof Map;var pt=function(){function t(e){var n=H(e);this.data=ct?new Map:new ht;var i=this;function r(t,e){n?i.set(t,e):i.set(e,t)}e instanceof t?e.each(r):e&&R(e,r)}return t.prototype.hasKey=function(t){return this.data.has(t)},t.prototype.get=function(t){return this.data.get(t)},t.prototype.set=function(t,e){return this.data.set(t,e),e},t.prototype.each=function(t,e){this.data.forEach((function(n,i){t.call(e,n,i)}))},t.prototype.keys=function(){var t=this.data.keys();return ct?Array.from(t):t},t.prototype.removeKey=function(t){this.data.delete(t)},t}();function ft(t){return new pt(t)}function dt(t,e){for(var n=new t.constructor(t.length+e.length),i=0;i<t.length;i++)n[i]=t[i];var r=t.length;for(i=0;i<e.length;i++)n[i+r]=e[i];return n}function gt(t,e){var n;if(Object.create)n=Object.create(t);else{var i=function(){};i.prototype=t,n=new i}return e&&D(n,e),n}function yt(t){var e=t.style;e.webkitUserSelect="none",e.userSelect="none",e.webkitTapHighlightColor="rgba(0,0,0,0)",e["-webkit-touch-callout"]="none"}function vt(t,e){return t.hasOwnProperty(e)}function mt(){}var _t=180/Math.PI,xt=Object.freeze({__proto__:null,guid:S,logError:M,clone:T,merge:C,mergeAll:function(t,e){for(var n=t[0],i=1,r=t.length;i<r;i++)n=C(n,t[i],e);return n},extend:D,defaults:I,createCanvas:k,indexOf:A,inherits:L,mixin:P,isArrayLike:O,each:R,map:N,reduce:E,filter:z,find:function(t,e,n){if(t&&e)for(var i=0,r=t.length;i<r;i++)if(e.call(n,t[i],i,t))return t[i]},keys:B,bind:F,curry:V,isArray:H,isFunction:W,isString:G,isStringSafe:U,isNumber:X,isObject:Y,isBuiltInObject:q,isTypedArray:Z,isDom:j,isGradientObject:K,isImagePatternObject:$,isRegExp:Q,eqNaN:J,retrieve:tt,retrieve2:et,retrieve3:nt,slice:it,normalizeCssArray:rt,assert:ot,trim:at,setAsPrimitive:lt,isPrimitive:ut,HashMap:pt,createHashMap:ft,concatArray:dt,createObject:gt,disableUserSelect:yt,hasOwn:vt,noop:mt,RADIAN_TO_DEGREE:_t});function wt(t,e){return null==t&&(t=0),null==e&&(e=0),[t,e]}function bt(t){return[t[0],t[1]]}function St(t,e,n){return t[0]=e[0]+n[0],t[1]=e[1]+n[1],t}function Mt(t,e,n){return t[0]=e[0]-n[0],t[1]=e[1]-n[1],t}function Tt(t){return Math.sqrt(Dt(t))}var Ct=Tt;function Dt(t){return t[0]*t[0]+t[1]*t[1]}var It=Dt;function kt(t,e,n){return t[0]=e[0]*n,t[1]=e[1]*n,t}function At(t,e){var n=Tt(e);return 0===n?(t[0]=0,t[1]=0):(t[0]=e[0]/n,t[1]=e[1]/n),t}function Lt(t,e){return Math.sqrt((t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1]))}var Pt=Lt;function Ot(t,e){return(t[0]-e[0])*(t[0]-e[0])+(t[1]-e[1])*(t[1]-e[1])}var Rt=Ot;function Nt(t,e,n,i){return t[0]=e[0]+i*(n[0]-e[0]),t[1]=e[1]+i*(n[1]-e[1]),t}function Et(t,e,n){var i=e[0],r=e[1];return t[0]=n[0]*i+n[2]*r+n[4],t[1]=n[1]*i+n[3]*r+n[5],t}function zt(t,e,n){return t[0]=Math.min(e[0],n[0]),t[1]=Math.min(e[1],n[1]),t}function Bt(t,e,n){return t[0]=Math.max(e[0],n[0]),t[1]=Math.max(e[1],n[1]),t}var Ft=Object.freeze({__proto__:null,create:wt,copy:function(t,e){return t[0]=e[0],t[1]=e[1],t},clone:bt,set:function(t,e,n){return t[0]=e,t[1]=n,t},add:St,scaleAndAdd:function(t,e,n,i){return t[0]=e[0]+n[0]*i,t[1]=e[1]+n[1]*i,t},sub:Mt,len:Tt,length:Ct,lenSquare:Dt,lengthSquare:It,mul:function(t,e,n){return t[0]=e[0]*n[0],t[1]=e[1]*n[1],t},div:function(t,e,n){return t[0]=e[0]/n[0],t[1]=e[1]/n[1],t},dot:function(t,e){return t[0]*e[0]+t[1]*e[1]},scale:kt,normalize:At,distance:Lt,dist:Pt,distanceSquare:Ot,distSquare:Rt,negate:function(t,e){return t[0]=-e[0],t[1]=-e[1],t},lerp:Nt,applyTransform:Et,min:zt,max:Bt}),Vt=function(t,e){this.target=t,this.topTarget=e&&e.topTarget},Ht=function(){function t(t){this.handler=t,t.on("mousedown",this._dragStart,this),t.on("mousemove",this._drag,this),t.on("mouseup",this._dragEnd,this)}return t.prototype._dragStart=function(t){for(var e=t.target;e&&!e.draggable;)e=e.parent||e.__hostTarget;e&&(this._draggingTarget=e,e.dragging=!0,this._x=t.offsetX,this._y=t.offsetY,this.handler.dispatchToElement(new Vt(e,t),"dragstart",t.event))},t.prototype._drag=function(t){var e=this._draggingTarget;if(e){var n=t.offsetX,i=t.offsetY,r=n-this._x,o=i-this._y;this._x=n,this._y=i,e.drift(r,o,t),this.handler.dispatchToElement(new Vt(e,t),"drag",t.event);var a=this.handler.findHover(n,i,e).target,s=this._dropTarget;this._dropTarget=a,e!==a&&(s&&a!==s&&this.handler.dispatchToElement(new Vt(s,t),"dragleave",t.event),a&&a!==s&&this.handler.dispatchToElement(new Vt(a,t),"dragenter",t.event))}},t.prototype._dragEnd=function(t){var e=this._draggingTarget;e&&(e.dragging=!1),this.handler.dispatchToElement(new Vt(e,t),"dragend",t.event),this._dropTarget&&this.handler.dispatchToElement(new Vt(this._dropTarget,t),"drop",t.event),this._draggingTarget=null,this._dropTarget=null},t}(),Wt=function(){function t(t){t&&(this._$eventProcessor=t)}return t.prototype.on=function(t,e,n,i){this._$handlers||(this._$handlers={});var r=this._$handlers;if("function"==typeof e&&(i=n,n=e,e=null),!n||!t)return this;var o=this._$eventProcessor;null!=e&&o&&o.normalizeQuery&&(e=o.normalizeQuery(e)),r[t]||(r[t]=[]);for(var a=0;a<r[t].length;a++)if(r[t][a].h===n)return this;var s={h:n,query:e,ctx:i||this,callAtLast:n.zrEventfulCallAtLast},l=r[t].length-1,u=r[t][l];return u&&u.callAtLast?r[t].splice(l,0,s):r[t].push(s),this},t.prototype.isSilent=function(t){var e=this._$handlers;return!e||!e[t]||!e[t].length},t.prototype.off=function(t,e){var n=this._$handlers;if(!n)return this;if(!t)return this._$handlers={},this;if(e){if(n[t]){for(var i=[],r=0,o=n[t].length;r<o;r++)n[t][r].h!==e&&i.push(n[t][r]);n[t]=i}n[t]&&0===n[t].length&&delete n[t]}else delete n[t];return this},t.prototype.trigger=function(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];if(!this._$handlers)return this;var i=this._$handlers[t],r=this._$eventProcessor;if(i)for(var o=e.length,a=i.length,s=0;s<a;s++){var l=i[s];if(!r||!r.filter||null==l.query||r.filter(t,l.query))switch(o){case 0:l.h.call(l.ctx);break;case 1:l.h.call(l.ctx,e[0]);break;case 2:l.h.call(l.ctx,e[0],e[1]);break;default:l.h.apply(l.ctx,e)}}return r&&r.afterTrigger&&r.afterTrigger(t),this},t.prototype.triggerWithContext=function(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];if(!this._$handlers)return this;var i=this._$handlers[t],r=this._$eventProcessor;if(i)for(var o=e.length,a=e[o-1],s=i.length,l=0;l<s;l++){var u=i[l];if(!r||!r.filter||null==u.query||r.filter(t,u.query))switch(o){case 0:u.h.call(a);break;case 1:u.h.call(a,e[0]);break;case 2:u.h.call(a,e[0],e[1]);break;default:u.h.apply(a,e.slice(1,o-1))}}return r&&r.afterTrigger&&r.afterTrigger(t),this},t}(),Gt=Math.log(2);function Ut(t,e,n,i,r,o){var a=i+"-"+r,s=t.length;if(o.hasOwnProperty(a))return o[a];if(1===e){var l=Math.round(Math.log((1<<s)-1&~r)/Gt);return t[n][l]}for(var u=i|1<<n,h=n+1;i&1<<h;)h++;for(var c=0,p=0,f=0;p<s;p++){var d=1<<p;d&r||(c+=(f%2?-1:1)*t[n][p]*Ut(t,e-1,h,u,r|d,o),f++)}return o[a]=c,c}function Xt(t,e){var n=[[t[0],t[1],1,0,0,0,-e[0]*t[0],-e[0]*t[1]],[0,0,0,t[0],t[1],1,-e[1]*t[0],-e[1]*t[1]],[t[2],t[3],1,0,0,0,-e[2]*t[2],-e[2]*t[3]],[0,0,0,t[2],t[3],1,-e[3]*t[2],-e[3]*t[3]],[t[4],t[5],1,0,0,0,-e[4]*t[4],-e[4]*t[5]],[0,0,0,t[4],t[5],1,-e[5]*t[4],-e[5]*t[5]],[t[6],t[7],1,0,0,0,-e[6]*t[6],-e[6]*t[7]],[0,0,0,t[6],t[7],1,-e[7]*t[6],-e[7]*t[7]]],i={},r=Ut(n,8,0,0,0,i);if(0!==r){for(var o=[],a=0;a<8;a++)for(var s=0;s<8;s++)null==o[s]&&(o[s]=0),o[s]+=((a+s)%2?-1:1)*Ut(n,7,0===a?1:0,1<<a,1<<s,i)/r*e[a];return function(t,e,n){var i=e*o[6]+n*o[7]+1;t[0]=(e*o[0]+n*o[1]+o[2])/i,t[1]=(e*o[3]+n*o[4]+o[5])/i}}}var Yt="___zrEVENTSAVED";function qt(t,e,n,i,o){if(e.getBoundingClientRect&&r.domSupported&&!Zt(e)){var a=e[Yt]||(e[Yt]={}),s=function(t,e){var n=e.markers;if(n)return n;n=e.markers=[];for(var i=["left","right"],r=["top","bottom"],o=0;o<4;o++){var a=document.createElement("div"),s=o%2,l=(o>>1)%2;a.style.cssText=["position: absolute","visibility: hidden","padding: 0","margin: 0","border-width: 0","user-select: none","width:0","height:0",i[s]+":0",r[l]+":0",i[1-s]+":auto",r[1-l]+":auto",""].join("!important;"),t.appendChild(a),n.push(a)}return n}(e,a),l=function(t,e,n){for(var i=n?"invTrans":"trans",r=e[i],o=e.srcCoords,a=[],s=[],l=!0,u=0;u<4;u++){var h=t[u].getBoundingClientRect(),c=2*u,p=h.left,f=h.top;a.push(p,f),l=l&&o&&p===o[c]&&f===o[c+1],s.push(t[u].offsetLeft,t[u].offsetTop)}return l&&r?r:(e.srcCoords=a,e[i]=n?Xt(s,a):Xt(a,s))}(s,a,o);if(l)return l(t,n,i),!0}return!1}function Zt(t){return"CANVAS"===t.nodeName.toUpperCase()}var jt=/([&<>"'])/g,Kt={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};function $t(t){return null==t?"":(t+"").replace(jt,(function(t,e){return Kt[e]}))}var Qt=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,Jt=[],te=r.browser.firefox&&+r.browser.version.split(".")[0]<39;function ee(t,e,n,i){return n=n||{},i?ne(t,e,n):te&&null!=e.layerX&&e.layerX!==e.offsetX?(n.zrX=e.layerX,n.zrY=e.layerY):null!=e.offsetX?(n.zrX=e.offsetX,n.zrY=e.offsetY):ne(t,e,n),n}function ne(t,e,n){if(r.domSupported&&t.getBoundingClientRect){var i=e.clientX,o=e.clientY;if(Zt(t)){var a=t.getBoundingClientRect();return n.zrX=i-a.left,void(n.zrY=o-a.top)}if(qt(Jt,t,i,o))return n.zrX=Jt[0],void(n.zrY=Jt[1])}n.zrX=n.zrY=0}function ie(t){return t||window.event}function re(t,e,n){if(null!=(e=ie(e)).zrX)return e;var i=e.type;if(i&&i.indexOf("touch")>=0){var r="touchend"!==i?e.targetTouches[0]:e.changedTouches[0];r&&ee(t,r,e,n)}else{ee(t,e,e,n);var o=function(t){var e=t.wheelDelta;if(e)return e;var n=t.deltaX,i=t.deltaY;if(null==n||null==i)return e;return 3*(0!==i?Math.abs(i):Math.abs(n))*(i>0?-1:i<0?1:n>0?-1:1)}(e);e.zrDelta=o?o/120:-(e.detail||0)/3}var a=e.button;return null==e.which&&void 0!==a&&Qt.test(e.type)&&(e.which=1&a?1:2&a?3:4&a?2:0),e}var oe=function(){function t(){this._track=[]}return t.prototype.recognize=function(t,e,n){return this._doTrack(t,e,n),this._recognize(t)},t.prototype.clear=function(){return this._track.length=0,this},t.prototype._doTrack=function(t,e,n){var i=t.touches;if(i){for(var r={points:[],touches:[],target:e,event:t},o=0,a=i.length;o<a;o++){var s=i[o],l=ee(n,s,{});r.points.push([l.zrX,l.zrY]),r.touches.push(s)}this._track.push(r)}},t.prototype._recognize=function(t){for(var e in se)if(se.hasOwnProperty(e)){var n=se[e](this._track,t);if(n)return n}},t}();function ae(t){var e=t[1][0]-t[0][0],n=t[1][1]-t[0][1];return Math.sqrt(e*e+n*n)}var se={pinch:function(t,e){var n=t.length;if(n){var i,r=(t[n-1]||{}).points,o=(t[n-2]||{}).points||r;if(o&&o.length>1&&r&&r.length>1){var a=ae(r)/ae(o);!isFinite(a)&&(a=1),e.pinchScale=a;var s=[((i=r)[0][0]+i[1][0])/2,(i[0][1]+i[1][1])/2];return e.pinchX=s[0],e.pinchY=s[1],{type:"pinch",target:t[0].target,event:e}}}}};function le(){return[1,0,0,1,0,0]}function ue(t){return t[0]=1,t[1]=0,t[2]=0,t[3]=1,t[4]=0,t[5]=0,t}function he(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4],t[5]=e[5],t}function ce(t,e,n){var i=e[0]*n[0]+e[2]*n[1],r=e[1]*n[0]+e[3]*n[1],o=e[0]*n[2]+e[2]*n[3],a=e[1]*n[2]+e[3]*n[3],s=e[0]*n[4]+e[2]*n[5]+e[4],l=e[1]*n[4]+e[3]*n[5]+e[5];return t[0]=i,t[1]=r,t[2]=o,t[3]=a,t[4]=s,t[5]=l,t}function pe(t,e,n){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t[4]=e[4]+n[0],t[5]=e[5]+n[1],t}function fe(t,e,n,i){void 0===i&&(i=[0,0]);var r=e[0],o=e[2],a=e[4],s=e[1],l=e[3],u=e[5],h=Math.sin(n),c=Math.cos(n);return t[0]=r*c+s*h,t[1]=-r*h+s*c,t[2]=o*c+l*h,t[3]=-o*h+c*l,t[4]=c*(a-i[0])+h*(u-i[1])+i[0],t[5]=c*(u-i[1])-h*(a-i[0])+i[1],t}function de(t,e,n){var i=n[0],r=n[1];return t[0]=e[0]*i,t[1]=e[1]*r,t[2]=e[2]*i,t[3]=e[3]*r,t[4]=e[4]*i,t[5]=e[5]*r,t}function ge(t,e){var n=e[0],i=e[2],r=e[4],o=e[1],a=e[3],s=e[5],l=n*a-o*i;return l?(l=1/l,t[0]=a*l,t[1]=-o*l,t[2]=-i*l,t[3]=n*l,t[4]=(i*s-a*r)*l,t[5]=(o*r-n*s)*l,t):null}var ye=Object.freeze({__proto__:null,create:le,identity:ue,copy:he,mul:ce,translate:pe,rotate:fe,scale:de,invert:ge,clone:function(t){var e=[1,0,0,1,0,0];return he(e,t),e}}),ve=function(){function t(t,e){this.x=t||0,this.y=e||0}return t.prototype.copy=function(t){return this.x=t.x,this.y=t.y,this},t.prototype.clone=function(){return new t(this.x,this.y)},t.prototype.set=function(t,e){return this.x=t,this.y=e,this},t.prototype.equal=function(t){return t.x===this.x&&t.y===this.y},t.prototype.add=function(t){return this.x+=t.x,this.y+=t.y,this},t.prototype.scale=function(t){this.x*=t,this.y*=t},t.prototype.scaleAndAdd=function(t,e){this.x+=t.x*e,this.y+=t.y*e},t.prototype.sub=function(t){return this.x-=t.x,this.y-=t.y,this},t.prototype.dot=function(t){return this.x*t.x+this.y*t.y},t.prototype.len=function(){return Math.sqrt(this.x*this.x+this.y*this.y)},t.prototype.lenSquare=function(){return this.x*this.x+this.y*this.y},t.prototype.normalize=function(){var t=this.len();return this.x/=t,this.y/=t,this},t.prototype.distance=function(t){var e=this.x-t.x,n=this.y-t.y;return Math.sqrt(e*e+n*n)},t.prototype.distanceSquare=function(t){var e=this.x-t.x,n=this.y-t.y;return e*e+n*n},t.prototype.negate=function(){return this.x=-this.x,this.y=-this.y,this},t.prototype.transform=function(t){if(t){var e=this.x,n=this.y;return this.x=t[0]*e+t[2]*n+t[4],this.y=t[1]*e+t[3]*n+t[5],this}},t.prototype.toArray=function(t){return t[0]=this.x,t[1]=this.y,t},t.prototype.fromArray=function(t){this.x=t[0],this.y=t[1]},t.set=function(t,e,n){t.x=e,t.y=n},t.copy=function(t,e){t.x=e.x,t.y=e.y},t.len=function(t){return Math.sqrt(t.x*t.x+t.y*t.y)},t.lenSquare=function(t){return t.x*t.x+t.y*t.y},t.dot=function(t,e){return t.x*e.x+t.y*e.y},t.add=function(t,e,n){t.x=e.x+n.x,t.y=e.y+n.y},t.sub=function(t,e,n){t.x=e.x-n.x,t.y=e.y-n.y},t.scale=function(t,e,n){t.x=e.x*n,t.y=e.y*n},t.scaleAndAdd=function(t,e,n,i){t.x=e.x+n.x*i,t.y=e.y+n.y*i},t.lerp=function(t,e,n,i){var r=1-i;t.x=r*e.x+i*n.x,t.y=r*e.y+i*n.y},t}(),me=Math.min,_e=Math.max,xe=new ve,we=new ve,be=new ve,Se=new ve,Me=new ve,Te=new ve,Ce=function(){function t(t,e,n,i){n<0&&(t+=n,n=-n),i<0&&(e+=i,i=-i),this.x=t,this.y=e,this.width=n,this.height=i}return t.prototype.union=function(t){var e=me(t.x,this.x),n=me(t.y,this.y);isFinite(this.x)&&isFinite(this.width)?this.width=_e(t.x+t.width,this.x+this.width)-e:this.width=t.width,isFinite(this.y)&&isFinite(this.height)?this.height=_e(t.y+t.height,this.y+this.height)-n:this.height=t.height,this.x=e,this.y=n},t.prototype.applyTransform=function(e){t.applyTransform(this,this,e)},t.prototype.calculateTransform=function(t){var e=this,n=t.width/e.width,i=t.height/e.height,r=[1,0,0,1,0,0];return pe(r,r,[-e.x,-e.y]),de(r,r,[n,i]),pe(r,r,[t.x,t.y]),r},t.prototype.intersect=function(e,n){if(!e)return!1;e instanceof t||(e=t.create(e));var i=this,r=i.x,o=i.x+i.width,a=i.y,s=i.y+i.height,l=e.x,u=e.x+e.width,h=e.y,c=e.y+e.height,p=!(o<l||u<r||s<h||c<a);if(n){var f=1/0,d=0,g=Math.abs(o-l),y=Math.abs(u-r),v=Math.abs(s-h),m=Math.abs(c-a),_=Math.min(g,y),x=Math.min(v,m);o<l||u<r?_>d&&(d=_,g<y?ve.set(Te,-g,0):ve.set(Te,y,0)):_<f&&(f=_,g<y?ve.set(Me,g,0):ve.set(Me,-y,0)),s<h||c<a?x>d&&(d=x,v<m?ve.set(Te,0,-v):ve.set(Te,0,m)):_<f&&(f=_,v<m?ve.set(Me,0,v):ve.set(Me,0,-m))}return n&&ve.copy(n,p?Me:Te),p},t.prototype.contain=function(t,e){var n=this;return t>=n.x&&t<=n.x+n.width&&e>=n.y&&e<=n.y+n.height},t.prototype.clone=function(){return new t(this.x,this.y,this.width,this.height)},t.prototype.copy=function(e){t.copy(this,e)},t.prototype.plain=function(){return{x:this.x,y:this.y,width:this.width,height:this.height}},t.prototype.isFinite=function(){return isFinite(this.x)&&isFinite(this.y)&&isFinite(this.width)&&isFinite(this.height)},t.prototype.isZero=function(){return 0===this.width||0===this.height},t.create=function(e){return new t(e.x,e.y,e.width,e.height)},t.copy=function(t,e){t.x=e.x,t.y=e.y,t.width=e.width,t.height=e.height},t.applyTransform=function(e,n,i){if(i){if(i[1]<1e-5&&i[1]>-1e-5&&i[2]<1e-5&&i[2]>-1e-5){var r=i[0],o=i[3],a=i[4],s=i[5];return e.x=n.x*r+a,e.y=n.y*o+s,e.width=n.width*r,e.height=n.height*o,e.width<0&&(e.x+=e.width,e.width=-e.width),void(e.height<0&&(e.y+=e.height,e.height=-e.height))}xe.x=be.x=n.x,xe.y=Se.y=n.y,we.x=Se.x=n.x+n.width,we.y=be.y=n.y+n.height,xe.transform(i),Se.transform(i),we.transform(i),be.transform(i),e.x=me(xe.x,we.x,be.x,Se.x),e.y=me(xe.y,we.y,be.y,Se.y);var l=_e(xe.x,we.x,be.x,Se.x),u=_e(xe.y,we.y,be.y,Se.y);e.width=l-e.x,e.height=u-e.y}else e!==n&&t.copy(e,n)},t}(),De="silent";function Ie(){!function(t){t.preventDefault(),t.stopPropagation(),t.cancelBubble=!0}(this.event)}var ke=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.handler=null,e}return n(e,t),e.prototype.dispose=function(){},e.prototype.setCursor=function(){},e}(Wt),Ae=function(t,e){this.x=t,this.y=e},Le=["click","dblclick","mousewheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],Pe=new Ce(0,0,0,0),Oe=function(t){function e(e,n,i,r,o){var a=t.call(this)||this;return a._hovered=new Ae(0,0),a.storage=e,a.painter=n,a.painterRoot=r,a._pointerSize=o,i=i||new ke,a.proxy=null,a.setHandlerProxy(i),a._draggingMgr=new Ht(a),a}return n(e,t),e.prototype.setHandlerProxy=function(t){this.proxy&&this.proxy.dispose(),t&&(R(Le,(function(e){t.on&&t.on(e,this[e],this)}),this),t.handler=this),this.proxy=t},e.prototype.mousemove=function(t){var e=t.zrX,n=t.zrY,i=Ee(this,e,n),r=this._hovered,o=r.target;o&&!o.__zr&&(o=(r=this.findHover(r.x,r.y)).target);var a=this._hovered=i?new Ae(e,n):this.findHover(e,n),s=a.target,l=this.proxy;l.setCursor&&l.setCursor(s?s.cursor:"default"),o&&s!==o&&this.dispatchToElement(r,"mouseout",t),this.dispatchToElement(a,"mousemove",t),s&&s!==o&&this.dispatchToElement(a,"mouseover",t)},e.prototype.mouseout=function(t){var e=t.zrEventControl;"only_globalout"!==e&&this.dispatchToElement(this._hovered,"mouseout",t),"no_globalout"!==e&&this.trigger("globalout",{type:"globalout",event:t})},e.prototype.resize=function(){this._hovered=new Ae(0,0)},e.prototype.dispatch=function(t,e){var n=this[t];n&&n.call(this,e)},e.prototype.dispose=function(){this.proxy.dispose(),this.storage=null,this.proxy=null,this.painter=null},e.prototype.setCursorStyle=function(t){var e=this.proxy;e.setCursor&&e.setCursor(t)},e.prototype.dispatchToElement=function(t,e,n){var i=(t=t||{}).target;if(!i||!i.silent){for(var r="on"+e,o=function(t,e,n){return{type:t,event:n,target:e.target,topTarget:e.topTarget,cancelBubble:!1,offsetX:n.zrX,offsetY:n.zrY,gestureEvent:n.gestureEvent,pinchX:n.pinchX,pinchY:n.pinchY,pinchScale:n.pinchScale,wheelDelta:n.zrDelta,zrByTouch:n.zrByTouch,which:n.which,stop:Ie}}(e,t,n);i&&(i[r]&&(o.cancelBubble=!!i[r].call(i,o)),i.trigger(e,o),i=i.__hostTarget?i.__hostTarget:i.parent,!o.cancelBubble););o.cancelBubble||(this.trigger(e,o),this.painter&&this.painter.eachOtherLayer&&this.painter.eachOtherLayer((function(t){"function"==typeof t[r]&&t[r].call(t,o),t.trigger&&t.trigger(e,o)})))}},e.prototype.findHover=function(t,e,n){var i=this.storage.getDisplayList(),r=new Ae(t,e);if(Ne(i,r,t,e,n),this._pointerSize&&!r.target){for(var o=[],a=this._pointerSize,s=a/2,l=new Ce(t-s,e-s,a,a),u=i.length-1;u>=0;u--){var h=i[u];h===n||h.ignore||h.ignoreCoarsePointer||h.parent&&h.parent.ignoreCoarsePointer||(Pe.copy(h.getBoundingRect()),h.transform&&Pe.applyTransform(h.transform),Pe.intersect(l)&&o.push(h))}if(o.length)for(var c=Math.PI/12,p=2*Math.PI,f=0;f<s;f+=4)for(var d=0;d<p;d+=c){if(Ne(o,r,t+f*Math.cos(d),e+f*Math.sin(d),n),r.target)return r}}return r},e.prototype.processGesture=function(t,e){this._gestureMgr||(this._gestureMgr=new oe);var n=this._gestureMgr;"start"===e&&n.clear();var i=n.recognize(t,this.findHover(t.zrX,t.zrY,null).target,this.proxy.dom);if("end"===e&&n.clear(),i){var r=i.type;t.gestureEvent=r;var o=new Ae;o.target=i.target,this.dispatchToElement(o,r,i.event)}},e}(Wt);function Re(t,e,n){if(t[t.rectHover?"rectContain":"contain"](e,n)){for(var i=t,r=void 0,o=!1;i;){if(i.ignoreClip&&(o=!0),!o){var a=i.getClipPath();if(a&&!a.contain(e,n))return!1}i.silent&&(r=!0);var s=i.__hostTarget;i=s||i.parent}return!r||De}return!1}function Ne(t,e,n,i,r){for(var o=t.length-1;o>=0;o--){var a=t[o],s=void 0;if(a!==r&&!a.ignore&&(s=Re(a,n,i))&&(!e.topTarget&&(e.topTarget=a),s!==De)){e.target=a;break}}}function Ee(t,e,n){var i=t.painter;return e<0||e>i.getWidth()||n<0||n>i.getHeight()}R(["click","mousedown","mouseup","mousewheel","dblclick","contextmenu"],(function(t){Oe.prototype[t]=function(e){var n,i,r=e.zrX,o=e.zrY,a=Ee(this,r,o);if("mouseup"===t&&a||(i=(n=this.findHover(r,o)).target),"mousedown"===t)this._downEl=i,this._downPoint=[e.zrX,e.zrY],this._upEl=i;else if("mouseup"===t)this._upEl=i;else if("click"===t){if(this._downEl!==this._upEl||!this._downPoint||Pt(this._downPoint,[e.zrX,e.zrY])>4)return;this._downPoint=null}this.dispatchToElement(n,t,e)}}));function ze(t,e,n,i){var r=e+1;if(r===n)return 1;if(i(t[r++],t[e])<0){for(;r<n&&i(t[r],t[r-1])<0;)r++;!function(t,e,n){n--;for(;e<n;){var i=t[e];t[e++]=t[n],t[n--]=i}}(t,e,r)}else for(;r<n&&i(t[r],t[r-1])>=0;)r++;return r-e}function Be(t,e,n,i,r){for(i===e&&i++;i<n;i++){for(var o,a=t[i],s=e,l=i;s<l;)r(a,t[o=s+l>>>1])<0?l=o:s=o+1;var u=i-s;switch(u){case 3:t[s+3]=t[s+2];case 2:t[s+2]=t[s+1];case 1:t[s+1]=t[s];break;default:for(;u>0;)t[s+u]=t[s+u-1],u--}t[s]=a}}function Fe(t,e,n,i,r,o){var a=0,s=0,l=1;if(o(t,e[n+r])>0){for(s=i-r;l<s&&o(t,e[n+r+l])>0;)a=l,(l=1+(l<<1))<=0&&(l=s);l>s&&(l=s),a+=r,l+=r}else{for(s=r+1;l<s&&o(t,e[n+r-l])<=0;)a=l,(l=1+(l<<1))<=0&&(l=s);l>s&&(l=s);var u=a;a=r-l,l=r-u}for(a++;a<l;){var h=a+(l-a>>>1);o(t,e[n+h])>0?a=h+1:l=h}return l}function Ve(t,e,n,i,r,o){var a=0,s=0,l=1;if(o(t,e[n+r])<0){for(s=r+1;l<s&&o(t,e[n+r-l])<0;)a=l,(l=1+(l<<1))<=0&&(l=s);l>s&&(l=s);var u=a;a=r-l,l=r-u}else{for(s=i-r;l<s&&o(t,e[n+r+l])>=0;)a=l,(l=1+(l<<1))<=0&&(l=s);l>s&&(l=s),a+=r,l+=r}for(a++;a<l;){var h=a+(l-a>>>1);o(t,e[n+h])<0?l=h:a=h+1}return l}function He(t,e){var n,i,r=7,o=0,a=[];function s(s){var l=n[s],u=i[s],h=n[s+1],c=i[s+1];i[s]=u+c,s===o-3&&(n[s+1]=n[s+2],i[s+1]=i[s+2]),o--;var p=Ve(t[h],t,l,u,0,e);l+=p,0!==(u-=p)&&0!==(c=Fe(t[l+u-1],t,h,c,c-1,e))&&(u<=c?function(n,i,o,s){var l=0;for(l=0;l<i;l++)a[l]=t[n+l];var u=0,h=o,c=n;if(t[c++]=t[h++],0==--s){for(l=0;l<i;l++)t[c+l]=a[u+l];return}if(1===i){for(l=0;l<s;l++)t[c+l]=t[h+l];return void(t[c+s]=a[u])}var p,f,d,g=r;for(;;){p=0,f=0,d=!1;do{if(e(t[h],a[u])<0){if(t[c++]=t[h++],f++,p=0,0==--s){d=!0;break}}else if(t[c++]=a[u++],p++,f=0,1==--i){d=!0;break}}while((p|f)<g);if(d)break;do{if(0!==(p=Ve(t[h],a,u,i,0,e))){for(l=0;l<p;l++)t[c+l]=a[u+l];if(c+=p,u+=p,(i-=p)<=1){d=!0;break}}if(t[c++]=t[h++],0==--s){d=!0;break}if(0!==(f=Fe(a[u],t,h,s,0,e))){for(l=0;l<f;l++)t[c+l]=t[h+l];if(c+=f,h+=f,0===(s-=f)){d=!0;break}}if(t[c++]=a[u++],1==--i){d=!0;break}g--}while(p>=7||f>=7);if(d)break;g<0&&(g=0),g+=2}if((r=g)<1&&(r=1),1===i){for(l=0;l<s;l++)t[c+l]=t[h+l];t[c+s]=a[u]}else{if(0===i)throw new Error;for(l=0;l<i;l++)t[c+l]=a[u+l]}}(l,u,h,c):function(n,i,o,s){var l=0;for(l=0;l<s;l++)a[l]=t[o+l];var u=n+i-1,h=s-1,c=o+s-1,p=0,f=0;if(t[c--]=t[u--],0==--i){for(p=c-(s-1),l=0;l<s;l++)t[p+l]=a[l];return}if(1===s){for(f=(c-=i)+1,p=(u-=i)+1,l=i-1;l>=0;l--)t[f+l]=t[p+l];return void(t[c]=a[h])}var d=r;for(;;){var g=0,y=0,v=!1;do{if(e(a[h],t[u])<0){if(t[c--]=t[u--],g++,y=0,0==--i){v=!0;break}}else if(t[c--]=a[h--],y++,g=0,1==--s){v=!0;break}}while((g|y)<d);if(v)break;do{if(0!==(g=i-Ve(a[h],t,n,i,i-1,e))){for(i-=g,f=(c-=g)+1,p=(u-=g)+1,l=g-1;l>=0;l--)t[f+l]=t[p+l];if(0===i){v=!0;break}}if(t[c--]=a[h--],1==--s){v=!0;break}if(0!==(y=s-Fe(t[u],a,0,s,s-1,e))){for(s-=y,f=(c-=y)+1,p=(h-=y)+1,l=0;l<y;l++)t[f+l]=a[p+l];if(s<=1){v=!0;break}}if(t[c--]=t[u--],0==--i){v=!0;break}d--}while(g>=7||y>=7);if(v)break;d<0&&(d=0),d+=2}(r=d)<1&&(r=1);if(1===s){for(f=(c-=i)+1,p=(u-=i)+1,l=i-1;l>=0;l--)t[f+l]=t[p+l];t[c]=a[h]}else{if(0===s)throw new Error;for(p=c-(s-1),l=0;l<s;l++)t[p+l]=a[l]}}(l,u,h,c))}return n=[],i=[],{mergeRuns:function(){for(;o>1;){var t=o-2;if(t>=1&&i[t-1]<=i[t]+i[t+1]||t>=2&&i[t-2]<=i[t]+i[t-1])i[t-1]<i[t+1]&&t--;else if(i[t]>i[t+1])break;s(t)}},forceMergeRuns:function(){for(;o>1;){var t=o-2;t>0&&i[t-1]<i[t+1]&&t--,s(t)}},pushRun:function(t,e){n[o]=t,i[o]=e,o+=1}}}function We(t,e,n,i){n||(n=0),i||(i=t.length);var r=i-n;if(!(r<2)){var o=0;if(r<32)Be(t,n,i,n+(o=ze(t,n,i,e)),e);else{var a=He(t,e),s=function(t){for(var e=0;t>=32;)e|=1&t,t>>=1;return t+e}(r);do{if((o=ze(t,n,i,e))<s){var l=r;l>s&&(l=s),Be(t,n,n+l,n+o,e),o=l}a.pushRun(n,o),a.mergeRuns(),r-=o,n+=o}while(0!==r);a.forceMergeRuns()}}}var Ge=!1;function Ue(){Ge||(Ge=!0,console.warn("z / z2 / zlevel of displayable is invalid, which may cause unexpected errors"))}function Xe(t,e){return t.zlevel===e.zlevel?t.z===e.z?t.z2-e.z2:t.z-e.z:t.zlevel-e.zlevel}var Ye=function(){function t(){this._roots=[],this._displayList=[],this._displayListLen=0,this.displayableSortFunc=Xe}return t.prototype.traverse=function(t,e){for(var n=0;n<this._roots.length;n++)this._roots[n].traverse(t,e)},t.prototype.getDisplayList=function(t,e){e=e||!1;var n=this._displayList;return!t&&n.length||this.updateDisplayList(e),n},t.prototype.updateDisplayList=function(t){this._displayListLen=0;for(var e=this._roots,n=this._displayList,i=0,r=e.length;i<r;i++)this._updateAndAddDisplayable(e[i],null,t);n.length=this._displayListLen,We(n,Xe)},t.prototype._updateAndAddDisplayable=function(t,e,n){if(!t.ignore||n){t.beforeUpdate(),t.update(),t.afterUpdate();var i=t.getClipPath();if(t.ignoreClip)e=null;else if(i){e=e?e.slice():[];for(var r=i,o=t;r;)r.parent=o,r.updateTransform(),e.push(r),o=r,r=r.getClipPath()}if(t.childrenRef){for(var a=t.childrenRef(),s=0;s<a.length;s++){var l=a[s];t.__dirty&&(l.__dirty|=1),this._updateAndAddDisplayable(l,e,n)}t.__dirty=0}else{var u=t;e&&e.length?u.__clipPaths=e:u.__clipPaths&&u.__clipPaths.length>0&&(u.__clipPaths=[]),isNaN(u.z)&&(Ue(),u.z=0),isNaN(u.z2)&&(Ue(),u.z2=0),isNaN(u.zlevel)&&(Ue(),u.zlevel=0),this._displayList[this._displayListLen++]=u}var h=t.getDecalElement&&t.getDecalElement();h&&this._updateAndAddDisplayable(h,e,n);var c=t.getTextGuideLine();c&&this._updateAndAddDisplayable(c,e,n);var p=t.getTextContent();p&&this._updateAndAddDisplayable(p,e,n)}},t.prototype.addRoot=function(t){t.__zr&&t.__zr.storage===this||this._roots.push(t)},t.prototype.delRoot=function(t){if(t instanceof Array)for(var e=0,n=t.length;e<n;e++)this.delRoot(t[e]);else{var i=A(this._roots,t);i>=0&&this._roots.splice(i,1)}},t.prototype.delAllRoots=function(){this._roots=[],this._displayList=[],this._displayListLen=0},t.prototype.getRoots=function(){return this._roots},t.prototype.dispose=function(){this._displayList=null,this._roots=null},t}(),qe=r.hasGlobalWindow&&(window.requestAnimationFrame&&window.requestAnimationFrame.bind(window)||window.msRequestAnimationFrame&&window.msRequestAnimationFrame.bind(window)||window.mozRequestAnimationFrame||window.webkitRequestAnimationFrame)||function(t){return setTimeout(t,16)},Ze={linear:function(t){return t},quadraticIn:function(t){return t*t},quadraticOut:function(t){return t*(2-t)},quadraticInOut:function(t){return(t*=2)<1?.5*t*t:-.5*(--t*(t-2)-1)},cubicIn:function(t){return t*t*t},cubicOut:function(t){return--t*t*t+1},cubicInOut:function(t){return(t*=2)<1?.5*t*t*t:.5*((t-=2)*t*t+2)},quarticIn:function(t){return t*t*t*t},quarticOut:function(t){return 1- --t*t*t*t},quarticInOut:function(t){return(t*=2)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2)},quinticIn:function(t){return t*t*t*t*t},quinticOut:function(t){return--t*t*t*t*t+1},quinticInOut:function(t){return(t*=2)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2)},sinusoidalIn:function(t){return 1-Math.cos(t*Math.PI/2)},sinusoidalOut:function(t){return Math.sin(t*Math.PI/2)},sinusoidalInOut:function(t){return.5*(1-Math.cos(Math.PI*t))},exponentialIn:function(t){return 0===t?0:Math.pow(1024,t-1)},exponentialOut:function(t){return 1===t?1:1-Math.pow(2,-10*t)},exponentialInOut:function(t){return 0===t?0:1===t?1:(t*=2)<1?.5*Math.pow(1024,t-1):.5*(2-Math.pow(2,-10*(t-1)))},circularIn:function(t){return 1-Math.sqrt(1-t*t)},circularOut:function(t){return Math.sqrt(1- --t*t)},circularInOut:function(t){return(t*=2)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)},elasticIn:function(t){var e,n=.1;return 0===t?0:1===t?1:(!n||n<1?(n=1,e=.1):e=.4*Math.asin(1/n)/(2*Math.PI),-n*Math.pow(2,10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/.4))},elasticOut:function(t){var e,n=.1;return 0===t?0:1===t?1:(!n||n<1?(n=1,e=.1):e=.4*Math.asin(1/n)/(2*Math.PI),n*Math.pow(2,-10*t)*Math.sin((t-e)*(2*Math.PI)/.4)+1)},elasticInOut:function(t){var e,n=.1,i=.4;return 0===t?0:1===t?1:(!n||n<1?(n=1,e=.1):e=i*Math.asin(1/n)/(2*Math.PI),(t*=2)<1?n*Math.pow(2,10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/i)*-.5:n*Math.pow(2,-10*(t-=1))*Math.sin((t-e)*(2*Math.PI)/i)*.5+1)},backIn:function(t){var e=1.70158;return t*t*((e+1)*t-e)},backOut:function(t){var e=1.70158;return--t*t*((e+1)*t+e)+1},backInOut:function(t){var e=2.5949095;return(t*=2)<1?t*t*((e+1)*t-e)*.5:.5*((t-=2)*t*((e+1)*t+e)+2)},bounceIn:function(t){return 1-Ze.bounceOut(1-t)},bounceOut:function(t){return t<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},bounceInOut:function(t){return t<.5?.5*Ze.bounceIn(2*t):.5*Ze.bounceOut(2*t-1)+.5}},je=Math.pow,Ke=Math.sqrt,$e=1e-8,Qe=1e-4,Je=Ke(3),tn=1/3,en=wt(),nn=wt(),rn=wt();function on(t){return t>-1e-8&&t<$e}function an(t){return t>$e||t<-1e-8}function sn(t,e,n,i,r){var o=1-r;return o*o*(o*t+3*r*e)+r*r*(r*i+3*o*n)}function ln(t,e,n,i,r){var o=1-r;return 3*(((e-t)*o+2*(n-e)*r)*o+(i-n)*r*r)}function un(t,e,n,i,r,o){var a=i+3*(e-n)-t,s=3*(n-2*e+t),l=3*(e-t),u=t-r,h=s*s-3*a*l,c=s*l-9*a*u,p=l*l-3*s*u,f=0;if(on(h)&&on(c)){if(on(s))o[0]=0;else(M=-l/s)>=0&&M<=1&&(o[f++]=M)}else{var d=c*c-4*h*p;if(on(d)){var g=c/h,y=-g/2;(M=-s/a+g)>=0&&M<=1&&(o[f++]=M),y>=0&&y<=1&&(o[f++]=y)}else if(d>0){var v=Ke(d),m=h*s+1.5*a*(-c+v),_=h*s+1.5*a*(-c-v);(M=(-s-((m=m<0?-je(-m,tn):je(m,tn))+(_=_<0?-je(-_,tn):je(_,tn))))/(3*a))>=0&&M<=1&&(o[f++]=M)}else{var x=(2*h*s-3*a*c)/(2*Ke(h*h*h)),w=Math.acos(x)/3,b=Ke(h),S=Math.cos(w),M=(-s-2*b*S)/(3*a),T=(y=(-s+b*(S+Je*Math.sin(w)))/(3*a),(-s+b*(S-Je*Math.sin(w)))/(3*a));M>=0&&M<=1&&(o[f++]=M),y>=0&&y<=1&&(o[f++]=y),T>=0&&T<=1&&(o[f++]=T)}}return f}function hn(t,e,n,i,r){var o=6*n-12*e+6*t,a=9*e+3*i-3*t-9*n,s=3*e-3*t,l=0;if(on(a)){if(an(o))(h=-s/o)>=0&&h<=1&&(r[l++]=h)}else{var u=o*o-4*a*s;if(on(u))r[0]=-o/(2*a);else if(u>0){var h,c=Ke(u),p=(-o-c)/(2*a);(h=(-o+c)/(2*a))>=0&&h<=1&&(r[l++]=h),p>=0&&p<=1&&(r[l++]=p)}}return l}function cn(t,e,n,i,r,o){var a=(e-t)*r+t,s=(n-e)*r+e,l=(i-n)*r+n,u=(s-a)*r+a,h=(l-s)*r+s,c=(h-u)*r+u;o[0]=t,o[1]=a,o[2]=u,o[3]=c,o[4]=c,o[5]=h,o[6]=l,o[7]=i}function pn(t,e,n,i,r,o,a,s,l){for(var u=t,h=e,c=0,p=1/l,f=1;f<=l;f++){var d=f*p,g=sn(t,n,r,a,d),y=sn(e,i,o,s,d),v=g-u,m=y-h;c+=Math.sqrt(v*v+m*m),u=g,h=y}return c}function fn(t,e,n,i){var r=1-i;return r*(r*t+2*i*e)+i*i*n}function dn(t,e,n,i){return 2*((1-i)*(e-t)+i*(n-e))}function gn(t,e,n){var i=t+n-2*e;return 0===i?.5:(t-e)/i}function yn(t,e,n,i,r){var o=(e-t)*i+t,a=(n-e)*i+e,s=(a-o)*i+o;r[0]=t,r[1]=o,r[2]=s,r[3]=s,r[4]=a,r[5]=n}function vn(t,e,n,i,r,o,a){for(var s=t,l=e,u=0,h=1/a,c=1;c<=a;c++){var p=c*h,f=fn(t,n,r,p),d=fn(e,i,o,p),g=f-s,y=d-l;u+=Math.sqrt(g*g+y*y),s=f,l=d}return u}var mn=/cubic-bezier\(([0-9,\.e ]+)\)/;function _n(t){var e=t&&mn.exec(t);if(e){var n=e[1].split(","),i=+at(n[0]),r=+at(n[1]),o=+at(n[2]),a=+at(n[3]);if(isNaN(i+r+o+a))return;var s=[];return function(t){return t<=0?0:t>=1?1:un(0,i,o,1,t,s)&&sn(0,r,a,1,s[0])}}}var xn=function(){function t(t){this._inited=!1,this._startTime=0,this._pausedTime=0,this._paused=!1,this._life=t.life||1e3,this._delay=t.delay||0,this.loop=t.loop||!1,this.onframe=t.onframe||mt,this.ondestroy=t.ondestroy||mt,this.onrestart=t.onrestart||mt,t.easing&&this.setEasing(t.easing)}return t.prototype.step=function(t,e){if(this._inited||(this._startTime=t+this._delay,this._inited=!0),!this._paused){var n=this._life,i=t-this._startTime-this._pausedTime,r=i/n;r<0&&(r=0),r=Math.min(r,1);var o=this.easingFunc,a=o?o(r):r;if(this.onframe(a),1===r){if(!this.loop)return!0;var s=i%n;this._startTime=t-s,this._pausedTime=0,this.onrestart()}return!1}this._pausedTime+=e},t.prototype.pause=function(){this._paused=!0},t.prototype.resume=function(){this._paused=!1},t.prototype.setEasing=function(t){this.easing=t,this.easingFunc=W(t)?t:Ze[t]||_n(t)},t}(),wn=function(t){this.value=t},bn=function(){function t(){this._len=0}return t.prototype.insert=function(t){var e=new wn(t);return this.insertEntry(e),e},t.prototype.insertEntry=function(t){this.head?(this.tail.next=t,t.prev=this.tail,t.next=null,this.tail=t):this.head=this.tail=t,this._len++},t.prototype.remove=function(t){var e=t.prev,n=t.next;e?e.next=n:this.head=n,n?n.prev=e:this.tail=e,t.next=t.prev=null,this._len--},t.prototype.len=function(){return this._len},t.prototype.clear=function(){this.head=this.tail=null,this._len=0},t}(),Sn=function(){function t(t){this._list=new bn,this._maxSize=10,this._map={},this._maxSize=t}return t.prototype.put=function(t,e){var n=this._list,i=this._map,r=null;if(null==i[t]){var o=n.len(),a=this._lastRemovedEntry;if(o>=this._maxSize&&o>0){var s=n.head;n.remove(s),delete i[s.key],r=s.value,this._lastRemovedEntry=s}a?a.value=e:a=new wn(e),a.key=t,n.insertEntry(a),i[t]=a}return r},t.prototype.get=function(t){var e=this._map[t],n=this._list;if(null!=e)return e!==n.tail&&(n.remove(e),n.insertEntry(e)),e.value},t.prototype.clear=function(){this._list.clear(),this._map={}},t.prototype.len=function(){return this._list.len()},t}(),Mn={transparent:[0,0,0,0],aliceblue:[240,248,255,1],antiquewhite:[250,235,215,1],aqua:[0,255,255,1],aquamarine:[127,255,212,1],azure:[240,255,255,1],beige:[245,245,220,1],bisque:[255,228,196,1],black:[0,0,0,1],blanchedalmond:[255,235,205,1],blue:[0,0,255,1],blueviolet:[138,43,226,1],brown:[165,42,42,1],burlywood:[222,184,135,1],cadetblue:[95,158,160,1],chartreuse:[127,255,0,1],chocolate:[210,105,30,1],coral:[255,127,80,1],cornflowerblue:[100,149,237,1],cornsilk:[255,248,220,1],crimson:[220,20,60,1],cyan:[0,255,255,1],darkblue:[0,0,139,1],darkcyan:[0,139,139,1],darkgoldenrod:[184,134,11,1],darkgray:[169,169,169,1],darkgreen:[0,100,0,1],darkgrey:[169,169,169,1],darkkhaki:[189,183,107,1],darkmagenta:[139,0,139,1],darkolivegreen:[85,107,47,1],darkorange:[255,140,0,1],darkorchid:[153,50,204,1],darkred:[139,0,0,1],darksalmon:[233,150,122,1],darkseagreen:[143,188,143,1],darkslateblue:[72,61,139,1],darkslategray:[47,79,79,1],darkslategrey:[47,79,79,1],darkturquoise:[0,206,209,1],darkviolet:[148,0,211,1],deeppink:[255,20,147,1],deepskyblue:[0,191,255,1],dimgray:[105,105,105,1],dimgrey:[105,105,105,1],dodgerblue:[30,144,255,1],firebrick:[178,34,34,1],floralwhite:[255,250,240,1],forestgreen:[34,139,34,1],fuchsia:[255,0,255,1],gainsboro:[220,220,220,1],ghostwhite:[248,248,255,1],gold:[255,215,0,1],goldenrod:[218,165,32,1],gray:[128,128,128,1],green:[0,128,0,1],greenyellow:[173,255,47,1],grey:[128,128,128,1],honeydew:[240,255,240,1],hotpink:[255,105,180,1],indianred:[205,92,92,1],indigo:[75,0,130,1],ivory:[255,255,240,1],khaki:[240,230,140,1],lavender:[230,230,250,1],lavenderblush:[255,240,245,1],lawngreen:[124,252,0,1],lemonchiffon:[255,250,205,1],lightblue:[173,216,230,1],lightcoral:[240,128,128,1],lightcyan:[224,255,255,1],lightgoldenrodyellow:[250,250,210,1],lightgray:[211,211,211,1],lightgreen:[144,238,144,1],lightgrey:[211,211,211,1],lightpink:[255,182,193,1],lightsalmon:[255,160,122,1],lightseagreen:[32,178,170,1],lightskyblue:[135,206,250,1],lightslategray:[119,136,153,1],lightslategrey:[119,136,153,1],lightsteelblue:[176,196,222,1],lightyellow:[255,255,224,1],lime:[0,255,0,1],limegreen:[50,205,50,1],linen:[250,240,230,1],magenta:[255,0,255,1],maroon:[128,0,0,1],mediumaquamarine:[102,205,170,1],mediumblue:[0,0,205,1],mediumorchid:[186,85,211,1],mediumpurple:[147,112,219,1],mediumseagreen:[60,179,113,1],mediumslateblue:[123,104,238,1],mediumspringgreen:[0,250,154,1],mediumturquoise:[72,209,204,1],mediumvioletred:[199,21,133,1],midnightblue:[25,25,112,1],mintcream:[245,255,250,1],mistyrose:[255,228,225,1],moccasin:[255,228,181,1],navajowhite:[255,222,173,1],navy:[0,0,128,1],oldlace:[253,245,230,1],olive:[128,128,0,1],olivedrab:[107,142,35,1],orange:[255,165,0,1],orangered:[255,69,0,1],orchid:[218,112,214,1],palegoldenrod:[238,232,170,1],palegreen:[152,251,152,1],paleturquoise:[175,238,238,1],palevioletred:[219,112,147,1],papayawhip:[255,239,213,1],peachpuff:[255,218,185,1],peru:[205,133,63,1],pink:[255,192,203,1],plum:[221,160,221,1],powderblue:[176,224,230,1],purple:[128,0,128,1],red:[255,0,0,1],rosybrown:[188,143,143,1],royalblue:[65,105,225,1],saddlebrown:[139,69,19,1],salmon:[250,128,114,1],sandybrown:[244,164,96,1],seagreen:[46,139,87,1],seashell:[255,245,238,1],sienna:[160,82,45,1],silver:[192,192,192,1],skyblue:[135,206,235,1],slateblue:[106,90,205,1],slategray:[112,128,144,1],slategrey:[112,128,144,1],snow:[255,250,250,1],springgreen:[0,255,127,1],steelblue:[70,130,180,1],tan:[210,180,140,1],teal:[0,128,128,1],thistle:[216,191,216,1],tomato:[255,99,71,1],turquoise:[64,224,208,1],violet:[238,130,238,1],wheat:[245,222,179,1],white:[255,255,255,1],whitesmoke:[245,245,245,1],yellow:[255,255,0,1],yellowgreen:[154,205,50,1]};function Tn(t){return(t=Math.round(t))<0?0:t>255?255:t}function Cn(t){return t<0?0:t>1?1:t}function Dn(t){var e=t;return e.length&&"%"===e.charAt(e.length-1)?Tn(parseFloat(e)/100*255):Tn(parseInt(e,10))}function In(t){var e=t;return e.length&&"%"===e.charAt(e.length-1)?Cn(parseFloat(e)/100):Cn(parseFloat(e))}function kn(t,e,n){return n<0?n+=1:n>1&&(n-=1),6*n<1?t+(e-t)*n*6:2*n<1?e:3*n<2?t+(e-t)*(2/3-n)*6:t}function An(t,e,n){return t+(e-t)*n}function Ln(t,e,n,i,r){return t[0]=e,t[1]=n,t[2]=i,t[3]=r,t}function Pn(t,e){return t[0]=e[0],t[1]=e[1],t[2]=e[2],t[3]=e[3],t}var On=new Sn(20),Rn=null;function Nn(t,e){Rn&&Pn(Rn,e),Rn=On.put(t,Rn||e.slice())}function En(t,e){if(t){e=e||[];var n=On.get(t);if(n)return Pn(e,n);var i=(t+="").replace(/ /g,"").toLowerCase();if(i in Mn)return Pn(e,Mn[i]),Nn(t,e),e;var r,o=i.length;if("#"===i.charAt(0))return 4===o||5===o?(r=parseInt(i.slice(1,4),16))>=0&&r<=4095?(Ln(e,(3840&r)>>4|(3840&r)>>8,240&r|(240&r)>>4,15&r|(15&r)<<4,5===o?parseInt(i.slice(4),16)/15:1),Nn(t,e),e):void Ln(e,0,0,0,1):7===o||9===o?(r=parseInt(i.slice(1,7),16))>=0&&r<=16777215?(Ln(e,(16711680&r)>>16,(65280&r)>>8,255&r,9===o?parseInt(i.slice(7),16)/255:1),Nn(t,e),e):void Ln(e,0,0,0,1):void 0;var a=i.indexOf("("),s=i.indexOf(")");if(-1!==a&&s+1===o){var l=i.substr(0,a),u=i.substr(a+1,s-(a+1)).split(","),h=1;switch(l){case"rgba":if(4!==u.length)return 3===u.length?Ln(e,+u[0],+u[1],+u[2],1):Ln(e,0,0,0,1);h=In(u.pop());case"rgb":return u.length>=3?(Ln(e,Dn(u[0]),Dn(u[1]),Dn(u[2]),3===u.length?h:In(u[3])),Nn(t,e),e):void Ln(e,0,0,0,1);case"hsla":return 4!==u.length?void Ln(e,0,0,0,1):(u[3]=In(u[3]),zn(u,e),Nn(t,e),e);case"hsl":return 3!==u.length?void Ln(e,0,0,0,1):(zn(u,e),Nn(t,e),e);default:return}}Ln(e,0,0,0,1)}}function zn(t,e){var n=(parseFloat(t[0])%360+360)%360/360,i=In(t[1]),r=In(t[2]),o=r<=.5?r*(i+1):r+i-r*i,a=2*r-o;return Ln(e=e||[],Tn(255*kn(a,o,n+1/3)),Tn(255*kn(a,o,n)),Tn(255*kn(a,o,n-1/3)),1),4===t.length&&(e[3]=t[3]),e}function Bn(t,e){var n=En(t);if(n){for(var i=0;i<3;i++)n[i]=e<0?n[i]*(1-e)|0:(255-n[i])*e+n[i]|0,n[i]>255?n[i]=255:n[i]<0&&(n[i]=0);return Gn(n,4===n.length?"rgba":"rgb")}}function Fn(t,e,n){if(e&&e.length&&t>=0&&t<=1){n=n||[];var i=t*(e.length-1),r=Math.floor(i),o=Math.ceil(i),a=e[r],s=e[o],l=i-r;return n[0]=Tn(An(a[0],s[0],l)),n[1]=Tn(An(a[1],s[1],l)),n[2]=Tn(An(a[2],s[2],l)),n[3]=Cn(An(a[3],s[3],l)),n}}var Vn=Fn;function Hn(t,e,n){if(e&&e.length&&t>=0&&t<=1){var i=t*(e.length-1),r=Math.floor(i),o=Math.ceil(i),a=En(e[r]),s=En(e[o]),l=i-r,u=Gn([Tn(An(a[0],s[0],l)),Tn(An(a[1],s[1],l)),Tn(An(a[2],s[2],l)),Cn(An(a[3],s[3],l))],"rgba");return n?{color:u,leftIndex:r,rightIndex:o,value:i}:u}}var Wn=Hn;function Gn(t,e){if(t&&t.length){var n=t[0]+","+t[1]+","+t[2];return"rgba"!==e&&"hsva"!==e&&"hsla"!==e||(n+=","+t[3]),e+"("+n+")"}}function Un(t,e){var n=En(t);return n?(.299*n[0]+.587*n[1]+.114*n[2])*n[3]/255+(1-n[3])*e:0}var Xn=new Sn(100);function Yn(t){if(G(t)){var e=Xn.get(t);return e||(e=Bn(t,-.1),Xn.put(t,e)),e}if(K(t)){var n=D({},t);return n.colorStops=N(t.colorStops,(function(t){return{offset:t.offset,color:Bn(t.color,-.1)}})),n}return t}var qn=Object.freeze({__proto__:null,parse:En,lift:Bn,toHex:function(t){var e=En(t);if(e)return((1<<24)+(e[0]<<16)+(e[1]<<8)+ +e[2]).toString(16).slice(1)},fastLerp:Fn,fastMapToColor:Vn,lerp:Hn,mapToColor:Wn,modifyHSL:function(t,e,n,i){var r,o=En(t);if(t)return o=function(t){if(t){var e,n,i=t[0]/255,r=t[1]/255,o=t[2]/255,a=Math.min(i,r,o),s=Math.max(i,r,o),l=s-a,u=(s+a)/2;if(0===l)e=0,n=0;else{n=u<.5?l/(s+a):l/(2-s-a);var h=((s-i)/6+l/2)/l,c=((s-r)/6+l/2)/l,p=((s-o)/6+l/2)/l;i===s?e=p-c:r===s?e=1/3+h-p:o===s&&(e=2/3+c-h),e<0&&(e+=1),e>1&&(e-=1)}var f=[360*e,n,u];return null!=t[3]&&f.push(t[3]),f}}(o),null!=e&&(o[0]=(r=e,(r=Math.round(r))<0?0:r>360?360:r)),null!=n&&(o[1]=In(n)),null!=i&&(o[2]=In(i)),Gn(zn(o),"rgba")},modifyAlpha:function(t,e){var n=En(t);if(n&&null!=e)return n[3]=Cn(e),Gn(n,"rgba")},stringify:Gn,lum:Un,random:function(){return Gn([Math.round(255*Math.random()),Math.round(255*Math.random()),Math.round(255*Math.random())],"rgb")},liftColor:Yn});var Zn=Array.prototype.slice;function jn(t,e,n){return(e-t)*n+t}function Kn(t,e,n,i){for(var r=e.length,o=0;o<r;o++)t[o]=jn(e[o],n[o],i);return t}function $n(t,e,n,i){for(var r=e.length,o=0;o<r;o++)t[o]=e[o]+n[o]*i;return t}function Qn(t,e,n,i){for(var r=e.length,o=r&&e[0].length,a=0;a<r;a++){t[a]||(t[a]=[]);for(var s=0;s<o;s++)t[a][s]=e[a][s]+n[a][s]*i}return t}function Jn(t,e){for(var n=t.length,i=e.length,r=n>i?e:t,o=Math.min(n,i),a=r[o-1]||{color:[0,0,0,0],offset:0},s=o;s<Math.max(n,i);s++)r.push({offset:a.offset,color:a.color.slice()})}function ti(t,e,n){var i=t,r=e;if(i.push&&r.push){var o=i.length,a=r.length;if(o!==a)if(o>a)i.length=a;else for(var s=o;s<a;s++)i.push(1===n?r[s]:Zn.call(r[s]));var l=i[0]&&i[0].length;for(s=0;s<i.length;s++)if(1===n)isNaN(i[s])&&(i[s]=r[s]);else for(var u=0;u<l;u++)isNaN(i[s][u])&&(i[s][u]=r[s][u])}}function ei(t){if(O(t)){var e=t.length;if(O(t[0])){for(var n=[],i=0;i<e;i++)n.push(Zn.call(t[i]));return n}return Zn.call(t)}return t}function ni(t){return t[0]=Math.floor(t[0])||0,t[1]=Math.floor(t[1])||0,t[2]=Math.floor(t[2])||0,t[3]=null==t[3]?1:t[3],"rgba("+t.join(",")+")"}function ii(t){return 4===t||5===t}function ri(t){return 1===t||2===t}var oi=[0,0,0,0],ai=function(){function t(t){this.keyframes=[],this.discrete=!1,this._invalid=!1,this._needsSort=!1,this._lastFr=0,this._lastFrP=0,this.propName=t}return t.prototype.isFinished=function(){return this._finished},t.prototype.setFinished=function(){this._finished=!0,this._additiveTrack&&this._additiveTrack.setFinished()},t.prototype.needsAnimate=function(){return this.keyframes.length>=1},t.prototype.getAdditiveTrack=function(){return this._additiveTrack},t.prototype.addKeyframe=function(t,e,n){this._needsSort=!0;var i=this.keyframes,r=i.length,o=!1,a=6,s=e;if(O(e)){var l=function(t){return O(t&&t[0])?2:1}(e);a=l,(1===l&&!X(e[0])||2===l&&!X(e[0][0]))&&(o=!0)}else if(X(e)&&!J(e))a=0;else if(G(e))if(isNaN(+e)){var u=En(e);u&&(s=u,a=3)}else a=0;else if(K(e)){var h=D({},s);h.colorStops=N(e.colorStops,(function(t){return{offset:t.offset,color:En(t.color)}})),"linear"===e.type?a=4:function(t){return"radial"===t.type}(e)&&(a=5),s=h}0===r?this.valType=a:a===this.valType&&6!==a||(o=!0),this.discrete=this.discrete||o;var c={time:t,value:s,rawValue:e,percent:0};return n&&(c.easing=n,c.easingFunc=W(n)?n:Ze[n]||_n(n)),i.push(c),c},t.prototype.prepare=function(t,e){var n=this.keyframes;this._needsSort&&n.sort((function(t,e){return t.time-e.time}));for(var i=this.valType,r=n.length,o=n[r-1],a=this.discrete,s=ri(i),l=ii(i),u=0;u<r;u++){var h=n[u],c=h.value,p=o.value;h.percent=h.time/t,a||(s&&u!==r-1?ti(c,p,i):l&&Jn(c.colorStops,p.colorStops))}if(!a&&5!==i&&e&&this.needsAnimate()&&e.needsAnimate()&&i===e.valType&&!e._finished){this._additiveTrack=e;var f=n[0].value;for(u=0;u<r;u++)0===i?n[u].additiveValue=n[u].value-f:3===i?n[u].additiveValue=$n([],n[u].value,f,-1):ri(i)&&(n[u].additiveValue=1===i?$n([],n[u].value,f,-1):Qn([],n[u].value,f,-1))}},t.prototype.step=function(t,e){if(!this._finished){this._additiveTrack&&this._additiveTrack._finished&&(this._additiveTrack=null);var n,i,r,o=null!=this._additiveTrack,a=o?"additiveValue":"value",s=this.valType,l=this.keyframes,u=l.length,h=this.propName,c=3===s,p=this._lastFr,f=Math.min;if(1===u)i=r=l[0];else{if(e<0)n=0;else if(e<this._lastFrP){for(n=f(p+1,u-1);n>=0&&!(l[n].percent<=e);n--);n=f(n,u-2)}else{for(n=p;n<u&&!(l[n].percent>e);n++);n=f(n-1,u-2)}r=l[n+1],i=l[n]}if(i&&r){this._lastFr=n,this._lastFrP=e;var d=r.percent-i.percent,g=0===d?1:f((e-i.percent)/d,1);r.easingFunc&&(g=r.easingFunc(g));var y=o?this._additiveValue:c?oi:t[h];if(!ri(s)&&!c||y||(y=this._additiveValue=[]),this.discrete)t[h]=g<1?i.rawValue:r.rawValue;else if(ri(s))1===s?Kn(y,i[a],r[a],g):function(t,e,n,i){for(var r=e.length,o=r&&e[0].length,a=0;a<r;a++){t[a]||(t[a]=[]);for(var s=0;s<o;s++)t[a][s]=jn(e[a][s],n[a][s],i)}}(y,i[a],r[a],g);else if(ii(s)){var v=i[a],m=r[a],_=4===s;t[h]={type:_?"linear":"radial",x:jn(v.x,m.x,g),y:jn(v.y,m.y,g),colorStops:N(v.colorStops,(function(t,e){var n=m.colorStops[e];return{offset:jn(t.offset,n.offset,g),color:ni(Kn([],t.color,n.color,g))}})),global:m.global},_?(t[h].x2=jn(v.x2,m.x2,g),t[h].y2=jn(v.y2,m.y2,g)):t[h].r=jn(v.r,m.r,g)}else if(c)Kn(y,i[a],r[a],g),o||(t[h]=ni(y));else{var x=jn(i[a],r[a],g);o?this._additiveValue=x:t[h]=x}o&&this._addToTarget(t)}}},t.prototype._addToTarget=function(t){var e=this.valType,n=this.propName,i=this._additiveValue;0===e?t[n]=t[n]+i:3===e?(En(t[n],oi),$n(oi,oi,i,1),t[n]=ni(oi)):1===e?$n(t[n],t[n],i,1):2===e&&Qn(t[n],t[n],i,1)},t}(),si=function(){function t(t,e,n,i){this._tracks={},this._trackKeys=[],this._maxTime=0,this._started=0,this._clip=null,this._target=t,this._loop=e,e&&i?M("Can' use additive animation on looped animation."):(this._additiveAnimators=i,this._allowDiscrete=n)}return t.prototype.getMaxTime=function(){return this._maxTime},t.prototype.getDelay=function(){return this._delay},t.prototype.getLoop=function(){return this._loop},t.prototype.getTarget=function(){return this._target},t.prototype.changeTarget=function(t){this._target=t},t.prototype.when=function(t,e,n){return this.whenWithKeys(t,e,B(e),n)},t.prototype.whenWithKeys=function(t,e,n,i){for(var r=this._tracks,o=0;o<n.length;o++){var a=n[o],s=r[a];if(!s){s=r[a]=new ai(a);var l=void 0,u=this._getAdditiveTrack(a);if(u){var h=u.keyframes,c=h[h.length-1];l=c&&c.value,3===u.valType&&l&&(l=ni(l))}else l=this._target[a];if(null==l)continue;t>0&&s.addKeyframe(0,ei(l),i),this._trackKeys.push(a)}s.addKeyframe(t,ei(e[a]),i)}return this._maxTime=Math.max(this._maxTime,t),this},t.prototype.pause=function(){this._clip.pause(),this._paused=!0},t.prototype.resume=function(){this._clip.resume(),this._paused=!1},t.prototype.isPaused=function(){return!!this._paused},t.prototype.duration=function(t){return this._maxTime=t,this._force=!0,this},t.prototype._doneCallback=function(){this._setTracksFinished(),this._clip=null;var t=this._doneCbs;if(t)for(var e=t.length,n=0;n<e;n++)t[n].call(this)},t.prototype._abortedCallback=function(){this._setTracksFinished();var t=this.animation,e=this._abortedCbs;if(t&&t.removeClip(this._clip),this._clip=null,e)for(var n=0;n<e.length;n++)e[n].call(this)},t.prototype._setTracksFinished=function(){for(var t=this._tracks,e=this._trackKeys,n=0;n<e.length;n++)t[e[n]].setFinished()},t.prototype._getAdditiveTrack=function(t){var e,n=this._additiveAnimators;if(n)for(var i=0;i<n.length;i++){var r=n[i].getTrack(t);r&&(e=r)}return e},t.prototype.start=function(t){if(!(this._started>0)){this._started=1;for(var e=this,n=[],i=this._maxTime||0,r=0;r<this._trackKeys.length;r++){var o=this._trackKeys[r],a=this._tracks[o],s=this._getAdditiveTrack(o),l=a.keyframes,u=l.length;if(a.prepare(i,s),a.needsAnimate())if(!this._allowDiscrete&&a.discrete){var h=l[u-1];h&&(e._target[a.propName]=h.rawValue),a.setFinished()}else n.push(a)}if(n.length||this._force){var c=new xn({life:i,loop:this._loop,delay:this._delay||0,onframe:function(t){e._started=2;var i=e._additiveAnimators;if(i){for(var r=!1,o=0;o<i.length;o++)if(i[o]._clip){r=!0;break}r||(e._additiveAnimators=null)}for(o=0;o<n.length;o++)n[o].step(e._target,t);var a=e._onframeCbs;if(a)for(o=0;o<a.length;o++)a[o](e._target,t)},ondestroy:function(){e._doneCallback()}});this._clip=c,this.animation&&this.animation.addClip(c),t&&c.setEasing(t)}else this._doneCallback();return this}},t.prototype.stop=function(t){if(this._clip){var e=this._clip;t&&e.onframe(1),this._abortedCallback()}},t.prototype.delay=function(t){return this._delay=t,this},t.prototype.during=function(t){return t&&(this._onframeCbs||(this._onframeCbs=[]),this._onframeCbs.push(t)),this},t.prototype.done=function(t){return t&&(this._doneCbs||(this._doneCbs=[]),this._doneCbs.push(t)),this},t.prototype.aborted=function(t){return t&&(this._abortedCbs||(this._abortedCbs=[]),this._abortedCbs.push(t)),this},t.prototype.getClip=function(){return this._clip},t.prototype.getTrack=function(t){return this._tracks[t]},t.prototype.getTracks=function(){var t=this;return N(this._trackKeys,(function(e){return t._tracks[e]}))},t.prototype.stopTracks=function(t,e){if(!t.length||!this._clip)return!0;for(var n=this._tracks,i=this._trackKeys,r=0;r<t.length;r++){var o=n[t[r]];o&&!o.isFinished()&&(e?o.step(this._target,1):1===this._started&&o.step(this._target,0),o.setFinished())}var a=!0;for(r=0;r<i.length;r++)if(!n[i[r]].isFinished()){a=!1;break}return a&&this._abortedCallback(),a},t.prototype.saveTo=function(t,e,n){if(t){e=e||this._trackKeys;for(var i=0;i<e.length;i++){var r=e[i],o=this._tracks[r];if(o&&!o.isFinished()){var a=o.keyframes,s=a[n?0:a.length-1];s&&(t[r]=ei(s.rawValue))}}}},t.prototype.__changeFinalValue=function(t,e){e=e||B(t);for(var n=0;n<e.length;n++){var i=e[n],r=this._tracks[i];if(r){var o=r.keyframes;if(o.length>1){var a=o.pop();r.addKeyframe(a.time,t[i]),r.prepare(this._maxTime,r.getAdditiveTrack())}}}},t}();function li(){return(new Date).getTime()}var ui,hi,ci=function(t){function e(e){var n=t.call(this)||this;return n._running=!1,n._time=0,n._pausedTime=0,n._pauseStart=0,n._paused=!1,e=e||{},n.stage=e.stage||{},n}return n(e,t),e.prototype.addClip=function(t){t.animation&&this.removeClip(t),this._head?(this._tail.next=t,t.prev=this._tail,t.next=null,this._tail=t):this._head=this._tail=t,t.animation=this},e.prototype.addAnimator=function(t){t.animation=this;var e=t.getClip();e&&this.addClip(e)},e.prototype.removeClip=function(t){if(t.animation){var e=t.prev,n=t.next;e?e.next=n:this._head=n,n?n.prev=e:this._tail=e,t.next=t.prev=t.animation=null}},e.prototype.removeAnimator=function(t){var e=t.getClip();e&&this.removeClip(e),t.animation=null},e.prototype.update=function(t){for(var e=li()-this._pausedTime,n=e-this._time,i=this._head;i;){var r=i.next;i.step(e,n)?(i.ondestroy(),this.removeClip(i),i=r):i=r}this._time=e,t||(this.trigger("frame",n),this.stage.update&&this.stage.update())},e.prototype._startLoop=function(){var t=this;this._running=!0,qe((function e(){t._running&&(qe(e),!t._paused&&t.update())}))},e.prototype.start=function(){this._running||(this._time=li(),this._pausedTime=0,this._startLoop())},e.prototype.stop=function(){this._running=!1},e.prototype.pause=function(){this._paused||(this._pauseStart=li(),this._paused=!0)},e.prototype.resume=function(){this._paused&&(this._pausedTime+=li()-this._pauseStart,this._paused=!1)},e.prototype.clear=function(){for(var t=this._head;t;){var e=t.next;t.prev=t.next=t.animation=null,t=e}this._head=this._tail=null},e.prototype.isFinished=function(){return null==this._head},e.prototype.animate=function(t,e){e=e||{},this.start();var n=new si(t,e.loop);return this.addAnimator(n),n},e}(Wt),pi=r.domSupported,fi=(hi={pointerdown:1,pointerup:1,pointermove:1,pointerout:1},{mouse:ui=["click","dblclick","mousewheel","wheel","mouseout","mouseup","mousedown","mousemove","contextmenu"],touch:["touchstart","touchend","touchmove"],pointer:N(ui,(function(t){var e=t.replace("mouse","pointer");return hi.hasOwnProperty(e)?e:t}))}),di=["mousemove","mouseup"],gi=["pointermove","pointerup"],yi=!1;function vi(t){var e=t.pointerType;return"pen"===e||"touch"===e}function mi(t){t&&(t.zrByTouch=!0)}function _i(t,e){for(var n=e,i=!1;n&&9!==n.nodeType&&!(i=n.domBelongToZr||n!==e&&n===t.painterRoot);)n=n.parentNode;return i}var xi=function(t,e){this.stopPropagation=mt,this.stopImmediatePropagation=mt,this.preventDefault=mt,this.type=e.type,this.target=this.currentTarget=t.dom,this.pointerType=e.pointerType,this.clientX=e.clientX,this.clientY=e.clientY},wi={mousedown:function(t){t=re(this.dom,t),this.__mayPointerCapture=[t.zrX,t.zrY],this.trigger("mousedown",t)},mousemove:function(t){t=re(this.dom,t);var e=this.__mayPointerCapture;!e||t.zrX===e[0]&&t.zrY===e[1]||this.__togglePointerCapture(!0),this.trigger("mousemove",t)},mouseup:function(t){t=re(this.dom,t),this.__togglePointerCapture(!1),this.trigger("mouseup",t)},mouseout:function(t){_i(this,(t=re(this.dom,t)).toElement||t.relatedTarget)||(this.__pointerCapturing&&(t.zrEventControl="no_globalout"),this.trigger("mouseout",t))},wheel:function(t){yi=!0,t=re(this.dom,t),this.trigger("mousewheel",t)},mousewheel:function(t){yi||(t=re(this.dom,t),this.trigger("mousewheel",t))},touchstart:function(t){mi(t=re(this.dom,t)),this.__lastTouchMoment=new Date,this.handler.processGesture(t,"start"),wi.mousemove.call(this,t),wi.mousedown.call(this,t)},touchmove:function(t){mi(t=re(this.dom,t)),this.handler.processGesture(t,"change"),wi.mousemove.call(this,t)},touchend:function(t){mi(t=re(this.dom,t)),this.handler.processGesture(t,"end"),wi.mouseup.call(this,t),+new Date-+this.__lastTouchMoment<300&&wi.click.call(this,t)},pointerdown:function(t){wi.mousedown.call(this,t)},pointermove:function(t){vi(t)||wi.mousemove.call(this,t)},pointerup:function(t){wi.mouseup.call(this,t)},pointerout:function(t){vi(t)||wi.mouseout.call(this,t)}};R(["click","dblclick","contextmenu"],(function(t){wi[t]=function(e){e=re(this.dom,e),this.trigger(t,e)}}));var bi={pointermove:function(t){vi(t)||bi.mousemove.call(this,t)},pointerup:function(t){bi.mouseup.call(this,t)},mousemove:function(t){this.trigger("mousemove",t)},mouseup:function(t){var e=this.__pointerCapturing;this.__togglePointerCapture(!1),this.trigger("mouseup",t),e&&(t.zrEventControl="only_globalout",this.trigger("mouseout",t))}};function Si(t,e){var n=e.domHandlers;r.pointerEventsSupported?R(fi.pointer,(function(i){Ti(e,i,(function(e){n[i].call(t,e)}))})):(r.touchEventsSupported&&R(fi.touch,(function(i){Ti(e,i,(function(r){n[i].call(t,r),function(t){t.touching=!0,null!=t.touchTimer&&(clearTimeout(t.touchTimer),t.touchTimer=null),t.touchTimer=setTimeout((function(){t.touching=!1,t.touchTimer=null}),700)}(e)}))})),R(fi.mouse,(function(i){Ti(e,i,(function(r){r=ie(r),e.touching||n[i].call(t,r)}))})))}function Mi(t,e){function n(n){Ti(e,n,(function(i){i=ie(i),_i(t,i.target)||(i=function(t,e){return re(t.dom,new xi(t,e),!0)}(t,i),e.domHandlers[n].call(t,i))}),{capture:!0})}r.pointerEventsSupported?R(gi,n):r.touchEventsSupported||R(di,n)}function Ti(t,e,n,i){t.mounted[e]=n,t.listenerOpts[e]=i,function(t,e,n,i){t.addEventListener(e,n,i)}(t.domTarget,e,n,i)}function Ci(t){var e,n,i,r,o=t.mounted;for(var a in o)o.hasOwnProperty(a)&&(e=t.domTarget,n=a,i=o[a],r=t.listenerOpts[a],e.removeEventListener(n,i,r));t.mounted={}}var Di=function(t,e){this.mounted={},this.listenerOpts={},this.touching=!1,this.domTarget=t,this.domHandlers=e},Ii=function(t){function e(e,n){var i=t.call(this)||this;return i.__pointerCapturing=!1,i.dom=e,i.painterRoot=n,i._localHandlerScope=new Di(e,wi),pi&&(i._globalHandlerScope=new Di(document,bi)),Si(i,i._localHandlerScope),i}return n(e,t),e.prototype.dispose=function(){Ci(this._localHandlerScope),pi&&Ci(this._globalHandlerScope)},e.prototype.setCursor=function(t){this.dom.style&&(this.dom.style.cursor=t||"default")},e.prototype.__togglePointerCapture=function(t){if(this.__mayPointerCapture=null,pi&&+this.__pointerCapturing^+t){this.__pointerCapturing=t;var e=this._globalHandlerScope;t?Mi(this,e):Ci(e)}},e}(Wt),ki=1;r.hasGlobalWindow&&(ki=Math.max(window.devicePixelRatio||window.screen&&window.screen.deviceXDPI/window.screen.logicalXDPI||1,1));var Ai=ki,Li="#333",Pi="#ccc",Oi=ue,Ri=5e-5;function Ni(t){return t>Ri||t<-5e-5}var Ei=[],zi=[],Bi=[1,0,0,1,0,0],Fi=Math.abs,Vi=function(){function t(){}return t.prototype.getLocalTransform=function(e){return t.getLocalTransform(this,e)},t.prototype.setPosition=function(t){this.x=t[0],this.y=t[1]},t.prototype.setScale=function(t){this.scaleX=t[0],this.scaleY=t[1]},t.prototype.setSkew=function(t){this.skewX=t[0],this.skewY=t[1]},t.prototype.setOrigin=function(t){this.originX=t[0],this.originY=t[1]},t.prototype.needLocalTransform=function(){return Ni(this.rotation)||Ni(this.x)||Ni(this.y)||Ni(this.scaleX-1)||Ni(this.scaleY-1)||Ni(this.skewX)||Ni(this.skewY)},t.prototype.updateTransform=function(){var t=this.parent&&this.parent.transform,e=this.needLocalTransform(),n=this.transform;e||t?(n=n||[1,0,0,1,0,0],e?this.getLocalTransform(n):Oi(n),t&&(e?ce(n,t,n):he(n,t)),this.transform=n,this._resolveGlobalScaleRatio(n)):n&&(Oi(n),this.invTransform=null)},t.prototype._resolveGlobalScaleRatio=function(t){var e=this.globalScaleRatio;if(null!=e&&1!==e){this.getGlobalScale(Ei);var n=Ei[0]<0?-1:1,i=Ei[1]<0?-1:1,r=((Ei[0]-n)*e+n)/Ei[0]||0,o=((Ei[1]-i)*e+i)/Ei[1]||0;t[0]*=r,t[1]*=r,t[2]*=o,t[3]*=o}this.invTransform=this.invTransform||[1,0,0,1,0,0],ge(this.invTransform,t)},t.prototype.getComputedTransform=function(){for(var t=this,e=[];t;)e.push(t),t=t.parent;for(;t=e.pop();)t.updateTransform();return this.transform},t.prototype.setLocalTransform=function(t){if(t){var e=t[0]*t[0]+t[1]*t[1],n=t[2]*t[2]+t[3]*t[3],i=Math.atan2(t[1],t[0]),r=Math.PI/2+i-Math.atan2(t[3],t[2]);n=Math.sqrt(n)*Math.cos(r),e=Math.sqrt(e),this.skewX=r,this.skewY=0,this.rotation=-i,this.x=+t[4],this.y=+t[5],this.scaleX=e,this.scaleY=n,this.originX=0,this.originY=0}},t.prototype.decomposeTransform=function(){if(this.transform){var t=this.parent,e=this.transform;t&&t.transform&&(t.invTransform=t.invTransform||[1,0,0,1,0,0],ce(zi,t.invTransform,e),e=zi);var n=this.originX,i=this.originY;(n||i)&&(Bi[4]=n,Bi[5]=i,ce(zi,e,Bi),zi[4]-=n,zi[5]-=i,e=zi),this.setLocalTransform(e)}},t.prototype.getGlobalScale=function(t){var e=this.transform;return t=t||[],e?(t[0]=Math.sqrt(e[0]*e[0]+e[1]*e[1]),t[1]=Math.sqrt(e[2]*e[2]+e[3]*e[3]),e[0]<0&&(t[0]=-t[0]),e[3]<0&&(t[1]=-t[1]),t):(t[0]=1,t[1]=1,t)},t.prototype.transformCoordToLocal=function(t,e){var n=[t,e],i=this.invTransform;return i&&Et(n,n,i),n},t.prototype.transformCoordToGlobal=function(t,e){var n=[t,e],i=this.transform;return i&&Et(n,n,i),n},t.prototype.getLineScale=function(){var t=this.transform;return t&&Fi(t[0]-1)>1e-10&&Fi(t[3]-1)>1e-10?Math.sqrt(Fi(t[0]*t[3]-t[2]*t[1])):1},t.prototype.copyTransform=function(t){!function(t,e){for(var n=0;n<Hi.length;n++){var i=Hi[n];t[i]=e[i]}}(this,t)},t.getLocalTransform=function(t,e){e=e||[];var n=t.originX||0,i=t.originY||0,r=t.scaleX,o=t.scaleY,a=t.anchorX,s=t.anchorY,l=t.rotation||0,u=t.x,h=t.y,c=t.skewX?Math.tan(t.skewX):0,p=t.skewY?Math.tan(-t.skewY):0;if(n||i||a||s){var f=n+a,d=i+s;e[4]=-f*r-c*d*o,e[5]=-d*o-p*f*r}else e[4]=e[5]=0;return e[0]=r,e[3]=o,e[1]=p*r,e[2]=c*o,l&&fe(e,e,l),e[4]+=n+u,e[5]+=i+h,e},t.initDefaultProps=function(){var e=t.prototype;e.scaleX=e.scaleY=e.globalScaleRatio=1,e.x=e.y=e.originX=e.originY=e.skewX=e.skewY=e.rotation=e.anchorX=e.anchorY=0}(),t}(),Hi=["x","y","originX","originY","anchorX","anchorY","rotation","scaleX","scaleY","skewX","skewY"];var Wi={};function Gi(t,e){var n=Wi[e=e||o];n||(n=Wi[e]=new Sn(500));var i=n.get(t);return null==i&&(i=u.measureText(t,e).width,n.put(t,i)),i}function Ui(t,e,n,i){var r=Gi(t,e),o=Zi(e),a=Yi(0,r,n),s=qi(0,o,i);return new Ce(a,s,r,o)}function Xi(t,e,n,i){var r=((t||"")+"").split("\n");if(1===r.length)return Ui(r[0],e,n,i);for(var o=new Ce(0,0,0,0),a=0;a<r.length;a++){var s=Ui(r[a],e,n,i);0===a?o.copy(s):o.union(s)}return o}function Yi(t,e,n){return"right"===n?t-=e:"center"===n&&(t-=e/2),t}function qi(t,e,n){return"middle"===n?t-=e/2:"bottom"===n&&(t-=e),t}function Zi(t){return Gi("国",t)}function ji(t,e){return"string"==typeof t?t.lastIndexOf("%")>=0?parseFloat(t)/100*e:parseFloat(t):t}function Ki(t,e,n){var i=e.position||"inside",r=null!=e.distance?e.distance:5,o=n.height,a=n.width,s=o/2,l=n.x,u=n.y,h="left",c="top";if(i instanceof Array)l+=ji(i[0],n.width),u+=ji(i[1],n.height),h=null,c=null;else switch(i){case"left":l-=r,u+=s,h="right",c="middle";break;case"right":l+=r+a,u+=s,c="middle";break;case"top":l+=a/2,u-=r,h="center",c="bottom";break;case"bottom":l+=a/2,u+=o+r,h="center";break;case"inside":l+=a/2,u+=s,h="center",c="middle";break;case"insideLeft":l+=r,u+=s,c="middle";break;case"insideRight":l+=a-r,u+=s,h="right",c="middle";break;case"insideTop":l+=a/2,u+=r,h="center";break;case"insideBottom":l+=a/2,u+=o-r,h="center",c="bottom";break;case"insideTopLeft":l+=r,u+=r;break;case"insideTopRight":l+=a-r,u+=r,h="right";break;case"insideBottomLeft":l+=r,u+=o-r,c="bottom";break;case"insideBottomRight":l+=a-r,u+=o-r,h="right",c="bottom"}return(t=t||{}).x=l,t.y=u,t.align=h,t.verticalAlign=c,t}var $i="__zr_normal__",Qi=Hi.concat(["ignore"]),Ji=E(Hi,(function(t,e){return t[e]=!0,t}),{ignore:!1}),tr={},er=new Ce(0,0,0,0),nr=function(){function t(t){this.id=S(),this.animators=[],this.currentStates=[],this.states={},this._init(t)}return t.prototype._init=function(t){this.attr(t)},t.prototype.drift=function(t,e,n){switch(this.draggable){case"horizontal":e=0;break;case"vertical":t=0}var i=this.transform;i||(i=this.transform=[1,0,0,1,0,0]),i[4]+=t,i[5]+=e,this.decomposeTransform(),this.markRedraw()},t.prototype.beforeUpdate=function(){},t.prototype.afterUpdate=function(){},t.prototype.update=function(){this.updateTransform(),this.__dirty&&this.updateInnerText()},t.prototype.updateInnerText=function(t){var e=this._textContent;if(e&&(!e.ignore||t)){this.textConfig||(this.textConfig={});var n=this.textConfig,i=n.local,r=e.innerTransformable,o=void 0,a=void 0,s=!1;r.parent=i?this:null;var l=!1;if(r.copyTransform(e),null!=n.position){var u=er;n.layoutRect?u.copy(n.layoutRect):u.copy(this.getBoundingRect()),i||u.applyTransform(this.transform),this.calculateTextPosition?this.calculateTextPosition(tr,n,u):Ki(tr,n,u),r.x=tr.x,r.y=tr.y,o=tr.align,a=tr.verticalAlign;var h=n.origin;if(h&&null!=n.rotation){var c=void 0,p=void 0;"center"===h?(c=.5*u.width,p=.5*u.height):(c=ji(h[0],u.width),p=ji(h[1],u.height)),l=!0,r.originX=-r.x+c+(i?0:u.x),r.originY=-r.y+p+(i?0:u.y)}}null!=n.rotation&&(r.rotation=n.rotation);var f=n.offset;f&&(r.x+=f[0],r.y+=f[1],l||(r.originX=-f[0],r.originY=-f[1]));var d=null==n.inside?"string"==typeof n.position&&n.position.indexOf("inside")>=0:n.inside,g=this._innerTextDefaultStyle||(this._innerTextDefaultStyle={}),y=void 0,v=void 0,m=void 0;d&&this.canBeInsideText()?(y=n.insideFill,v=n.insideStroke,null!=y&&"auto"!==y||(y=this.getInsideTextFill()),null!=v&&"auto"!==v||(v=this.getInsideTextStroke(y),m=!0)):(y=n.outsideFill,v=n.outsideStroke,null!=y&&"auto"!==y||(y=this.getOutsideFill()),null!=v&&"auto"!==v||(v=this.getOutsideStroke(y),m=!0)),(y=y||"#000")===g.fill&&v===g.stroke&&m===g.autoStroke&&o===g.align&&a===g.verticalAlign||(s=!0,g.fill=y,g.stroke=v,g.autoStroke=m,g.align=o,g.verticalAlign=a,e.setDefaultTextStyle(g)),e.__dirty|=1,s&&e.dirtyStyle(!0)}},t.prototype.canBeInsideText=function(){return!0},t.prototype.getInsideTextFill=function(){return"#fff"},t.prototype.getInsideTextStroke=function(t){return"#000"},t.prototype.getOutsideFill=function(){return this.__zr&&this.__zr.isDarkMode()?Pi:Li},t.prototype.getOutsideStroke=function(t){var e=this.__zr&&this.__zr.getBackgroundColor(),n="string"==typeof e&&En(e);n||(n=[255,255,255,1]);for(var i=n[3],r=this.__zr.isDarkMode(),o=0;o<3;o++)n[o]=n[o]*i+(r?0:255)*(1-i);return n[3]=1,Gn(n,"rgba")},t.prototype.traverse=function(t,e){},t.prototype.attrKV=function(t,e){"textConfig"===t?this.setTextConfig(e):"textContent"===t?this.setTextContent(e):"clipPath"===t?this.setClipPath(e):"extra"===t?(this.extra=this.extra||{},D(this.extra,e)):this[t]=e},t.prototype.hide=function(){this.ignore=!0,this.markRedraw()},t.prototype.show=function(){this.ignore=!1,this.markRedraw()},t.prototype.attr=function(t,e){if("string"==typeof t)this.attrKV(t,e);else if(Y(t))for(var n=B(t),i=0;i<n.length;i++){var r=n[i];this.attrKV(r,t[r])}return this.markRedraw(),this},t.prototype.saveCurrentToNormalState=function(t){this._innerSaveToNormal(t);for(var e=this._normalState,n=0;n<this.animators.length;n++){var i=this.animators[n],r=i.__fromStateTransition;if(!(i.getLoop()||r&&r!==$i)){var o=i.targetName,a=o?e[o]:e;i.saveTo(a)}}},t.prototype._innerSaveToNormal=function(t){var e=this._normalState;e||(e=this._normalState={}),t.textConfig&&!e.textConfig&&(e.textConfig=this.textConfig),this._savePrimaryToNormal(t,e,Qi)},t.prototype._savePrimaryToNormal=function(t,e,n){for(var i=0;i<n.length;i++){var r=n[i];null==t[r]||r in e||(e[r]=this[r])}},t.prototype.hasState=function(){return this.currentStates.length>0},t.prototype.getState=function(t){return this.states[t]},t.prototype.ensureState=function(t){var e=this.states;return e[t]||(e[t]={}),e[t]},t.prototype.clearStates=function(t){this.useState($i,!1,t)},t.prototype.useState=function(t,e,n,i){var r=t===$i;if(this.hasState()||!r){var o=this.currentStates,a=this.stateTransition;if(!(A(o,t)>=0)||!e&&1!==o.length){var s;if(this.stateProxy&&!r&&(s=this.stateProxy(t)),s||(s=this.states&&this.states[t]),s||r){r||this.saveCurrentToNormalState(s);var l=!!(s&&s.hoverLayer||i);l&&this._toggleHoverLayerFlag(!0),this._applyStateObj(t,s,this._normalState,e,!n&&!this.__inHover&&a&&a.duration>0,a);var u=this._textContent,h=this._textGuide;return u&&u.useState(t,e,n,l),h&&h.useState(t,e,n,l),r?(this.currentStates=[],this._normalState={}):e?this.currentStates.push(t):this.currentStates=[t],this._updateAnimationTargets(),this.markRedraw(),!l&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=-2),s}M("State "+t+" not exists.")}}},t.prototype.useStates=function(t,e,n){if(t.length){var i=[],r=this.currentStates,o=t.length,a=o===r.length;if(a)for(var s=0;s<o;s++)if(t[s]!==r[s]){a=!1;break}if(a)return;for(s=0;s<o;s++){var l=t[s],u=void 0;this.stateProxy&&(u=this.stateProxy(l,t)),u||(u=this.states[l]),u&&i.push(u)}var h=i[o-1],c=!!(h&&h.hoverLayer||n);c&&this._toggleHoverLayerFlag(!0);var p=this._mergeStates(i),f=this.stateTransition;this.saveCurrentToNormalState(p),this._applyStateObj(t.join(","),p,this._normalState,!1,!e&&!this.__inHover&&f&&f.duration>0,f);var d=this._textContent,g=this._textGuide;d&&d.useStates(t,e,c),g&&g.useStates(t,e,c),this._updateAnimationTargets(),this.currentStates=t.slice(),this.markRedraw(),!c&&this.__inHover&&(this._toggleHoverLayerFlag(!1),this.__dirty&=-2)}else this.clearStates()},t.prototype.isSilent=function(){for(var t=this.silent,e=this.parent;!t&&e;){if(e.silent){t=!0;break}e=e.parent}return t},t.prototype._updateAnimationTargets=function(){for(var t=0;t<this.animators.length;t++){var e=this.animators[t];e.targetName&&e.changeTarget(this[e.targetName])}},t.prototype.removeState=function(t){var e=A(this.currentStates,t);if(e>=0){var n=this.currentStates.slice();n.splice(e,1),this.useStates(n)}},t.prototype.replaceState=function(t,e,n){var i=this.currentStates.slice(),r=A(i,t),o=A(i,e)>=0;r>=0?o?i.splice(r,1):i[r]=e:n&&!o&&i.push(e),this.useStates(i)},t.prototype.toggleState=function(t,e){e?this.useState(t,!0):this.removeState(t)},t.prototype._mergeStates=function(t){for(var e,n={},i=0;i<t.length;i++){var r=t[i];D(n,r),r.textConfig&&D(e=e||{},r.textConfig)}return e&&(n.textConfig=e),n},t.prototype._applyStateObj=function(t,e,n,i,r,o){var a=!(e&&i);e&&e.textConfig?(this.textConfig=D({},i?this.textConfig:n.textConfig),D(this.textConfig,e.textConfig)):a&&n.textConfig&&(this.textConfig=n.textConfig);for(var s={},l=!1,u=0;u<Qi.length;u++){var h=Qi[u],c=r&&Ji[h];e&&null!=e[h]?c?(l=!0,s[h]=e[h]):this[h]=e[h]:a&&null!=n[h]&&(c?(l=!0,s[h]=n[h]):this[h]=n[h])}if(!r)for(u=0;u<this.animators.length;u++){var p=this.animators[u],f=p.targetName;p.getLoop()||p.__changeFinalValue(f?(e||n)[f]:e||n)}l&&this._transitionState(t,s,o)},t.prototype._attachComponent=function(t){if((!t.__zr||t.__hostTarget)&&t!==this){var e=this.__zr;e&&t.addSelfToZr(e),t.__zr=e,t.__hostTarget=this}},t.prototype._detachComponent=function(t){t.__zr&&t.removeSelfFromZr(t.__zr),t.__zr=null,t.__hostTarget=null},t.prototype.getClipPath=function(){return this._clipPath},t.prototype.setClipPath=function(t){this._clipPath&&this._clipPath!==t&&this.removeClipPath(),this._attachComponent(t),this._clipPath=t,this.markRedraw()},t.prototype.removeClipPath=function(){var t=this._clipPath;t&&(this._detachComponent(t),this._clipPath=null,this.markRedraw())},t.prototype.getTextContent=function(){return this._textContent},t.prototype.setTextContent=function(t){var e=this._textContent;e!==t&&(e&&e!==t&&this.removeTextContent(),t.innerTransformable=new Vi,this._attachComponent(t),this._textContent=t,this.markRedraw())},t.prototype.setTextConfig=function(t){this.textConfig||(this.textConfig={}),D(this.textConfig,t),this.markRedraw()},t.prototype.removeTextConfig=function(){this.textConfig=null,this.markRedraw()},t.prototype.removeTextContent=function(){var t=this._textContent;t&&(t.innerTransformable=null,this._detachComponent(t),this._textContent=null,this._innerTextDefaultStyle=null,this.markRedraw())},t.prototype.getTextGuideLine=function(){return this._textGuide},t.prototype.setTextGuideLine=function(t){this._textGuide&&this._textGuide!==t&&this.removeTextGuideLine(),this._attachComponent(t),this._textGuide=t,this.markRedraw()},t.prototype.removeTextGuideLine=function(){var t=this._textGuide;t&&(this._detachComponent(t),this._textGuide=null,this.markRedraw())},t.prototype.markRedraw=function(){this.__dirty|=1;var t=this.__zr;t&&(this.__inHover?t.refreshHover():t.refresh()),this.__hostTarget&&this.__hostTarget.markRedraw()},t.prototype.dirty=function(){this.markRedraw()},t.prototype._toggleHoverLayerFlag=function(t){this.__inHover=t;var e=this._textContent,n=this._textGuide;e&&(e.__inHover=t),n&&(n.__inHover=t)},t.prototype.addSelfToZr=function(t){if(this.__zr!==t){this.__zr=t;var e=this.animators;if(e)for(var n=0;n<e.length;n++)t.animation.addAnimator(e[n]);this._clipPath&&this._clipPath.addSelfToZr(t),this._textContent&&this._textContent.addSelfToZr(t),this._textGuide&&this._textGuide.addSelfToZr(t)}},t.prototype.removeSelfFromZr=function(t){if(this.__zr){this.__zr=null;var e=this.animators;if(e)for(var n=0;n<e.length;n++)t.animation.removeAnimator(e[n]);this._clipPath&&this._clipPath.removeSelfFromZr(t),this._textContent&&this._textContent.removeSelfFromZr(t),this._textGuide&&this._textGuide.removeSelfFromZr(t)}},t.prototype.animate=function(t,e,n){var i=t?this[t]:this;var r=new si(i,e,n);return t&&(r.targetName=t),this.addAnimator(r,t),r},t.prototype.addAnimator=function(t,e){var n=this.__zr,i=this;t.during((function(){i.updateDuringAnimation(e)})).done((function(){var e=i.animators,n=A(e,t);n>=0&&e.splice(n,1)})),this.animators.push(t),n&&n.animation.addAnimator(t),n&&n.wakeUp()},t.prototype.updateDuringAnimation=function(t){this.markRedraw()},t.prototype.stopAnimation=function(t,e){for(var n=this.animators,i=n.length,r=[],o=0;o<i;o++){var a=n[o];t&&t!==a.scope?r.push(a):a.stop(e)}return this.animators=r,this},t.prototype.animateTo=function(t,e,n){ir(this,t,e,n)},t.prototype.animateFrom=function(t,e,n){ir(this,t,e,n,!0)},t.prototype._transitionState=function(t,e,n,i){for(var r=ir(this,e,n,i),o=0;o<r.length;o++)r[o].__fromStateTransition=t},t.prototype.getBoundingRect=function(){return null},t.prototype.getPaintRect=function(){return null},t.initDefaultProps=function(){var e=t.prototype;e.type="element",e.name="",e.ignore=e.silent=e.isGroup=e.draggable=e.dragging=e.ignoreClip=e.__inHover=!1,e.__dirty=1;function n(t,n,i,r){function o(t,e){Object.defineProperty(e,0,{get:function(){return t[i]},set:function(e){t[i]=e}}),Object.defineProperty(e,1,{get:function(){return t[r]},set:function(e){t[r]=e}})}Object.defineProperty(e,t,{get:function(){this[n]||o(this,this[n]=[]);return this[n]},set:function(t){this[i]=t[0],this[r]=t[1],this[n]=t,o(this,t)}})}Object.defineProperty&&(n("position","_legacyPos","x","y"),n("scale","_legacyScale","scaleX","scaleY"),n("origin","_legacyOrigin","originX","originY"))}(),t}();function ir(t,e,n,i,r){var o=[];ar(t,"",t,e,n=n||{},i,o,r);var a=o.length,s=!1,l=n.done,u=n.aborted,h=function(){s=!0,--a<=0&&(s?l&&l():u&&u())},c=function(){--a<=0&&(s?l&&l():u&&u())};a||l&&l(),o.length>0&&n.during&&o[0].during((function(t,e){n.during(e)}));for(var p=0;p<o.length;p++){var f=o[p];h&&f.done(h),c&&f.aborted(c),n.force&&f.duration(n.duration),f.start(n.easing)}return o}function rr(t,e,n){for(var i=0;i<n;i++)t[i]=e[i]}function or(t,e,n){if(O(e[n]))if(O(t[n])||(t[n]=[]),Z(e[n])){var i=e[n].length;t[n].length!==i&&(t[n]=new e[n].constructor(i),rr(t[n],e[n],i))}else{var r=e[n],o=t[n],a=r.length;if(O(r[0]))for(var s=r[0].length,l=0;l<a;l++)o[l]?rr(o[l],r[l],s):o[l]=Array.prototype.slice.call(r[l]);else rr(o,r,a);o.length=r.length}else t[n]=e[n]}function ar(t,e,n,i,r,o,a,s){for(var l=B(i),u=r.duration,h=r.delay,c=r.additive,p=r.setToFinal,f=!Y(o),d=t.animators,g=[],y=0;y<l.length;y++){var v=l[y],m=i[v];if(null!=m&&null!=n[v]&&(f||o[v]))if(!Y(m)||O(m)||K(m))g.push(v);else{if(e){s||(n[v]=m,t.updateDuringAnimation(e));continue}ar(t,v,n[v],m,r,o&&o[v],a,s)}else s||(n[v]=m,t.updateDuringAnimation(e),g.push(v))}var _=g.length;if(!c&&_)for(var x=0;x<d.length;x++){if((b=d[x]).targetName===e)if(b.stopTracks(g)){var w=A(d,b);d.splice(w,1)}}if(r.force||(g=z(g,(function(t){return e=i[t],r=n[t],!(e===r||O(e)&&O(r)&&function(t,e){var n=t.length;if(n!==e.length)return!1;for(var i=0;i<n;i++)if(t[i]!==e[i])return!1;return!0}(e,r));var e,r})),_=g.length),_>0||r.force&&!a.length){var b,S=void 0,M=void 0,T=void 0;if(s){M={},p&&(S={});for(x=0;x<_;x++){M[v=g[x]]=n[v],p?S[v]=i[v]:n[v]=i[v]}}else if(p){T={};for(x=0;x<_;x++){T[v=g[x]]=ei(n[v]),or(n,i,v)}}(b=new si(n,!1,!1,c?z(d,(function(t){return t.targetName===e})):null)).targetName=e,r.scope&&(b.scope=r.scope),p&&S&&b.whenWithKeys(0,S,g),T&&b.whenWithKeys(0,T,g),b.whenWithKeys(null==u?500:u,s?M:i,g).delay(h||0),t.addAnimator(b,e),a.push(b)}}P(nr,Wt),P(nr,Vi);var sr=function(t){function e(e){var n=t.call(this)||this;return n.isGroup=!0,n._children=[],n.attr(e),n}return n(e,t),e.prototype.childrenRef=function(){return this._children},e.prototype.children=function(){return this._children.slice()},e.prototype.childAt=function(t){return this._children[t]},e.prototype.childOfName=function(t){for(var e=this._children,n=0;n<e.length;n++)if(e[n].name===t)return e[n]},e.prototype.childCount=function(){return this._children.length},e.prototype.add=function(t){return t&&t!==this&&t.parent!==this&&(this._children.push(t),this._doAdd(t)),this},e.prototype.addBefore=function(t,e){if(t&&t!==this&&t.parent!==this&&e&&e.parent===this){var n=this._children,i=n.indexOf(e);i>=0&&(n.splice(i,0,t),this._doAdd(t))}return this},e.prototype.replace=function(t,e){var n=A(this._children,t);return n>=0&&this.replaceAt(e,n),this},e.prototype.replaceAt=function(t,e){var n=this._children,i=n[e];if(t&&t!==this&&t.parent!==this&&t!==i){n[e]=t,i.parent=null;var r=this.__zr;r&&i.removeSelfFromZr(r),this._doAdd(t)}return this},e.prototype._doAdd=function(t){t.parent&&t.parent.remove(t),t.parent=this;var e=this.__zr;e&&e!==t.__zr&&t.addSelfToZr(e),e&&e.refresh()},e.prototype.remove=function(t){var e=this.__zr,n=this._children,i=A(n,t);return i<0||(n.splice(i,1),t.parent=null,e&&t.removeSelfFromZr(e),e&&e.refresh()),this},e.prototype.removeAll=function(){for(var t=this._children,e=this.__zr,n=0;n<t.length;n++){var i=t[n];e&&i.removeSelfFromZr(e),i.parent=null}return t.length=0,this},e.prototype.eachChild=function(t,e){for(var n=this._children,i=0;i<n.length;i++){var r=n[i];t.call(e,r,i)}return this},e.prototype.traverse=function(t,e){for(var n=0;n<this._children.length;n++){var i=this._children[n],r=t.call(e,i);i.isGroup&&!r&&i.traverse(t,e)}return this},e.prototype.addSelfToZr=function(e){t.prototype.addSelfToZr.call(this,e);for(var n=0;n<this._children.length;n++){this._children[n].addSelfToZr(e)}},e.prototype.removeSelfFromZr=function(e){t.prototype.removeSelfFromZr.call(this,e);for(var n=0;n<this._children.length;n++){this._children[n].removeSelfFromZr(e)}},e.prototype.getBoundingRect=function(t){for(var e=new Ce(0,0,0,0),n=t||this._children,i=[],r=null,o=0;o<n.length;o++){var a=n[o];if(!a.ignore&&!a.invisible){var s=a.getBoundingRect(),l=a.getLocalTransform(i);l?(Ce.applyTransform(e,s,l),(r=r||e.clone()).union(e)):(r=r||s.clone()).union(s)}}return r||e},e}(nr);sr.prototype.type="group";
/*!
    * ZRender, a high performance 2d drawing library.
    *
    * Copyright (c) 2013, Baidu Inc.
    * All rights reserved.
    *
    * LICENSE
    * https://github.com/ecomfe/zrender/blob/master/LICENSE.txt
    */
var lr={},ur={};var hr,cr=function(){function t(t,e,n){var i=this;this._sleepAfterStill=10,this._stillFrameAccum=0,this._needsRefresh=!0,this._needsRefreshHover=!0,this._darkMode=!1,n=n||{},this.dom=e,this.id=t;var o=new Ye,a=n.renderer||"canvas";lr[a]||(a=B(lr)[0]),n.useDirtyRect=null!=n.useDirtyRect&&n.useDirtyRect;var s=new lr[a](e,o,n,t),l=n.ssr||s.ssrOnly;this.storage=o,this.painter=s;var u,h=r.node||r.worker||l?null:new Ii(s.getViewportRoot(),s.root),c=n.useCoarsePointer;(null==c||"auto"===c?r.touchEventsSupported:!!c)&&(u=et(n.pointerSize,44)),this.handler=new Oe(o,s,h,s.root,u),this.animation=new ci({stage:{update:l?null:function(){return i._flush(!0)}}}),l||this.animation.start()}return t.prototype.add=function(t){!this._disposed&&t&&(this.storage.addRoot(t),t.addSelfToZr(this),this.refresh())},t.prototype.remove=function(t){!this._disposed&&t&&(this.storage.delRoot(t),t.removeSelfFromZr(this),this.refresh())},t.prototype.configLayer=function(t,e){this._disposed||(this.painter.configLayer&&this.painter.configLayer(t,e),this.refresh())},t.prototype.setBackgroundColor=function(t){this._disposed||(this.painter.setBackgroundColor&&this.painter.setBackgroundColor(t),this.refresh(),this._backgroundColor=t,this._darkMode=function(t){if(!t)return!1;if("string"==typeof t)return Un(t,1)<.4;if(t.colorStops){for(var e=t.colorStops,n=0,i=e.length,r=0;r<i;r++)n+=Un(e[r].color,1);return(n/=i)<.4}return!1}(t))},t.prototype.getBackgroundColor=function(){return this._backgroundColor},t.prototype.setDarkMode=function(t){this._darkMode=t},t.prototype.isDarkMode=function(){return this._darkMode},t.prototype.refreshImmediately=function(t){this._disposed||(t||this.animation.update(!0),this._needsRefresh=!1,this.painter.refresh(),this._needsRefresh=!1)},t.prototype.refresh=function(){this._disposed||(this._needsRefresh=!0,this.animation.start())},t.prototype.flush=function(){this._disposed||this._flush(!1)},t.prototype._flush=function(t){var e,n=li();this._needsRefresh&&(e=!0,this.refreshImmediately(t)),this._needsRefreshHover&&(e=!0,this.refreshHoverImmediately());var i=li();e?(this._stillFrameAccum=0,this.trigger("rendered",{elapsedTime:i-n})):this._sleepAfterStill>0&&(this._stillFrameAccum++,this._stillFrameAccum>this._sleepAfterStill&&this.animation.stop())},t.prototype.setSleepAfterStill=function(t){this._sleepAfterStill=t},t.prototype.wakeUp=function(){this._disposed||(this.animation.start(),this._stillFrameAccum=0)},t.prototype.refreshHover=function(){this._needsRefreshHover=!0},t.prototype.refreshHoverImmediately=function(){this._disposed||(this._needsRefreshHover=!1,this.painter.refreshHover&&"canvas"===this.painter.getType()&&this.painter.refreshHover())},t.prototype.resize=function(t){this._disposed||(t=t||{},this.painter.resize(t.width,t.height),this.handler.resize())},t.prototype.clearAnimation=function(){this._disposed||this.animation.clear()},t.prototype.getWidth=function(){if(!this._disposed)return this.painter.getWidth()},t.prototype.getHeight=function(){if(!this._disposed)return this.painter.getHeight()},t.prototype.setCursorStyle=function(t){this._disposed||this.handler.setCursorStyle(t)},t.prototype.findHover=function(t,e){if(!this._disposed)return this.handler.findHover(t,e)},t.prototype.on=function(t,e,n){return this._disposed||this.handler.on(t,e,n),this},t.prototype.off=function(t,e){this._disposed||this.handler.off(t,e)},t.prototype.trigger=function(t,e){this._disposed||this.handler.trigger(t,e)},t.prototype.clear=function(){if(!this._disposed){for(var t=this.storage.getRoots(),e=0;e<t.length;e++)t[e]instanceof sr&&t[e].removeSelfFromZr(this);this.storage.delAllRoots(),this.painter.clear()}},t.prototype.dispose=function(){var t;this._disposed||(this.animation.stop(),this.clear(),this.storage.dispose(),this.painter.dispose(),this.handler.dispose(),this.animation=this.storage=this.painter=this.handler=null,this._disposed=!0,t=this.id,delete ur[t])},t}();function pr(t,e){var n=new cr(S(),t,e);return ur[n.id]=n,n}function fr(t,e){lr[t]=e}function dr(t){hr=t}var gr=Object.freeze({__proto__:null,init:pr,dispose:function(t){t.dispose()},disposeAll:function(){for(var t in ur)ur.hasOwnProperty(t)&&ur[t].dispose();ur={}},getInstance:function(t){return ur[t]},registerPainter:fr,getElementSSRData:function(t){if("function"==typeof hr)return hr(t)},registerSSRDataGetter:dr,version:"5.6.1"}),yr=1e-4;function vr(t,e,n,i){var r=e[0],o=e[1],a=n[0],s=n[1],l=o-r,u=s-a;if(0===l)return 0===u?a:(a+s)/2;if(i)if(l>0){if(t<=r)return a;if(t>=o)return s}else{if(t>=r)return a;if(t<=o)return s}else{if(t===r)return a;if(t===o)return s}return(t-r)/l*u+a}function mr(t,e){switch(t){case"center":case"middle":t="50%";break;case"left":case"top":t="0%";break;case"right":case"bottom":t="100%"}return G(t)?(n=t,n.replace(/^\s+|\s+$/g,"")).match(/%$/)?parseFloat(t)/100*e:parseFloat(t):null==t?NaN:+t;var n}function _r(t,e,n){return null==e&&(e=10),e=Math.min(Math.max(0,e),20),t=(+t).toFixed(e),n?t:+t}function xr(t){if(t=+t,isNaN(t))return 0;if(t>1e-14)for(var e=1,n=0;n<15;n++,e*=10)if(Math.round(t*e)/e===t)return n;return wr(t)}function wr(t){var e=t.toString().toLowerCase(),n=e.indexOf("e"),i=n>0?+e.slice(n+1):0,r=n>0?n:e.length,o=e.indexOf("."),a=o<0?0:r-1-o;return Math.max(0,a-i)}function br(t,e){var n=Math.log,i=Math.LN10,r=Math.floor(n(t[1]-t[0])/i),o=Math.round(n(Math.abs(e[1]-e[0]))/i),a=Math.min(Math.max(-r+o,0),20);return isFinite(a)?a:20}function Sr(t,e){var n=E(t,(function(t,e){return t+(isNaN(e)?0:e)}),0);if(0===n)return[];for(var i=Math.pow(10,e),r=N(t,(function(t){return(isNaN(t)?0:t)/n*i*100})),o=100*i,a=N(r,(function(t){return Math.floor(t)})),s=E(a,(function(t,e){return t+e}),0),l=N(r,(function(t,e){return t-a[e]}));s<o;){for(var u=Number.NEGATIVE_INFINITY,h=null,c=0,p=l.length;c<p;++c)l[c]>u&&(u=l[c],h=c);++a[h],l[h]=0,++s}return N(a,(function(t){return t/i}))}function Mr(t,e){var n=Math.max(xr(t),xr(e)),i=t+e;return n>20?i:_r(i,n)}function Tr(t){var e=2*Math.PI;return(t%e+e)%e}function Cr(t){return t>-1e-4&&t<yr}var Dr=/^(?:(\d{4})(?:[-\/](\d{1,2})(?:[-\/](\d{1,2})(?:[T ](\d{1,2})(?::(\d{1,2})(?::(\d{1,2})(?:[.,](\d+))?)?)?(Z|[\+\-]\d\d:?\d\d)?)?)?)?)?$/;function Ir(t){if(t instanceof Date)return t;if(G(t)){var e=Dr.exec(t);if(!e)return new Date(NaN);if(e[8]){var n=+e[4]||0;return"Z"!==e[8].toUpperCase()&&(n-=+e[8].slice(0,3)),new Date(Date.UTC(+e[1],+(e[2]||1)-1,+e[3]||1,n,+(e[5]||0),+e[6]||0,e[7]?+e[7].substring(0,3):0))}return new Date(+e[1],+(e[2]||1)-1,+e[3]||1,+e[4]||0,+(e[5]||0),+e[6]||0,e[7]?+e[7].substring(0,3):0)}return null==t?new Date(NaN):new Date(Math.round(t))}function kr(t){return Math.pow(10,Ar(t))}function Ar(t){if(0===t)return 0;var e=Math.floor(Math.log(t)/Math.LN10);return t/Math.pow(10,e)>=10&&e++,e}function Lr(t,e){var n=Ar(t),i=Math.pow(10,n),r=t/i;return t=(e?r<1.5?1:r<2.5?2:r<4?3:r<7?5:10:r<1?1:r<2?2:r<3?3:r<5?5:10)*i,n>=-20?+t.toFixed(n<0?-n:0):t}function Pr(t){var e=parseFloat(t);return e==t&&(0!==e||!G(t)||t.indexOf("x")<=0)?e:NaN}function Or(t){return!isNaN(Pr(t))}function Rr(t,e){return 0===e?t:Rr(e,t%e)}function Nr(t,e){return null==t?e:null==e?t:t*e/Rr(t,e)}"undefined"!=typeof console&&console.warn&&console.log;function Er(t){0}function zr(t){throw new Error(t)}function Br(t,e,n){return(e-t)*n+t}var Fr="series\0";function Vr(t){return t instanceof Array?t:null==t?[]:[t]}function Hr(t,e,n){if(t){t[e]=t[e]||{},t.emphasis=t.emphasis||{},t.emphasis[e]=t.emphasis[e]||{};for(var i=0,r=n.length;i<r;i++){var o=n[i];!t.emphasis[e].hasOwnProperty(o)&&t[e].hasOwnProperty(o)&&(t.emphasis[e][o]=t[e][o])}}}var Wr=["fontStyle","fontWeight","fontSize","fontFamily","rich","tag","color","textBorderColor","textBorderWidth","width","height","lineHeight","align","verticalAlign","baseline","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY","backgroundColor","borderColor","borderWidth","borderRadius","padding"];function Gr(t){return!Y(t)||H(t)||t instanceof Date?t:t.value}function Ur(t){return Y(t)&&!(t instanceof Array)}function Xr(t,e,n){var i="normalMerge"===n,r="replaceMerge"===n,o="replaceAll"===n;t=t||[],e=(e||[]).slice();var a=ft();R(e,(function(t,n){Y(t)||(e[n]=null)}));var s,l,u=function(t,e,n){var i=[];if("replaceAll"===n)return i;for(var r=0;r<t.length;r++){var o=t[r];o&&null!=o.id&&e.set(o.id,r),i.push({existing:"replaceMerge"===n||Kr(o)?null:o,newOption:null,keyInfo:null,brandNew:null})}return i}(t,a,n);return(i||r)&&function(t,e,n,i){R(i,(function(r,o){if(r&&null!=r.id){var a=qr(r.id),s=n.get(a);if(null!=s){var l=t[s];ot(!l.newOption,'Duplicated option on id "'+a+'".'),l.newOption=r,l.existing=e[s],i[o]=null}}}))}(u,t,a,e),i&&function(t,e){R(e,(function(n,i){if(n&&null!=n.name)for(var r=0;r<t.length;r++){var o=t[r].existing;if(!t[r].newOption&&o&&(null==o.id||null==n.id)&&!Kr(n)&&!Kr(o)&&Yr("name",o,n))return t[r].newOption=n,void(e[i]=null)}}))}(u,e),i||r?function(t,e,n){R(e,(function(e){if(e){for(var i,r=0;(i=t[r])&&(i.newOption||Kr(i.existing)||i.existing&&null!=e.id&&!Yr("id",e,i.existing));)r++;i?(i.newOption=e,i.brandNew=n):t.push({newOption:e,brandNew:n,existing:null,keyInfo:null}),r++}}))}(u,e,r):o&&function(t,e){R(e,(function(e){t.push({newOption:e,brandNew:!0,existing:null,keyInfo:null})}))}(u,e),s=u,l=ft(),R(s,(function(t){var e=t.existing;e&&l.set(e.id,t)})),R(s,(function(t){var e=t.newOption;ot(!e||null==e.id||!l.get(e.id)||l.get(e.id)===t,"id duplicates: "+(e&&e.id)),e&&null!=e.id&&l.set(e.id,t),!t.keyInfo&&(t.keyInfo={})})),R(s,(function(t,e){var n=t.existing,i=t.newOption,r=t.keyInfo;if(Y(i)){if(r.name=null!=i.name?qr(i.name):n?n.name:Fr+e,n)r.id=qr(n.id);else if(null!=i.id)r.id=qr(i.id);else{var o=0;do{r.id="\0"+r.name+"\0"+o++}while(l.get(r.id))}l.set(r.id,t)}})),u}function Yr(t,e,n){var i=Zr(e[t],null),r=Zr(n[t],null);return null!=i&&null!=r&&i===r}function qr(t){return Zr(t,"")}function Zr(t,e){return null==t?e:G(t)?t:X(t)||U(t)?t+"":e}function jr(t){var e=t.name;return!(!e||!e.indexOf(Fr))}function Kr(t){return t&&null!=t.id&&0===qr(t.id).indexOf("\0_ec_\0")}function $r(t,e){return null!=e.dataIndexInside?e.dataIndexInside:null!=e.dataIndex?H(e.dataIndex)?N(e.dataIndex,(function(e){return t.indexOfRawIndex(e)})):t.indexOfRawIndex(e.dataIndex):null!=e.name?H(e.name)?N(e.name,(function(e){return t.indexOfName(e)})):t.indexOfName(e.name):void 0}function Qr(){var t="__ec_inner_"+Jr++;return function(e){return e[t]||(e[t]={})}}var Jr=Math.round(9*Math.random());function to(t,e,n){var i=eo(e,n),r=i.mainTypeSpecified,o=i.queryOptionMap,a=i.others,s=n?n.defaultMainType:null;return!r&&s&&o.set(s,{}),o.each((function(e,i){var r=io(t,i,e,{useDefault:s===i,enableAll:!n||null==n.enableAll||n.enableAll,enableNone:!n||null==n.enableNone||n.enableNone});a[i+"Models"]=r.models,a[i+"Model"]=r.models[0]})),a}function eo(t,e){var n;if(G(t)){var i={};i[t+"Index"]=0,n=i}else n=t;var r=ft(),o={},a=!1;return R(n,(function(t,n){if("dataIndex"!==n&&"dataIndexInside"!==n){var i=n.match(/^(\w+)(Index|Id|Name)$/)||[],s=i[1],l=(i[2]||"").toLowerCase();if(s&&l&&!(e&&e.includeMainTypes&&A(e.includeMainTypes,s)<0))a=a||!!s,(r.get(s)||r.set(s,{}))[l]=t}else o[n]=t})),{mainTypeSpecified:a,queryOptionMap:r,others:o}}var no={useDefault:!0,enableAll:!1,enableNone:!1};function io(t,e,n,i){i=i||no;var r=n.index,o=n.id,a=n.name,s={models:null,specified:null!=r||null!=o||null!=a};if(!s.specified){var l=void 0;return s.models=i.useDefault&&(l=t.getComponent(e))?[l]:[],s}return"none"===r||!1===r?(ot(i.enableNone,'`"none"` or `false` is not a valid value on index option.'),s.models=[],s):("all"===r&&(ot(i.enableAll,'`"all"` is not a valid value on index option.'),r=o=a=null),s.models=t.queryComponents({mainType:e,index:r,id:o,name:a}),s)}function ro(t,e,n){t.setAttribute?t.setAttribute(e,n):t[e]=n}var oo="___EC__COMPONENT__CONTAINER___",ao="___EC__EXTENDED_CLASS___";function so(t){var e={main:"",sub:""};if(t){var n=t.split(".");e.main=n[0]||"",e.sub=n[1]||""}return e}function lo(t,e){t.$constructor=t,t.extend=function(t){var e,i,r=this;return W(i=r)&&/^class\s/.test(Function.prototype.toString.call(i))?e=function(t){function e(){return t.apply(this,arguments)||this}return n(e,t),e}(r):(e=function(){(t.$constructor||r).apply(this,arguments)},L(e,this)),D(e.prototype,t),e[ao]=!0,e.extend=this.extend,e.superCall=co,e.superApply=po,e.superClass=r,e}}function uo(t,e){t.extend=e.extend}var ho=Math.round(10*Math.random());function co(t,e){for(var n=[],i=2;i<arguments.length;i++)n[i-2]=arguments[i];return this.superClass.prototype[e].apply(t,n)}function po(t,e,n){return this.superClass.prototype[e].apply(t,n)}function fo(t){var e={};t.registerClass=function(t){var n,i=t.type||t.prototype.type;if(i){ot(/^[a-zA-Z0-9_]+([.][a-zA-Z0-9_]+)?$/.test(n=i),'componentType "'+n+'" illegal'),t.prototype.type=i;var r=so(i);if(r.sub){if(r.sub!==oo){var o=function(t){var n=e[t.main];n&&n[oo]||((n=e[t.main]={})[oo]=!0);return n}(r);o[r.sub]=t}}else e[r.main]=t}return t},t.getClass=function(t,n,i){var r=e[t];if(r&&r[oo]&&(r=n?r[n]:null),i&&!r)throw new Error(n?"Component "+t+"."+(n||"")+" is used but not imported.":t+".type should be specified.");return r},t.getClassesByMainType=function(t){var n=so(t),i=[],r=e[n.main];return r&&r[oo]?R(r,(function(t,e){e!==oo&&i.push(t)})):i.push(r),i},t.hasClass=function(t){var n=so(t);return!!e[n.main]},t.getAllClassMainTypes=function(){var t=[];return R(e,(function(e,n){t.push(n)})),t},t.hasSubTypes=function(t){var n=so(t),i=e[n.main];return i&&i[oo]}}function go(t,e){for(var n=0;n<t.length;n++)t[n][1]||(t[n][1]=t[n][0]);return e=e||!1,function(n,i,r){for(var o={},a=0;a<t.length;a++){var s=t[a][1];if(!(i&&A(i,s)>=0||r&&A(r,s)<0)){var l=n.getShallow(s,e);null!=l&&(o[t[a][0]]=l)}}return o}}var yo=go([["fill","color"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["opacity"],["shadowColor"]]),vo=function(){function t(){}return t.prototype.getAreaStyle=function(t,e){return yo(this,t,e)},t}(),mo=new Sn(50);function _o(t){if("string"==typeof t){var e=mo.get(t);return e&&e.image}return t}function xo(t,e,n,i,r){if(t){if("string"==typeof t){if(e&&e.__zrImageSrc===t||!n)return e;var o=mo.get(t),a={hostEl:n,cb:i,cbPayload:r};return o?!bo(e=o.image)&&o.pending.push(a):((e=u.loadImage(t,wo,wo)).__zrImageSrc=t,mo.put(t,e.__cachedImgObj={image:e,pending:[a]})),e}return t}return e}function wo(){var t=this.__cachedImgObj;this.onload=this.onerror=this.__cachedImgObj=null;for(var e=0;e<t.pending.length;e++){var n=t.pending[e],i=n.cb;i&&i(this,n.cbPayload),n.hostEl.dirty()}t.pending.length=0}function bo(t){return t&&t.width&&t.height}var So=/\{([a-zA-Z0-9_]+)\|([^}]*)\}/g;function Mo(t,e,n,i,r,o){if(!n)return t.text="",void(t.isTruncated=!1);var a=(e+"").split("\n");o=To(n,i,r,o);for(var s=!1,l={},u=0,h=a.length;u<h;u++)Co(l,a[u],o),a[u]=l.textLine,s=s||l.isTruncated;t.text=a.join("\n"),t.isTruncated=s}function To(t,e,n,i){var r=D({},i=i||{});r.font=e,n=et(n,"..."),r.maxIterations=et(i.maxIterations,2);var o=r.minChar=et(i.minChar,0);r.cnCharWidth=Gi("国",e);var a=r.ascCharWidth=Gi("a",e);r.placeholder=et(i.placeholder,"");for(var s=t=Math.max(0,t-1),l=0;l<o&&s>=a;l++)s-=a;var u=Gi(n,e);return u>s&&(n="",u=0),s=t-u,r.ellipsis=n,r.ellipsisWidth=u,r.contentWidth=s,r.containerWidth=t,r}function Co(t,e,n){var i=n.containerWidth,r=n.font,o=n.contentWidth;if(!i)return t.textLine="",void(t.isTruncated=!1);var a=Gi(e,r);if(a<=i)return t.textLine=e,void(t.isTruncated=!1);for(var s=0;;s++){if(a<=o||s>=n.maxIterations){e+=n.ellipsis;break}var l=0===s?Do(e,o,n.ascCharWidth,n.cnCharWidth):a>0?Math.floor(e.length*o/a):0;a=Gi(e=e.substr(0,l),r)}""===e&&(e=n.placeholder),t.textLine=e,t.isTruncated=!0}function Do(t,e,n,i){for(var r=0,o=0,a=t.length;o<a&&r<e;o++){var s=t.charCodeAt(o);r+=0<=s&&s<=127?n:i}return o}var Io=function(){},ko=function(t){this.tokens=[],t&&(this.tokens=t)},Ao=function(){this.width=0,this.height=0,this.contentWidth=0,this.contentHeight=0,this.outerWidth=0,this.outerHeight=0,this.lines=[],this.isTruncated=!1};function Lo(t,e,n,i,r){var o,a,s=""===e,l=r&&n.rich[r]||{},u=t.lines,h=l.font||n.font,c=!1;if(i){var p=l.padding,f=p?p[1]+p[3]:0;if(null!=l.width&&"auto"!==l.width){var d=ji(l.width,i.width)+f;u.length>0&&d+i.accumWidth>i.width&&(o=e.split("\n"),c=!0),i.accumWidth=d}else{var g=Ro(e,h,i.width,i.breakAll,i.accumWidth);i.accumWidth=g.accumWidth+f,a=g.linesWidths,o=g.lines}}else o=e.split("\n");for(var y=0;y<o.length;y++){var v=o[y],m=new Io;if(m.styleName=r,m.text=v,m.isLineHolder=!v&&!s,"number"==typeof l.width?m.width=l.width:m.width=a?a[y]:Gi(v,h),y||c)u.push(new ko([m]));else{var _=(u[u.length-1]||(u[0]=new ko)).tokens,x=_.length;1===x&&_[0].isLineHolder?_[0]=m:(v||!x||s)&&_.push(m)}}}var Po=E(",&?/;] ".split(""),(function(t,e){return t[e]=!0,t}),{});function Oo(t){return!function(t){var e=t.charCodeAt(0);return e>=32&&e<=591||e>=880&&e<=4351||e>=4608&&e<=5119||e>=7680&&e<=8303}(t)||!!Po[t]}function Ro(t,e,n,i,r){for(var o=[],a=[],s="",l="",u=0,h=0,c=0;c<t.length;c++){var p=t.charAt(c);if("\n"!==p){var f=Gi(p,e),d=!i&&!Oo(p);(o.length?h+f>n:r+h+f>n)?h?(s||l)&&(d?(s||(s=l,l="",h=u=0),o.push(s),a.push(h-u),l+=p,s="",h=u+=f):(l&&(s+=l,l="",u=0),o.push(s),a.push(h),s=p,h=f)):d?(o.push(l),a.push(u),l=p,u=f):(o.push(p),a.push(f)):(h+=f,d?(l+=p,u+=f):(l&&(s+=l,l="",u=0),s+=p))}else l&&(s+=l,h+=u),o.push(s),a.push(h),s="",l="",u=0,h=0}return o.length||s||(s=t,l="",u=0),l&&(s+=l),s&&(o.push(s),a.push(h)),1===o.length&&(h+=r),{accumWidth:h,lines:o,linesWidths:a}}var No="__zr_style_"+Math.round(10*Math.random()),Eo={shadowBlur:0,shadowOffsetX:0,shadowOffsetY:0,shadowColor:"#000",opacity:1,blend:"source-over"},zo={style:{shadowBlur:!0,shadowOffsetX:!0,shadowOffsetY:!0,shadowColor:!0,opacity:!0}};Eo[No]=!0;var Bo=["z","z2","invisible"],Fo=["invisible"],Vo=function(t){function e(e){return t.call(this,e)||this}var i;return n(e,t),e.prototype._init=function(e){for(var n=B(e),i=0;i<n.length;i++){var r=n[i];"style"===r?this.useStyle(e[r]):t.prototype.attrKV.call(this,r,e[r])}this.style||this.useStyle({})},e.prototype.beforeBrush=function(){},e.prototype.afterBrush=function(){},e.prototype.innerBeforeBrush=function(){},e.prototype.innerAfterBrush=function(){},e.prototype.shouldBePainted=function(t,e,n,i){var r=this.transform;if(this.ignore||this.invisible||0===this.style.opacity||this.culling&&function(t,e,n){Ho.copy(t.getBoundingRect()),t.transform&&Ho.applyTransform(t.transform);return Wo.width=e,Wo.height=n,!Ho.intersect(Wo)}(this,t,e)||r&&!r[0]&&!r[3])return!1;if(n&&this.__clipPaths)for(var o=0;o<this.__clipPaths.length;++o)if(this.__clipPaths[o].isZeroArea())return!1;if(i&&this.parent)for(var a=this.parent;a;){if(a.ignore)return!1;a=a.parent}return!0},e.prototype.contain=function(t,e){return this.rectContain(t,e)},e.prototype.traverse=function(t,e){t.call(e,this)},e.prototype.rectContain=function(t,e){var n=this.transformCoordToLocal(t,e);return this.getBoundingRect().contain(n[0],n[1])},e.prototype.getPaintRect=function(){var t=this._paintRect;if(!this._paintRect||this.__dirty){var e=this.transform,n=this.getBoundingRect(),i=this.style,r=i.shadowBlur||0,o=i.shadowOffsetX||0,a=i.shadowOffsetY||0;t=this._paintRect||(this._paintRect=new Ce(0,0,0,0)),e?Ce.applyTransform(t,n,e):t.copy(n),(r||o||a)&&(t.width+=2*r+Math.abs(o),t.height+=2*r+Math.abs(a),t.x=Math.min(t.x,t.x+o-r),t.y=Math.min(t.y,t.y+a-r));var s=this.dirtyRectTolerance;t.isZero()||(t.x=Math.floor(t.x-s),t.y=Math.floor(t.y-s),t.width=Math.ceil(t.width+1+2*s),t.height=Math.ceil(t.height+1+2*s))}return t},e.prototype.setPrevPaintRect=function(t){t?(this._prevPaintRect=this._prevPaintRect||new Ce(0,0,0,0),this._prevPaintRect.copy(t)):this._prevPaintRect=null},e.prototype.getPrevPaintRect=function(){return this._prevPaintRect},e.prototype.animateStyle=function(t){return this.animate("style",t)},e.prototype.updateDuringAnimation=function(t){"style"===t?this.dirtyStyle():this.markRedraw()},e.prototype.attrKV=function(e,n){"style"!==e?t.prototype.attrKV.call(this,e,n):this.style?this.setStyle(n):this.useStyle(n)},e.prototype.setStyle=function(t,e){return"string"==typeof t?this.style[t]=e:D(this.style,t),this.dirtyStyle(),this},e.prototype.dirtyStyle=function(t){t||this.markRedraw(),this.__dirty|=2,this._rect&&(this._rect=null)},e.prototype.dirty=function(){this.dirtyStyle()},e.prototype.styleChanged=function(){return!!(2&this.__dirty)},e.prototype.styleUpdated=function(){this.__dirty&=-3},e.prototype.createStyle=function(t){return gt(Eo,t)},e.prototype.useStyle=function(t){t[No]||(t=this.createStyle(t)),this.__inHover?this.__hoverStyle=t:this.style=t,this.dirtyStyle()},e.prototype.isStyleObject=function(t){return t[No]},e.prototype._innerSaveToNormal=function(e){t.prototype._innerSaveToNormal.call(this,e);var n=this._normalState;e.style&&!n.style&&(n.style=this._mergeStyle(this.createStyle(),this.style)),this._savePrimaryToNormal(e,n,Bo)},e.prototype._applyStateObj=function(e,n,i,r,o,a){t.prototype._applyStateObj.call(this,e,n,i,r,o,a);var s,l=!(n&&r);if(n&&n.style?o?r?s=n.style:(s=this._mergeStyle(this.createStyle(),i.style),this._mergeStyle(s,n.style)):(s=this._mergeStyle(this.createStyle(),r?this.style:i.style),this._mergeStyle(s,n.style)):l&&(s=i.style),s)if(o){var u=this.style;if(this.style=this.createStyle(l?{}:u),l)for(var h=B(u),c=0;c<h.length;c++){(f=h[c])in s&&(s[f]=s[f],this.style[f]=u[f])}var p=B(s);for(c=0;c<p.length;c++){var f=p[c];this.style[f]=this.style[f]}this._transitionState(e,{style:s},a,this.getAnimationStyleProps())}else this.useStyle(s);var d=this.__inHover?Fo:Bo;for(c=0;c<d.length;c++){f=d[c];n&&null!=n[f]?this[f]=n[f]:l&&null!=i[f]&&(this[f]=i[f])}},e.prototype._mergeStates=function(e){for(var n,i=t.prototype._mergeStates.call(this,e),r=0;r<e.length;r++){var o=e[r];o.style&&(n=n||{},this._mergeStyle(n,o.style))}return n&&(i.style=n),i},e.prototype._mergeStyle=function(t,e){return D(t,e),t},e.prototype.getAnimationStyleProps=function(){return zo},e.initDefaultProps=((i=e.prototype).type="displayable",i.invisible=!1,i.z=0,i.z2=0,i.zlevel=0,i.culling=!1,i.cursor="pointer",i.rectHover=!1,i.incremental=!1,i._rect=null,i.dirtyRectTolerance=0,void(i.__dirty=3)),e}(nr),Ho=new Ce(0,0,0,0),Wo=new Ce(0,0,0,0);var Go=Math.min,Uo=Math.max,Xo=Math.sin,Yo=Math.cos,qo=2*Math.PI,Zo=wt(),jo=wt(),Ko=wt();function $o(t,e,n,i,r,o){r[0]=Go(t,n),r[1]=Go(e,i),o[0]=Uo(t,n),o[1]=Uo(e,i)}var Qo=[],Jo=[];function ta(t,e,n,i,r,o,a,s,l,u){var h=hn,c=sn,p=h(t,n,r,a,Qo);l[0]=1/0,l[1]=1/0,u[0]=-1/0,u[1]=-1/0;for(var f=0;f<p;f++){var d=c(t,n,r,a,Qo[f]);l[0]=Go(d,l[0]),u[0]=Uo(d,u[0])}p=h(e,i,o,s,Jo);for(f=0;f<p;f++){var g=c(e,i,o,s,Jo[f]);l[1]=Go(g,l[1]),u[1]=Uo(g,u[1])}l[0]=Go(t,l[0]),u[0]=Uo(t,u[0]),l[0]=Go(a,l[0]),u[0]=Uo(a,u[0]),l[1]=Go(e,l[1]),u[1]=Uo(e,u[1]),l[1]=Go(s,l[1]),u[1]=Uo(s,u[1])}function ea(t,e,n,i,r,o,a,s){var l=gn,u=fn,h=Uo(Go(l(t,n,r),1),0),c=Uo(Go(l(e,i,o),1),0),p=u(t,n,r,h),f=u(e,i,o,c);a[0]=Go(t,r,p),a[1]=Go(e,o,f),s[0]=Uo(t,r,p),s[1]=Uo(e,o,f)}function na(t,e,n,i,r,o,a,s,l){var u=zt,h=Bt,c=Math.abs(r-o);if(c%qo<1e-4&&c>1e-4)return s[0]=t-n,s[1]=e-i,l[0]=t+n,void(l[1]=e+i);if(Zo[0]=Yo(r)*n+t,Zo[1]=Xo(r)*i+e,jo[0]=Yo(o)*n+t,jo[1]=Xo(o)*i+e,u(s,Zo,jo),h(l,Zo,jo),(r%=qo)<0&&(r+=qo),(o%=qo)<0&&(o+=qo),r>o&&!a?o+=qo:r<o&&a&&(r+=qo),a){var p=o;o=r,r=p}for(var f=0;f<o;f+=Math.PI/2)f>r&&(Ko[0]=Yo(f)*n+t,Ko[1]=Xo(f)*i+e,u(s,Ko,s),h(l,Ko,l))}var ia={M:1,L:2,C:3,Q:4,A:5,Z:6,R:7},ra=[],oa=[],aa=[],sa=[],la=[],ua=[],ha=Math.min,ca=Math.max,pa=Math.cos,fa=Math.sin,da=Math.abs,ga=Math.PI,ya=2*ga,va="undefined"!=typeof Float32Array,ma=[];function _a(t){return Math.round(t/ga*1e8)/1e8%2*ga}function xa(t,e){var n=_a(t[0]);n<0&&(n+=ya);var i=n-t[0],r=t[1];r+=i,!e&&r-n>=ya?r=n+ya:e&&n-r>=ya?r=n-ya:!e&&n>r?r=n+(ya-_a(n-r)):e&&n<r&&(r=n-(ya-_a(r-n))),t[0]=n,t[1]=r}var wa=function(){function t(t){this.dpr=1,this._xi=0,this._yi=0,this._x0=0,this._y0=0,this._len=0,t&&(this._saveData=!1),this._saveData&&(this.data=[])}return t.prototype.increaseVersion=function(){this._version++},t.prototype.getVersion=function(){return this._version},t.prototype.setScale=function(t,e,n){(n=n||0)>0&&(this._ux=da(n/Ai/t)||0,this._uy=da(n/Ai/e)||0)},t.prototype.setDPR=function(t){this.dpr=t},t.prototype.setContext=function(t){this._ctx=t},t.prototype.getContext=function(){return this._ctx},t.prototype.beginPath=function(){return this._ctx&&this._ctx.beginPath(),this.reset(),this},t.prototype.reset=function(){this._saveData&&(this._len=0),this._pathSegLen&&(this._pathSegLen=null,this._pathLen=0),this._version++},t.prototype.moveTo=function(t,e){return this._drawPendingPt(),this.addData(ia.M,t,e),this._ctx&&this._ctx.moveTo(t,e),this._x0=t,this._y0=e,this._xi=t,this._yi=e,this},t.prototype.lineTo=function(t,e){var n=da(t-this._xi),i=da(e-this._yi),r=n>this._ux||i>this._uy;if(this.addData(ia.L,t,e),this._ctx&&r&&this._ctx.lineTo(t,e),r)this._xi=t,this._yi=e,this._pendingPtDist=0;else{var o=n*n+i*i;o>this._pendingPtDist&&(this._pendingPtX=t,this._pendingPtY=e,this._pendingPtDist=o)}return this},t.prototype.bezierCurveTo=function(t,e,n,i,r,o){return this._drawPendingPt(),this.addData(ia.C,t,e,n,i,r,o),this._ctx&&this._ctx.bezierCurveTo(t,e,n,i,r,o),this._xi=r,this._yi=o,this},t.prototype.quadraticCurveTo=function(t,e,n,i){return this._drawPendingPt(),this.addData(ia.Q,t,e,n,i),this._ctx&&this._ctx.quadraticCurveTo(t,e,n,i),this._xi=n,this._yi=i,this},t.prototype.arc=function(t,e,n,i,r,o){this._drawPendingPt(),ma[0]=i,ma[1]=r,xa(ma,o),i=ma[0];var a=(r=ma[1])-i;return this.addData(ia.A,t,e,n,n,i,a,0,o?0:1),this._ctx&&this._ctx.arc(t,e,n,i,r,o),this._xi=pa(r)*n+t,this._yi=fa(r)*n+e,this},t.prototype.arcTo=function(t,e,n,i,r){return this._drawPendingPt(),this._ctx&&this._ctx.arcTo(t,e,n,i,r),this},t.prototype.rect=function(t,e,n,i){return this._drawPendingPt(),this._ctx&&this._ctx.rect(t,e,n,i),this.addData(ia.R,t,e,n,i),this},t.prototype.closePath=function(){this._drawPendingPt(),this.addData(ia.Z);var t=this._ctx,e=this._x0,n=this._y0;return t&&t.closePath(),this._xi=e,this._yi=n,this},t.prototype.fill=function(t){t&&t.fill(),this.toStatic()},t.prototype.stroke=function(t){t&&t.stroke(),this.toStatic()},t.prototype.len=function(){return this._len},t.prototype.setData=function(t){var e=t.length;this.data&&this.data.length===e||!va||(this.data=new Float32Array(e));for(var n=0;n<e;n++)this.data[n]=t[n];this._len=e},t.prototype.appendPath=function(t){t instanceof Array||(t=[t]);for(var e=t.length,n=0,i=this._len,r=0;r<e;r++)n+=t[r].len();va&&this.data instanceof Float32Array&&(this.data=new Float32Array(i+n));for(r=0;r<e;r++)for(var o=t[r].data,a=0;a<o.length;a++)this.data[i++]=o[a];this._len=i},t.prototype.addData=function(t,e,n,i,r,o,a,s,l){if(this._saveData){var u=this.data;this._len+arguments.length>u.length&&(this._expandData(),u=this.data);for(var h=0;h<arguments.length;h++)u[this._len++]=arguments[h]}},t.prototype._drawPendingPt=function(){this._pendingPtDist>0&&(this._ctx&&this._ctx.lineTo(this._pendingPtX,this._pendingPtY),this._pendingPtDist=0)},t.prototype._expandData=function(){if(!(this.data instanceof Array)){for(var t=[],e=0;e<this._len;e++)t[e]=this.data[e];this.data=t}},t.prototype.toStatic=function(){if(this._saveData){this._drawPendingPt();var t=this.data;t instanceof Array&&(t.length=this._len,va&&this._len>11&&(this.data=new Float32Array(t)))}},t.prototype.getBoundingRect=function(){aa[0]=aa[1]=la[0]=la[1]=Number.MAX_VALUE,sa[0]=sa[1]=ua[0]=ua[1]=-Number.MAX_VALUE;var t,e=this.data,n=0,i=0,r=0,o=0;for(t=0;t<this._len;){var a=e[t++],s=1===t;switch(s&&(r=n=e[t],o=i=e[t+1]),a){case ia.M:n=r=e[t++],i=o=e[t++],la[0]=r,la[1]=o,ua[0]=r,ua[1]=o;break;case ia.L:$o(n,i,e[t],e[t+1],la,ua),n=e[t++],i=e[t++];break;case ia.C:ta(n,i,e[t++],e[t++],e[t++],e[t++],e[t],e[t+1],la,ua),n=e[t++],i=e[t++];break;case ia.Q:ea(n,i,e[t++],e[t++],e[t],e[t+1],la,ua),n=e[t++],i=e[t++];break;case ia.A:var l=e[t++],u=e[t++],h=e[t++],c=e[t++],p=e[t++],f=e[t++]+p;t+=1;var d=!e[t++];s&&(r=pa(p)*h+l,o=fa(p)*c+u),na(l,u,h,c,p,f,d,la,ua),n=pa(f)*h+l,i=fa(f)*c+u;break;case ia.R:$o(r=n=e[t++],o=i=e[t++],r+e[t++],o+e[t++],la,ua);break;case ia.Z:n=r,i=o}zt(aa,aa,la),Bt(sa,sa,ua)}return 0===t&&(aa[0]=aa[1]=sa[0]=sa[1]=0),new Ce(aa[0],aa[1],sa[0]-aa[0],sa[1]-aa[1])},t.prototype._calculateLength=function(){var t=this.data,e=this._len,n=this._ux,i=this._uy,r=0,o=0,a=0,s=0;this._pathSegLen||(this._pathSegLen=[]);for(var l=this._pathSegLen,u=0,h=0,c=0;c<e;){var p=t[c++],f=1===c;f&&(a=r=t[c],s=o=t[c+1]);var d=-1;switch(p){case ia.M:r=a=t[c++],o=s=t[c++];break;case ia.L:var g=t[c++],y=(_=t[c++])-o;(da(k=g-r)>n||da(y)>i||c===e-1)&&(d=Math.sqrt(k*k+y*y),r=g,o=_);break;case ia.C:var v=t[c++],m=t[c++],_=(g=t[c++],t[c++]),x=t[c++],w=t[c++];d=pn(r,o,v,m,g,_,x,w,10),r=x,o=w;break;case ia.Q:d=vn(r,o,v=t[c++],m=t[c++],g=t[c++],_=t[c++],10),r=g,o=_;break;case ia.A:var b=t[c++],S=t[c++],M=t[c++],T=t[c++],C=t[c++],D=t[c++],I=D+C;c+=1,f&&(a=pa(C)*M+b,s=fa(C)*T+S),d=ca(M,T)*ha(ya,Math.abs(D)),r=pa(I)*M+b,o=fa(I)*T+S;break;case ia.R:a=r=t[c++],s=o=t[c++],d=2*t[c++]+2*t[c++];break;case ia.Z:var k=a-r;y=s-o;d=Math.sqrt(k*k+y*y),r=a,o=s}d>=0&&(l[h++]=d,u+=d)}return this._pathLen=u,u},t.prototype.rebuildPath=function(t,e){var n,i,r,o,a,s,l,u,h,c,p=this.data,f=this._ux,d=this._uy,g=this._len,y=e<1,v=0,m=0,_=0;if(!y||(this._pathSegLen||this._calculateLength(),l=this._pathSegLen,u=e*this._pathLen))t:for(var x=0;x<g;){var w=p[x++],b=1===x;switch(b&&(n=r=p[x],i=o=p[x+1]),w!==ia.L&&_>0&&(t.lineTo(h,c),_=0),w){case ia.M:n=r=p[x++],i=o=p[x++],t.moveTo(r,o);break;case ia.L:a=p[x++],s=p[x++];var S=da(a-r),M=da(s-o);if(S>f||M>d){if(y){if(v+(Z=l[m++])>u){var T=(u-v)/Z;t.lineTo(r*(1-T)+a*T,o*(1-T)+s*T);break t}v+=Z}t.lineTo(a,s),r=a,o=s,_=0}else{var C=S*S+M*M;C>_&&(h=a,c=s,_=C)}break;case ia.C:var D=p[x++],I=p[x++],k=p[x++],A=p[x++],L=p[x++],P=p[x++];if(y){if(v+(Z=l[m++])>u){cn(r,D,k,L,T=(u-v)/Z,ra),cn(o,I,A,P,T,oa),t.bezierCurveTo(ra[1],oa[1],ra[2],oa[2],ra[3],oa[3]);break t}v+=Z}t.bezierCurveTo(D,I,k,A,L,P),r=L,o=P;break;case ia.Q:D=p[x++],I=p[x++],k=p[x++],A=p[x++];if(y){if(v+(Z=l[m++])>u){yn(r,D,k,T=(u-v)/Z,ra),yn(o,I,A,T,oa),t.quadraticCurveTo(ra[1],oa[1],ra[2],oa[2]);break t}v+=Z}t.quadraticCurveTo(D,I,k,A),r=k,o=A;break;case ia.A:var O=p[x++],R=p[x++],N=p[x++],E=p[x++],z=p[x++],B=p[x++],F=p[x++],V=!p[x++],H=N>E?N:E,W=da(N-E)>.001,G=z+B,U=!1;if(y)v+(Z=l[m++])>u&&(G=z+B*(u-v)/Z,U=!0),v+=Z;if(W&&t.ellipse?t.ellipse(O,R,N,E,F,z,G,V):t.arc(O,R,H,z,G,V),U)break t;b&&(n=pa(z)*N+O,i=fa(z)*E+R),r=pa(G)*N+O,o=fa(G)*E+R;break;case ia.R:n=r=p[x],i=o=p[x+1],a=p[x++],s=p[x++];var X=p[x++],Y=p[x++];if(y){if(v+(Z=l[m++])>u){var q=u-v;t.moveTo(a,s),t.lineTo(a+ha(q,X),s),(q-=X)>0&&t.lineTo(a+X,s+ha(q,Y)),(q-=Y)>0&&t.lineTo(a+ca(X-q,0),s+Y),(q-=X)>0&&t.lineTo(a,s+ca(Y-q,0));break t}v+=Z}t.rect(a,s,X,Y);break;case ia.Z:if(y){var Z;if(v+(Z=l[m++])>u){T=(u-v)/Z;t.lineTo(r*(1-T)+n*T,o*(1-T)+i*T);break t}v+=Z}t.closePath(),r=n,o=i}}},t.prototype.clone=function(){var e=new t,n=this.data;return e.data=n.slice?n.slice():Array.prototype.slice.call(n),e._len=this._len,e},t.CMD=ia,t.initDefaultProps=function(){var e=t.prototype;e._saveData=!0,e._ux=0,e._uy=0,e._pendingPtDist=0,e._version=0}(),t}();function ba(t,e,n,i,r,o,a){if(0===r)return!1;var s=r,l=0;if(a>e+s&&a>i+s||a<e-s&&a<i-s||o>t+s&&o>n+s||o<t-s&&o<n-s)return!1;if(t===n)return Math.abs(o-t)<=s/2;var u=(l=(e-i)/(t-n))*o-a+(t*i-n*e)/(t-n);return u*u/(l*l+1)<=s/2*s/2}function Sa(t,e,n,i,r,o,a,s,l,u,h){if(0===l)return!1;var c=l;if(h>e+c&&h>i+c&&h>o+c&&h>s+c||h<e-c&&h<i-c&&h<o-c&&h<s-c||u>t+c&&u>n+c&&u>r+c&&u>a+c||u<t-c&&u<n-c&&u<r-c&&u<a-c)return!1;var p=function(t,e,n,i,r,o,a,s,l,u,h){var c,p,f,d,g,y=.005,v=1/0;en[0]=l,en[1]=u;for(var m=0;m<1;m+=.05)nn[0]=sn(t,n,r,a,m),nn[1]=sn(e,i,o,s,m),(d=Rt(en,nn))<v&&(c=m,v=d);v=1/0;for(var _=0;_<32&&!(y<Qe);_++)p=c-y,f=c+y,nn[0]=sn(t,n,r,a,p),nn[1]=sn(e,i,o,s,p),d=Rt(nn,en),p>=0&&d<v?(c=p,v=d):(rn[0]=sn(t,n,r,a,f),rn[1]=sn(e,i,o,s,f),g=Rt(rn,en),f<=1&&g<v?(c=f,v=g):y*=.5);return h&&(h[0]=sn(t,n,r,a,c),h[1]=sn(e,i,o,s,c)),Ke(v)}(t,e,n,i,r,o,a,s,u,h,null);return p<=c/2}function Ma(t,e,n,i,r,o,a,s,l){if(0===a)return!1;var u=a;if(l>e+u&&l>i+u&&l>o+u||l<e-u&&l<i-u&&l<o-u||s>t+u&&s>n+u&&s>r+u||s<t-u&&s<n-u&&s<r-u)return!1;var h=function(t,e,n,i,r,o,a,s,l){var u,h=.005,c=1/0;en[0]=a,en[1]=s;for(var p=0;p<1;p+=.05)nn[0]=fn(t,n,r,p),nn[1]=fn(e,i,o,p),(y=Rt(en,nn))<c&&(u=p,c=y);c=1/0;for(var f=0;f<32&&!(h<Qe);f++){var d=u-h,g=u+h;nn[0]=fn(t,n,r,d),nn[1]=fn(e,i,o,d);var y=Rt(nn,en);if(d>=0&&y<c)u=d,c=y;else{rn[0]=fn(t,n,r,g),rn[1]=fn(e,i,o,g);var v=Rt(rn,en);g<=1&&v<c?(u=g,c=v):h*=.5}}return l&&(l[0]=fn(t,n,r,u),l[1]=fn(e,i,o,u)),Ke(c)}(t,e,n,i,r,o,s,l,null);return h<=u/2}var Ta=2*Math.PI;function Ca(t){return(t%=Ta)<0&&(t+=Ta),t}var Da=2*Math.PI;function Ia(t,e,n,i,r,o,a,s,l){if(0===a)return!1;var u=a;s-=t,l-=e;var h=Math.sqrt(s*s+l*l);if(h-u>n||h+u<n)return!1;if(Math.abs(i-r)%Da<1e-4)return!0;if(o){var c=i;i=Ca(r),r=Ca(c)}else i=Ca(i),r=Ca(r);i>r&&(r+=Da);var p=Math.atan2(l,s);return p<0&&(p+=Da),p>=i&&p<=r||p+Da>=i&&p+Da<=r}function ka(t,e,n,i,r,o){if(o>e&&o>i||o<e&&o<i)return 0;if(i===e)return 0;var a=(o-e)/(i-e),s=i<e?1:-1;1!==a&&0!==a||(s=i<e?.5:-.5);var l=a*(n-t)+t;return l===r?1/0:l>r?s:0}var Aa=wa.CMD,La=2*Math.PI;var Pa=[-1,-1,-1],Oa=[-1,-1];function Ra(t,e,n,i,r,o,a,s,l,u){if(u>e&&u>i&&u>o&&u>s||u<e&&u<i&&u<o&&u<s)return 0;var h,c=un(e,i,o,s,u,Pa);if(0===c)return 0;for(var p=0,f=-1,d=void 0,g=void 0,y=0;y<c;y++){var v=Pa[y],m=0===v||1===v?.5:1;sn(t,n,r,a,v)<l||(f<0&&(f=hn(e,i,o,s,Oa),Oa[1]<Oa[0]&&f>1&&(h=void 0,h=Oa[0],Oa[0]=Oa[1],Oa[1]=h),d=sn(e,i,o,s,Oa[0]),f>1&&(g=sn(e,i,o,s,Oa[1]))),2===f?v<Oa[0]?p+=d<e?m:-m:v<Oa[1]?p+=g<d?m:-m:p+=s<g?m:-m:v<Oa[0]?p+=d<e?m:-m:p+=s<d?m:-m)}return p}function Na(t,e,n,i,r,o,a,s){if(s>e&&s>i&&s>o||s<e&&s<i&&s<o)return 0;var l=function(t,e,n,i,r){var o=t-2*e+n,a=2*(e-t),s=t-i,l=0;if(on(o))an(a)&&(h=-s/a)>=0&&h<=1&&(r[l++]=h);else{var u=a*a-4*o*s;if(on(u))(h=-a/(2*o))>=0&&h<=1&&(r[l++]=h);else if(u>0){var h,c=Ke(u),p=(-a-c)/(2*o);(h=(-a+c)/(2*o))>=0&&h<=1&&(r[l++]=h),p>=0&&p<=1&&(r[l++]=p)}}return l}(e,i,o,s,Pa);if(0===l)return 0;var u=gn(e,i,o);if(u>=0&&u<=1){for(var h=0,c=fn(e,i,o,u),p=0;p<l;p++){var f=0===Pa[p]||1===Pa[p]?.5:1;fn(t,n,r,Pa[p])<a||(Pa[p]<u?h+=c<e?f:-f:h+=o<c?f:-f)}return h}f=0===Pa[0]||1===Pa[0]?.5:1;return fn(t,n,r,Pa[0])<a?0:o<e?f:-f}function Ea(t,e,n,i,r,o,a,s){if((s-=e)>n||s<-n)return 0;var l=Math.sqrt(n*n-s*s);Pa[0]=-l,Pa[1]=l;var u=Math.abs(i-r);if(u<1e-4)return 0;if(u>=La-1e-4){i=0,r=La;var h=o?1:-1;return a>=Pa[0]+t&&a<=Pa[1]+t?h:0}if(i>r){var c=i;i=r,r=c}i<0&&(i+=La,r+=La);for(var p=0,f=0;f<2;f++){var d=Pa[f];if(d+t>a){var g=Math.atan2(s,d);h=o?1:-1;g<0&&(g=La+g),(g>=i&&g<=r||g+La>=i&&g+La<=r)&&(g>Math.PI/2&&g<1.5*Math.PI&&(h=-h),p+=h)}}return p}function za(t,e,n,i,r){for(var o,a,s,l,u=t.data,h=t.len(),c=0,p=0,f=0,d=0,g=0,y=0;y<h;){var v=u[y++],m=1===y;switch(v===Aa.M&&y>1&&(n||(c+=ka(p,f,d,g,i,r))),m&&(d=p=u[y],g=f=u[y+1]),v){case Aa.M:p=d=u[y++],f=g=u[y++];break;case Aa.L:if(n){if(ba(p,f,u[y],u[y+1],e,i,r))return!0}else c+=ka(p,f,u[y],u[y+1],i,r)||0;p=u[y++],f=u[y++];break;case Aa.C:if(n){if(Sa(p,f,u[y++],u[y++],u[y++],u[y++],u[y],u[y+1],e,i,r))return!0}else c+=Ra(p,f,u[y++],u[y++],u[y++],u[y++],u[y],u[y+1],i,r)||0;p=u[y++],f=u[y++];break;case Aa.Q:if(n){if(Ma(p,f,u[y++],u[y++],u[y],u[y+1],e,i,r))return!0}else c+=Na(p,f,u[y++],u[y++],u[y],u[y+1],i,r)||0;p=u[y++],f=u[y++];break;case Aa.A:var _=u[y++],x=u[y++],w=u[y++],b=u[y++],S=u[y++],M=u[y++];y+=1;var T=!!(1-u[y++]);o=Math.cos(S)*w+_,a=Math.sin(S)*b+x,m?(d=o,g=a):c+=ka(p,f,o,a,i,r);var C=(i-_)*b/w+_;if(n){if(Ia(_,x,b,S,S+M,T,e,C,r))return!0}else c+=Ea(_,x,b,S,S+M,T,C,r);p=Math.cos(S+M)*w+_,f=Math.sin(S+M)*b+x;break;case Aa.R:if(d=p=u[y++],g=f=u[y++],o=d+u[y++],a=g+u[y++],n){if(ba(d,g,o,g,e,i,r)||ba(o,g,o,a,e,i,r)||ba(o,a,d,a,e,i,r)||ba(d,a,d,g,e,i,r))return!0}else c+=ka(o,g,o,a,i,r),c+=ka(d,a,d,g,i,r);break;case Aa.Z:if(n){if(ba(p,f,d,g,e,i,r))return!0}else c+=ka(p,f,d,g,i,r);p=d,f=g}}return n||(s=f,l=g,Math.abs(s-l)<1e-4)||(c+=ka(p,f,d,g,i,r)||0),0!==c}var Ba=I({fill:"#000",stroke:null,strokePercent:1,fillOpacity:1,strokeOpacity:1,lineDashOffset:0,lineWidth:1,lineCap:"butt",miterLimit:10,strokeNoScale:!1,strokeFirst:!1},Eo),Fa={style:I({fill:!0,stroke:!0,strokePercent:!0,fillOpacity:!0,strokeOpacity:!0,lineDashOffset:!0,lineWidth:!0,miterLimit:!0},zo.style)},Va=Hi.concat(["invisible","culling","z","z2","zlevel","parent"]),Ha=function(t){function e(e){return t.call(this,e)||this}var i;return n(e,t),e.prototype.update=function(){var n=this;t.prototype.update.call(this);var i=this.style;if(i.decal){var r=this._decalEl=this._decalEl||new e;r.buildPath===e.prototype.buildPath&&(r.buildPath=function(t){n.buildPath(t,n.shape)}),r.silent=!0;var o=r.style;for(var a in i)o[a]!==i[a]&&(o[a]=i[a]);o.fill=i.fill?i.decal:null,o.decal=null,o.shadowColor=null,i.strokeFirst&&(o.stroke=null);for(var s=0;s<Va.length;++s)r[Va[s]]=this[Va[s]];r.__dirty|=1}else this._decalEl&&(this._decalEl=null)},e.prototype.getDecalElement=function(){return this._decalEl},e.prototype._init=function(e){var n=B(e);this.shape=this.getDefaultShape();var i=this.getDefaultStyle();i&&this.useStyle(i);for(var r=0;r<n.length;r++){var o=n[r],a=e[o];"style"===o?this.style?D(this.style,a):this.useStyle(a):"shape"===o?D(this.shape,a):t.prototype.attrKV.call(this,o,a)}this.style||this.useStyle({})},e.prototype.getDefaultStyle=function(){return null},e.prototype.getDefaultShape=function(){return{}},e.prototype.canBeInsideText=function(){return this.hasFill()},e.prototype.getInsideTextFill=function(){var t=this.style.fill;if("none"!==t){if(G(t)){var e=Un(t,0);return e>.5?Li:e>.2?"#eee":Pi}if(t)return Pi}return Li},e.prototype.getInsideTextStroke=function(t){var e=this.style.fill;if(G(e)){var n=this.__zr;if(!(!n||!n.isDarkMode())===Un(t,0)<.4)return e}},e.prototype.buildPath=function(t,e,n){},e.prototype.pathUpdated=function(){this.__dirty&=-5},e.prototype.getUpdatedPathProxy=function(t){return!this.path&&this.createPathProxy(),this.path.beginPath(),this.buildPath(this.path,this.shape,t),this.path},e.prototype.createPathProxy=function(){this.path=new wa(!1)},e.prototype.hasStroke=function(){var t=this.style,e=t.stroke;return!(null==e||"none"===e||!(t.lineWidth>0))},e.prototype.hasFill=function(){var t=this.style.fill;return null!=t&&"none"!==t},e.prototype.getBoundingRect=function(){var t=this._rect,e=this.style,n=!t;if(n){var i=!1;this.path||(i=!0,this.createPathProxy());var r=this.path;(i||4&this.__dirty)&&(r.beginPath(),this.buildPath(r,this.shape,!1),this.pathUpdated()),t=r.getBoundingRect()}if(this._rect=t,this.hasStroke()&&this.path&&this.path.len()>0){var o=this._rectStroke||(this._rectStroke=t.clone());if(this.__dirty||n){o.copy(t);var a=e.strokeNoScale?this.getLineScale():1,s=e.lineWidth;if(!this.hasFill()){var l=this.strokeContainThreshold;s=Math.max(s,null==l?4:l)}a>1e-10&&(o.width+=s/a,o.height+=s/a,o.x-=s/a/2,o.y-=s/a/2)}return o}return t},e.prototype.contain=function(t,e){var n=this.transformCoordToLocal(t,e),i=this.getBoundingRect(),r=this.style;if(t=n[0],e=n[1],i.contain(t,e)){var o=this.path;if(this.hasStroke()){var a=r.lineWidth,s=r.strokeNoScale?this.getLineScale():1;if(s>1e-10&&(this.hasFill()||(a=Math.max(a,this.strokeContainThreshold)),function(t,e,n,i){return za(t,e,!0,n,i)}(o,a/s,t,e)))return!0}if(this.hasFill())return function(t,e,n){return za(t,0,!1,e,n)}(o,t,e)}return!1},e.prototype.dirtyShape=function(){this.__dirty|=4,this._rect&&(this._rect=null),this._decalEl&&this._decalEl.dirtyShape(),this.markRedraw()},e.prototype.dirty=function(){this.dirtyStyle(),this.dirtyShape()},e.prototype.animateShape=function(t){return this.animate("shape",t)},e.prototype.updateDuringAnimation=function(t){"style"===t?this.dirtyStyle():"shape"===t?this.dirtyShape():this.markRedraw()},e.prototype.attrKV=function(e,n){"shape"===e?this.setShape(n):t.prototype.attrKV.call(this,e,n)},e.prototype.setShape=function(t,e){var n=this.shape;return n||(n=this.shape={}),"string"==typeof t?n[t]=e:D(n,t),this.dirtyShape(),this},e.prototype.shapeChanged=function(){return!!(4&this.__dirty)},e.prototype.createStyle=function(t){return gt(Ba,t)},e.prototype._innerSaveToNormal=function(e){t.prototype._innerSaveToNormal.call(this,e);var n=this._normalState;e.shape&&!n.shape&&(n.shape=D({},this.shape))},e.prototype._applyStateObj=function(e,n,i,r,o,a){t.prototype._applyStateObj.call(this,e,n,i,r,o,a);var s,l=!(n&&r);if(n&&n.shape?o?r?s=n.shape:(s=D({},i.shape),D(s,n.shape)):(s=D({},r?this.shape:i.shape),D(s,n.shape)):l&&(s=i.shape),s)if(o){this.shape=D({},this.shape);for(var u={},h=B(s),c=0;c<h.length;c++){var p=h[c];"object"==typeof s[p]?this.shape[p]=s[p]:u[p]=s[p]}this._transitionState(e,{shape:u},a)}else this.shape=s,this.dirtyShape()},e.prototype._mergeStates=function(e){for(var n,i=t.prototype._mergeStates.call(this,e),r=0;r<e.length;r++){var o=e[r];o.shape&&(n=n||{},this._mergeStyle(n,o.shape))}return n&&(i.shape=n),i},e.prototype.getAnimationStyleProps=function(){return Fa},e.prototype.isZeroArea=function(){return!1},e.extend=function(t){var i=function(e){function i(n){var i=e.call(this,n)||this;return t.init&&t.init.call(i,n),i}return n(i,e),i.prototype.getDefaultStyle=function(){return T(t.style)},i.prototype.getDefaultShape=function(){return T(t.shape)},i}(e);for(var r in t)"function"==typeof t[r]&&(i.prototype[r]=t[r]);return i},e.initDefaultProps=((i=e.prototype).type="path",i.strokeContainThreshold=5,i.segmentIgnoreThreshold=0,i.subPixelOptimize=!1,i.autoBatch=!1,void(i.__dirty=7)),e}(Vo),Wa=I({strokeFirst:!0,font:o,x:0,y:0,textAlign:"left",textBaseline:"top",miterLimit:2},Ba),Ga=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.hasStroke=function(){var t=this.style,e=t.stroke;return null!=e&&"none"!==e&&t.lineWidth>0},e.prototype.hasFill=function(){var t=this.style.fill;return null!=t&&"none"!==t},e.prototype.createStyle=function(t){return gt(Wa,t)},e.prototype.setBoundingRect=function(t){this._rect=t},e.prototype.getBoundingRect=function(){var t=this.style;if(!this._rect){var e=t.text;null!=e?e+="":e="";var n=Xi(e,t.font,t.textAlign,t.textBaseline);if(n.x+=t.x||0,n.y+=t.y||0,this.hasStroke()){var i=t.lineWidth;n.x-=i/2,n.y-=i/2,n.width+=i,n.height+=i}this._rect=n}return this._rect},e.initDefaultProps=void(e.prototype.dirtyRectTolerance=10),e}(Vo);Ga.prototype.type="tspan";var Ua=I({x:0,y:0},Eo),Xa={style:I({x:!0,y:!0,width:!0,height:!0,sx:!0,sy:!0,sWidth:!0,sHeight:!0},zo.style)};var Ya=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.createStyle=function(t){return gt(Ua,t)},e.prototype._getSize=function(t){var e=this.style,n=e[t];if(null!=n)return n;var i,r=(i=e.image)&&"string"!=typeof i&&i.width&&i.height?e.image:this.__image;if(!r)return 0;var o="width"===t?"height":"width",a=e[o];return null==a?r[t]:r[t]/r[o]*a},e.prototype.getWidth=function(){return this._getSize("width")},e.prototype.getHeight=function(){return this._getSize("height")},e.prototype.getAnimationStyleProps=function(){return Xa},e.prototype.getBoundingRect=function(){var t=this.style;return this._rect||(this._rect=new Ce(t.x||0,t.y||0,this.getWidth(),this.getHeight())),this._rect},e}(Vo);Ya.prototype.type="image";var qa=Math.round;function Za(t,e,n){if(e){var i=e.x1,r=e.x2,o=e.y1,a=e.y2;t.x1=i,t.x2=r,t.y1=o,t.y2=a;var s=n&&n.lineWidth;return s?(qa(2*i)===qa(2*r)&&(t.x1=t.x2=ja(i,s,!0)),qa(2*o)===qa(2*a)&&(t.y1=t.y2=ja(o,s,!0)),t):t}}function ja(t,e,n){if(!e)return t;var i=qa(2*t);return(i+qa(e))%2==0?i/2:(i+(n?1:-1))/2}var Ka=function(){this.x=0,this.y=0,this.width=0,this.height=0},$a={},Qa=function(t){function e(e){return t.call(this,e)||this}return n(e,t),e.prototype.getDefaultShape=function(){return new Ka},e.prototype.buildPath=function(t,e){var n,i,r,o;if(this.subPixelOptimize){var a=function(t,e,n){if(e){var i=e.x,r=e.y,o=e.width,a=e.height;t.x=i,t.y=r,t.width=o,t.height=a;var s=n&&n.lineWidth;return s?(t.x=ja(i,s,!0),t.y=ja(r,s,!0),t.width=Math.max(ja(i+o,s,!1)-t.x,0===o?0:1),t.height=Math.max(ja(r+a,s,!1)-t.y,0===a?0:1),t):t}}($a,e,this.style);n=a.x,i=a.y,r=a.width,o=a.height,a.r=e.r,e=a}else n=e.x,i=e.y,r=e.width,o=e.height;e.r?function(t,e){var n,i,r,o,a,s=e.x,l=e.y,u=e.width,h=e.height,c=e.r;u<0&&(s+=u,u=-u),h<0&&(l+=h,h=-h),"number"==typeof c?n=i=r=o=c:c instanceof Array?1===c.length?n=i=r=o=c[0]:2===c.length?(n=r=c[0],i=o=c[1]):3===c.length?(n=c[0],i=o=c[1],r=c[2]):(n=c[0],i=c[1],r=c[2],o=c[3]):n=i=r=o=0,n+i>u&&(n*=u/(a=n+i),i*=u/a),r+o>u&&(r*=u/(a=r+o),o*=u/a),i+r>h&&(i*=h/(a=i+r),r*=h/a),n+o>h&&(n*=h/(a=n+o),o*=h/a),t.moveTo(s+n,l),t.lineTo(s+u-i,l),0!==i&&t.arc(s+u-i,l+i,i,-Math.PI/2,0),t.lineTo(s+u,l+h-r),0!==r&&t.arc(s+u-r,l+h-r,r,0,Math.PI/2),t.lineTo(s+o,l+h),0!==o&&t.arc(s+o,l+h-o,o,Math.PI/2,Math.PI),t.lineTo(s,l+n),0!==n&&t.arc(s+n,l+n,n,Math.PI,1.5*Math.PI)}(t,e):t.rect(n,i,r,o)},e.prototype.isZeroArea=function(){return!this.shape.width||!this.shape.height},e}(Ha);Qa.prototype.type="rect";var Ja={fill:"#000"},ts={style:I({fill:!0,stroke:!0,fillOpacity:!0,strokeOpacity:!0,lineWidth:!0,fontSize:!0,lineHeight:!0,width:!0,height:!0,textShadowColor:!0,textShadowBlur:!0,textShadowOffsetX:!0,textShadowOffsetY:!0,backgroundColor:!0,padding:!0,borderColor:!0,borderWidth:!0,borderRadius:!0},zo.style)},es=function(t){function e(e){var n=t.call(this)||this;return n.type="text",n._children=[],n._defaultStyle=Ja,n.attr(e),n}return n(e,t),e.prototype.childrenRef=function(){return this._children},e.prototype.update=function(){t.prototype.update.call(this),this.styleChanged()&&this._updateSubTexts();for(var e=0;e<this._children.length;e++){var n=this._children[e];n.zlevel=this.zlevel,n.z=this.z,n.z2=this.z2,n.culling=this.culling,n.cursor=this.cursor,n.invisible=this.invisible}},e.prototype.updateTransform=function(){var e=this.innerTransformable;e?(e.updateTransform(),e.transform&&(this.transform=e.transform)):t.prototype.updateTransform.call(this)},e.prototype.getLocalTransform=function(e){var n=this.innerTransformable;return n?n.getLocalTransform(e):t.prototype.getLocalTransform.call(this,e)},e.prototype.getComputedTransform=function(){return this.__hostTarget&&(this.__hostTarget.getComputedTransform(),this.__hostTarget.updateInnerText(!0)),t.prototype.getComputedTransform.call(this)},e.prototype._updateSubTexts=function(){var t;this._childCursor=0,ss(t=this.style),R(t.rich,ss),this.style.rich?this._updateRichTexts():this._updatePlainTexts(),this._children.length=this._childCursor,this.styleUpdated()},e.prototype.addSelfToZr=function(e){t.prototype.addSelfToZr.call(this,e);for(var n=0;n<this._children.length;n++)this._children[n].__zr=e},e.prototype.removeSelfFromZr=function(e){t.prototype.removeSelfFromZr.call(this,e);for(var n=0;n<this._children.length;n++)this._children[n].__zr=null},e.prototype.getBoundingRect=function(){if(this.styleChanged()&&this._updateSubTexts(),!this._rect){for(var t=new Ce(0,0,0,0),e=this._children,n=[],i=null,r=0;r<e.length;r++){var o=e[r],a=o.getBoundingRect(),s=o.getLocalTransform(n);s?(t.copy(a),t.applyTransform(s),(i=i||t.clone()).union(t)):(i=i||a.clone()).union(a)}this._rect=i||t}return this._rect},e.prototype.setDefaultTextStyle=function(t){this._defaultStyle=t||Ja},e.prototype.setTextContent=function(t){0},e.prototype._mergeStyle=function(t,e){if(!e)return t;var n=e.rich,i=t.rich||n&&{};return D(t,e),n&&i?(this._mergeRich(i,n),t.rich=i):i&&(t.rich=i),t},e.prototype._mergeRich=function(t,e){for(var n=B(e),i=0;i<n.length;i++){var r=n[i];t[r]=t[r]||{},D(t[r],e[r])}},e.prototype.getAnimationStyleProps=function(){return ts},e.prototype._getOrCreateChild=function(t){var e=this._children[this._childCursor];return e&&e instanceof t||(e=new t),this._children[this._childCursor++]=e,e.__zr=this.__zr,e.parent=this,e},e.prototype._updatePlainTexts=function(){var t=this.style,e=t.font||o,n=t.padding,i=function(t,e){null!=t&&(t+="");var n,i=e.overflow,r=e.padding,o=e.font,a="truncate"===i,s=Zi(o),l=et(e.lineHeight,s),u=!!e.backgroundColor,h="truncate"===e.lineOverflow,c=!1,p=e.width,f=(n=null==p||"break"!==i&&"breakAll"!==i?t?t.split("\n"):[]:t?Ro(t,e.font,p,"breakAll"===i,0).lines:[]).length*l,d=et(e.height,f);if(f>d&&h){var g=Math.floor(d/l);c=c||n.length>g,n=n.slice(0,g)}if(t&&a&&null!=p)for(var y=To(p,o,e.ellipsis,{minChar:e.truncateMinChar,placeholder:e.placeholder}),v={},m=0;m<n.length;m++)Co(v,n[m],y),n[m]=v.textLine,c=c||v.isTruncated;var _=d,x=0;for(m=0;m<n.length;m++)x=Math.max(Gi(n[m],o),x);null==p&&(p=x);var w=x;return r&&(_+=r[0]+r[2],w+=r[1]+r[3],p+=r[1]+r[3]),u&&(w=p),{lines:n,height:d,outerWidth:w,outerHeight:_,lineHeight:l,calculatedLineHeight:s,contentWidth:x,contentHeight:f,width:p,isTruncated:c}}(cs(t),t),r=ps(t),a=!!t.backgroundColor,s=i.outerHeight,l=i.outerWidth,u=i.contentWidth,h=i.lines,c=i.lineHeight,p=this._defaultStyle;this.isTruncated=!!i.isTruncated;var f=t.x||0,d=t.y||0,g=t.align||p.align||"left",y=t.verticalAlign||p.verticalAlign||"top",v=f,m=qi(d,i.contentHeight,y);if(r||n){var _=Yi(f,l,g),x=qi(d,s,y);r&&this._renderBackground(t,t,_,x,l,s)}m+=c/2,n&&(v=hs(f,g,n),"top"===y?m+=n[0]:"bottom"===y&&(m-=n[2]));for(var w=0,b=!1,S=(us("fill"in t?t.fill:(b=!0,p.fill))),M=(ls("stroke"in t?t.stroke:a||p.autoStroke&&!b?null:(w=2,p.stroke))),T=t.textShadowBlur>0,C=null!=t.width&&("truncate"===t.overflow||"break"===t.overflow||"breakAll"===t.overflow),D=i.calculatedLineHeight,I=0;I<h.length;I++){var k=this._getOrCreateChild(Ga),A=k.createStyle();k.useStyle(A),A.text=h[I],A.x=v,A.y=m,g&&(A.textAlign=g),A.textBaseline="middle",A.opacity=t.opacity,A.strokeFirst=!0,T&&(A.shadowBlur=t.textShadowBlur||0,A.shadowColor=t.textShadowColor||"transparent",A.shadowOffsetX=t.textShadowOffsetX||0,A.shadowOffsetY=t.textShadowOffsetY||0),A.stroke=M,A.fill=S,M&&(A.lineWidth=t.lineWidth||w,A.lineDash=t.lineDash,A.lineDashOffset=t.lineDashOffset||0),A.font=e,as(A,t),m+=c,C&&k.setBoundingRect(new Ce(Yi(A.x,u,A.textAlign),qi(A.y,D,A.textBaseline),u,D))}},e.prototype._updateRichTexts=function(){var t=this.style,e=function(t,e){var n=new Ao;if(null!=t&&(t+=""),!t)return n;for(var i,r=e.width,o=e.height,a=e.overflow,s="break"!==a&&"breakAll"!==a||null==r?null:{width:r,accumWidth:0,breakAll:"breakAll"===a},l=So.lastIndex=0;null!=(i=So.exec(t));){var u=i.index;u>l&&Lo(n,t.substring(l,u),e,s),Lo(n,i[2],e,s,i[1]),l=So.lastIndex}l<t.length&&Lo(n,t.substring(l,t.length),e,s);var h=[],c=0,p=0,f=e.padding,d="truncate"===a,g="truncate"===e.lineOverflow,y={};function v(t,e,n){t.width=e,t.lineHeight=n,c+=n,p=Math.max(p,e)}t:for(var m=0;m<n.lines.length;m++){for(var _=n.lines[m],x=0,w=0,b=0;b<_.tokens.length;b++){var S=(R=_.tokens[b]).styleName&&e.rich[R.styleName]||{},M=R.textPadding=S.padding,T=M?M[1]+M[3]:0,C=R.font=S.font||e.font;R.contentHeight=Zi(C);var D=et(S.height,R.contentHeight);if(R.innerHeight=D,M&&(D+=M[0]+M[2]),R.height=D,R.lineHeight=nt(S.lineHeight,e.lineHeight,D),R.align=S&&S.align||e.align,R.verticalAlign=S&&S.verticalAlign||"middle",g&&null!=o&&c+R.lineHeight>o){var I=n.lines.length;b>0?(_.tokens=_.tokens.slice(0,b),v(_,w,x),n.lines=n.lines.slice(0,m+1)):n.lines=n.lines.slice(0,m),n.isTruncated=n.isTruncated||n.lines.length<I;break t}var k=S.width,A=null==k||"auto"===k;if("string"==typeof k&&"%"===k.charAt(k.length-1))R.percentWidth=k,h.push(R),R.contentWidth=Gi(R.text,C);else{if(A){var L=S.backgroundColor,P=L&&L.image;P&&bo(P=_o(P))&&(R.width=Math.max(R.width,P.width*D/P.height))}var O=d&&null!=r?r-w:null;null!=O&&O<R.width?!A||O<T?(R.text="",R.width=R.contentWidth=0):(Mo(y,R.text,O-T,C,e.ellipsis,{minChar:e.truncateMinChar}),R.text=y.text,n.isTruncated=n.isTruncated||y.isTruncated,R.width=R.contentWidth=Gi(R.text,C)):R.contentWidth=Gi(R.text,C)}R.width+=T,w+=R.width,S&&(x=Math.max(x,R.lineHeight))}v(_,w,x)}for(n.outerWidth=n.width=et(r,p),n.outerHeight=n.height=et(o,c),n.contentHeight=c,n.contentWidth=p,f&&(n.outerWidth+=f[1]+f[3],n.outerHeight+=f[0]+f[2]),m=0;m<h.length;m++){var R,N=(R=h[m]).percentWidth;R.width=parseInt(N,10)/100*n.width}return n}(cs(t),t),n=e.width,i=e.outerWidth,r=e.outerHeight,o=t.padding,a=t.x||0,s=t.y||0,l=this._defaultStyle,u=t.align||l.align,h=t.verticalAlign||l.verticalAlign;this.isTruncated=!!e.isTruncated;var c=Yi(a,i,u),p=qi(s,r,h),f=c,d=p;o&&(f+=o[3],d+=o[0]);var g=f+n;ps(t)&&this._renderBackground(t,t,c,p,i,r);for(var y=!!t.backgroundColor,v=0;v<e.lines.length;v++){for(var m=e.lines[v],_=m.tokens,x=_.length,w=m.lineHeight,b=m.width,S=0,M=f,T=g,C=x-1,D=void 0;S<x&&(!(D=_[S]).align||"left"===D.align);)this._placeToken(D,t,w,d,M,"left",y),b-=D.width,M+=D.width,S++;for(;C>=0&&"right"===(D=_[C]).align;)this._placeToken(D,t,w,d,T,"right",y),b-=D.width,T-=D.width,C--;for(M+=(n-(M-f)-(g-T)-b)/2;S<=C;)D=_[S],this._placeToken(D,t,w,d,M+D.width/2,"center",y),M+=D.width,S++;d+=w}},e.prototype._placeToken=function(t,e,n,i,r,a,s){var l=e.rich[t.styleName]||{};l.text=t.text;var u=t.verticalAlign,h=i+n/2;"top"===u?h=i+t.height/2:"bottom"===u&&(h=i+n-t.height/2),!t.isLineHolder&&ps(l)&&this._renderBackground(l,e,"right"===a?r-t.width:"center"===a?r-t.width/2:r,h-t.height/2,t.width,t.height);var c=!!l.backgroundColor,p=t.textPadding;p&&(r=hs(r,a,p),h-=t.height/2-p[0]-t.innerHeight/2);var f=this._getOrCreateChild(Ga),d=f.createStyle();f.useStyle(d);var g=this._defaultStyle,y=!1,v=0,m=us("fill"in l?l.fill:"fill"in e?e.fill:(y=!0,g.fill)),_=ls("stroke"in l?l.stroke:"stroke"in e?e.stroke:c||s||g.autoStroke&&!y?null:(v=2,g.stroke)),x=l.textShadowBlur>0||e.textShadowBlur>0;d.text=t.text,d.x=r,d.y=h,x&&(d.shadowBlur=l.textShadowBlur||e.textShadowBlur||0,d.shadowColor=l.textShadowColor||e.textShadowColor||"transparent",d.shadowOffsetX=l.textShadowOffsetX||e.textShadowOffsetX||0,d.shadowOffsetY=l.textShadowOffsetY||e.textShadowOffsetY||0),d.textAlign=a,d.textBaseline="middle",d.font=t.font||o,d.opacity=nt(l.opacity,e.opacity,1),as(d,l),_&&(d.lineWidth=nt(l.lineWidth,e.lineWidth,v),d.lineDash=et(l.lineDash,e.lineDash),d.lineDashOffset=e.lineDashOffset||0,d.stroke=_),m&&(d.fill=m);var w=t.contentWidth,b=t.contentHeight;f.setBoundingRect(new Ce(Yi(d.x,w,d.textAlign),qi(d.y,b,d.textBaseline),w,b))},e.prototype._renderBackground=function(t,e,n,i,r,o){var a,s,l,u=t.backgroundColor,h=t.borderWidth,c=t.borderColor,p=u&&u.image,f=u&&!p,d=t.borderRadius,g=this;if(f||t.lineHeight||h&&c){(a=this._getOrCreateChild(Qa)).useStyle(a.createStyle()),a.style.fill=null;var y=a.shape;y.x=n,y.y=i,y.width=r,y.height=o,y.r=d,a.dirtyShape()}if(f)(l=a.style).fill=u||null,l.fillOpacity=et(t.fillOpacity,1);else if(p){(s=this._getOrCreateChild(Ya)).onload=function(){g.dirtyStyle()};var v=s.style;v.image=u.image,v.x=n,v.y=i,v.width=r,v.height=o}h&&c&&((l=a.style).lineWidth=h,l.stroke=c,l.strokeOpacity=et(t.strokeOpacity,1),l.lineDash=t.borderDash,l.lineDashOffset=t.borderDashOffset||0,a.strokeContainThreshold=0,a.hasFill()&&a.hasStroke()&&(l.strokeFirst=!0,l.lineWidth*=2));var m=(a||s).style;m.shadowBlur=t.shadowBlur||0,m.shadowColor=t.shadowColor||"transparent",m.shadowOffsetX=t.shadowOffsetX||0,m.shadowOffsetY=t.shadowOffsetY||0,m.opacity=nt(t.opacity,e.opacity,1)},e.makeFont=function(t){var e="";return function(t){return null!=t.fontSize||t.fontFamily||t.fontWeight}(t)&&(e=[t.fontStyle,t.fontWeight,os(t.fontSize),t.fontFamily||"sans-serif"].join(" ")),e&&at(e)||t.textFont||t.font},e}(Vo),ns={left:!0,right:1,center:1},is={top:1,bottom:1,middle:1},rs=["fontStyle","fontWeight","fontSize","fontFamily"];function os(t){return"string"!=typeof t||-1===t.indexOf("px")&&-1===t.indexOf("rem")&&-1===t.indexOf("em")?isNaN(+t)?"12px":t+"px":t}function as(t,e){for(var n=0;n<rs.length;n++){var i=rs[n],r=e[i];null!=r&&(t[i]=r)}}function ss(t){if(t){t.font=es.makeFont(t);var e=t.align;"middle"===e&&(e="center"),t.align=null==e||ns[e]?e:"left";var n=t.verticalAlign;"center"===n&&(n="middle"),t.verticalAlign=null==n||is[n]?n:"top",t.padding&&(t.padding=rt(t.padding))}}function ls(t,e){return null==t||e<=0||"transparent"===t||"none"===t?null:t.image||t.colorStops?"#000":t}function us(t){return null==t||"none"===t?null:t.image||t.colorStops?"#000":t}function hs(t,e,n){return"right"===e?t-n[1]:"center"===e?t+n[3]/2-n[1]/2:t+n[3]}function cs(t){var e=t.text;return null!=e&&(e+=""),e}function ps(t){return!!(t.backgroundColor||t.lineHeight||t.borderWidth&&t.borderColor)}var fs=Qr(),ds=1,gs={},ys=Qr(),vs=Qr(),ms=["emphasis","blur","select"],_s=["normal","emphasis","blur","select"],xs="highlight",ws="downplay",bs="select",Ss="unselect",Ms="toggleSelect";function Ts(t){return null!=t&&"none"!==t}function Cs(t,e,n){t.onHoverStateChange&&(t.hoverState||0)!==n&&t.onHoverStateChange(e),t.hoverState=n}function Ds(t){Cs(t,"emphasis",2)}function Is(t){2===t.hoverState&&Cs(t,"normal",0)}function ks(t){Cs(t,"blur",1)}function As(t){1===t.hoverState&&Cs(t,"normal",0)}function Ls(t){t.selected=!0}function Ps(t){t.selected=!1}function Os(t,e,n){e(t,n)}function Rs(t,e,n){Os(t,e,n),t.isGroup&&t.traverse((function(t){Os(t,e,n)}))}function Ns(t,e){switch(e){case"emphasis":t.hoverState=2;break;case"normal":t.hoverState=0;break;case"blur":t.hoverState=1;break;case"select":t.selected=!0}}function Es(t,e){var n=this.states[t];if(this.style){if("emphasis"===t)return function(t,e,n,i){var r=n&&A(n,"select")>=0,o=!1;if(t instanceof Ha){var a=ys(t),s=r&&a.selectFill||a.normalFill,l=r&&a.selectStroke||a.normalStroke;if(Ts(s)||Ts(l)){var u=(i=i||{}).style||{};"inherit"===u.fill?(o=!0,i=D({},i),(u=D({},u)).fill=s):!Ts(u.fill)&&Ts(s)?(o=!0,i=D({},i),(u=D({},u)).fill=Yn(s)):!Ts(u.stroke)&&Ts(l)&&(o||(i=D({},i),u=D({},u)),u.stroke=Yn(l)),i.style=u}}if(i&&null==i.z2){o||(i=D({},i));var h=t.z2EmphasisLift;i.z2=t.z2+(null!=h?h:10)}return i}(this,0,e,n);if("blur"===t)return function(t,e,n){var i=A(t.currentStates,e)>=0,r=t.style.opacity,o=i?null:function(t,e,n,i){for(var r=t.style,o={},a=0;a<e.length;a++){var s=e[a],l=r[s];o[s]=null==l?i&&i[s]:l}for(a=0;a<t.animators.length;a++){var u=t.animators[a];u.__fromStateTransition&&u.__fromStateTransition.indexOf(n)<0&&"style"===u.targetName&&u.saveTo(o,e)}return o}(t,["opacity"],e,{opacity:1}),a=(n=n||{}).style||{};return null==a.opacity&&(n=D({},n),a=D({opacity:i?r:.1*o.opacity},a),n.style=a),n}(this,t,n);if("select"===t)return function(t,e,n){if(n&&null==n.z2){n=D({},n);var i=t.z2SelectLift;n.z2=t.z2+(null!=i?i:9)}return n}(this,0,n)}return n}function zs(t){t.stateProxy=Es;var e=t.getTextContent(),n=t.getTextGuideLine();e&&(e.stateProxy=Es),n&&(n.stateProxy=Es)}function Bs(t,e){!Xs(t,e)&&!t.__highByOuter&&Rs(t,Ds)}function Fs(t,e){!Xs(t,e)&&!t.__highByOuter&&Rs(t,Is)}function Vs(t,e){t.__highByOuter|=1<<(e||0),Rs(t,Ds)}function Hs(t,e){!(t.__highByOuter&=~(1<<(e||0)))&&Rs(t,Is)}function Ws(t){Rs(t,As)}function Gs(t){Rs(t,Ls)}function Us(t){Rs(t,Ps)}function Xs(t,e){return t.__highDownSilentOnTouch&&e.zrByTouch}function Ys(t){var e=t.getModel(),n=[],i=[];e.eachComponent((function(e,r){var o=vs(r),a="series"===e,s=a?t.getViewOfSeriesModel(r):t.getViewOfComponentModel(r);!a&&i.push(s),o.isBlured&&(s.group.traverse((function(t){As(t)})),a&&n.push(r)),o.isBlured=!1})),R(i,(function(t){t&&t.toggleBlurSeries&&t.toggleBlurSeries(n,!1,e)}))}function qs(t,e,n,i){var r=i.getModel();function o(t,e){for(var n=0;n<e.length;n++){var i=t.getItemGraphicEl(e[n]);i&&Ws(i)}}if(n=n||"coordinateSystem",null!=t&&e&&"none"!==e){var a=r.getSeriesByIndex(t),s=a.coordinateSystem;s&&s.master&&(s=s.master);var l=[];r.eachSeries((function(t){var r=a===t,u=t.coordinateSystem;if(u&&u.master&&(u=u.master),!("series"===n&&!r||"coordinateSystem"===n&&!(u&&s?u===s:r)||"series"===e&&r)){if(i.getViewOfSeriesModel(t).group.traverse((function(t){t.__highByOuter&&r&&"self"===e||ks(t)})),O(e))o(t.getData(),e);else if(Y(e))for(var h=B(e),c=0;c<h.length;c++)o(t.getData(h[c]),e[h[c]]);l.push(t),vs(t).isBlured=!0}})),r.eachComponent((function(t,e){if("series"!==t){var n=i.getViewOfComponentModel(e);n&&n.toggleBlurSeries&&n.toggleBlurSeries(l,!0,r)}}))}}function Zs(t,e,n){if(null!=t&&null!=e){var i=n.getModel().getComponent(t,e);if(i){vs(i).isBlured=!0;var r=n.getViewOfComponentModel(i);r&&r.focusBlurEnabled&&r.group.traverse((function(t){ks(t)}))}}}function js(t,e,n,i){var r={focusSelf:!1,dispatchers:null};if(null==t||"series"===t||null==e||null==n)return r;var o=i.getModel().getComponent(t,e);if(!o)return r;var a=i.getViewOfComponentModel(o);if(!a||!a.findHighDownDispatchers)return r;for(var s,l=a.findHighDownDispatchers(n),u=0;u<l.length;u++)if("self"===fs(l[u]).focus){s=!0;break}return{focusSelf:s,dispatchers:l}}function Ks(t){R(t.getAllData(),(function(e){var n=e.data,i=e.type;n.eachItemGraphicEl((function(e,n){t.isSelected(n,i)?Gs(e):Us(e)}))}))}function $s(t){var e=[];return t.eachSeries((function(t){R(t.getAllData(),(function(n){n.data;var i=n.type,r=t.getSelectedDataIndices();if(r.length>0){var o={dataIndex:r,seriesIndex:t.seriesIndex};null!=i&&(o.dataType=i),e.push(o)}}))})),e}function Qs(t,e,n){il(t,!0),Rs(t,zs),function(t,e,n){var i=fs(t);null!=e?(i.focus=e,i.blurScope=n):i.focus&&(i.focus=null)}(t,e,n)}function Js(t,e,n,i){i?function(t){il(t,!1)}(t):Qs(t,e,n)}var tl=["emphasis","blur","select"],el={itemStyle:"getItemStyle",lineStyle:"getLineStyle",areaStyle:"getAreaStyle"};function nl(t,e,n,i){n=n||"itemStyle";for(var r=0;r<tl.length;r++){var o=tl[r],a=e.getModel([o,n]);t.ensureState(o).style=i?i(a):a[el[n]]()}}function il(t,e){var n=!1===e,i=t;t.highDownSilentOnTouch&&(i.__highDownSilentOnTouch=t.highDownSilentOnTouch),n&&!i.__highDownDispatcher||(i.__highByOuter=i.__highByOuter||0,i.__highDownDispatcher=!n)}function rl(t){return!(!t||!t.__highDownDispatcher)}function ol(t){var e=t.type;return e===bs||e===Ss||e===Ms}function al(t){var e=t.type;return e===xs||e===ws}var sl=wa.CMD,ll=[[],[],[]],ul=Math.sqrt,hl=Math.atan2;var cl=Math.sqrt,pl=Math.sin,fl=Math.cos,dl=Math.PI;function gl(t){return Math.sqrt(t[0]*t[0]+t[1]*t[1])}function yl(t,e){return(t[0]*e[0]+t[1]*e[1])/(gl(t)*gl(e))}function vl(t,e){return(t[0]*e[1]<t[1]*e[0]?-1:1)*Math.acos(yl(t,e))}function ml(t,e,n,i,r,o,a,s,l,u,h){var c=l*(dl/180),p=fl(c)*(t-n)/2+pl(c)*(e-i)/2,f=-1*pl(c)*(t-n)/2+fl(c)*(e-i)/2,d=p*p/(a*a)+f*f/(s*s);d>1&&(a*=cl(d),s*=cl(d));var g=(r===o?-1:1)*cl((a*a*(s*s)-a*a*(f*f)-s*s*(p*p))/(a*a*(f*f)+s*s*(p*p)))||0,y=g*a*f/s,v=g*-s*p/a,m=(t+n)/2+fl(c)*y-pl(c)*v,_=(e+i)/2+pl(c)*y+fl(c)*v,x=vl([1,0],[(p-y)/a,(f-v)/s]),w=[(p-y)/a,(f-v)/s],b=[(-1*p-y)/a,(-1*f-v)/s],S=vl(w,b);if(yl(w,b)<=-1&&(S=dl),yl(w,b)>=1&&(S=0),S<0){var M=Math.round(S/dl*1e6)/1e6;S=2*dl+M%2*dl}h.addData(u,m,_,a,s,x,S,c,o)}var _l=/([mlvhzcqtsa])([^mlvhzcqtsa]*)/gi,xl=/-?([0-9]*\.)?[0-9]+([eE]-?[0-9]+)?/g;var wl=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.applyTransform=function(t){},e}(Ha);function bl(t){return null!=t.setData}function Sl(t,e){var n=function(t){var e=new wa;if(!t)return e;var n,i=0,r=0,o=i,a=r,s=wa.CMD,l=t.match(_l);if(!l)return e;for(var u=0;u<l.length;u++){for(var h=l[u],c=h.charAt(0),p=void 0,f=h.match(xl)||[],d=f.length,g=0;g<d;g++)f[g]=parseFloat(f[g]);for(var y=0;y<d;){var v=void 0,m=void 0,_=void 0,x=void 0,w=void 0,b=void 0,S=void 0,M=i,T=r,C=void 0,D=void 0;switch(c){case"l":i+=f[y++],r+=f[y++],p=s.L,e.addData(p,i,r);break;case"L":i=f[y++],r=f[y++],p=s.L,e.addData(p,i,r);break;case"m":i+=f[y++],r+=f[y++],p=s.M,e.addData(p,i,r),o=i,a=r,c="l";break;case"M":i=f[y++],r=f[y++],p=s.M,e.addData(p,i,r),o=i,a=r,c="L";break;case"h":i+=f[y++],p=s.L,e.addData(p,i,r);break;case"H":i=f[y++],p=s.L,e.addData(p,i,r);break;case"v":r+=f[y++],p=s.L,e.addData(p,i,r);break;case"V":r=f[y++],p=s.L,e.addData(p,i,r);break;case"C":p=s.C,e.addData(p,f[y++],f[y++],f[y++],f[y++],f[y++],f[y++]),i=f[y-2],r=f[y-1];break;case"c":p=s.C,e.addData(p,f[y++]+i,f[y++]+r,f[y++]+i,f[y++]+r,f[y++]+i,f[y++]+r),i+=f[y-2],r+=f[y-1];break;case"S":v=i,m=r,C=e.len(),D=e.data,n===s.C&&(v+=i-D[C-4],m+=r-D[C-3]),p=s.C,M=f[y++],T=f[y++],i=f[y++],r=f[y++],e.addData(p,v,m,M,T,i,r);break;case"s":v=i,m=r,C=e.len(),D=e.data,n===s.C&&(v+=i-D[C-4],m+=r-D[C-3]),p=s.C,M=i+f[y++],T=r+f[y++],i+=f[y++],r+=f[y++],e.addData(p,v,m,M,T,i,r);break;case"Q":M=f[y++],T=f[y++],i=f[y++],r=f[y++],p=s.Q,e.addData(p,M,T,i,r);break;case"q":M=f[y++]+i,T=f[y++]+r,i+=f[y++],r+=f[y++],p=s.Q,e.addData(p,M,T,i,r);break;case"T":v=i,m=r,C=e.len(),D=e.data,n===s.Q&&(v+=i-D[C-4],m+=r-D[C-3]),i=f[y++],r=f[y++],p=s.Q,e.addData(p,v,m,i,r);break;case"t":v=i,m=r,C=e.len(),D=e.data,n===s.Q&&(v+=i-D[C-4],m+=r-D[C-3]),i+=f[y++],r+=f[y++],p=s.Q,e.addData(p,v,m,i,r);break;case"A":_=f[y++],x=f[y++],w=f[y++],b=f[y++],S=f[y++],ml(M=i,T=r,i=f[y++],r=f[y++],b,S,_,x,w,p=s.A,e);break;case"a":_=f[y++],x=f[y++],w=f[y++],b=f[y++],S=f[y++],ml(M=i,T=r,i+=f[y++],r+=f[y++],b,S,_,x,w,p=s.A,e)}}"z"!==c&&"Z"!==c||(p=s.Z,e.addData(p),i=o,r=a),n=p}return e.toStatic(),e}(t),i=D({},e);return i.buildPath=function(t){if(bl(t)){t.setData(n.data),(e=t.getContext())&&t.rebuildPath(e,1)}else{var e=t;n.rebuildPath(e,1)}},i.applyTransform=function(t){!function(t,e){if(e){var n,i,r,o,a,s,l=t.data,u=t.len(),h=sl.M,c=sl.C,p=sl.L,f=sl.R,d=sl.A,g=sl.Q;for(r=0,o=0;r<u;){switch(n=l[r++],o=r,i=0,n){case h:case p:i=1;break;case c:i=3;break;case g:i=2;break;case d:var y=e[4],v=e[5],m=ul(e[0]*e[0]+e[1]*e[1]),_=ul(e[2]*e[2]+e[3]*e[3]),x=hl(-e[1]/_,e[0]/m);l[r]*=m,l[r++]+=y,l[r]*=_,l[r++]+=v,l[r++]*=m,l[r++]*=_,l[r++]+=x,l[r++]+=x,o=r+=2;break;case f:s[0]=l[r++],s[1]=l[r++],Et(s,s,e),l[o++]=s[0],l[o++]=s[1],s[0]+=l[r++],s[1]+=l[r++],Et(s,s,e),l[o++]=s[0],l[o++]=s[1]}for(a=0;a<i;a++){var w=ll[a];w[0]=l[r++],w[1]=l[r++],Et(w,w,e),l[o++]=w[0],l[o++]=w[1]}}t.increaseVersion()}}(n,t),this.dirtyShape()},i}var Ml=function(){this.cx=0,this.cy=0,this.r=0},Tl=function(t){function e(e){return t.call(this,e)||this}return n(e,t),e.prototype.getDefaultShape=function(){return new Ml},e.prototype.buildPath=function(t,e){t.moveTo(e.cx+e.r,e.cy),t.arc(e.cx,e.cy,e.r,0,2*Math.PI)},e}(Ha);Tl.prototype.type="circle";var Cl=function(){this.cx=0,this.cy=0,this.rx=0,this.ry=0},Dl=function(t){function e(e){return t.call(this,e)||this}return n(e,t),e.prototype.getDefaultShape=function(){return new Cl},e.prototype.buildPath=function(t,e){var n=.5522848,i=e.cx,r=e.cy,o=e.rx,a=e.ry,s=o*n,l=a*n;t.moveTo(i-o,r),t.bezierCurveTo(i-o,r-l,i-s,r-a,i,r-a),t.bezierCurveTo(i+s,r-a,i+o,r-l,i+o,r),t.bezierCurveTo(i+o,r+l,i+s,r+a,i,r+a),t.bezierCurveTo(i-s,r+a,i-o,r+l,i-o,r),t.closePath()},e}(Ha);Dl.prototype.type="ellipse";var Il=Math.PI,kl=2*Il,Al=Math.sin,Ll=Math.cos,Pl=Math.acos,Ol=Math.atan2,Rl=Math.abs,Nl=Math.sqrt,El=Math.max,zl=Math.min,Bl=1e-4;function Fl(t,e,n,i,r,o,a){var s=t-n,l=e-i,u=(a?o:-o)/Nl(s*s+l*l),h=u*l,c=-u*s,p=t+h,f=e+c,d=n+h,g=i+c,y=(p+d)/2,v=(f+g)/2,m=d-p,_=g-f,x=m*m+_*_,w=r-o,b=p*g-d*f,S=(_<0?-1:1)*Nl(El(0,w*w*x-b*b)),M=(b*_-m*S)/x,T=(-b*m-_*S)/x,C=(b*_+m*S)/x,D=(-b*m+_*S)/x,I=M-y,k=T-v,A=C-y,L=D-v;return I*I+k*k>A*A+L*L&&(M=C,T=D),{cx:M,cy:T,x0:-h,y0:-c,x1:M*(r/w-1),y1:T*(r/w-1)}}function Vl(t,e){var n,i=El(e.r,0),r=El(e.r0||0,0),o=i>0;if(o||r>0){if(o||(i=r,r=0),r>i){var a=i;i=r,r=a}var s=e.startAngle,l=e.endAngle;if(!isNaN(s)&&!isNaN(l)){var u=e.cx,h=e.cy,c=!!e.clockwise,p=Rl(l-s),f=p>kl&&p%kl;if(f>Bl&&(p=f),i>Bl)if(p>kl-Bl)t.moveTo(u+i*Ll(s),h+i*Al(s)),t.arc(u,h,i,s,l,!c),r>Bl&&(t.moveTo(u+r*Ll(l),h+r*Al(l)),t.arc(u,h,r,l,s,c));else{var d=void 0,g=void 0,y=void 0,v=void 0,m=void 0,_=void 0,x=void 0,w=void 0,b=void 0,S=void 0,M=void 0,T=void 0,C=void 0,D=void 0,I=void 0,k=void 0,A=i*Ll(s),L=i*Al(s),P=r*Ll(l),O=r*Al(l),R=p>Bl;if(R){var N=e.cornerRadius;N&&(n=function(t){var e;if(H(t)){var n=t.length;if(!n)return t;e=1===n?[t[0],t[0],0,0]:2===n?[t[0],t[0],t[1],t[1]]:3===n?t.concat(t[2]):t}else e=[t,t,t,t];return e}(N),d=n[0],g=n[1],y=n[2],v=n[3]);var E=Rl(i-r)/2;if(m=zl(E,y),_=zl(E,v),x=zl(E,d),w=zl(E,g),M=b=El(m,_),T=S=El(x,w),(b>Bl||S>Bl)&&(C=i*Ll(l),D=i*Al(l),I=r*Ll(s),k=r*Al(s),p<Il)){var z=function(t,e,n,i,r,o,a,s){var l=n-t,u=i-e,h=a-r,c=s-o,p=c*l-h*u;if(!(p*p<Bl))return[t+(p=(h*(e-o)-c*(t-r))/p)*l,e+p*u]}(A,L,I,k,C,D,P,O);if(z){var B=A-z[0],F=L-z[1],V=C-z[0],W=D-z[1],G=1/Al(Pl((B*V+F*W)/(Nl(B*B+F*F)*Nl(V*V+W*W)))/2),U=Nl(z[0]*z[0]+z[1]*z[1]);M=zl(b,(i-U)/(G+1)),T=zl(S,(r-U)/(G-1))}}}if(R)if(M>Bl){var X=zl(y,M),Y=zl(v,M),q=Fl(I,k,A,L,i,X,c),Z=Fl(C,D,P,O,i,Y,c);t.moveTo(u+q.cx+q.x0,h+q.cy+q.y0),M<b&&X===Y?t.arc(u+q.cx,h+q.cy,M,Ol(q.y0,q.x0),Ol(Z.y0,Z.x0),!c):(X>0&&t.arc(u+q.cx,h+q.cy,X,Ol(q.y0,q.x0),Ol(q.y1,q.x1),!c),t.arc(u,h,i,Ol(q.cy+q.y1,q.cx+q.x1),Ol(Z.cy+Z.y1,Z.cx+Z.x1),!c),Y>0&&t.arc(u+Z.cx,h+Z.cy,Y,Ol(Z.y1,Z.x1),Ol(Z.y0,Z.x0),!c))}else t.moveTo(u+A,h+L),t.arc(u,h,i,s,l,!c);else t.moveTo(u+A,h+L);if(r>Bl&&R)if(T>Bl){X=zl(d,T),q=Fl(P,O,C,D,r,-(Y=zl(g,T)),c),Z=Fl(A,L,I,k,r,-X,c);t.lineTo(u+q.cx+q.x0,h+q.cy+q.y0),T<S&&X===Y?t.arc(u+q.cx,h+q.cy,T,Ol(q.y0,q.x0),Ol(Z.y0,Z.x0),!c):(Y>0&&t.arc(u+q.cx,h+q.cy,Y,Ol(q.y0,q.x0),Ol(q.y1,q.x1),!c),t.arc(u,h,r,Ol(q.cy+q.y1,q.cx+q.x1),Ol(Z.cy+Z.y1,Z.cx+Z.x1),c),X>0&&t.arc(u+Z.cx,h+Z.cy,X,Ol(Z.y1,Z.x1),Ol(Z.y0,Z.x0),!c))}else t.lineTo(u+P,h+O),t.arc(u,h,r,l,s,c);else t.lineTo(u+P,h+O)}else t.moveTo(u,h);t.closePath()}}}var Hl=function(){this.cx=0,this.cy=0,this.r0=0,this.r=0,this.startAngle=0,this.endAngle=2*Math.PI,this.clockwise=!0,this.cornerRadius=0},Wl=function(t){function e(e){return t.call(this,e)||this}return n(e,t),e.prototype.getDefaultShape=function(){return new Hl},e.prototype.buildPath=function(t,e){Vl(t,e)},e.prototype.isZeroArea=function(){return this.shape.startAngle===this.shape.endAngle||this.shape.r===this.shape.r0},e}(Ha);Wl.prototype.type="sector";var Gl=function(){this.cx=0,this.cy=0,this.r=0,this.r0=0},Ul=function(t){function e(e){return t.call(this,e)||this}return n(e,t),e.prototype.getDefaultShape=function(){return new Gl},e.prototype.buildPath=function(t,e){var n=e.cx,i=e.cy,r=2*Math.PI;t.moveTo(n+e.r,i),t.arc(n,i,e.r,0,r,!1),t.moveTo(n+e.r0,i),t.arc(n,i,e.r0,0,r,!0)},e}(Ha);function Xl(t,e,n){var i=e.smooth,r=e.points;if(r&&r.length>=2){if(i){var o=function(t,e,n,i){var r,o,a,s,l=[],u=[],h=[],c=[];if(i){a=[1/0,1/0],s=[-1/0,-1/0];for(var p=0,f=t.length;p<f;p++)zt(a,a,t[p]),Bt(s,s,t[p]);zt(a,a,i[0]),Bt(s,s,i[1])}for(p=0,f=t.length;p<f;p++){var d=t[p];if(n)r=t[p?p-1:f-1],o=t[(p+1)%f];else{if(0===p||p===f-1){l.push(bt(t[p]));continue}r=t[p-1],o=t[p+1]}Mt(u,o,r),kt(u,u,e);var g=Lt(d,r),y=Lt(d,o),v=g+y;0!==v&&(g/=v,y/=v),kt(h,u,-g),kt(c,u,y);var m=St([],d,h),_=St([],d,c);i&&(Bt(m,m,a),zt(m,m,s),Bt(_,_,a),zt(_,_,s)),l.push(m),l.push(_)}return n&&l.push(l.shift()),l}(r,i,n,e.smoothConstraint);t.moveTo(r[0][0],r[0][1]);for(var a=r.length,s=0;s<(n?a:a-1);s++){var l=o[2*s],u=o[2*s+1],h=r[(s+1)%a];t.bezierCurveTo(l[0],l[1],u[0],u[1],h[0],h[1])}}else{t.moveTo(r[0][0],r[0][1]);s=1;for(var c=r.length;s<c;s++)t.lineTo(r[s][0],r[s][1])}n&&t.closePath()}}Ul.prototype.type="ring";var Yl=function(){this.points=null,this.smooth=0,this.smoothConstraint=null},ql=function(t){function e(e){return t.call(this,e)||this}return n(e,t),e.prototype.getDefaultShape=function(){return new Yl},e.prototype.buildPath=function(t,e){Xl(t,e,!0)},e}(Ha);ql.prototype.type="polygon";var Zl=function(){this.points=null,this.percent=1,this.smooth=0,this.smoothConstraint=null},jl=function(t){function e(e){return t.call(this,e)||this}return n(e,t),e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new Zl},e.prototype.buildPath=function(t,e){Xl(t,e,!1)},e}(Ha);jl.prototype.type="polyline";var Kl={},$l=function(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.percent=1},Ql=function(t){function e(e){return t.call(this,e)||this}return n(e,t),e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new $l},e.prototype.buildPath=function(t,e){var n,i,r,o;if(this.subPixelOptimize){var a=Za(Kl,e,this.style);n=a.x1,i=a.y1,r=a.x2,o=a.y2}else n=e.x1,i=e.y1,r=e.x2,o=e.y2;var s=e.percent;0!==s&&(t.moveTo(n,i),s<1&&(r=n*(1-s)+r*s,o=i*(1-s)+o*s),t.lineTo(r,o))},e.prototype.pointAt=function(t){var e=this.shape;return[e.x1*(1-t)+e.x2*t,e.y1*(1-t)+e.y2*t]},e}(Ha);Ql.prototype.type="line";var Jl=[],tu=function(){this.x1=0,this.y1=0,this.x2=0,this.y2=0,this.cpx1=0,this.cpy1=0,this.percent=1};function eu(t,e,n){var i=t.cpx2,r=t.cpy2;return null!=i||null!=r?[(n?ln:sn)(t.x1,t.cpx1,t.cpx2,t.x2,e),(n?ln:sn)(t.y1,t.cpy1,t.cpy2,t.y2,e)]:[(n?dn:fn)(t.x1,t.cpx1,t.x2,e),(n?dn:fn)(t.y1,t.cpy1,t.y2,e)]}var nu=function(t){function e(e){return t.call(this,e)||this}return n(e,t),e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new tu},e.prototype.buildPath=function(t,e){var n=e.x1,i=e.y1,r=e.x2,o=e.y2,a=e.cpx1,s=e.cpy1,l=e.cpx2,u=e.cpy2,h=e.percent;0!==h&&(t.moveTo(n,i),null==l||null==u?(h<1&&(yn(n,a,r,h,Jl),a=Jl[1],r=Jl[2],yn(i,s,o,h,Jl),s=Jl[1],o=Jl[2]),t.quadraticCurveTo(a,s,r,o)):(h<1&&(cn(n,a,l,r,h,Jl),a=Jl[1],l=Jl[2],r=Jl[3],cn(i,s,u,o,h,Jl),s=Jl[1],u=Jl[2],o=Jl[3]),t.bezierCurveTo(a,s,l,u,r,o)))},e.prototype.pointAt=function(t){return eu(this.shape,t,!1)},e.prototype.tangentAt=function(t){var e=eu(this.shape,t,!0);return At(e,e)},e}(Ha);nu.prototype.type="bezier-curve";var iu=function(){this.cx=0,this.cy=0,this.r=0,this.startAngle=0,this.endAngle=2*Math.PI,this.clockwise=!0},ru=function(t){function e(e){return t.call(this,e)||this}return n(e,t),e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new iu},e.prototype.buildPath=function(t,e){var n=e.cx,i=e.cy,r=Math.max(e.r,0),o=e.startAngle,a=e.endAngle,s=e.clockwise,l=Math.cos(o),u=Math.sin(o);t.moveTo(l*r+n,u*r+i),t.arc(n,i,r,o,a,!s)},e}(Ha);ru.prototype.type="arc";var ou=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="compound",e}return n(e,t),e.prototype._updatePathDirty=function(){for(var t=this.shape.paths,e=this.shapeChanged(),n=0;n<t.length;n++)e=e||t[n].shapeChanged();e&&this.dirtyShape()},e.prototype.beforeBrush=function(){this._updatePathDirty();for(var t=this.shape.paths||[],e=this.getGlobalScale(),n=0;n<t.length;n++)t[n].path||t[n].createPathProxy(),t[n].path.setScale(e[0],e[1],t[n].segmentIgnoreThreshold)},e.prototype.buildPath=function(t,e){for(var n=e.paths||[],i=0;i<n.length;i++)n[i].buildPath(t,n[i].shape,!0)},e.prototype.afterBrush=function(){for(var t=this.shape.paths||[],e=0;e<t.length;e++)t[e].pathUpdated()},e.prototype.getBoundingRect=function(){return this._updatePathDirty.call(this),Ha.prototype.getBoundingRect.call(this)},e}(Ha),au=function(){function t(t){this.colorStops=t||[]}return t.prototype.addColorStop=function(t,e){this.colorStops.push({offset:t,color:e})},t}(),su=function(t){function e(e,n,i,r,o,a){var s=t.call(this,o)||this;return s.x=null==e?0:e,s.y=null==n?0:n,s.x2=null==i?1:i,s.y2=null==r?0:r,s.type="linear",s.global=a||!1,s}return n(e,t),e}(au),lu=function(t){function e(e,n,i,r,o){var a=t.call(this,r)||this;return a.x=null==e?.5:e,a.y=null==n?.5:n,a.r=null==i?.5:i,a.type="radial",a.global=o||!1,a}return n(e,t),e}(au),uu=[0,0],hu=[0,0],cu=new ve,pu=new ve,fu=function(){function t(t,e){this._corners=[],this._axes=[],this._origin=[0,0];for(var n=0;n<4;n++)this._corners[n]=new ve;for(n=0;n<2;n++)this._axes[n]=new ve;t&&this.fromBoundingRect(t,e)}return t.prototype.fromBoundingRect=function(t,e){var n=this._corners,i=this._axes,r=t.x,o=t.y,a=r+t.width,s=o+t.height;if(n[0].set(r,o),n[1].set(a,o),n[2].set(a,s),n[3].set(r,s),e)for(var l=0;l<4;l++)n[l].transform(e);ve.sub(i[0],n[1],n[0]),ve.sub(i[1],n[3],n[0]),i[0].normalize(),i[1].normalize();for(l=0;l<2;l++)this._origin[l]=i[l].dot(n[0])},t.prototype.intersect=function(t,e){var n=!0,i=!e;return cu.set(1/0,1/0),pu.set(0,0),!this._intersectCheckOneSide(this,t,cu,pu,i,1)&&(n=!1,i)||!this._intersectCheckOneSide(t,this,cu,pu,i,-1)&&(n=!1,i)||i||ve.copy(e,n?cu:pu),n},t.prototype._intersectCheckOneSide=function(t,e,n,i,r,o){for(var a=!0,s=0;s<2;s++){var l=this._axes[s];if(this._getProjMinMaxOnAxis(s,t._corners,uu),this._getProjMinMaxOnAxis(s,e._corners,hu),uu[1]<hu[0]||uu[0]>hu[1]){if(a=!1,r)return a;var u=Math.abs(hu[0]-uu[1]),h=Math.abs(uu[0]-hu[1]);Math.min(u,h)>i.len()&&(u<h?ve.scale(i,l,-u*o):ve.scale(i,l,h*o))}else if(n){u=Math.abs(hu[0]-uu[1]),h=Math.abs(uu[0]-hu[1]);Math.min(u,h)<n.len()&&(u<h?ve.scale(n,l,u*o):ve.scale(n,l,-h*o))}}return a},t.prototype._getProjMinMaxOnAxis=function(t,e,n){for(var i=this._axes[t],r=this._origin,o=e[0].dot(i)+r[t],a=o,s=o,l=1;l<e.length;l++){var u=e[l].dot(i)+r[t];a=Math.min(u,a),s=Math.max(u,s)}n[0]=a,n[1]=s},t}(),du=[],gu=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.notClear=!0,e.incremental=!0,e._displayables=[],e._temporaryDisplayables=[],e._cursor=0,e}return n(e,t),e.prototype.traverse=function(t,e){t.call(e,this)},e.prototype.useStyle=function(){this.style={}},e.prototype.getCursor=function(){return this._cursor},e.prototype.innerAfterBrush=function(){this._cursor=this._displayables.length},e.prototype.clearDisplaybles=function(){this._displayables=[],this._temporaryDisplayables=[],this._cursor=0,this.markRedraw(),this.notClear=!1},e.prototype.clearTemporalDisplayables=function(){this._temporaryDisplayables=[]},e.prototype.addDisplayable=function(t,e){e?this._temporaryDisplayables.push(t):this._displayables.push(t),this.markRedraw()},e.prototype.addDisplayables=function(t,e){e=e||!1;for(var n=0;n<t.length;n++)this.addDisplayable(t[n],e)},e.prototype.getDisplayables=function(){return this._displayables},e.prototype.getTemporalDisplayables=function(){return this._temporaryDisplayables},e.prototype.eachPendingDisplayable=function(t){for(var e=this._cursor;e<this._displayables.length;e++)t&&t(this._displayables[e]);for(e=0;e<this._temporaryDisplayables.length;e++)t&&t(this._temporaryDisplayables[e])},e.prototype.update=function(){this.updateTransform();for(var t=this._cursor;t<this._displayables.length;t++){(e=this._displayables[t]).parent=this,e.update(),e.parent=null}for(t=0;t<this._temporaryDisplayables.length;t++){var e;(e=this._temporaryDisplayables[t]).parent=this,e.update(),e.parent=null}},e.prototype.getBoundingRect=function(){if(!this._rect){for(var t=new Ce(1/0,1/0,-1/0,-1/0),e=0;e<this._displayables.length;e++){var n=this._displayables[e],i=n.getBoundingRect().clone();n.needLocalTransform()&&i.applyTransform(n.getLocalTransform(du)),t.union(i)}this._rect=t}return this._rect},e.prototype.contain=function(t,e){var n=this.transformCoordToLocal(t,e);if(this.getBoundingRect().contain(n[0],n[1]))for(var i=0;i<this._displayables.length;i++){if(this._displayables[i].contain(t,e))return!0}return!1},e}(Vo),yu=Qr();function vu(t,e,n,i,r,o,a){var s,l=!1;W(r)?(a=o,o=r,r=null):Y(r)&&(o=r.cb,a=r.during,l=r.isFrom,s=r.removeOpt,r=r.dataIndex);var u="leave"===t;u||e.stopAnimation("leave");var h=function(t,e,n,i,r){var o;if(e&&e.ecModel){var a=e.ecModel.getUpdatePayload();o=a&&a.animation}var s="update"===t;if(e&&e.isAnimationEnabled()){var l=void 0,u=void 0,h=void 0;return i?(l=et(i.duration,200),u=et(i.easing,"cubicOut"),h=0):(l=e.getShallow(s?"animationDurationUpdate":"animationDuration"),u=e.getShallow(s?"animationEasingUpdate":"animationEasing"),h=e.getShallow(s?"animationDelayUpdate":"animationDelay")),o&&(null!=o.duration&&(l=o.duration),null!=o.easing&&(u=o.easing),null!=o.delay&&(h=o.delay)),W(h)&&(h=h(n,r)),W(l)&&(l=l(n)),{duration:l||0,delay:h,easing:u}}return null}(t,i,r,u?s||{}:null,i&&i.getAnimationDelayParams?i.getAnimationDelayParams(e,r):null);if(h&&h.duration>0){var c={duration:h.duration,delay:h.delay||0,easing:h.easing,done:o,force:!!o||!!a,setToFinal:!u,scope:t,during:a};l?e.animateFrom(n,c):e.animateTo(n,c)}else e.stopAnimation(),!l&&e.attr(n),a&&a(1),o&&o()}function mu(t,e,n,i,r,o){vu("update",t,e,n,i,r,o)}function _u(t,e,n,i,r,o){vu("enter",t,e,n,i,r,o)}function xu(t){if(!t.__zr)return!0;for(var e=0;e<t.animators.length;e++){if("leave"===t.animators[e].scope)return!0}return!1}function wu(t,e,n,i,r,o){xu(t)||vu("leave",t,e,n,i,r,o)}function bu(t,e,n,i){t.removeTextContent(),t.removeTextGuideLine(),wu(t,{style:{opacity:0}},e,n,i)}function Su(t,e,n){function i(){t.parent&&t.parent.remove(t)}t.isGroup?t.traverse((function(t){t.isGroup||bu(t,e,n,i)})):bu(t,e,n,i)}function Mu(t){yu(t).oldStyle=t.style}var Tu=Math.max,Cu=Math.min,Du={};var Iu=function(t,e){var i=Sl(t,e);return function(t){function e(e){var n=t.call(this,e)||this;return n.applyTransform=i.applyTransform,n.buildPath=i.buildPath,n}return n(e,t),e}(wl)};function ku(t,e){Du[t]=e}function Au(t,e,n,i){var r=function(t,e){return new wl(Sl(t,e))}(t,e);return n&&("center"===i&&(n=Pu(n,r.getBoundingRect())),Ru(r,n)),r}function Lu(t,e,n){var i=new Ya({style:{image:t,x:e.x,y:e.y,width:e.width,height:e.height},onload:function(t){if("center"===n){var r={width:t.width,height:t.height};i.setStyle(Pu(e,r))}}});return i}function Pu(t,e){var n,i=e.width/e.height,r=t.height*i;return n=r<=t.width?t.height:(r=t.width)/i,{x:t.x+t.width/2-r/2,y:t.y+t.height/2-n/2,width:r,height:n}}var Ou=function(t,e){for(var n=[],i=t.length,r=0;r<i;r++){var o=t[r];n.push(o.getUpdatedPathProxy(!0))}var a=new Ha(e);return a.createPathProxy(),a.buildPath=function(t){if(bl(t)){t.appendPath(n);var e=t.getContext();e&&t.rebuildPath(e,1)}},a};function Ru(t,e){if(t.applyTransform){var n=t.getBoundingRect().calculateTransform(e);t.applyTransform(n)}}function Nu(t,e){return Za(t,t,{lineWidth:e}),t}function Eu(t){return!t.isGroup}function zu(t){var e=t.itemTooltipOption,n=t.componentModel,i=t.itemName,r=G(e)?{formatter:e}:e,o=n.mainType,a=n.componentIndex,s={componentType:o,name:i,$vars:["name"]};s[o+"Index"]=a;var l=t.formatterParamsExtra;l&&R(B(l),(function(t){vt(s,t)||(s[t]=l[t],s.$vars.push(t))}));var u=fs(t.el);u.componentMainType=o,u.componentIndex=a,u.tooltipConfig={name:i,option:I({content:i,encodeHTMLContent:!0,formatterParams:s},r)}}function Bu(t,e){var n;t.isGroup&&(n=e(t)),n||t.traverse(e)}function Fu(t,e){if(t)if(H(t))for(var n=0;n<t.length;n++)Bu(t[n],e);else Bu(t,e)}ku("circle",Tl),ku("ellipse",Dl),ku("sector",Wl),ku("ring",Ul),ku("polygon",ql),ku("polyline",jl),ku("rect",Qa),ku("line",Ql),ku("bezierCurve",nu),ku("arc",ru);var Vu={};function Hu(t,e,n){var i,r=t.labelFetcher,o=t.labelDataIndex,a=t.labelDimIndex,s=e.normal;r&&(i=r.getFormattedLabel(o,"normal",null,a,s&&s.get("formatter"),null!=n?{interpolatedValue:n}:null)),null==i&&(i=W(t.defaultText)?t.defaultText(o,t,n):t.defaultText);for(var l={normal:i},u=0;u<ms.length;u++){var h=ms[u],c=e[h];l[h]=et(r?r.getFormattedLabel(o,h,null,a,c&&c.get("formatter")):null,i)}return l}function Wu(t,e,n,i){n=n||Vu;for(var r=t instanceof es,o=!1,a=0;a<_s.length;a++){if((p=e[_s[a]])&&p.getShallow("show")){o=!0;break}}var s=r?t:t.getTextContent();if(o){r||(s||(s=new es,t.setTextContent(s)),t.stateProxy&&(s.stateProxy=t.stateProxy));var l=Hu(n,e),u=e.normal,h=!!u.getShallow("show"),c=Uu(u,i&&i.normal,n,!1,!r);c.text=l.normal,r||t.setTextConfig(Xu(u,n,!1));for(a=0;a<ms.length;a++){var p,f=ms[a];if(p=e[f]){var d=s.ensureState(f),g=!!et(p.getShallow("show"),h);if(g!==h&&(d.ignore=!g),d.style=Uu(p,i&&i[f],n,!0,!r),d.style.text=l[f],!r)t.ensureState(f).textConfig=Xu(p,n,!0)}}s.silent=!!u.getShallow("silent"),null!=s.style.x&&(c.x=s.style.x),null!=s.style.y&&(c.y=s.style.y),s.ignore=!h,s.useStyle(c),s.dirty(),n.enableTextSetter&&(Ku(s).setLabelText=function(t){var i=Hu(n,e,t);!function(t,e){for(var n=0;n<ms.length;n++){var i=ms[n],r=e[i],o=t.ensureState(i);o.style=o.style||{},o.style.text=r}var a=t.currentStates.slice();t.clearStates(!0),t.setStyle({text:e.normal}),t.useStates(a,!0)}(s,i)})}else s&&(s.ignore=!0);t.dirty()}function Gu(t,e){e=e||"label";for(var n={normal:t.getModel(e)},i=0;i<ms.length;i++){var r=ms[i];n[r]=t.getModel([r,e])}return n}function Uu(t,e,n,i,r){var o={};return function(t,e,n,i,r){n=n||Vu;var o,a=e.ecModel,s=a&&a.option.textStyle,l=function(t){var e;for(;t&&t!==t.ecModel;){var n=(t.option||Vu).rich;if(n){e=e||{};for(var i=B(n),r=0;r<i.length;r++){e[i[r]]=1}}t=t.parentModel}return e}(e);if(l)for(var u in o={},l)if(l.hasOwnProperty(u)){var h=e.getModel(["rich",u]);ju(o[u]={},h,s,n,i,r,!1,!0)}o&&(t.rich=o);var c=e.get("overflow");c&&(t.overflow=c);var p=e.get("minMargin");null!=p&&(t.margin=p);ju(t,e,s,n,i,r,!0,!1)}(o,t,n,i,r),e&&D(o,e),o}function Xu(t,e,n){e=e||{};var i,r={},o=t.getShallow("rotate"),a=et(t.getShallow("distance"),n?null:5),s=t.getShallow("offset");return"outside"===(i=t.getShallow("position")||(n?null:"inside"))&&(i=e.defaultOutsidePosition||"top"),null!=i&&(r.position=i),null!=s&&(r.offset=s),null!=o&&(o*=Math.PI/180,r.rotation=o),null!=a&&(r.distance=a),r.outsideFill="inherit"===t.get("color")?e.inheritColor||null:"auto",r}var Yu=["fontStyle","fontWeight","fontSize","fontFamily","textShadowColor","textShadowBlur","textShadowOffsetX","textShadowOffsetY"],qu=["align","lineHeight","width","height","tag","verticalAlign","ellipsis"],Zu=["padding","borderWidth","borderRadius","borderDashOffset","backgroundColor","borderColor","shadowColor","shadowBlur","shadowOffsetX","shadowOffsetY"];function ju(t,e,n,i,r,o,a,s){n=!r&&n||Vu;var l=i&&i.inheritColor,u=e.getShallow("color"),h=e.getShallow("textBorderColor"),c=et(e.getShallow("opacity"),n.opacity);"inherit"!==u&&"auto"!==u||(u=l||null),"inherit"!==h&&"auto"!==h||(h=l||null),o||(u=u||n.color,h=h||n.textBorderColor),null!=u&&(t.fill=u),null!=h&&(t.stroke=h);var p=et(e.getShallow("textBorderWidth"),n.textBorderWidth);null!=p&&(t.lineWidth=p);var f=et(e.getShallow("textBorderType"),n.textBorderType);null!=f&&(t.lineDash=f);var d=et(e.getShallow("textBorderDashOffset"),n.textBorderDashOffset);null!=d&&(t.lineDashOffset=d),r||null!=c||s||(c=i&&i.defaultOpacity),null!=c&&(t.opacity=c),r||o||null==t.fill&&i.inheritColor&&(t.fill=i.inheritColor);for(var g=0;g<Yu.length;g++){var y=Yu[g];null!=(m=et(e.getShallow(y),n[y]))&&(t[y]=m)}for(g=0;g<qu.length;g++){y=qu[g];null!=(m=e.getShallow(y))&&(t[y]=m)}if(null==t.verticalAlign){var v=e.getShallow("baseline");null!=v&&(t.verticalAlign=v)}if(!a||!i.disableBox){for(g=0;g<Zu.length;g++){var m;y=Zu[g];null!=(m=e.getShallow(y))&&(t[y]=m)}var _=e.getShallow("borderType");null!=_&&(t.borderDash=_),"auto"!==t.backgroundColor&&"inherit"!==t.backgroundColor||!l||(t.backgroundColor=l),"auto"!==t.borderColor&&"inherit"!==t.borderColor||!l||(t.borderColor=l)}}var Ku=Qr();var $u,Qu,Ju=["textStyle","color"],th=["fontStyle","fontWeight","fontSize","fontFamily","padding","lineHeight","rich","width","height","overflow"],eh=new es,nh=function(){function t(){}return t.prototype.getTextColor=function(t){var e=this.ecModel;return this.getShallow("color")||(!t&&e?e.get(Ju):null)},t.prototype.getFont=function(){return t={fontStyle:this.getShallow("fontStyle"),fontWeight:this.getShallow("fontWeight"),fontSize:this.getShallow("fontSize"),fontFamily:this.getShallow("fontFamily")},e=this.ecModel,n=e&&e.getModel("textStyle"),at([t.fontStyle||n&&n.getShallow("fontStyle")||"",t.fontWeight||n&&n.getShallow("fontWeight")||"",(t.fontSize||n&&n.getShallow("fontSize")||12)+"px",t.fontFamily||n&&n.getShallow("fontFamily")||"sans-serif"].join(" "));var t,e,n},t.prototype.getTextRect=function(t){for(var e={text:t,verticalAlign:this.getShallow("verticalAlign")||this.getShallow("baseline")},n=0;n<th.length;n++)e[th[n]]=this.getShallow(th[n]);return eh.useStyle(e),eh.update(),eh.getBoundingRect()},t}(),ih=[["lineWidth","width"],["stroke","color"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["lineDash","type"],["lineDashOffset","dashOffset"],["lineCap","cap"],["lineJoin","join"],["miterLimit"]],rh=go(ih),oh=function(){function t(){}return t.prototype.getLineStyle=function(t){return rh(this,t)},t}(),ah=[["fill","color"],["stroke","borderColor"],["lineWidth","borderWidth"],["opacity"],["shadowBlur"],["shadowOffsetX"],["shadowOffsetY"],["shadowColor"],["lineDash","borderType"],["lineDashOffset","borderDashOffset"],["lineCap","borderCap"],["lineJoin","borderJoin"],["miterLimit","borderMiterLimit"]],sh=go(ah),lh=function(){function t(){}return t.prototype.getItemStyle=function(t,e){return sh(this,t,e)},t}(),uh=function(){function t(t,e,n){this.parentModel=e,this.ecModel=n,this.option=t}return t.prototype.init=function(t,e,n){for(var i=[],r=3;r<arguments.length;r++)i[r-3]=arguments[r]},t.prototype.mergeOption=function(t,e){C(this.option,t,!0)},t.prototype.get=function(t,e){return null==t?this.option:this._doGet(this.parsePath(t),!e&&this.parentModel)},t.prototype.getShallow=function(t,e){var n=this.option,i=null==n?n:n[t];if(null==i&&!e){var r=this.parentModel;r&&(i=r.getShallow(t))}return i},t.prototype.getModel=function(e,n){var i=null!=e,r=i?this.parsePath(e):null;return new t(i?this._doGet(r):this.option,n=n||this.parentModel&&this.parentModel.getModel(this.resolveParentPath(r)),this.ecModel)},t.prototype.isEmpty=function(){return null==this.option},t.prototype.restoreData=function(){},t.prototype.clone=function(){return new(0,this.constructor)(T(this.option))},t.prototype.parsePath=function(t){return"string"==typeof t?t.split("."):t},t.prototype.resolveParentPath=function(t){return t},t.prototype.isAnimationEnabled=function(){if(!r.node&&this.option){if(null!=this.option.animation)return!!this.option.animation;if(this.parentModel)return this.parentModel.isAnimationEnabled()}},t.prototype._doGet=function(t,e){var n=this.option;if(!t)return n;for(var i=0;i<t.length&&(!t[i]||null!=(n=n&&"object"==typeof n?n[t[i]]:null));i++);return null==n&&e&&(n=e._doGet(this.resolveParentPath(t),e.parentModel)),n},t}();lo(uh),$u=uh,Qu=["__\0is_clz",ho++].join("_"),$u.prototype[Qu]=!0,$u.isInstance=function(t){return!(!t||!t[Qu])},P(uh,oh),P(uh,lh),P(uh,vo),P(uh,nh);var hh=Math.round(10*Math.random());function ch(t){return[t||"",hh++].join("_")}var ph="ZH",fh="EN",dh=fh,gh={},yh={},vh=r.domSupported&&(document.documentElement.lang||navigator.language||navigator.browserLanguage||dh).toUpperCase().indexOf(ph)>-1?ph:dh;function mh(t,e){t=t.toUpperCase(),yh[t]=new uh(e),gh[t]=e}mh(fh,{time:{month:["January","February","March","April","May","June","July","August","September","October","November","December"],monthAbbr:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],dayOfWeek:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"],dayOfWeekAbbr:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"]},legend:{selector:{all:"All",inverse:"Inv"}},toolbox:{brush:{title:{rect:"Box Select",polygon:"Lasso Select",lineX:"Horizontally Select",lineY:"Vertically Select",keep:"Keep Selections",clear:"Clear Selections"}},dataView:{title:"Data View",lang:["Data View","Close","Refresh"]},dataZoom:{title:{zoom:"Zoom",back:"Zoom Reset"}},magicType:{title:{line:"Switch to Line Chart",bar:"Switch to Bar Chart",stack:"Stack",tiled:"Tile"}},restore:{title:"Restore"},saveAsImage:{title:"Save as Image",lang:["Right Click to Save Image"]}},series:{typeNames:{pie:"Pie chart",bar:"Bar chart",line:"Line chart",scatter:"Scatter plot",effectScatter:"Ripple scatter plot",radar:"Radar chart",tree:"Tree",treemap:"Treemap",boxplot:"Boxplot",candlestick:"Candlestick",k:"K line chart",heatmap:"Heat map",map:"Map",parallel:"Parallel coordinate map",lines:"Line graph",graph:"Relationship graph",sankey:"Sankey diagram",funnel:"Funnel chart",gauge:"Gauge",pictorialBar:"Pictorial bar",themeRiver:"Theme River Map",sunburst:"Sunburst",custom:"Custom chart",chart:"Chart"}},aria:{general:{withTitle:'This is a chart about "{title}"',withoutTitle:"This is a chart"},series:{single:{prefix:"",withName:" with type {seriesType} named {seriesName}.",withoutName:" with type {seriesType}."},multiple:{prefix:". It consists of {seriesCount} series count.",withName:" The {seriesId} series is a {seriesType} representing {seriesName}.",withoutName:" The {seriesId} series is a {seriesType}.",separator:{middle:"",end:""}}},data:{allData:"The data is as follows: ",partialData:"The first {displayCnt} items are: ",withName:"the data for {name} is {value}",withoutName:"{value}",separator:{middle:", ",end:". "}}}}),mh(ph,{time:{month:["一月","二月","三月","四月","五月","六月","七月","八月","九月","十月","十一月","十二月"],monthAbbr:["1月","2月","3月","4月","5月","6月","7月","8月","9月","10月","11月","12月"],dayOfWeek:["星期日","星期一","星期二","星期三","星期四","星期五","星期六"],dayOfWeekAbbr:["日","一","二","三","四","五","六"]},legend:{selector:{all:"全选",inverse:"反选"}},toolbox:{brush:{title:{rect:"矩形选择",polygon:"圈选",lineX:"横向选择",lineY:"纵向选择",keep:"保持选择",clear:"清除选择"}},dataView:{title:"数据视图",lang:["数据视图","关闭","刷新"]},dataZoom:{title:{zoom:"区域缩放",back:"区域缩放还原"}},magicType:{title:{line:"切换为折线图",bar:"切换为柱状图",stack:"切换为堆叠",tiled:"切换为平铺"}},restore:{title:"还原"},saveAsImage:{title:"保存为图片",lang:["右键另存为图片"]}},series:{typeNames:{pie:"饼图",bar:"柱状图",line:"折线图",scatter:"散点图",effectScatter:"涟漪散点图",radar:"雷达图",tree:"树图",treemap:"矩形树图",boxplot:"箱型图",candlestick:"K线图",k:"K线图",heatmap:"热力图",map:"地图",parallel:"平行坐标图",lines:"线图",graph:"关系图",sankey:"桑基图",funnel:"漏斗图",gauge:"仪表盘图",pictorialBar:"象形柱图",themeRiver:"主题河流图",sunburst:"旭日图",custom:"自定义图表",chart:"图表"}},aria:{general:{withTitle:"这是一个关于“{title}”的图表。",withoutTitle:"这是一个图表，"},series:{single:{prefix:"",withName:"图表类型是{seriesType}，表示{seriesName}。",withoutName:"图表类型是{seriesType}。"},multiple:{prefix:"它由{seriesCount}个图表系列组成。",withName:"第{seriesId}个系列是一个表示{seriesName}的{seriesType}，",withoutName:"第{seriesId}个系列是一个{seriesType}，",separator:{middle:"；",end:"。"}}},data:{allData:"其数据是——",partialData:"其中，前{displayCnt}项是——",withName:"{name}的数据是{value}",withoutName:"{value}",separator:{middle:"，",end:""}}}});var _h=1e3,xh=6e4,wh=36e5,bh=864e5,Sh=31536e6,Mh={year:"{yyyy}",month:"{MMM}",day:"{d}",hour:"{HH}:{mm}",minute:"{HH}:{mm}",second:"{HH}:{mm}:{ss}",millisecond:"{HH}:{mm}:{ss} {SSS}",none:"{yyyy}-{MM}-{dd} {HH}:{mm}:{ss} {SSS}"},Th="{yyyy}-{MM}-{dd}",Ch={year:"{yyyy}",month:"{yyyy}-{MM}",day:Th,hour:Th+" "+Mh.hour,minute:Th+" "+Mh.minute,second:Th+" "+Mh.second,millisecond:Mh.none},Dh=["year","month","day","hour","minute","second","millisecond"],Ih=["year","half-year","quarter","month","week","half-week","day","half-day","quarter-day","hour","minute","second","millisecond"];function kh(t,e){return"0000".substr(0,e-(t+="").length)+t}function Ah(t){switch(t){case"half-year":case"quarter":return"month";case"week":case"half-week":return"day";case"half-day":case"quarter-day":return"hour";default:return t}}function Lh(t){return t===Ah(t)}function Ph(t,e,n,i){var r=Ir(t),o=r[Nh(n)](),a=r[Eh(n)]()+1,s=Math.floor((a-1)/3)+1,l=r[zh(n)](),u=r["get"+(n?"UTC":"")+"Day"](),h=r[Bh(n)](),c=(h-1)%12+1,p=r[Fh(n)](),f=r[Vh(n)](),d=r[Hh(n)](),g=h>=12?"pm":"am",y=g.toUpperCase(),v=i instanceof uh?i:function(t){return yh[t]}(i||vh)||yh[dh],m=v.getModel("time"),_=m.get("month"),x=m.get("monthAbbr"),w=m.get("dayOfWeek"),b=m.get("dayOfWeekAbbr");return(e||"").replace(/{a}/g,g+"").replace(/{A}/g,y+"").replace(/{yyyy}/g,o+"").replace(/{yy}/g,kh(o%100+"",2)).replace(/{Q}/g,s+"").replace(/{MMMM}/g,_[a-1]).replace(/{MMM}/g,x[a-1]).replace(/{MM}/g,kh(a,2)).replace(/{M}/g,a+"").replace(/{dd}/g,kh(l,2)).replace(/{d}/g,l+"").replace(/{eeee}/g,w[u]).replace(/{ee}/g,b[u]).replace(/{e}/g,u+"").replace(/{HH}/g,kh(h,2)).replace(/{H}/g,h+"").replace(/{hh}/g,kh(c+"",2)).replace(/{h}/g,c+"").replace(/{mm}/g,kh(p,2)).replace(/{m}/g,p+"").replace(/{ss}/g,kh(f,2)).replace(/{s}/g,f+"").replace(/{SSS}/g,kh(d,3)).replace(/{S}/g,d+"")}function Oh(t,e){var n=Ir(t),i=n[Eh(e)]()+1,r=n[zh(e)](),o=n[Bh(e)](),a=n[Fh(e)](),s=n[Vh(e)](),l=0===n[Hh(e)](),u=l&&0===s,h=u&&0===a,c=h&&0===o,p=c&&1===r;return p&&1===i?"year":p?"month":c?"day":h?"hour":u?"minute":l?"second":"millisecond"}function Rh(t,e,n){var i=X(t)?Ir(t):t;switch(e=e||Oh(t,n)){case"year":return i[Nh(n)]();case"half-year":return i[Eh(n)]()>=6?1:0;case"quarter":return Math.floor((i[Eh(n)]()+1)/4);case"month":return i[Eh(n)]();case"day":return i[zh(n)]();case"half-day":return i[Bh(n)]()/24;case"hour":return i[Bh(n)]();case"minute":return i[Fh(n)]();case"second":return i[Vh(n)]();case"millisecond":return i[Hh(n)]()}}function Nh(t){return t?"getUTCFullYear":"getFullYear"}function Eh(t){return t?"getUTCMonth":"getMonth"}function zh(t){return t?"getUTCDate":"getDate"}function Bh(t){return t?"getUTCHours":"getHours"}function Fh(t){return t?"getUTCMinutes":"getMinutes"}function Vh(t){return t?"getUTCSeconds":"getSeconds"}function Hh(t){return t?"getUTCMilliseconds":"getMilliseconds"}function Wh(t){return t?"setUTCFullYear":"setFullYear"}function Gh(t){return t?"setUTCMonth":"setMonth"}function Uh(t){return t?"setUTCDate":"setDate"}function Xh(t){return t?"setUTCHours":"setHours"}function Yh(t){return t?"setUTCMinutes":"setMinutes"}function qh(t){return t?"setUTCSeconds":"setSeconds"}function Zh(t){return t?"setUTCMilliseconds":"setMilliseconds"}function jh(t){if(!Or(t))return G(t)?t:"-";var e=(t+"").split(".");return e[0].replace(/(\d{1,3})(?=(?:\d{3})+(?!\d))/g,"$1,")+(e.length>1?"."+e[1]:"")}var Kh=rt,$h=["a","b","c","d","e","f","g"],Qh=function(t,e){return"{"+t+(null==e?"":e)+"}"};function Jh(t,e,n){H(e)||(e=[e]);var i=e.length;if(!i)return"";for(var r=e[0].$vars||[],o=0;o<r.length;o++){var a=$h[o];t=t.replace(Qh(a),Qh(a,0))}for(var s=0;s<i;s++)for(var l=0;l<r.length;l++){var u=e[s][r[l]];t=t.replace(Qh($h[l],s),n?$t(u):u)}return t}function tc(t,e){return e=e||"transparent",G(t)?t:Y(t)&&t.colorStops&&(t.colorStops[0]||{}).color||e}var ec=R,nc=["left","right","top","bottom","width","height"],ic=[["width","left","right"],["height","top","bottom"]];function rc(t,e,n,i,r){var o=0,a=0;null==i&&(i=1/0),null==r&&(r=1/0);var s=0;e.eachChild((function(l,u){var h,c,p=l.getBoundingRect(),f=e.childAt(u+1),d=f&&f.getBoundingRect();if("horizontal"===t){var g=p.width+(d?-d.x+p.x:0);(h=o+g)>i||l.newline?(o=0,h=g,a+=s+n,s=p.height):s=Math.max(s,p.height)}else{var y=p.height+(d?-d.y+p.y:0);(c=a+y)>r||l.newline?(o+=s+n,a=0,c=y,s=p.width):s=Math.max(s,p.width)}l.newline||(l.x=o,l.y=a,l.markRedraw(),"horizontal"===t?o=h+n:a=c+n)}))}V(rc,"vertical"),V(rc,"horizontal");function oc(t,e,n){n=Kh(n||0);var i=e.width,r=e.height,o=mr(t.left,i),a=mr(t.top,r),s=mr(t.right,i),l=mr(t.bottom,r),u=mr(t.width,i),h=mr(t.height,r),c=n[2]+n[0],p=n[1]+n[3],f=t.aspect;switch(isNaN(u)&&(u=i-s-p-o),isNaN(h)&&(h=r-l-c-a),null!=f&&(isNaN(u)&&isNaN(h)&&(f>i/r?u=.8*i:h=.8*r),isNaN(u)&&(u=f*h),isNaN(h)&&(h=u/f)),isNaN(o)&&(o=i-s-u-p),isNaN(a)&&(a=r-l-h-c),t.left||t.right){case"center":o=i/2-u/2-n[3];break;case"right":o=i-u-p}switch(t.top||t.bottom){case"middle":case"center":a=r/2-h/2-n[0];break;case"bottom":a=r-h-c}o=o||0,a=a||0,isNaN(u)&&(u=i-p-o-(s||0)),isNaN(h)&&(h=r-c-a-(l||0));var d=new Ce(o+n[3],a+n[0],u,h);return d.margin=n,d}function ac(t){var e=t.layoutMode||t.constructor.layoutMode;return Y(e)?e:e?{type:e}:null}function sc(t,e,n){var i=n&&n.ignoreSize;!H(i)&&(i=[i,i]);var r=a(ic[0],0),o=a(ic[1],1);function a(n,r){var o={},a=0,u={},h=0;if(ec(n,(function(e){u[e]=t[e]})),ec(n,(function(t){s(e,t)&&(o[t]=u[t]=e[t]),l(o,t)&&a++,l(u,t)&&h++})),i[r])return l(e,n[1])?u[n[2]]=null:l(e,n[2])&&(u[n[1]]=null),u;if(2!==h&&a){if(a>=2)return o;for(var c=0;c<n.length;c++){var p=n[c];if(!s(o,p)&&s(t,p)){o[p]=t[p];break}}return o}return u}function s(t,e){return t.hasOwnProperty(e)}function l(t,e){return null!=t[e]&&"auto"!==t[e]}function u(t,e,n){ec(t,(function(t){e[t]=n[t]}))}u(ic[0],t,r),u(ic[1],t,o)}function lc(t){return function(t,e){return e&&t&&ec(nc,(function(n){e.hasOwnProperty(n)&&(t[n]=e[n])})),t}({},t)}var uc=Qr(),hc=function(t){function e(e,n,i){var r=t.call(this,e,n,i)||this;return r.uid=ch("ec_cpt_model"),r}return n(e,t),e.prototype.init=function(t,e,n){this.mergeDefaultAndTheme(t,n)},e.prototype.mergeDefaultAndTheme=function(t,e){var n=ac(this),i=n?lc(t):{};C(t,e.getTheme().get(this.mainType)),C(t,this.getDefaultOption()),n&&sc(t,i,n)},e.prototype.mergeOption=function(t,e){C(this.option,t,!0);var n=ac(this);n&&sc(this.option,t,n)},e.prototype.optionUpdated=function(t,e){},e.prototype.getDefaultOption=function(){var t=this.constructor;if(!function(t){return!(!t||!t[ao])}(t))return t.defaultOption;var e=uc(this);if(!e.defaultOption){for(var n=[],i=t;i;){var r=i.prototype.defaultOption;r&&n.push(r),i=i.superClass}for(var o={},a=n.length-1;a>=0;a--)o=C(o,n[a],!0);e.defaultOption=o}return e.defaultOption},e.prototype.getReferringComponents=function(t,e){var n=t+"Index",i=t+"Id";return io(this.ecModel,t,{index:this.get(n,!0),id:this.get(i,!0)},e)},e.prototype.getBoxLayoutParams=function(){var t=this;return{left:t.get("left"),top:t.get("top"),right:t.get("right"),bottom:t.get("bottom"),width:t.get("width"),height:t.get("height")}},e.prototype.getZLevelKey=function(){return""},e.prototype.setZLevel=function(t){this.option.zlevel=t},e.protoInitialize=function(){var t=e.prototype;t.type="component",t.id="",t.name="",t.mainType="",t.subType="",t.componentIndex=0}(),e}(uh);uo(hc,uh),fo(hc),function(t){var e={};t.registerSubTypeDefaulter=function(t,n){var i=so(t);e[i.main]=n},t.determineSubType=function(n,i){var r=i.type;if(!r){var o=so(n).main;t.hasSubTypes(n)&&e[o]&&(r=e[o](i))}return r}}(hc),function(t,e){function n(t,e){return t[e]||(t[e]={predecessor:[],successor:[]}),t[e]}t.topologicalTravel=function(t,i,r,o){if(t.length){var a=function(t){var i={},r=[];return R(t,(function(o){var a=n(i,o),s=function(t,e){var n=[];return R(t,(function(t){A(e,t)>=0&&n.push(t)})),n}(a.originalDeps=e(o),t);a.entryCount=s.length,0===a.entryCount&&r.push(o),R(s,(function(t){A(a.predecessor,t)<0&&a.predecessor.push(t);var e=n(i,t);A(e.successor,t)<0&&e.successor.push(o)}))})),{graph:i,noEntryList:r}}(i),s=a.graph,l=a.noEntryList,u={};for(R(t,(function(t){u[t]=!0}));l.length;){var h=l.pop(),c=s[h],p=!!u[h];p&&(r.call(o,h,c.originalDeps.slice()),delete u[h]),R(c.successor,p?d:f)}R(u,(function(){var t="";throw new Error(t)}))}function f(t){s[t].entryCount--,0===s[t].entryCount&&l.push(t)}function d(t){u[t]=!0,f(t)}}}(hc,(function(t){var e=[];R(hc.getClassesByMainType(t),(function(t){e=e.concat(t.dependencies||t.prototype.dependencies||[])})),e=N(e,(function(t){return so(t).main})),"dataset"!==t&&A(e,"dataset")<=0&&e.unshift("dataset");return e}));var cc="";"undefined"!=typeof navigator&&(cc=navigator.platform||"");var pc="rgba(0, 0, 0, 0.2)",fc={darkMode:"auto",colorBy:"series",color:["#5470c6","#91cc75","#fac858","#ee6666","#73c0de","#3ba272","#fc8452","#9a60b4","#ea7ccc"],gradientColor:["#f6efa6","#d88273","#bf444c"],aria:{decal:{decals:[{color:pc,dashArrayX:[1,0],dashArrayY:[2,5],symbolSize:1,rotation:Math.PI/6},{color:pc,symbol:"circle",dashArrayX:[[8,8],[0,8,8,0]],dashArrayY:[6,0],symbolSize:.8},{color:pc,dashArrayX:[1,0],dashArrayY:[4,3],rotation:-Math.PI/4},{color:pc,dashArrayX:[[6,6],[0,6,6,0]],dashArrayY:[6,0]},{color:pc,dashArrayX:[[1,0],[1,6]],dashArrayY:[1,0,6,0],rotation:Math.PI/4},{color:pc,symbol:"triangle",dashArrayX:[[9,9],[0,9,9,0]],dashArrayY:[7,2],symbolSize:.75}]}},textStyle:{fontFamily:cc.match(/^Win/)?"Microsoft YaHei":"sans-serif",fontSize:12,fontStyle:"normal",fontWeight:"normal"},blendMode:null,stateAnimation:{duration:300,easing:"cubicOut"},animation:"auto",animationDuration:1e3,animationDurationUpdate:500,animationEasing:"cubicInOut",animationEasingUpdate:"cubicInOut",animationThreshold:2e3,progressiveThreshold:3e3,progressive:400,hoverLayerThreshold:3e3,useUTC:!1},dc=ft(["tooltip","label","itemName","itemId","itemGroupId","itemChildGroupId","seriesName"]),gc="original",yc="arrayRows",vc="objectRows",mc="keyedColumns",_c="typedArray",xc="unknown",wc="column",bc="row",Sc=1,Mc=2,Tc=3,Cc=Qr();function Dc(t,e,n){var i={},r=kc(e);if(!r||!t)return i;var o,a,s=[],l=[],u=e.ecModel,h=Cc(u).datasetMap,c=r.uid+"_"+n.seriesLayoutBy;R(t=t.slice(),(function(e,n){var r=Y(e)?e:t[n]={name:e};"ordinal"===r.type&&null==o&&(o=n,a=d(r)),i[r.name]=[]}));var p=h.get(c)||h.set(c,{categoryWayDim:a,valueWayDim:0});function f(t,e,n){for(var i=0;i<n;i++)t.push(e+i)}function d(t){var e=t.dimsDef;return e?e.length:1}return R(t,(function(t,e){var n=t.name,r=d(t);if(null==o){var a=p.valueWayDim;f(i[n],a,r),f(l,a,r),p.valueWayDim+=r}else if(o===e)f(i[n],0,r),f(s,0,r);else{a=p.categoryWayDim;f(i[n],a,r),f(l,a,r),p.categoryWayDim+=r}})),s.length&&(i.itemName=s),l.length&&(i.seriesName=l),i}function Ic(t,e,n){var i={};if(!kc(t))return i;var r,o=e.sourceFormat,a=e.dimensionsDefine;o!==vc&&o!==mc||R(a,(function(t,e){"name"===(Y(t)?t.name:t)&&(r=e)}));var s=function(){for(var t={},i={},s=[],l=0,u=Math.min(5,n);l<u;l++){var h=Lc(e.data,o,e.seriesLayoutBy,a,e.startIndex,l);s.push(h);var c=h===Tc;if(c&&null==t.v&&l!==r&&(t.v=l),(null==t.n||t.n===t.v||!c&&s[t.n]===Tc)&&(t.n=l),p(t)&&s[t.n]!==Tc)return t;c||(h===Mc&&null==i.v&&l!==r&&(i.v=l),null!=i.n&&i.n!==i.v||(i.n=l))}function p(t){return null!=t.v&&null!=t.n}return p(t)?t:p(i)?i:null}();if(s){i.value=[s.v];var l=null!=r?r:s.n;i.itemName=[l],i.seriesName=[l]}return i}function kc(t){if(!t.get("data",!0))return io(t.ecModel,"dataset",{index:t.get("datasetIndex",!0),id:t.get("datasetId",!0)},no).models[0]}function Ac(t,e){return Lc(t.data,t.sourceFormat,t.seriesLayoutBy,t.dimensionsDefine,t.startIndex,e)}function Lc(t,e,n,i,r,o){var a,s,l;if(Z(t))return Tc;if(i){var u=i[o];Y(u)?(s=u.name,l=u.type):G(u)&&(s=u)}if(null!=l)return"ordinal"===l?Sc:Tc;if(e===yc){var h=t;if(n===bc){for(var c=h[o],p=0;p<(c||[]).length&&p<5;p++)if(null!=(a=m(c[r+p])))return a}else for(p=0;p<h.length&&p<5;p++){var f=h[r+p];if(f&&null!=(a=m(f[o])))return a}}else if(e===vc){var d=t;if(!s)return Tc;for(p=0;p<d.length&&p<5;p++){if((y=d[p])&&null!=(a=m(y[s])))return a}}else if(e===mc){if(!s)return Tc;if(!(c=t[s])||Z(c))return Tc;for(p=0;p<c.length&&p<5;p++)if(null!=(a=m(c[p])))return a}else if(e===gc){var g=t;for(p=0;p<g.length&&p<5;p++){var y,v=Gr(y=g[p]);if(!H(v))return Tc;if(null!=(a=m(v[o])))return a}}function m(t){var e=G(t);return null!=t&&Number.isFinite(Number(t))&&""!==t?e?Mc:Tc:e&&"-"!==t?Sc:void 0}return Tc}var Pc=ft();var Oc,Rc,Nc,Ec=Qr(),zc=Qr(),Bc=function(){function t(){}return t.prototype.getColorFromPalette=function(t,e,n){var i=Vr(this.get("color",!0)),r=this.get("colorLayer",!0);return Vc(this,Ec,i,r,t,e,n)},t.prototype.clearColorPalette=function(){!function(t,e){e(t).paletteIdx=0,e(t).paletteNameMap={}}(this,Ec)},t}();function Fc(t,e,n,i){var r=Vr(t.get(["aria","decal","decals"]));return Vc(t,zc,r,null,e,n,i)}function Vc(t,e,n,i,r,o,a){var s=e(o=o||t),l=s.paletteIdx||0,u=s.paletteNameMap=s.paletteNameMap||{};if(u.hasOwnProperty(r))return u[r];var h=null!=a&&i?function(t,e){for(var n=t.length,i=0;i<n;i++)if(t[i].length>e)return t[i];return t[n-1]}(i,a):n;if((h=h||n)&&h.length){var c=h[l];return r&&(u[r]=c),s.paletteIdx=(l+1)%h.length,c}}var Hc="\0_ec_inner";var Wc=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.init=function(t,e,n,i,r,o){i=i||{},this.option=null,this._theme=new uh(i),this._locale=new uh(r),this._optionManager=o},e.prototype.setOption=function(t,e,n){var i=Xc(e);this._optionManager.setOption(t,n,i),this._resetOption(null,i)},e.prototype.resetOption=function(t,e){return this._resetOption(t,Xc(e))},e.prototype._resetOption=function(t,e){var n=!1,i=this._optionManager;if(!t||"recreate"===t){var r=i.mountOption("recreate"===t);0,this.option&&"recreate"!==t?(this.restoreData(),this._mergeOption(r,e)):Nc(this,r),n=!0}if("timeline"!==t&&"media"!==t||this.restoreData(),!t||"recreate"===t||"timeline"===t){var o=i.getTimelineOption(this);o&&(n=!0,this._mergeOption(o,e))}if(!t||"recreate"===t||"media"===t){var a=i.getMediaOption(this);a.length&&R(a,(function(t){n=!0,this._mergeOption(t,e)}),this)}return n},e.prototype.mergeOption=function(t){this._mergeOption(t,null)},e.prototype._mergeOption=function(t,e){var n=this.option,i=this._componentsMap,r=this._componentsCount,o=[],a=ft(),s=e&&e.replaceMergeMainTypeMap;Cc(this).datasetMap=ft(),R(t,(function(t,e){null!=t&&(hc.hasClass(e)?e&&(o.push(e),a.set(e,!0)):n[e]=null==n[e]?T(t):C(n[e],t,!0))})),s&&s.each((function(t,e){hc.hasClass(e)&&!a.get(e)&&(o.push(e),a.set(e,!0))})),hc.topologicalTravel(o,hc.getAllClassMainTypes(),(function(e){var o=function(t,e,n){var i=Pc.get(e);if(!i)return n;var r=i(t);return r?n.concat(r):n}(this,e,Vr(t[e])),a=i.get(e),l=a?s&&s.get(e)?"replaceMerge":"normalMerge":"replaceAll",u=Xr(a,o,l);(function(t,e,n){R(t,(function(t){var i=t.newOption;Y(i)&&(t.keyInfo.mainType=e,t.keyInfo.subType=function(t,e,n,i){return e.type?e.type:n?n.subType:i.determineSubType(t,e)}(e,i,t.existing,n))}))})(u,e,hc),n[e]=null,i.set(e,null),r.set(e,0);var h,c=[],p=[],f=0;R(u,(function(t,n){var i=t.existing,r=t.newOption;if(r){var o="series"===e,a=hc.getClass(e,t.keyInfo.subType,!o);if(!a)return;if("tooltip"===e){if(h)return void 0;h=!0}if(i&&i.constructor===a)i.name=t.keyInfo.name,i.mergeOption(r,this),i.optionUpdated(r,!1);else{var s=D({componentIndex:n},t.keyInfo);D(i=new a(r,this,this,s),s),t.brandNew&&(i.__requireNewView=!0),i.init(r,this,this),i.optionUpdated(null,!0)}}else i&&(i.mergeOption({},this),i.optionUpdated({},!1));i?(c.push(i.option),p.push(i),f++):(c.push(void 0),p.push(void 0))}),this),n[e]=c,i.set(e,p),r.set(e,f),"series"===e&&Oc(this)}),this),this._seriesIndices||Oc(this)},e.prototype.getOption=function(){var t=T(this.option);return R(t,(function(e,n){if(hc.hasClass(n)){for(var i=Vr(e),r=i.length,o=!1,a=r-1;a>=0;a--)i[a]&&!Kr(i[a])?o=!0:(i[a]=null,!o&&r--);i.length=r,t[n]=i}})),delete t[Hc],t},e.prototype.getTheme=function(){return this._theme},e.prototype.getLocaleModel=function(){return this._locale},e.prototype.setUpdatePayload=function(t){this._payload=t},e.prototype.getUpdatePayload=function(){return this._payload},e.prototype.getComponent=function(t,e){var n=this._componentsMap.get(t);if(n){var i=n[e||0];if(i)return i;if(null==e)for(var r=0;r<n.length;r++)if(n[r])return n[r]}},e.prototype.queryComponents=function(t){var e=t.mainType;if(!e)return[];var n,i=t.index,r=t.id,o=t.name,a=this._componentsMap.get(e);return a&&a.length?(null!=i?(n=[],R(Vr(i),(function(t){a[t]&&n.push(a[t])}))):n=null!=r?Gc("id",r,a):null!=o?Gc("name",o,a):z(a,(function(t){return!!t})),Uc(n,t)):[]},e.prototype.findComponents=function(t){var e,n,i,r,o,a=t.query,s=t.mainType,l=(n=s+"Index",i=s+"Id",r=s+"Name",!(e=a)||null==e[n]&&null==e[i]&&null==e[r]?null:{mainType:s,index:e[n],id:e[i],name:e[r]}),u=l?this.queryComponents(l):z(this._componentsMap.get(s),(function(t){return!!t}));return o=Uc(u,t),t.filter?z(o,t.filter):o},e.prototype.eachComponent=function(t,e,n){var i=this._componentsMap;if(W(t)){var r=e,o=t;i.each((function(t,e){for(var n=0;t&&n<t.length;n++){var i=t[n];i&&o.call(r,e,i,i.componentIndex)}}))}else for(var a=G(t)?i.get(t):Y(t)?this.findComponents(t):null,s=0;a&&s<a.length;s++){var l=a[s];l&&e.call(n,l,l.componentIndex)}},e.prototype.getSeriesByName=function(t){var e=Zr(t,null);return z(this._componentsMap.get("series"),(function(t){return!!t&&null!=e&&t.name===e}))},e.prototype.getSeriesByIndex=function(t){return this._componentsMap.get("series")[t]},e.prototype.getSeriesByType=function(t){return z(this._componentsMap.get("series"),(function(e){return!!e&&e.subType===t}))},e.prototype.getSeries=function(){return z(this._componentsMap.get("series"),(function(t){return!!t}))},e.prototype.getSeriesCount=function(){return this._componentsCount.get("series")},e.prototype.eachSeries=function(t,e){Rc(this),R(this._seriesIndices,(function(n){var i=this._componentsMap.get("series")[n];t.call(e,i,n)}),this)},e.prototype.eachRawSeries=function(t,e){R(this._componentsMap.get("series"),(function(n){n&&t.call(e,n,n.componentIndex)}))},e.prototype.eachSeriesByType=function(t,e,n){Rc(this),R(this._seriesIndices,(function(i){var r=this._componentsMap.get("series")[i];r.subType===t&&e.call(n,r,i)}),this)},e.prototype.eachRawSeriesByType=function(t,e,n){return R(this.getSeriesByType(t),e,n)},e.prototype.isSeriesFiltered=function(t){return Rc(this),null==this._seriesIndicesMap.get(t.componentIndex)},e.prototype.getCurrentSeriesIndices=function(){return(this._seriesIndices||[]).slice()},e.prototype.filterSeries=function(t,e){Rc(this);var n=[];R(this._seriesIndices,(function(i){var r=this._componentsMap.get("series")[i];t.call(e,r,i)&&n.push(i)}),this),this._seriesIndices=n,this._seriesIndicesMap=ft(n)},e.prototype.restoreData=function(t){Oc(this);var e=this._componentsMap,n=[];e.each((function(t,e){hc.hasClass(e)&&n.push(e)})),hc.topologicalTravel(n,hc.getAllClassMainTypes(),(function(n){R(e.get(n),(function(e){!e||"series"===n&&function(t,e){if(e){var n=e.seriesIndex,i=e.seriesId,r=e.seriesName;return null!=n&&t.componentIndex!==n||null!=i&&t.id!==i||null!=r&&t.name!==r}}(e,t)||e.restoreData()}))}))},e.internalField=(Oc=function(t){var e=t._seriesIndices=[];R(t._componentsMap.get("series"),(function(t){t&&e.push(t.componentIndex)})),t._seriesIndicesMap=ft(e)},Rc=function(t){},void(Nc=function(t,e){t.option={},t.option[Hc]=1,t._componentsMap=ft({series:[]}),t._componentsCount=ft();var n=e.aria;Y(n)&&null==n.enabled&&(n.enabled=!0),function(t,e){var n=t.color&&!t.colorLayer;R(e,(function(e,i){"colorLayer"===i&&n||hc.hasClass(i)||("object"==typeof e?t[i]=t[i]?C(t[i],e,!1):T(e):null==t[i]&&(t[i]=e))}))}(e,t._theme.option),C(e,fc,!1),t._mergeOption(e,null)})),e}(uh);function Gc(t,e,n){if(H(e)){var i=ft();return R(e,(function(t){null!=t&&(null!=Zr(t,null)&&i.set(t,!0))})),z(n,(function(e){return e&&i.get(e[t])}))}var r=Zr(e,null);return z(n,(function(e){return e&&null!=r&&e[t]===r}))}function Uc(t,e){return e.hasOwnProperty("subType")?z(t,(function(t){return t&&t.subType===e.subType})):t}function Xc(t){var e=ft();return t&&R(Vr(t.replaceMerge),(function(t){e.set(t,!0)})),{replaceMergeMainTypeMap:e}}P(Wc,Bc);var Yc=["getDom","getZr","getWidth","getHeight","getDevicePixelRatio","dispatchAction","isSSR","isDisposed","on","off","getDataURL","getConnectedDataURL","getOption","getId","updateLabelLayout"],qc=function(t){R(Yc,(function(e){this[e]=F(t[e],t)}),this)},Zc={},jc=function(){function t(){this._coordinateSystems=[]}return t.prototype.create=function(t,e){var n=[];R(Zc,(function(i,r){var o=i.create(t,e);n=n.concat(o||[])})),this._coordinateSystems=n},t.prototype.update=function(t,e){R(this._coordinateSystems,(function(n){n.update&&n.update(t,e)}))},t.prototype.getCoordinateSystems=function(){return this._coordinateSystems.slice()},t.register=function(t,e){Zc[t]=e},t.get=function(t){return Zc[t]},t}(),Kc=/^(min|max)?(.+)$/,$c=function(){function t(t){this._timelineOptions=[],this._mediaList=[],this._currentMediaIndices=[],this._api=t}return t.prototype.setOption=function(t,e,n){t&&(R(Vr(t.series),(function(t){t&&t.data&&Z(t.data)&&lt(t.data)})),R(Vr(t.dataset),(function(t){t&&t.source&&Z(t.source)&&lt(t.source)}))),t=T(t);var i=this._optionBackup,r=function(t,e,n){var i,r,o=[],a=t.baseOption,s=t.timeline,l=t.options,u=t.media,h=!!t.media,c=!!(l||s||a&&a.timeline);a?(r=a).timeline||(r.timeline=s):((c||h)&&(t.options=t.media=null),r=t);h&&H(u)&&R(u,(function(t){t&&t.option&&(t.query?o.push(t):i||(i=t))}));function p(t){R(e,(function(e){e(t,n)}))}return p(r),R(l,(function(t){return p(t)})),R(o,(function(t){return p(t.option)})),{baseOption:r,timelineOptions:l||[],mediaDefault:i,mediaList:o}}(t,e,!i);this._newBaseOption=r.baseOption,i?(r.timelineOptions.length&&(i.timelineOptions=r.timelineOptions),r.mediaList.length&&(i.mediaList=r.mediaList),r.mediaDefault&&(i.mediaDefault=r.mediaDefault)):this._optionBackup=r},t.prototype.mountOption=function(t){var e=this._optionBackup;return this._timelineOptions=e.timelineOptions,this._mediaList=e.mediaList,this._mediaDefault=e.mediaDefault,this._currentMediaIndices=[],T(t?e.baseOption:this._newBaseOption)},t.prototype.getTimelineOption=function(t){var e,n=this._timelineOptions;if(n.length){var i=t.getComponent("timeline");i&&(e=T(n[i.getCurrentIndex()]))}return e},t.prototype.getMediaOption=function(t){var e,n,i=this._api.getWidth(),r=this._api.getHeight(),o=this._mediaList,a=this._mediaDefault,s=[],l=[];if(!o.length&&!a)return l;for(var u=0,h=o.length;u<h;u++)Qc(o[u].query,i,r)&&s.push(u);return!s.length&&a&&(s=[-1]),s.length&&(e=s,n=this._currentMediaIndices,e.join(",")!==n.join(","))&&(l=N(s,(function(t){return T(-1===t?a.option:o[t].option)}))),this._currentMediaIndices=s,l},t}();function Qc(t,e,n){var i={width:e,height:n,aspectratio:e/n},r=!0;return R(t,(function(t,e){var n=e.match(Kc);if(n&&n[1]&&n[2]){var o=n[1],a=n[2].toLowerCase();(function(t,e,n){return"min"===n?t>=e:"max"===n?t<=e:t===e})(i[a],t,o)||(r=!1)}})),r}var Jc=R,tp=Y,ep=["areaStyle","lineStyle","nodeStyle","linkStyle","chordStyle","label","labelLine"];function np(t){var e=t&&t.itemStyle;if(e)for(var n=0,i=ep.length;n<i;n++){var r=ep[n],o=e.normal,a=e.emphasis;o&&o[r]&&(t[r]=t[r]||{},t[r].normal?C(t[r].normal,o[r]):t[r].normal=o[r],o[r]=null),a&&a[r]&&(t[r]=t[r]||{},t[r].emphasis?C(t[r].emphasis,a[r]):t[r].emphasis=a[r],a[r]=null)}}function ip(t,e,n){if(t&&t[e]&&(t[e].normal||t[e].emphasis)){var i=t[e].normal,r=t[e].emphasis;i&&(n?(t[e].normal=t[e].emphasis=null,I(t[e],i)):t[e]=i),r&&(t.emphasis=t.emphasis||{},t.emphasis[e]=r,r.focus&&(t.emphasis.focus=r.focus),r.blurScope&&(t.emphasis.blurScope=r.blurScope))}}function rp(t){ip(t,"itemStyle"),ip(t,"lineStyle"),ip(t,"areaStyle"),ip(t,"label"),ip(t,"labelLine"),ip(t,"upperLabel"),ip(t,"edgeLabel")}function op(t,e){var n=tp(t)&&t[e],i=tp(n)&&n.textStyle;if(i){0;for(var r=0,o=Wr.length;r<o;r++){var a=Wr[r];i.hasOwnProperty(a)&&(n[a]=i[a])}}}function ap(t){t&&(rp(t),op(t,"label"),t.emphasis&&op(t.emphasis,"label"))}function sp(t){return H(t)?t:t?[t]:[]}function lp(t){return(H(t)?t[0]:t)||{}}function up(t,e){Jc(sp(t.series),(function(t){tp(t)&&function(t){if(tp(t)){np(t),rp(t),op(t,"label"),op(t,"upperLabel"),op(t,"edgeLabel"),t.emphasis&&(op(t.emphasis,"label"),op(t.emphasis,"upperLabel"),op(t.emphasis,"edgeLabel"));var e=t.markPoint;e&&(np(e),ap(e));var n=t.markLine;n&&(np(n),ap(n));var i=t.markArea;i&&ap(i);var r=t.data;if("graph"===t.type){r=r||t.nodes;var o=t.links||t.edges;if(o&&!Z(o))for(var a=0;a<o.length;a++)ap(o[a]);R(t.categories,(function(t){rp(t)}))}if(r&&!Z(r))for(a=0;a<r.length;a++)ap(r[a]);if((e=t.markPoint)&&e.data){var s=e.data;for(a=0;a<s.length;a++)ap(s[a])}if((n=t.markLine)&&n.data){var l=n.data;for(a=0;a<l.length;a++)H(l[a])?(ap(l[a][0]),ap(l[a][1])):ap(l[a])}"gauge"===t.type?(op(t,"axisLabel"),op(t,"title"),op(t,"detail")):"treemap"===t.type?(ip(t.breadcrumb,"itemStyle"),R(t.levels,(function(t){rp(t)}))):"tree"===t.type&&rp(t.leaves)}}(t)}));var n=["xAxis","yAxis","radiusAxis","angleAxis","singleAxis","parallelAxis","radar"];e&&n.push("valueAxis","categoryAxis","logAxis","timeAxis"),Jc(n,(function(e){Jc(sp(t[e]),(function(t){t&&(op(t,"axisLabel"),op(t.axisPointer,"label"))}))})),Jc(sp(t.parallel),(function(t){var e=t&&t.parallelAxisDefault;op(e,"axisLabel"),op(e&&e.axisPointer,"label")})),Jc(sp(t.calendar),(function(t){ip(t,"itemStyle"),op(t,"dayLabel"),op(t,"monthLabel"),op(t,"yearLabel")})),Jc(sp(t.radar),(function(t){op(t,"name"),t.name&&null==t.axisName&&(t.axisName=t.name,delete t.name),null!=t.nameGap&&null==t.axisNameGap&&(t.axisNameGap=t.nameGap,delete t.nameGap)})),Jc(sp(t.geo),(function(t){tp(t)&&(ap(t),Jc(sp(t.regions),(function(t){ap(t)})))})),Jc(sp(t.timeline),(function(t){ap(t),ip(t,"label"),ip(t,"itemStyle"),ip(t,"controlStyle",!0);var e=t.data;H(e)&&R(e,(function(t){Y(t)&&(ip(t,"label"),ip(t,"itemStyle"))}))})),Jc(sp(t.toolbox),(function(t){ip(t,"iconStyle"),Jc(t.feature,(function(t){ip(t,"iconStyle")}))})),op(lp(t.axisPointer),"label"),op(lp(t.tooltip).axisPointer,"label")}function hp(t){t&&R(cp,(function(e){e[0]in t&&!(e[1]in t)&&(t[e[1]]=t[e[0]])}))}var cp=[["x","left"],["y","top"],["x2","right"],["y2","bottom"]],pp=["grid","geo","parallel","legend","toolbox","title","visualMap","dataZoom","timeline"],fp=[["borderRadius","barBorderRadius"],["borderColor","barBorderColor"],["borderWidth","barBorderWidth"]];function dp(t){var e=t&&t.itemStyle;if(e)for(var n=0;n<fp.length;n++){var i=fp[n][1],r=fp[n][0];null!=e[i]&&(e[r]=e[i])}}function gp(t){t&&"edge"===t.alignTo&&null!=t.margin&&null==t.edgeDistance&&(t.edgeDistance=t.margin)}function yp(t){t&&t.downplay&&!t.blur&&(t.blur=t.downplay)}function vp(t,e){if(t)for(var n=0;n<t.length;n++)e(t[n]),t[n]&&vp(t[n].children,e)}function mp(t,e){up(t,e),t.series=Vr(t.series),R(t.series,(function(t){if(Y(t)){var e=t.type;if("line"===e)null!=t.clipOverflow&&(t.clip=t.clipOverflow);else if("pie"===e||"gauge"===e){if(null!=t.clockWise&&(t.clockwise=t.clockWise),gp(t.label),(r=t.data)&&!Z(r))for(var n=0;n<r.length;n++)gp(r[n]);null!=t.hoverOffset&&(t.emphasis=t.emphasis||{},(t.emphasis.scaleSize=null)&&(t.emphasis.scaleSize=t.hoverOffset))}else if("gauge"===e){var i=function(t,e){for(var n=e.split(","),i=t,r=0;r<n.length&&null!=(i=i&&i[n[r]]);r++);return i}(t,"pointer.color");null!=i&&function(t,e,n,i){for(var r,o=e.split(","),a=t,s=0;s<o.length-1;s++)null==a[r=o[s]]&&(a[r]={}),a=a[r];(i||null==a[o[s]])&&(a[o[s]]=n)}(t,"itemStyle.color",i)}else if("bar"===e){var r;if(dp(t),dp(t.backgroundStyle),dp(t.emphasis),(r=t.data)&&!Z(r))for(n=0;n<r.length;n++)"object"==typeof r[n]&&(dp(r[n]),dp(r[n]&&r[n].emphasis))}else if("sunburst"===e){var o=t.highlightPolicy;o&&(t.emphasis=t.emphasis||{},t.emphasis.focus||(t.emphasis.focus=o)),yp(t),vp(t.data,yp)}else"graph"===e||"sankey"===e?function(t){t&&null!=t.focusNodeAdjacency&&(t.emphasis=t.emphasis||{},null==t.emphasis.focus&&(t.emphasis.focus="adjacency"))}(t):"map"===e&&(t.mapType&&!t.map&&(t.map=t.mapType),t.mapLocation&&I(t,t.mapLocation));null!=t.hoverAnimation&&(t.emphasis=t.emphasis||{},t.emphasis&&null==t.emphasis.scale&&(t.emphasis.scale=t.hoverAnimation)),hp(t)}})),t.dataRange&&(t.visualMap=t.dataRange),R(pp,(function(e){var n=t[e];n&&(H(n)||(n=[n]),R(n,(function(t){hp(t)})))}))}function _p(t){R(t,(function(e,n){var i=[],r=[NaN,NaN],o=[e.stackResultDimension,e.stackedOverDimension],a=e.data,s=e.isStackedByIndex,l=e.seriesModel.get("stackStrategy")||"samesign";a.modify(o,(function(o,u,h){var c,p,f=a.get(e.stackedDimension,h);if(isNaN(f))return r;s?p=a.getRawIndex(h):c=a.get(e.stackedByDimension,h);for(var d=NaN,g=n-1;g>=0;g--){var y=t[g];if(s||(p=y.data.rawIndexOf(y.stackedByDimension,c)),p>=0){var v=y.data.getByRawIndex(y.stackResultDimension,p);if("all"===l||"positive"===l&&v>0||"negative"===l&&v<0||"samesign"===l&&f>=0&&v>0||"samesign"===l&&f<=0&&v<0){f=Mr(f,v),d=v;break}}}return i[0]=f,i[1]=d,i}))}))}var xp,wp,bp,Sp,Mp,Tp=function(t){this.data=t.data||(t.sourceFormat===mc?{}:[]),this.sourceFormat=t.sourceFormat||xc,this.seriesLayoutBy=t.seriesLayoutBy||wc,this.startIndex=t.startIndex||0,this.dimensionsDetectedCount=t.dimensionsDetectedCount,this.metaRawOption=t.metaRawOption;var e=this.dimensionsDefine=t.dimensionsDefine;if(e)for(var n=0;n<e.length;n++){var i=e[n];null==i.type&&Ac(this,n)===Sc&&(i.type="ordinal")}};function Cp(t){return t instanceof Tp}function Dp(t,e,n){n=n||kp(t);var i=e.seriesLayoutBy,r=function(t,e,n,i,r){var o,a;if(!t)return{dimensionsDefine:Ap(r),startIndex:a,dimensionsDetectedCount:o};if(e===yc){var s=t;"auto"===i||null==i?Lp((function(t){null!=t&&"-"!==t&&(G(t)?null==a&&(a=1):a=0)}),n,s,10):a=X(i)?i:i?1:0,r||1!==a||(r=[],Lp((function(t,e){r[e]=null!=t?t+"":""}),n,s,1/0)),o=r?r.length:n===bc?s.length:s[0]?s[0].length:null}else if(e===vc)r||(r=function(t){var e,n=0;for(;n<t.length&&!(e=t[n++]););if(e)return B(e)}(t));else if(e===mc)r||(r=[],R(t,(function(t,e){r.push(e)})));else if(e===gc){var l=Gr(t[0]);o=H(l)&&l.length||1}return{startIndex:a,dimensionsDefine:Ap(r),dimensionsDetectedCount:o}}(t,n,i,e.sourceHeader,e.dimensions);return new Tp({data:t,sourceFormat:n,seriesLayoutBy:i,dimensionsDefine:r.dimensionsDefine,startIndex:r.startIndex,dimensionsDetectedCount:r.dimensionsDetectedCount,metaRawOption:T(e)})}function Ip(t){return new Tp({data:t,sourceFormat:Z(t)?_c:gc})}function kp(t){var e=xc;if(Z(t))e=_c;else if(H(t)){0===t.length&&(e=yc);for(var n=0,i=t.length;n<i;n++){var r=t[n];if(null!=r){if(H(r)||Z(r)){e=yc;break}if(Y(r)){e=vc;break}}}}else if(Y(t))for(var o in t)if(vt(t,o)&&O(t[o])){e=mc;break}return e}function Ap(t){if(t){var e=ft();return N(t,(function(t,n){var i={name:(t=Y(t)?t:{name:t}).name,displayName:t.displayName,type:t.type};if(null==i.name)return i;i.name+="",null==i.displayName&&(i.displayName=i.name);var r=e.get(i.name);return r?i.name+="-"+r.count++:e.set(i.name,{count:1}),i}))}}function Lp(t,e,n,i){if(e===bc)for(var r=0;r<n.length&&r<i;r++)t(n[r]?n[r][0]:null,r);else{var o=n[0]||[];for(r=0;r<o.length&&r<i;r++)t(o[r],r)}}function Pp(t){var e=t.sourceFormat;return e===vc||e===mc}var Op=function(){function t(t,e){var n=Cp(t)?t:Ip(t);this._source=n;var i=this._data=n.data;n.sourceFormat===_c&&(this._offset=0,this._dimSize=e,this._data=i),Mp(this,i,n)}return t.prototype.getSource=function(){return this._source},t.prototype.count=function(){return 0},t.prototype.getItem=function(t,e){},t.prototype.appendData=function(t){},t.prototype.clean=function(){},t.protoInitialize=function(){var e=t.prototype;e.pure=!1,e.persistent=!0}(),t.internalField=function(){var t;Mp=function(t,r,o){var a=o.sourceFormat,s=o.seriesLayoutBy,l=o.startIndex,u=o.dimensionsDefine,h=Sp[Gp(a,s)];if(D(t,h),a===_c)t.getItem=e,t.count=i,t.fillStorage=n;else{var c=Ep(a,s);t.getItem=F(c,null,r,l,u);var p=Fp(a,s);t.count=F(p,null,r,l,u)}};var e=function(t,e){t-=this._offset,e=e||[];for(var n=this._data,i=this._dimSize,r=i*t,o=0;o<i;o++)e[o]=n[r+o];return e},n=function(t,e,n,i){for(var r=this._data,o=this._dimSize,a=0;a<o;a++){for(var s=i[a],l=null==s[0]?1/0:s[0],u=null==s[1]?-1/0:s[1],h=e-t,c=n[a],p=0;p<h;p++){var f=r[p*o+a];c[t+p]=f,f<l&&(l=f),f>u&&(u=f)}s[0]=l,s[1]=u}},i=function(){return this._data?this._data.length/this._dimSize:0};function r(t){for(var e=0;e<t.length;e++)this._data.push(t[e])}(t={})[yc+"_"+wc]={pure:!0,appendData:r},t[yc+"_"+bc]={pure:!0,appendData:function(){throw new Error('Do not support appendData when set seriesLayoutBy: "row".')}},t[vc]={pure:!0,appendData:r},t[mc]={pure:!0,appendData:function(t){var e=this._data;R(t,(function(t,n){for(var i=e[n]||(e[n]=[]),r=0;r<(t||[]).length;r++)i.push(t[r])}))}},t[gc]={appendData:r},t[_c]={persistent:!1,pure:!0,appendData:function(t){this._data=t},clean:function(){this._offset+=this.count(),this._data=null}},Sp=t}(),t}(),Rp=function(t,e,n,i){return t[i]},Np=((xp={})[yc+"_"+wc]=function(t,e,n,i){return t[i+e]},xp[yc+"_"+bc]=function(t,e,n,i,r){i+=e;for(var o=r||[],a=t,s=0;s<a.length;s++){var l=a[s];o[s]=l?l[i]:null}return o},xp[vc]=Rp,xp[mc]=function(t,e,n,i,r){for(var o=r||[],a=0;a<n.length;a++){var s=n[a].name;0;var l=t[s];o[a]=l?l[i]:null}return o},xp[gc]=Rp,xp);function Ep(t,e){var n=Np[Gp(t,e)];return n}var zp=function(t,e,n){return t.length},Bp=((wp={})[yc+"_"+wc]=function(t,e,n){return Math.max(0,t.length-e)},wp[yc+"_"+bc]=function(t,e,n){var i=t[0];return i?Math.max(0,i.length-e):0},wp[vc]=zp,wp[mc]=function(t,e,n){var i=n[0].name;var r=t[i];return r?r.length:0},wp[gc]=zp,wp);function Fp(t,e){var n=Bp[Gp(t,e)];return n}var Vp=function(t,e,n){return t[e]},Hp=((bp={})[yc]=Vp,bp[vc]=function(t,e,n){return t[n]},bp[mc]=Vp,bp[gc]=function(t,e,n){var i=Gr(t);return i instanceof Array?i[e]:i},bp[_c]=Vp,bp);function Wp(t){var e=Hp[t];return e}function Gp(t,e){return t===yc?t+"_"+e:t}function Up(t,e,n){if(t){var i=t.getRawDataItem(e);if(null!=i){var r=t.getStore(),o=r.getSource().sourceFormat;if(null!=n){var a=t.getDimensionIndex(n),s=r.getDimensionProperty(a);return Wp(o)(i,a,s)}var l=i;return o===gc&&(l=Gr(i)),l}}}var Xp=/\{@(.+?)\}/g,Yp=function(){function t(){}return t.prototype.getDataParams=function(t,e){var n=this.getData(e),i=this.getRawValue(t,e),r=n.getRawIndex(t),o=n.getName(t),a=n.getRawDataItem(t),s=n.getItemVisual(t,"style"),l=s&&s[n.getItemVisual(t,"drawType")||"fill"],u=s&&s.stroke,h=this.mainType,c="series"===h,p=n.userOutput&&n.userOutput.get();return{componentType:h,componentSubType:this.subType,componentIndex:this.componentIndex,seriesType:c?this.subType:null,seriesIndex:this.seriesIndex,seriesId:c?this.id:null,seriesName:c?this.name:null,name:o,dataIndex:r,data:a,dataType:e,value:i,color:l,borderColor:u,dimensionNames:p?p.fullDimensions:null,encode:p?p.encode:null,$vars:["seriesName","name","value"]}},t.prototype.getFormattedLabel=function(t,e,n,i,r,o){e=e||"normal";var a=this.getData(n),s=this.getDataParams(t,n);(o&&(s.value=o.interpolatedValue),null!=i&&H(s.value)&&(s.value=s.value[i]),r)||(r=a.getItemModel(t).get("normal"===e?["label","formatter"]:[e,"label","formatter"]));return W(r)?(s.status=e,s.dimensionIndex=i,r(s)):G(r)?Jh(r,s).replace(Xp,(function(e,n){var i=n.length,r=n;"["===r.charAt(0)&&"]"===r.charAt(i-1)&&(r=+r.slice(1,i-1));var s=Up(a,t,r);if(o&&H(o.interpolatedValue)){var l=a.getDimensionIndex(r);l>=0&&(s=o.interpolatedValue[l])}return null!=s?s+"":""})):void 0},t.prototype.getRawValue=function(t,e){return Up(this.getData(e),t)},t.prototype.formatTooltip=function(t,e,n){},t}();function qp(t){return new Zp(t)}var Zp=function(){function t(t){t=t||{},this._reset=t.reset,this._plan=t.plan,this._count=t.count,this._onDirty=t.onDirty,this._dirty=!0}return t.prototype.perform=function(t){var e,n=this._upstream,i=t&&t.skip;if(this._dirty&&n){var r=this.context;r.data=r.outputData=n.context.outputData}this.__pipeline&&(this.__pipeline.currentTask=this),this._plan&&!i&&(e=this._plan(this.context));var o,a=h(this._modBy),s=this._modDataCount||0,l=h(t&&t.modBy),u=t&&t.modDataCount||0;function h(t){return!(t>=1)&&(t=1),t}a===l&&s===u||(e="reset"),(this._dirty||"reset"===e)&&(this._dirty=!1,o=this._doReset(i)),this._modBy=l,this._modDataCount=u;var c=t&&t.step;if(this._dueEnd=n?n._outputDueEnd:this._count?this._count(this.context):1/0,this._progress){var p=this._dueIndex,f=Math.min(null!=c?this._dueIndex+c:1/0,this._dueEnd);if(!i&&(o||p<f)){var d=this._progress;if(H(d))for(var g=0;g<d.length;g++)this._doProgress(d[g],p,f,l,u);else this._doProgress(d,p,f,l,u)}this._dueIndex=f;var y=null!=this._settedOutputEnd?this._settedOutputEnd:f;0,this._outputDueEnd=y}else this._dueIndex=this._outputDueEnd=null!=this._settedOutputEnd?this._settedOutputEnd:this._dueEnd;return this.unfinished()},t.prototype.dirty=function(){this._dirty=!0,this._onDirty&&this._onDirty(this.context)},t.prototype._doProgress=function(t,e,n,i,r){jp.reset(e,n,i,r),this._callingProgress=t,this._callingProgress({start:e,end:n,count:n-e,next:jp.next},this.context)},t.prototype._doReset=function(t){var e,n;this._dueIndex=this._outputDueEnd=this._dueEnd=0,this._settedOutputEnd=null,!t&&this._reset&&((e=this._reset(this.context))&&e.progress&&(n=e.forceFirstProgress,e=e.progress),H(e)&&!e.length&&(e=null)),this._progress=e,this._modBy=this._modDataCount=null;var i=this._downstream;return i&&i.dirty(),n},t.prototype.unfinished=function(){return this._progress&&this._dueIndex<this._dueEnd},t.prototype.pipe=function(t){(this._downstream!==t||this._dirty)&&(this._downstream=t,t._upstream=this,t.dirty())},t.prototype.dispose=function(){this._disposed||(this._upstream&&(this._upstream._downstream=null),this._downstream&&(this._downstream._upstream=null),this._dirty=!1,this._disposed=!0)},t.prototype.getUpstream=function(){return this._upstream},t.prototype.getDownstream=function(){return this._downstream},t.prototype.setOutputEnd=function(t){this._outputDueEnd=this._settedOutputEnd=t},t}(),jp=function(){var t,e,n,i,r,o={reset:function(l,u,h,c){e=l,t=u,n=h,i=c,r=Math.ceil(i/n),o.next=n>1&&i>0?s:a}};return o;function a(){return e<t?e++:null}function s(){var o=e%r*n+Math.ceil(e/r),a=e>=t?null:o<i?o:e;return e++,a}}();function Kp(t,e){var n=e&&e.type;return"ordinal"===n?t:("time"!==n||X(t)||null==t||"-"===t||(t=+Ir(t)),null==t||""===t?NaN:Number(t))}ft({number:function(t){return parseFloat(t)},time:function(t){return+Ir(t)},trim:function(t){return G(t)?at(t):t}});var $p=function(){function t(){}return t.prototype.getRawData=function(){throw new Error("not supported")},t.prototype.getRawDataItem=function(t){throw new Error("not supported")},t.prototype.cloneRawData=function(){},t.prototype.getDimensionInfo=function(t){},t.prototype.cloneAllDimensionInfo=function(){},t.prototype.count=function(){},t.prototype.retrieveValue=function(t,e){},t.prototype.retrieveValueFromItem=function(t,e){},t.prototype.convertValue=function(t,e){return Kp(t,e)},t}();function Qp(t){var e=t.sourceFormat;if(!of(e)){var n="";0,zr(n)}return t.data}function Jp(t){var e=t.sourceFormat,n=t.data;if(!of(e)){var i="";0,zr(i)}if(e===yc){for(var r=[],o=0,a=n.length;o<a;o++)r.push(n[o].slice());return r}if(e===vc){for(r=[],o=0,a=n.length;o<a;o++)r.push(D({},n[o]));return r}}function tf(t,e,n){if(null!=n)return X(n)||!isNaN(n)&&!vt(e,n)?t[n]:vt(e,n)?e[n]:void 0}function ef(t){return T(t)}var nf=ft();function rf(t,e,n,i){var r="";e.length||zr(r),Y(t)||zr(r);var o=t.type,a=nf.get(o);a||zr(r);var s=N(e,(function(t){return function(t,e){var n=new $p,i=t.data,r=n.sourceFormat=t.sourceFormat,o=t.startIndex,a="";t.seriesLayoutBy!==wc&&zr(a);var s=[],l={},u=t.dimensionsDefine;if(u)R(u,(function(t,e){var n=t.name,i={index:e,name:n,displayName:t.displayName};if(s.push(i),null!=n){var r="";vt(l,n)&&zr(r),l[n]=i}}));else for(var h=0;h<t.dimensionsDetectedCount;h++)s.push({index:h});var c=Ep(r,wc);e.__isBuiltIn&&(n.getRawDataItem=function(t){return c(i,o,s,t)},n.getRawData=F(Qp,null,t)),n.cloneRawData=F(Jp,null,t);var p=Fp(r,wc);n.count=F(p,null,i,o,s);var f=Wp(r);n.retrieveValue=function(t,e){var n=c(i,o,s,t);return d(n,e)};var d=n.retrieveValueFromItem=function(t,e){if(null!=t){var n=s[e];return n?f(t,e,n.name):void 0}};return n.getDimensionInfo=F(tf,null,s,l),n.cloneAllDimensionInfo=F(ef,null,s),n}(t,a)})),l=Vr(a.transform({upstream:s[0],upstreamList:s,config:T(t.config)}));return N(l,(function(t,n){var i,r="";Y(t)||zr(r),t.data||zr(r),of(kp(t.data))||zr(r);var o=e[0];if(o&&0===n&&!t.dimensions){var a=o.startIndex;a&&(t.data=o.data.slice(0,a).concat(t.data)),i={seriesLayoutBy:wc,sourceHeader:a,dimensions:o.metaRawOption.dimensions}}else i={seriesLayoutBy:wc,sourceHeader:0,dimensions:t.dimensions};return Dp(t.data,i,null)}))}function of(t){return t===yc||t===vc}var af,sf="undefined",lf=typeof Uint32Array===sf?Array:Uint32Array,uf=typeof Uint16Array===sf?Array:Uint16Array,hf=typeof Int32Array===sf?Array:Int32Array,cf=typeof Float64Array===sf?Array:Float64Array,pf={float:cf,int:hf,ordinal:Array,number:Array,time:cf};function ff(t){return t>65535?lf:uf}function df(t,e,n,i,r){var o=pf[n||"float"];if(r){var a=t[e],s=a&&a.length;if(s!==i){for(var l=new o(i),u=0;u<s;u++)l[u]=a[u];t[e]=l}}else t[e]=new o(i)}var gf=function(){function t(){this._chunks=[],this._rawExtent=[],this._extent=[],this._count=0,this._rawCount=0,this._calcDimNameToIdx=ft()}return t.prototype.initData=function(t,e,n){this._provider=t,this._chunks=[],this._indices=null,this.getRawIndex=this._getRawIdxIdentity;var i=t.getSource(),r=this.defaultDimValueGetter=af[i.sourceFormat];this._dimValueGetter=n||r,this._rawExtent=[];Pp(i);this._dimensions=N(e,(function(t){return{type:t.type,property:t.property}})),this._initDataFromProvider(0,t.count())},t.prototype.getProvider=function(){return this._provider},t.prototype.getSource=function(){return this._provider.getSource()},t.prototype.ensureCalculationDimension=function(t,e){var n=this._calcDimNameToIdx,i=this._dimensions,r=n.get(t);if(null!=r){if(i[r].type===e)return r}else r=i.length;return i[r]={type:e},n.set(t,r),this._chunks[r]=new pf[e||"float"](this._rawCount),this._rawExtent[r]=[1/0,-1/0],r},t.prototype.collectOrdinalMeta=function(t,e){var n=this._chunks[t],i=this._dimensions[t],r=this._rawExtent,o=i.ordinalOffset||0,a=n.length;0===o&&(r[t]=[1/0,-1/0]);for(var s=r[t],l=o;l<a;l++){var u=n[l]=e.parseAndCollect(n[l]);isNaN(u)||(s[0]=Math.min(u,s[0]),s[1]=Math.max(u,s[1]))}i.ordinalMeta=e,i.ordinalOffset=a,i.type="ordinal"},t.prototype.getOrdinalMeta=function(t){return this._dimensions[t].ordinalMeta},t.prototype.getDimensionProperty=function(t){var e=this._dimensions[t];return e&&e.property},t.prototype.appendData=function(t){var e=this._provider,n=this.count();e.appendData(t);var i=e.count();return e.persistent||(i+=n),n<i&&this._initDataFromProvider(n,i,!0),[n,i]},t.prototype.appendValues=function(t,e){for(var n=this._chunks,i=this._dimensions,r=i.length,o=this._rawExtent,a=this.count(),s=a+Math.max(t.length,e||0),l=0;l<r;l++){df(n,l,(f=i[l]).type,s,!0)}for(var u=[],h=a;h<s;h++)for(var c=h-a,p=0;p<r;p++){var f=i[p],d=af.arrayRows.call(this,t[c]||u,f.property,c,p);n[p][h]=d;var g=o[p];d<g[0]&&(g[0]=d),d>g[1]&&(g[1]=d)}return this._rawCount=this._count=s,{start:a,end:s}},t.prototype._initDataFromProvider=function(t,e,n){for(var i=this._provider,r=this._chunks,o=this._dimensions,a=o.length,s=this._rawExtent,l=N(o,(function(t){return t.property})),u=0;u<a;u++){var h=o[u];s[u]||(s[u]=[1/0,-1/0]),df(r,u,h.type,e,n)}if(i.fillStorage)i.fillStorage(t,e,r,s);else for(var c=[],p=t;p<e;p++){c=i.getItem(p,c);for(var f=0;f<a;f++){var d=r[f],g=this._dimValueGetter(c,l[f],p,f);d[p]=g;var y=s[f];g<y[0]&&(y[0]=g),g>y[1]&&(y[1]=g)}}!i.persistent&&i.clean&&i.clean(),this._rawCount=this._count=e,this._extent=[]},t.prototype.count=function(){return this._count},t.prototype.get=function(t,e){if(!(e>=0&&e<this._count))return NaN;var n=this._chunks[t];return n?n[this.getRawIndex(e)]:NaN},t.prototype.getValues=function(t,e){var n=[],i=[];if(null==e){e=t,t=[];for(var r=0;r<this._dimensions.length;r++)i.push(r)}else i=t;r=0;for(var o=i.length;r<o;r++)n.push(this.get(i[r],e));return n},t.prototype.getByRawIndex=function(t,e){if(!(e>=0&&e<this._rawCount))return NaN;var n=this._chunks[t];return n?n[e]:NaN},t.prototype.getSum=function(t){var e=0;if(this._chunks[t])for(var n=0,i=this.count();n<i;n++){var r=this.get(t,n);isNaN(r)||(e+=r)}return e},t.prototype.getMedian=function(t){var e=[];this.each([t],(function(t){isNaN(t)||e.push(t)}));var n=e.sort((function(t,e){return t-e})),i=this.count();return 0===i?0:i%2==1?n[(i-1)/2]:(n[i/2]+n[i/2-1])/2},t.prototype.indexOfRawIndex=function(t){if(t>=this._rawCount||t<0)return-1;if(!this._indices)return t;var e=this._indices,n=e[t];if(null!=n&&n<this._count&&n===t)return t;for(var i=0,r=this._count-1;i<=r;){var o=(i+r)/2|0;if(e[o]<t)i=o+1;else{if(!(e[o]>t))return o;r=o-1}}return-1},t.prototype.indicesOfNearest=function(t,e,n){var i=this._chunks[t],r=[];if(!i)return r;null==n&&(n=1/0);for(var o=1/0,a=-1,s=0,l=0,u=this.count();l<u;l++){var h=e-i[this.getRawIndex(l)],c=Math.abs(h);c<=n&&((c<o||c===o&&h>=0&&a<0)&&(o=c,a=h,s=0),h===a&&(r[s++]=l))}return r.length=s,r},t.prototype.getIndices=function(){var t,e=this._indices;if(e){var n=e.constructor,i=this._count;if(n===Array){t=new n(i);for(var r=0;r<i;r++)t[r]=e[r]}else t=new n(e.buffer,0,i)}else{t=new(n=ff(this._rawCount))(this.count());for(r=0;r<t.length;r++)t[r]=r}return t},t.prototype.filter=function(t,e){if(!this._count)return this;for(var n=this.clone(),i=n.count(),r=new(ff(n._rawCount))(i),o=[],a=t.length,s=0,l=t[0],u=n._chunks,h=0;h<i;h++){var c=void 0,p=n.getRawIndex(h);if(0===a)c=e(h);else if(1===a){c=e(u[l][p],h)}else{for(var f=0;f<a;f++)o[f]=u[t[f]][p];o[f]=h,c=e.apply(null,o)}c&&(r[s++]=p)}return s<i&&(n._indices=r),n._count=s,n._extent=[],n._updateGetRawIdx(),n},t.prototype.selectRange=function(t){var e=this.clone(),n=e._count;if(!n)return this;var i=B(t),r=i.length;if(!r)return this;var o=e.count(),a=new(ff(e._rawCount))(o),s=0,l=i[0],u=t[l][0],h=t[l][1],c=e._chunks,p=!1;if(!e._indices){var f=0;if(1===r){for(var d=c[i[0]],g=0;g<n;g++){((_=d[g])>=u&&_<=h||isNaN(_))&&(a[s++]=f),f++}p=!0}else if(2===r){d=c[i[0]];var y=c[i[1]],v=t[i[1]][0],m=t[i[1]][1];for(g=0;g<n;g++){var _=d[g],x=y[g];(_>=u&&_<=h||isNaN(_))&&(x>=v&&x<=m||isNaN(x))&&(a[s++]=f),f++}p=!0}}if(!p)if(1===r)for(g=0;g<o;g++){var w=e.getRawIndex(g);((_=c[i[0]][w])>=u&&_<=h||isNaN(_))&&(a[s++]=w)}else for(g=0;g<o;g++){for(var b=!0,S=(w=e.getRawIndex(g),0);S<r;S++){var M=i[S];((_=c[M][w])<t[M][0]||_>t[M][1])&&(b=!1)}b&&(a[s++]=e.getRawIndex(g))}return s<o&&(e._indices=a),e._count=s,e._extent=[],e._updateGetRawIdx(),e},t.prototype.map=function(t,e){var n=this.clone(t);return this._updateDims(n,t,e),n},t.prototype.modify=function(t,e){this._updateDims(this,t,e)},t.prototype._updateDims=function(t,e,n){for(var i=t._chunks,r=[],o=e.length,a=t.count(),s=[],l=t._rawExtent,u=0;u<e.length;u++)l[e[u]]=[1/0,-1/0];for(var h=0;h<a;h++){for(var c=t.getRawIndex(h),p=0;p<o;p++)s[p]=i[e[p]][c];s[o]=h;var f=n&&n.apply(null,s);if(null!=f){"object"!=typeof f&&(r[0]=f,f=r);for(u=0;u<f.length;u++){var d=e[u],g=f[u],y=l[d],v=i[d];v&&(v[c]=g),g<y[0]&&(y[0]=g),g>y[1]&&(y[1]=g)}}}},t.prototype.lttbDownSample=function(t,e){var n,i,r,o=this.clone([t],!0),a=o._chunks[t],s=this.count(),l=0,u=Math.floor(1/e),h=this.getRawIndex(0),c=new(ff(this._rawCount))(Math.min(2*(Math.ceil(s/u)+2),s));c[l++]=h;for(var p=1;p<s-1;p+=u){for(var f=Math.min(p+u,s-1),d=Math.min(p+2*u,s),g=(d+f)/2,y=0,v=f;v<d;v++){var m=a[T=this.getRawIndex(v)];isNaN(m)||(y+=m)}y/=d-f;var _=p,x=Math.min(p+u,s),w=p-1,b=a[h];n=-1,r=_;var S=-1,M=0;for(v=_;v<x;v++){var T;m=a[T=this.getRawIndex(v)];isNaN(m)?(M++,S<0&&(S=T)):(i=Math.abs((w-g)*(m-b)-(w-v)*(y-b)))>n&&(n=i,r=T)}M>0&&M<x-_&&(c[l++]=Math.min(S,r),r=Math.max(S,r)),c[l++]=r,h=r}return c[l++]=this.getRawIndex(s-1),o._count=l,o._indices=c,o.getRawIndex=this._getRawIdx,o},t.prototype.minmaxDownSample=function(t,e){for(var n=this.clone([t],!0),i=n._chunks,r=Math.floor(1/e),o=i[t],a=this.count(),s=new(ff(this._rawCount))(2*Math.ceil(a/r)),l=0,u=0;u<a;u+=r){var h=u,c=o[this.getRawIndex(h)],p=u,f=o[this.getRawIndex(p)],d=r;u+r>a&&(d=a-u);for(var g=0;g<d;g++){var y=o[this.getRawIndex(u+g)];y<c&&(c=y,h=u+g),y>f&&(f=y,p=u+g)}var v=this.getRawIndex(h),m=this.getRawIndex(p);h<p?(s[l++]=v,s[l++]=m):(s[l++]=m,s[l++]=v)}return n._count=l,n._indices=s,n._updateGetRawIdx(),n},t.prototype.downSample=function(t,e,n,i){for(var r=this.clone([t],!0),o=r._chunks,a=[],s=Math.floor(1/e),l=o[t],u=this.count(),h=r._rawExtent[t]=[1/0,-1/0],c=new(ff(this._rawCount))(Math.ceil(u/s)),p=0,f=0;f<u;f+=s){s>u-f&&(s=u-f,a.length=s);for(var d=0;d<s;d++){var g=this.getRawIndex(f+d);a[d]=l[g]}var y=n(a),v=this.getRawIndex(Math.min(f+i(a,y)||0,u-1));l[v]=y,y<h[0]&&(h[0]=y),y>h[1]&&(h[1]=y),c[p++]=v}return r._count=p,r._indices=c,r._updateGetRawIdx(),r},t.prototype.each=function(t,e){if(this._count)for(var n=t.length,i=this._chunks,r=0,o=this.count();r<o;r++){var a=this.getRawIndex(r);switch(n){case 0:e(r);break;case 1:e(i[t[0]][a],r);break;case 2:e(i[t[0]][a],i[t[1]][a],r);break;default:for(var s=0,l=[];s<n;s++)l[s]=i[t[s]][a];l[s]=r,e.apply(null,l)}}},t.prototype.getDataExtent=function(t){var e=this._chunks[t],n=[1/0,-1/0];if(!e)return n;var i,r=this.count();if(!this._indices)return this._rawExtent[t].slice();if(i=this._extent[t])return i.slice();for(var o=(i=n)[0],a=i[1],s=0;s<r;s++){var l=e[this.getRawIndex(s)];l<o&&(o=l),l>a&&(a=l)}return i=[o,a],this._extent[t]=i,i},t.prototype.getRawDataItem=function(t){var e=this.getRawIndex(t);if(this._provider.persistent)return this._provider.getItem(e);for(var n=[],i=this._chunks,r=0;r<i.length;r++)n.push(i[r][e]);return n},t.prototype.clone=function(e,n){var i,r,o=new t,a=this._chunks,s=e&&E(e,(function(t,e){return t[e]=!0,t}),{});if(s)for(var l=0;l<a.length;l++)o._chunks[l]=s[l]?(i=a[l],r=void 0,(r=i.constructor)===Array?i.slice():new r(i)):a[l];else o._chunks=a;return this._copyCommonProps(o),n||(o._indices=this._cloneIndices()),o._updateGetRawIdx(),o},t.prototype._copyCommonProps=function(t){t._count=this._count,t._rawCount=this._rawCount,t._provider=this._provider,t._dimensions=this._dimensions,t._extent=T(this._extent),t._rawExtent=T(this._rawExtent)},t.prototype._cloneIndices=function(){if(this._indices){var t=this._indices.constructor,e=void 0;if(t===Array){var n=this._indices.length;e=new t(n);for(var i=0;i<n;i++)e[i]=this._indices[i]}else e=new t(this._indices);return e}return null},t.prototype._getRawIdxIdentity=function(t){return t},t.prototype._getRawIdx=function(t){return t<this._count&&t>=0?this._indices[t]:-1},t.prototype._updateGetRawIdx=function(){this.getRawIndex=this._indices?this._getRawIdx:this._getRawIdxIdentity},t.internalField=function(){function t(t,e,n,i){return Kp(t[i],this._dimensions[i])}af={arrayRows:t,objectRows:function(t,e,n,i){return Kp(t[e],this._dimensions[i])},keyedColumns:t,original:function(t,e,n,i){var r=t&&(null==t.value?t:t.value);return Kp(r instanceof Array?r[i]:r,this._dimensions[i])},typedArray:function(t,e,n,i){return t[i]}}}(),t}(),yf=function(){function t(t){this._sourceList=[],this._storeList=[],this._upstreamSignList=[],this._versionSignBase=0,this._dirty=!0,this._sourceHost=t}return t.prototype.dirty=function(){this._setLocalSource([],[]),this._storeList=[],this._dirty=!0},t.prototype._setLocalSource=function(t,e){this._sourceList=t,this._upstreamSignList=e,this._versionSignBase++,this._versionSignBase>9e10&&(this._versionSignBase=0)},t.prototype._getVersionSign=function(){return this._sourceHost.uid+"_"+this._versionSignBase},t.prototype.prepareSource=function(){this._isDirty()&&(this._createSource(),this._dirty=!1)},t.prototype._createSource=function(){this._setLocalSource([],[]);var t,e,n=this._sourceHost,i=this._getUpstreamSourceManagers(),r=!!i.length;if(mf(n)){var o=n,a=void 0,s=void 0,l=void 0;if(r){var u=i[0];u.prepareSource(),a=(l=u.getSource()).data,s=l.sourceFormat,e=[u._getVersionSign()]}else s=Z(a=o.get("data",!0))?_c:gc,e=[];var h=this._getSourceMetaRawOption()||{},c=l&&l.metaRawOption||{},p=et(h.seriesLayoutBy,c.seriesLayoutBy)||null,f=et(h.sourceHeader,c.sourceHeader),d=et(h.dimensions,c.dimensions);t=p!==c.seriesLayoutBy||!!f!=!!c.sourceHeader||d?[Dp(a,{seriesLayoutBy:p,sourceHeader:f,dimensions:d},s)]:[]}else{var g=n;if(r){var y=this._applyTransform(i);t=y.sourceList,e=y.upstreamSignList}else{t=[Dp(g.get("source",!0),this._getSourceMetaRawOption(),null)],e=[]}}this._setLocalSource(t,e)},t.prototype._applyTransform=function(t){var e,n=this._sourceHost,i=n.get("transform",!0),r=n.get("fromTransformResult",!0);if(null!=r){var o="";1!==t.length&&_f(o)}var a,s=[],l=[];return R(t,(function(t){t.prepareSource();var e=t.getSource(r||0),n="";null==r||e||_f(n),s.push(e),l.push(t._getVersionSign())})),i?e=function(t,e,n){var i=Vr(t),r=i.length,o="";r||zr(o);for(var a=0,s=r;a<s;a++)e=rf(i[a],e),a!==s-1&&(e.length=Math.max(e.length,1));return e}(i,s,n.componentIndex):null!=r&&(e=[(a=s[0],new Tp({data:a.data,sourceFormat:a.sourceFormat,seriesLayoutBy:a.seriesLayoutBy,dimensionsDefine:T(a.dimensionsDefine),startIndex:a.startIndex,dimensionsDetectedCount:a.dimensionsDetectedCount}))]),{sourceList:e,upstreamSignList:l}},t.prototype._isDirty=function(){if(this._dirty)return!0;for(var t=this._getUpstreamSourceManagers(),e=0;e<t.length;e++){var n=t[e];if(n._isDirty()||this._upstreamSignList[e]!==n._getVersionSign())return!0}},t.prototype.getSource=function(t){t=t||0;var e=this._sourceList[t];if(!e){var n=this._getUpstreamSourceManagers();return n[0]&&n[0].getSource(t)}return e},t.prototype.getSharedDataStore=function(t){var e=t.makeStoreSchema();return this._innerGetDataStore(e.dimensions,t.source,e.hash)},t.prototype._innerGetDataStore=function(t,e,n){var i=this._storeList,r=i[0];r||(r=i[0]={});var o=r[n];if(!o){var a=this._getUpstreamSourceManagers()[0];mf(this._sourceHost)&&a?o=a._innerGetDataStore(t,e,n):(o=new gf).initData(new Op(e,t.length),t),r[n]=o}return o},t.prototype._getUpstreamSourceManagers=function(){var t=this._sourceHost;if(mf(t)){var e=kc(t);return e?[e.getSourceManager()]:[]}return N(function(t){return t.get("transform",!0)||t.get("fromTransformResult",!0)?io(t.ecModel,"dataset",{index:t.get("fromDatasetIndex",!0),id:t.get("fromDatasetId",!0)},no).models:[]}(t),(function(t){return t.getSourceManager()}))},t.prototype._getSourceMetaRawOption=function(){var t,e,n,i=this._sourceHost;if(mf(i))t=i.get("seriesLayoutBy",!0),e=i.get("sourceHeader",!0),n=i.get("dimensions",!0);else if(!this._getUpstreamSourceManagers().length){var r=i;t=r.get("seriesLayoutBy",!0),e=r.get("sourceHeader",!0),n=r.get("dimensions",!0)}return{seriesLayoutBy:t,sourceHeader:e,dimensions:n}},t}();function vf(t){t.option.transform&&lt(t.option.transform)}function mf(t){return"series"===t.mainType}function _f(t){throw new Error(t)}function xf(t,e){return e.type=t,e}function wf(t){var e,n,i,r,o=t.series,a=t.dataIndex,s=t.multipleSeries,l=o.getData(),u=l.mapDimensionsAll("defaultedTooltip"),h=u.length,c=o.getRawValue(a),p=H(c),f=function(t,e){return tc(t.getData().getItemVisual(e,"style")[t.visualDrawType])}(o,a);if(h>1||p&&!h){var d=function(t,e,n,i,r){var o=e.getData(),a=E(t,(function(t,e,n){var i=o.getDimensionInfo(n);return t||i&&!1!==i.tooltip&&null!=i.displayName}),!1),s=[],l=[],u=[];function h(t,e){var n=o.getDimensionInfo(e);n&&!1!==n.otherDims.tooltip&&(a?u.push(xf("nameValue",{markerType:"subItem",markerColor:r,name:n.displayName,value:t,valueType:n.type})):(s.push(t),l.push(n.type)))}return i.length?R(i,(function(t){h(Up(o,n,t),t)})):R(t,h),{inlineValues:s,inlineValueTypes:l,blocks:u}}(c,o,a,u,f);e=d.inlineValues,n=d.inlineValueTypes,i=d.blocks,r=d.inlineValues[0]}else if(h){var g=l.getDimensionInfo(u[0]);r=e=Up(l,a,u[0]),n=g.type}else r=e=p?c[0]:c;var y=jr(o),v=y&&o.name||"",m=l.getName(a),_=s?v:m;return xf("section",{header:v,noHeader:s||!y,sortParam:r,blocks:[xf("nameValue",{markerType:"item",markerColor:f,name:_,noName:!at(_),value:e,valueType:n,dataIndex:a})].concat(i||[])})}var bf=Qr();function Sf(t,e){return t.getName(e)||t.getId(e)}var Mf=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e._selectedDataIndicesMap={},e}return n(e,t),e.prototype.init=function(t,e,n){this.seriesIndex=this.componentIndex,this.dataTask=qp({count:Cf,reset:Df}),this.dataTask.context={model:this},this.mergeDefaultAndTheme(t,n),(bf(this).sourceManager=new yf(this)).prepareSource();var i=this.getInitialData(t,n);kf(i,this),this.dataTask.context.data=i,bf(this).dataBeforeProcessed=i,Tf(this),this._initSelectedMapFromData(i)},e.prototype.mergeDefaultAndTheme=function(t,e){var n=ac(this),i=n?lc(t):{},r=this.subType;hc.hasClass(r)&&(r+="Series"),C(t,e.getTheme().get(this.subType)),C(t,this.getDefaultOption()),Hr(t,"label",["show"]),this.fillDataTextStyle(t.data),n&&sc(t,i,n)},e.prototype.mergeOption=function(t,e){t=C(this.option,t,!0),this.fillDataTextStyle(t.data);var n=ac(this);n&&sc(this.option,t,n);var i=bf(this).sourceManager;i.dirty(),i.prepareSource();var r=this.getInitialData(t,e);kf(r,this),this.dataTask.dirty(),this.dataTask.context.data=r,bf(this).dataBeforeProcessed=r,Tf(this),this._initSelectedMapFromData(r)},e.prototype.fillDataTextStyle=function(t){if(t&&!Z(t))for(var e=["show"],n=0;n<t.length;n++)t[n]&&t[n].label&&Hr(t[n],"label",e)},e.prototype.getInitialData=function(t,e){},e.prototype.appendData=function(t){this.getRawData().appendData(t.data)},e.prototype.getData=function(t){var e=Lf(this);if(e){var n=e.context.data;return null!=t&&n.getLinkedData?n.getLinkedData(t):n}return bf(this).data},e.prototype.getAllData=function(){var t=this.getData();return t&&t.getLinkedDataAll?t.getLinkedDataAll():[{data:t}]},e.prototype.setData=function(t){var e=Lf(this);if(e){var n=e.context;n.outputData=t,e!==this.dataTask&&(n.data=t)}bf(this).data=t},e.prototype.getEncode=function(){var t=this.get("encode",!0);if(t)return ft(t)},e.prototype.getSourceManager=function(){return bf(this).sourceManager},e.prototype.getSource=function(){return this.getSourceManager().getSource()},e.prototype.getRawData=function(){return bf(this).dataBeforeProcessed},e.prototype.getColorBy=function(){return this.get("colorBy")||"series"},e.prototype.isColorBySeries=function(){return"series"===this.getColorBy()},e.prototype.getBaseAxis=function(){var t=this.coordinateSystem;return t&&t.getBaseAxis&&t.getBaseAxis()},e.prototype.formatTooltip=function(t,e,n){return wf({series:this,dataIndex:t,multipleSeries:e})},e.prototype.isAnimationEnabled=function(){var t=this.ecModel;if(r.node&&(!t||!t.ssr))return!1;var e=this.getShallow("animation");return e&&this.getData().count()>this.getShallow("animationThreshold")&&(e=!1),!!e},e.prototype.restoreData=function(){this.dataTask.dirty()},e.prototype.getColorFromPalette=function(t,e,n){var i=this.ecModel,r=Bc.prototype.getColorFromPalette.call(this,t,e,n);return r||(r=i.getColorFromPalette(t,e,n)),r},e.prototype.coordDimToDataDim=function(t){return this.getRawData().mapDimensionsAll(t)},e.prototype.getProgressive=function(){return this.get("progressive")},e.prototype.getProgressiveThreshold=function(){return this.get("progressiveThreshold")},e.prototype.select=function(t,e){this._innerSelect(this.getData(e),t)},e.prototype.unselect=function(t,e){var n=this.option.selectedMap;if(n){var i=this.option.selectedMode,r=this.getData(e);if("series"===i||"all"===n)return this.option.selectedMap={},void(this._selectedDataIndicesMap={});for(var o=0;o<t.length;o++){var a=Sf(r,t[o]);n[a]=!1,this._selectedDataIndicesMap[a]=-1}}},e.prototype.toggleSelect=function(t,e){for(var n=[],i=0;i<t.length;i++)n[0]=t[i],this.isSelected(t[i],e)?this.unselect(n,e):this.select(n,e)},e.prototype.getSelectedDataIndices=function(){if("all"===this.option.selectedMap)return[].slice.call(this.getData().getIndices());for(var t=this._selectedDataIndicesMap,e=B(t),n=[],i=0;i<e.length;i++){var r=t[e[i]];r>=0&&n.push(r)}return n},e.prototype.isSelected=function(t,e){var n=this.option.selectedMap;if(!n)return!1;var i=this.getData(e);return("all"===n||n[Sf(i,t)])&&!i.getItemModel(t).get(["select","disabled"])},e.prototype.isUniversalTransitionEnabled=function(){if(this.__universalTransitionEnabled)return!0;var t=this.option.universalTransition;return!!t&&(!0===t||t&&t.enabled)},e.prototype._innerSelect=function(t,e){var n,i,r=this.option,o=r.selectedMode,a=e.length;if(o&&a)if("series"===o)r.selectedMap="all";else if("multiple"===o){Y(r.selectedMap)||(r.selectedMap={});for(var s=r.selectedMap,l=0;l<a;l++){var u=e[l];s[c=Sf(t,u)]=!0,this._selectedDataIndicesMap[c]=t.getRawIndex(u)}}else if("single"===o||!0===o){var h=e[a-1],c=Sf(t,h);r.selectedMap=((n={})[c]=!0,n),this._selectedDataIndicesMap=((i={})[c]=t.getRawIndex(h),i)}},e.prototype._initSelectedMapFromData=function(t){if(!this.option.selectedMap){var e=[];t.hasItemOption&&t.each((function(n){var i=t.getRawDataItem(n);i&&i.selected&&e.push(n)})),e.length>0&&this._innerSelect(t,e)}},e.registerClass=function(t){return hc.registerClass(t)},e.protoInitialize=function(){var t=e.prototype;t.type="series.__base__",t.seriesIndex=0,t.ignoreStyleOnData=!1,t.hasSymbolVisual=!1,t.defaultSymbol="circle",t.visualStyleAccessPath="itemStyle",t.visualDrawType="fill"}(),e}(hc);function Tf(t){var e=t.name;jr(t)||(t.name=function(t){var e=t.getRawData(),n=e.mapDimensionsAll("seriesName"),i=[];return R(n,(function(t){var n=e.getDimensionInfo(t);n.displayName&&i.push(n.displayName)})),i.join(" ")}(t)||e)}function Cf(t){return t.model.getRawData().count()}function Df(t){var e=t.model;return e.setData(e.getRawData().cloneShallow()),If}function If(t,e){e.outputData&&t.end>e.outputData.count()&&e.model.getRawData().cloneShallow(e.outputData)}function kf(t,e){R(dt(t.CHANGABLE_METHODS,t.DOWNSAMPLE_METHODS),(function(n){t.wrapMethod(n,V(Af,e))}))}function Af(t,e){var n=Lf(t);return n&&n.setOutputEnd((e||this).count()),e}function Lf(t){var e=(t.ecModel||{}).scheduler,n=e&&e.getPipeline(t.uid);if(n){var i=n.currentTask;if(i){var r=i.agentStubMap;r&&(i=r.get(t.uid))}return i}}P(Mf,Yp),P(Mf,Bc),uo(Mf,hc);var Pf=function(){function t(){this.group=new sr,this.uid=ch("viewComponent")}return t.prototype.init=function(t,e){},t.prototype.render=function(t,e,n,i){},t.prototype.dispose=function(t,e){},t.prototype.updateView=function(t,e,n,i){},t.prototype.updateLayout=function(t,e,n,i){},t.prototype.updateVisual=function(t,e,n,i){},t.prototype.toggleBlurSeries=function(t,e,n){},t.prototype.eachRendered=function(t){var e=this.group;e&&e.traverse(t)},t}();function Of(){var t=Qr();return function(e){var n=t(e),i=e.pipelineContext,r=!!n.large,o=!!n.progressiveRender,a=n.large=!(!i||!i.large),s=n.progressiveRender=!(!i||!i.progressiveRender);return!(r===a&&o===s)&&"reset"}}lo(Pf),fo(Pf);var Rf=Qr(),Nf=Of(),Ef=function(){function t(){this.group=new sr,this.uid=ch("viewChart"),this.renderTask=qp({plan:Ff,reset:Vf}),this.renderTask.context={view:this}}return t.prototype.init=function(t,e){},t.prototype.render=function(t,e,n,i){0},t.prototype.highlight=function(t,e,n,i){var r=t.getData(i&&i.dataType);r&&Bf(r,i,"emphasis")},t.prototype.downplay=function(t,e,n,i){var r=t.getData(i&&i.dataType);r&&Bf(r,i,"normal")},t.prototype.remove=function(t,e){this.group.removeAll()},t.prototype.dispose=function(t,e){},t.prototype.updateView=function(t,e,n,i){this.render(t,e,n,i)},t.prototype.updateLayout=function(t,e,n,i){this.render(t,e,n,i)},t.prototype.updateVisual=function(t,e,n,i){this.render(t,e,n,i)},t.prototype.eachRendered=function(t){Fu(this.group,t)},t.markUpdateMethod=function(t,e){Rf(t).updateMethod=e},t.protoInitialize=void(t.prototype.type="chart"),t}();function zf(t,e,n){t&&rl(t)&&("emphasis"===e?Vs:Hs)(t,n)}function Bf(t,e,n){var i=$r(t,e),r=e&&null!=e.highlightKey?function(t){var e=gs[t];return null==e&&ds<=32&&(e=gs[t]=ds++),e}(e.highlightKey):null;null!=i?R(Vr(i),(function(e){zf(t.getItemGraphicEl(e),n,r)})):t.eachItemGraphicEl((function(t){zf(t,n,r)}))}function Ff(t){return Nf(t.model)}function Vf(t){var e=t.model,n=t.ecModel,i=t.api,r=t.payload,o=e.pipelineContext.progressiveRender,a=t.view,s=r&&Rf(r).updateMethod,l=o?"incrementalPrepareRender":s&&a[s]?s:"render";return"render"!==l&&a[l](e,n,i,r),Hf[l]}lo(Ef),fo(Ef);var Hf={incrementalPrepareRender:{progress:function(t,e){e.view.incrementalRender(t,e.model,e.ecModel,e.api,e.payload)}},render:{forceFirstProgress:!0,progress:function(t,e){e.view.render(e.model,e.ecModel,e.api,e.payload)}}};function Wf(t,e,n){var i,r,o,a,s,l=0,u=0,h=null;function c(){u=(new Date).getTime(),h=null,t.apply(o,a||[])}e=e||0;var p=function(){for(var t=[],p=0;p<arguments.length;p++)t[p]=arguments[p];i=(new Date).getTime(),o=this,a=t;var f=s||e,d=s||n;s=null,r=i-(d?l:u)-f,clearTimeout(h),d?h=setTimeout(c,f):r>=0?c():h=setTimeout(c,-r),l=i};return p.clear=function(){h&&(clearTimeout(h),h=null)},p.debounceNextCall=function(t){s=t},p}var Gf=Qr(),Uf={itemStyle:go(ah,!0),lineStyle:go(ih,!0)},Xf={lineStyle:"stroke",itemStyle:"fill"};function Yf(t,e){var n=t.visualStyleMapper||Uf[e];return n||(console.warn("Unknown style type '"+e+"'."),Uf.itemStyle)}function qf(t,e){var n=t.visualDrawType||Xf[e];return n||(console.warn("Unknown style type '"+e+"'."),"fill")}var Zf={createOnAllSeries:!0,performRawSeries:!0,reset:function(t,e){var n=t.getData(),i=t.visualStyleAccessPath||"itemStyle",r=t.getModel(i),o=Yf(t,i)(r),a=r.getShallow("decal");a&&(n.setVisual("decal",a),a.dirty=!0);var s=qf(t,i),l=o[s],u=W(l)?l:null,h="auto"===o.fill||"auto"===o.stroke;if(!o[s]||u||h){var c=t.getColorFromPalette(t.name,null,e.getSeriesCount());o[s]||(o[s]=c,n.setVisual("colorFromPalette",!0)),o.fill="auto"===o.fill||W(o.fill)?c:o.fill,o.stroke="auto"===o.stroke||W(o.stroke)?c:o.stroke}if(n.setVisual("style",o),n.setVisual("drawType",s),!e.isSeriesFiltered(t)&&u)return n.setVisual("colorFromPalette",!1),{dataEach:function(e,n){var i=t.getDataParams(n),r=D({},o);r[s]=u(i),e.setItemVisual(n,"style",r)}}}},jf=new uh,Kf={createOnAllSeries:!0,performRawSeries:!0,reset:function(t,e){if(!t.ignoreStyleOnData&&!e.isSeriesFiltered(t)){var n=t.getData(),i=t.visualStyleAccessPath||"itemStyle",r=Yf(t,i),o=n.getVisual("drawType");return{dataEach:n.hasItemOption?function(t,e){var n=t.getRawDataItem(e);if(n&&n[i]){jf.option=n[i];var a=r(jf);D(t.ensureUniqueItemVisual(e,"style"),a),jf.option.decal&&(t.setItemVisual(e,"decal",jf.option.decal),jf.option.decal.dirty=!0),o in a&&t.setItemVisual(e,"colorFromPalette",!1)}}:null}}}},$f={performRawSeries:!0,overallReset:function(t){var e=ft();t.eachSeries((function(t){var n=t.getColorBy();if(!t.isColorBySeries()){var i=t.type+"-"+n,r=e.get(i);r||(r={},e.set(i,r)),Gf(t).scope=r}})),t.eachSeries((function(e){if(!e.isColorBySeries()&&!t.isSeriesFiltered(e)){var n=e.getRawData(),i={},r=e.getData(),o=Gf(e).scope,a=e.visualStyleAccessPath||"itemStyle",s=qf(e,a);r.each((function(t){var e=r.getRawIndex(t);i[e]=t})),n.each((function(t){var a=i[t];if(r.getItemVisual(a,"colorFromPalette")){var l=r.ensureUniqueItemVisual(a,"style"),u=n.getName(t)||t+"",h=n.count();l[s]=e.getColorFromPalette(u,o,h)}}))}}))}},Qf=Math.PI;var Jf=function(){function t(t,e,n,i){this._stageTaskMap=ft(),this.ecInstance=t,this.api=e,n=this._dataProcessorHandlers=n.slice(),i=this._visualHandlers=i.slice(),this._allHandlers=n.concat(i)}return t.prototype.restoreData=function(t,e){t.restoreData(e),this._stageTaskMap.each((function(t){var e=t.overallTask;e&&e.dirty()}))},t.prototype.getPerformArgs=function(t,e){if(t.__pipeline){var n=this._pipelineMap.get(t.__pipeline.id),i=n.context,r=!e&&n.progressiveEnabled&&(!i||i.progressiveRender)&&t.__idxInPipeline>n.blockIndex?n.step:null,o=i&&i.modDataCount;return{step:r,modBy:null!=o?Math.ceil(o/r):null,modDataCount:o}}},t.prototype.getPipeline=function(t){return this._pipelineMap.get(t)},t.prototype.updateStreamModes=function(t,e){var n=this._pipelineMap.get(t.uid),i=t.getData().count(),r=n.progressiveEnabled&&e.incrementalPrepareRender&&i>=n.threshold,o=t.get("large")&&i>=t.get("largeThreshold"),a="mod"===t.get("progressiveChunkMode")?i:null;t.pipelineContext=n.context={progressiveRender:r,modDataCount:a,large:o}},t.prototype.restorePipelines=function(t){var e=this,n=e._pipelineMap=ft();t.eachSeries((function(t){var i=t.getProgressive(),r=t.uid;n.set(r,{id:r,head:null,tail:null,threshold:t.getProgressiveThreshold(),progressiveEnabled:i&&!(t.preventIncremental&&t.preventIncremental()),blockIndex:-1,step:Math.round(i||700),count:0}),e._pipe(t,t.dataTask)}))},t.prototype.prepareStageTasks=function(){var t=this._stageTaskMap,e=this.api.getModel(),n=this.api;R(this._allHandlers,(function(i){var r=t.get(i.uid)||t.set(i.uid,{}),o="";ot(!(i.reset&&i.overallReset),o),i.reset&&this._createSeriesStageTask(i,r,e,n),i.overallReset&&this._createOverallStageTask(i,r,e,n)}),this)},t.prototype.prepareView=function(t,e,n,i){var r=t.renderTask,o=r.context;o.model=e,o.ecModel=n,o.api=i,r.__block=!t.incrementalPrepareRender,this._pipe(e,r)},t.prototype.performDataProcessorTasks=function(t,e){this._performStageTasks(this._dataProcessorHandlers,t,e,{block:!0})},t.prototype.performVisualTasks=function(t,e,n){this._performStageTasks(this._visualHandlers,t,e,n)},t.prototype._performStageTasks=function(t,e,n,i){i=i||{};var r=!1,o=this;function a(t,e){return t.setDirty&&(!t.dirtyMap||t.dirtyMap.get(e.__pipeline.id))}R(t,(function(t,s){if(!i.visualType||i.visualType===t.visualType){var l=o._stageTaskMap.get(t.uid),u=l.seriesTaskMap,h=l.overallTask;if(h){var c,p=h.agentStubMap;p.each((function(t){a(i,t)&&(t.dirty(),c=!0)})),c&&h.dirty(),o.updatePayload(h,n);var f=o.getPerformArgs(h,i.block);p.each((function(t){t.perform(f)})),h.perform(f)&&(r=!0)}else u&&u.each((function(s,l){a(i,s)&&s.dirty();var u=o.getPerformArgs(s,i.block);u.skip=!t.performRawSeries&&e.isSeriesFiltered(s.context.model),o.updatePayload(s,n),s.perform(u)&&(r=!0)}))}})),this.unfinished=r||this.unfinished},t.prototype.performSeriesTasks=function(t){var e;t.eachSeries((function(t){e=t.dataTask.perform()||e})),this.unfinished=e||this.unfinished},t.prototype.plan=function(){this._pipelineMap.each((function(t){var e=t.tail;do{if(e.__block){t.blockIndex=e.__idxInPipeline;break}e=e.getUpstream()}while(e)}))},t.prototype.updatePayload=function(t,e){"remain"!==e&&(t.context.payload=e)},t.prototype._createSeriesStageTask=function(t,e,n,i){var r=this,o=e.seriesTaskMap,a=e.seriesTaskMap=ft(),s=t.seriesType,l=t.getTargetSeries;function u(e){var s=e.uid,l=a.set(s,o&&o.get(s)||qp({plan:rd,reset:od,count:ld}));l.context={model:e,ecModel:n,api:i,useClearVisual:t.isVisual&&!t.isLayout,plan:t.plan,reset:t.reset,scheduler:r},r._pipe(e,l)}t.createOnAllSeries?n.eachRawSeries(u):s?n.eachRawSeriesByType(s,u):l&&l(n,i).each(u)},t.prototype._createOverallStageTask=function(t,e,n,i){var r=this,o=e.overallTask=e.overallTask||qp({reset:td});o.context={ecModel:n,api:i,overallReset:t.overallReset,scheduler:r};var a=o.agentStubMap,s=o.agentStubMap=ft(),l=t.seriesType,u=t.getTargetSeries,h=!0,c=!1,p="";function f(t){var e=t.uid,n=s.set(e,a&&a.get(e)||(c=!0,qp({reset:ed,onDirty:id})));n.context={model:t,overallProgress:h},n.agent=o,n.__block=h,r._pipe(t,n)}ot(!t.createOnAllSeries,p),l?n.eachRawSeriesByType(l,f):u?u(n,i).each(f):(h=!1,R(n.getSeries(),f)),c&&o.dirty()},t.prototype._pipe=function(t,e){var n=t.uid,i=this._pipelineMap.get(n);!i.head&&(i.head=e),i.tail&&i.tail.pipe(e),i.tail=e,e.__idxInPipeline=i.count++,e.__pipeline=i},t.wrapStageHandler=function(t,e){return W(t)&&(t={overallReset:t,seriesType:ud(t)}),t.uid=ch("stageHandler"),e&&(t.visualType=e),t},t}();function td(t){t.overallReset(t.ecModel,t.api,t.payload)}function ed(t){return t.overallProgress&&nd}function nd(){this.agent.dirty(),this.getDownstream().dirty()}function id(){this.agent&&this.agent.dirty()}function rd(t){return t.plan?t.plan(t.model,t.ecModel,t.api,t.payload):null}function od(t){t.useClearVisual&&t.data.clearAllVisual();var e=t.resetDefines=Vr(t.reset(t.model,t.ecModel,t.api,t.payload));return e.length>1?N(e,(function(t,e){return sd(e)})):ad}var ad=sd(0);function sd(t){return function(e,n){var i=n.data,r=n.resetDefines[t];if(r&&r.dataEach)for(var o=e.start;o<e.end;o++)r.dataEach(i,o);else r&&r.progress&&r.progress(e,i)}}function ld(t){return t.data.count()}function ud(t){hd=null;try{t(cd,pd)}catch(t){}return hd}var hd,cd={},pd={};function fd(t,e){for(var n in e.prototype)t[n]=mt}fd(cd,Wc),fd(pd,qc),cd.eachSeriesByType=cd.eachRawSeriesByType=function(t){hd=t},cd.eachComponent=function(t){"series"===t.mainType&&t.subType&&(hd=t.subType)};var dd=["#37A2DA","#32C5E9","#67E0E3","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#E062AE","#E690D1","#e7bcf3","#9d96f5","#8378EA","#96BFFF"],gd={color:dd,colorLayer:[["#37A2DA","#ffd85c","#fd7b5f"],["#37A2DA","#67E0E3","#FFDB5C","#ff9f7f","#E062AE","#9d96f5"],["#37A2DA","#32C5E9","#9FE6B8","#FFDB5C","#ff9f7f","#fb7293","#e7bcf3","#8378EA","#96BFFF"],dd]},yd="#B9B8CE",vd="#100C2A",md=function(){return{axisLine:{lineStyle:{color:yd}},splitLine:{lineStyle:{color:"#484753"}},splitArea:{areaStyle:{color:["rgba(255,255,255,0.02)","rgba(255,255,255,0.05)"]}},minorSplitLine:{lineStyle:{color:"#20203B"}}}},_d=["#4992ff","#7cffb2","#fddd60","#ff6e76","#58d9f9","#05c091","#ff8a45","#8d48e3","#dd79ff"],xd={darkMode:!0,color:_d,backgroundColor:vd,axisPointer:{lineStyle:{color:"#817f91"},crossStyle:{color:"#817f91"},label:{color:"#fff"}},legend:{textStyle:{color:yd},pageTextStyle:{color:yd}},textStyle:{color:yd},title:{textStyle:{color:"#EEF1FA"},subtextStyle:{color:"#B9B8CE"}},toolbox:{iconStyle:{borderColor:yd}},dataZoom:{borderColor:"#71708A",textStyle:{color:yd},brushStyle:{color:"rgba(135,163,206,0.3)"},handleStyle:{color:"#353450",borderColor:"#C5CBE3"},moveHandleStyle:{color:"#B0B6C3",opacity:.3},fillerColor:"rgba(135,163,206,0.2)",emphasis:{handleStyle:{borderColor:"#91B7F2",color:"#4D587D"},moveHandleStyle:{color:"#636D9A",opacity:.7}},dataBackground:{lineStyle:{color:"#71708A",width:1},areaStyle:{color:"#71708A"}},selectedDataBackground:{lineStyle:{color:"#87A3CE"},areaStyle:{color:"#87A3CE"}}},visualMap:{textStyle:{color:yd}},timeline:{lineStyle:{color:yd},label:{color:yd},controlStyle:{color:yd,borderColor:yd}},calendar:{itemStyle:{color:vd},dayLabel:{color:yd},monthLabel:{color:yd},yearLabel:{color:yd}},timeAxis:md(),logAxis:md(),valueAxis:md(),categoryAxis:md(),line:{symbol:"circle"},graph:{color:_d},gauge:{title:{color:yd},axisLine:{lineStyle:{color:[[1,"rgba(207,212,219,0.2)"]]}},axisLabel:{color:yd},detail:{color:"#EEF1FA"}},candlestick:{itemStyle:{color:"#f64e56",color0:"#54ea92",borderColor:"#f64e56",borderColor0:"#54ea92"}}};xd.categoryAxis.splitLine.show=!1;var wd=function(){function t(){}return t.prototype.normalizeQuery=function(t){var e={},n={},i={};if(G(t)){var r=so(t);e.mainType=r.main||null,e.subType=r.sub||null}else{var o=["Index","Name","Id"],a={name:1,dataIndex:1,dataType:1};R(t,(function(t,r){for(var s=!1,l=0;l<o.length;l++){var u=o[l],h=r.lastIndexOf(u);if(h>0&&h===r.length-u.length){var c=r.slice(0,h);"data"!==c&&(e.mainType=c,e[u.toLowerCase()]=t,s=!0)}}a.hasOwnProperty(r)&&(n[r]=t,s=!0),s||(i[r]=t)}))}return{cptQuery:e,dataQuery:n,otherQuery:i}},t.prototype.filter=function(t,e){var n=this.eventInfo;if(!n)return!0;var i=n.targetEl,r=n.packedEvent,o=n.model,a=n.view;if(!o||!a)return!0;var s=e.cptQuery,l=e.dataQuery;return u(s,o,"mainType")&&u(s,o,"subType")&&u(s,o,"index","componentIndex")&&u(s,o,"name")&&u(s,o,"id")&&u(l,r,"name")&&u(l,r,"dataIndex")&&u(l,r,"dataType")&&(!a.filterForExposedEvent||a.filterForExposedEvent(t,e.otherQuery,i,r));function u(t,e,n,i){return null==t[n]||e[i||n]===t[n]}},t.prototype.afterTrigger=function(){this.eventInfo=null},t}(),bd=["symbol","symbolSize","symbolRotate","symbolOffset"],Sd=bd.concat(["symbolKeepAspect"]),Md={createOnAllSeries:!0,performRawSeries:!0,reset:function(t,e){var n=t.getData();if(t.legendIcon&&n.setVisual("legendIcon",t.legendIcon),t.hasSymbolVisual){for(var i={},r={},o=!1,a=0;a<bd.length;a++){var s=bd[a],l=t.get(s);W(l)?(o=!0,r[s]=l):i[s]=l}if(i.symbol=i.symbol||t.defaultSymbol,n.setVisual(D({legendIcon:t.legendIcon||i.symbol,symbolKeepAspect:t.get("symbolKeepAspect")},i)),!e.isSeriesFiltered(t)){var u=B(r);return{dataEach:o?function(e,n){for(var i=t.getRawValue(n),o=t.getDataParams(n),a=0;a<u.length;a++){var s=u[a];e.setItemVisual(n,s,r[s](i,o))}}:null}}}}},Td={createOnAllSeries:!0,performRawSeries:!0,reset:function(t,e){if(t.hasSymbolVisual&&!e.isSeriesFiltered(t))return{dataEach:t.getData().hasItemOption?function(t,e){for(var n=t.getItemModel(e),i=0;i<Sd.length;i++){var r=Sd[i],o=n.getShallow(r,!0);null!=o&&t.setItemVisual(e,r,o)}}:null}}};function Cd(t,e,n,i,r){var o=t+e;n.isSilent(o)||i.eachComponent({mainType:"series",subType:"pie"},(function(t){for(var e=t.seriesIndex,i=t.option.selectedMap,a=r.selected,s=0;s<a.length;s++)if(a[s].seriesIndex===e){var l=t.getData(),u=$r(l,r.fromActionPayload);n.trigger(o,{type:o,seriesId:t.id,name:H(u)?l.getName(u[0]):l.getName(u),selected:G(i)?i:D({},i)})}}))}function Dd(t,e,n){for(var i;t&&(!e(t)||(i=t,!n));)t=t.__hostTarget||t.parent;return i}var Id=Math.round(9*Math.random()),kd="function"==typeof Object.defineProperty,Ad=function(){function t(){this._id="__ec_inner_"+Id++}return t.prototype.get=function(t){return this._guard(t)[this._id]},t.prototype.set=function(t,e){var n=this._guard(t);return kd?Object.defineProperty(n,this._id,{value:e,enumerable:!1,configurable:!0}):n[this._id]=e,this},t.prototype.delete=function(t){return!!this.has(t)&&(delete this._guard(t)[this._id],!0)},t.prototype.has=function(t){return!!this._guard(t)[this._id]},t.prototype._guard=function(t){if(t!==Object(t))throw TypeError("Value of WeakMap is not a non-null object.");return t},t}(),Ld=Ha.extend({type:"triangle",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=e.width/2,o=e.height/2;t.moveTo(n,i-o),t.lineTo(n+r,i+o),t.lineTo(n-r,i+o),t.closePath()}}),Pd=Ha.extend({type:"diamond",shape:{cx:0,cy:0,width:0,height:0},buildPath:function(t,e){var n=e.cx,i=e.cy,r=e.width/2,o=e.height/2;t.moveTo(n,i-o),t.lineTo(n+r,i),t.lineTo(n,i+o),t.lineTo(n-r,i),t.closePath()}}),Od=Ha.extend({type:"pin",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var n=e.x,i=e.y,r=e.width/5*3,o=Math.max(r,e.height),a=r/2,s=a*a/(o-a),l=i-o+a+s,u=Math.asin(s/a),h=Math.cos(u)*a,c=Math.sin(u),p=Math.cos(u),f=.6*a,d=.7*a;t.moveTo(n-h,l+s),t.arc(n,l,a,Math.PI-u,2*Math.PI+u),t.bezierCurveTo(n+h-c*f,l+s+p*f,n,i-d,n,i),t.bezierCurveTo(n,i-d,n-h+c*f,l+s+p*f,n-h,l+s),t.closePath()}}),Rd=Ha.extend({type:"arrow",shape:{x:0,y:0,width:0,height:0},buildPath:function(t,e){var n=e.height,i=e.width,r=e.x,o=e.y,a=i/3*2;t.moveTo(r,o),t.lineTo(r+a,o+n),t.lineTo(r,o+n/4*3),t.lineTo(r-a,o+n),t.lineTo(r,o),t.closePath()}}),Nd={line:function(t,e,n,i,r){r.x1=t,r.y1=e+i/2,r.x2=t+n,r.y2=e+i/2},rect:function(t,e,n,i,r){r.x=t,r.y=e,r.width=n,r.height=i},roundRect:function(t,e,n,i,r){r.x=t,r.y=e,r.width=n,r.height=i,r.r=Math.min(n,i)/4},square:function(t,e,n,i,r){var o=Math.min(n,i);r.x=t,r.y=e,r.width=o,r.height=o},circle:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.r=Math.min(n,i)/2},diamond:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.width=n,r.height=i},pin:function(t,e,n,i,r){r.x=t+n/2,r.y=e+i/2,r.width=n,r.height=i},arrow:function(t,e,n,i,r){r.x=t+n/2,r.y=e+i/2,r.width=n,r.height=i},triangle:function(t,e,n,i,r){r.cx=t+n/2,r.cy=e+i/2,r.width=n,r.height=i}},Ed={};R({line:Ql,rect:Qa,roundRect:Qa,square:Qa,circle:Tl,diamond:Pd,pin:Od,arrow:Rd,triangle:Ld},(function(t,e){Ed[e]=new t}));var zd=Ha.extend({type:"symbol",shape:{symbolType:"",x:0,y:0,width:0,height:0},calculateTextPosition:function(t,e,n){var i=Ki(t,e,n),r=this.shape;return r&&"pin"===r.symbolType&&"inside"===e.position&&(i.y=n.y+.4*n.height),i},buildPath:function(t,e,n){var i=e.symbolType;if("none"!==i){var r=Ed[i];r||(r=Ed[i="rect"]),Nd[i](e.x,e.y,e.width,e.height,r.shape),r.buildPath(t,r.shape,n)}}});function Bd(t,e){if("image"!==this.type){var n=this.style;this.__isEmptyBrush?(n.stroke=t,n.fill=e||"#fff",n.lineWidth=2):"line"===this.shape.symbolType?n.stroke=t:n.fill=t,this.markRedraw()}}function Fd(t,e,n,i,r,o,a){var s,l=0===t.indexOf("empty");return l&&(t=t.substr(5,1).toLowerCase()+t.substr(6)),(s=0===t.indexOf("image://")?Lu(t.slice(8),new Ce(e,n,i,r),a?"center":"cover"):0===t.indexOf("path://")?Au(t.slice(7),{},new Ce(e,n,i,r),a?"center":"cover"):new zd({shape:{symbolType:t,x:e,y:n,width:i,height:r}})).__isEmptyBrush=l,s.setColor=Bd,o&&s.setColor(o),s}function Vd(t,e){if(null!=t)return H(t)||(t=[t,t]),[mr(t[0],e[0])||0,mr(et(t[1],t[0]),e[1])||0]}function Hd(t){return isFinite(t)}function Wd(t,e,n){for(var i="radial"===e.type?function(t,e,n){var i=n.width,r=n.height,o=Math.min(i,r),a=null==e.x?.5:e.x,s=null==e.y?.5:e.y,l=null==e.r?.5:e.r;return e.global||(a=a*i+n.x,s=s*r+n.y,l*=o),a=Hd(a)?a:.5,s=Hd(s)?s:.5,l=l>=0&&Hd(l)?l:.5,t.createRadialGradient(a,s,0,a,s,l)}(t,e,n):function(t,e,n){var i=null==e.x?0:e.x,r=null==e.x2?1:e.x2,o=null==e.y?0:e.y,a=null==e.y2?0:e.y2;return e.global||(i=i*n.width+n.x,r=r*n.width+n.x,o=o*n.height+n.y,a=a*n.height+n.y),i=Hd(i)?i:0,r=Hd(r)?r:1,o=Hd(o)?o:0,a=Hd(a)?a:0,t.createLinearGradient(i,o,r,a)}(t,e,n),r=e.colorStops,o=0;o<r.length;o++)i.addColorStop(r[o].offset,r[o].color);return i}function Gd(t){return parseInt(t,10)}function Ud(t,e,n){var i=["width","height"][e],r=["clientWidth","clientHeight"][e],o=["paddingLeft","paddingTop"][e],a=["paddingRight","paddingBottom"][e];if(null!=n[i]&&"auto"!==n[i])return parseFloat(n[i]);var s=document.defaultView.getComputedStyle(t);return(t[r]||Gd(s[i])||Gd(t.style[i]))-(Gd(s[o])||0)-(Gd(s[a])||0)|0}function Xd(t){var e,n,i=t.style,r=i.lineDash&&i.lineWidth>0&&(e=i.lineDash,n=i.lineWidth,e&&"solid"!==e&&n>0?"dashed"===e?[4*n,2*n]:"dotted"===e?[n]:X(e)?[e]:H(e)?e:null:null),o=i.lineDashOffset;if(r){var a=i.strokeNoScale&&t.getLineScale?t.getLineScale():1;a&&1!==a&&(r=N(r,(function(t){return t/a})),o/=a)}return[r,o]}var Yd=new wa(!0);function qd(t){var e=t.stroke;return!(null==e||"none"===e||!(t.lineWidth>0))}function Zd(t){return"string"==typeof t&&"none"!==t}function jd(t){var e=t.fill;return null!=e&&"none"!==e}function Kd(t,e){if(null!=e.fillOpacity&&1!==e.fillOpacity){var n=t.globalAlpha;t.globalAlpha=e.fillOpacity*e.opacity,t.fill(),t.globalAlpha=n}else t.fill()}function $d(t,e){if(null!=e.strokeOpacity&&1!==e.strokeOpacity){var n=t.globalAlpha;t.globalAlpha=e.strokeOpacity*e.opacity,t.stroke(),t.globalAlpha=n}else t.stroke()}function Qd(t,e,n){var i=xo(e.image,e.__image,n);if(bo(i)){var r=t.createPattern(i,e.repeat||"repeat");if("function"==typeof DOMMatrix&&r&&r.setTransform){var o=new DOMMatrix;o.translateSelf(e.x||0,e.y||0),o.rotateSelf(0,0,(e.rotation||0)*_t),o.scaleSelf(e.scaleX||1,e.scaleY||1),r.setTransform(o)}return r}}var Jd=["shadowBlur","shadowOffsetX","shadowOffsetY"],tg=[["lineCap","butt"],["lineJoin","miter"],["miterLimit",10]];function eg(t,e,n,i,r){var o=!1;if(!i&&e===(n=n||{}))return!1;if(i||e.opacity!==n.opacity){rg(t,r),o=!0;var a=Math.max(Math.min(e.opacity,1),0);t.globalAlpha=isNaN(a)?Eo.opacity:a}(i||e.blend!==n.blend)&&(o||(rg(t,r),o=!0),t.globalCompositeOperation=e.blend||Eo.blend);for(var s=0;s<Jd.length;s++){var l=Jd[s];(i||e[l]!==n[l])&&(o||(rg(t,r),o=!0),t[l]=t.dpr*(e[l]||0))}return(i||e.shadowColor!==n.shadowColor)&&(o||(rg(t,r),o=!0),t.shadowColor=e.shadowColor||Eo.shadowColor),o}function ng(t,e,n,i,r){var o=og(e,r.inHover),a=i?null:n&&og(n,r.inHover)||{};if(o===a)return!1;var s=eg(t,o,a,i,r);if((i||o.fill!==a.fill)&&(s||(rg(t,r),s=!0),Zd(o.fill)&&(t.fillStyle=o.fill)),(i||o.stroke!==a.stroke)&&(s||(rg(t,r),s=!0),Zd(o.stroke)&&(t.strokeStyle=o.stroke)),(i||o.opacity!==a.opacity)&&(s||(rg(t,r),s=!0),t.globalAlpha=null==o.opacity?1:o.opacity),e.hasStroke()){var l=o.lineWidth/(o.strokeNoScale&&e.getLineScale?e.getLineScale():1);t.lineWidth!==l&&(s||(rg(t,r),s=!0),t.lineWidth=l)}for(var u=0;u<tg.length;u++){var h=tg[u],c=h[0];(i||o[c]!==a[c])&&(s||(rg(t,r),s=!0),t[c]=o[c]||h[1])}return s}function ig(t,e){var n=e.transform,i=t.dpr||1;n?t.setTransform(i*n[0],i*n[1],i*n[2],i*n[3],i*n[4],i*n[5]):t.setTransform(i,0,0,i,0,0)}function rg(t,e){e.batchFill&&t.fill(),e.batchStroke&&t.stroke(),e.batchFill="",e.batchStroke=""}function og(t,e){return e&&t.__hoverStyle||t.style}function ag(t,e){sg(t,e,{inHover:!1,viewWidth:0,viewHeight:0},!0)}function sg(t,e,n,i){var r=e.transform;if(!e.shouldBePainted(n.viewWidth,n.viewHeight,!1,!1))return e.__dirty&=-2,void(e.__isRendered=!1);var a=e.__clipPaths,s=n.prevElClipPaths,l=!1,u=!1;if(s&&!function(t,e){if(t===e||!t&&!e)return!1;if(!t||!e||t.length!==e.length)return!0;for(var n=0;n<t.length;n++)if(t[n]!==e[n])return!0;return!1}(a,s)||(s&&s.length&&(rg(t,n),t.restore(),u=l=!0,n.prevElClipPaths=null,n.allClipped=!1,n.prevEl=null),a&&a.length&&(rg(t,n),t.save(),function(t,e,n){for(var i=!1,r=0;r<t.length;r++){var o=t[r];i=i||o.isZeroArea(),ig(e,o),e.beginPath(),o.buildPath(e,o.shape),e.clip()}n.allClipped=i}(a,t,n),l=!0),n.prevElClipPaths=a),n.allClipped)e.__isRendered=!1;else{e.beforeBrush&&e.beforeBrush(),e.innerBeforeBrush();var h=n.prevEl;h||(u=l=!0);var c,p,f=e instanceof Ha&&e.autoBatch&&function(t){var e=jd(t),n=qd(t);return!(t.lineDash||!(+e^+n)||e&&"string"!=typeof t.fill||n&&"string"!=typeof t.stroke||t.strokePercent<1||t.strokeOpacity<1||t.fillOpacity<1)}(e.style);l||(c=r,p=h.transform,c&&p?c[0]!==p[0]||c[1]!==p[1]||c[2]!==p[2]||c[3]!==p[3]||c[4]!==p[4]||c[5]!==p[5]:c||p)?(rg(t,n),ig(t,e)):f||rg(t,n);var d=og(e,n.inHover);e instanceof Ha?(1!==n.lastDrawType&&(u=!0,n.lastDrawType=1),ng(t,e,h,u,n),f&&(n.batchFill||n.batchStroke)||t.beginPath(),function(t,e,n,i){var r,o=qd(n),a=jd(n),s=n.strokePercent,l=s<1,u=!e.path;e.silent&&!l||!u||e.createPathProxy();var h=e.path||Yd,c=e.__dirty;if(!i){var p=n.fill,f=n.stroke,d=a&&!!p.colorStops,g=o&&!!f.colorStops,y=a&&!!p.image,v=o&&!!f.image,m=void 0,_=void 0,x=void 0,w=void 0,b=void 0;(d||g)&&(b=e.getBoundingRect()),d&&(m=c?Wd(t,p,b):e.__canvasFillGradient,e.__canvasFillGradient=m),g&&(_=c?Wd(t,f,b):e.__canvasStrokeGradient,e.__canvasStrokeGradient=_),y&&(x=c||!e.__canvasFillPattern?Qd(t,p,e):e.__canvasFillPattern,e.__canvasFillPattern=x),v&&(w=c||!e.__canvasStrokePattern?Qd(t,f,e):e.__canvasStrokePattern,e.__canvasStrokePattern=x),d?t.fillStyle=m:y&&(x?t.fillStyle=x:a=!1),g?t.strokeStyle=_:v&&(w?t.strokeStyle=w:o=!1)}var S,M,T=e.getGlobalScale();h.setScale(T[0],T[1],e.segmentIgnoreThreshold),t.setLineDash&&n.lineDash&&(S=(r=Xd(e))[0],M=r[1]);var C=!0;(u||4&c)&&(h.setDPR(t.dpr),l?h.setContext(null):(h.setContext(t),C=!1),h.reset(),e.buildPath(h,e.shape,i),h.toStatic(),e.pathUpdated()),C&&h.rebuildPath(t,l?s:1),S&&(t.setLineDash(S),t.lineDashOffset=M),i||(n.strokeFirst?(o&&$d(t,n),a&&Kd(t,n)):(a&&Kd(t,n),o&&$d(t,n))),S&&t.setLineDash([])}(t,e,d,f),f&&(n.batchFill=d.fill||"",n.batchStroke=d.stroke||"")):e instanceof Ga?(3!==n.lastDrawType&&(u=!0,n.lastDrawType=3),ng(t,e,h,u,n),function(t,e,n){var i,r=n.text;if(null!=r&&(r+=""),r){t.font=n.font||o,t.textAlign=n.textAlign,t.textBaseline=n.textBaseline;var a=void 0,s=void 0;t.setLineDash&&n.lineDash&&(a=(i=Xd(e))[0],s=i[1]),a&&(t.setLineDash(a),t.lineDashOffset=s),n.strokeFirst?(qd(n)&&t.strokeText(r,n.x,n.y),jd(n)&&t.fillText(r,n.x,n.y)):(jd(n)&&t.fillText(r,n.x,n.y),qd(n)&&t.strokeText(r,n.x,n.y)),a&&t.setLineDash([])}}(t,e,d)):e instanceof Ya?(2!==n.lastDrawType&&(u=!0,n.lastDrawType=2),function(t,e,n,i,r){eg(t,og(e,r.inHover),n&&og(n,r.inHover),i,r)}(t,e,h,u,n),function(t,e,n){var i=e.__image=xo(n.image,e.__image,e,e.onload);if(i&&bo(i)){var r=n.x||0,o=n.y||0,a=e.getWidth(),s=e.getHeight(),l=i.width/i.height;if(null==a&&null!=s?a=s*l:null==s&&null!=a?s=a/l:null==a&&null==s&&(a=i.width,s=i.height),n.sWidth&&n.sHeight){var u=n.sx||0,h=n.sy||0;t.drawImage(i,u,h,n.sWidth,n.sHeight,r,o,a,s)}else if(n.sx&&n.sy){var c=a-(u=n.sx),p=s-(h=n.sy);t.drawImage(i,u,h,c,p,r,o,a,s)}else t.drawImage(i,r,o,a,s)}}(t,e,d)):e.getTemporalDisplayables&&(4!==n.lastDrawType&&(u=!0,n.lastDrawType=4),function(t,e,n){var i=e.getDisplayables(),r=e.getTemporalDisplayables();t.save();var o,a,s={prevElClipPaths:null,prevEl:null,allClipped:!1,viewWidth:n.viewWidth,viewHeight:n.viewHeight,inHover:n.inHover};for(o=e.getCursor(),a=i.length;o<a;o++){(h=i[o]).beforeBrush&&h.beforeBrush(),h.innerBeforeBrush(),sg(t,h,s,o===a-1),h.innerAfterBrush(),h.afterBrush&&h.afterBrush(),s.prevEl=h}for(var l=0,u=r.length;l<u;l++){var h;(h=r[l]).beforeBrush&&h.beforeBrush(),h.innerBeforeBrush(),sg(t,h,s,l===u-1),h.innerAfterBrush(),h.afterBrush&&h.afterBrush(),s.prevEl=h}e.clearTemporalDisplayables(),e.notClear=!0,t.restore()}(t,e,n)),f&&i&&rg(t,n),e.innerAfterBrush(),e.afterBrush&&e.afterBrush(),n.prevEl=e,e.__dirty=0,e.__isRendered=!0}}var lg=new Ad,ug=new Sn(100),hg=["symbol","symbolSize","symbolKeepAspect","color","backgroundColor","dashArrayX","dashArrayY","maxTileWidth","maxTileHeight"];function cg(t,e){if("none"===t)return null;var n=e.getDevicePixelRatio(),i=e.getZr(),r="svg"===i.painter.type;t.dirty&&lg.delete(t);var o=lg.get(t);if(o)return o;var a=I(t,{symbol:"rect",symbolSize:1,symbolKeepAspect:!0,color:"rgba(0, 0, 0, 0.2)",backgroundColor:null,dashArrayX:5,dashArrayY:5,rotation:0,maxTileWidth:512,maxTileHeight:512});"none"===a.backgroundColor&&(a.backgroundColor=null);var s={repeat:"repeat"};return function(t){for(var e,o=[n],s=!0,l=0;l<hg.length;++l){var h=a[hg[l]];if(null!=h&&!H(h)&&!G(h)&&!X(h)&&"boolean"!=typeof h){s=!1;break}o.push(h)}if(s){e=o.join(",")+(r?"-svg":"");var c=ug.get(e);c&&(r?t.svgElement=c:t.image=c)}var p,f=fg(a.dashArrayX),d=function(t){if(!t||"object"==typeof t&&0===t.length)return[0,0];if(X(t)){var e=Math.ceil(t);return[e,e]}var n=N(t,(function(t){return Math.ceil(t)}));return t.length%2?n.concat(n):n}(a.dashArrayY),g=pg(a.symbol),y=(w=f,N(w,(function(t){return dg(t)}))),v=dg(d),m=!r&&u.createCanvas(),_=r&&{tag:"g",attrs:{},key:"dcl",children:[]},x=function(){for(var t=1,e=0,n=y.length;e<n;++e)t=Nr(t,y[e]);var i=1;for(e=0,n=g.length;e<n;++e)i=Nr(i,g[e].length);t*=i;var r=v*y.length*g.length;return{width:Math.max(1,Math.min(t,a.maxTileWidth)),height:Math.max(1,Math.min(r,a.maxTileHeight))}}();var w;m&&(m.width=x.width*n,m.height=x.height*n,p=m.getContext("2d"));(function(){p&&(p.clearRect(0,0,m.width,m.height),a.backgroundColor&&(p.fillStyle=a.backgroundColor,p.fillRect(0,0,m.width,m.height)));for(var t=0,e=0;e<d.length;++e)t+=d[e];if(t<=0)return;var o=-v,s=0,l=0,u=0;for(;o<x.height;){if(s%2==0){for(var h=l/2%g.length,c=0,y=0,w=0;c<2*x.width;){var b=0;for(e=0;e<f[u].length;++e)b+=f[u][e];if(b<=0)break;if(y%2==0){var S=.5*(1-a.symbolSize),M=c+f[u][y]*S,T=o+d[s]*S,C=f[u][y]*a.symbolSize,D=d[s]*a.symbolSize,I=w/2%g[h].length;k(M,T,C,D,g[h][I])}c+=f[u][y],++w,++y===f[u].length&&(y=0)}++u===f.length&&(u=0)}o+=d[s],++l,++s===d.length&&(s=0)}function k(t,e,o,s,l){var u=r?1:n,h=Fd(l,t*u,e*u,o*u,s*u,a.color,a.symbolKeepAspect);if(r){var c=i.painter.renderOneToVNode(h);c&&_.children.push(c)}else ag(p,h)}})(),s&&ug.put(e,m||_);t.image=m,t.svgElement=_,t.svgWidth=x.width,t.svgHeight=x.height}(s),s.rotation=a.rotation,s.scaleX=s.scaleY=r?1:1/n,lg.set(t,s),t.dirty=!1,s}function pg(t){if(!t||0===t.length)return[["rect"]];if(G(t))return[[t]];for(var e=!0,n=0;n<t.length;++n)if(!G(t[n])){e=!1;break}if(e)return pg([t]);var i=[];for(n=0;n<t.length;++n)G(t[n])?i.push([t[n]]):i.push(t[n]);return i}function fg(t){if(!t||0===t.length)return[[0,0]];if(X(t))return[[r=Math.ceil(t),r]];for(var e=!0,n=0;n<t.length;++n)if(!X(t[n])){e=!1;break}if(e)return fg([t]);var i=[];for(n=0;n<t.length;++n)if(X(t[n])){var r=Math.ceil(t[n]);i.push([r,r])}else{(r=N(t[n],(function(t){return Math.ceil(t)}))).length%2==1?i.push(r.concat(r)):i.push(r)}return i}function dg(t){for(var e=0,n=0;n<t.length;++n)e+=t[n];return t.length%2==1?2*e:e}var gg=new Wt,yg={};function vg(t){return yg[t]}var mg=2e3,_g=4500,xg={PROCESSOR:{FILTER:1e3,SERIES_FILTER:800,STATISTIC:5e3},VISUAL:{LAYOUT:1e3,PROGRESSIVE_LAYOUT:1100,GLOBAL:mg,CHART:3e3,POST_CHART_LAYOUT:4600,COMPONENT:4e3,BRUSH:5e3,CHART_ITEM:_g,ARIA:6e3,DECAL:7e3}},wg="__flagInMainProcess",bg="__pendingUpdate",Sg="__needsUpdateStatus",Mg=/^[a-zA-Z0-9_]+$/,Tg="__connectUpdateStatus";function Cg(t){return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];if(!this.isDisposed())return Ig(this,t,e);Qg(this.id)}}function Dg(t){return function(){for(var e=[],n=0;n<arguments.length;n++)e[n]=arguments[n];return Ig(this,t,e)}}function Ig(t,e,n){return n[0]=n[0]&&n[0].toLowerCase(),Wt.prototype[e].apply(t,n)}var kg,Ag,Lg,Pg,Og,Rg,Ng,Eg,zg,Bg,Fg,Vg,Hg,Wg,Gg,Ug,Xg,Yg,qg=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e}(Wt),Zg=qg.prototype;Zg.on=Dg("on"),Zg.off=Dg("off");var jg=function(t){function e(e,n,i){var r=t.call(this,new wd)||this;r._chartsViews=[],r._chartsMap={},r._componentsViews=[],r._componentsMap={},r._pendingActions=[],i=i||{},G(n)&&(n=ry[n]),r._dom=e;var o="canvas",a="auto",s=!1;i.ssr&&dr((function(t){var e=fs(t),n=e.dataIndex;if(null!=n){var i=ft();return i.set("series_index",e.seriesIndex),i.set("data_index",n),e.ssrType&&i.set("ssr_type",e.ssrType),i}}));var l=r._zr=pr(e,{renderer:i.renderer||o,devicePixelRatio:i.devicePixelRatio,width:i.width,height:i.height,ssr:i.ssr,useDirtyRect:et(i.useDirtyRect,s),useCoarsePointer:et(i.useCoarsePointer,a),pointerSize:i.pointerSize});r._ssr=i.ssr,r._throttledZrFlush=Wf(F(l.flush,l),17),(n=T(n))&&mp(n,!0),r._theme=n,r._locale=function(t){if(G(t)){var e=gh[t.toUpperCase()]||{};return t===ph||t===fh?T(e):C(T(e),T(gh[dh]),!1)}return C(T(t),T(gh[dh]),!1)}(i.locale||vh),r._coordSysMgr=new jc;var u=r._api=Gg(r);function h(t,e){return t.__prio-e.__prio}return We(iy,h),We(ey,h),r._scheduler=new Jf(r,u,ey,iy),r._messageCenter=new qg,r._initEvents(),r.resize=F(r.resize,r),l.animation.on("frame",r._onframe,r),Bg(l,r),Fg(l,r),lt(r),r}return n(e,t),e.prototype._onframe=function(){if(!this._disposed){Yg(this);var t=this._scheduler;if(this[bg]){var e=this[bg].silent;this[wg]=!0;try{kg(this),Pg.update.call(this,null,this[bg].updateParams)}catch(t){throw this[wg]=!1,this[bg]=null,t}this._zr.flush(),this[wg]=!1,this[bg]=null,Eg.call(this,e),zg.call(this,e)}else if(t.unfinished){var n=1,i=this._model,r=this._api;t.unfinished=!1;do{var o=+new Date;t.performSeriesTasks(i),t.performDataProcessorTasks(i),Rg(this,i),t.performVisualTasks(i),Wg(this,this._model,r,"remain",{}),n-=+new Date-o}while(n>0&&t.unfinished);t.unfinished||this._zr.flush()}}},e.prototype.getDom=function(){return this._dom},e.prototype.getId=function(){return this.id},e.prototype.getZr=function(){return this._zr},e.prototype.isSSR=function(){return this._ssr},e.prototype.setOption=function(t,e,n){if(!this[wg])if(this._disposed)Qg(this.id);else{var i,r,o;if(Y(e)&&(n=e.lazyUpdate,i=e.silent,r=e.replaceMerge,o=e.transition,e=e.notMerge),this[wg]=!0,!this._model||e){var a=new $c(this._api),s=this._theme,l=this._model=new Wc;l.scheduler=this._scheduler,l.ssr=this._ssr,l.init(null,null,null,s,this._locale,a)}this._model.setOption(t,{replaceMerge:r},ny);var u={seriesTransition:o,optionChanged:!0};if(n)this[bg]={silent:i,updateParams:u},this[wg]=!1,this.getZr().wakeUp();else{try{kg(this),Pg.update.call(this,null,u)}catch(t){throw this[bg]=null,this[wg]=!1,t}this._ssr||this._zr.flush(),this[bg]=null,this[wg]=!1,Eg.call(this,i),zg.call(this,i)}}},e.prototype.setTheme=function(){Er()},e.prototype.getModel=function(){return this._model},e.prototype.getOption=function(){return this._model&&this._model.getOption()},e.prototype.getWidth=function(){return this._zr.getWidth()},e.prototype.getHeight=function(){return this._zr.getHeight()},e.prototype.getDevicePixelRatio=function(){return this._zr.painter.dpr||r.hasGlobalWindow&&window.devicePixelRatio||1},e.prototype.getRenderedCanvas=function(t){return this.renderToCanvas(t)},e.prototype.renderToCanvas=function(t){t=t||{};var e=this._zr.painter;return e.getRenderedCanvas({backgroundColor:t.backgroundColor||this._model.get("backgroundColor"),pixelRatio:t.pixelRatio||this.getDevicePixelRatio()})},e.prototype.renderToSVGString=function(t){t=t||{};var e=this._zr.painter;return e.renderToString({useViewBox:t.useViewBox})},e.prototype.getSvgDataURL=function(){if(r.svgSupported){var t=this._zr;return R(t.storage.getDisplayList(),(function(t){t.stopAnimation(null,!0)})),t.painter.toDataURL()}},e.prototype.getDataURL=function(t){if(!this._disposed){var e=(t=t||{}).excludeComponents,n=this._model,i=[],r=this;R(e,(function(t){n.eachComponent({mainType:t},(function(t){var e=r._componentsMap[t.__viewId];e.group.ignore||(i.push(e),e.group.ignore=!0)}))}));var o="svg"===this._zr.painter.getType()?this.getSvgDataURL():this.renderToCanvas(t).toDataURL("image/"+(t&&t.type||"png"));return R(i,(function(t){t.group.ignore=!1})),o}Qg(this.id)},e.prototype.getConnectedDataURL=function(t){if(!this._disposed){var e="svg"===t.type,n=this.group,i=Math.min,r=Math.max,o=1/0;if(sy[n]){var a=o,s=o,l=-1/0,h=-1/0,c=[],p=t&&t.pixelRatio||this.getDevicePixelRatio();R(ay,(function(o,u){if(o.group===n){var p=e?o.getZr().painter.getSvgDom().innerHTML:o.renderToCanvas(T(t)),f=o.getDom().getBoundingClientRect();a=i(f.left,a),s=i(f.top,s),l=r(f.right,l),h=r(f.bottom,h),c.push({dom:p,left:f.left,top:f.top})}}));var f=(l*=p)-(a*=p),d=(h*=p)-(s*=p),g=u.createCanvas(),y=pr(g,{renderer:e?"svg":"canvas"});if(y.resize({width:f,height:d}),e){var v="";return R(c,(function(t){var e=t.left-a,n=t.top-s;v+='<g transform="translate('+e+","+n+')">'+t.dom+"</g>"})),y.painter.getSvgRoot().innerHTML=v,t.connectedBackgroundColor&&y.painter.setBackgroundColor(t.connectedBackgroundColor),y.refreshImmediately(),y.painter.toDataURL()}return t.connectedBackgroundColor&&y.add(new Qa({shape:{x:0,y:0,width:f,height:d},style:{fill:t.connectedBackgroundColor}})),R(c,(function(t){var e=new Ya({style:{x:t.left*p-a,y:t.top*p-s,image:t.dom}});y.add(e)})),y.refreshImmediately(),g.toDataURL("image/"+(t&&t.type||"png"))}return this.getDataURL(t)}Qg(this.id)},e.prototype.convertToPixel=function(t,e){return Og(this,"convertToPixel",t,e)},e.prototype.convertFromPixel=function(t,e){return Og(this,"convertFromPixel",t,e)},e.prototype.containPixel=function(t,e){var n;if(!this._disposed)return R(to(this._model,t),(function(t,i){i.indexOf("Models")>=0&&R(t,(function(t){var r=t.coordinateSystem;if(r&&r.containPoint)n=n||!!r.containPoint(e);else if("seriesModels"===i){var o=this._chartsMap[t.__viewId];o&&o.containPoint&&(n=n||o.containPoint(e,t))}else 0}),this)}),this),!!n;Qg(this.id)},e.prototype.getVisual=function(t,e){var n=to(this._model,t,{defaultMainType:"series"}),i=n.seriesModel;var r=i.getData(),o=n.hasOwnProperty("dataIndexInside")?n.dataIndexInside:n.hasOwnProperty("dataIndex")?r.indexOfRawIndex(n.dataIndex):null;return null!=o?function(t,e,n){switch(n){case"color":return t.getItemVisual(e,"style")[t.getVisual("drawType")];case"opacity":return t.getItemVisual(e,"style").opacity;case"symbol":case"symbolSize":case"liftZ":return t.getItemVisual(e,n)}}(r,o,e):function(t,e){switch(e){case"color":return t.getVisual("style")[t.getVisual("drawType")];case"opacity":return t.getVisual("style").opacity;case"symbol":case"symbolSize":case"liftZ":return t.getVisual(e)}}(r,e)},e.prototype.getViewOfComponentModel=function(t){return this._componentsMap[t.__viewId]},e.prototype.getViewOfSeriesModel=function(t){return this._chartsMap[t.__viewId]},e.prototype._initEvents=function(){var t,e,n,i=this;R($g,(function(t){var e=function(e){var n,r=i.getModel(),o=e.target,a="globalout"===t;if(a?n={}:o&&Dd(o,(function(t){var e=fs(t);if(e&&null!=e.dataIndex){var i=e.dataModel||r.getSeriesByIndex(e.seriesIndex);return n=i&&i.getDataParams(e.dataIndex,e.dataType,o)||{},!0}if(e.eventData)return n=D({},e.eventData),!0}),!0),n){var s=n.componentType,l=n.componentIndex;"markLine"!==s&&"markPoint"!==s&&"markArea"!==s||(s="series",l=n.seriesIndex);var u=s&&null!=l&&r.getComponent(s,l),h=u&&i["series"===u.mainType?"_chartsMap":"_componentsMap"][u.__viewId];0,n.event=e,n.type=t,i._$eventProcessor.eventInfo={targetEl:o,packedEvent:n,model:u,view:h},i.trigger(t,n)}};e.zrEventfulCallAtLast=!0,i._zr.on(t,e,i)})),R(ty,(function(t,e){i._messageCenter.on(e,(function(t){this.trigger(e,t)}),i)})),R(["selectchanged"],(function(t){i._messageCenter.on(t,(function(e){this.trigger(t,e)}),i)})),t=this._messageCenter,e=this,n=this._api,t.on("selectchanged",(function(t){var i=n.getModel();t.isFromClick?(Cd("map","selectchanged",e,i,t),Cd("pie","selectchanged",e,i,t)):"select"===t.fromAction?(Cd("map","selected",e,i,t),Cd("pie","selected",e,i,t)):"unselect"===t.fromAction&&(Cd("map","unselected",e,i,t),Cd("pie","unselected",e,i,t))}))},e.prototype.isDisposed=function(){return this._disposed},e.prototype.clear=function(){this._disposed?Qg(this.id):this.setOption({series:[]},!0)},e.prototype.dispose=function(){if(this._disposed)Qg(this.id);else{this._disposed=!0,this.getDom()&&ro(this.getDom(),hy,"");var t=this,e=t._api,n=t._model;R(t._componentsViews,(function(t){t.dispose(n,e)})),R(t._chartsViews,(function(t){t.dispose(n,e)})),t._zr.dispose(),t._dom=t._model=t._chartsMap=t._componentsMap=t._chartsViews=t._componentsViews=t._scheduler=t._api=t._zr=t._throttledZrFlush=t._theme=t._coordSysMgr=t._messageCenter=null,delete ay[t.id]}},e.prototype.resize=function(t){if(!this[wg])if(this._disposed)Qg(this.id);else{this._zr.resize(t);var e=this._model;if(this._loadingFX&&this._loadingFX.resize(),e){var n=e.resetOption("media"),i=t&&t.silent;this[bg]&&(null==i&&(i=this[bg].silent),n=!0,this[bg]=null),this[wg]=!0;try{n&&kg(this),Pg.update.call(this,{type:"resize",animation:D({duration:0},t&&t.animation)})}catch(t){throw this[wg]=!1,t}this[wg]=!1,Eg.call(this,i),zg.call(this,i)}}},e.prototype.showLoading=function(t,e){if(this._disposed)Qg(this.id);else if(Y(t)&&(e=t,t=""),t=t||"default",this.hideLoading(),oy[t]){var n=oy[t](this._api,e),i=this._zr;this._loadingFX=n,i.add(n)}},e.prototype.hideLoading=function(){this._disposed?Qg(this.id):(this._loadingFX&&this._zr.remove(this._loadingFX),this._loadingFX=null)},e.prototype.makeActionFromEvent=function(t){var e=D({},t);return e.type=ty[t.type],e},e.prototype.dispatchAction=function(t,e){if(this._disposed)Qg(this.id);else if(Y(e)||(e={silent:!!e}),Jg[t.type]&&this._model)if(this[wg])this._pendingActions.push(t);else{var n=e.silent;Ng.call(this,t,n);var i=e.flush;i?this._zr.flush():!1!==i&&r.browser.weChat&&this._throttledZrFlush(),Eg.call(this,n),zg.call(this,n)}},e.prototype.updateLabelLayout=function(){gg.trigger("series:layoutlabels",this._model,this._api,{updatedSeries:[]})},e.prototype.appendData=function(t){if(this._disposed)Qg(this.id);else{var e=t.seriesIndex,n=this.getModel().getSeriesByIndex(e);0,n.appendData(t),this._scheduler.unfinished=!0,this.getZr().wakeUp()}},e.internalField=function(){function t(t){t.clearColorPalette(),t.eachSeries((function(t){t.clearColorPalette()}))}function e(t){for(var e=[],n=t.currentStates,i=0;i<n.length;i++){var r=n[i];"emphasis"!==r&&"blur"!==r&&"select"!==r&&e.push(r)}t.selected&&t.states.select&&e.push("select"),2===t.hoverState&&t.states.emphasis?e.push("emphasis"):1===t.hoverState&&t.states.blur&&e.push("blur"),t.useStates(e)}function i(t,e){if(!t.preventAutoZ){var n=t.get("z")||0,i=t.get("zlevel")||0;e.eachRendered((function(t){return o(t,n,i,-1/0),!0}))}}function o(t,e,n,i){var r=t.getTextContent(),a=t.getTextGuideLine();if(t.isGroup)for(var s=t.childrenRef(),l=0;l<s.length;l++)i=Math.max(o(s[l],e,n,i),i);else t.z=e,t.zlevel=n,i=Math.max(t.z2,i);if(r&&(r.z=e,r.zlevel=n,isFinite(i)&&(r.z2=i+2)),a){var u=t.textGuideLineConfig;a.z=e,a.zlevel=n,isFinite(i)&&(a.z2=i+(u&&u.showAbove?1:-1))}return i}function a(t,e){e.eachRendered((function(t){if(!xu(t)){var e=t.getTextContent(),n=t.getTextGuideLine();t.stateTransition&&(t.stateTransition=null),e&&e.stateTransition&&(e.stateTransition=null),n&&n.stateTransition&&(n.stateTransition=null),t.hasState()?(t.prevStates=t.currentStates,t.clearStates()):t.prevStates&&(t.prevStates=null)}}))}function s(t,n){var i=t.getModel("stateAnimation"),r=t.isAnimationEnabled(),o=i.get("duration"),a=o>0?{duration:o,delay:i.get("delay"),easing:i.get("easing")}:null;n.eachRendered((function(t){if(t.states&&t.states.emphasis){if(xu(t))return;if(t instanceof Ha&&function(t){var e=ys(t);e.normalFill=t.style.fill,e.normalStroke=t.style.stroke;var n=t.states.select||{};e.selectFill=n.style&&n.style.fill||null,e.selectStroke=n.style&&n.style.stroke||null}(t),t.__dirty){var n=t.prevStates;n&&t.useStates(n)}if(r){t.stateTransition=a;var i=t.getTextContent(),o=t.getTextGuideLine();i&&(i.stateTransition=a),o&&(o.stateTransition=a)}t.__dirty&&e(t)}}))}kg=function(t){var e=t._scheduler;e.restorePipelines(t._model),e.prepareStageTasks(),Ag(t,!0),Ag(t,!1),e.plan()},Ag=function(t,e){for(var n=t._model,i=t._scheduler,r=e?t._componentsViews:t._chartsViews,o=e?t._componentsMap:t._chartsMap,a=t._zr,s=t._api,l=0;l<r.length;l++)r[l].__alive=!1;function u(t){var l=t.__requireNewView;t.__requireNewView=!1;var u="_ec_"+t.id+"_"+t.type,h=!l&&o[u];if(!h){var c=so(t.type),p=e?Pf.getClass(c.main,c.sub):Ef.getClass(c.sub);0,(h=new p).init(n,s),o[u]=h,r.push(h),a.add(h.group)}t.__viewId=h.__id=u,h.__alive=!0,h.__model=t,h.group.__ecComponentInfo={mainType:t.mainType,index:t.componentIndex},!e&&i.prepareView(h,t,n,s)}e?n.eachComponent((function(t,e){"series"!==t&&u(e)})):n.eachSeries(u);for(l=0;l<r.length;){var h=r[l];h.__alive?l++:(!e&&h.renderTask.dispose(),a.remove(h.group),h.dispose(n,s),r.splice(l,1),o[h.__id]===h&&delete o[h.__id],h.__id=h.group.__ecComponentInfo=null)}},Lg=function(t,e,n,i,r){var o=t._model;if(o.setUpdatePayload(n),i){var a={};a[i+"Id"]=n[i+"Id"],a[i+"Index"]=n[i+"Index"],a[i+"Name"]=n[i+"Name"];var s={mainType:i,query:a};r&&(s.subType=r);var l,u=n.excludeSeriesId;null!=u&&(l=ft(),R(Vr(u),(function(t){var e=Zr(t,null);null!=e&&l.set(e,!0)}))),o&&o.eachComponent(s,(function(e){if(!(l&&null!=l.get(e.id)))if(al(n))if(e instanceof Mf)n.type!==xs||n.notBlur||e.get(["emphasis","disabled"])||function(t,e,n){var i=t.seriesIndex,r=t.getData(e.dataType);if(r){var o=$r(r,e);o=(H(o)?o[0]:o)||0;var a=r.getItemGraphicEl(o);if(!a)for(var s=r.count(),l=0;!a&&l<s;)a=r.getItemGraphicEl(l++);if(a){var u=fs(a);qs(i,u.focus,u.blurScope,n)}else{var h=t.get(["emphasis","focus"]),c=t.get(["emphasis","blurScope"]);null!=h&&qs(i,h,c,n)}}}(e,n,t._api);else{var i=js(e.mainType,e.componentIndex,n.name,t._api),r=i.focusSelf,o=i.dispatchers;n.type===xs&&r&&!n.notBlur&&Zs(e.mainType,e.componentIndex,t._api),o&&R(o,(function(t){n.type===xs?Vs(t):Hs(t)}))}else ol(n)&&e instanceof Mf&&(!function(t,e,n){if(ol(e)){var i=e.dataType,r=$r(t.getData(i),e);H(r)||(r=[r]),t[e.type===Ms?"toggleSelect":e.type===bs?"select":"unselect"](r,i)}}(e,n,t._api),Ks(e),Xg(t))}),t),o&&o.eachComponent(s,(function(e){l&&null!=l.get(e.id)||h(t["series"===i?"_chartsMap":"_componentsMap"][e.__viewId])}),t)}else R([].concat(t._componentsViews).concat(t._chartsViews),h);function h(i){i&&i.__alive&&i[e]&&i[e](i.__model,o,t._api,n)}},Pg={prepareAndUpdate:function(t){kg(this),Pg.update.call(this,t,{optionChanged:null!=t.newOption})},update:function(e,n){var i=this._model,r=this._api,o=this._zr,a=this._coordSysMgr,s=this._scheduler;if(i){i.setUpdatePayload(e),s.restoreData(i,e),s.performSeriesTasks(i),a.create(i,r),s.performDataProcessorTasks(i,e),Rg(this,i),a.update(i,r),t(i),s.performVisualTasks(i,e),Vg(this,i,r,e,n);var l=i.get("backgroundColor")||"transparent",u=i.get("darkMode");o.setBackgroundColor(l),null!=u&&"auto"!==u&&o.setDarkMode(u),gg.trigger("afterupdate",i,r)}},updateTransform:function(e){var n=this,i=this._model,r=this._api;if(i){i.setUpdatePayload(e);var o=[];i.eachComponent((function(t,a){if("series"!==t){var s=n.getViewOfComponentModel(a);if(s&&s.__alive)if(s.updateTransform){var l=s.updateTransform(a,i,r,e);l&&l.update&&o.push(s)}else o.push(s)}}));var a=ft();i.eachSeries((function(t){var o=n._chartsMap[t.__viewId];if(o.updateTransform){var s=o.updateTransform(t,i,r,e);s&&s.update&&a.set(t.uid,1)}else a.set(t.uid,1)})),t(i),this._scheduler.performVisualTasks(i,e,{setDirty:!0,dirtyMap:a}),Wg(this,i,r,e,{},a),gg.trigger("afterupdate",i,r)}},updateView:function(e){var n=this._model;n&&(n.setUpdatePayload(e),Ef.markUpdateMethod(e,"updateView"),t(n),this._scheduler.performVisualTasks(n,e,{setDirty:!0}),Vg(this,n,this._api,e,{}),gg.trigger("afterupdate",n,this._api))},updateVisual:function(e){var n=this,i=this._model;i&&(i.setUpdatePayload(e),i.eachSeries((function(t){t.getData().clearAllVisual()})),Ef.markUpdateMethod(e,"updateVisual"),t(i),this._scheduler.performVisualTasks(i,e,{visualType:"visual",setDirty:!0}),i.eachComponent((function(t,r){if("series"!==t){var o=n.getViewOfComponentModel(r);o&&o.__alive&&o.updateVisual(r,i,n._api,e)}})),i.eachSeries((function(t){n._chartsMap[t.__viewId].updateVisual(t,i,n._api,e)})),gg.trigger("afterupdate",i,this._api))},updateLayout:function(t){Pg.update.call(this,t)}},Og=function(t,e,n,i){if(t._disposed)Qg(t.id);else{for(var r,o=t._model,a=t._coordSysMgr.getCoordinateSystems(),s=to(o,n),l=0;l<a.length;l++){var u=a[l];if(u[e]&&null!=(r=u[e](o,s,i)))return r}0}},Rg=function(t,e){var n=t._chartsMap,i=t._scheduler;e.eachSeries((function(t){i.updateStreamModes(t,n[t.__viewId])}))},Ng=function(t,e){var n=this,i=this.getModel(),r=t.type,o=t.escapeConnect,a=Jg[r],s=a.actionInfo,l=(s.update||"update").split(":"),u=l.pop(),h=null!=l[0]&&so(l[0]);this[wg]=!0;var c=[t],p=!1;t.batch&&(p=!0,c=N(t.batch,(function(e){return(e=I(D({},e),t)).batch=null,e})));var f,d=[],g=ol(t),y=al(t);if(y&&Ys(this._api),R(c,(function(e){if((f=(f=a.action(e,n._model,n._api))||D({},e)).type=s.event||f.type,d.push(f),y){var i=eo(t),r=i.queryOptionMap,o=i.mainTypeSpecified?r.keys()[0]:"series";Lg(n,u,e,o),Xg(n)}else g?(Lg(n,u,e,"series"),Xg(n)):h&&Lg(n,u,e,h.main,h.sub)})),"none"!==u&&!y&&!g&&!h)try{this[bg]?(kg(this),Pg.update.call(this,t),this[bg]=null):Pg[u].call(this,t)}catch(t){throw this[wg]=!1,t}if(f=p?{type:s.event||r,escapeConnect:o,batch:d}:d[0],this[wg]=!1,!e){var v=this._messageCenter;if(v.trigger(f.type,f),g){var m={type:"selectchanged",escapeConnect:o,selected:$s(i),isFromClick:t.isFromClick||!1,fromAction:t.type,fromActionPayload:t};v.trigger(m.type,m)}}},Eg=function(t){for(var e=this._pendingActions;e.length;){var n=e.shift();Ng.call(this,n,t)}},zg=function(t){!t&&this.trigger("updated")},Bg=function(t,e){t.on("rendered",(function(n){e.trigger("rendered",n),!t.animation.isFinished()||e[bg]||e._scheduler.unfinished||e._pendingActions.length||e.trigger("finished")}))},Fg=function(t,e){t.on("mouseover",(function(t){var n=Dd(t.target,rl);n&&(!function(t,e,n){var i=fs(t),r=js(i.componentMainType,i.componentIndex,i.componentHighDownName,n),o=r.dispatchers,a=r.focusSelf;o?(a&&Zs(i.componentMainType,i.componentIndex,n),R(o,(function(t){return Bs(t,e)}))):(qs(i.seriesIndex,i.focus,i.blurScope,n),"self"===i.focus&&Zs(i.componentMainType,i.componentIndex,n),Bs(t,e))}(n,t,e._api),Xg(e))})).on("mouseout",(function(t){var n=Dd(t.target,rl);n&&(!function(t,e,n){Ys(n);var i=fs(t),r=js(i.componentMainType,i.componentIndex,i.componentHighDownName,n).dispatchers;r?R(r,(function(t){return Fs(t,e)})):Fs(t,e)}(n,t,e._api),Xg(e))})).on("click",(function(t){var n=Dd(t.target,(function(t){return null!=fs(t).dataIndex}),!0);if(n){var i=n.selected?"unselect":"select",r=fs(n);e._api.dispatchAction({type:i,dataType:r.dataType,dataIndexInside:r.dataIndex,seriesIndex:r.seriesIndex,isFromClick:!0})}}))},Vg=function(t,e,n,i,r){!function(t){var e=[],n=[],i=!1;if(t.eachComponent((function(t,r){var o=r.get("zlevel")||0,a=r.get("z")||0,s=r.getZLevelKey();i=i||!!s,("series"===t?n:e).push({zlevel:o,z:a,idx:r.componentIndex,type:t,key:s})})),i){var r,o,a=e.concat(n);We(a,(function(t,e){return t.zlevel===e.zlevel?t.z-e.z:t.zlevel-e.zlevel})),R(a,(function(e){var n=t.getComponent(e.type,e.idx),i=e.zlevel,a=e.key;null!=r&&(i=Math.max(r,i)),a?(i===r&&a!==o&&i++,o=a):o&&(i===r&&i++,o=""),r=i,n.setZLevel(i)}))}}(e),Hg(t,e,n,i,r),R(t._chartsViews,(function(t){t.__alive=!1})),Wg(t,e,n,i,r),R(t._chartsViews,(function(t){t.__alive||t.remove(e,n)}))},Hg=function(t,e,n,r,o,l){R(l||t._componentsViews,(function(t){var o=t.__model;a(o,t),t.render(o,e,n,r),i(o,t),s(o,t)}))},Wg=function(t,e,n,o,l,u){var h=t._scheduler;l=D(l||{},{updatedSeries:e.getSeries()}),gg.trigger("series:beforeupdate",e,n,l);var c=!1;e.eachSeries((function(e){var n=t._chartsMap[e.__viewId];n.__alive=!0;var i=n.renderTask;h.updatePayload(i,o),a(e,n),u&&u.get(e.uid)&&i.dirty(),i.perform(h.getPerformArgs(i))&&(c=!0),n.group.silent=!!e.get("silent"),function(t,e){var n=t.get("blendMode")||null;e.eachRendered((function(t){t.isGroup||(t.style.blend=n)}))}(e,n),Ks(e)})),h.unfinished=c||h.unfinished,gg.trigger("series:layoutlabels",e,n,l),gg.trigger("series:transition",e,n,l),e.eachSeries((function(e){var n=t._chartsMap[e.__viewId];i(e,n),s(e,n)})),function(t,e){var n=t._zr,i=n.storage,o=0;i.traverse((function(t){t.isGroup||o++})),o>e.get("hoverLayerThreshold")&&!r.node&&!r.worker&&e.eachSeries((function(e){if(!e.preventUsingHoverLayer){var n=t._chartsMap[e.__viewId];n.__alive&&n.eachRendered((function(t){t.states.emphasis&&(t.states.emphasis.hoverLayer=!0)}))}}))}(t,e),gg.trigger("series:afterupdate",e,n,l)},Xg=function(t){t[Sg]=!0,t.getZr().wakeUp()},Yg=function(t){t[Sg]&&(t.getZr().storage.traverse((function(t){xu(t)||e(t)})),t[Sg]=!1)},Gg=function(t){return new(function(e){function i(){return null!==e&&e.apply(this,arguments)||this}return n(i,e),i.prototype.getCoordinateSystems=function(){return t._coordSysMgr.getCoordinateSystems()},i.prototype.getComponentByElement=function(e){for(;e;){var n=e.__ecComponentInfo;if(null!=n)return t._model.getComponent(n.mainType,n.index);e=e.parent}},i.prototype.enterEmphasis=function(e,n){Vs(e,n),Xg(t)},i.prototype.leaveEmphasis=function(e,n){Hs(e,n),Xg(t)},i.prototype.enterBlur=function(e){!function(t){Rs(t,ks)}(e),Xg(t)},i.prototype.leaveBlur=function(e){Ws(e),Xg(t)},i.prototype.enterSelect=function(e){Gs(e),Xg(t)},i.prototype.leaveSelect=function(e){Us(e),Xg(t)},i.prototype.getModel=function(){return t.getModel()},i.prototype.getViewOfComponentModel=function(e){return t.getViewOfComponentModel(e)},i.prototype.getViewOfSeriesModel=function(e){return t.getViewOfSeriesModel(e)},i}(qc))(t)},Ug=function(t){function e(t,e){for(var n=0;n<t.length;n++){t[n][Tg]=e}}R(ty,(function(n,i){t._messageCenter.on(i,(function(n){if(sy[t.group]&&0!==t[Tg]){if(n&&n.escapeConnect)return;var i=t.makeActionFromEvent(n),r=[];R(ay,(function(e){e!==t&&e.group===t.group&&r.push(e)})),e(r,0),R(r,(function(t){1!==t[Tg]&&t.dispatchAction(i)})),e(r,2)}}))}))}}(),e}(Wt),Kg=jg.prototype;Kg.on=Cg("on"),Kg.off=Cg("off"),Kg.one=function(t,e,n){var i=this;Er(),this.on.call(this,t,(function n(){for(var r=[],o=0;o<arguments.length;o++)r[o]=arguments[o];e&&e.apply&&e.apply(this,r),i.off(t,n)}),n)};var $g=["click","dblclick","mouseover","mouseout","mousemove","mousedown","mouseup","globalout","contextmenu"];function Qg(t){0}var Jg={},ty={},ey=[],ny=[],iy=[],ry={},oy={},ay={},sy={},ly=+new Date-0,uy=+new Date-0,hy="_echarts_instance_";function cy(t){sy[t]=!1}var py=cy;function fy(t){return ay[function(t,e){return t.getAttribute?t.getAttribute(e):t[e]}(t,hy)]}function dy(t,e){ry[t]=e}function gy(t){A(ny,t)<0&&ny.push(t)}function yy(t,e){Ty(ey,t,e,2e3)}function vy(t){_y("afterinit",t)}function my(t){_y("afterupdate",t)}function _y(t,e){gg.on(t,e)}function xy(t,e,n){W(e)&&(n=e,e="");var i=Y(t)?t.type:[t,t={event:e}][0];t.event=(t.event||i).toLowerCase(),e=t.event,ty[e]||(ot(Mg.test(i)&&Mg.test(e)),Jg[i]||(Jg[i]={action:n,actionInfo:t}),ty[e]=i)}function wy(t,e){jc.register(t,e)}function by(t,e){Ty(iy,t,e,1e3,"layout")}function Sy(t,e){Ty(iy,t,e,3e3,"visual")}var My=[];function Ty(t,e,n,i,r){if((W(e)||Y(e))&&(n=e,e=i),!(A(My,n)>=0)){My.push(n);var o=Jf.wrapStageHandler(n,r);o.__prio=e,o.__raw=n,t.push(o)}}function Cy(t,e){oy[t]=e}function Dy(t,e,n){var i=vg("registerMap");i&&i(t,e,n)}var Iy=function(t){var e=(t=T(t)).type,n="";e||zr(n);var i=e.split(":");2!==i.length&&zr(n);var r=!1;"echarts"===i[0]&&(e=i[1],r=!0),t.__isBuiltIn=r,nf.set(e,t)};Sy(mg,Zf),Sy(_g,Kf),Sy(_g,$f),Sy(mg,Md),Sy(_g,Td),Sy(7e3,(function(t,e){t.eachRawSeries((function(n){if(!t.isSeriesFiltered(n)){var i=n.getData();i.hasItemVisual()&&i.each((function(t){var n=i.getItemVisual(t,"decal");n&&(i.ensureUniqueItemVisual(t,"style").decal=cg(n,e))}));var r=i.getVisual("decal");if(r)i.getVisual("style").decal=cg(r,e)}}))})),gy(mp),yy(900,(function(t){var e=ft();t.eachSeries((function(t){var n=t.get("stack");if(n){var i=e.get(n)||e.set(n,[]),r=t.getData(),o={stackResultDimension:r.getCalculationInfo("stackResultDimension"),stackedOverDimension:r.getCalculationInfo("stackedOverDimension"),stackedDimension:r.getCalculationInfo("stackedDimension"),stackedByDimension:r.getCalculationInfo("stackedByDimension"),isStackedByIndex:r.getCalculationInfo("isStackedByIndex"),data:r,seriesModel:t};if(!o.stackedDimension||!o.isStackedByIndex&&!o.stackedByDimension)return;i.length&&r.setCalculationInfo("stackedOnSeries",i[i.length-1].seriesModel),i.push(o)}})),e.each(_p)})),Cy("default",(function(t,e){I(e=e||{},{text:"loading",textColor:"#000",fontSize:12,fontWeight:"normal",fontStyle:"normal",fontFamily:"sans-serif",maskColor:"rgba(255, 255, 255, 0.8)",showSpinner:!0,color:"#5470c6",spinnerRadius:10,lineWidth:5,zlevel:0});var n=new sr,i=new Qa({style:{fill:e.maskColor},zlevel:e.zlevel,z:1e4});n.add(i);var r,o=new es({style:{text:e.text,fill:e.textColor,fontSize:e.fontSize,fontWeight:e.fontWeight,fontStyle:e.fontStyle,fontFamily:e.fontFamily},zlevel:e.zlevel,z:10001}),a=new Qa({style:{fill:"none"},textContent:o,textConfig:{position:"right",distance:10},zlevel:e.zlevel,z:10001});return n.add(a),e.showSpinner&&((r=new ru({shape:{startAngle:-Qf/2,endAngle:-Qf/2+.1,r:e.spinnerRadius},style:{stroke:e.color,lineCap:"round",lineWidth:e.lineWidth},zlevel:e.zlevel,z:10001})).animateShape(!0).when(1e3,{endAngle:3*Qf/2}).start("circularInOut"),r.animateShape(!0).when(1e3,{startAngle:3*Qf/2}).delay(300).start("circularInOut"),n.add(r)),n.resize=function(){var n=o.getBoundingRect().width,s=e.showSpinner?e.spinnerRadius:0,l=(t.getWidth()-2*s-(e.showSpinner&&n?10:0)-n)/2-(e.showSpinner&&n?0:5+n/2)+(e.showSpinner?0:n/2)+(n?0:s),u=t.getHeight()/2;e.showSpinner&&r.setShape({cx:l,cy:u}),a.setShape({x:l-s,y:u-s,width:2*s,height:2*s}),i.setShape({x:0,y:0,width:t.getWidth(),height:t.getHeight()})},n.resize(),n})),xy({type:xs,event:xs,update:xs},mt),xy({type:ws,event:ws,update:ws},mt),xy({type:bs,event:bs,update:bs},mt),xy({type:Ss,event:Ss,update:Ss},mt),xy({type:Ms,event:Ms,update:Ms},mt),dy("light",gd),dy("dark",xd);var ky=[],Ay={registerPreprocessor:gy,registerProcessor:yy,registerPostInit:vy,registerPostUpdate:my,registerUpdateLifecycle:_y,registerAction:xy,registerCoordinateSystem:wy,registerLayout:by,registerVisual:Sy,registerTransform:Iy,registerLoading:Cy,registerMap:Dy,registerImpl:function(t,e){yg[t]=e},PRIORITY:xg,ComponentModel:hc,ComponentView:Pf,SeriesModel:Mf,ChartView:Ef,registerComponentModel:function(t){hc.registerClass(t)},registerComponentView:function(t){Pf.registerClass(t)},registerSeriesModel:function(t){Mf.registerClass(t)},registerChartView:function(t){Ef.registerClass(t)},registerSubTypeDefaulter:function(t,e){hc.registerSubTypeDefaulter(t,e)},registerPainter:function(t,e){fr(t,e)}};function Ly(t){H(t)?R(t,(function(t){Ly(t)})):A(ky,t)>=0||(ky.push(t),W(t)&&(t={install:t}),t.install(Ay))}function Py(t){return null==t?0:t.length||1}function Oy(t){return t}var Ry=function(){function t(t,e,n,i,r,o){this._old=t,this._new=e,this._oldKeyGetter=n||Oy,this._newKeyGetter=i||Oy,this.context=r,this._diffModeMultiple="multiple"===o}return t.prototype.add=function(t){return this._add=t,this},t.prototype.update=function(t){return this._update=t,this},t.prototype.updateManyToOne=function(t){return this._updateManyToOne=t,this},t.prototype.updateOneToMany=function(t){return this._updateOneToMany=t,this},t.prototype.updateManyToMany=function(t){return this._updateManyToMany=t,this},t.prototype.remove=function(t){return this._remove=t,this},t.prototype.execute=function(){this[this._diffModeMultiple?"_executeMultiple":"_executeOneToOne"]()},t.prototype._executeOneToOne=function(){var t=this._old,e=this._new,n={},i=new Array(t.length),r=new Array(e.length);this._initIndexMap(t,null,i,"_oldKeyGetter"),this._initIndexMap(e,n,r,"_newKeyGetter");for(var o=0;o<t.length;o++){var a=i[o],s=n[a],l=Py(s);if(l>1){var u=s.shift();1===s.length&&(n[a]=s[0]),this._update&&this._update(u,o)}else 1===l?(n[a]=null,this._update&&this._update(s,o)):this._remove&&this._remove(o)}this._performRestAdd(r,n)},t.prototype._executeMultiple=function(){var t=this._old,e=this._new,n={},i={},r=[],o=[];this._initIndexMap(t,n,r,"_oldKeyGetter"),this._initIndexMap(e,i,o,"_newKeyGetter");for(var a=0;a<r.length;a++){var s=r[a],l=n[s],u=i[s],h=Py(l),c=Py(u);if(h>1&&1===c)this._updateManyToOne&&this._updateManyToOne(u,l),i[s]=null;else if(1===h&&c>1)this._updateOneToMany&&this._updateOneToMany(u,l),i[s]=null;else if(1===h&&1===c)this._update&&this._update(u,l),i[s]=null;else if(h>1&&c>1)this._updateManyToMany&&this._updateManyToMany(u,l),i[s]=null;else if(h>1)for(var p=0;p<h;p++)this._remove&&this._remove(l[p]);else this._remove&&this._remove(l)}this._performRestAdd(o,i)},t.prototype._performRestAdd=function(t,e){for(var n=0;n<t.length;n++){var i=t[n],r=e[i],o=Py(r);if(o>1)for(var a=0;a<o;a++)this._add&&this._add(r[a]);else 1===o&&this._add&&this._add(r);e[i]=null}},t.prototype._initIndexMap=function(t,e,n,i){for(var r=this._diffModeMultiple,o=0;o<t.length;o++){var a="_ec_"+this[i](t[o],o);if(r||(n[o]=a),e){var s=e[a],l=Py(s);0===l?(e[a]=o,r&&n.push(a)):1===l?e[a]=[s,o]:s.push(o)}}},t}(),Ny=function(){function t(t,e){this._encode=t,this._schema=e}return t.prototype.get=function(){return{fullDimensions:this._getFullDimensionNames(),encode:this._encode}},t.prototype._getFullDimensionNames=function(){return this._cachedDimNames||(this._cachedDimNames=this._schema?this._schema.makeOutputDimensionNames():[]),this._cachedDimNames},t}();function Ey(t,e){return t.hasOwnProperty(e)||(t[e]=[]),t[e]}var zy=function(t){this.otherDims={},null!=t&&D(this,t)},By=Qr(),Fy={float:"f",int:"i",ordinal:"o",number:"n",time:"t"},Vy=function(){function t(t){this.dimensions=t.dimensions,this._dimOmitted=t.dimensionOmitted,this.source=t.source,this._fullDimCount=t.fullDimensionCount,this._updateDimOmitted(t.dimensionOmitted)}return t.prototype.isDimensionOmitted=function(){return this._dimOmitted},t.prototype._updateDimOmitted=function(t){this._dimOmitted=t,t&&(this._dimNameMap||(this._dimNameMap=Gy(this.source)))},t.prototype.getSourceDimensionIndex=function(t){return et(this._dimNameMap.get(t),-1)},t.prototype.getSourceDimension=function(t){var e=this.source.dimensionsDefine;if(e)return e[t]},t.prototype.makeStoreSchema=function(){for(var t=this._fullDimCount,e=Pp(this.source),n=!Uy(t),i="",r=[],o=0,a=0;o<t;o++){var s=void 0,l=void 0,u=void 0,h=this.dimensions[a];if(h&&h.storeDimIndex===o)s=e?h.name:null,l=h.type,u=h.ordinalMeta,a++;else{var c=this.getSourceDimension(o);c&&(s=e?c.name:null,l=c.type)}r.push({property:s,type:l,ordinalMeta:u}),!e||null==s||h&&h.isCalculationCoord||(i+=n?s.replace(/\`/g,"`1").replace(/\$/g,"`2"):s),i+="$",i+=Fy[l]||"f",u&&(i+=u.uid),i+="$"}var p=this.source;return{dimensions:r,hash:[p.seriesLayoutBy,p.startIndex,i].join("$$")}},t.prototype.makeOutputDimensionNames=function(){for(var t=[],e=0,n=0;e<this._fullDimCount;e++){var i=void 0,r=this.dimensions[n];if(r&&r.storeDimIndex===e)r.isCalculationCoord||(i=r.name),n++;else{var o=this.getSourceDimension(e);o&&(i=o.name)}t.push(i)}return t},t.prototype.appendCalculationDimension=function(t){this.dimensions.push(t),t.isCalculationCoord=!0,this._fullDimCount++,this._updateDimOmitted(!0)},t}();function Hy(t){return t instanceof Vy}function Wy(t){for(var e=ft(),n=0;n<(t||[]).length;n++){var i=t[n],r=Y(i)?i.name:i;null!=r&&null==e.get(r)&&e.set(r,n)}return e}function Gy(t){var e=By(t);return e.dimNameMap||(e.dimNameMap=Wy(t.dimensionsDefine))}function Uy(t){return t>30}var Xy,Yy,qy,Zy,jy,Ky,$y,Qy=Y,Jy=N,tv="undefined"==typeof Int32Array?Array:Int32Array,ev=["hasItemOption","_nameList","_idList","_invertedIndicesMap","_dimSummary","userOutput","_rawData","_dimValueGetter","_nameDimIdx","_idDimIdx","_nameRepeatCount"],nv=["_approximateExtent"],iv=function(){function t(t,e){var n;this.type="list",this._dimOmitted=!1,this._nameList=[],this._idList=[],this._visual={},this._layout={},this._itemVisuals=[],this._itemLayouts=[],this._graphicEls=[],this._approximateExtent={},this._calculationInfo={},this.hasItemOption=!1,this.TRANSFERABLE_METHODS=["cloneShallow","downSample","minmaxDownSample","lttbDownSample","map"],this.CHANGABLE_METHODS=["filterSelf","selectRange"],this.DOWNSAMPLE_METHODS=["downSample","minmaxDownSample","lttbDownSample"];var i=!1;Hy(t)?(n=t.dimensions,this._dimOmitted=t.isDimensionOmitted(),this._schema=t):(i=!0,n=t),n=n||["x","y"];for(var r={},o=[],a={},s=!1,l={},u=0;u<n.length;u++){var h=n[u],c=G(h)?new zy({name:h}):h instanceof zy?h:new zy(h),p=c.name;c.type=c.type||"float",c.coordDim||(c.coordDim=p,c.coordDimIndex=0);var f=c.otherDims=c.otherDims||{};o.push(p),r[p]=c,null!=l[p]&&(s=!0),c.createInvertedIndices&&(a[p]=[]),0===f.itemName&&(this._nameDimIdx=u),0===f.itemId&&(this._idDimIdx=u),i&&(c.storeDimIndex=u)}if(this.dimensions=o,this._dimInfos=r,this._initGetDimensionInfo(s),this.hostModel=e,this._invertedIndicesMap=a,this._dimOmitted){var d=this._dimIdxToName=ft();R(o,(function(t){d.set(r[t].storeDimIndex,t)}))}}return t.prototype.getDimension=function(t){var e=this._recognizeDimIndex(t);if(null==e)return t;if(e=t,!this._dimOmitted)return this.dimensions[e];var n=this._dimIdxToName.get(e);if(null!=n)return n;var i=this._schema.getSourceDimension(e);return i?i.name:void 0},t.prototype.getDimensionIndex=function(t){var e=this._recognizeDimIndex(t);if(null!=e)return e;if(null==t)return-1;var n=this._getDimInfo(t);return n?n.storeDimIndex:this._dimOmitted?this._schema.getSourceDimensionIndex(t):-1},t.prototype._recognizeDimIndex=function(t){if(X(t)||null!=t&&!isNaN(t)&&!this._getDimInfo(t)&&(!this._dimOmitted||this._schema.getSourceDimensionIndex(t)<0))return+t},t.prototype._getStoreDimIndex=function(t){var e=this.getDimensionIndex(t);return e},t.prototype.getDimensionInfo=function(t){return this._getDimInfo(this.getDimension(t))},t.prototype._initGetDimensionInfo=function(t){var e=this._dimInfos;this._getDimInfo=t?function(t){return e.hasOwnProperty(t)?e[t]:void 0}:function(t){return e[t]}},t.prototype.getDimensionsOnCoord=function(){return this._dimSummary.dataDimsOnCoord.slice()},t.prototype.mapDimension=function(t,e){var n=this._dimSummary;if(null==e)return n.encodeFirstDimNotExtra[t];var i=n.encode[t];return i?i[e]:null},t.prototype.mapDimensionsAll=function(t){return(this._dimSummary.encode[t]||[]).slice()},t.prototype.getStore=function(){return this._store},t.prototype.initData=function(t,e,n){var i,r=this;if(t instanceof gf&&(i=t),!i){var o=this.dimensions,a=Cp(t)||O(t)?new Op(t,o.length):t;i=new gf;var s=Jy(o,(function(t){return{type:r._dimInfos[t].type,property:t}}));i.initData(a,s,n)}this._store=i,this._nameList=(e||[]).slice(),this._idList=[],this._nameRepeatCount={},this._doInit(0,i.count()),this._dimSummary=function(t,e){var n={},i=n.encode={},r=ft(),o=[],a=[],s={};R(t.dimensions,(function(e){var n,l=t.getDimensionInfo(e),u=l.coordDim;if(u){var h=l.coordDimIndex;Ey(i,u)[h]=e,l.isExtraCoord||(r.set(u,1),"ordinal"!==(n=l.type)&&"time"!==n&&(o[0]=e),Ey(s,u)[h]=t.getDimensionIndex(l.name)),l.defaultTooltip&&a.push(e)}dc.each((function(t,e){var n=Ey(i,e),r=l.otherDims[e];null!=r&&!1!==r&&(n[r]=l.name)}))}));var l=[],u={};r.each((function(t,e){var n=i[e];u[e]=n[0],l=l.concat(n)})),n.dataDimsOnCoord=l,n.dataDimIndicesOnCoord=N(l,(function(e){return t.getDimensionInfo(e).storeDimIndex})),n.encodeFirstDimNotExtra=u;var h=i.label;h&&h.length&&(o=h.slice());var c=i.tooltip;return c&&c.length?a=c.slice():a.length||(a=o.slice()),i.defaultedLabel=o,i.defaultedTooltip=a,n.userOutput=new Ny(s,e),n}(this,this._schema),this.userOutput=this._dimSummary.userOutput},t.prototype.appendData=function(t){var e=this._store.appendData(t);this._doInit(e[0],e[1])},t.prototype.appendValues=function(t,e){var n=this._store.appendValues(t,e&&e.length),i=n.start,r=n.end,o=this._shouldMakeIdFromName();if(this._updateOrdinalMeta(),e)for(var a=i;a<r;a++){var s=a-i;this._nameList[a]=e[s],o&&$y(this,a)}},t.prototype._updateOrdinalMeta=function(){for(var t=this._store,e=this.dimensions,n=0;n<e.length;n++){var i=this._dimInfos[e[n]];i.ordinalMeta&&t.collectOrdinalMeta(i.storeDimIndex,i.ordinalMeta)}},t.prototype._shouldMakeIdFromName=function(){var t=this._store.getProvider();return null==this._idDimIdx&&t.getSource().sourceFormat!==_c&&!t.fillStorage},t.prototype._doInit=function(t,e){if(!(t>=e)){var n=this._store.getProvider();this._updateOrdinalMeta();var i=this._nameList,r=this._idList;if(n.getSource().sourceFormat===gc&&!n.pure)for(var o=[],a=t;a<e;a++){var s=n.getItem(a,o);if(!this.hasItemOption&&Ur(s)&&(this.hasItemOption=!0),s){var l=s.name;null==i[a]&&null!=l&&(i[a]=Zr(l,null));var u=s.id;null==r[a]&&null!=u&&(r[a]=Zr(u,null))}}if(this._shouldMakeIdFromName())for(a=t;a<e;a++)$y(this,a);Xy(this)}},t.prototype.getApproximateExtent=function(t){return this._approximateExtent[t]||this._store.getDataExtent(this._getStoreDimIndex(t))},t.prototype.setApproximateExtent=function(t,e){e=this.getDimension(e),this._approximateExtent[e]=t.slice()},t.prototype.getCalculationInfo=function(t){return this._calculationInfo[t]},t.prototype.setCalculationInfo=function(t,e){Qy(t)?D(this._calculationInfo,t):this._calculationInfo[t]=e},t.prototype.getName=function(t){var e=this.getRawIndex(t),n=this._nameList[e];return null==n&&null!=this._nameDimIdx&&(n=qy(this,this._nameDimIdx,e)),null==n&&(n=""),n},t.prototype._getCategory=function(t,e){var n=this._store.get(t,e),i=this._store.getOrdinalMeta(t);return i?i.categories[n]:n},t.prototype.getId=function(t){return Yy(this,this.getRawIndex(t))},t.prototype.count=function(){return this._store.count()},t.prototype.get=function(t,e){var n=this._store,i=this._dimInfos[t];if(i)return n.get(i.storeDimIndex,e)},t.prototype.getByRawIndex=function(t,e){var n=this._store,i=this._dimInfos[t];if(i)return n.getByRawIndex(i.storeDimIndex,e)},t.prototype.getIndices=function(){return this._store.getIndices()},t.prototype.getDataExtent=function(t){return this._store.getDataExtent(this._getStoreDimIndex(t))},t.prototype.getSum=function(t){return this._store.getSum(this._getStoreDimIndex(t))},t.prototype.getMedian=function(t){return this._store.getMedian(this._getStoreDimIndex(t))},t.prototype.getValues=function(t,e){var n=this,i=this._store;return H(t)?i.getValues(Jy(t,(function(t){return n._getStoreDimIndex(t)})),e):i.getValues(t)},t.prototype.hasValue=function(t){for(var e=this._dimSummary.dataDimIndicesOnCoord,n=0,i=e.length;n<i;n++)if(isNaN(this._store.get(e[n],t)))return!1;return!0},t.prototype.indexOfName=function(t){for(var e=0,n=this._store.count();e<n;e++)if(this.getName(e)===t)return e;return-1},t.prototype.getRawIndex=function(t){return this._store.getRawIndex(t)},t.prototype.indexOfRawIndex=function(t){return this._store.indexOfRawIndex(t)},t.prototype.rawIndexOf=function(t,e){var n=t&&this._invertedIndicesMap[t];var i=n&&n[e];return null==i||isNaN(i)?-1:i},t.prototype.indicesOfNearest=function(t,e,n){return this._store.indicesOfNearest(this._getStoreDimIndex(t),e,n)},t.prototype.each=function(t,e,n){W(t)&&(n=e,e=t,t=[]);var i=n||this,r=Jy(Zy(t),this._getStoreDimIndex,this);this._store.each(r,i?F(e,i):e)},t.prototype.filterSelf=function(t,e,n){W(t)&&(n=e,e=t,t=[]);var i=n||this,r=Jy(Zy(t),this._getStoreDimIndex,this);return this._store=this._store.filter(r,i?F(e,i):e),this},t.prototype.selectRange=function(t){var e=this,n={};return R(B(t),(function(i){var r=e._getStoreDimIndex(i);n[r]=t[i]})),this._store=this._store.selectRange(n),this},t.prototype.mapArray=function(t,e,n){W(t)&&(n=e,e=t,t=[]),n=n||this;var i=[];return this.each(t,(function(){i.push(e&&e.apply(this,arguments))}),n),i},t.prototype.map=function(t,e,n,i){var r=n||i||this,o=Jy(Zy(t),this._getStoreDimIndex,this),a=Ky(this);return a._store=this._store.map(o,r?F(e,r):e),a},t.prototype.modify=function(t,e,n,i){var r=n||i||this;var o=Jy(Zy(t),this._getStoreDimIndex,this);this._store.modify(o,r?F(e,r):e)},t.prototype.downSample=function(t,e,n,i){var r=Ky(this);return r._store=this._store.downSample(this._getStoreDimIndex(t),e,n,i),r},t.prototype.minmaxDownSample=function(t,e){var n=Ky(this);return n._store=this._store.minmaxDownSample(this._getStoreDimIndex(t),e),n},t.prototype.lttbDownSample=function(t,e){var n=Ky(this);return n._store=this._store.lttbDownSample(this._getStoreDimIndex(t),e),n},t.prototype.getRawDataItem=function(t){return this._store.getRawDataItem(t)},t.prototype.getItemModel=function(t){var e=this.hostModel,n=this.getRawDataItem(t);return new uh(n,e,e&&e.ecModel)},t.prototype.diff=function(t){var e=this;return new Ry(t?t.getStore().getIndices():[],this.getStore().getIndices(),(function(e){return Yy(t,e)}),(function(t){return Yy(e,t)}))},t.prototype.getVisual=function(t){var e=this._visual;return e&&e[t]},t.prototype.setVisual=function(t,e){this._visual=this._visual||{},Qy(t)?D(this._visual,t):this._visual[t]=e},t.prototype.getItemVisual=function(t,e){var n=this._itemVisuals[t],i=n&&n[e];return null==i?this.getVisual(e):i},t.prototype.hasItemVisual=function(){return this._itemVisuals.length>0},t.prototype.ensureUniqueItemVisual=function(t,e){var n=this._itemVisuals,i=n[t];i||(i=n[t]={});var r=i[e];return null==r&&(H(r=this.getVisual(e))?r=r.slice():Qy(r)&&(r=D({},r)),i[e]=r),r},t.prototype.setItemVisual=function(t,e,n){var i=this._itemVisuals[t]||{};this._itemVisuals[t]=i,Qy(e)?D(i,e):i[e]=n},t.prototype.clearAllVisual=function(){this._visual={},this._itemVisuals=[]},t.prototype.setLayout=function(t,e){Qy(t)?D(this._layout,t):this._layout[t]=e},t.prototype.getLayout=function(t){return this._layout[t]},t.prototype.getItemLayout=function(t){return this._itemLayouts[t]},t.prototype.setItemLayout=function(t,e,n){this._itemLayouts[t]=n?D(this._itemLayouts[t]||{},e):e},t.prototype.clearItemLayouts=function(){this._itemLayouts.length=0},t.prototype.setItemGraphicEl=function(t,e){!function(t,e,n,i){if(i){var r=fs(i);r.dataIndex=n,r.dataType=e,r.seriesIndex=t,r.ssrType="chart","group"===i.type&&i.traverse((function(i){var r=fs(i);r.seriesIndex=t,r.dataIndex=n,r.dataType=e,r.ssrType="chart"}))}}(this.hostModel&&this.hostModel.seriesIndex,this.dataType,t,e),this._graphicEls[t]=e},t.prototype.getItemGraphicEl=function(t){return this._graphicEls[t]},t.prototype.eachItemGraphicEl=function(t,e){R(this._graphicEls,(function(n,i){n&&t&&t.call(e,n,i)}))},t.prototype.cloneShallow=function(e){return e||(e=new t(this._schema?this._schema:Jy(this.dimensions,this._getDimInfo,this),this.hostModel)),jy(e,this),e._store=this._store,e},t.prototype.wrapMethod=function(t,e){var n=this[t];W(n)&&(this.__wrappedMethods=this.__wrappedMethods||[],this.__wrappedMethods.push(t),this[t]=function(){var t=n.apply(this,arguments);return e.apply(this,[t].concat(it(arguments)))})},t.internalField=(Xy=function(t){var e=t._invertedIndicesMap;R(e,(function(n,i){var r=t._dimInfos[i],o=r.ordinalMeta,a=t._store;if(o){n=e[i]=new tv(o.categories.length);for(var s=0;s<n.length;s++)n[s]=-1;for(s=0;s<a.count();s++)n[a.get(r.storeDimIndex,s)]=s}}))},qy=function(t,e,n){return Zr(t._getCategory(e,n),null)},Yy=function(t,e){var n=t._idList[e];return null==n&&null!=t._idDimIdx&&(n=qy(t,t._idDimIdx,e)),null==n&&(n="e\0\0"+e),n},Zy=function(t){return H(t)||(t=null!=t?[t]:[]),t},Ky=function(e){var n=new t(e._schema?e._schema:Jy(e.dimensions,e._getDimInfo,e),e.hostModel);return jy(n,e),n},jy=function(t,e){R(ev.concat(e.__wrappedMethods||[]),(function(n){e.hasOwnProperty(n)&&(t[n]=e[n])})),t.__wrappedMethods=e.__wrappedMethods,R(nv,(function(n){t[n]=T(e[n])})),t._calculationInfo=D({},e._calculationInfo)},void($y=function(t,e){var n=t._nameList,i=t._idList,r=t._nameDimIdx,o=t._idDimIdx,a=n[e],s=i[e];if(null==a&&null!=r&&(n[e]=a=qy(t,r,e)),null==s&&null!=o&&(i[e]=s=qy(t,o,e)),null==s&&null!=a){var l=t._nameRepeatCount,u=l[a]=(l[a]||0)+1;s=a,u>1&&(s+="__ec__"+u),i[e]=s}})),t}();function rv(t,e){Cp(t)||(t=Ip(t));var n=(e=e||{}).coordDimensions||[],i=e.dimensionsDefine||t.dimensionsDefine||[],r=ft(),o=[],a=function(t,e,n,i){var r=Math.max(t.dimensionsDetectedCount||1,e.length,n.length,i||0);return R(e,(function(t){var e;Y(t)&&(e=t.dimsDef)&&(r=Math.max(r,e.length))})),r}(t,n,i,e.dimensionsCount),s=e.canOmitUnusedDimensions&&Uy(a),l=i===t.dimensionsDefine,u=l?Gy(t):Wy(i),h=e.encodeDefine;!h&&e.encodeDefaulter&&(h=e.encodeDefaulter(t,a));for(var c=ft(h),p=new hf(a),f=0;f<p.length;f++)p[f]=-1;function d(t){var e=p[t];if(e<0){var n=i[t],r=Y(n)?n:{name:n},a=new zy,s=r.name;null!=s&&null!=u.get(s)&&(a.name=a.displayName=s),null!=r.type&&(a.type=r.type),null!=r.displayName&&(a.displayName=r.displayName);var l=o.length;return p[t]=l,a.storeDimIndex=t,o.push(a),a}return o[e]}if(!s)for(f=0;f<a;f++)d(f);c.each((function(t,e){var n=Vr(t).slice();if(1===n.length&&!G(n[0])&&n[0]<0)c.set(e,!1);else{var i=c.set(e,[]);R(n,(function(t,n){var r=G(t)?u.get(t):t;null!=r&&r<a&&(i[n]=r,y(d(r),e,n))}))}}));var g=0;function y(t,e,n){null!=dc.get(e)?t.otherDims[e]=n:(t.coordDim=e,t.coordDimIndex=n,r.set(e,!0))}R(n,(function(t){var e,n,i,r;if(G(t))e=t,r={};else{e=(r=t).name;var o=r.ordinalMeta;r.ordinalMeta=null,(r=D({},r)).ordinalMeta=o,n=r.dimsDef,i=r.otherDims,r.name=r.coordDim=r.coordDimIndex=r.dimsDef=r.otherDims=null}var s=c.get(e);if(!1!==s){if(!(s=Vr(s)).length)for(var u=0;u<(n&&n.length||1);u++){for(;g<a&&null!=d(g).coordDim;)g++;g<a&&s.push(g++)}R(s,(function(t,o){var a=d(t);if(l&&null!=r.type&&(a.type=r.type),y(I(a,r),e,o),null==a.name&&n){var s=n[o];!Y(s)&&(s={name:s}),a.name=a.displayName=s.name,a.defaultTooltip=s.defaultTooltip}i&&I(a.otherDims,i)}))}}));var v=e.generateCoord,m=e.generateCoordCount,_=null!=m;m=v?m||1:0;var x=v||"value";function w(t){null==t.name&&(t.name=t.coordDim)}if(s)R(o,(function(t){w(t)})),o.sort((function(t,e){return t.storeDimIndex-e.storeDimIndex}));else for(var b=0;b<a;b++){var S=d(b);null==S.coordDim&&(S.coordDim=ov(x,r,_),S.coordDimIndex=0,(!v||m<=0)&&(S.isExtraCoord=!0),m--),w(S),null!=S.type||Ac(t,b)!==Sc&&(!S.isExtraCoord||null==S.otherDims.itemName&&null==S.otherDims.seriesName)||(S.type="ordinal")}return function(t){for(var e=ft(),n=0;n<t.length;n++){var i=t[n],r=i.name,o=e.get(r)||0;o>0&&(i.name=r+(o-1)),o++,e.set(r,o)}}(o),new Vy({source:t,dimensions:o,fullDimensionCount:a,dimensionOmitted:s})}function ov(t,e,n){if(n||e.hasKey(t)){for(var i=0;e.hasKey(t+i);)i++;t+=i}return e.set(t,!0),t}var av=function(t){this.coordSysDims=[],this.axisMap=ft(),this.categoryAxisMap=ft(),this.coordSysName=t};var sv={cartesian2d:function(t,e,n,i){var r=t.getReferringComponents("xAxis",no).models[0],o=t.getReferringComponents("yAxis",no).models[0];e.coordSysDims=["x","y"],n.set("x",r),n.set("y",o),lv(r)&&(i.set("x",r),e.firstCategoryDimIndex=0),lv(o)&&(i.set("y",o),null==e.firstCategoryDimIndex&&(e.firstCategoryDimIndex=1))},singleAxis:function(t,e,n,i){var r=t.getReferringComponents("singleAxis",no).models[0];e.coordSysDims=["single"],n.set("single",r),lv(r)&&(i.set("single",r),e.firstCategoryDimIndex=0)},polar:function(t,e,n,i){var r=t.getReferringComponents("polar",no).models[0],o=r.findAxisModel("radiusAxis"),a=r.findAxisModel("angleAxis");e.coordSysDims=["radius","angle"],n.set("radius",o),n.set("angle",a),lv(o)&&(i.set("radius",o),e.firstCategoryDimIndex=0),lv(a)&&(i.set("angle",a),null==e.firstCategoryDimIndex&&(e.firstCategoryDimIndex=1))},geo:function(t,e,n,i){e.coordSysDims=["lng","lat"]},parallel:function(t,e,n,i){var r=t.ecModel,o=r.getComponent("parallel",t.get("parallelIndex")),a=e.coordSysDims=o.dimensions.slice();R(o.parallelAxisIndex,(function(t,o){var s=r.getComponent("parallelAxis",t),l=a[o];n.set(l,s),lv(s)&&(i.set(l,s),null==e.firstCategoryDimIndex&&(e.firstCategoryDimIndex=o))}))}};function lv(t){return"category"===t.get("type")}function uv(t,e,n){var i,r,o,a=(n=n||{}).byIndex,s=n.stackedCoordDimension;!function(t){return!Hy(t.schema)}(e)?(r=e.schema,i=r.dimensions,o=e.store):i=e;var l,u,h,c,p=!(!t||!t.get("stack"));if(R(i,(function(t,e){G(t)&&(i[e]=t={name:t}),p&&!t.isExtraCoord&&(a||l||!t.ordinalMeta||(l=t),u||"ordinal"===t.type||"time"===t.type||s&&s!==t.coordDim||(u=t))})),!u||a||l||(a=!0),u){h="__\0ecstackresult_"+t.id,c="__\0ecstackedover_"+t.id,l&&(l.createInvertedIndices=!0);var f=u.coordDim,d=u.type,g=0;R(i,(function(t){t.coordDim===f&&g++}));var y={name:h,coordDim:f,coordDimIndex:g,type:d,isExtraCoord:!0,isCalculationCoord:!0,storeDimIndex:i.length},v={name:c,coordDim:c,coordDimIndex:g+1,type:d,isExtraCoord:!0,isCalculationCoord:!0,storeDimIndex:i.length+1};r?(o&&(y.storeDimIndex=o.ensureCalculationDimension(c,d),v.storeDimIndex=o.ensureCalculationDimension(h,d)),r.appendCalculationDimension(y),r.appendCalculationDimension(v)):(i.push(y),i.push(v))}return{stackedDimension:u&&u.name,stackedByDimension:l&&l.name,isStackedByIndex:a,stackedOverDimension:c,stackResultDimension:h}}function hv(t,e){return!!e&&e===t.getCalculationInfo("stackedDimension")}function cv(t,e){return hv(t,e)?t.getCalculationInfo("stackResultDimension"):e}function pv(t,e,n){n=n||{};var i,r=e.getSourceManager(),o=!1;t?(o=!0,i=Ip(t)):o=(i=r.getSource()).sourceFormat===gc;var a=function(t){var e=t.get("coordinateSystem"),n=new av(e),i=sv[e];if(i)return i(t,n,n.axisMap,n.categoryAxisMap),n}(e),s=function(t,e){var n,i=t.get("coordinateSystem"),r=jc.get(i);return e&&e.coordSysDims&&(n=N(e.coordSysDims,(function(t){var n={name:t},i=e.axisMap.get(t);if(i){var r=i.get("type");n.type=function(t){return"category"===t?"ordinal":"time"===t?"time":"float"}(r)}return n}))),n||(n=r&&(r.getDimensionsInfo?r.getDimensionsInfo():r.dimensions.slice())||["x","y"]),n}(e,a),l=n.useEncodeDefaulter,u=W(l)?l:l?V(Dc,s,e):null,h=rv(i,{coordDimensions:s,generateCoord:n.generateCoord,encodeDefine:e.getEncode(),encodeDefaulter:u,canOmitUnusedDimensions:!o}),c=function(t,e,n){var i,r;return n&&R(t,(function(t,o){var a=t.coordDim,s=n.categoryAxisMap.get(a);s&&(null==i&&(i=o),t.ordinalMeta=s.getOrdinalMeta(),e&&(t.createInvertedIndices=!0)),null!=t.otherDims.itemName&&(r=!0)})),r||null==i||(t[i].otherDims.itemName=0),i}(h.dimensions,n.createInvertedIndices,a),p=o?null:r.getSharedDataStore(h),f=uv(e,{schema:h,store:p}),d=new iv(h,e);d.setCalculationInfo(f);var g=null!=c&&function(t){if(t.sourceFormat===gc){return!H(Gr(function(t){var e=0;for(;e<t.length&&null==t[e];)e++;return t[e]}(t.data||[])))}}(i)?function(t,e,n,i){return i===c?n:this.defaultDimValueGetter(t,e,n,i)}:null;return d.hasItemOption=!1,d.initData(o?i:p,null,g),d}var fv=function(){function t(t){this._setting=t||{},this._extent=[1/0,-1/0]}return t.prototype.getSetting=function(t){return this._setting[t]},t.prototype.unionExtent=function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1])},t.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},t.prototype.getExtent=function(){return this._extent.slice()},t.prototype.setExtent=function(t,e){var n=this._extent;isNaN(t)||(n[0]=t),isNaN(e)||(n[1]=e)},t.prototype.isInExtentRange=function(t){return this._extent[0]<=t&&this._extent[1]>=t},t.prototype.isBlank=function(){return this._isBlank},t.prototype.setBlank=function(t){this._isBlank=t},t}();fo(fv);var dv=0,gv=function(){function t(t){this.categories=t.categories||[],this._needCollect=t.needCollect,this._deduplication=t.deduplication,this.uid=++dv}return t.createByAxisModel=function(e){var n=e.option,i=n.data,r=i&&N(i,yv);return new t({categories:r,needCollect:!r,deduplication:!1!==n.dedplication})},t.prototype.getOrdinal=function(t){return this._getOrCreateMap().get(t)},t.prototype.parseAndCollect=function(t){var e,n=this._needCollect;if(!G(t)&&!n)return t;if(n&&!this._deduplication)return e=this.categories.length,this.categories[e]=t,e;var i=this._getOrCreateMap();return null==(e=i.get(t))&&(n?(e=this.categories.length,this.categories[e]=t,i.set(t,e)):e=NaN),e},t.prototype._getOrCreateMap=function(){return this._map||(this._map=ft(this.categories))},t}();function yv(t){return Y(t)&&null!=t.value?t.value:t+""}function vv(t){return"interval"===t.type||"log"===t.type}function mv(t,e,n,i){var r={},o=t[1]-t[0],a=r.interval=Lr(o/e,!0);null!=n&&a<n&&(a=r.interval=n),null!=i&&a>i&&(a=r.interval=i);var s=r.intervalPrecision=xv(a);return function(t,e){!isFinite(t[0])&&(t[0]=e[0]),!isFinite(t[1])&&(t[1]=e[1]),wv(t,0,e),wv(t,1,e),t[0]>t[1]&&(t[0]=t[1])}(r.niceTickExtent=[_r(Math.ceil(t[0]/a)*a,s),_r(Math.floor(t[1]/a)*a,s)],t),r}function _v(t){var e=Math.pow(10,Ar(t)),n=t/e;return n?2===n?n=3:3===n?n=5:n*=2:n=1,_r(n*e)}function xv(t){return xr(t)+2}function wv(t,e,n){t[e]=Math.max(Math.min(t[e],n[1]),n[0])}function bv(t,e){return t>=e[0]&&t<=e[1]}function Sv(t,e){return e[1]===e[0]?.5:(t-e[0])/(e[1]-e[0])}function Mv(t,e){return t*(e[1]-e[0])+e[0]}var Tv=function(t){function e(e){var n=t.call(this,e)||this;n.type="ordinal";var i=n.getSetting("ordinalMeta");return i||(i=new gv({})),H(i)&&(i=new gv({categories:N(i,(function(t){return Y(t)?t.value:t}))})),n._ordinalMeta=i,n._extent=n.getSetting("extent")||[0,i.categories.length-1],n}return n(e,t),e.prototype.parse=function(t){return null==t?NaN:G(t)?this._ordinalMeta.getOrdinal(t):Math.round(t)},e.prototype.contain=function(t){return bv(t=this.parse(t),this._extent)&&null!=this._ordinalMeta.categories[t]},e.prototype.normalize=function(t){return Sv(t=this._getTickNumber(this.parse(t)),this._extent)},e.prototype.scale=function(t){return t=Math.round(Mv(t,this._extent)),this.getRawOrdinalNumber(t)},e.prototype.getTicks=function(){for(var t=[],e=this._extent,n=e[0];n<=e[1];)t.push({value:n}),n++;return t},e.prototype.getMinorTicks=function(t){},e.prototype.setSortInfo=function(t){if(null!=t){for(var e=t.ordinalNumbers,n=this._ordinalNumbersByTick=[],i=this._ticksByOrdinalNumber=[],r=0,o=this._ordinalMeta.categories.length,a=Math.min(o,e.length);r<a;++r){var s=e[r];n[r]=s,i[s]=r}for(var l=0;r<o;++r){for(;null!=i[l];)l++;n.push(l),i[l]=r}}else this._ordinalNumbersByTick=this._ticksByOrdinalNumber=null},e.prototype._getTickNumber=function(t){var e=this._ticksByOrdinalNumber;return e&&t>=0&&t<e.length?e[t]:t},e.prototype.getRawOrdinalNumber=function(t){var e=this._ordinalNumbersByTick;return e&&t>=0&&t<e.length?e[t]:t},e.prototype.getLabel=function(t){if(!this.isBlank()){var e=this.getRawOrdinalNumber(t.value),n=this._ordinalMeta.categories[e];return null==n?"":n+""}},e.prototype.count=function(){return this._extent[1]-this._extent[0]+1},e.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},e.prototype.isInExtentRange=function(t){return t=this._getTickNumber(t),this._extent[0]<=t&&this._extent[1]>=t},e.prototype.getOrdinalMeta=function(){return this._ordinalMeta},e.prototype.calcNiceTicks=function(){},e.prototype.calcNiceExtent=function(){},e.type="ordinal",e}(fv);fv.registerClass(Tv);var Cv=_r,Dv=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="interval",e._interval=0,e._intervalPrecision=2,e}return n(e,t),e.prototype.parse=function(t){return t},e.prototype.contain=function(t){return bv(t,this._extent)},e.prototype.normalize=function(t){return Sv(t,this._extent)},e.prototype.scale=function(t){return Mv(t,this._extent)},e.prototype.setExtent=function(t,e){var n=this._extent;isNaN(t)||(n[0]=parseFloat(t)),isNaN(e)||(n[1]=parseFloat(e))},e.prototype.unionExtent=function(t){var e=this._extent;t[0]<e[0]&&(e[0]=t[0]),t[1]>e[1]&&(e[1]=t[1]),this.setExtent(e[0],e[1])},e.prototype.getInterval=function(){return this._interval},e.prototype.setInterval=function(t){this._interval=t,this._niceExtent=this._extent.slice(),this._intervalPrecision=xv(t)},e.prototype.getTicks=function(t){var e=this._interval,n=this._extent,i=this._niceExtent,r=this._intervalPrecision,o=[];if(!e)return o;n[0]<i[0]&&(t?o.push({value:Cv(i[0]-e,r)}):o.push({value:n[0]}));for(var a=i[0];a<=i[1]&&(o.push({value:a}),(a=Cv(a+e,r))!==o[o.length-1].value);)if(o.length>1e4)return[];var s=o.length?o[o.length-1].value:i[1];return n[1]>s&&(t?o.push({value:Cv(s+e,r)}):o.push({value:n[1]})),o},e.prototype.getMinorTicks=function(t){for(var e=this.getTicks(!0),n=[],i=this.getExtent(),r=1;r<e.length;r++){for(var o=e[r],a=e[r-1],s=0,l=[],u=(o.value-a.value)/t;s<t-1;){var h=Cv(a.value+(s+1)*u);h>i[0]&&h<i[1]&&l.push(h),s++}n.push(l)}return n},e.prototype.getLabel=function(t,e){if(null==t)return"";var n=e&&e.precision;return null==n?n=xr(t.value)||0:"auto"===n&&(n=this._intervalPrecision),jh(Cv(t.value,n,!0))},e.prototype.calcNiceTicks=function(t,e,n){t=t||5;var i=this._extent,r=i[1]-i[0];if(isFinite(r)){r<0&&(r=-r,i.reverse());var o=mv(i,t,e,n);this._intervalPrecision=o.intervalPrecision,this._interval=o.interval,this._niceExtent=o.niceTickExtent}},e.prototype.calcNiceExtent=function(t){var e=this._extent;if(e[0]===e[1])if(0!==e[0]){var n=Math.abs(e[0]);t.fixMax||(e[1]+=n/2),e[0]-=n/2}else e[1]=1;var i=e[1]-e[0];isFinite(i)||(e[0]=0,e[1]=1),this.calcNiceTicks(t.splitNumber,t.minInterval,t.maxInterval);var r=this._interval;t.fixMin||(e[0]=Cv(Math.floor(e[0]/r)*r)),t.fixMax||(e[1]=Cv(Math.ceil(e[1]/r)*r))},e.prototype.setNiceExtent=function(t,e){this._niceExtent=[t,e]},e.type="interval",e}(fv);fv.registerClass(Dv);var Iv="undefined"!=typeof Float32Array,kv=Iv?Float32Array:Array;function Av(t){return H(t)?Iv?new Float32Array(t):t:new kv(t)}function Lv(t){return t.get("stack")||"__ec_stack_"+t.seriesIndex}function Pv(t){return t.dim+t.index}function Ov(t,e){var n=[];return e.eachSeriesByType(t,(function(t){Ev(t)&&n.push(t)})),n}function Rv(t){var e=function(t){var e={};R(t,(function(t){var n=t.coordinateSystem.getBaseAxis();if("time"===n.type||"value"===n.type)for(var i=t.getData(),r=n.dim+"_"+n.index,o=i.getDimensionIndex(i.mapDimension(n.dim)),a=i.getStore(),s=0,l=a.count();s<l;++s){var u=a.get(o,s);e[r]?e[r].push(u):e[r]=[u]}}));var n={};for(var i in e)if(e.hasOwnProperty(i)){var r=e[i];if(r){r.sort((function(t,e){return t-e}));for(var o=null,a=1;a<r.length;++a){var s=r[a]-r[a-1];s>0&&(o=null===o?s:Math.min(o,s))}n[i]=o}}return n}(t),n=[];return R(t,(function(t){var i,r=t.coordinateSystem.getBaseAxis(),o=r.getExtent();if("category"===r.type)i=r.getBandWidth();else if("value"===r.type||"time"===r.type){var a=r.dim+"_"+r.index,s=e[a],l=Math.abs(o[1]-o[0]),u=r.scale.getExtent(),h=Math.abs(u[1]-u[0]);i=s?l/h*s:l}else{var c=t.getData();i=Math.abs(o[1]-o[0])/c.count()}var p=mr(t.get("barWidth"),i),f=mr(t.get("barMaxWidth"),i),d=mr(t.get("barMinWidth")||(zv(t)?.5:1),i),g=t.get("barGap"),y=t.get("barCategoryGap");n.push({bandWidth:i,barWidth:p,barMaxWidth:f,barMinWidth:d,barGap:g,barCategoryGap:y,axisKey:Pv(r),stackId:Lv(t)})})),function(t){var e={};R(t,(function(t,n){var i=t.axisKey,r=t.bandWidth,o=e[i]||{bandWidth:r,remainedWidth:r,autoWidthCount:0,categoryGap:null,gap:"20%",stacks:{}},a=o.stacks;e[i]=o;var s=t.stackId;a[s]||o.autoWidthCount++,a[s]=a[s]||{width:0,maxWidth:0};var l=t.barWidth;l&&!a[s].width&&(a[s].width=l,l=Math.min(o.remainedWidth,l),o.remainedWidth-=l);var u=t.barMaxWidth;u&&(a[s].maxWidth=u);var h=t.barMinWidth;h&&(a[s].minWidth=h);var c=t.barGap;null!=c&&(o.gap=c);var p=t.barCategoryGap;null!=p&&(o.categoryGap=p)}));var n={};return R(e,(function(t,e){n[e]={};var i=t.stacks,r=t.bandWidth,o=t.categoryGap;if(null==o){var a=B(i).length;o=Math.max(35-4*a,15)+"%"}var s=mr(o,r),l=mr(t.gap,1),u=t.remainedWidth,h=t.autoWidthCount,c=(u-s)/(h+(h-1)*l);c=Math.max(c,0),R(i,(function(t){var e=t.maxWidth,n=t.minWidth;if(t.width){i=t.width;e&&(i=Math.min(i,e)),n&&(i=Math.max(i,n)),t.width=i,u-=i+l*i,h--}else{var i=c;e&&e<i&&(i=Math.min(e,u)),n&&n>i&&(i=n),i!==c&&(t.width=i,u-=i+l*i,h--)}})),c=(u-s)/(h+(h-1)*l),c=Math.max(c,0);var p,f=0;R(i,(function(t,e){t.width||(t.width=c),p=t,f+=t.width*(1+l)})),p&&(f-=p.width*l);var d=-f/2;R(i,(function(t,i){n[e][i]=n[e][i]||{bandWidth:r,offset:d,width:t.width},d+=t.width*(1+l)}))})),n}(n)}function Nv(t,e){var n=Ov(t,e),i=Rv(n);R(n,(function(t){var e=t.getData(),n=t.coordinateSystem.getBaseAxis(),r=Lv(t),o=i[Pv(n)][r],a=o.offset,s=o.width;e.setLayout({bandWidth:o.bandWidth,offset:a,size:s})}))}function Ev(t){return t.coordinateSystem&&"cartesian2d"===t.coordinateSystem.type}function zv(t){return t.pipelineContext&&t.pipelineContext.large}var Bv=function(t){function e(e){var n=t.call(this,e)||this;return n.type="time",n}return n(e,t),e.prototype.getLabel=function(t){var e=this.getSetting("useUTC");return Ph(t.value,Ch[function(t){switch(t){case"year":case"month":return"day";case"millisecond":return"millisecond";default:return"second"}}(Ah(this._minLevelUnit))]||Ch.second,e,this.getSetting("locale"))},e.prototype.getFormattedLabel=function(t,e,n){var i=this.getSetting("useUTC");return function(t,e,n,i,r){var o=null;if(G(n))o=n;else if(W(n))o=n(t.value,e,{level:t.level});else{var a=D({},Mh);if(t.level>0)for(var s=0;s<Dh.length;++s)a[Dh[s]]="{primary|"+a[Dh[s]]+"}";var l=n?!1===n.inherit?n:I(n,a):a,u=Oh(t.value,r);if(l[u])o=l[u];else if(l.inherit){for(s=Ih.indexOf(u)-1;s>=0;--s)if(l[u]){o=l[u];break}o=o||a.none}if(H(o)){var h=null==t.level?0:t.level>=0?t.level:o.length+t.level;o=o[h=Math.min(h,o.length-1)]}}return Ph(new Date(t.value),o,r,i)}(t,e,n,this.getSetting("locale"),i)},e.prototype.getTicks=function(){var t=this._interval,e=this._extent,n=[];if(!t)return n;n.push({value:e[0],level:0});var i=this.getSetting("useUTC"),r=function(t,e,n,i){var r=1e4,o=Ih,a=0;function s(t,e,n,r,o,a,s){for(var l=new Date(e),u=e,h=l[r]();u<n&&u<=i[1];)s.push({value:u}),h+=t,l[o](h),u=l.getTime();s.push({value:u,notAdd:!0})}function l(t,r,o){var a=[],l=!r.length;if(!function(t,e,n,i){var r=Ir(e),o=Ir(n),a=function(t){return Rh(r,t,i)===Rh(o,t,i)},s=function(){return a("year")},l=function(){return s()&&a("month")},u=function(){return l()&&a("day")},h=function(){return u()&&a("hour")},c=function(){return h()&&a("minute")},p=function(){return c()&&a("second")},f=function(){return p()&&a("millisecond")};switch(t){case"year":return s();case"month":return l();case"day":return u();case"hour":return h();case"minute":return c();case"second":return p();case"millisecond":return f()}}(Ah(t),i[0],i[1],n)){l&&(r=[{value:Xv(new Date(i[0]),t,n)},{value:i[1]}]);for(var u=0;u<r.length-1;u++){var h=r[u].value,c=r[u+1].value;if(h!==c){var p=void 0,f=void 0,d=void 0,g=!1;switch(t){case"year":p=Math.max(1,Math.round(e/bh/365)),f=Nh(n),d=Wh(n);break;case"half-year":case"quarter":case"month":p=Hv(e),f=Eh(n),d=Gh(n);break;case"week":case"half-week":case"day":p=Vv(e),f=zh(n),d=Uh(n),g=!0;break;case"half-day":case"quarter-day":case"hour":p=Wv(e),f=Bh(n),d=Xh(n);break;case"minute":p=Gv(e,!0),f=Fh(n),d=Yh(n);break;case"second":p=Gv(e,!1),f=Vh(n),d=qh(n);break;case"millisecond":p=Uv(e),f=Hh(n),d=Zh(n)}s(p,h,c,f,d,g,a),"year"===t&&o.length>1&&0===u&&o.unshift({value:o[0].value-p})}}for(u=0;u<a.length;u++)o.push(a[u]);return a}}for(var u=[],h=[],c=0,p=0,f=0;f<o.length&&a++<r;++f){var d=Ah(o[f]);if(Lh(o[f]))if(l(o[f],u[u.length-1]||[],h),d!==(o[f+1]?Ah(o[f+1]):null)){if(h.length){p=c,h.sort((function(t,e){return t.value-e.value}));for(var g=[],y=0;y<h.length;++y){var v=h[y].value;0!==y&&h[y-1].value===v||(g.push(h[y]),v>=i[0]&&v<=i[1]&&c++)}var m=(i[1]-i[0])/e;if(c>1.5*m&&p>m/1.5)break;if(u.push(g),c>m||t===o[f])break}h=[]}}0;var _=z(N(u,(function(t){return z(t,(function(t){return t.value>=i[0]&&t.value<=i[1]&&!t.notAdd}))})),(function(t){return t.length>0})),x=[],w=_.length-1;for(f=0;f<_.length;++f)for(var b=_[f],S=0;S<b.length;++S)x.push({value:b[S].value,level:w-f});x.sort((function(t,e){return t.value-e.value}));var M=[];for(f=0;f<x.length;++f)0!==f&&x[f].value===x[f-1].value||M.push(x[f]);return M}(this._minLevelUnit,this._approxInterval,i,e);return(n=n.concat(r)).push({value:e[1],level:0}),n},e.prototype.calcNiceExtent=function(t){var e=this._extent;if(e[0]===e[1]&&(e[0]-=bh,e[1]+=bh),e[1]===-1/0&&e[0]===1/0){var n=new Date;e[1]=+new Date(n.getFullYear(),n.getMonth(),n.getDate()),e[0]=e[1]-bh}this.calcNiceTicks(t.splitNumber,t.minInterval,t.maxInterval)},e.prototype.calcNiceTicks=function(t,e,n){t=t||10;var i=this._extent,r=i[1]-i[0];this._approxInterval=r/t,null!=e&&this._approxInterval<e&&(this._approxInterval=e),null!=n&&this._approxInterval>n&&(this._approxInterval=n);var o=Fv.length,a=Math.min(function(t,e,n,i){for(;n<i;){var r=n+i>>>1;t[r][1]<e?n=r+1:i=r}return n}(Fv,this._approxInterval,0,o),o-1);this._interval=Fv[a][1],this._minLevelUnit=Fv[Math.max(a-1,0)][0]},e.prototype.parse=function(t){return X(t)?t:+Ir(t)},e.prototype.contain=function(t){return bv(this.parse(t),this._extent)},e.prototype.normalize=function(t){return Sv(this.parse(t),this._extent)},e.prototype.scale=function(t){return Mv(t,this._extent)},e.type="time",e}(Dv),Fv=[["second",_h],["minute",xh],["hour",wh],["quarter-day",216e5],["half-day",432e5],["day",10368e4],["half-week",3024e5],["week",6048e5],["month",26784e5],["quarter",8208e6],["half-year",Sh/2],["year",Sh]];function Vv(t,e){return(t/=bh)>16?16:t>7.5?7:t>3.5?4:t>1.5?2:1}function Hv(t){return(t/=2592e6)>6?6:t>3?3:t>2?2:1}function Wv(t){return(t/=wh)>12?12:t>6?6:t>3.5?4:t>2?2:1}function Gv(t,e){return(t/=e?xh:_h)>30?30:t>20?20:t>15?15:t>10?10:t>5?5:t>2?2:1}function Uv(t){return Lr(t,!0)}function Xv(t,e,n){var i=new Date(t);switch(Ah(e)){case"year":case"month":i[Gh(n)](0);case"day":i[Uh(n)](1);case"hour":i[Xh(n)](0);case"minute":i[Yh(n)](0);case"second":i[qh(n)](0),i[Zh(n)](0)}return i.getTime()}fv.registerClass(Bv);var Yv=fv.prototype,qv=Dv.prototype,Zv=_r,jv=Math.floor,Kv=Math.ceil,$v=Math.pow,Qv=Math.log,Jv=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="log",e.base=10,e._originalScale=new Dv,e._interval=0,e}return n(e,t),e.prototype.getTicks=function(t){var e=this._originalScale,n=this._extent,i=e.getExtent();return N(qv.getTicks.call(this,t),(function(t){var e=t.value,r=_r($v(this.base,e));return r=e===n[0]&&this._fixMin?em(r,i[0]):r,{value:r=e===n[1]&&this._fixMax?em(r,i[1]):r}}),this)},e.prototype.setExtent=function(t,e){var n=Qv(this.base);t=Qv(Math.max(0,t))/n,e=Qv(Math.max(0,e))/n,qv.setExtent.call(this,t,e)},e.prototype.getExtent=function(){var t=this.base,e=Yv.getExtent.call(this);e[0]=$v(t,e[0]),e[1]=$v(t,e[1]);var n=this._originalScale.getExtent();return this._fixMin&&(e[0]=em(e[0],n[0])),this._fixMax&&(e[1]=em(e[1],n[1])),e},e.prototype.unionExtent=function(t){this._originalScale.unionExtent(t);var e=this.base;t[0]=Qv(t[0])/Qv(e),t[1]=Qv(t[1])/Qv(e),Yv.unionExtent.call(this,t)},e.prototype.unionExtentFromData=function(t,e){this.unionExtent(t.getApproximateExtent(e))},e.prototype.calcNiceTicks=function(t){t=t||10;var e=this._extent,n=e[1]-e[0];if(!(n===1/0||n<=0)){var i=kr(n);for(t/n*i<=.5&&(i*=10);!isNaN(i)&&Math.abs(i)<1&&Math.abs(i)>0;)i*=10;var r=[_r(Kv(e[0]/i)*i),_r(jv(e[1]/i)*i)];this._interval=i,this._niceExtent=r}},e.prototype.calcNiceExtent=function(t){qv.calcNiceExtent.call(this,t),this._fixMin=t.fixMin,this._fixMax=t.fixMax},e.prototype.parse=function(t){return t},e.prototype.contain=function(t){return bv(t=Qv(t)/Qv(this.base),this._extent)},e.prototype.normalize=function(t){return Sv(t=Qv(t)/Qv(this.base),this._extent)},e.prototype.scale=function(t){return t=Mv(t,this._extent),$v(this.base,t)},e.type="log",e}(fv),tm=Jv.prototype;function em(t,e){return Zv(t,xr(e))}tm.getMinorTicks=qv.getMinorTicks,tm.getLabel=qv.getLabel,fv.registerClass(Jv);var nm=function(){function t(t,e,n){this._prepareParams(t,e,n)}return t.prototype._prepareParams=function(t,e,n){n[1]<n[0]&&(n=[NaN,NaN]),this._dataMin=n[0],this._dataMax=n[1];var i=this._isOrdinal="ordinal"===t.type;this._needCrossZero="interval"===t.type&&e.getNeedCrossZero&&e.getNeedCrossZero();var r=e.get("min",!0);null==r&&(r=e.get("startValue",!0));var o=this._modelMinRaw=r;W(o)?this._modelMinNum=om(t,o({min:n[0],max:n[1]})):"dataMin"!==o&&(this._modelMinNum=om(t,o));var a=this._modelMaxRaw=e.get("max",!0);if(W(a)?this._modelMaxNum=om(t,a({min:n[0],max:n[1]})):"dataMax"!==a&&(this._modelMaxNum=om(t,a)),i)this._axisDataLen=e.getCategories().length;else{var s=e.get("boundaryGap"),l=H(s)?s:[s||0,s||0];"boolean"==typeof l[0]||"boolean"==typeof l[1]?this._boundaryGapInner=[0,0]:this._boundaryGapInner=[ji(l[0],1),ji(l[1],1)]}},t.prototype.calculate=function(){var t=this._isOrdinal,e=this._dataMin,n=this._dataMax,i=this._axisDataLen,r=this._boundaryGapInner,o=t?null:n-e||Math.abs(e),a="dataMin"===this._modelMinRaw?e:this._modelMinNum,s="dataMax"===this._modelMaxRaw?n:this._modelMaxNum,l=null!=a,u=null!=s;null==a&&(a=t?i?0:NaN:e-r[0]*o),null==s&&(s=t?i?i-1:NaN:n+r[1]*o),(null==a||!isFinite(a))&&(a=NaN),(null==s||!isFinite(s))&&(s=NaN);var h=J(a)||J(s)||t&&!i;this._needCrossZero&&(a>0&&s>0&&!l&&(a=0),a<0&&s<0&&!u&&(s=0));var c=this._determinedMin,p=this._determinedMax;return null!=c&&(a=c,l=!0),null!=p&&(s=p,u=!0),{min:a,max:s,minFixed:l,maxFixed:u,isBlank:h}},t.prototype.modifyDataMinMax=function(t,e){this[rm[t]]=e},t.prototype.setDeterminedMinMax=function(t,e){var n=im[t];this[n]=e},t.prototype.freeze=function(){this.frozen=!0},t}(),im={min:"_determinedMin",max:"_determinedMax"},rm={min:"_dataMin",max:"_dataMax"};function om(t,e){return null==e?null:J(e)?NaN:t.parse(e)}function am(t,e){var n=t.type,i=function(t,e,n){var i=t.rawExtentInfo;return i||(i=new nm(t,e,n),t.rawExtentInfo=i,i)}(t,e,t.getExtent()).calculate();t.setBlank(i.isBlank);var r=i.min,o=i.max,a=e.ecModel;if(a&&"time"===n){var s=Ov("bar",a),l=!1;if(R(s,(function(t){l=l||t.getBaseAxis()===e.axis})),l){var u=Rv(s),h=function(t,e,n,i){var r=n.axis.getExtent(),o=Math.abs(r[1]-r[0]),a=function(t,e,n){if(t&&e){var i=t[Pv(e)];return null!=i&&null!=n?i[Lv(n)]:i}}(i,n.axis);if(void 0===a)return{min:t,max:e};var s=1/0;R(a,(function(t){s=Math.min(t.offset,s)}));var l=-1/0;R(a,(function(t){l=Math.max(t.offset+t.width,l)})),s=Math.abs(s),l=Math.abs(l);var u=s+l,h=e-t,c=h/(1-(s+l)/o)-h;return e+=c*(l/u),t-=c*(s/u),{min:t,max:e}}(r,o,e,u);r=h.min,o=h.max}}return{extent:[r,o],fixMin:i.minFixed,fixMax:i.maxFixed}}function sm(t,e){var n=e,i=am(t,n),r=i.extent,o=n.get("splitNumber");t instanceof Jv&&(t.base=n.get("logBase"));var a=t.type,s=n.get("interval"),l="interval"===a||"time"===a;t.setExtent(r[0],r[1]),t.calcNiceExtent({splitNumber:o,fixMin:i.fixMin,fixMax:i.fixMax,minInterval:l?n.get("minInterval"):null,maxInterval:l?n.get("maxInterval"):null}),null!=s&&t.setInterval&&t.setInterval(s)}function lm(t,e){if(e=e||t.get("type"))switch(e){case"category":return new Tv({ordinalMeta:t.getOrdinalMeta?t.getOrdinalMeta():t.getCategories(),extent:[1/0,-1/0]});case"time":return new Bv({locale:t.ecModel.getLocaleModel(),useUTC:t.ecModel.get("useUTC")});default:return new(fv.getClass(e)||Dv)}}function um(t){var e,n,i=t.getLabelModel().get("formatter"),r="category"===t.type?t.scale.getExtent()[0]:null;return"time"===t.scale.type?(n=i,function(e,i){return t.scale.getFormattedLabel(e,i,n)}):G(i)?function(e){return function(n){var i=t.scale.getLabel(n);return e.replace("{value}",null!=i?i:"")}}(i):W(i)?(e=i,function(n,i){return null!=r&&(i=n.value-r),e(function(t,e){return"category"===t.type?t.scale.getLabel(e):e.value}(t,n),i,null!=n.level?{level:n.level}:null)}):function(e){return t.scale.getLabel(e)}}function hm(t,e){var n=e*Math.PI/180,i=t.width,r=t.height,o=i*Math.abs(Math.cos(n))+Math.abs(r*Math.sin(n)),a=i*Math.abs(Math.sin(n))+Math.abs(r*Math.cos(n));return new Ce(t.x,t.y,o,a)}function cm(t){var e=t.get("interval");return null==e?"auto":e}function pm(t){return"category"===t.type&&0===cm(t.getLabelModel())}var fm=function(){function t(){}return t.prototype.getNeedCrossZero=function(){return!this.option.scale},t.prototype.getCoordSysModel=function(){},t}();var dm={isDimensionStacked:hv,enableDataStack:uv,getStackedDimension:cv};var gm=Object.freeze({__proto__:null,createList:function(t){return pv(null,t)},getLayoutRect:oc,dataStack:dm,createScale:function(t,e){var n=e;e instanceof uh||(n=new uh(e));var i=lm(n);return i.setExtent(t[0],t[1]),sm(i,n),i},mixinAxisModelCommonMethods:function(t){P(t,fm)},getECData:fs,createTextStyle:function(t,e){return Uu(t,null,null,"normal"!==(e=e||{}).state)},createDimensions:function(t,e){return rv(t,e).dimensions},createSymbol:Fd,enableHoverEmphasis:Qs});function ym(t,e){return Math.abs(t-e)<1e-8}function vm(t,e,n){var i=0,r=t[0];if(!r)return!1;for(var o=1;o<t.length;o++){var a=t[o];i+=ka(r[0],r[1],a[0],a[1],e,n),r=a}var s=t[0];return ym(r[0],s[0])&&ym(r[1],s[1])||(i+=ka(r[0],r[1],s[0],s[1],e,n)),0!==i}var mm=[];function _m(t,e){for(var n=0;n<t.length;n++)Et(t[n],t[n],e)}function xm(t,e,n,i){for(var r=0;r<t.length;r++){var o=t[r];i&&(o=i.project(o)),o&&isFinite(o[0])&&isFinite(o[1])&&(zt(e,e,o),Bt(n,n,o))}}var wm=function(){function t(t){this.name=t}return t.prototype.setCenter=function(t){this._center=t},t.prototype.getCenter=function(){var t=this._center;return t||(t=this._center=this.calcCenter()),t},t}(),bm=function(t,e){this.type="polygon",this.exterior=t,this.interiors=e},Sm=function(t){this.type="linestring",this.points=t},Mm=function(t){function e(e,n,i){var r=t.call(this,e)||this;return r.type="geoJSON",r.geometries=n,r._center=i&&[i[0],i[1]],r}return n(e,t),e.prototype.calcCenter=function(){for(var t,e=this.geometries,n=0,i=0;i<e.length;i++){var r=e[i],o=r.exterior,a=o&&o.length;a>n&&(t=r,n=a)}if(t)return function(t){for(var e=0,n=0,i=0,r=t.length,o=t[r-1][0],a=t[r-1][1],s=0;s<r;s++){var l=t[s][0],u=t[s][1],h=o*u-l*a;e+=h,n+=(o+l)*h,i+=(a+u)*h,o=l,a=u}return e?[n/e/3,i/e/3,e]:[t[0][0]||0,t[0][1]||0]}(t.exterior);var s=this.getBoundingRect();return[s.x+s.width/2,s.y+s.height/2]},e.prototype.getBoundingRect=function(t){var e=this._rect;if(e&&!t)return e;var n=[1/0,1/0],i=[-1/0,-1/0];return R(this.geometries,(function(e){"polygon"===e.type?xm(e.exterior,n,i,t):R(e.points,(function(e){xm(e,n,i,t)}))})),isFinite(n[0])&&isFinite(n[1])&&isFinite(i[0])&&isFinite(i[1])||(n[0]=n[1]=i[0]=i[1]=0),e=new Ce(n[0],n[1],i[0]-n[0],i[1]-n[1]),t||(this._rect=e),e},e.prototype.contain=function(t){var e=this.getBoundingRect(),n=this.geometries;if(!e.contain(t[0],t[1]))return!1;t:for(var i=0,r=n.length;i<r;i++){var o=n[i];if("polygon"===o.type){var a=o.exterior,s=o.interiors;if(vm(a,t[0],t[1])){for(var l=0;l<(s?s.length:0);l++)if(vm(s[l],t[0],t[1]))continue t;return!0}}}return!1},e.prototype.transformTo=function(t,e,n,i){var r=this.getBoundingRect(),o=r.width/r.height;n?i||(i=n/o):n=o*i;for(var a=new Ce(t,e,n,i),s=r.calculateTransform(a),l=this.geometries,u=0;u<l.length;u++){var h=l[u];"polygon"===h.type?(_m(h.exterior,s),R(h.interiors,(function(t){_m(t,s)}))):R(h.points,(function(t){_m(t,s)}))}(r=this._rect).copy(a),this._center=[r.x+r.width/2,r.y+r.height/2]},e.prototype.cloneShallow=function(t){null==t&&(t=this.name);var n=new e(t,this.geometries,this._center);return n._rect=this._rect,n.transformTo=null,n},e}(wm);!function(t){function e(e,n){var i=t.call(this,e)||this;return i.type="geoSVG",i._elOnlyForCalculate=n,i}n(e,t),e.prototype.calcCenter=function(){for(var t=this._elOnlyForCalculate,e=t.getBoundingRect(),n=[e.x+e.width/2,e.y+e.height/2],i=ue(mm),r=t;r&&!r.isGeoSVGGraphicRoot;)ce(i,r.getLocalTransform(),i),r=r.parent;return ge(i,i),Et(n,n,i),n}}(wm);function Tm(t,e,n){for(var i=0;i<t.length;i++)t[i]=Cm(t[i],e[i],n)}function Cm(t,e,n){for(var i=[],r=e[0],o=e[1],a=0;a<t.length;a+=2){var s=t.charCodeAt(a)-64,l=t.charCodeAt(a+1)-64;s=s>>1^-(1&s),l=l>>1^-(1&l),r=s+=r,o=l+=o,i.push([s/n,l/n])}return i}function Dm(t,e){return N(z((t=function(t){if(!t.UTF8Encoding)return t;var e=t,n=e.UTF8Scale;return null==n&&(n=1024),R(e.features,(function(t){var e=t.geometry,i=e.encodeOffsets,r=e.coordinates;if(i)switch(e.type){case"LineString":e.coordinates=Cm(r,i,n);break;case"Polygon":case"MultiLineString":Tm(r,i,n);break;case"MultiPolygon":R(r,(function(t,e){return Tm(t,i[e],n)}))}})),e.UTF8Encoding=!1,e}(t)).features,(function(t){return t.geometry&&t.properties&&t.geometry.coordinates.length>0})),(function(t){var n=t.properties,i=t.geometry,r=[];switch(i.type){case"Polygon":var o=i.coordinates;r.push(new bm(o[0],o.slice(1)));break;case"MultiPolygon":R(i.coordinates,(function(t){t[0]&&r.push(new bm(t[0],t.slice(1)))}));break;case"LineString":r.push(new Sm([i.coordinates]));break;case"MultiLineString":r.push(new Sm(i.coordinates))}var a=new Mm(n[e||"name"],r,n.cp);return a.properties=n,a}))}var Im=Object.freeze({__proto__:null,linearMap:vr,round:_r,asc:function(t){return t.sort((function(t,e){return t-e})),t},getPrecision:xr,getPrecisionSafe:wr,getPixelPrecision:br,getPercentWithPrecision:function(t,e,n){return t[e]&&Sr(t,n)[e]||0},MAX_SAFE_INTEGER:9007199254740991,remRadian:Tr,isRadianAroundZero:Cr,parseDate:Ir,quantity:kr,quantityExponent:Ar,nice:Lr,quantile:function(t,e){var n=(t.length-1)*e+1,i=Math.floor(n),r=+t[i-1],o=n-i;return o?r+o*(t[i]-r):r},reformIntervals:function(t){t.sort((function(t,e){return s(t,e,0)?-1:1}));for(var e=-1/0,n=1,i=0;i<t.length;){for(var r=t[i].interval,o=t[i].close,a=0;a<2;a++)r[a]<=e&&(r[a]=e,o[a]=a?1:1-n),e=r[a],n=o[a];r[0]===r[1]&&o[0]*o[1]!=1?t.splice(i,1):i++}return t;function s(t,e,n){return t.interval[n]<e.interval[n]||t.interval[n]===e.interval[n]&&(t.close[n]-e.close[n]==(n?-1:1)||!n&&s(t,e,1))}},isNumeric:Or,numericToNumber:Pr}),km=Object.freeze({__proto__:null,parse:Ir,format:Ph}),Am=Object.freeze({__proto__:null,extendShape:function(t){return Ha.extend(t)},extendPath:function(t,e){return Iu(t,e)},makePath:Au,makeImage:Lu,mergePath:Ou,resizePath:Ru,createIcon:function(t,e,n){var i=D({rectHover:!0},e),r=i.style={strokeNoScale:!0};if(n=n||{x:-1,y:-1,width:2,height:2},t)return 0===t.indexOf("image://")?(r.image=t.slice(8),I(r,n),new Ya(i)):Au(t.replace("path://",""),i,n,"center")},updateProps:mu,initProps:_u,getTransform:function(t,e){for(var n=ue([]);t&&t!==e;)ce(n,t.getLocalTransform(),n),t=t.parent;return n},clipPointsByRect:function(t,e){return N(t,(function(t){var n=t[0];n=Tu(n,e.x),n=Cu(n,e.x+e.width);var i=t[1];return i=Tu(i,e.y),[n,i=Cu(i,e.y+e.height)]}))},clipRectByRect:function(t,e){var n=Tu(t.x,e.x),i=Cu(t.x+t.width,e.x+e.width),r=Tu(t.y,e.y),o=Cu(t.y+t.height,e.y+e.height);if(i>=n&&o>=r)return{x:n,y:r,width:i-n,height:o-r}},registerShape:ku,getShapeClass:function(t){if(Du.hasOwnProperty(t))return Du[t]},Group:sr,Image:Ya,Text:es,Circle:Tl,Ellipse:Dl,Sector:Wl,Ring:Ul,Polygon:ql,Polyline:jl,Rect:Qa,Line:Ql,BezierCurve:nu,Arc:ru,IncrementalDisplayable:gu,CompoundPath:ou,LinearGradient:su,RadialGradient:lu,BoundingRect:Ce}),Lm=Object.freeze({__proto__:null,addCommas:jh,toCamelCase:function(t,e){return t=(t||"").toLowerCase().replace(/-(.)/g,(function(t,e){return e.toUpperCase()})),e&&t&&(t=t.charAt(0).toUpperCase()+t.slice(1)),t},normalizeCssArray:Kh,encodeHTML:$t,formatTpl:Jh,getTooltipMarker:function(t,e){var n=G(t)?{color:t,extraCssText:e}:t||{},i=n.color,r=n.type;e=n.extraCssText;var o=n.renderMode||"html";return i?"html"===o?"subItem"===r?'<span style="display:inline-block;vertical-align:middle;margin-right:8px;margin-left:3px;border-radius:4px;width:4px;height:4px;background-color:'+$t(i)+";"+(e||"")+'"></span>':'<span style="display:inline-block;margin-right:4px;border-radius:10px;width:10px;height:10px;background-color:'+$t(i)+";"+(e||"")+'"></span>':{renderMode:o,content:"{"+(n.markerId||"markerX")+"|}  ",style:"subItem"===r?{width:4,height:4,borderRadius:2,backgroundColor:i}:{width:10,height:10,borderRadius:5,backgroundColor:i}}:""},formatTime:function(t,e,n){"week"!==t&&"month"!==t&&"quarter"!==t&&"half-year"!==t&&"year"!==t||(t="MM-dd\nyyyy");var i=Ir(e),r=n?"getUTC":"get",o=i[r+"FullYear"](),a=i[r+"Month"]()+1,s=i[r+"Date"](),l=i[r+"Hours"](),u=i[r+"Minutes"](),h=i[r+"Seconds"](),c=i[r+"Milliseconds"]();return t=t.replace("MM",kh(a,2)).replace("M",a).replace("yyyy",o).replace("yy",kh(o%100+"",2)).replace("dd",kh(s,2)).replace("d",s).replace("hh",kh(l,2)).replace("h",l).replace("mm",kh(u,2)).replace("m",u).replace("ss",kh(h,2)).replace("s",h).replace("SSS",kh(c,3))},capitalFirst:function(t){return t?t.charAt(0).toUpperCase()+t.substr(1):t},truncateText:function(t,e,n,i,r){var o={};return Mo(o,t,e,n,i,r),o.text},getTextRect:function(t,e,n,i,r,o,a,s){return new es({style:{text:t,font:e,align:n,verticalAlign:i,padding:r,rich:o,overflow:a?"truncate":null,lineHeight:s}}).getBoundingRect()}}),Pm=Object.freeze({__proto__:null,map:N,each:R,indexOf:A,inherits:L,reduce:E,filter:z,bind:F,curry:V,isArray:H,isString:G,isObject:Y,isFunction:W,extend:D,defaults:I,clone:T,merge:C}),Om=Qr();function Rm(t,e){var n=N(e,(function(e){return t.scale.parse(e)}));return"time"===t.type&&n.length>0&&(n.sort(),n.unshift(n[0]),n.push(n[n.length-1])),n}function Nm(t){var e=t.getLabelModel().get("customValues");if(e){var n=um(t),i=t.scale.getExtent();return{labels:N(z(Rm(t,e),(function(t){return t>=i[0]&&t<=i[1]})),(function(e){var i={value:e};return{formattedLabel:n(i),rawLabel:t.scale.getLabel(i),tickValue:e}}))}}return"category"===t.type?function(t){var e=t.getLabelModel(),n=zm(t,e);return!e.get("show")||t.scale.isBlank()?{labels:[],labelCategoryInterval:n.labelCategoryInterval}:n}(t):function(t){var e=t.scale.getTicks(),n=um(t);return{labels:N(e,(function(e,i){return{level:e.level,formattedLabel:n(e,i),rawLabel:t.scale.getLabel(e),tickValue:e.value}}))}}(t)}function Em(t,e){var n=t.getTickModel().get("customValues");if(n){var i=t.scale.getExtent();return{ticks:z(Rm(t,n),(function(t){return t>=i[0]&&t<=i[1]}))}}return"category"===t.type?function(t,e){var n,i,r=Bm(t,"ticks"),o=cm(e),a=Fm(r,o);if(a)return a;e.get("show")&&!t.scale.isBlank()||(n=[]);if(W(o))n=Wm(t,o,!0);else if("auto"===o){var s=zm(t,t.getLabelModel());i=s.labelCategoryInterval,n=N(s.labels,(function(t){return t.tickValue}))}else n=Hm(t,i=o,!0);return Vm(r,o,{ticks:n,tickCategoryInterval:i})}(t,e):{ticks:N(t.scale.getTicks(),(function(t){return t.value}))}}function zm(t,e){var n,i,r=Bm(t,"labels"),o=cm(e),a=Fm(r,o);return a||(W(o)?n=Wm(t,o):(i="auto"===o?function(t){var e=Om(t).autoInterval;return null!=e?e:Om(t).autoInterval=t.calculateCategoryInterval()}(t):o,n=Hm(t,i)),Vm(r,o,{labels:n,labelCategoryInterval:i}))}function Bm(t,e){return Om(t)[e]||(Om(t)[e]=[])}function Fm(t,e){for(var n=0;n<t.length;n++)if(t[n].key===e)return t[n].value}function Vm(t,e,n){return t.push({key:e,value:n}),n}function Hm(t,e,n){var i=um(t),r=t.scale,o=r.getExtent(),a=t.getLabelModel(),s=[],l=Math.max((e||0)+1,1),u=o[0],h=r.count();0!==u&&l>1&&h/l>2&&(u=Math.round(Math.ceil(u/l)*l));var c=pm(t),p=a.get("showMinLabel")||c,f=a.get("showMaxLabel")||c;p&&u!==o[0]&&g(o[0]);for(var d=u;d<=o[1];d+=l)g(d);function g(t){var e={value:t};s.push(n?t:{formattedLabel:i(e),rawLabel:r.getLabel(e),tickValue:t})}return f&&d-l!==o[1]&&g(o[1]),s}function Wm(t,e,n){var i=t.scale,r=um(t),o=[];return R(i.getTicks(),(function(t){var a=i.getLabel(t),s=t.value;e(t.value,a)&&o.push(n?s:{formattedLabel:r(t),rawLabel:a,tickValue:s})})),o}var Gm=[0,1],Um=function(){function t(t,e,n){this.onBand=!1,this.inverse=!1,this.dim=t,this.scale=e,this._extent=n||[0,0]}return t.prototype.contain=function(t){var e=this._extent,n=Math.min(e[0],e[1]),i=Math.max(e[0],e[1]);return t>=n&&t<=i},t.prototype.containData=function(t){return this.scale.contain(t)},t.prototype.getExtent=function(){return this._extent.slice()},t.prototype.getPixelPrecision=function(t){return br(t||this.scale.getExtent(),this._extent)},t.prototype.setExtent=function(t,e){var n=this._extent;n[0]=t,n[1]=e},t.prototype.dataToCoord=function(t,e){var n=this._extent,i=this.scale;return t=i.normalize(t),this.onBand&&"ordinal"===i.type&&Xm(n=n.slice(),i.count()),vr(t,Gm,n,e)},t.prototype.coordToData=function(t,e){var n=this._extent,i=this.scale;this.onBand&&"ordinal"===i.type&&Xm(n=n.slice(),i.count());var r=vr(t,n,Gm,e);return this.scale.scale(r)},t.prototype.pointToData=function(t,e){},t.prototype.getTicksCoords=function(t){var e=(t=t||{}).tickModel||this.getTickModel(),n=N(Em(this,e).ticks,(function(t){return{coord:this.dataToCoord("ordinal"===this.scale.type?this.scale.getRawOrdinalNumber(t):t),tickValue:t}}),this);return function(t,e,n,i){var r=e.length;if(!t.onBand||n||!r)return;var o,a,s=t.getExtent();if(1===r)e[0].coord=s[0],o=e[1]={coord:s[1],tickValue:e[0].tickValue};else{var l=e[r-1].tickValue-e[0].tickValue,u=(e[r-1].coord-e[0].coord)/l;R(e,(function(t){t.coord-=u/2}));var h=t.scale.getExtent();a=1+h[1]-e[r-1].tickValue,o={coord:e[r-1].coord+u*a,tickValue:h[1]+1},e.push(o)}var c=s[0]>s[1];p(e[0].coord,s[0])&&(i?e[0].coord=s[0]:e.shift());i&&p(s[0],e[0].coord)&&e.unshift({coord:s[0]});p(s[1],o.coord)&&(i?o.coord=s[1]:e.pop());i&&p(o.coord,s[1])&&e.push({coord:s[1]});function p(t,e){return t=_r(t),e=_r(e),c?t>e:t<e}}(this,n,e.get("alignWithLabel"),t.clamp),n},t.prototype.getMinorTicksCoords=function(){if("ordinal"===this.scale.type)return[];var t=this.model.getModel("minorTick").get("splitNumber");return t>0&&t<100||(t=5),N(this.scale.getMinorTicks(t),(function(t){return N(t,(function(t){return{coord:this.dataToCoord(t),tickValue:t}}),this)}),this)},t.prototype.getViewLabels=function(){return Nm(this).labels},t.prototype.getLabelModel=function(){return this.model.getModel("axisLabel")},t.prototype.getTickModel=function(){return this.model.getModel("axisTick")},t.prototype.getBandWidth=function(){var t=this._extent,e=this.scale.getExtent(),n=e[1]-e[0]+(this.onBand?1:0);0===n&&(n=1);var i=Math.abs(t[1]-t[0]);return Math.abs(i)/n},t.prototype.calculateCategoryInterval=function(){return function(t){var e=function(t){var e=t.getLabelModel();return{axisRotate:t.getRotate?t.getRotate():t.isHorizontal&&!t.isHorizontal()?90:0,labelRotate:e.get("rotate")||0,font:e.getFont()}}(t),n=um(t),i=(e.axisRotate-e.labelRotate)/180*Math.PI,r=t.scale,o=r.getExtent(),a=r.count();if(o[1]-o[0]<1)return 0;var s=1;a>40&&(s=Math.max(1,Math.floor(a/40)));for(var l=o[0],u=t.dataToCoord(l+1)-t.dataToCoord(l),h=Math.abs(u*Math.cos(i)),c=Math.abs(u*Math.sin(i)),p=0,f=0;l<=o[1];l+=s){var d,g,y=Xi(n({value:l}),e.font,"center","top");d=1.3*y.width,g=1.3*y.height,p=Math.max(p,d,7),f=Math.max(f,g,7)}var v=p/h,m=f/c;isNaN(v)&&(v=1/0),isNaN(m)&&(m=1/0);var _=Math.max(0,Math.floor(Math.min(v,m))),x=Om(t.model),w=t.getExtent(),b=x.lastAutoInterval,S=x.lastTickCount;return null!=b&&null!=S&&Math.abs(b-_)<=1&&Math.abs(S-a)<=1&&b>_&&x.axisExtent0===w[0]&&x.axisExtent1===w[1]?_=b:(x.lastTickCount=a,x.lastAutoInterval=_,x.axisExtent0=w[0],x.axisExtent1=w[1]),_}(this)},t}();function Xm(t,e){var n=(t[1]-t[0])/e/2;t[0]+=n,t[1]-=n}function Ym(t,e,n,i,r,o,a,s){var l=r-t,u=o-e,h=n-t,c=i-e,p=Math.sqrt(h*h+c*c),f=(l*(h/=p)+u*(c/=p))/p;s&&(f=Math.min(Math.max(f,0),1)),f*=p;var d=a[0]=t+f*h,g=a[1]=e+f*c;return Math.sqrt((d-r)*(d-r)+(g-o)*(g-o))}var qm=new ve,Zm=new ve,jm=new ve,Km=new ve,$m=new ve,Qm=[],Jm=new ve;function t_(t,e){if(e<=180&&e>0){e=e/180*Math.PI,qm.fromArray(t[0]),Zm.fromArray(t[1]),jm.fromArray(t[2]),ve.sub(Km,qm,Zm),ve.sub($m,jm,Zm);var n=Km.len(),i=$m.len();if(!(n<.001||i<.001)){Km.scale(1/n),$m.scale(1/i);var r=Km.dot($m);if(Math.cos(e)<r){var o=Ym(Zm.x,Zm.y,jm.x,jm.y,qm.x,qm.y,Qm,!1);Jm.fromArray(Qm),Jm.scaleAndAdd($m,o/Math.tan(Math.PI-e));var a=jm.x!==Zm.x?(Jm.x-Zm.x)/(jm.x-Zm.x):(Jm.y-Zm.y)/(jm.y-Zm.y);if(isNaN(a))return;a<0?ve.copy(Jm,Zm):a>1&&ve.copy(Jm,jm),Jm.toArray(t[1])}}}}function e_(t,e,n){if(n<=180&&n>0){n=n/180*Math.PI,qm.fromArray(t[0]),Zm.fromArray(t[1]),jm.fromArray(t[2]),ve.sub(Km,Zm,qm),ve.sub($m,jm,Zm);var i=Km.len(),r=$m.len();if(!(i<.001||r<.001))if(Km.scale(1/i),$m.scale(1/r),Km.dot(e)<Math.cos(n)){var o=Ym(Zm.x,Zm.y,jm.x,jm.y,qm.x,qm.y,Qm,!1);Jm.fromArray(Qm);var a=Math.PI/2,s=a+Math.acos($m.dot(e))-n;if(s>=a)ve.copy(Jm,jm);else{Jm.scaleAndAdd($m,o/Math.tan(Math.PI/2-s));var l=jm.x!==Zm.x?(Jm.x-Zm.x)/(jm.x-Zm.x):(Jm.y-Zm.y)/(jm.y-Zm.y);if(isNaN(l))return;l<0?ve.copy(Jm,Zm):l>1&&ve.copy(Jm,jm)}Jm.toArray(t[1])}}}function n_(t,e,n,i){var r="normal"===n,o=r?t:t.ensureState(n);o.ignore=e;var a=i.get("smooth");a&&!0===a&&(a=.3),o.shape=o.shape||{},a>0&&(o.shape.smooth=a);var s=i.getModel("lineStyle").getLineStyle();r?t.useStyle(s):o.style=s}function i_(t,e){var n=e.smooth,i=e.points;if(i)if(t.moveTo(i[0][0],i[0][1]),n>0&&i.length>=3){var r=Pt(i[0],i[1]),o=Pt(i[1],i[2]);if(!r||!o)return t.lineTo(i[1][0],i[1][1]),void t.lineTo(i[2][0],i[2][1]);var a=Math.min(r,o)*n,s=Nt([],i[1],i[0],a/r),l=Nt([],i[1],i[2],a/o),u=Nt([],s,l,.5);t.bezierCurveTo(s[0],s[1],s[0],s[1],u[0],u[1]),t.bezierCurveTo(l[0],l[1],l[0],l[1],i[2][0],i[2][1])}else for(var h=1;h<i.length;h++)t.lineTo(i[h][0],i[h][1])}function r_(t,e,n,i){return function(t,e,n,i,r,o){var a=t.length;if(!(a<2)){t.sort((function(t,n){return t.rect[e]-n.rect[e]}));for(var s,l=0,u=!1,h=0,c=0;c<a;c++){var p=t[c],f=p.rect;(s=f[e]-l)<0&&(f[e]-=s,p.label[e]-=s,u=!0),h+=Math.max(-s,0),l=f[e]+f[n]}h>0&&o&&x(-h/a,0,a);var d,g,y=t[0],v=t[a-1];return m(),d<0&&w(-d,.8),g<0&&w(g,.8),m(),_(d,g,1),_(g,d,-1),m(),d<0&&b(-d),g<0&&b(g),u}function m(){d=y.rect[e]-i,g=r-v.rect[e]-v.rect[n]}function _(t,e,n){if(t<0){var i=Math.min(e,-t);if(i>0){x(i*n,0,a);var r=i+t;r<0&&w(-r*n,1)}else w(-t*n,1)}}function x(n,i,r){0!==n&&(u=!0);for(var o=i;o<r;o++){var a=t[o];a.rect[e]+=n,a.label[e]+=n}}function w(i,r){for(var o=[],s=0,l=1;l<a;l++){var u=t[l-1].rect,h=Math.max(t[l].rect[e]-u[e]-u[n],0);o.push(h),s+=h}if(s){var c=Math.min(Math.abs(i)/s,r);if(i>0)for(l=0;l<a-1;l++)x(o[l]*c,0,l+1);else for(l=a-1;l>0;l--)x(-o[l-1]*c,l,a)}}function b(t){var e=t<0?-1:1;t=Math.abs(t);for(var n=Math.ceil(t/(a-1)),i=0;i<a-1;i++)if(e>0?x(n,0,i+1):x(-n,a-i-1,a),(t-=n)<=0)return}}(t,"y","height",e,n,i)}function o_(t,e,n){var i=u.createCanvas(),r=e.getWidth(),o=e.getHeight(),a=i.style;return a&&(a.position="absolute",a.left="0",a.top="0",a.width=r+"px",a.height=o+"px",i.setAttribute("data-zr-dom-id",t)),i.width=r*n,i.height=o*n,i}var a_=function(t){function e(e,n,i){var r,o=t.call(this)||this;o.motionBlur=!1,o.lastFrameAlpha=.7,o.dpr=1,o.virtual=!1,o.config={},o.incremental=!1,o.zlevel=0,o.maxRepaintRectCount=5,o.__dirty=!0,o.__firstTimePaint=!0,o.__used=!1,o.__drawIndex=0,o.__startIndex=0,o.__endIndex=0,o.__prevStartIndex=null,o.__prevEndIndex=null,i=i||Ai,"string"==typeof e?r=o_(e,n,i):Y(e)&&(e=(r=e).id),o.id=e,o.dom=r;var a=r.style;return a&&(yt(r),r.onselectstart=function(){return!1},a.padding="0",a.margin="0",a.borderWidth="0"),o.painter=n,o.dpr=i,o}return n(e,t),e.prototype.getElementCount=function(){return this.__endIndex-this.__startIndex},e.prototype.afterBrush=function(){this.__prevStartIndex=this.__startIndex,this.__prevEndIndex=this.__endIndex},e.prototype.initContext=function(){this.ctx=this.dom.getContext("2d"),this.ctx.dpr=this.dpr},e.prototype.setUnpainted=function(){this.__firstTimePaint=!0},e.prototype.createBackBuffer=function(){var t=this.dpr;this.domBack=o_("back-"+this.id,this.painter,t),this.ctxBack=this.domBack.getContext("2d"),1!==t&&this.ctxBack.scale(t,t)},e.prototype.createRepaintRects=function(t,e,n,i){if(this.__firstTimePaint)return this.__firstTimePaint=!1,null;var r,o=[],a=this.maxRepaintRectCount,s=!1,l=new Ce(0,0,0,0);function u(t){if(t.isFinite()&&!t.isZero())if(0===o.length){(e=new Ce(0,0,0,0)).copy(t),o.push(e)}else{for(var e,n=!1,i=1/0,r=0,u=0;u<o.length;++u){var h=o[u];if(h.intersect(t)){var c=new Ce(0,0,0,0);c.copy(h),c.union(t),o[u]=c,n=!0;break}if(s){l.copy(t),l.union(h);var p=t.width*t.height,f=h.width*h.height,d=l.width*l.height-p-f;d<i&&(i=d,r=u)}}if(s&&(o[r].union(t),n=!0),!n)(e=new Ce(0,0,0,0)).copy(t),o.push(e);s||(s=o.length>=a)}}for(var h=this.__startIndex;h<this.__endIndex;++h){if(f=t[h]){var c=f.shouldBePainted(n,i,!0,!0);(d=f.__isRendered&&(1&f.__dirty||!c)?f.getPrevPaintRect():null)&&u(d);var p=c&&(1&f.__dirty||!f.__isRendered)?f.getPaintRect():null;p&&u(p)}}for(h=this.__prevStartIndex;h<this.__prevEndIndex;++h){var f,d;c=(f=e[h])&&f.shouldBePainted(n,i,!0,!0);if(f&&(!c||!f.__zr)&&f.__isRendered)(d=f.getPrevPaintRect())&&u(d)}do{r=!1;for(h=0;h<o.length;)if(o[h].isZero())o.splice(h,1);else{for(var g=h+1;g<o.length;)o[h].intersect(o[g])?(r=!0,o[h].union(o[g]),o.splice(g,1)):g++;h++}}while(r);return this._paintRects=o,o},e.prototype.debugGetPaintRects=function(){return(this._paintRects||[]).slice()},e.prototype.resize=function(t,e){var n=this.dpr,i=this.dom,r=i.style,o=this.domBack;r&&(r.width=t+"px",r.height=e+"px"),i.width=t*n,i.height=e*n,o&&(o.width=t*n,o.height=e*n,1!==n&&this.ctxBack.scale(n,n))},e.prototype.clear=function(t,e,n){var i=this.dom,r=this.ctx,o=i.width,a=i.height;e=e||this.clearColor;var s=this.motionBlur&&!t,l=this.lastFrameAlpha,u=this.dpr,h=this;s&&(this.domBack||this.createBackBuffer(),this.ctxBack.globalCompositeOperation="copy",this.ctxBack.drawImage(i,0,0,o/u,a/u));var c=this.domBack;function p(t,n,i,o){if(r.clearRect(t,n,i,o),e&&"transparent"!==e){var a=void 0;if(K(e))a=(e.global||e.__width===i&&e.__height===o)&&e.__canvasGradient||Wd(r,e,{x:0,y:0,width:i,height:o}),e.__canvasGradient=a,e.__width=i,e.__height=o;else $(e)&&(e.scaleX=e.scaleX||u,e.scaleY=e.scaleY||u,a=Qd(r,e,{dirty:function(){h.setUnpainted(),h.painter.refresh()}}));r.save(),r.fillStyle=a||e,r.fillRect(t,n,i,o),r.restore()}s&&(r.save(),r.globalAlpha=l,r.drawImage(c,t,n,i,o),r.restore())}!n||s?p(0,0,o,a):n.length&&R(n,(function(t){p(t.x*u,t.y*u,t.width*u,t.height*u)}))},e}(Wt),s_=1e5,l_=314159,u_=.01;var h_=function(){function t(t,e,n,i){this.type="canvas",this._zlevelList=[],this._prevDisplayList=[],this._layers={},this._layerConfig={},this._needsManuallyCompositing=!1,this.type="canvas";var r=!t.nodeName||"CANVAS"===t.nodeName.toUpperCase();this._opts=n=D({},n||{}),this.dpr=n.devicePixelRatio||Ai,this._singleCanvas=r,this.root=t,t.style&&(yt(t),t.innerHTML=""),this.storage=e;var o=this._zlevelList;this._prevDisplayList=[];var a=this._layers;if(r){var s=t,l=s.width,u=s.height;null!=n.width&&(l=n.width),null!=n.height&&(u=n.height),this.dpr=n.devicePixelRatio||1,s.width=l*this.dpr,s.height=u*this.dpr,this._width=l,this._height=u;var h=new a_(s,this,this.dpr);h.__builtin__=!0,h.initContext(),a[314159]=h,h.zlevel=l_,o.push(l_),this._domRoot=t}else{this._width=Ud(t,0,n),this._height=Ud(t,1,n);var c=this._domRoot=function(t,e){var n=document.createElement("div");return n.style.cssText=["position:relative","width:"+t+"px","height:"+e+"px","padding:0","margin:0","border-width:0"].join(";")+";",n}(this._width,this._height);t.appendChild(c)}}return t.prototype.getType=function(){return"canvas"},t.prototype.isSingleCanvas=function(){return this._singleCanvas},t.prototype.getViewportRoot=function(){return this._domRoot},t.prototype.getViewportRootOffset=function(){var t=this.getViewportRoot();if(t)return{offsetLeft:t.offsetLeft||0,offsetTop:t.offsetTop||0}},t.prototype.refresh=function(t){var e=this.storage.getDisplayList(!0),n=this._prevDisplayList,i=this._zlevelList;this._redrawId=Math.random(),this._paintList(e,n,t,this._redrawId);for(var r=0;r<i.length;r++){var o=i[r],a=this._layers[o];if(!a.__builtin__&&a.refresh){var s=0===r?this._backgroundColor:null;a.refresh(s)}}return this._opts.useDirtyRect&&(this._prevDisplayList=e.slice()),this},t.prototype.refreshHover=function(){this._paintHoverList(this.storage.getDisplayList(!1))},t.prototype._paintHoverList=function(t){var e=t.length,n=this._hoverlayer;if(n&&n.clear(),e){for(var i,r={inHover:!0,viewWidth:this._width,viewHeight:this._height},o=0;o<e;o++){var a=t[o];a.__inHover&&(n||(n=this._hoverlayer=this.getLayer(s_)),i||(i=n.ctx).save(),sg(i,a,r,o===e-1))}i&&i.restore()}},t.prototype.getHoverLayer=function(){return this.getLayer(s_)},t.prototype.paintOne=function(t,e){ag(t,e)},t.prototype._paintList=function(t,e,n,i){if(this._redrawId===i){n=n||!1,this._updateLayerStatus(t);var r=this._doPaintList(t,e,n),o=r.finished,a=r.needsRefreshHover;if(this._needsManuallyCompositing&&this._compositeManually(),a&&this._paintHoverList(t),o)this.eachLayer((function(t){t.afterBrush&&t.afterBrush()}));else{var s=this;qe((function(){s._paintList(t,e,n,i)}))}}},t.prototype._compositeManually=function(){var t=this.getLayer(l_).ctx,e=this._domRoot.width,n=this._domRoot.height;t.clearRect(0,0,e,n),this.eachBuiltinLayer((function(i){i.virtual&&t.drawImage(i.dom,0,0,e,n)}))},t.prototype._doPaintList=function(t,e,n){for(var i=this,o=[],a=this._opts.useDirtyRect,s=0;s<this._zlevelList.length;s++){var l=this._zlevelList[s],u=this._layers[l];u.__builtin__&&u!==this._hoverlayer&&(u.__dirty||n)&&o.push(u)}for(var h=!0,c=!1,p=function(r){var s,l=o[r],u=l.ctx,p=a&&l.createRepaintRects(t,e,f._width,f._height),d=n?l.__startIndex:l.__drawIndex,g=!n&&l.incremental&&Date.now,y=g&&Date.now(),v=l.zlevel===f._zlevelList[0]?f._backgroundColor:null;if(l.__startIndex===l.__endIndex)l.clear(!1,v,p);else if(d===l.__startIndex){var m=t[d];m.incremental&&m.notClear&&!n||l.clear(!1,v,p)}-1===d&&(console.error("For some unknown reason. drawIndex is -1"),d=l.__startIndex);var _=function(e){var n={inHover:!1,allClipped:!1,prevEl:null,viewWidth:i._width,viewHeight:i._height};for(s=d;s<l.__endIndex;s++){var r=t[s];if(r.__inHover&&(c=!0),i._doPaintEl(r,l,a,e,n,s===l.__endIndex-1),g)if(Date.now()-y>15)break}n.prevElClipPaths&&u.restore()};if(p)if(0===p.length)s=l.__endIndex;else for(var x=f.dpr,w=0;w<p.length;++w){var b=p[w];u.save(),u.beginPath(),u.rect(b.x*x,b.y*x,b.width*x,b.height*x),u.clip(),_(b),u.restore()}else u.save(),_(),u.restore();l.__drawIndex=s,l.__drawIndex<l.__endIndex&&(h=!1)},f=this,d=0;d<o.length;d++)p(d);return r.wxa&&R(this._layers,(function(t){t&&t.ctx&&t.ctx.draw&&t.ctx.draw()})),{finished:h,needsRefreshHover:c}},t.prototype._doPaintEl=function(t,e,n,i,r,o){var a=e.ctx;if(n){var s=t.getPaintRect();(!i||s&&s.intersect(i))&&(sg(a,t,r,o),t.setPrevPaintRect(s))}else sg(a,t,r,o)},t.prototype.getLayer=function(t,e){this._singleCanvas&&!this._needsManuallyCompositing&&(t=l_);var n=this._layers[t];return n||((n=new a_("zr_"+t,this,this.dpr)).zlevel=t,n.__builtin__=!0,this._layerConfig[t]?C(n,this._layerConfig[t],!0):this._layerConfig[t-u_]&&C(n,this._layerConfig[t-u_],!0),e&&(n.virtual=e),this.insertLayer(t,n),n.initContext()),n},t.prototype.insertLayer=function(t,e){var n=this._layers,i=this._zlevelList,r=i.length,o=this._domRoot,a=null,s=-1;if(!n[t]&&function(t){return!!t&&(!!t.__builtin__||"function"==typeof t.resize&&"function"==typeof t.refresh)}(e)){if(r>0&&t>i[0]){for(s=0;s<r-1&&!(i[s]<t&&i[s+1]>t);s++);a=n[i[s]]}if(i.splice(s+1,0,t),n[t]=e,!e.virtual)if(a){var l=a.dom;l.nextSibling?o.insertBefore(e.dom,l.nextSibling):o.appendChild(e.dom)}else o.firstChild?o.insertBefore(e.dom,o.firstChild):o.appendChild(e.dom);e.painter||(e.painter=this)}},t.prototype.eachLayer=function(t,e){for(var n=this._zlevelList,i=0;i<n.length;i++){var r=n[i];t.call(e,this._layers[r],r)}},t.prototype.eachBuiltinLayer=function(t,e){for(var n=this._zlevelList,i=0;i<n.length;i++){var r=n[i],o=this._layers[r];o.__builtin__&&t.call(e,o,r)}},t.prototype.eachOtherLayer=function(t,e){for(var n=this._zlevelList,i=0;i<n.length;i++){var r=n[i],o=this._layers[r];o.__builtin__||t.call(e,o,r)}},t.prototype.getLayers=function(){return this._layers},t.prototype._updateLayerStatus=function(t){function e(t){o&&(o.__endIndex!==t&&(o.__dirty=!0),o.__endIndex=t)}if(this.eachBuiltinLayer((function(t,e){t.__dirty=t.__used=!1})),this._singleCanvas)for(var n=1;n<t.length;n++){if((s=t[n]).zlevel!==t[n-1].zlevel||s.incremental){this._needsManuallyCompositing=!0;break}}var i,r,o=null,a=0;for(r=0;r<t.length;r++){var s,l=(s=t[r]).zlevel,u=void 0;i!==l&&(i=l,a=0),s.incremental?((u=this.getLayer(l+.001,this._needsManuallyCompositing)).incremental=!0,a=1):u=this.getLayer(l+(a>0?u_:0),this._needsManuallyCompositing),u.__builtin__||M("ZLevel "+l+" has been used by unkown layer "+u.id),u!==o&&(u.__used=!0,u.__startIndex!==r&&(u.__dirty=!0),u.__startIndex=r,u.incremental?u.__drawIndex=-1:u.__drawIndex=r,e(r),o=u),1&s.__dirty&&!s.__inHover&&(u.__dirty=!0,u.incremental&&u.__drawIndex<0&&(u.__drawIndex=r))}e(r),this.eachBuiltinLayer((function(t,e){!t.__used&&t.getElementCount()>0&&(t.__dirty=!0,t.__startIndex=t.__endIndex=t.__drawIndex=0),t.__dirty&&t.__drawIndex<0&&(t.__drawIndex=t.__startIndex)}))},t.prototype.clear=function(){return this.eachBuiltinLayer(this._clearLayer),this},t.prototype._clearLayer=function(t){t.clear()},t.prototype.setBackgroundColor=function(t){this._backgroundColor=t,R(this._layers,(function(t){t.setUnpainted()}))},t.prototype.configLayer=function(t,e){if(e){var n=this._layerConfig;n[t]?C(n[t],e,!0):n[t]=e;for(var i=0;i<this._zlevelList.length;i++){var r=this._zlevelList[i];if(r===t||r===t+u_)C(this._layers[r],n[t],!0)}}},t.prototype.delLayer=function(t){var e=this._layers,n=this._zlevelList,i=e[t];i&&(i.dom.parentNode.removeChild(i.dom),delete e[t],n.splice(A(n,t),1))},t.prototype.resize=function(t,e){if(this._domRoot.style){var n=this._domRoot;n.style.display="none";var i=this._opts,r=this.root;if(null!=t&&(i.width=t),null!=e&&(i.height=e),t=Ud(r,0,i),e=Ud(r,1,i),n.style.display="",this._width!==t||e!==this._height){for(var o in n.style.width=t+"px",n.style.height=e+"px",this._layers)this._layers.hasOwnProperty(o)&&this._layers[o].resize(t,e);this.refresh(!0)}this._width=t,this._height=e}else{if(null==t||null==e)return;this._width=t,this._height=e,this.getLayer(l_).resize(t,e)}return this},t.prototype.clearLayer=function(t){var e=this._layers[t];e&&e.clear()},t.prototype.dispose=function(){this.root.innerHTML="",this.root=this.storage=this._domRoot=this._layers=null},t.prototype.getRenderedCanvas=function(t){if(t=t||{},this._singleCanvas&&!this._compositeManually)return this._layers[314159].dom;var e=new a_("image",this,t.pixelRatio||this.dpr);e.initContext(),e.clear(!1,t.backgroundColor||this._backgroundColor);var n=e.ctx;if(t.pixelRatio<=this.dpr){this.refresh();var i=e.dom.width,r=e.dom.height;this.eachLayer((function(t){t.__builtin__?n.drawImage(t.dom,0,0,i,r):t.renderToCanvas&&(n.save(),t.renderToCanvas(n),n.restore())}))}else for(var o={inHover:!1,viewWidth:this._width,viewHeight:this._height},a=this.storage.getDisplayList(!0),s=0,l=a.length;s<l;s++){var u=a[s];sg(n,u,o,s===l-1)}return e.dom},t.prototype.getWidth=function(){return this._width},t.prototype.getHeight=function(){return this._height},t}();var c_=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n.hasSymbolVisual=!0,n}return n(e,t),e.prototype.getInitialData=function(t){return pv(null,this,{useEncodeDefaulter:!0})},e.prototype.getLegendIcon=function(t){var e=new sr,n=Fd("line",0,t.itemHeight/2,t.itemWidth,0,t.lineStyle.stroke,!1);e.add(n),n.setStyle(t.lineStyle);var i=this.getData().getVisual("symbol"),r=this.getData().getVisual("symbolRotate"),o="none"===i?"circle":i,a=.8*t.itemHeight,s=Fd(o,(t.itemWidth-a)/2,(t.itemHeight-a)/2,a,a,t.itemStyle.fill);e.add(s),s.setStyle(t.itemStyle);var l="inherit"===t.iconRotate?r:t.iconRotate||0;return s.rotation=l*Math.PI/180,s.setOrigin([t.itemWidth/2,t.itemHeight/2]),o.indexOf("empty")>-1&&(s.style.stroke=s.style.fill,s.style.fill="#fff",s.style.lineWidth=2),e},e.type="series.line",e.dependencies=["grid","polar"],e.defaultOption={z:3,coordinateSystem:"cartesian2d",legendHoverLink:!0,clip:!0,label:{position:"top"},endLabel:{show:!1,valueAnimation:!0,distance:8},lineStyle:{width:2,type:"solid"},emphasis:{scale:!0},step:!1,smooth:!1,smoothMonotone:null,symbol:"emptyCircle",symbolSize:4,symbolRotate:null,showSymbol:!0,showAllSymbol:"auto",connectNulls:!1,sampling:"none",animationEasing:"linear",progressive:0,hoverLayerThreshold:1/0,universalTransition:{divideShape:"clone"},triggerLineEvent:!1},e}(Mf);function p_(t,e){var n=t.mapDimensionsAll("defaultedLabel"),i=n.length;if(1===i){var r=Up(t,e,n[0]);return null!=r?r+"":null}if(i){for(var o=[],a=0;a<n.length;a++)o.push(Up(t,e,n[a]));return o.join(" ")}}function f_(t,e){var n=t.mapDimensionsAll("defaultedLabel");if(!H(e))return e+"";for(var i=[],r=0;r<n.length;r++){var o=t.getDimensionIndex(n[r]);o>=0&&i.push(e[o])}return i.join(" ")}var d_=function(t){function e(e,n,i,r){var o=t.call(this)||this;return o.updateData(e,n,i,r),o}return n(e,t),e.prototype._createSymbol=function(t,e,n,i,r){this.removeAll();var o=Fd(t,-1,-1,2,2,null,r);o.attr({z2:100,culling:!0,scaleX:i[0]/2,scaleY:i[1]/2}),o.drift=g_,this._symbolType=t,this.add(o)},e.prototype.stopSymbolAnimation=function(t){this.childAt(0).stopAnimation(null,t)},e.prototype.getSymbolType=function(){return this._symbolType},e.prototype.getSymbolPath=function(){return this.childAt(0)},e.prototype.highlight=function(){Vs(this.childAt(0))},e.prototype.downplay=function(){Hs(this.childAt(0))},e.prototype.setZ=function(t,e){var n=this.childAt(0);n.zlevel=t,n.z=e},e.prototype.setDraggable=function(t,e){var n=this.childAt(0);n.draggable=t,n.cursor=!e&&t?"move":n.cursor},e.prototype.updateData=function(t,n,i,r){this.silent=!1;var o=t.getItemVisual(n,"symbol")||"circle",a=t.hostModel,s=e.getSymbolSize(t,n),l=o!==this._symbolType,u=r&&r.disableAnimation;if(l){var h=t.getItemVisual(n,"symbolKeepAspect");this._createSymbol(o,t,n,s,h)}else{(p=this.childAt(0)).silent=!1;var c={scaleX:s[0]/2,scaleY:s[1]/2};u?p.attr(c):mu(p,c,a,n),Mu(p)}if(this._updateCommon(t,n,s,i,r),l){var p=this.childAt(0);if(!u){c={scaleX:this._sizeX,scaleY:this._sizeY,style:{opacity:p.style.opacity}};p.scaleX=p.scaleY=0,p.style.opacity=0,_u(p,c,a,n)}}u&&this.childAt(0).stopAnimation("leave")},e.prototype._updateCommon=function(t,e,n,i,r){var o,a,s,l,u,h,c,p,f,d=this.childAt(0),g=t.hostModel;if(i&&(o=i.emphasisItemStyle,a=i.blurItemStyle,s=i.selectItemStyle,l=i.focus,u=i.blurScope,c=i.labelStatesModels,p=i.hoverScale,f=i.cursorStyle,h=i.emphasisDisabled),!i||t.hasItemOption){var y=i&&i.itemModel?i.itemModel:t.getItemModel(e),v=y.getModel("emphasis");o=v.getModel("itemStyle").getItemStyle(),s=y.getModel(["select","itemStyle"]).getItemStyle(),a=y.getModel(["blur","itemStyle"]).getItemStyle(),l=v.get("focus"),u=v.get("blurScope"),h=v.get("disabled"),c=Gu(y),p=v.getShallow("scale"),f=y.getShallow("cursor")}var m=t.getItemVisual(e,"symbolRotate");d.attr("rotation",(m||0)*Math.PI/180||0);var _=Vd(t.getItemVisual(e,"symbolOffset"),n);_&&(d.x=_[0],d.y=_[1]),f&&d.attr("cursor",f);var x=t.getItemVisual(e,"style"),w=x.fill;if(d instanceof Ya){var b=d.style;d.useStyle(D({image:b.image,x:b.x,y:b.y,width:b.width,height:b.height},x))}else d.__isEmptyBrush?d.useStyle(D({},x)):d.useStyle(x),d.style.decal=null,d.setColor(w,r&&r.symbolInnerColor),d.style.strokeNoScale=!0;var S=t.getItemVisual(e,"liftZ"),M=this._z2;null!=S?null==M&&(this._z2=d.z2,d.z2+=S):null!=M&&(d.z2=M,this._z2=null);var T=r&&r.useNameLabel;Wu(d,c,{labelFetcher:g,labelDataIndex:e,defaultText:function(e){return T?t.getName(e):p_(t,e)},inheritColor:w,defaultOpacity:x.opacity}),this._sizeX=n[0]/2,this._sizeY=n[1]/2;var C=d.ensureState("emphasis");C.style=o,d.ensureState("select").style=s,d.ensureState("blur").style=a;var I=null==p||!0===p?Math.max(1.1,3/this._sizeY):isFinite(p)&&p>0?+p:1;C.scaleX=this._sizeX*I,C.scaleY=this._sizeY*I,this.setSymbolScale(1),Js(this,l,u,h)},e.prototype.setSymbolScale=function(t){this.scaleX=this.scaleY=t},e.prototype.fadeOut=function(t,e,n){var i=this.childAt(0),r=fs(this).dataIndex,o=n&&n.animation;if(this.silent=i.silent=!0,n&&n.fadeLabel){var a=i.getTextContent();a&&wu(a,{style:{opacity:0}},e,{dataIndex:r,removeOpt:o,cb:function(){i.removeTextContent()}})}else i.removeTextContent();wu(i,{style:{opacity:0},scaleX:0,scaleY:0},e,{dataIndex:r,cb:t,removeOpt:o})},e.getSymbolSize=function(t,e){return H(n=t.getItemVisual(e,"symbolSize"))||(n=[+n,+n]),[n[0]||0,n[1]||0];var n},e}(sr);function g_(t,e){this.parent.drift(t,e)}function y_(t,e,n,i){return e&&!isNaN(e[0])&&!isNaN(e[1])&&!(i.isIgnore&&i.isIgnore(n))&&!(i.clipShape&&!i.clipShape.contain(e[0],e[1]))&&"none"!==t.getItemVisual(n,"symbol")}function v_(t){return null==t||Y(t)||(t={isIgnore:t}),t||{}}function m_(t){var e=t.hostModel,n=e.getModel("emphasis");return{emphasisItemStyle:n.getModel("itemStyle").getItemStyle(),blurItemStyle:e.getModel(["blur","itemStyle"]).getItemStyle(),selectItemStyle:e.getModel(["select","itemStyle"]).getItemStyle(),focus:n.get("focus"),blurScope:n.get("blurScope"),emphasisDisabled:n.get("disabled"),hoverScale:n.get("scale"),labelStatesModels:Gu(e),cursorStyle:e.get("cursor")}}var __=function(){function t(t){this.group=new sr,this._SymbolCtor=t||d_}return t.prototype.updateData=function(t,e){this._progressiveEls=null,e=v_(e);var n=this.group,i=t.hostModel,r=this._data,o=this._SymbolCtor,a=e.disableAnimation,s=m_(t),l={disableAnimation:a},u=e.getSymbolPoint||function(e){return t.getItemLayout(e)};r||n.removeAll(),t.diff(r).add((function(i){var r=u(i);if(y_(t,r,i,e)){var a=new o(t,i,s,l);a.setPosition(r),t.setItemGraphicEl(i,a),n.add(a)}})).update((function(h,c){var p=r.getItemGraphicEl(c),f=u(h);if(y_(t,f,h,e)){var d=t.getItemVisual(h,"symbol")||"circle",g=p&&p.getSymbolType&&p.getSymbolType();if(!p||g&&g!==d)n.remove(p),(p=new o(t,h,s,l)).setPosition(f);else{p.updateData(t,h,s,l);var y={x:f[0],y:f[1]};a?p.attr(y):mu(p,y,i)}n.add(p),t.setItemGraphicEl(h,p)}else n.remove(p)})).remove((function(t){var e=r.getItemGraphicEl(t);e&&e.fadeOut((function(){n.remove(e)}),i)})).execute(),this._getSymbolPoint=u,this._data=t},t.prototype.updateLayout=function(){var t=this,e=this._data;e&&e.eachItemGraphicEl((function(e,n){var i=t._getSymbolPoint(n);e.setPosition(i),e.markRedraw()}))},t.prototype.incrementalPrepareUpdate=function(t){this._seriesScope=m_(t),this._data=null,this.group.removeAll()},t.prototype.incrementalUpdate=function(t,e,n){function i(t){t.isGroup||(t.incremental=!0,t.ensureState("emphasis").hoverLayer=!0)}this._progressiveEls=[],n=v_(n);for(var r=t.start;r<t.end;r++){var o=e.getItemLayout(r);if(y_(e,o,r,n)){var a=new this._SymbolCtor(e,r,this._seriesScope);a.traverse(i),a.setPosition(o),this.group.add(a),e.setItemGraphicEl(r,a),this._progressiveEls.push(a)}}},t.prototype.eachRendered=function(t){Fu(this._progressiveEls||this.group,t)},t.prototype.remove=function(t){var e=this.group,n=this._data;n&&t?n.eachItemGraphicEl((function(t){t.fadeOut((function(){e.remove(t)}),n.hostModel)})):e.removeAll()},t}();function x_(t,e,n){var i=t.getBaseAxis(),r=t.getOtherAxis(i),o=function(t,e){var n=0,i=t.scale.getExtent();"start"===e?n=i[0]:"end"===e?n=i[1]:X(e)&&!isNaN(e)?n=e:i[0]>0?n=i[0]:i[1]<0&&(n=i[1]);return n}(r,n),a=i.dim,s=r.dim,l=e.mapDimension(s),u=e.mapDimension(a),h="x"===s||"radius"===s?1:0,c=N(t.dimensions,(function(t){return e.mapDimension(t)})),p=!1,f=e.getCalculationInfo("stackResultDimension");return hv(e,c[0])&&(p=!0,c[0]=f),hv(e,c[1])&&(p=!0,c[1]=f),{dataDimsForPoint:c,valueStart:o,valueAxisDim:s,baseAxisDim:a,stacked:!!p,valueDim:l,baseDim:u,baseDataOffset:h,stackedOverDimension:e.getCalculationInfo("stackedOverDimension")}}function w_(t,e,n,i){var r=NaN;t.stacked&&(r=n.get(n.getCalculationInfo("stackedOverDimension"),i)),isNaN(r)&&(r=t.valueStart);var o=t.baseDataOffset,a=[];return a[o]=n.get(t.baseDim,i),a[1-o]=r,e.dataToPoint(a)}var b_=Math.min,S_=Math.max;function M_(t,e){return isNaN(t)||isNaN(e)}function T_(t,e,n,i,r,o,a,s,l){for(var u,h,c,p,f,d,g=n,y=0;y<i;y++){var v=e[2*g],m=e[2*g+1];if(g>=r||g<0)break;if(M_(v,m)){if(l){g+=o;continue}break}if(g===n)t[o>0?"moveTo":"lineTo"](v,m),c=v,p=m;else{var _=v-u,x=m-h;if(_*_+x*x<.5){g+=o;continue}if(a>0){for(var w=g+o,b=e[2*w],S=e[2*w+1];b===v&&S===m&&y<i;)y++,g+=o,b=e[2*(w+=o)],S=e[2*w+1],_=(v=e[2*g])-u,x=(m=e[2*g+1])-h;var M=y+1;if(l)for(;M_(b,S)&&M<i;)M++,b=e[2*(w+=o)],S=e[2*w+1];var T=.5,C=0,D=0,I=void 0,k=void 0;if(M>=i||M_(b,S))f=v,d=m;else{C=b-u,D=S-h;var A=v-u,L=b-v,P=m-h,O=S-m,R=void 0,N=void 0;if("x"===s){var E=C>0?1:-1;f=v-E*(R=Math.abs(A))*a,d=m,I=v+E*(N=Math.abs(L))*a,k=m}else if("y"===s){var z=D>0?1:-1;f=v,d=m-z*(R=Math.abs(P))*a,I=v,k=m+z*(N=Math.abs(O))*a}else R=Math.sqrt(A*A+P*P),f=v-C*a*(1-(T=(N=Math.sqrt(L*L+O*O))/(N+R))),d=m-D*a*(1-T),k=m+D*a*T,I=b_(I=v+C*a*T,S_(b,v)),k=b_(k,S_(S,m)),I=S_(I,b_(b,v)),d=m-(D=(k=S_(k,b_(S,m)))-m)*R/N,f=b_(f=v-(C=I-v)*R/N,S_(u,v)),d=b_(d,S_(h,m)),I=v+(C=v-(f=S_(f,b_(u,v))))*N/R,k=m+(D=m-(d=S_(d,b_(h,m))))*N/R}t.bezierCurveTo(c,p,f,d,v,m),c=I,p=k}else t.lineTo(v,m)}u=v,h=m,g+=o}return y}var C_=function(){this.smooth=0,this.smoothConstraint=!0},D_=function(t){function e(e){var n=t.call(this,e)||this;return n.type="ec-polyline",n}return n(e,t),e.prototype.getDefaultStyle=function(){return{stroke:"#000",fill:null}},e.prototype.getDefaultShape=function(){return new C_},e.prototype.buildPath=function(t,e){var n=e.points,i=0,r=n.length/2;if(e.connectNulls){for(;r>0&&M_(n[2*r-2],n[2*r-1]);r--);for(;i<r&&M_(n[2*i],n[2*i+1]);i++);}for(;i<r;)i+=T_(t,n,i,r,r,1,e.smooth,e.smoothMonotone,e.connectNulls)+1},e.prototype.getPointOn=function(t,e){this.path||(this.createPathProxy(),this.buildPath(this.path,this.shape));for(var n,i,r=this.path.data,o=wa.CMD,a="x"===e,s=[],l=0;l<r.length;){var u=void 0,h=void 0,c=void 0,p=void 0,f=void 0,d=void 0,g=void 0;switch(r[l++]){case o.M:n=r[l++],i=r[l++];break;case o.L:if(u=r[l++],h=r[l++],(g=a?(t-n)/(u-n):(t-i)/(h-i))<=1&&g>=0){var y=a?(h-i)*g+i:(u-n)*g+n;return a?[t,y]:[y,t]}n=u,i=h;break;case o.C:u=r[l++],h=r[l++],c=r[l++],p=r[l++],f=r[l++],d=r[l++];var v=a?un(n,u,c,f,t,s):un(i,h,p,d,t,s);if(v>0)for(var m=0;m<v;m++){var _=s[m];if(_<=1&&_>=0){y=a?sn(i,h,p,d,_):sn(n,u,c,f,_);return a?[t,y]:[y,t]}}n=f,i=d}}},e}(Ha),I_=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e}(C_),k_=function(t){function e(e){var n=t.call(this,e)||this;return n.type="ec-polygon",n}return n(e,t),e.prototype.getDefaultShape=function(){return new I_},e.prototype.buildPath=function(t,e){var n=e.points,i=e.stackedOnPoints,r=0,o=n.length/2,a=e.smoothMonotone;if(e.connectNulls){for(;o>0&&M_(n[2*o-2],n[2*o-1]);o--);for(;r<o&&M_(n[2*r],n[2*r+1]);r++);}for(;r<o;){var s=T_(t,n,r,o,o,1,e.smooth,a,e.connectNulls);T_(t,i,r+s-1,s,o,-1,e.stackedOnSmooth,a,e.connectNulls),r+=s+1,t.closePath()}},e}(Ha);function A_(t,e,n,i,r){var o=t.getArea(),a=o.x,s=o.y,l=o.width,u=o.height,h=n.get(["lineStyle","width"])||0;a-=h/2,s-=h/2,l+=h,u+=h,l=Math.ceil(l),a!==Math.floor(a)&&(a=Math.floor(a),l++);var c=new Qa({shape:{x:a,y:s,width:l,height:u}});if(e){var p=t.getBaseAxis(),f=p.isHorizontal(),d=p.inverse;f?(d&&(c.shape.x+=l),c.shape.width=0):(d||(c.shape.y+=u),c.shape.height=0);var g=W(r)?function(t){r(t,c)}:null;_u(c,{shape:{width:l,height:u,x:a,y:s}},n,null,i,g)}return c}function L_(t,e,n){var i=t.getArea(),r=_r(i.r0,1),o=_r(i.r,1),a=new Wl({shape:{cx:_r(t.cx,1),cy:_r(t.cy,1),r0:r,r:o,startAngle:i.startAngle,endAngle:i.endAngle,clockwise:i.clockwise}});e&&("angle"===t.getBaseAxis().dim?a.shape.endAngle=i.startAngle:a.shape.r=r,_u(a,{shape:{endAngle:i.endAngle,r:o}},n));return a}function P_(t,e){return t.type===e}function O_(t,e){if(t.length===e.length){for(var n=0;n<t.length;n++)if(t[n]!==e[n])return;return!0}}function R_(t){for(var e=1/0,n=1/0,i=-1/0,r=-1/0,o=0;o<t.length;){var a=t[o++],s=t[o++];isNaN(a)||(e=Math.min(a,e),i=Math.max(a,i)),isNaN(s)||(n=Math.min(s,n),r=Math.max(s,r))}return[[e,n],[i,r]]}function N_(t,e){var n=R_(t),i=n[0],r=n[1],o=R_(e),a=o[0],s=o[1];return Math.max(Math.abs(i[0]-a[0]),Math.abs(i[1]-a[1]),Math.abs(r[0]-s[0]),Math.abs(r[1]-s[1]))}function E_(t){return X(t)?t:t?.5:0}function z_(t,e,n,i,r){var o=n.getBaseAxis(),a="x"===o.dim||"radius"===o.dim?0:1,s=[],l=0,u=[],h=[],c=[],p=[];if(r){for(l=0;l<t.length;l+=2){var f=e||t;isNaN(f[l])||isNaN(f[l+1])||p.push(t[l],t[l+1])}t=p}for(l=0;l<t.length-2;l+=2)switch(c[0]=t[l+2],c[1]=t[l+3],h[0]=t[l],h[1]=t[l+1],s.push(h[0],h[1]),i){case"end":u[a]=c[a],u[1-a]=h[1-a],s.push(u[0],u[1]);break;case"middle":var d=(h[a]+c[a])/2,g=[];u[a]=g[a]=d,u[1-a]=h[1-a],g[1-a]=c[1-a],s.push(u[0],u[1]),s.push(g[0],g[1]);break;default:u[a]=h[a],u[1-a]=c[1-a],s.push(u[0],u[1])}return s.push(t[l++],t[l++]),s}function B_(t,e,n){var i=t.getVisual("visualMeta");if(i&&i.length&&t.count()&&"cartesian2d"===e.type){for(var r,o,a=i.length-1;a>=0;a--){var s=t.getDimensionInfo(i[a].dimension);if("x"===(r=s&&s.coordDim)||"y"===r){o=i[a];break}}if(o){var l=e.getAxis(r),u=N(o.stops,(function(t){return{coord:l.toGlobalCoord(l.dataToCoord(t.value)),color:t.color}})),h=u.length,c=o.outerColors.slice();h&&u[0].coord>u[h-1].coord&&(u.reverse(),c.reverse());var p=function(t,e){var n,i,r=[],o=t.length;function a(t,e,n){var i=t.coord;return{coord:n,color:Hn((n-i)/(e.coord-i),[t.color,e.color])}}for(var s=0;s<o;s++){var l=t[s],u=l.coord;if(u<0)n=l;else{if(u>e){i?r.push(a(i,l,e)):n&&r.push(a(n,l,0),a(n,l,e));break}n&&(r.push(a(n,l,0)),n=null),r.push(l),i=l}}return r}(u,"x"===r?n.getWidth():n.getHeight()),f=p.length;if(!f&&h)return u[0].coord<0?c[1]?c[1]:u[h-1].color:c[0]?c[0]:u[0].color;var d=p[0].coord-10,g=p[f-1].coord+10,y=g-d;if(y<.001)return"transparent";R(p,(function(t){t.offset=(t.coord-d)/y})),p.push({offset:f?p[f-1].offset:.5,color:c[1]||"transparent"}),p.unshift({offset:f?p[0].offset:.5,color:c[0]||"transparent"});var v=new su(0,0,0,0,p,!0);return v[r]=d,v[r+"2"]=g,v}}}function F_(t,e,n){var i=t.get("showAllSymbol"),r="auto"===i;if(!i||r){var o=n.getAxesByScale("ordinal")[0];if(o&&(!r||!function(t,e){var n=t.getExtent(),i=Math.abs(n[1]-n[0])/t.scale.count();isNaN(i)&&(i=0);for(var r=e.count(),o=Math.max(1,Math.round(r/5)),a=0;a<r;a+=o)if(1.5*d_.getSymbolSize(e,a)[t.isHorizontal()?1:0]>i)return!1;return!0}(o,e))){var a=e.mapDimension(o.dim),s={};return R(o.getViewLabels(),(function(t){var e=o.scale.getRawOrdinalNumber(t.tickValue);s[e]=1})),function(t){return!s.hasOwnProperty(e.get(a,t))}}}}function V_(t,e){return[t[2*e],t[2*e+1]]}function H_(t){if(t.get(["endLabel","show"]))return!0;for(var e=0;e<ms.length;e++)if(t.get([ms[e],"endLabel","show"]))return!0;return!1}function W_(t,e,n,i){if(P_(e,"cartesian2d")){var r=i.getModel("endLabel"),o=r.get("valueAnimation"),a=i.getData(),s={lastFrameIndex:0},l=H_(i)?function(n,i){t._endLabelOnDuring(n,i,a,s,o,r,e)}:null,u=e.getBaseAxis().isHorizontal(),h=A_(e,n,i,(function(){var e=t._endLabel;e&&n&&null!=s.originalX&&e.attr({x:s.originalX,y:s.originalY})}),l);if(!i.get("clip",!0)){var c=h.shape,p=Math.max(c.width,c.height);u?(c.y-=p,c.height+=2*p):(c.x-=p,c.width+=2*p)}return l&&l(1,h),h}return L_(e,n,i)}var G_=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.init=function(){var t=new sr,e=new __;this.group.add(e.group),this._symbolDraw=e,this._lineGroup=t,this._changePolyState=F(this._changePolyState,this)},e.prototype.render=function(t,e,n){var i=t.coordinateSystem,r=this.group,o=t.getData(),a=t.getModel("lineStyle"),s=t.getModel("areaStyle"),l=o.getLayout("points")||[],u="polar"===i.type,h=this._coordSys,c=this._symbolDraw,p=this._polyline,f=this._polygon,d=this._lineGroup,g=!e.ssr&&t.get("animation"),y=!s.isEmpty(),v=s.get("origin"),m=x_(i,o,v),_=y&&function(t,e,n){if(!n.valueDim)return[];for(var i=e.count(),r=Av(2*i),o=0;o<i;o++){var a=w_(n,t,e,o);r[2*o]=a[0],r[2*o+1]=a[1]}return r}(i,o,m),x=t.get("showSymbol"),w=t.get("connectNulls"),b=x&&!u&&F_(t,o,i),S=this._data;S&&S.eachItemGraphicEl((function(t,e){t.__temp&&(r.remove(t),S.setItemGraphicEl(e,null))})),x||c.remove(),r.add(d);var M,T=!u&&t.get("step");i&&i.getArea&&t.get("clip",!0)&&(null!=(M=i.getArea()).width?(M.x-=.1,M.y-=.1,M.width+=.2,M.height+=.2):M.r0&&(M.r0-=.5,M.r+=.5)),this._clipShapeForSymbol=M;var C=B_(o,i,n)||o.getVisual("style")[o.getVisual("drawType")];if(p&&h.type===i.type&&T===this._step){y&&!f?f=this._newPolygon(l,_):f&&!y&&(d.remove(f),f=this._polygon=null),u||this._initOrUpdateEndLabel(t,i,tc(C));var D=d.getClipPath();if(D)_u(D,{shape:W_(this,i,!1,t).shape},t);else d.setClipPath(W_(this,i,!0,t));x&&c.updateData(o,{isIgnore:b,clipShape:M,disableAnimation:!0,getSymbolPoint:function(t){return[l[2*t],l[2*t+1]]}}),O_(this._stackedOnPoints,_)&&O_(this._points,l)||(g?this._doUpdateAnimation(o,_,i,n,T,v,w):(T&&(_&&(_=z_(_,l,i,T,w)),l=z_(l,null,i,T,w)),p.setShape({points:l}),f&&f.setShape({points:l,stackedOnPoints:_})))}else x&&c.updateData(o,{isIgnore:b,clipShape:M,disableAnimation:!0,getSymbolPoint:function(t){return[l[2*t],l[2*t+1]]}}),g&&this._initSymbolLabelAnimation(o,i,M),T&&(_&&(_=z_(_,l,i,T,w)),l=z_(l,null,i,T,w)),p=this._newPolyline(l),y?f=this._newPolygon(l,_):f&&(d.remove(f),f=this._polygon=null),u||this._initOrUpdateEndLabel(t,i,tc(C)),d.setClipPath(W_(this,i,!0,t));var k=t.getModel("emphasis"),A=k.get("focus"),L=k.get("blurScope"),P=k.get("disabled");(p.useStyle(I(a.getLineStyle(),{fill:"none",stroke:C,lineJoin:"bevel"})),nl(p,t,"lineStyle"),p.style.lineWidth>0&&"bolder"===t.get(["emphasis","lineStyle","width"]))&&(p.getState("emphasis").style.lineWidth=+p.style.lineWidth+1);fs(p).seriesIndex=t.seriesIndex,Js(p,A,L,P);var O=E_(t.get("smooth")),R=t.get("smoothMonotone");if(p.setShape({smooth:O,smoothMonotone:R,connectNulls:w}),f){var N=o.getCalculationInfo("stackedOnSeries"),E=0;f.useStyle(I(s.getAreaStyle(),{fill:C,opacity:.7,lineJoin:"bevel",decal:o.getVisual("style").decal})),N&&(E=E_(N.get("smooth"))),f.setShape({smooth:O,stackedOnSmooth:E,smoothMonotone:R,connectNulls:w}),nl(f,t,"areaStyle"),fs(f).seriesIndex=t.seriesIndex,Js(f,A,L,P)}var z=this._changePolyState;o.eachItemGraphicEl((function(t){t&&(t.onHoverStateChange=z)})),this._polyline.onHoverStateChange=z,this._data=o,this._coordSys=i,this._stackedOnPoints=_,this._points=l,this._step=T,this._valueOrigin=v,t.get("triggerLineEvent")&&(this.packEventData(t,p),f&&this.packEventData(t,f))},e.prototype.packEventData=function(t,e){fs(e).eventData={componentType:"series",componentSubType:"line",componentIndex:t.componentIndex,seriesIndex:t.seriesIndex,seriesName:t.name,seriesType:"line"}},e.prototype.highlight=function(t,e,n,i){var r=t.getData(),o=$r(r,i);if(this._changePolyState("emphasis"),!(o instanceof Array)&&null!=o&&o>=0){var a=r.getLayout("points"),s=r.getItemGraphicEl(o);if(!s){var l=a[2*o],u=a[2*o+1];if(isNaN(l)||isNaN(u))return;if(this._clipShapeForSymbol&&!this._clipShapeForSymbol.contain(l,u))return;var h=t.get("zlevel")||0,c=t.get("z")||0;(s=new d_(r,o)).x=l,s.y=u,s.setZ(h,c);var p=s.getSymbolPath().getTextContent();p&&(p.zlevel=h,p.z=c,p.z2=this._polyline.z2+1),s.__temp=!0,r.setItemGraphicEl(o,s),s.stopSymbolAnimation(!0),this.group.add(s)}s.highlight()}else Ef.prototype.highlight.call(this,t,e,n,i)},e.prototype.downplay=function(t,e,n,i){var r=t.getData(),o=$r(r,i);if(this._changePolyState("normal"),null!=o&&o>=0){var a=r.getItemGraphicEl(o);a&&(a.__temp?(r.setItemGraphicEl(o,null),this.group.remove(a)):a.downplay())}else Ef.prototype.downplay.call(this,t,e,n,i)},e.prototype._changePolyState=function(t){var e=this._polygon;Ns(this._polyline,t),e&&Ns(e,t)},e.prototype._newPolyline=function(t){var e=this._polyline;return e&&this._lineGroup.remove(e),e=new D_({shape:{points:t},segmentIgnoreThreshold:2,z2:10}),this._lineGroup.add(e),this._polyline=e,e},e.prototype._newPolygon=function(t,e){var n=this._polygon;return n&&this._lineGroup.remove(n),n=new k_({shape:{points:t,stackedOnPoints:e},segmentIgnoreThreshold:2}),this._lineGroup.add(n),this._polygon=n,n},e.prototype._initSymbolLabelAnimation=function(t,e,n){var i,r,o=e.getBaseAxis(),a=o.inverse;"cartesian2d"===e.type?(i=o.isHorizontal(),r=!1):"polar"===e.type&&(i="angle"===o.dim,r=!0);var s=t.hostModel,l=s.get("animationDuration");W(l)&&(l=l(null));var u=s.get("animationDelay")||0,h=W(u)?u(null):u;t.eachItemGraphicEl((function(t,o){var s=t;if(s){var c=[t.x,t.y],p=void 0,f=void 0,d=void 0;if(n)if(r){var g=n,y=e.pointToCoord(c);i?(p=g.startAngle,f=g.endAngle,d=-y[1]/180*Math.PI):(p=g.r0,f=g.r,d=y[0])}else{var v=n;i?(p=v.x,f=v.x+v.width,d=t.x):(p=v.y+v.height,f=v.y,d=t.y)}var m=f===p?0:(d-p)/(f-p);a&&(m=1-m);var _=W(u)?u(o):l*m+h,x=s.getSymbolPath(),w=x.getTextContent();s.attr({scaleX:0,scaleY:0}),s.animateTo({scaleX:1,scaleY:1},{duration:200,setToFinal:!0,delay:_}),w&&w.animateFrom({style:{opacity:0}},{duration:300,delay:_}),x.disableLabelAnimation=!0}}))},e.prototype._initOrUpdateEndLabel=function(t,e,n){var i=t.getModel("endLabel");if(H_(t)){var r=t.getData(),o=this._polyline,a=r.getLayout("points");if(!a)return o.removeTextContent(),void(this._endLabel=null);var s=this._endLabel;s||((s=this._endLabel=new es({z2:200})).ignoreClip=!0,o.setTextContent(this._endLabel),o.disableLabelAnimation=!0);var l=function(t){for(var e,n,i=t.length/2;i>0&&(e=t[2*i-2],n=t[2*i-1],isNaN(e)||isNaN(n));i--);return i-1}(a);l>=0&&(Wu(o,Gu(t,"endLabel"),{inheritColor:n,labelFetcher:t,labelDataIndex:l,defaultText:function(t,e,n){return null!=n?f_(r,n):p_(r,t)},enableTextSetter:!0},function(t,e){var n=e.getBaseAxis(),i=n.isHorizontal(),r=n.inverse,o=i?r?"right":"left":"center",a=i?"middle":r?"top":"bottom";return{normal:{align:t.get("align")||o,verticalAlign:t.get("verticalAlign")||a}}}(i,e)),o.textConfig.position=null)}else this._endLabel&&(this._polyline.removeTextContent(),this._endLabel=null)},e.prototype._endLabelOnDuring=function(t,e,n,i,r,o,a){var s=this._endLabel,l=this._polyline;if(s){t<1&&null==i.originalX&&(i.originalX=s.x,i.originalY=s.y);var u=n.getLayout("points"),h=n.hostModel,c=h.get("connectNulls"),p=o.get("precision"),f=o.get("distance")||0,d=a.getBaseAxis(),g=d.isHorizontal(),y=d.inverse,v=e.shape,m=y?g?v.x:v.y+v.height:g?v.x+v.width:v.y,_=(g?f:0)*(y?-1:1),x=(g?0:-f)*(y?-1:1),w=g?"x":"y",b=function(t,e,n){for(var i,r,o=t.length/2,a="x"===n?0:1,s=0,l=-1,u=0;u<o;u++)if(r=t[2*u+a],!isNaN(r)&&!isNaN(t[2*u+1-a]))if(0!==u){if(i<=e&&r>=e||i>=e&&r<=e){l=u;break}s=u,i=r}else i=r;return{range:[s,l],t:(e-i)/(r-i)}}(u,m,w),S=b.range,M=S[1]-S[0],T=void 0;if(M>=1){if(M>1&&!c){var C=V_(u,S[0]);s.attr({x:C[0]+_,y:C[1]+x}),r&&(T=h.getRawValue(S[0]))}else{(C=l.getPointOn(m,w))&&s.attr({x:C[0]+_,y:C[1]+x});var D=h.getRawValue(S[0]),I=h.getRawValue(S[1]);r&&(T=function(t,e,n,i,r){var o=null==e||"auto"===e;if(null==i)return i;if(X(i))return _r(d=Br(n||0,i,r),o?Math.max(xr(n||0),xr(i)):e);if(G(i))return r<1?n:i;for(var a=[],s=n,l=i,u=Math.max(s?s.length:0,l.length),h=0;h<u;++h){var c=t.getDimensionInfo(h);if(c&&"ordinal"===c.type)a[h]=(r<1&&s?s:l)[h];else{var p=s&&s[h]?s[h]:0,f=l[h],d=Br(p,f,r);a[h]=_r(d,o?Math.max(xr(p),xr(f)):e)}}return a}(n,p,D,I,b.t))}i.lastFrameIndex=S[0]}else{var k=1===t||i.lastFrameIndex>0?S[0]:0;C=V_(u,k);r&&(T=h.getRawValue(k)),s.attr({x:C[0]+_,y:C[1]+x})}if(r){var A=Ku(s);"function"==typeof A.setLabelText&&A.setLabelText(T)}}},e.prototype._doUpdateAnimation=function(t,e,n,i,r,o,a){var s=this._polyline,l=this._polygon,u=t.hostModel,h=function(t,e,n,i,r,o,a,s){for(var l=function(t,e){var n=[];return e.diff(t).add((function(t){n.push({cmd:"+",idx:t})})).update((function(t,e){n.push({cmd:"=",idx:e,idx1:t})})).remove((function(t){n.push({cmd:"-",idx:t})})).execute(),n}(t,e),u=[],h=[],c=[],p=[],f=[],d=[],g=[],y=x_(r,e,a),v=t.getLayout("points")||[],m=e.getLayout("points")||[],_=0;_<l.length;_++){var x=l[_],w=!0,b=void 0,S=void 0;switch(x.cmd){case"=":b=2*x.idx,S=2*x.idx1;var M=v[b],T=v[b+1],C=m[S],D=m[S+1];(isNaN(M)||isNaN(T))&&(M=C,T=D),u.push(M,T),h.push(C,D),c.push(n[b],n[b+1]),p.push(i[S],i[S+1]),g.push(e.getRawIndex(x.idx1));break;case"+":var I=x.idx,k=y.dataDimsForPoint,A=r.dataToPoint([e.get(k[0],I),e.get(k[1],I)]);S=2*I,u.push(A[0],A[1]),h.push(m[S],m[S+1]);var L=w_(y,r,e,I);c.push(L[0],L[1]),p.push(i[S],i[S+1]),g.push(e.getRawIndex(I));break;case"-":w=!1}w&&(f.push(x),d.push(d.length))}d.sort((function(t,e){return g[t]-g[e]}));var P=u.length,O=Av(P),R=Av(P),N=Av(P),E=Av(P),z=[];for(_=0;_<d.length;_++){var B=d[_],F=2*_,V=2*B;O[F]=u[V],O[F+1]=u[V+1],R[F]=h[V],R[F+1]=h[V+1],N[F]=c[V],N[F+1]=c[V+1],E[F]=p[V],E[F+1]=p[V+1],z[_]=f[B]}return{current:O,next:R,stackedOnCurrent:N,stackedOnNext:E,status:z}}(this._data,t,this._stackedOnPoints,e,this._coordSys,0,this._valueOrigin),c=h.current,p=h.stackedOnCurrent,f=h.next,d=h.stackedOnNext;if(r&&(p=z_(h.stackedOnCurrent,h.current,n,r,a),c=z_(h.current,null,n,r,a),d=z_(h.stackedOnNext,h.next,n,r,a),f=z_(h.next,null,n,r,a)),N_(c,f)>3e3||l&&N_(p,d)>3e3)return s.stopAnimation(),s.setShape({points:f}),void(l&&(l.stopAnimation(),l.setShape({points:f,stackedOnPoints:d})));s.shape.__points=h.current,s.shape.points=c;var g={shape:{points:f}};h.current!==c&&(g.shape.__points=h.next),s.stopAnimation(),mu(s,g,u),l&&(l.setShape({points:c,stackedOnPoints:p}),l.stopAnimation(),mu(l,{shape:{stackedOnPoints:d}},u),s.shape.points!==l.shape.points&&(l.shape.points=s.shape.points));for(var y=[],v=h.status,m=0;m<v.length;m++){if("="===v[m].cmd){var _=t.getItemGraphicEl(v[m].idx1);_&&y.push({el:_,ptIdx:m})}}s.animators&&s.animators.length&&s.animators[0].during((function(){l&&l.dirtyShape();for(var t=s.shape.__points,e=0;e<y.length;e++){var n=y[e].el,i=2*y[e].ptIdx;n.x=t[i],n.y=t[i+1],n.markRedraw()}}))},e.prototype.remove=function(t){var e=this.group,n=this._data;this._lineGroup.removeAll(),this._symbolDraw.remove(!0),n&&n.eachItemGraphicEl((function(t,i){t.__temp&&(e.remove(t),n.setItemGraphicEl(i,null))})),this._polyline=this._polygon=this._coordSys=this._points=this._stackedOnPoints=this._endLabel=this._data=null},e.type="line",e}(Ef);var U_={average:function(t){for(var e=0,n=0,i=0;i<t.length;i++)isNaN(t[i])||(e+=t[i],n++);return 0===n?NaN:e/n},sum:function(t){for(var e=0,n=0;n<t.length;n++)e+=t[n]||0;return e},max:function(t){for(var e=-1/0,n=0;n<t.length;n++)t[n]>e&&(e=t[n]);return isFinite(e)?e:NaN},min:function(t){for(var e=1/0,n=0;n<t.length;n++)t[n]<e&&(e=t[n]);return isFinite(e)?e:NaN},nearest:function(t){return t[0]}},X_=function(t){return Math.round(t.length/2)};function Y_(t){return{seriesType:t,reset:function(t,e,n){var i=t.getData(),r=t.get("sampling"),o=t.coordinateSystem,a=i.count();if(a>10&&"cartesian2d"===o.type&&r){var s=o.getBaseAxis(),l=o.getOtherAxis(s),u=s.getExtent(),h=n.getDevicePixelRatio(),c=Math.abs(u[1]-u[0])*(h||1),p=Math.round(a/c);if(isFinite(p)&&p>1){"lttb"===r?t.setData(i.lttbDownSample(i.mapDimension(l.dim),1/p)):"minmax"===r&&t.setData(i.minmaxDownSample(i.mapDimension(l.dim),1/p));var f=void 0;G(r)?f=U_[r]:W(r)&&(f=r),f&&t.setData(i.downSample(i.mapDimension(l.dim),1/p,f,X_))}}}}}var q_=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return n(e,t),e.prototype.getInitialData=function(t,e){return pv(null,this,{useEncodeDefaulter:!0})},e.prototype.getMarkerPosition=function(t,e,n){var i=this.coordinateSystem;if(i&&i.clampData){var r=i.clampData(t),o=i.dataToPoint(r);if(n)R(i.getAxes(),(function(t,n){if("category"===t.type&&null!=e){var i=t.getTicksCoords(),a=t.getTickModel().get("alignWithLabel"),s=r[n],l="x1"===e[n]||"y1"===e[n];if(l&&!a&&(s+=1),i.length<2)return;if(2===i.length)return void(o[n]=t.toGlobalCoord(t.getExtent()[l?1:0]));for(var u=void 0,h=void 0,c=1,p=0;p<i.length;p++){var f=i[p].coord,d=p===i.length-1?i[p-1].tickValue+c:i[p].tickValue;if(d===s){h=f;break}if(d<s)u=f;else if(null!=u&&d>s){h=(f+u)/2;break}1===p&&(c=d-i[0].tickValue)}null==h&&(u?u&&(h=i[i.length-1].coord):h=i[0].coord),o[n]=t.toGlobalCoord(h)}}));else{var a=this.getData(),s=a.getLayout("offset"),l=a.getLayout("size"),u=i.getBaseAxis().isHorizontal()?0:1;o[u]+=s+l/2}return o}return[NaN,NaN]},e.type="series.__base_bar__",e.defaultOption={z:2,coordinateSystem:"cartesian2d",legendHoverLink:!0,barMinHeight:0,barMinAngle:0,large:!1,largeThreshold:400,progressive:3e3,progressiveChunkMode:"mod"},e}(Mf);Mf.registerClass(q_);var Z_=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}var i,r;return n(e,t),e.prototype.getInitialData=function(){return pv(null,this,{useEncodeDefaulter:!0,createInvertedIndices:!!this.get("realtimeSort",!0)||null})},e.prototype.getProgressive=function(){return!!this.get("large")&&this.get("progressive")},e.prototype.getProgressiveThreshold=function(){var t=this.get("progressiveThreshold"),e=this.get("largeThreshold");return e>t&&(t=e),t},e.prototype.brushSelector=function(t,e,n){return n.rect(e.getItemLayout(t))},e.type="series.bar",e.dependencies=["grid","polar"],e.defaultOption=(i=q_.defaultOption,r={clip:!0,roundCap:!1,showBackground:!1,backgroundStyle:{color:"rgba(180, 180, 180, 0.2)",borderColor:null,borderWidth:0,borderType:"solid",borderRadius:0,shadowBlur:0,shadowColor:null,shadowOffsetX:0,shadowOffsetY:0,opacity:1},select:{itemStyle:{borderColor:"#212121"}},realtimeSort:!1},C(C({},i,!0),r,!0)),e}(q_),j_=function(){this.cx=0,this.cy=0,this.r0=0,this.r=0,this.startAngle=0,this.endAngle=2*Math.PI,this.clockwise=!0},K_=function(t){function e(e){var n=t.call(this,e)||this;return n.type="sausage",n}return n(e,t),e.prototype.getDefaultShape=function(){return new j_},e.prototype.buildPath=function(t,e){var n=e.cx,i=e.cy,r=Math.max(e.r0||0,0),o=Math.max(e.r,0),a=.5*(o-r),s=r+a,l=e.startAngle,u=e.endAngle,h=e.clockwise,c=2*Math.PI,p=h?u-l<c:l-u<c;p||(l=u-(h?c:-c));var f=Math.cos(l),d=Math.sin(l),g=Math.cos(u),y=Math.sin(u);p?(t.moveTo(f*r+n,d*r+i),t.arc(f*s+n,d*s+i,a,-Math.PI+l,l,!h)):t.moveTo(f*o+n,d*o+i),t.arc(n,i,o,l,u,!h),t.arc(g*s+n,y*s+i,a,u-2*Math.PI,u-Math.PI,!h),0!==r&&t.arc(n,i,r,u,l,h)},e}(Ha);function $_(t,e,n){return e*Math.sin(t)*(n?-1:1)}function Q_(t,e,n){return e*Math.cos(t)*(n?1:-1)}function J_(t,e,n){var i=t.get("borderRadius");if(null==i)return n?{cornerRadius:0}:null;H(i)||(i=[i,i,i,i]);var r=Math.abs(e.r||0-e.r0||0);return{cornerRadius:N(i,(function(t){return ji(t,r)}))}}var tx=Math.max,ex=Math.min;var nx=function(t){function e(){var n=t.call(this)||this;return n.type=e.type,n._isFirstFrame=!0,n}return n(e,t),e.prototype.render=function(t,e,n,i){this._model=t,this._removeOnRenderedListener(n),this._updateDrawMode(t);var r=t.get("coordinateSystem");("cartesian2d"===r||"polar"===r)&&(this._progressiveEls=null,this._isLargeDraw?this._renderLarge(t,e,n):this._renderNormal(t,e,n,i))},e.prototype.incrementalPrepareRender=function(t){this._clear(),this._updateDrawMode(t),this._updateLargeClip(t)},e.prototype.incrementalRender=function(t,e){this._progressiveEls=[],this._incrementalRenderLarge(t,e)},e.prototype.eachRendered=function(t){Fu(this._progressiveEls||this.group,t)},e.prototype._updateDrawMode=function(t){var e=t.pipelineContext.large;null!=this._isLargeDraw&&e===this._isLargeDraw||(this._isLargeDraw=e,this._clear())},e.prototype._renderNormal=function(t,e,n,i){var r,o=this.group,a=t.getData(),s=this._data,l=t.coordinateSystem,u=l.getBaseAxis();"cartesian2d"===l.type?r=u.isHorizontal():"polar"===l.type&&(r="angle"===u.dim);var h=t.isAnimationEnabled()?t:null,c=function(t,e){var n=t.get("realtimeSort",!0),i=e.getBaseAxis();0;if(n&&"category"===i.type&&"cartesian2d"===e.type)return{baseAxis:i,otherAxis:e.getOtherAxis(i)}}(t,l);c&&this._enableRealtimeSort(c,a,n);var p=t.get("clip",!0)||c,f=function(t,e){var n=t.getArea&&t.getArea();if(P_(t,"cartesian2d")){var i=t.getBaseAxis();if("category"!==i.type||!i.onBand){var r=e.getLayout("bandWidth");i.isHorizontal()?(n.x-=r,n.width+=2*r):(n.y-=r,n.height+=2*r)}}return n}(l,a);o.removeClipPath();var d=t.get("roundCap",!0),g=t.get("showBackground",!0),y=t.getModel("backgroundStyle"),v=y.get("borderRadius")||0,m=[],_=this._backgroundEls,x=i&&i.isInitSort,w=i&&"changeAxisOrder"===i.type;function b(t){var e=hx[l.type](a,t),n=function(t,e,n){var i="polar"===t.type?Wl:Qa;return new i({shape:vx(e,n,t),silent:!0,z2:0})}(l,r,e);return n.useStyle(y.getItemStyle()),"cartesian2d"===l.type?n.setShape("r",v):n.setShape("cornerRadius",v),m[t]=n,n}a.diff(s).add((function(e){var n=a.getItemModel(e),i=hx[l.type](a,e,n);if(g&&b(e),a.hasValue(e)&&ux[l.type](i)){var s=!1;p&&(s=ix[l.type](f,i));var y=rx[l.type](t,a,e,i,r,h,u.model,!1,d);c&&(y.forceLabelAnimation=!0),px(y,a,e,n,i,t,r,"polar"===l.type),x?y.attr({shape:i}):c?ox(c,h,y,i,e,r,!1,!1):_u(y,{shape:i},t,e),a.setItemGraphicEl(e,y),o.add(y),y.ignore=s}})).update((function(e,n){var i=a.getItemModel(e),S=hx[l.type](a,e,i);if(g){var M=void 0;0===_.length?M=b(n):((M=_[n]).useStyle(y.getItemStyle()),"cartesian2d"===l.type?M.setShape("r",v):M.setShape("cornerRadius",v),m[e]=M);var T=hx[l.type](a,e);mu(M,{shape:vx(r,T,l)},h,e)}var C=s.getItemGraphicEl(n);if(a.hasValue(e)&&ux[l.type](S)){var D=!1;if(p&&(D=ix[l.type](f,S))&&o.remove(C),C?Mu(C):C=rx[l.type](t,a,e,S,r,h,u.model,!!C,d),c&&(C.forceLabelAnimation=!0),w){var I=C.getTextContent();if(I){var k=Ku(I);null!=k.prevValue&&(k.prevValue=k.value)}}else px(C,a,e,i,S,t,r,"polar"===l.type);x?C.attr({shape:S}):c?ox(c,h,C,S,e,r,!0,w):mu(C,{shape:S},t,e,null),a.setItemGraphicEl(e,C),C.ignore=D,o.add(C)}else o.remove(C)})).remove((function(e){var n=s.getItemGraphicEl(e);n&&Su(n,t,e)})).execute();var S=this._backgroundGroup||(this._backgroundGroup=new sr);S.removeAll();for(var M=0;M<m.length;++M)S.add(m[M]);o.add(S),this._backgroundEls=m,this._data=a},e.prototype._renderLarge=function(t,e,n){this._clear(),gx(t,this.group),this._updateLargeClip(t)},e.prototype._incrementalRenderLarge=function(t,e){this._removeBackground(),gx(e,this.group,this._progressiveEls,!0)},e.prototype._updateLargeClip=function(t){var e=t.get("clip",!0)&&function(t,e,n,i,r){return t?"polar"===t.type?L_(t,e,n):"cartesian2d"===t.type?A_(t,e,n,i,r):null:null}(t.coordinateSystem,!1,t),n=this.group;e?n.setClipPath(e):n.removeClipPath()},e.prototype._enableRealtimeSort=function(t,e,n){var i=this;if(e.count()){var r=t.baseAxis;if(this._isFirstFrame)this._dispatchInitSort(e,t,n),this._isFirstFrame=!1;else{var o=function(t){var n=e.getItemGraphicEl(t),i=n&&n.shape;return i&&Math.abs(r.isHorizontal()?i.height:i.width)||0};this._onRendered=function(){i._updateSortWithinSameData(e,o,r,n)},n.getZr().on("rendered",this._onRendered)}}},e.prototype._dataSort=function(t,e,n){var i=[];return t.each(t.mapDimension(e.dim),(function(t,e){var r=n(e);r=null==r?NaN:r,i.push({dataIndex:e,mappedValue:r,ordinalNumber:t})})),i.sort((function(t,e){return e.mappedValue-t.mappedValue})),{ordinalNumbers:N(i,(function(t){return t.ordinalNumber}))}},e.prototype._isOrderChangedWithinSameData=function(t,e,n){for(var i=n.scale,r=t.mapDimension(n.dim),o=Number.MAX_VALUE,a=0,s=i.getOrdinalMeta().categories.length;a<s;++a){var l=t.rawIndexOf(r,i.getRawOrdinalNumber(a)),u=l<0?Number.MIN_VALUE:e(t.indexOfRawIndex(l));if(u>o)return!0;o=u}return!1},e.prototype._isOrderDifferentInView=function(t,e){for(var n=e.scale,i=n.getExtent(),r=Math.max(0,i[0]),o=Math.min(i[1],n.getOrdinalMeta().categories.length-1);r<=o;++r)if(t.ordinalNumbers[r]!==n.getRawOrdinalNumber(r))return!0},e.prototype._updateSortWithinSameData=function(t,e,n,i){if(this._isOrderChangedWithinSameData(t,e,n)){var r=this._dataSort(t,n,e);this._isOrderDifferentInView(r,n)&&(this._removeOnRenderedListener(i),i.dispatchAction({type:"changeAxisOrder",componentType:n.dim+"Axis",axisId:n.index,sortInfo:r}))}},e.prototype._dispatchInitSort=function(t,e,n){var i=e.baseAxis,r=this._dataSort(t,i,(function(n){return t.get(t.mapDimension(e.otherAxis.dim),n)}));n.dispatchAction({type:"changeAxisOrder",componentType:i.dim+"Axis",isInitSort:!0,axisId:i.index,sortInfo:r})},e.prototype.remove=function(t,e){this._clear(this._model),this._removeOnRenderedListener(e)},e.prototype.dispose=function(t,e){this._removeOnRenderedListener(e)},e.prototype._removeOnRenderedListener=function(t){this._onRendered&&(t.getZr().off("rendered",this._onRendered),this._onRendered=null)},e.prototype._clear=function(t){var e=this.group,n=this._data;t&&t.isAnimationEnabled()&&n&&!this._isLargeDraw?(this._removeBackground(),this._backgroundEls=[],n.eachItemGraphicEl((function(e){Su(e,t,fs(e).dataIndex)}))):e.removeAll(),this._data=null,this._isFirstFrame=!0},e.prototype._removeBackground=function(){this.group.remove(this._backgroundGroup),this._backgroundGroup=null},e.type="bar",e}(Ef),ix={cartesian2d:function(t,e){var n=e.width<0?-1:1,i=e.height<0?-1:1;n<0&&(e.x+=e.width,e.width=-e.width),i<0&&(e.y+=e.height,e.height=-e.height);var r=t.x+t.width,o=t.y+t.height,a=tx(e.x,t.x),s=ex(e.x+e.width,r),l=tx(e.y,t.y),u=ex(e.y+e.height,o),h=s<a,c=u<l;return e.x=h&&a>r?s:a,e.y=c&&l>o?u:l,e.width=h?0:s-a,e.height=c?0:u-l,n<0&&(e.x+=e.width,e.width=-e.width),i<0&&(e.y+=e.height,e.height=-e.height),h||c},polar:function(t,e){var n=e.r0<=e.r?1:-1;if(n<0){var i=e.r;e.r=e.r0,e.r0=i}var r=ex(e.r,t.r),o=tx(e.r0,t.r0);e.r=r,e.r0=o;var a=r-o<0;if(n<0){i=e.r;e.r=e.r0,e.r0=i}return a}},rx={cartesian2d:function(t,e,n,i,r,o,a,s,l){var u=new Qa({shape:D({},i),z2:1});(u.__dataIndex=n,u.name="item",o)&&(u.shape[r?"height":"width"]=0);return u},polar:function(t,e,n,i,r,o,a,s,l){var u=!r&&l?K_:Wl,h=new u({shape:i,z2:1});h.name="item";var c,p,f=cx(r);if(h.calculateTextPosition=(c=f,p=({isRoundCap:u===K_}||{}).isRoundCap,function(t,e,n){var i=e.position;if(!i||i instanceof Array)return Ki(t,e,n);var r=c(i),o=null!=e.distance?e.distance:5,a=this.shape,s=a.cx,l=a.cy,u=a.r,h=a.r0,f=(u+h)/2,d=a.startAngle,g=a.endAngle,y=(d+g)/2,v=p?Math.abs(u-h)/2:0,m=Math.cos,_=Math.sin,x=s+u*m(d),w=l+u*_(d),b="left",S="top";switch(r){case"startArc":x=s+(h-o)*m(y),w=l+(h-o)*_(y),b="center",S="top";break;case"insideStartArc":x=s+(h+o)*m(y),w=l+(h+o)*_(y),b="center",S="bottom";break;case"startAngle":x=s+f*m(d)+$_(d,o+v,!1),w=l+f*_(d)+Q_(d,o+v,!1),b="right",S="middle";break;case"insideStartAngle":x=s+f*m(d)+$_(d,-o+v,!1),w=l+f*_(d)+Q_(d,-o+v,!1),b="left",S="middle";break;case"middle":x=s+f*m(y),w=l+f*_(y),b="center",S="middle";break;case"endArc":x=s+(u+o)*m(y),w=l+(u+o)*_(y),b="center",S="bottom";break;case"insideEndArc":x=s+(u-o)*m(y),w=l+(u-o)*_(y),b="center",S="top";break;case"endAngle":x=s+f*m(g)+$_(g,o+v,!0),w=l+f*_(g)+Q_(g,o+v,!0),b="left",S="middle";break;case"insideEndAngle":x=s+f*m(g)+$_(g,-o+v,!0),w=l+f*_(g)+Q_(g,-o+v,!0),b="right",S="middle";break;default:return Ki(t,e,n)}return(t=t||{}).x=x,t.y=w,t.align=b,t.verticalAlign=S,t}),o){var d=r?"r":"endAngle",g={};h.shape[d]=r?i.r0:i.startAngle,g[d]=i[d],(s?mu:_u)(h,{shape:g},o)}return h}};function ox(t,e,n,i,r,o,a,s){var l,u;o?(u={x:i.x,width:i.width},l={y:i.y,height:i.height}):(u={y:i.y,height:i.height},l={x:i.x,width:i.width}),s||(a?mu:_u)(n,{shape:l},e,r,null),(a?mu:_u)(n,{shape:u},e?t.baseAxis.model:null,r)}function ax(t,e){for(var n=0;n<e.length;n++)if(!isFinite(t[e[n]]))return!0;return!1}var sx=["x","y","width","height"],lx=["cx","cy","r","startAngle","endAngle"],ux={cartesian2d:function(t){return!ax(t,sx)},polar:function(t){return!ax(t,lx)}},hx={cartesian2d:function(t,e,n){var i=t.getItemLayout(e),r=n?function(t,e){var n=t.get(["itemStyle","borderColor"]);if(!n||"none"===n)return 0;var i=t.get(["itemStyle","borderWidth"])||0,r=isNaN(e.width)?Number.MAX_VALUE:Math.abs(e.width),o=isNaN(e.height)?Number.MAX_VALUE:Math.abs(e.height);return Math.min(i,r,o)}(n,i):0,o=i.width>0?1:-1,a=i.height>0?1:-1;return{x:i.x+o*r/2,y:i.y+a*r/2,width:i.width-o*r,height:i.height-a*r}},polar:function(t,e,n){var i=t.getItemLayout(e);return{cx:i.cx,cy:i.cy,r0:i.r0,r:i.r,startAngle:i.startAngle,endAngle:i.endAngle,clockwise:i.clockwise}}};function cx(t){return function(t){var e=t?"Arc":"Angle";return function(t){switch(t){case"start":case"insideStart":case"end":case"insideEnd":return t+e;default:return t}}}(t)}function px(t,e,n,i,r,o,a,s){var l=e.getItemVisual(n,"style");if(s){if(!o.get("roundCap")){var u=t.shape;D(u,J_(i.getModel("itemStyle"),u,!0)),t.setShape(u)}}else{var h=i.get(["itemStyle","borderRadius"])||0;t.setShape("r",h)}t.useStyle(l);var c=i.getShallow("cursor");c&&t.attr("cursor",c);var p=s?a?r.r>=r.r0?"endArc":"startArc":r.endAngle>=r.startAngle?"endAngle":"startAngle":a?r.height>=0?"bottom":"top":r.width>=0?"right":"left",f=Gu(i);Wu(t,f,{labelFetcher:o,labelDataIndex:n,defaultText:p_(o.getData(),n),inheritColor:l.fill,defaultOpacity:l.opacity,defaultOutsidePosition:p});var d=t.getTextContent();if(s&&d){var g=i.get(["label","position"]);t.textConfig.inside="middle"===g||null,function(t,e,n,i){if(X(i))t.setTextConfig({rotation:i});else if(H(e))t.setTextConfig({rotation:0});else{var r,o=t.shape,a=o.clockwise?o.startAngle:o.endAngle,s=o.clockwise?o.endAngle:o.startAngle,l=(a+s)/2,u=n(e);switch(u){case"startArc":case"insideStartArc":case"middle":case"insideEndArc":case"endArc":r=l;break;case"startAngle":case"insideStartAngle":r=a;break;case"endAngle":case"insideEndAngle":r=s;break;default:return void t.setTextConfig({rotation:0})}var h=1.5*Math.PI-r;"middle"===u&&h>Math.PI/2&&h<1.5*Math.PI&&(h-=Math.PI),t.setTextConfig({rotation:h})}}(t,"outside"===g?p:g,cx(a),i.get(["label","rotate"]))}!function(t,e,n,i){if(t){var r=Ku(t);r.prevValue=r.value,r.value=n;var o=e.normal;r.valueAnimation=o.get("valueAnimation"),r.valueAnimation&&(r.precision=o.get("precision"),r.defaultInterpolatedText=i,r.statesModels=e)}}(d,f,o.getRawValue(n),(function(t){return f_(e,t)}));var y=i.getModel(["emphasis"]);Js(t,y.get("focus"),y.get("blurScope"),y.get("disabled")),nl(t,i),function(t){return null!=t.startAngle&&null!=t.endAngle&&t.startAngle===t.endAngle}(r)&&(t.style.fill="none",t.style.stroke="none",R(t.states,(function(t){t.style&&(t.style.fill=t.style.stroke="none")})))}var fx=function(){},dx=function(t){function e(e){var n=t.call(this,e)||this;return n.type="largeBar",n}return n(e,t),e.prototype.getDefaultShape=function(){return new fx},e.prototype.buildPath=function(t,e){for(var n=e.points,i=this.baseDimIdx,r=1-this.baseDimIdx,o=[],a=[],s=this.barWidth,l=0;l<n.length;l+=3)a[i]=s,a[r]=n[l+2],o[i]=n[l+i],o[r]=n[l+r],t.rect(o[0],o[1],a[0],a[1])},e}(Ha);function gx(t,e,n,i){var r=t.getData(),o=r.getLayout("valueAxisHorizontal")?1:0,a=r.getLayout("largeDataIndices"),s=r.getLayout("size"),l=t.getModel("backgroundStyle"),u=r.getLayout("largeBackgroundPoints");if(u){var h=new dx({shape:{points:u},incremental:!!i,silent:!0,z2:0});h.baseDimIdx=o,h.largeDataIndices=a,h.barWidth=s,h.useStyle(l.getItemStyle()),e.add(h),n&&n.push(h)}var c=new dx({shape:{points:r.getLayout("largePoints")},incremental:!!i,ignoreCoarsePointer:!0,z2:1});c.baseDimIdx=o,c.largeDataIndices=a,c.barWidth=s,e.add(c),c.useStyle(r.getVisual("style")),c.style.stroke=null,fs(c).seriesIndex=t.seriesIndex,t.get("silent")||(c.on("mousedown",yx),c.on("mousemove",yx)),n&&n.push(c)}var yx=Wf((function(t){var e=function(t,e,n){for(var i=t.baseDimIdx,r=1-i,o=t.shape.points,a=t.largeDataIndices,s=[],l=[],u=t.barWidth,h=0,c=o.length/3;h<c;h++){var p=3*h;if(l[i]=u,l[r]=o[p+2],s[i]=o[p+i],s[r]=o[p+r],l[r]<0&&(s[r]+=l[r],l[r]=-l[r]),e>=s[0]&&e<=s[0]+l[0]&&n>=s[1]&&n<=s[1]+l[1])return a[h]}return-1}(this,t.offsetX,t.offsetY);fs(this).dataIndex=e>=0?e:null}),30,!1);function vx(t,e,n){if(P_(n,"cartesian2d")){var i=e,r=n.getArea();return{x:t?i.x:r.x,y:t?r.y:i.y,width:t?i.width:r.width,height:t?r.height:i.height}}var o=e;return{cx:(r=n.getArea()).cx,cy:r.cy,r0:t?r.r0:o.r0,r:t?r.r:o.r,startAngle:t?o.startAngle:0,endAngle:t?o.endAngle:2*Math.PI}}var mx=2*Math.PI,_x=Math.PI/180;function xx(t,e){return oc(t.getBoxLayoutParams(),{width:e.getWidth(),height:e.getHeight()})}function bx(t,e){var n=xx(t,e),i=t.get("center"),r=t.get("radius");H(r)||(r=[0,r]);var o,a,s=mr(n.width,e.getWidth()),l=mr(n.height,e.getHeight()),u=Math.min(s,l),h=mr(r[0],u/2),c=mr(r[1],u/2),p=t.coordinateSystem;if(p){var f=p.dataToPoint(i);o=f[0]||0,a=f[1]||0}else H(i)||(i=[i,i]),o=mr(i[0],s)+n.x,a=mr(i[1],l)+n.y;return{cx:o,cy:a,r0:h,r:c}}function Sx(t,e,n){e.eachSeriesByType(t,(function(t){var e=t.getData(),i=e.mapDimension("value"),r=xx(t,n),o=bx(t,n),a=o.cx,s=o.cy,l=o.r,u=o.r0,h=-t.get("startAngle")*_x,c=t.get("endAngle"),p=t.get("padAngle")*_x;c="auto"===c?h-mx:-c*_x;var f=t.get("minAngle")*_x+p,d=0;e.each(i,(function(t){!isNaN(t)&&d++}));var g=e.getSum(i),y=Math.PI/(g||d)*2,v=t.get("clockwise"),m=t.get("roseType"),_=t.get("stillShowZeroSum"),x=e.getDataExtent(i);x[0]=0;var w=v?1:-1,b=[h,c],S=w*p/2;xa(b,!v),h=b[0],c=b[1];var M=Mx(t);M.startAngle=h,M.endAngle=c,M.clockwise=v;var T=Math.abs(c-h),C=T,D=0,I=h;if(e.setLayout({viewRect:r,r:l}),e.each(i,(function(t,n){var i;if(isNaN(t))e.setItemLayout(n,{angle:NaN,startAngle:NaN,endAngle:NaN,clockwise:v,cx:a,cy:s,r0:u,r:m?NaN:l});else{(i="area"!==m?0===g&&_?y:t*y:T/d)<f?(i=f,C-=f):D+=t;var r=I+w*i,o=0,h=0;p>i?h=o=I+w*i/2:(o=I+S,h=r-S),e.setItemLayout(n,{angle:i,startAngle:o,endAngle:h,clockwise:v,cx:a,cy:s,r0:u,r:m?vr(t,x,[u,l]):l}),I=r}})),C<mx&&d)if(C<=.001){var k=T/d;e.each(i,(function(t,n){if(!isNaN(t)){var i=e.getItemLayout(n);i.angle=k;var r=0,o=0;k<p?o=r=h+w*(n+.5)*k:(r=h+w*n*k+S,o=h+w*(n+1)*k-S),i.startAngle=r,i.endAngle=o}}))}else y=C/D,I=h,e.each(i,(function(t,n){if(!isNaN(t)){var i=e.getItemLayout(n),r=i.angle===f?f:t*y,o=0,a=0;r<p?a=o=I+w*r/2:(o=I+S,a=I+w*r-S),i.startAngle=o,i.endAngle=a,I+=w*r}}))}))}var Mx=Qr();var Tx=Math.PI/180;function Cx(t,e,n,i,r,o,a,s,l,u){if(!(t.length<2)){for(var h=t.length,c=0;c<h;c++)if("outer"===t[c].position&&"labelLine"===t[c].labelAlignTo){var p=t[c].label.x-u;t[c].linePoints[1][0]+=p,t[c].label.x=u}r_(t,l,l+a)&&function(t){for(var o={list:[],maxY:0},a={list:[],maxY:0},s=0;s<t.length;s++)if("none"===t[s].labelAlignTo){var l=t[s],u=l.label.y>n?a:o,h=Math.abs(l.label.y-n);if(h>=u.maxY){var c=l.label.x-e-l.len2*r,p=i+l.len,d=Math.abs(c)<p?Math.sqrt(h*h/(1-c*c/p/p)):p;u.rB=d,u.maxY=h}u.list.push(l)}f(o),f(a)}(t)}function f(t){for(var o=t.rB,a=o*o,s=0;s<t.list.length;s++){var l=t.list[s],u=Math.abs(l.label.y-n),h=i+l.len,c=h*h,p=Math.sqrt(Math.abs((1-u*u/a)*c)),f=e+(p+l.len2)*r,d=f-l.label.x;Dx(l,l.targetTextWidth-d*r,!0),l.label.x=f}}}function Dx(t,e,n){if(void 0===n&&(n=!1),null==t.labelStyleWidth){var i=t.label,r=i.style,o=t.rect,a=r.backgroundColor,s=r.padding,l=s?s[1]+s[3]:0,u=r.overflow,h=o.width+(a?0:l);if(e<h||n){var c=o.height;if(u&&u.match("break")){i.setStyle("backgroundColor",null),i.setStyle("width",e-l);var p=i.getBoundingRect();i.setStyle("width",Math.ceil(p.width)),i.setStyle("backgroundColor",a)}else{var f=e-l,d=e<h?f:n?f>t.unconstrainedWidth?null:f:null;i.setStyle("width",d)}var g=i.getBoundingRect();o.width=g.width;var y=(i.style.margin||0)+2.1;o.height=g.height+y,o.y-=(o.height-c)/2}}}function Ix(t){return"center"===t.position}function kx(t){var e,n,i=t.getData(),r=[],o=!1,a=(t.get("minShowLabelAngle")||0)*Tx,s=i.getLayout("viewRect"),l=i.getLayout("r"),u=s.width,h=s.x,c=s.y,p=s.height;function f(t){t.ignore=!0}i.each((function(t){var s=i.getItemGraphicEl(t),c=s.shape,p=s.getTextContent(),d=s.getTextGuideLine(),g=i.getItemModel(t),y=g.getModel("label"),v=y.get("position")||g.get(["emphasis","label","position"]),m=y.get("distanceToLabelLine"),_=y.get("alignTo"),x=mr(y.get("edgeDistance"),u),w=y.get("bleedMargin"),b=g.getModel("labelLine"),S=b.get("length");S=mr(S,u);var M=b.get("length2");if(M=mr(M,u),Math.abs(c.endAngle-c.startAngle)<a)return R(p.states,f),p.ignore=!0,void(d&&(R(d.states,f),d.ignore=!0));if(function(t){if(!t.ignore)return!0;for(var e in t.states)if(!1===t.states[e].ignore)return!0;return!1}(p)){var T,C,D,I,k=(c.startAngle+c.endAngle)/2,A=Math.cos(k),L=Math.sin(k);e=c.cx,n=c.cy;var P="inside"===v||"inner"===v;if("center"===v)T=c.cx,C=c.cy,I="center";else{var O=(P?(c.r+c.r0)/2*A:c.r*A)+e,N=(P?(c.r+c.r0)/2*L:c.r*L)+n;if(T=O+3*A,C=N+3*L,!P){var E=O+A*(S+l-c.r),z=N+L*(S+l-c.r),B=E+(A<0?-1:1)*M;T="edge"===_?A<0?h+x:h+u-x:B+(A<0?-m:m),C=z,D=[[O,N],[E,z],[B,z]]}I=P?"center":"edge"===_?A>0?"right":"left":A>0?"left":"right"}var F=Math.PI,V=0,H=y.get("rotate");if(X(H))V=H*(F/180);else if("center"===v)V=0;else if("radial"===H||!0===H){V=A<0?-k+F:-k}else if("tangential"===H&&"outside"!==v&&"outer"!==v){var W=Math.atan2(A,L);W<0&&(W=2*F+W),L>0&&(W=F+W),V=W-F}if(o=!!V,p.x=T,p.y=C,p.rotation=V,p.setStyle({verticalAlign:"middle"}),P){p.setStyle({align:I});var G=p.states.select;G&&(G.x+=p.x,G.y+=p.y)}else{var U=p.getBoundingRect().clone();U.applyTransform(p.getComputedTransform());var Y=(p.style.margin||0)+2.1;U.y-=Y/2,U.height+=Y,r.push({label:p,labelLine:d,position:v,len:S,len2:M,minTurnAngle:b.get("minTurnAngle"),maxSurfaceAngle:b.get("maxSurfaceAngle"),surfaceNormal:new ve(A,L),linePoints:D,textAlign:I,labelDistance:m,labelAlignTo:_,edgeDistance:x,bleedMargin:w,rect:U,unconstrainedWidth:U.width,labelStyleWidth:p.style.width})}s.setTextConfig({inside:P})}})),!o&&t.get("avoidLabelOverlap")&&function(t,e,n,i,r,o,a,s){for(var l=[],u=[],h=Number.MAX_VALUE,c=-Number.MAX_VALUE,p=0;p<t.length;p++){var f=t[p].label;Ix(t[p])||(f.x<e?(h=Math.min(h,f.x),l.push(t[p])):(c=Math.max(c,f.x),u.push(t[p])))}for(p=0;p<t.length;p++)if(!Ix(y=t[p])&&y.linePoints){if(null!=y.labelStyleWidth)continue;f=y.label;var d=y.linePoints,g=void 0;g="edge"===y.labelAlignTo?f.x<e?d[2][0]-y.labelDistance-a-y.edgeDistance:a+r-y.edgeDistance-d[2][0]-y.labelDistance:"labelLine"===y.labelAlignTo?f.x<e?h-a-y.bleedMargin:a+r-c-y.bleedMargin:f.x<e?f.x-a-y.bleedMargin:a+r-f.x-y.bleedMargin,y.targetTextWidth=g,Dx(y,g)}for(Cx(u,e,n,i,1,0,o,0,s,c),Cx(l,e,n,i,-1,0,o,0,s,h),p=0;p<t.length;p++){var y;if(!Ix(y=t[p])&&y.linePoints){f=y.label,d=y.linePoints;var v="edge"===y.labelAlignTo,m=f.style.padding,_=m?m[1]+m[3]:0,x=f.style.backgroundColor?0:_,w=y.rect.width+x,b=d[1][0]-d[2][0];v?f.x<e?d[2][0]=a+y.edgeDistance+w+y.labelDistance:d[2][0]=a+r-y.edgeDistance-w-y.labelDistance:(f.x<e?d[2][0]=f.x+y.labelDistance:d[2][0]=f.x-y.labelDistance,d[1][0]=d[2][0]+b),d[1][1]=d[2][1]=f.y}}}(r,e,n,l,u,p,h,c);for(var d=0;d<r.length;d++){var g=r[d],y=g.label,v=g.labelLine,m=isNaN(y.x)||isNaN(y.y);if(y){y.setStyle({align:g.textAlign}),m&&(R(y.states,f),y.ignore=!0);var _=y.states.select;_&&(_.x+=y.x,_.y+=y.y)}if(v){var x=g.linePoints;m||!x?(R(v.states,f),v.ignore=!0):(t_(x,g.minTurnAngle),e_(x,g.surfaceNormal,g.maxSurfaceAngle),v.setShape({points:x}),y.__hostTarget.textGuideLineConfig={anchor:new ve(x[0][0],x[0][1])})}}}var Ax=function(t){function e(e,n,i){var r=t.call(this)||this;r.z2=2;var o=new es;return r.setTextContent(o),r.updateData(e,n,i,!0),r}return n(e,t),e.prototype.updateData=function(t,e,n,i){var r=this,o=t.hostModel,a=t.getItemModel(e),s=a.getModel("emphasis"),l=t.getItemLayout(e),u=D(J_(a.getModel("itemStyle"),l,!0),l);if(isNaN(u.startAngle))r.setShape(u);else{if(i){r.setShape(u);var h=o.getShallow("animationType");o.ecModel.ssr?(_u(r,{scaleX:0,scaleY:0},o,{dataIndex:e,isFrom:!0}),r.originX=u.cx,r.originY=u.cy):"scale"===h?(r.shape.r=l.r0,_u(r,{shape:{r:l.r}},o,e)):null!=n?(r.setShape({startAngle:n,endAngle:n}),_u(r,{shape:{startAngle:l.startAngle,endAngle:l.endAngle}},o,e)):(r.shape.endAngle=l.startAngle,mu(r,{shape:{endAngle:l.endAngle}},o,e))}else Mu(r),mu(r,{shape:u},o,e);r.useStyle(t.getItemVisual(e,"style")),nl(r,a);var c=(l.startAngle+l.endAngle)/2,p=o.get("selectedOffset"),f=Math.cos(c)*p,d=Math.sin(c)*p,g=a.getShallow("cursor");g&&r.attr("cursor",g),this._updateLabel(o,t,e),r.ensureState("emphasis").shape=D({r:l.r+(s.get("scale")&&s.get("scaleSize")||0)},J_(s.getModel("itemStyle"),l)),D(r.ensureState("select"),{x:f,y:d,shape:J_(a.getModel(["select","itemStyle"]),l)}),D(r.ensureState("blur"),{shape:J_(a.getModel(["blur","itemStyle"]),l)});var y=r.getTextGuideLine(),v=r.getTextContent();y&&D(y.ensureState("select"),{x:f,y:d}),D(v.ensureState("select"),{x:f,y:d}),Js(this,s.get("focus"),s.get("blurScope"),s.get("disabled"))}},e.prototype._updateLabel=function(t,e,n){var i=this,r=e.getItemModel(n),o=r.getModel("labelLine"),a=e.getItemVisual(n,"style"),s=a&&a.fill,l=a&&a.opacity;Wu(i,Gu(r),{labelFetcher:e.hostModel,labelDataIndex:n,inheritColor:s,defaultOpacity:l,defaultText:t.getFormattedLabel(n,"normal")||e.getName(n)});var u=i.getTextContent();i.setTextConfig({position:null,rotation:null}),u.attr({z2:10});var h=t.get(["label","position"]);if("outside"!==h&&"outer"!==h)i.removeTextGuideLine();else{var c=this.getTextGuideLine();c||(c=new jl,this.setTextGuideLine(c)),function(t,e,n){var i=t.getTextGuideLine(),r=t.getTextContent();if(r){for(var o=e.normal,a=o.get("show"),s=r.ignore,l=0;l<_s.length;l++){var u=_s[l],h=e[u],c="normal"===u;if(h){var p=h.get("show");if((c?s:et(r.states[u]&&r.states[u].ignore,s))||!et(p,a)){var f=c?i:i&&i.states[u];f&&(f.ignore=!0),i&&n_(i,!0,u,h);continue}i||(i=new jl,t.setTextGuideLine(i),c||!s&&a||n_(i,!0,"normal",e.normal),t.stateProxy&&(i.stateProxy=t.stateProxy)),n_(i,!1,u,h)}}if(i){I(i.style,n),i.style.fill=null;var d=o.get("showAbove");(t.textGuideLineConfig=t.textGuideLineConfig||{}).showAbove=d||!1,i.buildPath=i_}}else i&&t.removeTextGuideLine()}(this,function(t,e){e=e||"labelLine";for(var n={normal:t.getModel(e)},i=0;i<ms.length;i++){var r=ms[i];n[r]=t.getModel([r,e])}return n}(r),{stroke:s,opacity:nt(o.get(["lineStyle","opacity"]),l,1)})}},e}(Wl),Lx=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.ignoreLabelLineUpdate=!0,e}return n(e,t),e.prototype.render=function(t,e,n,i){var r,o=t.getData(),a=this._data,s=this.group;if(!a&&o.count()>0){for(var l=o.getItemLayout(0),u=1;isNaN(l&&l.startAngle)&&u<o.count();++u)l=o.getItemLayout(u);l&&(r=l.startAngle)}if(this._emptyCircleSector&&s.remove(this._emptyCircleSector),0===o.count()&&t.get("showEmptyCircle")){var h=Mx(t),c=new Wl({shape:D(bx(t,n),h)});c.useStyle(t.getModel("emptyCircleStyle").getItemStyle()),this._emptyCircleSector=c,s.add(c)}o.diff(a).add((function(t){var e=new Ax(o,t,r);o.setItemGraphicEl(t,e),s.add(e)})).update((function(t,e){var n=a.getItemGraphicEl(e);n.updateData(o,t,r),n.off("click"),s.add(n),o.setItemGraphicEl(t,n)})).remove((function(e){Su(a.getItemGraphicEl(e),t,e)})).execute(),kx(t),"expansion"!==t.get("animationTypeUpdate")&&(this._data=o)},e.prototype.dispose=function(){},e.prototype.containPoint=function(t,e){var n=e.getData().getItemLayout(0);if(n){var i=t[0]-n.cx,r=t[1]-n.cy,o=Math.sqrt(i*i+r*r);return o<=n.r&&o>=n.r0}},e.type="pie",e}(Ef);var Px=function(){function t(t,e){this._getDataWithEncodedVisual=t,this._getRawData=e}return t.prototype.getAllNames=function(){var t=this._getRawData();return t.mapArray(t.getName)},t.prototype.containName=function(t){return this._getRawData().indexOfName(t)>=0},t.prototype.indexOfName=function(t){return this._getDataWithEncodedVisual().indexOfName(t)},t.prototype.getItemVisual=function(t,e){return this._getDataWithEncodedVisual().getItemVisual(t,e)},t}(),Ox=Qr(),Rx=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.init=function(e){t.prototype.init.apply(this,arguments),this.legendVisualProvider=new Px(F(this.getData,this),F(this.getRawData,this)),this._defaultLabelLine(e)},e.prototype.mergeOption=function(){t.prototype.mergeOption.apply(this,arguments)},e.prototype.getInitialData=function(){return function(t,e,n){e=H(e)&&{coordDimensions:e}||D({encodeDefine:t.getEncode()},e);var i=t.getSource(),r=rv(i,e).dimensions,o=new iv(r,t);return o.initData(i,n),o}(this,{coordDimensions:["value"],encodeDefaulter:V(Ic,this)})},e.prototype.getDataParams=function(e){var n=this.getData(),i=Ox(n),r=i.seats;if(!r){var o=[];n.each(n.mapDimension("value"),(function(t){o.push(t)})),r=i.seats=Sr(o,n.hostModel.get("percentPrecision"))}var a=t.prototype.getDataParams.call(this,e);return a.percent=r[e]||0,a.$vars.push("percent"),a},e.prototype._defaultLabelLine=function(t){Hr(t,"labelLine",["show"]);var e=t.labelLine,n=t.emphasis.labelLine;e.show=e.show&&t.label.show,n.show=n.show&&t.emphasis.label.show},e.type="series.pie",e.defaultOption={z:2,legendHoverLink:!0,colorBy:"data",center:["50%","50%"],radius:[0,"75%"],clockwise:!0,startAngle:90,endAngle:"auto",padAngle:0,minAngle:0,minShowLabelAngle:0,selectedOffset:10,percentPrecision:2,stillShowZeroSum:!0,left:0,top:0,right:0,bottom:0,width:null,height:null,label:{rotate:0,show:!0,overflow:"truncate",position:"outer",alignTo:"none",edgeDistance:"25%",bleedMargin:10,distanceToLabelLine:5},labelLine:{show:!0,length:15,length2:15,smooth:!1,minTurnAngle:90,maxSurfaceAngle:90,lineStyle:{width:1,type:"solid"}},itemStyle:{borderWidth:1,borderJoin:"round"},showEmptyCircle:!0,emptyCircleStyle:{color:"lightgray",opacity:1},labelLayout:{hideOverlap:!0},emphasis:{scale:!0,scaleSize:5},avoidLabelOverlap:!0,animationType:"expansion",animationDuration:1e3,animationTypeUpdate:"transition",animationEasingUpdate:"cubicInOut",animationDurationUpdate:500,animationEasing:"cubicInOut"},e}(Mf);var Nx=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.type="grid",e.dependencies=["xAxis","yAxis"],e.layoutMode="box",e.defaultOption={show:!1,z:0,left:"10%",top:60,right:"10%",bottom:70,containLabel:!1,backgroundColor:"rgba(0,0,0,0)",borderWidth:1,borderColor:"#ccc"},e}(hc),Ex=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e.prototype.getCoordSysModel=function(){return this.getReferringComponents("grid",no).models[0]},e.type="cartesian2dAxis",e}(hc);P(Ex,fm);var zx={show:!0,z:0,inverse:!1,name:"",nameLocation:"end",nameRotate:null,nameTruncate:{maxWidth:null,ellipsis:"...",placeholder:"."},nameTextStyle:{},nameGap:15,silent:!1,triggerEvent:!1,tooltip:{show:!1},axisPointer:{},axisLine:{show:!0,onZero:!0,onZeroAxisIndex:null,lineStyle:{color:"#6E7079",width:1,type:"solid"},symbol:["none","none"],symbolSize:[10,15]},axisTick:{show:!0,inside:!1,length:5,lineStyle:{width:1}},axisLabel:{show:!0,inside:!1,rotate:0,showMinLabel:null,showMaxLabel:null,margin:8,fontSize:12},splitLine:{show:!0,showMinLine:!0,showMaxLine:!0,lineStyle:{color:["#E0E6F1"],width:1,type:"solid"}},splitArea:{show:!1,areaStyle:{color:["rgba(250,250,250,0.2)","rgba(210,219,238,0.2)"]}}},Bx=C({boundaryGap:!0,deduplication:null,splitLine:{show:!1},axisTick:{alignWithLabel:!1,interval:"auto"},axisLabel:{interval:"auto"}},zx),Fx=C({boundaryGap:[0,0],axisLine:{show:"auto"},axisTick:{show:"auto"},splitNumber:5,minorTick:{show:!1,splitNumber:5,length:3,lineStyle:{}},minorSplitLine:{show:!1,lineStyle:{color:"#F4F7FD",width:1}}},zx),Vx={category:Bx,value:Fx,time:C({splitNumber:6,axisLabel:{showMinLabel:!1,showMaxLabel:!1,rich:{primary:{fontWeight:"bold"}}},splitLine:{show:!1}},Fx),log:I({logBase:10},Fx)},Hx={value:1,category:1,time:1,log:1};function Wx(t,e,i,r){R(Hx,(function(o,a){var s=C(C({},Vx[a],!0),r,!0),l=function(t){function i(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e+"Axis."+a,n}return n(i,t),i.prototype.mergeDefaultAndTheme=function(t,e){var n=ac(this),i=n?lc(t):{};C(t,e.getTheme().get(a+"Axis")),C(t,this.getDefaultOption()),t.type=Gx(t),n&&sc(t,i,n)},i.prototype.optionUpdated=function(){"category"===this.option.type&&(this.__ordinalMeta=gv.createByAxisModel(this))},i.prototype.getCategories=function(t){var e=this.option;if("category"===e.type)return t?e.data:this.__ordinalMeta.categories},i.prototype.getOrdinalMeta=function(){return this.__ordinalMeta},i.type=e+"Axis."+a,i.defaultOption=s,i}(i);t.registerComponentModel(l)})),t.registerSubTypeDefaulter(e+"Axis",Gx)}function Gx(t){return t.type||(t.data?"category":"value")}var Ux=function(){function t(t){this.type="cartesian",this._dimList=[],this._axes={},this.name=t||""}return t.prototype.getAxis=function(t){return this._axes[t]},t.prototype.getAxes=function(){return N(this._dimList,(function(t){return this._axes[t]}),this)},t.prototype.getAxesByScale=function(t){return t=t.toLowerCase(),z(this.getAxes(),(function(e){return e.scale.type===t}))},t.prototype.addAxis=function(t){var e=t.dim;this._axes[e]=t,this._dimList.push(e)},t}(),Xx=["x","y"];function Yx(t){return"interval"===t.type||"time"===t.type}var qx=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="cartesian2d",e.dimensions=Xx,e}return n(e,t),e.prototype.calcAffineTransform=function(){this._transform=this._invTransform=null;var t=this.getAxis("x").scale,e=this.getAxis("y").scale;if(Yx(t)&&Yx(e)){var n=t.getExtent(),i=e.getExtent(),r=this.dataToPoint([n[0],i[0]]),o=this.dataToPoint([n[1],i[1]]),a=n[1]-n[0],s=i[1]-i[0];if(a&&s){var l=(o[0]-r[0])/a,u=(o[1]-r[1])/s,h=r[0]-n[0]*l,c=r[1]-i[0]*u,p=this._transform=[l,0,0,u,h,c];this._invTransform=ge([],p)}}},e.prototype.getBaseAxis=function(){return this.getAxesByScale("ordinal")[0]||this.getAxesByScale("time")[0]||this.getAxis("x")},e.prototype.containPoint=function(t){var e=this.getAxis("x"),n=this.getAxis("y");return e.contain(e.toLocalCoord(t[0]))&&n.contain(n.toLocalCoord(t[1]))},e.prototype.containData=function(t){return this.getAxis("x").containData(t[0])&&this.getAxis("y").containData(t[1])},e.prototype.containZone=function(t,e){var n=this.dataToPoint(t),i=this.dataToPoint(e),r=this.getArea(),o=new Ce(n[0],n[1],i[0]-n[0],i[1]-n[1]);return r.intersect(o)},e.prototype.dataToPoint=function(t,e,n){n=n||[];var i=t[0],r=t[1];if(this._transform&&null!=i&&isFinite(i)&&null!=r&&isFinite(r))return Et(n,t,this._transform);var o=this.getAxis("x"),a=this.getAxis("y");return n[0]=o.toGlobalCoord(o.dataToCoord(i,e)),n[1]=a.toGlobalCoord(a.dataToCoord(r,e)),n},e.prototype.clampData=function(t,e){var n=this.getAxis("x").scale,i=this.getAxis("y").scale,r=n.getExtent(),o=i.getExtent(),a=n.parse(t[0]),s=i.parse(t[1]);return(e=e||[])[0]=Math.min(Math.max(Math.min(r[0],r[1]),a),Math.max(r[0],r[1])),e[1]=Math.min(Math.max(Math.min(o[0],o[1]),s),Math.max(o[0],o[1])),e},e.prototype.pointToData=function(t,e){var n=[];if(this._invTransform)return Et(n,t,this._invTransform);var i=this.getAxis("x"),r=this.getAxis("y");return n[0]=i.coordToData(i.toLocalCoord(t[0]),e),n[1]=r.coordToData(r.toLocalCoord(t[1]),e),n},e.prototype.getOtherAxis=function(t){return this.getAxis("x"===t.dim?"y":"x")},e.prototype.getArea=function(t){t=t||0;var e=this.getAxis("x").getGlobalExtent(),n=this.getAxis("y").getGlobalExtent(),i=Math.min(e[0],e[1])-t,r=Math.min(n[0],n[1])-t,o=Math.max(e[0],e[1])-i+t,a=Math.max(n[0],n[1])-r+t;return new Ce(i,r,o,a)},e}(Ux),Zx=function(t){function e(e,n,i,r,o){var a=t.call(this,e,n,i)||this;return a.index=0,a.type=r||"value",a.position=o||"bottom",a}return n(e,t),e.prototype.isHorizontal=function(){var t=this.position;return"top"===t||"bottom"===t},e.prototype.getGlobalExtent=function(t){var e=this.getExtent();return e[0]=this.toGlobalCoord(e[0]),e[1]=this.toGlobalCoord(e[1]),t&&e[0]>e[1]&&e.reverse(),e},e.prototype.pointToData=function(t,e){return this.coordToData(this.toLocalCoord(t["x"===this.dim?0:1]),e)},e.prototype.setCategorySortInfo=function(t){if("category"!==this.type)return!1;this.model.option.categorySortInfo=t,this.scale.setSortInfo(t)},e}(Um);function jx(t){return"cartesian2d"===t.get("coordinateSystem")}function Kx(t){var e={xAxisModel:null,yAxisModel:null};return R(e,(function(n,i){var r=i.replace(/Model$/,""),o=t.getReferringComponents(r,no).models[0];e[i]=o})),e}var $x=Math.log;function Qx(t,e,n){var i=Dv.prototype,r=i.getTicks.call(n),o=i.getTicks.call(n,!0),a=r.length-1,s=i.getInterval.call(n),l=am(t,e),u=l.extent,h=l.fixMin,c=l.fixMax;if("log"===t.type){var p=$x(t.base);u=[$x(u[0])/p,$x(u[1])/p]}t.setExtent(u[0],u[1]),t.calcNiceExtent({splitNumber:a,fixMin:h,fixMax:c});var f=i.getExtent.call(t);h&&(u[0]=f[0]),c&&(u[1]=f[1]);var d=i.getInterval.call(t),g=u[0],y=u[1];if(h&&c)d=(y-g)/a;else if(h)for(y=u[0]+d*a;y<u[1]&&isFinite(y)&&isFinite(u[1]);)d=_v(d),y=u[0]+d*a;else if(c)for(g=u[1]-d*a;g>u[0]&&isFinite(g)&&isFinite(u[0]);)d=_v(d),g=u[1]-d*a;else{t.getTicks().length-1>a&&(d=_v(d));var v=d*a;(g=_r((y=Math.ceil(u[1]/d)*d)-v))<0&&u[0]>=0?(g=0,y=_r(v)):y>0&&u[1]<=0&&(y=0,g=-_r(v))}var m=(r[0].value-o[0].value)/s,_=(r[a].value-o[a].value)/s;i.setExtent.call(t,g+d*m,y+d*_),i.setInterval.call(t,d),(m||_)&&i.setNiceExtent.call(t,g+d,y-d)}var Jx=function(){function t(t,e,n){this.type="grid",this._coordsMap={},this._coordsList=[],this._axesMap={},this._axesList=[],this.axisPointerEnabled=!0,this.dimensions=Xx,this._initCartesian(t,e,n),this.model=t}return t.prototype.getRect=function(){return this._rect},t.prototype.update=function(t,e){var n=this._axesMap;function i(t){var e,n=B(t),i=n.length;if(i){for(var r=[],o=i-1;o>=0;o--){var a=t[+n[o]],s=a.model,l=a.scale;vv(l)&&s.get("alignTicks")&&null==s.get("interval")?r.push(a):(sm(l,s),vv(l)&&(e=a))}r.length&&(e||sm((e=r.pop()).scale,e.model),R(r,(function(t){Qx(t.scale,t.model,e.scale)})))}}this._updateScale(t,this.model),i(n.x),i(n.y);var r={};R(n.x,(function(t){ew(n,"y",t,r)})),R(n.y,(function(t){ew(n,"x",t,r)})),this.resize(this.model,e)},t.prototype.resize=function(t,e,n){var i=t.getBoxLayoutParams(),r=!n&&t.get("containLabel"),o=oc(i,{width:e.getWidth(),height:e.getHeight()});this._rect=o;var a=this._axesList;function s(){R(a,(function(t){var e=t.isHorizontal(),n=e?[0,o.width]:[0,o.height],i=t.inverse?1:0;t.setExtent(n[i],n[1-i]),function(t,e){var n=t.getExtent(),i=n[0]+n[1];t.toGlobalCoord="x"===t.dim?function(t){return t+e}:function(t){return i-t+e},t.toLocalCoord="x"===t.dim?function(t){return t-e}:function(t){return i-t+e}}(t,e?o.x:o.y)}))}s(),r&&(R(a,(function(t){if(!t.model.get(["axisLabel","inside"])){var e=function(t){var e=t.model,n=t.scale;if(e.get(["axisLabel","show"])&&!n.isBlank()){var i,r,o=n.getExtent();r=n instanceof Tv?n.count():(i=n.getTicks()).length;var a,s=t.getLabelModel(),l=um(t),u=1;r>40&&(u=Math.ceil(r/40));for(var h=0;h<r;h+=u){var c=l(i?i[h]:{value:o[0]+h},h),p=hm(s.getTextRect(c),s.get("rotate")||0);a?a.union(p):a=p}return a}}(t);if(e){var n=t.isHorizontal()?"height":"width",i=t.model.get(["axisLabel","margin"]);o[n]-=e[n]+i,"top"===t.position?o.y+=e.height+i:"left"===t.position&&(o.x+=e.width+i)}}})),s()),R(this._coordsList,(function(t){t.calcAffineTransform()}))},t.prototype.getAxis=function(t,e){var n=this._axesMap[t];if(null!=n)return n[e||0]},t.prototype.getAxes=function(){return this._axesList.slice()},t.prototype.getCartesian=function(t,e){if(null!=t&&null!=e){var n="x"+t+"y"+e;return this._coordsMap[n]}Y(t)&&(e=t.yAxisIndex,t=t.xAxisIndex);for(var i=0,r=this._coordsList;i<r.length;i++)if(r[i].getAxis("x").index===t||r[i].getAxis("y").index===e)return r[i]},t.prototype.getCartesians=function(){return this._coordsList.slice()},t.prototype.convertToPixel=function(t,e,n){var i=this._findConvertTarget(e);return i.cartesian?i.cartesian.dataToPoint(n):i.axis?i.axis.toGlobalCoord(i.axis.dataToCoord(n)):null},t.prototype.convertFromPixel=function(t,e,n){var i=this._findConvertTarget(e);return i.cartesian?i.cartesian.pointToData(n):i.axis?i.axis.coordToData(i.axis.toLocalCoord(n)):null},t.prototype._findConvertTarget=function(t){var e,n,i=t.seriesModel,r=t.xAxisModel||i&&i.getReferringComponents("xAxis",no).models[0],o=t.yAxisModel||i&&i.getReferringComponents("yAxis",no).models[0],a=t.gridModel,s=this._coordsList;if(i)A(s,e=i.coordinateSystem)<0&&(e=null);else if(r&&o)e=this.getCartesian(r.componentIndex,o.componentIndex);else if(r)n=this.getAxis("x",r.componentIndex);else if(o)n=this.getAxis("y",o.componentIndex);else if(a){a.coordinateSystem===this&&(e=this._coordsList[0])}return{cartesian:e,axis:n}},t.prototype.containPoint=function(t){var e=this._coordsList[0];if(e)return e.containPoint(t)},t.prototype._initCartesian=function(t,e,n){var i=this,r=this,o={left:!1,right:!1,top:!1,bottom:!1},a={x:{},y:{}},s={x:0,y:0};if(e.eachComponent("xAxis",l("x"),this),e.eachComponent("yAxis",l("y"),this),!s.x||!s.y)return this._axesMap={},void(this._axesList=[]);function l(e){return function(n,i){if(tw(n,t)){var l=n.get("position");"x"===e?"top"!==l&&"bottom"!==l&&(l=o.bottom?"top":"bottom"):"left"!==l&&"right"!==l&&(l=o.left?"right":"left"),o[l]=!0;var u=new Zx(e,lm(n),[0,0],n.get("type"),l),h="category"===u.type;u.onBand=h&&n.get("boundaryGap"),u.inverse=n.get("inverse"),n.axis=u,u.model=n,u.grid=r,u.index=i,r._axesList.push(u),a[e][i]=u,s[e]++}}}this._axesMap=a,R(a.x,(function(e,n){R(a.y,(function(r,o){var a="x"+n+"y"+o,s=new qx(a);s.master=i,s.model=t,i._coordsMap[a]=s,i._coordsList.push(s),s.addAxis(e),s.addAxis(r)}))}))},t.prototype._updateScale=function(t,e){function n(t,e){R(function(t,e){var n={};return R(t.mapDimensionsAll(e),(function(e){n[cv(t,e)]=!0})),B(n)}(t,e.dim),(function(n){e.scale.unionExtentFromData(t,n)}))}R(this._axesList,(function(t){if(t.scale.setExtent(1/0,-1/0),"category"===t.type){var e=t.model.get("categorySortInfo");t.scale.setSortInfo(e)}})),t.eachSeries((function(t){if(jx(t)){var i=Kx(t),r=i.xAxisModel,o=i.yAxisModel;if(!tw(r,e)||!tw(o,e))return;var a=this.getCartesian(r.componentIndex,o.componentIndex),s=t.getData(),l=a.getAxis("x"),u=a.getAxis("y");n(s,l),n(s,u)}}),this)},t.prototype.getTooltipAxes=function(t){var e=[],n=[];return R(this.getCartesians(),(function(i){var r=null!=t&&"auto"!==t?i.getAxis(t):i.getBaseAxis(),o=i.getOtherAxis(r);A(e,r)<0&&e.push(r),A(n,o)<0&&n.push(o)})),{baseAxes:e,otherAxes:n}},t.create=function(e,n){var i=[];return e.eachComponent("grid",(function(r,o){var a=new t(r,e,n);a.name="grid_"+o,a.resize(r,n,!0),r.coordinateSystem=a,i.push(a)})),e.eachSeries((function(t){if(jx(t)){var e=Kx(t),n=e.xAxisModel,i=e.yAxisModel,r=n.getCoordSysModel();0;var o=r.coordinateSystem;t.coordinateSystem=o.getCartesian(n.componentIndex,i.componentIndex)}})),i},t.dimensions=Xx,t}();function tw(t,e){return t.getCoordSysModel()===e}function ew(t,e,n,i){n.getAxesOnZeroOf=function(){return r?[r]:[]};var r,o=t[e],a=n.model,s=a.get(["axisLine","onZero"]),l=a.get(["axisLine","onZeroAxisIndex"]);if(s){if(null!=l)nw(o[l])&&(r=o[l]);else for(var u in o)if(o.hasOwnProperty(u)&&nw(o[u])&&!i[h(o[u])]){r=o[u];break}r&&(i[h(r)]=!0)}function h(t){return t.dim+"_"+t.index}}function nw(t){return t&&"category"!==t.type&&"time"!==t.type&&function(t){var e=t.scale.getExtent(),n=e[0],i=e[1];return!(n>0&&i>0||n<0&&i<0)}(t)}var iw=Math.PI,rw=function(){function t(t,e){this.group=new sr,this.opt=e,this.axisModel=t,I(e,{labelOffset:0,nameDirection:1,tickDirection:1,labelDirection:1,silent:!0,handleAutoShown:function(){return!0}});var n=new sr({x:e.position[0],y:e.position[1],rotation:e.rotation});n.updateTransform(),this._transformGroup=n}return t.prototype.hasBuilder=function(t){return!!ow[t]},t.prototype.add=function(t){ow[t](this.opt,this.axisModel,this.group,this._transformGroup)},t.prototype.getGroup=function(){return this.group},t.innerTextLayout=function(t,e,n){var i,r,o=Tr(e-t);return Cr(o)?(r=n>0?"top":"bottom",i="center"):Cr(o-iw)?(r=n>0?"bottom":"top",i="center"):(r="middle",i=o>0&&o<iw?n>0?"right":"left":n>0?"left":"right"),{rotation:o,textAlign:i,textVerticalAlign:r}},t.makeAxisEventDataBase=function(t){var e={componentType:t.mainType,componentIndex:t.componentIndex};return e[t.mainType+"Index"]=t.componentIndex,e},t.isLabelSilent=function(t){var e=t.get("tooltip");return t.get("silent")||!(t.get("triggerEvent")||e&&e.show)},t}(),ow={axisLine:function(t,e,n,i){var r=e.get(["axisLine","show"]);if("auto"===r&&t.handleAutoShown&&(r=t.handleAutoShown("axisLine")),r){var o=e.axis.getExtent(),a=i.transform,s=[o[0],0],l=[o[1],0],u=s[0]>l[0];a&&(Et(s,s,a),Et(l,l,a));var h=D({lineCap:"round"},e.getModel(["axisLine","lineStyle"]).getLineStyle()),c=new Ql({shape:{x1:s[0],y1:s[1],x2:l[0],y2:l[1]},style:h,strokeContainThreshold:t.strokeContainThreshold||5,silent:!0,z2:1});Nu(c.shape,c.style.lineWidth),c.anid="line",n.add(c);var p=e.get(["axisLine","symbol"]);if(null!=p){var f=e.get(["axisLine","symbolSize"]);G(p)&&(p=[p,p]),(G(f)||X(f))&&(f=[f,f]);var d=Vd(e.get(["axisLine","symbolOffset"])||0,f),g=f[0],y=f[1];R([{rotate:t.rotation+Math.PI/2,offset:d[0],r:0},{rotate:t.rotation-Math.PI/2,offset:d[1],r:Math.sqrt((s[0]-l[0])*(s[0]-l[0])+(s[1]-l[1])*(s[1]-l[1]))}],(function(e,i){if("none"!==p[i]&&null!=p[i]){var r=Fd(p[i],-g/2,-y/2,g,y,h.stroke,!0),o=e.r+e.offset,a=u?l:s;r.attr({rotation:e.rotate,x:a[0]+o*Math.cos(t.rotation),y:a[1]-o*Math.sin(t.rotation),silent:!0,z2:11}),n.add(r)}}))}}},axisTickLabel:function(t,e,n,i){var r=function(t,e,n,i){var r=n.axis,o=n.getModel("axisTick"),a=o.get("show");"auto"===a&&i.handleAutoShown&&(a=i.handleAutoShown("axisTick"));if(!a||r.scale.isBlank())return;for(var s=o.getModel("lineStyle"),l=i.tickDirection*o.get("length"),u=uw(r.getTicksCoords(),e.transform,l,I(s.getLineStyle(),{stroke:n.get(["axisLine","lineStyle","color"])}),"ticks"),h=0;h<u.length;h++)t.add(u[h]);return u}(n,i,e,t),o=function(t,e,n,i){var r=n.axis,o=tt(i.axisLabelShow,n.get(["axisLabel","show"]));if(!o||r.scale.isBlank())return;var a=n.getModel("axisLabel"),s=a.get("margin"),l=r.getViewLabels(),u=(tt(i.labelRotate,a.get("rotate"))||0)*iw/180,h=rw.innerTextLayout(i.rotation,u,i.labelDirection),c=n.getCategories&&n.getCategories(!0),p=[],f=rw.isLabelSilent(n),d=n.get("triggerEvent");return R(l,(function(o,u){var g="ordinal"===r.scale.type?r.scale.getRawOrdinalNumber(o.tickValue):o.tickValue,y=o.formattedLabel,v=o.rawLabel,m=a;if(c&&c[g]){var _=c[g];Y(_)&&_.textStyle&&(m=new uh(_.textStyle,a,n.ecModel))}var x=m.getTextColor()||n.get(["axisLine","lineStyle","color"]),w=r.dataToCoord(g),b=m.getShallow("align",!0)||h.textAlign,S=et(m.getShallow("alignMinLabel",!0),b),M=et(m.getShallow("alignMaxLabel",!0),b),T=m.getShallow("verticalAlign",!0)||m.getShallow("baseline",!0)||h.textVerticalAlign,C=et(m.getShallow("verticalAlignMinLabel",!0),T),D=et(m.getShallow("verticalAlignMaxLabel",!0),T),I=new es({x:w,y:i.labelOffset+i.labelDirection*s,rotation:h.rotation,silent:f,z2:10+(o.level||0),style:Uu(m,{text:y,align:0===u?S:u===l.length-1?M:b,verticalAlign:0===u?C:u===l.length-1?D:T,fill:W(x)?x("category"===r.type?v:"value"===r.type?g+"":g,u):x})});if(I.anid="label_"+g,zu({el:I,componentModel:n,itemName:y,formatterParamsExtra:{isTruncated:function(){return I.isTruncated},value:v,tickIndex:u}}),d){var k=rw.makeAxisEventDataBase(n);k.targetType="axisLabel",k.value=v,k.tickIndex=u,"category"===r.type&&(k.dataIndex=g),fs(I).eventData=k}e.add(I),I.updateTransform(),p.push(I),t.add(I),I.decomposeTransform()})),p}(n,i,e,t);(function(t,e,n){if(pm(t.axis))return;var i=t.get(["axisLabel","showMinLabel"]),r=t.get(["axisLabel","showMaxLabel"]);e=e||[],n=n||[];var o=e[0],a=e[1],s=e[e.length-1],l=e[e.length-2],u=n[0],h=n[1],c=n[n.length-1],p=n[n.length-2];!1===i?(aw(o),aw(u)):sw(o,a)&&(i?(aw(a),aw(h)):(aw(o),aw(u)));!1===r?(aw(s),aw(c)):sw(l,s)&&(r?(aw(l),aw(p)):(aw(s),aw(c)))}(e,o,r),function(t,e,n,i){var r=n.axis,o=n.getModel("minorTick");if(!o.get("show")||r.scale.isBlank())return;var a=r.getMinorTicksCoords();if(!a.length)return;for(var s=o.getModel("lineStyle"),l=i*o.get("length"),u=I(s.getLineStyle(),I(n.getModel("axisTick").getLineStyle(),{stroke:n.get(["axisLine","lineStyle","color"])})),h=0;h<a.length;h++)for(var c=uw(a[h],e.transform,l,u,"minorticks_"+h),p=0;p<c.length;p++)t.add(c[p])}(n,i,e,t.tickDirection),e.get(["axisLabel","hideOverlap"]))&&function(t){var e=[];t.sort((function(t,e){return e.priority-t.priority}));var n=new Ce(0,0,0,0);function i(t){if(!t.ignore){var e=t.ensureState("emphasis");null==e.ignore&&(e.ignore=!1)}t.ignore=!0}for(var r=0;r<t.length;r++){var o=t[r],a=o.axisAligned,s=o.localRect,l=o.transform,u=o.label,h=o.labelLine;n.copy(o.rect),n.width-=.1,n.height-=.1,n.x+=.05,n.y+=.05;for(var c=o.obb,p=!1,f=0;f<e.length;f++){var d=e[f];if(n.intersect(d.rect)){if(a&&d.axisAligned){p=!0;break}if(d.obb||(d.obb=new fu(d.localRect,d.transform)),c||(c=new fu(s,l)),c.intersect(d.obb)){p=!0;break}}}p?(i(u),h&&i(h)):(u.attr("ignore",o.defaultAttr.ignore),h&&h.attr("ignore",o.defaultAttr.labelGuideIgnore),e.push(o))}}(function(t){for(var e=[],n=0;n<t.length;n++){var i=t[n];if(!i.defaultAttr.ignore){var r=i.label,o=r.getComputedTransform(),a=r.getBoundingRect(),s=!o||o[1]<1e-5&&o[2]<1e-5,l=r.style.margin||0,u=a.clone();u.applyTransform(o),u.x-=l/2,u.y-=l/2,u.width+=l,u.height+=l;var h=s?new fu(a,o):null;e.push({label:r,labelLine:i.labelLine,rect:u,localRect:a,obb:h,priority:i.priority,defaultAttr:i.defaultAttr,layoutOption:i.computedLayoutOption,axisAligned:s,transform:o})}}return e}(N(o,(function(t){return{label:t,priority:t.z2,defaultAttr:{ignore:t.ignore}}}))))},axisName:function(t,e,n,i){var r=tt(t.axisName,e.get("name"));if(r){var o,a,s=e.get("nameLocation"),l=t.nameDirection,u=e.getModel("nameTextStyle"),h=e.get("nameGap")||0,c=e.axis.getExtent(),p=c[0]>c[1]?-1:1,f=["start"===s?c[0]-p*h:"end"===s?c[1]+p*h:(c[0]+c[1])/2,lw(s)?t.labelOffset+l*h:0],d=e.get("nameRotate");null!=d&&(d=d*iw/180),lw(s)?o=rw.innerTextLayout(t.rotation,null!=d?d:t.rotation,l):(o=function(t,e,n,i){var r,o,a=Tr(n-t),s=i[0]>i[1],l="start"===e&&!s||"start"!==e&&s;Cr(a-iw/2)?(o=l?"bottom":"top",r="center"):Cr(a-1.5*iw)?(o=l?"top":"bottom",r="center"):(o="middle",r=a<1.5*iw&&a>iw/2?l?"left":"right":l?"right":"left");return{rotation:a,textAlign:r,textVerticalAlign:o}}(t.rotation,s,d||0,c),null!=(a=t.axisNameAvailableWidth)&&(a=Math.abs(a/Math.sin(o.rotation)),!isFinite(a)&&(a=null)));var g=u.getFont(),y=e.get("nameTruncate",!0)||{},v=y.ellipsis,m=tt(t.nameTruncateMaxWidth,y.maxWidth,a),_=new es({x:f[0],y:f[1],rotation:o.rotation,silent:rw.isLabelSilent(e),style:Uu(u,{text:r,font:g,overflow:"truncate",width:m,ellipsis:v,fill:u.getTextColor()||e.get(["axisLine","lineStyle","color"]),align:u.get("align")||o.textAlign,verticalAlign:u.get("verticalAlign")||o.textVerticalAlign}),z2:1});if(zu({el:_,componentModel:e,itemName:r}),_.__fullText=r,_.anid="name",e.get("triggerEvent")){var x=rw.makeAxisEventDataBase(e);x.targetType="axisName",x.name=r,fs(_).eventData=x}i.add(_),_.updateTransform(),n.add(_),_.decomposeTransform()}}};function aw(t){t&&(t.ignore=!0)}function sw(t,e){var n=t&&t.getBoundingRect().clone(),i=e&&e.getBoundingRect().clone();if(n&&i){var r=ue([]);return fe(r,r,-t.rotation),n.applyTransform(ce([],r,t.getLocalTransform())),i.applyTransform(ce([],r,e.getLocalTransform())),n.intersect(i)}}function lw(t){return"middle"===t||"center"===t}function uw(t,e,n,i,r){for(var o=[],a=[],s=[],l=0;l<t.length;l++){var u=t[l].coord;a[0]=u,a[1]=0,s[0]=u,s[1]=n,e&&(Et(a,a,e),Et(s,s,e));var h=new Ql({shape:{x1:a[0],y1:a[1],x2:s[0],y2:s[1]},style:i,z2:2,autoBatch:!0,silent:!0});Nu(h.shape,h.style.lineWidth),h.anid=r+"_"+t[l].tickValue,o.push(h)}return o}function hw(t){var e=cw(t);if(e){var n=e.axisPointerModel,i=e.axis.scale,r=n.option,o=n.get("status"),a=n.get("value");null!=a&&(a=i.parse(a));var s=function(t){return!!t.get(["handle","show"])}(n);null==o&&(r.status=s?"show":"hide");var l=i.getExtent().slice();l[0]>l[1]&&l.reverse(),(null==a||a>l[1])&&(a=l[1]),a<l[0]&&(a=l[0]),r.value=a,s&&(r.status=e.axis.scale.isBlank()?"hide":"show")}}function cw(t){var e,n=(t.ecModel.getComponent("axisPointer")||{}).coordSysAxesInfo;return n&&n.axesInfo[(e=t,e.type+"||"+e.id)]}var pw={},fw=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return n(e,t),e.prototype.render=function(e,n,i,r){this.axisPointerClass&&hw(e),t.prototype.render.apply(this,arguments),this._doUpdateAxisPointerClass(e,i,!0)},e.prototype.updateAxisPointer=function(t,e,n,i){this._doUpdateAxisPointerClass(t,n,!1)},e.prototype.remove=function(t,e){var n=this._axisPointer;n&&n.remove(e)},e.prototype.dispose=function(e,n){this._disposeAxisPointer(n),t.prototype.dispose.apply(this,arguments)},e.prototype._doUpdateAxisPointerClass=function(t,n,i){var r=e.getAxisPointerClass(this.axisPointerClass);if(r){var o=function(t){var e=cw(t);return e&&e.axisPointerModel}(t);o?(this._axisPointer||(this._axisPointer=new r)).render(t,o,n,i):this._disposeAxisPointer(n)}},e.prototype._disposeAxisPointer=function(t){this._axisPointer&&this._axisPointer.dispose(t),this._axisPointer=null},e.registerAxisPointerClass=function(t,e){pw[t]=e},e.getAxisPointerClass=function(t){return t&&pw[t]},e.type="axis",e}(Pf),dw=Qr();var gw=["axisLine","axisTickLabel","axisName"],yw=["splitArea","splitLine","minorSplitLine"],vw=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n.axisPointerClass="CartesianAxisPointer",n}return n(e,t),e.prototype.render=function(e,n,i,r){this.group.removeAll();var o=this._axisGroup;if(this._axisGroup=new sr,this.group.add(this._axisGroup),e.get("show")){var a=e.getCoordSysModel(),s=function(t,e,n){n=n||{};var i=t.coordinateSystem,r=e.axis,o={},a=r.getAxesOnZeroOf()[0],s=r.position,l=a?"onZero":s,u=r.dim,h=i.getRect(),c=[h.x,h.x+h.width,h.y,h.y+h.height],p={left:0,right:1,top:0,bottom:1,onZero:2},f=e.get("offset")||0,d="x"===u?[c[2]-f,c[3]+f]:[c[0]-f,c[1]+f];if(a){var g=a.toGlobalCoord(a.dataToCoord(0));d[p.onZero]=Math.max(Math.min(g,d[1]),d[0])}o.position=["y"===u?d[p[l]]:c[0],"x"===u?d[p[l]]:c[3]],o.rotation=Math.PI/2*("x"===u?0:1),o.labelDirection=o.tickDirection=o.nameDirection={top:-1,bottom:1,left:-1,right:1}[s],o.labelOffset=a?d[p[s]]-d[p.onZero]:0,e.get(["axisTick","inside"])&&(o.tickDirection=-o.tickDirection),tt(n.labelInside,e.get(["axisLabel","inside"]))&&(o.labelDirection=-o.labelDirection);var y=e.get(["axisLabel","rotate"]);return o.labelRotate="top"===l?-y:y,o.z2=1,o}(a,e),l=new rw(e,D({handleAutoShown:function(t){for(var n=a.coordinateSystem.getCartesians(),i=0;i<n.length;i++)if(vv(n[i].getOtherAxis(e.axis).scale))return!0;return!1}},s));R(gw,l.add,l),this._axisGroup.add(l.getGroup()),R(yw,(function(t){e.get([t,"show"])&&mw[t](this,this._axisGroup,e,a)}),this),r&&"changeAxisOrder"===r.type&&r.isInitSort||function(t,e,n){if(t&&e){var i,r=(i={},t.traverse((function(t){Eu(t)&&t.anid&&(i[t.anid]=t)})),i);e.traverse((function(t){if(Eu(t)&&t.anid){var e=r[t.anid];if(e){var i=o(t);t.attr(o(e)),mu(t,i,n,fs(t).dataIndex)}}}))}function o(t){var e={x:t.x,y:t.y,rotation:t.rotation};return function(t){return null!=t.shape}(t)&&(e.shape=D({},t.shape)),e}}(o,this._axisGroup,e),t.prototype.render.call(this,e,n,i,r)}},e.prototype.remove=function(){dw(this).splitAreaColors=null},e.type="cartesianAxis",e}(fw),mw={splitLine:function(t,e,n,i){var r=n.axis;if(!r.scale.isBlank()){var o=n.getModel("splitLine"),a=o.getModel("lineStyle"),s=a.get("color"),l=!1!==o.get("showMinLine"),u=!1!==o.get("showMaxLine");s=H(s)?s:[s];for(var h=i.coordinateSystem.getRect(),c=r.isHorizontal(),p=0,f=r.getTicksCoords({tickModel:o}),d=[],g=[],y=a.getLineStyle(),v=0;v<f.length;v++){var m=r.toGlobalCoord(f[v].coord);if((0!==v||l)&&(v!==f.length-1||u)){var _=f[v].tickValue;c?(d[0]=m,d[1]=h.y,g[0]=m,g[1]=h.y+h.height):(d[0]=h.x,d[1]=m,g[0]=h.x+h.width,g[1]=m);var x=p++%s.length,w=new Ql({anid:null!=_?"line_"+_:null,autoBatch:!0,shape:{x1:d[0],y1:d[1],x2:g[0],y2:g[1]},style:I({stroke:s[x]},y),silent:!0});Nu(w.shape,y.lineWidth),e.add(w)}}}},minorSplitLine:function(t,e,n,i){var r=n.axis,o=n.getModel("minorSplitLine").getModel("lineStyle"),a=i.coordinateSystem.getRect(),s=r.isHorizontal(),l=r.getMinorTicksCoords();if(l.length)for(var u=[],h=[],c=o.getLineStyle(),p=0;p<l.length;p++)for(var f=0;f<l[p].length;f++){var d=r.toGlobalCoord(l[p][f].coord);s?(u[0]=d,u[1]=a.y,h[0]=d,h[1]=a.y+a.height):(u[0]=a.x,u[1]=d,h[0]=a.x+a.width,h[1]=d);var g=new Ql({anid:"minor_line_"+l[p][f].tickValue,autoBatch:!0,shape:{x1:u[0],y1:u[1],x2:h[0],y2:h[1]},style:c,silent:!0});Nu(g.shape,c.lineWidth),e.add(g)}},splitArea:function(t,e,n,i){!function(t,e,n,i){var r=n.axis;if(!r.scale.isBlank()){var o=n.getModel("splitArea"),a=o.getModel("areaStyle"),s=a.get("color"),l=i.coordinateSystem.getRect(),u=r.getTicksCoords({tickModel:o,clamp:!0});if(u.length){var h=s.length,c=dw(t).splitAreaColors,p=ft(),f=0;if(c)for(var d=0;d<u.length;d++){var g=c.get(u[d].tickValue);if(null!=g){f=(g+(h-1)*d)%h;break}}var y=r.toGlobalCoord(u[0].coord),v=a.getAreaStyle();for(s=H(s)?s:[s],d=1;d<u.length;d++){var m=r.toGlobalCoord(u[d].coord),_=void 0,x=void 0,w=void 0,b=void 0;r.isHorizontal()?(_=y,x=l.y,w=m-_,b=l.height,y=_+w):(_=l.x,x=y,w=l.width,y=x+(b=m-x));var S=u[d-1].tickValue;null!=S&&p.set(S,f),e.add(new Qa({anid:null!=S?"area_"+S:null,shape:{x:_,y:x,width:w,height:b},style:I({fill:s[f]},v),autoBatch:!0,silent:!0})),f=(f+1)%h}dw(t).splitAreaColors=p}}}(t,e,n,i)}},_w=function(t){function e(){var n=null!==t&&t.apply(this,arguments)||this;return n.type=e.type,n}return n(e,t),e.type="xAxis",e}(vw),xw=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type=_w.type,e}return n(e,t),e.type="yAxis",e}(vw),ww=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="grid",e}return n(e,t),e.prototype.render=function(t,e){this.group.removeAll(),t.get("show")&&this.group.add(new Qa({shape:t.coordinateSystem.getRect(),style:I({fill:t.get("backgroundColor")},t.getItemStyle()),silent:!0,z2:-1}))},e.type="grid",e}(Pf),bw={offset:0};var Sw={label:{enabled:!0},decal:{show:!1}},Mw=Qr(),Tw={};function Cw(t,e){var n=t.getModel("aria");if(n.get("enabled")){var i=T(Sw);C(i.label,t.getLocaleModel().get("aria"),!1),C(n.option,i,!1),function(){if(n.getModel("decal").get("show")){var e=ft();t.eachSeries((function(t){if(!t.isColorBySeries()){var n=e.get(t.type);n||(n={},e.set(t.type,n)),Mw(t).scope=n}})),t.eachRawSeries((function(e){if(!t.isSeriesFiltered(e))if(W(e.enableAriaDecal))e.enableAriaDecal();else{var n=e.getData();if(e.isColorBySeries()){var i=Fc(e.ecModel,e.name,Tw,t.getSeriesCount()),r=n.getVisual("decal");n.setVisual("decal",u(r,i))}else{var o=e.getRawData(),a={},s=Mw(e).scope;n.each((function(t){var e=n.getRawIndex(t);a[e]=t}));var l=o.count();o.each((function(t){var i=a[t],r=o.getName(t)||t+"",h=Fc(e.ecModel,r,s,l),c=n.getItemVisual(i,"decal");n.setItemVisual(i,"decal",u(c,h))}))}}function u(t,e){var n=t?D(D({},e),t):e;return n.dirty=!0,n}}))}}(),function(){var i=e.getZr().dom;if(!i)return;var o=t.getLocaleModel().get("aria"),a=n.getModel("label");if(a.option=I(a.option,o),!a.get("enabled"))return;if(i.setAttribute("role","img"),a.get("description"))return void i.setAttribute("aria-label",a.get("description"));var s,l=t.getSeriesCount(),u=a.get(["data","maxCount"])||10,h=a.get(["series","maxCount"])||10,c=Math.min(l,h);if(l<1)return;var p=function(){var e=t.get("title");e&&e.length&&(e=e[0]);return e&&e.text}();s=p?r(a.get(["general","withTitle"]),{title:p}):a.get(["general","withoutTitle"]);var f=[];s+=r(l>1?a.get(["series","multiple","prefix"]):a.get(["series","single","prefix"]),{seriesCount:l}),t.eachSeries((function(e,n){if(n<c){var i=void 0,o=e.get("name")?"withName":"withoutName";i=r(i=l>1?a.get(["series","multiple",o]):a.get(["series","single",o]),{seriesId:e.seriesIndex,seriesName:e.get("name"),seriesType:(x=e.subType,w=t.getLocaleModel().get(["series","typeNames"]),w[x]||w.chart)});var s=e.getData();if(s.count()>u)i+=r(a.get(["data","partialData"]),{displayCnt:u});else i+=a.get(["data","allData"]);for(var h=a.get(["data","separator","middle"]),p=a.get(["data","separator","end"]),d=a.get(["data","excludeDimensionId"]),g=[],y=0;y<s.count();y++)if(y<u){var v=s.getName(y),m=d?z(s.getValues(y),(function(t,e){return-1===A(d,e)})):s.getValues(y),_=a.get(["data",v?"withName":"withoutName"]);g.push(r(_,{name:v,value:m.join(h)}))}i+=g.join(h)+p,f.push(i)}var x,w}));var d=a.getModel(["series","multiple","separator"]),g=d.get("middle"),y=d.get("end");s+=f.join(g)+y,i.setAttribute("aria-label",s)}()}function r(t,e){if(!G(t))return t;var n=t;return R(e,(function(t,e){n=n.replace(new RegExp("\\{\\s*"+e+"\\s*\\}","g"),t)})),n}}function Dw(t){if(t&&t.aria){var e=t.aria;null!=e.show&&(e.enabled=e.show),e.label=e.label||{},R(["description","general","series","data"],(function(t){null!=e[t]&&(e.label[t]=e[t])}))}}var Iw=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="dataset",e}return n(e,t),e.prototype.init=function(e,n,i){t.prototype.init.call(this,e,n,i),this._sourceManager=new yf(this),vf(this)},e.prototype.mergeOption=function(e,n){t.prototype.mergeOption.call(this,e,n),vf(this)},e.prototype.optionUpdated=function(){this._sourceManager.dirty()},e.prototype.getSourceManager=function(){return this._sourceManager},e.type="dataset",e.defaultOption={seriesLayoutBy:wc},e}(hc),kw=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.type="dataset",e}return n(e,t),e.type="dataset",e}(Pf);Ly([function(t){t.registerPainter("canvas",h_)}]),Ly([function(t){t.registerChartView(G_),t.registerSeriesModel(c_),t.registerLayout(function(t,e){return{seriesType:t,plan:Of(),reset:function(t){var n=t.getData(),i=t.coordinateSystem,r=t.pipelineContext,o=e||r.large;if(i){var a=N(i.dimensions,(function(t){return n.mapDimension(t)})).slice(0,2),s=a.length,l=n.getCalculationInfo("stackResultDimension");hv(n,a[0])&&(a[0]=l),hv(n,a[1])&&(a[1]=l);var u=n.getStore(),h=n.getDimensionIndex(a[0]),c=n.getDimensionIndex(a[1]);return s&&{progress:function(t,e){for(var n=t.end-t.start,r=o&&Av(n*s),a=[],l=[],p=t.start,f=0;p<t.end;p++){var d=void 0;if(1===s){var g=u.get(h,p);d=i.dataToPoint(g,null,l)}else a[0]=u.get(h,p),a[1]=u.get(c,p),d=i.dataToPoint(a,null,l);o?(r[f++]=d[0],r[f++]=d[1]):e.setItemLayout(p,d.slice())}o&&e.setLayout("points",r)}}}}}}("line",!0)),t.registerVisual({seriesType:"line",reset:function(t){var e=t.getData(),n=t.getModel("lineStyle").getLineStyle();n&&!n.stroke&&(n.stroke=e.getVisual("style").fill),e.setVisual("legendLineStyle",n)}}),t.registerProcessor(t.PRIORITY.PROCESSOR.STATISTIC,Y_("line"))},function(t){t.registerChartView(nx),t.registerSeriesModel(Z_),t.registerLayout(t.PRIORITY.VISUAL.LAYOUT,V(Nv,"bar")),t.registerLayout(t.PRIORITY.VISUAL.PROGRESSIVE_LAYOUT,function(t){return{seriesType:t,plan:Of(),reset:function(t){if(Ev(t)){var e=t.getData(),n=t.coordinateSystem,i=n.getBaseAxis(),r=n.getOtherAxis(i),o=e.getDimensionIndex(e.mapDimension(r.dim)),a=e.getDimensionIndex(e.mapDimension(i.dim)),s=t.get("showBackground",!0),l=e.mapDimension(r.dim),u=e.getCalculationInfo("stackResultDimension"),h=hv(e,l)&&!!e.getCalculationInfo("stackedOnSeries"),c=r.isHorizontal(),p=function(t,e){var n=e.model.get("startValue");return n||(n=0),e.toGlobalCoord(e.dataToCoord("log"===e.type?n>0?n:1:n))}(0,r),f=zv(t),d=t.get("barMinHeight")||0,g=u&&e.getDimensionIndex(u),y=e.getLayout("size"),v=e.getLayout("offset");return{progress:function(t,e){for(var i,r=t.count,l=f&&Av(3*r),u=f&&s&&Av(3*r),m=f&&Av(r),_=n.master.getRect(),x=c?_.width:_.height,w=e.getStore(),b=0;null!=(i=t.next());){var S=w.get(h?g:o,i),M=w.get(a,i),T=p,C=void 0;h&&(C=+S-w.get(o,i));var D=void 0,I=void 0,k=void 0,A=void 0;if(c){var L=n.dataToPoint([S,M]);h&&(T=n.dataToPoint([C,M])[0]),D=T,I=L[1]+v,k=L[0]-T,A=y,Math.abs(k)<d&&(k=(k<0?-1:1)*d)}else L=n.dataToPoint([M,S]),h&&(T=n.dataToPoint([M,C])[1]),D=L[0]+v,I=T,k=y,A=L[1]-T,Math.abs(A)<d&&(A=(A<=0?-1:1)*d);f?(l[b]=D,l[b+1]=I,l[b+2]=c?k:A,u&&(u[b]=c?_.x:D,u[b+1]=c?I:_.y,u[b+2]=x),m[i]=i):e.setItemLayout(i,{x:D,y:I,width:k,height:A}),b+=3}f&&e.setLayout({largePoints:l,largeDataIndices:m,largeBackgroundPoints:u,valueAxisHorizontal:c})}}}}}}("bar")),t.registerProcessor(t.PRIORITY.PROCESSOR.STATISTIC,Y_("bar")),t.registerAction({type:"changeAxisOrder",event:"changeAxisOrder",update:"update"},(function(t,e){var n=t.componentType||"series";e.eachComponent({mainType:n,query:t},(function(e){t.sortInfo&&e.axis.setCategorySortInfo(t.sortInfo)}))}))},function(t){t.registerChartView(Lx),t.registerSeriesModel(Rx),function(t,e){function n(e,n){var i=[];return e.eachComponent({mainType:"series",subType:t,query:n},(function(t){i.push(t.seriesIndex)})),i}R([[t+"ToggleSelect","toggleSelect"],[t+"Select","select"],[t+"UnSelect","unselect"]],(function(t){e(t[0],(function(e,i,r){e=D({},e),r.dispatchAction(D(e,{type:t[1],seriesIndex:n(i,e)}))}))}))}("pie",t.registerAction),t.registerLayout(V(Sx,"pie")),t.registerProcessor(function(t){return{seriesType:t,reset:function(t,e){var n=e.findComponents({mainType:"legend"});if(n&&n.length){var i=t.getData();i.filterSelf((function(t){for(var e=i.getName(t),r=0;r<n.length;r++)if(!n[r].isSelected(e))return!1;return!0}))}}}}("pie")),t.registerProcessor(function(t){return{seriesType:t,reset:function(t,e){var n=t.getData();n.filterSelf((function(t){var e=n.mapDimension("value"),i=n.get(e,t);return!(X(i)&&!isNaN(i)&&i<0)}))}}}("pie"))}]),Ly([function(t){t.registerComponentView(ww),t.registerComponentModel(Nx),t.registerCoordinateSystem("cartesian2d",Jx),Wx(t,"x",Ex,bw),Wx(t,"y",Ex,bw),t.registerComponentView(_w),t.registerComponentView(xw),t.registerPreprocessor((function(t){t.xAxis&&t.yAxis&&!t.grid&&(t.grid={})}))},function(t){t.registerPreprocessor(Dw),t.registerVisual(t.PRIORITY.VISUAL.ARIA,Cw)},function(t){t.registerComponentModel(Iw),t.registerComponentView(kw)}]),t.Axis=Um,t.ChartView=Ef,t.ComponentModel=hc,t.ComponentView=Pf,t.List=iv,t.Model=uh,t.PRIORITY=xg,t.SeriesModel=Mf,t.color=qn,t.connect=function(t){if(H(t)){var e=t;t=null,R(e,(function(e){null!=e.group&&(t=e.group)})),t=t||"g_"+uy++,R(e,(function(e){e.group=t}))}return sy[t]=!0,t},t.dataTool={},t.dependencies={zrender:"5.6.1"},t.disConnect=py,t.disconnect=cy,t.dispose=function(t){G(t)?t=ay[t]:t instanceof jg||(t=fy(t)),t instanceof jg&&!t.isDisposed()&&t.dispose()},t.env=r,t.extendChartView=function(t){var e=Ef.extend(t);return Ef.registerClass(e),e},t.extendComponentModel=function(t){var e=hc.extend(t);return hc.registerClass(e),e},t.extendComponentView=function(t){var e=Pf.extend(t);return Pf.registerClass(e),e},t.extendSeriesModel=function(t){var e=Mf.extend(t);return Mf.registerClass(e),e},t.format=Lm,t.getCoordinateSystemDimensions=function(t){var e=jc.get(t);if(e)return e.getDimensionsInfo?e.getDimensionsInfo():e.dimensions.slice()},t.getInstanceByDom=fy,t.getInstanceById=function(t){return ay[t]},t.getMap=function(t){var e=vg("getMap");return e&&e(t)},t.graphic=Am,t.helper=gm,t.init=function(t,e,n){var i=!(n&&n.ssr);if(i){0;var r=fy(t);if(r)return r;0}var o=new jg(t,e,n);return o.id="ec_"+ly++,ay[o.id]=o,i&&ro(t,hy,o.id),Ug(o),gg.trigger("afterinit",o),o},t.innerDrawElementOnCanvas=ag,t.matrix=ye,t.number=Im,t.parseGeoJSON=Dm,t.parseGeoJson=Dm,t.registerAction=xy,t.registerCoordinateSystem=wy,t.registerLayout=by,t.registerLoading=Cy,t.registerLocale=mh,t.registerMap=Dy,t.registerPostInit=vy,t.registerPostUpdate=my,t.registerPreprocessor=gy,t.registerProcessor=yy,t.registerTheme=dy,t.registerTransform=Iy,t.registerUpdateLifecycle=_y,t.registerVisual=Sy,t.setCanvasCreator=function(t){h({createCanvas:t})},t.setPlatformAPI=h,t.throttle=Wf,t.time=km,t.use=Ly,t.util=Pm,t.vector=Ft,t.version="5.6.0",t.zrUtil=xt,t.zrender=gr,Object.defineProperty(t,"__esModule",{value:!0})}));
