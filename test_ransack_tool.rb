puts '=== 测试RansackTool ==='
begin
  agent = Bot::Agent.first
  intent = agent.intents.find_by(name: '人员查询')
  
  puts "Intent: #{intent.name}"
  
  ransack_tool = Bot::Tools::RansackTool.new(intent: intent)
  
  # 设置Bot::Current.conversation
  conversation = agent.conversations.first || agent.conversations.create!(user: User.first)
  Bot::Current.conversation = conversation
  
  puts "Conversation: #{conversation.id}"
  
  # 测试通过send方法调用
  puts "\n=== 通过send方法调用query_records ==="
  result = ransack_tool.send(:query_records, query: '有多少人？')
  
  puts "结果类型: #{result.class}"
  puts "结果keys: #{result.keys}"
  puts "data: #{result[:data]}"
  puts "artifact: #{result[:artifact]}"
  
  # 检查artifact
  if result[:artifact]
    artifact = result[:artifact]
    puts "\n🎉 RansackTool Artifact创建成功!"
    puts "  ID: #{artifact.id}"
    puts "  Type: #{artifact.type}"
    puts "  Class: #{artifact.class}"
  else
    puts "\n❌ RansackTool没有创建Artifact"
  end
  
rescue StandardError => e
  puts "❌ 失败: #{e.message}"
  puts e.backtrace.first(10)
ensure
  Bot::Current.conversation = nil
end
