GIT
  remote: ******************:open-source/acts_as_pasting.git
  revision: f005710576bb2dc49dcebdb785171a263dd69aec
  specs:
    acts_as_pasting (0.1.1)

GIT
  remote: ******************:open-source/rspec-rails-swagger.git
  revision: c1b2bb18d1fc3358c0640355750230b1bbc20476
  specs:
    rspec-rails-swagger (0.1.4)
      rails (>= 3.1)
      rspec-rails

GIT
  remote: ******************:open-source/simple_controller.git
  revision: 3663f731372c1d48a6a80e600dce7126f43cdb6c
  specs:
    simple_controller (1.1.0)
      inherited_resources
      pundit
      ransack
      responders
      ta_ransack_mongo
      will_paginate

GIT
  remote: ******************:open-source/tallty_import_export.git
  revision: 8d65ffbd39dc583b4b2dc95c6d4dd6e1b9f9ba18
  specs:
    tallty_import_export (1.1.6)
      activesupport
      attr_json
      caxlsx
      matrix
      redis
      redis-objects
      roo
      roo-xls
      tallty_form
      zip-zip

GIT
  remote: ******************:ta-rails/rails_action_core.git
  revision: 6c57d8576a923298bda3ad56cf81cc332390d9fb
  specs:
    rails_action_core (0.1.0)
      jbuilder
      rack-cors
      rails

GIT
  remote: ******************:ta-rails/rails_assessment.git
  revision: 736b6f47008ce721af192fa2282ed35aa0baaea6
  specs:
    rails_assessment (0.1.0)
      jbuilder
      rack-cors
      rails

GIT
  remote: ******************:ta-rails/rails_audit.git
  revision: a87cb1d0b0864d3e5e484ebeab0b9a379da0cf60
  specs:
    rails_audit (0.1.0)
      device_detector
      jbuilder
      rack-cors
      rails

GIT
  remote: ******************:ta-rails/rails_bpm.git
  revision: 3c5ac083de0b1c3082924c3d3a550e5bef299dbd
  specs:
    rails_bpm (0.1.0)
      jbuilder
      rack-cors
      rails
      tallty_import_export

GIT
  remote: ******************:ta-rails/rails_chat.git
  revision: e43f9663934ceccf8795ebe37feab8e22cc76f0e
  specs:
    rails_chat (0.1.0)
      aliyunsdkcore
      faye-websocket
      jbuilder
      rack-cors
      rails (~> 7.1.0)

GIT
  remote: ******************:ta-rails/rails_com.git
  revision: d81f61ed56ac7f5185635dd9d159012baeb5c67d
  specs:
    rails_com (0.1.0)
      aasm
      active_record_extended
      acts_as_list
      ancestry
      attr_json
      aws-sdk-s3
      closure_tree
      deep_cloneable
      execjs
      groupdate
      jbuilder
      matrix
      mime-types
      nilify_blanks
      ohm
      paper_trail
      paranoia
      pundit
      rack-cors
      rails
      ransack
      ransack-enum
      redis
      redis-namespace
      redis-objects
      responders
      rolify
      ruby-pinyin
      strip_attributes
      ta_by_star
      ta_deep_pluck
      ta_default_value_for
      ta_has_event
      typhoeus
      uppy-s3_multipart
      uuidtools
      zip_tricks

GIT
  remote: ******************:ta-rails/rails_data.git
  revision: 27b5fbe0299f518c763542cbfb19c4b92f376384
  specs:
    rails_data (0.1.0)
      jbuilder
      rack-cors
      rails
      tallty_import_export

GIT
  remote: ******************:ta-rails/rails_dingtalk.git
  revision: ad0c7681778510c8e03ca79337efd82cfbacb5ed
  specs:
    rails_dingtalk (0.1.0)
      http
      jbuilder
      rack-cors
      rails (~> 7.1.0)

GIT
  remote: ******************:ta-rails/rails_favor.git
  revision: 464dd93b9342742723bc35a3435a60727dd1c28e
  specs:
    rails_favor (0.1.0)
      jbuilder
      rack-cors
      rails

GIT
  remote: ******************:ta-rails/rails_grant.git
  revision: d587b92e5900269d1d186fae175e5ae53fd532a0
  specs:
    rails_grant (0.1.0)
      jbuilder
      rack-cors
      rails

GIT
  remote: ******************:ta-rails/rails_notify.git
  revision: 10d2bb11bf978c8a4813e2bb6e6e4bfe5797201d
  specs:
    rails_notify (0.1.0)
      acts_as_commentable_with_threading
      jbuilder
      rack-cors
      rails
      rest-client

GIT
  remote: ******************:ta-rails/rails_opm.git
  revision: 1ea806238cb23610781fc6ebaf74a7c216eff104
  specs:
    rails_opm (0.1.0)
      jbuilder
      rack-cors
      rails (>= 7.1.3.2)

GIT
  remote: ******************:ta-rails/rails_permit.git
  revision: 4daec7dd20d866e4d8197d101350bba9547bc025
  specs:
    rails_permit (0.1.0)
      jbuilder
      rack-cors
      rails

GIT
  remote: ******************:ta-rails/rails_reg.git
  revision: 82da2191cec5fccd17b18cfe181a91d7ab1251c9
  specs:
    rails_reg (0.1.0)
      jbuilder
      rack-cors
      rails

GIT
  remote: ******************:ta-rails/rails_region.git
  revision: 1d5151147bf1a9b2d08f3e8e5d51e026b86c409f
  specs:
    rails_region (0.1.0)
      down
      jbuilder
      rack-cors
      rails
      ruby-pinyin

GIT
  remote: ******************:ta-rails/rails_res.git
  revision: 78fe03240127d582826ce87c8ff604469109891b
  specs:
    rails_res (0.1.0)
      jbuilder
      rack-cors
      rails (~> 7.1.0)

GIT
  remote: ******************:ta-rails/rails_rss.git
  revision: 18704859f9d2ebf5acd8ca7e295e5f49c6b3d235
  specs:
    rails_rss (0.1.0)
      faraday
      jbuilder
      rack-cors
      rails (>= 7.1.3.3)

GIT
  remote: ******************:ta-rails/rails_schedule.git
  revision: 5a8809b265b4b2f517be4b1df1cf393bdecdf3f2
  specs:
    rails_schedule (0.1.0)
      jbuilder
      rack-cors
      rails (~> 7.1.0)
      ruby_lunardate

GIT
  remote: ******************:ta-rails/rails_serve.git
  revision: 35407053e751dc7f8bfbfaf1bbe16fe73cbbb3f0
  specs:
    rails_serve (0.1.0)
      jbuilder
      rack-cors
      rails (~> 7.1.0)

GIT
  remote: ******************:ta-rails/rails_sms_auth.git
  revision: ac5c298339d3a667bfa581c5277e99bfb3b93417
  specs:
    rails_sms_auth (0.1.0)
      jbuilder
      rack-cors
      rails

GIT
  remote: ******************:ta-rails/rails_soa_auth.git
  revision: 8c74bf8559a9675431f61f012241137a71d874a8
  specs:
    rails_soa_auth (1.0.0)
      jbuilder
      rack-cors
      rails

GIT
  remote: ******************:ta-rails/rails_spider.git
  revision: c49a9504cd92bae7955412a53bc33ccd8f18216c
  specs:
    rails_spider (0.1.0)
      jbuilder
      rack-cors
      rails (>= 7.1.5)

GIT
  remote: ******************:ta-rails/rails_tofu.git
  revision: 4e430b69972d054e1b344fe4ce68ae75f6c16cf5
  specs:
    rails_tofu (0.1.0)
      jbuilder
      rack-cors
      rails

PATH
  remote: ../rails_bot
  specs:
    rails_bot (0.1.0)
      faraday
      jbuilder
      langchainrb
      rack-cors
      rails (>= 7.1.5)
      ruby-openai
      streamio-ffmpeg

PATH
  remote: ../rails_iest
  specs:
    rails_iest (0.1.0)
      jbuilder
      rack-cors
      rails (~> 7.1.0)

GEM
  remote: https://gems.ruby-china.com/
  specs:
    Ascii85 (2.0.1)
    aasm (5.5.1)
      concurrent-ruby (~> 1.0)
    actioncable (*******)
      actionpack (= *******)
      activesupport (= *******)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
      zeitwerk (~> 2.6)
    actionmailbox (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      mail (>= 2.7.1)
      net-imap
      net-pop
      net-smtp
    actionmailer (*******)
      actionpack (= *******)
      actionview (= *******)
      activejob (= *******)
      activesupport (= *******)
      mail (~> 2.5, >= 2.5.4)
      net-imap
      net-pop
      net-smtp
      rails-dom-testing (~> 2.2)
    actionpack (*******)
      actionview (= *******)
      activesupport (= *******)
      nokogiri (>= 1.8.5)
      racc
      rack (>= 2.2.4)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    actiontext (*******)
      actionpack (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (*******)
      activesupport (= *******)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    active_record_extended (3.3.0)
      activerecord (>= 5.2, < 8.0.0)
      pg (< 3.0)
    activejob (*******)
      activesupport (= *******)
      globalid (>= 0.3.6)
    activemodel (*******)
      activesupport (= *******)
    activerecord (*******)
      activemodel (= *******)
      activesupport (= *******)
      timeout (>= 0.4.0)
    activestorage (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activesupport (= *******)
      marcel (~> 1.0)
    activesupport (*******)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.0.2)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      mutex_m
      securerandom (>= 0.3)
      tzinfo (~> 2.0)
    acts_as_commentable_with_threading (2.0.1)
      activerecord (>= 4.0)
      activesupport (>= 4.0)
      awesome_nested_set (>= 3.0)
    acts_as_list (1.2.4)
      activerecord (>= 6.1)
      activesupport (>= 6.1)
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    afm (0.2.2)
    aliyunsdkcore (0.0.17)
      activesupport (>= 3.0.0)
      faraday (>= 0.15.4)
    ancestry (4.3.3)
      activerecord (>= 5.2.6)
    annotate (3.2.0)
      activerecord (>= 3.2, < 8.0)
      rake (>= 10.4, < 14.0)
    ast (2.4.3)
    attr_json (2.5.0)
      activerecord (>= 6.0.0, < 8.1)
    awesome_nested_set (3.8.0)
      activerecord (>= 4.0.0, < 8.1)
    aws-eventstream (1.4.0)
    aws-partitions (1.1136.0)
    aws-sdk-core (3.227.0)
      aws-eventstream (~> 1, >= 1.3.0)
      aws-partitions (~> 1, >= 1.992.0)
      aws-sigv4 (~> 1.9)
      base64
      jmespath (~> 1, >= 1.6.1)
      logger
    aws-sdk-kms (1.108.0)
      aws-sdk-core (~> 3, >= 3.227.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-s3 (1.194.0)
      aws-sdk-core (~> 3, >= 3.227.0)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.5)
    aws-sigv4 (1.12.1)
      aws-eventstream (~> 1, >= 1.0.2)
    baran (0.1.12)
    base64 (0.3.0)
    benchmark (0.4.1)
    bigdecimal (3.2.2)
    bootsnap (1.18.6)
      msgpack (~> 1.2)
    brakeman (7.1.0)
      racc
    builder (3.3.0)
    byebug (12.0.0)
    caxlsx (4.2.0)
      htmlentities (~> 4.3, >= 4.3.4)
      marcel (~> 1.0)
      nokogiri (~> 1.10, >= 1.10.4)
      rubyzip (>= 1.3.0, < 3)
    closure_tree (7.4.0)
      activerecord (>= 4.2.10)
      with_advisory_lock (>= 4.0.0)
    coderay (1.1.3)
    concurrent-ruby (1.3.5)
    connection_pool (2.5.3)
    content_disposition (1.0.0)
    crass (1.0.6)
    csv (3.3.5)
    date (3.4.1)
    debug (1.11.0)
      irb (~> 1.10)
      reline (>= 0.3.8)
    deep_cloneable (3.2.1)
      activerecord (>= 3.1.0, < 9)
    device_detector (1.1.3)
    diff-lcs (1.6.2)
    docile (1.4.1)
    domain_name (0.6.20240107)
    dotenv (3.1.8)
    dotenv-rails (3.1.8)
      dotenv (= 3.1.8)
      railties (>= 6.1)
    down (5.4.2)
      addressable (~> 2.8)
    drb (2.2.3)
    erb (5.0.2)
    erubi (1.13.1)
    et-orbi (1.2.11)
      tzinfo
    ethon (0.16.0)
      ffi (>= 1.15.0)
    event_stream_parser (1.0.0)
    eventmachine (1.2.7)
    execjs (2.10.0)
    factory_bot (6.5.4)
      activesupport (>= 6.1.0)
    factory_bot_rails (6.5.0)
      factory_bot (~> 6.5)
      railties (>= 6.1.0)
    faraday (2.13.4)
      faraday-net_http (>= 2.0, < 3.5)
      json
      logger
    faraday-multipart (1.1.1)
      multipart-post (~> 2.0)
    faraday-net_http (3.4.1)
      net-http (>= 0.5.0)
    faye-websocket (0.12.0)
      eventmachine (>= 0.12.0)
      websocket-driver (>= 0.8.0)
    ffi (1.17.2)
    ffi (1.17.2-arm64-darwin)
    ffi-compiler (1.3.2)
      ffi (>= 1.15.5)
      rake
    fugit (1.11.1)
      et-orbi (~> 1, >= 1.2.11)
      raabro (~> 1.4)
    globalid (1.2.1)
      activesupport (>= 6.1)
    groupdate (6.7.0)
      activesupport (>= 7.1)
    has_scope (0.8.2)
      actionpack (>= 5.2)
      activesupport (>= 5.2)
    hashery (2.1.2)
    hiredis (0.6.3)
    htmlentities (4.3.4)
    http (5.3.1)
      addressable (~> 2.8)
      http-cookie (~> 1.0)
      http-form_data (~> 2.2)
      llhttp-ffi (~> 0.5.0)
    http-accept (1.7.0)
    http-cookie (1.0.8)
      domain_name (~> 0.5)
    http-form_data (2.3.0)
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    inherited_resources (2.1.0)
      actionpack (>= 7.0)
      has_scope (>= 0.6)
      railties (>= 7.0)
      responders (>= 2)
    io-console (0.8.1)
    irb (1.15.2)
      pp (>= 0.6.0)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    jbuilder (2.13.0)
      actionview (>= 5.0.0)
      activesupport (>= 5.0.0)
    jmespath (1.6.2)
    json (2.13.2)
    json-schema (5.2.1)
      addressable (~> 2.8)
      bigdecimal (~> 3.1)
    langchainrb (0.19.5)
      baran (~> 0.1.9)
      csv
      json-schema (>= 4, < 6)
      matrix
      pragmatic_segmenter (~> 0.3.0)
      zeitwerk (~> 2.5)
    language_server-protocol (********)
    lint_roller (1.1.0)
    llhttp-ffi (0.5.1)
      ffi-compiler (~> 1.0)
      rake (~> 13.0)
    logger (1.7.0)
    loofah (2.24.1)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    matrix (0.4.3)
    method_source (1.1.0)
    mime-types (3.7.0)
      logger
      mime-types-data (~> 3.2025, >= 3.2025.0507)
    mime-types-data (3.2025.0722)
    mina (1.2.5)
      rake
    mina-multistage (1.0.4)
      mina (~> 1.0)
    mini_magick (5.3.0)
      logger
    mini_mime (1.1.5)
    mini_portile2 (2.8.9)
    minitest (5.25.5)
    mocha (2.7.1)
      ruby2_keywords (>= 0.0.5)
    msgpack (1.8.0)
    multi_json (1.17.0)
    multipart-post (2.4.1)
    mustermann (3.0.3)
      ruby2_keywords (~> 0.0.1)
    mutex_m (0.3.0)
    mysql2 (0.5.6)
    neighbor (0.6.0)
      activerecord (>= 7.1)
    nest (3.2.0)
      redic
    net-http (0.6.0)
      uri
    net-imap (0.5.9)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-smtp (0.5.1)
      net-protocol
    netrc (0.11.0)
    nilify_blanks (1.4.0)
      activerecord (>= 4.0.0)
      activesupport (>= 4.0.0)
    nio4r (2.7.4)
    nokogiri (1.18.9)
      mini_portile2 (~> 2.8.2)
      racc (~> 1.4)
    nokogiri (1.18.9-arm64-darwin)
      racc (~> 1.4)
    ohm (3.1.1)
      nest (~> 3)
      redic (~> 1.5.0)
      stal
    paper_trail (16.0.0)
      activerecord (>= 6.1)
      request_store (~> 1.4)
    parallel (1.27.0)
    paranoia (3.0.1)
      activerecord (>= 6, < 8.1)
    parser (*******)
      ast (~> 2.4.1)
      racc
    pdf-reader (2.14.1)
      Ascii85 (>= 1.0, < 3.0, != 2.0.0)
      afm (~> 0.2.1)
      hashery (~> 2.0)
      ruby-rc4
      ttfunk
    pg (1.6.0)
    pg (1.6.0-arm64-darwin)
    pgvector (0.3.2)
    pluck_all (2.3.4)
      activesupport (>= 3.0.0)
      rails_compatibility (>= 0.0.10)
    pp (0.6.2)
      prettyprint
    pragmatic_segmenter (0.3.24)
    prettyprint (0.2.0)
    prism (1.4.0)
    pry (0.15.2)
      coderay (~> 1.1)
      method_source (~> 1.0)
    pry-byebug (3.11.0)
      byebug (~> 12.0)
      pry (>= 0.13, < 0.16)
    pry-rails (0.3.11)
      pry (>= 0.13.0)
    psych (5.2.6)
      date
      stringio
    public_suffix (6.0.2)
    puma (6.6.0)
      nio4r (~> 2.0)
    pundit (2.5.0)
      activesupport (>= 3.0.0)
    raabro (1.4.0)
    racc (1.8.1)
    rack (2.2.17)
    rack-cors (2.0.2)
      rack (>= 2.0.0)
    rack-protection (3.2.0)
      base64 (>= 0.1.0)
      rack (~> 2.2, >= 2.2.4)
    rack-session (1.0.2)
      rack (< 3)
    rack-test (2.2.0)
      rack (>= 1.3)
    rackup (1.0.1)
      rack (< 3)
      webrick
    rails (*******)
      actioncable (= *******)
      actionmailbox (= *******)
      actionmailer (= *******)
      actionpack (= *******)
      actiontext (= *******)
      actionview (= *******)
      activejob (= *******)
      activemodel (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      bundler (>= 1.15.0)
      railties (= *******)
    rails-dom-testing (2.3.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    rails_compatibility (0.0.10)
      activerecord (>= 3)
    railties (*******)
      actionpack (= *******)
      activesupport (= *******)
      irb
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)
    rainbow (3.1.1)
    rake (13.3.0)
    ransack (4.3.0)
      activerecord (>= 6.1.5)
      activesupport (>= 6.1.5)
      i18n
    ransack-enum (1.0.0)
    rdoc (6.14.2)
      erb
      psych (>= 4.0.0)
    redic (1.5.3)
      hiredis
    redis (4.8.1)
    redis-namespace (1.11.0)
      redis (>= 4)
    redis-objects (1.7.0)
      redis
    regexp_parser (2.10.0)
    reline (0.6.2)
      io-console (~> 0.5)
    request_store (1.7.0)
      rack (>= 1.4)
    responders (3.1.1)
      actionpack (>= 5.2)
      railties (>= 5.2)
    rest-client (2.1.0)
      http-accept (>= 1.7.0, < 2.0)
      http-cookie (>= 1.0.2, < 2.0)
      mime-types (>= 1.16, < 4.0)
      netrc (~> 0.8)
    rexml (3.4.1)
    rmmseg-cpp-new (0.3.1)
    roda (3.94.0)
      rack
    rolify (6.0.1)
    roo (2.10.1)
      nokogiri (~> 1)
      rubyzip (>= 1.3.0, < 3.0.0)
    roo-xls (1.2.0)
      nokogiri
      roo (>= 2.0.0, < 3)
      spreadsheet (> 0.9.0)
    rspec-core (3.13.5)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.5)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-mocks (3.13.5)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-rails (7.1.1)
      actionpack (>= 7.0)
      activesupport (>= 7.0)
      railties (>= 7.0)
      rspec-core (~> 3.13)
      rspec-expectations (~> 3.13)
      rspec-mocks (~> 3.13)
      rspec-support (~> 3.13)
    rspec-support (3.13.4)
    rubocop (1.79.0)
      json (~> 2.3)
      language_server-protocol (~> ********)
      lint_roller (~> 1.1.0)
      parallel (~> 1.10)
      parser (>= *******)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 2.9.3, < 3.0)
      rubocop-ast (>= 1.46.0, < 2.0)
      ruby-progressbar (~> 1.7)
      tsort (>= 0.2.0)
      unicode-display_width (>= 2.4.0, < 4.0)
    rubocop-ast (1.46.0)
      parser (>= 3.3.7.2)
      prism (~> 1.4)
    rubocop-performance (1.25.0)
      lint_roller (~> 1.1)
      rubocop (>= 1.75.0, < 2.0)
      rubocop-ast (>= 1.38.0, < 2.0)
    rubocop-rails (2.32.0)
      activesupport (>= 4.2.0)
      lint_roller (~> 1.1)
      rack (>= 1.1)
      rubocop (>= 1.75.0, < 2.0)
      rubocop-ast (>= 1.44.0, < 2.0)
    rubocop-rails-omakase (1.1.0)
      rubocop (>= 1.72)
      rubocop-performance (>= 1.24)
      rubocop-rails (>= 2.30)
    ruby-ole (********)
    ruby-openai (8.1.0)
      event_stream_parser (>= 0.3.0, < 2.0.0)
      faraday (>= 1)
      faraday-multipart (>= 1)
    ruby-pinyin (0.5.0)
      rmmseg-cpp-new (~> 0.3.1)
    ruby-progressbar (1.13.0)
    ruby-rc4 (0.1.5)
    ruby2_keywords (0.0.5)
    ruby_lunardate (0.1.1)
    rubyzip (2.4.1)
    rufus-scheduler (3.9.2)
      fugit (~> 1.1, >= 1.11.1)
    sassc (2.4.0)
      ffi (~> 1.9)
    securerandom (0.4.1)
    selenium-webdriver (4.10.0)
      rexml (~> 3.2, >= 3.2.5)
      rubyzip (>= 1.2.2, < 3.0)
      websocket (~> 1.0)
    sequel (5.94.0)
      bigdecimal
    shoulda-matchers (6.5.0)
      activesupport (>= 5.2.0)
    sidekiq (6.5.12)
      connection_pool (>= 2.2.5, < 3)
      rack (~> 2.0)
      redis (>= 4.5.0, < 5)
    sidekiq-scheduler (5.0.6)
      rufus-scheduler (~> 3.2)
      sidekiq (>= 6, < 8)
      tilt (>= 1.4.0, < 3)
    simplecov (0.22.0)
      docile (~> 1.1)
      simplecov-html (~> 0.11)
      simplecov_json_formatter (~> 0.1)
    simplecov-html (0.13.2)
    simplecov_json_formatter (0.1.4)
    sinatra (3.2.0)
      mustermann (~> 3.0)
      rack (~> 2.2, >= 2.2.4)
      rack-protection (= 3.2.0)
      tilt (~> 2.0)
    spreadsheet (1.3.4)
      bigdecimal
      logger
      ruby-ole
    sprockets (4.2.2)
      concurrent-ruby (~> 1.0)
      logger
      rack (>= 2.2.4, < 4)
    sqlite3 (2.7.3)
      mini_portile2 (~> 2.8.0)
    sqlite3 (2.7.3-arm64-darwin)
    stal (0.3.0)
      redic (~> 1.5)
    streamio-ffmpeg (3.0.2)
      multi_json (~> 1.8)
    stringio (3.1.7)
    strip_attributes (2.0.0)
      activemodel (>= 3.0, < 9.0)
    ta_by_star (4.1.0)
      activesupport (>= 3.2.0)
    ta_deep_pluck (1.3.0)
      activerecord (>= 3)
      pluck_all (>= 2.3.2)
      rails_compatibility (>= 0.0.4)
    ta_default_value_for (3.4.0)
      activerecord (>= 3.2.0, < 8.0)
    ta_has_event (1.0.1)
      activerecord (>= 4.0)
      verbs
    ta_ransack_mongo (1.0.3)
    tallty_duck_record (1.1.3)
      activemodel (>= 5.0)
      activesupport (>= 5.0)
    tallty_form (1.0.0)
      tallty_duck_record
    thor (1.4.0)
    tilt (2.6.1)
    timeout (0.4.3)
    tsort (0.2.0)
    ttfunk (1.8.0)
      bigdecimal (~> 3.1)
    typhoeus (1.4.1)
      ethon (>= 0.9.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    unicode-display_width (3.1.4)
      unicode-emoji (~> 4.0, >= 4.0.4)
    unicode-emoji (4.0.4)
    uppy-s3_multipart (1.2.1)
      aws-sdk-s3 (~> 1.0)
      content_disposition (~> 1.0)
      roda (>= 2.27, < 4)
    uri (1.0.3)
    uuidtools (3.0.0)
    verbs (3.1.0)
      activesupport (>= 2.3.4)
      i18n
    webdrivers (5.3.1)
      nokogiri (~> 1.6)
      rubyzip (>= 1.3.0)
      selenium-webdriver (~> 4.0, < 4.11)
    webrick (1.9.1)
    websocket (1.2.11)
    websocket-driver (0.8.0)
      base64
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    will_paginate (4.0.1)
    with_advisory_lock (5.3.0)
      activerecord (>= 6.1)
      zeitwerk (>= 2.6)
    zeitwerk (2.7.3)
    zip-zip (0.3)
      rubyzip (>= 1.0.0)
    zip_tricks (5.6.0)

PLATFORMS
  arm64-darwin-23
  ruby

DEPENDENCIES
  acts_as_pasting!
  annotate
  bootsnap
  brakeman
  debug
  dotenv-rails
  factory_bot_rails
  faraday
  mina
  mina-multistage
  mini_magick
  mocha
  mysql2
  neighbor
  pdf-reader
  pg
  pgvector
  pry-byebug
  pry-rails
  puma (>= 5.0)
  rails (~> 7.1.0)
  rails_action_core!
  rails_assessment!
  rails_audit!
  rails_bot!
  rails_bpm!
  rails_chat!
  rails_com!
  rails_data!
  rails_dingtalk!
  rails_favor!
  rails_grant!
  rails_iest!
  rails_notify!
  rails_opm!
  rails_permit!
  rails_reg!
  rails_region!
  rails_res!
  rails_rss!
  rails_schedule!
  rails_serve!
  rails_sms_auth!
  rails_soa_auth!
  rails_spider!
  rails_tofu!
  rspec-rails
  rspec-rails-swagger!
  rubocop
  rubocop-performance
  rubocop-rails
  rubocop-rails-omakase
  ruby-openai
  sassc
  selenium-webdriver
  sequel
  shoulda-matchers
  sidekiq (~> 6.5.7)
  sidekiq-scheduler
  simple_controller!
  simplecov
  sinatra
  sprockets
  sqlite3 (>= 1.4)
  tallty_import_export!
  tzinfo-data
  webdrivers

BUNDLED WITH
   2.5.23
