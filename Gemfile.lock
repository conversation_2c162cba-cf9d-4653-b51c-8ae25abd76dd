GIT
  remote: ******************:ta-rails/rails_action_core.git
  revision: 6c57d8576a923298bda3ad56cf81cc332390d9fb
  specs:
    rails_action_core (0.1.0)
      jbuilder
      rack-cors
      rails

GIT
  remote: ******************:ta-rails/rails_bpm.git
  revision: 5de77ec2e69369ba0c4af282d30d9be547bfb92f
  specs:
    rails_bpm (0.1.0)
      jbuilder
      rack-cors
      rails
      tallty_import_export

GIT
  remote: ******************:ta-rails/rails_com.git
  revision: f5f88a9942857cc0749ffcda6f804750de2e3c6a
  specs:
    rails_com (0.1.0)
      aasm
      active_record_extended
      acts_as_list
      ancestry
      attr_json
      aws-sdk-s3
      closure_tree
      deep_cloneable
      execjs
      groupdate
      jbuilder
      matrix
      mime-types
      nilify_blanks
      ohm
      paper_trail
      paranoia
      pundit
      rack-cors
      rails
      ransack
      ransack-enum
      redis
      redis-namespace
      redis-objects
      responders
      rolify
      ruby-pinyin
      strip_attributes
      ta_by_star
      ta_deep_pluck
      ta_default_value_for
      ta_has_event
      typhoeus
      uppy-s3_multipart
      uuidtools
      zip_tricks

GIT
  remote: ******************:ta-rails/rails_data.git
  revision: 27b5fbe0299f518c763542cbfb19c4b92f376384
  specs:
    rails_data (0.1.0)
      jbuilder
      rack-cors
      rails
      tallty_import_export

GIT
  remote: ******************:ta-rails/rails_notify.git
  revision: aa03bce9764f1bd4ccb4d6e9d7b01c6d0e39323e
  specs:
    rails_notify (0.1.0)
      acts_as_commentable_with_threading
      jbuilder
      rack-cors
      rails
      rest-client

GIT
  remote: ******************:ta-rails/rails_permit.git
  revision: f8c145175a87a6b5d647dabc83672f91f81b7796
  specs:
    rails_permit (0.1.0)
      jbuilder
      rack-cors
      rails

GIT
  remote: ******************:ta-rails/rails_reg.git
  revision: 82da2191cec5fccd17b18cfe181a91d7ab1251c9
  specs:
    rails_reg (0.1.0)
      jbuilder
      rack-cors
      rails

GIT
  remote: ******************:ta-rails/rails_res.git
  revision: 2674169bb00bb780046449b58fac35132423f063
  specs:
    rails_res (0.1.0)
      jbuilder
      rack-cors
      rails

GIT
  remote: ******************:ta-rails/rails_state.git
  revision: 4cb9bf32ba74152c565f6fbdcb066f2b6de9c264
  specs:
    rails_state (0.1.0)
      jbuilder
      rack-cors
      rails

GIT
  remote: ******************:ta-rails/rails_tofu.git
  revision: 4e430b69972d054e1b344fe4ce68ae75f6c16cf5
  specs:
    rails_tofu (0.1.0)
      jbuilder
      rack-cors
      rails

GIT
  remote: http://git.tallty.com/open-source/rspec-rails-swagger.git
  revision: c1b2bb18d1fc3358c0640355750230b1bbc20476
  specs:
    rspec-rails-swagger (0.1.4)
      rails (>= 3.1)
      rspec-rails

GIT
  remote: https://git.tallty.com/open-source/simple_controller.git
  revision: e8c693162ebbc29d82db5389b680d0f7fe18ecb0
  specs:
    simple_controller (1.1.0)
      inherited_resources
      pundit
      ransack
      responders
      ta_ransack_mongo
      will_paginate

PATH
  remote: .
  specs:
    rails_serve (0.1.0)
      jbuilder
      rack-cors
      rails (~> 7.1.0)

GEM
  remote: https://gems.ruby-china.com/
  specs:
    aasm (5.5.0)
      concurrent-ruby (~> 1.0)
    actioncable (7.1.4)
      actionpack (= 7.1.4)
      activesupport (= 7.1.4)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
      zeitwerk (~> 2.6)
    actionmailbox (7.1.4)
      actionpack (= 7.1.4)
      activejob (= 7.1.4)
      activerecord (= 7.1.4)
      activestorage (= 7.1.4)
      activesupport (= 7.1.4)
      mail (>= 2.7.1)
      net-imap
      net-pop
      net-smtp
    actionmailer (7.1.4)
      actionpack (= 7.1.4)
      actionview (= 7.1.4)
      activejob (= 7.1.4)
      activesupport (= 7.1.4)
      mail (~> 2.5, >= 2.5.4)
      net-imap
      net-pop
      net-smtp
      rails-dom-testing (~> 2.2)
    actionpack (7.1.4)
      actionview (= 7.1.4)
      activesupport (= 7.1.4)
      nokogiri (>= 1.8.5)
      racc
      rack (>= 2.2.4)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    actiontext (7.1.4)
      actionpack (= 7.1.4)
      activerecord (= 7.1.4)
      activestorage (= 7.1.4)
      activesupport (= 7.1.4)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (7.1.4)
      activesupport (= 7.1.4)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    active_record_extended (3.3.0)
      activerecord (>= 5.2, < 8.0.0)
      pg (< 3.0)
    activejob (7.1.4)
      activesupport (= 7.1.4)
      globalid (>= 0.3.6)
    activemodel (7.1.4)
      activesupport (= 7.1.4)
    activerecord (7.1.4)
      activemodel (= 7.1.4)
      activesupport (= 7.1.4)
      timeout (>= 0.4.0)
    activestorage (7.1.4)
      actionpack (= 7.1.4)
      activejob (= 7.1.4)
      activerecord (= 7.1.4)
      activesupport (= 7.1.4)
      marcel (~> 1.0)
    activesupport (7.1.4)
      base64
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.0.2)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      minitest (>= 5.1)
      mutex_m
      tzinfo (~> 2.0)
    acts_as_commentable_with_threading (2.0.1)
      activerecord (>= 4.0)
      activesupport (>= 4.0)
      awesome_nested_set (>= 3.0)
    acts_as_list (1.2.3)
      activerecord (>= 6.1)
      activesupport (>= 6.1)
    ancestry (4.3.3)
      activerecord (>= 5.2.6)
    annotate (3.2.0)
      activerecord (>= 3.2, < 8.0)
      rake (>= 10.4, < 14.0)
    attr_json (2.4.0)
      activerecord (>= 6.0.0, < 7.3)
    awesome_nested_set (3.7.0)
      activerecord (>= 4.0.0, < 8.0)
    awesome_print (1.9.2)
    aws-eventstream (1.3.0)
    aws-partitions (1.990.0)
    aws-sdk-core (3.209.1)
      aws-eventstream (~> 1, >= 1.3.0)
      aws-partitions (~> 1, >= 1.651.0)
      aws-sigv4 (~> 1.9)
      jmespath (~> 1, >= 1.6.1)
    aws-sdk-kms (1.94.0)
      aws-sdk-core (~> 3, >= 3.207.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-s3 (1.167.0)
      aws-sdk-core (~> 3, >= 3.207.0)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.5)
    aws-sigv4 (1.10.0)
      aws-eventstream (~> 1, >= 1.0.2)
    base64 (0.2.0)
    better_errors (2.10.1)
      erubi (>= 1.0.0)
      rack (>= 0.9.0)
      rouge (>= 1.0.0)
    bigdecimal (3.1.8)
    binding_of_caller (1.0.1)
      debug_inspector (>= 1.2.0)
    builder (3.3.0)
    byebug (11.1.3)
    caxlsx (4.1.0)
      htmlentities (~> 4.3, >= 4.3.4)
      marcel (~> 1.0)
      nokogiri (~> 1.10, >= 1.10.4)
      rubyzip (>= 1.3.0, < 3)
    closure_tree (7.4.0)
      activerecord (>= 4.2.10)
      with_advisory_lock (>= 4.0.0)
    coderay (1.1.3)
    concurrent-ruby (1.3.4)
    connection_pool (2.4.1)
    content_disposition (1.0.0)
    crass (1.0.6)
    date (3.3.4)
    debug_inspector (1.2.0)
    deep_cloneable (3.2.0)
      activerecord (>= 3.1.0, < 8)
    diff-lcs (1.5.1)
    domain_name (0.6.20240107)
    dotenv (3.1.4)
    dotenv-rails (3.1.4)
      dotenv (= 3.1.4)
      railties (>= 6.1)
    drb (2.2.1)
    erubi (1.13.0)
    ethon (0.16.0)
      ffi (>= 1.15.0)
    execjs (2.9.1)
    factory_bot (6.5.0)
      activesupport (>= 5.0.0)
    factory_bot_rails (6.4.3)
      factory_bot (~> 6.4)
      railties (>= 5.0.0)
    ffi (1.17.0)
    ffi (1.17.0-aarch64-linux-gnu)
    ffi (1.17.0-arm-linux-gnu)
    ffi (1.17.0-arm64-darwin)
    ffi (1.17.0-x86-linux-gnu)
    ffi (1.17.0-x86_64-darwin)
    ffi (1.17.0-x86_64-linux-gnu)
    globalid (1.2.1)
      activesupport (>= 6.1)
    groupdate (6.5.1)
      activesupport (>= 7)
    has_scope (0.8.2)
      actionpack (>= 5.2)
      activesupport (>= 5.2)
    hirb (0.7.3)
    hiredis (0.6.3)
    htmlentities (4.3.4)
    http-accept (1.7.0)
    http-cookie (1.0.7)
      domain_name (~> 0.5)
    i18n (1.14.6)
      concurrent-ruby (~> 1.0)
    inherited_resources (2.0.1)
      actionpack (>= 6.0)
      has_scope (>= 0.6)
      railties (>= 6.0)
      responders (>= 2)
    io-console (0.7.2)
    irb (1.14.1)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    jbuilder (2.13.0)
      actionview (>= 5.0.0)
      activesupport (>= 5.0.0)
    jmespath (1.6.2)
    logger (1.6.1)
    loofah (2.22.0)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.4)
    matrix (0.4.2)
    method_source (1.1.0)
    mime-types (3.6.0)
      logger
      mime-types-data (~> 3.2015)
    mime-types-data (3.2024.1001)
    mini_mime (1.1.5)
    mini_portile2 (2.8.7)
    minitest (5.25.1)
    mutex_m (0.2.0)
    nest (3.2.0)
      redic
    net-imap (0.4.17)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-smtp (0.5.0)
      net-protocol
    netrc (0.11.0)
    nilify_blanks (1.4.0)
      activerecord (>= 4.0.0)
      activesupport (>= 4.0.0)
    nio4r (2.7.3)
    nokogiri (1.16.7)
      mini_portile2 (~> 2.8.2)
      racc (~> 1.4)
    nokogiri (1.16.7-aarch64-linux)
      racc (~> 1.4)
    nokogiri (1.16.7-arm-linux)
      racc (~> 1.4)
    nokogiri (1.16.7-arm64-darwin)
      racc (~> 1.4)
    nokogiri (1.16.7-x86-linux)
      racc (~> 1.4)
    nokogiri (1.16.7-x86_64-darwin)
      racc (~> 1.4)
    nokogiri (1.16.7-x86_64-linux)
      racc (~> 1.4)
    ohm (3.1.1)
      nest (~> 3)
      redic (~> 1.5.0)
      stal
    paper_trail (15.2.0)
      activerecord (>= 6.1)
      request_store (~> 1.4)
    paranoia (3.0.0)
      activerecord (>= 6, < 8.1)
    pg (1.5.8)
    pluck_all (2.3.4)
      activesupport (>= 3.0.0)
      rails_compatibility (>= 0.0.10)
    pry (0.14.2)
      coderay (~> 1.1)
      method_source (~> 1.0)
    pry-byebug (3.10.1)
      byebug (~> 11.0)
      pry (>= 0.13, < 0.15)
    pry-doc (1.5.0)
      pry (~> 0.11)
      yard (~> 0.9.11)
    pry-rails (0.3.11)
      pry (>= 0.13.0)
    psych (5.1.2)
      stringio
    pundit (2.4.0)
      activesupport (>= 3.0.0)
    racc (1.8.1)
    rack (3.1.8)
    rack-cors (2.0.2)
      rack (>= 2.0.0)
    rack-session (2.0.0)
      rack (>= 3.0.0)
    rack-test (2.1.0)
      rack (>= 1.3)
    rackup (2.1.0)
      rack (>= 3)
      webrick (~> 1.8)
    rails (7.1.4)
      actioncable (= 7.1.4)
      actionmailbox (= 7.1.4)
      actionmailer (= 7.1.4)
      actionpack (= 7.1.4)
      actiontext (= 7.1.4)
      actionview (= 7.1.4)
      activejob (= 7.1.4)
      activemodel (= 7.1.4)
      activerecord (= 7.1.4)
      activestorage (= 7.1.4)
      activesupport (= 7.1.4)
      bundler (>= 1.15.0)
      railties (= 7.1.4)
    rails-dom-testing (2.2.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.0)
      loofah (~> 2.21)
      nokogiri (~> 1.14)
    rails_compatibility (0.0.10)
      activerecord (>= 3)
    railties (7.1.4)
      actionpack (= 7.1.4)
      activesupport (= 7.1.4)
      irb
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)
    rake (13.2.1)
    ransack (4.2.1)
      activerecord (>= 6.1.5)
      activesupport (>= 6.1.5)
      i18n
    ransack-enum (1.0.0)
    rdoc (6.7.0)
      psych (>= 4.0.0)
    redic (1.5.3)
      hiredis
    redis (5.3.0)
      redis-client (>= 0.22.0)
    redis-client (0.22.2)
      connection_pool
    redis-namespace (1.11.0)
      redis (>= 4)
    redis-objects (1.7.0)
      redis
    reline (0.5.10)
      io-console (~> 0.5)
    request_store (1.7.0)
      rack (>= 1.4)
    responders (3.1.1)
      actionpack (>= 5.2)
      railties (>= 5.2)
    rest-client (2.1.0)
      http-accept (>= 1.7.0, < 2.0)
      http-cookie (>= 1.0.2, < 2.0)
      mime-types (>= 1.16, < 4.0)
      netrc (~> 0.8)
    rmmseg-cpp-new (0.3.1)
    roda (3.85.0)
      rack
    rolify (6.0.1)
    roo (2.10.1)
      nokogiri (~> 1)
      rubyzip (>= 1.3.0, < 3.0.0)
    roo-xls (1.2.0)
      nokogiri
      roo (>= 2.0.0, < 3)
      spreadsheet (> 0.9.0)
    rouge (4.4.0)
    rspec-core (3.13.1)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.3)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-mocks (3.13.2)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-rails (7.0.1)
      actionpack (>= 7.0)
      activesupport (>= 7.0)
      railties (>= 7.0)
      rspec-core (~> 3.13)
      rspec-expectations (~> 3.13)
      rspec-mocks (~> 3.13)
      rspec-support (~> 3.13)
    rspec-support (3.13.1)
    ruby-ole (********)
    ruby-pinyin (0.5.0)
      rmmseg-cpp-new (~> 0.3.1)
    rubyzip (2.3.2)
    sassc (2.4.0)
      ffi (~> 1.9)
    sassc-rails (2.1.2)
      railties (>= 4.0.0)
      sassc (>= 2.0)
      sprockets (> 3.0)
      sprockets-rails
      tilt
    shoulda-matchers (6.4.0)
      activesupport (>= 5.2.0)
    spreadsheet (1.3.1)
      bigdecimal
      ruby-ole
    sprockets (4.2.1)
      concurrent-ruby (~> 1.0)
      rack (>= 2.2.4, < 4)
    sprockets-rails (3.5.2)
      actionpack (>= 6.1)
      activesupport (>= 6.1)
      sprockets (>= 3.0.0)
    sqlite3 (2.1.0)
      mini_portile2 (~> 2.8.0)
    sqlite3 (2.1.0-aarch64-linux-gnu)
    sqlite3 (2.1.0-arm-linux-gnu)
    sqlite3 (2.1.0-arm64-darwin)
    sqlite3 (2.1.0-x86-linux-gnu)
    sqlite3 (2.1.0-x86_64-darwin)
    sqlite3 (2.1.0-x86_64-linux-gnu)
    stal (0.3.0)
      redic (~> 1.5)
    stringio (3.1.1)
    strip_attributes (1.13.0)
      activemodel (>= 3.0, < 8.0)
    ta_by_star (4.1.0)
      activesupport (>= 3.2.0)
    ta_deep_pluck (1.3.0)
      activerecord (>= 3)
      pluck_all (>= 2.3.2)
      rails_compatibility (>= 0.0.4)
    ta_default_value_for (3.4.0)
      activerecord (>= 3.2.0, < 8.0)
    ta_has_event (1.0.1)
      activerecord (>= 4.0)
      verbs
    ta_ransack_mongo (1.0.3)
    tallty_duck_record (1.1.3)
      activemodel (>= 5.0)
      activesupport (>= 5.0)
    tallty_form (1.0.0)
      tallty_duck_record
    tallty_import_export (1.1.5)
      activesupport
      attr_json
      caxlsx
      redis
      redis-objects
      roo
      roo-xls
      tallty_form
      zip-zip
    thor (1.3.2)
    tilt (2.4.0)
    timeout (0.4.1)
    typhoeus (1.4.1)
      ethon (>= 0.9.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    uppy-s3_multipart (1.2.1)
      aws-sdk-s3 (~> 1.0)
      content_disposition (~> 1.0)
      roda (>= 2.27, < 4)
    uuidtools (2.2.0)
    verbs (3.1.0)
      activesupport (>= 2.3.4)
      i18n
    webrick (1.8.2)
    websocket-driver (0.7.6)
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    will_paginate (4.0.1)
    with_advisory_lock (5.1.0)
      activerecord (>= 6.1)
      zeitwerk (>= 2.6)
    yard (0.9.37)
    zeitwerk (2.7.0)
    zip-zip (0.3)
      rubyzip (>= 1.0.0)
    zip_tricks (5.6.0)

PLATFORMS
  aarch64-linux
  arm-linux
  arm64-darwin
  ruby
  x86-linux
  x86_64-darwin
  x86_64-linux

DEPENDENCIES
  annotate
  awesome_print
  better_errors
  binding_of_caller
  dotenv-rails
  factory_bot_rails
  hirb
  pg
  pry-byebug
  pry-doc
  pry-rails
  rails_action_core!
  rails_bpm!
  rails_com!
  rails_data!
  rails_notify!
  rails_permit!
  rails_reg!
  rails_res!
  rails_serve!
  rails_state!
  rails_tofu!
  rspec-rails
  rspec-rails-swagger!
  sassc-rails
  shoulda-matchers
  simple_controller!
  sqlite3

BUNDLED WITH
   2.5.6
