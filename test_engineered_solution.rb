puts '=== 测试工程化解决方案 ==='
begin
  agent = Bot::Agent.first
  user = User.first
  
  # 测试查询分类器
  puts "\n1. 测试查询分类器"
  puts "-" * 40
  
  test_queries = [
    "素材数量？",
    "前3个月有多少素材呢？",
    "今年开始有多少新素材？",
    "素材的分类情况如何？",
    "你好，今天天气怎么样？",  # 非素材查询
    "帮我写一篇文章",          # 非素材查询
  ]
  
  test_queries.each do |query|
    query_type = Bot::QueryClassifier.classify(query)
    puts "查询: #{query}"
    if query_type
      puts "  类型: #{query_type.type}"
      puts "  置信度: #{query_type.confidence.round(3)}"
      puts "  强制工具调用: #{query_type.should_force_tool_call?}"
      puts "  匹配关键词: #{query_type.matched_keywords.join(', ')}"
    else
      puts "  类型: 未分类"
    end
    puts
  end
  
  # 测试完整的工程化流程
  puts "\n2. 测试完整工程化流程"
  puts "-" * 40
  
  # 素材查询测试
  material_queries = [
    "素材数量？",
    "前3个月有多少素材呢？",
    "今年开始有多少新素材？",
    "素材的分类情况如何？",
    "各类素材的数量分布",
  ]
  
  total_queries = 0
  successful_queries = 0
  forced_tool_calls = 0
  
  material_queries.each_with_index do |query, index|
    puts "\n查询 #{index + 1}/#{material_queries.count}: #{query}"
    puts "-" * 30
    
    total_queries += 1
    
    # 为每个查询创建独立的对话
    conversation = agent.conversations.create!(user: user)
    Bot::Current.conversation = conversation
    
    begin
      # 执行查询
      response = agent.chat(query, user: user, conversation_id: conversation.id)
      
      # 分析预处理信息
      preprocessing_info = response[:preprocessing_info] || {}
      validation_result = response[:validation_result] || {}
      
      puts "预处理信息:"
      puts "  查询类型: #{preprocessing_info[:query_type]}"
      puts "  置信度: #{preprocessing_info[:confidence]}"
      puts "  强制工具调用: #{preprocessing_info[:forced_tool_call]}"
      
      if preprocessing_info[:forced_tool_call]
        forced_tool_calls += 1
        puts "  工具结果: #{preprocessing_info[:tool_result]&.dig(:message)&.[](0..60)}..."
      end
      
      # 分析响应质量
      text_messages = response[:messages].select { |msg| msg[:content_type] == 'text' }
      artifact_messages = response[:messages].select { |msg| msg[:content_type] == 'artifact' }
      
      has_classification = text_messages.any? { |msg| 
        content = msg[:content]
        classification_count = 0
        classification_count += 1 if content.include?('文字')
        classification_count += 1 if content.include?('图片')
        classification_count += 1 if content.include?('视频')
        classification_count += 1 if content.include?('漫画')
        classification_count >= 2
      }
      
      has_artifact = artifact_messages.any? { |msg|
        artifact = msg[:content]
        artifact['tool_cname'] == 'Bot::Tools::ActivityQueryTool'
      }
      
      # 验证结果
      puts "验证结果:"
      puts "  响应有效: #{validation_result[:valid]}"
      puts "  分类统计: #{has_classification ? '✅' : '❌'}"
      puts "  组件显示: #{has_artifact ? '✅' : '❌'}"
      
      if validation_result[:issues]&.any?
        puts "  问题: #{validation_result[:issues].join(', ')}"
      end
      
      # 判断是否成功
      is_success = has_classification && has_artifact
      
      if is_success
        successful_queries += 1
        puts "结果: ✅ 成功"
      else
        puts "结果: ❌ 失败"
      end
      
      # 显示响应内容摘要
      if text_messages.any?
        content = text_messages.first[:content]
        puts "回答摘要: #{content[0..80]}#{content.length > 80 ? '...' : ''}"
      end
      
    rescue StandardError => e
      puts "❌ 查询执行失败: #{e.message}"
      puts e.backtrace.first(3)
    ensure
      Bot::Current.conversation = nil
    end
  end
  
  # 计算最终结果
  success_rate = (successful_queries.to_f / total_queries * 100).round(1)
  forced_tool_call_rate = (forced_tool_calls.to_f / total_queries * 100).round(1)
  
  puts "\n" + "="*60
  puts "工程化解决方案测试结果"
  puts "="*60
  puts "总查询数: #{total_queries}"
  puts "成功查询数: #{successful_queries}"
  puts "成功率: #{success_rate}%"
  puts "强制工具调用数: #{forced_tool_calls}"
  puts "强制工具调用率: #{forced_tool_call_rate}%"
  puts "目标成功率: 80%"
  puts "测试结果: #{success_rate >= 80 ? '🎉 达标' : '❌ 未达标'}"
  
  if success_rate >= 80
    puts "\n✅ 工程化解决方案成功！"
    puts "🎉 通过强制工具调用和响应验证，素材查询功能稳定性大幅提升！"
    puts "💡 关键改进："
    puts "   - 查询预处理：自动识别素材查询并强制调用工具"
    puts "   - 响应验证：确保回答质量和组件显示"
    puts "   - 工程化可控：不依赖LLM的不确定行为"
  else
    puts "\n⚠️ 需要进一步调优工程化方案"
    puts "💡 可能的改进方向："
    puts "   - 优化查询分类器的关键词和置信度阈值"
    puts "   - 改进查询重写策略"
    puts "   - 增强响应验证规则"
  end
  
  puts "\n📊 与之前方案对比："
  puts "   - 纯LLM方案成功率: ~25%"
  puts "   - 强化提示词方案成功率: ~73%"
  puts "   - 工程化方案成功率: #{success_rate}%"
  puts "   - 强制工具调用率: #{forced_tool_call_rate}% (目标: 100%)"
  
rescue StandardError => e
  puts "❌ 测试失败: #{e.message}"
  puts e.backtrace.first(10)
end
