#!/usr/bin/env ruby

# 测试artifact序列化的差异
require_relative 'config/environment'

puts "=== 测试Artifact序列化差异 ==="

# 获取最新的artifact
latest_artifact = Bot::ActivityListArtifact.order(created_at: :desc).first

if latest_artifact
  puts "最新Artifact ID: #{latest_artifact.id}"
  puts "tool_conf: #{latest_artifact.tool_conf.inspect}"
  
  # 测试as_json
  puts "\n=== as_json输出 ==="
  as_json_result = latest_artifact.as_json
  puts "as_json结果: #{as_json_result.inspect}"
  puts "as_json中的tool_conf: #{as_json_result['tool_conf'].inspect}"
  
  # 测试as_jbuilder_json
  puts "\n=== as_jbuilder_json输出 ==="
  as_jbuilder_json_result = latest_artifact.as_jbuilder_json
  puts "as_jbuilder_json结果: #{as_jbuilder_json_result.inspect}"
  puts "as_jbuilder_json中的tool_conf: #{as_jbuilder_json_result[:tool_conf] || as_jbuilder_json_result['tool_conf']}"
  
  # 模拟Bot::Assistant中的消息构建
  puts "\n=== 模拟消息构建 ==="
  
  # 使用as_json的方式（修复前）
  message_with_as_json = {
    content_type: 'artifact',
    content: latest_artifact.as_json
  }
  puts "使用as_json的消息: #{message_with_as_json.inspect}"
  puts "content.tool_conf.model_class: #{message_with_as_json[:content]['tool_conf']&.dig('model_class')}"
  
  # 使用as_jbuilder_json的方式（修复后）
  message_with_as_jbuilder_json = {
    content_type: 'artifact',
    content: latest_artifact.as_jbuilder_json
  }
  puts "\n使用as_jbuilder_json的消息: #{message_with_as_jbuilder_json.inspect}"
  puts "content.tool_conf.model_class: #{message_with_as_jbuilder_json[:content][:tool_conf]&.dig('model_class') || message_with_as_jbuilder_json[:content]['tool_conf']&.dig('model_class')}"
  
else
  puts "没有找到Artifact记录"
end

puts "\n=== 测试完成 ==="
