#!/usr/bin/env ruby

# 测试AI查询修复效果
query = ARGV[0] || "今年有多少招投标类的素材？"

puts "测试AI查询修复效果: #{query}"
puts "=" * 60

begin
  # 创建用户和Agent
  user = User.first || User.create!(name: "测试用户", email: "<EMAIL>")
  app = user.app
  agent = app.bot_agents.first || app.bot_agents.create!(
    name: "测试Agent",
    description: "用于测试的Agent",
    instructions: "你是一个测试助手"
  )

  # 创建对话
  conversation = agent.conversations.create!(user: user)

  puts "1. 测试Agent.chat方法..."
  response = agent.chat(query, user: user, conversation_id: conversation.id)

  puts "Agent响应:"
  if response[:messages]
    response[:messages].each_with_index do |msg, index|
      puts "#{index + 1}. [#{msg[:content_type]}] #{msg[:content]}"
    end
  else
    puts response.inspect
  end

  # 检查预处理信息
  if response[:preprocessing_info]
    puts "\n2. 预处理信息:"
    puts "- 强制工具调用: #{response[:preprocessing_info][:forced_tool_call]}"
    puts "- 查询类型: #{response[:preprocessing_info][:query_type]}"
    puts "- 置信度: #{response[:preprocessing_info][:confidence]}"
    puts "- 策略: #{response[:preprocessing_info][:strategy]}"
  end

  # 检查验证结果
  if response[:validation_result]
    puts "\n3. 验证结果:"
    puts "- 有效: #{response[:validation_result][:valid]}"
    puts "- 问题: #{response[:validation_result][:issues]}"
    puts "- 建议: #{response[:validation_result][:suggestions]}"
  end

  # 直接测试ActivityQueryTool
  puts "\n4. 直接测试ActivityQueryTool..."
  tool = Bot::Tools::ActivityQueryTool.new
  tool_result = tool.query_activities(query: query)
  
  puts "工具返回结果:"
  puts "- 总数: #{tool_result[:total_count]}"
  puts "- 消息: #{tool_result[:message]}"
  puts "- 条件: #{tool_result[:conditions]}"

  # 验证数据一致性
  puts "\n5. 验证数据一致性..."
  
  # 手动查询验证
  manual_conditions = tool_result[:conditions]
  base_query = Serve::Activity.all
  ransack_query = base_query.ransack(manual_conditions)
  manual_count = ransack_query.result.count
  
  puts "手动查询结果: #{manual_count}"
  puts "工具查询结果: #{tool_result[:total_count]}"
  
  if manual_count == tool_result[:total_count]
    puts "✅ 数据一致性验证通过"
  else
    puts "❌ 数据一致性验证失败"
  end

  # 分析时间字段使用
  puts "\n6. 分析时间字段使用..."
  if manual_conditions.key?('published_at_gteq') || manual_conditions.key?(:published_at_gteq)
    puts "✅ 正确使用了published_at字段"
  elsif manual_conditions.key?('created_at_gteq') || manual_conditions.key?(:created_at_gteq)
    puts "❌ 错误使用了created_at字段"
  else
    puts "⚠️  没有检测到时间字段"
  end

  # 检查标签查询逻辑
  puts "\n7. 检查标签查询逻辑..."
  if manual_conditions.to_s.include?('ai_tags') || manual_conditions.to_s.include?('serve_tags')
    puts "✅ 使用了标签查询"
  else
    puts "⚠️  没有使用标签查询"
  end

  # 最终结果对比
  puts "\n8. 最终结果对比..."
  puts "AI响应中的数量: #{response[:messages]&.first&.dig(:content)&.scan(/\d+/)&.first}"
  puts "工具返回的数量: #{tool_result[:total_count]}"
  puts "手动验证的数量: #{manual_count}"

rescue => e
  puts "❌ 测试失败: #{e.message}"
  puts e.backtrace.first(10)
end
