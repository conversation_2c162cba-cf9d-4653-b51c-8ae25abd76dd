require:
  - rubocop-rails  # 引入 rubocop-rails 规则
  - rubocop-performance  # 引入 rubocop-performance 规则

AllCops:
  Exclude:
    - 'bin/**/*'  # 排除 bin 目录下的所有文件
    - 'db/**/*'  # 排除 db 目录下的所有文件
    - 'vendor/**/*'  # 排除 vendor 目录下的所有文件
    - 'node_modules/**/*'  # 排除 node_modules 目录下的所有文件
    - 'tmp/**/*'  # 排除 tmp 目录下的所有文件
  DisabledByDefault: false  # 默认启用所有规则
  TargetRubyVersion: 3.2.2  # 目标 Ruby 版本
  NewCops: enable  # 启用新规则

# 布局相关
Layout/LineLength:
  Max: 120  # 最大行长度为 120，设置允许的最大行长度

Layout/EmptyLinesAroundClassBody:
  EnforcedStyle: empty_lines  # 类体周围强制使用空行，设置为 empty_lines 或 no_empty_lines

Layout/EmptyLinesAroundModuleBody:
  EnforcedStyle: empty_lines  # 模块体周围强制使用空行，设置为 empty_lines 或 no_empty_lines

Layout/EmptyLinesAroundBlockBody:
  EnforcedStyle: no_empty_lines  # 块体周围不强制使用空行，设置为 empty_lines 或 no_empty_lines

Layout/SpaceInsideHashLiteralBraces:
  EnforcedStyle: space  # 哈希字面量大括号内强制使用空格，设置为 space 或 no_space

Layout/FirstHashElementIndentation:
  EnforcedStyle: consistent  # 第一个哈希元素的缩进风格一致，设置为 consistent 或 inconsistent

Layout/MultilineMethodCallIndentation:
  EnforcedStyle: indented  # 多行方法调用的缩进风格为缩进，设置为 indented 或 aligned

# 风格相关
Style/StringLiterals:
  EnforcedStyle: double_quotes  # 强制使用单引号字符串，设置为 single_quotes 或 double_quotes

Style/FrozenStringLiteralComment:
  Enabled: false  # 禁用冻结字符串字面量注释，设置为 true 或 false

Style/Documentation:
  Enabled: false  # 禁用文档注释，设置为 true 或 false

Style/ClassAndModuleChildren:
  Enabled: false  # 禁用类和模块子类的规则，设置为 true 或 false

Style/SymbolArray:
  EnforcedStyle: brackets  # 强制使用括号的符号数组，设置为 brackets 或 no_brackets

Style/WordArray:
  EnforcedStyle: brackets  # 强制使用括号的单词数组，设置为 brackets 或 no_brackets

# 命名相关
Naming/VariableNumber:
  EnforcedStyle: snake_case  # 强制使用蛇形命名法的变量，设置为 snake_case 或其他命名法

# 度量相关
Metrics/MethodLength:
  Max: 20  # 方法最大长度为 20，设置允许的最大方法长度

Metrics/AbcSize:
  Max: 30  # ABC 大小最大为 30，设置允许的最大 ABC 大小

Metrics/ClassLength:
  Max: 200  # 类最大长度为 200，设置允许的最大类长度

Metrics/BlockLength:
  Exclude:
    - 'spec/**/*'  # 排除 spec 目录下的所有文件
    - 'config/routes.rb'  # 排除 config/routes.rb 文件
