{"name": "vue3-template", "version": "2.0.0", "private": true, "scripts": {"build": "vue-cli-service build", "lint": "vue-cli-service lint", "analyz": "vue-cli-service build --mode analyz", "build:dev": "vue-cli-service build --mode development", "build:production": "vue-cli-service build --mode production", "dev": "vue-cli-service serve --mode development", "production": "vue-cli-service serve --mode production", "lint:check": "vue-cli-service lint --no-fix", "prettier": "prettier -c --write '**/*'", "pretty-quick": "pretty-quick", "publish:all": "npm run publish:production && npm run publish:test", "publish:production": "npm run build:production && node scripts/deploy.js", "pull": "git pull && git submodule update --recursive --init && git submodule update --recursive --remote"}, "dependencies": {"@ant-design-vue/use": "^0.0.1-0", "@ant-design/icons-vue": "^6.0.0", "@antv/g6": "^4.2.6", "@ckeditor/ckeditor5-build-decoupled-document": "^28.0.0", "@ckeditor/ckeditor5-vue": "^2.0.1", "@he-tree/vue3": "^1.2.8", "@rails/actioncable": "^7.1.3-2", "@tiptap/extension-character-count": "^2.11.5", "@tiptap/extension-code-block-lowlight": "^2.11.3", "@tiptap/extension-color": "^2.11.5", "@tiptap/extension-highlight": "^2.11.5", "@tiptap/extension-image": "^2.11.5", "@tiptap/extension-task-item": "^2.11.5", "@tiptap/extension-task-list": "^2.11.5", "@tiptap/extension-text-align": "^2.11.5", "@tiptap/extension-text-style": "^2.11.5", "@tiptap/starter-kit": "^2.11.5", "@tiptap/vue-3": "^2.11.5", "@tybys/jweixin": "^1.6.10007", "@types/crypto-js": "^4.1.1", "@types/element-resize-detector": "^1.1.3", "@types/jsoneditor": "^8.6.1", "@types/jsonwebtoken": "^8.5.1", "@types/lodash-es": "^4.17.3", "@types/rails__actioncable": "^6.1.10", "@types/spark-md5": "^3.0.2", "@types/video.js": "^7.3.17", "@vue-office/docx": "^1.6.0", "@vue-office/pdf": "^1.6.5", "@vueuse/core": "^10.11.0", "@wangeditor/editor": "^5.1.1", "@wangeditor/editor-for-vue": "^5.1.11", "@wangeditor/plugin-md": "^1.0.0", "@wangeditor/plugin-mention": "^1.0.0", "ant-design-vue": "^3.2.12", "aplayer": "^1.10.1", "axios": "^0.20.0", "change-case": "^4.1.2", "clipboard": "^2.0.11", "core-js": "^3.6.5", "crypto-js": "^4.1.1", "docxtemplater": "^3.60.1", "dom-to-image": "^2.6.0", "dom-to-image-more": "^3.1.1", "echarts": "^5.5.0", "echarts-gl": "^2.0.0", "echarts-liquidfill": "^3.1.0", "echarts-wordcloud": "^2.1.0", "element-resize-detector": "^1.2.4", "eval5": "^1.4.7", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "gdt-jsapi": "^1.9.39", "html-docx-js-typescript": "^0.1.5", "jsonata": "^1.8.6", "jsoneditor": "^9.5.5", "jsonwebtoken": "^8.5.1", "lodash-es": "^4.17.15", "lottie-web": "^5.10.2", "lowlight": "^2.0.0", "markdown-it": "^14.1.0", "mitt": "^2.1.0", "moment": "^2.28.0", "nprogress": "^0.2.0", "numeral": "^2.0.6", "pizzip": "^3.1.8", "pluralize": "^8.0.0", "pug": "^3.0.2", "pug-loader": "^2.4.0", "pug-plain-loader": "^1.1.0", "qrcode": "^1.5.3", "resize-detector": "^0.2.2", "screenfull": "^5.0.2", "scroll-into-view-if-needed": "^2.2.26", "smooth-dnd": "^0.12.1", "smooth-scroll-into-view-if-needed": "^1.1.33", "spark-md5": "^3.0.1", "store": "^2.0.12", "stylus": "^0.54.8", "swiper": "^8.1.5", "three": "^0.171.0", "tiptap-markdown": "^0.8.10", "utils": "^0.3.1", "v-viewer": "^3.0.5", "video.js": "^7.11.8", "vue": "3.3.4", "vue-click-outside": "^1.1.0", "vue-i18n": "~9.0.0-0", "vue-json-pretty": "^2.4.0", "vue-router": "^4.0.0", "vue3-marquee": "^4.2.0-beta.1", "vue3-seamless-scroll": "^2.0.1", "vuedraggable": "^4.0.1", "vuex": "~4.0.0", "vuex-persistedstate": "^4.0.0-beta.1", "windicss": "^3.5.1"}, "devDependencies": {"@babel/plugin-transform-typescript": "^7.12.1", "@iboying/easy-deploy": "^0.4.0", "@types/echarts": "^4.6.6", "@types/lodash": "^4.14.161", "@types/nprogress": "^0.2.0", "@types/numeral": "0.0.28", "@types/pluralize": "^0.0.29", "@types/qrcode": "^1.5.0", "@types/store": "^2.0.2", "@typescript-eslint/eslint-plugin": "^4.14.0", "@typescript-eslint/parser": "^4.14.0", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-typescript": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.15", "@vue/compiler-sfc": "3.0.10", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^7.0.0", "babel-eslint": "^10.1.0", "babel-plugin-import": "^1.13.0", "cheerio": "^1.0.0-rc.5", "eslint": "^7.18.0", "eslint-plugin-html": "^6.0.3", "eslint-plugin-prettier": "^3.1.3", "eslint-plugin-vue": "^7.5.0", "husky": "^5.0.9", "less": "^4.1.0", "less-loader": "^7.3.0", "lint-staged": "^10.5.0", "mockjs": "^1.1.0", "prettier": "^2.2.1", "prettier-plugin-style-order": "^0.2.2", "prettier-quick": "^0.0.5", "string-replace-webpack-plugin": "^0.1.3", "stylelint": "^13.7.0", "stylelint-config-css-modules": "^2.2.0", "stylelint-config-prettier": "^8.0.1", "stylelint-config-rational-order": "^0.1.2", "stylelint-config-standard": "^20.0.0", "stylelint-declaration-block-no-ignored-properties": "^2.1.0", "stylelint-no-unsupported-browser-features": "^4.1.4", "stylelint-order": "^4.0.0", "stylelint-webpack-plugin": "^2.1.1", "stylus-loader": "3.0.2", "typescript": "~4.4.3", "umi-mock-middleware": "^1.0.0", "vue-cli-plugin-windicss": "~1.1.3", "vue-eslint-parser": "^7.4.0", "vue-loader": "^16.8.3", "webpack-bundle-analyzer": "^3.8.0"}, "husky": {"hooks": {"pre-commit": "lint-staged && pretty-quick --staged"}}, "lint-staged": {"*.{js,jsx,vue,ts,tsx}": ["vue-cli-service lint", "git add"]}}