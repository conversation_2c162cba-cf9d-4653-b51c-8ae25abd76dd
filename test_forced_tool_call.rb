puts "=== 测试强制工具调用机制 ==="

begin
  agent = Bot::Agent.first
  user = User.first
  
  query = "素材数量？"
  puts "测试查询: #{query}"
  
  # 1. 测试查询分类
  query_type = Bot::QueryClassifier.classify(query)
  puts "分类结果:"
  puts "  类型: #{query_type.type}"
  puts "  置信度: #{query_type.confidence}"
  puts "  阈值: #{query_type.config[:confidence_threshold]}"
  puts "  应该强制调用: #{query_type.should_force_tool_call?}"
  
  # 2. 测试预处理器
  puts "\n测试预处理器:"
  result = Bot::QueryPreprocessor.process(query, agent: agent, user: user, conversation: nil)
  puts "预处理结果: #{result[:preprocessing_info]}"
  
rescue StandardError => e
  puts "错误: #{e.message}"
  puts e.backtrace.first(5)
end
