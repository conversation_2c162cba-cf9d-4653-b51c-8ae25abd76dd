---
description: 
globs: .vue,.ts
alwaysApply: false
---
---
description: Vue 3 IEST项目代码规范
globs: "**/*.vue"
alwaysApply: true
---

# Vue 3 代码规范

## 1. 组件结构规范

### 1.1 组件文件命名
- 全局通用组件以`Com`为前缀，例如：`ComUserSelector.vue`
- 特定领域组件以领域名称为前缀，例如：`TaInput.vue`，`IestOrgTree.vue`
- 文件名使用大驼峰命名法（PascalCase）

### 1.2 基础组件结构
```vue
<script lang="ts">
// 导入语句
import { defineComponent, ref, computed, toRefs } from 'vue';
import { PropType } from 'vue';

// 组件定义
const ComponentName = defineComponent({
  name: 'ComponentName',          // 必须与文件名保持一致
  components: {},                 // 子组件注册
  props: {},                      // 属性定义
  emits: [],                      // 事件定义
  setup(props, { emit }) {        // 组合式API
    // 数据处理逻辑

    // 返回模板所需数据和方法
    return {
      ...toRefs(props),
      // 其他数据和方法
    };
  },
});
export default ComponentName;
</script>

<template lang="pug">
// 使用Pug模板语法
</template>

<style lang="stylus" scoped>
// 使用Stylus样式语法
</style>
```

### 1.3 组件属性顺序
1. name - 组件名称
2. components - 子组件注册
3. props - 属性定义
4. emits - 事件定义
5. setup - 组合式API入口

## 2. 类型系统规范

### 2.1 Props类型定义
```typescript
props: {
  value: { type: String, default: '' },                        // 简单类型
  disabled: { type: Boolean, default: false },                 // 布尔类型
  item: { type: Object as PropType<TItem>, default: () => ({}) }, // 对象类型带类型定义
  list: { type: Array as PropType<string[]>, default: () => [] },  // 数组类型带类型定义
  size: { type: String as PropType<'small'|'regular'|'large'>, default: 'regular' }, // 带枚举的类型
}
```

### 2.2 接口和类型定义
```typescript
// 接口命名使用大驼峰，以I为前缀
interface IUser {
  id: number;
  name: string;
  age?: number;  // 可选属性
}

// 组件属性类型
interface TaTemplateFormItem {
  name: string;
  type: string;
  rules: Rule[];
  model: { attr_type: string };
  options: Record<string, any>;
  key: string;
  model_key: string;
  fields: TaTemplateFormItem[];
}

// 枚举类型
type FormItemSize = 'small' | 'regular' | 'large';
```

## 3. 组合式API最佳实践

### 3.1 响应式数据定义
```typescript
// ref 用于简单数据类型
const count = ref(0);
const message = ref('');
const flag = ref(true);

// reactive 用于复杂对象
const state = reactive({
  form: { name: '', age: 0 },
  loadingStatus: false
});

// computed 计算属性
const fullName = computed(() => `${firstName.value} ${lastName.value}`);

// 双向绑定计算属性
const localValue = computed({
  get: () => props.value,
  set: val => {
    emit('update:value', val);
  },
});
```

### 3.2 事件处理
```typescript
// 定义事件
emits: ['update:value', 'syncValue', 'change'],

// 触发事件
const handleChange = () => {
  emit('change', value.value);
};

// 失焦事件处理
const onBlur = () => {
  nextTick(() => {
    emit('syncValue', props.item, localValue.value);
  });
};
```

### 3.3 生命周期钩子
```typescript
// 在setup中使用生命周期钩子
import { onMounted, onUpdated, onUnmounted } from 'vue';

setup() {
  onMounted(() => {
    // 组件挂载后执行
  });

  onUpdated(() => {
    // 组件更新后执行
  });

  onUnmounted(() => {
    // 组件卸载前执行
  });
}
```

### 3.4 自定义组合函数
```typescript
// src/composables/useForm.ts
export function useFormState() {
  const formData = reactive({});
  const formErrors = reactive({});

  const setFormData = (field, value) => {
    formData[field] = value;
  };

  const validate = () => {
    // 表单验证逻辑
  };

  return {
    formData,
    formErrors,
    setFormData,
    validate
  };
}

// 在组件中使用
import { useFormState } from '@/composables/useForm';

setup() {
  const { formData, validate } = useFormState();
  return { formData, validate };
}
```

## 4. 模板语法规范

### 4.1 Pug模板语法规范
```pug
// 基本元素
div.class-name
  span 文本内容

// 绑定属性
button.btn-primary(
  :class="{'is-active': isActive}"
  @click="handleClick"
) 按钮文本

// 条件渲染
div(v-if="isVisible")
div(v-else)

// 列表渲染
ul
  li(v-for="item in items" :key="item.id") {{item.name}}

// 组件使用
ComIestOrgTree.mr-4.transition-all(
  :store='orgStore'
  :class='{"basis-1/4": display,"basis-0":!display}',
  @select='onSelect',
  :display='display',
  @display='toggleDisplay'
)
```

### 4.2 动态类名规范
```pug
// 条件类名
div(
  :class="[
    'base-class',
    { 'active-class': isActive },
    disabled ? 'disabled-class' : ''
  ]"
)

// 使用计算属性
div(:class="computedClass")

// 结合TailwindCSS
div(
  class="flex items-center"
  :class="[
    size === 'small' ? 'text-sm' : 'text-base',
    { 'opacity-50': disabled }
  ]"
)
```

## 5. WindiCSS/TailwindCSS 规范

### 5.1 基础用法
```pug
// 布局类
.flex.flex-col.justify-between.items-center

// 间距类
.m-4.p-2.gap-4

// 尺寸类
.w-full.h-screen.max-w-md

// 颜色类
.bg-primary-800.text-white.hover:bg-primary-700

// 响应式类
.lg:flex-row.md:w-1/2.sm:p-4
```

### 5.2 主题色结合使用
```pug
// 使用主题色变量
button.bg-primary-800.hover:bg-primary-700

// 动态主题
div(
  :class="[
    isDark ? 'dark-theme' : 'light-theme',
    `text-${themeColor}`
  ]"
)
```

### 5.3 自定义组件
```javascript
// TailwindCSS插件自定义组件
plugins: [
  plugin(function ({ addComponents, theme }) {
    addComponents({
      '.iest-card-container': {
        boxShadow: theme('boxShadow.sm'),
        borderRadius: theme('borderRadius.lg'),
        backgroundColor: theme('colors.white'),
        padding: theme('padding.4'),
      },
    });
  }),
]
```

## 6. 主题色规范

### 6.1 主题色系统定义
```css
/* 在src/engines/base/base-core/stylus/index.styl中定义 */
$white = #fff
$black = #000
$primaryColor = #3DA8F5
$warningColor = #f29851
$successColor = #52c41a
$dangerColor = #f5222d
$infoColor = #999999
$blackColor = #333333
$darkColor = #5C6C8F

/* TailwindCSS主题色 */
theme: {
  colors: {
    primary: {
      900: '#233876',
      800: 'rgba(30, 66, 159, 1)',
      700: '#1A56DB',
      300: '#A4CAFE',
      200: '#C3DDFD',
    },
    screen: '#011848',
    'screen-transparent': 'rgba(255, 255, 255, 0.10)',
  }
}
```

### 6.2 主题色使用规范
```vue
<template lang="pug">
/* 推荐用法 */
button.btn-primary(
  class="hover:bg-[var(--primary-hover)]"
  :style="{color: textColor}"
)

/* 备选方案 */
button(
  :class="[
    `text-${themeColor}`,
    isDark ? 'dark-theme' : ''
  ]"
)
</template>

<style scoped>
/* 在CSS中使用 */
.btn-primary {
  background: var(--primary-color);
  &:hover {
    background: var(--primary-hover);
  }
}
</style>
```

### 6.3 暗色主题配置
```javascript
// 暗色模式配置
darkMode: ['selector', '[data-mode="dark"]'],
antdvThemeDarkVariables: {
  'text-color': 'fade(@white, 65%)',
  'disabled-color': '#A4CAFE',
  'component-background': 'rgba(30, 66, 159, 1)',
  'body-background': '#233876',
  'modal-content-bg': 'white',
  // 更多暗色主题变量...
}
```

## 7. 样式作用域规范

### 7.1 Stylus使用规范
```stylus
// 在<style lang="stylus" scoped>中使用

// 嵌套选择器
.component
  display flex
  width 100%

  &:hover
    background-color $primaryColor

  .child-element
    padding 10px
```

### 7.2 CSS变量使用
```css
/* 全局CSS变量 */
:root {
  --primary-color: #3DA8F5;
  --warning-color: #f29851;
}

/* 组件中使用 */
.component {
  color: var(--primary-color);
  border-color: var(--warning-color);
}
```

### 7.3 混合样式方案
```vue
<template lang="pug">
.flex.items-center.custom-component
  span.text-primary-800 文本内容
</template>

<style lang="stylus" scoped>
.custom-component
  background-color $primaryColor

  &:hover
    opacity 0.8
</style>
```

## 8. 项目特定规则

### 8.1 组件前缀规范
Com - 通用组件前缀
Ta  - 表单相关组件前缀
Iest - IEST业务组件前缀
Res - 资源业务组件前缀
Bpm - 流程业务组件前缀

### 8.2 API调用规范
```typescript
// 使用VStore封装API
import { VStore } from '@/lib/vails';
import { SomeApi } from '@/engines/domain/apis/some.api';
import { SomeModel } from '@/engines/domain/models/some.model';

setup() {
  const store = new VStore(new SomeApi(), SomeModel);

  // 获取列表
  const loadData = async () => {
    await store.index({ params: { page: 1,per_page:15 } });
  };

  // 创建
  const createItem = async (data) => {
    await store.create(data);
  };

  onMounted(loadData);

  return { store, createItem };
}
```

### 8.3 调试规范
```typescript
// 添加调试输出
const debug = (...args) => {
  if (process.env.NODE_ENV !== 'production') {
    console.log('[ComponentName]', ...args);
  }
};

// 监听状态变化
watch(someState, (newVal, oldVal) => {
  debug('状态变更', { oldVal, newVal });
});
```
