#!/usr/bin/env ruby

# 简单的重构验证脚本
puts "=== ActivityQueryTool 重构验证 ==="

# 加载必要的文件
require_relative 'app/services/bot/tools/activity_query_config'

begin
  # 测试1: 配置类基本功能
  puts "\n1. 测试配置类基本功能..."
  config = Bot::Tools::ActivityQueryConfig.new({}, validate_model_existence: false)
  puts "✅ 配置类创建成功"
  
  # 测试配置项访问
  puts "   默认模型类: #{config.model_class}"
  puts "   默认artifact类型: #{config.artifact_type}"
  puts "   时间字段: #{config.time_fields.join(', ')}"
  
  # 测试2: 类型映射功能
  puts "\n2. 测试类型映射功能..."
  test_queries = ['有多少文章', '图片数量', '视频素材', '漫画内容']
  test_queries.each do |query|
    type = config.extract_content_type_from_query(query)
    puts "   查询: '#{query}' -> 类型: #{type || '未识别'}"
  end
  
  # 测试3: 状态映射功能
  puts "\n3. 测试状态映射功能..."
  test_states = ['published', 'pending', 'draft', 'unknown']
  test_states.each do |state|
    desc = config.get_state_description(state)
    puts "   状态: '#{state}' -> 描述: #{desc}"
  end
  
  # 测试4: 时间字段描述
  puts "\n4. 测试时间字段描述..."
  config.time_fields.each do |field|
    desc = config.get_time_field_description(field)
    puts "   字段: '#{field}' -> 描述: #{desc}"
  end
  
  # 测试5: 自定义配置
  puts "\n5. 测试自定义配置..."
  custom_config = Bot::Tools::ActivityQueryConfig.new({
    content_type_mapping: {
      '文档' => '文档类型',
      '音频' => '音频类型'
    },
    state_mapping: {
      'active' => '激活',
      'inactive' => '停用'
    }
  }, validate_model_existence: false)
  
  puts "   自定义类型映射: #{custom_config.extract_content_type_from_query('文档数量')}"
  puts "   自定义状态映射: #{custom_config.get_state_description('active')}"
  
  puts "\n🎉 所有测试通过！重构成功！"
  
rescue => e
  puts "\n❌ 测试失败: #{e.message}"
  puts e.backtrace.first(5).join("\n")
end

puts "\n=== 重构优势总结 ==="
puts "✅ 消除硬编码: 所有映射关系都可配置"
puts "✅ 提升灵活性: 支持自定义配置"
puts "✅ 增强可维护性: 配置集中管理"
puts "✅ 保持兼容性: 默认行为不变"
puts "✅ 支持扩展: 易于添加新的映射类型"
