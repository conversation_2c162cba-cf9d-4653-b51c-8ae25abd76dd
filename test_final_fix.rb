puts '=== 测试最终修复效果 ==='
begin
  agent = Bot::Agent.first
  user = User.first

  # 设置Bot::Current.conversation
  conversation = agent.conversations.first || agent.conversations.create!(user: user)
  Bot::Current.conversation = conversation

  puts "Agent: #{agent.name}"
  puts "User: #{user.name}"
  puts "Conversation: #{conversation.id}"

  # 创建Bot::Assistant实例
  puts "\n=== 创建Bot::Assistant实例 ==="
  assistant = agent.initialize_assistant(conversation_id: conversation.id)

  # 测试chat方法
  puts "\n=== 测试chat方法 ==="
  response = assistant.chat('过去一年里有多少素材？', conversation: conversation)

  puts "响应类型: #{response.class}"
  puts "响应keys: #{response.keys}"

  # 检查消息
  if response[:messages]
    puts "\n=== 检查消息 ==="
    response[:messages].each_with_index do |msg, index|
      puts "消息 #{index + 1}:"
      puts "  role: #{msg[:role]}"
      puts "  content: #{msg[:content][0..100] if msg[:content]}"

      next unless msg[:tool_calls]

      puts '  ✅ 包含工具调用!'
      msg[:tool_calls].each do |call|
        puts "    工具: #{call['function']['name']}"
      end
    end
  end

  # 检查artifacts（在messages中）
  artifact_messages = response[:messages].select { |msg| msg[:content_type] == 'artifact' }

  if artifact_messages.any?
    puts "\n=== 检查Artifacts ==="
    puts "Artifacts数量: #{artifact_messages.count}"
    artifact_messages.each_with_index do |msg, index|
      artifact = msg[:content]
      puts "Artifact #{index + 1}:"
      puts "  ID: #{artifact['id']}"
      puts "  Type: #{artifact['type']}"
      puts "  Tool: #{artifact['tool_cname']}"
      puts "  Function: #{artifact['tool_function']}"
      puts "  Meta: #{artifact['meta'] ? '存在' : '不存在'}"
    end
  else
    puts "\n❌ 没有Artifacts"
  end

  # 检查最终结果
  puts "\n=== 最终结果 ==="
  if artifact_messages.any?
    puts '🎉 成功！Artifact已创建，ComActivityListTool组件应该会显示！'
  else
    puts '❌ 失败！没有创建Artifact，ComActivityListTool组件不会显示。'
  end
rescue StandardError => e
  puts "❌ 失败: #{e.message}"
  puts e.backtrace.first(10)
ensure
  Bot::Current.conversation = nil
end
