puts '=== 测试Langchain工具调用流程 ==='
begin
  agent = Bot::Agent.first
  intent = agent.intents.find_by(name: '素材查询')

  # 设置Bot::Current.conversation
  conversation = agent.conversations.first || agent.conversations.create!(user: User.first)
  Bot::Current.conversation = conversation

  puts "Intent: #{intent.name}"
  puts "Conversation: #{conversation.id}"

  # 创建工具
  activity_tool = Bot::Tools::ActivityQueryTool.new(intent: intent)

  # 测试工具的function_schemas
  puts "\n=== 检查工具的function_schemas ==="
  schemas = activity_tool.function_schemas
  puts "Schemas类型: #{schemas.class}"
  puts "Schemas: #{schemas.inspect[0..200]}"

  # 创建Langchain::Assistant
  puts "\n=== 创建Langchain::Assistant ==="
  assistant = Langchain::Assistant.new(
    llm: agent.send(:default_llm),
    tools: [activity_tool],
    instructions: '你是一个AI助手',
    tool_choice: 'auto'
  )

  puts 'Assistant创建成功'
  puts "Tools: #{assistant.tools.count}个"

  # 重写工具的call方法来添加调试
  original_call = activity_tool.method(:call)
  activity_tool.define_singleton_method(:call) do |function_name:, arguments:|
    puts '🔍 工具被调用:'
    puts "  函数: #{function_name}"
    puts "  参数: #{arguments}"

    result = original_call.call(function_name: function_name, arguments: arguments)

    puts '🔍 工具返回结果:'
    puts "  类型: #{result.class}"
    puts "  内容: #{result.inspect[0..200]}"

    result
  end

  # 执行对话
  puts "\n=== 执行对话 ==="
  response = assistant.add_message_and_run!(content: '过去一年里有多少素材？')

  puts "\n=== 检查响应 ==="
  puts "响应类型: #{response.class}"
  puts "响应内容: #{response.content[0..200]}"

  # 检查消息历史
  puts "\n=== 检查消息历史 ==="
  assistant.messages.each_with_index do |msg, index|
    puts "消息 #{index + 1}: role=#{msg.role}"
    if msg.tool_calls.present?
      puts '  ✅ 包含工具调用!'
      msg.tool_calls.each do |call|
        puts "    工具: #{call['function']['name']}"
        puts "    参数: #{call['function']['arguments']}"
      end
    elsif msg.tool_call_id.present?
      puts '  ✅ 工具响应消息!'
      puts "    tool_call_id: #{msg.tool_call_id}"
      puts "    content: #{msg.content[0..200]}"
    else
      puts "  内容: #{msg.content[0..100]}"
    end
  end
rescue StandardError => e
  puts "❌ 失败: #{e.message}"
  puts e.backtrace.first(10)
ensure
  Bot::Current.conversation = nil
end
