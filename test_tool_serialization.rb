puts '=== 测试工具响应序列化 ==='
begin
  agent = Bot::Agent.first
  intent = agent.intents.find_by(name: '素材查询')
  
  activity_tool = Bot::Tools::ActivityQueryTool.new(intent: intent)
  
  # 设置Bot::Current.conversation
  conversation = agent.conversations.first || agent.conversations.create!(user: User.first)
  Bot::Current.conversation = conversation
  
  puts "Intent: #{intent.name}"
  puts "Conversation: #{conversation.id}"
  
  # 执行工具调用
  puts "\n=== 执行工具调用 ==="
  result = activity_tool.send(:query_activities, query: '过去一年里有多少素材？')
  
  puts "工具返回结果:"
  puts "  类型: #{result.class}"
  puts "  Keys: #{result.keys}"
  puts "  Data: #{result[:data].class}"
  puts "  Artifact: #{result[:artifact].class}"
  
  # 测试JSON序列化
  puts "\n=== 测试JSON序列化 ==="
  begin
    json_result = result.to_json
    puts "✅ JSON序列化成功"
    puts "JSON长度: #{json_result.length}"
    puts "JSON前100字符: #{json_result[0..100]}"
    
    # 检查artifact在JSON中的表示
    if json_result.include?('"artifact":null')
      puts "❌ JSON中artifact为null"
    elsif json_result.include?('"artifact":{')
      puts "✅ JSON中artifact为对象"
    else
      puts "⚠️ JSON中artifact状态未知"
    end
    
    # 尝试解析JSON
    parsed = JSON.parse(json_result, symbolize_names: true)
    puts "解析后artifact: #{parsed[:artifact].class}"
    
  rescue => e
    puts "❌ JSON序列化失败: #{e.message}"
  end
  
  # 测试artifact的as_json方法
  puts "\n=== 测试artifact的as_json ==="
  if result[:artifact]
    artifact = result[:artifact]
    puts "Artifact类型: #{artifact.class}"
    
    begin
      artifact_json = artifact.as_json
      puts "✅ artifact.as_json成功"
      puts "as_json类型: #{artifact_json.class}"
      puts "as_json keys: #{artifact_json.keys if artifact_json.is_a?(Hash)}"
    rescue => e
      puts "❌ artifact.as_json失败: #{e.message}"
    end
    
    begin
      artifact_to_json = artifact.to_json
      puts "✅ artifact.to_json成功"
      puts "to_json长度: #{artifact_to_json.length}"
    rescue => e
      puts "❌ artifact.to_json失败: #{e.message}"
    end
  end
  
rescue StandardError => e
  puts "❌ 失败: #{e.message}"
  puts e.backtrace.first(5)
ensure
  Bot::Current.conversation = nil
end
