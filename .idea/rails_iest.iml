<?xml version="1.0" encoding="UTF-8"?>
<module type="RUBY_MODULE" version="4">
  <component name="FacetManager">
    <facet type="RailsFacetType" name="Ruby on Rails">
      <configuration>
        <RAILS_FACET_CONFIG_ID NAME="RAILS_FACET_SUPPORT_REMOVED" VALUE="false" />
        <RAILS_FACET_CONFIG_ID NAME="RAILS_TESTS_SOURCES_PATCHED" VALUE="true" />
        <RAILS_FACET_CONFIG_ID NAME="RAILS_FACET_APPLICATION_ROOT" VALUE="$MODULE_DIR$" />
      </configuration>
    </facet>
  </component>
  <component name="ModuleRunConfigurationManager">
    <shared />
  </component>
  <component name="NewModuleRootManager">
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/features" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/spec" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/test" isTestSource="true" />
      <excludeFolder url="file://$MODULE_DIR$/.bundle" />
      <excludeFolder url="file://$MODULE_DIR$/public/packs" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/bundle" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/cache" />
      <excludeFolder url="file://$MODULE_DIR$/log" />
      <excludeFolder url="file://$MODULE_DIR$/tmp" />
      <excludeFolder url="file://$MODULE_DIR$/public/system" />
      <excludeFolder url="file://$MODULE_DIR$/components" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="library" scope="PROVIDED" name="aasm (v5.5.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="actioncable (v7.1.5.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="actionmailbox (v7.1.5.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="actionmailer (v7.1.5.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="actionpack (v7.1.5.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="actiontext (v7.1.5.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="actionview (v7.1.5.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="active_record_extended (v3.3.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="activejob (v7.1.5.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="activemodel (v7.1.5.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="activerecord (v7.1.5.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="activestorage (v7.1.5.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="activesupport (v7.1.5.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="acts_as_commentable_with_threading (v2.0.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="acts_as_list (v1.2.4, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="addressable (v2.8.7, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="aliyunsdkcore (v0.0.17, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ancestry (v4.3.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="annotate (v3.2.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ast (v2.4.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="attr_json (v2.5.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="awesome_nested_set (v3.8.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="awesome_print (v1.9.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="aws-eventstream (v1.3.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="aws-partitions (v1.1054.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="aws-sdk-core (v3.219.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="aws-sdk-kms (v1.99.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="aws-sdk-s3 (v1.182.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="aws-sigv4 (v1.11.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="baran (v0.1.12, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="base64 (v0.2.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="benchmark (v0.4.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="better_errors (v2.10.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="bigdecimal (v3.1.9, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="binding_of_caller (v1.0.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="builder (v3.3.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="bundler (v2.5.16, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="byebug (v11.1.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="caxlsx (v4.2.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="closure_tree (v7.4.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="coderay (v1.1.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="concurrent-ruby (v1.3.5, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="connection_pool (v2.5.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="content_disposition (v1.0.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="crass (v1.0.6, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="csv (v3.3.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="date (v3.4.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="debug_inspector (v1.2.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="deep_cloneable (v3.2.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="diff-lcs (v1.6.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="domain_name (v0.6.20240107, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="dotenv (v3.1.7, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="dotenv-rails (v3.1.7, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="down (v5.4.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="drb (v2.2.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="erubi (v1.13.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ethon (v0.16.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="event_stream_parser (v1.0.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="eventmachine (v1.2.7, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="execjs (v2.10.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="factory_bot (v6.5.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="factory_bot_rails (v6.4.4, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="faraday (v2.12.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="faraday-multipart (v1.1.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="faraday-net_http (v3.4.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="faye-websocket (v0.11.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ffi (v1.17.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ffi-compiler (v1.3.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="globalid (v1.2.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="groupdate (v6.5.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="has_scope (v0.8.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="hirb (v0.7.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="hiredis (v0.6.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="htmlentities (v4.3.4, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="http (v5.2.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="http-accept (v1.7.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="http-cookie (v1.0.8, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="http-form_data (v2.3.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="i18n (v1.14.7, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="inherited_resources (v2.1.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="io-console (v0.8.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="irb (v1.15.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="jbuilder (v2.13.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="jmespath (v1.6.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="json (v2.10.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="json-schema (v4.3.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="langchainrb (v0.19.4, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="language_server-protocol (v3.17.0.4, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="lint_roller (v1.1.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="llhttp-ffi (v0.5.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="logger (v1.6.6, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="loofah (v2.24.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="mail (v2.8.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="marcel (v1.0.4, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="matrix (v0.4.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="method_source (v1.1.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="mime-types (v3.6.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="mime-types-data (v3.2025.0220, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="mini_mime (v1.1.5, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="mini_portile2 (v2.8.8, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="minitest (v5.25.4, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="multi_json (v1.15.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="multipart-post (v2.4.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="mutex_m (v0.3.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="neighbor (v0.5.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="nest (v3.2.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="net-http (v0.6.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="net-imap (v0.5.6, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="net-pop (v0.1.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="net-protocol (v0.2.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="net-smtp (v0.5.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="netrc (v0.11.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="nilify_blanks (v1.4.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="nio4r (v2.7.4, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="nokogiri (v1.18.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ohm (v3.1.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="paper_trail (v16.0.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="parallel (v1.26.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="paranoia (v3.0.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="parser (v3.3.7.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="pg (v1.5.9, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="pluck_all (v2.3.4, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="pp (v0.6.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="pragmatic_segmenter (v0.3.24, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="prettyprint (v0.2.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="pry (v0.14.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="pry-byebug (v3.10.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="pry-doc (v1.5.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="pry-rails (v0.3.11, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="psych (v5.2.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="public_suffix (v6.0.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="puma (v6.6.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="pundit (v2.4.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="racc (v1.8.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rack (v3.1.10, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rack-cors (v2.0.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rack-session (v2.1.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rack-test (v2.2.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rackup (v2.2.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails (v7.1.5.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails-dom-testing (v2.2.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails-html-sanitizer (v1.6.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_action_core (v0.1.0@6c57d8, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_bot (v0.1.0@b14da4, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_bpm (v0.1.0@7a3449, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_chat (v0.1.0@e43f96, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_com (v0.1.0@643ff4, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_compatibility (v0.0.10, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_data (v0.1.0@27b5fb, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_dingtalk (v0.1.0@ba0bae, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_grant (v0.1.0@d587b9, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_notify (v0.1.0@d0ba17, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_permit (v0.1.0@4daec7, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_reg (v0.1.0@82da21, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_region (v0.1.0@1d5151, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_res (v0.1.0@c18ca7, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_rss (v0.1.0@187048, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_schedule (v0.1.0@078396, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_serve (v0.1.0@b46827, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_soa_auth (v1.0.0@3a71f9, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_tofu (v0.1.0@4e430b, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="railties (v7.1.5.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rainbow (v3.1.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rake (v13.2.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ransack (v4.3.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ransack-enum (v1.0.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rdoc (v6.12.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="redic (v1.5.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="redis (v5.4.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="redis-client (v0.23.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="redis-namespace (v1.11.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="redis-objects (v1.7.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="regexp_parser (v2.10.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="reline (v0.6.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="request_store (v1.7.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="responders (v3.1.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rest-client (v2.1.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rmmseg-cpp-new (v0.3.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="roda (v3.89.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rolify (v6.0.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="roo (v2.10.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="roo-xls (v1.2.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rouge (v4.5.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rspec-core (v3.13.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rspec-expectations (v3.13.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rspec-mocks (v3.13.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rspec-rails (v7.1.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rspec-rails-swagger (v0.1.4@c1b2bb, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rspec-support (v3.13.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rubocop (v1.72.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rubocop-ast (v1.38.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rubocop-minitest (v0.37.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rubocop-performance (v1.24.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rubocop-rails (v2.30.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rubocop-rails-omakase (v1.0.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ruby-ole (v1.2.13.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ruby-openai (v7.4.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ruby-pinyin (v0.5.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ruby-progressbar (v1.13.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ruby_lunardate (v0.1.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rubyzip (v2.4.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="sassc (v2.4.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="securerandom (v0.4.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="shoulda-matchers (v6.4.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="simple_controller (v1.1.0@3663f7, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="spreadsheet (v1.3.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="sprockets (v4.2.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="sprockets-rails (v3.5.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="sqlite3 (v2.6.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="stal (v0.3.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="streamio-ffmpeg (v3.0.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="stringio (v3.1.5, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="strip_attributes (v1.14.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ta_by_star (v4.1.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ta_deep_pluck (v1.3.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ta_default_value_for (v3.4.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ta_has_event (v1.0.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ta_ransack_mongo (v1.0.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="tallty_duck_record (v1.1.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="tallty_form (v1.0.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="tallty_import_export (v1.1.5, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="thor (v1.3.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="timeout (v0.4.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="typhoeus (v1.4.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="tzinfo (v2.0.6, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="unicode-display_width (v3.1.4, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="unicode-emoji (v4.0.4, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="uppy-s3_multipart (v1.2.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="uri (v1.0.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="uuidtools (v2.2.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="verbs (v3.1.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="websocket-driver (v0.7.7, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="websocket-extensions (v0.1.5, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="will_paginate (v4.0.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="with_advisory_lock (v5.1.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="yard (v0.9.37, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="zeitwerk (v2.7.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="zip-zip (v0.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="zip_tricks (v5.6.0, RVM: ruby-3.2.2) [gem]" level="application" />
  </component>
  <component name="RailsGeneratorsCache">
    <option name="generators">
      <list>
        <option value="active_record:migration" />
        <option value="active_record:model" />
        <option value="active_record:observer" />
        <option value="active_record:session_migration" />
        <option value="controller" />
        <option value="erb:controller" />
        <option value="erb:mailer" />
        <option value="erb:scaffold" />
        <option value="generator" />
        <option value="helper" />
        <option value="integration_test" />
        <option value="mailer" />
        <option value="metal" />
        <option value="migration" />
        <option value="model" />
        <option value="model_subclass" />
        <option value="observer" />
        <option value="performance_test" />
        <option value="plugin" />
        <option value="resource" />
        <option value="scaffold" />
        <option value="scaffold_controller" />
        <option value="session_migration" />
        <option value="stylesheets" />
        <option value="test_unit:controller" />
        <option value="test_unit:helper" />
        <option value="test_unit:integration" />
        <option value="test_unit:mailer" />
        <option value="test_unit:model" />
        <option value="test_unit:observer" />
        <option value="test_unit:performance" />
        <option value="test_unit:plugin" />
        <option value="test_unit:scaffold" />
      </list>
    </option>
    <option name="myGenerators">
      <list>
        <option value="active_record:migration" />
        <option value="active_record:model" />
        <option value="active_record:observer" />
        <option value="active_record:session_migration" />
        <option value="controller" />
        <option value="erb:controller" />
        <option value="erb:mailer" />
        <option value="erb:scaffold" />
        <option value="generator" />
        <option value="helper" />
        <option value="integration_test" />
        <option value="mailer" />
        <option value="metal" />
        <option value="migration" />
        <option value="model" />
        <option value="model_subclass" />
        <option value="observer" />
        <option value="performance_test" />
        <option value="plugin" />
        <option value="resource" />
        <option value="scaffold" />
        <option value="scaffold_controller" />
        <option value="session_migration" />
        <option value="stylesheets" />
        <option value="test_unit:controller" />
        <option value="test_unit:helper" />
        <option value="test_unit:integration" />
        <option value="test_unit:mailer" />
        <option value="test_unit:model" />
        <option value="test_unit:observer" />
        <option value="test_unit:performance" />
        <option value="test_unit:plugin" />
        <option value="test_unit:scaffold" />
      </list>
    </option>
  </component>
  <component name="RailsPaths" isManagedAutomatically="true">
    <entry key="app">
      <value>file://$MODULE_DIR$/app</value>
    </entry>
    <entry key="app/assets">
      <value>file://$MODULE_DIR$/app/assets</value>
    </entry>
    <entry key="app/channels">
      <value>file://$MODULE_DIR$/app/channels</value>
    </entry>
    <entry key="app/controllers">
      <value>file://$MODULE_DIR$/app/controllers</value>
    </entry>
    <entry key="app/helpers">
      <value>file://$MODULE_DIR$/app/helpers</value>
    </entry>
    <entry key="app/mailers">
      <value>file://$MODULE_DIR$/app/mailers</value>
    </entry>
    <entry key="app/models">
      <value>file://$MODULE_DIR$/app/models</value>
    </entry>
    <entry key="app/views">
      <value>file://$MODULE_DIR$/app/views</value>
    </entry>
    <entry key="config">
      <value>file://$MODULE_DIR$/config</value>
    </entry>
    <entry key="config/cable">
      <value>file://$MODULE_DIR$/config/cable.yml</value>
    </entry>
    <entry key="config/database">
      <value>file://$MODULE_DIR$/config/database.yml</value>
    </entry>
    <entry key="config/environment">
      <value>file://$MODULE_DIR$/config/environment.rb</value>
    </entry>
    <entry key="config/environments">
      <value>file://$MODULE_DIR$/config/environments</value>
    </entry>
    <entry key="config/initializers">
      <value>file://$MODULE_DIR$/config/initializers</value>
    </entry>
    <entry key="config/locales">
      <value>file://$MODULE_DIR$/config/locales</value>
    </entry>
    <entry key="config/routes">
      <value>file://$MODULE_DIR$/config/routes</value>
    </entry>
    <entry key="config/routes.rb">
      <value>file://$MODULE_DIR$/config/routes.rb</value>
    </entry>
    <entry key="config/secrets">
      <value>file://$MODULE_DIR$/config</value>
    </entry>
    <entry key="db">
      <value>file://$MODULE_DIR$/db</value>
    </entry>
    <entry key="db/migrate">
      <value>file://$MODULE_DIR$/db/migrate</value>
    </entry>
    <entry key="db/seeds.rb">
      <value>file://$MODULE_DIR$/db/seeds.rb</value>
    </entry>
    <entry key="lib">
      <value>file://$MODULE_DIR$/lib</value>
    </entry>
    <entry key="lib/assets">
      <value>file://$MODULE_DIR$/lib/assets</value>
    </entry>
    <entry key="lib/tasks">
      <value>file://$MODULE_DIR$/lib/tasks</value>
    </entry>
    <entry key="lib/templates">
      <value>file://$MODULE_DIR$/lib/templates</value>
    </entry>
    <entry key="log">
      <value>file://$MODULE_DIR$/log/development.log</value>
    </entry>
    <entry key="public">
      <value>file://$MODULE_DIR$/public</value>
    </entry>
    <entry key="public/javascripts">
      <value>file://$MODULE_DIR$/public/javascripts</value>
    </entry>
    <entry key="public/stylesheets">
      <value>file://$MODULE_DIR$/public/stylesheets</value>
    </entry>
    <entry key="tmp">
      <value>file://$MODULE_DIR$/tmp</value>
    </entry>
    <entry key="vendor">
      <value>file://$MODULE_DIR$/vendor</value>
    </entry>
    <entry key="vendor/assets">
      <value>file://$MODULE_DIR$/vendor/assets</value>
    </entry>
  </component>
  <component name="RakeTasksCache">
    <option name="myRootTask">
      <RakeTaskImpl id="rake">
        <subtasks>
          <RakeTaskImpl id="app">
            <subtasks>
              <RakeTaskImpl id="action_mailbox">
                <subtasks>
                  <RakeTaskImpl id="ingress">
                    <subtasks>
                      <RakeTaskImpl description="Relay an inbound email from Exim to Action Mailbox (URL and INGRESS_PASSWORD required)" fullCommand="app:action_mailbox:ingress:exim" id="exim" />
                      <RakeTaskImpl description="Relay an inbound email from Postfix to Action Mailbox (URL and INGRESS_PASSWORD required)" fullCommand="app:action_mailbox:ingress:postfix" id="postfix" />
                      <RakeTaskImpl description="Relay an inbound email from Qmail to Action Mailbox (URL and INGRESS_PASSWORD required)" fullCommand="app:action_mailbox:ingress:qmail" id="qmail" />
                      <RakeTaskImpl description="" fullCommand="app:action_mailbox:ingress:environment" id="environment" />
                    </subtasks>
                  </RakeTaskImpl>
                  <RakeTaskImpl description="Install Action Mailbox and its dependencies" fullCommand="app:action_mailbox:install" id="install" />
                  <RakeTaskImpl id="install">
                    <subtasks>
                      <RakeTaskImpl description="Copy migrations from action_mailbox to application" fullCommand="app:action_mailbox:install:migrations" id="migrations" />
                    </subtasks>
                  </RakeTaskImpl>
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="action_text">
                <subtasks>
                  <RakeTaskImpl description="Copy over the migration, stylesheet, and JavaScript files" fullCommand="app:action_text:install" id="install" />
                  <RakeTaskImpl id="install">
                    <subtasks>
                      <RakeTaskImpl description="Copy migrations from action_text to application" fullCommand="app:action_text:install:migrations" id="migrations" />
                    </subtasks>
                  </RakeTaskImpl>
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="active_storage">
                <subtasks>
                  <RakeTaskImpl description="Copy over the migration needed to the application" fullCommand="app:active_storage:install" id="install" />
                  <RakeTaskImpl id="install">
                    <subtasks>
                      <RakeTaskImpl description="" fullCommand="app:active_storage:install:migrations" id="migrations" />
                    </subtasks>
                  </RakeTaskImpl>
                  <RakeTaskImpl description="" fullCommand="app:active_storage:update" id="update" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="app">
                <subtasks>
                  <RakeTaskImpl description="Apply the template supplied by LOCATION=(/path/to/template) or URL" fullCommand="app:app:template" id="template" />
                  <RakeTaskImpl description="Update configs and some other initially generated files (or use just update:configs or update:bin)" fullCommand="app:app:update" id="update" />
                  <RakeTaskImpl id="templates">
                    <subtasks>
                      <RakeTaskImpl description="" fullCommand="app:app:templates:copy" id="copy" />
                    </subtasks>
                  </RakeTaskImpl>
                  <RakeTaskImpl id="update">
                    <subtasks>
                      <RakeTaskImpl description="" fullCommand="app:app:update:active_storage" id="active_storage" />
                      <RakeTaskImpl description="" fullCommand="app:app:update:bin" id="bin" />
                      <RakeTaskImpl description="" fullCommand="app:app:update:configs" id="configs" />
                      <RakeTaskImpl description="" fullCommand="app:app:update:upgrade_guide_info" id="upgrade_guide_info" />
                    </subtasks>
                  </RakeTaskImpl>
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="assets">
                <subtasks>
                  <RakeTaskImpl description="Remove old compiled assets" fullCommand="app:assets:clean[keep]" id="clean[keep]" />
                  <RakeTaskImpl description="Remove compiled assets" fullCommand="app:assets:clobber" id="clobber" />
                  <RakeTaskImpl description="Load asset compile environment" fullCommand="app:assets:environment" id="environment" />
                  <RakeTaskImpl description="Compile all the assets named in config.assets.precompile" fullCommand="app:assets:precompile" id="precompile" />
                  <RakeTaskImpl description="" fullCommand="app:assets:clean" id="clean" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="db">
                <subtasks>
                  <RakeTaskImpl description="Create the database from DATABASE_URL or config/database.yml for the current RAILS_ENV (use db:create:all to create all databases in the config). Without RAILS_ENV or when RAILS_ENV is development, it defaults to creating the development and test databases, except when DATABASE_URL is present" fullCommand="app:db:create" id="create" />
                  <RakeTaskImpl description="Drop the database from DATABASE_URL or config/database.yml for the current RAILS_ENV (use db:drop:all to drop all databases in the config). Without RAILS_ENV or when RAILS_ENV is development, it defaults to dropping the development and test databases, except when DATABASE_URL is present" fullCommand="app:db:drop" id="drop" />
                  <RakeTaskImpl id="encryption">
                    <subtasks>
                      <RakeTaskImpl description="Generate a set of keys for configuring Active Record encryption in a given environment" fullCommand="app:db:encryption:init" id="init" />
                    </subtasks>
                  </RakeTaskImpl>
                  <RakeTaskImpl id="environment">
                    <subtasks>
                      <RakeTaskImpl description="Set the environment value for the database" fullCommand="app:db:environment:set" id="set" />
                    </subtasks>
                  </RakeTaskImpl>
                  <RakeTaskImpl id="fixtures">
                    <subtasks>
                      <RakeTaskImpl description="Load fixtures into the current environment's database" fullCommand="app:db:fixtures:load" id="load" />
                      <RakeTaskImpl description="" fullCommand="app:db:fixtures:identify" id="identify" />
                    </subtasks>
                  </RakeTaskImpl>
                  <RakeTaskImpl description="Migrate the database (options: VERSION=x, VERBOSE=false, SCOPE=blog)" fullCommand="app:db:migrate" id="migrate" />
                  <RakeTaskImpl id="migrate">
                    <subtasks>
                      <RakeTaskImpl description="Run the &quot;down&quot; for a given migration VERSION" fullCommand="app:db:migrate:down" id="down" />
                      <RakeTaskImpl description="Roll back the database one migration and re-migrate up (options: STEP=x, VERSION=x)" fullCommand="app:db:migrate:redo" id="redo" />
                      <RakeTaskImpl description="Display status of migrations" fullCommand="app:db:migrate:status" id="status" />
                      <RakeTaskImpl description="Run the &quot;up&quot; for a given migration VERSION" fullCommand="app:db:migrate:up" id="up" />
                      <RakeTaskImpl description="" fullCommand="app:db:migrate:reset" id="reset" />
                    </subtasks>
                  </RakeTaskImpl>
                  <RakeTaskImpl description="Run setup if database does not exist, or run migrations if it does" fullCommand="app:db:prepare" id="prepare" />
                  <RakeTaskImpl description="Drop and recreate all databases from their schema for the current environment and load the seeds" fullCommand="app:db:reset" id="reset" />
                  <RakeTaskImpl description="Roll the schema back to the previous version (specify steps w/ STEP=n)" fullCommand="app:db:rollback" id="rollback" />
                  <RakeTaskImpl id="schema">
                    <subtasks>
                      <RakeTaskImpl id="cache">
                        <subtasks>
                          <RakeTaskImpl description="Clear a db/schema_cache.yml file" fullCommand="app:db:schema:cache:clear" id="clear" />
                          <RakeTaskImpl description="Create a db/schema_cache.yml file" fullCommand="app:db:schema:cache:dump" id="dump" />
                        </subtasks>
                      </RakeTaskImpl>
                      <RakeTaskImpl description="Create a database schema file (either db/schema.rb or db/structure.sql, depending on `ENV['SCHEMA_FORMAT']` or `config.active_record.schema_format`)" fullCommand="app:db:schema:dump" id="dump" />
                      <RakeTaskImpl description="Load a database schema file (either db/schema.rb or db/structure.sql, depending on `ENV['SCHEMA_FORMAT']` or `config.active_record.schema_format`) into the database" fullCommand="app:db:schema:load" id="load" />
                    </subtasks>
                  </RakeTaskImpl>
                  <RakeTaskImpl description="Load the seed data from db/seeds.rb" fullCommand="app:db:seed" id="seed" />
                  <RakeTaskImpl id="seed">
                    <subtasks>
                      <RakeTaskImpl description="Truncate tables of each database for current environment and load the seeds" fullCommand="app:db:seed:replant" id="replant" />
                    </subtasks>
                  </RakeTaskImpl>
                  <RakeTaskImpl description="Create all databases, load all schemas, and initialize with the seed data (use db:reset to also drop all databases first)" fullCommand="app:db:setup" id="setup" />
                  <RakeTaskImpl description="Retrieve the current schema version number" fullCommand="app:db:version" id="version" />
                  <RakeTaskImpl description="" fullCommand="app:db:_dump" id="_dump" />
                  <RakeTaskImpl description="" fullCommand="app:db:abort_if_pending_migrations" id="abort_if_pending_migrations" />
                  <RakeTaskImpl description="" fullCommand="app:db:charset" id="charset" />
                  <RakeTaskImpl description="" fullCommand="app:db:check_protected_environments" id="check_protected_environments" />
                  <RakeTaskImpl description="" fullCommand="app:db:collation" id="collation" />
                  <RakeTaskImpl id="create">
                    <subtasks>
                      <RakeTaskImpl description="" fullCommand="app:db:create:all" id="all" />
                    </subtasks>
                  </RakeTaskImpl>
                  <RakeTaskImpl id="drop">
                    <subtasks>
                      <RakeTaskImpl description="" fullCommand="app:db:drop:_unsafe" id="_unsafe" />
                      <RakeTaskImpl description="" fullCommand="app:db:drop:all" id="all" />
                    </subtasks>
                  </RakeTaskImpl>
                  <RakeTaskImpl description="" fullCommand="app:db:forward" id="forward" />
                  <RakeTaskImpl description="" fullCommand="app:db:load_config" id="load_config" />
                  <RakeTaskImpl description="" fullCommand="app:db:purge" id="purge" />
                  <RakeTaskImpl id="purge">
                    <subtasks>
                      <RakeTaskImpl description="" fullCommand="app:db:purge:all" id="all" />
                    </subtasks>
                  </RakeTaskImpl>
                  <RakeTaskImpl id="reset">
                    <subtasks>
                      <RakeTaskImpl description="" fullCommand="app:db:reset:all" id="all" />
                    </subtasks>
                  </RakeTaskImpl>
                  <RakeTaskImpl id="setup">
                    <subtasks>
                      <RakeTaskImpl description="" fullCommand="app:db:setup:all" id="all" />
                    </subtasks>
                  </RakeTaskImpl>
                  <RakeTaskImpl id="test">
                    <subtasks>
                      <RakeTaskImpl description="" fullCommand="app:db:test:load_schema" id="load_schema" />
                      <RakeTaskImpl description="" fullCommand="app:db:test:prepare" id="prepare" />
                      <RakeTaskImpl description="" fullCommand="app:db:test:purge" id="purge" />
                    </subtasks>
                  </RakeTaskImpl>
                  <RakeTaskImpl description="" fullCommand="app:db:truncate_all" id="truncate_all" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="log">
                <subtasks>
                  <RakeTaskImpl description="Truncate all/specified *.log files in log/ to zero bytes (specify which logs with LOGS=test,development)" fullCommand="app:log:clear" id="clear" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="permission">
                <subtasks>
                  <RakeTaskImpl description="Clean up permission cache" fullCommand="app:permission:cache_clean" id="cache_clean" />
                  <RakeTaskImpl description="migrate permission of default role" fullCommand="app:permission:export" id="export" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="rails_action_core_engine">
                <subtasks>
                  <RakeTaskImpl id="install">
                    <subtasks>
                      <RakeTaskImpl description="Copy migrations from rails_action_core_engine to application" fullCommand="app:rails_action_core_engine:install:migrations" id="migrations" />
                    </subtasks>
                  </RakeTaskImpl>
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="rails_bot_engine">
                <subtasks>
                  <RakeTaskImpl id="install">
                    <subtasks>
                      <RakeTaskImpl description="Copy migrations from rails_bot_engine to application" fullCommand="app:rails_bot_engine:install:migrations" id="migrations" />
                    </subtasks>
                  </RakeTaskImpl>
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="rails_bpm_engine">
                <subtasks>
                  <RakeTaskImpl id="install">
                    <subtasks>
                      <RakeTaskImpl description="Copy migrations from rails_bpm_engine to application" fullCommand="app:rails_bpm_engine:install:migrations" id="migrations" />
                    </subtasks>
                  </RakeTaskImpl>
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="rails_chat_engine">
                <subtasks>
                  <RakeTaskImpl id="install">
                    <subtasks>
                      <RakeTaskImpl description="Copy migrations from rails_chat_engine to application" fullCommand="app:rails_chat_engine:install:migrations" id="migrations" />
                    </subtasks>
                  </RakeTaskImpl>
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="rails_com_engine">
                <subtasks>
                  <RakeTaskImpl id="install">
                    <subtasks>
                      <RakeTaskImpl description="Copy migrations from rails_com_engine to application" fullCommand="app:rails_com_engine:install:migrations" id="migrations" />
                    </subtasks>
                  </RakeTaskImpl>
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="rails_data_engine">
                <subtasks>
                  <RakeTaskImpl id="install">
                    <subtasks>
                      <RakeTaskImpl description="Copy migrations from rails_data_engine to application" fullCommand="app:rails_data_engine:install:migrations" id="migrations" />
                    </subtasks>
                  </RakeTaskImpl>
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="rails_dingtalk_engine">
                <subtasks>
                  <RakeTaskImpl id="install">
                    <subtasks>
                      <RakeTaskImpl description="Copy migrations from rails_dingtalk_engine to application" fullCommand="app:rails_dingtalk_engine:install:migrations" id="migrations" />
                    </subtasks>
                  </RakeTaskImpl>
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="rails_grant_engine">
                <subtasks>
                  <RakeTaskImpl id="install">
                    <subtasks>
                      <RakeTaskImpl description="Copy migrations from rails_grant_engine to application" fullCommand="app:rails_grant_engine:install:migrations" id="migrations" />
                    </subtasks>
                  </RakeTaskImpl>
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="rails_iest_engine">
                <subtasks>
                  <RakeTaskImpl id="install">
                    <subtasks>
                      <RakeTaskImpl description="Copy migrations from rails_iest_engine to application" fullCommand="app:rails_iest_engine:install:migrations" id="migrations" />
                    </subtasks>
                  </RakeTaskImpl>
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="rails_notify_engine">
                <subtasks>
                  <RakeTaskImpl id="install">
                    <subtasks>
                      <RakeTaskImpl description="Copy migrations from rails_notify_engine to application" fullCommand="app:rails_notify_engine:install:migrations" id="migrations" />
                    </subtasks>
                  </RakeTaskImpl>
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="rails_permit_engine">
                <subtasks>
                  <RakeTaskImpl id="install">
                    <subtasks>
                      <RakeTaskImpl description="Copy migrations from rails_permit_engine to application" fullCommand="app:rails_permit_engine:install:migrations" id="migrations" />
                    </subtasks>
                  </RakeTaskImpl>
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="rails_reg_engine">
                <subtasks>
                  <RakeTaskImpl id="install">
                    <subtasks>
                      <RakeTaskImpl description="Copy migrations from rails_reg_engine to application" fullCommand="app:rails_reg_engine:install:migrations" id="migrations" />
                    </subtasks>
                  </RakeTaskImpl>
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="rails_region_engine">
                <subtasks>
                  <RakeTaskImpl id="install">
                    <subtasks>
                      <RakeTaskImpl description="Copy migrations from rails_region_engine to application" fullCommand="app:rails_region_engine:install:migrations" id="migrations" />
                    </subtasks>
                  </RakeTaskImpl>
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="rails_res_engine">
                <subtasks>
                  <RakeTaskImpl id="install">
                    <subtasks>
                      <RakeTaskImpl description="Copy migrations from rails_res_engine to application" fullCommand="app:rails_res_engine:install:migrations" id="migrations" />
                    </subtasks>
                  </RakeTaskImpl>
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="rails_schedule_engine">
                <subtasks>
                  <RakeTaskImpl id="install">
                    <subtasks>
                      <RakeTaskImpl description="Copy migrations from rails_schedule_engine to application" fullCommand="app:rails_schedule_engine:install:migrations" id="migrations" />
                    </subtasks>
                  </RakeTaskImpl>
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="rails_serve_engine">
                <subtasks>
                  <RakeTaskImpl id="install">
                    <subtasks>
                      <RakeTaskImpl description="Copy migrations from rails_serve_engine to application" fullCommand="app:rails_serve_engine:install:migrations" id="migrations" />
                    </subtasks>
                  </RakeTaskImpl>
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="rails_soa_auth_engine">
                <subtasks>
                  <RakeTaskImpl id="install">
                    <subtasks>
                      <RakeTaskImpl description="Copy migrations from rails_soa_auth_engine to application" fullCommand="app:rails_soa_auth_engine:install:migrations" id="migrations" />
                    </subtasks>
                  </RakeTaskImpl>
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="rails_tofu_engine">
                <subtasks>
                  <RakeTaskImpl id="install">
                    <subtasks>
                      <RakeTaskImpl description="Copy migrations from rails_tofu_engine to application" fullCommand="app:rails_tofu_engine:install:migrations" id="migrations" />
                    </subtasks>
                  </RakeTaskImpl>
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl description="Run all specs in spec directory (excluding plugin specs)" fullCommand="app:spec" id="spec" />
              <RakeTaskImpl id="spec">
                <subtasks>
                  <RakeTaskImpl description="Run the code examples in spec/models" fullCommand="app:spec:models" id="models" />
                  <RakeTaskImpl description="Run the code examples in spec/requests" fullCommand="app:spec:requests" id="requests" />
                  <RakeTaskImpl description="" fullCommand="app:spec:prepare" id="prepare" />
                  <RakeTaskImpl description="" fullCommand="app:spec:statsetup" id="statsetup" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl description="Regenerate Swagger docs" fullCommand="app:swagger" id="swagger" />
              <RakeTaskImpl id="time">
                <subtasks>
                  <RakeTaskImpl description="List all time zones, list by two-letter country code (`bin/rails time:zones[US]`), or list by UTC offset (`bin/rails time:zones[-8]`)" fullCommand="app:time:zones[country_or_offset]" id="zones[country_or_offset]" />
                  <RakeTaskImpl description="" fullCommand="app:time:zones" id="zones" />
                  <RakeTaskImpl id="zones">
                    <subtasks>
                      <RakeTaskImpl description="" fullCommand="app:time:zones:all" id="all" />
                      <RakeTaskImpl description="" fullCommand="app:time:zones:local" id="local" />
                      <RakeTaskImpl description="" fullCommand="app:time:zones:us" id="us" />
                    </subtasks>
                  </RakeTaskImpl>
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="tmp">
                <subtasks>
                  <RakeTaskImpl description="Clear cache, socket and screenshot files from tmp/ (narrow w/ tmp:cache:clear, tmp:sockets:clear, tmp:screenshots:clear)" fullCommand="app:tmp:clear" id="clear" />
                  <RakeTaskImpl description="Create tmp directories for cache, sockets, and pids" fullCommand="app:tmp:create" id="create" />
                  <RakeTaskImpl id="cache">
                    <subtasks>
                      <RakeTaskImpl description="" fullCommand="app:tmp:cache:clear" id="clear" />
                    </subtasks>
                  </RakeTaskImpl>
                  <RakeTaskImpl id="pids">
                    <subtasks>
                      <RakeTaskImpl description="" fullCommand="app:tmp:pids:clear" id="clear" />
                    </subtasks>
                  </RakeTaskImpl>
                  <RakeTaskImpl id="screenshots">
                    <subtasks>
                      <RakeTaskImpl description="" fullCommand="app:tmp:screenshots:clear" id="clear" />
                    </subtasks>
                  </RakeTaskImpl>
                  <RakeTaskImpl id="sockets">
                    <subtasks>
                      <RakeTaskImpl description="" fullCommand="app:tmp:sockets:clear" id="clear" />
                    </subtasks>
                  </RakeTaskImpl>
                  <RakeTaskImpl id="storage">
                    <subtasks>
                      <RakeTaskImpl description="" fullCommand="app:tmp:storage:clear" id="clear" />
                    </subtasks>
                  </RakeTaskImpl>
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl description="Update some initially generated files" fullCommand="app:update" id="update" />
              <RakeTaskImpl id="yarn">
                <subtasks>
                  <RakeTaskImpl description="Install all JavaScript dependencies as specified via Yarn" fullCommand="app:yarn:install" id="install" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="zeitwerk">
                <subtasks>
                  <RakeTaskImpl description="Check project structure for Zeitwerk compatibility" fullCommand="app:zeitwerk:check" id="check" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl description="" fullCommand="app:default" id="default" />
              <RakeTaskImpl description="" fullCommand="app:environment" id="environment" />
              <RakeTaskImpl id="railties">
                <subtasks>
                  <RakeTaskImpl id="install">
                    <subtasks>
                      <RakeTaskImpl description="" fullCommand="app:railties:install:migrations" id="migrations" />
                    </subtasks>
                  </RakeTaskImpl>
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl description="" fullCommand="app:stats" id="stats" />
              <RakeTaskImpl id="update">
                <subtasks>
                  <RakeTaskImpl description="" fullCommand="app:update:bin" id="bin" />
                </subtasks>
              </RakeTaskImpl>
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl description="Build rails_iest-0.1.0.gem into the pkg directory" fullCommand="build" id="build" />
          <RakeTaskImpl id="build">
            <subtasks>
              <RakeTaskImpl description="Generate SHA512 checksum of rails_iest-0.1.0.gem into the checksums directory" fullCommand="build:checksum" id="checksum" />
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl description="Remove any temporary products" fullCommand="clean" id="clean" />
          <RakeTaskImpl description="Remove any generated files" fullCommand="clobber" id="clobber" />
          <RakeTaskImpl id="db">
            <subtasks>
              <RakeTaskImpl description="Create the database from config/database.yml for the current Rails.env (use db:create:all to create all databases in the config)" fullCommand="db:create" id="create" />
              <RakeTaskImpl description="Drop the database for the current Rails.env (use db:drop:all to drop all databases)" fullCommand="db:drop" id="drop" />
              <RakeTaskImpl id="fixtures">
                <subtasks>
                  <RakeTaskImpl description="Load fixtures into the current environment's database" fullCommand="db:fixtures:load" id="load" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl description="Migrate the database (options: VERSION=x, VERBOSE=false)" fullCommand="db:migrate" id="migrate" />
              <RakeTaskImpl id="migrate">
                <subtasks>
                  <RakeTaskImpl description="Display status of migrations" fullCommand="db:migrate:status" id="status" />
                  <RakeTaskImpl description="" fullCommand="db:migrate:down" id="down" />
                  <RakeTaskImpl description="" fullCommand="db:migrate:redo" id="redo" />
                  <RakeTaskImpl description="" fullCommand="db:migrate:reset" id="reset" />
                  <RakeTaskImpl description="" fullCommand="db:migrate:up" id="up" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl description="Roll the schema back to the previous version (specify steps w/ STEP=n)" fullCommand="db:rollback" id="rollback" />
              <RakeTaskImpl id="schema">
                <subtasks>
                  <RakeTaskImpl description="Create a database schema file (either db/schema.rb or db/structure.sql, depending on `config.active_record.schema_format`)" fullCommand="db:schema:dump" id="dump" />
                  <RakeTaskImpl description="Load a schema.rb file into the database" fullCommand="db:schema:load" id="load" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl description="Load the seed data from db/seeds.rb" fullCommand="db:seed" id="seed" />
              <RakeTaskImpl description="Create the database, load the schema, and initialize with the seed data (use db:reset to also drop the database first)" fullCommand="db:setup" id="setup" />
              <RakeTaskImpl description="Retrieve the current schema version number" fullCommand="db:version" id="version" />
              <RakeTaskImpl id="create">
                <subtasks>
                  <RakeTaskImpl description="" fullCommand="db:create:all" id="all" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="drop">
                <subtasks>
                  <RakeTaskImpl description="" fullCommand="db:drop:all" id="all" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl description="" fullCommand="db:reset" id="reset" />
              <RakeTaskImpl id="test">
                <subtasks>
                  <RakeTaskImpl description="" fullCommand="db:test:prepare" id="prepare" />
                </subtasks>
              </RakeTaskImpl>
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl description="Build and install rails_iest-0.1.0.gem into system gems" fullCommand="install" id="install" />
          <RakeTaskImpl id="install">
            <subtasks>
              <RakeTaskImpl description="Build and install rails_iest-0.1.0.gem into system gems without network access" fullCommand="install:local" id="local" />
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl description="Create tag v0.1.0 and build and push rails_iest-0.1.0.gem to Set to 'http://mygemserver.com'" fullCommand="release[remote]" id="release[remote]" />
          <RakeTaskImpl description="Run all specs in spec directory (excluding plugin specs)" fullCommand="spec" id="spec" />
          <RakeTaskImpl description="Report code statistics (KLOCs, etc) from the application or engine" fullCommand="stats" id="stats" />
          <RakeTaskImpl description="" fullCommand="default" id="default" />
          <RakeTaskImpl description="" fullCommand="environment" id="environment" />
          <RakeTaskImpl description="" fullCommand="load_app" id="load_app" />
          <RakeTaskImpl description="" fullCommand="release" id="release" />
          <RakeTaskImpl id="release">
            <subtasks>
              <RakeTaskImpl description="" fullCommand="release:guard_clean" id="guard_clean" />
              <RakeTaskImpl description="" fullCommand="release:rubygem_push" id="rubygem_push" />
              <RakeTaskImpl description="" fullCommand="release:source_control_push" id="source_control_push" />
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl description="" fullCommand="tmp" id="tmp" />
          <RakeTaskImpl description="" fullCommand="tmp/cache" id="tmp/cache" />
          <RakeTaskImpl description="" fullCommand="tmp/cache/assets" id="tmp/cache/assets" />
          <RakeTaskImpl description="" fullCommand="tmp/pids" id="tmp/pids" />
          <RakeTaskImpl description="" fullCommand="tmp/sockets" id="tmp/sockets" />
        </subtasks>
      </RakeTaskImpl>
    </option>
  </component>
</module>