{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Schema for Cody settings in the Cody VSCode Extension.", "description": "This prevents invalid Cody specific configuration in the settings file.", "type": "object", "allOf": [{"$ref": "https://json.schemastore.org/package"}], "properties": {"cody.serverEndpoint": {"order": 1, "type": "string", "description": "URL to the Sourcegraph instance.", "examples": "https://example.sourcegraph.com", "markdownDeprecationMessage": "**Deprecated**: Please sign in via the UI instead. If you are already signed in, you can empty this field to remove this warning.", "deprecationMessage": "Deprecated: Please sign in via the UI instead."}, "cody.codebase": {"order": 2, "type": "string", "markdownDescription": "A Git repository URL to use instead of allowing <PERSON> to infer the Git repository from the workspace.", "examples": ["https://github.com/sourcegraph/cody", "ssh://**************/sourcegraph/cody"]}, "cody.customHeaders": {"order": 4, "type": "object", "markdownDescription": "Adds custom HTTP headers to all network requests to the Sourcegraph endpoint. Defining required headers here ensures requests are properly forwarded through intermediary proxy servers, which may mandate certain custom headers for internal or external communication.", "default": {}, "examples": [{"Cache-Control": "no-cache", "Proxy-Authenticate": "Basic"}]}, "cody.autocomplete.enabled": {"order": 5, "type": "boolean", "markdownDescription": "Enables code autocompletions.", "default": true}, "cody.autocomplete.triggerDelay": {"order": 5, "type": "number", "markdownDescription": "The trigger delay ensures a minimum wait time before showing autocomplete suggestions to avoid accidental autocomplete triggers.", "default": 0}, "cody.autocomplete.languages": {"order": 5, "type": "object", "markdownDescription": "Enables or disables code autocompletions for specified [language ids](https://code.visualstudio.com/docs/languages/identifiers). `\"*\"` is the default fallback if no language-specific setting is found.\n\nThe default setting: \n\n```json\n{\n  \"*\": true\n}\n```\n\nTo disable autocomplete for a given [language id](https://code.visualstudio.com/docs/languages/identifiers#_known-language-identifiers) set its value to `false`, for example:\n\n```json\n{\n  \"*\": true,\n  \"plaintext\": false\n}\n```", "default": {"*": true}, "examples": [{"*": true, "plaintext": false}]}, "cody.commandCodeLenses": {"order": 8, "type": "boolean", "markdownDescription": "Adds code lenses to current file for quick access to Cody commands.", "default": false}, "cody.chat.preInstruction": {"order": 6, "type": "string", "markdownDescription": "A custom instruction to be included at the start of all chat messages (e.g. \"Answer all my questions in Spanish.\")", "examples": ["Answer all my questions in Spanish."]}, "cody.chat.defaultLocation": {"order": 6, "type": "string", "enum": ["sticky", "sidebar", "editor"], "markdownDescription": "Controls where the Cody chat view opens when the user invokes the `Cody: New Chat` command, or the Alt+L and Alt+/ shortcuts.", "enumDescriptions": ["Opens in the last-activated view location, which is set whenever the user explicitly chooses to open chat in a given location", "Opens in the sidebar", "Opens in an editor panel"], "default": "sticky"}, "cody.edit.preInstruction": {"order": 7, "type": "string", "markdownDescription": "A custom instruction to be included at the end of all instructions for edit commands (e.g. \"Write all unit tests with Je<PERSON> instead of detected framework.\")", "examples": ["Write all unit tests with Je<PERSON> instead of detected framework."]}, "cody.codeActions.enabled": {"order": 11, "title": "Cody Code Actions", "type": "boolean", "markdownDescription": "Add Cody options to Quick Fix menus for fixing, explaining, documenting, and editing code.", "default": true}, "cody.commandHints.enabled": {"order": 12, "title": "Cody Command Hints", "type": "boolean", "markdownDescription": "Enable hints for Cody commands such as \"Opt+K to Edit\" or \"Opt+D to Document\"", "default": true}, "cody.experimental.tracing": {"order": 99, "type": "boolean", "markdownDescription": "Enable OpenTelemetry tracing", "default": false}, "cody.experimental.commitMessage": {"order": 99, "type": "boolean", "markdownDescription": "Enable commit message generation", "default": false}, "cody.experimental.minion.anthropicKey": {"order": 99, "type": "string", "default": ""}, "cody.experimental.noxide.enabled": {"order": 99, "type": "boolean", "default": true}, "cody.debug.verbose": {"order": 99, "type": "boolean", "markdownDescription": "Enables verbose debug output. Debug messages may contain more details if the invocation includes verbose information."}, "cody.debug.filter": {"order": 99, "type": "string", "markdownDescription": "Regular expression to filter debug output. If empty, defaults to '.*', which prints all messages."}, "cody.telemetry.level": {"order": 99, "type": "string", "enum": ["all", "off"], "enumDescriptions": ["Sends usage data and errors.", "Disables all extension telemetry."], "markdownDescription": "Controls the telemetry about <PERSON> usage and errors. See [Cody usage and privacy notice](https://about.sourcegraph.com/terms/cody-notice).", "default": "all"}, "cody.autocomplete.advanced.provider": {"type": "string", "default": "default", "enum": ["default", "experimental-ollama"], "enumDescriptions": ["Our recommended setup with the best balance of quality and latency. We continuously update this for optimal performance.", "Experimental support for Ollama users. Use `cody.autocomplete.experimental.ollamaOptions` to configure requests to Ollama server."], "markdownDescription": "The provider for code autocomplete. Users should rely on the `default` provider for the best experience. The underlying model can be found in the Cody output channel logs."}, "cody.autocomplete.completeSuggestWidgetSelection": {"type": "boolean", "default": true, "markdownDescription": "Autocomplete based on the currently selection in the suggest widget. Requires the VS Code user setting `editor.inlineSuggest.suppressSuggestions` set to true and will change it to true in user settings if it is not true."}, "cody.autocomplete.formatOnAccept": {"type": "boolean", "default": false, "markdownDescription": "Format completions on accept using [the default document formatter](https://code.visualstudio.com/docs/editor/codebasics#_formatting)."}, "cody.autocomplete.disableInsideComments": {"type": "boolean", "default": false, "markdownDescription": "Prevent autocomplete requests while inside code comments."}, "cody.experimental.foldingRanges": {"type": "string", "enum": ["lsp", "indentation-based"], "enumDescriptions": ["Use folding ranges that are enabled by default in VS Code, and are usually powered by LSP", "Use custom implementation of folding ranges that is indentation based. This is the implementation that is used by other Cody clients like the JetBrains plugin"], "markdownDescription": "Determines the algorithm <PERSON> uses to detect folding ranges. <PERSON> uses folding ranges for several features like the 'Document code' command", "default": "all"}, "cody.autocomplete.experimental.graphContext": {"type": "string", "default": null, "enum": [null, "tsc", "tsc-mixed"], "markdownDescription": "Use the code graph to retrieve context for autocomplete requests."}, "cody.autocomplete.experimental.fireworksOptions": {"type": "object", "markdownDescription": "Experimental options for the direct-Fireworks autocomplete provider.", "properties": {"url": {"type": "string", "description": "The URL of the Fireworks API.", "default": "https://api.fireworks.ai/inference/v1/completions"}, "token": {"type": "string", "description": "The access token of the Fireworks API."}, "model": {"type": "string", "description": "The model ID can be acquired from `firectl list deployments`", "default": "accounts/sourcegraph/models/starcoder2-7b"}, "parameters": {"type": "object", "description": "Parameters for querying the the model.", "properties": {"temperature": "number", "top_k": "number", "top_p": "number", "stop": {"type": "array", "default": [], "items": {"type": "string"}}}}}}, "cody.autocomplete.experimental.ollamaOptions": {"type": "object", "markdownDescription": "Options for the [Ollama](https://ollama.ai/) experimental autocomplete provider.", "default": {"url": "http://localhost:11434", "model": "deepseek-coder:6.7b-base-q4_K_M"}, "properties": {"url": {"type": "string", "description": "The URL of the Ollama API.", "default": "http://localhost:11434"}, "model": {"type": "string", "default": "deepseek-coder:6.7b-base-q4_K_M", "examples": ["codellama:7b-code", "codellama:13b-code", "deepseek-coder:6.7b-base-q4_K_M", "starcoder2:7b", "starcoder2:15b"]}, "parameters": {"type": "object", "description": "Parameters for how Ollama will run the model. See Ollama [PARAMETER documentation](https://github.com/jmorganca/ollama/blob/main/docs/api.md#generate-request-with-options).", "properties": {"num_ctx": "number", "temperature": "number", "top_k": "number", "top_p": "number"}}}}, "openctx.enable": {"type": "boolean", "markdownDescription": "Enable OpenCtx providers for Cody.", "default": true}, "openctx.providers": {"type": "object", "markdownDescription": "OpenCtx providers configuration.", "default": {}}, "cody.internal.unstable": {"order": 999, "type": "boolean", "markdownDescription": "[INTERNAL ONLY] Enable all unstable experimental features.", "default": false}, "cody.net.mode": {"type": "string", "enum": ["auto", "bypass", "vscode"], "enumDescriptions": ["Default behavior. If `cody.net.proxy.endpoint` is configured, bypassing is automatically enabled, otherwise direct is used.", "Bypass VSCode's network stack, using Cody's instead.", "Ignore Cody's network stack, including proxy settings, even if `cody.net.proxy.endpoint` is configured, and use VSCode's network stack instead."], "default": "auto", "markdownDescription": "[EXPERIMENTAL] Forcefully enable/disable bypassing VSCode's network stack. Bypassing is automatically enabled if `cody.net.proxy.endpoint` is configured. In some scenarios bypassing VSCode network settings could give some performance benefits and forces usage of <PERSON>'s proxy instead of one configured globally for VSCode."}, "cody.net.proxy.endpoint": {"markdownDescription": "The proxy service to use when proxying requests to the Sourcegraph instance. Supports HTTP(S), SOCKS 4/5, and UNIX domain sockets. When using HTTP(S) or SOCKS proxies, include the port number with a colon. For UNIX domain sockets, use either the full path or prefix with a tilde for a path in the OS user's home directory.\n\nExamples:\n- `https://localhost:7080`\n- `socks5://*******:1080`\n- `unix://~/cody-proxy.sock`", "type": "string", "default": "", "pattern": "^((http|https|socks|socks4|socks4a|socks5|socks5h)://[^:]+:\\d+|unix://(~|/|[a-zA-Z]:\\\\).+)?$", "examples": ["https://localhost:7080", "socks5://*******:1080", "unix://~/cody-proxy.sock"]}, "cody.net.proxy.skipCertValidation": {"description": "Whether to skip proxy server CA cert validation. Useful if the proxy server uses a self-signed certificate.", "type": "boolean", "default": false}, "cody.agentic.context": {"type": "object", "properties": {"shell": {"allow": "array", "block": "array"}}, "default": {"shell": {"allow": ["*"], "block": ["rm", "chmod", "shutdown", "history", "user", "sudo", "su", "passwd", "chown", "chgrp", "kill", "reboot", "poweroff", "init", "systemctl", "journalctl", "dmesg", "lsblk", "lsmod", "modprobe", "insmod", "rmmod", "lsusb", "lspci"]}}, "examples": {"shell": {"allow": ["git", "gh"], "block": ["history", "sudo", "rm"]}}, "markdownDescription": "Configures shell command execution for Cody Agent when terminal context is enabled (feature flag required). The 'allow' property can be either ['*'] for all commands or an array of allowed command prefixes. The 'block' property is an array of blocked commands that will be combined with built-in safety blocklist. Commands in the blocklist take precedence over allowed commands. When a command is executed, its output will be used as context. If no 'allow' list is specified, the feature will be disabled."}, "cody.net.proxy.cacert": {"markdownDescription": "Either the PEM-encoded CA certificate the proxy uses (replace newlines with `\\n`), or the path (absolute or tilde) to the file containing that certificate.\n\nExamples:\n- `~/.mitmproxy/mitmproxy-ca-cert.pem`\n- `-----B<PERSON>IN CERTIFICATE-----\\n...\\n-----END CERTIFICATE-----`", "type": "string", "default": "", "examples": ["~/.mitmproxy/mitmproxy-ca-cert.pem", "-----BEGIN CERTIFICATE-----\n...\n-----END CERTIFICATE-----"]}}}