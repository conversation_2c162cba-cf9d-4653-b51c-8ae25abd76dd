<?xml version="1.0" encoding="UTF-8"?>
<module type="RUBY_MODULE" version="4">
  <component name="FacetManager">
    <facet type="RailsFacetType" name="Ruby on Rails">
      <configuration>
        <RAILS_FACET_CONFIG_ID NAME="RAILS_FACET_SUPPORT_REMOVED" VALUE="false" />
        <RAILS_FACET_CONFIG_ID NAME="RAILS_TESTS_SOURCES_PATCHED" VALUE="true" />
        <RAILS_FACET_CONFIG_ID NAME="RAILS_FACET_APPLICATION_ROOT" VALUE="$MODULE_DIR$" />
      </configuration>
    </facet>
  </component>
  <component name="ModuleRunConfigurationManager">
    <shared />
  </component>
  <component name="NewModuleRootManager">
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/features" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/spec" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/test" isTestSource="true" />
      <excludeFolder url="file://$MODULE_DIR$/.bundle" />
      <excludeFolder url="file://$MODULE_DIR$/public/packs" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/bundle" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/cache" />
      <excludeFolder url="file://$MODULE_DIR$/log" />
      <excludeFolder url="file://$MODULE_DIR$/tmp" />
      <excludeFolder url="file://$MODULE_DIR$/public/system" />
      <excludeFolder url="file://$MODULE_DIR$/components" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="module-library">
      <library name="rails_iest (v0.1.0) [path][gem]" type="rubylib">
        <properties>
          <option name="version" value="4" />
        </properties>
        <CLASSES>
          <root url="file://$MODULE_DIR$/../rails_iest/db" />
          <root url="file://$MODULE_DIR$/../rails_iest/app" />
          <root url="file://$MODULE_DIR$/../rails_iest/bin" />
          <root url="file://$MODULE_DIR$/../rails_iest/lib" />
          <root url="file://$MODULE_DIR$/../rails_iest/.git" />
          <root url="file://$MODULE_DIR$/../rails_iest/spec" />
          <root url="file://$MODULE_DIR$/../rails_iest/.idea" />
          <root url="file://$MODULE_DIR$/../rails_iest/config" />
          <root url="file://$MODULE_DIR$/../rails_iest/.github" />
          <root url="file://$MODULE_DIR$/../rails_iest/.ruby-lsp" />
        </CLASSES>
        <JAVADOC />
        <SOURCES>
          <root url="file://$MODULE_DIR$/../rails_iest/db" />
          <root url="file://$MODULE_DIR$/../rails_iest/app" />
          <root url="file://$MODULE_DIR$/../rails_iest/bin" />
          <root url="file://$MODULE_DIR$/../rails_iest/lib" />
          <root url="file://$MODULE_DIR$/../rails_iest/.git" />
          <root url="file://$MODULE_DIR$/../rails_iest/spec" />
          <root url="file://$MODULE_DIR$/../rails_iest/.idea" />
          <root url="file://$MODULE_DIR$/../rails_iest/config" />
          <root url="file://$MODULE_DIR$/../rails_iest/.github" />
          <root url="file://$MODULE_DIR$/../rails_iest/.ruby-lsp" />
        </SOURCES>
        <excluded>
          <root url="file://$MODULE_DIR$/../rails_iest/bin" />
          <root url="file://$MODULE_DIR$/../rails_iest/.git" />
          <root url="file://$MODULE_DIR$/../rails_iest/spec" />
          <root url="file://$MODULE_DIR$/../rails_iest/.idea" />
          <root url="file://$MODULE_DIR$/../rails_iest/.github" />
          <root url="file://$MODULE_DIR$/../rails_iest/.ruby-lsp" />
        </excluded>
      </library>
    </orderEntry>
    <orderEntry type="library" scope="PROVIDED" name="Ascii85 (v2.0.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="aasm (v5.5.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="actioncable (v7.1.5.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="actionmailbox (v7.1.5.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="actionmailer (v7.1.5.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="actionpack (v7.1.5.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="actiontext (v7.1.5.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="actionview (v7.1.5.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="active_record_extended (v3.3.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="activejob (v7.1.5.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="activemodel (v7.1.5.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="activerecord (v7.1.5.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="activestorage (v7.1.5.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="activesupport (v7.1.5.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="acts_as_commentable_with_threading (v2.0.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="acts_as_list (v1.2.4, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="addressable (v2.8.7, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="afm (v0.2.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="aliyunsdkcore (v0.0.17, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ancestry (v4.3.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="annotate (v3.2.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ast (v2.4.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="attr_json (v2.5.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="awesome_nested_set (v3.8.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="aws-eventstream (v1.3.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="aws-partitions (v1.1052.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="aws-sdk-core (v3.219.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="aws-sdk-kms (v1.99.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="aws-sdk-s3 (v1.182.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="aws-sigv4 (v1.11.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="baran (v0.1.12, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="base64 (v0.2.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="benchmark (v0.4.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="bigdecimal (v3.1.9, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="bootsnap (v1.18.4, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="brakeman (v7.0.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="builder (v3.3.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="bundler (v2.5.23, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="byebug (v11.1.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="caxlsx (v4.2.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="closure_tree (v7.4.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="coderay (v1.1.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="concurrent-ruby (v1.3.5, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="connection_pool (v2.5.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="content_disposition (v1.0.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="crass (v1.0.6, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="csv (v3.3.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="date (v3.4.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="debug (v1.10.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="deep_cloneable (v3.2.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="diff-lcs (v1.6.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="docile (v1.4.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="domain_name (v0.6.20240107, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="dotenv (v3.1.7, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="dotenv-rails (v3.1.7, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="down (v5.4.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="drb (v2.2.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="erubi (v1.13.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="et-orbi (v1.2.11, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ethon (v0.16.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="event_stream_parser (v1.0.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="eventmachine (v1.2.7, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="execjs (v2.10.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="factory_bot (v6.5.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="factory_bot_rails (v6.4.4, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="faraday (v2.12.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="faraday-multipart (v1.1.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="faraday-net_http (v3.4.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="faye-websocket (v0.11.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ffi (v1.17.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ffi-compiler (v1.3.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="fugit (v1.11.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="globalid (v1.2.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="groupdate (v6.5.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="has_scope (v0.8.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="hashery (v2.1.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="hiredis (v0.6.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="htmlentities (v4.3.4, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="http (v5.2.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="http-accept (v1.7.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="http-cookie (v1.0.8, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="http-form_data (v2.3.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="i18n (v1.14.7, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="inherited_resources (v2.1.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="io-console (v0.8.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="irb (v1.15.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="jbuilder (v2.13.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="jmespath (v1.6.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="json (v2.10.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="json-schema (v4.3.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="langchainrb (v0.19.4, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="language_server-protocol (v3.17.0.4, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="lint_roller (v1.1.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="llhttp-ffi (v0.5.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="logger (v1.6.6, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="loofah (v2.24.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="mail (v2.8.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="marcel (v1.0.4, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="matrix (v0.4.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="method_source (v1.1.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="mime-types (v3.6.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="mime-types-data (v3.2025.0220, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="mina (v1.2.5, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="mina-multistage (v1.0.4, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="mini_magick (v5.1.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="mini_mime (v1.1.5, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="mini_portile2 (v2.8.8, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="minitest (v5.25.4, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="msgpack (v1.8.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="multi_json (v1.15.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="multipart-post (v2.4.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="mustermann (v3.0.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="mutex_m (v0.3.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="mysql2 (v0.5.6, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="neighbor (v0.5.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="nest (v3.2.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="net-http (v0.6.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="net-imap (v0.5.6, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="net-pop (v0.1.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="net-protocol (v0.2.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="net-smtp (v0.5.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="netrc (v0.11.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="nilify_blanks (v1.4.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="nio4r (v2.7.4, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="nokogiri (v1.18.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ohm (v3.1.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="paper_trail (v16.0.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="parallel (v1.26.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="paranoia (v3.0.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="parser (v3.3.7.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="pdf-reader (v2.14.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="pg (v1.5.9, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="pgvector (v0.3.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="pluck_all (v2.3.4, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="pp (v0.6.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="pragmatic_segmenter (v0.3.24, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="prettyprint (v0.2.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="pry (v0.14.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="pry-byebug (v3.10.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="pry-rails (v0.3.11, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="psych (v5.2.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="public_suffix (v6.0.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="puma (v6.6.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="pundit (v2.4.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="raabro (v1.4.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="racc (v1.8.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rack (v2.2.11, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rack-cors (v2.0.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rack-protection (v3.2.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rack-session (v1.0.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rack-test (v2.2.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rackup (v1.0.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails (v7.1.5.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails-dom-testing (v2.2.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails-html-sanitizer (v1.6.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_action_core (v0.1.0@6c57d8, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_assessment (v0.1.0@5a1093, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_bot (v0.1.0@bfba16, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_bpm (v0.1.0@7a3449, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_chat (v0.1.0@e43f96, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_com (v0.1.0@643ff4, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_compatibility (v0.0.10, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_data (v0.1.0@27b5fb, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_dingtalk (v0.1.0@ba0bae, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_grant (v0.1.0@d587b9, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_notify (v0.1.0@d0ba17, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_opm (v0.1.0@c8cec5, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_permit (v0.1.0@4daec7, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_reg (v0.1.0@82da21, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_region (v0.1.0@1d5151, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_res (v0.1.0@c18ca7, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_rss (v0.1.0@187048, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_schedule (v0.1.0@078396, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_serve (v0.1.0@b46827, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_sms_auth (v0.1.0@ac5c29, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_soa_auth (v1.0.0@3a71f9, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_spider (v0.1.0@c49a95, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_tofu (v0.1.0@4e430b, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="railties (v7.1.5.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rainbow (v3.1.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rake (v13.2.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ransack (v4.3.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ransack-enum (v1.0.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rdoc (v6.12.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="redic (v1.5.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="redis (v4.8.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="redis-namespace (v1.11.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="redis-objects (v1.7.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="regexp_parser (v2.10.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="reline (v0.6.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="request_store (v1.7.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="responders (v3.1.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rest-client (v2.1.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rexml (v3.4.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rmmseg-cpp-new (v0.3.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="roda (v3.89.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rolify (v6.0.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="roo (v2.10.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="roo-xls (v1.2.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rspec-core (v3.13.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rspec-expectations (v3.13.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rspec-mocks (v3.13.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rspec-rails (v7.1.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rspec-rails-swagger (v0.1.4@c1b2bb, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rspec-support (v3.13.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rubocop (v1.72.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rubocop-ast (v1.38.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rubocop-minitest (v0.37.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rubocop-performance (v1.24.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rubocop-rails (v2.30.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rubocop-rails-omakase (v1.0.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ruby-ole (v1.2.13.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ruby-openai (v7.4.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ruby-pinyin (v0.5.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ruby-progressbar (v1.13.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ruby-rc4 (v0.1.5, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ruby2_keywords (v0.0.5, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ruby_lunardate (v0.1.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rubyzip (v2.4.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rufus-scheduler (v3.9.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="sassc (v2.4.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="securerandom (v0.4.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="selenium-webdriver (v4.10.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="sequel (v5.89.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="shoulda-matchers (v6.4.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="sidekiq (v6.5.12, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="sidekiq-scheduler (v5.0.6, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="simple_controller (v1.1.0@3663f7, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="simplecov (v0.22.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="simplecov-html (v0.13.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="simplecov_json_formatter (v0.1.4, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="sinatra (v3.2.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="spreadsheet (v1.3.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="sprockets (v4.2.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="sqlite3 (v2.5.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="stal (v0.3.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="streamio-ffmpeg (v3.0.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="stringio (v3.1.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="strip_attributes (v1.14.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ta_by_star (v4.1.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ta_deep_pluck (v1.3.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ta_default_value_for (v3.4.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ta_has_event (v1.0.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ta_ransack_mongo (v1.0.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="tallty_duck_record (v1.1.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="tallty_form (v1.0.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="tallty_import_export (v1.1.6@765c88, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="thor (v1.3.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="tilt (v2.6.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="timeout (v0.4.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ttfunk (v1.8.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="typhoeus (v1.4.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="tzinfo (v2.0.6, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="unicode-display_width (v3.1.4, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="unicode-emoji (v4.0.4, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="uppy-s3_multipart (v1.2.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="uri (v1.0.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="uuidtools (v2.2.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="verbs (v3.1.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="webdrivers (v5.3.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="webrick (v1.9.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="websocket (v1.2.11, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="websocket-driver (v0.7.7, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="websocket-extensions (v0.1.5, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="will_paginate (v4.0.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="with_advisory_lock (v5.1.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="zeitwerk (v2.7.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="zip-zip (v0.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="zip_tricks (v5.6.0, RVM: ruby-3.2.2) [gem]" level="application" />
  </component>
  <component name="RModuleSettingsStorage">
    <LOAD_PATH number="0" />
    <I18N_FOLDERS number="1" string0="$MODULE_DIR$/config/locales" />
  </component>
  <component name="RailsGeneratorsCache">
    <option name="generators">
      <list>
        <option value="aasm" />
        <option value="active_record:aasm" />
        <option value="active_record:application_record" />
        <option value="active_record:event" />
        <option value="active_record:migration" />
        <option value="active_record:model" />
        <option value="active_record:multi_db" />
        <option value="active_record:rolify" />
        <option value="acts_as_commentable_upgrade_migration" />
        <option value="acts_as_commentable_with_threading_migration" />
        <option value="acts_as_pasting:migrations" />
        <option value="annotate:install" />
        <option value="application_record" />
        <option value="benchmark" />
        <option value="channel" />
        <option value="closure_tree:config" />
        <option value="closure_tree:migration" />
        <option value="controller" />
        <option value="factory_bot:model" />
        <option value="generator" />
        <option value="inherited_resources_controller" />
        <option value="integration_test" />
        <option value="jbuilder" />
        <option value="job" />
        <option value="mailbox" />
        <option value="mailer" />
        <option value="migration" />
        <option value="model" />
        <option value="mongoid:aasm" />
        <option value="neighbor:cube" />
        <option value="neighbor:sqlite" />
        <option value="neighbor:vector" />
        <option value="nobrainer:aasm" />
        <option value="paper_trail:install" />
        <option value="paper_trail:migration" />
        <option value="paper_trail:update_item_subtype" />
        <option value="pundit:install" />
        <option value="pundit:policy" />
        <option value="rails_com:migration" />
        <option value="rails_com:migrations" />
        <option value="resource" />
        <option value="responders:install" />
        <option value="responders_controller" />
        <option value="rolify" />
        <option value="rolify:user" />
        <option value="rspec:channel" />
        <option value="rspec:controller" />
        <option value="rspec:feature" />
        <option value="rspec:generator" />
        <option value="rspec:helper" />
        <option value="rspec:install" />
        <option value="rspec:job" />
        <option value="rspec:mailbox" />
        <option value="rspec:mailer" />
        <option value="rspec:model" />
        <option value="rspec:policy" />
        <option value="rspec:request" />
        <option value="rspec:scaffold" />
        <option value="rspec:swagger" />
        <option value="rspec:swagger_install" />
        <option value="rspec:system" />
        <option value="rspec:view" />
        <option value="scaffold" />
        <option value="scaffold_controller" />
        <option value="sidekiq:job" />
        <option value="simple_controller" />
        <option value="system_test" />
        <option value="task" />
        <option value="test_unit:channel" />
        <option value="test_unit:controller" />
        <option value="test_unit:generator" />
        <option value="test_unit:helper" />
        <option value="test_unit:install" />
        <option value="test_unit:integration" />
        <option value="test_unit:job" />
        <option value="test_unit:mailbox" />
        <option value="test_unit:mailer" />
        <option value="test_unit:model" />
        <option value="test_unit:plugin" />
        <option value="test_unit:policy" />
        <option value="test_unit:scaffold" />
        <option value="test_unit:system" />
      </list>
    </option>
    <option name="myGenerators">
      <list>
        <option value="aasm" />
        <option value="active_record:aasm" />
        <option value="active_record:application_record" />
        <option value="active_record:event" />
        <option value="active_record:migration" />
        <option value="active_record:model" />
        <option value="active_record:multi_db" />
        <option value="active_record:rolify" />
        <option value="acts_as_commentable_upgrade_migration" />
        <option value="acts_as_commentable_with_threading_migration" />
        <option value="acts_as_pasting:migrations" />
        <option value="annotate:install" />
        <option value="application_record" />
        <option value="benchmark" />
        <option value="channel" />
        <option value="closure_tree:config" />
        <option value="closure_tree:migration" />
        <option value="controller" />
        <option value="factory_bot:model" />
        <option value="generator" />
        <option value="inherited_resources_controller" />
        <option value="integration_test" />
        <option value="jbuilder" />
        <option value="job" />
        <option value="mailbox" />
        <option value="mailer" />
        <option value="migration" />
        <option value="model" />
        <option value="mongoid:aasm" />
        <option value="neighbor:cube" />
        <option value="neighbor:sqlite" />
        <option value="neighbor:vector" />
        <option value="nobrainer:aasm" />
        <option value="paper_trail:install" />
        <option value="paper_trail:migration" />
        <option value="paper_trail:update_item_subtype" />
        <option value="pundit:install" />
        <option value="pundit:policy" />
        <option value="rails_com:migration" />
        <option value="rails_com:migrations" />
        <option value="resource" />
        <option value="responders:install" />
        <option value="responders_controller" />
        <option value="rolify" />
        <option value="rolify:user" />
        <option value="rspec:channel" />
        <option value="rspec:controller" />
        <option value="rspec:feature" />
        <option value="rspec:generator" />
        <option value="rspec:helper" />
        <option value="rspec:install" />
        <option value="rspec:job" />
        <option value="rspec:mailbox" />
        <option value="rspec:mailer" />
        <option value="rspec:model" />
        <option value="rspec:policy" />
        <option value="rspec:request" />
        <option value="rspec:scaffold" />
        <option value="rspec:swagger" />
        <option value="rspec:swagger_install" />
        <option value="rspec:system" />
        <option value="rspec:view" />
        <option value="scaffold" />
        <option value="scaffold_controller" />
        <option value="sidekiq:job" />
        <option value="simple_controller" />
        <option value="system_test" />
        <option value="task" />
        <option value="test_unit:channel" />
        <option value="test_unit:controller" />
        <option value="test_unit:generator" />
        <option value="test_unit:helper" />
        <option value="test_unit:install" />
        <option value="test_unit:integration" />
        <option value="test_unit:job" />
        <option value="test_unit:mailbox" />
        <option value="test_unit:mailer" />
        <option value="test_unit:model" />
        <option value="test_unit:plugin" />
        <option value="test_unit:policy" />
        <option value="test_unit:scaffold" />
        <option value="test_unit:system" />
      </list>
    </option>
  </component>
  <component name="RailsPaths" isManagedAutomatically="true">
    <entry key="app">
      <value>file://$MODULE_DIR$/app</value>
    </entry>
    <entry key="app/assets">
      <value>file://$MODULE_DIR$/app/assets</value>
    </entry>
    <entry key="app/channels">
      <value>file://$MODULE_DIR$/app/channels</value>
    </entry>
    <entry key="app/controllers">
      <value>file://$MODULE_DIR$/app/controllers</value>
    </entry>
    <entry key="app/helpers">
      <value>file://$MODULE_DIR$/app/helpers</value>
    </entry>
    <entry key="app/mailers">
      <value>file://$MODULE_DIR$/app/mailers</value>
    </entry>
    <entry key="app/models">
      <value>file://$MODULE_DIR$/app/models</value>
    </entry>
    <entry key="app/views">
      <value>file://$MODULE_DIR$/app/views</value>
    </entry>
    <entry key="config">
      <value>file://$MODULE_DIR$/config</value>
    </entry>
    <entry key="config/cable">
      <value>file://$MODULE_DIR$/config/cable.yml</value>
    </entry>
    <entry key="config/database">
      <value>file://$MODULE_DIR$/config/database.yml</value>
    </entry>
    <entry key="config/environment">
      <value>file://$MODULE_DIR$/config/environment.rb</value>
    </entry>
    <entry key="config/environments">
      <value>file://$MODULE_DIR$/config/environments</value>
    </entry>
    <entry key="config/initializers">
      <value>file://$MODULE_DIR$/config/initializers</value>
    </entry>
    <entry key="config/locales">
      <value>file://$MODULE_DIR$/config/locales</value>
    </entry>
    <entry key="config/routes">
      <value>file://$MODULE_DIR$/config/routes</value>
    </entry>
    <entry key="config/routes.rb">
      <value>file://$MODULE_DIR$/config/routes.rb</value>
    </entry>
    <entry key="config/secrets">
      <value>file://$MODULE_DIR$/config</value>
    </entry>
    <entry key="db">
      <value>file://$MODULE_DIR$/db</value>
    </entry>
    <entry key="db/migrate">
      <value>file://$MODULE_DIR$/db/migrate</value>
    </entry>
    <entry key="db/seeds.rb">
      <value>file://$MODULE_DIR$/db/seeds.rb</value>
    </entry>
    <entry key="lib">
      <value>file://$MODULE_DIR$/lib</value>
    </entry>
    <entry key="lib/assets">
      <value>file://$MODULE_DIR$/lib/assets</value>
    </entry>
    <entry key="lib/tasks">
      <value>file://$MODULE_DIR$/lib/tasks</value>
    </entry>
    <entry key="lib/templates">
      <value>file://$MODULE_DIR$/lib/templates</value>
    </entry>
    <entry key="log">
      <value>file://$MODULE_DIR$/log/development.log</value>
    </entry>
    <entry key="public">
      <value>file://$MODULE_DIR$/public</value>
    </entry>
    <entry key="public/javascripts">
      <value>file://$MODULE_DIR$/public/javascripts</value>
    </entry>
    <entry key="public/stylesheets">
      <value>file://$MODULE_DIR$/public/stylesheets</value>
    </entry>
    <entry key="test/mailers/previews">
      <value>file://$MODULE_DIR$/spec/mailers/previews</value>
      <value>file://$MODULE_DIR$/test/mailers/previews</value>
      <value>file://$MODULE_DIR$/test/mailers/previews</value>
    </entry>
    <entry key="tmp">
      <value>file://$MODULE_DIR$/tmp</value>
    </entry>
    <entry key="vendor">
      <value>file://$MODULE_DIR$/vendor</value>
    </entry>
    <entry key="vendor/assets">
      <value>file://$MODULE_DIR$/vendor/assets</value>
    </entry>
  </component>
  <component name="RakeTasksCache">
    <option name="myRootTask">
      <RakeTaskImpl id="rake">
        <subtasks>
          <RakeTaskImpl id="action_mailbox">
            <subtasks>
              <RakeTaskImpl id="ingress">
                <subtasks>
                  <RakeTaskImpl description="Relay an inbound email from Exim to Action Mailbox (URL and INGRESS_PASSWORD required)" fullCommand="action_mailbox:ingress:exim" id="exim" />
                  <RakeTaskImpl description="Relay an inbound email from Postfix to Action Mailbox (URL and INGRESS_PASSWORD required)" fullCommand="action_mailbox:ingress:postfix" id="postfix" />
                  <RakeTaskImpl description="Relay an inbound email from Qmail to Action Mailbox (URL and INGRESS_PASSWORD required)" fullCommand="action_mailbox:ingress:qmail" id="qmail" />
                  <RakeTaskImpl description="" fullCommand="action_mailbox:ingress:environment" id="environment" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl description="Install Action Mailbox and its dependencies" fullCommand="action_mailbox:install" id="install" />
              <RakeTaskImpl id="install">
                <subtasks>
                  <RakeTaskImpl description="Copy migrations from action_mailbox to application" fullCommand="action_mailbox:install:migrations" id="migrations" />
                </subtasks>
              </RakeTaskImpl>
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="action_text">
            <subtasks>
              <RakeTaskImpl description="Copy over the migration, stylesheet, and JavaScript files" fullCommand="action_text:install" id="install" />
              <RakeTaskImpl id="install">
                <subtasks>
                  <RakeTaskImpl description="Copy migrations from action_text to application" fullCommand="action_text:install:migrations" id="migrations" />
                </subtasks>
              </RakeTaskImpl>
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="active_storage">
            <subtasks>
              <RakeTaskImpl description="Copy over the migration needed to the application" fullCommand="active_storage:install" id="install" />
              <RakeTaskImpl id="install">
                <subtasks>
                  <RakeTaskImpl description="" fullCommand="active_storage:install:migrations" id="migrations" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl description="" fullCommand="active_storage:update" id="update" />
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="app">
            <subtasks>
              <RakeTaskImpl description="Apply the template supplied by LOCATION=(/path/to/template) or URL" fullCommand="app:template" id="template" />
              <RakeTaskImpl description="Update configs and some other initially generated files (or use just update:configs or update:bin)" fullCommand="app:update" id="update" />
              <RakeTaskImpl id="templates">
                <subtasks>
                  <RakeTaskImpl description="" fullCommand="app:templates:copy" id="copy" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="update">
                <subtasks>
                  <RakeTaskImpl description="" fullCommand="app:update:active_storage" id="active_storage" />
                  <RakeTaskImpl description="" fullCommand="app:update:bin" id="bin" />
                  <RakeTaskImpl description="" fullCommand="app:update:configs" id="configs" />
                  <RakeTaskImpl description="" fullCommand="app:update:upgrade_guide_info" id="upgrade_guide_info" />
                </subtasks>
              </RakeTaskImpl>
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="db">
            <subtasks>
              <RakeTaskImpl description="Create the database from DATABASE_URL or config/database.yml for the current RAILS_ENV (use db:create:all to create all databases in the config). Without RAILS_ENV or when RAILS_ENV is development, it defaults to creating the development and test databases, except when DATABASE_URL is present" fullCommand="db:create" id="create" />
              <RakeTaskImpl description="Drop the database from DATABASE_URL or config/database.yml for the current RAILS_ENV (use db:drop:all to drop all databases in the config). Without RAILS_ENV or when RAILS_ENV is development, it defaults to dropping the development and test databases, except when DATABASE_URL is present" fullCommand="db:drop" id="drop" />
              <RakeTaskImpl id="encryption">
                <subtasks>
                  <RakeTaskImpl description="Generate a set of keys for configuring Active Record encryption in a given environment" fullCommand="db:encryption:init" id="init" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="environment">
                <subtasks>
                  <RakeTaskImpl description="Set the environment value for the database" fullCommand="db:environment:set" id="set" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="fixtures">
                <subtasks>
                  <RakeTaskImpl description="Load fixtures into the current environment's database" fullCommand="db:fixtures:load" id="load" />
                  <RakeTaskImpl description="" fullCommand="db:fixtures:identify" id="identify" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl description="Migrate the database (options: VERSION=x, VERBOSE=false, SCOPE=blog)" fullCommand="db:migrate" id="migrate" />
              <RakeTaskImpl id="migrate">
                <subtasks>
                  <RakeTaskImpl description="Run the &quot;down&quot; for a given migration VERSION" fullCommand="db:migrate:down" id="down" />
                  <RakeTaskImpl description="Roll back the database one migration and re-migrate up (options: STEP=x, VERSION=x)" fullCommand="db:migrate:redo" id="redo" />
                  <RakeTaskImpl description="Display status of migrations" fullCommand="db:migrate:status" id="status" />
                  <RakeTaskImpl description="Run the &quot;up&quot; for a given migration VERSION" fullCommand="db:migrate:up" id="up" />
                  <RakeTaskImpl description="" fullCommand="db:migrate:reset" id="reset" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl description="Run setup if database does not exist, or run migrations if it does" fullCommand="db:prepare" id="prepare" />
              <RakeTaskImpl description="Drop and recreate all databases from their schema for the current environment and load the seeds" fullCommand="db:reset" id="reset" />
              <RakeTaskImpl description="Roll the schema back to the previous version (specify steps w/ STEP=n)" fullCommand="db:rollback" id="rollback" />
              <RakeTaskImpl id="schema">
                <subtasks>
                  <RakeTaskImpl id="cache">
                    <subtasks>
                      <RakeTaskImpl description="Clear a db/schema_cache.yml file" fullCommand="db:schema:cache:clear" id="clear" />
                      <RakeTaskImpl description="Create a db/schema_cache.yml file" fullCommand="db:schema:cache:dump" id="dump" />
                    </subtasks>
                  </RakeTaskImpl>
                  <RakeTaskImpl description="Create a database schema file (either db/schema.rb or db/structure.sql, depending on `ENV['SCHEMA_FORMAT']` or `config.active_record.schema_format`)" fullCommand="db:schema:dump" id="dump" />
                  <RakeTaskImpl description="Load a database schema file (either db/schema.rb or db/structure.sql, depending on `ENV['SCHEMA_FORMAT']` or `config.active_record.schema_format`) into the database" fullCommand="db:schema:load" id="load" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl description="Load the seed data from db/seeds.rb" fullCommand="db:seed" id="seed" />
              <RakeTaskImpl id="seed">
                <subtasks>
                  <RakeTaskImpl description="Truncate tables of each database for current environment and load the seeds" fullCommand="db:seed:replant" id="replant" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl description="Create all databases, load all schemas, and initialize with the seed data (use db:reset to also drop all databases first)" fullCommand="db:setup" id="setup" />
              <RakeTaskImpl description="Retrieve the current schema version number" fullCommand="db:version" id="version" />
              <RakeTaskImpl description="" fullCommand="db:_dump" id="_dump" />
              <RakeTaskImpl description="" fullCommand="db:abort_if_pending_migrations" id="abort_if_pending_migrations" />
              <RakeTaskImpl description="" fullCommand="db:charset" id="charset" />
              <RakeTaskImpl description="" fullCommand="db:check_protected_environments" id="check_protected_environments" />
              <RakeTaskImpl description="" fullCommand="db:collation" id="collation" />
              <RakeTaskImpl id="create">
                <subtasks>
                  <RakeTaskImpl description="" fullCommand="db:create:all" id="all" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="drop">
                <subtasks>
                  <RakeTaskImpl description="" fullCommand="db:drop:_unsafe" id="_unsafe" />
                  <RakeTaskImpl description="" fullCommand="db:drop:all" id="all" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl description="" fullCommand="db:forward" id="forward" />
              <RakeTaskImpl description="" fullCommand="db:load_config" id="load_config" />
              <RakeTaskImpl description="" fullCommand="db:purge" id="purge" />
              <RakeTaskImpl id="purge">
                <subtasks>
                  <RakeTaskImpl description="" fullCommand="db:purge:all" id="all" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="reset">
                <subtasks>
                  <RakeTaskImpl description="" fullCommand="db:reset:all" id="all" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="setup">
                <subtasks>
                  <RakeTaskImpl description="" fullCommand="db:setup:all" id="all" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="test">
                <subtasks>
                  <RakeTaskImpl description="" fullCommand="db:test:load_schema" id="load_schema" />
                  <RakeTaskImpl description="" fullCommand="db:test:prepare" id="prepare" />
                  <RakeTaskImpl description="" fullCommand="db:test:purge" id="purge" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl description="" fullCommand="db:truncate_all" id="truncate_all" />
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="log">
            <subtasks>
              <RakeTaskImpl description="Truncate all/specified *.log files in log/ to zero bytes (specify which logs with LOGS=test,development)" fullCommand="log:clear" id="clear" />
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="permission">
            <subtasks>
              <RakeTaskImpl description="Clean up permission cache" fullCommand="permission:cache_clean" id="cache_clean" />
              <RakeTaskImpl description="migrate permission of default role" fullCommand="permission:export" id="export" />
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="rails_action_core_engine">
            <subtasks>
              <RakeTaskImpl id="install">
                <subtasks>
                  <RakeTaskImpl description="Copy migrations from rails_action_core_engine to application" fullCommand="rails_action_core_engine:install:migrations" id="migrations" />
                </subtasks>
              </RakeTaskImpl>
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="rails_assessment_engine">
            <subtasks>
              <RakeTaskImpl id="install">
                <subtasks>
                  <RakeTaskImpl description="Copy migrations from rails_assessment_engine to application" fullCommand="rails_assessment_engine:install:migrations" id="migrations" />
                </subtasks>
              </RakeTaskImpl>
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="rails_bot_engine">
            <subtasks>
              <RakeTaskImpl id="install">
                <subtasks>
                  <RakeTaskImpl description="Copy migrations from rails_bot_engine to application" fullCommand="rails_bot_engine:install:migrations" id="migrations" />
                </subtasks>
              </RakeTaskImpl>
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="rails_bpm_engine">
            <subtasks>
              <RakeTaskImpl id="install">
                <subtasks>
                  <RakeTaskImpl description="Copy migrations from rails_bpm_engine to application" fullCommand="rails_bpm_engine:install:migrations" id="migrations" />
                </subtasks>
              </RakeTaskImpl>
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="rails_chat_engine">
            <subtasks>
              <RakeTaskImpl id="install">
                <subtasks>
                  <RakeTaskImpl description="Copy migrations from rails_chat_engine to application" fullCommand="rails_chat_engine:install:migrations" id="migrations" />
                </subtasks>
              </RakeTaskImpl>
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="rails_com_engine">
            <subtasks>
              <RakeTaskImpl id="install">
                <subtasks>
                  <RakeTaskImpl description="Copy migrations from rails_com_engine to application" fullCommand="rails_com_engine:install:migrations" id="migrations" />
                </subtasks>
              </RakeTaskImpl>
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="rails_data_engine">
            <subtasks>
              <RakeTaskImpl id="install">
                <subtasks>
                  <RakeTaskImpl description="Copy migrations from rails_data_engine to application" fullCommand="rails_data_engine:install:migrations" id="migrations" />
                </subtasks>
              </RakeTaskImpl>
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="rails_dingtalk_engine">
            <subtasks>
              <RakeTaskImpl id="install">
                <subtasks>
                  <RakeTaskImpl description="Copy migrations from rails_dingtalk_engine to application" fullCommand="rails_dingtalk_engine:install:migrations" id="migrations" />
                </subtasks>
              </RakeTaskImpl>
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="rails_grant_engine">
            <subtasks>
              <RakeTaskImpl id="install">
                <subtasks>
                  <RakeTaskImpl description="Copy migrations from rails_grant_engine to application" fullCommand="rails_grant_engine:install:migrations" id="migrations" />
                </subtasks>
              </RakeTaskImpl>
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="rails_iest_engine">
            <subtasks>
              <RakeTaskImpl id="install">
                <subtasks>
                  <RakeTaskImpl description="Copy migrations from rails_iest_engine to application" fullCommand="rails_iest_engine:install:migrations" id="migrations" />
                </subtasks>
              </RakeTaskImpl>
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="rails_notify_engine">
            <subtasks>
              <RakeTaskImpl id="install">
                <subtasks>
                  <RakeTaskImpl description="Copy migrations from rails_notify_engine to application" fullCommand="rails_notify_engine:install:migrations" id="migrations" />
                </subtasks>
              </RakeTaskImpl>
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="rails_opm_engine">
            <subtasks>
              <RakeTaskImpl id="install">
                <subtasks>
                  <RakeTaskImpl description="Copy migrations from rails_opm_engine to application" fullCommand="rails_opm_engine:install:migrations" id="migrations" />
                </subtasks>
              </RakeTaskImpl>
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="rails_permit_engine">
            <subtasks>
              <RakeTaskImpl id="install">
                <subtasks>
                  <RakeTaskImpl description="Copy migrations from rails_permit_engine to application" fullCommand="rails_permit_engine:install:migrations" id="migrations" />
                </subtasks>
              </RakeTaskImpl>
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="rails_reg_engine">
            <subtasks>
              <RakeTaskImpl id="install">
                <subtasks>
                  <RakeTaskImpl description="Copy migrations from rails_reg_engine to application" fullCommand="rails_reg_engine:install:migrations" id="migrations" />
                </subtasks>
              </RakeTaskImpl>
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="rails_region_engine">
            <subtasks>
              <RakeTaskImpl id="install">
                <subtasks>
                  <RakeTaskImpl description="Copy migrations from rails_region_engine to application" fullCommand="rails_region_engine:install:migrations" id="migrations" />
                </subtasks>
              </RakeTaskImpl>
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="rails_res_engine">
            <subtasks>
              <RakeTaskImpl id="install">
                <subtasks>
                  <RakeTaskImpl description="Copy migrations from rails_res_engine to application" fullCommand="rails_res_engine:install:migrations" id="migrations" />
                </subtasks>
              </RakeTaskImpl>
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="rails_schedule_engine">
            <subtasks>
              <RakeTaskImpl id="install">
                <subtasks>
                  <RakeTaskImpl description="Copy migrations from rails_schedule_engine to application" fullCommand="rails_schedule_engine:install:migrations" id="migrations" />
                </subtasks>
              </RakeTaskImpl>
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="rails_serve_engine">
            <subtasks>
              <RakeTaskImpl id="install">
                <subtasks>
                  <RakeTaskImpl description="Copy migrations from rails_serve_engine to application" fullCommand="rails_serve_engine:install:migrations" id="migrations" />
                </subtasks>
              </RakeTaskImpl>
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="rails_sms_auth_engine">
            <subtasks>
              <RakeTaskImpl id="install">
                <subtasks>
                  <RakeTaskImpl description="Copy migrations from rails_sms_auth_engine to application" fullCommand="rails_sms_auth_engine:install:migrations" id="migrations" />
                </subtasks>
              </RakeTaskImpl>
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="rails_soa_auth_engine">
            <subtasks>
              <RakeTaskImpl id="install">
                <subtasks>
                  <RakeTaskImpl description="Copy migrations from rails_soa_auth_engine to application" fullCommand="rails_soa_auth_engine:install:migrations" id="migrations" />
                </subtasks>
              </RakeTaskImpl>
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="rails_spider_engine">
            <subtasks>
              <RakeTaskImpl id="install">
                <subtasks>
                  <RakeTaskImpl description="Copy migrations from rails_spider_engine to application" fullCommand="rails_spider_engine:install:migrations" id="migrations" />
                </subtasks>
              </RakeTaskImpl>
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="rails_tofu_engine">
            <subtasks>
              <RakeTaskImpl id="install">
                <subtasks>
                  <RakeTaskImpl description="Copy migrations from rails_tofu_engine to application" fullCommand="rails_tofu_engine:install:migrations" id="migrations" />
                </subtasks>
              </RakeTaskImpl>
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl description="Run all specs in spec directory (excluding plugin specs)" fullCommand="spec" id="spec" />
          <RakeTaskImpl id="spec">
            <subtasks>
              <RakeTaskImpl description="Run the code examples in spec/models" fullCommand="spec:models" id="models" />
              <RakeTaskImpl description="Run the code examples in spec/requests" fullCommand="spec:requests" id="requests" />
              <RakeTaskImpl description="" fullCommand="spec:prepare" id="prepare" />
              <RakeTaskImpl description="" fullCommand="spec:statsetup" id="statsetup" />
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl description="Report code statistics (KLOCs, etc) from the application or engine" fullCommand="stats" id="stats" />
          <RakeTaskImpl description="Regenerate Swagger docs" fullCommand="swagger" id="swagger" />
          <RakeTaskImpl description="Run all tests in test folder except system ones" fullCommand="test" id="test" />
          <RakeTaskImpl id="test">
            <subtasks>
              <RakeTaskImpl description="Reset the database and run `bin/rails test`" fullCommand="test:db" id="db" />
              <RakeTaskImpl description="" fullCommand="test:all" id="all" />
              <RakeTaskImpl description="" fullCommand="test:channels" id="channels" />
              <RakeTaskImpl description="" fullCommand="test:controllers" id="controllers" />
              <RakeTaskImpl description="" fullCommand="test:functionals" id="functionals" />
              <RakeTaskImpl description="" fullCommand="test:generators" id="generators" />
              <RakeTaskImpl description="" fullCommand="test:helpers" id="helpers" />
              <RakeTaskImpl description="" fullCommand="test:integration" id="integration" />
              <RakeTaskImpl description="" fullCommand="test:jobs" id="jobs" />
              <RakeTaskImpl description="" fullCommand="test:mailboxes" id="mailboxes" />
              <RakeTaskImpl description="" fullCommand="test:mailers" id="mailers" />
              <RakeTaskImpl description="" fullCommand="test:models" id="models" />
              <RakeTaskImpl description="" fullCommand="test:prepare" id="prepare" />
              <RakeTaskImpl description="" fullCommand="test:run" id="run" />
              <RakeTaskImpl description="" fullCommand="test:system" id="system" />
              <RakeTaskImpl description="" fullCommand="test:units" id="units" />
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="time">
            <subtasks>
              <RakeTaskImpl description="List all time zones, list by two-letter country code (`bin/rails time:zones[US]`), or list by UTC offset (`bin/rails time:zones[-8]`)" fullCommand="time:zones[country_or_offset]" id="zones[country_or_offset]" />
              <RakeTaskImpl description="" fullCommand="time:zones" id="zones" />
              <RakeTaskImpl id="zones">
                <subtasks>
                  <RakeTaskImpl description="" fullCommand="time:zones:all" id="all" />
                  <RakeTaskImpl description="" fullCommand="time:zones:local" id="local" />
                  <RakeTaskImpl description="" fullCommand="time:zones:us" id="us" />
                </subtasks>
              </RakeTaskImpl>
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="tmp">
            <subtasks>
              <RakeTaskImpl description="Clear cache, socket and screenshot files from tmp/ (narrow w/ tmp:cache:clear, tmp:sockets:clear, tmp:screenshots:clear)" fullCommand="tmp:clear" id="clear" />
              <RakeTaskImpl description="Create tmp directories for cache, sockets, and pids" fullCommand="tmp:create" id="create" />
              <RakeTaskImpl id="cache">
                <subtasks>
                  <RakeTaskImpl description="" fullCommand="tmp:cache:clear" id="clear" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="pids">
                <subtasks>
                  <RakeTaskImpl description="" fullCommand="tmp:pids:clear" id="clear" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="screenshots">
                <subtasks>
                  <RakeTaskImpl description="" fullCommand="tmp:screenshots:clear" id="clear" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="sockets">
                <subtasks>
                  <RakeTaskImpl description="" fullCommand="tmp:sockets:clear" id="clear" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="storage">
                <subtasks>
                  <RakeTaskImpl description="" fullCommand="tmp:storage:clear" id="clear" />
                </subtasks>
              </RakeTaskImpl>
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="webdrivers">
            <subtasks>
              <RakeTaskImpl id="chromedriver">
                <subtasks>
                  <RakeTaskImpl description="Force remove chromedriver" fullCommand="webdrivers:chromedriver:remove" id="remove" />
                  <RakeTaskImpl description="Remove and download updated chromedriver if necessary" fullCommand="webdrivers:chromedriver:update[version]" id="update[version]" />
                  <RakeTaskImpl description="Print current chromedriver version" fullCommand="webdrivers:chromedriver:version" id="version" />
                  <RakeTaskImpl description="" fullCommand="webdrivers:chromedriver:update" id="update" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="edgedriver">
                <subtasks>
                  <RakeTaskImpl description="Force remove edgedriver" fullCommand="webdrivers:edgedriver:remove" id="remove" />
                  <RakeTaskImpl description="Remove and download updated edgedriver if necessary" fullCommand="webdrivers:edgedriver:update[version]" id="update[version]" />
                  <RakeTaskImpl description="Print current edgedriver version" fullCommand="webdrivers:edgedriver:version" id="version" />
                  <RakeTaskImpl description="" fullCommand="webdrivers:edgedriver:update" id="update" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="geckodriver">
                <subtasks>
                  <RakeTaskImpl description="Force remove geckodriver" fullCommand="webdrivers:geckodriver:remove" id="remove" />
                  <RakeTaskImpl description="Remove and download updated geckodriver if necessary" fullCommand="webdrivers:geckodriver:update[version]" id="update[version]" />
                  <RakeTaskImpl description="Print current geckodriver version" fullCommand="webdrivers:geckodriver:version" id="version" />
                  <RakeTaskImpl description="" fullCommand="webdrivers:geckodriver:update" id="update" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="iedriver">
                <subtasks>
                  <RakeTaskImpl description="Force remove IEDriverServer" fullCommand="webdrivers:iedriver:remove" id="remove" />
                  <RakeTaskImpl description="Remove and download updated IEDriverServer if necessary" fullCommand="webdrivers:iedriver:update[version]" id="update[version]" />
                  <RakeTaskImpl description="Print current IEDriverServer version" fullCommand="webdrivers:iedriver:version" id="version" />
                  <RakeTaskImpl description="" fullCommand="webdrivers:iedriver:update" id="update" />
                </subtasks>
              </RakeTaskImpl>
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="yarn">
            <subtasks>
              <RakeTaskImpl description="Install all JavaScript dependencies as specified via Yarn" fullCommand="yarn:install" id="install" />
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="zeitwerk">
            <subtasks>
              <RakeTaskImpl description="Check project structure for Zeitwerk compatibility" fullCommand="zeitwerk:check" id="check" />
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl description="" fullCommand="annotate" id="annotate" />
          <RakeTaskImpl description="" fullCommand="bin" id="bin" />
          <RakeTaskImpl description="" fullCommand="bundle" id="bundle" />
          <RakeTaskImpl description="" fullCommand="default" id="default" />
          <RakeTaskImpl description="" fullCommand="environment" id="environment" />
          <RakeTaskImpl id="railties">
            <subtasks>
              <RakeTaskImpl id="install">
                <subtasks>
                  <RakeTaskImpl description="" fullCommand="railties:install:migrations" id="migrations" />
                </subtasks>
              </RakeTaskImpl>
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl description="" fullCommand="rubocop" id="rubocop" />
          <RakeTaskImpl description="" fullCommand="scp" id="scp" />
          <RakeTaskImpl description="" fullCommand="start" id="start" />
          <RakeTaskImpl description="" fullCommand="tmp" id="tmp" />
          <RakeTaskImpl description="" fullCommand="tmp/cache" id="tmp/cache" />
          <RakeTaskImpl description="" fullCommand="tmp/cache/assets" id="tmp/cache/assets" />
          <RakeTaskImpl description="" fullCommand="tmp/pids" id="tmp/pids" />
          <RakeTaskImpl description="" fullCommand="tmp/sockets" id="tmp/sockets" />
        </subtasks>
      </RakeTaskImpl>
    </option>
  </component>
  <component name="RakeTasksCache-v2">
    <option name="myRootTask">
      <RakeTaskImpl id="rake">
        <subtasks>
          <RakeTaskImpl id="action_mailbox">
            <subtasks>
              <RakeTaskImpl id="ingress">
                <subtasks>
                  <RakeTaskImpl description="Relay an inbound email from Exim to Action Mailbox (URL and INGRESS_PASSWORD required)" fullCommand="action_mailbox:ingress:exim" id="exim" />
                  <RakeTaskImpl description="Relay an inbound email from Postfix to Action Mailbox (URL and INGRESS_PASSWORD required)" fullCommand="action_mailbox:ingress:postfix" id="postfix" />
                  <RakeTaskImpl description="Relay an inbound email from Qmail to Action Mailbox (URL and INGRESS_PASSWORD required)" fullCommand="action_mailbox:ingress:qmail" id="qmail" />
                  <RakeTaskImpl description="" fullCommand="action_mailbox:ingress:environment" id="environment" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl description="Install Action Mailbox and its dependencies" fullCommand="action_mailbox:install" id="install" />
              <RakeTaskImpl id="install">
                <subtasks>
                  <RakeTaskImpl description="Copy migrations from action_mailbox to application" fullCommand="action_mailbox:install:migrations" id="migrations" />
                </subtasks>
              </RakeTaskImpl>
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="action_text">
            <subtasks>
              <RakeTaskImpl description="Copy over the migration, stylesheet, and JavaScript files" fullCommand="action_text:install" id="install" />
              <RakeTaskImpl id="install">
                <subtasks>
                  <RakeTaskImpl description="Copy migrations from action_text to application" fullCommand="action_text:install:migrations" id="migrations" />
                </subtasks>
              </RakeTaskImpl>
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="active_storage">
            <subtasks>
              <RakeTaskImpl description="Copy over the migration needed to the application" fullCommand="active_storage:install" id="install" />
              <RakeTaskImpl id="install">
                <subtasks>
                  <RakeTaskImpl description="" fullCommand="active_storage:install:migrations" id="migrations" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl description="" fullCommand="active_storage:update" id="update" />
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="app">
            <subtasks>
              <RakeTaskImpl description="Apply the template supplied by LOCATION=(/path/to/template) or URL" fullCommand="app:template" id="template" />
              <RakeTaskImpl description="Update configs and some other initially generated files (or use just update:configs or update:bin)" fullCommand="app:update" id="update" />
              <RakeTaskImpl id="templates">
                <subtasks>
                  <RakeTaskImpl description="" fullCommand="app:templates:copy" id="copy" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="update">
                <subtasks>
                  <RakeTaskImpl description="" fullCommand="app:update:active_storage" id="active_storage" />
                  <RakeTaskImpl description="" fullCommand="app:update:bin" id="bin" />
                  <RakeTaskImpl description="" fullCommand="app:update:configs" id="configs" />
                  <RakeTaskImpl description="" fullCommand="app:update:upgrade_guide_info" id="upgrade_guide_info" />
                </subtasks>
              </RakeTaskImpl>
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="db">
            <subtasks>
              <RakeTaskImpl description="Create the database from DATABASE_URL or config/database.yml for the current RAILS_ENV (use db:create:all to create all databases in the config). Without RAILS_ENV or when RAILS_ENV is development, it defaults to creating the development and test databases, except when DATABASE_URL is present" fullCommand="db:create" id="create" />
              <RakeTaskImpl description="Drop the database from DATABASE_URL or config/database.yml for the current RAILS_ENV (use db:drop:all to drop all databases in the config). Without RAILS_ENV or when RAILS_ENV is development, it defaults to dropping the development and test databases, except when DATABASE_URL is present" fullCommand="db:drop" id="drop" />
              <RakeTaskImpl id="encryption">
                <subtasks>
                  <RakeTaskImpl description="Generate a set of keys for configuring Active Record encryption in a given environment" fullCommand="db:encryption:init" id="init" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="environment">
                <subtasks>
                  <RakeTaskImpl description="Set the environment value for the database" fullCommand="db:environment:set" id="set" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="fixtures">
                <subtasks>
                  <RakeTaskImpl description="Load fixtures into the current environment's database" fullCommand="db:fixtures:load" id="load" />
                  <RakeTaskImpl description="" fullCommand="db:fixtures:identify" id="identify" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl description="Migrate the database (options: VERSION=x, VERBOSE=false, SCOPE=blog)" fullCommand="db:migrate" id="migrate" />
              <RakeTaskImpl id="migrate">
                <subtasks>
                  <RakeTaskImpl description="Run the &quot;down&quot; for a given migration VERSION" fullCommand="db:migrate:down" id="down" />
                  <RakeTaskImpl description="Roll back the database one migration and re-migrate up (options: STEP=x, VERSION=x)" fullCommand="db:migrate:redo" id="redo" />
                  <RakeTaskImpl description="Display status of migrations" fullCommand="db:migrate:status" id="status" />
                  <RakeTaskImpl description="Run the &quot;up&quot; for a given migration VERSION" fullCommand="db:migrate:up" id="up" />
                  <RakeTaskImpl description="" fullCommand="db:migrate:reset" id="reset" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl description="Run setup if database does not exist, or run migrations if it does" fullCommand="db:prepare" id="prepare" />
              <RakeTaskImpl description="Drop and recreate all databases from their schema for the current environment and load the seeds" fullCommand="db:reset" id="reset" />
              <RakeTaskImpl description="Roll the schema back to the previous version (specify steps w/ STEP=n)" fullCommand="db:rollback" id="rollback" />
              <RakeTaskImpl id="schema">
                <subtasks>
                  <RakeTaskImpl id="cache">
                    <subtasks>
                      <RakeTaskImpl description="Clear a db/schema_cache.yml file" fullCommand="db:schema:cache:clear" id="clear" />
                      <RakeTaskImpl description="Create a db/schema_cache.yml file" fullCommand="db:schema:cache:dump" id="dump" />
                    </subtasks>
                  </RakeTaskImpl>
                  <RakeTaskImpl description="Create a database schema file (either db/schema.rb or db/structure.sql, depending on `ENV['SCHEMA_FORMAT']` or `config.active_record.schema_format`)" fullCommand="db:schema:dump" id="dump" />
                  <RakeTaskImpl description="Load a database schema file (either db/schema.rb or db/structure.sql, depending on `ENV['SCHEMA_FORMAT']` or `config.active_record.schema_format`) into the database" fullCommand="db:schema:load" id="load" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl description="Load the seed data from db/seeds.rb" fullCommand="db:seed" id="seed" />
              <RakeTaskImpl id="seed">
                <subtasks>
                  <RakeTaskImpl description="Truncate tables of each database for current environment and load the seeds" fullCommand="db:seed:replant" id="replant" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl description="Create all databases, load all schemas, and initialize with the seed data (use db:reset to also drop all databases first)" fullCommand="db:setup" id="setup" />
              <RakeTaskImpl description="Retrieve the current schema version number" fullCommand="db:version" id="version" />
              <RakeTaskImpl description="" fullCommand="db:_dump" id="_dump" />
              <RakeTaskImpl description="" fullCommand="db:abort_if_pending_migrations" id="abort_if_pending_migrations" />
              <RakeTaskImpl description="" fullCommand="db:charset" id="charset" />
              <RakeTaskImpl description="" fullCommand="db:check_protected_environments" id="check_protected_environments" />
              <RakeTaskImpl description="" fullCommand="db:collation" id="collation" />
              <RakeTaskImpl id="create">
                <subtasks>
                  <RakeTaskImpl description="" fullCommand="db:create:all" id="all" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="drop">
                <subtasks>
                  <RakeTaskImpl description="" fullCommand="db:drop:_unsafe" id="_unsafe" />
                  <RakeTaskImpl description="" fullCommand="db:drop:all" id="all" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl description="" fullCommand="db:forward" id="forward" />
              <RakeTaskImpl description="" fullCommand="db:load_config" id="load_config" />
              <RakeTaskImpl description="" fullCommand="db:purge" id="purge" />
              <RakeTaskImpl id="purge">
                <subtasks>
                  <RakeTaskImpl description="" fullCommand="db:purge:all" id="all" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="reset">
                <subtasks>
                  <RakeTaskImpl description="" fullCommand="db:reset:all" id="all" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="setup">
                <subtasks>
                  <RakeTaskImpl description="" fullCommand="db:setup:all" id="all" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="test">
                <subtasks>
                  <RakeTaskImpl description="" fullCommand="db:test:load_schema" id="load_schema" />
                  <RakeTaskImpl description="" fullCommand="db:test:prepare" id="prepare" />
                  <RakeTaskImpl description="" fullCommand="db:test:purge" id="purge" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl description="" fullCommand="db:truncate_all" id="truncate_all" />
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="log">
            <subtasks>
              <RakeTaskImpl description="Truncate all/specified *.log files in log/ to zero bytes (specify which logs with LOGS=test,development)" fullCommand="log:clear" id="clear" />
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="permission">
            <subtasks>
              <RakeTaskImpl description="Clean up permission cache" fullCommand="permission:cache_clean" id="cache_clean" />
              <RakeTaskImpl description="migrate permission of default role" fullCommand="permission:export" id="export" />
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="rails_action_core_engine">
            <subtasks>
              <RakeTaskImpl id="install">
                <subtasks>
                  <RakeTaskImpl description="Copy migrations from rails_action_core_engine to application" fullCommand="rails_action_core_engine:install:migrations" id="migrations" />
                </subtasks>
              </RakeTaskImpl>
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="rails_assessment_engine">
            <subtasks>
              <RakeTaskImpl id="install">
                <subtasks>
                  <RakeTaskImpl description="Copy migrations from rails_assessment_engine to application" fullCommand="rails_assessment_engine:install:migrations" id="migrations" />
                </subtasks>
              </RakeTaskImpl>
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="rails_bpm_engine">
            <subtasks>
              <RakeTaskImpl id="install">
                <subtasks>
                  <RakeTaskImpl description="Copy migrations from rails_bpm_engine to application" fullCommand="rails_bpm_engine:install:migrations" id="migrations" />
                </subtasks>
              </RakeTaskImpl>
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="rails_chat_engine">
            <subtasks>
              <RakeTaskImpl id="install">
                <subtasks>
                  <RakeTaskImpl description="Copy migrations from rails_chat_engine to application" fullCommand="rails_chat_engine:install:migrations" id="migrations" />
                </subtasks>
              </RakeTaskImpl>
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="rails_com_engine">
            <subtasks>
              <RakeTaskImpl id="install">
                <subtasks>
                  <RakeTaskImpl description="Copy migrations from rails_com_engine to application" fullCommand="rails_com_engine:install:migrations" id="migrations" />
                </subtasks>
              </RakeTaskImpl>
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="rails_data_engine">
            <subtasks>
              <RakeTaskImpl id="install">
                <subtasks>
                  <RakeTaskImpl description="Copy migrations from rails_data_engine to application" fullCommand="rails_data_engine:install:migrations" id="migrations" />
                </subtasks>
              </RakeTaskImpl>
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="rails_dingtalk_engine">
            <subtasks>
              <RakeTaskImpl id="install">
                <subtasks>
                  <RakeTaskImpl description="Copy migrations from rails_dingtalk_engine to application" fullCommand="rails_dingtalk_engine:install:migrations" id="migrations" />
                </subtasks>
              </RakeTaskImpl>
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="rails_grant_engine">
            <subtasks>
              <RakeTaskImpl id="install">
                <subtasks>
                  <RakeTaskImpl description="Copy migrations from rails_grant_engine to application" fullCommand="rails_grant_engine:install:migrations" id="migrations" />
                </subtasks>
              </RakeTaskImpl>
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="rails_iest_engine">
            <subtasks>
              <RakeTaskImpl id="install">
                <subtasks>
                  <RakeTaskImpl description="Copy migrations from rails_iest_engine to application" fullCommand="rails_iest_engine:install:migrations" id="migrations" />
                </subtasks>
              </RakeTaskImpl>
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="rails_notify_engine">
            <subtasks>
              <RakeTaskImpl id="install">
                <subtasks>
                  <RakeTaskImpl description="Copy migrations from rails_notify_engine to application" fullCommand="rails_notify_engine:install:migrations" id="migrations" />
                </subtasks>
              </RakeTaskImpl>
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="rails_opm_engine">
            <subtasks>
              <RakeTaskImpl id="install">
                <subtasks>
                  <RakeTaskImpl description="Copy migrations from rails_opm_engine to application" fullCommand="rails_opm_engine:install:migrations" id="migrations" />
                </subtasks>
              </RakeTaskImpl>
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="rails_permit_engine">
            <subtasks>
              <RakeTaskImpl id="install">
                <subtasks>
                  <RakeTaskImpl description="Copy migrations from rails_permit_engine to application" fullCommand="rails_permit_engine:install:migrations" id="migrations" />
                </subtasks>
              </RakeTaskImpl>
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="rails_reg_engine">
            <subtasks>
              <RakeTaskImpl id="install">
                <subtasks>
                  <RakeTaskImpl description="Copy migrations from rails_reg_engine to application" fullCommand="rails_reg_engine:install:migrations" id="migrations" />
                </subtasks>
              </RakeTaskImpl>
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="rails_region_engine">
            <subtasks>
              <RakeTaskImpl id="install">
                <subtasks>
                  <RakeTaskImpl description="Copy migrations from rails_region_engine to application" fullCommand="rails_region_engine:install:migrations" id="migrations" />
                </subtasks>
              </RakeTaskImpl>
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="rails_res_engine">
            <subtasks>
              <RakeTaskImpl id="install">
                <subtasks>
                  <RakeTaskImpl description="Copy migrations from rails_res_engine to application" fullCommand="rails_res_engine:install:migrations" id="migrations" />
                </subtasks>
              </RakeTaskImpl>
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="rails_schedule_engine">
            <subtasks>
              <RakeTaskImpl id="install">
                <subtasks>
                  <RakeTaskImpl description="Copy migrations from rails_schedule_engine to application" fullCommand="rails_schedule_engine:install:migrations" id="migrations" />
                </subtasks>
              </RakeTaskImpl>
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="rails_serve_engine">
            <subtasks>
              <RakeTaskImpl id="install">
                <subtasks>
                  <RakeTaskImpl description="Copy migrations from rails_serve_engine to application" fullCommand="rails_serve_engine:install:migrations" id="migrations" />
                </subtasks>
              </RakeTaskImpl>
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="rails_sms_auth_engine">
            <subtasks>
              <RakeTaskImpl id="install">
                <subtasks>
                  <RakeTaskImpl description="Copy migrations from rails_sms_auth_engine to application" fullCommand="rails_sms_auth_engine:install:migrations" id="migrations" />
                </subtasks>
              </RakeTaskImpl>
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="rails_soa_auth_engine">
            <subtasks>
              <RakeTaskImpl id="install">
                <subtasks>
                  <RakeTaskImpl description="Copy migrations from rails_soa_auth_engine to application" fullCommand="rails_soa_auth_engine:install:migrations" id="migrations" />
                </subtasks>
              </RakeTaskImpl>
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="rails_spider_engine">
            <subtasks>
              <RakeTaskImpl id="install">
                <subtasks>
                  <RakeTaskImpl description="Copy migrations from rails_spider_engine to application" fullCommand="rails_spider_engine:install:migrations" id="migrations" />
                </subtasks>
              </RakeTaskImpl>
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="rails_tofu_engine">
            <subtasks>
              <RakeTaskImpl id="install">
                <subtasks>
                  <RakeTaskImpl description="Copy migrations from rails_tofu_engine to application" fullCommand="rails_tofu_engine:install:migrations" id="migrations" />
                </subtasks>
              </RakeTaskImpl>
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl description="Run all specs in spec directory (excluding plugin specs)" fullCommand="spec" id="spec" />
          <RakeTaskImpl id="spec">
            <subtasks>
              <RakeTaskImpl description="Run the code examples in spec/models" fullCommand="spec:models" id="models" />
              <RakeTaskImpl description="Run the code examples in spec/requests" fullCommand="spec:requests" id="requests" />
              <RakeTaskImpl description="" fullCommand="spec:prepare" id="prepare" />
              <RakeTaskImpl description="" fullCommand="spec:statsetup" id="statsetup" />
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl description="Report code statistics (KLOCs, etc) from the application or engine" fullCommand="stats" id="stats" />
          <RakeTaskImpl description="Regenerate Swagger docs" fullCommand="swagger" id="swagger" />
          <RakeTaskImpl description="Run all tests in test folder except system ones" fullCommand="test" id="test" />
          <RakeTaskImpl id="test">
            <subtasks>
              <RakeTaskImpl description="Reset the database and run `bin/rails test`" fullCommand="test:db" id="db" />
              <RakeTaskImpl description="" fullCommand="test:all" id="all" />
              <RakeTaskImpl description="" fullCommand="test:channels" id="channels" />
              <RakeTaskImpl description="" fullCommand="test:controllers" id="controllers" />
              <RakeTaskImpl description="" fullCommand="test:functionals" id="functionals" />
              <RakeTaskImpl description="" fullCommand="test:generators" id="generators" />
              <RakeTaskImpl description="" fullCommand="test:helpers" id="helpers" />
              <RakeTaskImpl description="" fullCommand="test:integration" id="integration" />
              <RakeTaskImpl description="" fullCommand="test:jobs" id="jobs" />
              <RakeTaskImpl description="" fullCommand="test:mailboxes" id="mailboxes" />
              <RakeTaskImpl description="" fullCommand="test:mailers" id="mailers" />
              <RakeTaskImpl description="" fullCommand="test:models" id="models" />
              <RakeTaskImpl description="" fullCommand="test:prepare" id="prepare" />
              <RakeTaskImpl description="" fullCommand="test:run" id="run" />
              <RakeTaskImpl description="" fullCommand="test:system" id="system" />
              <RakeTaskImpl description="" fullCommand="test:units" id="units" />
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="time">
            <subtasks>
              <RakeTaskImpl description="List all time zones, list by two-letter country code (`bin/rails time:zones[US]`), or list by UTC offset (`bin/rails time:zones[-8]`)" fullCommand="time:zones[country_or_offset]" id="zones[country_or_offset]" />
              <RakeTaskImpl description="" fullCommand="time:zones" id="zones" />
              <RakeTaskImpl id="zones">
                <subtasks>
                  <RakeTaskImpl description="" fullCommand="time:zones:all" id="all" />
                  <RakeTaskImpl description="" fullCommand="time:zones:local" id="local" />
                  <RakeTaskImpl description="" fullCommand="time:zones:us" id="us" />
                </subtasks>
              </RakeTaskImpl>
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="tmp">
            <subtasks>
              <RakeTaskImpl description="Clear cache, socket and screenshot files from tmp/ (narrow w/ tmp:cache:clear, tmp:sockets:clear, tmp:screenshots:clear)" fullCommand="tmp:clear" id="clear" />
              <RakeTaskImpl description="Create tmp directories for cache, sockets, and pids" fullCommand="tmp:create" id="create" />
              <RakeTaskImpl id="cache">
                <subtasks>
                  <RakeTaskImpl description="" fullCommand="tmp:cache:clear" id="clear" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="pids">
                <subtasks>
                  <RakeTaskImpl description="" fullCommand="tmp:pids:clear" id="clear" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="screenshots">
                <subtasks>
                  <RakeTaskImpl description="" fullCommand="tmp:screenshots:clear" id="clear" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="sockets">
                <subtasks>
                  <RakeTaskImpl description="" fullCommand="tmp:sockets:clear" id="clear" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="storage">
                <subtasks>
                  <RakeTaskImpl description="" fullCommand="tmp:storage:clear" id="clear" />
                </subtasks>
              </RakeTaskImpl>
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="webdrivers">
            <subtasks>
              <RakeTaskImpl id="chromedriver">
                <subtasks>
                  <RakeTaskImpl description="Force remove chromedriver" fullCommand="webdrivers:chromedriver:remove" id="remove" />
                  <RakeTaskImpl description="Remove and download updated chromedriver if necessary" fullCommand="webdrivers:chromedriver:update[version]" id="update[version]" />
                  <RakeTaskImpl description="Print current chromedriver version" fullCommand="webdrivers:chromedriver:version" id="version" />
                  <RakeTaskImpl description="" fullCommand="webdrivers:chromedriver:update" id="update" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="edgedriver">
                <subtasks>
                  <RakeTaskImpl description="Force remove edgedriver" fullCommand="webdrivers:edgedriver:remove" id="remove" />
                  <RakeTaskImpl description="Remove and download updated edgedriver if necessary" fullCommand="webdrivers:edgedriver:update[version]" id="update[version]" />
                  <RakeTaskImpl description="Print current edgedriver version" fullCommand="webdrivers:edgedriver:version" id="version" />
                  <RakeTaskImpl description="" fullCommand="webdrivers:edgedriver:update" id="update" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="geckodriver">
                <subtasks>
                  <RakeTaskImpl description="Force remove geckodriver" fullCommand="webdrivers:geckodriver:remove" id="remove" />
                  <RakeTaskImpl description="Remove and download updated geckodriver if necessary" fullCommand="webdrivers:geckodriver:update[version]" id="update[version]" />
                  <RakeTaskImpl description="Print current geckodriver version" fullCommand="webdrivers:geckodriver:version" id="version" />
                  <RakeTaskImpl description="" fullCommand="webdrivers:geckodriver:update" id="update" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="iedriver">
                <subtasks>
                  <RakeTaskImpl description="Force remove IEDriverServer" fullCommand="webdrivers:iedriver:remove" id="remove" />
                  <RakeTaskImpl description="Remove and download updated IEDriverServer if necessary" fullCommand="webdrivers:iedriver:update[version]" id="update[version]" />
                  <RakeTaskImpl description="Print current IEDriverServer version" fullCommand="webdrivers:iedriver:version" id="version" />
                  <RakeTaskImpl description="" fullCommand="webdrivers:iedriver:update" id="update" />
                </subtasks>
              </RakeTaskImpl>
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="yarn">
            <subtasks>
              <RakeTaskImpl description="Install all JavaScript dependencies as specified via Yarn" fullCommand="yarn:install" id="install" />
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl id="zeitwerk">
            <subtasks>
              <RakeTaskImpl description="Check project structure for Zeitwerk compatibility" fullCommand="zeitwerk:check" id="check" />
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl description="" fullCommand="annotate" id="annotate" />
          <RakeTaskImpl description="" fullCommand="bin" id="bin" />
          <RakeTaskImpl description="" fullCommand="bundle" id="bundle" />
          <RakeTaskImpl description="" fullCommand="default" id="default" />
          <RakeTaskImpl description="" fullCommand="environment" id="environment" />
          <RakeTaskImpl id="railties">
            <subtasks>
              <RakeTaskImpl id="install">
                <subtasks>
                  <RakeTaskImpl description="" fullCommand="railties:install:migrations" id="migrations" />
                </subtasks>
              </RakeTaskImpl>
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl description="" fullCommand="rubocop" id="rubocop" />
          <RakeTaskImpl description="" fullCommand="scp" id="scp" />
          <RakeTaskImpl description="" fullCommand="start" id="start" />
          <RakeTaskImpl description="" fullCommand="tmp" id="tmp" />
          <RakeTaskImpl description="" fullCommand="tmp/cache" id="tmp/cache" />
          <RakeTaskImpl description="" fullCommand="tmp/cache/assets" id="tmp/cache/assets" />
          <RakeTaskImpl description="" fullCommand="tmp/pids" id="tmp/pids" />
          <RakeTaskImpl description="" fullCommand="tmp/sockets" id="tmp/sockets" />
        </subtasks>
      </RakeTaskImpl>
    </option>
  </component>
</module>