<?xml version="1.0" encoding="UTF-8"?>
<module type="RUBY_MODULE" version="4">
  <component name="FacetManager">
    <facet type="RailsFacetType" name="Ruby on Rails">
      <configuration>
        <RAILS_FACET_CONFIG_ID NAME="RAILS_FACET_SUPPORT_REMOVED" VALUE="false" />
        <RAILS_FACET_CONFIG_ID NAME="RAILS_TESTS_SOURCES_PATCHED" VALUE="true" />
        <RAILS_FACET_CONFIG_ID NAME="RAILS_FACET_APPLICATION_ROOT" VALUE="$MODULE_DIR$" />
      </configuration>
    </facet>
  </component>
  <component name="ModuleRunConfigurationManager">
    <shared />
  </component>
  <component name="NewModuleRootManager">
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/features" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/spec" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/test" isTestSource="true" />
      <excludeFolder url="file://$MODULE_DIR$/.bundle" />
      <excludeFolder url="file://$MODULE_DIR$/public/packs" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/bundle" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/cache" />
      <excludeFolder url="file://$MODULE_DIR$/log" />
      <excludeFolder url="file://$MODULE_DIR$/tmp" />
      <excludeFolder url="file://$MODULE_DIR$/public/system" />
      <excludeFolder url="file://$MODULE_DIR$/components" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="library" scope="PROVIDED" name="aasm (v5.5.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="actioncable (v7.1.5.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="actionmailbox (v7.1.5.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="actionmailer (v7.1.5.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="actionpack (v7.1.5.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="actiontext (v7.1.5.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="actionview (v7.1.5.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="active_record_extended (v3.3.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="activejob (v7.1.5.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="activemodel (v7.1.5.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="activerecord (v7.1.5.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="activestorage (v7.1.5.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="activesupport (v7.1.5.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="acts_as_commentable_with_threading (v2.0.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="acts_as_list (v1.2.4, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="addressable (v2.8.7, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ancestry (v4.3.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="annotate (v3.2.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="attr_json (v2.5.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="awesome_nested_set (v3.8.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="awesome_print (v1.9.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="aws-eventstream (v1.3.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="aws-partitions (v1.1029.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="aws-sdk-core (v3.214.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="aws-sdk-kms (v1.96.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="aws-sdk-s3 (v1.176.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="aws-sigv4 (v1.10.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="baran (v0.1.12, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="base64 (v0.2.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="benchmark (v0.4.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="better_errors (v2.10.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="bigdecimal (v3.1.8, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="binding_of_caller (v1.0.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="builder (v3.3.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="bundler (v2.5.23, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="byebug (v11.1.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="caxlsx (v4.1.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="closure_tree (v7.4.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="coderay (v1.1.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="concurrent-ruby (v1.3.4, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="connection_pool (v2.4.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="content_disposition (v1.0.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="crass (v1.0.6, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="date (v3.4.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="debug_inspector (v1.2.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="deep_cloneable (v3.2.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="diff-lcs (v1.5.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="domain_name (v0.6.20240107, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="dotenv (v3.1.4, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="dotenv-rails (v3.1.4, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="drb (v2.2.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="erubi (v1.13.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ethon (v0.16.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="event_stream_parser (v1.0.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="execjs (v2.10.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="factory_bot (v6.5.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="factory_bot_rails (v6.4.4, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="faraday (v2.12.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="faraday-multipart (v1.0.4, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="faraday-net_http (v3.4.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ffi (v1.17.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="globalid (v1.2.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="groupdate (v6.5.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="has_scope (v0.8.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="hirb (v0.7.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="hiredis (v0.6.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="htmlentities (v4.3.4, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="http-accept (v1.7.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="http-cookie (v1.0.8, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="i18n (v1.14.6, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="inherited_resources (v2.1.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="io-console (v0.8.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="irb (v1.14.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="jbuilder (v2.13.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="jmespath (v1.6.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="json (v2.9.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="json-schema (v4.3.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="langchainrb (v0.19.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="logger (v1.6.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="loofah (v2.23.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="mail (v2.8.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="marcel (v1.0.4, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="matrix (v0.4.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="method_source (v1.1.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="mime-types (v3.6.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="mime-types-data (v3.2024.1203, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="mini_mime (v1.1.5, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="mini_portile2 (v2.8.8, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="minitest (v5.25.4, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="multipart-post (v2.4.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="mutex_m (v0.3.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="nest (v3.2.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="net-http (v0.6.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="net-imap (v0.5.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="net-pop (v0.1.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="net-protocol (v0.2.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="net-smtp (v0.5.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="netrc (v0.11.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="nilify_blanks (v1.4.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="nio4r (v2.7.4, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="nokogiri (v1.17.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ohm (v3.1.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="paper_trail (v16.0.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="paranoia (v3.0.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="pg (v1.5.9, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="pluck_all (v2.3.4, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="pragmatic_segmenter (v0.3.24, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="pry (v0.14.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="pry-byebug (v3.10.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="pry-doc (v1.5.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="pry-rails (v0.3.11, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="psych (v5.2.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="public_suffix (v6.0.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="puma (v6.5.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="pundit (v2.4.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="racc (v1.8.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rack (v3.1.8, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rack-cors (v2.0.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rack-session (v2.0.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rack-test (v2.1.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rackup (v2.2.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails (v7.1.5.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails-dom-testing (v2.2.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails-html-sanitizer (v1.6.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_action_core (v0.1.0@6c57d8, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_bpm (v0.1.0@43e935, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_com (v0.1.0@8c5ad8, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_compatibility (v0.0.10, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_data (v0.1.0@27b5fb, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_notify (v0.1.0@aa03bc, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_permit (v0.1.0@4daec7, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_res (v0.1.0@727bcd, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_tofu (v0.1.0@4e430b, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="railties (v7.1.5.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rake (v13.2.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ransack (v4.2.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ransack-enum (v1.0.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rdoc (v6.8.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="redic (v1.5.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="redis (v5.3.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="redis-client (v0.23.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="redis-namespace (v1.11.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="redis-objects (v1.7.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="reline (v0.5.12, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="request_store (v1.7.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="responders (v3.1.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rest-client (v2.1.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rmmseg-cpp-new (v0.3.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="roda (v3.87.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rolify (v6.0.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="roo (v2.10.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="roo-xls (v1.2.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rouge (v4.5.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rspec-core (v3.13.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rspec-expectations (v3.13.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rspec-mocks (v3.13.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rspec-rails (v7.1.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rspec-rails-swagger (v0.1.4@c1b2bb, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rspec-support (v3.13.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ruby-ole (v1.2.13.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ruby-openai (v7.3.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ruby-pinyin (v0.5.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rubyzip (v2.3.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="sassc (v2.4.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="securerandom (v0.4.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="shoulda-matchers (v6.4.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="simple_controller (v1.1.0@e8c693, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="spreadsheet (v1.3.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="sprockets (v4.2.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="sprockets-rails (v3.5.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="sqlite3 (v2.4.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="stal (v0.3.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="stringio (v3.1.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="strip_attributes (v1.14.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ta_by_star (v4.1.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ta_deep_pluck (v1.3.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ta_default_value_for (v3.4.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ta_has_event (v1.0.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ta_ransack_mongo (v1.0.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="tallty_duck_record (v1.1.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="tallty_form (v1.0.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="tallty_import_export (v1.1.5, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="thor (v1.3.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="timeout (v0.4.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="typhoeus (v1.4.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="tzinfo (v2.0.6, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="uppy-s3_multipart (v1.2.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="uri (v1.0.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="uuidtools (v2.2.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="verbs (v3.1.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="websocket-driver (v0.7.6, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="websocket-extensions (v0.1.5, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="will_paginate (v4.0.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="with_advisory_lock (v5.1.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="yard (v0.9.37, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="zeitwerk (v2.7.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="zip-zip (v0.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="zip_tricks (v5.6.0, RVM: ruby-3.2.2) [gem]" level="application" />
  </component>
  <component name="RModuleSettingsStorage">
    <LOAD_PATH number="0" />
    <I18N_FOLDERS number="1" string0="$MODULE_DIR$/config/locales" />
  </component>
  <component name="RailsGeneratorsCache">
    <option name="generators">
      <list>
        <option value="active_record:migration" />
        <option value="active_record:model" />
        <option value="active_record:observer" />
        <option value="active_record:session_migration" />
        <option value="controller" />
        <option value="erb:controller" />
        <option value="erb:mailer" />
        <option value="erb:scaffold" />
        <option value="generator" />
        <option value="helper" />
        <option value="integration_test" />
        <option value="mailer" />
        <option value="metal" />
        <option value="migration" />
        <option value="model" />
        <option value="model_subclass" />
        <option value="observer" />
        <option value="performance_test" />
        <option value="plugin" />
        <option value="resource" />
        <option value="scaffold" />
        <option value="scaffold_controller" />
        <option value="session_migration" />
        <option value="stylesheets" />
        <option value="test_unit:controller" />
        <option value="test_unit:helper" />
        <option value="test_unit:integration" />
        <option value="test_unit:mailer" />
        <option value="test_unit:model" />
        <option value="test_unit:observer" />
        <option value="test_unit:performance" />
        <option value="test_unit:plugin" />
        <option value="test_unit:scaffold" />
      </list>
    </option>
    <option name="myGenerators">
      <list>
        <option value="active_record:migration" />
        <option value="active_record:model" />
        <option value="active_record:observer" />
        <option value="active_record:session_migration" />
        <option value="controller" />
        <option value="erb:controller" />
        <option value="erb:mailer" />
        <option value="erb:scaffold" />
        <option value="generator" />
        <option value="helper" />
        <option value="integration_test" />
        <option value="mailer" />
        <option value="metal" />
        <option value="migration" />
        <option value="model" />
        <option value="model_subclass" />
        <option value="observer" />
        <option value="performance_test" />
        <option value="plugin" />
        <option value="resource" />
        <option value="scaffold" />
        <option value="scaffold_controller" />
        <option value="session_migration" />
        <option value="stylesheets" />
        <option value="test_unit:controller" />
        <option value="test_unit:helper" />
        <option value="test_unit:integration" />
        <option value="test_unit:mailer" />
        <option value="test_unit:model" />
        <option value="test_unit:observer" />
        <option value="test_unit:performance" />
        <option value="test_unit:plugin" />
        <option value="test_unit:scaffold" />
      </list>
    </option>
  </component>
  <component name="RakeTasksCache">
    <option name="myRootTask">
      <RakeTaskImpl id="rake" />
    </option>
  </component>
</module>