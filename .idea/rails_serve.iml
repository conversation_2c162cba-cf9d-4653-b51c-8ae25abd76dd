<?xml version="1.0" encoding="UTF-8"?>
<module type="RUBY_MODULE" version="4">
  <component name="FacetManager">
    <facet type="RailsFacetType" name="Ruby on Rails">
      <configuration>
        <RAILS_FACET_CONFIG_ID NAME="RAILS_FACET_SUPPORT_REMOVED" VALUE="false" />
        <RAILS_FACET_CONFIG_ID NAME="RAILS_TESTS_SOURCES_PATCHED" VALUE="true" />
        <RAILS_FACET_CONFIG_ID NAME="RAILS_FACET_APPLICATION_ROOT" VALUE="$MODULE_DIR$" />
      </configuration>
    </facet>
  </component>
  <component name="ModuleRunConfigurationManager">
    <shared />
  </component>
  <component name="NewModuleRootManager">
    <content url="file://$MODULE_DIR$">
      <sourceFolder url="file://$MODULE_DIR$/features" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/spec" isTestSource="true" />
      <sourceFolder url="file://$MODULE_DIR$/test" isTestSource="true" />
      <excludeFolder url="file://$MODULE_DIR$/.bundle" />
      <excludeFolder url="file://$MODULE_DIR$/public/packs" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/bundle" />
      <excludeFolder url="file://$MODULE_DIR$/vendor/cache" />
      <excludeFolder url="file://$MODULE_DIR$/log" />
      <excludeFolder url="file://$MODULE_DIR$/tmp" />
      <excludeFolder url="file://$MODULE_DIR$/public/system" />
      <excludeFolder url="file://$MODULE_DIR$/components" />
    </content>
    <orderEntry type="inheritedJdk" />
    <orderEntry type="sourceFolder" forTests="false" />
    <orderEntry type="library" scope="PROVIDED" name="aasm (v5.5.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="actioncable (v7.1.3.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="actionmailbox (v7.1.3.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="actionmailer (v7.1.3.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="actionpack (v7.1.3.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="actiontext (v7.1.3.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="actionview (v7.1.3.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="active_record_extended (v3.2.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="activejob (v7.1.3.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="activemodel (v7.1.3.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="activerecord (v7.1.3.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="activestorage (v7.1.3.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="activesupport (v7.1.3.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="acts_as_commentable_with_threading (v2.0.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="acts_as_list (v1.1.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ancestry (v4.3.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="annotate (v3.2.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="attr_json (v2.3.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="awesome_nested_set (v3.6.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="awesome_print (v1.9.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="aws-eventstream (v1.3.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="aws-partitions (v1.906.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="aws-sdk-core (v3.191.6, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="aws-sdk-kms (v1.78.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="aws-sdk-s3 (v1.146.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="aws-sigv4 (v1.8.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="base64 (v0.2.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="better_errors (v2.10.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="bigdecimal (v3.1.7, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="binding_of_caller (v1.0.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="builder (v3.2.4, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="bundler (v2.5.6, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="byebug (v11.1.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="caxlsx (v4.1.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="closure_tree (v7.4.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="coderay (v1.1.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="concurrent-ruby (v1.2.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="connection_pool (v2.4.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="content_disposition (v1.0.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="crass (v1.0.6, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="date (v3.3.4, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="debug_inspector (v1.2.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="deep_cloneable (v3.2.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="diff-lcs (v1.5.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="domain_name (v0.6.20240107, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="dotenv (v3.1.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="dotenv-rails (v3.1.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="drb (v2.2.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="erubi (v1.12.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ethon (v0.16.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="execjs (v2.9.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="factory_bot (v6.4.6, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="factory_bot_rails (v6.4.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ffi (v1.16.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="globalid (v1.2.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="groupdate (v6.4.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="has_scope (v0.8.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="hirb (v0.7.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="hiredis (v0.6.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="htmlentities (v4.3.4, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="http-accept (v1.7.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="http-cookie (v1.0.5, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="i18n (v1.14.4, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="inherited_resources (v1.14.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="io-console (v0.7.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="irb (v1.12.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="jbuilder (v2.11.5, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="jmespath (v1.6.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="loofah (v2.22.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="mail (v2.8.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="marcel (v1.0.4, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="method_source (v1.0.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="mime-types (v3.5.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="mime-types-data (v3.2024.0305, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="mini_mime (v1.1.5, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="mini_portile2 (v2.8.7, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="minitest (v5.22.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="mutex_m (v0.2.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="nest (v3.2.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="net-imap (v0.4.10, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="net-pop (v0.1.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="net-protocol (v0.2.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="net-smtp (v0.5.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="netrc (v0.11.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="nilify_blanks (v1.4.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="nio4r (v2.7.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="nokogiri (v1.16.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ohm (v3.1.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="paper_trail (v15.1.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="paranoia (v2.6.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="pg (v1.5.6, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="pluck_all (v2.3.4, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="pry (v0.14.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="pry-byebug (v3.10.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="pry-doc (v1.5.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="pry-rails (v0.3.9, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="psych (v5.1.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="pundit (v2.3.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="racc (v1.7.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rack (v3.0.10, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rack-cors (v2.0.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rack-session (v2.0.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rack-test (v2.1.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rackup (v2.1.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails (v7.1.3.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails-dom-testing (v2.2.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails-html-sanitizer (v1.6.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_action_core (v0.1.0@637295, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_bpm (v0.1.0@335b4a, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_com (v0.1.0@1120c1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_compatibility (v0.0.10, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_data (v0.1.0@eeff35, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_notify (v0.1.0@97bc63, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_permit (v0.1.0@acaccc, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_reg (v0.1.0@82da21, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_res (v0.1.0@3e5b72, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_state (v0.1.0@ae2485, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rails_tofu (v0.1.0@f48ad7, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="railties (v7.1.3.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rake (v13.2.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ransack (v3.2.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ransack-enum (v1.0.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rdoc (v6.6.3.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="redic (v1.5.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="redis (v5.1.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="redis-client (v0.21.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="redis-namespace (v1.11.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="redis-objects (v1.7.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="reline (v0.5.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="request_store (v1.6.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="responders (v3.1.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rest-client (v2.1.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rmmseg-cpp-new (v0.3.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="roda (v3.78.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rolify (v6.0.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="roo (v2.10.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="roo-xls (v1.2.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rouge (v4.2.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rspec-core (v3.13.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rspec-expectations (v3.13.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rspec-mocks (v3.13.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rspec-rails (v6.1.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rspec-rails-swagger (v0.1.4@c1b2bb, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rspec-support (v3.13.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ruby-ole (v1.2.13.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ruby-pinyin (v0.5.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="rubyzip (v2.3.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="sassc (v2.4.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="sassc-rails (v2.1.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="shoulda-matchers (v6.2.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="simple_controller (v1.1.0@8a6d6f, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="spreadsheet (v1.3.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="sprockets (v4.2.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="sprockets-rails (v3.4.2, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="sqlite3 (v1.7.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="stal (v0.3.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="stringio (v3.1.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="strip_attributes (v1.13.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ta_by_star (v4.1.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ta_deep_pluck (v1.3.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ta_default_value_for (v3.4.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ta_has_event (v1.0.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="ta_ransack_mongo (v1.0.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="tallty_duck_record (v1.1.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="tallty_form (v1.0.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="tallty_import_export (v1.1.5, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="thor (v1.3.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="tilt (v2.3.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="timeout (v0.4.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="typhoeus (v1.4.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="tzinfo (v2.0.6, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="uppy-s3_multipart (v1.2.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="uuidtools (v2.2.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="verbs (v3.1.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="webrick (v1.8.1, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="websocket-driver (v0.7.6, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="websocket-extensions (v0.1.5, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="will_paginate (v4.0.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="with_advisory_lock (v5.1.0, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="yard (v0.9.36, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="zeitwerk (v2.6.13, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="zip-zip (v0.3, RVM: ruby-3.2.2) [gem]" level="application" />
    <orderEntry type="library" scope="PROVIDED" name="zip_tricks (v5.6.0, RVM: ruby-3.2.2) [gem]" level="application" />
  </component>
  <component name="RakeTasksCache">
    <option name="myRootTask">
      <RakeTaskImpl id="rake">
        <subtasks>
          <RakeTaskImpl id="app">
            <subtasks>
              <RakeTaskImpl id="action_mailbox">
                <subtasks>
                  <RakeTaskImpl id="ingress">
                    <subtasks>
                      <RakeTaskImpl description="Relay an inbound email from Exim to Action Mailbox (URL and INGRESS_PASSWORD required)" fullCommand="app:action_mailbox:ingress:exim" id="exim" />
                      <RakeTaskImpl description="Relay an inbound email from Postfix to Action Mailbox (URL and INGRESS_PASSWORD required)" fullCommand="app:action_mailbox:ingress:postfix" id="postfix" />
                      <RakeTaskImpl description="Relay an inbound email from Qmail to Action Mailbox (URL and INGRESS_PASSWORD required)" fullCommand="app:action_mailbox:ingress:qmail" id="qmail" />
                      <RakeTaskImpl description="" fullCommand="app:action_mailbox:ingress:environment" id="environment" />
                    </subtasks>
                  </RakeTaskImpl>
                  <RakeTaskImpl description="Install Action Mailbox and its dependencies" fullCommand="app:action_mailbox:install" id="install" />
                  <RakeTaskImpl id="install">
                    <subtasks>
                      <RakeTaskImpl description="Copy migrations from action_mailbox to application" fullCommand="app:action_mailbox:install:migrations" id="migrations" />
                    </subtasks>
                  </RakeTaskImpl>
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="action_text">
                <subtasks>
                  <RakeTaskImpl description="Copy over the migration, stylesheet, and JavaScript files" fullCommand="app:action_text:install" id="install" />
                  <RakeTaskImpl id="install">
                    <subtasks>
                      <RakeTaskImpl description="Copy migrations from action_text to application" fullCommand="app:action_text:install:migrations" id="migrations" />
                    </subtasks>
                  </RakeTaskImpl>
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="active_storage">
                <subtasks>
                  <RakeTaskImpl description="Copy over the migration needed to the application" fullCommand="app:active_storage:install" id="install" />
                  <RakeTaskImpl id="install">
                    <subtasks>
                      <RakeTaskImpl description="" fullCommand="app:active_storage:install:migrations" id="migrations" />
                    </subtasks>
                  </RakeTaskImpl>
                  <RakeTaskImpl description="" fullCommand="app:active_storage:update" id="update" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="app">
                <subtasks>
                  <RakeTaskImpl description="Apply the template supplied by LOCATION=(/path/to/template) or URL" fullCommand="app:app:template" id="template" />
                  <RakeTaskImpl description="Update configs and some other initially generated files (or use just update:configs or update:bin)" fullCommand="app:app:update" id="update" />
                  <RakeTaskImpl id="templates">
                    <subtasks>
                      <RakeTaskImpl description="" fullCommand="app:app:templates:copy" id="copy" />
                    </subtasks>
                  </RakeTaskImpl>
                  <RakeTaskImpl id="update">
                    <subtasks>
                      <RakeTaskImpl description="" fullCommand="app:app:update:active_storage" id="active_storage" />
                      <RakeTaskImpl description="" fullCommand="app:app:update:bin" id="bin" />
                      <RakeTaskImpl description="" fullCommand="app:app:update:configs" id="configs" />
                      <RakeTaskImpl description="" fullCommand="app:app:update:upgrade_guide_info" id="upgrade_guide_info" />
                    </subtasks>
                  </RakeTaskImpl>
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="assets">
                <subtasks>
                  <RakeTaskImpl description="Remove old compiled assets" fullCommand="app:assets:clean[keep]" id="clean[keep]" />
                  <RakeTaskImpl description="Remove compiled assets" fullCommand="app:assets:clobber" id="clobber" />
                  <RakeTaskImpl description="Load asset compile environment" fullCommand="app:assets:environment" id="environment" />
                  <RakeTaskImpl description="Compile all the assets named in config.assets.precompile" fullCommand="app:assets:precompile" id="precompile" />
                  <RakeTaskImpl description="" fullCommand="app:assets:clean" id="clean" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="db">
                <subtasks>
                  <RakeTaskImpl description="Create the database from DATABASE_URL or config/database.yml for the current RAILS_ENV (use db:create:all to create all databases in the config). Without RAILS_ENV or when RAILS_ENV is development, it defaults to creating the development and test databases, except when DATABASE_URL is present" fullCommand="app:db:create" id="create" />
                  <RakeTaskImpl description="Drop the database from DATABASE_URL or config/database.yml for the current RAILS_ENV (use db:drop:all to drop all databases in the config). Without RAILS_ENV or when RAILS_ENV is development, it defaults to dropping the development and test databases, except when DATABASE_URL is present" fullCommand="app:db:drop" id="drop" />
                  <RakeTaskImpl id="encryption">
                    <subtasks>
                      <RakeTaskImpl description="Generate a set of keys for configuring Active Record encryption in a given environment" fullCommand="app:db:encryption:init" id="init" />
                    </subtasks>
                  </RakeTaskImpl>
                  <RakeTaskImpl id="environment">
                    <subtasks>
                      <RakeTaskImpl description="Set the environment value for the database" fullCommand="app:db:environment:set" id="set" />
                    </subtasks>
                  </RakeTaskImpl>
                  <RakeTaskImpl id="fixtures">
                    <subtasks>
                      <RakeTaskImpl description="Load fixtures into the current environment's database" fullCommand="app:db:fixtures:load" id="load" />
                      <RakeTaskImpl description="" fullCommand="app:db:fixtures:identify" id="identify" />
                    </subtasks>
                  </RakeTaskImpl>
                  <RakeTaskImpl description="Migrate the database (options: VERSION=x, VERBOSE=false, SCOPE=blog)" fullCommand="app:db:migrate" id="migrate" />
                  <RakeTaskImpl id="migrate">
                    <subtasks>
                      <RakeTaskImpl description="Run the &quot;down&quot; for a given migration VERSION" fullCommand="app:db:migrate:down" id="down" />
                      <RakeTaskImpl description="Roll back the database one migration and re-migrate up (options: STEP=x, VERSION=x)" fullCommand="app:db:migrate:redo" id="redo" />
                      <RakeTaskImpl description="Display status of migrations" fullCommand="app:db:migrate:status" id="status" />
                      <RakeTaskImpl description="Run the &quot;up&quot; for a given migration VERSION" fullCommand="app:db:migrate:up" id="up" />
                      <RakeTaskImpl description="" fullCommand="app:db:migrate:reset" id="reset" />
                    </subtasks>
                  </RakeTaskImpl>
                  <RakeTaskImpl description="Run setup if database does not exist, or run migrations if it does" fullCommand="app:db:prepare" id="prepare" />
                  <RakeTaskImpl description="Drop and recreate all databases from their schema for the current environment and load the seeds" fullCommand="app:db:reset" id="reset" />
                  <RakeTaskImpl description="Roll the schema back to the previous version (specify steps w/ STEP=n)" fullCommand="app:db:rollback" id="rollback" />
                  <RakeTaskImpl id="schema">
                    <subtasks>
                      <RakeTaskImpl id="cache">
                        <subtasks>
                          <RakeTaskImpl description="Clear a db/schema_cache.yml file" fullCommand="app:db:schema:cache:clear" id="clear" />
                          <RakeTaskImpl description="Create a db/schema_cache.yml file" fullCommand="app:db:schema:cache:dump" id="dump" />
                        </subtasks>
                      </RakeTaskImpl>
                      <RakeTaskImpl description="Create a database schema file (either db/schema.rb or db/structure.sql, depending on `ENV['SCHEMA_FORMAT']` or `config.active_record.schema_format`)" fullCommand="app:db:schema:dump" id="dump" />
                      <RakeTaskImpl description="Load a database schema file (either db/schema.rb or db/structure.sql, depending on `ENV['SCHEMA_FORMAT']` or `config.active_record.schema_format`) into the database" fullCommand="app:db:schema:load" id="load" />
                    </subtasks>
                  </RakeTaskImpl>
                  <RakeTaskImpl description="Load the seed data from db/seeds.rb" fullCommand="app:db:seed" id="seed" />
                  <RakeTaskImpl id="seed">
                    <subtasks>
                      <RakeTaskImpl description="Truncate tables of each database for current environment and load the seeds" fullCommand="app:db:seed:replant" id="replant" />
                    </subtasks>
                  </RakeTaskImpl>
                  <RakeTaskImpl description="Create all databases, load all schemas, and initialize with the seed data (use db:reset to also drop all databases first)" fullCommand="app:db:setup" id="setup" />
                  <RakeTaskImpl description="Retrieve the current schema version number" fullCommand="app:db:version" id="version" />
                  <RakeTaskImpl description="" fullCommand="app:db:_dump" id="_dump" />
                  <RakeTaskImpl description="" fullCommand="app:db:abort_if_pending_migrations" id="abort_if_pending_migrations" />
                  <RakeTaskImpl description="" fullCommand="app:db:charset" id="charset" />
                  <RakeTaskImpl description="" fullCommand="app:db:check_protected_environments" id="check_protected_environments" />
                  <RakeTaskImpl description="" fullCommand="app:db:collation" id="collation" />
                  <RakeTaskImpl id="create">
                    <subtasks>
                      <RakeTaskImpl description="" fullCommand="app:db:create:all" id="all" />
                    </subtasks>
                  </RakeTaskImpl>
                  <RakeTaskImpl id="drop">
                    <subtasks>
                      <RakeTaskImpl description="" fullCommand="app:db:drop:_unsafe" id="_unsafe" />
                      <RakeTaskImpl description="" fullCommand="app:db:drop:all" id="all" />
                    </subtasks>
                  </RakeTaskImpl>
                  <RakeTaskImpl description="" fullCommand="app:db:forward" id="forward" />
                  <RakeTaskImpl description="" fullCommand="app:db:load_config" id="load_config" />
                  <RakeTaskImpl description="" fullCommand="app:db:purge" id="purge" />
                  <RakeTaskImpl id="purge">
                    <subtasks>
                      <RakeTaskImpl description="" fullCommand="app:db:purge:all" id="all" />
                    </subtasks>
                  </RakeTaskImpl>
                  <RakeTaskImpl id="reset">
                    <subtasks>
                      <RakeTaskImpl description="" fullCommand="app:db:reset:all" id="all" />
                    </subtasks>
                  </RakeTaskImpl>
                  <RakeTaskImpl id="setup">
                    <subtasks>
                      <RakeTaskImpl description="" fullCommand="app:db:setup:all" id="all" />
                    </subtasks>
                  </RakeTaskImpl>
                  <RakeTaskImpl id="test">
                    <subtasks>
                      <RakeTaskImpl description="" fullCommand="app:db:test:load_schema" id="load_schema" />
                      <RakeTaskImpl description="" fullCommand="app:db:test:prepare" id="prepare" />
                      <RakeTaskImpl description="" fullCommand="app:db:test:purge" id="purge" />
                    </subtasks>
                  </RakeTaskImpl>
                  <RakeTaskImpl description="" fullCommand="app:db:truncate_all" id="truncate_all" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="log">
                <subtasks>
                  <RakeTaskImpl description="Truncate all/specified *.log files in log/ to zero bytes (specify which logs with LOGS=test,development)" fullCommand="app:log:clear" id="clear" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="rails_action_core_engine">
                <subtasks>
                  <RakeTaskImpl id="install">
                    <subtasks>
                      <RakeTaskImpl description="Copy migrations from rails_action_core_engine to application" fullCommand="app:rails_action_core_engine:install:migrations" id="migrations" />
                    </subtasks>
                  </RakeTaskImpl>
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="rails_bpm_engine">
                <subtasks>
                  <RakeTaskImpl id="install">
                    <subtasks>
                      <RakeTaskImpl description="Copy migrations from rails_bpm_engine to application" fullCommand="app:rails_bpm_engine:install:migrations" id="migrations" />
                    </subtasks>
                  </RakeTaskImpl>
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="rails_com_engine">
                <subtasks>
                  <RakeTaskImpl id="install">
                    <subtasks>
                      <RakeTaskImpl description="Copy migrations from rails_com_engine to application" fullCommand="app:rails_com_engine:install:migrations" id="migrations" />
                    </subtasks>
                  </RakeTaskImpl>
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="rails_data_engine">
                <subtasks>
                  <RakeTaskImpl id="install">
                    <subtasks>
                      <RakeTaskImpl description="Copy migrations from rails_data_engine to application" fullCommand="app:rails_data_engine:install:migrations" id="migrations" />
                    </subtasks>
                  </RakeTaskImpl>
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="rails_notify_engine">
                <subtasks>
                  <RakeTaskImpl id="install">
                    <subtasks>
                      <RakeTaskImpl description="Copy migrations from rails_notify_engine to application" fullCommand="app:rails_notify_engine:install:migrations" id="migrations" />
                    </subtasks>
                  </RakeTaskImpl>
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="rails_permit_engine">
                <subtasks>
                  <RakeTaskImpl id="install">
                    <subtasks>
                      <RakeTaskImpl description="Copy migrations from rails_permit_engine to application" fullCommand="app:rails_permit_engine:install:migrations" id="migrations" />
                    </subtasks>
                  </RakeTaskImpl>
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="rails_reg_engine">
                <subtasks>
                  <RakeTaskImpl id="install">
                    <subtasks>
                      <RakeTaskImpl description="Copy migrations from rails_reg_engine to application" fullCommand="app:rails_reg_engine:install:migrations" id="migrations" />
                    </subtasks>
                  </RakeTaskImpl>
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="rails_res_engine">
                <subtasks>
                  <RakeTaskImpl id="install">
                    <subtasks>
                      <RakeTaskImpl description="Copy migrations from rails_res_engine to application" fullCommand="app:rails_res_engine:install:migrations" id="migrations" />
                    </subtasks>
                  </RakeTaskImpl>
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="rails_serve_engine">
                <subtasks>
                  <RakeTaskImpl id="install">
                    <subtasks>
                      <RakeTaskImpl description="Copy migrations from rails_serve_engine to application" fullCommand="app:rails_serve_engine:install:migrations" id="migrations" />
                    </subtasks>
                  </RakeTaskImpl>
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="rails_state_engine">
                <subtasks>
                  <RakeTaskImpl id="install">
                    <subtasks>
                      <RakeTaskImpl description="Copy migrations from rails_state_engine to application" fullCommand="app:rails_state_engine:install:migrations" id="migrations" />
                    </subtasks>
                  </RakeTaskImpl>
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="rails_tofu_engine">
                <subtasks>
                  <RakeTaskImpl id="install">
                    <subtasks>
                      <RakeTaskImpl description="Copy migrations from rails_tofu_engine to application" fullCommand="app:rails_tofu_engine:install:migrations" id="migrations" />
                    </subtasks>
                  </RakeTaskImpl>
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="route">
                <subtasks>
                  <RakeTaskImpl description="export routes of current engine" fullCommand="app:route:export" id="export" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl description="Run all specs in spec directory (excluding plugin specs)" fullCommand="app:spec" id="spec" />
              <RakeTaskImpl id="spec">
                <subtasks>
                  <RakeTaskImpl description="Run the code examples in spec/models" fullCommand="app:spec:models" id="models" />
                  <RakeTaskImpl description="Run the code examples in spec/requests" fullCommand="app:spec:requests" id="requests" />
                  <RakeTaskImpl description="" fullCommand="app:spec:prepare" id="prepare" />
                  <RakeTaskImpl description="" fullCommand="app:spec:statsetup" id="statsetup" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl description="Regenerate Swagger docs" fullCommand="app:swagger" id="swagger" />
              <RakeTaskImpl id="time">
                <subtasks>
                  <RakeTaskImpl description="List all time zones, list by two-letter country code (`bin/rails time:zones[US]`), or list by UTC offset (`bin/rails time:zones[-8]`)" fullCommand="app:time:zones[country_or_offset]" id="zones[country_or_offset]" />
                  <RakeTaskImpl description="" fullCommand="app:time:zones" id="zones" />
                  <RakeTaskImpl id="zones">
                    <subtasks>
                      <RakeTaskImpl description="" fullCommand="app:time:zones:all" id="all" />
                      <RakeTaskImpl description="" fullCommand="app:time:zones:local" id="local" />
                      <RakeTaskImpl description="" fullCommand="app:time:zones:us" id="us" />
                    </subtasks>
                  </RakeTaskImpl>
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="tmp">
                <subtasks>
                  <RakeTaskImpl description="Clear cache, socket and screenshot files from tmp/ (narrow w/ tmp:cache:clear, tmp:sockets:clear, tmp:screenshots:clear)" fullCommand="app:tmp:clear" id="clear" />
                  <RakeTaskImpl description="Create tmp directories for cache, sockets, and pids" fullCommand="app:tmp:create" id="create" />
                  <RakeTaskImpl id="cache">
                    <subtasks>
                      <RakeTaskImpl description="" fullCommand="app:tmp:cache:clear" id="clear" />
                    </subtasks>
                  </RakeTaskImpl>
                  <RakeTaskImpl id="pids">
                    <subtasks>
                      <RakeTaskImpl description="" fullCommand="app:tmp:pids:clear" id="clear" />
                    </subtasks>
                  </RakeTaskImpl>
                  <RakeTaskImpl id="screenshots">
                    <subtasks>
                      <RakeTaskImpl description="" fullCommand="app:tmp:screenshots:clear" id="clear" />
                    </subtasks>
                  </RakeTaskImpl>
                  <RakeTaskImpl id="sockets">
                    <subtasks>
                      <RakeTaskImpl description="" fullCommand="app:tmp:sockets:clear" id="clear" />
                    </subtasks>
                  </RakeTaskImpl>
                  <RakeTaskImpl id="storage">
                    <subtasks>
                      <RakeTaskImpl description="" fullCommand="app:tmp:storage:clear" id="clear" />
                    </subtasks>
                  </RakeTaskImpl>
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl description="Update some initially generated files" fullCommand="app:update" id="update" />
              <RakeTaskImpl id="yarn">
                <subtasks>
                  <RakeTaskImpl description="Install all JavaScript dependencies as specified via Yarn" fullCommand="app:yarn:install" id="install" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="zeitwerk">
                <subtasks>
                  <RakeTaskImpl description="Check project structure for Zeitwerk compatibility" fullCommand="app:zeitwerk:check" id="check" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl description="" fullCommand="app:default" id="default" />
              <RakeTaskImpl description="" fullCommand="app:environment" id="environment" />
              <RakeTaskImpl id="railties">
                <subtasks>
                  <RakeTaskImpl id="install">
                    <subtasks>
                      <RakeTaskImpl description="" fullCommand="app:railties:install:migrations" id="migrations" />
                    </subtasks>
                  </RakeTaskImpl>
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl description="" fullCommand="app:stats" id="stats" />
              <RakeTaskImpl id="update">
                <subtasks>
                  <RakeTaskImpl description="" fullCommand="app:update:bin" id="bin" />
                </subtasks>
              </RakeTaskImpl>
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl description="Build rails_serve-0.1.0.gem into the pkg directory" fullCommand="build" id="build" />
          <RakeTaskImpl id="build">
            <subtasks>
              <RakeTaskImpl description="Generate SHA512 checksum if rails_serve-0.1.0.gem into the checksums directory" fullCommand="build:checksum" id="checksum" />
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl description="Remove any temporary products" fullCommand="clean" id="clean" />
          <RakeTaskImpl description="Remove any generated files" fullCommand="clobber" id="clobber" />
          <RakeTaskImpl id="db">
            <subtasks>
              <RakeTaskImpl description="Create the database from config/database.yml for the current Rails.env (use db:create:all to create all databases in the config)" fullCommand="db:create" id="create" />
              <RakeTaskImpl description="Drop the database for the current Rails.env (use db:drop:all to drop all databases)" fullCommand="db:drop" id="drop" />
              <RakeTaskImpl id="fixtures">
                <subtasks>
                  <RakeTaskImpl description="Load fixtures into the current environment's database" fullCommand="db:fixtures:load" id="load" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl description="Migrate the database (options: VERSION=x, VERBOSE=false)" fullCommand="db:migrate" id="migrate" />
              <RakeTaskImpl id="migrate">
                <subtasks>
                  <RakeTaskImpl description="Display status of migrations" fullCommand="db:migrate:status" id="status" />
                  <RakeTaskImpl description="" fullCommand="db:migrate:down" id="down" />
                  <RakeTaskImpl description="" fullCommand="db:migrate:redo" id="redo" />
                  <RakeTaskImpl description="" fullCommand="db:migrate:reset" id="reset" />
                  <RakeTaskImpl description="" fullCommand="db:migrate:up" id="up" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl description="Roll the schema back to the previous version (specify steps w/ STEP=n)" fullCommand="db:rollback" id="rollback" />
              <RakeTaskImpl id="schema">
                <subtasks>
                  <RakeTaskImpl description="Create a database schema file (either db/schema.rb or db/structure.sql, depending on `config.active_record.schema_format`)" fullCommand="db:schema:dump" id="dump" />
                  <RakeTaskImpl description="Load a schema.rb file into the database" fullCommand="db:schema:load" id="load" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl description="Load the seed data from db/seeds.rb" fullCommand="db:seed" id="seed" />
              <RakeTaskImpl description="Create the database, load the schema, and initialize with the seed data (use db:reset to also drop the database first)" fullCommand="db:setup" id="setup" />
              <RakeTaskImpl description="Retrieve the current schema version number" fullCommand="db:version" id="version" />
              <RakeTaskImpl id="create">
                <subtasks>
                  <RakeTaskImpl description="" fullCommand="db:create:all" id="all" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl id="drop">
                <subtasks>
                  <RakeTaskImpl description="" fullCommand="db:drop:all" id="all" />
                </subtasks>
              </RakeTaskImpl>
              <RakeTaskImpl description="" fullCommand="db:reset" id="reset" />
              <RakeTaskImpl id="test">
                <subtasks>
                  <RakeTaskImpl description="" fullCommand="db:test:prepare" id="prepare" />
                </subtasks>
              </RakeTaskImpl>
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl description="Build and install rails_serve-0.1.0.gem into system gems" fullCommand="install" id="install" />
          <RakeTaskImpl id="install">
            <subtasks>
              <RakeTaskImpl description="Build and install rails_serve-0.1.0.gem into system gems without network access" fullCommand="install:local" id="local" />
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl description="Create tag v0.1.0 and build and push rails_serve-0.1.0.gem to Set to 'http://mygemserver.com'" fullCommand="release[remote]" id="release[remote]" />
          <RakeTaskImpl description="Run all specs in spec directory (excluding plugin specs)" fullCommand="spec" id="spec" />
          <RakeTaskImpl description="Report code statistics (KLOCs, etc) from the application or engine" fullCommand="stats" id="stats" />
          <RakeTaskImpl description="" fullCommand="default" id="default" />
          <RakeTaskImpl description="" fullCommand="environment" id="environment" />
          <RakeTaskImpl description="" fullCommand="load_app" id="load_app" />
          <RakeTaskImpl description="" fullCommand="release" id="release" />
          <RakeTaskImpl id="release">
            <subtasks>
              <RakeTaskImpl description="" fullCommand="release:guard_clean" id="guard_clean" />
              <RakeTaskImpl description="" fullCommand="release:rubygem_push" id="rubygem_push" />
              <RakeTaskImpl description="" fullCommand="release:source_control_push" id="source_control_push" />
            </subtasks>
          </RakeTaskImpl>
          <RakeTaskImpl description="" fullCommand="tmp" id="tmp" />
          <RakeTaskImpl description="" fullCommand="tmp/cache" id="tmp/cache" />
          <RakeTaskImpl description="" fullCommand="tmp/cache/assets" id="tmp/cache/assets" />
          <RakeTaskImpl description="" fullCommand="tmp/pids" id="tmp/pids" />
          <RakeTaskImpl description="" fullCommand="tmp/sockets" id="tmp/sockets" />
        </subtasks>
      </RakeTaskImpl>
    </option>
  </component>
</module>