#!/usr/bin/env ruby

# 测试标签搜索修复效果
query = ARGV[0] || "今年有多少招投标类的素材？"

puts "测试标签搜索修复效果: #{query}"
puts "=" * 60

begin
  # 1. 测试RansackDsl转换
  puts "1. 测试RansackDsl转换..."
  transformer = Bot::Transformers::RansackDsl.new(Serve::Activity)
  conditions = transformer.transform(query)
  puts "转换后的条件: #{conditions.inspect}"
  
  # 2. 测试数据库查询
  puts "\n2. 测试数据库查询..."
  base_query = Serve::Activity.all
  ransack_query = base_query.ransack(conditions)
  records = ransack_query.result
  total_count = records.count
  
  puts "查询结果总数: #{total_count}"
  puts "生成的SQL: #{records.to_sql}"
  
  # 3. 显示查询结果详情
  if total_count > 0
    puts "\n3. 查询结果详情..."
    records.each_with_index do |record, index|
      puts "#{index + 1}. #{record.name}"
      puts "   发布时间: #{record.published_at}"
      puts "   创建时间: #{record.created_at}"
      
      # 检查AI标签
      ai_tags = record.ai_tags.where("name ILIKE ?", "%招投标%")
      puts "   AI标签: #{ai_tags.pluck(:name)}" if ai_tags.any?
      
      # 检查用户标签
      user_tags = record.tags.where("name ILIKE ?", "%招投标%")
      puts "   用户标签: #{user_tags.pluck(:name)}" if user_tags.any?
      
      # 检查标题匹配
      if record.name.include?("招投标")
        puts "   标题匹配: ✅"
      end
      
      puts ""
    end
  end
  
  # 4. 测试ActivityQueryTool
  puts "4. 测试ActivityQueryTool..."
  tool = Bot::Tools::ActivityQueryTool.new
  tool_result = tool.query_activities(query: query)
  
  puts "工具返回结果:"
  puts "- 总数: #{tool_result[:total_count]}"
  puts "- 消息: #{tool_result[:message]}"
  puts "- 条件: #{tool_result[:conditions]}"
  
  # 5. 验证数据一致性
  puts "\n5. 验证数据一致性..."
  manual_count = total_count
  tool_count = tool_result[:total_count]
  
  puts "手动查询结果: #{manual_count}"
  puts "工具查询结果: #{tool_count}"
  
  if manual_count == tool_count
    puts "✅ 数据一致性验证通过"
  else
    puts "❌ 数据一致性验证失败"
  end
  
  # 6. 检查查询条件类型
  puts "\n6. 检查查询条件类型..."
  conditions_str = conditions.to_s
  
  if conditions_str.include?('ai_tags') && conditions_str.include?('tags')
    puts "✅ 同时搜索AI标签和用户标签"
  elsif conditions_str.include?('ai_tags')
    puts "⚠️  只搜索AI标签"
  elsif conditions_str.include?('tags')
    puts "⚠️  只搜索用户标签"
  else
    puts "❌ 没有搜索标签"
  end
  
  if conditions_str.include?('name_cont')
    puts "✅ 包含标题搜索"
  else
    puts "⚠️  没有标题搜索"
  end
  
  if conditions_str.include?('"m"=>"or"') || conditions_str.include?(':m=>"or"')
    puts "✅ 使用OR逻辑组合"
  else
    puts "⚠️  使用AND逻辑组合"
  end
  
  # 7. 对比期望结果
  puts "\n7. 对比期望结果..."
  puts "期望结果: 2个素材（基于手动调试）"
  puts "实际结果: #{tool_count}个素材"
  
  if tool_count >= 2
    puts "✅ 结果符合期望或更好"
  else
    puts "❌ 结果少于期望，可能还有遗漏"
  end

rescue => e
  puts "❌ 测试失败: #{e.message}"
  puts e.backtrace.first(10)
end
