{"key": "container_layout_1708503238518_1", "type": "container_layout", "model": {"create_default_value": {}, "update_default_value": {}}, "rules": [], "fields": [{"key": "TaTemplateFormDesignerKey-1", "type": "layout", "model": {}, "rules": [], "fields": [{"key": "input_1708503240723_2", "name": "名称", "type": "input", "model": {"summary": true, "attr_type": "string"}, "rules": [{"type": "string", "message": "请填写正确的名称", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 24}, "model_key": "name", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "image_single_new_1708503247560_3", "name": "封面图", "type": "image_single_new", "model": {"summary": true, "attr_type": "array"}, "rules": [{"type": "array", "message": "请填写正确的封面图", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"size": "regular", "span": 24}, "model_key": "cover_image.files", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "radio_1712136002606_0", "name": "关联对象", "type": "radio", "model": {"attr_type": "string"}, "rules": [], "fields": [], "actions": [], "options": {"span": 24, "select": [{"label": "活动", "value": "Serve::Activity"}, {"label": "H5", "value": ""}], "multiple": false, "table_items": [], "import_export_headers": [], "display_configurable_form": {}}, "model_key": "source_type", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "condition_1712136845679_2", "name": "条件块", "type": "condition", "rules": [], "fields": [], "actions": [], "options": {"span": 24}, "model_key": "condition_1712136845679_2", "conditions": [{"val": "", "name": "条件1", "type": "complex", "fields": [{"key": "api_single_1712136173224_1", "name": "关联活动", "type": "api_single", "model": {"attr_type": "number"}, "rules": [], "fields": [], "actions": [], "options": {"path": "/serve/manage/user/activities", "span": 24, "table_items": [{"name": "名称", "type": "string", "search": true, "data_index": "name"}], "import_export_headers": [], "display_configurable_form": {}}, "model_key": "source_id", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}], "model_key": "", "complex_condition": {"groups": [{"items": [{"_id": "1712136858738_0", "desc": {"name": "关联对象", "optZh": "含有", "template": {"key": "key", "type": "layout", "fields": [{"key": "ditto_1712136866071_3", "icon": "FolderOutlined", "name": "值", "type": "select", "model": {"attr_type": "array"}, "rules": [], "fields": [], "options": {"span": 24, "ditto": "ditto_single", "select": [{"label": "活动", "value": "Serve::Activity"}, {"label": "H5", "value": ""}], "disabled": false, "multiple": true, "table_items": [], "formDisabled": false, "accessibility": "writeable", "dynamic_component": "", "import_export_headers": [], "display_configurable_form": {}}, "model_key": "val", "conditions": [], "model_key_prefix": "rule", "model_key_configuration": []}]}, "modelValue": {"rule": {"val": ["Serve::Activity"]}}}, "rule": {"key": "source_type", "opt": "include", "val": ["Serve::Activity"], "type": "Com::Attr::ConditionRules::SingleChoice", "key_name": "关联对象"}}]}]}}, {"val": "", "name": "条件2", "type": "complex", "fields": [{"key": "input_1712136940614_5", "name": "链接", "type": "input", "model": {"attr_type": "string"}, "rules": [], "fields": [], "actions": [], "options": {"span": 24}, "model_key": "model_payload.url", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}], "model_key": "", "complex_condition": {"groups": [{"items": [{"_id": "1712136982188_1", "desc": {"name": "关联对象", "optZh": "含有", "template": {"key": "key", "type": "layout", "fields": [{"key": "ditto_1712136990703_6", "icon": "FolderOutlined", "name": "值", "type": "select", "model": {"attr_type": "array"}, "rules": [], "fields": [], "options": {"span": 24, "ditto": "ditto_single", "select": [{"label": "活动", "value": "Serve::Activity"}, {"label": "H5", "value": ""}], "disabled": false, "multiple": true, "table_items": [], "formDisabled": false, "accessibility": "writeable", "dynamic_component": "", "import_export_headers": [], "display_configurable_form": {}}, "model_key": "val", "conditions": [], "model_key_prefix": "rule", "model_key_configuration": []}]}, "modelValue": {"rule": {"val": ["H5"]}}}, "rule": {"key": "source_type", "opt": "include", "val": ["H5"], "type": "Com::Attr::ConditionRules::SingleChoice", "key_name": "关联对象"}}]}]}}], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}], "actions": [], "options": {}, "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}], "actions": [{"key": "create", "enabled": true}, {"key": "update", "enabled": true}, {"key": "delete", "enabled": true}, {"key": "import", "enabled": true}, {"key": "export", "enabled": true}], "options": {"label": {}, "theme": {"card": {"content": "<p><br></p>"}, "form": {}, "background": {}}, "searcher": [], "create_text": "提交", "update_text": "提交", "disabled_actions": {}}, "model_key": "container_layout_1708503238518_1", "conditions": [], "index_attributes": [], "column_attributes": [{"_id": "image_single_new_1708503247560_3", "index": {"on": true}, "title": ["封面图"], "export": {"on": true}, "import": {"on": true}, "filters": [], "dataIndex": "cover_image.files", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "input_1708503240723_2", "index": {"on": true}, "title": ["名称"], "export": {"on": true}, "import": {"on": true}, "filters": [], "dataIndex": "name", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}], "model_key_configuration": []}