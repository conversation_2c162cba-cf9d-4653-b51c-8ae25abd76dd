{"key": "container_layout_1708504281375_1", "type": "container_layout", "model": {"create_default_value": {}, "update_default_value": {}}, "rules": [], "fields": [{"key": "TaTemplateFormDesignerKey-1", "type": "layout", "model": {}, "rules": [], "fields": [{"key": "time_range_new_1708505187397_7", "name": "时间范围", "type": "time_range_new", "model": {"summary": true, "attr_type": "array"}, "rules": [], "fields": [], "actions": [], "options": {"span": 24}, "model_key": "time_range_new_1708505187397_7", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": [{"key": "start_at", "label": "开始时间", "model_key": "effective_at", "custom_label": "开始时间"}, {"key": "end_at", "label": "结束时间", "model_key": "invalid_at", "custom_label": "结束时间"}]}, {"key": "input_1708504283374_2", "name": "活动名称", "type": "input", "model": {"summary": true, "attr_type": "string"}, "rules": [{"type": "string", "message": "请填写正确的活动名称", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 24}, "model_key": "name", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "select_1708505278837_10", "name": "活动类型", "type": "select", "model": {"summary": true, "attr_type": "string"}, "rules": [{"type": "string", "message": "请填写正确的活动类型", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 24, "select": [{"label": "需审批活动", "value": "Serve::BpmActivity"}, {"label": "报名活动", "value": "Serve::ExpActivity"}, {"label": "预约活动", "value": "Serve::OperActivity"}, {"label": "银泰365", "value": "Serve::CouponActivity"}, {"label": "人才房摇号", "value": "Serve::TalentActivity"}, {"label": "公众号活动", "value": "Serve::WechatActivity"}, {"label": "展示活动", "value": "Serve::ArticleActivity"}], "multiple": false, "table_items": [], "import_export_headers": [{"_id": "1708505283326_1"}], "display_configurable_form": {}}, "model_key": "type", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "input_1712066293929_1", "name": "活动简称", "type": "input", "model": {"attr_type": "string"}, "rules": [], "fields": [], "actions": [], "options": {"span": 24}, "model_key": "model_payload.short_name", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "select_1708505114547_6", "name": "发布状态", "type": "select", "model": {"summary": true, "attr_type": "string"}, "rules": [], "fields": [], "actions": [], "options": {"span": 24, "select": [{"label": "已发布", "value": "published"}, {"label": "未发布", "value": "pending"}], "multiple": false, "viewHidden": false, "table_items": [], "formDisabled": false, "import_export_headers": [{"_id": "1708505118224_0"}], "display_configurable_form": {}}, "model_key": "state", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "radio_1712064478475_1", "name": "发布类型", "type": "radio", "model": {"attr_type": "string"}, "rules": [], "fields": [], "actions": [], "options": {"span": 12, "select": [{"label": "部门", "value": "Department"}], "multiple": false, "table_items": [], "import_export_headers": [{"_id": "1712064480863_0"}], "display_configurable_form": {}}, "model_key": "target_type", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "condition_1712064515921_2", "name": "条件块", "type": "condition", "rules": [], "fields": [], "actions": [], "options": {"span": 12}, "model_key": "condition_1712064515921_2", "conditions": [{"val": "", "name": "条件1", "type": "complex", "fields": [{"key": "api_single_1712064522468_3", "name": "发布部门", "type": "api_single", "model": {"attr_type": "number"}, "rules": [], "fields": [], "actions": [], "options": {"path": "/res/user/departments", "span": 24, "table_items": [{"name": "名称", "type": "string", "search": true, "data_index": "name"}], "import_export_headers": [{"_id": "1712064523978_1"}], "display_configurable_form": {}}, "model_key": "target_id", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}], "model_key": "", "complex_condition": {"groups": [{"items": [{"_id": "1712064572166_2", "desc": {"name": "发布类型", "optZh": "含有", "template": {"key": "key", "type": "layout", "fields": [{"key": "ditto_1712064576231_4", "icon": "FolderOutlined", "name": "值", "type": "select", "model": {"attr_type": "array"}, "rules": [], "fields": [], "options": {"span": 24, "ditto": "ditto_single", "select": [{"label": "部门", "value": "Department"}], "disabled": false, "multiple": true, "table_items": [], "formDisabled": false, "accessibility": "writeable", "dynamic_component": "", "import_export_headers": [{"_id": "1712064480863_0"}], "display_configurable_form": {}}, "model_key": "val", "conditions": [], "model_key_prefix": "rule", "model_key_configuration": []}]}, "modelValue": {"rule": {"val": ["Department"]}}}, "rule": {"key": "target_type", "opt": "include", "val": ["Department"], "type": "Com::Attr::ConditionRules::SingleChoice", "key_name": "发布类型"}}]}]}}], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "api_1708743051220_2", "name": "关联分类", "type": "api", "model": {"attr_type": "array"}, "rules": [], "fields": [], "actions": [], "options": {"path": "/serve/manage/submodules/${submoduleId}/catalogs", "span": 24, "multiple": true, "table_items": [{"name": "分类名称", "type": "string", "search": true, "data_index": "name"}, {"name": "封面图", "type": "string", "search": false, "data_index": "cover_image.files"}], "import_export_headers": [{"_id": "1708743054565_1"}], "display_configurable_form": {}}, "model_key": "catalog_ids", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "api_1708743196142_3", "name": "关联标签", "type": "api", "model": {"attr_type": "array"}, "rules": [], "fields": [], "actions": [], "options": {"path": "/serve/manage/submodules/${submoduleId}/tags", "span": 24, "multiple": true, "table_items": [{"name": "标签名称", "type": "string", "search": true, "data_index": "name"}, {"name": "封面图", "type": "string", "search": false, "data_index": "cover_image.files"}], "import_export_headers": [{"_id": "1708743197905_2"}], "display_configurable_form": {}}, "model_key": "tag_ids", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "image_single_new_1712051732543_2", "name": "图标", "type": "image_single_new", "model": {"attr_type": "array"}, "rules": [], "fields": [], "actions": [], "options": {"size": "regular", "span": 24, "fileSize": 3}, "model_key": "icon.files", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "image_single_new_1708504908547_3", "name": "封面图", "type": "image_single_new", "model": {"summary": true, "attr_type": "array"}, "rules": [], "fields": [], "actions": [], "options": {"size": "regular", "span": 24}, "model_key": "cover_image.files", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "content_1708504952410_4", "name": "详情", "type": "content", "model": {"attr_type": "array"}, "rules": [], "fields": [], "actions": [], "options": {"span": 24}, "model_key": "content.content", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "switch_1709878276856_0", "name": "热门", "type": "switch", "model": {"attr_type": "boolean"}, "rules": [], "fields": [], "actions": [], "options": {"span": 12, "select": [{"label": "", "value": ""}, {"label": "", "value": ""}], "multiple": false}, "model_key": "is_hotted", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "switch_1709025737469_8", "name": "开启地图", "type": "switch", "model": {"attr_type": "boolean"}, "rules": [], "fields": [], "actions": [], "options": {"span": 12, "select": [{"label": "", "value": ""}, {"label": "", "value": ""}], "multiple": false}, "model_key": "model_payload.geo_enable", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "condition_1709025911178_11", "name": "条件块", "type": "condition", "rules": [], "fields": [], "actions": [], "options": {"span": 24}, "model_key": "condition_1709025911178_11", "conditions": [{"val": "", "name": "条件1", "type": "complex", "fields": [{"key": "input_1709025836577_10", "name": "地址", "type": "input", "model": {"attr_type": "string"}, "rules": [], "fields": [], "actions": [], "options": {"span": 24}, "model_key": "model_payload.geo_address", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "map_1709026561379_0", "name": "地图", "type": "map", "model": {"attr_type": "object"}, "rules": [], "fields": [], "actions": [], "options": {"span": 24}, "model_key": "map_info", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}], "model_key": "", "complex_condition": {"groups": [{"items": [{"_id": "1709026700161_0", "desc": {"name": "开启地图", "optZh": "等于", "template": {"key": "key", "type": "layout", "fields": [{"key": "switch_1632802235314_19", "name": "值", "type": "switch", "model": {"attr_type": "boolean"}, "rules": [], "fields": [], "options": {"span": 24, "placeholder": ""}, "model_key": "val", "conditions": [], "model_key_prefix": "rule"}]}, "modelValue": {"rule": {"val": true}}}, "rule": {"key": "model_payload.geo_enable", "opt": "==", "val": true, "type": "Com::Attr::ConditionRules::Boolean", "key_name": "开启地图"}}]}]}}], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "condition_1712062741768_1", "name": "条件块", "type": "condition", "rules": [], "fields": [], "actions": [], "options": {"span": 24}, "model_key": "condition_1712062741768_1", "conditions": [{"val": "", "name": "条件1", "type": "complex", "fields": [{"key": "input_1712062706702_0", "name": "跳转链接", "type": "input", "model": {"attr_type": "string"}, "rules": [{"type": "string", "message": "请填写正确的跳转链接", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 24}, "model_key": "model_payload.url", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}], "model_key": "", "complex_condition": {"groups": [{"items": [{"_id": "1712062752924_0", "desc": {"name": "活动类型", "optZh": "含有", "template": {"key": "key", "type": "layout", "fields": [{"key": "ditto_1712062769964_2", "name": "值", "type": "select", "model": {"summary": true, "attr_type": "array"}, "rules": [], "fields": [], "actions": [], "options": {"span": 24, "ditto": "ditto_single", "select": [{"label": "需审批活动", "value": "Serve::BpmActivity"}, {"label": "报名活动", "value": "Serve::ExpActivity"}, {"label": "预约活动", "value": "Serve::OperActivity"}, {"label": "银泰365", "value": "Serve::CouponActivity"}, {"label": "人才房摇号", "value": "Serve::TalentActivity"}, {"label": "公众号活动", "value": "Serve::WechatActivity"}, {"label": "展示活动", "value": "Serve::ArticleActivity"}], "disabled": false, "multiple": true, "table_items": [], "formDisabled": false, "accessibility": "writeable", "dynamic_component": "", "import_export_headers": [{"_id": "1708505283326_1"}], "display_configurable_form": {}}, "model_key": "val", "conditions": [], "index_attributes": [], "model_key_prefix": "rule", "column_attributes": [], "model_key_configuration": []}]}, "modelValue": {"rule": {"val": ["Serve::WechatActivity"]}}}, "rule": {"key": "type", "opt": "include", "val": ["Serve::WechatActivity"], "type": "Com::Attr::ConditionRules::SingleChoice", "key_name": "活动类型"}}]}]}}], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "switch_1709025509574_0", "name": "开启查看权限", "type": "switch", "model": {"attr_type": "boolean"}, "rules": [], "fields": [], "actions": [], "options": {"span": 12, "select": [{"label": "", "value": ""}, {"label": "", "value": ""}], "multiple": false}, "model_key": "view_enable", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "condition_1709025591865_2", "name": "条件块", "type": "condition", "rules": [], "fields": [], "actions": [], "options": {"span": 12}, "model_key": "condition_1709025591865_2", "conditions": [{"val": "", "name": "条件1", "type": "complex", "fields": [{"key": "res_permit_polymorphic_1708743378748_5", "name": "查看权限", "type": "res_permit_polymorphic", "model": {"attr_type": "array"}, "rules": [], "fields": [], "actions": [], "options": {"span": 24}, "model_key": "view_serve_permit_actions", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}], "model_key": "", "complex_condition": {"groups": [{"items": [{"_id": "1709025604300_0", "desc": {"name": "开启查看权限", "optZh": "等于", "template": {"key": "key", "type": "layout", "fields": [{"key": "switch_1632802235314_19", "name": "值", "type": "switch", "model": {"attr_type": "boolean"}, "rules": [], "fields": [], "options": {"span": 24, "placeholder": ""}, "model_key": "val", "conditions": [], "model_key_prefix": "rule"}]}, "modelValue": {"rule": {"val": true}}}, "rule": {"key": "view_enable", "opt": "==", "val": true, "type": "Com::Attr::ConditionRules::Boolean", "key_name": "开启查看权限"}}]}]}}], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "switch_1709025526531_1", "name": "开启使用权限", "type": "switch", "model": {"attr_type": "boolean"}, "rules": [], "fields": [], "actions": [], "options": {"span": 12, "select": [{"label": "", "value": ""}, {"label": "", "value": ""}], "multiple": false}, "model_key": "use_enable", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "condition_1709025648126_5", "name": "条件块", "type": "condition", "rules": [], "fields": [], "actions": [], "options": {"span": 12}, "model_key": "condition_1709025648126_5", "conditions": [{"val": "", "name": "条件1", "type": "complex", "fields": [{"key": "res_permit_polymorphic_1708743367303_4", "name": "使用权限", "type": "res_permit_polymorphic", "model": {"attr_type": "array"}, "rules": [], "fields": [], "actions": [], "options": {"span": 24}, "model_key": "use_serve_permit_actions", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}], "model_key": "", "complex_condition": {"groups": [{"items": [{"_id": "1709025672786_1", "desc": {"name": "开启使用权限", "optZh": "等于", "template": {"key": "key", "type": "layout", "fields": [{"key": "switch_1632802235314_19", "name": "值", "type": "switch", "model": {"attr_type": "boolean"}, "rules": [], "fields": [], "options": {"span": 24, "placeholder": ""}, "model_key": "val", "conditions": [], "model_key_prefix": "rule"}]}, "modelValue": {"rule": {"val": true}}}, "rule": {"key": "use_enable", "opt": "==", "val": true, "type": "Com::Attr::ConditionRules::Boolean", "key_name": "开启使用权限"}}]}]}}], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "switch_1709277673729_1", "name": "开启管理权限", "type": "switch", "model": {"attr_type": "boolean"}, "rules": [], "fields": [], "actions": [], "options": {"span": 12, "select": [{"label": "", "value": ""}, {"label": "", "value": ""}], "multiple": false}, "model_key": "manage_enable", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "condition_1709277744165_3", "name": "条件块", "type": "condition", "rules": [], "fields": [], "actions": [], "options": {"span": 12}, "model_key": "condition_1709277744165_3", "conditions": [{"val": "", "name": "条件1", "type": "complex", "fields": [{"key": "res_permit_polymorphic_1709277645741_0", "name": "管理权限", "type": "res_permit_polymorphic", "model": {"attr_type": "array"}, "rules": [], "fields": [], "actions": [], "options": {"span": 24}, "model_key": "manage_serve_permit_actions", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}], "model_key": "", "complex_condition": {"groups": [{"items": [{"_id": "1709277757951_0", "desc": {"name": "开启管理权限", "optZh": "等于", "template": {"key": "key", "type": "layout", "fields": [{"key": "switch_1632802235314_19", "name": "值", "type": "switch", "model": {"attr_type": "boolean"}, "rules": [], "fields": [], "options": {"span": 24, "placeholder": ""}, "model_key": "val", "conditions": [], "model_key_prefix": "rule"}]}, "modelValue": {"rule": {"val": true}}}, "rule": {"key": "manage_enable", "opt": "==", "val": true, "type": "Com::Attr::ConditionRules::Boolean", "key_name": "开启管理权限"}}]}]}}], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}], "actions": [], "options": {"theme": {"card": {"content": "<p><br></p>"}}}, "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}], "actions": [{"key": "create", "enabled": true}, {"key": "update", "enabled": true}, {"key": "delete", "enabled": true}, {"key": "import", "enabled": true}, {"key": "export", "enabled": true}], "options": {"label": {}, "theme": {"card": {"content": "<p><br></p>"}, "form": {}, "background": {}}, "searcher": [], "create_text": "提交", "update_text": "提交", "disabled_actions": {}}, "model_key": "container_layout_1708504281375_1", "conditions": [], "index_attributes": [{"key": "select_1708505114547_6", "name": "发布状态", "data_index": "state"}, {"key": "time_range_new_1708505187397_7", "name": "时间范围", "data_index": "time_range_new_1708505187397_7"}, {"key": "select_1708505278837_10", "name": "活动类型", "data_index": "type"}], "column_attributes": [{"_id": "input_1708504283374_2", "index": {"on": true}, "title": ["活动名称"], "export": {"on": true}, "import": {"on": true}, "filters": [], "dataIndex": "name", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1712039596143_0", "index": {"on": true}, "title": ["活动分类"], "export": {"on": true}, "import": {"on": true}, "render": "TableRendersAuto", "filters": [], "dataIndex": "catalogs.name", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "select_1708505278837_10", "index": {"on": true}, "title": ["活动类型"], "export": {"on": true}, "import": {"on": true}, "filters": [], "dataIndex": "type", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "time_range_new_1708505187397_7", "index": {"on": true}, "title": ["活动开始时间"], "export": {"on": true}, "import": {"on": true}, "filters": [], "dataIndex": "effective_at", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1709694783604_0", "index": {"on": true}, "title": ["活动结束时间"], "export": {"on": true}, "import": {"on": true}, "render": "TableRendersAuto", "filters": [], "dataIndex": "invalid_at", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "select_1708505114547_6", "index": {"on": true}, "title": ["发布状态"], "export": {"on": true}, "import": {"on": true}, "filters": [], "dataIndex": "state", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "column_attributes_1709024037314_0", "index": {"on": true}, "title": ["创建审批状态"], "export": {"on": false, "aggre": [{"_id": "1709028640506_4"}]}, "import": {"on": false, "aggre": [{"_id": "1709028640519_5"}]}, "render": "ComServeActivitiesCreateInstanceCell", "filters": [], "dataIndex": "create_instance_state", "filteredValue": [], "sortDirections": [], "filteredDetails": [{"_id": "1709028640486_3"}], "defaultFilteredValue": []}], "model_key_configuration": []}