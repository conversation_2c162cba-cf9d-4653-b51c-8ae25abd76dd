{"key": "container_layout_1708503906622_1", "type": "container_layout", "model": {"create_default_value": {}, "update_default_value": {}}, "rules": [], "fields": [{"key": "TaTemplateFormDesignerKey-1", "type": "layout", "model": {}, "rules": [], "fields": [{"key": "input_1708503908246_2", "name": "名称", "type": "input", "model": {"summary": true, "attr_type": "string"}, "rules": [{"type": "string", "message": "请填写正确的名称", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 24}, "model_key": "name", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "image_single_new_1708503917930_3", "name": "封面图", "type": "image_single_new", "model": {"summary": true, "attr_type": "array"}, "rules": [], "fields": [], "actions": [], "options": {"size": "regular", "span": 24}, "model_key": "cover_image.files", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "key_layout_1708936453660_1", "name": "嵌套对象", "type": "key_layout", "rules": [], "fields": [{"key": "select_1708936502946_2", "name": "活动列表布局方式", "type": "radio", "model": {"attr_type": "string"}, "rules": [{"type": "string", "message": "请填写正确的活动列表布局方式", "required": true, "rule_type": "required"}], "fields": [], "actions": [], "options": {"span": 24, "select": [{"label": "列表", "value": "index"}, {"label": "瀑布流", "value": "flow"}], "multiple": false, "table_items": [], "import_export_headers": [{"_id": "1708936506469_0"}], "display_configurable_form": {}}, "model_key": "activity_index_layout", "conditions": [], "index_attributes": [], "model_key_prefix": "layout", "column_attributes": [], "model_key_configuration": []}], "actions": [], "options": {"span": 24}, "model_key": "layout", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "input_1712065242939_0", "name": "备注", "type": "input", "model": {"attr_type": "string"}, "rules": [], "fields": [], "actions": [], "options": {"span": 24}, "model_key": "model_payload.remark", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "radio_1712063724982_0", "name": "发布类型", "type": "radio", "model": {"attr_type": "string"}, "rules": [], "fields": [], "actions": [], "options": {"span": 12, "select": [{"label": "部门", "value": "Department"}], "multiple": false, "table_items": [], "import_export_headers": [{"_id": "1712063770856_0"}], "display_configurable_form": {}}, "model_key": "target_type", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "condition_1712063765976_1", "name": "条件块", "type": "condition", "rules": [], "fields": [], "actions": [], "options": {"span": 12}, "model_key": "condition_1712063765976_1", "conditions": [{"val": "", "name": "条件1", "type": "complex", "fields": [{"key": "api_single_1712042054504_0", "name": "发布部门", "type": "api_single", "model": {"attr_type": "number"}, "rules": [], "fields": [], "actions": [], "options": {"path": "/res/user/departments", "span": 24, "table_items": [{"name": "名称", "type": "string", "search": true, "data_index": "name"}], "import_export_headers": [{"_id": "1712042086523_0"}], "display_configurable_form": {}}, "model_key": "target_id", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}], "model_key": "", "complex_condition": {"groups": [{"items": [{"_id": "1712063785240_1", "desc": {"name": "发布类型", "optZh": "含有", "template": {"key": "key", "type": "layout", "fields": [{"key": "ditto_1712064002622_5", "name": "值", "type": "select", "model": {"attr_type": "array"}, "rules": [], "fields": [], "actions": [], "options": {"span": 24, "ditto": "ditto_single", "select": [{"label": "部门", "value": "Department"}], "disabled": false, "multiple": true, "table_items": [], "formDisabled": false, "accessibility": "writeable", "dynamic_component": "", "import_export_headers": [{"_id": "1712063770856_0"}], "display_configurable_form": {}}, "model_key": "val", "conditions": [], "index_attributes": [], "model_key_prefix": "rule", "column_attributes": [], "model_key_configuration": []}]}, "modelValue": {"rule": {"val": ["Department"]}}}, "rule": {"key": "target_type", "opt": "include", "val": ["Department"], "type": "Com::Attr::ConditionRules::SingleChoice", "key_name": "发布类型"}}]}]}}], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "image_single_new_1712021268135_0", "name": "图标", "type": "image_single_new", "model": {"attr_type": "array"}, "rules": [], "fields": [], "actions": [], "options": {"size": "regular", "span": 24}, "model_key": "icon.files", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "switch_1712111391683_0", "name": "依据标签分类", "type": "switch", "model": {"attr_type": "boolean"}, "rules": [], "fields": [], "actions": [], "options": {"span": 24, "select": [{"label": "", "value": ""}, {"label": "", "value": ""}], "multiple": false}, "model_key": "model_payload.is_tab_show", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "switch_1709870789965_1", "name": "开启查看权限", "type": "switch", "model": {"attr_type": "boolean"}, "rules": [], "fields": [], "actions": [], "options": {"span": 12, "select": [], "multiple": false}, "model_key": "view_enable", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "condition_1709871423982_0", "name": "条件块", "type": "condition", "rules": [], "fields": [], "actions": [], "options": {"span": 12}, "model_key": "condition_1709871423982_0", "conditions": [{"val": "", "name": "条件1", "type": "complex", "fields": [{"key": "res_permit_polymorphic_1708743434849_6", "name": "查看权限", "type": "res_permit_polymorphic", "model": {"attr_type": "array"}, "rules": [], "fields": [], "actions": [], "options": {"span": 24}, "model_key": "view_serve_permit_actions", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}], "model_key": "", "complex_condition": {"groups": [{"items": [{"_id": "1709871433332_0", "desc": {"name": "开启查看权限", "optZh": "等于", "template": {"key": "key", "type": "layout", "fields": [{"key": "switch_1632802235314_19", "name": "值", "type": "switch", "model": {"attr_type": "boolean"}, "rules": [], "fields": [], "options": {"span": 24, "placeholder": ""}, "model_key": "val", "conditions": [], "model_key_prefix": "rule"}]}, "modelValue": {"rule": {"val": true}}}, "rule": {"key": "view_enable", "opt": "==", "val": true, "type": "Com::Attr::ConditionRules::Boolean", "key_name": "开启查看权限"}}]}]}}], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "switch_1709870789614_0", "name": "开启使用权限", "type": "switch", "model": {"attr_type": "boolean"}, "rules": [], "fields": [], "actions": [], "options": {"span": 12, "select": [], "multiple": false}, "model_key": "use_enable", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}, {"key": "condition_1709871626156_3", "name": "条件块", "type": "condition", "rules": [], "fields": [], "actions": [], "options": {"span": 12}, "model_key": "condition_1709871626156_3", "conditions": [{"val": "", "name": "条件1", "type": "complex", "fields": [{"key": "res_permit_polymorphic_1708743437833_7", "name": "使用权限", "type": "res_permit_polymorphic", "model": {"attr_type": "array"}, "rules": [], "fields": [], "actions": [], "options": {"span": 24}, "model_key": "use_serve_permit_actions", "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}], "model_key": "", "complex_condition": {"groups": [{"items": [{"_id": "1709871646928_1", "desc": {"name": "开启使用权限", "optZh": "等于", "template": {"key": "key", "type": "layout", "fields": [{"key": "switch_1632802235314_19", "name": "值", "type": "switch", "model": {"attr_type": "boolean"}, "rules": [], "fields": [], "options": {"span": 24, "placeholder": ""}, "model_key": "val", "conditions": [], "model_key_prefix": "rule"}]}, "modelValue": {"rule": {"val": true}}}, "rule": {"key": "use_enable", "opt": "==", "val": true, "type": "Com::Attr::ConditionRules::Boolean", "key_name": "开启使用权限"}}]}]}}], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}], "actions": [], "options": {"label": {}, "theme": {"card": {"content": "<p><br></p>"}}}, "conditions": [], "index_attributes": [], "model_key_prefix": "", "column_attributes": [], "model_key_configuration": []}], "actions": [{"key": "create", "enabled": true}, {"key": "update", "enabled": true}, {"key": "delete", "enabled": true}, {"key": "import", "enabled": true}, {"key": "export", "enabled": true}], "options": {"label": {}, "theme": {"card": {"content": "<p><br></p>"}, "form": {}, "background": {}}, "searcher": [], "create_text": "提交", "update_text": "提交", "disabled_actions": {}}, "model_key": "container_layout_1708503906622_1", "conditions": [], "index_attributes": [], "column_attributes": [{"_id": "image_single_new_1708503917930_3", "index": {"on": true}, "title": ["封面图"], "export": {"on": true}, "import": {"on": true}, "filters": [], "dataIndex": "cover_image.files", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}, {"_id": "input_1708503908246_2", "index": {"on": true}, "title": ["名称"], "export": {"on": true}, "import": {"on": true}, "filters": [], "dataIndex": "name", "filteredValue": [], "sortDirections": [], "filteredDetails": [], "defaultFilteredValue": []}], "model_key_configuration": []}