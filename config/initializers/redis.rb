redis_config = { url: ENV.fetch("REDIS_URL", nil) }
redis_config[:password] = ENV["REDIS_PASSWORD"] if ENV["REDIS_PASSWORD"].present?
redis_config[:db] = ENV["REDIS_DB"] if ENV["REDIS_DB"].present?

Ohm.redis = Redis.new(redis_config)
Redis::Objects.redis = Redis.new(redis_config)

# 系统中各模块需要使用的redis
$redis = Redis.new(redis_config)
$token_redis = Redis::Namespace.new(:token, redis: $redis)
$verify_code_redis = Redis::Namespace.new(:verify_code, redis: $redis)
$limit_redis = Redis::Namespace.new(:limit, redis: $redis)
$broadcast_redis = Redis::Namespace.new(:broadcast, redis: $redis)
$access_token_redis = Redis::Namespace.new(:access_token, redis: $redis)
$redis_resources_redis = Redis::Namespace.new(:redis_resources, redis: $redis)

# $active_job_redis = Redis::Namespace.new(:active_job, redis: $redis)
