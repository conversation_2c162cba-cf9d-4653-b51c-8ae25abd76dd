if ENV["REDIS_CLUSTER_URL"].present?
  # cluster模式
  redis_config = { cluster: [ENV["REDIS_CLUSTER_URL"]] }
  redis_config[:password] = ENV["REDIS_PASSWORD"] if <PERSON>NV["REDIS_PASSWORD"].present?
else
  redis_config = { url: ENV.fetch("REDIS_URL", nil) }
  redis_config[:password] = ENV["REDIS_PASSWORD"] if ENV["REDIS_PASSWORD"].present?
  redis_config[:db] = ENV["SIDEKIQ_REDIS_DB"] if ENV["SIDEKIQ_REDIS_DB"].present?
end

Sidekiq.configure_server do |config|
  config.redis = redis_config
  config.logger = Logger.new("./log/sidekiq.log")
end

Sidekiq.configure_client do |config|
  config.redis = redis_config
end
