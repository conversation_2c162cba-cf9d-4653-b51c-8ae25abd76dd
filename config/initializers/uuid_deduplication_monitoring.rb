# UUID消息去重功能监控配置
# 提供基础的性能监控和事件记录

Rails.application.configure do
  # 配置UUID去重功能的专用日志记录器
  config.uuid_deduplication_logger = ActiveSupport::Logger.new(
    Rails.root.join('log', 'uuid_deduplication.log')
  )

  # 设置日志级别
  config.uuid_deduplication_logger.level = Rails.env.production? ? Logger::INFO : Logger::DEBUG

  # 配置日志格式
  config.uuid_deduplication_logger.formatter = proc do |severity, datetime, progname, msg|
    "[#{datetime.strftime('%Y-%m-%d %H:%M:%S')}] #{severity} [UUID_DEDUP] #{msg}\n"
  end
end

# UUID去重监控模块
module UuidDeduplicationMonitoring
  extend self

  # 性能监控阈值配置
  PERFORMANCE_THRESHOLDS = {
    template_selection_time: 100,     # 模板选择时间阈值（毫秒）
    deduplication_query_time: 50,    # 去重查询时间阈值（毫秒）
    message_generation_time: 200     # 消息生成时间阈值（毫秒）
  }.freeze

  # 记录性能指标
  def log_performance(operation, duration_ms, metadata = {})
    logger.info "PERFORMANCE: #{operation} completed in #{duration_ms}ms #{metadata.to_json}"

    # 检查性能阈值
    threshold = PERFORMANCE_THRESHOLDS[operation.to_sym]
    if threshold && duration_ms > threshold
      logger.warn "SLOW_OPERATION: #{operation} took #{duration_ms}ms (threshold: #{threshold}ms)"
    end
  end

  # 记录业务指标
  def log_business_metric(metric, value, metadata = {})
    logger.info "METRIC: #{metric}=#{value} #{metadata.to_json}"
  end

  # 记录错误
  def log_error(operation, error, metadata = {})
    logger.error "ERROR: #{operation} failed - #{error.class}: #{error.message} #{metadata.to_json}"
    logger.error "BACKTRACE: #{error.backtrace.first(3).join(' | ')}"
  end

  # 记录去重事件
  def log_deduplication_event(event_type, details = {})
    case event_type
    when :template_selected
      logger.info "EVENT: Template selected - #{details.to_json}"
    when :content_deduplicated
      logger.info "EVENT: Content deduplicated - #{details.to_json}"
    when :fallback_triggered
      logger.warn "EVENT: Fallback triggered - #{details.to_json}"
    when :no_available_templates
      logger.warn "EVENT: No available templates - #{details.to_json}"
    else
      logger.info "EVENT: #{event_type} - #{details.to_json}"
    end
  end

  private

  # 获取日志记录器
  def logger
    Rails.application.config.uuid_deduplication_logger
  end
end
