# SQLite version 3.x
#   gem install sqlite3
#
#   Ensure the SQLite 3 gem is defined in your Gemfile
#   gem 'sqlite3'
#
default: &default
  adapter: <%= ENV["DATABASE_ADAPTER"] %>
  pool: <%= ENV["DATABASE_POOL"] %>
  encoding: <%= ENV["DATABASE_ENCODING"] %>
  username: <%= ENV["DATABASE_USERNAME"] %>
  password: <%= ENV["DATABASE_PASSWORD"] %>
  host: <%= ENV["DATABASE_HOST"] %>
  port: <%= ENV["DATABASE_PORT"] %>

development:
  <<: *default
  database: <%= ENV["DATABASE_NAME"] %>

# Warning: The database defined as "test" will be erased and
# re-generated from your development database when you run "rake".
# Do not set this db to the same as development or production.
test:
  <<: *default
  database: <%= ENV["DATABASE_NAME"] %>

production:
  <<: *default
  database: <%= ENV["DATABASE_NAME"] %>

biz:
  adapter: <%= ENV["BID_DATABASE_ADAPTER"] %>
  pool: <%= ENV["BID_DATABASE_POOL"] %>
  encoding: <%= ENV["BID_DATABASE_ENCODING"] %>
  username: <%= ENV["BID_DATABASE_USERNAME"] %>
  password: <%= ENV["BID_DATABASE_PASSWORD"] %>
  host: <%= ENV["BID_DATABASE_HOST"] %>
  port: <%= ENV["BID_DATABASE_PORT"] %>
  database: <%= ENV["BID_DATABASE_NAME"] %>