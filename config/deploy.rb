set :stages, ["test", "production"]
set :default_stage, "test"

require "mina/rails"
require "mina/git"
require "mina/multistage"
# require 'mina/rbenv'  # for rbenv support. (https://rbenv.org)
require "mina/rvm" # for rvm support. (https://rvm.io)
require "mina/bundler"

# Basic settings:
#   domain       - The hostname to SSH to.
#   deploy_to    - Path to deploy into.
#   repository   - Git repo to clone from. (needed by mina/git)
#   branch       - Branch name to deploy. (needed by mina/git)

set :application_name, "hz_iest_api"
set :repository, "******************:gov/hz_iest_api.git"
set :branch, "main"
set :rails_env, "production"

# Optional settings:
#   set :user, 'foobar'          # Username in the server to SSH to.
#   set :port, '30000'           # SSH port number.
set :forward_agent, true # SSH forward_agent.

# Shared dirs and files will be symlinked into the app-folder by the 'deploy:link_shared_paths' step.
# Some plugins already add folders to shared_dirs like `mina/rails` add `public/assets`, `vendor/bundle` and many more
# run `mina -d` to see all folders and files already included in `shared_dirs` and `shared_files`
# set :shared_dirs, fetch(:shared_dirs, []).push('public/assets')
# set :shared_files, fetch(:shared_files, []).push('config/database.yml', 'config/secrets.yml')
set :shared_dirs, fetch(:shared_dirs, []).push("tmp/pids", "tmp/sockets", "public")
set :shared_files, fetch(:shared_files, []).push(".env.production.local")

# sidekiq
set :sidekiq_pid, "tmp/pids/sidekiq.pid"

# This task is the environment that is loaded for all remote run commands, such as
# `mina deploy` or `mina rake`.
task :remote_environment do
  # If you're using rbenv, use this to load the rbenv environment.
  # Be sure to commit your .ruby-version or .rbenv-version to your repository.
  # invoke :'rbenv:load'

  # For those using RVM, use this to load an RVM version@gemset.
  # invoke :'rvm:use', 'ruby-3.2.2'
end

# Put any custom commands you need to run at setup
# All paths in `shared_dirs` and `shared_paths` will be created on their own.
task :setup do
  # command %{rbenv install 2.3.0 --skip-existing}
  in_path(fetch(:shared_path)) do
    command %(touch .env.production.local)
    command %(mkdir -p tmp/pids)
    command %(mkdir -p pids/sockets)
  end
end

desc "Deploys the current version to the server."
task :deploy do
  # uncomment this line to make sure you pushed your local branch to the remote origin
  # invoke :'git:ensure_pushed'
  deploy do
    # Put things that will set up an empty directory into a fully set-up
    # instance of your project.
    invoke :"git:clone"
    invoke :"deploy:link_shared_paths"
    command %(bundle config set without 'development test')
    invoke :"bundle:install"
    command "rake app:update:bin"
    # invoke :'rails:db_create'
    command %(bundle exec rails db:migrate RAILS_ENV=production)
    # invoke :'rails[db:migrate]'
    invoke :"deploy:cleanup"

    on :launch do
      # in_path(fetch(:current_path)) do
      #   command %{mkdir -p tmp/}
      #   command %{touch tmp/restart.txt}
      # end
      invoke :load

      # invoke :'systemctl:restart', fetch(:sidekiq_service_name)
      invoke :"systemctl:restart", fetch(:puma_service_name)
    end
  end

  # invoke(:"systemctl:restart['puma-soa-okr']")
  # you can use `run :local` to run tasks on local machine before of after the deploy scripts
  # run(:local){ say 'done' }
end

task :load do
  workers = fetch(:workers, 2)
  threads = fetch(:threads, 20)
  command %(export WEB_CONCURRENCY=#{workers})
  command %(export RAILS_MAX_THREADS=#{threads})
end

namespace :systemctl do
  desc "Start a systemd service"
  task :start, [:service] do |_t, args|
    command %(sudo systemctl start #{args[:service]})
  end

  desc "Restart a systemd service"
  task :restart, [:service] do |_t, args|
    command %(sudo systemctl restart #{args[:service]})
  end

  desc "Stop a systemd service"
  task :stop, [:service] do |_t, args|
    command %(sudo systemctl stop #{args[:service]})
  end

  desc "Get status of a systemd service"
  task :status, [:service] do |_t, args|
    command %(sudo systemctl status #{args[:service]})
  end
end

namespace :docker do
  task :setup do
    invoke :setup
  end

  task :deploy_all do
    (fetch(:stages) || []).each do |stage|
      set :stage, stage
      file = "#{_stages_dir}/#{fetch(:stage)}.rb"
      load file
      invoke :"docker:deploy"
    end
  end

  task :deploy do
    deploy do
      invoke :"git:clone"
      invoke :"deploy:link_shared_paths"
      on :launch do
        invoke :"docker:reset_link_shared_paths"
        invoke :"docker:restart"
        invoke :"deploy:cleanup"
        # invoke :'docker:health_check'
      end
    end
  end

  task :reset_link_shared_paths do
    in_path fetch(:deploy_to).to_s do
      command %(
        ln -sf #{fetch(:shared_path)}/.env.production.local #{fetch(:current_path)}/docker/.env
      )
    end
  end

  task :stop do
    in_path "#{fetch(:current_path)}/docker" do
      command %(
        if [ -f #{fetch(:docker_compose_path)} ]; then
          #{fetch(:docker_compose_command)} -f #{fetch(:docker_compose_path)} down
        else
          #{fetch(:docker_compose_command)} down
        fi
      )
    end
  end

  desc "start server"
  task :start do
    in_path "#{fetch(:current_path)}/docker" do
      command %(
        if [ -f #{fetch(:docker_compose_path)} ]; then
          #{fetch(:docker_compose_command)} -f #{fetch(:docker_compose_path)} up -d
        else
          #{fetch(:docker_compose_command)} up -d
        fi
      )
    end
  end

  task :rebuild do
    in_path "#{fetch(:deploy_to)}/current/docker" do
      command %(
      if [ -f #{fetch(:docker_compose_path)} ]; then
        #{fetch(:docker_compose_command)} -f #{fetch(:docker_compose_path)} down
        DOCKER_BUILDKIT=0 #{fetch(:docker_compose_command)} -f #{fetch(:docker_compose_path)} up -d --build
      else
        #{fetch(:docker_compose_command)} down
        DOCKER_BUILDKIT=0 #{fetch(:docker_compose_command)} up -d --build
      fi
    )
    end
  end

  task :restart do
    in_path "#{fetch(:deploy_to)}/current/docker" do
      command %(
        if [ -f #{fetch(:docker_compose_path)} ]; then
          #{fetch(:docker_compose_command)} -f #{fetch(:docker_compose_path)} down
          DOCKER_BUILDKIT=0 #{fetch(:docker_compose_command)} -f #{fetch(:docker_compose_path)} up -d
        else
          #{fetch(:docker_compose_command)} down
          DOCKER_BUILDKIT=0 #{fetch(:docker_compose_command)} up -d
        fi
      )
    end
  end

  task :rollback do
    in_path fetch(:releases_path) do
      # TODO: add check if there are more than 1 release
      command %{rollback_release=$(ls -1A | sort -n | tail -n 2 | head -n 1)}
      comment %(Rollbacking to release: $rollback_release)
      command %(ln -nfs #{fetch(:releases_path)}/$rollback_release #{fetch(:current_path)})
      command %{current_release=$(ls -1A | sort -n | tail -n 1)}
      comment %(Deleting current release: $current_release)
      command %(rm -rf #{fetch(:releases_path)}/$current_release)
    end
    invoke :"docker:reset_link_shared_paths"
    invoke :"docker:restart"
    invoke :"deploy:cleanup"
  end

  desc "Clean up old releases."
  task :cleanup do
    ensure!(:keep_releases)
    ensure!(:deploy_to)

    comment %{Cleaning up old releases (keeping #{fetch(:keep_releases)})}
    in_path fetch(:releases_path) do
      command %{count=$(ls -A1 | sort -rn | wc -l)}
      command %{remove=$((count > #{fetch(:keep_releases)} ? count - #{fetch(:keep_releases)} : 0))}
      command %(ls -A1 | sort -rn | tail -n $remove | xargs rm -rf {})
    end
  end

  task :health_check do
    in_path fetch(:deploy_to).to_s do
      command %{
        status=$(curl -o /dev/null -s -w "%<http_code>s" -f http://localhost:#{fetch(:docker_port)}/apps.json)
        if [ "$status" -eq 200 ]; then
          echo deploy success
        else
          rm -rf releases/$(ls releases -1A | sort -n | tail -n 1)
          echo deploy failed
          exit 10
        fi
      }
    end
  end
end
