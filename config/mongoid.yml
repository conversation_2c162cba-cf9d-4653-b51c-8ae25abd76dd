development:
  clients:
    default:
      database: <%= ENV["MONGO_DB_NAME"] %>
      hosts: <%= ENV["MONGO_DB_HOSTS"].split(',') %>
      options:
        user: <%= ENV["MONGO_DB_USERNAME"] %>
        password: <%= ENV["MONGO_DB_PASSWORD"] %>
  options:

test:
  clients:
    default:
      database: <%= ENV["MONGO_DB_NAME"] %>
      hosts: <%= ENV["MONGO_DB_HOSTS"].split(',') %>
      options:
        user: <%= ENV["MONGO_DB_USERNAME"] %>
        password: <%= ENV["MONGO_DB_PASSWORD"] %>
        read:
          mode: :primary
        max_pool_size: <%= ENV["MONGO_DB_POOL"] %>

production:
  clients:
    default:
      database: <%= ENV["MONGO_DB_NAME"] %>
      hosts: <%= ENV["MONGO_DB_HOSTS"].split(',') %>
      options:
        user: <%= ENV["MONGO_DB_USERNAME"] %>
        password: <%= ENV["MONGO_DB_PASSWORD"] %>
        read:
          mode: :primary
        max_pool_size: <%= ENV["MONGO_DB_POOL"] %>
