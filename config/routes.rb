Rails.application.routes.draw do
  namespace :bot do
    namespace :manage do
      resources :agents
      resources :intents
      resources :paperworks
      resources :report_templates do
        resources :review_rules
        resources :reports
      end

      resources :reviewers do
        resources :review_rules
        resources :review_documents
      end
      resources :meetings do
        post :generate_topic_and_summary, on: :member
        post :ask, on: :member
      end
    end

    namespace :user do
      resources :agents, only: [:index]
      resources :paperworks

      resources :conversations do
        # 具体发送消息
        post :chat, on: :member

        resources :messages, only: [:index, :show]
        resources :artifacts, only: [:index, :show, :update] do
          post :activate, on: :member
        end
      end

      ###### report ###########
      resources :report_templates, only: [:index, :show]
      resources :reports do
        post :perform_review, on: :member
        # 文本处理接口
        post :process_text, on: :member
        # 根据模板生成报告
        post :generate_from_template, on: :member

        resources :review_rules, only: [:index, :show]
        # 审查结果
        resources :review_results, except: [:create]
      end
      ################

      ##### reviewer ####
      resources :reviewers, only: [:index, :show]

      resources :review_documents do
        post :perform_review, on: :member
        post :ask, on: :member

        resources :review_rules, only: [:index, :show]
        resources :review_results, except: [:create]
      end
      ###############

      resources :meetings do
        post :generate_topic_and_summary, on: :member
        post :ask, on: :member
      end
    end
  end
end
