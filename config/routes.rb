require "sidekiq/web"
Rails.application.routes.draw do
  # Define your application routes per the DSL in https://guides.rubyonrails.org/routing.html

  # Reveal health status on /up that returns 200 if the app boots with no exceptions, otherwise 500.
  # Can be used by load balancers and uptime monitors to verify that the app is live.
  get "up" => "rails/health#show", as: :rails_health_check
  mount Sidekiq::Web => "/sidekiq"
  # Defines the root path route ("/")
  # root "posts#index"

  namespace :res do
    namespace :member do
      resources :org_users, only: [:index] do
        importable
        post :import_precheck, on: :collection
      end
      resources :org_departments, only: [:index]
    end
  end

  namespace :serve do
    namespace :manage do
      resources :bid_projects
      resources :submodules do
        resources :activities do
          post :crawl_wechat_article, on: :collection
          post :batch_crawl_wechat_articles, on: :collection
          get 'crawl_task_status/:task_id', action: :crawl_task_status, on: :collection, as: :crawl_task_status
          get :crawl_tasks_list, on: :collection
          delete :cleanup_crawl_tasks, on: :collection
          delete 'delete_crawl_task/:task_id', action: :delete_crawl_task, on: :collection, as: :delete_crawl_task
        end
      end
    end

    namespace :user do
      resources :bid_projects, only: [:index, :show]
      resources :duty_change, only: [:index, :show]
    end
  end

  namespace :api do
    get 'image_proxy', to: 'image_proxy#show'
    delete 'test/delete_crawl_task/:task_id', to: 'test#delete_crawl_task'
    get 'test/crawl_tasks_list', to: 'test#crawl_tasks_list'

    namespace :dingtalk do
      match :callback, to: 'callbacks#create', via: [:get, :post]
      post :test_callback, to: 'test_callbacks#create'
    end
  end

  namespace :com do
    namespace :user do
      resources :redis_resources, only: [:index, :show, :create, :destroy] do
        post :batch_destroy, on: :collection
      end
    end
  end
end
