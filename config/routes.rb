Rails.application.routes.draw do
  namespace :serve do
    namespace :admin do
      resources :submodules
      resources :banners
    end

    namespace :manage do
      resources :submodules, only: [:index, :show] do
        resources :banners
        resources :catalogs
        resources :tags
        resources :groups
        resources :activities do
          origin_qrcodeable
          origin_scanable
          origin_registerable
          exportable
        end
        resources :entries do
          origin_registerable
          exportable
        end
      end

      namespace :user do
        resources :activities, only: [:index, :show] do
          origin_qrcodeable
          origin_scanable
          origin_registerable
          resources :entries
        end
        resources :entries, only: [:index, :show] do
          origin_registerable
        end
      end
    end

    namespace :user do
      resources :submodules, only: [:index, :show] do
        resources :banners, only: [:index, :show]
        resources :tags, only: [:index, :show]
        resources :catalogs, only: [:index, :show]
        resources :groups, only: [:index, :show]
        resources :activities, only: [:index, :show]
        resources :entries, only: [:index, :show, :create, :update]
      end
      resources :permit_actions, only: [:index, :show]

      namespace :entry do
        resources :activities, only: [:index, :show]
        resources :entries, only: [:index, :show]
      end
    end
  end
end
