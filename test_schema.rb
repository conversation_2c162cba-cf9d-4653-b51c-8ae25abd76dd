puts '=== 详细工具调用检测测试 ==='
begin
  agent = Bot::Agent.first
  user = User.first

  puts "Agent: #{agent.name}"
  puts "User: #{user.name}"

  # 先直接测试ActivityQueryTool获取基准数据
  puts "\n=== 基准测试：直接调用ActivityQueryTool ==="
  activity_tool = Bot::Tools::ActivityQueryTool.new(intent: agent.intents.find_by(name: '素材查询'))
  direct_result = activity_tool.query_activities(query: '过去一年里有多少素材？')
  puts "直接调用结果: #{direct_result}"
  puts "结果长度: #{direct_result.length}字符"

  puts "\n=== 测试Agent对话并检查工具调用 ==="
  message = '过去一年里有多少素材？'
  puts "测试消息: #{message}"

  # 初始化assistant
  assistant = agent.initialize_assistant
  puts "Assistant工具数量: #{assistant.tools.count}"
  puts "Tool choice: #{assistant.instance_variable_get(:@tool_choice)}"

  # 启用详细日志
  Rails.logger.level = Logger::DEBUG

  # 记录调用前的消息数量
  initial_message_count = assistant.messages.count
  puts "调用前消息数量: #{initial_message_count}"

  # 执行对话
  response = agent.chat(message, user: user)

  # 检查调用后的消息
  final_message_count = assistant.messages.count
  puts "调用后消息数量: #{final_message_count}"

  # 检查所有消息中的工具调用
  puts "\n=== 检查Assistant消息历史 ==="
  assistant.messages.each_with_index do |msg, index|
    puts "消息 #{index + 1}: role=#{msg.role}"
    if msg.tool_calls.present?
      puts '  ✅ 发现工具调用!'
      msg.tool_calls.each do |call|
        puts "    工具: #{call['function']['name']}"
        puts "    参数: #{call['function']['arguments']}"
      end
    else
      puts "  内容: #{msg.content[0..100] if msg.content}..."
    end
  end

  puts "\n=== 检查响应内容 ==="
  puts "响应消息数量: #{response[:messages].count}"
  response[:messages].each_with_index do |msg, index|
    puts "响应消息 #{index + 1}: #{msg[:content_type]}"
    if msg[:content_type] == 'text'
      puts "  内容: #{msg[:content][0..200]}..."
    elsif msg[:content_type] == 'artifact'
      puts '  ✅ 发现Artifact!'
      puts "  类型: #{msg[:content]['type']}"
    end
  end
rescue StandardError => e
  puts "❌ 失败: #{e.message}"
  puts e.backtrace.first(10)
end
