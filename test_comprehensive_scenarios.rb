puts '=== 综合边界测试：多轮对话素材查询功能 ==='
begin
  agent = Bot::Agent.first
  user = User.first
  
  # 定义各种边界测试场景
  test_scenarios = [
    {
      name: "标准时间范围查询",
      queries: [
        "过去一年里有多少素材？",
        "上个月有多少素材？",
        "前3个月有多少素材呢？",
        "今年开始有多少新素材？",
        "最近一周有多少素材？"
      ]
    },
    {
      name: "简化查询表达",
      queries: [
        "素材数量？",
        "有多少内容？",
        "资料统计",
        "素材情况",
        "内容总数"
      ]
    },
    {
      name: "混合查询类型",
      queries: [
        "过去一年的素材分类情况如何？",
        "今年新增了哪些类型的素材？",
        "最近的素材主要是什么类型？",
        "素材的分布情况怎么样？",
        "各类素材的数量对比"
      ]
    },
    {
      name: "上下文相关查询",
      queries: [
        "过去一年有多少素材？",
        "那上个月呢？",
        "前面提到的素材中，图片有多少？",
        "再看看今年的情况",
        "对比一下最近三个月"
      ]
    },
    {
      name: "边界表达测试",
      queries: [
        "素材",
        "内容",
        "有什么素材吗？",
        "素材多不多？",
        "能看看素材情况吗？"
      ]
    }
  ]
  
  total_queries = 0
  successful_queries = 0
  
  test_scenarios.each_with_index do |scenario, scenario_index|
    puts "\n" + "="*80
    puts "测试场景 #{scenario_index + 1}: #{scenario[:name]}"
    puts "="*80
    
    # 为每个场景创建新的对话
    conversation = agent.conversations.create!(user: user)
    Bot::Current.conversation = conversation
    
    scenario_success = 0
    
    begin
      scenario[:queries].each_with_index do |query, query_index|
        puts "\n" + "-"*60
        puts "查询 #{query_index + 1}: #{query}"
        puts "-"*60
        
        total_queries += 1
        
        # 执行查询
        response = agent.chat(query, user: user, conversation_id: conversation.id)
        
        # 分析响应
        text_messages = response[:messages].select { |msg| msg[:content_type] == 'text' }
        artifact_messages = response[:messages].select { |msg| msg[:content_type] == 'artifact' }
        
        # 检查工具调用
        has_tool_call = response[:messages].count >= 2
        
        # 检查分类统计
        has_classification = text_messages.any? { |msg| 
          content = msg[:content]
          content.include?('文字') || content.include?('图片') || content.include?('视频') || content.include?('漫画')
        }
        
        # 检查组件显示
        has_artifact = artifact_messages.any? { |msg|
          artifact = msg[:content]
          artifact['tool_cname'] == 'Bot::Tools::ActivityQueryTool'
        }
        
        # 判断是否成功
        is_success = has_tool_call && has_classification && has_artifact
        
        if is_success
          successful_queries += 1
          scenario_success += 1
        end
        
        puts "工具调用: #{has_tool_call ? '✅' : '❌'}"
        puts "分类统计: #{has_classification ? '✅' : '❌'}"
        puts "组件显示: #{has_artifact ? '✅' : '❌'}"
        puts "结果: #{is_success ? '🎉 成功' : '❌ 失败'}"
        
        # 显示响应内容摘要
        if text_messages.any?
          content = text_messages.first[:content]
          puts "回答摘要: #{content[0..80]}#{content.length > 80 ? '...' : ''}"
        end
        
        # 等待一下，模拟真实对话间隔
        sleep(0.5)
      end
      
      scenario_success_rate = (scenario_success.to_f / scenario[:queries].count * 100).round(1)
      puts "\n--- 场景总结 ---"
      puts "成功率: #{scenario_success}/#{scenario[:queries].count} (#{scenario_success_rate}%)"
      puts "状态: #{scenario_success_rate >= 80 ? '✅ 通过' : '❌ 未达标'}"
      
    ensure
      Bot::Current.conversation = nil
    end
  end
  
  # 计算总体成功率
  overall_success_rate = (successful_queries.to_f / total_queries * 100).round(1)
  
  puts "\n" + "="*80
  puts "综合测试结果"
  puts "="*80
  puts "总查询数: #{total_queries}"
  puts "成功查询数: #{successful_queries}"
  puts "总体成功率: #{overall_success_rate}%"
  puts "目标成功率: 80%"
  puts "测试结果: #{overall_success_rate >= 80 ? '🎉 达标' : '❌ 未达标'}"
  
  if overall_success_rate >= 80
    puts "\n✅ 素材查询功能在多轮对话中表现稳定，满足80%成功率要求！"
  else
    puts "\n⚠️ 需要进一步优化，当前成功率未达到80%目标。"
    
    # 分析失败原因
    puts "\n失败查询分析："
    puts "- 检查是否有特定的查询模式导致失败"
    puts "- 验证工具调用逻辑是否需要进一步强化"
    puts "- 考虑是否需要优化查询识别规则"
  end
  
rescue StandardError => e
  puts "❌ 测试失败: #{e.message}"
  puts e.backtrace.first(10)
end
