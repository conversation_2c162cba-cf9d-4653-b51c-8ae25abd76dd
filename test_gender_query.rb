#!/usr/bin/env ruby

# 测试性别查询功能
puts "=== 测试OrgTool性别查询功能 ==="

# 测试extract_gender方法
def test_extract_gender
  puts "\n--- 测试extract_gender方法 ---"

  test_cases = [
    "滨江区纪委的男性有多少人？",
    "女性员工有多少？",
    "查询男的有多少人",
    "统计女的人数",
    "未知性别的人员",
    "滨江区纪委有多少人？"  # 无性别
  ]

  test_cases.each do |query|
    # 模拟extract_gender方法
    gender = if query.include?("男性") || query.include?("男的") || query.include?("男人")
      "男性"
    elsif query.include?("女性") || query.include?("女的") || query.include?("女人")
      "女性"
    elsif query.include?("未知性别") || query.include?("性别未知")
      "未知"
    else
      nil
    end

    puts "查询: '#{query}' -> 性别: #{gender || '无'}"
  end
end

# 测试build_result_message方法
def test_build_result_message
  puts "\n--- 测试build_result_message方法 ---"

  test_cases = [
    ["滨江区", "纪委", "男性", 25],
    ["滨江区", "纪委", nil, 77],
    [nil, "纪委", "女性", 15],
    ["滨江区", nil, "男性", 100],
    [nil, nil, "男性", 500]
  ]

  test_cases.each do |org_name, department_name, gender, count|
    # 模拟build_result_message方法
    conditions = []
    conditions << "#{org_name}" if org_name && !org_name.empty?
    conditions << "#{department_name}" if department_name && !department_name.empty?

    location_text = conditions.join("")

    message = if gender && !gender.empty?
      if location_text && !location_text.empty?
        "「#{location_text}」的#{gender}共有 #{count} 人"
      else
        "#{gender}共有 #{count} 人"
      end
    else
      if location_text && !location_text.empty?
        "「#{location_text}」共有 #{count} 人"
      else
        "符合条件的人员共有 #{count} 人"
      end
    end

    puts "组织: #{org_name || '无'}, 部门: #{department_name || '无'}, 性别: #{gender || '无'}, 数量: #{count}"
    puts "结果: #{message}"
    puts ""
  end
end

# 运行测试
test_extract_gender
test_build_result_message

puts "\n=== 测试完成 ==="
puts "现在可以测试实际的Bot查询："
puts "1. '滨江区纪委的男性有多少人？'"
puts "2. '滨江区纪委的女性有多少人？'"
puts "3. '滨江区纪委有多少人？'"
