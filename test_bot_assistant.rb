puts '=== 测试Bot::Assistant.chat方法 ==='
begin
  agent = Bot::Agent.first
  user = User.first
  
  puts "Agent: #{agent.name}"
  puts "User: #{user.name}"
  
  # 测试Bot::Assistant.chat方法
  puts "\n=== 测试Bot::Assistant.chat方法 ==="
  message = '过去一年里有多少素材？'
  puts "测试消息: #{message}"
  
  # 创建对话
  conversation = agent.conversations.create!(user: user)
  puts "创建对话: #{conversation.id}"
  
  # 初始化assistant
  assistant = agent.initialize_assistant(conversation_id: conversation.id)
  
  # 测试chat方法
  response = assistant.chat(message, conversation: conversation)
  
  puts "\n=== 响应分析 ==="
  puts "响应类型: #{response.class}"
  puts "响应keys: #{response.keys}"
  puts "消息数量: #{response[:messages].count}"
  
  response[:messages].each_with_index do |msg, index|
    puts "\n消息 #{index + 1}:"
    puts "  content_type: #{msg[:content_type]}"
    if msg[:content_type] == 'text'
      puts "  content: #{msg[:content][0..100]}..."
    elsif msg[:content_type] == 'artifact'
      puts "  ✅ 发现Artifact!"
      puts "  artifact类型: #{msg[:content]['type']}"
      puts "  artifact数据: #{msg[:content]['data'].keys if msg[:content]['data']}"
    end
  end
  
  # 检查Langchain::Assistant的消息
  puts "\n=== Langchain::Assistant消息检查 ==="
  assistant.messages.each_with_index do |msg, index|
    puts "消息 #{index + 1}: role=#{msg.role}"
    if msg.tool_calls.present?
      puts "  ✅ 包含工具调用!"
      msg.tool_calls.each do |call|
        puts "    工具: #{call['function']['name']}"
        puts "    ID: #{call['id']}"
      end
    elsif msg.tool_call_id.present?
      puts "  ✅ 工具响应消息!"
      puts "    tool_call_id: #{msg.tool_call_id}"
      puts "    content类型: #{msg.content.class}"
      puts "    content: #{msg.content.inspect[0..200]}..."
    else
      puts "  内容: #{msg.content[0..100] if msg.content}..."
    end
  end
  
rescue StandardError => e
  puts "❌ 失败: #{e.message}"
  puts e.backtrace.first(10)
end
