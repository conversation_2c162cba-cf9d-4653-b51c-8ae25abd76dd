puts '=== 测试Artifact创建 ==='
begin
  agent = Bot::Agent.first
  user = User.first
  
  puts "Agent: #{agent.name}"
  puts "User: #{user.name}"
  
  # 直接测试ActivityQueryTool
  puts "\n=== 直接测试ActivityQueryTool ==="
  intent = agent.intents.find_by(name: '素材查询')
  puts "Intent: #{intent.name}"
  
  activity_tool = Bot::Tools::ActivityQueryTool.new(intent: intent)
  
  # 测试query_activities方法
  puts "\n=== 调用query_activities方法 ==="
  result = activity_tool.query_activities(query: '过去一年里有多少素材？')
  
  puts "结果类型: #{result.class}"
  puts "结果keys: #{result.keys}"
  puts "data: #{result[:data]}"
  puts "artifact: #{result[:artifact]}"
  
  # 检查context
  puts "\n=== 检查工具context ==="
  context = activity_tool.instance_variable_get(:@context)
  puts "Context: #{context}"
  
  if context && context[:query_activities]
    puts "query_activities context: #{context[:query_activities]}"
    if context[:query_activities][:artifact]
      puts "✅ Context中有artifact!"
      puts "Artifact类型: #{context[:query_activities][:artifact].class}"
      puts "Artifact内容: #{context[:query_activities][:artifact].inspect}"
    else
      puts "❌ Context中没有artifact"
    end
  else
    puts "❌ 没有query_activities context"
  end
  
rescue StandardError => e
  puts "❌ 失败: #{e.message}"
  puts e.backtrace.first(10)
end
