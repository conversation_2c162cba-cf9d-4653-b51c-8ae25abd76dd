#!/usr/bin/env ruby

puts '=== 测试 rule_government_project_change_today 的完整流程 ==='

# 清除今天的缓存（如果存在）
today = Date.current
puts "今天日期: #{today}"

cache_record = Irs::GovernmentProject.find_by_date(today)
if cache_record
  puts "删除现有缓存记录: #{cache_record.id}"
  cache_record.destroy!
  puts "缓存已删除"
else
  puts "今天没有缓存记录"
end

puts "\n=== 测试缓存检查逻辑 ==="
is_valid = Irs::GovernmentProject.cache_valid?(today)
puts "cache_valid?(#{today}): #{is_valid}"

puts "\n=== 调用 rule_government_project_change_today ==="
result = Serve::BidProject.rule_government_project_change_today
puts "返回项目数量: #{result.count}"

# 检查是否创建了新的缓存
puts "\n=== 检查新缓存 ==="
new_cache = Irs::GovernmentProject.find_by_date(today)
if new_cache
  puts "新缓存已创建:"
  puts "  项目数量: #{new_cache.projects_count}"
  puts "  成功状态: #{new_cache.success?}"
  puts "  缓存时间: #{new_cache.cached_at}"
else
  puts "没有创建新缓存"
end

puts "\n=== 测试完成 ==="
