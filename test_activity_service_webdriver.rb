#!/usr/bin/env ruby

# 测试修复后的ActivityService WebDriver配置
# 用于验证szjjyth3生产服务器上的Chrome和ChromeDriver配置

puts "=== 测试ActivityService WebDriver配置 ==="

# 设置Rails环境
ENV['RAILS_ENV'] ||= 'development'
require_relative 'config/environment'

begin
  puts "\n1. 测试Chrome路径检测:"
  
  # 模拟Linux环境下的路径检测
  possible_chrome_paths = [
    '/tmp/chrome-linux64/chrome',  # szjjyth3服务器上的路径
    '/usr/bin/google-chrome',
    '/usr/bin/google-chrome-stable',
    '/usr/bin/chromium-browser',
    '/usr/bin/chromium'
  ]

  chrome_path = possible_chrome_paths.find { |path| File.exist?(path) }
  if chrome_path
    puts "  ✓ 找到Chrome: #{chrome_path}"
    
    # 检查Chrome版本（如果可能）
    begin
      version_output = `"#{chrome_path}" --version 2>&1`
      puts "    版本: #{version_output.strip}"
    rescue => e
      puts "    版本检查失败: #{e.message}"
    end
  else
    puts "  ✗ 未找到Chrome浏览器"
    puts "    检查的路径: #{possible_chrome_paths.join(', ')}"
  end

  puts "\n2. 测试ChromeDriver路径检测:"
  
  possible_driver_paths = [
    '/usr/local/bin/chromedriver',  # szjjyth3服务器上的路径
    '/usr/bin/chromedriver',
    '/opt/chromedriver/chromedriver',
    ENV['CHROMEDRIVER_PATH']
  ].compact

  driver_path = possible_driver_paths.find { |path| File.exist?(path) }
  if driver_path
    puts "  ✓ 找到ChromeDriver: #{driver_path}"
    
    # 检查ChromeDriver版本
    begin
      version_output = `"#{driver_path}" --version 2>&1`
      puts "    版本: #{version_output.strip}"
    rescue => e
      puts "    版本检查失败: #{e.message}"
    end
  else
    puts "  ✗ 未找到ChromeDriver"
    puts "    检查的路径: #{possible_driver_paths.join(', ')}"
  end

  puts "\n3. 测试ActivityService WebDriver配置:"
  
  # 创建ActivityService实例并测试配置方法
  service = Serve::ActivityService.new
  
  # 测试Chrome选项配置
  puts "  正在配置Chrome选项..."
  options = service.send(:configure_chrome_options)
  puts "  ✓ Chrome选项配置成功"
  puts "    二进制路径: #{options.binary || '默认路径'}"
  puts "    参数数量: #{options.args.length}"
  
  # 显示主要参数
  key_args = options.args.select { |arg| 
    arg.include?('headless') || arg.include?('no-sandbox') || arg.include?('disable-dev-shm-usage')
  }
  puts "    关键参数: #{key_args.join(', ')}"

  puts "\n4. 测试WebDriver创建:"
  
  begin
    puts "  正在创建WebDriver实例..."
    driver = service.send(:create_webdriver_with_service, options)
    puts "  ✓ WebDriver创建成功"
    
    # 测试基本功能
    puts "  正在测试基本功能..."
    driver.get('https://www.baidu.com')
    puts "  ✓ 页面访问成功"
    puts "    页面标题: #{driver.title}"
    puts "    页面URL: #{driver.current_url}"
    
    driver.quit
    puts "  ✓ WebDriver正常关闭"
    
  rescue => e
    puts "  ✗ WebDriver测试失败: #{e.message}"
    puts "    错误类型: #{e.class}"
    puts "    错误详情: #{e.backtrace.first(3).join("\n    ")}"
  end

  puts "\n5. 测试微信文章抓取功能:"
  
  begin
    puts "  正在测试get_element_by_driver方法..."
    
    # 创建测试设置
    test_setting = {
      url: 'https://mp.weixin.qq.com/s/LW5DoDQezmuwT5qQXSXI1A',
      css: 'body'
    }
    
    result = service.send(:get_element_by_driver, test_setting)
    
    if result && result.respond_to?(:text)
      puts "  ✓ 微信文章抓取测试成功"
      puts "    返回类型: #{result.class}"
      puts "    内容长度: #{result.text.length} 字符"
      puts "    内容预览: #{result.text[0..100]}..."
    else
      puts "  ⚠️ 微信文章抓取返回了意外结果"
      puts "    返回值: #{result.inspect}"
    end
    
  rescue => e
    puts "  ✗ 微信文章抓取测试失败: #{e.message}"
    puts "    错误类型: #{e.class}"
    puts "    这可能是由于网络问题或微信页面结构变化"
  end

rescue => e
  puts "\n❌ 测试过程中发生错误:"
  puts "  错误: #{e.message}"
  puts "  类型: #{e.class}"
  puts "  位置: #{e.backtrace.first}"
end

puts "\n=== 测试完成 ==="
puts "\n💡 提示:"
puts "  - 如果Chrome或ChromeDriver未找到，请确保已正确安装"
puts "  - 如果WebDriver创建失败，检查版本兼容性"
puts "  - 如果微信文章抓取失败，可能是网络或反爬虫限制"
puts "  - 生产环境建议使用更长的超时时间和重试机制"
