#!/usr/bin/env ruby

# 测试各种时间查询的修复效果
test_queries = [
  "今年有多少招投标类的素材？",
  "去年发布了多少素材？", 
  "本月有哪些新素材？",
  "最近一周的素材数量",
  "2024年的素材统计"
]

puts "测试各种时间查询的修复效果"
puts "=" * 60

test_queries.each_with_index do |query, index|
  puts "\n#{index + 1}. 测试查询: #{query}"
  puts "-" * 40
  
  begin
    # 测试RansackDsl转换
    transformer = Bot::Transformers::RansackDsl.new(Serve::Activity)
    conditions = transformer.transform(query)
    
    puts "转换条件: #{conditions.inspect}"
    
    # 检查时间字段使用
    time_field_used = nil
    if conditions.to_s.include?('published_at')
      time_field_used = 'published_at ✅'
    elsif conditions.to_s.include?('created_at')
      time_field_used = 'created_at ❌'
    else
      time_field_used = '无时间字段 ⚠️'
    end
    
    puts "时间字段: #{time_field_used}"
    
    # 执行查询
    base_query = Serve::Activity.all
    ransack_query = base_query.ransack(conditions)
    count = ransack_query.result.count
    
    puts "查询结果: #{count} 个素材"
    
    # 测试ActivityQueryTool
    tool = Bot::Tools::ActivityQueryTool.new
    tool_result = tool.query_activities(query: query)
    
    puts "工具结果: #{tool_result[:total_count]} 个素材"
    
    # 验证一致性
    if count == tool_result[:total_count]
      puts "一致性: ✅ 通过"
    else
      puts "一致性: ❌ 失败 (#{count} vs #{tool_result[:total_count]})"
    end
    
  rescue => e
    puts "❌ 查询失败: #{e.message}"
  end
end

puts "\n" + "=" * 60
puts "测试完成"
