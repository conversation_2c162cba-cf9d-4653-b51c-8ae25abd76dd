puts '=== 深度调试LLM工具调用问题 ==='
begin
  agent = Bot::Agent.first
  user = User.first

  puts "Agent: #{agent.name}"
  puts "User: #{user.name}"

  # 测试1: 检查LLM和适配器配置
  puts "\n=== 测试1: LLM和适配器配置 ==="
  assistant = agent.initialize_assistant
  llm = assistant.instance_variable_get(:@llm)
  puts "LLM类型: #{llm.class}"
  puts "LLM默认模型: #{llm.defaults[:chat_model]}"

  adapter = Langchain::Assistant::LLM::Adapter.build(llm)
  puts "适配器类型: #{adapter.class}"

  # 测试2: 检查工具注册
  puts "\n=== 测试2: 工具注册检查 ==="
  puts "Assistant工具数量: #{assistant.tools.count}"
  puts "Tool choice: #{assistant.instance_variable_get(:@tool_choice)}"

  assistant.tools.each_with_index do |tool, index|
    puts "工具 #{index + 1}: #{tool.class.name}"
    if tool.respond_to?(:function_schemas)
      schemas = tool.function_schemas
      puts "  函数: #{schemas.instance_variable_get(:@schemas).keys.join(', ')}"
    end
  end

  # 测试3: 手动构建chat参数
  puts "\n=== 测试3: 手动构建chat参数 ==="
  messages = [
    { role: 'system', content: assistant.instructions },
    { role: 'user', content: '过去一年里有多少素材？' }
  ]

  chat_params = adapter.build_chat_params(
    messages: messages.map { |msg| adapter.build_message(role: msg[:role], content: msg[:content]) },
    instructions: assistant.instructions,
    tools: assistant.tools,
    tool_choice: 'auto',
    parallel_tool_calls: true
  )

  puts 'Chat参数:'
  puts "  messages数量: #{chat_params[:messages].count}"
  puts "  tools数量: #{chat_params[:tools]&.count || 0}"
  puts "  tool_choice: #{chat_params[:tool_choice]}"
  puts "  parallel_tool_calls: #{chat_params[:parallel_tool_calls]}"

  # 测试4: 直接调用LLM
  puts "\n=== 测试4: 直接调用LLM ==="
  llm_response = llm.chat(**chat_params)
  puts "LLM响应类型: #{llm_response.class}"
  puts "响应内容: #{llm_response.chat_completion[0..200]}..."

  if llm_response.respond_to?(:tool_calls) && llm_response.tool_calls.present?
    puts '✅ 发现工具调用!'
    llm_response.tool_calls.each do |call|
      puts "  工具: #{call.dig('function', 'name')}"
      puts "  参数: #{call.dig('function', 'arguments')}"
    end
  else
    puts '❌ 没有工具调用'
  end
rescue StandardError => e
  puts "❌ 失败: #{e.message}"
  puts e.backtrace.first(10)
end
