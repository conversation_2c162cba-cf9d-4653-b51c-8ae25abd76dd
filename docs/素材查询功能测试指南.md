# 素材查询功能测试指南

## 测试环境准备

### 1. 确保服务运行
- 后端Rails服务: `http://localhost:3000`
- 前端Vue服务: `http://localhost:8080`
- LLM服务: `http://**************:7077`

### 2. 数据准备
确保数据库中有足够的测试数据：
```ruby
# 在Rails console中检查数据
Serve::Activity.count
Serve::Activity.where('created_at >= ?', 30.days.ago).count
Serve::Activity.where(state: 'published').count
```

## 核心功能测试

### 测试场景1: 时间范围查询

**测试用例1.1: 最近一个月的素材**
- **输入**: "最近一个月的素材"
- **预期结果**:
  - AI识别为"素材查询"Intent
  - 显示文本: "在最近一个月内，我们有X个素材记录..."
  - 显示ComActivityListTool组件
  - 组件标题显示查询条件
  - 列表显示最近30天的素材

**测试用例1.2: 最近一周的素材**
- **输入**: "最近一周的素材"
- **预期结果**:
  - 查询条件: `created_at >= 7.days.ago`
  - 显示最近7天的素材列表

**测试用例1.3: 最近三个月的素材**
- **输入**: "最近三个月的素材"
- **预期结果**:
  - 查询条件: `created_at >= 90.days.ago`
  - 显示最近90天的素材列表

### 测试场景2: 状态过滤查询

**测试用例2.1: 已发布的素材**
- **输入**: "已发布的素材"
- **预期结果**:
  - 查询条件: `state = 'published'`
  - 只显示已发布状态的素材

**测试用例2.2: 待发布的素材**
- **输入**: "待发布的素材"
- **预期结果**:
  - 查询条件: `state = 'pending'`
  - 只显示待发布状态的素材

**测试用例2.3: 草稿素材**
- **输入**: "草稿素材"
- **预期结果**:
  - 查询条件: `state = 'draft'`
  - 只显示草稿状态的素材

### 测试场景3: 组合条件查询

**测试用例3.1: 时间+状态组合**
- **输入**: "最近一周已发布的素材"
- **预期结果**:
  - 查询条件: `created_at >= 7.days.ago AND state = 'published'`
  - 显示同时满足时间和状态条件的素材

**测试用例3.2: 复杂组合查询**
- **输入**: "最近一个月待发布的素材"
- **预期结果**:
  - 正确解析多个条件
  - 显示精确的查询结果

### 测试场景4: 边界情况测试

**测试用例4.1: 空结果查询**
- **输入**: "最近一小时的素材"
- **预期结果**:
  - 显示"暂无符合条件的素材"
  - 组件正常渲染，显示空状态

**测试用例4.2: 模糊查询**
- **输入**: "素材"
- **预期结果**:
  - 显示所有素材（无特定条件）
  - 或提示用户提供更具体的查询条件

**测试用例4.3: 无效查询**
- **输入**: "昨天的新闻"
- **预期结果**:
  - AI可能不识别为素材查询
  - 或回退到通用回复

## 前端组件交互测试

### 测试场景5: 组件功能测试

**测试用例5.1: 素材卡片显示**
- **检查项**:
  - 素材标题正确显示
  - 创建时间格式正确
  - 状态标签正确显示
  - 缩略图正常加载

**测试用例5.2: 详情弹窗**
- **操作**: 点击素材卡片
- **预期结果**:
  - 弹出详情模态框
  - 显示完整的素材信息
  - 支持关闭操作

**测试用例5.3: 原文链接**
- **操作**: 点击"查看原文"按钮
- **预期结果**:
  - 新窗口打开原文链接
  - 链接地址正确

**测试用例5.4: 分页加载**
- **操作**: 滚动到列表底部
- **预期结果**:
  - 自动加载更多素材
  - 加载状态正确显示

### 测试场景6: 响应式设计测试

**测试用例6.1: 移动端适配**
- **检查项**:
  - 组件在移动设备上正常显示
  - 触摸操作响应正常
  - 文字大小适中

**测试用例6.2: 不同屏幕尺寸**
- **检查项**:
  - 大屏幕下布局合理
  - 小屏幕下内容不被截断

## 性能测试

### 测试场景7: 性能指标测试

**测试用例7.1: 查询响应时间**
- **指标**: 从输入到组件渲染完成 < 3秒
- **测试方法**: 使用浏览器开发者工具测量

**测试用例7.2: 大数据量测试**
- **场景**: 查询返回100+条素材
- **预期**: 组件正常渲染，无明显卡顿

**测试用例7.3: 并发查询测试**
- **场景**: 快速连续发送多个查询
- **预期**: 系统稳定，无错误

## 错误处理测试

### 测试场景8: 异常情况测试

**测试用例8.1: 网络异常**
- **模拟**: 断开网络连接
- **预期**: 显示友好的错误提示

**测试用例8.2: 服务器错误**
- **模拟**: 后端返回500错误
- **预期**: 组件降级显示，不崩溃

**测试用例8.3: LLM服务超时**
- **模拟**: LLM服务响应超时
- **预期**: 回退到默认处理或错误提示

## 调试指南

### 1. 后端调试

**检查Intent识别**:
```ruby
# Rails console
result = Chat::Intent.request_intent("最近一个月的素材")
puts result.inspect
```

**检查Artifact创建**:
```ruby
# 查看最新创建的Artifact
artifact = Bot::ActivityListArtifact.last
puts artifact.meta
puts artifact.activate
```

**检查查询执行**:
```ruby
# 手动执行查询
conditions = { created_at_gteq: 30.days.ago }
results = Serve::Activity.ransack(conditions).result
puts results.count
```

### 2. 前端调试

**检查组件接收的数据**:
```javascript
// 在ComActivityListTool.vue的setup方法中
console.log('接收到的props:', props.params);
console.log('查询参数:', props.params?.payload?.params?.q);
```

**检查API调用**:
```javascript
// 在浏览器开发者工具的Network面板中
// 查看ServeManageUserActivitiesApi的请求和响应
```

**检查组件渲染**:
```javascript
// 在浏览器开发者工具的Vue DevTools中
// 查看组件状态和数据流
```

### 3. 日志分析

**后端日志**:
```bash
# 查看Rails日志
tail -f log/development.log | grep -i "intent\|artifact\|ransack"
```

**前端日志**:
```javascript
// 浏览器控制台查看
// 组件加载、API调用、错误信息等
```

## 测试检查清单

### 基础功能 ✓
- [ ] 时间范围查询正常工作
- [ ] 状态过滤查询正常工作
- [ ] 组合条件查询正常工作
- [ ] 空结果处理正确

### 组件交互 ✓
- [ ] 素材卡片正确显示
- [ ] 详情弹窗正常工作
- [ ] 原文链接正确跳转
- [ ] 分页加载正常

### 性能表现 ✓
- [ ] 查询响应时间合理
- [ ] 大数据量处理正常
- [ ] 内存使用稳定

### 错误处理 ✓
- [ ] 网络异常处理正确
- [ ] 服务器错误处理正确
- [ ] 用户体验友好

### 兼容性 ✓
- [ ] 不同浏览器正常工作
- [ ] 移动端适配良好
- [ ] 响应式设计正确

## 预期测试结果

完成所有测试后，素材查询功能应该：

1. **准确识别用户意图**: 各种自然语言表达都能正确识别为素材查询
2. **精确执行查询**: 查询条件解析准确，数据库查询结果正确
3. **完美组件渲染**: 前端组件正常显示，交互流畅
4. **优秀用户体验**: 响应快速，界面友好，错误处理得当
5. **稳定系统性能**: 在各种情况下都能稳定运行

如果测试中发现问题，请参考调试指南进行排查，或查看相关文档进行修复。
