# 素材查询功能测试计划

## 测试目标
确定AI对话中素材查询功能的问题边界，特别是时间表达的识别和解析能力。

## 测试环境
- 系统：hz_iest_api开发环境
- Intent：素材查询 (ID: 18)
- 工具：Bot::Tools::ActivityQueryTool
- LLM模型：qwen

## 测试分类

### 1. 基础时间表达（预期成功）
- [ ] "最近一周的素材"
- [ ] "最近一个月的素材" 
- [ ] "最近三个月的素材"
- [ ] "今天的素材"
- [ ] "昨天的素材"

### 2. 年份查询（已知问题）
- [ ] "去年有多少素材？"
- [ ] "今年有多少素材？"
- [ ] "2024年有多少素材？"
- [ ] "2023年的素材"

### 3. 月份查询（已知问题）
- [ ] "上个月有多少新增的素材？"
- [ ] "本月有多少素材？"
- [ ] "这个月的素材"
- [ ] "1月份的素材"

### 4. 复杂时间表达（预期失败）
- [ ] "最近半个月有多少素材？"
- [ ] "最近两周的素材"
- [ ] "过去15天的素材"
- [ ] "前天到昨天的素材"

### 5. 数量查询表达
- [ ] "有多少素材？"
- [ ] "素材数量统计"
- [ ] "统计素材总数"
- [ ] "素材总共有几个？"

### 6. 状态查询
- [ ] "已发布的素材"
- [ ] "待发布的素材"
- [ ] "草稿状态的素材"

## 测试记录格式

每个测试用例记录：
1. **输入查询**：用户输入的原始查询
2. **是否触发工具**：是/否
3. **返回结果**：AI的回复内容
4. **组件显示**：ComActivityListTool是否出现
5. **查询条件**：解析出的Ransack条件（如果有）
6. **备注**：其他观察到的现象

## 测试执行

### 测试步骤
1. 清空对话历史（避免上下文干扰）
2. 输入测试查询
3. 观察后端日志
4. 记录前端显示结果
5. 检查数据库artifact记录

### 日志监控
```bash
tail -f log/development.log | grep -E "(素材查询|ActivityQueryTool|RansackDsl)"
```

### 数据库检查
```ruby
# 检查最新的artifact
Bot::ActivityListArtifact.order(created_at: :desc).limit(5)

# 检查解析的查询条件
artifact = Bot::ActivityListArtifact.last
puts artifact.meta['q']
```

## 预期结果分析

### 成功标准
1. AI识别出素材查询意图
2. 调用ActivityQueryTool
3. 创建ActivityListArtifact
4. 前端显示ComActivityListTool组件
5. 查询条件正确解析

### 失败模式
1. **Intent识别失败**：AI直接回答，不调用工具
2. **时间解析失败**：调用工具但查询条件为空或错误
3. **组件渲染失败**：有artifact但前端不显示组件

## 后续行动

根据测试结果确定：
1. 哪些时间表达可以正常工作
2. 哪些表达需要优化
3. 是否需要修改Intent描述
4. 是否需要增强时间解析逻辑

## 测试执行时间
计划执行时间：2025-08-01
执行人员：AI助手 + 用户
预计耗时：30-45分钟
