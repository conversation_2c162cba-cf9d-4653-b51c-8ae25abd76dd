# 素材查询功能使用指南

## 概述

通过AI对话框可以使用自然语言查询Serve::Activity（素材）记录。系统使用ActivityQueryTool将自然语言转换为查询条件，并返回前端组件可用的数据格式。

## 项目结构

- **ActivityQueryTool**: 位于 `rails_bot/app/services/bot/tools/activity_query_tool.rb`
- **ActivityListArtifact**: 位于 `rails_bot/app/models/bot/activity_list_artifact.rb`
- **前端组件**: 位于 `zj_iest/src/components/bot/tools/ComActivityListTool.vue`
- **Intent配置**: 数据库中的Bot::Intent记录（ID: 18，名称: "素材查询"）

## 支持的查询类型

### 1. 时间范围查询

```
用户输入: "最近一周有哪些新素材"
转换为: { created_at_gteq: 7.days.ago }

用户输入: "最近一个月的素材"
转换为: { created_at_gteq: 30.days.ago }

用户输入: "本月发布的素材"
转换为: {
  published_at_gteq: Time.current.beginning_of_month,
  published_at_lteq: Time.current.end_of_month
}
```

### 2. 状态查询

```
用户输入: "已发布的素材"
转换为: { state_eq: "published" }

用户输入: "待发布的素材"
转换为: { state_eq: "pending" }
```

### 3. 来源查询

```
用户输入: "微信公众号的素材"
转换为: { origin_name_cont: "微信" }
```

### 4. 标签查询

```
用户输入: "带有技术标签的素材"
转换为: { ai_tags_name_cont: "技术" }
```

### 5. 组合查询

```
用户输入: "最近一周已发布的技术类素材"
转换为: {
  g: [
    { created_at_gteq: 7.days.ago },
    { state_eq: "published" },
    { ai_tags_name_cont: "技术" }
  ],
  m: "and"
}
```

## 配置的字段

在Activity模型中配置了以下可搜索字段：

```ruby
searchable_attributes :name, :state, :created_at, :published_at
searchable_associations :origin, :ai_tags

def self.searchable_field_descriptions
  {
    name: {
      type: :attribute,
      column_type: :string,
      description: '素材名称'
    },
    state: {
      type: :attribute,
      column_type: :string,
      description: '素材状态',
      values: ['待发布(pending)', '已发布(published)']
    },
    created_at: {
      type: :attribute,
      column_type: :datetime,
      description: '创建时间，用于查询最近创建的素材'
    },
    published_at: {
      type: :attribute,
      column_type: :datetime,
      description: '发布时间，用于查询最近发布的素材'
    },
    origin: {
      type: :association,
      association_type: :belongs_to,
      fields: { name: '素材来源名称' }
    },
    ai_tags: {
      type: :association,
      association_type: :has_many,
      fields: { name: '标签名称' }
    }
  }
end
```

## 部署和使用

### 1. 运行seeds创建Intent

```bash
cd rails_bot
rails db:seed:replant FILTER=activity_intent
```

### 2. 在AI对话中使用

直接使用自然语言查询：
- "最近一周有哪些新素材"
- "查找已发布的素材"
- "微信公众号的素材"
- "带有技术标签的素材"

### 3. 编程方式使用

```ruby
# 创建工具实例
tool = Bot::Tools::ActivityTool.new

# 执行查询
result = tool.query_records(query: "最近一周有哪些新素材")
puts result[:total_count] # 输出符合条件的记录数量

# 使用预定义方法
tool.recent_activities(days: 7)
tool.published_activities
tool.pending_activities
```

## 测试

运行测试验证功能：

```bash
cd rails_bot
rspec spec/services/bot/tools/activity_tool_spec.rb
```

## 扩展

如需支持更多字段，在Activity模型的`searchable_field_descriptions`中添加相应配置即可。
