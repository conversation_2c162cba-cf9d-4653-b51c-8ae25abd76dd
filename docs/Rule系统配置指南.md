# Rule 系统配置指南

## 概述

Rule 系统是 hz_iest_api 项目中的核心功能，用于自动生成 Pack 并发送消息给相关用户。本文档详细说明了 Rule 系统的配置参数、工作原理和故障排查方法。

## 系统架构

### 核心模型关系

```
Rule (规则)
├── RuleItem (规则项) - 具体的执行单元
├── RuleConf (规则配置) - 时间调度配置
├── OrgRelation (组织关联) - 权限范围控制
├── Pack (执行包) - 自动生成的执行实例
└── Irs::GovernmentProject (政府项目缓存) - 政府项目API数据缓存
```

### 数据流程图

```
政府项目API → Irs::GovernmentProject缓存 →
BidProject.scope查询 → Rule触发 →
RuleRecord生成 → 调度执行 → Pack生成 → 消息发送
```

## Rule 模型配置

### 基本字段

- **name**: 规则名称 (必填)
- **state**: 规则状态 (`draft`, `used`, `closed`)
- **code**: 规则编号/类型 (如 `relate`)
- **latest_send_at**: 最新发送时间 (影响时间间隔控制)
- **options**: JSON 配置 (包含各种高级配置)

### 状态说明

- `draft`: 草稿状态，不会执行
- `used`: 启用状态，正常执行 ✅
- `closed`: 关闭状态，停止执行

### 关键方法

- `effective?`: 检查规则是否有效
- `used?`: 检查规则是否启用
- `all_related_orgs`: 获取所有关联的组织

## RuleItem 模型配置

### 基本字段

- **name**: 规则项名称
- **state**: 状态 (`used`, `todo`, `closed`)
- **option**: JSON 配置 (核心配置项)

### 状态要求

⚠️ **重要**: 只有状态为 `used` 的 RuleItem 才会参与 Pack 生成！

### option 配置结构

#### 1. record 配置 (数据源)

```json
{
	"record": {
		"enabled": true,
		"klass": "Serve::BidProject",
		"scopes": [
			{
				"method": "rule_government_setup_today",
				"scope_type": "class_method"
			}
		]
	}
}
```

#### 2. relate_user 配置 (目标用户)

```json
{
	"relate_user": {
		"method": "rule_contactor_ids",
		"args": ""
	}
}
```

#### 3. send_type 配置

- `direct`: 直接发送
- `relate`: 关联发送

## RuleConf 配置 (时间调度)

### 调度系统概述

RuleConf 控制 Rule 的执行时机，支持两种模式：

1. **事件驱动模式**: rule_conf 为空，Pack 立即生成
2. **调度驱动模式**: 配置 schedule_category，按计划执行

### advance_time 配置

控制 Rule 执行的时间间隔，防止频繁执行。

```json
{
	"advance_time": {
		"value": 1,
		"unit": "hour"
	}
}
```

#### 支持的时间单位

- `minute`: 分钟
- `hour`: 小时 ✅ 推荐
- `day`: 天
- `workday`: 工作日

#### 工作原理

- Rule 执行后会更新 `latest_send_at`
- 下次执行需要满足: `当前时间 - latest_send_at >= advance_time`
- 如果 `advance_time` 为空，可能导致时间判断错误

### schedule_category 配置 (调度模式)

#### interval 调度 (定时执行)

```json
{
	"schedule_category": "interval",
	"schedule_val": [
		{
			"hour": 22, // 执行小时 (0-23)
			"minute": 0, // 执行分钟 (0-59)
			"unit": "day", // 时间单位
			"value": 1 // 间隔值 (每1天)
		}
	]
}
```

#### 调度模式的影响

⚠️ **重要**: 使用调度模式时，Pack 的 send_at 将设置为未来的调度时间，而不是当前时间

**示例**:

- 配置: 每天 22:00 执行
- 当前时间: 2025-07-17 15:00
- Pack 的 send_at: 2025-07-17 22:00 (当天 22:00)
- 如果已过 22:00: 2025-07-18 22:00 (次日 22:00)

### 模式选择建议

#### 事件驱动模式 (推荐)

**适用场景**:

- 廉洁提醒类 Rule (立项后立即发送)
- 状态变更通知
- 实时业务提醒

**配置方法**:

```ruby
rule.update!(rule_conf: {})
```

#### 调度驱动模式

**适用场景**:

- 定期报告
- 批量数据处理
- 非紧急通知

**配置方法**:

```ruby
rule.rule_conf = {
  schedule_category: "interval",
  schedule_val: [{ hour: 9, minute: 0, unit: "day", value: 1 }],
  advance_time: { value: 1, unit: "hour" }
}
rule.save!
```

## 组织关联配置

### OrgRelation 模型

Rule 通过 OrgRelation 表关联组织，控制权限范围。

#### 关联模式

- `itself`: 仅关联指定组织
- `subtree`: 关联指定组织及其所有子组织

#### 配置方法

通过管理界面或代码配置：

```ruby
# 为Rule添加组织关联
rule.org_relations.create!(
  org: target_org,
  mode: 'itself'  # 或 'subtree'
)
```

### org 过滤机制

⚠️ **关键**: relate_records 方法会根据 Rule 关联的组织进行过滤：

```ruby
# 如果模型没有自定义find_records_by_rule方法
association.ransack(org_id_in: rule.all_related_orgs.map(&:id)).result
```

**常见问题**:

- Rule 没有关联任何组织 → 返回 0 个项目
- 项目的 org_id 为 nil → 被过滤掉

## Pack 自动生成流程

### 1. 数据筛选 (relate_records)

```ruby
# 1. 应用scope方法
projects = BidProject.rule_government_setup_today

# 2. 应用org过滤
filtered_projects = projects.ransack(
  org_id_in: rule.all_related_orgs.map(&:id)
).result

# 3. 过滤已有Pack
final_projects = filtered_projects.select do |project|
  # 有联系人 && 没有现有Pack
  project.rule_contactor_ids.any? &&
  !Pack.exists?(rule: rule, source: project)
end
```

### 2. Pack 创建

为每个符合条件的项目创建 Pack 实例。

### 3. 消息发送

根据 RuleItem 配置发送消息给目标用户。

## 常见问题排查

### 1. relate_records 返回 0 个项目

#### 检查步骤

```ruby
rule = Serve::Rule.find(rule_id)
item = rule.rule_items.first

# 1. 检查Rule状态
puts "Rule状态: #{rule.state}, 是否used: #{rule.used?}"

# 2. 检查RuleItem状态
puts "RuleItem状态: #{item.state}, 是否used: #{item.used?}"

# 3. 检查scope方法
scope_method = item.option.record.scopes.first.method
projects = BidProject.send(scope_method)
puts "scope方法返回: #{projects.count}个项目"

# 4. 检查org关联
puts "Rule关联org数量: #{rule.all_related_orgs.count}"

# 5. 检查org过滤
org_filtered = projects.ransack(
  org_id_in: rule.all_related_orgs.map(&:id)
).result
puts "org过滤后: #{org_filtered.count}个项目"
```

#### 常见原因

1. **Rule 状态不是 used** → 修改为 used
2. **RuleItem 状态不是 used** → 修改为 used
3. **Rule 没有关联组织** → 配置组织关联
4. **项目 org_id 为 nil** → 调用项目的 async!方法修复
5. **scope 方法返回空** → 检查数据或 scope 实现

### 2. Pack 重复生成问题

#### 问题现象

同一个项目生成了多个 Pack，例如：

- Pack 1: 项目 A - 法定代表人
- Pack 2: 项目 A - 项目联系人

#### 原因分析

**这通常是预期行为**，因为：

```ruby
# Rule.generate_pack! 方法
rule_items.used.each { |item| item.generate_pack!(rule_record: rule_record) }

# 如果Rule有多个used状态的RuleItem：
# - RuleItem 1: 发送给法定代表人
# - RuleItem 2: 发送给项目联系人
# 结果：每个项目 × RuleItem数量 = 多个Pack
```

#### 解决方案

1. **确认业务需求**: 是否需要分别发送给不同角色？
2. **如果不需要重复**: 只保留一个 RuleItem 为 used 状态
3. **如果需要合并**: 修改 RuleItem 配置，在一个 Pack 中包含多个目标用户

### 3. Pack 发送时间异常

#### 问题现象

Pack 的 send_at 显示未来时间，如：2025-11-25 22:00:00

#### 原因分析

**Pack 的 send_at 来自调度系统**：

```ruby
# Pack创建时
send_at: rule_record&.schedule_offset_at

# rule_record.schedule_offset_at来自Rule的调度配置
# 如果Rule配置了schedule_val，会计算下次执行时间
```

#### 检查调度配置

```ruby
rule = Serve::Rule.find(rule_id)
puts "调度配置: #{rule.rule_conf.schedule_category}"
puts "调度值: #{rule.rule_conf.schedule_val.inspect}"

# 示例配置：每天22:00执行
# schedule_val: [{ hour: 22, minute: 0, unit: "day", value: 1 }]
```

#### 解决方案

**方案 1: 禁用调度系统（推荐）**

```ruby
rule = Serve::Rule.find(rule_id)
rule.update!(rule_conf: {})
# Pack将使用当前时间作为send_at
```

**方案 2: 配置立即发送**

```ruby
rule = Serve::Rule.find(rule_id)
rule.rule_conf.advance_time = { value: 0, unit: 'hour' }
rule.save!
```

**方案 3: 修改调度时间**

```ruby
rule = Serve::Rule.find(rule_id)
rule.rule_conf.schedule_val = [
  { hour: Time.current.hour, minute: Time.current.min, unit: "day", value: 1 }
]
rule.save!
```

### 2. 时间条件不满足

#### 检查代码

```ruby
rule = Serve::Rule.find(rule_id)

# 检查advance_time配置
advance_time = rule.rule_conf.advance_time
puts "advance_time: #{advance_time.value} #{advance_time.unit}"

# 检查上次发送时间
puts "latest_send_at: #{rule.latest_send_at}"

# 计算时间差
if rule.latest_send_at
  time_diff = Time.current - rule.latest_send_at
  required_seconds = advance_time.value * 3600  # 假设单位是hour
  puts "时间差: #{time_diff}秒, 需要: #{required_seconds}秒"
  puts "满足条件: #{time_diff >= required_seconds}"
end
```

### 3. 项目数据问题

#### 修复项目 org_id

```ruby
# 批量修复org_id为nil的项目
BidProject.where(org_id: nil).find_each do |project|
  begin
    project.async!  # 从zbxm同步数据
    puts "修复项目 #{project.id}: #{project.name}"
  rescue => e
    puts "修复失败 #{project.id}: #{e.message}"
  end
end
```

## 最佳实践

### 1. Rule 配置原则

#### 基础配置检查清单

- ✅ Rule 状态设置为 `used`
- ✅ 配置组织关联 (至少 1 个组织)
- ✅ 选择合适的调度模式
- ✅ 设置合理的 `advance_time`

#### 调度模式选择

```ruby
# 事件驱动 (立即发送) - 推荐用于提醒类Rule
rule.update!(rule_conf: {})

# 调度驱动 (定时发送) - 推荐用于报告类Rule
rule.rule_conf = {
  schedule_category: "interval",
  schedule_val: [{ hour: 9, minute: 0, unit: "day", value: 1 }],
  advance_time: { value: 1, unit: "hour" }
}
```

### 2. RuleItem 配置原则

#### 状态管理

- ✅ 确保状态为 `used` (只有 used 状态才会执行)
- ✅ 避免多个 RuleItem 同时 used (除非需要分角色发送)
- ✅ 定期检查 RuleItem 的有效性

#### scope 方法选择

```ruby
# 推荐: 使用明确的时间范围
scope :rule_xxx_today, -> { where(created_at: Date.current.beginning_of_day..Date.current.end_of_day) }

# 避免: 使用模糊的时间条件
scope :rule_xxx_recent, -> { where("created_at > ?", 1.day.ago) }
```

#### relate_user 配置

```ruby
# 标准配置
{
  "relate_user": {
    "method": "rule_contactor_ids",  // 获取联系人ID的方法
    "args": ""                       // 方法参数
  }
}
```

### 3. 组织关联管理

#### 关联模式选择

- `itself`: 仅关联指定组织 (精确控制)
- `subtree`: 关联组织及其子组织 (范围覆盖)

#### 配置建议

```ruby
# 为Rule添加组织关联
rule.org_relations.create!(
  org: target_org,
  mode: 'subtree'  # 推荐使用subtree覆盖子组织
)

# 批量添加组织关联
target_orgs.each do |org|
  rule.org_relations.find_or_create_by!(
    org: org,
    mode: 'itself'
  )
end
```

### 4. 数据维护策略

#### 定期维护任务

```ruby
# 1. 检查项目org_id完整性
nil_org_count = Serve::BidProject.where(org_id: nil).count
puts "需要修复的项目数量: #{nil_org_count}"

# 2. 批量修复项目org_id (小批量执行)
Serve::BidProject.where(org_id: nil).limit(50).each do |project|
  project.async! rescue nil
end

# 3. 检查Rule健康状态
Serve::Rule.used.each do |rule|
  issues = []
  issues << "无组织关联" if rule.all_related_orgs.empty?
  issues << "无used状态的RuleItem" if rule.rule_items.used.empty?
  puts "Rule #{rule.id}: #{issues.any? ? issues.join(', ') : '正常'}"
end
```

#### 监控指标

- Pack 生成数量趋势
- 消息发送成功率
- Rule 执行频率
- 数据同步状态

### 5. 故障排查流程

#### 标准排查步骤

1. **检查 Rule 基础配置** (状态、组织关联)
2. **检查 RuleItem 配置** (状态、scope 方法)
3. **验证数据源** (scope 方法返回结果)
4. **检查 org 过滤** (项目 org_id 匹配)
5. **验证时间条件** (advance_time 设置)

#### 快速诊断命令

```ruby
def diagnose_rule(rule_id)
  rule = Serve::Rule.find(rule_id)

  puts "=== Rule #{rule_id} 诊断 ==="
  puts "状态: #{rule.state} (#{rule.used? ? '✅' : '❌'})"
  puts "组织: #{rule.all_related_orgs.count}个 (#{rule.all_related_orgs.any? ? '✅' : '❌'})"
  puts "RuleItem: #{rule.rule_items.used.count}个used (#{rule.rule_items.used.any? ? '✅' : '❌'})"

  rule.rule_items.used.each do |item|
    count = item.relate_records.count rescue 0
    puts "  #{item.name}: #{count}个项目"
  end
end
```

### 6. 性能优化建议

#### 数据库优化

```sql
-- 推荐索引
CREATE INDEX idx_serve_rules_state ON serve_rules(state);
CREATE INDEX idx_serve_rule_items_state ON serve_rule_items(state);
CREATE INDEX idx_serve_bid_projects_org_id ON serve_bid_projects(org_id);
CREATE INDEX idx_serve_packs_rule_source ON serve_packs(rule_id, source_id, source_type);
```

#### 查询优化

```ruby
# 使用includes预加载关联数据
rule.rule_items.used.includes(:rule, :option)

# 避免N+1查询
projects = BidProject.includes(:org, :zbxm)
```

### 7. 安全考虑

#### 权限控制

- Rule 配置修改需要管理员权限
- 组织关联变更需要审批流程
- 敏感数据访问需要日志记录

#### 数据保护

- 定期备份 Rule 配置
- 重要变更前创建快照
- 测试环境验证后再应用到生产

## 配置示例

### 完整的 Rule 配置示例

```ruby
# 创建Rule
rule = Serve::Rule.create!(
  name: "施工许可证办理",
  state: "used",
  code: "relate",
  rule_conf: {
    advance_time: {
      value: 1,
      unit: "hour"
    }
  }
)

# 配置组织关联
rule.org_relations.create!(
  org: target_org,
  mode: "itself"
)

# 创建RuleItem
rule.rule_items.create!(
  name: "施工许可证办理-项目联系人",
  state: "used",
  option: {
    send_type: "relate",
    record: {
      enabled: true,
      klass: "Serve::BidProject",
      scopes: [
        {
          method: "rule_government_construction_permit_today",
          scope_type: "class_method"
        }
      ]
    },
    relate_user: {
      method: "rule_contactor_ids",
      args: ""
    }
  }
)
```

## 高级配置

### scope 方法说明

#### 常用 scope 方法

- `rule_government_setup_today`: 今天立项的项目
- `rule_government_setup_yesterday`: 昨天立项的项目
- `rule_government_construction_permit_today`: 今天施工许可的项目
- `rule_construction_permit_current_bid`: 当前招标的施工许可项目

#### 自定义 scope 方法

在 BidProject 模型中定义：

```ruby
class Serve::BidProject
  scope :rule_custom_scope, -> {
    where("payload->>'custom_field' IS NOT NULL")
      .where(created_at: Date.current.beginning_of_day..Date.current.end_of_day)
  }
end
```

### 消息模板配置

#### template_message 配置

```json
{
	"message": {
		"enabled": true,
		"template_message_title": "廉洁提醒",
		"template_message_body": "您好，您的项目【{{project_name}}】需要注意廉洁要求",
		"templates": [
			{
				"method": "project_name",
				"key": "project_name"
			}
		]
	}
}
```

### 调试工具

#### 1. 检查 Rule 完整状态

```ruby
def debug_rule(rule_id)
  rule = Serve::Rule.find(rule_id)

  puts "=== Rule #{rule_id} 调试信息 ==="
  puts "名称: #{rule.name}"
  puts "状态: #{rule.state} (used?: #{rule.used?})"
  puts "类型: #{rule.code}"
  puts "最后发送: #{rule.latest_send_at}"

  # 时间配置
  if rule.rule_conf.advance_time.present?
    puts "时间间隔: #{rule.rule_conf.advance_time.value} #{rule.rule_conf.advance_time.unit}"
  else
    puts "⚠️  时间间隔未配置"
  end

  # 组织关联
  puts "关联组织数量: #{rule.all_related_orgs.count}"
  if rule.all_related_orgs.empty?
    puts "⚠️  未配置组织关联"
  end

  # RuleItem状态
  puts "\n=== RuleItems ==="
  rule.rule_items.each do |item|
    puts "#{item.name}: 状态=#{item.state} (used?: #{item.used?})"

    # 检查relate_records
    if item.used?
      count = item.relate_records.count
      puts "  relate_records返回: #{count}个项目"
      if count == 0
        puts "  ⚠️  没有符合条件的项目"
      end
    end
  end
end

# 使用示例
debug_rule(44)
```

#### 2. 批量修复工具

```ruby
def fix_rule_issues(rule_id)
  rule = Serve::Rule.find(rule_id)

  puts "=== 修复Rule #{rule_id} ==="

  # 1. 修复Rule状态
  if rule.state != 'used'
    rule.update!(state: 'used')
    puts "✅ Rule状态修复为used"
  end

  # 2. 修复RuleItem状态
  rule.rule_items.each do |item|
    if item.state != 'used'
      item.update!(state: 'used')
      puts "✅ RuleItem #{item.id} 状态修复为used"
    end
  end

  # 3. 检查advance_time
  if rule.rule_conf.advance_time.blank?
    puts "⚠️  需要手动配置advance_time"
  end

  # 4. 检查组织关联
  if rule.all_related_orgs.empty?
    puts "⚠️  需要手动配置组织关联"
  end

  puts "修复完成！"
end
```

## 监控和维护

### 1. 定期检查脚本

```ruby
# 检查所有Rule的健康状态
def check_all_rules_health
  Serve::Rule.used.each do |rule|
    puts "\n=== Rule #{rule.id}: #{rule.name} ==="

    # 检查基本配置
    issues = []
    issues << "无组织关联" if rule.all_related_orgs.empty?
    issues << "无advance_time" if rule.rule_conf.advance_time.blank?
    issues << "无used状态的RuleItem" if rule.rule_items.used.empty?

    if issues.any?
      puts "⚠️  问题: #{issues.join(', ')}"
    else
      puts "✅ 配置正常"
    end

    # 检查最近执行情况
    recent_packs = rule.packs.where(created_at: 1.day.ago..)
    puts "最近24小时Pack数量: #{recent_packs.count}"
  end
end
```

### 2. 数据同步维护

```ruby
# 修复项目org_id
def fix_project_orgs
  nil_org_projects = Serve::BidProject.where(org_id: nil)
  puts "发现 #{nil_org_projects.count} 个项目org_id为nil"

  nil_org_projects.find_each do |project|
    begin
      project.async!
      puts "✅ 修复项目 #{project.id}"
    rescue => e
      puts "❌ 修复失败 #{project.id}: #{e.message}"
    end
  end
end
```

## 性能优化

### 1. 索引建议

确保以下字段有适当的索引：

- `serve_rules.state`
- `serve_rule_items.state`
- `serve_bid_projects.org_id`
- `serve_packs.rule_id`
- `org_relations.source_id, source_type`

### 2. 查询优化

- 使用 `includes` 预加载关联数据
- 避免在循环中执行数据库查询
- 合理使用缓存

## 运维手册

### 1. 日常检查清单

#### 每日检查 (5 分钟)

```ruby
# 检查Rule系统整体状态
puts "=== Rule系统日常检查 ==="
puts "活跃Rule数量: #{Serve::Rule.used.count}"
puts "今日Pack数量: #{Serve::Pack.where(created_at: Date.current.beginning_of_day..).count}"
puts "待发送Pack: #{Serve::Pack.where(state: 'pending').count}"

# 检查异常Rule
Serve::Rule.used.each do |rule|
  if rule.all_related_orgs.empty?
    puts "⚠️  Rule #{rule.id} 无组织关联"
  end
  if rule.rule_items.used.empty?
    puts "⚠️  Rule #{rule.id} 无有效RuleItem"
  end
end
```

#### 每周检查 (15 分钟)

```ruby
# 数据完整性检查
nil_org_count = Serve::BidProject.where(org_id: nil).count
puts "org_id为nil的项目: #{nil_org_count}个"

# Pack生成趋势
7.times do |i|
  date = i.days.ago.to_date
  count = Serve::Pack.where(created_at: date.beginning_of_day..date.end_of_day).count
  puts "#{date}: #{count}个Pack"
end
```

### 2. 常见运维任务

#### 新增 Rule 的标准流程

1. **创建 Rule 基础配置**
2. **配置组织关联**
3. **创建 RuleItem**
4. **测试验证**
5. **启用 Rule**

```ruby
# 示例：创建新的廉洁提醒Rule
rule = Serve::Rule.create!(
  name: "新项目廉洁提醒",
  state: "draft",  # 先创建为草稿
  code: "relate"
)

# 配置组织关联
target_orgs = Serve::Org.where(name: ["市住建局", "市发改委"])
target_orgs.each do |org|
  rule.org_relations.create!(org: org, mode: "subtree")
end

# 创建RuleItem
rule.rule_items.create!(
  name: "项目立项提醒",
  state: "draft",  # 先创建为草稿
  option: {
    send_type: "relate",
    record: {
      enabled: true,
      klass: "Serve::BidProject",
      scopes: [{ method: "rule_government_setup_today", scope_type: "class_method" }]
    },
    relate_user: { method: "rule_contactor_ids", args: "" }
  }
)

# 测试验证后启用
rule.update!(state: "used")
rule.rule_items.update_all(state: "used")
```

#### 修改现有 Rule 配置

```ruby
# 安全修改流程
rule = Serve::Rule.find(rule_id)

# 1. 备份当前配置
backup = {
  rule_conf: rule.rule_conf.dup,
  state: rule.state,
  rule_items: rule.rule_items.map { |item|
    { id: item.id, state: item.state, option: item.option.dup }
  }
}

# 2. 应用修改
rule.update!(rule_conf: new_config)

# 3. 验证修改
# 测试relate_records是否正常
# 检查Pack生成是否符合预期

# 4. 如需回滚
rule.update!(rule_conf: backup[:rule_conf])
```

#### 批量数据修复

```ruby
# 修复项目org_id (分批执行，避免超时)
batch_size = 50
Serve::BidProject.where(org_id: nil).find_in_batches(batch_size: batch_size) do |batch|
  batch.each do |project|
    begin
      project.async!
      puts "✅ 修复项目 #{project.id}"
    rescue => e
      puts "❌ 修复失败 #{project.id}: #{e.message}"
    end
  end
  sleep 1  # 避免数据库压力过大
end
```

### 3. 故障恢复

#### 紧急修复步骤

如果 Rule 系统完全不工作：

1. **检查 Redis 连接**

```ruby
# 测试Redis连接
Redis.current.ping
```

2. **检查 Rule 基本配置**

```ruby
# 检查关键Rule状态
critical_rules = [39, 44]  # 重要Rule ID
critical_rules.each do |rule_id|
  rule = Serve::Rule.find(rule_id)
  puts "Rule #{rule_id}: #{rule.state}, 组织#{rule.all_related_orgs.count}个"
end
```

3. **修复组织关联**

```ruby
# 为缺失组织关联的Rule添加关联
Serve::Rule.used.each do |rule|
  if rule.all_related_orgs.empty?
    # 复制其他Rule的组织关联
    template_rule = Serve::Rule.find(39)  # 使用Rule 39作为模板
    template_rule.org_relations.each do |relation|
      rule.org_relations.create!(org: relation.org, mode: relation.mode)
    end
  end
end
```

4. **重启相关服务**

```bash
# 重启Rails应用
docker restart puma_hz_iest_api

# 重启Redis (如需要)
docker restart redis_container
```

#### 回滚方案

如果配置错误导致问题：

```ruby
# 1. 恢复Rule状态
rule.update!(state: "draft")  # 暂停Rule执行

# 2. 清理错误生成的Pack
error_packs = Serve::Pack.where(rule: rule, created_at: 1.hour.ago..)
error_packs.destroy_all

# 3. 恢复正确配置
rule.update!(rule_conf: backup_config)
rule.update!(state: "used")  # 重新启用
```

### 4. 监控和告警

#### 关键指标监控

- Rule 执行成功率
- Pack 生成数量异常
- 消息发送失败率
- 数据同步延迟

#### 告警规则建议

```ruby
# 检查是否有Rule长时间未生成Pack
Serve::Rule.used.each do |rule|
  last_pack = rule.packs.order(created_at: :desc).first
  if last_pack.nil? || last_pack.created_at < 24.hours.ago
    puts "⚠️  Rule #{rule.id} 超过24小时未生成Pack"
  end
end

# 检查Pack发送失败率
failed_rate = Serve::Pack.where(created_at: 1.day.ago.., state: 'failed').count.to_f /
              Serve::Pack.where(created_at: 1.day.ago..).count
if failed_rate > 0.1  # 失败率超过10%
  puts "⚠️  Pack发送失败率过高: #{(failed_rate * 100).round(2)}%"
end
```

## 快速参考

### 常用命令速查

#### Rule 状态检查

```ruby
# 检查单个Rule
rule = Serve::Rule.find(rule_id)
puts "#{rule.name}: #{rule.state}, 组织#{rule.all_related_orgs.count}个, RuleItem#{rule.rule_items.used.count}个"

# 检查所有Rule
Serve::Rule.used.each { |r| puts "Rule #{r.id}: #{r.name} - #{r.rule_items.used.count}个RuleItem" }
```

#### 快速修复命令

```ruby
# 修复Rule状态
Serve::Rule.find(rule_id).update!(state: "used")

# 修复RuleItem状态
Serve::Rule.find(rule_id).rule_items.update_all(state: "used")

# 禁用调度系统
Serve::Rule.find(rule_id).update!(rule_conf: {})

# 修复项目org_id
Serve::BidProject.where(org_id: nil).limit(10).each(&:async!)
```

#### 测试命令

```ruby
# 测试relate_records
rule = Serve::Rule.find(rule_id)
item = rule.rule_items.used.first
puts "relate_records: #{item.relate_records.count}个项目"

# 手动创建Pack测试
pack = Serve::Pack.create!(
  rule: rule,
  rule_item: item,
  source: Serve::BidProject.first,
  state: 'pending'
)
```

### 配置模板

#### 事件驱动 Rule 模板

```ruby
rule = Serve::Rule.create!(
  name: "事件驱动提醒",
  state: "used",
  code: "relate",
  rule_conf: {}  # 空配置，事件驱动
)
```

#### 调度驱动 Rule 模板

```ruby
rule = Serve::Rule.create!(
  name: "定时报告",
  state: "used",
  code: "relate",
  rule_conf: {
    schedule_category: "interval",
    schedule_val: [{ hour: 9, minute: 0, unit: "day", value: 1 }],
    advance_time: { value: 1, unit: "hour" }
  }
)
```

### 故障排查清单

#### ✅ 基础检查

- [ ] Rule 状态是否为 used
- [ ] RuleItem 状态是否为 used
- [ ] Rule 是否有组织关联
- [ ] 项目 org_id 是否为 nil

#### ✅ 数据检查

- [ ] scope 方法是否返回数据
- [ ] org 过滤后是否有项目
- [ ] 项目是否有联系人
- [ ] 是否存在重复 Pack

#### ✅ 时间检查

- [ ] advance_time 是否配置合理
- [ ] 调度模式是否符合业务需求
- [ ] Pack 的 send_at 是否正确

### 联系信息

**技术支持**: 开发团队
**文档维护**: 系统管理员
**紧急联系**: 运维团队

## 消息接收者标签分配系统

### 概述

基于消息发送记录为人员分配标签的系统，支持通过 Pack 或 Rule 为已发送消息的接收者批量分配现有的人员标签。

### 核心服务类

`MessageBasedTagAssignmentService` 提供以下功能：

- 为指定 Pack 中已发送消息的人员分配标签
- 为指定 Rule 中已发送消息的人员分配标签
- 获取 Pack/Rule 的消息接收者统计信息

### 使用方法

#### 1. 通过 Rake 任务使用

```bash
# 为Pack的消息接收者分配标签
rake message_tags:assign_by_pack[123,'公职人员','滨江区']

# 为Rule的消息接收者分配标签
rake message_tags:assign_by_rule[66,'公职人员','滨江区']

# 查看Pack的统计信息
rake message_tags:stats_by_pack[123]

# 查看Rule的统计信息
rake message_tags:stats_by_rule[66]

# 列出所有可用标签
rake message_tags:list_tags

# 显示帮助信息
rake message_tags:help
```

#### 2. 通过代码调用

```ruby
# 为Pack分配标签
pack = Serve::Pack.find(123)
result = MessageBasedTagAssignmentService.assign_tag_by_pack(pack, "公职人员", "滨江区")

# 为Rule分配标签
rule = Serve::Rule.find(66)
result = MessageBasedTagAssignmentService.assign_tag_by_rule(rule, "公职人员", "滨江区")

# 获取统计信息
stats = MessageBasedTagAssignmentService.get_pack_message_recipients_stats(pack)
```

### 分配规则

- 只为成功发送的消息（state: 'successed'）的接收者分配标签
- 跳过已有相同标签的用户，避免重复分配
- 只为属于指定组织的用户分配标签
- 标签必须在系统中已存在，不会自动创建新标签

### 未来发展方向

#### 智能标签推断系统

基于消息接收记录自动推断用户身份特征并分配标签：

**1. 公职人员标签推断**

- 接收廉洁提醒消息 → 推断为公职人员
- 接收节假日廉洁提醒 → 公职人员可能性很高

**2. 中层干部标签推断**

- 频繁接收项目管理类消息 → 可能是中层管理人员
- 接收招标业务消息且数量较多 → 可能是业务管理人员

**3. 关键少数标签推断**

- 接收"政治生日通知上级领导"类消息 → 可能是领导层
- 同时接收多种类型的重要消息 → 领导层特征

**4. 纪检人员标签推断**

- 廉洁相关消息占比 ≥80% → 可能是纪检监察人员
- 接收廉洁提醒消息的频次特别高 → 工作需要特征

**5. 一般干部标签推断**

- 接收适量业务消息但不符合中层特征 → 一般工作人员
- 主要接收 direct 类型消息 → 基础岗位特征

#### 实现思路

```ruby
# 未来可扩展的智能推断方法
def assign_intelligent_tags(user, org_name = "滨江区")
  messages = user.serve_messages.includes(:rule)
  return if messages.empty?

  tags_to_assign = []

  # 基于消息记录推断标签
  tags_to_assign << "公职人员" if should_assign_public_official_tag?(user, messages)
  tags_to_assign << "中层干部" if should_assign_middle_manager_tag?(user, messages)
  tags_to_assign << "关键少数" if should_assign_key_minority_tag?(user, messages)
  tags_to_assign << "纪检人员" if should_assign_discipline_staff_tag?(user, messages)
  tags_to_assign << "一般干部" if should_assign_general_staff_tag?(user, messages)

  # 分配标签
  assign_tags(user, tags_to_assign.compact.uniq, org_name)
end
```

#### 技术挑战

- 消息内容分析的准确性有限
- 需要结合浙政钉数据进行交叉验证
- 标签推断规则需要持续优化和调整
- 需要建立标签分配的审核和纠错机制

## Rule 66 政府项目变更通知配置

### 概述

Rule 66 是专门用于政府项目变更通知的规则，基于政府项目 API 数据自动生成廉洁提醒。

### 配置详情

- **Rule ID**: 66
- **名称**: 项目变更申请
- **类型**: `relate` (关联发送)
- **租户**: IEST + BID (多租户配置)
- **调度**: 每天 10:00 执行 (提前 1 小时即 9:00)

### 数据源配置

- **Scope**: `rule_government_project_change_yesterday`
- **数据来源**: 政府项目 API 缓存 (Irs::GovernmentProject)
- **触发条件**: 昨天有政府审批变更的项目

### 政府项目 API 缓存系统

#### 缓存表结构

- **表名**: `irs_government_projects`
- **主要字段**:
  - `query_date`: 查询日期
  - `success`: 成功状态
  - `projects_count`: 项目数量
  - `raw_data`: 原始 API 响应数据
  - `cached_at`: 缓存时间

#### 缓存数据统计 (截至 2025-07-18)

- **时间跨度**: 2024-12-02 至 2025-07-17 (228 天)
- **总记录数**: 228 条
- **成功记录**: 228 条 (100%成功率)
- **总项目数**: 约 15,000+个项目
- **平均每日项目数**: 约 66 个项目

#### 常用查询语句

```sql
-- 检查缓存数据时间跨度
SELECT MIN(query_date) as earliest_date, MAX(query_date) as latest_date, COUNT(*) as total_records
FROM irs_government_projects;

-- 按日期统计记录数和项目数
SELECT query_date, COUNT(*) as record_count, SUM(projects_count) as total_projects,
       COUNT(CASE WHEN success = true THEN 1 END) as successful_count
FROM irs_government_projects
GROUP BY query_date
ORDER BY query_date DESC;

-- 检查最近30天的数据
SELECT query_date, projects_count, success, error_message, cached_at
FROM irs_government_projects
WHERE query_date >= CURRENT_DATE - INTERVAL '30 days'
ORDER BY query_date DESC;

-- 检查特定日期的项目详情
SELECT raw_data->'governmentProjectList' as projects
FROM irs_government_projects
WHERE query_date = '2025-07-17' AND success = true;
```

### Rule 66 工作流程

#### 1. 数据获取流程

```
政府项目API → Irs::GovernmentProject缓存 →
BidProject.rule_government_project_change_yesterday →
过滤已发送通知的项目 → 生成Pack
```

#### 2. 匹配链条

```
政府API项目 → deal_code →
Biz::Zbxm.invest_project_code →
Biz::Zbxm.projectCode →
Serve::BidProject.code
```

#### 3. 过滤逻辑

- 只选择昨天有政府审批变更的项目
- 排除当天已发送"项目变更通知"或"工程变更通知"的项目
- 只为有联系人数据的项目生成 Pack

### Pack 租户关联修复

#### 问题描述

Rule 66 的 Pack 创建时存在租户关联问题：

- **TanentAction 记录**: 正确创建 ✅
- **tanent_id 字段**: 未设置 ❌
- **前端查询**: 依赖 tanent_id 字段，导致 Pack 不可见

#### 修复方案

1. **批量修复现有 Pack**: 设置`tanent_id = 1` (IEST)
2. **修复 Pack 模型**: 更新`ensure_tanent_association`方法
3. **代码修复**: 在 rails_iest gem 中实现

#### 修复后的 ensure_tanent_association 方法

```ruby
def ensure_tanent_association
  # 检查tanent_id字段是否已设置（前端依赖此字段）
  return if tanent_id.present?

  target_tanents = determine_target_tanents
  if target_tanents.any?
    # 设置tanent_id字段（前端依赖）
    primary_tanent_id = target_tanents.find { |t| t.code == 'IEST' }&.id || target_tanents.first.id
    update_column(:tanent_id, primary_tanent_id)

    # 创建TanentAction记录
    target_tanents.each do |tanent|
      Serve::TanentAction.find_or_create_by(
        target_type: 'Serve::Pack',
        target_id: id,
        user_type: 'Tanent',
        user_id: tanent.id,
        action_type: 'relate_tanent'
      )
    end
  end
end
```

### 验证和监控

#### Pack 生成验证

```ruby
# 检查Rule 66的Pack
rule66 = Serve::Rule.find(66)
packs = rule66.packs.order(created_at: :desc).limit(10)

packs.each do |pack|
  puts "Pack #{pack.id}: tanent_id=#{pack.tanent_id}, tanents=#{pack.tanents.pluck(:id)}"
end
```

#### 前端查询验证

```ruby
# 模拟前端查询
tanent_1_packs = Serve::Pack.where(tanent_id: 1, rule: rule66)
puts "前端能看到的Pack数量: #{tanent_1_packs.count}"
```

### 故障排查

#### 常见问题

1. **Pack 不在前端显示**: 检查 tanent_id 字段是否设置
2. **没有项目变更**: 检查政府项目 API 缓存是否正常
3. **Pack 生成失败**: 检查用户数据(联系人/法人)是否存在

#### 调试命令

```ruby
# 检查政府项目缓存
Irs::GovernmentProject.recent.limit(5).each do |record|
  puts "#{record.query_date}: #{record.projects_count}个项目"
end

# 检查Rule 66触发数据
projects = Serve::BidProject.rule_government_project_change_yesterday
puts "昨天变更项目: #{projects.count}个"

# 手动生成Pack
rule66 = Serve::Rule.find(66)
result = rule66.generate_pack!
puts "Pack生成结果: #{result}"
```

---

**注意**: 本文档基于实际调试经验编写，涵盖了 Rule 系统的核心配置和常见问题。建议定期更新此文档以反映系统变化。

**版本**: v2.1
**最后更新**: 2025-07-18
**维护者**: 开发团队
