# 系统架构

## 核心组件

### 1. Irs::GovernmentProjectService
政府项目接口服务，负责：
- 调用政府项目API
- 缓存数据管理
- 错误处理和降级

### 2. Irs::GovernmentProject
缓存数据模型，包含：
- 查询日期和原始数据
- 项目数量统计
- 成功/失败状态
- 缓存时间戳

### 3. Serve::BidProject
招投标项目模型，提供：
- 政府项目数据集成
- 项目状态更新方法
- 过滤和查询scope

### 4. 缓存管理工具
- Rake任务：缓存管理和维护
- 后台任务：自动清理和预热
- 监控工具：数据完整性检查

## 缓存策略

### 缓存机制
- **缓存时间**: 24小时自动过期
- **存储方式**: PostgreSQL数据库表 `irs_government_projects`
- **查询优先级**: 缓存优先，接口降级
- **默认查询**: 昨天的数据（Date.yesterday）

### 缓存流程

```mermaid
graph TD
    A[请求数据] --> B{缓存存在?}
    B -->|是| C{缓存有效?}
    B -->|否| D[调用API]
    C -->|是| E[返回缓存数据]
    C -->|否| D[调用API]
    D --> F{API成功?}
    F -->|是| G[更新缓存]
    F -->|否| H{有过期缓存?}
    G --> I[返回新数据]
    H -->|是| J[返回过期缓存]
    H -->|否| K[返回错误]
    
    style E fill:#e8f5e8
    style I fill:#e8f5e8
    style J fill:#fff3e0
    style K fill:#ffebee
```

### 缓存优势

1. **性能提升**: 避免重复的网络请求，响应速度提升90%+
2. **降级保护**: 接口异常时使用缓存数据，保证业务连续性
3. **数据持久化**: 历史数据可追溯查询，支持数据分析
4. **智能刷新**: 支持强制刷新和自动过期机制

## 数据库设计

### irs_government_projects 表结构

```sql
CREATE TABLE irs_government_projects (
  id BIGSERIAL PRIMARY KEY,
  query_date DATE NOT NULL,           -- 查询日期
  raw_data JSONB NOT NULL,           -- 原始API响应数据
  projects_count INTEGER DEFAULT 0,  -- 项目数量
  success BOOLEAN DEFAULT true,      -- 查询是否成功
  error_message TEXT,                -- 错误信息
  cached_at TIMESTAMP NOT NULL,      -- 缓存时间
  created_at TIMESTAMP NOT NULL,
  updated_at TIMESTAMP NOT NULL
);

-- 索引
CREATE UNIQUE INDEX idx_government_projects_date ON irs_government_projects(query_date);
CREATE INDEX idx_government_projects_cached_at ON irs_government_projects(cached_at);
CREATE INDEX idx_government_projects_success ON irs_government_projects(success);
```

## 系统集成

### 与现有系统的集成点

1. **BidProject模型扩展**
   - 新增政府项目相关scope
   - 项目状态更新方法
   - 元数据存储

2. **缓存层集成**
   - 利用现有Redis缓存
   - 数据库缓存策略
   - 内存缓存优化

3. **任务调度集成**
   - 集成现有的任务队列
   - 定时任务管理
   - 错误处理和重试

## 性能考虑

### 查询优化
- 数据库索引优化
- JSON查询性能
- 分页和限制

### 缓存优化
- 缓存命中率监控
- 内存使用优化
- 过期策略调整

### 网络优化
- 连接池管理
- 超时设置
- 重试机制
