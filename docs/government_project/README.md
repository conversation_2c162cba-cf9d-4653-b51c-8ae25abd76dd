# 政府项目信息接口系统

## 概述

政府项目信息接口系统用于获取政府投资项目的审批信息，包括立项、施工许可证、项目变更等各个阶段的数据。本系统已实现智能缓存策略，避免重复调用接口，提高查询效率和系统稳定性。

## 📚 文档导航

### 基础文档
- [系统架构](./architecture.md) - 核心组件和缓存策略
- [数据结构说明](./data_structure.md) - 接口数据格式和局限性
- [快速开始](./quick_start.md) - 基本使用方法和示例

### 使用指南
- [API使用指南](./api_usage.md) - 详细的API调用方法
- [缓存管理](./cache_management.md) - 缓存操作和管理工具
- [BidProject集成](./bid_project_integration.md) - 与BidProject模型的集成使用

### 高级功能
- [数据同步流程](./data_sync_workflow.md) - 完整的数据获取和处理流程
- [扩展开发指南](./extension_guide.md) - 集成其他接口和扩展功能
- [监控和告警](./monitoring.md) - 系统监控和数据完整性检查

### 运维文档
- [故障排除](./troubleshooting.md) - 常见问题和解决方案
- [最佳实践](./best_practices.md) - 使用建议和优化方案
- [版本更新记录](./changelog.md) - 功能更新和变更记录

## 🚀 快速开始

```ruby
# 获取昨天的政府项目数据
result = Irs::GovernmentProjectService.fetch_projects_by_date

# 获取昨日立项的项目
setup_projects = Serve::BidProject.rule_government_setup_yesterday

# 查看缓存统计
rake government_project:cache:stats
```

## 🔧 核心功能

- ✅ **智能缓存**: 24小时缓存策略，避免重复API调用
- ✅ **降级保护**: 接口异常时自动使用缓存数据
- ✅ **类型过滤**: 支持立项、施工许可证、变更等类型过滤
- ✅ **BidProject集成**: 无缝集成现有项目管理系统
- ✅ **管理工具**: 完整的缓存管理和监控工具

## ⚠️ 重要说明

**数据局限性**: 政府项目接口只返回审批事件的基本信息（项目编号、审批事项、完成时间），不包含项目的详细信息（名称、金额、联系人等）。详细信息需要通过其他接口或数据源获取。

## 📞 支持

如有问题，请查看 [故障排除文档](./troubleshooting.md) 或联系开发团队。

---

> **注意**: 此文档替代了原来的 `government_project_api_usage.md` 大文档。新的模块化结构更易于维护和查找信息。
