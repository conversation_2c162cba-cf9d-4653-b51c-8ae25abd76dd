# 故障排除

## 常见问题

### 1. scope方法返回0个结果

**问题描述**: 调用 `rule_government_setup_yesterday` 等方法返回0个项目

**原因分析**:
- 本地BidProject表中没有匹配的项目记录
- 政府项目的deal_code与本地项目的code字段不匹配

**解决方案**:

```ruby
# 检查缓存中的项目数据
cache = Irs::GovernmentProject.find_by(query_date: Date.yesterday)
projects = cache.raw_data['governmentProjectList']

# 检查本地是否有匹配的项目
codes = projects.map { |p| p['deal_code'] }
local_projects = Serve::BidProject.where(code: codes)
puts "缓存中项目数: #{projects.size}, 本地匹配数: #{local_projects.count}"

# 查看未匹配的项目编号
missing_codes = codes - local_projects.pluck(:code)
puts "未匹配的项目编号: #{missing_codes}"
```

### 2. 缓存数据不存在

**问题描述**: 提示缓存数据不存在

**解决方案**:
```bash
# 手动刷新指定日期的缓存
rake government_project:cache:refresh[2025-07-01]

# 预热最近几天的缓存
rake government_project:cache:warmup[7]
```

### 3. 接口调用超时

**问题描述**: API调用超时或网络错误

**解决方案**:
- 检查网络连接
- 增加超时时间配置
- 使用缓存数据作为降级

```ruby
# 检查网络连接
result = Irs::GovernmentProjectService.fetch_projects_by_date('2025-07-01', force_refresh: true)
puts result.message if result.failure?

# 使用缓存数据（即使过期）
cache_record = Irs::GovernmentProject.find_by(query_date: '2025-07-01')
if cache_record
  puts "使用缓存数据: #{cache_record.projects_count} 个项目"
end
```

### 4. 数据格式错误

**问题描述**: 接口返回数据格式不正确

**解决方案**:
- 检查接口返回格式
- 验证安全密钥
- 查看详细错误日志

```ruby
# 查看原始响应
result = Irs::GovernmentProjectService.fetch_projects_by_date('2025-07-01')
puts result.raw_response

# 检查错误信息
if result.failure?
  puts "错误代码: #{result.error_code}"
  puts "错误信息: #{result.message}"
end
```

### 5. 缓存数据过期

**问题描述**: 缓存数据已过期但未自动更新

**解决方案**:
```bash
# 强制刷新缓存
rake government_project:cache:refresh

# 检查缓存状态
rake government_project:cache:stats
```

## 测试结果说明

### 实际测试数据（2025-07-09）

- 缓存中总项目数：134个
- 项目类型分布：
  - 政府投资项目可行性研究报告审批：32个
  - 政府投资项目初步设计审批：17个
  - 建筑工程施工许可（多合一）：12个
  - 政府投资项目建议书审批：6个
  - 信息变更：67个

### scope方法测试结果

- 立项项目：3个（本地数据库中有匹配记录）
- 施工许可证项目：0个（本地数据库中无匹配记录）
- 变更项目：0个（本地数据库中无匹配记录）

**结论**: 系统功能正常，返回0个结果是因为本地BidProject表中没有对应的项目记录。

### 数据流向说明

```mermaid
graph TD
    A[政府项目接口] --> B[审批事件数据]
    B --> C[缓存系统]
    C --> D[按类型过滤]
    D --> E[提取deal_code]
    E --> F[匹配本地BidProject]
    F --> G{本地是否有记录?}
    G -->|有| H[返回BidProject记录]
    G -->|无| I[返回空结果]
    I --> J[需要通过其他接口获取项目详情]
    
    style A fill:#e1f5fe
    style C fill:#f3e5f5
    style H fill:#e8f5e8
    style I fill:#fff3e0
    style J fill:#ffebee
```

## 调试命令

### 查看系统状态

```bash
# 查看详细统计
rake government_project:cache:stats

# 验证数据完整性
rake government_project:cache:validate

# 查看Rails日志
tail -f log/production.log | grep "政府项目"
```

### 检查数据匹配情况

```ruby
# 在Rails控制台中执行
codes = Irs::GovernmentProject.find_by(query_date: Date.yesterday).raw_data['governmentProjectList'].map{|p| p['deal_code']}
Serve::BidProject.where(code: codes).count
```

### 手动测试接口

```ruby
# 测试接口连通性
service = Irs::GovernmentProjectService.new
result = service.fetch_projects_by_date('2025-07-01', force_refresh: true)

puts "接口状态: #{result.success? ? '正常' : '异常'}"
puts "响应时间: #{result.response_time}ms" if result.respond_to?(:response_time)
puts "项目数量: #{result.count}"
```

## 性能问题

### 查询慢的问题

**原因**: 大量数据查询或索引缺失

**解决方案**:
```sql
-- 检查索引
EXPLAIN ANALYZE SELECT * FROM irs_government_projects WHERE query_date = '2025-07-01';

-- 添加缺失的索引
CREATE INDEX IF NOT EXISTS idx_government_projects_date ON irs_government_projects(query_date);
```

### 内存使用过高

**原因**: 缓存数据过多或未及时清理

**解决方案**:
```bash
# 清理过期缓存
rake government_project:cache:cleanup[7]

# 检查内存使用
ps aux | grep rails
```

## 错误代码说明

| 错误代码 | 说明 | 解决方案 |
|----------|------|----------|
| `INVALID_DATE_FORMAT` | 日期格式错误 | 使用 YYYY-MM-DD 格式 |
| `REQUEST_TIMEOUT` | 请求超时 | 检查网络或增加超时时间 |
| `NETWORK_ERROR` | 网络连接错误 | 检查网络连接 |
| `API_ERROR` | 接口返回错误 | 检查API状态和参数 |
| `CACHE_NOT_FOUND` | 缓存数据不存在 | 刷新缓存或检查日期 |
| `CACHED_ERROR` | 缓存数据获取失败 | 检查数据库连接 |

## 联系支持

如果以上方法都无法解决问题，请：

1. 收集错误日志和相关信息
2. 记录重现步骤
3. 联系开发团队

**日志收集命令**:
```bash
# 收集最近的错误日志
tail -n 100 log/production.log | grep -i error

# 收集政府项目相关日志
grep "政府项目" log/production.log | tail -n 50
```
