# 数据结构说明

## 政府项目接口数据结构

### 接口返回格式

政府项目接口返回的是**项目审批事件**，而不是完整的项目信息：

```json
{
  "code": "1",
  "message": "请求成功",
  "governmentProjectList": [
    {
      "deal_code": "2504-330783-04-01-558656",     // 项目编号（关键匹配字段）
      "item_name": "政府投资项目可行性研究报告审批",  // 审批事项名称
      "real_finish_time": "2025-07-01 16:49:45"   // 审批完成时间
    }
  ]
}
```

### 数据字段说明

| 字段名 | 类型 | 说明 | 用途 |
|--------|------|------|------|
| `deal_code` | String | 项目编号 | 与本地BidProject.code匹配的关键字段 |
| `item_name` | String | 审批事项名称 | 用于分类过滤（立项、许可证、变更等） |
| `real_finish_time` | String | 审批完成时间 | 记录审批事件的时间戳 |

## 重要局限性

### ✅ 接口包含的信息
- 项目编号（deal_code）
- 审批事项类型
- 审批完成时间

### ❌ 接口不包含的信息
- 项目详细名称和描述
- 项目金额和预算信息
- 建设单位和联系人信息
- 项目地址和建设地点
- 招标公告详细内容
- 项目进度和状态信息

## 审批事项类型

### 立项相关
- `政府投资项目可行性研究报告审批`
- `政府投资项目建议书审批`
- `政府投资项目初步设计审批`

### 施工许可相关
- `建筑工程施工许可（多合一）`
- `建设工程规划许可证`

### 变更相关
- `信息变更`
- `项目变更`

## 数据匹配逻辑

### 匹配流程

```mermaid
graph TD
    A[政府接口数据] --> B[提取deal_code]
    B --> C[在BidProject表中查找]
    C --> D{找到匹配项目?}
    D -->|是| E[返回BidProject记录]
    D -->|否| F[返回空结果]
    F --> G[需要创建项目记录]
    
    style E fill:#e8f5e8
    style F fill:#fff3e0
    style G fill:#ffebee
```

### 过滤规则

```ruby
# 立项项目过滤
def filter_setup_projects(projects)
  projects.select { |p|
    name = p["item_name"].to_s
    name.include?("可行性研究") || 
    name.include?("建议书审批") || 
    name.include?("立项")
  }
end

# 施工许可证项目过滤
def filter_permit_projects(projects)
  projects.select { |p|
    name = p["item_name"].to_s
    name.include?("建筑工程施工许可") || 
    (name.include?("建设") && name.include?("许可证"))
  }
end

# 变更项目过滤
def filter_change_projects(projects)
  projects.select { |p|
    name = p["item_name"].to_s
    name.include?("变更") || name.include?("信息变更")
  }
end
```

## 缓存数据结构

### Irs::GovernmentProject 模型

```ruby
class Irs::GovernmentProject < ApplicationRecord
  # 字段说明
  # query_date: Date        - 查询日期
  # raw_data: Hash          - 原始API响应数据
  # projects_count: Integer - 项目数量
  # success: Boolean        - 查询是否成功
  # error_message: String   - 错误信息（如果有）
  # cached_at: DateTime     - 缓存时间
end
```

### 缓存数据示例

```json
{
  "id": 3,
  "query_date": "2025-07-09",
  "raw_data": {
    "code": "1",
    "message": "请求成功",
    "governmentProjectList": [...]
  },
  "projects_count": 134,
  "success": true,
  "error_message": null,
  "cached_at": "2025-07-10T19:52:57.712894+08:00"
}
```

## 数据转换示例

### 从API响应到缓存记录

```ruby
# API响应
api_response = {
  "code" => "1",
  "message" => "请求成功",
  "governmentProjectList" => [
    {
      "deal_code" => "2504-330783-04-01-558656",
      "item_name" => "政府投资项目可行性研究报告审批",
      "real_finish_time" => "2025-07-01 16:49:45"
    }
  ]
}

# 创建缓存记录
cache_record = Irs::GovernmentProject.create!(
  query_date: Date.yesterday,
  raw_data: api_response,
  projects_count: api_response['governmentProjectList']&.size || 0,
  success: true,
  cached_at: Time.current
)
```

### 从缓存记录到BidProject查询

```ruby
# 从缓存获取数据
cache_record = Irs::GovernmentProject.find_by(query_date: Date.yesterday)
projects = cache_record.raw_data['governmentProjectList']

# 过滤立项项目
setup_projects = projects.select { |p| 
  p['item_name'].include?('可行性研究') || p['item_name'].include?('建议书')
}

# 提取项目编号
deal_codes = setup_projects.map { |p| p['deal_code'] }

# 查询本地项目
local_projects = Serve::BidProject.where(code: deal_codes)
```
