# 快速开始

## 基本使用

### 1. 获取政府项目数据

```ruby
# 获取昨天的政府项目数据（推荐方式，使用缓存）
result = Irs::GovernmentProjectService.fetch_projects_by_date

# 获取指定日期的数据
result = Irs::GovernmentProjectService.fetch_projects_by_date('2025-07-01')

# 强制刷新缓存（绕过缓存直接调用接口）
result = Irs::GovernmentProjectService.fetch_projects_by_date('2025-07-01', force_refresh: true)

# 检查结果
if result.success?
  puts "获取成功，共 #{result.count} 个审批事件"
  puts "数据来源：#{result.raw_response&.include?('CACHED') ? '缓存' : '接口'}"
  
  # 查看审批事件详情
  result.projects.each do |event|
    puts "项目编号: #{event['deal_code']}"
    puts "审批事项: #{event['item_name']}"
    puts "完成时间: #{event['real_finish_time']}"
  end
else
  puts "获取失败：#{result.message}"
end
```

### 2. 使用BidProject集成方法（推荐）

**重要理解**: 这些方法返回的是**本地项目记录**，不是政府接口的原始数据

```ruby
# 获取昨日立项的项目（返回本地BidProject记录）
setup_projects = Serve::BidProject.rule_government_setup_yesterday
puts "昨日立项项目：#{setup_projects.count} 个"

# 内部逻辑：
# 1. 从缓存获取昨日的政府审批事件
# 2. 过滤出立项相关事件（可行性研究、建议书审批）
# 3. 提取deal_code列表
# 4. 在本地BidProject表中查找匹配的项目
# 5. 返回匹配的BidProject记录

# 获取昨日获得施工许可证的项目（自动过滤已处理）
permit_projects = Serve::BidProject.rule_government_construction_permit_yesterday
puts "昨日施工许可证项目：#{permit_projects.count} 个"

# 获取昨日有变更的项目（自动过滤已处理）
change_projects = Serve::BidProject.rule_government_project_change_yesterday
puts "昨日变更项目：#{change_projects.count} 个"
```

### 3. 带日期参数的方法

```ruby
# 获取指定日期的立项项目
setup_projects = Serve::BidProject.rule_government_setup_by_date('2025-07-09')
puts "7月9日立项项目：#{setup_projects.count} 个"

# 获取指定日期的施工许可证项目
permit_projects = Serve::BidProject.rule_government_construction_permit_by_date('2025-07-09')

# 获取指定日期的变更项目
change_projects = Serve::BidProject.rule_government_project_change_by_date('2025-07-09')

# 支持Date对象
date_obj = Date.parse('2025-07-09')
setup_projects = Serve::BidProject.rule_government_setup_by_date(date_obj)
```

## 缓存管理

### 基本缓存操作

```bash
# 查看缓存统计信息
rake government_project:cache:stats

# 清理过期缓存（默认保留30天）
rake government_project:cache:cleanup

# 强制刷新昨天的缓存
rake government_project:cache:refresh

# 预热最近7天的缓存
rake government_project:cache:warmup
```

### 编程方式管理缓存

```ruby
# 检查缓存是否有效
is_valid = Irs::GovernmentProject.cache_valid?('2025-07-01')

# 获取缓存统计信息
stats = Irs::GovernmentProject.cache_statistics
puts "总记录数: #{stats[:total_records]}"
puts "成功记录数: #{stats[:successful_records]}"
puts "总项目数: #{stats[:total_projects]}"

# 清理过期缓存
deleted_count = Irs::GovernmentProject.cleanup_expired_cache(30)
```

## 常见使用场景

### 场景1: 每日项目状态检查

```ruby
# 检查昨日的项目审批情况
def daily_project_check
  puts "=== 昨日项目审批情况 ==="
  
  # 立项项目
  setup_projects = Serve::BidProject.rule_government_setup_yesterday
  puts "新立项项目: #{setup_projects.count} 个"
  
  # 施工许可证项目
  permit_projects = Serve::BidProject.rule_government_construction_permit_yesterday
  puts "获得施工许可证项目: #{permit_projects.count} 个"
  
  # 变更项目
  change_projects = Serve::BidProject.rule_government_project_change_yesterday
  puts "有变更的项目: #{change_projects.count} 个"
  
  # 详细信息
  setup_projects.each do |project|
    puts "立项: #{project.name} (#{project.code})"
  end
end
```

### 场景2: 项目状态更新

```ruby
# 更新项目审批状态
def update_project_status(date = Date.yesterday)
  # 获取立项事件并更新项目状态
  setup_events = Serve::BidProject.fetch_government_projects_by_date(date, "立项")
  
  setup_events.each do |event|
    project = Serve::BidProject.find_by(code: event['deal_code'])
    next unless project
    
    # 更新立项时间
    project.setup_at = Time.parse(event['real_finish_time'])
    project.save!
    
    puts "更新项目立项状态: #{project.name}"
  end
end
```

### 场景3: 数据完整性检查

```ruby
# 检查数据完整性
def check_data_completeness(date = Date.yesterday)
  # 获取所有审批事件
  all_events = Serve::BidProject.fetch_government_projects_by_date(date)
  deal_codes = all_events.map { |e| e['deal_code'] }
  
  # 检查本地项目匹配情况
  local_projects = Serve::BidProject.where(code: deal_codes)
  
  puts "审批事件总数: #{all_events.size}"
  puts "本地匹配项目: #{local_projects.count}"
  puts "匹配率: #{(local_projects.count.to_f / all_events.size * 100).round(2)}%"
  
  # 列出未匹配的项目
  missing_codes = deal_codes - local_projects.pluck(:code)
  if missing_codes.any?
    puts "未匹配的项目编号:"
    missing_codes.each { |code| puts "  - #{code}" }
  end
end
```

## 环境配置

### 必需的环境变量

```bash
# 政府项目接口地址
GOVERNMENT_PROJECT_API_URL=http://59.202.39.226:18181/rest/DataSharingRestService/getGovernmentProjectInfoByDate

# 政府项目接口安全密钥
GOVERNMENT_PROJECT_SECURITY_KEY=f823792357a911f088eefa163e999ea2

# 请求超时时间（可选，默认30秒）
GOVERNMENT_PROJECT_TIMEOUT=30
```

### 数据库迁移

```bash
# 运行迁移创建缓存表
rails db:migrate
```

## 下一步

- 查看 [API使用指南](./api_usage.md) 了解详细的API调用方法
- 查看 [缓存管理](./cache_management.md) 了解高级缓存操作
- 查看 [故障排除](./troubleshooting.md) 解决常见问题
