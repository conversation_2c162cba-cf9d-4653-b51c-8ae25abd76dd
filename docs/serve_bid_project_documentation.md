# Serve::BidProject 模型文档

## 概述
`Serve::BidProject` 是招标项目管理的核心模型，用于管理招标项目的完整生命周期，包括立项、登记、开标、中标等各个阶段。

## 模型特性
- **自动序列号**: 使用 `seqable` 生成唯一序列号
- **区域支持**: 集成 `Region::Ext::AreaSource` 和 `acts_as_area` 支持区域管理
- **政府项目集成**: 包含 `Serve::GovernmentProjectIntegration` 模块
- **生效时间管理**: 使用 `effectable` 管理项目有效期

## 数据库字段

### 基础信息
- `code`: 项目编号
- `name`: 项目标题
- `state`: 项目状态 (pending/setuped/registered/opening/finished/closed)
- `content`: 项目内容 (JSONB)
- `region_code`: 地区编码

### 时间字段
- `setup_at`: 立项时间
- `register_at`: 登记时间
- `open_at`: 开标时间
- `win_at`: 中标时间

### 人员信息
- `manager_name`: 负责人姓名
- `contactor_name`: 联系人姓名
- `legaler_name`: 项目法人姓名
- `phone`: 联系电话

### 单位信息
- `unit`: 招标单位
- `unit_code`: 招标单位编号

### 其他字段
- `amount`: 项目金额
- `send_state`: 发送消息状态
- `meta`: 元数据 (JSONB)
- `payload`: 扩展字段 (JSONB)
- `attachments`: 附件 (JSONB)

## 关联关系

### 归属关系
- `belongs_to :app`: 所属应用
- `belongs_to :org`: 所属组织 (可选)
- `belongs_to :contactor`: 联系人用户 (可选)
- `belongs_to :manager`: 负责人用户 (可选)
- `belongs_to :legaler`: 法人用户 (可选)

### 一对多关系
- `has_many :packs`: 消息包
- `has_many :bid_notices`: 招标公告
- `has_many :bid_results`: 招标结果
- `has_many :bid_items`: 招标项目
- `has_many :bid_tenders`: 招标候选

## Scope 方法

### 1. rule_before_open_bid
**用途**: 获取即将开标的项目
**参数**:
- `rule`: 规则对象 (可选)
- `period`: 天数，默认2天

**示例**:
```ruby
Serve::BidProject.rule_before_open_bid(nil, 3) # 获取3天内开标的项目
```

### 2. rule_current_bid
**用途**: 获取昨日登记的招标项目（排除已发送的）
**参数**:
- `rule`: 规则对象 (可选)
- `org`: 组织参数 (可选)

**功能**:
- 查询昨日登记的项目
- 过滤掉已经发送过消息的项目

### 3. rule_construction_permit_current_bid
**用途**: 获取需要施工许可证通知的项目
**参数**:
- `rule`: 规则对象 (可选)

**功能**:
- 查询5天前到昨天登记的项目
- 过滤掉已处理施工许可证的项目
- 排除已发送通知的项目

### 4. 政府项目集成 Scope (来自 GovernmentProjectIntegration)
- `rule_government_setup_by_date`: 根据日期获取立项项目
- `rule_government_construction_permit_by_date`: 根据日期获取施工许可证项目
- `rule_government_project_change_by_date`: 根据日期获取变更项目
- `rule_government_setup_yesterday/today`: 昨日/今日立项项目
- `rule_government_construction_permit_yesterday/today`: 昨日/今日施工许可证项目
- `rule_government_project_change_yesterday/today`: 昨日/今日变更项目

## 实例方法

### 用户ID获取方法
这些方法用于消息发送时获取目标用户：

#### rule_contactor_ids(*args)
**用途**: 获取项目联系人ID
**返回**: `[contactor_id]`

#### rule_legaler_ids(*args)
**用途**: 获取项目法人ID
**功能**:
- 通过 IRS 接口查询法人代表信息
- 在系统中匹配对应用户
- 支持重试机制（最多10次）
- 自动查找部门和领导部门

#### rule_manager_ids(*args)
**用途**: 获取项目负责人ID
**返回**: `[manager_id]`

### 施工许可证相关方法

#### rule_construction_permit_recipient_ids(user_catalog)
**用途**: 获取施工许可证通知的接收人ID
**参数**: `user_catalog` - 用户类型 ("legaler" 或 "contactor")
**功能**:
- 获取指定类型的用户ID
- 应该只在通过政府项目接口确认项目获得施工许可证后调用
**注意**: 此方法已简化，不再进行施工许可证状态检查，项目筛选应通过 `rule_government_construction_permit_by_date` scope 完成

### 模板消息相关方法

#### template_message_user_name(**args)
**用途**: 生成模板消息中的用户名称
**参数**: `user` - 用户对象
**返回**: 格式化的用户名称（如"张三项目联系人"）

#### project_name(*args)
**用途**: 获取项目名称
**返回**: 项目名称

#### register_date(*args)
**用途**: 获取格式化的登记日期
**返回**: "YYYY年MM月DD日" 格式

#### setup_date(*args)
**用途**: 获取格式化的立项日期
**返回**: "YYYY年MM月DD日" 格式

### 招投标数据关联方法

#### zbxm
**用途**: 获取关联的招标项目数据
**返回**: `Biz::Zbxm` 对象

#### xm / zbgg / zbjg / zbhx
**用途**: 获取项目、招标公告、招标结果、招标候选数据

### 数据同步方法

#### async!
**用途**: 同步招投标信息到当前项目
**功能**:
- 从招标公告获取项目内容
- 处理联系人信息
- 更新项目各项字段
- 关联区域和组织

#### self.async_all!(app: App.first, time: nil)
**用途**: 批量同步招投标项目信息
**参数**:
- `app`: 目标应用
- `time`: 同步起始时间，默认昨天

## 状态枚举
```ruby
enum :state, {
  pending: "pending",      # 待处理
  setuped: "setuped",      # 已立项
  registerd: "registered", # 已登记
  opening: "opening",      # 开标中
  finished: "finished",    # 已完成
  closed: "closed"         # 已关闭
}
```

## 使用示例

### 获取需要通知的项目
```ruby
# 获取昨日登记的新项目
new_projects = Serve::BidProject.rule_current_bid

# 获取需要施工许可证通知的项目
permit_projects = Serve::BidProject.rule_construction_permit_current_bid

# 获取即将开标的项目
opening_projects = Serve::BidProject.rule_before_open_bid(nil, 3)
```

### 获取通知对象
```ruby
project = Serve::BidProject.first

# 获取联系人ID
contactor_ids = project.rule_contactor_ids

# 获取法人ID（会调用外部接口）
legaler_ids = project.rule_legaler_ids

# 获取施工许可证通知对象
legaler_permit_ids = project.rule_construction_permit_recipient_ids("legaler")
contactor_permit_ids = project.rule_construction_permit_recipient_ids("contactor")
```

### 获取施工许可证项目（推荐方式）
```ruby
# 通过政府项目接口获取昨日获得施工许可证的项目
permit_projects = Serve::BidProject.rule_government_construction_permit_by_date(nil, Date.yesterday)

# 为这些项目获取通知对象
permit_projects.each do |project|
  legaler_ids = project.rule_construction_permit_recipient_ids("legaler")
  contactor_ids = project.rule_construction_permit_recipient_ids("contactor")
  # 发送通知...
end
```

## 注意事项

1. **外部接口依赖**: `rule_legaler_ids` 方法依赖 IRS 外部接口
2. **重试机制**: 外部接口调用都有重试机制，避免网络问题导致的失败
3. **政府项目接口优先**: 推荐使用政府项目接口的 scope 方法来筛选项目，而不是逐个检查项目状态
4. **日志记录**: 所有关键操作都有详细的日志记录
5. **性能考虑**: 大批量操作时建议使用 `find_each` 避免内存问题
6. **数据一致性**: 施工许可证项目筛选现在完全依赖政府项目接口，确保数据的及时性和准确性

## 相关模型
- `Serve::Pack`: 消息包
- `Serve::Rule`: 规则配置
- `Biz::Zbxm`: 招标项目数据
- `User`: 用户模型
- `Org`: 组织模型
- `Department`: 部门模型
