# 素材查询功能架构图

## 系统架构流程图

```mermaid
graph TD
    A[用户输入: "最近一个月的素材"] --> B[前端聊天界面]
    B --> C[Bot::User::ConversationsController]
    C --> D[Bot::Agent系统]
    D --> E[LLM Intent识别]
    E --> F{匹配Intent}
    F -->|匹配成功| G[素材查询Intent<br/>ID: 18]
    F -->|匹配失败| H[其他Intent或默认处理]
    
    G --> I[Bot::Tools::RansackTool]
    I --> J[LLM解析查询条件]
    J --> K[生成Ransack参数<br/>{created_at_gteq: 30.days.ago}]
    K --> L[执行数据库查询<br/>Serve::Activity.ransack]
    L --> M[查询结果统计<br/>total_count: 9]
    
    M --> N[创建Bot::ActivityListArtifact]
    N --> O[设置meta数据<br/>{q: conditions, total_count: 9}]
    O --> P[调用artifact.activate]
    P --> Q[生成前端数据格式<br/>包含payload.params.q]
    
    Q --> R[创建Bot::Message]
    R --> S[包含artifact的消息JSON]
    S --> T[返回给前端]
    
    T --> U[前端接收消息]
    U --> V[识别artifact类型<br/>Bot::ActivityListArtifact]
    V --> W[映射到ComActivityListTool.vue]
    W --> X[提取查询参数<br/>props.params.payload.params.q]
    X --> Y[调用ServeManageUserActivitiesApi]
    Y --> Z[渲染TaIndexView组件]
    Z --> AA[显示素材列表界面]
    
    AA --> BB[用户交互]
    BB --> CC[点击素材卡片]
    CC --> DD[显示详情弹窗]
    BB --> EE[点击查看原文]
    EE --> FF[打开原文链接]
```

## 数据流转图

```mermaid
sequenceDiagram
    participant U as 用户
    participant F as 前端界面
    participant C as Controller
    participant A as Bot::Agent
    participant L as LLM服务
    participant T as RansackTool
    participant D as 数据库
    participant AR as Artifact
    participant CO as 组件

    U->>F: 输入"最近一个月的素材"
    F->>C: POST /conversations/:id/messages
    C->>A: 创建消息并处理
    A->>L: 请求Intent识别
    L->>A: 返回"素材查询"Intent
    A->>T: 调用RansackTool.query_records
    T->>L: 解析查询条件
    L->>T: 返回{created_at_gteq: 30.days.ago}
    T->>D: 执行Serve::Activity查询
    D->>T: 返回查询结果(9条记录)
    T->>AR: 创建ActivityListArtifact
    AR->>AR: 设置meta和payload数据
    AR->>C: 返回artifact JSON
    C->>F: 返回包含artifact的消息
    F->>CO: 渲染ComActivityListTool组件
    CO->>F: 显示素材列表界面
    F->>U: 展示查询结果
```

## 组件关系图

```mermaid
classDiagram
    class BotAgent {
        +process_message()
        +identify_intent()
        +call_tool()
    }
    
    class BotIntent {
        +id: 18
        +name: "素材查询"
        +tool_cname: "Bot::Tools::RansackTool"
        +tool_conf: JSON
    }
    
    class RansackTool {
        +query_records(query)
        +parse_conditions()
        +execute_query()
        +create_artifact()
    }
    
    class ActivityListArtifact {
        +meta: JSON
        +activate()
        +query_conditions()
        +total_count()
    }
    
    class ComActivityListTool {
        +props: params
        +activityStore: VStore
        +onMounted()
        +onShow()
    }
    
    class ServeActivity {
        +name: String
        +state: String
        +created_at: DateTime
        +content: JSON
    }
    
    BotAgent --> BotIntent : 识别
    BotIntent --> RansackTool : 调用
    RansackTool --> ServeActivity : 查询
    RansackTool --> ActivityListArtifact : 创建
    ActivityListArtifact --> ComActivityListTool : 渲染
    ComActivityListTool --> ServeActivity : 显示
```

## 技术栈架构

```mermaid
graph LR
    subgraph "前端技术栈"
        A1[Vue 3]
        A2[TypeScript]
        A3[Ant Design Vue]
        A4[TaIndexView组件]
        A5[VStore状态管理]
    end
    
    subgraph "后端技术栈"
        B1[Ruby on Rails]
        B2[Bot::Agent系统]
        B3[Langchain集成]
        B4[Ransack查询]
        B5[Jbuilder JSON]
    end
    
    subgraph "数据层"
        C1[PostgreSQL]
        C2[Serve::Activity模型]
        C3[Bot::Artifact模型]
        C4[Bot::Intent配置]
    end
    
    subgraph "AI服务"
        D1[LLM服务]
        D2[Intent识别]
        D3[查询解析]
    end
    
    A1 --> B1
    B2 --> D1
    B4 --> C1
    D2 --> C4
```

## 关键配置说明

### Intent配置
```json
{
  "id": 18,
  "name": "素材查询",
  "description": "专门查询系统中的素材信息，支持时间范围、状态、关键词等条件查询",
  "tool_cname": "Bot::Tools::RansackTool",
  "tool_function": "query_records",
  "tool_conf": {
    "model_class": "Serve::Activity",
    "scope_chain": "all"
  }
}
```

### 前端组件映射
```typescript
export const BotMentionTypeMapping: VObject = {
  'Bot::ActivityListArtifact': defineAsyncComponent(
    () => import('@/components/bot/tools/ComActivityListTool.vue'),
  ),
};
```

### 查询条件映射
```ruby
# 时间范围映射
{
  "最近一周" => { created_at_gteq: 7.days.ago },
  "最近一个月" => { created_at_gteq: 30.days.ago },
  "最近三个月" => { created_at_gteq: 90.days.ago }
}

# 状态映射
{
  "已发布" => { state_eq: "published" },
  "待发布" => { state_eq: "pending" },
  "草稿" => { state_eq: "draft" },
  "已归档" => { state_eq: "archived" }
}
```

## 性能优化点

1. **查询优化**
   - 数据库索引: `created_at`, `state`
   - 分页查询: 默认10条/页
   - 查询缓存: Redis缓存热门查询

2. **前端优化**
   - 组件懒加载: `defineAsyncComponent`
   - 虚拟滚动: 大列表性能优化
   - 防抖查询: 避免频繁API调用

3. **AI服务优化**
   - Intent缓存: 相同查询复用结果
   - 并发控制: 限制同时处理的请求数
   - 超时处理: 避免长时间等待

这个架构确保了系统的可扩展性、可维护性和高性能。
