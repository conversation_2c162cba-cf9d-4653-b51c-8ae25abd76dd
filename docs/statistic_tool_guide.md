# StatisticTool 使用指南

## 概述

`StatisticTool` 是基于 rails_com engine 的 `ta_collection_statistic` 和 `ta_resource_statistic` 方法开发的智能统计分析工具。它通过自然语言描述自动生成统计条件，提供了两种主要的统计功能：

1. **集合统计** (`collection_statistic`): 通过自然语言描述对数据集合进行统计分析
2. **资源统计** (`resource_statistic`): 通过自然语言描述对单个资源及其关联数据进行统计分析

## 核心特性

### 🤖 智能语言理解
- 支持自然语言描述统计需求
- 自动解析统计条件和过滤条件
- 智能识别统计方法和目标字段

### 📊 强大的统计能力
- 基础聚合计算（count, sum, average, maximum, minimum）
- 条件过滤统计
- 时间范围统计
- 关联数据统计
- 多维度统计分析

### 🔧 灵活的配置
- 支持 Scope 链式调用
- 支持多层关联统计
- 自动错误处理和日志记录
- Artifact 自动记录和追踪

## 初始化参数

```ruby
tool = Bot::Tools::StatisticTool.new(
  model_class: Bot::Meeting,        # 必需：要统计的模型类
  scope_chain: ['finished'],       # 可选：scope 链
  llm: llm_instance                # 可选：LLM 实例
)
```

### 参数说明

- **model_class**: 要进行统计的 ActiveRecord 模型类（必须包含 `Bot::Searchable` 模块）
- **scope_chain**: 可选的 scope 链，支持数组或字符串格式
- **llm**: 可选的 LLM 实例，用于自然语言处理

## 使用方法

### 集合统计

```ruby
result = tool.collection_statistic(
  query: "统计技术部有多少人"  # 自然语言描述
)
```

### 资源统计

```ruby
result = tool.resource_statistic(
  resource_id: 1,                    # 资源ID
  query: "统计该用户的会议数量"        # 自然语言描述
)
```

## 支持的自然语言模式

### 基础统计
- "统计用户总数"
- "统计会议数量"
- "统计报告总数"

### 条件统计
- "统计技术部有多少人"
- "统计已完成的会议数量"
- "统计状态为处理中的任务"

### 时间范围统计
- "统计最近一个月的会议数量"
- "统计本月的报告数量"
- "统计最近一周的活动"

### 多维度统计
- "统计会议的总数、平均时长和最新会议时间"
- "统计用户数量和平均年龄"

### 分组统计
- "统计技术部男和女各有多少人"
- "统计各部门分别有多少人"
- "统计各种状态的会议分别有多少个"
- "统计各部门的人数分布"

### 带 Scope 的统计
- "统计活跃用户中各部门的人数"
- "统计最近登录的技术部员工数量"
- "统计活跃且最近登录的用户总数"
- "统计已完成的会议中各状态分布"

### 关联统计
- "统计该用户的会议数量"
- "统计该部门的员工数和项目数"

## 工作原理

### 1. 自然语言解析

工具使用 `Bot::Transformers::StatisticDsl` 将自然语言描述转换为统计配置：

```ruby
# 输入: "统计技术部有多少人"
# 输出:
{
  items: [
    {
      key: 'department_stats',
      filter: { departments_name_eq: '技术部' },
      caculator: {
        type: 'caculation',
        caculations: [
          { name: '技术部人数', method: 'count' }
        ]
      }
    }
  ]
}
```

### 2. 分组统计的三种实现方式

对于分组统计查询（如"统计技术部男和女各有多少人"），工具支持三种不同的实现方式，按优先级排序：

#### 方式一：使用多个 filter 条件（优先选择）
```ruby
{
  type: 'caculation',
  caculations: [
    { key: '男', name: '男', filter: { gender_eq: '男' }, method: 'count' },
    { key: '女', name: '女', filter: { gender_eq: '女' }, method: 'count' }
  ]
}
```

#### 方式二：使用 caculation 中的 group_attr
```ruby
{
  type: 'caculation',
  caculations: [
    { name: '性别分布', group_attr: 'gender', method: 'count' }
  ]
}
```

#### 方式三：使用 group_calc 计算器（最后选择）
```ruby
{
  type: 'group_calc',
  method: 'group_count',
  group_attrs: ['gender']
}
```

### 3. 统计执行

生成的配置会被转换为 `Com::Attr::Stat::Collection` 或 `Com::Attr::Stat::Resource` 对象，然后调用相应的 `ta_statistic` 方法执行统计。

### 4. 支持的统计方法

#### 基础统计方法
- **count**: 计数统计
- **sum**: 求和统计（数值字段）
- **average**: 平均值统计（数值字段）
- **maximum**: 最大值统计
- **minimum**: 最小值统计

#### 分组统计方法
- **group_count**: 分组计数统计
- **group_sum**: 分组求和统计
- **group_average**: 分组平均值统计

### 5. 分组统计方式选择策略

工具按以下优先级自动选择分组统计实现方式：

1. **多个 filter 条件（优先）**: 适用于需要对特定值进行精确统计的场景，灵活性最高，结果最明确
2. **group_attr（次选）**: 适用于需要在 caculation 中混合分组和非分组统计的场景
3. **group_calc（最后）**: 适用于简单的分组统计，仅在前两种方式不适用时选择

### 6. Scopes 功能支持

`StatisticTool` 现在支持在统计条件中使用 `scopes`，可以应用模型定义的 scope 进行过滤：

#### Scopes 的使用方式
```ruby
{
  type: 'caculation',
  caculations: [
    {
      name: '活跃用户统计',
      method: 'count',
      scopes: ['active']                    # 单个 scope
    },
    {
      name: '最近活跃用户统计',
      method: 'count',
      scopes: ['active', 'recent_login']    # 多个 scope
    },
    {
      name: '带参数的 scope',
      method: 'count',
      scopes: { 'created_after' => '2023-01-01' }  # 带参数的 scope
    }
  ]
}
```

#### 支持的 Scope 格式
- **字符串**: `"active"`
- **数组**: `["active", "recent_login"]`
- **Hash**: `{ "created_after" => "2023-01-01" }`
- **链式调用**: `"active.recent"`

## 使用示例

### 基础集合统计

```ruby
tool = Bot::Tools::StatisticTool.new(model_class: Bot::Meeting)

# 简单计数
result = tool.collection_statistic(query: "统计会议总数")

# 条件统计
result = tool.collection_statistic(query: "统计技术部有多少人")

# 时间范围统计
result = tool.collection_statistic(query: "统计最近一个月的会议数量")

# 多维度统计
result = tool.collection_statistic(query: "统计会议的总数、平均时长和最新会议时间")

# 分组统计
result = tool.collection_statistic(query: "统计技术部男和女各有多少人")
result = tool.collection_statistic(query: "统计各部门分别有多少人")
result = tool.collection_statistic(query: "统计各种状态的会议分别有多少个")

# 带 Scope 的统计
result = tool.collection_statistic(query: "统计活跃用户中各部门的人数")
result = tool.collection_statistic(query: "统计最近登录的技术部员工数量")
result = tool.collection_statistic(query: "统计活跃且最近登录的用户总数")
```

### 资源关联统计

```ruby
tool = Bot::Tools::StatisticTool.new(model_class: User)

# 简单关联统计
result = tool.resource_statistic(
  resource_id: 1,
  query: "统计该用户的会议数量"
)

# 条件关联统计
result = tool.resource_statistic(
  resource_id: 1,
  query: "统计该用户已完成的会议数量"
)

# 复杂关联统计
result = tool.resource_statistic(
  resource_id: 1,
  query: "统计该用户的活动数据，包括创建的报告数、参与的会议数"
)
```

### 带 Scope 的统计

```ruby
# 只统计已完成的会议
tool = Bot::Tools::StatisticTool.new(
  model_class: Bot::Meeting,
  scope_chain: ['finished']
)

result = tool.collection_statistic(query: "统计会议数量")
```

## 返回结果格式

### 成功响应

```ruby
{
  status: 'success',
  data: {
    'stat_key' => {
      '统计项名称' => 统计值,
      ...
    }
  },
  total_records: 100,        # 仅集合统计
  resource_found: true,      # 仅资源统计
  message: '完成统计分析: 查询描述'
}
```

### 错误响应

```ruby
{
  status: 'error',
  data: {},
  message: '统计分析失败: 错误信息'
}
```

### 资源未找到响应

```ruby
{
  status: 'not_found',
  data: {},
  resource_found: false,
  message: '无法找到ID为 xxx 的记录'
}
```

## 模型配置要求

使用 StatisticTool 的模型必须包含 `Bot::Searchable` 模块：

```ruby
class User < ApplicationRecord
  include Bot::Searchable

  # 定义可搜索的属性
  searchable_attributes :name, :email, :age, :created_at

  # 定义可搜索的关联
  searchable_associations :departments, :bot_meetings

  # 定义可搜索的 scope
  searchable_scopes :active, :recent
end
```

## 注意事项

1. **模型要求**: 模型必须包含 `Bot::Searchable` 模块并正确配置可搜索字段
2. **性能考虑**: 大数据量统计时建议添加适当的索引
3. **自然语言**: 描述要清晰明确，避免歧义
4. **错误处理**: 工具会自动捕获并记录错误，返回友好的错误信息

## 扩展功能

### 自定义 Scope Chain

```ruby
# 字符串格式
tool = Bot::Tools::StatisticTool.new(
  model_class: Bot::Meeting,
  scope_chain: 'Current.app.bot_meetings'
)

# 数组格式
tool = Bot::Tools::StatisticTool.new(
  model_class: Bot::Meeting,
  scope_chain: ['finished', 'recent']
)
```

### Artifact 支持

工具自动创建和更新 Artifact，记录：
- 原始查询描述
- 生成的统计条件
- 执行结果
- 元数据信息

这使得统计结果可以被追踪和重现。

## 最佳实践

1. **清晰的描述**: 使用明确的自然语言描述统计需求
2. **合理的范围**: 避免过于宽泛的统计查询
3. **适当的过滤**: 在描述中包含必要的过滤条件
4. **测试验证**: 在生产环境使用前充分测试统计结果的准确性
