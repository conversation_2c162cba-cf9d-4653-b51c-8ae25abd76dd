# UUID 消息去重功能使用指南

## 📋 概述

UUID 消息去重功能已成功部署到 hz_iest_api 项目中，支持传统 Rails 模板和 n8n 工作流的并行使用，确保用户不会收到重复的消息内容。

## 🎯 核心特性

- **双模式支持**：传统 Rails 模板 + n8n 工作流完美共存
- **智能去重**：基于 UUID 和模板签名的精确去重
- **向后兼容**：现有功能完全不受影响
- **降级保护**：n8n 工作流失败时自动降级到传统模板
- **简化 UUID**：传统模板使用 `template_ID` 格式，简洁明了

## 🛠️ 实施内容

### 1. 数据库变更

#### 新增字段

```sql
-- serve_messages表
ALTER TABLE serve_messages ADD COLUMN content_uuid VARCHAR(255);
ALTER TABLE serve_messages ADD COLUMN workflow_metadata JSONB;
```

#### 新增索引

```sql
CREATE INDEX idx_serve_messages_content_uuid ON serve_messages(content_uuid);
CREATE INDEX idx_serve_messages_user_content_uuid ON serve_messages(user_id, content_uuid);
```

### 2. 模型增强

#### MessageTemplate 模型新增方法

- `rule_uses_n8n_workflow?` - 判断规则是否使用 n8n 工作流（基于 Rule.model_payload）
- `n8n_workflow_id` - 获取 n8n 工作流 ID（从 Rule.model_payload 获取）
- `n8n_workflow_url` - 获取 n8n 工作流 URL
- `template_signature` - 生成模板签名用于去重（格式：`template_ID`）
- `content_changed?` - 检测内容是否变化

#### Pack 模型新增方法

- `select_unique_message_template_with_uuid` - UUID 方案的模板选择
- `user_received_content_uuid?` - 检查用户是否收到过指定 UUID 消息
- `user_received_template_signature?` - 检查用户是否收到过指定签名消息
- `generate_content_via_n8n` - 调用 n8n 工作流生成内容
- `create_fallback_option` - 创建降级选项

## 🔧 使用方法

### 1. 传统模板模式（默认）

模板默认使用传统 Rails 模板模式，无需额外配置：

```ruby
template = Serve::MessageTemplate.create!(
  name: "廉洁提醒模板",
  rule: rule,
  option: { /* 模板配置 */ }
  # 自动使用传统模板模式
)
```

### 2. n8n 工作流模式

在 Rule 级别配置 n8n 工作流：

```ruby
# 在Rule的model_payload中配置n8n工作流
rule.update!(
  model_payload: {
    n8n_workflow: "bidding_reminder_workflow"
  }
)

# 创建模板（无需额外配置，会自动使用Rule的n8n配置）
template = Serve::MessageTemplate.create!(
  name: "n8n廉洁提醒模板",
  rule: rule,
  option: { /* 模板配置 */ }
)
```

### 3. 环境变量配置

```bash
# .env文件
N8N_WEBHOOK_URL=https://your-n8n-instance.com
N8N_API_TOKEN=your_api_token_here
```

## 🧪 测试验证

### 运行测试脚本

```bash
# 在hz_iest_api项目中执行
rails runner scripts/test_uuid_deduplication.rb
```

### 测试结果示例

```
🧪 开始测试UUID消息去重功能
============================================================

📋 测试环境信息
   规则ID: 38 - 招标项目登记
   用户ID: 1703573 - 傅晓磊
   消息模板数量: 2

🔍 测试1: 数据库结构检查
   ✅ Rule.model_payload.n8n_workflow 配置存在: tkZK9oDhNZjabQ4v
   ✅ version 字段存在，当前版本: 1

🔧 测试2: 模型方法检查
   ✅ rule_uses_n8n_workflow? 方法存在，返回: true
   ✅ n8n_workflow_id 方法存在，返回: tkZK9oDhNZjabQ4v
   ✅ n8n_workflow_url 方法存在，返回: /webhook/tkZK9oDhNZjabQ4v
   ✅ template_signature 方法存在，返回: template_1
   ✅ content_changed? 方法存在，返回: false

📋 测试3: 传统模板模式
   ✅ 找到可用模板: 招投标图文信息
      模板签名: template_1
      使用n8n工作流: false

============================================================
📊 测试结果汇总
   ✅ 成功: 10 项
   ❌ 失败: 0 项
   📈 成功率: 100.0%

🎯 测试结论：
   ✅ 所有测试通过，UUID去重功能已就绪！
```

## 🔄 去重逻辑

### 传统模板去重

```ruby
template_signature = "#{template.id}_v#{template.version}"
unless user_received_template_signature?(user, template_signature)
  # 可以使用此模板
end
```

### n8n 工作流去重

```ruby
n8n_result = generate_content_via_n8n(template, user)
content_uuid = n8n_result.dig('deduplication_metadata', 'content_uuid')
unless user_received_content_uuid?(user, content_uuid)
  # 可以使用此内容
end
```

## 📊 监控指标

### 关键日志

```ruby
# 模板选择日志
Rails.logger.info "选择模板: #{content_uuid}, 类型: #{type}"

# n8n工作流调用日志
Rails.logger.info "n8n工作流生成成功: #{content_uuid}"
Rails.logger.error "n8n工作流调用失败: #{error_message}"

# 版本更新日志
Rails.logger.info "模板 #{id} 内容变化，版本号更新为 #{version}"
```

### 性能监控

- n8n 工作流响应时间
- 去重查询执行时间
- 降级触发频率
- 消息生成成功率

## 🔍 故障排除

### 常见问题

**Q: n8n 工作流调用失败**
A: 检查环境变量配置、网络连接、n8n 服务状态

**Q: 模板版本号没有自动递增**
A: 检查 content_changed?方法的逻辑，确保关键字段变化被正确检测

**Q: 去重逻辑不生效**
A: 检查数据库索引、payload 格式、查询条件

### 调试方法

```ruby
# 检查模板配置
template = Serve::MessageTemplate.find(id)
puts "规则使用n8n: #{template.rule_uses_n8n_workflow?}"
puts "工作流ID: #{template.n8n_workflow_id}"
puts "工作流URL: #{template.n8n_workflow_url}"
puts "版本号: #{template.version}"

# 检查用户消息历史
user = User.find(user_id)
messages = user.serve_messages.joins(:pack).where(serve_packs: { rule_id: rule_id })
messages.each do |msg|
  puts "UUID: #{msg.payload&.dig('content_uuid')}"
  puts "签名: #{msg.payload&.dig('template_signature')}"
end
```

## 🚀 部署建议

### 分阶段部署

1. **阶段 1**：部署代码和数据库迁移，保持所有模板为传统模式
2. **阶段 2**：选择部分模板切换到 n8n 工作流模式进行测试
3. **阶段 3**：根据测试结果逐步扩大 n8n 工作流的使用范围

### 回滚方案

如果需要回滚：

1. 清空 Rule 的`model_payload.n8n_workflow`配置
2. 系统会自动降级到传统模板模式
3. 数据库结构变更可以通过迁移回滚

## 📝 总结

UUID 消息去重方案成功实现了：

- ✅ 传统模板和 n8n 工作流的无缝并存
- ✅ 精确的消息去重逻辑
- ✅ 完整的向后兼容性
- ✅ 强大的错误处理和降级机制
- ✅ 灵活的版本管理

该方案为未来的 n8n 工作流集成提供了坚实的基础，同时保持了系统的稳定性和可扩展性。
