# 浙政钉岗位变动检测解决方案：事件回调机制

## 📚 目录

- [浙政钉岗位变动检测解决方案：事件回调机制](#浙政钉岗位变动检测解决方案事件回调机制)
  - [📚 目录](#-目录)
  - [🎯 问题简述](#-问题简述)
  - [💡 什么是事件回调机制？](#-什么是事件回调机制)
    - [简单理解](#简单理解)
    - [技术原理](#技术原理)
    - [覆盖范围](#覆盖范围)
  - [🚀 核心优势](#-核心优势)
    - [对比传统方式](#对比传统方式)
    - [重要澄清](#重要澄清)
  - [� 回调数据格式详解](#-回调数据格式详解)
    - [回调消息结构](#回调消息结构)
      - [HTTP请求格式](#http请求格式)
      - [参数说明](#参数说明)
    - [Content字段详细格式](#content字段详细格式)
      - [员工信息变更事件](#员工信息变更事件)
      - [组织关联员工事件](#组织关联员工事件)
      - [组织取关员工事件](#组织取关员工事件)
    - [实际接收示例](#实际接收示例)
    - [🔍 回调数据特点说明](#-回调数据特点说明)
      - [重要特点](#重要特点)
      - [处理流程](#处理流程)
      - [与现有同步逻辑的关系](#与现有同步逻辑的关系)
    - [🔑 租户ID处理说明](#-租户id处理说明)
      - [注册回调的逻辑](#注册回调的逻辑)
      - [回调消息中的租户ID](#回调消息中的租户id)
      - [租户ID映射关系](#租户id映射关系)
      - [完整的处理流程](#完整的处理流程)
  - [�📋 具体实施方案](#-具体实施方案)
    - [第一步：在rails\_dingtalk gem中添加回调功能](#第一步在rails_dingtalk-gem中添加回调功能)
      - [1.1 添加回调注册API](#11-添加回调注册api)
      - [1.2 创建回调管理模型](#12-创建回调管理模型)
      - [1.3 添加回调控制器](#13-添加回调控制器)
    - [第二步：在主项目中集成回调功能](#第二步在主项目中集成回调功能)
      - [2.1 添加路由配置](#21-添加路由配置)
      - [2.2 创建回调管理界面](#22-创建回调管理界面)
      - [2.3 修改现有同步服务](#23-修改现有同步服务)
      - [1.4 创建异步处理任务](#14-创建异步处理任务)
    - [第三步：部署和配置](#第三步部署和配置)
      - [3.1 环境配置](#31-环境配置)
      - [3.2 数据库迁移](#32-数据库迁移)
      - [3.3 初始化回调注册](#33-初始化回调注册)
  - [🚀 使用指南（技术小白版）](#-使用指南技术小白版)
    - [第一步：理解回调机制](#第一步理解回调机制)
    - [第二步：部署步骤](#第二步部署步骤)
    - [第三步：验证效果](#第三步验证效果)
    - [第四步：监控运行](#第四步监控运行)
  - [🎯 总结](#-总结)
  - [🧪 测试方案详解](#-测试方案详解)
    - [问题：如何测试回调功能？](#问题如何测试回调功能)
    - [解决方案：多种测试方法](#解决方案多种测试方法)
      - [方案一：模拟回调测试（推荐）](#方案一模拟回调测试推荐)
      - [方案二：HTTP请求测试](#方案二http请求测试)
      - [方案三：开发环境回调测试](#方案三开发环境回调测试)
      - [方案四：日志监控测试](#方案四日志监控测试)
      - [方案五：联系浙政钉技术支持](#方案五联系浙政钉技术支持)
    - [测试检查清单](#测试检查清单)
      - [基础功能测试](#基础功能测试)
      - [错误处理测试](#错误处理测试)
      - [性能测试](#性能测试)
    - [推荐测试流程](#推荐测试流程)
  - [📋 客户汇报材料](#-客户汇报材料)
    - [浙政钉岗位变动检测技术评估结果汇报](#浙政钉岗位变动检测技术评估结果汇报)
    - [简化版客户汇报（200字版本）](#简化版客户汇报200字版本)

---

> **📍 快速导航**：
> - 如果您想了解**回调数据格式**，请直接跳转到 [📨 回调数据格式详解](#-回调数据格式详解) ⭐ **新增内容**
> - 如果您想看**具体代码实现**，请直接跳转到 [📋 具体实施方案](#-具体实施方案)
> - 如果您是**技术小白**，请直接跳转到 [🚀 使用指南](#-使用指南技术小白版)
>
> **💡 提示**：目录中的链接可能显示异常，请使用上方的快速导航链接

---

## 🎯 问题简述

**现状**：当浙政钉管理员直接修改员工岗位名称时，我们的系统检测不到这种变动。

**原因**：现有同步机制依赖时间戳判断，但直接修改岗位不会更新创建时间。

**解决方案**：使用浙政钉官方的**事件回调机制**，实现实时变动通知。https://open-portal.on-premises.dingtalk.com/portal/#/helpdoc?apiType=serverapi&docKey=2674832

## 💡 什么是事件回调机制？

### 简单理解

想象一下微信群聊：
- 当有人发消息时，群里所有人都会立即收到通知
- 事件回调就是这样：当浙政钉有人员变动时，系统会立即"通知"我们

### 技术原理

1. **我们注册一个"接收地址"**（就像留下电话号码）
2. **浙政钉发生变动时**，会自动发送消息到这个地址
3. **我们接收消息**，然后更新本地数据

### 覆盖范围

✅ **员工信息修改**：姓名、岗位、职级等任何变动
✅ **部门调动**：员工从A部门调到B部门
✅ **新增员工**：新入职员工
✅ **员工离职**：员工离开组织
✅ **组织架构调整**：部门合并、拆分等

## 🚀 核心优势

### 对比传统方式

| 特性 | 传统轮询方式 | 事件回调方式 |
|------|-------------|-------------|
| **响应速度** | 最长1小时延迟 | **实时（秒级）** |
| **准确性** | 可能遗漏变动 | **100%准确** |
| **服务器负载** | 持续高负载 | **几乎为零** |
| **网络消耗** | 大量无效请求 | **按需通知** |
| **扩展性** | 人员越多越慢 | **人员数量无关** |

### 重要澄清

❌ **误解**：需要为每个员工单独注册监听
✅ **事实**：整个组织只需要1个回调地址

❌ **误解**：10万人需要10万个监听器
✅ **事实**：10万人和1千人用的是同一个回调地址

## � 回调数据格式详解

### 回调消息结构

当浙政钉发生人员变动时，会向您的回调地址发送POST请求，数据格式如下：

#### HTTP请求格式

```http
POST /api/dingtalk/callback HTTP/1.1
Content-Type: application/x-www-form-urlencoded; charset=UTF-8

# HTTP Headers
_TENANT_ID_: 123456789
_EVENT_NAME_: EMPLOYEE_ADD_UPDATE
_DISPATCH_ID_: 0e276c97-c3f9-44c2-88ab-335123991c45
_SOURCE_MESSAGE_ID_: source-msg-id-12345
_CALLBACK_MESSAGE_ID_: callback-msg-id-67890

# POST Body (form表单格式)
tenantId=123456789&eventName=EMPLOYEE_ADD_UPDATE&eventTag=MOZI_VDS_TENANT_CHANGE|EMPLOYEE_ADD_UPDATE&dispatchId=0e276c97-c3f9-44c2-88ab-335123991c45&sourceMessageId=source-msg-id-12345&callbackMessageId=callback-msg-id-67890&content={"tag":"员工信息变更","tenantId":"123456789","organizationCodes":"ORG001,ORG002","employeeCodes":"EMP001,EMP002,EMP003"}
```

#### 参数说明

| 参数名 | 位置 | 类型 | 说明 | 示例 |
|--------|------|------|------|------|
| `_TENANT_ID_` | Header | String | 租户ID | `123456789` |
| `_EVENT_NAME_` | Header | String | 事件名称 | `EMPLOYEE_ADD_UPDATE` |
| `_DISPATCH_ID_` | Header | String | 分发ID | `0e276c97-c3f9-44c2-88ab-335123991c45` |
| `tenantId` | Body | String | 租户ID | `123456789` |
| `eventName` | Body | String | 事件名称 | `EMPLOYEE_ADD_UPDATE` |
| `eventTag` | Body | String | 完整事件标识 | `MOZI_VDS_TENANT_CHANGE\|EMPLOYEE_ADD_UPDATE` |
| `dispatchId` | Body | String | 消息分发ID | `0e276c97-c3f9-44c2-88ab-335123991c45` |
| `content` | Body | String | 业务数据（JSON字符串） | 见下方详细说明 |

### Content字段详细格式

`content` 字段是一个JSON字符串，包含具体的变动信息：

#### 员工信息变更事件

```json
{
  "tag": "员工信息变更",
  "tenantId": "123456789",
  "organizationCodes": "ORG001,ORG002,ORG003",
  "employeeCodes": "EMP001,EMP002,EMP003"
}
```

#### 组织关联员工事件

```json
{
  "tag": "组织关联员工",
  "tenantId": "123456789",
  "organizationCodes": "ORG001",
  "employeeCodes": "EMP001,EMP002"
}
```

#### 组织取关员工事件

```json
{
  "tag": "组织取关员工",
  "tenantId": "123456789",
  "organizationCodes": "ORG001",
  "employeeCodes": "EMP001"
}
```

### 实际接收示例

在Rails控制器中，您会收到这样的参数：

```ruby
def callback
  # 从params中获取数据
  tenant_id = params[:tenantId]           # "123456789"
  event_name = params[:eventName]         # "EMPLOYEE_ADD_UPDATE"
  event_tag = params[:eventTag]           # "MOZI_VDS_TENANT_CHANGE|EMPLOYEE_ADD_UPDATE"
  dispatch_id = params[:dispatchId]       # "0e276c97-c3f9-44c2-88ab-335123991c45"
  content_str = params[:content]          # JSON字符串

  # 解析content
  content = JSON.parse(content_str)
  # {
  #   "tag" => "员工信息变更",
  #   "tenantId" => "123456789",
  #   "organizationCodes" => "ORG001,ORG002",
  #   "employeeCodes" => "EMP001,EMP002,EMP003"
  # }

  # 提取变动的员工和组织
  employee_codes = content['employeeCodes']&.split(',') || []
  organization_codes = content['organizationCodes']&.split(',') || []

  Rails.logger.info "收到变动通知: 员工#{employee_codes}, 组织#{organization_codes}"
end
```

### 🔍 回调数据特点说明

#### 重要特点

1. **批量通知**：一次回调可能包含多个员工的变动
   - `employeeCodes` 是逗号分隔的字符串：`"EMP001,EMP002,EMP003"`
   - 需要用 `split(',')` 分割成数组处理

2. **只包含变动标识**：回调消息只告诉你"谁变了"，不包含具体变动内容
   - 收到 `EMP001` 变动通知
   - 需要调用 `pageOrganizationEmployeePositions` API 获取最新数据
   - 然后与本地数据对比，确定具体变动内容

3. **实时性**：变动发生后几秒内就会收到通知

4. **可靠性**：浙政钉会重试16次，确保消息送达

#### 处理流程

```ruby
def handle_employee_update(content, tenant_id)
  data = JSON.parse(content)
  employee_codes = data['employeeCodes']&.split(',') || []

  Rails.logger.info "收到员工变动通知: #{employee_codes.join(', ')}"

  employee_codes.each do |employee_code|
    # 异步处理每个员工的同步
    # 在Job中会调用API获取该员工的最新信息
    Dingtalk::SyncEmployeeJob.perform_later(employee_code.strip, tenant_id)
  end
end
```

#### 与现有同步逻辑的关系

```mermaid
graph TD
    A[浙政钉管理员修改员工岗位] --> B[浙政钉系统检测到变动]
    B --> C[发送回调通知到我们的系统]
    C --> D[解析回调消息，获取员工代码]
    D --> E[调用现有API获取员工最新信息]
    E --> F[使用现有MemberService同步逻辑]
    F --> G[更新本地数据库]
    G --> H[记录岗位变动到DutyChange表]
```

**关键优势**：
- 🎯 **精准触发**：只同步真正发生变动的员工
- ⚡ **实时响应**：变动后几秒内开始处理
- 🔄 **复用现有逻辑**：不需要重写同步代码，只是触发方式改变

### 🔑 租户ID处理说明

**重要澄清**：注册回调时**不需要**指定租户ID，租户ID是在回调消息中**自动包含**的。

#### 注册回调的逻辑

```ruby
# 为每个Dingtalk::GovClient注册回调
clients = Dingtalk::GovClient.all

clients.each do |client|
  # 使用client的corp_id作为身份标识
  # 注册时不需要指定租户ID，浙政钉会根据client的身份自动关联

  client.register_event_callback(
    event_tag: 'MOZI_VDS_TENANT_CHANGE|EMPLOYEE_ADD_UPDATE',
    callback_url: 'https://your-domain.com/api/dingtalk/callback'
  )
end
```

#### 回调消息中的租户ID

当浙政钉发送回调时，会在消息中包含租户ID：

```ruby
def callback
  tenant_id = params[:tenantId]  # 这里的租户ID对应client.corp_id

  # 根据租户ID找到对应的client和组织
  client = Dingtalk::GovClient.find_by(corp_id: tenant_id)
  org = client&.app&.orgs&.first  # 或者根据具体业务逻辑查找

  # 处理回调...
end
```

#### 租户ID映射关系

```ruby
# 在您的例子中：
client = Dingtalk::GovClient.find(1)
client.corp_id  # => "196729"

# 当浙政钉发送回调时：
# params[:tenantId] => "196729"
#
# 通过这个映射关系找到对应的client：
client = Dingtalk::GovClient.find_by(corp_id: "196729")
```

#### 完整的处理流程

```ruby
def handle_employee_update(content, tenant_id)
  # 1. 根据租户ID找到对应的client
  client = Dingtalk::GovClient.find_by(corp_id: tenant_id)
  return unless client

  # 2. 找到对应的组织
  org = client.app.orgs.first  # 根据实际业务逻辑调整
  return unless org

  # 3. 解析变动的员工
  data = JSON.parse(content)
  employee_codes = data['employeeCodes']&.split(',') || []

  # 4. 异步处理每个员工
  employee_codes.each do |employee_code|
    Dingtalk::SyncEmployeeJob.perform_later(employee_code.strip, client, org)
  end
end
```

**总结**：
- ✅ 注册回调时：使用client身份，不需要指定租户ID
- ✅ 接收回调时：浙政钉自动包含租户ID
- ✅ 处理回调时：通过租户ID反向查找对应的client和组织

## �📋 具体实施方案

### 第一步：在rails_dingtalk gem中添加回调功能

#### 1.1 添加回调注册API

在 `app/models/dingtalk/api/gov.rb` 中添加：

```ruby
# 注册事件回调
def register_event_callback(event_tag:, callback_url:)
  post '/openplatform/message/register_event_callback',
    {
      eventTag: event_tag,
      callbackUrl: callback_url
    }
end

# 查询回调注册状态
def query_callback_define
  post '/openplatform/message/query_callback_define', {}
end

# 删除回调注册
def delete_event_callback(event_tag:)
  post '/openplatform/message/delete_event_callback',
    {
      eventTag: event_tag
    }
end
```

#### 1.2 创建回调管理模型

创建 `app/models/dingtalk/callback_registration.rb`：

```ruby
class Dingtalk::CallbackRegistration < ApplicationRecord
  belongs_to :client, class_name: 'Dingtalk::Client'

  attribute :event_tag, :string, comment: '事件标识'
  attribute :callback_url, :string, comment: '回调地址'
  attribute :status, :string, comment: '状态', default: 'pending'
  attribute :registered_at, :datetime, comment: '注册时间'

  enum status: {
    pending: 'pending',      # 待注册
    active: 'active',        # 已激活
    failed: 'failed',        # 注册失败
    inactive: 'inactive'     # 已失效
  }

  # 注册回调
  def register!
    result = client.register_event_callback(
      event_tag: event_tag,
      callback_url: callback_url
    )

    if result.data.dig('success') == 'true'
      update!(status: 'active', registered_at: Time.current)
    else
      update!(status: 'failed')
      raise "回调注册失败: #{result.data}"
    end
  end
end
```

#### 1.3 添加回调控制器

在 `app/controllers/dingtalk/callbacks_controller.rb` 中：

```ruby
class Dingtalk::CallbacksController < ApplicationController
  skip_before_action :verify_authenticity_token

  def create
    event_tag = params[:eventTag]
    content = params[:content]
    tenant_id = params[:tenantId]

    Rails.logger.info "收到浙政钉回调: #{event_tag}, 内容: #{content}"

    case event_tag
    when 'MOZI_VDS_TENANT_CHANGE|EMPLOYEE_ADD_UPDATE'
      handle_employee_update(content, tenant_id)
    when 'MOZI_VDS_TENANT_CHANGE|ORGANIZATION_ATTACH_EMPLOYEE'
      handle_employee_attach(content, tenant_id)
    when 'MOZI_VDS_TENANT_CHANGE|ORGANIZATION_DETACH_EMPLOYEE'
      handle_employee_detach(content, tenant_id)
    end

    render json: { errcode: "0", errmsg: "成功" }
  end

  private

  def handle_employee_update(content, tenant_id)
    data = JSON.parse(content)
    employee_codes = data['employeeCodes']&.split(',') || []

    employee_codes.each do |employee_code|
      # 异步处理员工同步
      Dingtalk::SyncEmployeeJob.perform_later(employee_code, tenant_id)
    end
  end

  def handle_employee_attach(content, tenant_id)
    # 处理员工加入组织事件
    data = JSON.parse(content)
    employee_codes = data['employeeCodes']&.split(',') || []
    organization_codes = data['organizationCodes']&.split(',') || []

    Rails.logger.info "员工 #{employee_codes} 加入组织 #{organization_codes}"

    employee_codes.each do |employee_code|
      Dingtalk::SyncEmployeeJob.perform_later(employee_code, tenant_id)
    end
  end

  def handle_employee_detach(content, tenant_id)
    # 处理员工离开组织事件
    data = JSON.parse(content)
    employee_codes = data['employeeCodes']&.split(',') || []
    organization_codes = data['organizationCodes']&.split(',') || []

    Rails.logger.info "员工 #{employee_codes} 离开组织 #{organization_codes}"

    employee_codes.each do |employee_code|
      Dingtalk::SyncEmployeeJob.perform_later(employee_code, tenant_id)
    end
  end
end
```

### 第二步：在主项目中集成回调功能

#### 2.1 添加路由配置

在主项目的 `config/routes.rb` 中添加：

```ruby
Rails.application.routes.draw do
  # 其他路由...

  namespace :api do
    namespace :dingtalk do
      post :callback, to: 'callbacks#handle_event'
    end
  end
end
```

#### 2.2 创建回调管理界面

创建 `app/controllers/admin/dingtalk_callbacks_controller.rb`：

```ruby
class Admin::DingtalkCallbacksController < ApplicationController
  before_action :authenticate_admin!

  def index
    @registrations = Dingtalk::CallbackRegistration.includes(:client)
  end

  def new
    @registration = Dingtalk::CallbackRegistration.new
    @clients = Dingtalk::Client.all
  end

  def create
    @registration = Dingtalk::CallbackRegistration.new(registration_params)

    if @registration.save
      begin
        @registration.register!
        redirect_to admin_dingtalk_callbacks_path, notice: '回调注册成功'
      rescue StandardError => e
        @registration.update(status: 'failed')
        redirect_to admin_dingtalk_callbacks_path, alert: "注册失败: #{e.message}"
      end
    else
      render :new
    end
  end

  def destroy
    @registration = Dingtalk::CallbackRegistration.find(params[:id])

    begin
      @registration.client.delete_event_callback(event_tag: @registration.event_tag)
      @registration.destroy
      redirect_to admin_dingtalk_callbacks_path, notice: '回调删除成功'
    rescue StandardError => e
      redirect_to admin_dingtalk_callbacks_path, alert: "删除失败: #{e.message}"
    end
  end

  private

  def registration_params
    params.require(:dingtalk_callback_registration).permit(:client_id, :event_tag, :callback_url)
  end
end
```

#### 2.3 修改现有同步服务

在 `app/services/dingtalk/member_service.rb` 中添加回调触发的同步方法：

```ruby
class Dingtalk::MemberService
  # 现有代码...

  # 新增：基于回调的单个员工同步
  def sync_employee_by_code(employee_code, org_code = nil)
    Rails.logger.info "#{LOG_PREFIX} 开始同步单个员工: #{employee_code}"

    # 如果没有指定组织代码，尝试从所有组织中查找
    target_org_code = org_code || find_employee_org_code(employee_code)
    return unless target_org_code

    # 获取员工信息
    response = fetch_members_page(target_org_code, 1)
    validate_api_response!(response, "fetching employee #{employee_code}")

    members_data = response.data.dig("content", "data") || []
    target_member = members_data.find { |m| m["employeeCode"] == employee_code }

    return unless target_member

    # 预处理职位信息
    process_member_position(target_member)

    # 获取账户信息
    account_info = fetch_account_info([target_member])

    # 找到对应的组织和部门
    dept_code = target_member.dig("govEmployeePosition", "organizationCode")
    org = Org.find_by(code: target_org_code)
    department = org&.departments&.find_by(code: dept_code)

    return unless org && department

    # 同步员工数据
    account_data = prepare_account_data(target_member, account_info)
    sync_member(target_member, account_data, org: org, department: department)

    Rails.logger.info "#{LOG_PREFIX} 成功同步员工: #{employee_code}"
  end

  private

  def find_employee_org_code(employee_code)
    # 在所有有权限的组织中查找该员工
    # 这里需要根据实际业务逻辑来实现
    # 可能需要遍历多个组织来查找员工
    nil
  end
end
```

#### 1.4 创建异步处理任务

创建 `app/jobs/dingtalk/sync_employee_job.rb`：

```ruby
class Dingtalk::SyncEmployeeJob < ApplicationJob
  queue_as :default

  def perform(employee_code, tenant_id)
    Rails.logger.info "开始同步员工: #{employee_code}, 租户: #{tenant_id}"

    # 根据租户ID找到对应的client
    client = Dingtalk::GovClient.find_by(corp_id: tenant_id)
    return unless client

    # 根据client找到对应的组织
    org = client.app.orgs.first  # 根据实际业务逻辑调整
    return unless org

    # 调用现有的同步服务
    member_service = Dingtalk::MemberService.new(client)

    # 根据员工代码获取员工信息并同步
    sync_single_employee(member_service, employee_code, org)

  rescue StandardError => e
    Rails.logger.error "同步员工失败: #{employee_code}, 错误: #{e.message}"
    Rails.logger.error e.backtrace.join("\n")
  end

  private

  def find_org_by_tenant_id(tenant_id)
    # 根据实际的租户ID映射逻辑来查找组织
    # 这里需要根据你们的具体业务逻辑来实现
    Org.joins(:dingtalk_clients)
       .where(dingtalk_clients: { corp_id: tenant_id })
       .first
  end

  def sync_single_employee(member_service, employee_code, org)
    # 获取员工详细信息
    response = member_service.client.pageOrganizationEmployeePositions(
      organizationCode: org.code,
      pageSize: 100,
      pageNo: 1,
      returnTotalSize: true
    )

    return unless response&.data&.dig("content", "success")

    members_data = response.data.dig("content", "data") || []
    target_member = members_data.find { |m| m["employeeCode"] == employee_code }

    if target_member
      # 预处理员工职位信息
      member_service.send(:process_member_position, target_member)

      # 获取账户信息
      account_info = member_service.send(:fetch_account_info, [target_member])

      # 找到员工所属部门
      dept_code = target_member.dig("govEmployeePosition", "organizationCode")
      department = org.departments.find_by(code: dept_code)

      if department
        # 同步员工数据
        account_data = member_service.send(:prepare_account_data, target_member, account_info)
        member_service.send(:sync_member, target_member, account_data, org: org, department: department)

        Rails.logger.info "成功同步员工: #{employee_code}"
      else
        Rails.logger.warn "未找到员工 #{employee_code} 对应的部门: #{dept_code}"
      end
    else
      Rails.logger.warn "未找到员工: #{employee_code}"
    end
  end
end
```

### 第三步：部署和配置

#### 3.1 环境配置

在 `.env` 文件中添加：

```bash
# 浙政钉回调配置
DINGTALK_CALLBACK_BASE_URL=https://your-domain.com
DINGTALK_CALLBACK_SECRET=your-secret-key
```

#### 3.2 数据库迁移

创建回调注册表的迁移文件：

```ruby
class CreateDingtalkCallbackRegistrations < ActiveRecord::Migration[7.1]
  def change
    create_table :dingtalk_callback_registrations do |t|
      t.references :client, null: false, foreign_key: { to_table: :dingtalk_clients }
      t.string :event_tag, null: false, comment: '事件标识'
      t.string :callback_url, null: false, comment: '回调地址'
      t.string :status, default: 'pending', comment: '状态'
      t.datetime :registered_at, comment: '注册时间'
      t.timestamps
    end

    add_index :dingtalk_callback_registrations, [:client_id, :event_tag], unique: true
  end
end
```

#### 3.3 初始化回调注册

创建初始化脚本 `lib/tasks/dingtalk_callback.rake`：

```ruby
namespace :dingtalk do
  namespace :callback do
    desc "注册浙政钉事件回调"
    task register: :environment do
      puts "开始注册浙政钉事件回调..."

      # 获取所有浙政钉政务客户端
      clients = Dingtalk::GovClient.all

      # 需要注册的事件类型
      event_tags = [
        'MOZI_VDS_TENANT_CHANGE|EMPLOYEE_ADD_UPDATE',
        'MOZI_VDS_TENANT_CHANGE|ORGANIZATION_ATTACH_EMPLOYEE',
        'MOZI_VDS_TENANT_CHANGE|ORGANIZATION_DETACH_EMPLOYEE'
      ]

      clients.each do |client|
        puts "为客户端 #{client.name} 注册回调..."

        event_tags.each do |event_tag|
          registration = Dingtalk::CallbackRegistration.find_or_initialize_by(
            client: client,
            event_tag: event_tag
          )

          next if registration.persisted? && registration.active?

          registration.callback_url = "#{ENV['DINGTALK_CALLBACK_BASE_URL']}/api/dingtalk/callback"

          begin
            registration.save!
            registration.register!
            puts "  ✓ 成功注册: #{event_tag}"
          rescue StandardError => e
            puts "  ✗ 注册失败: #{event_tag}, 错误: #{e.message}"
          end
        end
      end

      puts "回调注册完成！"
    end

    desc "查询回调注册状态"
    task status: :environment do
      registrations = Dingtalk::CallbackRegistration.includes(:client)

      puts "回调注册状态："
      puts "-" * 80

      registrations.each do |reg|
        status_icon = case reg.status
                     when 'active' then '✓'
                     when 'failed' then '✗'
                     when 'pending' then '⏳'
                     else '?'
                     end

        puts "#{status_icon} #{reg.client.name} | #{reg.event_tag} | #{reg.status}"
      end
    end

    desc "删除所有回调注册"
    task unregister: :environment do
      puts "开始删除所有回调注册..."

      Dingtalk::CallbackRegistration.active.each do |registration|
        begin
          registration.client.delete_event_callback(event_tag: registration.event_tag)
          registration.destroy
          puts "  ✓ 删除成功: #{registration.event_tag}"
        rescue StandardError => e
          puts "  ✗ 删除失败: #{registration.event_tag}, 错误: #{e.message}"
        end
      end

      puts "删除完成！"
    end
  end
end
```

## 🚀 使用指南（技术小白版）

### 第一步：理解回调机制

**简单比喻**：
- 传统方式：像每小时打电话问"有没有新消息？"
- 回调方式：像留下电话号码，有消息时对方主动打给你

### 第二步：部署步骤

1. **更新代码**：将上述代码添加到项目中
2. **运行迁移**：`rails db:migrate`
3. **注册回调**：`rails dingtalk:callback:register`
4. **检查状态**：`rails dingtalk:callback:status`

### 第三步：验证效果

1. **在浙政钉中修改某个员工的岗位**
2. **查看日志**：应该能看到回调消息
3. **检查数据库**：员工信息应该已更新

### 第四步：监控运行

- 定期检查回调状态：`rails dingtalk:callback:status`
- 监控日志文件中的回调消息
- 如有异常，可重新注册：`rails dingtalk:callback:register`

## 🎯 总结

通过实施事件回调机制，您将获得：

✅ **实时同步**：岗位变动立即反映到系统中
✅ **100%准确**：不会遗漏任何变动
✅ **性能优异**：系统负载几乎为零
✅ **易于维护**：一次配置，长期受益

**这是解决浙政钉岗位变动检测问题的最佳方案！**

## 🧪 测试方案详解

### 问题：如何测试回调功能？

您提出了一个非常实际的问题：
- ❌ 无法预测何时会有岗位变动
- ❌ 没有浙政钉的岗位修改权限
- ❌ 无法主动触发真实的变动事件

### 解决方案：多种测试方法

#### 方案一：模拟回调测试（推荐）

创建一个测试工具来模拟浙政钉的回调请求：

```ruby
# lib/tasks/dingtalk_test.rake
namespace :dingtalk do
  namespace :test do
    desc "模拟浙政钉回调测试"
    task simulate_callback: :environment do
      puts "开始模拟浙政钉回调测试..."

      # 模拟回调数据
      test_data = {
        tenantId: "196729",  # 使用您实际的corp_id
        eventName: "EMPLOYEE_ADD_UPDATE",
        eventTag: "MOZI_VDS_TENANT_CHANGE|EMPLOYEE_ADD_UPDATE",
        dispatchId: "test-#{Time.current.to_i}",
        sourceMessageId: "test-source-#{Time.current.to_i}",
        callbackMessageId: "test-callback-#{Time.current.to_i}",
        content: {
          tag: "员工信息变更",
          tenantId: "196729",
          organizationCodes: "ORG001",  # 使用实际的组织代码
          employeeCodes: "EMP001,EMP002"  # 使用实际的员工代码
        }.to_json
      }

      # 直接调用回调处理逻辑
      controller = Dingtalk::CallbacksController.new
      controller.params = ActionController::Parameters.new(test_data)

      begin
        result = controller.create
        puts "✓ 回调处理成功: #{result}"
      rescue StandardError => e
        puts "✗ 回调处理失败: #{e.message}"
        puts e.backtrace.join("\n")
      end
    end

    desc "测试单个员工同步"
    task :sync_employee, [:employee_code, :tenant_id] => :environment do |t, args|
      employee_code = args[:employee_code] || "请提供员工代码"
      tenant_id = args[:tenant_id] || "196729"

      puts "测试同步员工: #{employee_code}, 租户: #{tenant_id}"

      begin
        Dingtalk::SyncEmployeeJob.perform_now(employee_code, tenant_id)
        puts "✓ 员工同步成功"
      rescue StandardError => e
        puts "✗ 员工同步失败: #{e.message}"
      end
    end
  end
end
```

**使用方法**：
```bash
# 模拟回调测试
rails dingtalk:test:simulate_callback

# 测试单个员工同步
rails dingtalk:test:sync_employee[EMP001,196729]
```

#### 方案二：HTTP请求测试

使用curl或Postman直接测试回调接口：

```bash
# 使用curl测试回调接口
curl -X POST http://localhost:3000/api/dingtalk/callback \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -H "_TENANT_ID_: 196729" \
  -H "_EVENT_NAME_: EMPLOYEE_ADD_UPDATE" \
  -d "tenantId=196729&eventName=EMPLOYEE_ADD_UPDATE&eventTag=MOZI_VDS_TENANT_CHANGE|EMPLOYEE_ADD_UPDATE&content={\"tag\":\"员工信息变更\",\"tenantId\":\"196729\",\"organizationCodes\":\"ORG001\",\"employeeCodes\":\"EMP001\"}"
```

#### 方案三：开发环境回调测试

如果您有开发环境的浙政钉权限，可以：

1. **注册测试回调**：
   ```ruby
   # 在rails console中执行
   client = Dingtalk::GovClient.first
   client.register_event_callback(
     event_tag: 'MOZI_VDS_TENANT_CHANGE|EMPLOYEE_ADD_UPDATE',
     callback_url: 'https://your-dev-domain.com/api/dingtalk/callback'
   )
   ```

2. **使用ngrok暴露本地服务**：
   ```bash
   # 安装ngrok
   brew install ngrok

   # 暴露本地3000端口
   ngrok http 3000

   # 使用ngrok提供的URL注册回调
   # 例如：https://abc123.ngrok.io/api/dingtalk/callback
   ```

#### 方案四：日志监控测试

即使无法主动触发，也可以准备好监控：

```ruby
# app/controllers/dingtalk/callbacks_controller.rb
class Dingtalk::CallbacksController < ApplicationController
  def create
    # 详细记录所有回调信息
    Rails.logger.info "=" * 80
    Rails.logger.info "收到浙政钉回调 - #{Time.current}"
    Rails.logger.info "Headers: #{request.headers.to_h.select { |k,v| k.start_with?('_') }}"
    Rails.logger.info "Params: #{params.to_unsafe_h}"
    Rails.logger.info "=" * 80

    # 保存到数据库用于分析
    CallbackLog.create!(
      headers: request.headers.to_h.select { |k,v| k.start_with?('_') },
      params: params.to_unsafe_h,
      received_at: Time.current
    )

    # 原有处理逻辑...
  end
end
```

#### 方案五：联系浙政钉技术支持

**建议步骤**：
1. 联系浙政钉技术支持
2. 说明需要测试回调功能
3. 请求在测试环境中手动触发一次变动事件
4. 或者请求提供回调测试工具

### 测试检查清单

#### 基础功能测试
- [ ] 回调接口能正常接收POST请求
- [ ] 能正确解析回调参数
- [ ] 能根据租户ID找到对应的client
- [ ] 能正确解析员工代码列表
- [ ] 异步任务能正常执行

#### 错误处理测试
- [ ] 无效租户ID的处理
- [ ] 无效员工代码的处理
- [ ] 网络异常的重试机制
- [ ] 数据库异常的处理

#### 性能测试
- [ ] 大量员工变动的批量处理
- [ ] 并发回调的处理能力
- [ ] 异步任务队列的处理效率

### 推荐测试流程

1. **第一步**：使用方案一进行基础功能测试
2. **第二步**：使用方案二进行HTTP接口测试
3. **第三步**：部署到测试环境，使用方案四进行监控
4. **第四步**：联系浙政钉技术支持进行真实环境测试

这样即使没有岗位修改权限，也能充分验证回调功能的正确性！

---

## 📋 客户汇报材料

### 浙政钉岗位变动检测技术评估结果汇报

经过技术团队深入调研，我们已完成浙政钉岗位变动检测问题的技术评估。根据浙政钉官方API文档分析，确认可通过事件回调机制解决当前岗位修改无法检测的问题，该方案在业务逻辑上完全可行。

但需要说明的是，此方案需要对接浙政钉的事件回调接口后获取真实回调数据才能最终确认实际效果。鉴于该接口的特殊性（无法主动触发岗位变动进行测试，且回调时机具有不确定性），加之事件回调机制与现有的定时轮询同步逻辑存在根本性差异，需要对现有程序架构进行较大幅度的改造，包括新增回调接收模块、异步处理机制、数据留存体系以及完整的测试验证框架等。

综合考虑技术实现复杂度、测试验证难度以及与现有系统的集成工作量，预计整个开发周期需要**10-15个工作日**，其中包含方案实施、功能测试、系统集成和上线部署等各个环节。

---

### 简化版客户汇报（200字版本）

关于浙政钉岗位变动检测问题，我们的技术评估已经完成。

**问题确认**：现有系统确实无法检测到管理员直接修改岗位名称的变动。

**解决方案**：采用浙政钉官方的事件回调机制，当有人员变动时系统会主动通知我们，实现实时同步。

**技术可行性**：根据官方文档分析，该方案在技术上可行，但需要实际对接后获取真实数据进行最终验证。

**开发难点**：事件回调机制与现有的定时同步逻辑完全不同，需要对程序架构进行较大改造，包括新增回调处理模块、数据留存机制等。另外，由于无法主动触发岗位变动，测试验证存在一定难度。

**最新进展**：我们已与浙政钉技术团队进行沟通，他们建议我们先对接消息通知接口进行测试验证。但需要纪委相关配置岗位的人员配合进行实际的岗位变动操作，才能进一步确定方案的实际可行性。

**时间评估**：考虑到技术实现复杂度、测试难度以及需要协调纪委人员配合测试，预计需要**10-15个工作日**完成开发和部署。

这是目前解决该问题的最佳方案，一次实施后可彻底解决岗位变动检测的痛点。
