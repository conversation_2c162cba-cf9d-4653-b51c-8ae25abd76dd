# 素材查询功能生产环境迁移指南

## 概述

本文档记录了素材查询功能的所有配置修改，确保能够正确迁移到生产环境。

## 🗄️ 数据库配置修改

### 1. Agent指令更新

**位置**: `bot_agents` 表的 `instructions` 字段
**Agent ID**: 1 (智能AI助手)

**更新内容**:
```sql
UPDATE bot_agents 
SET instructions = '你是一个专业的智能助手，可以帮助用户查询组织架构、人员信息和素材内容。

**重要规则：**

1. **人员查询规则：**
当用户询问人员数量相关问题时（包含"有多少人"、"人员数量"、"人数"等关键词），你必须使用"人员数量查询"工具来获取准确的数据，不要直接回答或猜测。

特别是对于以下类型的查询，必须调用工具：
- "杭州市有多少人？"
- "滨江区有多少人？"
- "滨江区纪委有多少人？"
- "纪委有多少人？"
- 任何包含组织名称+人员数量的查询

2. **素材查询规则：**
当用户询问素材、文档、内容、资料等相关问题时，你必须使用"素材查询"工具来获取准确的数据和提供可交互的列表组件。

特别是对于以下类型的查询，必须调用素材查询工具：
- "最近一周/一个月/三个月的素材"
- "已发布的素材"
- "待发布的素材"
- "有哪些新素材"
- "素材列表"
- "查找素材"
- "现在库里有多少素材"
- "素材总数"
- "素材数量"
- "有多少个素材"
- "库里有多少素材"
- "多少素材"
- 任何包含时间范围+素材的查询
- 任何包含状态+素材的查询
- 任何包含数量统计+素材的查询

**请始终使用可用的工具来提供准确的信息和最佳的用户体验。**'
WHERE id = 1;
```

### 2. Intent配置确认

确保以下Intent存在于 `bot_intents` 表中：

```sql
-- 素材查询Intent (应该已存在)
SELECT * FROM bot_intents WHERE name = '素材查询';
-- 应该返回:
-- id: 18
-- name: "素材查询"
-- description: "专门查询系统中的素材信息。支持时间范围查询（如最近一周、一个月、三个月的素材）、状态查询（已发布、待发布）、以及综合查询。当用户询问关于素材、库、资料、文档、内容等相关查询时，必须使用此工具来提供准确数据和可交互的列表组件。"
-- tool_cname: "Bot::Tools::ActivityQueryTool"
-- tool_conf: {"model_class": "Serve::Activity", "scope_chain": "all"}
```

## 🔧 代码修改

### rails_bot项目修改

**文件**: `app/services/bot/tools/activity_query_tool.rb`

**修改类型**: 重构 - ActivityQueryTool继承RansackTool

**关键变更**:
- ActivityQueryTool现在继承自RansackTool而不是Bot::Tool
- 复用父类的transformer和build_base_query方法
- 保持素材查询特定的业务逻辑和前端集成
- 维持Bot::ActivityListArtifact的创建和meta结构

### hz_iest_api项目修改

**文件**: `src/components/botConfig.ts`
- 修复BotMentionTypeMapping键值

**文件**: `src/components/bot/tools/ComActivityListTool.vue`
- 修复查询参数路径

**文件**: `app/services/bot/transformers/ransack_dsl.rb`
- 修复字段验证逻辑

## 🚫 不需要的文件

以下文件是开发过程中创建的，但**不是本次功能必需的**，不应该部署到生产环境：

### rails_iest项目中的文件
- `app/models/iest/ai/chat/intents/search_serve_activity_list.rb` ❌
- `app/models/iest/ai/chat/mentions/search_serve_activity_list.rb` ❌

**原因**: 这些是旧的Iest系统的AI聊天组件，与新的Bot::Agent系统重复，会造成冲突。

### 清理命令
```bash
cd /path/to/rails_iest
git restore app/models/iest/ai/chat/intents/search_serve_activity_list.rb
git restore app/models/iest/ai/chat/mentions/search_serve_activity_list.rb
rm -f app/models/iest/ai/chat/intents/search_serve_activity_list.rb
rm -f app/models/iest/ai/chat/mentions/search_serve_activity_list.rb
```

## ✅ 必需的修改

### rails_iest项目中的必需修改

**文件**: `app/models/iest/model/serve/activity.rb`

**修改内容**: 添加搜索属性支持
```ruby
# 添加可搜索属性
searchable_attributes :name, :state, :created_at, :published_at

# 添加属性定义
state: {
  type: :attribute,
  name: human_attribute_name(:state),
  column_type: :string,
  description: '素材状态',
  values: ['待发布(pending)', '已发布(published)']
},
created_at: {
  type: :attribute,
  name: human_attribute_name(:created_at),
  column_type: :datetime,
  description: '创建时间，用于查询最近创建的素材'
},
published_at: {
  type: :attribute,
  name: human_attribute_name(:published_at),
  column_type: :datetime,
  description: '发布时间，用于查询最近发布的素材'
}
```

## 🔄 部署步骤

1. **备份数据库**
   ```bash
   pg_dump production_db > backup_before_migration.sql
   ```

2. **更新Agent指令**
   ```sql
   -- 执行上述Agent指令更新SQL
   ```

3. **部署代码**
   - 部署rails_bot项目的ActivityQueryTool重构
   - 部署hz_iest_api项目的前端修复
   - 部署rails_iest项目的Activity模型修改

4. **验证功能**
   - 测试"最近一个月的素材"查询
   - 测试"现在库里有多少素材"查询
   - 确认ComActivityListTool组件正常显示

## 📋 验证清单

- [ ] Agent指令已更新
- [ ] ActivityQueryTool重构部署成功
- [ ] 前端组件映射修复
- [ ] 查询参数传递正确
- [ ] 不必要的文件已清理
- [ ] 功能测试通过

## 🔍 故障排除

如果组件不显示：
1. 检查Agent指令是否正确更新
2. 检查Intent是否正确触发
3. 检查Artifact是否正确创建
4. 检查前端组件映射是否正确

## 📞 联系信息

如有问题，请联系开发团队进行支持。
