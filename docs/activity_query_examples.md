# 素材查询功能使用示例

## 概述

通过AI对话框可以使用自然语言查询Serve::Activity（素材）记录。系统会自动将自然语言转换为Ransack查询条件。

## 支持的查询类型

### 1. 时间范围查询

```
用户输入: "最近一周有哪些新素材"
转换为: { created_at_gteq: 7.days.ago }

用户输入: "最近一个月的素材"  
转换为: { created_at_gteq: 30.days.ago }

用户输入: "本月发布的素材"
转换为: { 
  published_at_gteq: Time.current.beginning_of_month,
  published_at_lteq: Time.current.end_of_month 
}
```

### 2. 状态查询

```
用户输入: "已发布的素材"
转换为: { state_eq: "published" }

用户输入: "待发布的素材"
转换为: { state_eq: "pending" }
```

### 3. 来源查询

```
用户输入: "微信公众号的素材"
转换为: { origin_name_cont: "微信" }

用户输入: "某个特定来源的素材"
转换为: { origin_name_eq: "具体来源名称" }
```

### 4. 标签查询

```
用户输入: "带有技术标签的素材"
转换为: { ai_tags_name_cont: "技术" }

用户输入: "教育类素材"
转换为: { ai_tags_name_cont: "教育" }
```

### 5. 组合查询

```
用户输入: "最近一周已发布的技术类素材"
转换为: {
  g: [
    { created_at_gteq: 7.days.ago },
    { state_eq: "published" },
    { ai_tags_name_cont: "技术" }
  ],
  m: "and"
}
```

## 在代码中使用

### 直接使用工具

```ruby
# 创建工具实例
tool = Bot::Tools::ActivityTool.new

# 执行查询
result = tool.query_records(query: "最近一周有哪些新素材")
puts result[:total_count] # 输出符合条件的记录数量
```

### 通过Intent使用

```ruby
# 运行seeds文件创建Intent
rails db:seed:replant FILTER=bot_intents_activity

# 然后在AI对话中直接使用自然语言查询
```

## 扩展字段

如果需要支持更多字段的查询，可以在 `searchable_field_descriptions` 中添加：

```ruby
def self.searchable_field_descriptions
  {
    # 现有字段...
    
    # 新增字段示例
    views_count: {
      type: :attribute,
      name: human_attribute_name(:views_count),
      column_type: :integer,
      description: '浏览次数'
    },
    address: {
      type: :attribute,
      name: human_attribute_name(:address),
      column_type: :text,
      description: '地址信息'
    }
  }
end
```

## 注意事项

1. 确保在 `searchable_attributes` 中包含了所有要查询的字段
2. 时间字段查询支持相对时间（如"最近一周"）和绝对时间范围
3. 关联字段查询会自动处理JOIN操作
4. 复杂查询会自动使用AND逻辑组合条件
