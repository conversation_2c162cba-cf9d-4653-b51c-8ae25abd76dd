puts '=== 测试回调注册 ==='
begin
  agent = Bot::Agent.first
  intent = agent.intents.find_by(name: '素材查询')
  
  activity_tool = Bot::Tools::ActivityQueryTool.new(intent: intent)
  
  puts "=== 检查回调注册 ==="
  callbacks = activity_tool.class.callbacks
  puts "Callbacks: #{callbacks}"
  
  if callbacks
    callbacks.each do |method_name, method_callbacks|
      puts "方法 #{method_name}:"
      method_callbacks.each do |type, callback_list|
        puts "  #{type}: #{callback_list.count}个回调"
      end
    end
  else
    puts "❌ 没有注册回调"
  end
  
  # 检查父类回调
  puts "\n=== 检查父类回调 ==="
  parent_callbacks = Bot::Tools::RansackTool.callbacks
  puts "Parent callbacks: #{parent_callbacks}"
  
  if parent_callbacks
    parent_callbacks.each do |method_name, method_callbacks|
      puts "父类方法 #{method_name}:"
      method_callbacks.each do |type, callback_list|
        puts "  #{type}: #{callback_list.count}个回调"
      end
    end
  end
  
  # 测试手动执行before回调
  puts "\n=== 手动测试before回调 ==="
  activity_tool.instance_variable_set(:@context, { query_activities: { params: { query: '测试' } } })
  
  # 检查是否有before回调
  if callbacks && callbacks[:query_activities] && callbacks[:query_activities][:before]
    puts "✅ 找到before回调，手动执行..."
    callbacks[:query_activities][:before].each do |callback|
      callback.call(activity_tool.instance_variable_get(:@context)[:query_activities])
    end
    
    # 检查是否创建了artifact
    context = activity_tool.instance_variable_get(:@context)
    if context[:query_activities][:artifact]
      puts "✅ 手动执行成功，创建了artifact!"
      puts "Artifact: #{context[:query_activities][:artifact].class}"
    else
      puts "❌ 手动执行失败，没有创建artifact"
    end
  else
    puts "❌ 没有找到before回调"
  end
  
rescue StandardError => e
  puts "❌ 失败: #{e.message}"
  puts e.backtrace.first(10)
end
