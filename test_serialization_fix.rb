#!/usr/bin/env ruby

# 测试序列化修复效果
require_relative 'config/environment'

puts "=== 测试Bot::MessageContent序列化修复 ==="

# 测试1：Hash输入（Symbol key）
puts "\n--- 测试1：Hash输入（Symbol key） ---"
hash_input = { messages: [{ content: "测试消息" }] }
begin
  dumped = Bot::MessageContent.dump(hash_input)
  loaded = Bot::MessageContent.load(dumped)
  puts "✅ Symbol key测试成功"
  puts "Dumped: #{dumped}"
  puts "Loaded: #{loaded.class}"
rescue => e
  puts "❌ Symbol key测试失败: #{e.message}"
end

# 测试2：Hash输入（String key）
puts "\n--- 测试2：Hash输入（String key） ---"
hash_input_string = { 'messages' => [{ 'content' => "测试消息" }] }
begin
  dumped = Bot::MessageContent.dump(hash_input_string)
  loaded = Bot::MessageContent.load(dumped)
  puts "✅ String key测试成功"
  puts "Dumped: #{dumped}"
  puts "Loaded: #{loaded.class}"
rescue => e
  puts "❌ String key测试失败: #{e.message}"
end

# 测试3：String输入
puts "\n--- 测试3：String输入 ---"
string_input = "直接的字符串消息"
begin
  dumped = Bot::MessageContent.dump(string_input)
  loaded = Bot::MessageContent.load(dumped)
  puts "✅ String输入测试成功"
  puts "Dumped: #{dumped}"
  puts "Loaded: #{loaded.class}"
rescue => e
  puts "❌ String输入测试失败: #{e.message}"
end

# 测试4：nil输入
puts "\n--- 测试4：nil输入 ---"
begin
  dumped = Bot::MessageContent.dump(nil)
  puts "✅ nil输入测试成功: #{dumped.inspect}"
rescue => e
  puts "❌ nil输入测试失败: #{e.message}"
end

# 测试5：MessageContent实例输入
puts "\n--- 测试5：MessageContent实例输入 ---"
instance_input = Bot::MessageContent.new(messages: [{ content: "实例消息" }])
begin
  dumped = Bot::MessageContent.dump(instance_input)
  loaded = Bot::MessageContent.load(dumped)
  puts "✅ MessageContent实例测试成功"
  puts "Dumped: #{dumped}"
  puts "Loaded: #{loaded.class}"
rescue => e
  puts "❌ MessageContent实例测试失败: #{e.message}"
end

puts "\n=== 测试Bot::Assistant消息处理修复 ==="

# 创建测试环境
app = App.first
user = app&.users&.first
agent = app&.bot_agents&.first

if app && user && agent
  puts "\n--- 测试Assistant消息处理 ---"
  
  # 测试不同类型的消息输入
  test_messages = [
    "简单字符串消息",
    { messages: [{ content: "Hash格式消息" }] },
    { 'messages' => [{ 'content' => "String key Hash消息" }] }
  ]
  
  test_messages.each_with_index do |message, index|
    puts "\n测试消息 #{index + 1}: #{message.class}"
    begin
      # 直接测试Assistant的消息处理逻辑
      assistant = agent.send(:initialize_assistant)
      
      # 模拟format_content方法
      def format_content(content)
        case content
        when String
          content
        when Hash
          content[:content] || content['content'] || content.to_s
        else
          content.to_s
        end
      end
      
      # 测试消息处理逻辑
      content = if message.is_a?(Hash) && (message[:messages] || message['messages'])
                  messages = message[:messages] || message['messages']
                  case messages
                  when Array
                    messages.map { |msg| format_content(msg) }.join("\n")
                  else
                    format_content(messages)
                  end
                else
                  format_content(message)
                end
      
      puts "✅ 消息处理成功: #{content}"
      
    rescue => e
      puts "❌ 消息处理失败: #{e.message}"
    end
  end
else
  puts "❌ 缺少测试环境数据"
end

puts "\n=== 序列化修复测试完成 ==="
