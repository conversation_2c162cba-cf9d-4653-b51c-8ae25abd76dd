---
formatter:
  enabled: true  # 启用格式化器
  rubocop:
    command: ["bundle", "exec", "rubocop", "--autocorrect"]  # rubocop命令，自动修正代码
    use_bundler: true  # 使用Bundler

diagnostics:
  enabled: true  # 启用诊断
  rubocop:
    command: ["bundle", "exec", "rubocop", "--format", "json"]  # rubocop命令，输出格式为JSON
    use_bundler: true  # 使用Bundler

completion:
  enabled: true  # 启用代码补全

hover:
  enabled: true  # 启用悬停提示

definition:
  enabled: true  # 启用定义跳转

references:
  enabled: true  # 启用引用查找

document_highlight:
  enabled: true  # 启用文档高亮

document_symbols:
  enabled: true  # 启用文档符号

code_actions:
  enabled: true  # 启用代码操作
  rubocop:
    command: ["bundle", "exec", "rubocop", "--autocorrect"]  # rubocop命令，自动修正代码
    use_bundler: true  # 使用Bundler

folding_ranges:
  enabled: true  # 启用折叠范围

selection_ranges:
  enabled: true  # 启用选择范围

semantic_highlighting:
  enabled: true  # 启用语义高亮

inlay_hints:
  enabled: true  # 启用内联提示
