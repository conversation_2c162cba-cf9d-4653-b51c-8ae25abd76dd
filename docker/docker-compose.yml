services:
  puma_hz_iest_api:
    build:
      context: .
      args:
        UID: ${DOCKER_USER_UID}
        GID: ${DOCKER_USER_GID}
        PROJECT: ${DOCKER_PROJECT_NAME}
        USER: ${DOCKER_USER}
        ROOT: ${DOCKER_ROOT}
    container_name: ${DOCKER_CONTAINER_NAME}
    user: "${DOCKER_USER_UID}:${DOCKER_USER_GID}"
    env_file:
      - .env
    ports:
      - ${DOCKER_PORT}:${DOCKER_PORT}
    volumes:
      - ${DOCKER_ID_RSA}:/${DOCKER_ROOT}/${DOCKER_USER}/.ssh/id_rsa
      - ${DOCKER_KNOWN_HOSTS}:/${DOCKER_ROOT}/${DOCKER_USER}/.ssh/known_hosts
      - ${DOCKER_PROJECT}:/${DOCKER_ROOT}/${DOCKER_USER}/${DOCKER_PROJECT_NAME}
      - ${DOCKER_BUNDLE}:/usr/local/bundle
      - ${DOCKER_HOSTS}:/etc/hosts
    extra_hosts:
      - "host.docker.internal:**********"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:${DOCKER_PORT}/apps.json"]
      interval: 20s
      timeout: 10s
      retries: 3
      start_period: 60s
    command:
      - sh
      - -c
      - |
          rm -rf /${DOCKER_ROOT}/${DOCKER_USER}/${DOCKER_PROJECT_NAME}/current/tmp/pids /${DOCKER_ROOT}/${DOCKER_USER}/${DOCKER_PROJECT_NAME}/current/tmp/cache /${DOCKER_ROOT}/${DOCKER_USER}/${DOCKER_PROJECT_NAME}/current/tmp/sockets
          cd /${DOCKER_ROOT}/${DOCKER_USER}/${DOCKER_PROJECT_NAME}/current
          bundle install
          bundle exec rake app:update:bin
          bundle exec rails db:migrate RAILS_ENV=${DOCKER_RAILS_ENV}
          echo Rails env is ${DOCKER_RAILS_ENV}
          bundle exec rails s -p ${DOCKER_PORT} -e ${DOCKER_RAILS_ENV}
  sidekiq_hz_iest_api:
    build:
      context: .
      args:
        UID: ${DOCKER_USER_UID}
        GID: ${DOCKER_USER_GID}
        PROJECT: ${DOCKER_PROJECT_NAME}
        USER: ${DOCKER_USER}
        ROOT: ${DOCKER_ROOT}
    user: "${DOCKER_USER_UID}:${DOCKER_USER_GID}"
    env_file:
      - .env
    volumes:
      - ${DOCKER_ID_RSA}:/${DOCKER_ROOT}/${DOCKER_USER}/.ssh/id_rsa
      - ${DOCKER_KNOWN_HOSTS}:/${DOCKER_ROOT}/${DOCKER_USER}/.ssh/known_hosts
      - ${DOCKER_PROJECT}:/${DOCKER_ROOT}/${DOCKER_USER}/${DOCKER_PROJECT_NAME}
      - ${DOCKER_BUNDLE}:/usr/local/bundle
      - ${DOCKER_HOSTS}:/etc/hosts
    extra_hosts:
      - "host.docker.internal:**********"
    depends_on:
      puma_hz_iest_api:
        condition: service_healthy
    command:
      - sh
      - -c
      - |
          cd /${DOCKER_ROOT}/${DOCKER_USER}/${DOCKER_PROJECT_NAME}/current
          bundle install
          bundle exec rake app:update:bin
          bundle exec rails db:migrate RAILS_ENV=${DOCKER_RAILS_ENV}
          echo Rails env is ${DOCKER_RAILS_ENV}
          bundle exec sidekiq -e ${DOCKER_RAILS_ENV}
