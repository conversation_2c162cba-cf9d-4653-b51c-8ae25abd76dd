version: '3.8'
services:
  sidekiq_hz_iest_api:
    build:
      context: .
      args:
        UID: ${DOCKER_USER_UID}
        GID: ${DOCKER_USER_GID}
        PROJECT: ${DOCKER_PROJECT_NAME}
        USER: ${DOCKER_USER}
        ROOT: ${DOCKER_ROOT}
    user: "${DOCKER_USER_UID}:${DOCKER_USER_GID}"
    env_file:
      - .env
    volumes:
      - ${DOCKER_ID_RSA}:/${DOCKER_ROOT}/${DOCKER_USER}/.ssh/id_rsa
      - ${DOCKER_KNOWN_HOSTS}:/${DOCKER_ROOT}/${DOCKER_USER}/.ssh/known_hosts
      - ${DOCKER_PROJECT}:/${DOCKER_ROOT}/${DOCKER_USER}/${DOCKER_PROJECT_NAME}
      - ${DOCKER_BUNDLE}:/usr/local/bundle
      - ${DOCKER_HOSTS}:/etc/hosts
    extra_hosts:
      - "host.docker.internal:**************"
    command:
      - sh
      - -c
      - |
          cd current
          bundle install
          bundle exec rake app:update:bin
          echo Rails env is ${DOCKER_RAILS_ENV}
          bundle exec sidekiq -e ${DOCKER_RAILS_ENV}
