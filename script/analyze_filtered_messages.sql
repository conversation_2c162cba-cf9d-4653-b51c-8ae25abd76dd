-- 分析被租户过滤掉的消息数据

-- 1. 获取当前租户ID
-- 假设当前租户ID存储在变量中，请替换为实际值
-- SET @current_tanent_id = (SELECT id FROM tanents WHERE name = '当前租户名称' LIMIT 1);

-- 2. 找出被过滤掉的消息
-- 这些消息要么没有关联pack，要么pack没有关联到当前租户
WITH filtered_messages AS (
  SELECT m.* 
  FROM serve_messages m
  LEFT JOIN serve_packs p ON m.pack_id = p.id
  LEFT JOIN tanents_serve_packs tp ON p.id = tp.serve_pack_id
  WHERE tp.tanent_id IS NULL OR tp.tanent_id != @current_tanent_id
)

-- 3. 按规则(rule)分组统计
SELECT 
  r.name AS rule_name,
  m.rule_id,
  COUNT(*) AS message_count
FROM filtered_messages m
LEFT JOIN serve_rules r ON m.rule_id = r.id
GROUP BY m.rule_id, r.name
ORDER BY message_count DESC;

-- 4. 按时间段分析
SELECT 
  CASE 
    WHEN m.created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN '最近7天'
    WHEN m.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN '最近30天'
    WHEN m.created_at >= DATE_SUB(NOW(), INTERVAL 90 DAY) THEN '最近90天'
    ELSE '更早'
  END AS time_period,
  COUNT(*) AS message_count
FROM filtered_messages m
GROUP BY time_period
ORDER BY 
  CASE time_period
    WHEN '最近7天' THEN 1
    WHEN '最近30天' THEN 2
    WHEN '最近90天' THEN 3
    ELSE 4
  END;

-- 5. 按pack关联情况分析
SELECT 
  CASE WHEN pack_id IS NULL THEN '无pack关联' ELSE '有pack关联' END AS pack_status,
  COUNT(*) AS message_count
FROM filtered_messages
GROUP BY pack_status;

-- 6. 按状态分析
SELECT 
  COALESCE(state, 'null') AS message_state,
  COUNT(*) AS message_count
FROM filtered_messages
GROUP BY state;

-- 7. 消息样本(前10条)
SELECT 
  m.id,
  m.created_at,
  m.rule_id,
  r.name AS rule_name,
  m.pack_id,
  m.state,
  m.user_id,
  u.name AS user_name,
  LEFT(m.content, 50) AS content_preview
FROM filtered_messages m
LEFT JOIN serve_rules r ON m.rule_id = r.id
LEFT JOIN users u ON m.user_id = u.id
ORDER BY m.created_at DESC
LIMIT 10;

-- 8. 如果有pack关联，分析pack的租户情况
WITH pack_ids AS (
  SELECT DISTINCT pack_id 
  FROM filtered_messages 
  WHERE pack_id IS NOT NULL
)
SELECT 
  CASE 
    WHEN tp.tanent_id IS NULL THEN '无租户关联'
    WHEN tp.tanent_id != @current_tanent_id THEN '关联其他租户'
    ELSE '关联当前租户'
  END AS tanent_status,
  COUNT(DISTINCT p.id) AS pack_count
FROM pack_ids pi
JOIN serve_packs p ON pi.pack_id = p.id
LEFT JOIN tanents_serve_packs tp ON p.id = tp.serve_pack_id
GROUP BY tanent_status;
