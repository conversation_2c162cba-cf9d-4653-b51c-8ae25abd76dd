puts '=== 调试send方法执行 ==='
begin
  agent = Bot::Agent.first
  intent = agent.intents.find_by(name: '素材查询')

  activity_tool = Bot::Tools::ActivityQueryTool.new(intent: intent)

  # 设置Bot::Current.conversation
  conversation = agent.conversations.first || agent.conversations.create!(user: User.first)
  Bot::Current.conversation = conversation

  puts "Intent: #{intent.name}"
  puts "Conversation: #{conversation.id}"

  # 检查回调注册
  puts "\n=== 检查回调注册 ==="
  callbacks = activity_tool.class.callbacks
  puts "Callbacks: #{callbacks}"

  if callbacks && callbacks[:query_activities]
    puts 'query_activities回调:'
    callbacks[:query_activities].each do |type, callback_list|
      puts "  #{type}: #{callback_list.count}个"
    end
  else
    puts '❌ 没有query_activities回调'
  end

  # 添加调试到send方法
  puts "\n=== 调试send方法执行 ==="

  # 重写execute_callbacks方法来添加调试
  original_execute_callbacks = activity_tool.method(:execute_callbacks)
  activity_tool.define_singleton_method(:execute_callbacks) do |type, method_name, *data|
    puts "🔍 execute_callbacks被调用: type=#{type}, method=#{method_name}, data=#{data.inspect[0..100]}"

    method_name = method_name.to_s.to_sym
    callbacks = self.class.callbacks.dig(method_name, type)
    puts "🔍 找到回调: #{callbacks&.count || 0}个"

    return unless callbacks

    callbacks.each_with_index do |callback, index|
      puts "🔍 执行回调 #{index + 1}: #{callback.class}"
      begin
        case callback
        when Symbol, String
          send(method_name.to_s.to_sym, *data)
        when Proc
          puts "🔍 执行Proc回调，参数: #{data.inspect[0..100]}"
          puts "🔍 当前context: #{@context.inspect[0..200] if @context}"
          puts "🔍 当前method: #{@current_method}"
          puts "🔍 Bot::Current.conversation: #{Bot::Current.conversation&.id}"
          puts "🔍 Intent: #{@intent&.name}"

          instance_exec(*data, &callback)

          puts "🔍 执行后context: #{@context.inspect[0..200] if @context}"
        end
        puts "✅ 回调 #{index + 1} 执行成功"
      rescue StandardError => e
        puts "❌ 回调 #{index + 1} 执行失败: #{e.message}"
        puts "   #{e.backtrace.first(3).join('\n   ')}"
      end
    end
  end

  # 执行send方法
  puts "\n=== 执行send方法 ==="
  result = activity_tool.send(:query_activities, query: '过去一年里有多少素材？')

  puts "\n=== 结果检查 ==="
  puts "结果类型: #{result.class}"
  puts "结果keys: #{result.keys}"
  puts "artifact: #{result[:artifact] ? '✅ 存在' : '❌ 不存在'}"
rescue StandardError => e
  puts "❌ 失败: #{e.message}"
  puts e.backtrace.first(10)
ensure
  Bot::Current.conversation = nil
end
