# RailsBot
Short description and motivation.

## Usage
How to use my plugin.

## Installation
Add this line to your application's Gemfile:

```ruby
gem "rails_bot"
```

And then execute:
```bash
$ bundle
```

Or install it yourself as:
```bash
$ gem install rails_bot
```

## Contributing
Contribution directions go here.

## License
The gem is available as open source under the terms of the [MIT License](https://opensource.org/licenses/MIT).

## Ransack 查询配置

要在模型中启用 Ransack 查询功能，需要：

1. 引入 `Bot::Searchable` 模块：
```ruby
class YourModel < ApplicationRecord
  include Bot::Searchable

  # ... 其他代码
end
```

2. 配置可搜索的字段：
```ruby
# 定义可搜索的属性
searchable_attributes(
  :title,              # 字段名
  :description,        # 字段名
  :status             # 字段名
)

# 定义可搜索的关联
searchable_associations(
  :user,              # 关联名
  :approver           # 关联名
)

# 定义可搜索的scope
searchable_scopes(
  :unread,           # scope名
  :approved          # scope名
)
```

3. 添加字段描述（用于生成查询提示）：
```ruby
def self.searchable_field_descriptions
  {
    title: "字段描述，例如：'标题'",
    status: "字段描述，例如：'状态，包括：已读/未读'",
    user: {
      name: "关联字段描述，例如：'用户名'"
    }
  }
end
```

4. 在 Intent 中使用 RansackTool：
```ruby
Bot::Intent.create!(
  name: "查询记录",
  description: "使用自然语言查询记录",
  tool_cname: "Bot::Tools::RecordQueryTool",
  tool_conf: {
    scope_chain: "YourModel.some_scope"  # 可选的 scope 链
  }
)
```

### 支持的查询条件

RansackTool 支持以下查询条件：

- `eq`: 等于
- `not_eq`: 不等于
- `matches`: 匹配
- `does_not_match`: 不匹配
- `lt`: 小于
- `lteq`: 小于等于
- `gt`: 大于
- `gteq`: 大于等于
- `in`: 在列表中
- `not_in`: 不在列表中
- `cont`: 包含
- `not_cont`: 不包含
- `start`: 以...开始
- `not_start`: 不以...开始
- `end`: 以...结束
- `not_end`: 不以...结束
- `true`: 为真
- `false`: 为假
- `present`: 有值
- `blank`: 为空
- `null`: 为null
- `not_null`: 不为null

### 示例查询

```ruby
# 初始化工具
tool = Bot::Tools::RansackTool.new(
  model_class: YourModel,
  scope_chain: "some_scope.another_scope"
)

# 执行查询
result = tool.run(
  query: "标题包含'测试'且状态为未读的记录",
  page: 1,
  per_page: 20
)
