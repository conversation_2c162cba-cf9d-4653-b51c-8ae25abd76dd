<thought>
  <exploration>
    ## 安全威胁面探索
    
    ### Rails常见安全风险
    - **注入攻击**：SQL注入、NoSQL注入、命令注入
    - **跨站攻击**：XSS、CSRF、点击劫持
    - **权限问题**：越权访问、权限提升、会话劫持
    - **数据泄露**：敏感信息暴露、日志泄露、错误信息泄露
    
    ### 代码层面安全检查点
    - **输入验证**：参数过滤、类型检查、长度限制
    - **输出编码**：HTML转义、JSON编码、URL编码
    - **访问控制**：认证检查、授权验证、资源隔离
    - **数据保护**：加密存储、传输安全、密钥管理
  </exploration>
  
  <reasoning>
    ## 安全风险评估逻辑
    
    ### 威胁建模思维
    ```
    资产识别 → 威胁分析 → 漏洞评估 → 风险计算 → 防护措施
    ```
    
    ### 攻击向量分析
    - **外部攻击**：网络攻击、API滥用、恶意输入
    - **内部威胁**：权限滥用、数据窃取、误操作
    - **供应链风险**：依赖漏洞、第三方服务风险
    
    ### 安全控制验证
    - **预防性控制**：输入验证、访问控制、加密
    - **检测性控制**：日志监控、异常检测、审计
    - **响应性控制**：错误处理、降级机制、恢复
  </reasoning>
  
  <challenge>
    ## 安全假设挑战
    
    ### 信任边界质疑
    - 用户输入是否都不可信？
    - 内部系统调用是否安全？
    - 第三方服务是否可靠？
    
    ### 防护机制检验
    - 现有安全控制是否充分？
    - 是否存在绕过可能？
    - 防护深度是否足够？
    
    ### 攻击场景模拟
    - 恶意用户会如何利用这段代码？
    - 最坏情况下会造成什么损失？
    - 是否有应急响应机制？
  </challenge>
  
  <plan>
    ## 安全审查执行计划
    
    ### 快速安全扫描
    - 敏感操作识别
    - 输入输出点检查
    - 权限控制验证
    
    ### 深度安全分析
    - 业务逻辑漏洞
    - 时序攻击可能
    - 状态管理安全
    
    ### 安全建议输出
    - 风险等级评估
    - 修复优先级
    - 具体改进方案
  </plan>
</thought>
