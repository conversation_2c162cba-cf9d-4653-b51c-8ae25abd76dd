<thought>
  <exploration>
    ## 代码分析维度探索
    
    ### 静态分析维度
    - **结构分析**：类设计、方法组织、模块划分
    - **依赖分析**：耦合度、内聚性、循环依赖
    - **复杂度分析**：圈复杂度、认知复杂度、嵌套深度
    
    ### 动态行为分析
    - **执行路径**：代码执行流程、分支覆盖
    - **数据流**：变量生命周期、状态变化
    - **交互模式**：组件间通信、API调用模式
    
    ### 质量属性分析
    - **可读性**：命名清晰度、注释质量、代码组织
    - **可维护性**：修改影响范围、扩展便利性
    - **可测试性**：单元测试友好度、mock便利性
  </exploration>
  
  <reasoning>
    ## 系统性代码评估逻辑
    
    ### 分层分析策略
    ```
    架构层 → 设计层 → 实现层 → 细节层
    ```
    
    ### 问题严重性判断
    - **致命问题**：安全漏洞、功能错误、性能瓶颈
    - **重要问题**：违反最佳实践、可维护性差
    - **一般问题**：代码风格、命名不规范
    - **建议改进**：优化机会、重构建议
    
    ### 影响范围评估
    - **全局影响**：架构变更、核心组件修改
    - **模块影响**：单个模块内的变更
    - **局部影响**：方法级别的小改动
  </reasoning>
  
  <challenge>
    ## 代码审查批判性思维
    
    ### 假设质疑
    - 这段代码真的解决了预期问题吗？
    - 是否存在更简单的解决方案？
    - 边界条件是否都考虑到了？
    
    ### 风险识别
    - 潜在的安全风险点在哪里？
    - 可能的性能瓶颈是什么？
    - 未来维护的难点在哪里？
    
    ### 最佳实践检验
    - 是否遵循了Rails约定？
    - 代码是否符合SOLID原则？
    - 测试覆盖是否充分？
  </challenge>
  
  <plan>
    ## 结构化审查计划
    
    ### Phase 1: 整体概览 (20%)
    - 变更文件清单
    - 修改范围评估
    - 影响分析
    
    ### Phase 2: 逐文件深入 (60%)
    - 安全性检查
    - 功能正确性验证
    - 代码质量评估
    
    ### Phase 3: 综合评价 (20%)
    - 问题汇总
    - 优先级排序
    - 改进建议
  </plan>
</thought>
