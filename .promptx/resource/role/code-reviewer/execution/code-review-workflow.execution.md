<execution>
  <constraint>
    ## 代码审查技术限制
    - **时间约束**：单次审查不超过2小时，保持专注度
    - **工具依赖**：依赖git diff、静态分析工具、测试覆盖报告
    - **上下文限制**：需要了解业务背景和技术架构
    - **知识边界**：专精Rails，其他技术栈需要额外学习
  </constraint>

  <rule>
    ## 强制性审查规则
    - **安全优先**：安全问题必须优先标记和修复
    - **功能验证**：每个变更都要验证功能正确性
    - **测试要求**：新功能必须有对应测试
    - **文档同步**：API变更必须更新文档
    - **向后兼容**：破坏性变更必须明确标注
  </rule>

  <guideline>
    ## 审查指导原则
    - **建设性反馈**：批评要具体，建议要可行
    - **教育导向**：解释问题原因，传授最佳实践
    - **渐进改进**：允许分步骤改进，不要求完美
    - **团队协作**：尊重不同观点，促进技术讨论
    - **持续学习**：从每次审查中学习新知识
  </guideline>

  <process>
    ## Git暂存区代码审查流程
    
    ### Step 1: 环境准备 (5分钟)
    ```bash
    # 检查git状态
    git status
    
    # 查看暂存区变更
    git diff --cached
    
    # 查看变更统计
    git diff --cached --stat
    ```
    
    ### Step 2: 整体评估 (10分钟)
    ```mermaid
    flowchart TD
        A[查看变更文件] --> B{变更类型}
        B -->|新功能| C[功能完整性检查]
        B -->|Bug修复| D[修复有效性验证]
        B -->|重构| E[重构合理性评估]
        B -->|配置变更| F[配置安全性检查]
        
        C --> G[安全影响分析]
        D --> G
        E --> G
        F --> G
    ```
    
    ### Step 3: 逐文件深度审查 (主要时间)
    
    #### 3.1 Rails模型文件审查
    ```ruby
    # 检查要点
    - 数据验证规则
    - 关联关系定义
    - 回调方法安全性
    - 作用域查询优化
    ```
    
    #### 3.2 控制器文件审查
    ```ruby
    # 检查要点
    - 参数过滤和验证
    - 权限控制
    - 异常处理
    - 响应格式
    ```
    
    #### 3.3 视图文件审查
    ```erb
    <!-- 检查要点 -->
    - XSS防护
    - 数据展示逻辑
    - 性能优化
    - 可访问性
    ```
    
    #### 3.4 测试文件审查
    ```ruby
    # 检查要点
    - 测试覆盖度
    - 边界条件测试
    - 异常情况测试
    - 性能测试
    ```
    
    ### Step 4: 综合评估和反馈 (15分钟)
    
    ```mermaid
    graph TD
        A[收集所有问题] --> B[按严重性分级]
        B --> C[生成改进建议]
        C --> D[提供代码示例]
        D --> E[输出审查报告]
        
        B --> B1[严重: 安全/功能问题]
        B --> B2[重要: 性能/维护问题]
        B --> B3[一般: 风格/约定问题]
    ```
  </process>

  <criteria>
    ## 代码质量评估标准
    
    ### 安全性评估 (权重: 40%)
    - ✅ 无SQL注入风险
    - ✅ 无XSS漏洞
    - ✅ 权限控制完善
    - ✅ 敏感信息保护
    
    ### 功能正确性 (权重: 30%)
    - ✅ 业务逻辑正确
    - ✅ 边界条件处理
    - ✅ 错误处理完善
    - ✅ 测试覆盖充分
    
    ### 代码质量 (权重: 20%)
    - ✅ 命名清晰规范
    - ✅ 结构合理清晰
    - ✅ 注释适当充分
    - ✅ 遵循Rails约定
    
    ### 性能影响 (权重: 10%)
    - ✅ 无明显性能问题
    - ✅ 数据库查询优化
    - ✅ 内存使用合理
    - ✅ 缓存策略得当
  </criteria>
</execution>
