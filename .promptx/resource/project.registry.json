{"version": "2.0.0", "source": "project", "metadata": {"version": "2.0.0", "description": "project 级资源注册表", "createdAt": "2025-08-03T08:14:12.204Z", "updatedAt": "2025-08-03T08:14:12.207Z", "resourceCount": 4}, "resources": [{"id": "code-reviewer", "source": "project", "protocol": "role", "name": "Code Reviewer 角色", "description": "专业角色，提供特定领域的专业能力", "reference": "@project://.promptx/resource/role/code-reviewer/code-reviewer.role.md", "metadata": {"createdAt": "2025-08-03T08:14:12.205Z", "updatedAt": "2025-08-03T08:14:12.205Z", "scannedAt": "2025-08-03T08:14:12.205Z", "path": "role/code-reviewer/code-reviewer.role.md"}}, {"id": "code-review-workflow", "source": "project", "protocol": "execution", "name": "Code Review Workflow 执行模式", "description": "执行模式，定义具体的行为模式", "reference": "@project://.promptx/resource/role/code-reviewer/execution/code-review-workflow.execution.md", "metadata": {"createdAt": "2025-08-03T08:14:12.205Z", "updatedAt": "2025-08-03T08:14:12.205Z", "scannedAt": "2025-08-03T08:14:12.205Z", "path": "role/code-reviewer/execution/code-review-workflow.execution.md"}}, {"id": "analytical-thinking", "source": "project", "protocol": "thought", "name": "Analytical Thinking 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/code-reviewer/thought/analytical-thinking.thought.md", "metadata": {"createdAt": "2025-08-03T08:14:12.206Z", "updatedAt": "2025-08-03T08:14:12.206Z", "scannedAt": "2025-08-03T08:14:12.206Z", "path": "role/code-reviewer/thought/analytical-thinking.thought.md"}}, {"id": "security-mindset", "source": "project", "protocol": "thought", "name": "Security Mindset 思维模式", "description": "思维模式，指导AI的思考方式", "reference": "@project://.promptx/resource/role/code-reviewer/thought/security-mindset.thought.md", "metadata": {"createdAt": "2025-08-03T08:14:12.207Z", "updatedAt": "2025-08-03T08:14:12.207Z", "scannedAt": "2025-08-03T08:14:12.207Z", "path": "role/code-reviewer/thought/security-mindset.thought.md"}}], "stats": {"totalResources": 4, "byProtocol": {"role": 1, "execution": 1, "thought": 2}, "bySource": {"project": 4}}}