puts '=== 测试简单的Artifact创建 ==='
begin
  agent = Bot::Agent.first
  intent = agent.intents.find_by(name: '素材查询')
  
  puts "Intent: #{intent.name}"
  
  activity_tool = Bot::Tools::ActivityQueryTool.new(intent: intent)
  
  # 设置Bot::Current.conversation
  conversation = agent.conversations.first || agent.conversations.create!(user: User.first)
  Bot::Current.conversation = conversation
  
  puts "Conversation: #{conversation.id}"
  
  # 手动测试create_artifact方法
  puts "\n=== 手动测试create_artifact方法 ==="
  
  # 设置context和current_method
  activity_tool.instance_variable_set(:@context, { query_activities: { params: { query: '测试' } } })
  activity_tool.instance_variable_set(:@current_method, :query_activities)
  
  # 调用create_artifact
  activity_tool.send(:create_artifact, { query: '测试' }, type: 'Bot::ActivityListArtifact')
  
  # 检查context
  context = activity_tool.instance_variable_get(:@context)
  puts "Context: #{context}"
  
  if context[:query_activities][:artifact]
    artifact = context[:query_activities][:artifact]
    puts "✅ 手动创建Artifact成功!"
    puts "  ID: #{artifact.id}"
    puts "  Type: #{artifact.type}"
    puts "  Class: #{artifact.class}"
  else
    puts "❌ 手动创建Artifact失败"
  end
  
rescue StandardError => e
  puts "❌ 失败: #{e.message}"
  puts e.backtrace.first(10)
ensure
  Bot::Current.conversation = nil
end
