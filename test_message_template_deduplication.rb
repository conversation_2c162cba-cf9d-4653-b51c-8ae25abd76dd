#!/usr/bin/env ruby
# 测试消息模板去重功能
# 使用方法: ruby test_message_template_deduplication.rb

require_relative 'config/environment'

class MessageTemplateDeduplicationTest
  def initialize
    @test_user_id = 1703573  # 使用用户提供的测试用户ID
    @test_rule_id = 38       # 使用用户提供的测试规则ID
    puts "🧪 开始测试消息模板去重功能"
    puts "   测试用户ID: #{@test_user_id}"
    puts "   测试规则ID: #{@test_rule_id}"
  end

  def run_all_tests
    puts "\n" + "="*60
    puts "开始执行所有测试用例"
    puts "="*60

    test_basic_template_selection
    test_template_deduplication
    test_all_templates_used_scenario
    test_single_template_scenario
    test_empty_templates_scenario

    puts "\n" + "="*60
    puts "所有测试完成！"
    puts "="*60
  end

  private

  def test_basic_template_selection
    puts "\n📋 测试1: 基础模板选择功能"
    
    begin
      rule = Serve::Rule.find(@test_rule_id)
      user = User.find(@test_user_id)
      
      if rule.message_templates.used.any?
        templates = rule.message_templates.used
        puts "   ✅ 找到 #{templates.count} 个可用模板"
        
        # 创建一个测试Pack来调用方法
        pack = rule.packs.build(
          name: "测试Pack",
          creator_id: 1,
          org_id: 178132,
          state: 'pending'
        )
        
        # 测试模板选择
        selected_template = pack.send(:select_unique_message_template, templates, user)
        puts "   ✅ 成功选择模板: #{selected_template.name} (ID: #{selected_template.id})"
      else
        puts "   ⚠️  当前规则没有可用的消息模板"
      end
      
    rescue => e
      puts "   ❌ 测试失败: #{e.message}"
      puts "   错误详情: #{e.backtrace.first(3).join("\n   ")}"
    end
  end

  def test_template_deduplication
    puts "\n🔄 测试2: 模板去重功能"
    
    begin
      rule = Serve::Rule.find(@test_rule_id)
      user = User.find(@test_user_id)
      
      # 查看用户历史消息
      user_messages = user.serve_messages
                          .joins(:pack)
                          .where(packs: { rule: rule })
                          .where.not(payload: nil)
                          .order(created_at: :desc)
                          .limit(5)
      
      puts "   📊 用户历史消息数量: #{user_messages.count}"
      
      used_template_ids = []
      user_messages.each do |message|
        template_id = message.payload&.dig('template_id')
        if template_id
          used_template_ids << template_id
          puts "   📝 历史消息 #{message.id}: 使用模板ID #{template_id}"
        else
          puts "   📝 历史消息 #{message.id}: 无模板ID记录"
        end
      end
      
      puts "   🎯 已使用的模板ID: #{used_template_ids.uniq}"
      
    rescue => e
      puts "   ❌ 测试失败: #{e.message}"
    end
  end

  def test_all_templates_used_scenario
    puts "\n🔄 测试3: 所有模板都用过的场景"
    
    begin
      rule = Serve::Rule.find(@test_rule_id)
      user = User.find(@test_user_id)
      templates = rule.message_templates.used
      
      if templates.count > 1
        pack = rule.packs.build(
          name: "测试Pack",
          creator_id: 1,
          org_id: 178132,
          state: 'pending'
        )
        
        # 模拟所有模板都用过的情况
        puts "   🎭 模拟所有模板都用过的情况"
        
        # 获取最后使用的模板ID
        last_template_id = pack.send(:get_user_last_template_id, user)
        puts "   📋 最后使用的模板ID: #{last_template_id || '无'}"
        
        # 测试选择逻辑
        selected_template = pack.send(:select_unique_message_template, templates, user)
        puts "   ✅ 选择的模板: #{selected_template.name} (ID: #{selected_template.id})"
        
        if last_template_id && selected_template.id != last_template_id
          puts "   ✅ 成功避免了重复使用上次的模板"
        elsif last_template_id.nil?
          puts "   ℹ️  用户无历史记录，随机选择模板"
        else
          puts "   ⚠️  选择了与上次相同的模板（可能只有一个模板可用）"
        end
        
      else
        puts "   ⚠️  模板数量不足，无法测试去重逻辑"
      end
      
    rescue => e
      puts "   ❌ 测试失败: #{e.message}"
    end
  end

  def test_single_template_scenario
    puts "\n📋 测试4: 单个模板场景"
    
    begin
      rule = Serve::Rule.find(@test_rule_id)
      user = User.find(@test_user_id)
      templates = rule.message_templates.used
      
      if templates.any?
        # 只取第一个模板进行测试
        single_template = [templates.first]
        
        pack = rule.packs.build(
          name: "测试Pack",
          creator_id: 1,
          org_id: 178132,
          state: 'pending'
        )
        
        selected_template = pack.send(:select_unique_message_template, single_template, user)
        puts "   ✅ 单模板选择成功: #{selected_template.name} (ID: #{selected_template.id})"
        
      else
        puts "   ⚠️  没有可用模板进行测试"
      end
      
    rescue => e
      puts "   ❌ 测试失败: #{e.message}"
    end
  end

  def test_empty_templates_scenario
    puts "\n📋 测试5: 空模板数组场景"
    
    begin
      rule = Serve::Rule.find(@test_rule_id)
      user = User.find(@test_user_id)
      
      pack = rule.packs.build(
        name: "测试Pack",
        creator_id: 1,
        org_id: 178132,
        state: 'pending'
      )
      
      # 测试空数组
      result = pack.send(:select_unique_message_template, [], user)
      puts "   ✅ 空模板数组处理正常: #{result.nil? ? '返回nil' : '返回了模板'}"
      
    rescue => e
      puts "   ❌ 测试失败: #{e.message}"
    end
  end
end

# 运行测试
if __FILE__ == $0
  test = MessageTemplateDeduplicationTest.new
  test.run_all_tests
end
