#!/usr/bin/env ruby

# 测试修正后的ContentTemplate结构处理
puts "🧪 测试修正后的ContentTemplate结构处理"
puts "=" * 60

begin
  # 查找招投标登记规则
  rule = Serve::Rule.find_by(name: '招标项目登记') || Serve::Rule.where("name LIKE '%招投标%'").first
  user = User.find(3959)
  
  unless rule && user
    puts "❌ 未找到测试数据"
    exit 1
  end
  
  puts "📋 测试环境:"
  puts "  规则: #{rule.name} (ID: #{rule.id})"
  puts "  用户: #{user.name} (ID: #{user.id})"
  
  # 检查MessageTemplate结构
  message_templates = rule.message_templates.used
  puts "\n📋 MessageTemplate层级:"
  puts "  MessageTemplate数量: #{message_templates.count}"
  
  message_templates.each_with_index do |mt, index|
    puts "    MessageTemplate#{index + 1}: #{mt.name} (ID: #{mt.id})"
    
    if mt.option&.content&.enabled
      content_templates = mt.option.content.templates
      puts "      ContentTemplate数量: #{content_templates.count}"
      
      content_templates.each_with_index do |ct, ct_index|
        puts "        ContentTemplate#{ct_index + 1}:"
        puts "          _id: #{ct.attributes['_id']}"
        puts "          state: #{ct.state}"
        puts "          title: #{ct.title}"
        puts "          content: #{ct.content[0..50]}..." if ct.content
      end
    else
      puts "      无启用的content配置"
    end
  end
  
  # 测试修正后的内容模板获取逻辑
  puts "\n🔧 测试修正后的逻辑:"
  puts "-" * 40
  
  # 获取所有可用的ContentTemplate
  content_templates = []
  message_templates.each do |message_template|
    if message_template.option&.content&.enabled && message_template.option.content.templates.present?
      used_content_templates = message_template.option.content.templates.select { |ct| ct.state == 'used' }
      content_templates.concat(used_content_templates)
    end
  end
  
  puts "总可用ContentTemplate数量: #{content_templates.count}"
  
  if content_templates.any?
    # 创建测试Pack
    pack = rule.packs.build(
      name: "测试Pack - ContentTemplate",
      creator: user,
      org: user.orgs.first
    )
    
    # 测试选择逻辑
    selected_ct = pack.send(:select_different_content_template, content_templates, user)
    puts "\n选择的ContentTemplate:"
    puts "  _id: #{selected_ct.attributes['_id']}"
    puts "  title: #{selected_ct.title}"
    puts "  state: #{selected_ct.state}"
    
    # 测试消息生成
    parent_mt = message_templates.first
    message = parent_mt.get_message(user: user, source: 'test')
    
    puts "\n生成的消息:"
    puts "  标题: #{message.dig(:card, :title)}"
    puts "  内容: #{message.dig(:card, :content)[0..100]}..." if message.dig(:card, :content)
    
    # 测试payload生成
    payload = {
      content_template_id: selected_ct.attributes['_id'],
      content_template_title: selected_ct.title,
      generated_at: Time.current.iso8601,
      deduplication_type: 'simple'
    }
    
    puts "\n生成的payload:"
    puts "  content_template_id: #{payload[:content_template_id]}"
    puts "  content_template_title: #{payload[:content_template_title]}"
    puts "  deduplication_type: #{payload[:deduplication_type]}"
    
    puts "\n✅ ContentTemplate结构处理修正成功！"
    puts "- 正确获取到ContentTemplate对象"
    puts "- 正确提取_id属性"
    puts "- 正确处理state过滤"
    puts "- 正确生成payload"
    
  else
    puts "❌ 未找到可用的ContentTemplate"
  end

rescue StandardError => e
  puts "❌ 测试过程中出现错误: #{e.message}"
  puts "错误类型: #{e.class}"
  puts "错误位置: #{e.backtrace.first}"
end

puts "\n" + "=" * 60
puts "🏁 ContentTemplate结构测试完成"
