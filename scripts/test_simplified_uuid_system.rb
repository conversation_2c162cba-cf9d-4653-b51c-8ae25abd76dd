#!/usr/bin/env ruby
# 测试精简后的UUID去重系统
# 使用方法: rails runner scripts/test_simplified_uuid_system.rb

puts "🧪 测试精简后的UUID去重系统"
puts "=" * 50

class SimplifiedUuidSystemTest
  def initialize
    @test_user_id = 3959
    @target_rule_name = "招标项目登记"
  end

  def run_test
    puts "\n📋 第一阶段：环境检查"
    return unless check_environment
    
    puts "\n🔧 第二阶段：模型方法验证"
    test_model_methods
    
    puts "\n📨 第三阶段：模板选择测试"
    test_template_selection
    
    puts "\n🔍 第四阶段：去重功能验证"
    test_deduplication
    
    puts "\n📊 第五阶段：监控功能测试"
    test_monitoring
  end

  private

  def check_environment
    begin
      @test_user = User.find(@test_user_id)
      puts "   ✅ 找到测试用户: #{@test_user.name} (ID: #{@test_user_id})"
      
      @target_rule = Serve::Rule.find_by(name: @target_rule_name)
      if @target_rule.nil?
        puts "   ❌ 没有找到规则: #{@target_rule_name}"
        return false
      end
      
      puts "   ✅ 找到目标规则: #{@target_rule.name} (ID: #{@target_rule.id})"
      
      @templates = @target_rule.message_templates.used
      puts "   ✅ 找到 #{@templates.size} 个可用模板"
      
      true
    rescue => e
      puts "   ❌ 环境检查失败: #{e.message}"
      false
    end
  end

  def test_model_methods
    begin
      puts "   🔄 测试MessageTemplate方法..."
      
      template = @templates.first
      if template
        # 测试保留的方法
        signature = template.template_signature
        puts "   ✅ template_signature: #{signature}"
        
        message = template.get_message(user: @test_user)
        puts "   ✅ get_message: 包含template_signature = #{message[:template_signature]}"
        
        # 确认n8n方法已移除
        n8n_methods = [:rule_uses_n8n_workflow?, :n8n_workflow_id, :n8n_workflow_url]
        n8n_methods.each do |method|
          if template.respond_to?(method)
            puts "   ❌ n8n方法仍存在: #{method}"
          else
            puts "   ✅ n8n方法已移除: #{method}"
          end
        end
      end
      
    rescue => e
      puts "   ❌ 模型方法测试失败: #{e.message}"
    end
  end

  def test_template_selection
    begin
      puts "   🔄 测试模板选择逻辑..."
      
      # 创建测试Pack
      test_pack = @target_rule.packs.create!(
        name: "精简系统测试Pack_#{Time.current.to_i}",
        creator: User.first,
        state: 'pending'
      )
      
      test_pack.receivers.create!(user: @test_user)
      
      # 测试模板选择
      selected_option = test_pack.send(:select_unique_message_template_with_uuid, @templates, @test_user)
      
      if selected_option
        puts "   ✅ 模板选择成功:"
        puts "      类型: #{selected_option[:type]}"
        puts "      UUID: #{selected_option[:content_uuid]}"
        puts "      模板ID: #{selected_option[:template]&.id}"
        
        # 验证只有传统模板类型
        if selected_option[:type] == 'traditional_template'
          puts "   ✅ 确认为传统模板类型"
        else
          puts "   ❌ 意外的模板类型: #{selected_option[:type]}"
        end
        
        @test_pack = test_pack
        @selected_option = selected_option
      else
        puts "   ❌ 模板选择失败"
      end
      
    rescue => e
      puts "   ❌ 模板选择测试失败: #{e.message}"
      puts "   错误详情: #{e.backtrace.first(3).join("\n   ")}"
    end
  end

  def test_deduplication
    begin
      puts "   🔄 测试去重功能..."
      
      if @test_pack && @selected_option
        uuid = @selected_option[:content_uuid]
        
        # 创建消息
        message = Serve::Message.create!(
          user: @test_user,
          pack: @test_pack,
          content: "测试消息内容",
          payload: @selected_option[:message_content],
          content_uuid: uuid
        )
        
        puts "   ✅ 创建测试消息: ID #{message.id}, UUID: #{uuid}"
        
        # 测试去重查询
        exists = @test_pack.send(:user_received_content_uuid?, @test_user, uuid)
        puts "   ✅ 去重查询结果: #{exists ? '已存在' : '不存在'}"
        
        # 测试模板签名去重
        signature = @selected_option[:template]&.template_signature
        if signature
          signature_exists = @test_pack.send(:user_received_template_signature?, @test_user, signature)
          puts "   ✅ 模板签名查询结果: #{signature_exists ? '已存在' : '不存在'}"
        end
        
        # 测试UUID提取
        extracted_id = @test_pack.send(:extract_template_id_from_uuid, uuid)
        puts "   ✅ UUID提取模板ID: #{extracted_id}"
        
      else
        puts "   ❌ 无法测试去重功能：缺少测试数据"
      end
      
    rescue => e
      puts "   ❌ 去重功能测试失败: #{e.message}"
    end
  end

  def test_monitoring
    begin
      puts "   🔄 测试监控功能..."
      
      # 测试监控记录
      UuidDeduplicationMonitoring.log_performance(:test_operation, 50.5, { test: true })
      UuidDeduplicationMonitoring.log_business_metric(:test_metric, 1, { test: true })
      UuidDeduplicationMonitoring.log_deduplication_event(:template_selected, { test: true })
      
      puts "   ✅ 监控事件记录成功"
      
      # 检查日志文件
      log_file = Rails.root.join('log', 'uuid_deduplication.log')
      if File.exist?(log_file)
        puts "   ✅ 监控日志文件存在"
        
        # 检查最近的日志内容
        recent_logs = File.readlines(log_file).last(10)
        test_logs = recent_logs.select { |line| line.include?('test') }
        
        if test_logs.any?
          puts "   ✅ 监控日志记录正常 (#{test_logs.size}条测试记录)"
        else
          puts "   ❌ 监控日志中没有找到测试记录"
        end
      else
        puts "   ❌ 监控日志文件不存在"
      end
      
    rescue => e
      puts "   ❌ 监控功能测试失败: #{e.message}"
    end
  end
end

# 执行测试
begin
  tester = SimplifiedUuidSystemTest.new
  tester.run_test
rescue => e
  puts "❌ 测试执行失败: #{e.message}"
  puts "   错误详情: #{e.backtrace.first(5).join("\n   ")}"
end

puts "\n🏁 精简系统测试完成！"
puts "\n💡 系统已精简为只包含传统模板功能，n8n相关代码已完全移除。"
puts "   核心的UUID去重功能保持完整，监控系统正常工作。"
