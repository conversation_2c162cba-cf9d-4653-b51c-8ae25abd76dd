-- 快速检查 UUID 字段状态的 SQL 脚本

-- 1. 检查表结构
\d serve_messages;

-- 2. 统计总消息数
SELECT 'Total Messages' as metric, COUNT(*) as count FROM serve_messages;

-- 3. 统计有UUID的消息数
SELECT 'Messages with UUID' as metric, COUNT(*) as count FROM serve_messages WHERE content_uuid IS NOT NULL;

-- 4. 统计没有UUID的消息数
SELECT 'Messages without UUID' as metric, COUNT(*) as count FROM serve_messages WHERE content_uuid IS NULL;

-- 5. UUID覆盖率
SELECT 
  'UUID Coverage' as metric,
  CASE 
    WHEN COUNT(*) = 0 THEN '0%'
    ELSE ROUND(
      (COUNT(CASE WHEN content_uuid IS NOT NULL THEN 1 END) * 100.0 / COUNT(*)), 2
    ) || '%'
  END as coverage
FROM serve_messages;

-- 6. UUID格式分析
SELECT 
  'Template UUIDs' as type,
  COUNT(*) as count
FROM serve_messages 
WHERE content_uuid LIKE 'template_%';

SELECT 
  'Historical UUIDs' as type,
  COUNT(*) as count
FROM serve_messages 
WHERE content_uuid LIKE 'historical_%';

-- 7. 显示一些UUID示例
SELECT 
  'UUID Examples' as info,
  content_uuid
FROM serve_messages 
WHERE content_uuid IS NOT NULL 
LIMIT 5;

-- 8. 检查是否有重复的UUID
SELECT 
  'Duplicate UUIDs' as issue,
  content_uuid,
  COUNT(*) as count
FROM serve_messages 
WHERE content_uuid IS NOT NULL
GROUP BY content_uuid
HAVING COUNT(*) > 1
LIMIT 5;
