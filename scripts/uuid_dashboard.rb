#!/usr/bin/env ruby
# UUID消息去重功能监控仪表板（简化版）

puts "📊 UUID消息去重功能监控仪表板"
puts "=" * 60

begin
  puts "\n🕐 报告生成时间: #{Time.current.strftime('%Y-%m-%d %H:%M:%S')}"

  # 数据库指标分析
  puts "\n🗄️  数据库指标分析"
  puts "-" * 40

  # 统计消息模板数量
  total_templates = Serve::MessageTemplate.count

  puts "   📊 消息模板总数: #{total_templates}"

  # 统计消息数量
  total_messages = Serve::Message.count
  uuid_messages = Serve::Message.where.not(content_uuid: nil).count

  puts "   📊 消息总数: #{total_messages}"
  puts "   🆔 带UUID消息: #{uuid_messages}"

  # 统计n8n配置的规则
  n8n_rules = Serve::Rule.where("model_payload->>'n8n_workflow' IS NOT NULL").count
  total_rules = Serve::Rule.count

  puts "   📊 规则总数: #{total_rules}"
  puts "   🔗 n8n配置规则: #{n8n_rules}"

  # 最近24小时的消息统计
  recent_messages = Serve::Message.where("created_at > ?", 24.hours.ago).count
  puts "   📈 最近24小时消息: #{recent_messages}"

  # 系统健康检查
  puts "\n🏥 系统健康检查"
  puts "-" * 40

  # 检查数据库连接
  ActiveRecord::Base.connection.execute("SELECT 1")
  puts "   ✅ 数据库连接正常"

  # 检查必要的索引
  connection = ActiveRecord::Base.connection
  indexes = connection.indexes("serve_messages")
  uuid_index = indexes.find { |idx| idx.name.include?("content_uuid") }

  if uuid_index
    puts "   ✅ UUID索引存在"
  else
    puts "   ⚠️  缺少content_uuid索引"
  end

  puts "   🎉 系统健康状态良好！"
rescue StandardError => e
  puts "❌ 监控仪表板执行失败: #{e.message}"
end

puts "\n🏁 监控报告完成！"
