#!/usr/bin/env ruby
# 部署前环境检查脚本
# 使用方法: RAILS_ENV=production rails runner scripts/pre_deployment_check.rb
# 
# 功能:
# 1. 检查服务器环境是否准备就绪
# 2. 分析UUID重复情况
# 3. 评估修复影响和风险
# 4. 生成部署建议

puts "🔍 生产环境部署前检查"
puts "=" * 50

class PreDeploymentChecker
  def initialize
    @issues = []
    @warnings = []
    @recommendations = []
  end

  def run
    puts "\n📊 第一阶段：环境基础检查"
    check_environment
    
    puts "\n🔍 第二阶段：UUID重复情况分析"
    analyze_uuid_duplicates
    
    puts "\n📋 第三阶段：代码更新检查"
    check_code_updates
    
    puts "\n⚡ 第四阶段：性能影响评估"
    assess_performance_impact
    
    puts "\n📊 第五阶段：生成部署报告"
    generate_deployment_report
  end

  private

  def check_environment
    begin
      puts "   🔄 检查Rails环境..."
      puts "      当前环境: #{Rails.env}"
      puts "      Rails版本: #{Rails.version}"
      
      puts "   🔄 检查数据库连接..."
      total_messages = Serve::Message.count
      puts "      数据库连接: ✅ 正常"
      puts "      总消息数: #{total_messages}"
      
      puts "   🔄 检查关键表结构..."
      if Serve::Message.column_names.include?('content_uuid')
        puts "      content_uuid字段: ✅ 存在"
      else
        @issues << "content_uuid字段不存在"
        puts "      content_uuid字段: ❌ 不存在"
      end
      
      # 检查索引
      indexes = ActiveRecord::Base.connection.indexes('serve_messages')
      uuid_index = indexes.find { |idx| idx.columns.include?('content_uuid') }
      if uuid_index
        puts "      content_uuid索引: ✅ 存在"
      else
        @warnings << "content_uuid字段没有索引，可能影响查询性能"
        puts "      content_uuid索引: ⚠️  不存在"
      end
      
    rescue => e
      @issues << "环境检查失败: #{e.message}"
      puts "   ❌ 环境检查失败: #{e.message}"
    end
  end

  def analyze_uuid_duplicates
    begin
      puts "   🔄 分析UUID重复情况..."
      
      # 统计UUID分布
      total_with_uuid = Serve::Message.where.not(content_uuid: nil).count
      template_count = Serve::Message.where('content_uuid LIKE ?', 'template_%').count
      historical_count = Serve::Message.where('content_uuid LIKE ?', 'historical_%').count
      other_count = total_with_uuid - template_count - historical_count
      
      puts "      有UUID消息总数: #{total_with_uuid}"
      puts "      template_* 格式: #{template_count}条"
      puts "      historical_* 格式: #{historical_count}条"
      puts "      其他格式: #{other_count}条"
      
      # 检查重复UUID
      duplicates = Serve::Message.where.not(content_uuid: nil)
                                 .group(:content_uuid)
                                 .having('COUNT(*) > 1')
                                 .count
      
      puts "      重复UUID数量: #{duplicates.size}个"
      
      if duplicates.any?
        puts "      重复详情:"
        duplicates.each do |uuid, count|
          puts "         #{uuid}: #{count}条消息"
        end
        
        # 分析重复类型
        template_duplicates = duplicates.select { |uuid, _| uuid.start_with?('template_') }
        historical_duplicates = duplicates.select { |uuid, _| uuid.start_with?('historical_') }
        
        if template_duplicates.any?
          @issues << "发现#{template_duplicates.size}个template_*重复UUID，需要修复"
          puts "      template_* 重复: #{template_duplicates.size}个 ❌"
        end
        
        if historical_duplicates.any?
          @warnings << "发现#{historical_duplicates.size}个historical_*重复UUID，建议检查"
          puts "      historical_* 重复: #{historical_duplicates.size}个 ⚠️"
        end
      else
        puts "      重复检查: ✅ 无重复UUID"
      end
      
    rescue => e
      @issues << "UUID分析失败: #{e.message}"
      puts "   ❌ UUID分析失败: #{e.message}"
    end
  end

  def check_code_updates
    begin
      puts "   🔄 检查代码更新状态..."
      
      # 检查MessageTemplate模型是否有新方法
      template = Serve::MessageTemplate.first
      if template
        if template.respond_to?(:generate_unique_uuid)
          puts "      generate_unique_uuid方法: ✅ 已部署"
          
          # 测试方法是否正常工作
          user = User.first
          if user
            test_uuid = template.generate_unique_uuid(user)
            if test_uuid.include?('_')
              puts "      UUID生成测试: ✅ 正常 (#{test_uuid})"
            else
              @issues << "UUID生成方法返回格式异常"
              puts "      UUID生成测试: ❌ 格式异常"
            end
          else
            @warnings << "没有用户数据进行UUID生成测试"
          end
        else
          @issues << "generate_unique_uuid方法未部署"
          puts "      generate_unique_uuid方法: ❌ 未部署"
        end
        
        # 检查Pack模型的UUID生成逻辑
        pack = Serve::Pack.first
        if pack
          if pack.private_methods.include?(:select_unique_message_template_with_uuid)
            puts "      Pack UUID选择逻辑: ✅ 存在"
          else
            @warnings << "Pack模型的UUID选择逻辑可能需要更新"
          end
        end
      else
        @warnings << "没有MessageTemplate数据进行测试"
      end
      
    rescue => e
      @issues << "代码更新检查失败: #{e.message}"
      puts "   ❌ 代码更新检查失败: #{e.message}"
    end
  end

  def assess_performance_impact
    begin
      puts "   🔄 评估性能影响..."
      
      # 评估修复脚本的影响
      duplicates = Serve::Message.where.not(content_uuid: nil)
                                 .group(:content_uuid)
                                 .having('COUNT(*) > 1')
                                 .count
      
      if duplicates.any?
        affected_messages = duplicates.values.sum - duplicates.size
        estimated_time = (duplicates.size * 2) # 每个重复UUID预计2秒
        
        puts "      需要修复的消息数: #{affected_messages}条"
        puts "      预计修复时间: #{estimated_time}秒 (#{(estimated_time / 60.0).round(1)}分钟)"
        puts "      数据库锁定风险: #{affected_messages > 100 ? '中等' : '低'}"
        
        if affected_messages > 100
          @recommendations << "建议在业务低峰期执行修复脚本"
        end
        
        if estimated_time > 300  # 5分钟
          @recommendations << "修复时间较长，建议通知相关团队"
        end
      else
        puts "      修复影响: ✅ 无需修复"
      end
      
      # 检查数据库负载
      puts "      当前数据库大小评估:"
      puts "         serve_messages表: #{Serve::Message.count}条记录"
      
    rescue => e
      @warnings << "性能影响评估失败: #{e.message}"
      puts "   ⚠️  性能影响评估失败: #{e.message}"
    end
  end

  def generate_deployment_report
    puts "\n📊 部署前检查报告:"
    
    # 显示问题
    if @issues.any?
      puts "\n   ❌ 发现问题 (#{@issues.size}个):"
      @issues.each_with_index do |issue, index|
        puts "      #{index + 1}. #{issue}"
      end
      puts "\n   🚨 建议解决所有问题后再部署"
    else
      puts "\n   ✅ 没有发现阻塞性问题"
    end
    
    # 显示警告
    if @warnings.any?
      puts "\n   ⚠️  警告信息 (#{@warnings.size}个):"
      @warnings.each_with_index do |warning, index|
        puts "      #{index + 1}. #{warning}"
      end
    end
    
    # 显示建议
    if @recommendations.any?
      puts "\n   💡 部署建议 (#{@recommendations.size}个):"
      @recommendations.each_with_index do |rec, index|
        puts "      #{index + 1}. #{rec}"
      end
    end
    
    # 生成部署决策
    puts "\n🎯 部署决策建议:"
    if @issues.empty?
      puts "   ✅ 可以部署"
      puts "   📋 部署步骤:"
      puts "      1. 确保在业务低峰期执行"
      puts "      2. 备份serve_messages表"
      puts "      3. 部署代码更新"
      puts "      4. 运行修复脚本: RAILS_ENV=production rails runner scripts/production_fix_uuid_uniqueness.rb"
      puts "      5. 验证修复结果"
      puts "      6. 监控系统运行状态"
    else
      puts "   ❌ 暂不建议部署"
      puts "   🔧 请先解决发现的问题"
    end
    
    # 生成检查摘要
    puts "\n📋 检查摘要:"
    puts "   环境状态: #{@issues.empty? ? '✅ 正常' : '❌ 有问题'}"
    puts "   代码准备: #{@issues.any? { |i| i.include?('方法') } ? '❌ 未就绪' : '✅ 就绪'}"
    puts "   数据状态: #{@issues.any? { |i| i.include?('UUID') } ? '❌ 需修复' : '✅ 正常'}"
    puts "   部署风险: #{@issues.any? ? '高' : @warnings.any? ? '中' : '低'}"
  end
end

# 执行检查
begin
  checker = PreDeploymentChecker.new
  checker.run
rescue => e
  puts "❌ 检查执行失败: #{e.message}"
  puts "   错误详情: #{e.backtrace.first(5).join("\n   ")}"
end

puts "\n🏁 部署前检查完成！"
