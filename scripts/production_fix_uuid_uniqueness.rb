#!/usr/bin/env ruby
# 生产环境UUID唯一性修复脚本
# 使用方法: RAILS_ENV=production rails runner scripts/production_fix_uuid_uniqueness.rb
# 
# 安全特性:
# 1. 生产环境检查和确认
# 2. 数据备份建议
# 3. 详细的日志记录
# 4. 分批处理避免长时间锁表
# 5. 错误恢复机制

puts "🔧 生产环境UUID唯一性修复脚本"
puts "=" * 60

class ProductionUuidUniquenessFixer
  def initialize
    @start_time = Time.current
    @fixed_count = 0
    @error_count = 0
    @duplicate_uuids = []
    @log_file = Rails.root.join('log', "uuid_fix_#{@start_time.strftime('%Y%m%d_%H%M%S')}.log")
    @batch_size = 10  # 小批量处理，避免长时间锁表
  end

  def run
    puts "\n⚠️  生产环境安全检查"
    return unless production_safety_check
    
    puts "\n📊 第一阶段：环境检查和数据分析"
    return unless environment_check
    
    puts "\n🔍 第二阶段：识别重复UUID"
    return unless identify_duplicates
    
    puts "\n⏸️  执行前最后确认"
    return unless final_confirmation
    
    puts "\n🔧 第三阶段：分批修复重复UUID"
    fix_duplicates_in_batches
    
    puts "\n📋 第四阶段：验证修复结果"
    verify_fixes
    
    puts "\n📊 第五阶段：生成详细报告"
    generate_detailed_report
  end

  private

  def production_safety_check
    puts "   🔍 检查运行环境..."
    
    if Rails.env.development?
      puts "   ⚠️  当前环境: #{Rails.env}"
      puts "   💡 建议先在开发环境测试完成后再在生产环境运行"
    elsif Rails.env.production?
      puts "   ⚠️  当前环境: #{Rails.env} - 生产环境"
      puts "   🚨 请确保已经:"
      puts "      1. 在开发环境完成测试"
      puts "      2. 备份了serve_messages表"
      puts "      3. 在业务低峰期执行"
      puts "      4. 通知了相关团队成员"
      
      print "\n   确认继续执行? (输入 'YES' 继续): "
      confirmation = STDIN.gets.chomp
      
      unless confirmation == 'YES'
        puts "   ❌ 用户取消执行"
        return false
      end
    end
    
    log_message("INFO", "生产环境安全检查通过，开始执行修复")
    true
  end

  def environment_check
    begin
      puts "   🔄 检查数据库连接..."
      total_messages = Serve::Message.count
      puts "   ✅ 数据库连接正常，总消息数: #{total_messages}"
      
      puts "   🔄 检查content_uuid字段..."
      uuid_messages = Serve::Message.where.not(content_uuid: nil).count
      puts "   ✅ content_uuid字段正常，有UUID消息: #{uuid_messages}"
      
      puts "   🔄 检查必要的模型方法..."
      template = Serve::MessageTemplate.first
      if template&.respond_to?(:generate_unique_uuid)
        puts "   ✅ generate_unique_uuid方法存在"
      else
        puts "   ❌ generate_unique_uuid方法不存在，请先部署代码更新"
        return false
      end
      
      log_message("INFO", "环境检查完成 - 总消息:#{total_messages}, UUID消息:#{uuid_messages}")
      true
      
    rescue => e
      puts "   ❌ 环境检查失败: #{e.message}"
      log_message("ERROR", "环境检查失败: #{e.message}")
      false
    end
  end

  def identify_duplicates
    begin
      puts "   🔄 查找重复UUID..."
      
      @duplicate_uuids = Serve::Message.where.not(content_uuid: nil)
                                      .group(:content_uuid)
                                      .having('COUNT(*) > 1')
                                      .count
      
      puts "   📊 发现重复UUID: #{@duplicate_uuids.size}个"
      
      if @duplicate_uuids.empty?
        puts "   ✅ 没有发现重复UUID，无需修复"
        log_message("INFO", "没有发现重复UUID，脚本正常结束")
        return false
      end
      
      total_affected = @duplicate_uuids.values.sum
      puts "   📊 影响消息总数: #{total_affected}条"
      
      @duplicate_uuids.each do |uuid, count|
        puts "      #{uuid}: #{count}条消息"
        log_message("INFO", "发现重复UUID: #{uuid} (#{count}条消息)")
      end
      
      true
      
    rescue => e
      puts "   ❌ 识别重复UUID失败: #{e.message}"
      log_message("ERROR", "识别重复UUID失败: #{e.message}")
      false
    end
  end

  def final_confirmation
    puts "   ⚠️  即将修复 #{@duplicate_uuids.size} 个重复UUID"
    puts "   📊 预计修复消息数: #{@duplicate_uuids.values.sum - @duplicate_uuids.size} 条"
    puts "   ⏱️  预计执行时间: #{(@duplicate_uuids.size * 2)} 分钟"
    puts "   📝 日志文件: #{@log_file}"
    
    print "\n   最后确认，开始修复? (输入 'CONFIRM' 继续): "
    confirmation = STDIN.gets.chomp
    
    if confirmation == 'CONFIRM'
      log_message("INFO", "用户确认开始修复，重复UUID数量: #{@duplicate_uuids.size}")
      true
    else
      puts "   ❌ 用户取消修复"
      log_message("INFO", "用户取消修复")
      false
    end
  end

  def fix_duplicates_in_batches
    begin
      puts "   🔄 开始分批修复重复UUID..."
      log_message("INFO", "开始修复过程")
      
      @duplicate_uuids.each_with_index do |(duplicate_uuid, count), index|
        puts "\n   🔧 修复进度: #{index + 1}/#{@duplicate_uuids.size} - UUID: #{duplicate_uuid}"
        log_message("INFO", "开始修复UUID: #{duplicate_uuid} (#{count}条消息)")
        
        fix_single_duplicate_uuid(duplicate_uuid)
        
        # 每修复一个UUID后短暂休息，避免数据库压力
        sleep(1) if @duplicate_uuids.size > 5
      end
      
      log_message("INFO", "修复过程完成")
      
    rescue => e
      puts "   ❌ 修复过程失败: #{e.message}"
      log_message("ERROR", "修复过程失败: #{e.message}")
    end
  end

  def fix_single_duplicate_uuid(duplicate_uuid)
    begin
      # 获取所有使用此UUID的消息，按ID排序
      messages = Serve::Message.where(content_uuid: duplicate_uuid).order(:id)
      
      puts "      影响消息数: #{messages.count}"
      
      # 保留第一条消息的UUID不变，修复其他消息
      messages.each_with_index do |message, index|
        if index == 0
          puts "      保留: 消息ID #{message.id} - UUID: #{duplicate_uuid}"
          log_message("INFO", "保留消息ID #{message.id}的UUID: #{duplicate_uuid}")
        else
          new_uuid = generate_unique_uuid_for_message(message)
          
          begin
            # 使用事务确保数据一致性
            ActiveRecord::Base.transaction do
              message.update_column(:content_uuid, new_uuid)
            end
            
            puts "      修复: 消息ID #{message.id} - 新UUID: #{new_uuid}"
            log_message("INFO", "修复消息ID #{message.id}: #{duplicate_uuid} → #{new_uuid}")
            @fixed_count += 1
            
          rescue => e
            puts "      ❌ 修复失败: 消息ID #{message.id} - #{e.message}"
            log_message("ERROR", "修复消息ID #{message.id}失败: #{e.message}")
            @error_count += 1
          end
        end
      end
      
    rescue => e
      puts "      ❌ 处理UUID #{duplicate_uuid}失败: #{e.message}"
      log_message("ERROR", "处理UUID #{duplicate_uuid}失败: #{e.message}")
    end
  end

  def generate_unique_uuid_for_message(message)
    # 根据消息类型生成相应的唯一UUID
    original_uuid = message.content_uuid
    
    if original_uuid&.start_with?('template_')
      generate_template_uuid(message)
    elsif original_uuid&.start_with?('historical_')
      generate_historical_uuid(message)
    else
      # 其他情况使用通用格式
      "unique_#{SecureRandom.hex(8)}_#{message.id}"
    end
  end

  def generate_template_uuid(message)
    # 提取原始模板ID
    original_uuid = message.content_uuid
    template_id = original_uuid.match(/template_(\d+)/)[1] rescue 'unknown'
    
    # 生成唯一键
    timestamp = Time.current.to_f
    unique_key = "#{template_id}_#{message.user_id}_#{message.id}_#{timestamp}_#{SecureRandom.hex(4)}"
    hash_suffix = Digest::MD5.hexdigest(unique_key)[0..7]
    
    "template_#{template_id}_#{message.user_id}_#{hash_suffix}"
  end

  def generate_historical_uuid(message)
    # 为historical类型重新生成UUID
    content_key = extract_content_key(message)
    timestamp = message.created_at.to_i
    
    # 添加随机因子确保唯一性
    unique_key = "#{content_key}|msg_#{message.id}|#{timestamp}|#{SecureRandom.hex(4)}"
    unique_hash = Digest::MD5.hexdigest(unique_key)[0..7]
    
    "historical_#{unique_hash}_#{message.id}"
  end

  def extract_content_key(message)
    key_parts = []
    key_parts << message.content if message.content.present?
    
    if message.payload.present?
      key_parts << message.payload.dig('card', 'content') if message.payload.dig('card', 'content')
      key_parts << message.payload.dig('content', 'template', '_id') if message.payload.dig('content', 'template', '_id')
    end
    
    key_parts << message.user_id if message.user_id
    key_parts << message.pack_id if message.pack_id
    key_parts << "message_#{message.id}" if key_parts.empty?
    
    key_parts.compact.join('|')
  end

  def verify_fixes
    begin
      puts "   🔄 验证修复结果..."
      
      # 重新检查重复UUID
      remaining_duplicates = Serve::Message.where.not(content_uuid: nil)
                                          .group(:content_uuid)
                                          .having('COUNT(*) > 1')
                                          .count
      
      puts "   📊 修复后重复UUID数量: #{remaining_duplicates.size}"
      
      if remaining_duplicates.empty?
        puts "   ✅ 所有重复UUID已修复"
        log_message("INFO", "验证通过：所有重复UUID已修复")
      else
        puts "   ⚠️  仍有重复UUID需要处理:"
        remaining_duplicates.each do |uuid, count|
          puts "      #{uuid}: #{count}条"
          log_message("WARNING", "仍有重复UUID: #{uuid} (#{count}条)")
        end
      end
      
    rescue => e
      puts "   ❌ 验证失败: #{e.message}"
      log_message("ERROR", "验证失败: #{e.message}")
    end
  end

  def generate_detailed_report
    duration = Time.current - @start_time
    
    puts "\n📊 生产环境修复完成报告:"
    puts "   开始时间: #{@start_time.strftime('%Y-%m-%d %H:%M:%S')}"
    puts "   结束时间: #{Time.current.strftime('%Y-%m-%d %H:%M:%S')}"
    puts "   执行时长: #{duration.round(2)}秒"
    puts "   处理的重复UUID: #{@duplicate_uuids.size}个"
    puts "   成功修复消息: #{@fixed_count}条"
    puts "   修复失败消息: #{@error_count}条"
    puts "   成功率: #{@fixed_count + @error_count > 0 ? (@fixed_count.to_f / (@fixed_count + @error_count) * 100).round(2) : 0}%"
    puts "   日志文件: #{@log_file}"
    
    log_message("INFO", "修复完成 - 成功:#{@fixed_count}, 失败:#{@error_count}, 耗时:#{duration.round(2)}秒")
    
    if @error_count == 0
      puts "\n   ✅ 所有UUID重复问题已成功修复！"
      log_message("INFO", "修复任务完全成功")
    else
      puts "\n   ⚠️  部分修复失败，请检查日志文件: #{@log_file}"
      log_message("WARNING", "修复任务部分失败，需要人工检查")
    end
    
    puts "\n💡 后续建议:"
    puts "   1. 监控新消息的UUID生成情况"
    puts "   2. 定期检查UUID唯一性"
    puts "   3. 保留此次修复的日志文件"
  end

  def log_message(level, message)
    timestamp = Time.current.strftime('%Y-%m-%d %H:%M:%S')
    log_entry = "[#{timestamp}] #{level}: #{message}\n"
    
    File.open(@log_file, 'a') do |file|
      file.write(log_entry)
    end
  end
end

# 执行修复
begin
  fixer = ProductionUuidUniquenessFixer.new
  fixer.run
rescue => e
  puts "❌ 修复执行失败: #{e.message}"
  puts "   错误详情: #{e.backtrace.first(5).join("\n   ")}"
end

puts "\n🏁 生产环境UUID唯一性修复脚本执行完成！"
