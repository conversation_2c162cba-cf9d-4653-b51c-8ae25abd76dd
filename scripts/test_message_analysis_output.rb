#!/usr/bin/env ruby
# 测试消息分析脚本的输出格式
# 使用方法: rails runner scripts/test_message_analysis_output.rb

puts "🧪 测试消息重复统计输出格式"
puts "=" * 60

# 模拟数据
puts "📊 模拟用户消息统计结果:"
puts "  总接收消息数: 16 条"
puts
puts "📝 详细分析:"
puts "    - 不同内容类型: 3 种"
puts "      └─ 说明: 该用户接收到了3种不同的消息内容"
puts "    - 重复内容类型: 3 种"
puts "      └─ 说明: 其中有3种内容被重复发送(发送次数>1)"
puts "    - 重复消息总数: 16 条"
puts "      └─ 说明: 重复内容的总消息数量"
puts "    ⚠️  注意: 所有消息都是重复的，没有只发送1次的内容"
puts

puts "🔄 重复接收的消息内容详情:"
puts "   说明: 以下是被重复发送的消息内容，按重复次数排序"
puts

puts "1. 📨 重复消息 #1"
puts "   🔢 重复次数: 9 次 (同一内容被发送了9次)"
puts "   🆔 消息ID: 1001, 1002, 1003, 1004, 1005, 1006, 1007, 1008, 1009"
puts "   📅 时间范围: 2024-01-01 09:00 ~ 2024-01-01 17:00"
puts "   📝 消息内容:"
puts "      请注意遵守廉洁纪律，严禁收受礼品礼金..."
puts

puts "2. 📨 重复消息 #2"
puts "   🔢 重复次数: 4 次 (同一内容被发送了4次)"
puts "   🆔 消息ID: 1010, 1011, 1012, 1013"
puts "   📅 时间范围: 2024-01-01 10:00 ~ 2024-01-01 16:00"
puts "   📝 消息内容:"
puts "      会议提醒：明天上午9点召开廉政建设专题会议..."
puts

puts "3. 📨 重复消息 #3"
puts "   🔢 重复次数: 3 次 (同一内容被发送了3次)"
puts "   🆔 消息ID: 1014, 1015, 1016"
puts "   📅 时间范围: 2024-01-01 14:00 ~ 2024-01-01 18:00"
puts "   📝 消息内容:"
puts "      学习通知：请及时完成廉洁教育在线学习课程..."
puts

puts "📋 总结:"
puts "   - 最多重复的内容被发送了 9 次"
puts "   - 共有 3 种不同的内容被重复发送"
puts "   - 建议检查消息发送规则和去重机制"
puts

puts "💡 解读说明:"
puts "   这个结果表明："
puts "   1. 用户总共收到16条消息，但只有3种不同的内容"
puts "   2. 每种内容都被重复发送了多次（9次、4次、3次）"
puts "   3. 没有任何内容只发送了1次，说明可能存在重复发送问题"
puts "   4. 需要检查消息去重机制和发送规则配置"
puts

puts "🔧 建议行动:"
puts "   1. 检查规则ID [38,39,44] 的配置是否合理"
puts "   2. 验证 content_uuid 字段的生成逻辑"
puts "   3. 查看消息发送日志，确认重复发送的原因"
puts "   4. 考虑添加发送频率限制或改进去重算法"
