#!/usr/bin/env ruby
# UUID去重监控功能验证脚本
# 使用方法: rails runner scripts/verify_monitoring.rb

puts "📊 UUID去重监控功能验证"
puts "=" * 50

class MonitoringVerification
  def initialize
    @log_file = Rails.root.join('log', 'uuid_deduplication.log')
  end

  def run_verification
    puts "\n🔍 第一步：检查监控组件"
    check_monitoring_components
    
    puts "\n📝 第二步：测试日志记录"
    test_logging_functionality
    
    puts "\n📊 第三步：分析现有日志"
    analyze_existing_logs
    
    puts "\n🎯 第四步：监控建议"
    provide_monitoring_recommendations
  end

  private

  def check_monitoring_components
    begin
      # 检查监控模块是否可用
      if defined?(UuidDeduplicationMonitoring)
        puts "   ✅ UuidDeduplicationMonitoring模块已加载"
        
        # 检查关键方法
        methods = [:log_performance, :log_business_metric, :log_error, :log_deduplication_event]
        methods.each do |method|
          if UuidDeduplicationMonitoring.respond_to?(method)
            puts "   ✅ #{method}方法可用"
          else
            puts "   ❌ #{method}方法不可用"
          end
        end
      else
        puts "   ❌ UuidDeduplicationMonitoring模块未加载"
      end
      
      # 检查日志配置
      if Rails.application.config.respond_to?(:uuid_deduplication_logger)
        puts "   ✅ 专用日志记录器已配置"
      else
        puts "   ❌ 专用日志记录器未配置"
      end
      
    rescue => e
      puts "   ❌ 监控组件检查失败: #{e.message}"
    end
  end

  def test_logging_functionality
    begin
      puts "   🧪 测试各种日志记录功能..."
      
      # 测试性能日志
      UuidDeduplicationMonitoring.log_performance(
        :test_operation, 
        45.5, 
        { test: true, timestamp: Time.current }
      )
      puts "   ✅ 性能日志记录测试完成"
      
      # 测试业务指标日志
      UuidDeduplicationMonitoring.log_business_metric(
        :test_metric, 
        1, 
        { test: true }
      )
      puts "   ✅ 业务指标日志记录测试完成"
      
      # 测试去重事件日志
      UuidDeduplicationMonitoring.log_deduplication_event(
        :template_selected,
        { 
          template_id: 999, 
          content_uuid: 'test_template_999',
          test: true 
        }
      )
      puts "   ✅ 去重事件日志记录测试完成"
      
      # 测试错误日志
      test_error = StandardError.new("这是一个测试错误")
      UuidDeduplicationMonitoring.log_error(
        :test_error_operation,
        test_error,
        { test: true }
      )
      puts "   ✅ 错误日志记录测试完成"
      
    rescue => e
      puts "   ❌ 日志功能测试失败: #{e.message}"
    end
  end

  def analyze_existing_logs
    if File.exist?(@log_file)
      puts "   📁 日志文件存在: #{@log_file}"
      
      file_size = (File.size(@log_file) / 1024.0).round(2)
      puts "   📏 文件大小: #{file_size} KB"
      
      # 读取最近的日志
      recent_logs = File.readlines(@log_file).last(20)
      puts "   📄 最近20条日志记录:"
      
      if recent_logs.any?
        # 分析日志类型
        log_types = {
          performance: recent_logs.count { |line| line.include?('PERFORMANCE:') },
          metric: recent_logs.count { |line| line.include?('METRIC:') },
          event: recent_logs.count { |line| line.include?('DEDUP_EVENT:') },
          error: recent_logs.count { |line| line.include?('ERROR:') }
        }
        
        log_types.each do |type, count|
          puts "      #{type.to_s.upcase}: #{count} 条"
        end
        
        # 显示最近的几条日志
        puts "\n   📝 最近的日志内容:"
        recent_logs.last(5).each_with_index do |log, index|
          timestamp = log.match(/\[([\d\-\s:]+)\]/)[1] rescue 'Unknown'
          content = log.split('] ')[2]&.strip rescue log.strip
          puts "      #{index + 1}. [#{timestamp}] #{content[0..80]}..."
        end
      else
        puts "      📝 日志文件为空"
      end
      
    else
      puts "   ❌ 日志文件不存在: #{@log_file}"
      puts "      这可能意味着监控功能尚未被使用"
    end
  end

  def provide_monitoring_recommendations
    puts "\n💡 基于简化UUID方案的监控建议:"
    
    puts "\n   🎯 核心监控指标:"
    puts "      1. 模板选择性能 (template_selection_time)"
    puts "      2. 去重查询性能 (deduplication_query_time)"
    puts "      3. 模板耗尽率 (template_exhaustion_rate)"
    puts "      4. UUID格式正确性"
    
    puts "\n   📊 建议的性能阈值:"
    puts "      - 模板选择: < 100ms"
    puts "      - 去重查询: < 50ms"
    puts "      - 消息生成: < 200ms"
    
    puts "\n   🔧 可以简化的监控:"
    puts "      - 移除n8n相关监控（如果不使用n8n）"
    puts "      - 移除version相关监控"
    puts "      - 简化告警机制"
    
    puts "\n   📈 监控价值评估:"
    if File.exist?(@log_file) && File.size(@log_file) > 0
      puts "      ✅ 监控组件有价值，建议保留并优化"
    else
      puts "      ⚠️  监控组件未被使用，可考虑简化或移除"
    end
    
    puts "\n   🛠️  建议的监控配置:"
    puts "      - 生产环境: 保留核心性能和错误监控"
    puts "      - 开发环境: 可以禁用或使用DEBUG级别"
    puts "      - 日志轮转: 建议设置日志文件大小限制"
  end
end

# 执行监控验证
begin
  verifier = MonitoringVerification.new
  verifier.run_verification
rescue => e
  puts "❌ 监控验证失败: #{e.message}"
end

puts "\n🏁 监控功能验证完成！"
