require 'test_helper'

class Bot::Tools::StatisticToolTest < ActiveSupport::TestCase
  def setup
    @tool = Bot::Tools::StatisticTool.new(model_class: Bot::Meeting)
  end

  test "should initialize with model class" do
    assert_equal Bot::Meeting, @tool.model_class
    assert_nil @tool.scope_chain
    assert_instance_of Bot::Transformers::StatisticDsl, @tool.transformer
  end

  test "should initialize with string model class" do
    tool = Bot::Tools::StatisticTool.new(model_class: 'Bot::Meeting')
    assert_equal Bot::Meeting, tool.model_class
  end

  test "should initialize with scope chain" do
    tool = Bot::Tools::StatisticTool.new(
      model_class: Bot::Meeting,
      scope_chain: ['finished']
    )
    assert_equal ['finished'], tool.scope_chain
  end

  test "should define collection_statistic function" do
    schemas = @tool.class.function_schemas.instance_variable_get(:@schemas)
    assert schemas.key?(:collection_statistic)

    function_def = schemas[:collection_statistic][:function]
    assert_equal "对数据集合进行统计分析", function_def[:description]

    properties = function_def[:parameters][:properties]
    assert properties.key?(:query)
    assert properties[:query]['required']
  end

  test "should define resource_statistic function" do
    schemas = @tool.class.function_schemas.instance_variable_get(:@schemas)
    assert schemas.key?(:resource_statistic)

    function_def = schemas[:resource_statistic][:function]
    assert_equal "对单个资源进行统计分析", function_def[:description]

    properties = function_def[:parameters][:properties]
    assert properties.key?(:resource_id)
    assert properties[:resource_id]['required']
    assert properties.key?(:query)
    assert properties[:query]['required']
  end

  test "collection_statistic should handle natural language query" do
    # 创建测试数据
    app = create(:app)
    user = create(:user, app: app)
    create_list(:bot_meeting, 3, user: user, app: app, state: 'finished')
    create_list(:bot_meeting, 2, user: user, app: app, state: 'processing')

    # Mock transformer
    mock_stat_condition = Com::Attr::Stat::Collection.new({
      items: [
        {
          key: 'meeting_stats',
          caculator: {
            type: 'caculation',
            caculations: [
              { name: '总会议数', method: 'count' }
            ]
          }
        }
      ]
    })

    @tool.transformer.expects(:transform).with("统计会议总数").returns(mock_stat_condition)

    result = @tool.collection_statistic(query: "统计会议总数")

    assert_equal 'success', result[:status]
    assert_equal "完成统计分析: 统计会议总数", result[:message]
  end

  test "collection_statistic should handle errors gracefully" do
    # Mock transformer to raise error
    @tool.transformer.expects(:transform).raises(StandardError.new("Transform failed"))

    result = @tool.collection_statistic(query: "invalid query")

    assert_equal 'error', result[:status]
    assert_equal({}, result[:data])
    assert_equal 0, result[:total_records]
    assert result[:message].include?('统计分析失败')
  end

  test "resource_statistic should find and analyze resource" do
    # 创建测试数据
    app = create(:app)
    user = create(:user, app: app)
    create_list(:bot_meeting, 2, user: user, app: app, state: 'finished')
    create_list(:bot_meeting, 1, user: user, app: app, state: 'processing')

    tool = Bot::Tools::StatisticTool.new(model_class: User)

    # Mock transformer
    mock_stat_condition = Com::Attr::Stat::Resource.new({
      refs: [
        {
          relations: ['bot_meetings'],
          item: {
            key: 'user_meeting_stats',
            caculator: {
              type: 'caculation',
              caculations: [
                { name: '用户总会议数', method: 'count' }
              ]
            }
          }
        }
      ]
    })

    tool.transformer.expects(:transform_for_resource).with("统计该用户的会议数量").returns(mock_stat_condition)

    result = tool.resource_statistic(
      resource_id: user.id,
      query: "统计该用户的会议数量"
    )

    assert_equal 'success', result[:status]
    assert result[:resource_found]
    assert_equal "完成资源统计分析: 统计该用户的会议数量", result[:message]
  end

  test "resource_statistic should handle missing resource" do
    tool = Bot::Tools::StatisticTool.new(model_class: User)

    result = tool.resource_statistic(
      resource_id: 99999, # 不存在的ID
      query: "统计该用户的会议数量"
    )

    assert_equal 'not_found', result[:status]
    refute result[:resource_found]
    assert result[:message].include?('无法找到ID为 99999')
  end

  test "should build base query without scope chain" do
    base_query = @tool.send(:build_base_query)
    assert_equal Bot::Meeting, base_query
  end

  test "should build base query with array scope chain" do
    tool = Bot::Tools::StatisticTool.new(
      model_class: Bot::Meeting,
      scope_chain: ['finished']
    )
    
    # Mock the finished scope
    Bot::Meeting.expects(:finished).returns(Bot::Meeting.where(state: 'finished'))
    
    base_query = tool.send(:build_base_query)
    # 验证 scope 被调用
  end

  test "should build base query with string scope chain" do
    tool = Bot::Tools::StatisticTool.new(
      model_class: Bot::Meeting,
      scope_chain: 'finished.recent'
    )
    
    # Mock the scope chain
    finished_scope = mock('finished_scope')
    finished_scope.expects(:recent).returns(Bot::Meeting.where(state: 'finished'))
    Bot::Meeting.expects(:finished).returns(finished_scope)
    
    base_query = tool.send(:build_base_query)
    # 验证 scope 链被正确调用
  end

  private

  def create(factory_name, **attributes)
    # 简单的工厂方法，实际项目中应该使用 FactoryBot
    case factory_name
    when :app
      App.create!(name: 'Test App', **attributes)
    when :user
      User.create!(name: 'Test User', email: '<EMAIL>', **attributes)
    when :bot_meeting
      Bot::Meeting.create!(
        name: 'Test Meeting',
        state: 'pending',
        **attributes
      )
    end
  end

  def create_list(factory_name, count, **attributes)
    count.times.map { create(factory_name, **attributes) }
  end
end
