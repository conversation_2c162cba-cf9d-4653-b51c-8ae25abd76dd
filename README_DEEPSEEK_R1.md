# DeepSeek-R1 模型集成说明

## 概述

DeepSeek-R1 是一个具有推理能力的大语言模型，它在生成最终答案之前，会先生成一个思考链（Chain of Thought, CoT）来提高响应的准确性。我们的API集成允许用户访问DeepSeek-R1生成的CoT内容，使他们能够查看、显示和提取这些内容。

## 特点

- DeepSeek-R1 模型会返回 `reasoning_content` 字段，包含模型的推理过程
- 我们的集成会自动将 `reasoning_content` 合并到JSON响应中，使得用户代码能够正常工作
- 支持 `response_format` 参数，可以指定返回格式为 `json_object`
- 自动处理Markdown代码块格式（```json ... ```）的响应内容

## 使用方法

### 基本用法

```ruby
llm = Bot::LlmFactory.create(:deepseek)
response = llm.chat(
  messages: [
    {
      role: 'system',
      content: '你是一个专业的内容生成专家，请根据用户的需求生成内容。请以JSON格式返回，格式为：{"contents": ["内容1", "内容2", "内容3"]}'
    },
    {
      role: 'user',
      content: '生成三条关于春节的祝福语',
    }
  ],
  response_format: { type: 'json_object' }
)

# 获取完整响应（包含reasoning字段）
full_response = response.chat_completion

# 解析JSON
parsed_response = JSON.parse(full_response)
contents = parsed_response['contents']
reasoning = parsed_response['reasoning'] # 获取推理过程

# 直接获取原始内容和推理过程
original_content = response.completion
reasoning_content = response.reasoning_content
```

### 注意事项

1. 当使用 `response_format: { type: 'json_object' }` 时，DeepSeek-R1 会返回符合JSON格式的响应
2. DeepSeek-R1 可能会将JSON响应包装在Markdown代码块中（```json ... ```），我们的集成会自动处理这种情况
3. 我们的集成会自动将 `reasoning_content` 合并到JSON响应中，添加 `reasoning` 字段
4. 如果响应不是JSON格式，或者解析失败，`chat_completion` 方法会返回原始的 `content`

## 技术细节

DeepSeek-R1 API 返回的响应格式如下：

```json
{
  "choices": [
    {
      "message": {
        "role": "assistant",
        "content": "```json\n{\n  \"contents\": [...]\n}\n```",
        "reasoning_content": "推理过程"
      }
    }
  ]
}
```

我们的集成会自动处理这种格式，包括：
1. 提取Markdown代码块中的JSON内容
2. 将 `reasoning_content` 合并到JSON响应中
3. 返回处理后的JSON字符串

## 故障排除

如果遇到问题，可以尝试以下方法：

1. 确保使用了正确的 API 密钥和 URI
2. 检查 `response_format` 参数是否正确设置为 `{ type: 'json_object' }`
3. 查看日志，了解API请求和响应的详细信息
4. 尝试直接使用 `response.completion` 和 `response.reasoning_content` 获取原始内容
5. 如果JSON解析失败，检查原始响应格式，可能需要手动处理Markdown代码块 