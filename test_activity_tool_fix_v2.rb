#!/usr/bin/env ruby

# 测试ActivityTool修复效果
require_relative 'config/environment'

puts "=== 测试ActivityTool修复效果 ==="

# 获取测试数据
app = App.first
if app.nil?
  puts "❌ 没有找到App，请先创建应用数据"
  exit 1
end

user = app.users.first
if user.nil?
  puts "❌ 没有找到User，请先创建用户数据"
  exit 1
end

agent = app.agents.first
if agent.nil?
  puts "❌ 没有找到Agent，请先创建Agent"
  exit 1
end

puts "✅ 测试环境准备完成"
puts "- App: #{app.name}"
puts "- User: #{user.name}"
puts "- Agent: #{agent.name}"
puts "- 素材总数: #{app.activities.count}"

# 检查Agent的instructions是否包含强制工具调用指令
puts "\n=== 检查Agent Instructions ==="
instructions = agent.build_instructions
if instructions.include?("强制工具调用")
  puts "✅ Agent instructions已包含强制工具调用指令"
else
  puts "❌ Agent instructions缺少强制工具调用指令"
  puts "当前instructions: #{instructions}"
end

# 检查ActivityTool Intent配置
puts "\n=== 检查ActivityTool Intent配置 ==="
activity_intent = Bot::Intent.find_by(tool_cname: 'Bot::Tools::ActivityTool')
if activity_intent
  puts "✅ 找到ActivityTool Intent: #{activity_intent.name}"
  puts "描述: #{activity_intent.description}"
  if activity_intent.tool_conf['examples']&.include?('最近半年里增加了多少素材？')
    puts "✅ Intent examples包含目标查询"
  else
    puts "❌ Intent examples不包含目标查询"
  end
else
  puts "❌ 未找到ActivityTool Intent"
end

# 测试Agent chat功能
puts "\n=== 测试Agent Chat功能 ==="

test_query = "最近半年里增加了多少素材？"
puts "测试查询: #{test_query}"

begin
  # 清除之前的日志，便于观察
  Rails.logger.info("=" * 50)
  Rails.logger.info("开始测试ActivityTool修复效果")
  Rails.logger.info("测试查询: #{test_query}")
  Rails.logger.info("=" * 50)
  
  # 调用Agent chat方法
  result = agent.chat(test_query, user: user)
  
  puts "\n--- 测试结果分析 ---"
  puts "返回类型: #{result.class}"
  
  if result.is_a?(Hash)
    puts "返回键: #{result.keys}"
    puts "消息存在: #{result[:message].present?}"
    puts "消息内容: #{result[:message]}" if result[:message]
    puts "artifact存在: #{result[:artifact].present?}"
    
    if result[:artifact]
      puts "\n--- Artifact详细信息 ---"
      puts "artifact类型: #{result[:artifact][:type]}"
      puts "artifact标题: #{result[:artifact][:title]}"
      puts "artifact副标题: #{result[:artifact][:subtitle]}"
      puts "payload存在: #{result[:artifact][:payload].present?}"
    end
  else
    puts "返回内容: #{result}"
  end
  
rescue => e
  puts "❌ 测试失败: #{e.message}"
  puts "错误堆栈: #{e.backtrace.first(5).join("\n")}"
end

# 检查Rails日志中的ActivityTool执行记录
puts "\n=== 检查Rails日志 ==="
log_file = Rails.root.join('log', 'development.log')
if File.exist?(log_file)
  # 读取最近的日志
  recent_logs = `tail -n 100 #{log_file}`
  
  if recent_logs.include?("ActivityTool: 开始查询")
    puts "✅ 发现ActivityTool执行日志"
    activity_logs = recent_logs.split("\n").select { |line| line.include?("ActivityTool") }
    activity_logs.each { |log| puts "  #{log}" }
  else
    puts "❌ 未发现ActivityTool执行日志"
    puts "最近的日志片段:"
    recent_logs.split("\n").last(10).each { |log| puts "  #{log}" }
  end
else
  puts "❌ 日志文件不存在: #{log_file}"
end

# 检查数据库中的最新artifacts
puts "\n=== 检查数据库中的Artifacts ==="
recent_artifacts = Bot::Artifact.where(tool_function: 'query_activities')
                                .order(created_at: :desc)
                                .limit(3)

if recent_artifacts.any?
  puts "✅ 找到#{recent_artifacts.count}个相关artifacts"
  recent_artifacts.each_with_index do |artifact, index|
    puts "Artifact #{index + 1}:"
    puts "  ID: #{artifact.id}"
    puts "  Type: #{artifact.artifact_type}"
    puts "  创建时间: #{artifact.created_at}"
    puts "  Meta存在: #{artifact.meta.present?}"
  end
else
  puts "❌ 未找到相关artifacts"
end

puts "\n=== 修复效果总结 ==="

# 判断修复是否成功
success_indicators = []
success_indicators << "Agent instructions包含强制工具调用指令" if instructions.include?("强制工具调用")
success_indicators << "ActivityTool Intent配置正确" if activity_intent&.tool_conf&.dig('examples')&.include?('最近半年里增加了多少素材？')
success_indicators << "发现ActivityTool执行日志" if recent_logs&.include?("ActivityTool: 开始查询")
success_indicators << "找到相关artifacts" if recent_artifacts.any?

if success_indicators.length >= 3
  puts "✅ 修复效果良好！"
  puts "成功指标: #{success_indicators.join(', ')}"
  puts "\n建议："
  puts "1. 在前端Bot对话界面测试查询'最近半年里增加了多少素材？'"
  puts "2. 检查是否显示可交互的素材列表组件"
  puts "3. 验证用户体验是否符合预期（文本回复+可点击组件）"
else
  puts "❌ 修复效果不理想"
  puts "成功指标: #{success_indicators.join(', ')}"
  puts "缺失指标: #{4 - success_indicators.length}个"
  puts "\n建议："
  puts "1. 检查Agent instructions是否正确更新"
  puts "2. 检查ActivityTool Intent配置是否正确"
  puts "3. 考虑实施方案2（动态tool_choice强制调用方案）"
end

puts "\n测试完成！"
