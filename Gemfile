source 'https://gems.ruby-china.com'
# A Ruby gem to load environment variables from `.env`.
# https://github.com/bkeepers/dotenv
# If you use gems that require environment variables to be set before they are loaded, then list dotenv-rails in the Gemfile before those other gems and require dotenv/rails-now.
gem 'dotenv-rails', require: 'dotenv/rails-now'
git_source(:tallty) { |repo| "******************:#{repo}.git" }

git_source(:github) { |repo| "https://github.com/#{repo}.git" }

# Specify your gem's dependencies in rails_serve.gemspec.
gemspec

group :development do
  gem 'sqlite3'
end

# To use a debugger
# gem 'byebug', group: [:development, :test]
gem 'pg'
gem 'simple_controller', git: 'https://git.tallty.com/open-source/simple_controller.git'
gem 'rails_com', tallty: 'ta-rails/rails_com'
gem 'rails_bpm', tallty: 'ta-rails/rails_bpm'
gem 'rails_notify', tallty: 'ta-rails/rails_notify'
gem 'rails_state', tallty: 'ta-rails/rails_state'
gem 'rails_data', tallty: 'ta-rails/rails_data'
gem 'rails_action_core', tallty: 'ta-rails/rails_action_core'
gem 'rails_res', tallty: 'ta-rails/rails_res'
gem 'rails_tofu', tallty: 'ta-rails/rails_tofu'
gem 'rails_permit', tallty: 'ta-rails/rails_permit'
gem 'rails_reg', tallty: 'ta-rails/rails_reg'


group :development, :test do
  gem 'pry-byebug'
  gem 'rspec-rails-swagger', git: 'http://git.tallty.com/open-source/rspec-rails-swagger.git'
  gem 'rspec-rails'
  gem 'shoulda-matchers'
  gem 'factory_bot_rails'
end
