source 'https://gems.ruby-china.com'
# gsub_file GEMFILE, 'https://rubygems.org', 'https://mirrors.ustc.edu.cn/rubygems/'
# inject_into_file GEMFILE, after: "source "https://mirrors.ustc.edu.cn/rubygems/"" do <<-GEM_COMMENT

# A Ruby gem to load environment variables from `.env`.
# https://github.com/bkeepers/dotenv
# If you use gems that require environment variables to be set before they are loaded, then list dotenv-rails in the Gemfile before those other gems and require dotenv/rails-now.
gem 'dotenv-rails', require: 'dotenv/load'
git_source(:tallty) { |repo| "******************:#{repo}.git" }

git_source(:github) { |repo| "https://github.com/#{repo}.git" }

# Specify your gem's dependencies in rails_bot.gemspec.
gemspec

gem 'puma'

gem 'sqlite3'

gem 'sprockets-rails'

# Start debugger with binding.b [https://github.com/ruby/debug]
# gem "debug", ">= 1.0.0"
gem 'pg'
gem 'simple_controller', git: 'https://git.tallty.com/open-source/simple_controller.git'
gem 'rails_com', tallty: 'ta-rails/rails_com'
gem 'rails_data', tallty: 'ta-rails/rails_data'
gem 'rails_action_core', tallty: 'ta-rails/rails_action_core'
gem 'rails_res', tallty: 'ta-rails/rails_res'
gem 'rails_tofu', tallty: 'ta-rails/rails_tofu'
gem 'rails_permit', tallty: 'ta-rails/rails_permit'
gem 'rails_bpm', tallty: 'ta-rails/rails_bpm'
gem 'rails_notify', tallty: 'ta-rails/rails_notify'
gem 'ruby-openai'
gem 'faraday'
gem 'sequel'
gem 'pgvector'
gem 'pdf-reader'

group :development, :test do
  gem 'pry-byebug'
  gem 'rspec-rails-swagger', git: 'http://git.tallty.com/open-source/rspec-rails-swagger.git'
  gem 'rspec-rails'
  gem 'shoulda-matchers'
  gem 'factory_bot_rails'
end
