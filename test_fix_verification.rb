#!/usr/bin/env ruby

# 验证ActivityTool和OrgTool修复效果
puts "=== 验证Bot工具修复效果 ==="

# 模拟Rails Logger
class MockLogger
  def info(msg)
    puts "[INFO] #{msg}"
  end

  def error(msg)
    puts "[ERROR] #{msg}"
  end
end

# 模拟Rails
module Rails
  def self.logger
    @logger ||= MockLogger.new
  end
end

# 模拟Bot::Tool基类
module Bot
  class Tool
    def initialize
    end
  end
end

# 模拟Time.current和时间方法
class Time
  def self.current
    Time.now
  end
end

class Integer
  def months
    self * 30 * 24 * 60 * 60  # 简化的月份计算
  end

  def ago
    Time.current - self
  end
end

# 模拟present?方法
class Object
  def present?
    !blank?
  end

  def blank?
    respond_to?(:empty?) ? empty? : !self
  end
end

class NilClass
  def present?
    false
  end

  def blank?
    true
  end
end

# 模拟数据库关系
class MockActivitiesRelation
  def initialize(count = 602)
    @count = count
  end

  def count
    @count
  end

  def where(*args)
    self
  end

  def joins(*args)
    self
  end

  def distinct
    self
  end
end

class MockUsersRelation
  def initialize(count = 3)
    @count = count
  end

  def count
    @count
  end

  def where(*args)
    self
  end

  def joins(*args)
    self
  end

  def distinct
    self
  end
end

# 模拟App
class MockApp
  def activities
    MockActivitiesRelation.new(602)
  end

  def users
    MockUsersRelation.new(3)
  end
end

# 模拟User
class MockUser
  def app
    MockApp.new
  end
end

# 模拟Conversation
class MockConversation
  def user
    MockUser.new
  end
end

# 模拟Bot::Current
module Bot
  class Current
    @@conversation = nil

    def self.conversation
      @@conversation
    end

    def self.conversation=(conv)
      @@conversation = conv
    end
  end
end

# 设置模拟环境
Bot::Current.conversation = MockConversation.new

# 简化的ActivityTool实现（基于修复后的逻辑）
module Bot
  module Tools
    class ActivityTool < Bot::Tool
      def query_activities(query:)
        Rails.logger.info("ActivityTool: 查询='#{query}'")

        app = Bot::Current.conversation&.user&.app
        return { error: "用户未登录", total_count: 0 } unless app

        # 解析查询条件
        conditions = parse_query_conditions(query)

        # 模拟查询
        count = app.activities.count
        Rails.logger.info("ActivityTool: 查询结果数量=#{count}")

        # 构建消息
        message = count > 0 ? build_result_message(conditions, count) : build_no_result_message(conditions)

        # 构建基础响应
        response = {
          total_count: count,
          message: message,
          query_conditions: conditions
        }

        # 总是创建artifact，让用户可以查看详细的素材列表
        # 这样用户在查询素材数量时也能点击查看具体列表，即使结果为0也能看到查询条件
        artifact_data = create_activity_list_artifact(conditions, count)
        response[:artifact] = artifact_data if artifact_data

        Rails.logger.info("ActivityTool: 创建artifact - #{artifact_data.present? ? '成功' : '失败'}")

        response
      rescue => e
        Rails.logger.error("ActivityTool 查询失败: #{e.message}")
        {
          error: "查询失败: #{e.message}",
          total_count: 0
        }
      end

      private

      def parse_query_conditions(query)
        conditions = {}

        # 解析时间范围
        if query.include?("最近半年")
          conditions[:time_range] = { start_date: 6.months.ago }
        elsif query.include?("今天")
          conditions[:time_range] = { start_date: Time.current }
        elsif query.include?("本月")
          conditions[:time_range] = { start_date: Time.current }
        end

        # 解析关键词
        if match = query.match(/包含['"]([^'"]+)['"]/)
          conditions[:name_keywords] = match[1]
        end

        # 解析状态
        if query.include?("已发布")
          conditions[:status] = "published"
        elsif query.include?("草稿")
          conditions[:status] = "draft"
        end

        conditions
      end

      def build_result_message(conditions, count)
        parts = []

        if conditions[:time_range]
          parts << "最近半年"
        end

        if conditions[:name_keywords]
          parts << "包含'#{conditions[:name_keywords]}'"
        end

        if conditions[:status] == "published"
          parts << "已发布"
        elsif conditions[:status] == "draft"
          parts << "草稿"
        end

        condition_text = parts.any? ? "#{parts.join('、')}的" : ""
        "在#{condition_text}素材中，共找到#{count}个素材。如果您有更具体的需求或想了解其他时间段的数据，请告诉我。"
      end

      def build_no_result_message(conditions)
        parts = []

        if conditions[:time_range]
          parts << "指定时间范围"
        end

        if conditions[:name_keywords]
          parts << "包含'#{conditions[:name_keywords]}'"
        end

        condition_text = parts.any? ? parts.join('、') + '的' : ''
        "未找到#{condition_text}素材"
      end

      def create_activity_list_artifact(conditions, count)
        # 构建查询参数
        params = { q: {} }

        if conditions[:name_keywords].present?
          params[:q][:title_cont] = conditions[:name_keywords]
        end

        if conditions[:status].present?
          params[:q][:status_eq] = conditions[:status]
        end

        if conditions[:time_range].present?
          time_range = conditions[:time_range]
          if time_range[:start_date]
            params[:q][:created_at_gteq] = time_range[:start_date]
          end
        end

        # 返回artifact数据
        {
          type: 'Bot::ActivityListArtifact',
          title: "素材列表",
          subtitle: "共#{count}个",
          payload: {
            title: "素材列表",
            subtitle: "共#{count}个",
            params: params,
            total_count: count,
            query_conditions: conditions
          }
        }
      rescue => e
        Rails.logger.error("创建素材列表artifact失败: #{e.message}")
        nil
      end
    end
  end
end

# 测试修复效果
puts "\n--- 测试ActivityTool修复效果 ---"

tool = Bot::Tools::ActivityTool.new

# 测试1：用户实际查询场景
puts "\n1. 测试用户查询：'最近半年里增加了多少素材？'"
result1 = tool.query_activities(query: "最近半年里增加了多少素材？")

puts "结果验证："
puts "- 返回数量: #{result1[:total_count]}"
puts "- 返回消息: #{result1[:message]}"
puts "- artifact存在: #{result1[:artifact].present?}"
puts "- artifact类型: #{result1[:artifact][:type]}" if result1[:artifact]
puts "- 可点击查看列表: #{result1[:artifact] && result1[:artifact][:payload][:params].present?}"

# 测试2：简单数量查询
puts "\n2. 测试简单查询：'素材有多少个？'"
result2 = tool.query_activities(query: "素材有多少个？")

puts "结果验证："
puts "- artifact存在: #{result2[:artifact].present?}"
puts "- artifact类型: #{result2[:artifact][:type]}" if result2[:artifact]

# 测试3：关键词搜索
puts "\n3. 测试关键词搜索：'搜索包含\"通知\"的素材'"
result3 = tool.query_activities(query: "搜索包含'通知'的素材")

puts "结果验证："
puts "- artifact存在: #{result3[:artifact].present?}"
puts "- 查询参数正确: #{result3[:artifact][:payload][:params][:q][:title_cont] == '通知'}" if result3[:artifact]

# 测试4：无结果查询
puts "\n4. 测试无结果查询（模拟0个结果）"
# 模拟无结果
Bot::Current.conversation.user.app.instance_eval do
  def activities
    MockActivitiesRelation.new(0)
  end
end

result4 = tool.query_activities(query: "不存在的素材")

puts "结果验证："
puts "- 返回数量: #{result4[:total_count]}"
puts "- artifact仍然存在: #{result4[:artifact].present?}"
puts "- artifact类型: #{result4[:artifact][:type]}" if result4[:artifact]

puts "\n=== 修复验证总结 ==="

all_have_artifacts = [result1, result2, result3, result4].all? { |r| r[:artifact].present? }

if all_have_artifacts
  puts "✅ 修复成功！"
  puts "   - 所有查询都创建了artifact组件"
  puts "   - 用户可以点击查看详细的素材列表"
  puts "   - 即使查询结果为0也会显示组件"
else
  puts "❌ 修复失败！"
  puts "   - 仍有查询没有创建artifact组件"
end

puts "\n=== 用户体验验证 ==="
puts "用户查询'最近半年里增加了多少素材？'的完整体验："
puts "1. 文本回复: #{result1[:message]}"
puts "2. 显示组件: #{result1[:artifact] ? '✅ 是' : '❌ 否'}"
puts "3. 组件标题: #{result1[:artifact][:title]}" if result1[:artifact]
puts "4. 组件副标题: #{result1[:artifact][:subtitle]}" if result1[:artifact]
puts "5. 可点击查看详细列表: #{result1[:artifact] && result1[:artifact][:payload][:params].present? ? '✅ 是' : '❌ 否'}"

puts "\n测试完成！"
