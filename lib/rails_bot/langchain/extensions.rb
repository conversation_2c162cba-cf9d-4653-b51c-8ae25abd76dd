# frozen_string_literal: true

module RailsBot
  module Langchain
    module Extensions
      module AssistantLLMAdapters
        # 基础适配器功能
        module Base
          private

          def build_tools(tools)
            tools.map { |tool| 
              tool.respond_to?(:function_schemas) ? tool.function_schemas : tool.class.function_schemas
            }.flatten
          end

          def extract_tool_call_args(tool_call:)
            tool_call_args = super
            parse_intent_from_args(tool_call_args)
          end

          def parse_intent_from_args(tool_call_args)
            tool_call_id, tool_name, method_name, tool_arguments = tool_call_args

            if tool_name =~ /^(.+?)__intent_(\d+)$/
              tool_name = $1
              intent_id = $2
              [tool_call_id, tool_name, method_name, tool_arguments, intent_id]
            else
              tool_call_args
            end
          end
        end

        # OpenAI 格式适配器
        module OpenAI
          def extract_tool_call_args(tool_call:)
            tool_call_id = tool_call.dig("id")
            full_function_name = tool_call.dig("function", "name")

            if full_function_name =~ /^(.+?)__intent_(\d+)__(.+)$/
              tool_name = $1
              intent_id = $2
              method_name = $3

              tool_arguments = tool_call.dig("function", "arguments")
              tool_arguments = if tool_arguments.is_a?(Hash)
                                 ::Langchain::Utils::HashTransformer.symbolize_keys(tool_arguments)
                               else
                                 JSON.parse(tool_arguments, symbolize_names: true)
                               end

              [tool_call_id, tool_name, method_name, tool_arguments, intent_id]
            else
              super
            end
          end

          def build_tools(tools)
            tools.map { |tool| 
              schemas = tool.respond_to?(:function_schemas) ? tool.function_schemas : tool.class.function_schemas
              schemas.to_openai_format
            }.flatten
          end
        end

        # Anthropic 格式适配器
        module Anthropic
          def extract_tool_call_args(tool_call:)
            tool_call_id = tool_call.dig("id")
            full_function_name = tool_call.dig("name")

            if full_function_name =~ /^(.+?)__intent_(\d+)__(.+)$/
              tool_name = $1
              intent_id = $2
              method_name = $3
              tool_arguments = tool_call.dig("input").transform_keys(&:to_sym)

              [tool_call_id, tool_name, method_name, tool_arguments, intent_id]
            else
              super
            end
          end

          def build_tools(tools)
            tools.map { |tool| 
              schemas = tool.respond_to?(:function_schemas) ? tool.function_schemas : tool.class.function_schemas
              schemas.to_anthropic_format
            }.flatten
          end
        end

        # Google Gemini 格式适配器
        module GoogleGemini
          def extract_tool_call_args(tool_call:)
            full_function_name = tool_call.dig("functionCall", "name")

            if full_function_name =~ /^(.+?)__intent_(\d+)__(.+)$/
              tool_call_id = full_function_name # Gemini uses function name as ID
              tool_name = $1
              intent_id = $2
              method_name = $3
              tool_arguments = tool_call.dig("functionCall", "args")
                .transform_keys(&:to_sym)

              [tool_call_id, tool_name, method_name, tool_arguments, intent_id]
            else
              super
            end
          end

          def build_tools(tools)
            tools.map { |tool| 
              schemas = tool.respond_to?(:function_schemas) ? tool.function_schemas : tool.class.function_schemas
              schemas.to_google_gemini_format
            }.flatten
          end
        end

        # MistralAI 使用 OpenAI 格式
        module MistralAI
          def extract_tool_call_args(tool_call:)
            tool_call_id = tool_call.dig("id")
            full_function_name = tool_call.dig("function", "name")

            if full_function_name =~ /^(.+?)__intent_(\d+)__(.+)$/
              tool_name = $1
              intent_id = $2
              method_name = $3
              tool_arguments = tool_call.dig("function", "arguments")
              tool_arguments = if tool_arguments.is_a?(Hash)
                                 ::Langchain::Utils::HashTransformer.symbolize_keys(tool_arguments)
                               else
                                 JSON.parse(tool_arguments, symbolize_names: true)
                               end

              [tool_call_id, tool_name, method_name, tool_arguments, intent_id]
            else
              super
            end
          end

          def build_tools(tools)
            tools.map { |tool| 
              schemas = tool.respond_to?(:function_schemas) ? tool.function_schemas : tool.class.function_schemas
              schemas.to_openai_format
            }.flatten
          end
        end

        # Ollama 使用 OpenAI 格式
        module Ollama
          def extract_tool_call_args(tool_call:)
            tool_call_id = tool_call.dig("id")
            full_function_name = tool_call.dig("function", "name")

            if full_function_name =~ /^(.+?)__intent_(\d+)__(.+)$/
              tool_name = $1
              intent_id = $2
              method_name = $3
              tool_arguments = tool_call.dig("function", "arguments")
              tool_arguments = if tool_arguments.is_a?(Hash)
                                 ::Langchain::Utils::HashTransformer.symbolize_keys(tool_arguments)
                               else
                                 JSON.parse(tool_arguments, symbolize_names: true)
                               end

              [tool_call_id, tool_name, method_name, tool_arguments, intent_id]
            else
              super
            end
          end

          def build_tools(tools)
            tools.map { |tool| 
              schemas = tool.respond_to?(:function_schemas) ? tool.function_schemas : tool.class.function_schemas
              schemas.to_openai_format
            }.flatten
          end
        end

        # AWS Bedrock Anthropic 使用 Anthropic 格式
        module AwsBedrockAnthropic
          def extract_tool_call_args(tool_call:)
            tool_call_id = tool_call.dig("id")
            full_function_name = tool_call.dig("name")

            if full_function_name =~ /^(.+?)__intent_(\d+)__(.+)$/
              tool_name = $1
              intent_id = $2
              method_name = $3
              tool_arguments = tool_call.dig("input").transform_keys(&:to_sym)

              [tool_call_id, tool_name, method_name, tool_arguments, intent_id]
            else
              super
            end
          end

          def build_tools(tools)
            tools.map { |tool| 
              schemas = tool.respond_to?(:function_schemas) ? tool.function_schemas : tool.class.function_schemas
              schemas.to_anthropic_format
            }.flatten
          end
        end
      end

      # 扩展初始化
      def self.init
        # 扩展 Langchain 的适配器
        ::Langchain::Assistant::LLM::Adapters::Base.prepend(AssistantLLMAdapters::Base)
        ::Langchain::Assistant::LLM::Adapters::OpenAI.prepend(AssistantLLMAdapters::OpenAI)
        ::Langchain::Assistant::LLM::Adapters::Anthropic.prepend(AssistantLLMAdapters::Anthropic)
        ::Langchain::Assistant::LLM::Adapters::GoogleGemini.prepend(AssistantLLMAdapters::GoogleGemini)
        ::Langchain::Assistant::LLM::Adapters::MistralAI.prepend(AssistantLLMAdapters::MistralAI)
        ::Langchain::Assistant::LLM::Adapters::Ollama.prepend(AssistantLLMAdapters::Ollama)
        ::Langchain::Assistant::LLM::Adapters::AwsBedrockAnthropic.prepend(AssistantLLMAdapters::AwsBedrockAnthropic)
      end
    end
  end
end

# 初始化扩展
RailsBot::Langchain::Extensions.init
