module Langchain
  module LLM
    class QwenResponse < OpenAIResponse
      def model
        raw_response['model']
      end

      def created_at
        return unless raw_response.dig('created')

        Time.at(raw_response.dig('created'))
      end

      def completion
        completions&.dig(0, 'message', 'content')
      end

      def role
        completions&.dig(0, 'message', 'role')
      end

      def chat_completion
        completion
      end

      def tool_calls
        chat_completions.dig(0, 'message', 'tool_calls') || []
      end

      def completions
        raw_response['choices']
      end

      def chat_completions
        completions
      end

      def prompt_tokens
        raw_response.dig('usage', 'prompt_tokens')
      end

      def completion_tokens
        raw_response.dig('usage', 'completion_tokens')
      end

      def total_tokens
        raw_response.dig('usage', 'total_tokens')
      end
    end
  end
end
