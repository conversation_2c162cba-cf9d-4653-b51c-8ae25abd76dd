module Langchain
  module LLM
    class DeepseekResponse < OpenAIResponse
      def model
        raw_response['model']
      end

      def created_at
        return unless raw_response.dig('created')

        Time.at(raw_response.dig('created'))
      end

      def completion
        completions&.dig(0, 'message', 'content')
      end

      def role
        completions&.dig(0, 'message', 'role')
      end

      def chat_completion
        # 获取原始内容和推理内容
        content_str = completion
        reasoning = reasoning_content

        # 处理Markdown代码块格式
        if content_str && content_str.include?('```json')
          # 提取JSON内容
          json_match = content_str.match(/```json\s*(.*?)\s*```/m)
          if json_match && json_match[1]
            content_str = json_match[1].strip
          end
        end

        # 处理reasoning_content
        if reasoning && !reasoning.empty?
          begin
            if content_str && content_str.strip.start_with?('{') && content_str.strip.end_with?('}')
              content_json = JSON.parse(content_str)
              # 添加reasoning字段到JSON中
              content_json['reasoning'] = reasoning
              return content_json.to_json
            end
          rescue JSON::ParserError
            Langchain.logger.warn "Failed to parse JSON from deepseek-r1 response: #{content_str}"
          end
        end
        
        # 如果没有reasoning_content或者不是JSON格式，返回处理后的content
        content_str || completion
      end

      def reasoning_content
        completions&.dig(0, 'message', 'reasoning_content')
      end

      def tool_calls
        chat_completions.dig(0, 'message', 'tool_calls') || []
      end

      def completions
        raw_response['choices']
      end

      def chat_completions
        completions
      end

      def prompt_tokens
        raw_response.dig('usage', 'prompt_tokens')
      end

      def completion_tokens
        raw_response.dig('usage', 'completion_tokens')
      end

      def total_tokens
        raw_response.dig('usage', 'total_tokens')
      end
    end
  end
end
