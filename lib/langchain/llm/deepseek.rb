module Langchain
  module LLM
    class Deepseek < Base
      attr_reader :client, :defaults

      # Initialize a Deepseek LLM instance
      #
      # @param api_key [String] The API key to use
      # @param llm_options [Hash] Options for the LLM client
      # @param default_options [Hash] Default options for API calls
      def initialize(api_key:, llm_options: {}, default_options: {})
        @client = ::OpenAI::Client.new(
          access_token: api_key,
          uri_base: llm_options[:uri_base] || 'https://api.deepseek.com/v1',
          request_timeout: llm_options[:request_timeout] || 120,
        )

        @defaults = {
          chat_model: 'deepseek-chat',
          completion_model: 'deepseek-chat',
          embedding_model: 'text-embedding-v1',
          dimensions: 1536,
          temperature: 0.0,
          max_tokens: 2048,
          response_format: { type: 'text' },
        }.merge(default_options)
      end

      # Generate a chat completion
      #
      # @param messages [Array<Hash>] The messages to generate a completion for
      # @param model [String] The model to use
      # @param temperature [Float] The temperature to use
      # @param tools [Array] The tools to use
      # @param tool_choice [String] The tool choice to use
      # @param parallel_tool_calls [<PERSON><PERSON><PERSON>] Whether to make parallel tool calls
      # @return [Langchain::LLM::DeepseekResponse] Response object
      def chat(
        messages:,
        model: defaults[:chat_model],
        temperature: defaults[:temperature],
        tools: [],
        tool_choice: 'none',
        parallel_tool_calls: false,
        **params
      )
        # 添加详细的调试日志
        Langchain.logger.debug "Deepseek chat method called with:"
        Langchain.logger.debug "Messages: #{messages.inspect}"
        Langchain.logger.debug "Tools before processing: #{tools.inspect}"
        Langchain.logger.debug "Tool choice: #{tool_choice}"
        Langchain.logger.debug "Model: #{model}"

        # 处理response_format参数
        response_format = params[:response_format] || defaults[:response_format]
        
        request_params = {
          model: model,
          messages: messages,
          temperature: temperature,
          max_tokens: defaults[:max_tokens],
          frequency_penalty: 0,
          presence_penalty: 0,
          response_format: response_format,
          stream: false,
        }.merge(params)

        if tools.any?
          request_params[:tools] = tools
          request_params[:tool_choice] = tool_choice
          request_params[:parallel_tool_calls] = parallel_tool_calls
          # 添加最终请求参数的详细日志
          Langchain.logger.debug "Final request params to Deepseek: #{JSON.pretty_generate(request_params)}"
        end

        # 添加实际发送请求前的日志
        Langchain.logger.debug "Sending request to Deepseek API..."
        response = client.chat(parameters: request_params)
        # 添加响应日志
        Langchain.logger.debug "Received response from Deepseek API: #{response.inspect}"
        DeepseekResponse.new(response)
      rescue => e
        # 添加错误日志
        Langchain.logger.error "Deepseek API error: #{e.class} - #{e.message}"
        Langchain.logger.error "Request params were: #{JSON.pretty_generate(request_params)}"
        raise
      end

      # Generate a completion for a given prompt
      #
      # @param prompt [String] The prompt to generate a completion for
      # @param model [String] The model to use
      # @param temperature [Float] The temperature to use
      # @return [Langchain::LLM::DeepseekResponse] Response object
      def complete(
        prompt:,
        **params
      )
        Langchain.logger.warn 'DEPRECATED: `Langchain::LLM::OpenAI#complete` is deprecated, and will be removed in the next major version. Use `Langchain::LLM::OpenAI#chat` instead.'

        params[:stop] = params.delete(:stop_sequences) if params[:stop_sequences]
        # Should we still accept the `messages: []` parameter here?
        messages = [{ role: 'user', content: prompt }]
        chat(messages: messages, **params)
      end

      # Generate an embedding for a given text
      #
      # @param text [String] The text to generate an embedding for
      # @param model [String] The model to use
      # @return [Langchain::LLM::DeepseekResponse] Response object
      def embed(
        text:,
        model: defaults[:embedding_model],
        dimensions: defaults[:dimensions],
        **params
      )
        response = client.embeddings(
          parameters: {
            model: model,
            input: text,
            dimensions: dimensions,
          }.merge(params),
        )
        DeepseekResponse.new(response)
      end

      # Generate a summary for a given text
      #
      # @param text [String] The text to generate a summary for
      # @return [String] The summary
      def summarize(text:)
        response = chat(
          messages: [
            { role: 'system', content: 'Please provide a concise summary of the following text.' },
            { role: 'user', content: text },
          ],
          temperature: 0.0,
        )
        response.completion
      end
    end
  end
end
