module Langchain
  class Assistant
    module LLM
      module Adapters
        class Qwen < OpenAI
          # Build the chat parameters for the Qwen LLM
          #
          # @param messages [Array] The messages
          # @param instructions [String] The system instructions
          # @param tools [Array] The tools to use
          # @param tool_choice [String] The tool choice
          # @param parallel_tool_calls [<PERSON><PERSON><PERSON>] Whether to make parallel tool calls
          # @return [Hash] The chat parameters
          def build_chat_params(
            messages:,
            instructions:,
            tools:,
            tool_choice:,
            parallel_tool_calls:
          )
            params = { messages: messages }
            if tools.any?
              params[:tools] = build_tools(tools)
              params[:tool_choice] = build_tool_choice(tool_choice)
              params[:parallel_tool_calls] = parallel_tool_calls
            end

            params
          end

          # Build a Qwen message
          #
          # @param role [String] The role of the message
          # @param content [String] The content of the message
          # @param image_url [String] The image URL
          # @param tool_calls [Array] The tool calls
          # @param tool_call_id [String] The tool call ID
          # @return [Messages::QwenMessage] The Qwen message
          def build_message(role:, content: nil, image_url: nil, tool_calls: [], tool_call_id: nil)
            Messages::QwenMessage.new(
              role: role,
              content: content,
              image_url: image_url,
              tool_calls: tool_calls,
              tool_call_id: tool_call_id,
            )
          end

          # Extract tool call arguments from a tool call
          #
          # @param tool_call [Hash] The tool call
          # @return [Array] The tool call arguments
          def extract_tool_call_args(tool_call:)
            tool_call_id = tool_call.dig('id')
            full_function_name = tool_call.dig('function', 'name')

            if full_function_name =~ /^(.+?)__intent_(\d+)__(.+)$/
              tool_name = ::Regexp.last_match(1)
              intent_id = ::Regexp.last_match(2)
              method_name = ::Regexp.last_match(3)
              tool_arguments = tool_call.dig('function', 'arguments')
              tool_arguments = if tool_arguments.is_a?(Hash)
                                 ::Langchain::Utils::HashTransformer.symbolize_keys(tool_arguments)
                               else
                                 JSON.parse(tool_arguments, symbolize_names: true)
                               end

              [tool_call_id, tool_name, method_name, tool_arguments, intent_id]
            else
              super
            end
          end

          private

          # Build the tools for the Qwen LLM
          #
          # @param tools [Array] The tools to use
          # @return [Array] The tools
          def build_tools(tools)
            tools.map do |tool|
              schemas = tool.respond_to?(:function_schemas) ? tool.function_schemas : tool.class.function_schemas
              schemas.to_openai_format
            end.flatten
          end

          # Build the tool choice for the Qwen LLM
          #
          # @param tool_choice [String] The tool choice
          # @return [Hash] The tool choice
          def build_tool_choice(tool_choice)
            case tool_choice
            when 'none'
              'none'
            when 'auto'
              'auto'
            else
              {
                type: 'function',
                function: { name: tool_choice },
              }
            end
          end
        end
      end
    end
  end
end
