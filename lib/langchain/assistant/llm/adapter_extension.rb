module Langchain
  class Assistant
    module LLM
      module AdapterExtension
        def self.prepended(base)
          class << base
            prepend ClassMethods
          end
        end

        module ClassMethods
          def build(llm)
            if llm.is_a?(Langchain::LLM::Qwen)
              LLM::Adapters::Qwen.new
            elsif llm.is_a?(Langchain::LLM::Deepseek)
              LLM::Adapters::Deepseek.new
            else
              super
            end
          end
        end
      end

      class Adapter
        prepend AdapterExtension
      end
    end
  end
end
