module Langchain
  class Assistant
    module Messages
      class DeepseekMessage < OpenAIMessage
        # Initialize a new Deepseek message
        #
        # @param role [String] The role of the message
        # @param content [String] The content of the message
        # @param image_url [String] The URL of the image to include in the message
        # @param tool_calls [Array<Hash>] The tool calls made in the message
        # @param tool_call_id [String] The ID of the tool call
        def initialize(role:, content: nil, image_url: nil, tool_calls: [], tool_call_id: nil)
          @role = role
          @content = content
          @image_url = image_url
          @tool_calls = tool_calls
          @tool_call_id = tool_call_id
        end

        # Convert the message to a Deepseek API-compatible hash
        #
        # @return [Hash] The message as a Deepseek API-compatible hash
        def to_h
          # Deepseek的角色映射
          role = case @role
                 when 'system' then 'system'
                 when 'user' then 'user'
                 when 'assistant' then 'assistant'
                 when 'tool' then 'tool'
                 else
                   raise ArgumentError, "Unsupported role: #{@role}"
                 end

          message = { role: role }

          if @content
            message[:content] = if @image_url
                                  [
                                    { type: 'text', text: @content },
                                    { type: 'image_url', image_url: { url: @image_url } },
                                  ]
                                else
                                  @content
                                end
          end

          message[:tool_call_id] = @tool_call_id if @tool_call_id

          message[:tool_calls] = @tool_calls if @tool_calls.any?

          message
        end

        # Check if the message came from an LLM
        #
        # @return [Boolean] true/false whether this message was produced by an LLM
        def llm?
          @role == 'assistant'
        end

        # Check if the message came from an LLM
        #
        # @return [Boolean] true/false whether this message was produced by an LLM
        def assistant?
          llm?
        end

        # Check if the message are system instructions
        #
        # @return [Boolean] true/false whether this message contains system instructions
        def system?
          @role == 'system'
        end
      end
    end
  end
end
