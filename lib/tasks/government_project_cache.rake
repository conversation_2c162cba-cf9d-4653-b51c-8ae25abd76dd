# frozen_string_literal: true

namespace :government_project do
  namespace :cache do
    desc "显示政府项目缓存统计信息"
    task stats: :environment do
      puts "🔍 政府项目缓存统计信息"
      puts "=" * 50
      
      stats = Irs::GovernmentProject.cache_statistics
      
      puts "📊 总体统计:"
      puts "  总记录数: #{stats[:total_records]}"
      puts "  成功记录数: #{stats[:successful_records]}"
      puts "  失败记录数: #{stats[:failed_records]}"
      puts "  总项目数: #{stats[:total_projects]}"
      
      if stats[:latest_cache]
        puts "\n📅 最新缓存:"
        latest = stats[:latest_cache]
        puts "  日期: #{latest[:date]}"
        puts "  项目数量: #{latest[:projects_count]}"
        puts "  缓存时间: #{latest[:cached_at]}"
        puts "  是否过期: #{latest[:expired] ? '是' : '否'}"
      end
      
      # 显示最近7天的缓存情况
      puts "\n📈 最近7天缓存情况:"
      7.times do |i|
        date = (Date.current - i.days)
        record = Irs::GovernmentProject.find_by_date(date)
        status = if record
          record.success? ? "✅ #{record.projects_count}个项目" : "❌ 失败"
        else
          "⭕ 无缓存"
        end
        puts "  #{date.strftime('%Y-%m-%d')}: #{status}"
      end
    end

    desc "清理过期的政府项目缓存"
    task :cleanup, [:retention_days] => :environment do |_task, args|
      retention_days = (args[:retention_days] || 30).to_i
      
      puts "🧹 清理政府项目缓存"
      puts "=" * 50
      puts "保留天数: #{retention_days}"
      
      result = Irs::GovernmentProjectCacheCleanupJob.cleanup_now(retention_days)
      
      if result[:success]
        puts "✅ 清理成功"
        puts "删除记录数: #{result[:deleted_count]}"
        puts "剩余记录数: #{result[:remaining_records]}"
      else
        puts "❌ 清理失败"
        puts "错误信息: #{result[:error]}"
      end
    end

    desc "强制刷新指定日期的缓存"
    task :refresh, [:date] => :environment do |_task, args|
      date = args[:date] || Date.yesterday.strftime("%Y-%m-%d")
      
      puts "🔄 强制刷新政府项目缓存"
      puts "=" * 50
      puts "刷新日期: #{date}"
      
      begin
        result = Irs::GovernmentProjectService.fetch_projects_by_date(date, force_refresh: true)
        
        if result.success?
          puts "✅ 刷新成功"
          puts "项目数量: #{result.count}"
          puts "数据来源: #{result.raw_response&.include?('CACHED') ? '缓存' : '接口'}"
        else
          puts "❌ 刷新失败"
          puts "错误信息: #{result.message}"
          puts "错误代码: #{result.error_code}"
        end
      rescue => e
        puts "❌ 刷新异常"
        puts "错误信息: #{e.message}"
      end
    end

    desc "预热缓存（获取最近几天的数据）"
    task :warmup, [:days] => :environment do |_task, args|
      days = (args[:days] || 7).to_i
      
      puts "🔥 预热政府项目缓存"
      puts "=" * 50
      puts "预热天数: #{days}"
      
      success_count = 0
      error_count = 0
      
      days.times do |i|
        date = (Date.current - i.days).strftime("%Y-%m-%d")
        print "正在获取 #{date} 的数据... "
        
        begin
          result = Irs::GovernmentProjectService.fetch_projects_by_date(date)
          
          if result.success?
            puts "✅ 成功 (#{result.count}个项目)"
            success_count += 1
          else
            puts "❌ 失败: #{result.message}"
            error_count += 1
          end
        rescue => e
          puts "❌ 异常: #{e.message}"
          error_count += 1
        end
        
        # 避免请求过于频繁
        sleep(1) if i < days - 1
      end
      
      puts "\n📊 预热结果:"
      puts "成功: #{success_count} 天"
      puts "失败: #{error_count} 天"
    end

    desc "验证缓存数据完整性"
    task validate: :environment do
      puts "🔍 验证政府项目缓存数据完整性"
      puts "=" * 50
      
      total_records = Irs::GovernmentProject.count
      puts "总缓存记录数: #{total_records}"
      
      if total_records == 0
        puts "⚠️  没有缓存记录"
        next
      end
      
      # 检查数据完整性
      invalid_records = []
      
      Irs::GovernmentProject.find_each do |record|
        issues = []
        
        # 检查必要字段
        issues << "缺少查询日期" if record.query_date.blank?
        issues << "缺少缓存时间" if record.cached_at.blank?
        
        # 检查成功记录的数据完整性
        if record.success?
          issues << "成功记录缺少原始数据" if record.raw_data.blank?
          issues << "项目数量与实际不符" if record.projects_count != record.projects_list.size
        else
          issues << "失败记录缺少错误信息" if record.error_message.blank?
        end
        
        if issues.any?
          invalid_records << {
            id: record.id,
            date: record.query_date,
            issues: issues
          }
        end
      end
      
      if invalid_records.empty?
        puts "✅ 所有缓存记录数据完整"
      else
        puts "❌ 发现 #{invalid_records.size} 条无效记录:"
        invalid_records.each do |record|
          puts "  ID #{record[:id]} (#{record[:date]}): #{record[:issues].join(', ')}"
        end
      end
    end

    desc "显示帮助信息"
    task help: :environment do
      puts "🔧 政府项目缓存管理工具"
      puts "=" * 50
      puts
      puts "可用任务:"
      puts "  rake government_project:cache:stats                    # 显示缓存统计信息"
      puts "  rake government_project:cache:cleanup[retention_days]  # 清理过期缓存（默认保留30天）"
      puts "  rake government_project:cache:refresh[date]             # 强制刷新指定日期缓存（默认昨天）"
      puts "  rake government_project:cache:warmup[days]              # 预热缓存（默认7天）"
      puts "  rake government_project:cache:validate                  # 验证缓存数据完整性"
      puts "  rake government_project:cache:help                      # 显示此帮助信息"
      puts
      puts "示例:"
      puts "  rake government_project:cache:cleanup[7]                # 清理7天前的缓存"
      puts "  rake government_project:cache:refresh[2025-07-01]       # 刷新2025-07-01的缓存"
      puts "  rake government_project:cache:warmup[3]                 # 预热最近3天的缓存"
    end
  end
end
