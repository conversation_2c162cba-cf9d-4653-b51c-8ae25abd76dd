namespace :serve do
  namespace :uuid_deduplication do
    desc '验证UUID去重功能'
    task validate: :environment do
      puts '开始验证UUID去重功能...'

      # 检查数据库结构
      puts '检查数据库结构:'

      # 检查serve_messages表的content_uuid字段
      if ActiveRecord::Base.connection.column_exists?(:serve_messages, :content_uuid)
        puts '✅ serve_messages.content_uuid 字段存在'
      else
        puts '❌ serve_messages.content_uuid 字段不存在'
      end

      # 检查serve_messages表的payload字段（用于存储额外数据）
      if ActiveRecord::Base.connection.column_exists?(:serve_messages, :payload)
        puts '✅ serve_messages.payload 字段存在'
      else
        puts '❌ serve_messages.payload 字段不存在'
      end

      # 检查索引
      indexes = ActiveRecord::Base.connection.indexes('serve_messages')
      uuid_index = indexes.find { |idx| idx.name.include?('content_uuid') }

      if uuid_index
        puts '✅ content_uuid 索引存在'
      else
        puts '❌ content_uuid 索引不存在'
      end

      # 检查模型方法
      puts '\n检查模型方法:'

      template = Serve::MessageTemplate.first
      if template
        if template.respond_to?(:template_signature)
          puts '✅ MessageTemplate.template_signature 方法存在'
          puts "   示例签名: #{template.template_signature}"
        else
          puts '❌ MessageTemplate.template_signature 方法不存在'
        end

        if template.respond_to?(:generate_unique_uuid)
          puts '✅ MessageTemplate.generate_unique_uuid 方法存在'
          puts '   方法可用于生成唯一UUID'
        else
          puts '❌ MessageTemplate.generate_unique_uuid 方法不存在'
        end

        if template.respond_to?(:content_changed?)
          puts '✅ MessageTemplate.content_changed? 方法存在'
          puts '   方法可用于检查内容变化'
        else
          puts '❌ MessageTemplate.content_changed? 方法不存在'
        end
      else
        puts '❌ 没有找到MessageTemplate记录，无法验证方法'
      end

      puts '验证完成！'
    end

    desc '为历史数据补充UUID（调用迁移任务）'
    task migrate_historical_data: :environment do
      puts '开始历史数据UUID迁移...'
      Rake::Task['serve:uuid_migration:backfill_historical_uuids'].invoke
      Rake::Task['serve:uuid_migration:validate_historical_uuids'].invoke
    end

    desc '生成UUID去重功能监控报告'
    task monitoring_report: :environment do
      puts '生成UUID去重功能监控报告...'

      system('rails runner scripts/uuid_dashboard.rb')
    end

    desc '清理UUID去重日志文件'
    task cleanup_logs: :environment do
      log_file = Rails.root.join('log', 'uuid_deduplication.log')

      if File.exist?(log_file)
        file_size_mb = (File.size(log_file) / 1024.0 / 1024.0).round(2)
        puts "当前日志文件大小: #{file_size_mb} MB"

        if file_size_mb > 50
          # 备份并压缩旧日志
          backup_file = Rails.root.join('log', "uuid_deduplication_#{Date.current.strftime('%Y%m%d')}.log.bak")
          FileUtils.mv(log_file, backup_file)

          # 压缩备份文件
          system("gzip #{backup_file}")

          puts "日志文件已备份并压缩: #{backup_file}.gz"
          puts '新的日志文件将在下次记录时创建'
        else
          puts '日志文件大小正常，无需清理'
        end
      else
        puts '日志文件不存在'
      end
    end

    desc '分析UUID去重性能趋势'
    task analyze_performance: :environment do
      puts '分析UUID去重性能趋势...'

      if UuidDeduplicationMonitoring.respond_to?(:analyze_performance_trends)
        UuidDeduplicationMonitoring.analyze_performance_trends
        puts '性能趋势分析完成，请查看日志文件'
      else
        puts '性能趋势分析方法尚未实现，将在后续版本中提供'
        puts '当前可以查看日志文件: log/uuid_deduplication.log'
      end
    end
  end
end
