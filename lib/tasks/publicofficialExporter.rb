# 这个文件用于导出公职人员的信息到Excel文件，包含姓名、性别、联系方式等字段。

require "axlsx"

class PublicOfficialExporter

  def self.export(file_path = "public_officials.xlsx")
    # 获取所有符合条件的公职人员
    users = User.find_public_official_users

    # 创建Excel工作簿
    Axlsx::Package.new do |p|
      p.workbook.add_worksheet(name: "公职人员信息表") do |sheet|
        # 添加表头
        sheet.add_row [
          "姓名",
          "性别",
          "手机号",
          "邮箱",
          "政治面貌",
          "生日",
          "政治生日",
          "学历",
          "学位",
          "部门",
          "职务",
          "职级",
          "编制类型"
        ]

        # 遍历用户添加数据行
        users.find_each do |user|
          member = user.dingtalk_member
          department = user.departments.first

          # 获取政治面貌中文名称
          political_dict = JSON.parse(File.read("app/services/staff_dictionary.json"))["政治面貌code表"]
          political_name = political_dict[user.political] || user.political

          # 获取职级中文名称
          job_level_dict = JSON.parse(File.read("app/services/staff_dictionary.json"))["职级code表"]
          job_level_name = job_level_dict[member&.job_level_code] || member&.job_level_code

          # 获取编制类型中文名称
          budgeted_post_dict = JSON.parse(File.read("app/services/staff_dictionary.json"))["人员数据字典表"]
          budgeted_post_name = budgeted_post_dict[member&.budgeted_post_code] || member&.budgeted_post_code

          sheet.add_row [
            user.name,
            user.gender,
            user.mobile,
            user.email,
            political_name,
            user.birthday&.strftime("%Y-%m-%d"),
            user.political_birthday&.strftime("%Y-%m-%d"),
            user.education,
            user.degree,
            department&.name,
            member&.pos_job,
            job_level_name,
            budgeted_post_name
          ]
        end

        # 设置列宽
        sheet.column_widths 10, 8, 15, 20, 15, 12, 12, 10, 10, 20, 15, 15, 15
      end

      # 保存文件
      p.serialize(file_path)
    end

    # 返回文件路径
    file_path
  end

end
