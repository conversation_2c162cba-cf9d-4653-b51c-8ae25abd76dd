namespace :serve do
  namespace :uuid_migration do
    desc "为历史消息数据补充UUID值"
    task backfill_historical_uuids: :environment do
      puts "开始为历史消息数据补充UUID值..."

      # 统计需要处理的数据
      total_messages = Serve::Message.where(content_uuid: nil).count
      puts "发现 #{total_messages} 条需要补充UUID的历史消息"

      return if total_messages == 0

      batch_size = 1000
      processed = 0
      errors = 0

      Serve::Message.where(content_uuid: nil).find_in_batches(batch_size: batch_size) do |batch|
        batch.each do |message|
          # 为历史数据生成特殊的UUID格式
          historical_uuid = self.class.generate_historical_uuid(message)

          # 更新UUID字段
          message.update_column(:content_uuid, historical_uuid)
          processed += 1

          if processed % 100 == 0
            puts "已处理: #{processed}/#{total_messages} (#{(processed.to_f / total_messages * 100).round(2)}%)"
          end
        rescue StandardError => e
          errors += 1
          Rails.logger.error "处理消息 #{message.id} 时出错: #{e.message}"
          puts "错误: 消息ID #{message.id} - #{e.message}"
        end
      end

      puts "\n补充UUID完成!"
      puts "成功处理: #{processed} 条"
      puts "错误数量: #{errors} 条"
      puts "总计: #{processed + errors} 条"
    end

    desc "验证历史数据UUID补充结果"
    task validate_historical_uuids: :environment do
      puts "验证历史数据UUID补充结果..."

      # 检查是否还有未补充UUID的消息
      missing_uuid_count = Serve::Message.where(content_uuid: nil).count
      puts "未补充UUID的消息数量: #{missing_uuid_count}"

      # 检查历史UUID格式
      historical_uuid_count = Serve::Message.where("content_uuid LIKE 'historical_%'").count
      puts "历史UUID格式的消息数量: #{historical_uuid_count}"

      # 检查UUID唯一性
      total_messages = Serve::Message.where.not(content_uuid: nil).count
      unique_uuids = Serve::Message.where.not(content_uuid: nil).distinct.count(:content_uuid)
      puts "总消息数: #{total_messages}"
      puts "唯一UUID数: #{unique_uuids}"

      if total_messages == unique_uuids
        puts "✅ UUID唯一性验证通过"
      else
        puts "❌ 发现重复UUID: #{total_messages - unique_uuids} 个"

        # 显示重复的UUID
        duplicates = Serve::Message.where.not(content_uuid: nil)
          .group(:content_uuid)
          .having("COUNT(*) > 1")
          .count

        puts "重复的UUID:"
        duplicates.each do |uuid, count|
          puts "  #{uuid}: #{count} 次"
        end
      end
    end

    desc "清理重复的历史UUID"
    task cleanup_duplicate_uuids: :environment do
      puts "清理重复的历史UUID..."

      duplicates = Serve::Message.where.not(content_uuid: nil)
        .group(:content_uuid)
        .having("COUNT(*) > 1")
        .pluck(:content_uuid)

      return if duplicates.empty?

      puts "发现 #{duplicates.size} 个重复的UUID"

      duplicates.each do |duplicate_uuid|
        messages = Serve::Message.where(content_uuid: duplicate_uuid).order(:created_at)

        # 保留最早的消息，为其他消息重新生成UUID
        messages.offset(1).each do |message|
          new_uuid = self.class.generate_historical_uuid(message)
          message.update_column(:content_uuid, new_uuid)
          puts "消息 #{message.id} 的UUID已更新: #{duplicate_uuid} -> #{new_uuid}"
        end
      end

      puts "重复UUID清理完成"
    end

    # 为历史数据生成UUID
    # 格式: historical_<message_id>_<user_id>_<hash>
    def self.generate_historical_uuid(message)
      # 基础信息
      message_id = message.id
      user_id = message.user_id || 0
      created_at = message.created_at.to_i

      # 生成唯一标识
      unique_key = "#{message_id}_#{user_id}_#{created_at}_historical"
      hash_suffix = Digest::MD5.hexdigest(unique_key)[0..7]

      "historical_#{message_id}_#{user_id}_#{hash_suffix}"
    end
  end
end
