# 测试钉钉成员同步的工具类
class TestMemberSync

  class << self

    def test_department_sync(department_name = "纪委")
      # 1. 查找测试部门
      test_department = Department.find_by(name: department_name)
      unless test_department
        Rails.logger.debug { "未找到部门: #{department_name}" }
        return
      end

      Rails.logger.debug { "测试部门: #{test_department.name} (#{test_department.code})" }

      # 2. 记录同步前的状态
      print_department_status("同步前状态", test_department)

      # 3. 初始化服务并执行同步
      begin
        client = Dingtalk::Client.find(3) # 确保这个ID是正确的
        member_service = Dingtalk::MemberService.new(client)

        Rails.logger.debug "\n开始同步..."
        member_service.sync_members_by(test_department)
        Rails.logger.debug "同步完成"
      rescue StandardError => e
        Rails.logger.debug { "同步出错: #{e.message}" }
        Rails.logger.debug e.backtrace.first(5)
        return
      end

      # 4. 检查同步后的状态
      print_department_status("同步后状态", test_department)

      # 5. 检查问题成员
      check_problem_members(test_department)

      # 6. 显示最近的成员变更
      show_recent_changes(test_department)
    end

    private

    def print_department_status(title, department)
      Rails.logger.debug { "\n#{title}:" }
      Rails.logger.debug { "当前部门成员数: #{department.memberships.where(invalid_at: nil).count}" }
      Rails.logger.debug { "失效的成员关系数: #{department.memberships.where.not(invalid_at: nil).count}" }
    end

    def check_problem_members(department)
      Rails.logger.debug "\n检查问题成员:"
      problem_members = department.memberships.includes(:user, :member).where(invalid_at: nil).select do |m|
        m.user.nil? || m.member.nil?
      end

      if problem_members.any?
        Rails.logger.debug { "发现 #{problem_members.count} 个问题成员:" }
        problem_members.each do |m|
          puts "Membership ID: #{m.id}, User: #{m.user_id}, Member: #{m.member_id}"
        end
      else
        Rails.logger.debug "未发现问题成员"
      end
    end

    def show_recent_changes(department)
      Rails.logger.debug "\n最近更新的成员关系:"

      # 先查找部门的所有成员
      members = department.memberships.includes(:user, :member)
        .where("memberships.effective_at IS NULL OR memberships.effective_at <= ?", Time.current)
        .where("memberships.invalid_at IS NULL OR memberships.invalid_at >= ?", Time.current)

      if members.any?
        Rails.logger.debug "当前部门成员:"
        members.each do |m|
          Rails.logger.debug { "User: #{m.user&.name} (#{m.user_id})" }
          Rails.logger.debug { "Member ID: #{m.member_id}" }
          Rails.logger.debug { "Department: #{m.department&.name} (#{m.department_id})" }
          Rails.logger.debug { "更新时间: #{m.updated_at}" }
          Rails.logger.debug { "状态: #{m.invalid_at.nil? ? '有效' : '已失效'}" }

          # 显示该用户的所有部门关系
          user_departments = m.user.memberships.includes(:department)
            .where(member_id: m.member_id)
            .where("memberships.effective_at IS NULL OR memberships.effective_at <= ?", Time.current)
            .where("memberships.invalid_at IS NULL OR memberships.invalid_at >= ?", Time.current)

          Rails.logger.debug "该用户的所有部门关系:"
          user_departments.each do |ud|
            puts "  - #{ud.department&.name} (#{ud.department&.code})"
          end
          Rails.logger.debug "---"
        end
      else
        Rails.logger.debug "该部门没有有效成员"
      end

      # 显示上级部门的成员
      return unless department.parent && ["临时组织", "虚拟组织", "内设机构"].include?(department.type_name)

      Rails.logger.debug { "\n上级部门 (#{department.parent.name}) 的成员:" }
      parent_members = department.parent.memberships
        .includes(:user, :member)
        .where("memberships.effective_at IS NULL OR memberships.effective_at <= ?", Time.current)
        .where("memberships.invalid_at IS NULL OR memberships.invalid_at >= ?", Time.current)

      parent_members.each do |m|
        Rails.logger.debug { "User: #{m.user&.name} (#{m.user_id})" }
        Rails.logger.debug { "Department: #{m.department&.name} (#{m.department&.code})" }
        Rails.logger.debug "---"
      end
    end

  end

end
