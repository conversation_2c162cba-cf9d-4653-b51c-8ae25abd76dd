#!/usr/bin/env ruby

# 测试时间查询问题
query = ARGV[0] || "今年有多少招投标类的素材？"

puts "测试时间查询问题: #{query}"
puts "=" * 60

begin
  # 1. 测试RansackDsl转换
  puts "1. 测试RansackDsl转换..."
  transformer = Bot::Transformers::RansackDsl.new(Serve::Activity)
  conditions = transformer.transform(query)
  puts "转换后的条件: #{conditions.inspect}"
  
  # 2. 测试直接数据库查询
  puts "\n2. 测试数据库查询..."
  base_query = Serve::Activity.all
  ransack_query = base_query.ransack(conditions)
  records = ransack_query.result
  total_count = records.count
  
  puts "查询结果总数: #{total_count}"
  puts "生成的SQL: #{records.to_sql}"
  
  # 3. 分析查询结果的时间分布
  puts "\n3. 分析查询结果的时间分布..."
  if total_count > 0
    # 按年份统计
    year_stats = records.group("EXTRACT(year FROM published_at)").count
    puts "按发布年份统计: #{year_stats}"
    
    created_year_stats = records.group("EXTRACT(year FROM created_at)").count  
    puts "按创建年份统计: #{created_year_stats}"
    
    # 显示前5条记录的时间信息
    puts "\n前5条记录的时间信息:"
    records.limit(5).each_with_index do |record, index|
      puts "#{index + 1}. #{record.name}"
      puts "   创建时间: #{record.created_at}"
      puts "   发布时间: #{record.published_at}"
      puts "   状态: #{record.state}"
    end
  end
  
  # 4. 测试标签查询
  puts "\n4. 测试标签查询..."
  
  # 直接查询带有"招投标"标签的素材
  tag_query_sql = <<~SQL
    SELECT COUNT(DISTINCT sa.id) as count
    FROM serve_activities sa
    INNER JOIN serve_activity_actions saa ON saa.user_id = sa.id
      AND saa.user_type = 'Serve::Activity'
      AND saa.target_type = 'Serve::Tag'
      AND saa.action_type = 'ai_relate'
    INNER JOIN serve_tags st ON st.id = saa.target_id
    WHERE st.name LIKE '%招投标%'
  SQL
  
  tag_result = ActiveRecord::Base.connection.execute(tag_query_sql)
  tag_count = tag_result.first['count'].to_i
  puts "标签包含'招投标'的素材数量: #{tag_count}"
  
  # 直接查询标题包含"招投标"的素材
  title_count = Serve::Activity.where("name ILIKE ?", "%招投标%").count
  puts "标题包含'招投标'的素材数量: #{title_count}"
  
  # 5. 测试2025年的素材数量
  puts "\n5. 测试2025年的素材数量..."
  
  # 按发布时间查询2025年
  published_2025_count = Serve::Activity.where(
    published_at: Time.new(2025, 1, 1)..Time.new(2025, 12, 31, 23, 59, 59)
  ).count
  puts "2025年发布的素材数量: #{published_2025_count}"
  
  # 按创建时间查询2025年
  created_2025_count = Serve::Activity.where(
    created_at: Time.new(2025, 1, 1)..Time.new(2025, 12, 31, 23, 59, 59)
  ).count
  puts "2025年创建的素材数量: #{created_2025_count}"
  
  # 6. 测试组合查询：2025年 + 招投标
  puts "\n6. 测试组合查询：2025年 + 招投标..."
  
  # 2025年发布 + 标题包含招投标
  combo_published_count = Serve::Activity.where(
    published_at: Time.new(2025, 1, 1)..Time.new(2025, 12, 31, 23, 59, 59)
  ).where("name ILIKE ?", "%招投标%").count
  puts "2025年发布且标题包含'招投标'的素材: #{combo_published_count}"
  
  # 2025年创建 + 标题包含招投标
  combo_created_count = Serve::Activity.where(
    created_at: Time.new(2025, 1, 1)..Time.new(2025, 12, 31, 23, 59, 59)
  ).where("name ILIKE ?", "%招投标%").count
  puts "2025年创建且标题包含'招投标'的素材: #{combo_created_count}"
  
  # 7. 分析问题
  puts "\n7. 问题分析..."
  puts "AI查询返回: #{total_count} 个素材"
  puts "标签查询结果: #{tag_count} 个素材"
  puts "标题查询结果: #{title_count} 个素材"
  puts "2025年发布: #{published_2025_count} 个素材"
  puts "2025年创建: #{created_2025_count} 个素材"
  
  if conditions.key?('created_at_gteq') || conditions.key?(:created_at_gteq)
    puts "❌ 问题：使用了created_at字段进行时间筛选"
    puts "建议：应该使用published_at字段"
  elsif conditions.key?('published_at_gteq') || conditions.key?(:published_at_gteq)
    puts "✅ 正确：使用了published_at字段进行时间筛选"
  else
    puts "⚠️  警告：没有检测到时间筛选条件"
  end
  
rescue => e
  puts "❌ 测试失败: #{e.message}"
  puts e.backtrace.first(10)
end
