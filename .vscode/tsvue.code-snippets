{"Print to console": {"prefix": "tsvue", "description": "Basic vue typescript template", "body": ["<script lang='ts'>", "import { ref, defineComponent, toRefs } from 'vue';", "", "const componentName = defineComponent({", "  name: '<PERSON><PERSON><PERSON>',", "  components: {},", "  props: {", "    value: { type: String, default: 'copy' },", "  },", "  setup(props) {", "    return {", "      ...to<PERSON><PERSON>s(props),", "    };", "  },", "});", "export default componentName;", "</script>", "", "<template lang=\"pug\">", ".container", "  | component", "</template>", "", "<style lang=\"stylus\" scoped></style>", ""]}}