#!/usr/bin/env ruby

# 测试"去年有多少？"查询的完整响应数据结构
require_relative 'config/environment'

puts "=== 测试去年查询的完整响应 ==="

# 模拟用户查询
query = "去年有多少？"
puts "查询: #{query}"

# 创建ActivityQueryTool实例
tool = Bot::Tools::ActivityQueryTool.new

# 执行查询
puts "\n=== 执行查询 ==="
result = tool.query_activities(query: query)
puts "查询结果: #{result.inspect}"

# 检查是否创建了artifact
puts "\n=== 检查Artifact ==="
latest_artifact = Bot::ActivityListArtifact.order(created_at: :desc).first
if latest_artifact
  puts "最新Artifact ID: #{latest_artifact.id}"
  puts "Artifact类型: #{latest_artifact.type}"
  puts "tool_conf: #{latest_artifact.tool_conf.inspect}"
  puts "meta: #{latest_artifact.meta.inspect}"
  puts "function_params: #{latest_artifact.function_params.inspect}"
  
  # 检查as_jbuilder_json的输出
  puts "\n=== as_jbuilder_json输出 ==="
  json_output = latest_artifact.as_jbuilder_json
  puts "JSON输出: #{json_output.inspect}"
  
  # 检查tool_conf.model_class
  puts "\n=== 关键字段检查 ==="
  model_class = latest_artifact.tool_conf&.dig('model_class')
  puts "tool_conf.model_class: #{model_class.inspect}"
  
  # 检查前端需要的数据结构
  puts "\n=== 前端数据结构检查 ==="
  if json_output.is_a?(Hash)
    puts "content.tool_conf.model_class: #{json_output.dig(:content, :tool_conf, :model_class) || json_output.dig('content', 'tool_conf', 'model_class')}"
    puts "params.q: #{json_output.dig(:params, :q) || json_output.dig('params', 'q')}"
    puts "payload.params.q: #{json_output.dig(:payload, :params, :q) || json_output.dig('payload', 'params', 'q')}"
  end
else
  puts "没有找到Artifact记录"
end

# 检查BotMentionTypeMapping
puts "\n=== 检查组件映射 ==="
puts "当前model_class值: #{latest_artifact&.tool_conf&.dig('model_class')}"
puts "BotMentionTypeMapping中是否存在对应组件: 需要在前端检查"

puts "\n=== 测试完成 ==="
