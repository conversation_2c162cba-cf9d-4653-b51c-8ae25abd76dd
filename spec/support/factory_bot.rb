# RSpec
# spec/support/factory_bot.rb
RSpec.configure do |config|
  factories_directory = Rails.root.join('..', 'factories')
  engine_factory_directories = [RailsCom::Engine].map do |engine|
    engine.root.join('spec', 'factories')
  end
  FactoryBot.definition_file_paths = [factories_directory, *engine_factory_directories]
  FactoryBot.find_definitions
  config.include FactoryBot::Syntax::Methods
end

# Ensure the after_initialize callback is run during factory creation
FactoryBot::Strategy::Create.class_eval do
  def result(evaluation)
    instance = evaluation.object
    instance.run_callbacks(:initialize)
    instance.save!
    instance
  end
end

FactoryBot::Strategy::Build.class_eval do
  def result(evaluation)
    instance = evaluation.object
    instance.run_callbacks(:initialize)
    instance
  end
end

FactoryBot::Strategy::AttributesFor.class_eval do
  def result(evaluation)
    # 获取对象实例
    instance = evaluation.object

    # 触发 `after_initialize` 回调
    instance.run_callbacks(:initialize) do
      # 获取属性并做过滤和转换
      attributes = instance.attributes
        .reject { |_, value| value.nil? }  # 过滤掉 nil 值的属性
        .with_indifferent_access          # 使用 with_indifferent_access

      attributes
    end
  end
end
