RSpec.configure do |config|
  config.include FactoryBot::Syntax::Methods

  config.before(:each) do |example|
    @modules = example.metadata[:tags]&.first&.split(' ') || []
    @app = create :app
    @user = create(:user, app: @app)
    user_account_info = {
      app_id: @app.code,
      account_type: 'User',
      account: @user.account,
    }
    allow(User).to receive(:auth!).and_return(user_account_info)

    create_list :bot_agent, 2, app: @app
    @bot_agents = @app.bot_agents.all
    @bot_agent = @bot_agents.first
    @bot_agent_count = @bot_agents.count

    create_list :bot_intent, 2, app: @app
    @bot_intents = @app.bot_intents.all
    @bot_intent = @bot_intents.first
    @bot_intent_count = @bot_intents.count

    create_list :bot_conversation, 2, app: @app, agent: @bot_agent, user: @user
    @bot_conversations = Bot::Conversation.all
    @bot_conversation = @bot_conversations.first
    @bot_conversation_count = @bot_conversations.count

    create_list :bot_message, 2, conversation: @bot_conversation
    @bot_messages = Bot::Message.all
    @bot_message = @bot_messages.first
    @bot_message_count = @bot_messages.count

    create_list :bot_artifact, 2, conversation: @bot_conversation
    @bot_artifacts = Bot::Artifact.all
    @bot_artifact = @bot_artifacts.first
    @bot_artifact_count = @bot_artifacts.count

    create_list :bot_paperwork, 2, app: @app, user: @user, attachment: { url: 'https://www.baidu.com/test.pdf' }
    @bot_paperworks = Bot::Paperwork.all
    @bot_paperwork = @bot_paperworks.first
    @bot_paperwork_count = @bot_paperworks.count

    create_list :bot_report_template, 2, app: @app
    @bot_report_templates = @app.bot_report_templates.all
    @bot_report_template = @bot_report_templates.first
    @bot_report_template_count = @bot_report_templates.count

    create_list :bot_report, 2, app: @app, user: @user, report_template: @bot_report_template
    @bot_reports = Bot::Report.all
    @bot_report = @bot_reports.first
    @bot_report_count = @bot_reports.count

    create_list :bot_reviewer, 2, app: @app
    @bot_reviewers = @app.bot_reviewers.all
    @bot_reviewer = @bot_reviewers.first
    @bot_reviewer_count = @bot_reviewers.count

    create_list :bot_review_rule, 2, review_source: @bot_reviewer
    @bot_review_rules = Bot::ReviewRule.all
    @bot_review_rule = @bot_review_rules.first
    @bot_review_rule_count = @bot_review_rules.count

    create_list :bot_review_document, 2, reviewer: @bot_reviewer, user: @user, file: { 'url' => 'https://minio.tallty.com/tunghai/tunghai/2025/8439eae87c19168735d1eb5d6fd68f89.pdf' }
    @bot_review_documents = @app.bot_review_documents.all
    @bot_review_document = @bot_review_documents.first
    @bot_review_document_count = @bot_review_documents.count

    create_list :bot_review_result, 2, document_source: @bot_review_document, review_rule: @bot_review_rule
    @bot_review_results = Bot::ReviewResult.all
    @bot_review_result = @bot_review_results.first
    @bot_review_result_count = @bot_review_results.count

    create_list :bot_meeting, 2, app: @app, user: @user
    @bot_meetings = @app.bot_meetings.all
    @bot_meeting = @bot_meetings.first
    @bot_meeting_count = @bot_meetings.count
  end
end
