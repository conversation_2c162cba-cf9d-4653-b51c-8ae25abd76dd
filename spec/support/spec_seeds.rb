RSpec.configure do |config|
  config.include FactoryBot::Syntax::Methods

  config.before(:each) do |example|
    @modules = example.metadata[:tags]&.first&.split || []
    @app = create :app
    @user = create(:user, app: @app)
    user_account_info = {
      account_type: "User",
      account: @user.account
    }
    allow(User).to receive(:auth!).and_return(user_account_info)

    create_list :serve_bid_project, 2, app: @app
    @serve_bid_projects = Serve::BidProject.all
    @serve_bid_project = @serve_bid_projects.first
    @serve_bid_project_count = @serve_bid_projects.count
  end
end
