RSpec.configure do |config|
  config.include FactoryBot::Syntax::Methods

  config.before(:each) do |example|
    @modules = example.metadata[:tags]&.first&.split(' ') || []
    @app = create :app
    @user = create(:user, app: @app)
    user_account_info = {
      account_type: 'User',
      account: @user.account,
    }
    allow(User).to receive(:auth!).and_return(user_account_info)

    @org = Org.create(name: 'org', app: @app)
    @department = Department.create!(name: 'department', code: 'department', org: @org)
    @mod = Mod.create(name: 'serve', key: 'serve')
    Role.create(name: 'serve_manage', label: 'serve_manage', mod: @mod)
    Role.create(name: 'serve_admin', label: 'serve_admin', mod: @mod)

    create_list :serve_submodule, 2, app: @app
    @serve_submodules = Serve::Submodule.all
    @serve_submodule = @serve_submodules.first
    @serve_submodule_count = @serve_submodules.count
    @serve_submodule.update(manage_enable: true, manage_user_ids: [@user.id])
    @submodule = @serve_submodule

    create_list :serve_banner, 2, app: @app, submodule: @serve_submodule
    @serve_banners = Serve::Banner.all
    @serve_banner = @serve_banners.first
    @serve_banner_count = @serve_banners.count

    create_list :serve_group, 2, app: @app, submodule: @serve_submodule
    @serve_groups = Serve::Group.all
    @serve_group = @serve_groups.first
    @serve_group_count = @serve_groups.count

    create_list :serve_catalog, 2, app: @app, submodule: @serve_submodule, target: @department
    @serve_catalogs = Serve::Catalog.all
    @serve_catalog = @serve_catalogs.first
    @serve_catalog_count = @serve_catalogs.count
    @serve_catalog.update(view_enable: true, view_user_ids: [@user.id], use_enable: true, use_user_ids: [@user.id])

    create_list :serve_tag, 2, app: @app, submodule: @serve_submodule
    @serve_tags = Serve::Tag.all
    @serve_tag = @serve_tags.first
    @serve_tag_count = @serve_tags.count

    create_list :serve_activity, 2, app: @app, submodule: @serve_submodule, creator: @user,
      catalog_ids: [@serve_catalog.id], tag_ids: [@serve_tag.id]
    @serve_activities = Serve::Activity.all
    @serve_activity = @serve_activities.first
    @serve_activity_count = @serve_activities.count
    
    binding.pry
    

    Serve::Entry.create!(app: @app, submodule: @serve_submodule, activity: @serve_activity, user: @user)
    # Serve::Entry.create!(app: @app, submodule: @serve_submodule, activity: @serve_activity, user: @user)
    @serve_entries = Serve::Entry.all
    @serve_entry = @serve_entries.first
    @serve_entry_count = @serve_entries.count

    @serve_permit_actions = Serve::PermitAction.all
    @serve_permit_action = @serve_permit_actions.first
    @serve_permit_action_count = @serve_permit_actions.count
  end
end
