# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[7.1].define(version: 2024_10_24_133900) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "plpgsql"

  create_table "actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", null: false, comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id"], name: "actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_actions_on_app_id"
    t.index ["real_user_id"], name: "index_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_actions_on_user"
  end

  create_table "active_storage_attachments", force: :cascade do |t|
    t.string "record_type"
    t.bigint "record_id"
    t.bigint "blob_id"
    t.string "name"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["blob_id"], name: "index_active_storage_attachments_on_blob_id"
    t.index ["record_type", "record_id", "name", "blob_id"], name: "idx_on_record_type_record_id_name_blob_id_0be5805727", unique: true
    t.index ["record_type", "record_id"], name: "index_active_storage_attachments_on_record"
  end

  create_table "active_storage_blobs", force: :cascade do |t|
    t.jsonb "metadata", comment: "额外信息"
    t.string "app_code", comment: "app标识"
    t.string "key", comment: "key"
    t.string "filename", comment: "文件名称"
    t.string "content_type", comment: "文件类型"
    t.string "service_name", comment: "服务名称"
    t.integer "byte_size", comment: "文件大小"
    t.string "checksum", comment: "校验位"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["key"], name: "index_active_storage_blobs_on_key", unique: true
  end

  create_table "api_settings", force: :cascade do |t|
    t.bigint "model_define_id"
    t.bigint "app_id"
    t.string "klass", comment: "对应的active record class name"
    t.string "action", comment: "对应controller的action"
    t.string "uid", comment: "自动生成的唯一标识"
    t.jsonb "extract_conf"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_api_settings_on_app_id"
    t.index ["model_define_id"], name: "index_api_settings_on_model_define_id"
    t.index ["uid"], name: "index_api_settings_on_uid"
  end

  create_table "apps", force: :cascade do |t|
    t.string "code", comment: "应用标识"
    t.string "name", comment: "应用的名称"
    t.jsonb "settings", default: {}, comment: "配置信息"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "async_tasks", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "user_id"
    t.string "taskable_type"
    t.bigint "taskable_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "seq", comment: "编号"
    t.string "type", comment: "STI属性"
    t.string "flag", comment: "程序使用参数，唯一标识，前端配合使用"
    t.string "name", comment: "任务名称"
    t.integer "progress", comment: "进度(取整数)"
    t.string "state"
    t.string "perform_args", comment: "执行参数"
    t.jsonb "options", comment: "启动执行参数"
    t.jsonb "payload", comment: "处理信息"
    t.jsonb "result", comment: "异步处理的结果信息"
    t.jsonb "meta", comment: "额外信息"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_async_tasks_on_app_id"
    t.index ["taskable_type", "taskable_id"], name: "index_async_tasks_on_taskable"
    t.index ["user_id"], name: "index_async_tasks_on_user_id"
  end

  create_table "bpm_catalogs", force: :cascade do |t|
    t.bigint "app_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "名称"
    t.jsonb "icon", comment: "图标"
    t.integer "position", comment: "排序"
    t.boolean "published", comment: "是否发布"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_bpm_catalogs_on_app_id"
  end

  create_table "bpm_instance_relations", force: :cascade do |t|
    t.bigint "instance_id"
    t.string "source_type"
    t.bigint "source_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "seq", comment: "随机数"
    t.string "model_setting_flag", comment: "对应模型的flag"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["instance_id"], name: "index_bpm_instance_relations_on_instance_id"
    t.index ["source_type", "source_id"], name: "index_bpm_instance_relations_on_source"
  end

  create_table "bpm_instances", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "workflow_id"
    t.bigint "creator_id"
    t.string "flowable_type"
    t.bigint "flowable_id"
    t.integer "comments_count", comment: "评论数量"
    t.string "comment_conf", default: "open"
    t.string "seq", comment: "编号"
    t.string "type", comment: "STI"
    t.jsonb "payload", comment: "流程表单"
    t.jsonb "storage", default: {}, comment: "instance的数据存储，主要是有配置map_key 的 value，另外保存了token中配置的内容"
    t.jsonb "summary", default: {}, comment: "instance在列表页显示的内容"
    t.string "state", comment: "流程状态"
    t.string "flowable_flag", comment: "flowable不同流程的flag"
    t.integer "spent_time_in_second", comment: "耗时时长"
    t.jsonb "cache_payload", comment: "额外存储的结构，根据场合可以作为payload的存储"
    t.datetime "action_at", precision: nil, comment: "激活时间"
    t.jsonb "last_token_attr", default: {}, comment: "最新token信息"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_at"], name: "index_bpm_instances_on_action_at"
    t.index ["app_id"], name: "index_bpm_instances_on_app_id"
    t.index ["creator_id"], name: "index_bpm_instances_on_creator_id"
    t.index ["flowable_type", "flowable_id"], name: "index_bpm_instances_on_flowable"
    t.index ["seq"], name: "index_bpm_instances_on_seq", unique: true
    t.index ["state"], name: "index_bpm_instances_on_state"
    t.index ["type"], name: "index_bpm_instances_on_type"
    t.index ["workflow_id"], name: "index_bpm_instances_on_workflow_id"
  end

  create_table "bpm_place_relations", force: :cascade do |t|
    t.bigint "workflow_id"
    t.bigint "source_id"
    t.bigint "target_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["source_id"], name: "index_bpm_place_relations_on_source_id"
    t.index ["target_id"], name: "index_bpm_place_relations_on_target_id"
    t.index ["workflow_id"], name: "index_bpm_place_relations_on_workflow_id"
  end

  create_table "bpm_places", force: :cascade do |t|
    t.bigint "workflow_id"
    t.string "type", comment: "STI"
    t.string "seq", comment: "place的唯一序列号，保持一致"
    t.string "name", comment: "节点名称"
    t.string "desc", comment: "节点描述"
    t.integer "position", comment: "根据 tree 边生成的 position"
    t.boolean "is_summary", comment: "是否快捷引用"
    t.jsonb "fields"
    t.jsonb "place_form", default: {}
    t.jsonb "options", default: {}, comment: "节点的配置信息"
    t.jsonb "timer_options", default: {}
    t.jsonb "trigger_options", default: {}
    t.jsonb "token_actions", default: {}
    t.jsonb "layout_options", default: {}, comment: "前端页面使用的配置"
    t.jsonb "activate_options"
    t.jsonb "token_source_options"
    t.jsonb "form_setting"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["workflow_id"], name: "index_bpm_places_on_workflow_id"
  end

  create_table "bpm_rules", force: :cascade do |t|
    t.bigint "workflow_id"
    t.string "name", comment: "规则名称"
    t.integer "time_in_second", comment: "设定时间范围"
    t.string "type", comment: "STI"
    t.jsonb "options", comment: "具体配置内容"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["workflow_id"], name: "index_bpm_rules_on_workflow_id"
  end

  create_table "bpm_stars", force: :cascade do |t|
    t.bigint "user_id"
    t.bigint "workflow_id"
    t.integer "position", comment: "排序"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["user_id"], name: "index_bpm_stars_on_user_id"
    t.index ["workflow_id"], name: "index_bpm_stars_on_workflow_id"
  end

  create_table "bpm_tokens", force: :cascade do |t|
    t.bigint "place_id"
    t.bigint "previous_token_id"
    t.bigint "operator_id"
    t.bigint "app_id"
    t.bigint "workflow_id"
    t.bigint "instance_id"
    t.string "activate_source_type"
    t.bigint "activate_source_id"
    t.string "token_source_type"
    t.bigint "token_source_id"
    t.string "type", comment: "STI"
    t.string "place_type", comment: "Place的类型"
    t.string "transition_type", comment: "Transition的类型"
    t.string "name", comment: "Token的名称，默认取自Place"
    t.string "state", comment: "Token状态"
    t.text "comment", comment: "审批备注"
    t.jsonb "options", default: {}, comment: "Token的额外信息JSON"
    t.jsonb "token_payload", default: {}, comment: "对应place的place_payload，存储审批时候存储在tokne中的信息"
    t.integer "spent_time_in_second", comment: "耗时时长"
    t.jsonb "operate_logs"
    t.string "action_key", comment: "保存上一个action的操作"
    t.string "action_flag", comment: "保存action的操作flag，action key有可能是重复的，通过action_flag来做区分"
    t.integer "timestamp", comment: "时间戳，当前批次"
    t.datetime "action_at", precision: nil, comment: "激活时间"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_at"], name: "index_bpm_tokens_on_action_at"
    t.index ["activate_source_type", "activate_source_id"], name: "index_bpm_tokens_on_activate_source"
    t.index ["app_id"], name: "index_bpm_tokens_on_app_id"
    t.index ["instance_id"], name: "index_bpm_tokens_on_instance_id"
    t.index ["operator_id"], name: "index_bpm_tokens_on_operator_id"
    t.index ["place_id"], name: "index_bpm_tokens_on_place_id"
    t.index ["previous_token_id"], name: "index_bpm_tokens_on_previous_token_id"
    t.index ["state"], name: "index_bpm_tokens_on_state"
    t.index ["token_source_type", "token_source_id"], name: "index_bpm_tokens_on_token_source"
    t.index ["type"], name: "index_bpm_tokens_on_type"
    t.index ["workflow_id"], name: "index_bpm_tokens_on_workflow_id"
  end

  create_table "bpm_transitions", force: :cascade do |t|
    t.bigint "workflow_id"
    t.bigint "place_id"
    t.string "type", comment: "STI"
    t.jsonb "callback_options", default: {}, comment: "回调设置"
    t.jsonb "options", default: {}, comment: "transition跳转的额外设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["place_id"], name: "index_bpm_transitions_on_place_id"
    t.index ["workflow_id"], name: "index_bpm_transitions_on_workflow_id"
  end

  create_table "bpm_workflow_relations", force: :cascade do |t|
    t.string "workflowable_type"
    t.bigint "workflowable_id"
    t.bigint "workflow_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["workflow_id"], name: "index_bpm_workflow_relations_on_workflow_id"
    t.index ["workflowable_type", "workflowable_id"], name: "index_bpm_workflow_relations_on_workflowable"
  end

  create_table "bpm_workflows", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "creator_id"
    t.bigint "mod_id"
    t.bigint "catalog_id"
    t.jsonb "permits", default: {}
    t.boolean "permit_enable", comment: "是否开启permit的按钮"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "type", comment: "STI"
    t.string "name", comment: "流程名称"
    t.text "desc", comment: "流程描述"
    t.jsonb "icon", comment: "流程图标"
    t.jsonb "cover_image", comment: "流程封面"
    t.string "state", default: "todo"
    t.integer "position", comment: "catalog内排序"
    t.string "instance_type", comment: "自动生成的instance_type"
    t.string "classify", default: "direct"
    t.jsonb "form", default: {}
    t.jsonb "meta", default: {}, comment: "工作流额外配置信息 "
    t.jsonb "token_actions", default: {}
    t.jsonb "trigger_options", default: {}
    t.boolean "auto_complete_same_handle_token", comment: "是否跳过连续相同的审批人"
    t.jsonb "submit_options"
    t.jsonb "form_setting"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_bpm_workflows_on_app_id"
    t.index ["catalog_id"], name: "index_bpm_workflows_on_catalog_id"
    t.index ["creator_id"], name: "index_bpm_workflows_on_creator_id"
    t.index ["mod_id"], name: "index_bpm_workflows_on_mod_id"
    t.index ["type"], name: "index_bpm_workflows_on_type"
  end

  create_table "com_private_policies", force: :cascade do |t|
    t.bigint "app_id"
    t.string "name", comment: "条款名称"
    t.string "key", comment: "关键字，可能有不同业务模块需要使用的关键字"
    t.jsonb "content", comment: "隐私条款内容"
    t.integer "position", comment: "排序"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_com_private_policies_on_app_id"
  end

  create_table "com_record_storages", force: :cascade do |t|
    t.bigint "user_id"
    t.string "key", comment: "缓存区key"
    t.jsonb "storage", comment: "属性暂存区"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["key"], name: "index_com_record_storages_on_key"
    t.index ["user_id"], name: "index_com_record_storages_on_user_id"
  end

  create_table "com_search_items", force: :cascade do |t|
    t.bigint "app_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "搜索条件 "
    t.integer "position", comment: "位置"
    t.string "group_name", comment: "分组标识"
    t.boolean "enabled", comment: "是否启用"
    t.jsonb "conditions"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_com_search_items_on_app_id"
  end

  create_table "com_themes", force: :cascade do |t|
    t.bigint "app_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "主题名称"
    t.jsonb "conf", comment: "主题配置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_com_themes_on_app_id"
  end

  create_table "com_version_histories", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "creator_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "版本发布名称"
    t.string "version", comment: "版本号"
    t.jsonb "content", comment: "发布说明"
    t.integer "position", comment: "发布顺序"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_com_version_histories_on_app_id"
    t.index ["creator_id"], name: "index_com_version_histories_on_creator_id"
  end

  create_table "comments", force: :cascade do |t|
    t.string "commentable_type"
    t.bigint "commentable_id"
    t.bigint "user_id"
    t.string "title", comment: "标题"
    t.text "subject", comment: "简述"
    t.text "body", comment: "内容"
    t.jsonb "attachments", comment: "附件"
    t.integer "parent_id", comment: "父节点"
    t.integer "lft", comment: "左节点"
    t.integer "rgt", comment: "右节点"
    t.integer "depth", default: 0, null: false, comment: "层级"
    t.integer "children_count", default: 0, null: false, comment: "子评论数量"
    t.integer "position", comment: "排序"
    t.integer "likes_count", default: 0, comment: "点赞次数"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["commentable_type", "commentable_id"], name: "index_comments_on_commentable"
    t.index ["user_id"], name: "index_comments_on_user_id"
  end

  create_table "component_settings", force: :cascade do |t|
    t.bigint "app_id"
    t.string "seq", comment: "编号"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "组件配置名称"
    t.string "component_klass", comment: "组件类名称"
    t.string "component_path", comment: "组件类路径"
    t.jsonb "conf", comment: "组件配置的json结构"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_component_settings_on_app_id"
    t.index ["seq"], name: "index_component_settings_on_seq", unique: true
  end

  create_table "data_counter_stats", force: :cascade do |t|
    t.string "countable_type"
    t.bigint "countable_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.date "date", comment: "日期，如果是年、月则存第一天"
    t.integer "hour", comment: "小时"
    t.string "period", default: "hour"
    t.integer "view_count", default: 0, comment: "浏览量"
    t.integer "action_count", default: 0, comment: "使用量"
    t.integer "user_count", default: 0, comment: "用户量"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["countable_type", "countable_id"], name: "index_data_counter_stats_on_countable"
    t.index ["date"], name: "index_data_counter_stats_on_date"
    t.index ["hour"], name: "index_data_counter_stats_on_hour"
  end

  create_table "data_forms", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "create_user_id"
    t.string "source_type"
    t.bigint "source_id"
    t.string "record_type"
    t.bigint "record_id"
    t.string "type", comment: "STI属性"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "flag", comment: "可用作同一 source 下不同的关联关系的区分"
    t.string "source_flag", comment: "关联source的flag"
    t.string "state", comment: "数据状态"
    t.jsonb "payload", default: {}, comment: "存储的信息"
    t.jsonb "summary", comment: "通过form生成的缩略信息"
    t.jsonb "form_conf"
    t.jsonb "options", comment: "额外的数据信息"
    t.jsonb "meta", comment: "预留后续的数据存储"
    t.string "form_conf_seq", comment: "表单配置的seq，方便进行检索"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_data_forms_on_app_id"
    t.index ["create_user_id"], name: "index_data_forms_on_create_user_id"
    t.index ["record_type", "record_id"], name: "index_data_forms_on_record"
    t.index ["source_type", "source_id"], name: "index_data_forms_on_source"
    t.index ["type"], name: "index_data_forms_on_type"
  end

  create_table "data_scopes", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "tanent_id"
    t.bigint "user_id"
    t.string "name", comment: "名称"
    t.jsonb "config", default: {}, comment: "配置"
    t.jsonb "payload", default: {}
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_data_scopes_on_app_id"
    t.index ["tanent_id"], name: "index_data_scopes_on_tanent_id"
    t.index ["user_id"], name: "index_data_scopes_on_user_id"
  end

  create_table "data_transfers", force: :cascade do |t|
    t.bigint "app_id"
    t.string "source_type"
    t.bigint "source_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "op", comment: "操作"
    t.jsonb "infos", comment: "额外信息"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_data_transfers_on_app_id"
    t.index ["source_type", "source_id"], name: "index_data_transfers_on_source"
    t.index ["target_type", "target_id"], name: "index_data_transfers_on_target"
  end

  create_table "data_view_logs", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "user_id"
    t.string "source_type"
    t.bigint "source_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.datetime "view_at", precision: nil, comment: "最新访问时间"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_data_view_logs_on_app_id"
    t.index ["source_type", "source_id"], name: "index_data_view_logs_on_source"
    t.index ["user_id"], name: "index_data_view_logs_on_user_id"
  end

  create_table "department_hierarchies", force: :cascade do |t|
    t.integer "ancestor_id", null: false
    t.integer "descendant_id", null: false
    t.integer "generations", null: false
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.index ["ancestor_id", "descendant_id", "generations"], name: "department_anc_desc_idx", unique: true
    t.index ["descendant_id"], name: "department_desc_idx"
  end

  create_table "department_identities", force: :cascade do |t|
    t.bigint "app_id"
    t.string "name", comment: "部门类型名称"
    t.string "department_type", comment: "Department的类型，可能会关系到Department的STI"
    t.string "color", comment: "标签颜色"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_department_identities_on_app_id"
  end

  create_table "departments", force: :cascade do |t|
    t.bigint "org_id"
    t.bigint "root_org_id"
    t.bigint "department_identity_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.integer "parent_id", comment: "closure tree parent_id"
    t.string "code", comment: "组织标识"
    t.string "name", comment: "组织名称"
    t.string "short_name", comment: "组织简称"
    t.string "type", comment: "STI类型，可以是集团，或者在某些时候可能是学校这样的类型"
    t.integer "position", comment: "排序"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["department_identity_id"], name: "index_departments_on_department_identity_id"
    t.index ["org_id"], name: "index_departments_on_org_id"
    t.index ["root_org_id"], name: "index_departments_on_root_org_id"
  end

  create_table "duties", force: :cascade do |t|
    t.bigint "duty_group_id"
    t.bigint "org_id"
    t.bigint "department_id"
    t.string "name", comment: "职务名称"
    t.string "rank", comment: "职务等级"
    t.integer "position", comment: "排序"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "code", comment: "岗位标识"
    t.index ["department_id"], name: "index_duties_on_department_id"
    t.index ["duty_group_id"], name: "index_duties_on_duty_group_id"
    t.index ["org_id"], name: "index_duties_on_org_id"
  end

  create_table "duty_groups", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "org_id"
    t.string "name", comment: "角色组名称"
    t.integer "position", comment: "排序"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "code", comment: "岗位组标识"
    t.index ["app_id"], name: "index_duty_groups_on_app_id"
    t.index ["org_id"], name: "index_duty_groups_on_org_id"
  end

  create_table "forms_templates", force: :cascade do |t|
    t.bigint "app_id"
    t.string "uuid", comment: "表单的唯一标识，可以替代id给前端使用"
    t.string "name", comment: "表单的名称"
    t.jsonb "form", default: {}
    t.jsonb "form_setting"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_forms_templates_on_app_id"
    t.index ["uuid"], name: "index_forms_templates_on_uuid"
  end

  create_table "member_identities", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "org_id"
    t.jsonb "manages", default: {}
    t.boolean "manage_enable", comment: "是否开启permit的按钮"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "ancestry", comment: "树形结构"
    t.string "name", comment: "身份名称"
    t.string "member_type", comment: "Member的类型"
    t.integer "depth", comment: "树结构深度"
    t.integer "children_count", default: 0, comment: "子对象的数据"
    t.jsonb "form", default: {}, comment: "Member配置的表单"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "config", comment: "配置"
    t.index ["ancestry"], name: "index_member_identities_on_ancestry"
    t.index ["app_id"], name: "index_member_identities_on_app_id"
    t.index ["org_id"], name: "index_member_identities_on_org_id"
  end

  create_table "member_identity_permit_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", null: false, comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id"], name: "member_identity_permit_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_member_identity_permit_actions_on_app_id"
    t.index ["real_user_id"], name: "index_member_identity_permit_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "member_identity_permit_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_member_identity_permit_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "member_identity_permit_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_member_identity_permit_actions_on_user"
  end

  create_table "member_requests", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "user_id"
    t.bigint "member_identity_id"
    t.string "create_instance_state", comment: "状态"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "请求名称"
    t.jsonb "payload", comment: "相关信息，会存储到member的payload里"
    t.jsonb "member_attributes", comment: "相关信息，会存储到member的attributes里"
    t.jsonb "options"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "state", comment: "状态"
    t.datetime "create_instance_timestamp", comment: "创建工作流的操作时间"
    t.index ["app_id"], name: "index_member_requests_on_app_id"
    t.index ["member_identity_id"], name: "index_member_requests_on_member_identity_id"
    t.index ["user_id"], name: "index_member_requests_on_user_id"
  end

  create_table "members", force: :cascade do |t|
    t.bigint "user_id"
    t.bigint "member_identity_id"
    t.bigint "app_id"
    t.bigint "member_request_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.jsonb "payload", comment: "payload payload存储的字段"
    t.jsonb "payload_summary", comment: "payload summary存储的字段"
    t.string "type", comment: "STI类型，可以是集团，或者在某些时候可能是学校这样的类型"
    t.string "code", comment: "用户标识"
    t.datetime "blocked_at", precision: nil
    t.boolean "is_blocked"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_members_on_app_id"
    t.index ["blocked_at"], name: "index_members_on_blocked_at"
    t.index ["is_blocked"], name: "index_members_on_is_blocked"
    t.index ["member_identity_id"], name: "index_members_on_member_identity_id"
    t.index ["member_request_id"], name: "index_members_on_member_request_id"
    t.index ["type"], name: "index_members_on_type"
    t.index ["user_id"], name: "index_members_on_user_id"
  end

  create_table "memberships", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "org_id"
    t.bigint "user_id"
    t.bigint "member_id"
    t.bigint "department_id"
    t.bigint "duty_id"
    t.datetime "effective_at", precision: nil, comment: "生效时间，可以为空"
    t.datetime "invalid_at", precision: nil, comment: "失效时间，可以为空"
    t.jsonb "payload"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "create_instance_state", comment: "创建工作流的状态"
    t.datetime "create_instance_timestamp", comment: "创建工作流的操作时间"
    t.index ["app_id"], name: "index_memberships_on_app_id"
    t.index ["department_id"], name: "index_memberships_on_department_id"
    t.index ["duty_id"], name: "index_memberships_on_duty_id"
    t.index ["member_id"], name: "index_memberships_on_member_id"
    t.index ["org_id"], name: "index_memberships_on_org_id"
    t.index ["user_id"], name: "index_memberships_on_user_id"
  end

  create_table "model_confs", force: :cascade do |t|
    t.bigint "model_define_id"
    t.string "name", comment: "名称"
    t.string "klass", comment: "类名"
    t.jsonb "conf"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["model_define_id"], name: "index_model_confs_on_model_define_id"
  end

  create_table "model_defines", force: :cascade do |t|
    t.string "klass", comment: "对应设置的Model名称"
    t.string "name", comment: "模型设置的中文名"
    t.string "association_chain", comment: "查找的关系列表", array: true
    t.string "klass_singular", comment: "自动生成的模型名称，因为路由上传是以underscore方式来传输，所以在这里需要能进行唯一性查找"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "model_settings", force: :cascade do |t|
    t.bigint "model_define_id"
    t.string "setable_type"
    t.bigint "setable_id"
    t.bigint "app_id"
    t.bigint "forms_template_id"
    t.string "flag", default: "model", comment: "同一个模型中的不同定义，其中model代表是这个对象的模型"
    t.string "flag_name", default: "模型定义", comment: "flag对应中文名称"
    t.jsonb "form"
    t.jsonb "form_setting"
    t.jsonb "api_config"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "bpm_workflow_id"
    t.bigint "ref_model_setting_id"
    t.string "ref_model_setting_flag", comment: "关联model_setting_flag"
    t.index ["app_id"], name: "index_model_settings_on_app_id"
    t.index ["bpm_workflow_id"], name: "index_model_settings_on_bpm_workflow_id"
    t.index ["forms_template_id"], name: "index_model_settings_on_forms_template_id"
    t.index ["model_define_id"], name: "index_model_settings_on_model_define_id"
    t.index ["ref_model_setting_id"], name: "index_model_settings_on_ref_model_setting_id"
    t.index ["setable_type", "setable_id"], name: "index_model_settings_on_setable"
  end

  create_table "mods", force: :cascade do |t|
    t.string "name", comment: "模块名称"
    t.string "key", comment: "模块对应查找的key值"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
  end

  create_table "notify_info_messages", force: :cascade do |t|
    t.bigint "app_id"
    t.string "user_type"
    t.bigint "user_id"
    t.bigint "create_user_id"
    t.string "notifyable_type"
    t.bigint "notifyable_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "flag", comment: "发送标识，保证唯一性"
    t.string "title", comment: "发送标题"
    t.string "content", comment: "发送内容"
    t.jsonb "meta", comment: "额外信息"
    t.string "url", comment: "链接地址"
    t.datetime "read_at", precision: nil
    t.boolean "is_read"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_notify_info_messages_on_app_id"
    t.index ["create_user_id"], name: "index_notify_info_messages_on_create_user_id"
    t.index ["is_read"], name: "index_notify_info_messages_on_is_read"
    t.index ["notifyable_type", "notifyable_id"], name: "index_notify_info_messages_on_notifyable"
    t.index ["read_at"], name: "index_notify_info_messages_on_read_at"
    t.index ["user_type", "user_id"], name: "index_notify_info_messages_on_user"
  end

  create_table "notify_like_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", null: false, comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id"], name: "notify_like_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_notify_like_actions_on_app_id"
    t.index ["real_user_id"], name: "index_notify_like_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "notify_like_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_notify_like_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "notify_like_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_notify_like_actions_on_user"
  end

  create_table "notify_sms_messages", force: :cascade do |t|
    t.string "notifyable_type"
    t.bigint "notifyable_id"
    t.string "mobile", comment: "发送手机号"
    t.string "title", comment: "发送标题"
    t.string "account", comment: "发送账号"
    t.string "content", comment: "发送内容"
    t.jsonb "response", default: {}, comment: "发送结果"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["notifyable_type", "notifyable_id"], name: "index_notify_sms_messages_on_notifyable"
  end

  create_table "notify_template_messages", force: :cascade do |t|
    t.bigint "app_id"
    t.string "notifyable_type"
    t.bigint "notifyable_id"
    t.bigint "user_id"
    t.string "type", comment: "STI属性"
    t.string "state", comment: "状态"
    t.string "oauth_app_id", comment: "微信服务名称的标识"
    t.string "openid", comment: "微信发送的openid"
    t.jsonb "body", comment: "发送内容"
    t.jsonb "response", comment: "发送结果"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_notify_template_messages_on_app_id"
    t.index ["notifyable_type", "notifyable_id"], name: "index_notify_template_messages_on_notifyable"
    t.index ["user_id"], name: "index_notify_template_messages_on_user_id"
  end

  create_table "notify_wechat_template_messages", force: :cascade do |t|
    t.string "notifyable_type"
    t.bigint "notifyable_id"
    t.bigint "user_id"
    t.string "oauth_app_id", comment: "微信服务名称的标识"
    t.string "openid", comment: "微信发送的openid"
    t.jsonb "message", comment: "发送内容"
    t.jsonb "response", default: {}, comment: "发送结果"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["notifyable_type", "notifyable_id"], name: "index_notify_wechat_template_messages_on_notifyable"
    t.index ["user_id"], name: "index_notify_wechat_template_messages_on_user_id"
  end

  create_table "org_clients", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "org_id"
    t.string "client_type"
    t.bigint "client_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_org_clients_on_app_id"
    t.index ["client_type", "client_id"], name: "index_org_clients_on_client"
    t.index ["org_id"], name: "index_org_clients_on_org_id"
  end

  create_table "org_hierarchies", force: :cascade do |t|
    t.integer "ancestor_id", null: false
    t.integer "descendant_id", null: false
    t.integer "generations", null: false
    t.datetime "created_at", precision: nil
    t.datetime "updated_at", precision: nil
    t.index ["ancestor_id", "descendant_id", "generations"], name: "org_anc_desc_idx", unique: true
    t.index ["descendant_id"], name: "org_desc_idx"
  end

  create_table "org_identities", force: :cascade do |t|
    t.bigint "app_id"
    t.string "name", comment: "组织类型名称"
    t.string "org_type", comment: "Org的类型，可能会关系到Org的STI"
    t.integer "orgs_count", comment: "关联的Org数量"
    t.jsonb "form", default: {}, comment: "Member配置的表单"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_org_identities_on_app_id"
  end

  create_table "org_member_identities", force: :cascade do |t|
    t.bigint "app_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "名称"
    t.string "code", comment: "标识"
    t.string "org_member_type", comment: "OrgMember的类型"
    t.jsonb "settle_in_form", default: {}, comment: "入驻申请表单"
    t.jsonb "postpone_form", default: {}, comment: "延期申请表单"
    t.jsonb "form", default: {}, comment: "表单"
    t.jsonb "config", default: {}, comment: "配置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_org_member_identities_on_app_id"
  end

  create_table "org_members", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "org_id"
    t.bigint "org_member_identity_id"
    t.jsonb "payload", comment: "payload payload存储的字段"
    t.jsonb "payload_summary", comment: "payload summary存储的字段"
    t.date "effective_at", comment: "生效时间"
    t.date "invalid_at", comment: "失效时间"
    t.string "type", comment: "STI类型"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_org_members_on_app_id"
    t.index ["effective_at"], name: "index_org_members_on_effective_at"
    t.index ["invalid_at"], name: "index_org_members_on_invalid_at"
    t.index ["org_id"], name: "index_org_members_on_org_id"
    t.index ["org_member_identity_id"], name: "index_org_members_on_org_member_identity_id"
  end

  create_table "org_ownerships", force: :cascade do |t|
    t.bigint "org_id"
    t.bigint "user_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["org_id"], name: "index_org_ownerships_on_org_id"
    t.index ["user_id"], name: "index_org_ownerships_on_user_id"
  end

  create_table "org_requests", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "user_id"
    t.bigint "member_identity_id"
    t.bigint "member_id"
    t.bigint "org_id"
    t.bigint "org_member_identity_id"
    t.bigint "org_member_id"
    t.bigint "org_identity_id"
    t.bigint "tanent_id"
    t.string "create_instance_state", comment: "状态"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "组织名称"
    t.string "code", comment: "组织标识"
    t.jsonb "org_payload", comment: "相关信息，会存储到org的payload里"
    t.jsonb "member_payload", comment: "相关信息，会存储到member的payload里"
    t.string "type", comment: "STI"
    t.string "state", default: "draft", comment: "状态: draft, approving"
    t.datetime "approval_at", precision: nil, comment: "审批通过时间"
    t.jsonb "options", comment: "其他预留信息"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.datetime "create_instance_timestamp", comment: "创建工作流的操作时间"
    t.index ["app_id"], name: "index_org_requests_on_app_id"
    t.index ["member_id"], name: "index_org_requests_on_member_id"
    t.index ["member_identity_id"], name: "index_org_requests_on_member_identity_id"
    t.index ["org_id"], name: "index_org_requests_on_org_id"
    t.index ["org_identity_id"], name: "index_org_requests_on_org_identity_id"
    t.index ["org_member_id"], name: "index_org_requests_on_org_member_id"
    t.index ["org_member_identity_id"], name: "index_org_requests_on_org_member_identity_id"
    t.index ["tanent_id"], name: "index_org_requests_on_tanent_id"
    t.index ["user_id"], name: "index_org_requests_on_user_id"
  end

  create_table "orgs", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "org_identity_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.jsonb "payload", comment: "payload payload存储的字段"
    t.jsonb "payload_summary", comment: "payload summary存储的字段"
    t.integer "parent_id", comment: "closure tree parent_id"
    t.string "code", comment: "组织标识"
    t.string "name", comment: "组织名称"
    t.string "short_name", comment: "组织简称"
    t.string "type", comment: "STI类型，可以是集团，或者在某些时候可能是学校这样的类型"
    t.integer "position", comment: "排序"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_orgs_on_app_id"
    t.index ["org_identity_id"], name: "index_orgs_on_org_identity_id"
  end

  create_table "page_settings", force: :cascade do |t|
    t.bigint "app_id"
    t.string "seq", comment: "编号"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "页面配置名称"
    t.jsonb "conf", comment: "页面配置的json结构"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_page_settings_on_app_id"
    t.index ["seq"], name: "index_page_settings_on_seq", unique: true
  end

  create_table "paper_trail_versions", force: :cascade do |t|
    t.string "operator_type"
    t.bigint "operator_id"
    t.string "item_type", null: false
    t.integer "item_id", null: false
    t.string "event", null: false, comment: "create, update, destroy"
    t.string "whodunnit", comment: "whodunnit"
    t.jsonb "object", comment: "object attributes"
    t.jsonb "object_changes", comment: "object changes"
    t.jsonb "controller_info", comment: "controller info"
    t.jsonb "model_info", comment: "model info"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["item_type", "item_id"], name: "index_versions_on_item_id_item_type"
    t.index ["operator_type", "operator_id"], name: "index_paper_trail_versions_on_operator"
  end

  create_table "permit_controller_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "mod_id"
    t.bigint "tanent_id"
    t.bigint "user_id"
    t.string "platform", comment: "平台"
    t.string "name", comment: "action名称"
    t.string "klass", comment: "controller"
    t.string "action", comment: "action"
    t.jsonb "whitelist", default: {}
    t.jsonb "blacklist", default: {}
    t.jsonb "payload", default: {}
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_permit_controller_actions_on_app_id"
    t.index ["mod_id"], name: "index_permit_controller_actions_on_mod_id"
    t.index ["tanent_id"], name: "index_permit_controller_actions_on_tanent_id"
    t.index ["user_id"], name: "index_permit_controller_actions_on_user_id"
  end

  create_table "permit_permissions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "mod_id"
    t.bigint "tanent_id"
    t.bigint "user_id"
    t.string "platform", comment: "平台"
    t.string "aname", comment: "action名称"
    t.string "cname", comment: "controller名称"
    t.string "klass", comment: "controller"
    t.string "action", comment: "action"
    t.jsonb "whitelist"
    t.jsonb "blacklist"
    t.jsonb "payload"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "route_setting_id"
    t.integer "position"
    t.string "key"
    t.index ["app_id"], name: "index_permit_permissions_on_app_id"
    t.index ["mod_id"], name: "index_permit_permissions_on_mod_id"
    t.index ["route_setting_id"], name: "index_permit_permissions_on_route_setting_id"
    t.index ["tanent_id"], name: "index_permit_permissions_on_tanent_id"
    t.index ["user_id"], name: "index_permit_permissions_on_user_id"
  end

  create_table "pundit_permit_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", null: false, comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id"], name: "pundit_permit_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_pundit_permit_actions_on_app_id"
    t.index ["real_user_id"], name: "index_pundit_permit_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "pundit_permit_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_pundit_permit_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "pundit_permit_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_pundit_permit_actions_on_user"
  end

  create_table "reg_qrcodes", force: :cascade do |t|
    t.bigint "app_id"
    t.string "record_type"
    t.bigint "record_id"
    t.bigint "creator_id"
    t.string "type", comment: "STI属性"
    t.datetime "effective_at", precision: nil, comment: "生效时间"
    t.datetime "invalid_at", precision: nil, comment: "失效时间"
    t.string "code", comment: "码"
    t.string "state", comment: "状态"
    t.string "mod", comment: "模式"
    t.jsonb "payload", comment: "额外字段"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_reg_qrcodes_on_app_id"
    t.index ["creator_id"], name: "index_reg_qrcodes_on_creator_id"
    t.index ["effective_at"], name: "index_reg_qrcodes_on_effective_at"
    t.index ["invalid_at"], name: "index_reg_qrcodes_on_invalid_at"
    t.index ["record_type", "record_id"], name: "index_reg_qrcodes_on_record"
    t.index ["type"], name: "index_reg_qrcodes_on_type"
  end

  create_table "reg_records", force: :cascade do |t|
    t.bigint "app_id"
    t.string "origin_type"
    t.bigint "origin_id"
    t.string "source_type"
    t.bigint "source_id"
    t.bigint "user_id"
    t.bigint "creator_id"
    t.bigint "qrcode_id"
    t.string "type", comment: "STI属性"
    t.string "state", comment: "状态"
    t.datetime "register_at", precision: nil, comment: "扫码时间"
    t.jsonb "payload", comment: "额外字段"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_reg_records_on_app_id"
    t.index ["creator_id"], name: "index_reg_records_on_creator_id"
    t.index ["origin_type", "origin_id"], name: "index_reg_records_on_origin"
    t.index ["qrcode_id"], name: "index_reg_records_on_qrcode_id"
    t.index ["source_type", "source_id"], name: "index_reg_records_on_source"
    t.index ["type"], name: "index_reg_records_on_type"
    t.index ["user_id"], name: "index_reg_records_on_user_id"
  end

  create_table "res_book_relations", force: :cascade do |t|
    t.bigint "book_id"
    t.string "source_type"
    t.bigint "source_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["book_id"], name: "index_res_book_relations_on_book_id"
    t.index ["source_type", "source_id"], name: "index_res_book_relations_on_source"
  end

  create_table "res_books", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "名称"
    t.text "desc", comment: "说明"
    t.string "relation_type", comment: "通讯录的类型"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_res_books_on_app_id"
    t.index ["user_id"], name: "index_res_books_on_user_id"
  end

  create_table "res_member_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", null: false, comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id"], name: "res_member_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_res_member_actions_on_app_id"
    t.index ["real_user_id"], name: "index_res_member_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "res_member_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_res_member_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "res_member_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_res_member_actions_on_user"
  end

  create_table "res_permit_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", null: false, comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id"], name: "res_permit_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_res_permit_actions_on_app_id"
    t.index ["real_user_id"], name: "index_res_permit_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "res_permit_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_res_permit_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "res_permit_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_res_permit_actions_on_user"
  end

  create_table "res_tags", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "org_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "标签名称"
    t.string "color", comment: "标签颜色"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_res_tags_on_app_id"
    t.index ["org_id"], name: "index_res_tags_on_org_id"
  end

  create_table "res_tags_relations", force: :cascade do |t|
    t.bigint "tag_id"
    t.bigint "user_id"
    t.bigint "org_id"
    t.bigint "member_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["member_id"], name: "index_res_tags_relations_on_member_id"
    t.index ["org_id"], name: "index_res_tags_relations_on_org_id"
    t.index ["tag_id"], name: "index_res_tags_relations_on_tag_id"
    t.index ["user_id"], name: "index_res_tags_relations_on_user_id"
  end

  create_table "res_user_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", null: false, comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id"], name: "res_user_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_res_user_actions_on_app_id"
    t.index ["real_user_id"], name: "index_res_user_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "res_user_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_res_user_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "res_user_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_res_user_actions_on_user"
  end

  create_table "role_permission_relations", force: :cascade do |t|
    t.bigint "role_id"
    t.string "permission_type"
    t.bigint "permission_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["permission_type", "permission_id"], name: "index_role_permission_relations_on_permission"
    t.index ["role_id"], name: "index_role_permission_relations_on_role_id"
  end

  create_table "role_permit_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id"], name: "role_permit_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_role_permit_actions_on_app_id"
    t.index ["real_user_id"], name: "index_role_permit_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "role_permit_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_role_permit_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "role_permit_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_role_permit_actions_on_user"
  end

  create_table "roles", force: :cascade do |t|
    t.string "resource_type"
    t.bigint "resource_id"
    t.bigint "mod_id"
    t.string "name", comment: "权限标识"
    t.string "label", comment: "显示名称"
    t.jsonb "permits", default: {}
    t.boolean "permit_enable", comment: "是否开启permit的按钮"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "pinyin", comment: "拼音,排序用"
    t.index ["label"], name: "index_roles_on_label"
    t.index ["mod_id"], name: "index_roles_on_mod_id"
    t.index ["name"], name: "index_roles_on_name"
    t.index ["resource_type", "resource_id"], name: "index_roles_on_resource"
  end

  create_table "route_settings", force: :cascade do |t|
    t.bigint "app_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "module名称"
    t.jsonb "conf", comment: "module导出路由"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "mod_id"
    t.index ["app_id"], name: "index_route_settings_on_app_id"
    t.index ["mod_id"], name: "index_route_settings_on_mod_id"
  end

  create_table "serve_activities", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "submodule_id"
    t.string "source_type"
    t.bigint "source_id"
    t.string "name", comment: "名称"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "type", comment: "STI属性"
    t.datetime "effective_at", precision: nil, comment: "生效时间"
    t.datetime "invalid_at", precision: nil, comment: "失效时间"
    t.jsonb "views", default: {}
    t.boolean "view_enable", comment: "是否开启permit的按钮"
    t.jsonb "uses", default: {}
    t.boolean "use_enable", comment: "是否开启permit的按钮"
    t.string "state"
    t.jsonb "cover_image", comment: "封面图"
    t.integer "position", comment: "排序"
    t.jsonb "content", comment: "详情，body / images / video"
    t.text "address", comment: "地址"
    t.jsonb "layout", comment: "卡片样式"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.bigint "creator_id"
    t.integer "views_count", default: 0, comment: "浏览数量"
    t.jsonb "icon", comment: "icon"
    t.string "create_instance_state", comment: "创建工作流的状态"
    t.jsonb "manages", default: {}
    t.boolean "manage_enable", comment: "是否开启permit的按钮"
    t.datetime "hotted_at", precision: nil
    t.boolean "is_hotted"
    t.string "target_type"
    t.bigint "target_id"
    t.jsonb "attachments", comment: "附件"
    t.datetime "create_instance_timestamp", comment: "创建工作流的操作时间"
    t.index ["app_id"], name: "index_serve_activities_on_app_id"
    t.index ["creator_id"], name: "index_serve_activities_on_creator_id"
    t.index ["effective_at"], name: "index_serve_activities_on_effective_at"
    t.index ["invalid_at"], name: "index_serve_activities_on_invalid_at"
    t.index ["source_type", "source_id"], name: "index_serve_activities_on_source"
    t.index ["submodule_id"], name: "index_serve_activities_on_submodule_id"
    t.index ["target_type", "target_id"], name: "index_serve_activities_on_target"
    t.index ["type"], name: "index_serve_activities_on_type"
  end

  create_table "serve_activity_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", null: false, comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id"], name: "serve_activity_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_serve_activity_actions_on_app_id"
    t.index ["real_user_id"], name: "index_serve_activity_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "serve_activity_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_serve_activity_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "serve_activity_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_serve_activity_actions_on_user"
  end

  create_table "serve_banners", force: :cascade do |t|
    t.bigint "app_id"
    t.string "source_type"
    t.bigint "source_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.datetime "effective_at", precision: nil, comment: "生效时间"
    t.datetime "invalid_at", precision: nil, comment: "失效时间"
    t.datetime "published_at", precision: nil
    t.boolean "is_published"
    t.string "name", comment: "轮播图名称"
    t.integer "position", comment: "位置"
    t.jsonb "cover_image", comment: "封面图"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.jsonb "conf", comment: "呈现方式，根据前端需要设置，例如可以是PC或者Mobile的展现等"
    t.bigint "submodule_id"
    t.index ["app_id"], name: "index_serve_banners_on_app_id"
    t.index ["effective_at"], name: "index_serve_banners_on_effective_at"
    t.index ["invalid_at"], name: "index_serve_banners_on_invalid_at"
    t.index ["is_published"], name: "index_serve_banners_on_is_published"
    t.index ["published_at"], name: "index_serve_banners_on_published_at"
    t.index ["source_type", "source_id"], name: "index_serve_banners_on_source"
    t.index ["submodule_id"], name: "index_serve_banners_on_submodule_id"
  end

  create_table "serve_catalogs", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "submodule_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.jsonb "views", default: {}
    t.boolean "view_enable", comment: "是否开启permit的按钮"
    t.jsonb "uses", default: {}
    t.boolean "use_enable", comment: "是否开启permit的按钮"
    t.string "name", comment: "名称"
    t.integer "position", comment: "排序"
    t.jsonb "cover_image", comment: "封面图"
    t.jsonb "layout", comment: "布局方式配置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "state", comment: "状态"
    t.jsonb "icon", comment: "icon"
    t.bigint "creator_id"
    t.jsonb "manages", default: {}
    t.boolean "manage_enable", comment: "是否开启permit的按钮"
    t.string "target_type"
    t.bigint "target_id"
    t.index ["app_id"], name: "index_serve_catalogs_on_app_id"
    t.index ["creator_id"], name: "index_serve_catalogs_on_creator_id"
    t.index ["submodule_id"], name: "index_serve_catalogs_on_submodule_id"
    t.index ["target_type", "target_id"], name: "index_serve_catalogs_on_target"
  end

  create_table "serve_entries", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "submodule_id"
    t.bigint "activity_id"
    t.string "source_type"
    t.bigint "source_id"
    t.bigint "user_id"
    t.string "name", comment: "名称"
    t.string "type", comment: "STI属性"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "state", comment: "状态"
    t.datetime "order_at", precision: nil, comment: "报名时间"
    t.index ["activity_id"], name: "index_serve_entries_on_activity_id"
    t.index ["app_id"], name: "index_serve_entries_on_app_id"
    t.index ["source_type", "source_id"], name: "index_serve_entries_on_source"
    t.index ["submodule_id"], name: "index_serve_entries_on_submodule_id"
    t.index ["type"], name: "index_serve_entries_on_type"
    t.index ["user_id"], name: "index_serve_entries_on_user_id"
  end

  create_table "serve_groups", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "submodule_id"
    t.string "type", comment: "STI属性"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "name", comment: "名称"
    t.string "state", comment: "状态"
    t.integer "position", comment: "排序"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_serve_groups_on_app_id"
    t.index ["submodule_id"], name: "index_serve_groups_on_submodule_id"
  end

  create_table "serve_manage_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", null: false, comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id"], name: "serve_manage_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_serve_manage_actions_on_app_id"
    t.index ["real_user_id"], name: "index_serve_manage_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "serve_manage_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_serve_manage_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "serve_manage_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_serve_manage_actions_on_user"
  end

  create_table "serve_permit_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", null: false, comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id"], name: "serve_permit_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_serve_permit_actions_on_app_id"
    t.index ["real_user_id"], name: "index_serve_permit_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "serve_permit_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_serve_permit_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "serve_permit_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_serve_permit_actions_on_user"
  end

  create_table "serve_submodules", force: :cascade do |t|
    t.bigint "app_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "ancestry", comment: "树形结构"
    t.integer "depth", comment: "树结构深度"
    t.integer "children_count", default: 0, comment: "子对象的数据"
    t.jsonb "manages", default: {}
    t.boolean "manage_enable", comment: "是否开启permit的按钮"
    t.string "name", comment: "名称"
    t.integer "position", comment: "排序"
    t.jsonb "cover_image", comment: "封面图"
    t.string "key", comment: "程序内使用的标识"
    t.jsonb "layout", comment: "布局方式配置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.string "state", comment: "状态"
    t.index ["ancestry"], name: "index_serve_submodules_on_ancestry"
    t.index ["app_id"], name: "index_serve_submodules_on_app_id"
  end

  create_table "serve_tags", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "submodule_id"
    t.string "name", comment: "名称"
    t.string "color", comment: "颜色"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "position", comment: "排序"
    t.jsonb "option", comment: "配置项"
    t.integer "activity_count", comment: "活动数量"
    t.index ["app_id"], name: "index_serve_tags_on_app_id"
    t.index ["submodule_id"], name: "index_serve_tags_on_submodule_id"
  end

  create_table "state_activate_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", null: false, comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id"], name: "state_activate_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_state_activate_actions_on_app_id"
    t.index ["real_user_id"], name: "index_state_activate_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "state_activate_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_state_activate_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "state_activate_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_state_activate_actions_on_user"
  end

  create_table "state_bpm_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", null: false, comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id"], name: "state_bpm_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_state_bpm_actions_on_app_id"
    t.index ["real_user_id"], name: "index_state_bpm_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "state_bpm_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_state_bpm_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "state_bpm_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_state_bpm_actions_on_user"
  end

  create_table "state_events", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "machine_id"
    t.bigint "transition_id"
    t.string "user_type"
    t.bigint "user_id"
    t.bigint "source_id"
    t.bigint "target_id"
    t.string "eventable_type"
    t.bigint "eventable_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "type", comment: "STI"
    t.string "state"
    t.string "state_attr_name", comment: "状态机对应的模型属性名称"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_state_events_on_app_id"
    t.index ["eventable_type", "eventable_id"], name: "index_state_events_on_eventable"
    t.index ["machine_id"], name: "index_state_events_on_machine_id"
    t.index ["source_id"], name: "index_state_events_on_source_id"
    t.index ["target_id"], name: "index_state_events_on_target_id"
    t.index ["transition_id"], name: "index_state_events_on_transition_id"
    t.index ["user_type", "user_id"], name: "index_state_events_on_user"
  end

  create_table "state_machines", force: :cascade do |t|
    t.bigint "app_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.jsonb "permits", default: {}
    t.boolean "permit_enable", comment: "是否开启permit的按钮"
    t.string "name", comment: "状态机名称"
    t.string "state_attr_name", comment: "状态机对应模型属性名称"
    t.string "klass", comment: "类名"
    t.string "klass_singular", comment: "自动生成的模型名称，因为路由上传是以underscore方式来传输，所以在这里需要能进行唯一性查找"
    t.string "flag", comment: "程序使用标识"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_state_machines_on_app_id"
  end

  create_table "state_places", force: :cascade do |t|
    t.bigint "machine_id"
    t.string "seq", comment: "place的唯一序列号，保持一致"
    t.string "name", comment: "节点名称"
    t.string "state", comment: "节点状态"
    t.string "type", comment: "STI"
    t.integer "position", comment: "排序"
    t.jsonb "options"
    t.jsonb "trigger_options"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["machine_id"], name: "index_state_places_on_machine_id"
  end

  create_table "state_token_defines", force: :cascade do |t|
    t.bigint "machine_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "seq", comment: "token_define的唯一序列号，保持一致"
    t.string "name", comment: "名称"
    t.string "type", comment: "STI"
    t.string "token_type", comment: "对应token的type"
    t.string "token_flag", comment: "对应token的flag"
    t.string "token_default_state", comment: "token生成的默认state"
    t.jsonb "token_form"
    t.jsonb "options", comment: "配置信息"
    t.jsonb "limit_options"
    t.jsonb "user_options"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["machine_id"], name: "index_state_token_defines_on_machine_id"
  end

  create_table "state_tokens", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "machine_id"
    t.bigint "event_id"
    t.bigint "transition_id"
    t.bigint "token_define_id"
    t.string "token_source_type"
    t.bigint "token_source_id"
    t.string "eventable_type"
    t.bigint "eventable_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.jsonb "payload", comment: "payload payload存储的字段"
    t.jsonb "payload_summary", comment: "payload summary存储的字段"
    t.string "type", comment: "STI"
    t.string "name", comment: "处理节点名称"
    t.string "flag", comment: "处理节点flag"
    t.string "user_name", comment: "user的名称"
    t.string "state"
    t.jsonb "token_source_attributes", comment: "token source的attributes缓存"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_state_tokens_on_app_id"
    t.index ["event_id"], name: "index_state_tokens_on_event_id"
    t.index ["eventable_type", "eventable_id"], name: "index_state_tokens_on_eventable"
    t.index ["machine_id"], name: "index_state_tokens_on_machine_id"
    t.index ["token_define_id"], name: "index_state_tokens_on_token_define_id"
    t.index ["token_source_type", "token_source_id"], name: "index_state_tokens_on_token_source"
    t.index ["transition_id"], name: "index_state_tokens_on_transition_id"
    t.index ["user_type", "user_id"], name: "index_state_tokens_on_user"
  end

  create_table "state_transitions", force: :cascade do |t|
    t.bigint "machine_id"
    t.bigint "source_id"
    t.bigint "target_id"
    t.bigint "terminate_place_id"
    t.jsonb "permits", default: {}
    t.boolean "permit_enable", comment: "是否开启permit的按钮"
    t.string "type", comment: "STI"
    t.string "seq", comment: "transition的唯一序列号，保持一致"
    t.string "name", comment: "名称"
    t.string "event_name", comment: "操作的英文名称"
    t.string "flag", comment: "程序使用的标记位"
    t.boolean "auto_trigger", comment: "是否自动触发"
    t.jsonb "options", comment: "状态转换的具体配置信息，根据STI的类型不同而不同"
    t.jsonb "trigger_options"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["machine_id"], name: "index_state_transitions_on_machine_id"
    t.index ["source_id"], name: "index_state_transitions_on_source_id"
    t.index ["target_id"], name: "index_state_transitions_on_target_id"
    t.index ["terminate_place_id"], name: "index_state_transitions_on_terminate_place_id"
  end

  create_table "tanent_permit_actions", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "real_user_id"
    t.string "target_type"
    t.bigint "target_id"
    t.string "user_type"
    t.bigint "user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.string "action_type", null: false, comment: "具体action的业务flag"
    t.string "action_option", comment: "前端可以设置的信息"
    t.string "action_flag", comment: "后端使用的逻辑字段，前端不能设置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["action_type", "target_type", "target_id", "user_type", "user_id"], name: "tanent_permit_actions_uk_action_target_user", unique: true
    t.index ["app_id"], name: "index_tanent_permit_actions_on_app_id"
    t.index ["real_user_id"], name: "index_tanent_permit_actions_on_real_user_id"
    t.index ["target_type", "target_id", "action_type"], name: "tanent_permit_actions_target_action_type_index"
    t.index ["target_type", "target_id"], name: "index_tanent_permit_actions_on_target"
    t.index ["user_type", "user_id", "action_type"], name: "tanent_permit_actions_user_action_type_index"
    t.index ["user_type", "user_id"], name: "index_tanent_permit_actions_on_user"
  end

  create_table "tanent_resources", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "tanent_id"
    t.string "resource_type"
    t.bigint "resource_id"
    t.jsonb "payload", comment: "存额外信息"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_tanent_resources_on_app_id"
    t.index ["resource_type", "resource_id"], name: "index_tanent_resources_on_resource"
    t.index ["tanent_id"], name: "index_tanent_resources_on_tanent_id"
  end

  create_table "tanents", force: :cascade do |t|
    t.bigint "app_id"
    t.string "code", comment: "租户标识"
    t.string "name", comment: "租户名称"
    t.jsonb "manages", default: {}
    t.boolean "manage_enable", comment: "是否开启permit的按钮"
    t.jsonb "config", comment: "配置"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_tanents_on_app_id"
  end

  create_table "tofu_entries", force: :cascade do |t|
    t.string "source_type"
    t.bigint "source_id"
    t.jsonb "permits", default: {}
    t.boolean "permit_enable", comment: "是否开启permit的按钮"
    t.string "ancestry", comment: "树形结构"
    t.string "platform", default: "pc"
    t.string "layout", comment: "点击以后前端使用的layout"
    t.string "type", comment: "STI"
    t.string "name", comment: "名称"
    t.string "desc", comment: "描述"
    t.text "icon", comment: "显示的图片或者图标"
    t.text "url", comment: "跳转地址，如果只是menu，可以为空"
    t.string "open_mode", comment: "打开页面的方式"
    t.integer "position", comment: "位置"
    t.integer "depth", comment: "树结构深度"
    t.integer "children_count", default: 0, comment: "子对象的数据"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["ancestry"], name: "index_tofu_entries_on_ancestry"
    t.index ["source_type", "source_id"], name: "index_tofu_entries_on_source"
  end

  create_table "users", force: :cascade do |t|
    t.bigint "app_id"
    t.bigint "tanent_id"
    t.bigint "ref_user_id"
    t.string "model_flag", comment: "model flag，对应model_setting的flag"
    t.jsonb "model_payload", comment: "model payload存储的字段"
    t.jsonb "model_payload_summary", comment: "model summary存储的字段"
    t.jsonb "model_detail", comment: "model 存储的详情字段"
    t.datetime "effective_at", precision: nil, comment: "生效时间"
    t.datetime "invalid_at", precision: nil, comment: "失效时间"
    t.string "account", comment: "账号，关联登录"
    t.string "name", comment: "用户姓名"
    t.string "nickname", comment: "用户昵称"
    t.string "pinyin", comment: "用户名拼音"
    t.string "mobile", comment: "用户手机号"
    t.string "email", comment: "用户邮箱"
    t.string "gender", comment: "性别"
    t.jsonb "avatar", comment: "用户头像"
    t.string "identity_id", comment: "证件号码，需要时候可以作为唯一标识"
    t.datetime "last_visit_at", precision: nil, comment: "最后访问时间"
    t.datetime "blocked_at", precision: nil
    t.boolean "is_blocked"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["account"], name: "index_users_on_account"
    t.index ["app_id"], name: "index_users_on_app_id"
    t.index ["blocked_at"], name: "index_users_on_blocked_at"
    t.index ["effective_at"], name: "index_users_on_effective_at"
    t.index ["identity_id"], name: "index_users_on_identity_id"
    t.index ["invalid_at"], name: "index_users_on_invalid_at"
    t.index ["is_blocked"], name: "index_users_on_is_blocked"
    t.index ["ref_user_id"], name: "index_users_on_ref_user_id"
    t.index ["tanent_id"], name: "index_users_on_tanent_id"
  end

  create_table "users_roles", force: :cascade do |t|
    t.bigint "user_id"
    t.bigint "role_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["role_id"], name: "index_users_roles_on_role_id"
    t.index ["user_id"], name: "index_users_roles_on_user_id"
  end

  create_table "version_relationships", force: :cascade do |t|
    t.bigint "app_id"
    t.string "resource_type"
    t.bigint "resource_id"
    t.string "real_resource_type"
    t.bigint "real_resource_id"
    t.string "version_type"
    t.bigint "version_id"
    t.string "operator_type"
    t.bigint "operator_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["app_id"], name: "index_version_relationships_on_app_id"
    t.index ["operator_type", "operator_id"], name: "index_version_relationships_on_operator"
    t.index ["real_resource_type", "real_resource_id"], name: "index_version_relationships_on_real_resource"
    t.index ["resource_type", "resource_id"], name: "index_version_relationships_on_resource"
    t.index ["version_type", "version_id"], name: "index_version_relationships_on_version"
  end

end
