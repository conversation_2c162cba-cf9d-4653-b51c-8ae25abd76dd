require 'rails_helper'

RSpec.describe Bot::ResponseValidator, type: :service do
  describe '.validate' do
    it '应该创建实例并调用validate方法' do
      response = { messages: [{ content_type: 'text', content: 'test' }] }
      preprocessing_info = {}
      
      expect(described_class).to receive(:new).and_call_original
      expect_any_instance_of(described_class).to receive(:validate).with(response, preprocessing_info)
      
      described_class.validate(response, preprocessing_info)
    end
  end

  describe '#validate' do
    let(:validator) { described_class.new }

    context '通用响应验证' do
      it '应该验证包含文本消息的有效响应' do
        response = {
          messages: [
            { content_type: 'text', content: '这是一个足够长的有效响应内容' }
          ]
        }
        
        result = validator.validate(response)
        
        expect(result).to be_a(Bot::ResponseValidator::ValidationResult)
        expect(result.valid?).to be true
        expect(result.issues).to be_empty
        expect(result.suggestions).to be_empty
      end

      it '应该检测缺少文本响应的问题' do
        response = {
          messages: [
            { content_type: 'artifact', content: { type: 'list' } }
          ]
        }
        
        result = validator.validate(response)
        
        expect(result.valid?).to be false
        expect(result.issues).to include('缺少文本响应')
        expect(result.suggestions).to include('应该包含文本回答')
      end

      it '应该检测响应过于简短的问题' do
        response = {
          messages: [
            { content_type: 'text', content: '短' }
          ]
        }
        
        result = validator.validate(response)
        
        expect(result.valid?).to be false
        expect(result.issues).to include('响应过于简短')
        expect(result.suggestions).to include('应该提供更详细的回答')
      end

      it '应该处理空消息数组' do
        response = { messages: [] }
        
        result = validator.validate(response)
        
        expect(result.valid?).to be false
        expect(result.issues).to include('缺少文本响应')
      end

      it '应该处理缺少messages字段的响应' do
        response = {}
        
        result = validator.validate(response)
        
        expect(result.valid?).to be false
        expect(result.issues).to include('缺少文本响应')
      end
    end

    context '强制工具调用响应验证' do
      let(:preprocessing_info) do
        {
          forced_tool_call: true,
          query_type: :activity_query,
          tool_result: { message: '查询结果', total_count: 10 }
        }
      end

      it '应该验证活动查询的有效响应' do
        response = {
          messages: [
            { content_type: 'text', content: '根据查询结果，找到了10个素材' },
            { content_type: 'artifact', content: { type: 'activity_list' } }
          ]
        }
        
        result = validator.validate(response, preprocessing_info)
        
        expect(result.valid?).to be true
        expect(result.issues).to be_empty
      end

      it '应该检测缺少artifact的问题' do
        response = {
          messages: [
            { content_type: 'text', content: '这是一个有效的文本响应' }
          ]
        }
        
        result = validator.validate(response, preprocessing_info)
        
        expect(result.valid?).to be false
        expect(result.issues).to include('缺少查询结果组件')
        expect(result.suggestions).to include('应该包含ActivityListArtifact组件')
      end

      it '应该检测响应内容与工具结果不匹配的问题' do
        response = {
          messages: [
            { content_type: 'text', content: '无法找到相关信息' },
            { content_type: 'artifact', content: { type: 'activity_list' } }
          ]
        }
        
        result = validator.validate(response, preprocessing_info)
        
        expect(result.valid?).to be false
        expect(result.issues).to include('响应内容与查询结果不匹配')
        expect(result.suggestions).to include('响应应该反映实际的查询结果')
      end
    end

    context '非活动查询的强制工具调用' do
      let(:preprocessing_info) do
        {
          forced_tool_call: true,
          query_type: :unknown_query,
          tool_result: { message: '未知查询结果' }
        }
      end

      it '应该跳过特定验证' do
        response = {
          messages: [
            { content_type: 'text', content: '这是一个有效的响应' }
          ]
        }
        
        result = validator.validate(response, preprocessing_info)
        
        expect(result.valid?).to be true
      end
    end

    context '异常情况处理' do
      it '应该处理nil响应' do
        expect { validator.validate(nil) }.not_to raise_error
        
        result = validator.validate(nil)
        expect(result.valid?).to be false
      end

      it '应该处理空预处理信息' do
        response = {
          messages: [
            { content_type: 'text', content: '有效的响应内容' }
          ]
        }
        
        expect { validator.validate(response, {}) }.not_to raise_error
        
        result = validator.validate(response, {})
        expect(result.valid?).to be true
      end

      it '应该处理nil预处理信息' do
        response = {
          messages: [
            { content_type: 'text', content: '有效的响应内容' }
          ]
        }
        
        expect { validator.validate(response, nil) }.not_to raise_error
        
        result = validator.validate(response, nil)
        expect(result.valid?).to be true
      end
    end
  end

  describe 'ValidationResult' do
    describe '#initialize' do
      it '应该正确初始化有效结果' do
        result = Bot::ResponseValidator::ValidationResult.new(true, [], [])
        
        expect(result.valid?).to be true
        expect(result.issues).to be_empty
        expect(result.suggestions).to be_empty
        expect(result.has_issues?).to be false
      end

      it '应该正确初始化无效结果' do
        issues = ['问题1', '问题2']
        suggestions = ['建议1', '建议2']
        result = Bot::ResponseValidator::ValidationResult.new(false, issues, suggestions)
        
        expect(result.valid?).to be false
        expect(result.issues).to eq(issues)
        expect(result.suggestions).to eq(suggestions)
        expect(result.has_issues?).to be true
      end
    end

    describe '#has_issues?' do
      it '当有问题时应该返回true' do
        result = Bot::ResponseValidator::ValidationResult.new(false, ['问题'], [])
        expect(result.has_issues?).to be true
      end

      it '当没有问题时应该返回false' do
        result = Bot::ResponseValidator::ValidationResult.new(true, [], [])
        expect(result.has_issues?).to be false
      end
    end
  end

  describe '性能测试' do
    let(:validator) { described_class.new }

    it '应该在合理时间内完成验证' do
      response = {
        messages: [
          { content_type: 'text', content: '这是一个有效的响应内容' }
        ]
      }
      
      start_time = Time.current
      100.times { validator.validate(response) }
      end_time = Time.current
      
      expect(end_time - start_time).to be < 1.second
    end
  end
end
