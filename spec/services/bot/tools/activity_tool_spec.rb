require 'rails_helper'

RSpec.describe Bot::Tools::ActivityQueryTool, type: :service do
  let(:tool) { described_class.new }

  # 创建测试数据
  before do
    # 创建一些测试活动数据
    @published_activity = create(:activity, state: 'published', created_at: 1.week.ago)
    @pending_activity = create(:activity, state: 'pending', created_at: 2.days.ago)
    @old_activity = create(:activity, state: 'published', created_at: 2.months.ago)
  end

  describe '#query_activities' do
    context '基本功能测试' do
      it '应该返回正确的数据结构' do
        result = tool.query_activities(query: '所有素材')

        expect(result).to be_a(Hash)
        expect(result).to include(:message, :total_count, :conditions)
        expect(result[:message]).to be_a(String)
        expect(result[:total_count]).to be_a(Integer)
        expect(result[:conditions]).to be_a(Hash)
      end

      it '应该返回非空的消息内容' do
        result = tool.query_activities(query: '素材总数')

        expect(result[:message]).not_to be_empty
        expect(result[:message].length).to be > 10
      end
    end

    context '时间范围查询' do
      it '应该正确处理"最近一周"的查询' do
        result = tool.query_activities(query: '最近一周的素材')

        expect(result[:conditions]).to have_key('created_at_gteq')
        expect(result[:total_count]).to be >= 1 # 至少包含1周前创建的活动
        expect(result[:message]).to include('素材')
      end

      it '应该正确处理"最近一个月"的查询' do
        result = tool.query_activities(query: '最近一个月的素材')

        expect(result[:conditions]).to have_key('created_at_gteq')
        expect(result[:total_count]).to be >= 2 # 应该包含1周前和2天前的活动
      end

      it '应该正确处理"过去两个月"的查询' do
        result = tool.query_activities(query: '过去两个月的素材')

        expect(result[:conditions]).to have_key('created_at_gteq')
        expect(result[:total_count]).to be >= 3 # 应该包含所有测试数据
      end
    end

    context '状态查询' do
      it '应该正确处理已发布状态查询' do
        result = tool.query_activities(query: '已发布的素材')

        expect(result[:conditions]).to include('state_eq' => 'published')
        expect(result[:total_count]).to be >= 2 # 两个已发布的活动
        expect(result[:message]).to include('已发布')
      end

      it '应该正确处理待发布状态查询' do
        result = tool.query_activities(query: '待发布的素材')

        expect(result[:conditions]).to include('state_eq' => 'pending')
        expect(result[:total_count]).to be >= 1 # 一个待发布的活动
        expect(result[:message]).to include('待发布')
      end
    end

    context '数量查询' do
      it '应该正确处理总数查询' do
        result = tool.query_activities(query: '有多少个素材')

        expect(result[:message]).to include('总数')
        expect(result[:message]).to match(/\d+个/)
        expect(result[:total_count]).to be >= 3
      end

      it '应该正确处理统计查询' do
        result = tool.query_activities(query: '素材数量统计')

        expect(result[:message]).to include('素材')
        expect(result[:total_count]).to be_a(Integer)
      end
    end

    context '组合查询' do
      it '应该正确处理时间和状态的组合查询' do
        result = tool.query_activities(query: '最近一个月已发布的素材')

        expect(result[:conditions]).to have_key('created_at_gteq')
        expect(result[:conditions]).to include('state_eq' => 'published')
        expect(result[:total_count]).to be >= 1
      end
    end

    context '异常情况处理' do
      it '应该处理空查询' do
        expect { tool.query_activities(query: '') }.not_to raise_error

        result = tool.query_activities(query: '')
        expect(result).to be_a(Hash)
        expect(result).to have_key(:total_count)
      end

      it '应该处理无关查询' do
        result = tool.query_activities(query: '天气怎么样')

        expect(result).to be_a(Hash)
        expect(result[:total_count]).to be_a(Integer)
        expect(result[:message]).to be_a(String)
      end

      it '应该处理nil查询' do
        expect { tool.query_activities(query: nil) }.not_to raise_error
      end
    end

    context 'Artifact创建' do
      it '应该创建正确的artifact' do
        result = tool.query_activities(query: '所有素材')

        # 检查context中是否创建了artifact
        artifact = tool.instance_variable_get(:@context)&.dig(:query_activities, :artifact)
        expect(artifact).to be_present
        expect(artifact).to be_a(Bot::ActivityListArtifact)
      end
    end
  end

  describe '错误处理' do
    it '应该处理数据库连接错误' do
      allow(Serve::Activity).to receive(:ransack).and_raise(ActiveRecord::ConnectionNotEstablished)

      expect { tool.query_activities(query: '测试查询') }.to raise_error(ActiveRecord::ConnectionNotEstablished)
    end
  end

  describe '性能测试' do
    it '应该在合理时间内完成查询' do
      start_time = Time.current
      tool.query_activities(query: '最近一年的素材')
      end_time = Time.current

      expect(end_time - start_time).to be < 5.seconds
    end
  end
end
