require 'spec_helper'

# 简化的ResponseValidator测试，不依赖Rails环境
RSpec.describe 'Bot::ResponseValidator Logic' do
  describe 'ValidationResult' do
    let(:validation_result_class) do
      Class.new do
        attr_reader :valid, :issues, :suggestions

        def initialize(valid, issues, suggestions)
          @valid = valid
          @issues = issues
          @suggestions = suggestions
        end

        def valid?
          @valid
        end

        def has_issues?
          @issues.any?
        end
      end
    end

    it '应该正确初始化有效结果' do
      result = validation_result_class.new(true, [], [])

      expect(result.valid?).to be true
      expect(result.issues).to be_empty
      expect(result.suggestions).to be_empty
      expect(result.has_issues?).to be false
    end

    it '应该正确初始化无效结果' do
      issues = %w[问题1 问题2]
      suggestions = %w[建议1 建议2]
      result = validation_result_class.new(false, issues, suggestions)

      expect(result.valid?).to be false
      expect(result.issues).to eq(issues)
      expect(result.suggestions).to eq(suggestions)
      expect(result.has_issues?).to be true
    end
  end

  describe '响应验证逻辑' do
    def validate_response(response, preprocessing_info = {})
      issues = []
      suggestions = []

      # 处理nil情况
      return { valid: false, issues: ['响应为空'], suggestions: ['应该提供有效响应'] } if response.nil?

      preprocessing_info ||= {}

      # 检查是否有文本消息
      messages = response.dig(:messages) || []
      text_messages = messages.select { |msg| msg[:content_type] == 'text' }

      if text_messages.empty?
        issues << '缺少文本响应'
        suggestions << '应该包含文本回答'
      end

      # 检查响应长度
      if text_messages.any?
        content = text_messages.first[:content] || ''
        if content.length < 10
          issues << '响应过于简短'
          suggestions << '应该提供更详细的回答'
        end
      end

      # 如果是强制工具调用，检查是否有artifact
      if preprocessing_info[:forced_tool_call] && preprocessing_info[:query_type] == :activity_query
        artifact_messages = messages.select { |msg| msg[:content_type] == 'artifact' }
        if artifact_messages.empty?
          issues << '缺少查询结果组件'
          suggestions << '应该包含ActivityListArtifact组件'
        end

        # 检查响应内容与工具结果是否匹配
        if preprocessing_info[:tool_result] && text_messages.any?
          tool_result = preprocessing_info[:tool_result]
          response_content = text_messages.first[:content]

          if tool_result[:total_count] && tool_result[:total_count] > 0 && (response_content.include?('无法找到') || response_content.include?('没有'))
            issues << '响应内容与查询结果不匹配'
            suggestions << '响应应该反映实际的查询结果'
          end
        end
      end

      {
        valid: issues.empty?,
        issues: issues,
        suggestions: suggestions
      }
    end

    it '应该验证包含文本消息的有效响应' do
      response = {
        messages: [
          { content_type: 'text', content: '这是一个足够长的有效响应内容' }
        ]
      }

      result = validate_response(response)

      expect(result[:valid]).to be true
      expect(result[:issues]).to be_empty
      expect(result[:suggestions]).to be_empty
    end

    it '应该检测缺少文本响应的问题' do
      response = {
        messages: [
          { content_type: 'artifact', content: { type: 'list' } }
        ]
      }

      result = validate_response(response)

      expect(result[:valid]).to be false
      expect(result[:issues]).to include('缺少文本响应')
      expect(result[:suggestions]).to include('应该包含文本回答')
    end

    it '应该检测响应过于简短的问题' do
      response = {
        messages: [
          { content_type: 'text', content: '短' }
        ]
      }

      result = validate_response(response)

      expect(result[:valid]).to be false
      expect(result[:issues]).to include('响应过于简短')
      expect(result[:suggestions]).to include('应该提供更详细的回答')
    end

    it '应该处理空消息数组' do
      response = { messages: [] }

      result = validate_response(response)

      expect(result[:valid]).to be false
      expect(result[:issues]).to include('缺少文本响应')
    end

    it '应该处理缺少messages字段的响应' do
      response = {}

      result = validate_response(response)

      expect(result[:valid]).to be false
      expect(result[:issues]).to include('缺少文本响应')
    end

    it '应该验证活动查询的有效响应' do
      preprocessing_info = {
        forced_tool_call: true,
        query_type: :activity_query,
        tool_result: { message: '查询结果', total_count: 10 }
      }

      response = {
        messages: [
          { content_type: 'text', content: '根据查询结果，找到了10个素材' },
          { content_type: 'artifact', content: { type: 'activity_list' } }
        ]
      }

      result = validate_response(response, preprocessing_info)

      expect(result[:valid]).to be true
      expect(result[:issues]).to be_empty
    end

    it '应该检测缺少artifact的问题' do
      preprocessing_info = {
        forced_tool_call: true,
        query_type: :activity_query,
        tool_result: { message: '查询结果', total_count: 10 }
      }

      response = {
        messages: [
          { content_type: 'text', content: '这是一个有效的文本响应' }
        ]
      }

      result = validate_response(response, preprocessing_info)

      expect(result[:valid]).to be false
      expect(result[:issues]).to include('缺少查询结果组件')
      expect(result[:suggestions]).to include('应该包含ActivityListArtifact组件')
    end

    it '应该检测响应内容与工具结果不匹配的问题' do
      preprocessing_info = {
        forced_tool_call: true,
        query_type: :activity_query,
        tool_result: { message: '查询结果', total_count: 10 }
      }

      response = {
        messages: [
          { content_type: 'text', content: '无法找到相关信息' },
          { content_type: 'artifact', content: { type: 'activity_list' } }
        ]
      }

      result = validate_response(response, preprocessing_info)

      expect(result[:valid]).to be false
      expect(result[:issues]).to include('响应内容与查询结果不匹配')
      expect(result[:suggestions]).to include('响应应该反映实际的查询结果')
    end

    it '应该跳过非活动查询的特定验证' do
      preprocessing_info = {
        forced_tool_call: true,
        query_type: :unknown_query,
        tool_result: { message: '未知查询结果' }
      }

      response = {
        messages: [
          { content_type: 'text', content: '这是一个有效的响应内容足够长' }
        ]
      }

      result = validate_response(response, preprocessing_info)

      expect(result[:valid]).to be true
    end

    it '应该处理nil响应' do
      result = validate_response(nil)
      expect(result[:valid]).to be false
    end

    it '应该处理空预处理信息' do
      response = {
        messages: [
          { content_type: 'text', content: '有效的响应内容足够长' }
        ]
      }

      result = validate_response(response, {})
      expect(result[:valid]).to be true
    end

    it '应该处理nil预处理信息' do
      response = {
        messages: [
          { content_type: 'text', content: '有效的响应内容足够长' }
        ]
      }

      result = validate_response(response, nil)
      expect(result[:valid]).to be true
    end
  end

  describe '性能测试' do
    def validate_response_simple(response)
      messages = response&.dig(:messages) || []
      text_messages = messages.select { |msg| msg[:content_type] == 'text' }

      {
        valid: text_messages.any?,
        issues: [],
        suggestions: []
      }
    end

    it '应该在合理时间内完成验证' do
      response = {
        messages: [
          { content_type: 'text', content: '这是一个有效的响应内容' }
        ]
      }

      start_time = Time.now
      100.times { validate_response_simple(response) }
      end_time = Time.now

      expect(end_time - start_time).to be < 1.0
    end
  end
end
