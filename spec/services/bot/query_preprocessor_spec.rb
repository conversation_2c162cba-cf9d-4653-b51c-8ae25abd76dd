require 'rails_helper'

RSpec.describe Bot::QueryPreprocessor, type: :service do
  let(:agent) { double('Agent') }
  let(:user) { double('User') }
  let(:conversation) { double('Conversation') }
  let(:preprocessor) { described_class.new(agent, user, conversation) }

  describe '.process' do
    it '应该创建实例并调用process方法' do
      expect(described_class).to receive(:new).with(agent, user, conversation).and_call_original
      expect_any_instance_of(described_class).to receive(:process).with('test query')
      
      described_class.process('test query', agent: agent, user: user, conversation: conversation)
    end
  end

  describe '#process' do
    context '当查询需要强制工具调用时' do
      let(:query_type) do
        double('QueryType',
               type: :activity_query,
               confidence: 0.8,
               should_force_tool_call?: true,
               config: { confidence_threshold: 0.3 })
      end

      before do
        allow(Bot::QueryClassifier).to receive(:classify).and_return(query_type)
      end

      it '应该返回强制工具调用的结果' do
        query = '有多少个素材'
        result = preprocessor.process(query)

        expect(result).to be_a(Hash)
        expect(result).to have_key(:processed_query)
        expect(result).to have_key(:preprocessing_info)
        
        preprocessing_info = result[:preprocessing_info]
        expect(preprocessing_info[:forced_tool_call]).to be true
        expect(preprocessing_info[:query_type]).to eq(:activity_query)
        expect(preprocessing_info[:confidence]).to eq(0.8)
        expect(preprocessing_info[:original_query]).to eq(query)
        expect(preprocessing_info[:strategy]).to eq('query_rewrite')
      end

      it '应该重写查询以强制工具调用' do
        query = '素材总数'
        result = preprocessor.process(query)

        expect(result[:processed_query]).not_to eq(query)
        expect(result[:processed_query]).to include('素材查询')
      end
    end

    context '当查询不需要强制工具调用时' do
      let(:query_type) do
        double('QueryType',
               type: :activity_query,
               confidence: 0.2,
               should_force_tool_call?: false,
               config: { confidence_threshold: 0.3 })
      end

      before do
        allow(Bot::QueryClassifier).to receive(:classify).and_return(query_type)
      end

      it '应该返回原始查询' do
        query = '你好'
        result = preprocessor.process(query)

        expect(result[:processed_query]).to eq(query)
        expect(result[:preprocessing_info][:forced_tool_call]).to be false
        expect(result[:preprocessing_info][:query_type]).to eq(:activity_query)
        expect(result[:preprocessing_info][:confidence]).to eq(0.2)
      end
    end

    context '当查询无法分类时' do
      before do
        allow(Bot::QueryClassifier).to receive(:classify).and_return(nil)
      end

      it '应该返回原始查询' do
        query = '随意聊天'
        result = preprocessor.process(query)

        expect(result[:processed_query]).to eq(query)
        expect(result[:preprocessing_info][:forced_tool_call]).to be false
        expect(result[:preprocessing_info][:query_type]).to be_nil
        expect(result[:preprocessing_info][:confidence]).to be_nil
      end
    end

    context '异常情况处理' do
      it '应该处理空查询' do
        expect { preprocessor.process('') }.not_to raise_error
        
        result = preprocessor.process('')
        expect(result).to be_a(Hash)
        expect(result).to have_key(:processed_query)
        expect(result).to have_key(:preprocessing_info)
      end

      it '应该处理nil查询' do
        expect { preprocessor.process(nil) }.not_to raise_error
        
        result = preprocessor.process(nil)
        expect(result).to be_a(Hash)
      end

      it '应该处理分类器异常' do
        allow(Bot::QueryClassifier).to receive(:classify).and_raise(StandardError, 'Classification error')
        
        expect { preprocessor.process('test query') }.to raise_error(StandardError, 'Classification error')
      end
    end
  end

  describe '#extract_query_text' do
    it '应该正确提取字符串查询' do
      query = 'test string'
      result = preprocessor.send(:extract_query_text, query)
      
      expect(result).to eq('test string')
    end

    it '应该正确提取Hash格式的查询' do
      query = {
        messages: [
          { content: 'message 1' },
          { content: 'message 2' }
        ]
      }
      result = preprocessor.send(:extract_query_text, query)
      
      expect(result).to eq('message 1 message 2')
    end

    it '应该正确处理简单Hash查询' do
      query = { content: 'simple hash' }
      result = preprocessor.send(:extract_query_text, query)
      
      expect(result).to include('simple hash')
    end

    it '应该正确处理其他类型的查询' do
      query = 12345
      result = preprocessor.send(:extract_query_text, query)
      
      expect(result).to eq('12345')
    end
  end

  describe '日志记录' do
    it '应该记录预处理信息' do
      query = '测试查询'
      
      expect(Rails.logger).to receive(:info).with('=== Query Preprocessing ===')
      expect(Rails.logger).to receive(:info).with("Original query: #{query}")
      expect(Rails.logger).to receive(:info).with(/Classified as:/)
      
      preprocessor.process(query)
    end

    it '应该记录强制工具调用信息' do
      query_type = double('QueryType',
                          type: :activity_query,
                          confidence: 0.8,
                          should_force_tool_call?: true,
                          config: { confidence_threshold: 0.3 })
      
      allow(Bot::QueryClassifier).to receive(:classify).and_return(query_type)
      
      expect(Rails.logger).to receive(:info).with('Triggering forced tool call for activity_query')
      expect(Rails.logger).to receive(:info).with('Forcing tool call for activity_query')
      
      preprocessor.process('测试查询')
    end
  end

  describe '性能测试' do
    it '应该在合理时间内完成预处理' do
      query = '最近一个月有多少个素材'
      
      start_time = Time.current
      preprocessor.process(query)
      end_time = Time.current
      
      expect(end_time - start_time).to be < 1.second
    end
  end
end
