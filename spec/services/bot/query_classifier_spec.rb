require 'rails_helper'

RSpec.describe Bot::QueryClassifier, type: :service do
  # 不依赖数据库的纯逻辑测试
  describe '.classify' do
    context '活动查询分类' do
      it '应该识别素材数量查询' do
        queries = %w[
          有多少个素材
          素材总数是多少
          统计一下素材数量
          素材总量
        ]

        queries.each do |query|
          result = described_class.classify(query)

          expect(result).not_to be_nil
          expect(result.type).to eq(:activity_query)
          expect(result.confidence).to be > 0.3
          expect(result.should_force_tool_call?).to be true
        end
      end

      it '应该识别时间范围查询' do
        queries = %w[
          最近一周的素材
          过去一个月的内容
          今年的资料
          本月素材情况
        ]

        queries.each do |query|
          result = described_class.classify(query)

          expect(result).not_to be_nil
          expect(result.type).to eq(:activity_query)
          expect(result.confidence).to be > 0.3
        end
      end

      it '应该识别内容类型查询' do
        queries = %w[
          有哪些图片素材
          文章类型的内容
          视频资料
          漫画素材
        ]

        queries.each do |query|
          result = described_class.classify(query)

          expect(result).not_to be_nil
          expect(result.type).to eq(:activity_query)
        end
      end

      it '应该正确计算置信度' do
        # 高置信度查询（包含多个关键词）
        high_confidence_query = '最近一个月有多少个素材'
        result = described_class.classify(high_confidence_query)

        expect(result.confidence).to be > 0.5

        # 低置信度查询（只包含一个关键词）
        low_confidence_query = '素材'
        result = described_class.classify(low_confidence_query)

        expect(result.confidence).to be < 0.5
      end
    end

    context '非活动查询' do
      it '应该正确处理无关查询' do
        queries = %w[
          今天天气怎么样
          你好
          帮我写一篇文章
          什么是人工智能
        ]

        queries.each do |query|
          result = described_class.classify(query)

          expect(result).to be_nil
        end
      end

      it '应该处理空查询' do
        result = described_class.classify('')
        expect(result).to be_nil

        result = described_class.classify(nil)
        expect(result).to be_nil

        result = described_class.classify('   ')
        expect(result).to be_nil
      end
    end

    context '边界条件测试' do
      it '应该处理特殊字符' do
        query = '素材@#$%^&*()'
        result = described_class.classify(query)

        expect(result).not_to be_nil
        expect(result.type).to eq(:activity_query)
      end

      it '应该处理大小写混合' do
        query = '素材CONTENT资料'
        result = described_class.classify(query)

        expect(result).not_to be_nil
        expect(result.type).to eq(:activity_query)
      end

      it '应该处理长查询' do
        query = '请帮我查询一下最近三个月内所有已发布的素材内容的详细统计信息包括数量分布和类型分析'
        result = described_class.classify(query)

        expect(result).not_to be_nil
        expect(result.type).to eq(:activity_query)
        expect(result.confidence).to be > 0.7 # 包含多个关键词，置信度应该很高
      end
    end
  end

  describe 'QueryType' do
    let(:config) do
      {
        name: '测试查询',
        keywords: %w[测试 关键词],
        confidence_threshold: 0.5,
        tool_class: 'TestTool',
        tool_method: 'test_method'
      }
    end

    let(:query_type) { Bot::QueryClassifier::QueryType.new(:test_query, 0.6, ['测试'], config) }

    describe '#should_force_tool_call?' do
      it '当置信度高于阈值时应该返回true' do
        expect(query_type.should_force_tool_call?).to be true
      end

      it '当置信度低于阈值时应该返回false' do
        low_confidence_type = Bot::QueryClassifier::QueryType.new(:test_query, 0.3, ['测试'], config)
        expect(low_confidence_type.should_force_tool_call?).to be false
      end
    end

    describe '#tool_class' do
      it '应该返回正确的工具类名' do
        expect(query_type.tool_class).to eq('TestTool')
      end
    end

    describe '#tool_method' do
      it '应该返回正确的工具方法名' do
        expect(query_type.tool_method).to eq('test_method')
      end
    end
  end

  describe '配置验证' do
    it '应该包含正确的查询类型配置' do
      expect(Bot::QueryClassifier::QUERY_TYPES).to have_key(:activity_query)

      activity_config = Bot::QueryClassifier::QUERY_TYPES[:activity_query]
      expect(activity_config).to include(:name, :keywords, :tool_class, :tool_method, :confidence_threshold)
      expect(activity_config[:keywords]).to be_an(Array)
      expect(activity_config[:keywords]).not_to be_empty
    end

    it '应该有合理的置信度阈值' do
      activity_config = Bot::QueryClassifier::QUERY_TYPES[:activity_query]
      threshold = activity_config[:confidence_threshold]

      expect(threshold).to be_between(0.0, 1.0)
      expect(threshold).to be <= 0.5 # 不应该设置得太高
    end
  end

  describe '性能测试' do
    it '应该在合理时间内完成分类' do
      query = '最近一个月有多少个素材'

      start_time = Time.current
      100.times { described_class.classify(query) }
      end_time = Time.current

      expect(end_time - start_time).to be < 1.second
    end
  end
end
