require 'rails_helper'

RSpec.describe Bot::MessageContent do
  let(:valid_text_message) { { content_type: 'text', content: 'hello' } }
  let(:valid_mention_message) { { content_type: 'mention', content: { id: 1, name: '<PERSON>' } } }
  let(:valid_location_message) { { content_type: 'location', content: { lat: 37.7749, lon: -122.4194 } } }
  let(:valid_reference_message) do
    {
      content_type: 'reference',
      content: {
        id: 1,
        messages: [{ content_type: 'text', content: 'hello' }]
      }
    }
  end

  describe '#initialize' do
    it '创建空的消息内容对象' do
      content = described_class.new
      expect(content.messages).to be_empty
    end

    it '使用有效消息创建对象' do
      content = described_class.new(messages: [valid_text_message])
      expect(content.messages).to eq([valid_text_message])
    end

    it '当消息类型无效时抛出错误' do
      expect {
        described_class.new(messages: [{ content_type: 'invalid', content: 'test' }])
      }.to raise_error(Bot::MessageContent::InvalidContentTypeError)
    end
  end

  describe '#add_message' do
    let(:content) { described_class.new }

    it '添加文本消息' do
      content.add_message(**valid_text_message)
      expect(content.messages).to eq([valid_text_message])
    end

    it '添加mention消息' do
      content.add_message(**valid_mention_message)
      expect(content.messages).to eq([valid_mention_message])
    end

    it '添加location消息' do
      content.add_message(**valid_location_message)
      expect(content.messages).to eq([valid_location_message])
    end

    it '添加reference消息' do
      content.add_message(**valid_reference_message)
      expect(content.messages).to eq([valid_reference_message])
    end

    it '支持链式调用' do
      result = content.add_message(**valid_text_message)
                     .add_message(**valid_mention_message)
      expect(result).to eq(content)
      expect(content.messages.size).to eq(2)
    end

    context '当格式无效时' do
      it '验证mention格式' do
        expect {
          content.add_message(content_type: 'mention', content: { id: 1 })
        }.to raise_error(Bot::MessageContent::InvalidContentFormatError)
      end

      it '验证location格式' do
        expect {
          content.add_message(content_type: 'location', content: { lat: 'invalid' })
        }.to raise_error(Bot::MessageContent::InvalidContentFormatError)
      end

      it '验证reference格式' do
        expect {
          content.add_message(content_type: 'reference', content: { id: 1 })
        }.to raise_error(Bot::MessageContent::InvalidContentFormatError)
      end
    end
  end

  describe '#merge' do
    let(:content1) { described_class.new(messages: [valid_text_message]) }
    let(:content2) { described_class.new(messages: [valid_mention_message]) }

    it '合并两个MessageContent对象' do
      result = content1.merge(content2)
      expect(result.messages).to eq([valid_text_message, valid_mention_message])
    end

    it '不修改原对象' do
      content1.merge(content2)
      expect(content1.messages).to eq([valid_text_message])
      expect(content2.messages).to eq([valid_mention_message])
    end

    it '当参数类型错误时抛出错误' do
      expect { content1.merge("invalid") }.to raise_error(ArgumentError)
    end
  end

  describe '#messages_of_type' do
    let(:content) do
      described_class.new(messages: [
        valid_text_message,
        valid_mention_message,
        { content_type: 'text', content: 'world' }
      ])
    end

    it '返回指定类型的消息' do
      text_messages = content.messages_of_type('text')
      expect(text_messages.size).to eq(2)
      expect(text_messages.all? { |m| m[:content_type] == 'text' }).to be true
    end

    it '当类型无效时抛出错误' do
      expect { content.messages_of_type('invalid') }.to raise_error(Bot::MessageContent::InvalidContentTypeError)
    end
  end

  describe '#to_h and .from_h' do
    let(:messages) { [valid_text_message, valid_mention_message] }
    let(:content) { described_class.new(messages: messages) }

    it '转换为Hash并从Hash创建对象' do
      hash = content.to_h
      new_content = described_class.from_h(hash)
      expect(new_content.messages).to eq(messages)
    end

    it '处理空的messages' do
      new_content = described_class.from_h({})
      expect(new_content.messages).to be_empty
    end
  end
end
