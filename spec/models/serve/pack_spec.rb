require 'rails_helper'

RSpec.describe Serve::Pack, type: :model do
  let(:app) { create(:app) }
  let(:user) { create(:user, app: app) }
  let(:rule) { create(:serve_rule, app: app, options: { message_template_enabled: true }) }
  let(:pack) { create(:serve_pack, app: app, rule: rule, creator: user) }

  describe '#select_unique_message_template' do
    let!(:template1) { create(:serve_message_template, rule: rule, name: '模板1') }
    let!(:template2) { create(:serve_message_template, rule: rule, name: '模板2') }
    let!(:template3) { create(:serve_message_template, rule: rule, name: '模板3') }
    let(:templates) { [template1, template2, template3] }

    context '当用户没有历史消息时' do
      it '随机选择一个模板' do
        selected_template = pack.send(:select_unique_message_template, templates, user)
        expect(templates).to include(selected_template)
      end
    end

    context '当用户有历史消息但未使用所有模板时' do
      before do
        # 创建历史消息，使用template1
        create(:serve_message,
               user: user,
               pack: pack,
               payload: { 'template_id' => template1.id })
      end

      it '选择未使用过的模板' do
        selected_template = pack.send(:select_unique_message_template, templates, user)
        expect(selected_template).not_to eq(template1)
        expect([template2, template3]).to include(selected_template)
      end
    end

    context '当用户使用过所有模板时' do
      before do
        # 创建历史消息，使用所有模板
        create(:serve_message,
               user: user,
               pack: pack,
               payload: { 'template_id' => template1.id },
               created_at: 3.days.ago)
        create(:serve_message,
               user: user,
               pack: pack,
               payload: { 'template_id' => template2.id },
               created_at: 2.days.ago)
        create(:serve_message,
               user: user,
               pack: pack,
               payload: { 'template_id' => template3.id },
               created_at: 1.day.ago)
      end

      it '选择与最后一次不同的模板' do
        selected_template = pack.send(:select_unique_message_template, templates, user)
        expect(selected_template).not_to eq(template3) # template3是最后使用的
        expect([template1, template2]).to include(selected_template)
      end
    end

    context '当只有一个模板时' do
      let(:single_template) { [template1] }

      it '返回唯一的模板' do
        selected_template = pack.send(:select_unique_message_template, single_template, user)
        expect(selected_template).to eq(template1)
      end
    end

    context '当模板数组为空时' do
      let(:empty_templates) { [] }

      it '返回nil' do
        selected_template = pack.send(:select_unique_message_template, empty_templates, user)
        expect(selected_template).to be_nil
      end
    end
  end

  describe '#get_user_used_template_ids' do
    let!(:template1) { create(:serve_message_template, rule: rule) }
    let!(:template2) { create(:serve_message_template, rule: rule) }

    context '当用户有历史消息时' do
      before do
        create(:serve_message,
               user: user,
               pack: pack,
               payload: { 'template_id' => template1.id })
        create(:serve_message,
               user: user,
               pack: pack,
               payload: { 'template_id' => template2.id })
        # 创建一个没有template_id的消息
        create(:serve_message,
               user: user,
               pack: pack,
               payload: { 'other_data' => 'test' })
      end

      it '返回已使用的模板ID数组' do
        used_ids = pack.send(:get_user_used_template_ids, user)
        expect(used_ids).to contain_exactly(template1.id, template2.id)
      end
    end

    context '当用户没有历史消息时' do
      it '返回空数组' do
        used_ids = pack.send(:get_user_used_template_ids, user)
        expect(used_ids).to be_empty
      end
    end
  end

  describe '#get_user_last_template_id' do
    let!(:template1) { create(:serve_message_template, rule: rule) }
    let!(:template2) { create(:serve_message_template, rule: rule) }

    context '当用户有历史消息时' do
      before do
        create(:serve_message,
               user: user,
               pack: pack,
               payload: { 'template_id' => template1.id },
               created_at: 2.days.ago)
        create(:serve_message,
               user: user,
               pack: pack,
               payload: { 'template_id' => template2.id },
               created_at: 1.day.ago)
      end

      it '返回最后使用的模板ID' do
        last_id = pack.send(:get_user_last_template_id, user)
        expect(last_id).to eq(template2.id)
      end
    end

    context '当用户没有历史消息时' do
      it '返回nil' do
        last_id = pack.send(:get_user_last_template_id, user)
        expect(last_id).to be_nil
      end
    end
  end
end
