require 'rails_helper'

RSpec.describe Bot::MetaContent do
  let(:test_class) do
    Class.new(ApplicationRecord) do
      self.table_name = 'messages'  # 使用messages表进行测试
      include Bot::MetaContent
    end
  end

  let(:model) { test_class.new }
  let(:valid_text_message) { { content_type: 'text', content: 'hello' } }
  let(:valid_mention_message) { { content_type: 'mention', content: { id: 1, name: '<PERSON>' } } }
  let(:message_content) { Bot::MessageContent.new(messages: [valid_text_message]) }
  let(:another_message_content) { Bot::MessageContent.new(messages: [valid_mention_message]) }

  describe '#meta_content' do
    it '返回MessageContent对象' do
      expect(model.meta_content).to be_a(Bot::MessageContent)
    end

    it '当meta为nil时返回空的MessageContent' do
      model.meta = nil
      expect(model.meta_content.messages).to be_empty
    end

    it '缓存MessageContent对象' do
      content = model.meta_content
      expect(model.meta_content).to be(content)
    end
  end

  describe '#meta_content=' do
    it '设置meta内容' do
      model.meta_content = message_content
      expect(model.meta).to eq(message_content.to_h)
    end

    it '当参数类型错误时抛出错误' do
      expect { model.meta_content = "invalid" }.to raise_error(ArgumentError)
    end
  end

  describe '#add_meta_message' do
    it '添加消息到meta' do
      model.add_meta_message(**valid_text_message)
      expect(model.meta_content.messages).to include(valid_text_message)
    end

    it '支持链式调用' do
      result = model.add_meta_message(**valid_text_message)
                   .add_meta_message(**valid_mention_message)
      expect(result).to eq(model)
      expect(model.meta_content.messages.size).to eq(2)
    end
  end

  describe '#merge_meta' do
    context '当参数是MessageContent对象时' do
      it '合并meta内容' do
        model.meta_content = message_content
        model.merge_meta(another_message_content)
        expect(model.meta_content.messages).to eq([valid_text_message, valid_mention_message])
      end
    end

    context '当参数是Hash时' do
      it '合并meta内容' do
        model.meta_content = message_content
        model.merge_meta(another_message_content.to_h)
        expect(model.meta_content.messages).to eq([valid_text_message, valid_mention_message])
      end
    end

    it '当参数类型错误时抛出错误' do
      expect { model.merge_meta("invalid") }.to raise_error(ArgumentError)
    end

    it '支持链式调用' do
      result = model.merge_meta(message_content).merge_meta(another_message_content)
      expect(result).to eq(model)
    end
  end

  describe '#meta_messages_of_type' do
    before do
      model.meta_content = Bot::MessageContent.new(messages: [
        valid_text_message,
        valid_mention_message,
        { content_type: 'text', content: 'world' }
      ])
    end

    it '返回指定类型的消息' do
      text_messages = model.meta_messages_of_type('text')
      expect(text_messages.size).to eq(2)
      expect(text_messages.all? { |m| m[:content_type] == 'text' }).to be true
    end
  end
end
