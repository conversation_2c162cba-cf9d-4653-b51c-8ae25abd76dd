require "rails_helper"

RSpec.describe User, type: :model do
  describe ".find_date_field_within" do
    let!(:app) { create(:app) }
    let!(:user1) { create(:user, app: app, name: "张三", birthday: Date.new(2000, 7, 15)) }
    let!(:user2) { create(:user, app: app, name: "李四", birthday: Date.new(2000, 7, 28)) }
    let!(:user3) { create(:user, app: app, name: "王五", birthday: Date.new(2000, 7, 30)) }
    let!(:user4) { create(:user, app: app, name: "赵六", birthday: Date.new(2000, 8, 1)) }
    let!(:user5) { create(:user, app: app, name: "钱七", birthday: Date.new(2000, 8, 3)) }
    let!(:user6) { create(:user, app: app, name: "孙八", birthday: Date.new(2000, 8, 15)) }
    let!(:user7) { create(:user, app: app, name: "周九", birthday: Date.new(2000, 12, 25)) }
    let!(:user8) { create(:user, app: app, name: "吴十", birthday: Date.new(2000, 1, 5)) }

    context "同月查询" do
      it "正确查询7月15日到7月30日的生日" do
        users = User.find_date_field_within(nil, "2025-07-15", "2025-07-30", "birthday")
        expect(users.pluck(:name)).to contain_exactly("张三", "李四", "王五")
      end
    end

    context "跨月查询（同年）" do
      it "正确查询7月28日到8月3日的生日" do
        users = User.find_date_field_within(nil, "2025-07-28", "2025-08-03", "birthday")
        expect(users.pluck(:name)).to contain_exactly("李四", "王五", "赵六", "钱七")
      end

      it "不应该包含范围外的日期" do
        users = User.find_date_field_within(nil, "2025-07-28", "2025-08-03", "birthday")
        expect(users.pluck(:name)).not_to include("张三", "孙八")
      end
    end

    context "跨年查询" do
      it "正确查询12月20日到1月10日的生日" do
        users = User.find_date_field_within(nil, "2025-12-20", "2026-01-10", "birthday")
        expect(users.pluck(:name)).to contain_exactly("周九", "吴十")
      end
    end

    context "政治生日查询" do
      let!(:user_political) { create(:user, app: app, name: "政治生日用户", political_birthday: Date.new(2000, 7, 29)) }

      it "正确查询政治生日" do
        users = User.find_date_field_within(nil, "2025-07-28", "2025-08-03", "political_birthday")
        expect(users.pluck(:name)).to contain_exactly("政治生日用户")
      end
    end

    context "边界情况" do
      it "单日查询" do
        users = User.find_date_field_within(nil, "2025-07-28", "2025-07-28", "birthday")
        expect(users.pluck(:name)).to contain_exactly("李四")
      end

      it "空结果" do
        users = User.find_date_field_within(nil, "2025-06-01", "2025-06-30", "birthday")
        expect(users).to be_empty
      end
    end
  end

  describe "Serve::Birthday integration" do
    let!(:app) { create(:app) }
    let!(:org) { create(:org, name: "滨江区") }
    let!(:user1) { create(:user, app: app, name: "本周生日1", birthday: Date.new(2000, 7, 29)) }
    let!(:user2) { create(:user, app: app, name: "本周生日2", birthday: Date.new(2000, 8, 1)) }
    let!(:user3) { create(:user, app: app, name: "非本周生日", birthday: Date.new(2000, 7, 15)) }

    before do
      # 模拟当前时间为2025年7月28日（周一）
      travel_to Date.new(2025, 7, 28)
    end

    after do
    end

    it "正确创建本周生日记录" do
      Serve::Birthday.find_and_create_record("now", "birthday", "滨江区")

      record = Serve::Birthday.find_records_by_week("now", "生日").first
      expect(record).to be_present
      expect(record.payload.keys).to contain_exactly("本周生日1", "本周生日2")
      expect(record.payload.keys).not_to include("非本周生日")
    end
  end
end
