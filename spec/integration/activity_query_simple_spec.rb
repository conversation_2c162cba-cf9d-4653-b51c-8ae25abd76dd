require 'spec_helper'

# 简化的集成测试
RSpec.describe 'Activity Query Simple Integration' do
  describe '完整流程测试' do
    it '应该正确处理素材查询的完整流程' do
      # 1. 模拟查询分类
      query = '有多少个素材'

      # 模拟分类结果
      classification_result = {
        type: :activity_query,
        confidence: 0.8,
        should_force_tool_call: true,
        tool_class: 'Bot::Tools::ActivityQueryTool',
        tool_method: 'query_activities'
      }

      expect(classification_result[:type]).to eq(:activity_query)
      expect(classification_result[:should_force_tool_call]).to be true

      # 2. 模拟工具调用
      tool_result = {
        message: '根据查询条件，找到了15个素材',
        total_count: 15,
        conditions: { 'created_at_gteq' => '2024-01-01' }
      }

      expect(tool_result[:total_count]).to eq(15)
      expect(tool_result[:message]).to include('15个素材')

      # 3. 模拟响应构建
      response = {
        messages: [
          { content_type: 'text', content: tool_result[:message] },
          { content_type: 'artifact', content: { type: 'activity_list', data: tool_result } }
        ]
      }

      expect(response[:messages].size).to eq(2)
      expect(response[:messages][0][:content_type]).to eq('text')
      expect(response[:messages][1][:content_type]).to eq('artifact')

      # 4. 模拟响应验证
      preprocessing_info = {
        forced_tool_call: true,
        query_type: :activity_query,
        tool_result: tool_result
      }

      # 验证逻辑
      messages = response[:messages]
      text_messages = messages.select { |msg| msg[:content_type] == 'text' }
      artifact_messages = messages.select { |msg| msg[:content_type] == 'artifact' }

      # 检查文本消息
      expect(text_messages).not_to be_empty
      expect(text_messages.first[:content].length).to be > 10

      # 检查artifact消息（对于强制工具调用）
      if preprocessing_info[:forced_tool_call] && preprocessing_info[:query_type] == :activity_query
        expect(artifact_messages).not_to be_empty
      end

      # 验证通过
      validation_result = {
        valid: true,
        issues: [],
        suggestions: []
      }

      expect(validation_result[:valid]).to be true
      expect(validation_result[:issues]).to be_empty
    end

    it '应该正确处理无关查询' do
      # 1. 无关查询
      query = '你好吗朋友'

      # 2. 分类结果应该为空
      classification_result = nil

      expect(classification_result).to be_nil

      # 3. 不应该触发强制工具调用
      # 直接返回普通响应
      response = {
        messages: [
          { content_type: 'text', content: '你好！有什么可以帮助你的吗？' }
        ]
      }

      expect(response[:messages].size).to eq(1)
      expect(response[:messages][0][:content_type]).to eq('text')

      # 4. 验证普通响应
      messages = response[:messages]
      text_messages = messages.select { |msg| msg[:content_type] == 'text' }

      expect(text_messages).not_to be_empty
      expect(text_messages.first[:content].length).to be > 5

      validation_result = {
        valid: true,
        issues: [],
        suggestions: []
      }

      expect(validation_result[:valid]).to be true
    end

    it '应该检测响应验证问题' do
      # 1. 强制工具调用但缺少artifact的情况
      preprocessing_info = {
        forced_tool_call: true,
        query_type: :activity_query
      }

      response = {
        messages: [
          { content_type: 'text', content: '这是一个有效的文本响应' }
        ]
      }

      # 2. 验证逻辑
      messages = response[:messages]
      text_messages = messages.select { |msg| msg[:content_type] == 'text' }
      artifact_messages = messages.select { |msg| msg[:content_type] == 'artifact' }

      issues = []
      suggestions = []

      # 检查文本消息
      if text_messages.empty?
        issues << '缺少文本响应'
        suggestions << '应该包含文本回答'
      end

      # 检查artifact（对于强制工具调用）
      if preprocessing_info[:forced_tool_call] && preprocessing_info[:query_type] == :activity_query && artifact_messages.empty?
        issues << '缺少查询结果组件'
        suggestions << '应该包含ActivityListArtifact组件'
      end

      validation_result = {
        valid: issues.empty?,
        issues: issues,
        suggestions: suggestions
      }

      expect(validation_result[:valid]).to be false
      expect(validation_result[:issues]).to include('缺少查询结果组件')
      expect(validation_result[:suggestions]).to include('应该包含ActivityListArtifact组件')
    end
  end

  describe '边界条件测试' do
    it '应该处理空查询' do
      query = ''

      # 空查询不应该被分类
      classification_result = nil
      expect(classification_result).to be_nil
    end

    it '应该处理特殊字符查询' do
      query = '素材@#$%^&*()'

      # 应该能够识别关键词
      contains_keyword = query.include?('素材')
      expect(contains_keyword).to be true

      # 模拟分类结果
      classification_result = {
        type: :activity_query,
        confidence: 0.5
      }

      expect(classification_result[:type]).to eq(:activity_query)
    end

    it '应该处理长查询' do
      query = '请帮我查询一下最近三个月内所有已发布的素材内容的详细统计信息包括数量分布和类型分析'

      # 应该能够识别关键词
      contains_keywords = query.include?('素材') && query.include?('统计')
      expect(contains_keywords).to be true

      # 模拟分类结果
      classification_result = {
        type: :activity_query,
        confidence: 0.9
      }

      expect(classification_result[:confidence]).to be > 0.8
    end
  end
end
