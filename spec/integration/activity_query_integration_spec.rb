require 'spec_helper'

# 集成测试 - 验证活动查询功能的整体流程
RSpec.describe 'Activity Query Integration' do
  let(:mock_classifier) do
      Class.new do
        def self.classify(query)
          return nil if query.blank?

          # 模拟分类逻辑
          if query.include?('素材') || query.include?('多少')
            MockQueryType.new(:activity_query, 0.8, ['素材'], {
                                confidence_threshold: 0.3,
                                tool_class: 'Bot::Tools::ActivityQueryTool',
                                tool_method: 'query_activities'
                              })
          else
            nil
          end
        end
      end
    end

    let(:mock_query_type) do
      Class.new do
        attr_reader :type, :confidence, :matched_keywords, :config

        def initialize(type, confidence, matched_keywords, config)
          @type = type
          @confidence = confidence
          @matched_keywords = matched_keywords
          @config = config
        end

        def should_force_tool_call?
          confidence >= config[:confidence_threshold]
        end

        def tool_class
          config[:tool_class]
        end

        def tool_method
          config[:tool_method]
        end
      end
    end
  end

  let(:mock_query_type) do
    Class.new do
      attr_reader :type, :confidence, :matched_keywords, :config

      def initialize(type, confidence, matched_keywords, config)
        @type = type
        @confidence = confidence
        @matched_keywords = matched_keywords
        @config = config
      end

      def should_force_tool_call?
        confidence >= config[:confidence_threshold]
      end

      def tool_class
        config[:tool_class]
      end

      def tool_method
        config[:tool_method]
      end
    end
  end

  before do
    stub_const('MockQueryType', mock_query_type)
  end

  describe '查询分类到预处理的流程' do

    it '应该正确处理需要强制工具调用的查询' do
      query = '有多少个素材'

      # 模拟分类
      classification_result = mock_classifier.classify(query)

      expect(classification_result).not_to be_nil
      expect(classification_result.type).to eq(:activity_query)
      expect(classification_result.should_force_tool_call?).to be true
      expect(classification_result.tool_class).to eq('Bot::Tools::ActivityQueryTool')
      expect(classification_result.tool_method).to eq('query_activities')
    end

    it '应该正确处理不需要强制工具调用的查询' do
      query = '你好'

      # 模拟分类
      classification_result = mock_classifier.classify(query)

      expect(classification_result).to be_nil
    end

    it '应该正确处理空查询' do
      query = ''

      # 模拟分类
      classification_result = mock_classifier.classify(query)

      expect(classification_result).to be_nil
    end
  end

  describe '响应验证流程' do
    let(:mock_validator) do
      Class.new do
        def self.validate(response, preprocessing_info = {})
          MockValidationResult.new(response, preprocessing_info)
        end
      end
    end

    let(:mock_validation_result) do
      Class.new do
        attr_reader :response, :preprocessing_info

        def initialize(response, preprocessing_info)
          @response = response
          @preprocessing_info = preprocessing_info
          @issues = []
          @suggestions = []

          validate_response
        end

        def valid?
          @issues.empty?
        end

        def has_issues?
          @issues.any?
        end

        attr_reader :issues

        attr_reader :suggestions

        private

        def validate_response
          # 检查是否有文本消息
          messages = @response&.dig(:messages) || []
          text_messages = messages.select { |msg| msg[:content_type] == 'text' }

          if text_messages.empty?
            @issues << '缺少文本响应'
            @suggestions << '应该包含文本回答'
          end

          # 检查响应长度
          if text_messages.any?
            content = text_messages.first[:content] || ''
            if content.length < 10
              @issues << '响应过于简短'
              @suggestions << '应该提供更详细的回答'
            end
          end

          # 如果是强制工具调用，检查是否有artifact
          return unless @preprocessing_info[:forced_tool_call] && @preprocessing_info[:query_type] == :activity_query

          artifact_messages = messages.select { |msg| msg[:content_type] == 'artifact' }
          return unless artifact_messages.empty?

          @issues << '缺少查询结果组件'
          @suggestions << '应该包含ActivityListArtifact组件'
        end
      end
    end

    before do
      stub_const('MockValidationResult', mock_validation_result)
    end

    it '应该验证有效的响应' do
      response = {
        messages: [
          { content_type: 'text', content: '这是一个足够长的有效响应内容' }
        ]
      }

      result = mock_validator.validate(response)

      expect(result.valid?).to be true
      expect(result.issues).to be_empty
      expect(result.suggestions).to be_empty
    end

    it '应该检测缺少文本响应的问题' do
      response = {
        messages: [
          { content_type: 'artifact', content: { type: 'list' } }
        ]
      }

      result = mock_validator.validate(response)

      expect(result.valid?).to be false
      expect(result.issues).to include('缺少文本响应')
      expect(result.suggestions).to include('应该包含文本回答')
    end

    it '应该检测强制工具调用缺少artifact的问题' do
      response = {
        messages: [
          { content_type: 'text', content: '这是一个有效的文本响应' }
        ]
      }

      preprocessing_info = {
        forced_tool_call: true,
        query_type: :activity_query
      }

      result = mock_validator.validate(response, preprocessing_info)

      expect(result.valid?).to be false
      expect(result.issues).to include('缺少查询结果组件')
      expect(result.suggestions).to include('应该包含ActivityListArtifact组件')
    end

    it '应该验证完整的强制工具调用响应' do
      response = {
        messages: [
          { content_type: 'text', content: '根据查询结果，找到了10个素材' },
          { content_type: 'artifact', content: { type: 'activity_list' } }
        ]
      }

      preprocessing_info = {
        forced_tool_call: true,
        query_type: :activity_query
      }

      result = mock_validator.validate(response, preprocessing_info)

      expect(result.valid?).to be true
      expect(result.issues).to be_empty
    end
  end

  describe '端到端流程模拟' do
    let(:classifier) { mock_classifier }
    let(:validator) { mock_validation_result }

    it '应该完整处理素材查询请求' do
      # 1. 用户查询
      user_query = '最近一个月有多少个素材'

      # 2. 查询分类
      classification = classifier.classify(user_query)
      expect(classification).not_to be_nil
      expect(classification.should_force_tool_call?).to be true

      # 3. 模拟工具调用结果
      tool_result = {
        message: '根据查询条件，找到了15个素材',
        total_count: 15,
        conditions: { 'created_at_gteq' => '2024-01-01' }
      }

      # 4. 构建响应
      response = {
        messages: [
          { content_type: 'text', content: tool_result[:message] },
          { content_type: 'artifact', content: { type: 'activity_list', data: tool_result } }
        ]
      }

      # 5. 响应验证
      preprocessing_info = {
        forced_tool_call: true,
        query_type: :activity_query,
        tool_result: tool_result
      }

      validation_result = validator.new(response, preprocessing_info)

      expect(validation_result.valid?).to be true
      expect(response[:messages]).to have_exactly(2).items
      expect(response[:messages][0][:content]).to include('15个素材')
      expect(response[:messages][1][:content_type]).to eq('artifact')
    end
  end
end
