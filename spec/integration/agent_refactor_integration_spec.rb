require 'rails_helper'

RSpec.describe 'Agent Refactor Integration', type: :integration do
  let(:app) { App.first || App.create!(name: 'Test App') }
  let(:user) { User.first || User.create!(name: 'Test User', email: '<EMAIL>') }
  let(:agent) { Bot::Agent.first || Bot::Agent.create!(app: app, name: 'Test Agent') }

  describe 'Agent.chat method refactoring' do
    context 'basic functionality' do
      it 'responds to simple messages' do
        response = agent.chat('Hello', user: user)

        expect(response).to be_a(Hash)
        expect(response).to have_key(:messages)
        expect(response[:messages]).to be_an(<PERSON>rray)
        expect(response[:messages].first).to have_key(:content_type)
        expect(response[:messages].first).to have_key(:content)
      end

      it 'includes preprocessing info' do
        response = agent.chat('Test message', user: user)

        expect(response).to have_key(:preprocessing_info)
        expect(response[:preprocessing_info]).to be_a(Hash)
        expect(response[:preprocessing_info]).to have_key(:forced_tool_call)
      end

      it 'includes validation result' do
        response = agent.chat('Test message', user: user)

        expect(response).to have_key(:validation_result)
        expect(response[:validation_result]).to be_a(Hash)
        expect(response[:validation_result]).to have_key(:valid)
      end
    end

    context 'method decomposition verification' do
      it 'has the expected private methods' do
        expected_methods = %i[
          find_or_create_conversation
          log_chat_start
          preprocess_query
          save_user_message
          generate_response
          validate_and_improve_response
          add_debug_info
          extract_content_from_message
          build_response_from_assistant
          collect_artifacts_from_assistant
          extract_artifact_from_tool_call
          build_text_message
          build_artifact_messages
          save_assistant_message
          update_response_text_content
          extract_tool_message
          add_friendly_prefix
          add_supplementary_info
        ]

        expected_methods.each do |method_name|
          expect(agent.private_methods).to include(method_name)
        end
      end

      it 'chat method is concise (under 30 lines)' do
        # Read the source code of the chat method
        source_location = agent.method(:chat).source_location
        file_path = source_location[0]
        start_line = source_location[1]

        lines = File.readlines(file_path)
        method_lines = []
        current_line = start_line - 1
        indent_level = nil

        while current_line < lines.length
          line = lines[current_line]

          if indent_level.nil?
            # First line of method, determine indent level
            indent_level = line[/\A */].length
          elsif line.strip == 'end' && line[/\A */].length == indent_level
            # Found the matching end
            break
          end

          method_lines << line
          current_line += 1
        end

        # Count non-empty, non-comment lines
        code_lines = method_lines.reject { |line| line.strip.empty? || line.strip.start_with?('#') }

        expect(code_lines.length).to be <= 30,
                                     "Chat method should be under 30 lines, but has #{code_lines.length} lines"
      end
    end

    context 'error handling' do
      it 'handles invalid user gracefully' do
        expect do
          agent.chat('Test', user: nil)
        end.to raise_error(ArgumentError)
      end
    end
  end
end
