require 'swagger_helper'

RSpec.describe 'bot/user/paperworks', type: :request, capture_examples: true, tags: ["bot user"] do
  paperwork_ref = {
    type: :object, properties: {
      paperwork: {
        type: :object, properties: {
          app_id: { type: :integer, description: '' },
          user_id: { type: :integer, description: '' },
          name: { type: :string, description: '名称' },
          operate_at: { type: :datetime, description: '操作时间' },
          state: { type: :string, description: '' },
          attachment: { type: :jsonb, description: '附件结构，单一文件' },
          response: { type: :jsonb, description: '响应' },
          prompt_text: { type: :text, description: '提示词，由外部传入' },
        }
      }
    }
  }
  paperwork_value = FactoryBot.attributes_for(:bot_paperwork)

  before :each do
  end

  path '/bot/user/paperworks' do

    get(summary: 'list paperworks') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @bot_paperwork_count
        }
      end
    end

    post(summary: 'create paperwork') do
      produces 'application/json'
      consumes 'application/json'
      parameter :paperwork, in: :body, schema: paperwork_ref
      response(201, description: 'successful') do
        let(:paperwork) do
          { paperwork: paperwork_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['app_id']).to eq @app.id
          expect(body['user_id']).to eq @user.id
          expect(body['name']).to eq paperwork_value[:name]
          expect(body['operate_at']).to eq paperwork_value[:operate_at]
          expect(body['state']).to eq paperwork_value[:state]
          expect(body['attachment']).to eq paperwork_value[:attachment]
          expect(body['response']).to eq paperwork_value[:response]
          expect(body['prompt_text']).to eq paperwork_value[:prompt_text]
        }
      end
    end
  end

  path '/bot/user/paperworks/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show paperwork') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @bot_paperworks.first.id }
        it {
          body = JSON.parse(response.body)
          paperwork = @bot_paperworks.first
          expect(body['app_id']).to eq paperwork.app_id
          expect(body['user_id']).to eq paperwork.user_id
          expect(body['name']).to eq paperwork.name
          expect(body['operate_at']).to eq paperwork.operate_at
          expect(body['state']).to eq paperwork.state
          expect(body['attachment']).to eq paperwork.attachment
          expect(body['response']).to eq paperwork.response
          expect(body['prompt_text']).to eq paperwork.prompt_text
        }
      end
    end

    patch(summary: 'update paperwork') do
      produces 'application/json'
      consumes 'application/json'
      parameter :paperwork, in: :body, schema: paperwork_ref
      response(201, description: 'successful') do
        let(:id) { @bot_paperworks.first.id }
        let(:paperwork) do
          { paperwork: paperwork_value }
        end
      end
    end

    delete(summary: 'delete paperwork') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @bot_paperworks.first.id }
        it {
          expect(Bot::Paperwork.count).to eq(@bot_paperwork_count-1)
        }
      end
    end
  end
end
