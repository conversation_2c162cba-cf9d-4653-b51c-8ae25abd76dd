require 'swagger_helper'

RSpec.describe 'bot/user/reviewers', type: :request, capture_examples: true, tags: ['bot user'] do
  before :each do
  end

  path '/bot/user/reviewers' do
    get(summary: 'list reviewers') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @bot_reviewer_count
        }
      end
    end
  end

  path '/bot/user/reviewers/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show reviewer') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @bot_reviewers.first.id }
        it {
          body = JSON.parse(response.body)
          reviewer = @bot_reviewers.first
          expect(body['app_id']).to eq reviewer.app_id
          expect(body['name']).to eq reviewer.name
          expect(body['description']).to eq reviewer.description
          expect(body['instructions']).to eq reviewer.instructions
          expect(body['model_flag']).to eq reviewer.model_flag
          expect(body['model_payload']).to eq reviewer.model_payload
          expect(body['model_payload_summary']).to eq reviewer.model_payload_summary
          expect(body['model_detail']).to eq reviewer.model_detail
          expect(body['icon']).to eq reviewer.icon
        }
      end
    end
  end
end
