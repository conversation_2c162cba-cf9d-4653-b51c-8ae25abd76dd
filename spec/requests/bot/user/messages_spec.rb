require 'swagger_helper'

RSpec.describe 'bot/user/messages', type: :request, capture_examples: true, tags: ["bot user"] do
  message_ref = {
    type: :object, properties: {
      message: {
        type: :object, properties: {
          app_id: { type: :integer, description: '' },
          conversation_id: { type: :integer, description: '' },
          role: { type: :string, description: '发送对象' },
          meta: { type: :json, description: '消息发送的内容' },
        }
      }
    }
  }
  message_value = FactoryBot.attributes_for(:bot_message)

  before :each do
    @conversation = @bot_conversation
  end

  path '/bot/user/conversations/{conversation_id}/messages' do
    parameter 'conversation_id', in: :path, type: :string

    get(summary: 'list messages') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:conversation_id) { @conversation.id }
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @bot_message_count
        }
      end
    end
  end

  path '/bot/user/conversations/{conversation_id}/messages/{id}' do
    parameter 'conversation_id', in: :path, type: :string
    parameter 'id', in: :path, type: :string

    get(summary: 'show message') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:conversation_id) { @conversation.id }
        let(:id) { @bot_messages.first.id }
        it {
          body = JSON.parse(response.body)
          message = @bot_messages.first
          expect(body['app_id']).to eq message.app_id
          expect(body['conversation_id']).to eq message.conversation_id
          expect(body['role']).to eq message.role
          expect(body['meta']).to eq message.meta.as_json
        }
      end
    end
  end
end
