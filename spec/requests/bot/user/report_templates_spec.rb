require 'swagger_helper'

RSpec.describe 'bot/user/report_templates', type: :request, capture_examples: true, tags: ['bot user'] do
  before :each do
  end

  path '/bot/user/report_templates' do
    get(summary: 'list report_templates') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @bot_report_template_count
        }
      end
    end
  end

  path '/bot/user/report_templates/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show report_template') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @bot_report_templates.first.id }
        it {
          body = JSON.parse(response.body)
          report_template = @bot_report_templates.first
          expect(body['app_id']).to eq report_template.app_id
          expect(body['name']).to eq report_template.name
          expect(body['title']).to eq report_template.title
          expect(body['content']).to eq report_template.content
          expect(body['prompt']).to eq report_template.prompt
          expect(body['conf']).to eq report_template.conf
        }
      end
    end
  end
end
