require 'swagger_helper'

RSpec.describe 'bot/user/reports', type: :request, capture_examples: true, tags: ["bot user"] do
  report_ref = {
    type: :object, properties: {
      report: {
        type: :object, properties: {
          report_template_id: { type: :integer, description: '' },
          name: { type: :string, description: '名称' },
          title: { type: :string, description: '标题' },
          content: { type: :text, description: '内容' },
          variables: { type: :jsonb, description: '变量' },
        }
      }
    }
  }
  report_value = FactoryBot.attributes_for(:bot_report)

  before :each do
  end

  path '/bot/user/reports' do

    get(summary: 'list reports') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @bot_report_count
        }
      end
    end

    post(summary: 'create report') do
      produces 'application/json'
      consumes 'application/json'
      parameter :report, in: :body, schema: report_ref
      response(201, description: 'successful') do
        let(:report) do
          { report: report_value.merge(
            report_template_id: @bot_report_templates.first.id
          ) }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['app_id']).to eq @app.id
          expect(body['user_id']).to eq @user.id
          expect(body['report_template_id']).to eq @bot_report_templates.first.id
          expect(body['name']).to eq report_value[:name]
          expect(body['title']).to eq report_value[:title]
          expect(body['content']).to eq report_value[:content]
          expect(body['variables']).to eq report_value[:variables]
        }
      end
    end
  end

  path '/bot/user/reports/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show report') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @bot_reports.first.id }
        it {
          body = JSON.parse(response.body)
          report = @bot_reports.first
          expect(body['app_id']).to eq report.app_id
          expect(body['user_id']).to eq report.user_id
          expect(body['report_template_id']).to eq report.report_template_id
          expect(body['name']).to eq report.name
          expect(body['title']).to eq report.title
          expect(body['content']).to eq report.content
          expect(body['variables']).to eq report.variables
        }
      end
    end

    patch(summary: 'update report') do
      produces 'application/json'
      consumes 'application/json'
      parameter :report, in: :body, schema: report_ref
      response(201, description: 'successful') do
        let(:id) { @bot_reports.first.id }
        let(:report) do
          { report: report_value }
        end
      end
    end

    delete(summary: 'delete report') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @bot_reports.first.id }
        it {
          expect(Bot::Report.count).to eq(@bot_report_count-1)
        }
      end
    end
  end
end
