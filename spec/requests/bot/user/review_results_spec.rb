require 'swagger_helper'

RSpec.describe 'bot/user/review_results', type: :request, capture_examples: true, tags: ['bot user'] do
  review_result_ref = {
    type: :object, properties: {
      review_result: {
        type: :object, properties: {
          name: { type: :string, description: '结果名称' },
          raw: { type: :text, description: '原文' },
          score: { type: :integer, description: '分数' },
          reason: { type: :text, description: '原因' },
          suggest: { type: :text, description: '建议' },
          level: { type: :string, description: '' },
          meta: { type: :jsonb, description: '额外信息' },
        },
      },
    },
  }
  review_result_value = FactoryBot.attributes_for(:bot_review_result)

  before :each do
    @review_document = @bot_review_document
  end

  path '/bot/user/review_documents/{review_document_id}/review_results' do
    parameter 'review_document_id', in: :path, type: :string

    get(summary: 'list review_results') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:review_document_id) { @review_document.id }
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @bot_review_result_count
        }
      end
    end
  end

  path '/bot/user/review_documents/{review_document_id}/review_results/{id}' do
    parameter 'review_document_id', in: :path, type: :string
    parameter 'id', in: :path, type: :string

    get(summary: 'show review_result') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:review_document_id) { @review_document.id }
        let(:id) { @bot_review_results.first.id }
        it {
          body = JSON.parse(response.body)
          review_result = @bot_review_results.first
          expect(body['document_source_id']).to eq review_result.document_source_id
          expect(body['review_rule_id']).to eq review_result.review_rule_id
          expect(body['name']).to eq review_result.name
          expect(body['raw']).to eq review_result.raw
          expect(body['score']).to eq review_result.score
          expect(body['reason']).to eq review_result.reason
          expect(body['suggest']).to eq review_result.suggest
          expect(body['level']).to eq review_result.level
          expect(body['meta']).to eq review_result.meta
        }
      end
    end

    patch(summary: 'update review_result') do
      produces 'application/json'
      consumes 'application/json'
      parameter :review_result, in: :body, schema: review_result_ref
      response(201, description: 'successful') do
        let(:review_document_id) { @review_document.id }
        let(:id) { @bot_review_results.first.id }
        let(:review_result) do
          { review_result: review_result_value }
        end
      end
    end

    delete(summary: 'delete review_result') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:review_document_id) { @review_document.id }
        let(:id) { @bot_review_results.first.id }
        it {
          expect(Bot::ReviewResult.count).to eq(@bot_review_result_count - 1)
        }
      end
    end
  end
end
