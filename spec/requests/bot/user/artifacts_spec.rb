require 'swagger_helper'

RSpec.describe 'bot/user/artifacts', type: :request, capture_examples: true, tags: ["bot user"] do
  artifact_ref = {
    type: :object, properties: {
      artifact: {
        type: :object, properties: {
          conversation_id: { type: :integer, description: '' },
          source_type: { type: :string, description: '' },
          source_id: { type: :integer, description: '' },
          tool_function: { type: :string, description: '根据function名称' },
          function_params: { type: :jsonb, description: '函数调用信息，转换前' },
          meta: { type: :jsonb, description: '参数内容，自定义的方式' },
          info: { type: :jsonb, description: 'message返回的信息' },
          tool_cname: { type: :string, description: '根据模型名称' },
          tool_conf: { type: :jsonb, description: '参数配置' },
          intent_name: { type: :string, description: '根据名称' },
        }
      }
    }
  }
  artifact_value = FactoryBot.attributes_for(:bot_artifact)

  before :each do
    @conversation = @bot_conversation
  end

  path '/bot/user/conversations/{conversation_id}/artifacts' do
    parameter 'conversation_id', in: :path, type: :string

    get(summary: 'list artifacts') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:conversation_id) { @conversation.id }
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @bot_artifact_count
        }
      end
    end
  end

  path '/bot/user/conversations/{conversation_id}/artifacts/{id}' do
    parameter 'conversation_id', in: :path, type: :string
    parameter 'id', in: :path, type: :string

    get(summary: 'show artifact') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:conversation_id) { @conversation.id }
        let(:id) { @bot_artifacts.first.id }
        it {
          body = JSON.parse(response.body)
          artifact = @bot_artifacts.first
          expect(body['conversation_id']).to eq artifact.conversation_id
          expect(body['source_type']).to eq artifact.source_type
          expect(body['source_id']).to eq artifact.source_id
          expect(body['tool_function']).to eq artifact.tool_function
          expect(body['function_params']).to eq artifact.function_params
          expect(body['meta']).to eq artifact.meta
          expect(body['info']).to eq artifact.info
          expect(body['tool_cname']).to eq artifact.tool_cname
          expect(body['tool_conf']).to eq artifact.tool_conf
          expect(body['intent_name']).to eq artifact.intent_name
        }
      end
    end
  end
end
