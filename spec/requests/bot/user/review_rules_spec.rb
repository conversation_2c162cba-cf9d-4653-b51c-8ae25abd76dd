require 'swagger_helper'

RSpec.describe 'bot/user/review_rules', type: :request, capture_examples: true, tags: ['bot user'] do
  before :each do
    @review_document = @bot_review_document
  end

  path '/bot/user/review_documents/{review_document_id}/review_rules' do
    parameter 'review_document_id', in: :path, type: :string

    get(summary: 'list review_rules') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:review_document_id) { @review_document.id }
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @bot_review_rule_count
        }
      end
    end
  end

  path '/bot/user/review_documents/{review_document_id}/review_rules/{id}' do
    parameter 'review_document_id', in: :path, type: :string
    parameter 'id', in: :path, type: :string

    get(summary: 'show review_rule') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:review_document_id) { @review_document.id }
        let(:id) { @bot_review_rules.first.id }
        it {
          body = JSON.parse(response.body)
          review_rule = @bot_review_rules.first
          expect(body['reviewer_id']).to eq review_rule.reviewer_id
          expect(body['name']).to eq review_rule.name
          expect(body['content']).to eq review_rule.content
          expect(body['scoring_criteria']).to eq review_rule.scoring_criteria
          expect(body['active']).to eq review_rule.active
        }
      end
    end
  end
end
