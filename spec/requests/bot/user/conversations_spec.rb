require 'swagger_helper'

RSpec.describe 'bot/user/conversations', type: :request, capture_examples: true, tags: ["bot user"] do
  conversation_ref = {
    type: :object, properties: {
      conversation: {
        type: :object, properties: {
          app_id: { type: :integer, description: '' },
          user_id: { type: :integer, description: '' },
          name: { type: :string, description: '名称' },
          agent_id: { type: :integer, description: '' },
        }
      }
    }
  }
  conversation_value = FactoryBot.attributes_for(:bot_conversation)

  before :each do
  end

  path '/bot/user/conversations/{id}/chat' do
    parameter 'id', in: :path, type: :string

    post(summary: 'chat conversation') do
      produces 'application/json'
      consumes 'application/json'
      parameter :conversation, in: :body, schema: conversation_ref
      response(201, description: 'successful') do
        let(:id) { @bot_conversations.first.id }
        let(:conversation) do
          { conversation: {
            meta: {
              messages: [
                {content_type: "text", content: "hello"},
              ]
            }
          } }
        end
      end
    end
  end

  path '/bot/user/conversations' do

    get(summary: 'list conversations') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @bot_conversation_count
        }
      end
    end

    post(summary: 'create conversation') do
      produces 'application/json'
      consumes 'application/json'
      parameter :conversation, in: :body, schema: conversation_ref
      response(201, description: 'successful') do
        let(:conversation) do
          { conversation: conversation_value.merge(
            agent_id: @bot_agent.id
          ) }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['app_id']).to eq @app.id
          expect(body['user_id']).to eq @user.id
          expect(body['name']).to eq conversation_value[:name]
          expect(body['agent_id']).to eq @bot_agent.id
        }
      end
    end
  end

  path '/bot/user/conversations/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show conversation') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @bot_conversations.first.id }
        it {
          body = JSON.parse(response.body)
          conversation = @bot_conversations.first
          expect(body['app_id']).to eq conversation.app_id
          expect(body['user_id']).to eq conversation.user_id
          expect(body['name']).to eq conversation.name
          expect(body['agent_id']).to eq conversation.agent_id
        }
      end
    end

    patch(summary: 'update conversation') do
      produces 'application/json'
      consumes 'application/json'
      parameter :conversation, in: :body, schema: conversation_ref
      response(201, description: 'successful') do
        let(:id) { @bot_conversations.first.id }
        let(:conversation) do
          { conversation: conversation_value.merge(
            name: 'conversation name'
          ) }
        end
      end
    end

    delete(summary: 'delete conversation') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @bot_conversations.first.id }
        it {
          expect(Bot::Conversation.count).to eq(@bot_conversation_count-1)
        }
      end
    end
  end
end
