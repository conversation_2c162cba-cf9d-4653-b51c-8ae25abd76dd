require 'swagger_helper'

RSpec.describe 'bot/user/agents', type: :request, capture_examples: true, tags: ["bot user"] do
  agent_ref = {
    type: :object, properties: {
      agent: {
        type: :object, properties: {
          app_id: { type: :integer, description: '' },
          type: { type: :string, description: 'STI属性' },
          name: { type: :string, description: '工具名称' },
          description: { type: :text, description: '工具描述' },
          instructions: { type: :text, description: '工具介绍' },
        }
      }
    }
  }
  agent_value = FactoryBot.attributes_for(:bot_agent)

  before :each do
  end

  path '/bot/user/agents' do

    get(summary: 'list agents') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @bot_agent_count
        }
      end
    end
  end
end
