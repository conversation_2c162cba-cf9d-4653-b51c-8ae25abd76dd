require 'swagger_helper'

RSpec.describe 'bot/manage/review_documents', type: :request, capture_examples: true, tags: ['bot manage'] do
  review_document_ref = {
    type: :object, properties: {
      review_document: {
        type: :object, properties: {
          user_id: { type: :integer, description: '' },
          reviewer_id: { type: :integer, description: '' },
          name: { type: :string, description: '名称' },
          file: { type: :jsonb, description: '文件' },
          state: { type: :string, description: '' },
        },
      },
    },
  }
  review_document_value = FactoryBot.attributes_for(:bot_review_document)

  before :each do
    @user.add_role :bot_manage
    @reviewer = @bot_reviewer
  end

  path '/bot/manage/reviewers/{reviewer_id}/review_documents' do
    parameter 'reviewer_id', in: :path, type: :string

    get(summary: 'list review_documents') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:reviewer_id) { @reviewer.id }
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @bot_review_document_count
        }
      end
    end

    post(summary: 'create review_document') do
      produces 'application/json'
      consumes 'application/json'
      parameter :review_document, in: :body, schema: review_document_ref
      response(201, description: 'successful') do
        let(:reviewer_id) { @reviewer.id }
        let(:review_document) do
          { review_document: review_document_value.merge(
            user_id: @user.id,
          ) }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['app_id']).to eq @app.id
          expect(body['user_id']).to eq @user.id
          expect(body['reviewer_id']).to eq @reviewer.id
          expect(body['name']).to eq review_document_value[:name]
          expect(body['file']).to eq review_document_value[:file]
          expect(body['state']).to eq review_document_value[:state]
        }
      end
    end
  end

  path '/bot/manage/reviewers/{reviewer_id}/review_documents/{id}' do
    parameter 'reviewer_id', in: :path, type: :string
    parameter 'id', in: :path, type: :string

    get(summary: 'show review_document') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:reviewer_id) { @reviewer.id }
        let(:id) { @bot_review_documents.first.id }
        it {
          body = JSON.parse(response.body)
          review_document = @bot_review_documents.first
          expect(body['app_id']).to eq review_document.app_id
          expect(body['user_id']).to eq review_document.user_id
          expect(body['reviewer_id']).to eq review_document.reviewer_id
          expect(body['name']).to eq review_document.name
          expect(body['file']).to eq review_document.file
          expect(body['state']).to eq review_document.state
        }
      end
    end

    patch(summary: 'update review_document') do
      produces 'application/json'
      consumes 'application/json'
      parameter :review_document, in: :body, schema: review_document_ref
      response(201, description: 'successful') do
        let(:reviewer_id) { @reviewer.id }
        let(:id) { @bot_review_documents.first.id }
        let(:review_document) do
          { review_document: review_document_value }
        end
      end
    end

    delete(summary: 'delete review_document') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:reviewer_id) { @reviewer.id }
        let(:id) { @bot_review_documents.first.id }
        it {
          expect(@app.bot_review_documents.count).to eq(@bot_review_document_count - 1)
        }
      end
    end
  end
end
