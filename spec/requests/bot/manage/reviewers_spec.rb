require 'swagger_helper'

RSpec.describe 'bot/manage/reviewers', type: :request, capture_examples: true, tags: ['bot manage'] do
  reviewer_ref = {
    type: :object, properties: {
      reviewer: {
        type: :object, properties: {
          name: { type: :string, description: '名称' },
          description: { type: :text, description: '描述' },
          review_instructions: { type: :text, description: '介绍' },
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          model_payload_summary: { type: :jsonb, description: 'model summary存储的字段' },
          model_detail: { type: :jsonb, description: 'model 存储的详情字段' },
          icon: { type: :jsonb, description: '图标' },
        },
      },
    },
  }
  reviewer_value = FactoryBot.attributes_for(:bot_reviewer)

  before :each do
    @user.add_role :bot_manage
  end

  path '/bot/manage/reviewers' do
    get(summary: 'list reviewers') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @bot_reviewer_count
        }
      end
    end

    post(summary: 'create reviewer') do
      produces 'application/json'
      consumes 'application/json'
      parameter :reviewer, in: :body, schema: reviewer_ref
      response(201, description: 'successful') do
        let(:reviewer) do
          { reviewer: reviewer_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['app_id']).to eq @app.id
          expect(body['name']).to eq reviewer_value[:name]
          expect(body['description']).to eq reviewer_value[:description]
          expect(body['review_instructions']).to eq reviewer_value[:review_instructions]
          expect(body['model_flag']).to eq reviewer_value[:model_flag]
          expect(body['model_payload']).to eq reviewer_value[:model_payload]
          expect(body['model_payload_summary']).to eq reviewer_value[:model_payload_summary]
          expect(body['model_detail']).to eq reviewer_value[:model_detail]
          expect(body['icon']).to eq reviewer_value[:icon]
        }
      end
    end
  end

  path '/bot/manage/reviewers/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show reviewer') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @bot_reviewers.first.id }
        it {
          body = JSON.parse(response.body)
          reviewer = @bot_reviewers.first
          expect(body['app_id']).to eq reviewer.app_id
          expect(body['name']).to eq reviewer.name
          expect(body['description']).to eq reviewer.description
          expect(body['review_instructions']).to eq reviewer.review_instructions
          expect(body['model_flag']).to eq reviewer.model_flag
          expect(body['model_payload']).to eq reviewer.model_payload
          expect(body['model_payload_summary']).to eq reviewer.model_payload_summary
          expect(body['model_detail']).to eq reviewer.model_detail
          expect(body['icon']).to eq reviewer.icon
        }
      end
    end

    patch(summary: 'update reviewer') do
      produces 'application/json'
      consumes 'application/json'
      parameter :reviewer, in: :body, schema: reviewer_ref
      response(201, description: 'successful') do
        let(:id) { @bot_reviewers.first.id }
        let(:reviewer) do
          { reviewer: reviewer_value }
        end
      end
    end

    delete(summary: 'delete reviewer') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @bot_reviewers.first.id }
        it {
          expect(@app.bot_reviewers.count).to eq(@bot_reviewer_count - 1)
        }
      end
    end
  end
end
