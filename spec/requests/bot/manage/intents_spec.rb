require 'swagger_helper'

RSpec.describe 'bot/manage/intents', type: :request, capture_examples: true, tags: ["bot manage"] do
  intent_ref = {
    type: :object, properties: {
      intent: {
        type: :object, properties: {
          app_id: { type: :integer, description: '' },
          type: { type: :string, description: 'STI属性' },
          name: { type: :string, description: '工具名称' },
          description: { type: :text, description: '工具描述' },
          tool_cname: { type: :string, description: '工具类的名称' },
          tool_conf: { type: :jsonb, description: '工具配置' },
          llm_model_key: { type: :string, description: 'LlmFactory默认的大模型' },
        }
      }
    }
  }
  intent_value = FactoryBot.attributes_for(:bot_intent)

  before :each do
    @user.add_role :bot_manage
  end

  path '/bot/manage/intents' do
    get(summary: 'list intents') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @bot_intent_count
        }
      end
    end

    post(summary: 'create intent') do
      produces 'application/json'
      consumes 'application/json'
      parameter :intent, in: :body, schema: intent_ref
      response(201, description: 'successful') do
        let(:intent) do
          { intent: intent_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['app_id']).to eq @app.id
          expect(body['type']).to eq intent_value[:type]
          expect(body['name']).to eq intent_value[:name]
          expect(body['description']).to eq intent_value[:description]
          expect(body['tool_cname']).to eq intent_value[:tool_cname]
          expect(body['tool_conf']).to eq intent_value[:tool_conf]
          expect(body['llm_model_key']).to eq intent_value[:llm_model_key]
        }
      end
    end
  end

  path '/bot/manage/intents/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show intent') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @bot_intents.first.id }
        it {
          body = JSON.parse(response.body)
          intent = @bot_intents.first
          expect(body['app_id']).to eq intent.app_id
          expect(body['type']).to eq intent.type
          expect(body['name']).to eq intent.name
          expect(body['description']).to eq intent.description
          expect(body['tool_cname']).to eq intent.tool_cname
          expect(body['tool_conf']).to eq intent.tool_conf
          expect(body['llm_model_key']).to eq intent.llm_model_key
        }
      end
    end

    patch(summary: 'update intent') do
      produces 'application/json'
      consumes 'application/json'
      parameter :intent, in: :body, schema: intent_ref
      response(201, description: 'successful') do
        let(:id) { @bot_intents.first.id }
        let(:intent) do
          { intent: intent_value }
        end
      end
    end

    delete(summary: 'delete intent') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @bot_intents.first.id }
        it {
          expect(@app.bot_intents.count).to eq(@bot_intent_count - 1)
        }
      end
    end
  end
end
