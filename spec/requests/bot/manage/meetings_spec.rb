require 'swagger_helper'

RSpec.describe 'bot/manage/meetings', type: :request, capture_examples: true, tags: ['bot manage'] do
  meeting_ref = {
    type: :object, properties: {
      meeting: {
        type: :object, properties: {
          user_id: { type: :integer, description: '' },
          file: { type: :jsonb, description: '文件' },
          name: { type: :string, description: '会议名称' },
          meeting_time: { type: :string, description: '会议时间' },
          background: { type: :text, description: '会议背景' },
          topic: { type: :string, description: '会议主题' },
          summary: { type: :text, description: '会议纪要' },
          participants: { type: :string, description: '与会人员' },
          audio: { type: :jsonb, description: '会议录音' },
        },
      },
    },
  }
  meeting_value = FactoryBot.attributes_for(:bot_meeting)

  before :each do
    @user.add_role :bot_manage
  end

  # path '/bot/manage/meetings/{id}/generate_topic_and_summary' do
  #   parameter 'id', in: :path, type: :string
  #
  #   post(summary: 'generate_topic_and_summary meeting') do
  #     produces 'application/json'
  #     consumes 'application/json'
  #     parameter :meeting, in: :body, schema: meeting_ref
  #     response(201, description: 'successful') do
  #       let(:id) { @bot_meetings.first.id }
  #       let(:meeting) do
  #         { meeting: meeting_value }
  #       end
  #     end
  #   end
  # end
  #
  # path '/bot/manage/meetings/{id}/ask' do
  #   parameter 'id', in: :path, type: :string
  #
  #   post(summary: 'ask meeting') do
  #     produces 'application/json'
  #     consumes 'application/json'
  #     parameter :meeting, in: :body, schema: meeting_ref
  #     response(201, description: 'successful') do
  #       let(:id) { @bot_meetings.first.id }
  #       let(:meeting) do
  #         { meeting: meeting_value }
  #       end
  #     end
  #   end
  # end

  path '/bot/manage/meetings' do
    get(summary: 'list meetings') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @bot_meeting_count
        }
      end
    end

    post(summary: 'create meeting') do
      produces 'application/json'
      consumes 'application/json'
      parameter :meeting, in: :body, schema: meeting_ref
      response(201, description: 'successful') do
        let(:meeting) do
          { meeting: meeting_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['app_id']).to eq @app.id
          expect(body['user_id']).to eq @user.id
          expect(body['file']).to eq meeting_value[:file]
          expect(body['name']).to eq meeting_value[:name]
          expect(body['meeting_time']).to eq meeting_value[:meeting_time]
          expect(body['background']).to eq meeting_value[:background]
          expect(body['topic']).to eq meeting_value[:topic]
          expect(body['summary']).to eq meeting_value[:summary]
          expect(body['participants']).to eq meeting_value[:participants]
          expect(body['audio']).to eq meeting_value[:audio]
        }
      end
    end
  end

  path '/bot/manage/meetings/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show meeting') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @bot_meetings.first.id }
        it {
          body = JSON.parse(response.body)
          meeting = @bot_meetings.first
          expect(body['app_id']).to eq meeting.app_id
          expect(body['user_id']).to eq meeting.user_id
          expect(body['file']).to eq meeting.file
          expect(body['name']).to eq meeting.name
          expect(body['meeting_time']).to eq meeting.meeting_time
          expect(body['background']).to eq meeting.background
          expect(body['topic']).to eq meeting.topic
          expect(body['summary']).to eq meeting.summary
          expect(body['participants']).to eq meeting.participants
          expect(body['audio']).to eq meeting.audio
        }
      end
    end

    patch(summary: 'update meeting') do
      produces 'application/json'
      consumes 'application/json'
      parameter :meeting, in: :body, schema: meeting_ref
      response(201, description: 'successful') do
        let(:id) { @bot_meetings.first.id }
        let(:meeting) do
          { meeting: meeting_value }
        end
      end
    end

    delete(summary: 'delete meeting') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @bot_meetings.first.id }
        it {
          expect(Bot::Meeting.count).to eq(@bot_meeting_count - 1)
        }
      end
    end
  end
end
