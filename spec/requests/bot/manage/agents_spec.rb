require 'swagger_helper'

RSpec.describe 'bot/manage/agents', type: :request, capture_examples: true, tags: ["bot manage"] do
  agent_ref = {
    type: :object, properties: {
      agent: {
        type: :object, properties: {
          type: { type: :string, description: 'STI属性' },
          name: { type: :string, description: '工具名称' },
          description: { type: :text, description: '工具描述' },
          instructions: { type: :text, description: '工具介绍' },
          llm_model_key: { type: :string, description: 'LlmFactory默认的大模型' },
        }
      }
    }
  }
  agent_value = FactoryBot.attributes_for(:bot_agent)

  before :each do
    @user.add_role :bot_manage
  end

  path '/bot/manage/agents' do

    get(summary: 'list agents') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @bot_agent_count
        }
      end
    end

    post(summary: 'create agent') do
      produces 'application/json'
      consumes 'application/json'
      parameter :agent, in: :body, schema: agent_ref
      response(201, description: 'successful') do
        let(:agent) do
          { agent: agent_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['app_id']).to eq @app.id
          expect(body['type']).to eq agent_value[:type]
          expect(body['name']).to eq agent_value[:name]
          expect(body['description']).to eq agent_value[:description]
          expect(body['instructions']).to eq agent_value[:instructions]
          expect(body['llm_model_key']).to eq agent_value[:llm_model_key]
        }
      end
    end
  end

  path '/bot/manage/agents/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show agent') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @bot_agents.last.id }
        it {
          body = JSON.parse(response.body)
          agent = @bot_agents.last
          expect(body['app_id']).to eq agent.app_id
          expect(body['type']).to eq agent.type
          expect(body['name']).to eq agent.name
          expect(body['description']).to eq agent.description
          expect(body['instructions']).to eq agent.instructions
          expect(body['llm_model_key']).to eq agent.llm_model_key
        }
      end
    end

    patch(summary: 'update agent') do
      produces 'application/json'
      consumes 'application/json'
      parameter :agent, in: :body, schema: agent_ref
      response(201, description: 'successful') do
        let(:id) { @bot_agents.last.id }
        let(:agent) do
          { agent: agent_value }
        end
      end
    end

    delete(summary: 'delete agent') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @bot_agents.last.id }
        it {
          expect(Bot::Agent.count).to eq(@bot_agent_count-1)
        }
      end
    end
  end
end
