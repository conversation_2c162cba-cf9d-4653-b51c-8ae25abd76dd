require 'swagger_helper'

RSpec.describe 'bot/manage/review_rules', type: :request, capture_examples: true, tags: ['bot manage'] do
  review_rule_ref = {
    type: :object, properties: {
      review_rule: {
        type: :object, properties: {
          name: { type: :string, description: '名称' },
          content: { type: :text, description: '内容' },
          scoring_criteria: { type: :text, description: '评分标准' },
          active: { type: :boolean, description: '是否启用' },
        },
      },
    },
  }
  review_rule_value = FactoryBot.attributes_for(:bot_review_rule)

  before :each do
    @user.add_role :bot_manage
    @reviewer = @bot_reviewer
  end

  path '/bot/manage/reviewers/{reviewer_id}/review_rules' do
    parameter 'reviewer_id', in: :path, type: :string

    get(summary: 'list review_rules') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:reviewer_id) { @reviewer.id }
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @bot_review_rule_count
        }
      end
    end

    post(summary: 'create review_rule') do
      produces 'application/json'
      consumes 'application/json'
      parameter :review_rule, in: :body, schema: review_rule_ref
      response(201, description: 'successful') do
        let(:reviewer_id) { @reviewer.id }
        let(:review_rule) do
          { review_rule: review_rule_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['review_source_id']).to eq @reviewer.id
          expect(body['name']).to eq review_rule_value[:name]
          expect(body['content']).to eq review_rule_value[:content]
          expect(body['scoring_criteria']).to eq review_rule_value[:scoring_criteria]
          expect(body['active']).to eq review_rule_value[:active]
        }
      end
    end
  end

  path '/bot/manage/reviewers/{reviewer_id}/review_rules/{id}' do
    parameter 'reviewer_id', in: :path, type: :string
    parameter 'id', in: :path, type: :string

    get(summary: 'show review_rule') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:reviewer_id) { @reviewer.id }
        let(:id) { @bot_review_rules.first.id }
        it {
          body = JSON.parse(response.body)
          review_rule = @bot_review_rules.first
          expect(body['review_source_id']).to eq review_rule.review_source.id
          expect(body['name']).to eq review_rule.name
          expect(body['content']).to eq review_rule.content
          expect(body['scoring_criteria']).to eq review_rule.scoring_criteria
          expect(body['active']).to eq review_rule.active
        }
      end
    end

    patch(summary: 'update review_rule') do
      produces 'application/json'
      consumes 'application/json'
      parameter :review_rule, in: :body, schema: review_rule_ref
      response(201, description: 'successful') do
        let(:reviewer_id) { @reviewer.id }
        let(:id) { @bot_review_rules.first.id }
        let(:review_rule) do
          { review_rule: review_rule_value }
        end
      end
    end

    delete(summary: 'delete review_rule') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:reviewer_id) { @reviewer.id }
        let(:id) { @bot_review_rules.first.id }
        it {
          expect(Bot::ReviewRule.count).to eq(@bot_review_rule_count - 1)
        }
      end
    end
  end
end
