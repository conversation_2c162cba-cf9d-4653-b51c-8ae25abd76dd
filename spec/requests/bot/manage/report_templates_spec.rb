require 'swagger_helper'

RSpec.describe 'bot/manage/report_templates', type: :request, capture_examples: true, tags: ['bot manage'] do
  report_template_ref = {
    type: :object, properties: {
      report_template: {
        type: :object, properties: {
          name: { type: :string, description: '名称' },
          title: { type: :string, description: '标题' },
          content: { type: :text, description: '内容' },
          prompt: { type: :text, description: '提示词' },
          conf: { type: :jsonb, description: '变量配置' },
        },
      },
    },
  }
  report_template_value = FactoryBot.attributes_for(:bot_report_template)

  before :each do
    @user.add_role :bot_manage
  end

  path '/bot/manage/report_templates' do
    get(summary: 'list report_templates') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @bot_report_template_count
        }
      end
    end

    post(summary: 'create report_template') do
      produces 'application/json'
      consumes 'application/json'
      parameter :report_template, in: :body, schema: report_template_ref
      response(201, description: 'successful') do
        let(:report_template) do
          { report_template: report_template_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['app_id']).to eq @app.id
          expect(body['name']).to eq report_template_value[:name]
          expect(body['title']).to eq report_template_value[:title]
          expect(body['content']).to eq report_template_value[:content]
          expect(body['prompt']).to eq report_template_value[:prompt]
          expect(body['conf']).to eq report_template_value[:conf]
        }
      end
    end
  end

  path '/bot/manage/report_templates/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show report_template') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @bot_report_templates.first.id }
        it {
          body = JSON.parse(response.body)
          report_template = @bot_report_templates.first
          expect(body['app_id']).to eq report_template.app_id
          expect(body['name']).to eq report_template.name
          expect(body['title']).to eq report_template.title
          expect(body['content']).to eq report_template.content
          expect(body['prompt']).to eq report_template.prompt
          expect(body['conf']).to eq report_template.conf
        }
      end
    end

    patch(summary: 'update report_template') do
      produces 'application/json'
      consumes 'application/json'
      parameter :report_template, in: :body, schema: report_template_ref
      response(201, description: 'successful') do
        let(:id) { @bot_report_templates.first.id }
        let(:report_template) do
          { report_template: report_template_value }
        end
      end
    end

    delete(summary: 'delete report_template') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @bot_report_templates.first.id }
        it {
          expect(@app.bot_report_templates.count).to eq(@bot_report_template_count - 1)
        }
      end
    end
  end
end
