require 'swagger_helper'

RSpec.describe 'serve/admin/submodules', type: :request, capture_examples: true, tags: ["serve admin"] do
  submodule_ref = {
    type: :object, properties: {
      submodule: {
        type: :object, properties: {
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          model_payload_summary: { type: :jsonb, description: 'model summary存储的字段' },
          model_detail: { type: :jsonb, description: 'model 存储的详情字段' },
          ancestry: { type: :string, description: '树形结构' },
          depth: { type: :integer, description: '树结构深度' },
          children_count: { type: :integer, description: '子对象的数据' },
          manages: { type: :jsonb, description: '' },
          manage_enable: { type: :boolean, description: '是否开启permit的按钮' },
          name: { type: :string, description: '名称' },
          position: { type: :integer, description: '排序' },
          cover_image: { type: :jsonb, description: '封面图' },
          key: { type: :string, description: '程序内使用的标识' },
          layout: { type: :jsonb, description: '布局方式配置' },
        }
      }
    }
  }
  submodule_value = FactoryBot.attributes_for(:serve_submodule)

  before :each do
    @user.add_role :serve_admin
  end

  path '/serve/admin/submodules' do

    get(summary: 'list submodules') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @serve_submodule_count
        }
      end
    end

    post(summary: 'create submodule') do
      produces 'application/json'
      consumes 'application/json'
      parameter :submodule, in: :body, schema: submodule_ref
      response(201, description: 'successful') do
        let(:submodule) do
          { submodule: submodule_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['app_id']).to eq @app.id
          expect(body['model_flag']).to eq submodule_value[:model_flag]
          expect(body['model_payload']).to eq submodule_value[:model_payload]
          expect(body['model_payload_summary']).to eq submodule_value[:model_payload_summary]
          expect(body['model_detail']).to eq submodule_value[:model_detail]
          expect(body['ancestry']).to eq submodule_value[:ancestry]
          expect(body['depth']).not_to be_nil
          expect(body['children_count']).not_to be_nil
          expect(body['manages']).not_to be_nil
          expect(body['manage_enable']).not_to be_nil
          expect(body['name']).to eq submodule_value[:name]
          expect(body['position']).not_to be_nil
          expect(body['cover_image']).to eq submodule_value[:cover_image]
          expect(body['key']).to eq submodule_value[:key]
          expect(body['layout']).to eq submodule_value[:layout]
        }
      end
    end
  end

  path '/serve/admin/submodules/list_index' do

    post(summary: 'list_index submodule') do
      produces 'application/json'
      consumes 'application/json'
      parameter :submodule, in: :body, schema: submodule_ref
      response(200, description: 'successful') do
        let(:submodule) do
          { submodule: submodule_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @serve_submodule_count
        }
      end
    end
  end

  path '/serve/admin/submodules/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show submodule') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @serve_submodules.first.id }
        it {
          body = JSON.parse(response.body)
          submodule = @serve_submodules.first
          expect(body['app_id']).to eq submodule.app_id
          expect(body['model_flag']).to eq submodule.model_flag
          expect(body['model_payload']).to eq submodule.model_payload
          expect(body['model_payload_summary']).to eq submodule.model_payload_summary
          expect(body['model_detail']).to eq submodule.model_detail
          expect(body['ancestry']).to eq submodule.ancestry
          expect(body['depth']).to eq submodule.depth
          expect(body['children_count']).to eq submodule.children_count
          expect(body['manages']).not_to be_nil
          expect(body['manage_enable']).to eq submodule.manage_enable
          expect(body['name']).to eq submodule.name
          expect(body['position']).to eq submodule.position
          expect(body['cover_image']).to eq submodule.cover_image
          expect(body['key']).to eq submodule.key
          expect(body['layout']).to eq submodule.layout
        }
      end
    end

    patch(summary: 'update submodule') do
      produces 'application/json'
      consumes 'application/json'
      parameter :submodule, in: :body, schema: submodule_ref
      response(201, description: 'successful') do
        let(:id) { @serve_submodules.first.id }
        let(:submodule) do
          { submodule: submodule_value }
        end
      end
    end

    delete(summary: 'delete submodule') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @serve_submodules.first.id }
        it {
          expect(Serve::Submodule.count).to eq(@serve_submodule_count-1)
        }
      end
    end
  end
end
