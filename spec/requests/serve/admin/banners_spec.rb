require 'swagger_helper'

RSpec.describe 'serve/admin/banners', type: :request, capture_examples: true, tags: ["serve admin"] do
  banner_ref = {
    type: :object, properties: {
      banner: {
        type: :object, properties: {
          app_id: { type: :integer, description: '' },
          source_type: { type: :string, description: '' },
          source_id: { type: :integer, description: '' },
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          model_payload_summary: { type: :jsonb, description: 'model summary存储的字段' },
          model_detail: { type: :jsonb, description: 'model 存储的详情字段' },
          effective_at: { type: :datetime, description: '生效时间' },
          invalid_at: { type: :datetime, description: '失效时间' },
          published_at: { type: :datetime, description: '' },
          is_published: { type: :boolean, description: '' },
          name: { type: :string, description: '轮播图名称' },
          position: { type: :integer, description: '位置' },
          cover_image: { type: :jsonb, description: '封面图' },
          conf: { type: :string, description: '呈现方式，根据前端需要设置，例如可以是PC或者Mobile的展现等' },
        }
      }
    }
  }
  banner_value = FactoryBot.attributes_for(:serve_banner)

  before :each do
    @user.add_role :serve_admin
  end

  path '/serve/admin/banners' do

    get(summary: 'list banners') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @serve_banner_count
        }
      end
    end

    post(summary: 'create banner') do
      produces 'application/json'
      consumes 'application/json'
      parameter :banner, in: :body, schema: banner_ref
      response(201, description: 'successful') do
        let(:banner) do
          { banner: banner_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['app_id']).to eq @app.id
          expect(body['source_type']).to eq banner_value[:source_type]
          expect(body['source_id']).to eq banner_value[:source_id]
          expect(body['model_flag']).to eq banner_value[:model_flag]
          expect(body['model_payload']).to eq banner_value[:model_payload]
          expect(body['model_payload_summary']).to eq banner_value[:model_payload_summary]
          expect(body['model_detail']).to eq banner_value[:model_detail]
          expect(body['effective_at']).to eq banner_value[:effective_at]
          expect(body['invalid_at']).to eq banner_value[:invalid_at]
          expect(body['published_at']).to eq banner_value[:published_at]
          expect(body['is_published']).to eq false
          expect(body['name']).to eq banner_value[:name]
          expect(body['position']).not_to be_nil
          expect(body['cover_image']).to eq banner_value[:cover_image]
          expect(body['conf']).to eq banner_value[:conf]
        }
      end
    end
  end

  path '/serve/admin/banners/list_index' do

    post(summary: 'list_index banner') do
      produces 'application/json'
      consumes 'application/json'
      parameter :banner, in: :body, schema: banner_ref
      response(200, description: 'successful') do
        let(:banner) do
          { banner: banner_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @serve_banner_count
        }
      end
    end
  end

  path '/serve/admin/banners/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show banner') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @serve_banners.first.id }
        it {
          body = JSON.parse(response.body)
          banner = @serve_banners.first
          expect(body['app_id']).to eq banner.app_id
          expect(body['source_type']).to eq banner.source_type
          expect(body['source_id']).to eq banner.source_id
          expect(body['model_flag']).to eq banner.model_flag
          expect(body['model_payload']).to eq banner.model_payload
          expect(body['model_payload_summary']).to eq banner.model_payload_summary
          expect(body['model_detail']).to eq banner.model_detail
          expect(body['effective_at']).to eq banner.effective_at
          expect(body['invalid_at']).to eq banner.invalid_at
          expect(body['published_at']).to eq banner.published_at
          expect(body['is_published']).to eq banner.is_published
          expect(body['name']).to eq banner.name
          expect(body['position']).to eq banner.position
          expect(body['cover_image']).to eq banner.cover_image
          expect(body['conf']).to eq banner.conf
        }
      end
    end

    patch(summary: 'update banner') do
      produces 'application/json'
      consumes 'application/json'
      parameter :banner, in: :body, schema: banner_ref
      response(201, description: 'successful') do
        let(:id) { @serve_banners.first.id }
        let(:banner) do
          { banner: banner_value }
        end
      end
    end

    delete(summary: 'delete banner') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:id) { @serve_banners.first.id }
        it {
          expect(Serve::Banner.count).to eq(@serve_banner_count-1)
        }
      end
    end
  end
end
