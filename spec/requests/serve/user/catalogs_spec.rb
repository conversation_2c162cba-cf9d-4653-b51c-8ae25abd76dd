require 'swagger_helper'

RSpec.describe 'serve/user/catalogs', type: :request, capture_examples: true, tags: ["serve user"] do
  catalog_ref = {
    type: :object, properties: {
      catalog: {
        type: :object, properties: {
          app_id: { type: :integer, description: '' },
          submodule_id: { type: :integer, description: '' },
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          model_payload_summary: { type: :jsonb, description: 'model summary存储的字段' },
          model_detail: { type: :jsonb, description: 'model 存储的详情字段' },
          views: { type: :jsonb, description: '' },
          view_enable: { type: :boolean, description: '是否开启permit的按钮' },
          uses: { type: :jsonb, description: '' },
          use_enable: { type: :boolean, description: '是否开启permit的按钮' },
          name: { type: :string, description: '名称' },
          position: { type: :integer, description: '排序' },
          cover_image: { type: :jsonb, description: '封面图' },
          layout: { type: :jsonb, description: '布局方式配置' },
        }
      }
    }
  }
  catalog_value = FactoryBot.attributes_for(:serve_catalog)

  before :each do
  end

  path '/serve/user/submodules/{submodule_id}/catalogs' do
    parameter 'submodule_id', in: :path, type: :string

    get(summary: 'list catalogs') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:submodule_id) { @submodule.id }
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @serve_catalog_count
        }
      end
    end
  end

  path '/serve/user/submodules/{submodule_id}/catalogs/{id}' do
    parameter 'submodule_id', in: :path, type: :string
    parameter 'id', in: :path, type: :string

    get(summary: 'show catalog') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:submodule_id) { @submodule.id }
        let(:id) { @serve_catalogs.first.id }
        it {
          body = JSON.parse(response.body)
          catalog = @serve_catalogs.first
          expect(body['app_id']).to eq catalog.app_id
          expect(body['submodule_id']).to eq catalog.submodule_id
          expect(body['model_flag']).to eq catalog.model_flag
          expect(body['model_payload']).to eq catalog.model_payload
          expect(body['model_payload_summary']).to eq catalog.model_payload_summary
          expect(body['model_detail']).to eq catalog.model_detail
          expect(body['views']).not_to be_nil
          expect(body['view_enable']).to eq catalog.view_enable
          expect(body['uses']).not_to be_nil
          expect(body['use_enable']).to eq catalog.use_enable
          expect(body['name']).to eq catalog.name
          expect(body['position']).to eq catalog.position
          expect(body['cover_image']).to eq catalog.cover_image
          expect(body['layout']).to eq catalog.layout
        }
      end
    end
  end
end
