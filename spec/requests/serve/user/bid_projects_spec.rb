require "swagger_helper"

RSpec.describe "serve/user/bid_projects", type: :request, capture_examples: true, tags: ["serve user"] do
  FactoryBot.attributes_for(:serve_bid_project)

  before :each do
  end

  path "/serve/user/bid_projects" do
    get(summary: "list bid_projects") do
      produces "application/json"
      consumes "application/json"
      response(200, description: "successful") do
        it {
          body = response.parsed_body
          expect(body["records"].count).to eq @serve_bid_project_count
        }
      end
    end
  end

  path "/serve/user/bid_projects/{id}" do
    parameter "id", in: :path, type: :string

    get(summary: "show bid_project") do
      produces "application/json"
      consumes "application/json"
      response(200, description: "successful") do
        let(:id) { @serve_bid_projects.first.id }
        it {
          body = response.parsed_body
          bid_project = @serve_bid_projects.first
          expect(body["region_area_id"]).to eq bid_project.region_area_id
          expect(body["app_id"]).to eq bid_project.app_id
          expect(body["org_id"]).to eq bid_project.org_id
          expect(body["contactor_id"]).to eq bid_project.contactor_id
          expect(body["manager_id"]).to eq bid_project.manager_id
          expect(body["province"]).to eq bid_project.province
          expect(body["city"]).to eq bid_project.city
          expect(body["district"]).to eq bid_project.district
          expect(body["start_at"]).to eq bid_project.start_at
          expect(body["end_at"]).to eq bid_project.end_at
          expect(body["code"]).to eq bid_project.code
          expect(body["name"]).to eq bid_project.name
          expect(body["state"]).to eq bid_project.state
          expect(body["content"]).to eq bid_project.content
          expect(body["region_code"]).to eq bid_project.region_code
          expect(body["setup_at"]).to eq bid_project.setup_at
          expect(body["open_at"]).to eq bid_project.open_at
          expect(body["manager_name"]).to eq bid_project.manager_name
          expect(body["contactor_name"]).to eq bid_project.contactor_name
          expect(body["phone"]).to eq bid_project.phone
          expect(body["unit"]).to eq bid_project.unit
          expect(body["unit_code"]).to eq bid_project.unit_code
          expect(body["send_state"]).to eq bid_project.send_state
          expect(body["amount"]).to eq bid_project.amount
          expect(body["meta"]).to eq bid_project.meta
          expect(body["payload"]).to eq bid_project.payload
          expect(body["attachments"]).to eq bid_project.attachments
        }
      end
    end
  end
end
