require 'swagger_helper'

RSpec.describe 'serve/user/activities', type: :request, capture_examples: true, tags: ["serve user"] do
  activity_ref = {
    type: :object, properties: {
      activity: {
        type: :object, properties: {
          source_type: { type: :string, description: '' },
          source_id: { type: :integer, description: '' },
          app_id: { type: :integer, description: '' },
          submodule_id: { type: :integer, description: '' },
          creator_id: { type: :integer, description: '' },
          target_type: { type: :string, description: '' },
          target_id: { type: :integer, description: '' },
          name: { type: :string, description: '名称' },
          create_instance_state: { type: :string, description: '创建工作流的状态' },
          create_instance_timestamp: { type: :datetime, description: '创建工作流的操作时间' },
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          model_payload_summary: { type: :jsonb, description: 'model summary存储的字段' },
          model_detail: { type: :jsonb, description: 'model 存储的详情字段' },
          type: { type: :string, description: 'STI属性' },
          effective_at: { type: :datetime, description: '生效时间' },
          invalid_at: { type: :datetime, description: '失效时间' },
          hotted_at: { type: :datetime, description: '' },
          is_hotted: { type: :boolean, description: '' },
          views: { type: :jsonb, description: '权限设置' },
          view_enable: { type: :boolean, description: '是否开启permit的按钮' },
          uses: { type: :jsonb, description: '权限设置' },
          use_enable: { type: :boolean, description: '是否开启permit的按钮' },
          manages: { type: :jsonb, description: '权限设置' },
          manage_enable: { type: :boolean, description: '是否开启permit的按钮' },
          state: { type: :string, description: '' },
          cover_image: { type: :jsonb, description: '封面图' },
          icon: { type: :jsonb, description: 'icon' },
          attachments: { type: :jsonb, description: '附件' },
          position: { type: :integer, description: '排序' },
          content: { type: :jsonb, description: '详情，body / images / video' },
          address: { type: :text, description: '地址' },
          layout: { type: :jsonb, description: '卡片样式' },
          views_count: { type: :integer, description: '浏览数量' },
          ai_summary: { type: :text, description: 'AI 生成的摘要' },
          ai_summary_embedding: { type: :, description: '' },
        }
      }
    }
  }
  activity_value = FactoryBot.attributes_for(:serve_activity)

  before :each do
    @serve_activity_count = 5
    @serve_activities = FactoryBot.create_list(:serve_activity, @serve_activity_count)
  end

  path '/serve/user/submodules/{submodule_id}/activities' do
    parameter 'submodule_id', in: :path, type: :string

    get(summary: 'list activities') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:submodule_id) { @submodule.id }
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @serve_activity_count
        }
      end
    end
  end

  path '/serve/user/submodules/{submodule_id}/activities/list_index' do
    parameter 'submodule_id', in: :path, type: :string

    post(summary: 'list_index activity') do
      produces 'application/json'
      consumes 'application/json'
      parameter :activity, in: :body, schema: activity_ref
      response(201, description: 'successful') do
        let(:submodule_id) { @submodule.id }
        let(:activity) do
          { activity: activity_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @serve_activity_count
        }
      end
    end
  end

  path '/serve/user/submodules/{submodule_id}/activities/group_index' do
    parameter 'submodule_id', in: :path, type: :string

    post(summary: 'group_index activity') do
      produces 'application/json'
      consumes 'application/json'
      parameter :activity, in: :body, schema: activity_ref
      response(201, description: 'successful') do
        let(:submodule_id) { @submodule.id }
        let(:activity) do
          { activity: activity_value }
        end
      end
    end
  end

  path '/serve/user/submodules/{submodule_id}/activities/{id}' do
    parameter 'submodule_id', in: :path, type: :string
    parameter 'id', in: :path, type: :string

    get(summary: 'show activity') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:submodule_id) { @submodule.id }
        let(:id) { @serve_activities.first.id }
        it {
          body = JSON.parse(response.body)
          activity = @serve_activities.first
          expect(body['source_type']).to eq activity.source_type
          expect(body['source_id']).to eq activity.source_id
          expect(body['app_id']).to eq activity.app_id
          expect(body['submodule_id']).to eq activity.submodule_id
          expect(body['creator_id']).to eq activity.creator_id
          expect(body['target_type']).to eq activity.target_type
          expect(body['target_id']).to eq activity.target_id
          expect(body['name']).to eq activity.name
          expect(body['create_instance_state']).to eq activity.create_instance_state
          expect(body['create_instance_timestamp']).to eq activity.create_instance_timestamp
          expect(body['model_flag']).to eq activity.model_flag
          expect(body['model_payload']).to eq activity.model_payload
          expect(body['model_payload_summary']).to eq activity.model_payload_summary
          expect(body['model_detail']).to eq activity.model_detail
          expect(body['type']).to eq activity.type
          expect(body['effective_at']).to eq activity.effective_at
          expect(body['invalid_at']).to eq activity.invalid_at
          expect(body['hotted_at']).to eq activity.hotted_at
          expect(body['is_hotted']).to eq activity.is_hotted
          expect(body['views']).to eq activity.views
          expect(body['view_enable']).to eq activity.view_enable
          expect(body['uses']).to eq activity.uses
          expect(body['use_enable']).to eq activity.use_enable
          expect(body['manages']).to eq activity.manages
          expect(body['manage_enable']).to eq activity.manage_enable
          expect(body['state']).to eq activity.state
          expect(body['cover_image']).to eq activity.cover_image
          expect(body['icon']).to eq activity.icon
          expect(body['attachments']).to eq activity.attachments
          expect(body['position']).to eq activity.position
          expect(body['content']).to eq activity.content
          expect(body['address']).to eq activity.address
          expect(body['layout']).to eq activity.layout
          expect(body['views_count']).to eq activity.views_count
          expect(body['ai_summary']).to eq activity.ai_summary
          expect(body['ai_summary_embedding']).to eq activity.ai_summary_embedding
        }
      end
    end
  end
end
