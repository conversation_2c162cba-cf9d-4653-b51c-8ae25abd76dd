require 'swagger_helper'

RSpec.describe 'serve/user/permit_actions', type: :request, capture_examples: true, tags: ["serve user"] do
  permit_action_ref = {
    type: :object, properties: {
      permit_action: {
        type: :object, properties: {
          app_id: { type: :integer, description: '' },
          real_user_id: { type: :integer, description: '' },
          target_type: { type: :string, description: '' },
          target_id: { type: :integer, description: '' },
          user_type: { type: :string, description: '' },
          user_id: { type: :integer, description: '' },
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          model_payload_summary: { type: :jsonb, description: 'model summary存储的字段' },
          model_detail: { type: :jsonb, description: 'model 存储的详情字段' },
          action_type: { type: :string, description: '具体action的业务flag' },
          action_option: { type: :string, description: '前端可以设置的信息' },
          action_flag: { type: :string, description: '后端使用的逻辑字段，前端不能设置' },
        }
      }
    }
  }
  permit_action_value = FactoryBot.attributes_for(:serve_permit_action)

  before :each do
  end

  path '/serve/user/permit_actions' do
    get(summary: 'list permit_actions') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @user.serve_permit_actions.count
        }
      end
    end
  end

  # path '/serve/user/permit_actions/{id}' do
  #   parameter 'id', in: :path, type: :string

  #   get(summary: 'show permit_action') do
  #     produces 'application/json'
  #     consumes 'application/json'
  #     response(200, description: 'successful') do
  #       let(:id) { @user.serve_permit_actions.first.id }
  #       it {
  #         body = JSON.parse(response.body)
  #         permit_action = @user.serve_permit_actions.first
  #         expect(body['app_id']).to eq permit_action.app_id
  #         expect(body['real_user_id']).to eq permit_action.real_user_id
  #         expect(body['target_type']).to eq permit_action.target_type
  #         expect(body['target_id']).to eq permit_action.target_id
  #         expect(body['user_type']).to eq permit_action.user_type
  #         expect(body['user_id']).to eq permit_action.user_id
  #         expect(body['model_flag']).to eq permit_action.model_flag
  #         expect(body['model_payload']).to eq permit_action.model_payload
  #         expect(body['model_payload_summary']).to eq permit_action.model_payload_summary
  #         expect(body['model_detail']).to eq permit_action.model_detail
  #         expect(body['action_type']).to eq permit_action.action_type
  #         expect(body['action_option']).to eq permit_action.action_option
  #         expect(body['action_flag']).to eq permit_action.action_flag
  #       }
  #     end
  #   end
  # end
end
