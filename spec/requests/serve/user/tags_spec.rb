require 'swagger_helper'

RSpec.describe 'serve/user/tags', type: :request, capture_examples: true, tags: ["serve user"] do
  tag_ref = {
    type: :object, properties: {
      tag: {
        type: :object, properties: {
          app_id: { type: :integer, description: '' },
          submodule_id: { type: :integer, description: '' },
          name: { type: :string, description: '名称' },
          color: { type: :string, description: '颜色' },
          position: { type: :integer, description: '排序' },
        }
      }
    }
  }
  tag_value = FactoryBot.attributes_for(:serve_tag)

  before :each do
  end

  path '/serve/user/submodules/{submodule_id}/tags' do
    parameter 'submodule_id', in: :path, type: :string

    get(summary: 'list tags') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:submodule_id) { @submodule.id }
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @serve_tag_count
        }
      end
    end
  end

  path '/serve/user/submodules/{submodule_id}/tags/{id}' do
    parameter 'submodule_id', in: :path, type: :string
    parameter 'id', in: :path, type: :string

    get(summary: 'show tag') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:submodule_id) { @submodule.id }
        let(:id) { @serve_tags.first.id }
        it {
          body = JSON.parse(response.body)
          tag = @serve_tags.first
          expect(body['app_id']).to eq tag.app_id
          expect(body['submodule_id']).to eq tag.submodule_id
          expect(body['name']).to eq tag.name
          expect(body['color']).to eq tag.color
          expect(body['position']).to eq tag.position
        }
      end
    end
  end
end
