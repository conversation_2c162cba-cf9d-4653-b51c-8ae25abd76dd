require 'swagger_helper'

RSpec.describe 'serve/user/entry/entries', type: :request, capture_examples: true, tags: ["serve user entry"] do
  entry_ref = {
    type: :object, properties: {
      entry: {
        type: :object, properties: {
          app_id: { type: :integer, description: '' },
          submodule_id: { type: :integer, description: '' },
          activity_id: { type: :integer, description: '' },
          source_type: { type: :string, description: '' },
          source_id: { type: :integer, description: '' },
          user_id: { type: :integer, description: '' },
          name: { type: :string, description: '名称' },
          type: { type: :string, description: 'STI属性' },
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          model_payload_summary: { type: :jsonb, description: 'model summary存储的字段' },
          model_detail: { type: :jsonb, description: 'model 存储的详情字段' },
          state: { type: :string, description: '状态' },
          order_at: { type: :datetime, description: '报名时间' },
        }
      }
    }
  }
  entry_value = FactoryBot.attributes_for(:serve_entry)

  before :each do
  end

  path '/serve/user/entry/entries' do

    get(summary: 'list entries') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @serve_entry_count
        }
      end
    end
  end

  path '/serve/user/entry/entries/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show entry') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @serve_entries.first.id }
        it {
          body = JSON.parse(response.body)
          entry = @serve_entries.first
          expect(body['app_id']).to eq entry.app_id
          expect(body['submodule_id']).to eq entry.submodule_id
          expect(body['activity_id']).to eq entry.activity_id
          expect(body['source_type']).to eq entry.source_type
          expect(body['source_id']).to eq entry.source_id
          expect(body['user_id']).to eq entry.user_id
          expect(body['name']).to eq entry.name
          expect(body['type']).to eq entry.type
          expect(body['model_flag']).to eq entry.model_flag
          expect(body['model_payload']).to eq entry.model_payload
          expect(body['model_payload_summary']).to eq entry.model_payload_summary
          expect(body['model_detail']).to eq entry.model_detail
          expect(body['state']).to eq entry.state
          expect(body['order_at']).to eq entry.order_at
        }
      end
    end
  end
end
