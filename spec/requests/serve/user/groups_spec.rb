require 'swagger_helper'

RSpec.describe 'serve/user/groups', type: :request, capture_examples: true, tags: ["serve user"] do
  group_ref = {
    type: :object, properties: {
      group: {
        type: :object, properties: {
          app_id: { type: :integer, description: '' },
          submodule_id: { type: :integer, description: '' },
          type: { type: :string, description: 'STI属性' },
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          model_payload_summary: { type: :jsonb, description: 'model summary存储的字段' },
          model_detail: { type: :jsonb, description: 'model 存储的详情字段' },
          name: { type: :string, description: '名称' },
          state: { type: :string, description: '状态' },
          position: { type: :integer, description: '排序' },
        }
      }
    }
  }
  group_value = FactoryBot.attributes_for(:serve_group)

  before :each do
  end

  path '/serve/user/submodules/{submodule_id}/groups' do
    parameter 'submodule_id', in: :path, type: :string

    get(summary: 'list groups') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:submodule_id) { @submodule.id }
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @serve_group_count
        }
      end
    end
  end

  path '/serve/user/submodules/{submodule_id}/groups/{id}' do
    parameter 'submodule_id', in: :path, type: :string
    parameter 'id', in: :path, type: :string

    get(summary: 'show group') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:submodule_id) { @submodule.id }
        let(:id) { @serve_groups.first.id }
        it {
          body = JSON.parse(response.body)
          group = @serve_groups.first
          expect(body['app_id']).to eq group.app_id
          expect(body['submodule_id']).to eq group.submodule_id
          expect(body['type']).to eq group.type
          expect(body['model_flag']).to eq group.model_flag
          expect(body['model_payload']).to eq group.model_payload
          expect(body['model_payload_summary']).to eq group.model_payload_summary
          expect(body['model_detail']).to eq group.model_detail
          expect(body['name']).to eq group.name
          expect(body['state']).to eq group.state
          expect(body['position']).to eq group.position
        }
      end
    end
  end
end
