require "swagger_helper"

RSpec.describe "serve/manage/bid_projects", type: :request, capture_examples: true, tags: ["serve manage"] do
  bid_project_ref = {
    type: :object, properties: {
      bid_project: {
        type: :object, properties: {
          region_area_id: { type: :integer, description: "" },
          app_id: { type: :integer, description: "" },
          org_id: { type: :integer, description: "" },
          contactor_id: { type: :integer, description: "" },
          manager_id: { type: :integer, description: "" },
          province: { type: :string, description: "省" },
          city: { type: :string, description: "市" },
          district: { type: :string, description: "区" },
          start_at: { type: :datetime, description: "生效时间" },
          end_at: { type: :datetime, description: "失效时间" },
          code: { type: :string, description: "项目编号" },
          name: { type: :string, description: "标题" },
          state: { type: :string, description: "" },
          content: { type: :jsonb, description: "内容" },
          region_code: { type: :string, description: "地区编码" },
          setup_at: { type: :datetime, description: "创建时间" },
          open_at: { type: :datetime, description: "开标时间" },
          manager_name: { type: :string, description: "负责人" },
          contactor_name: { type: :string, description: "联系人" },
          phone: { type: :string, description: "联系电话" },
          unit: { type: :string, description: "招标单位" },
          unit_code: { type: :string, description: "招标单位编号" },
          send_state: { type: :string, description: "发送消息状态" },
          amount: { type: :decimal, description: "金额" },
          meta: { type: :jsonb, description: "元数据" },
          payload: { type: :jsonb, description: "扩展字段" },
          attachments: { type: :jsonb, description: "附件" }
        }
      }
    }
  }
  bid_project_value = FactoryBot.attributes_for(:serve_bid_project)

  before :each do
    @user.add_role :serve_manage
  end

  path "/serve/manage/bid_projects" do
    get(summary: "list bid_projects") do
      produces "application/json"
      consumes "application/json"
      response(200, description: "successful") do
        it {
          body = response.parsed_body
          expect(body["records"].count).to eq @serve_bid_project_count
        }
      end
    end

    post(summary: "create bid_project") do
      produces "application/json"
      consumes "application/json"
      parameter :bid_project, in: :body, schema: bid_project_ref
      response(201, description: "successful") do
        let(:bid_project) do
          { bid_project: bid_project_value }
        end
        it {
          body = response.parsed_body
          expect(body["region_area_id"]).to eq bid_project_value[:region_area_id]
          expect(body["app_id"]).to eq @app.id
          expect(body["org_id"]).to eq bid_project_value[:org_id]
          expect(body["contactor_id"]).to eq bid_project_value[:contactor_id]
          expect(body["manager_id"]).to eq bid_project_value[:manager_id]
          expect(body["province"]).to eq bid_project_value[:province]
          expect(body["city"]).to eq bid_project_value[:city]
          expect(body["district"]).to eq bid_project_value[:district]
          expect(body["start_at"]).to eq bid_project_value[:start_at]
          expect(body["end_at"]).to eq bid_project_value[:end_at]
          expect(body["code"]).to eq bid_project_value[:code]
          expect(body["name"]).to eq bid_project_value[:name]
          expect(body["state"]).to eq bid_project_value[:state]
          expect(body["content"]).to eq bid_project_value[:content]
          expect(body["region_code"]).to eq bid_project_value[:region_code]
          expect(body["setup_at"]).to eq bid_project_value[:setup_at]
          expect(body["open_at"]).to eq bid_project_value[:open_at]
          expect(body["manager_name"]).to eq bid_project_value[:manager_name]
          expect(body["contactor_name"]).to eq bid_project_value[:contactor_name]
          expect(body["phone"]).to eq bid_project_value[:phone]
          expect(body["unit"]).to eq bid_project_value[:unit]
          expect(body["unit_code"]).to eq bid_project_value[:unit_code]
          expect(body["send_state"]).to eq bid_project_value[:send_state]
          expect(body["amount"]).to eq bid_project_value[:amount]
          expect(body["meta"]).to eq bid_project_value[:meta]
          expect(body["payload"]).to eq bid_project_value[:payload]
          expect(body["attachments"]).to eq bid_project_value[:attachments]
        }
      end
    end
  end

  path "/serve/manage/bid_projects/{id}" do
    parameter "id", in: :path, type: :string

    get(summary: "show bid_project") do
      produces "application/json"
      consumes "application/json"
      response(200, description: "successful") do
        let(:id) { @serve_bid_projects.first.id }
        it {
          body = response.parsed_body
          bid_project = @serve_bid_projects.first
          expect(body["region_area_id"]).to eq bid_project.region_area_id
          expect(body["app_id"]).to eq bid_project.app_id
          expect(body["org_id"]).to eq bid_project.org_id
          expect(body["contactor_id"]).to eq bid_project.contactor_id
          expect(body["manager_id"]).to eq bid_project.manager_id
          expect(body["province"]).to eq bid_project.province
          expect(body["city"]).to eq bid_project.city
          expect(body["district"]).to eq bid_project.district
          expect(body["start_at"]).to eq bid_project.start_at
          expect(body["end_at"]).to eq bid_project.end_at
          expect(body["code"]).to eq bid_project.code
          expect(body["name"]).to eq bid_project.name
          expect(body["state"]).to eq bid_project.state
          expect(body["content"]).to eq bid_project.content
          expect(body["region_code"]).to eq bid_project.region_code
          expect(body["setup_at"]).to eq bid_project.setup_at
          expect(body["open_at"]).to eq bid_project.open_at
          expect(body["manager_name"]).to eq bid_project.manager_name
          expect(body["contactor_name"]).to eq bid_project.contactor_name
          expect(body["phone"]).to eq bid_project.phone
          expect(body["unit"]).to eq bid_project.unit
          expect(body["unit_code"]).to eq bid_project.unit_code
          expect(body["send_state"]).to eq bid_project.send_state
          expect(body["amount"]).to eq bid_project.amount
          expect(body["meta"]).to eq bid_project.meta
          expect(body["payload"]).to eq bid_project.payload
          expect(body["attachments"]).to eq bid_project.attachments
        }
      end
    end

    patch(summary: "update bid_project") do
      produces "application/json"
      consumes "application/json"
      parameter :bid_project, in: :body, schema: bid_project_ref
      response(201, description: "successful") do
        let(:id) { @serve_bid_projects.first.id }
        let(:bid_project) do
          { bid_project: bid_project_value }
        end
      end
    end

    delete(summary: "delete bid_project") do
      produces "application/json"
      consumes "application/json"
      response(204, description: "successful") do
        let(:id) { @serve_bid_projects.first.id }
        it {
          expect(Serve::BidProject.count).to eq(@serve_bid_project_count - 1)
        }
      end
    end
  end
end
