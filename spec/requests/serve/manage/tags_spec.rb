require 'swagger_helper'

RSpec.describe 'serve/manage/tags', type: :request, capture_examples: true, tags: ["serve manage"] do
  tag_ref = {
    type: :object, properties: {
      tag: {
        type: :object, properties: {
          app_id: { type: :integer, description: '' },
          submodule_id: { type: :integer, description: '' },
          name: { type: :string, description: '名称' },
          color: { type: :string, description: '颜色' },
        }
      }
    }
  }
  tag_value = FactoryBot.attributes_for(:serve_tag)

  before :each do
    @user.add_role :serve_manage
  end

  path '/serve/manage/submodules/{submodule_id}/tags' do
    parameter 'submodule_id', in: :path, type: :string

    get(summary: 'list tags') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:submodule_id) { @submodule.id }
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @serve_tag_count
        }
      end
    end

    post(summary: 'create tag') do
      produces 'application/json'
      consumes 'application/json'
      parameter :tag, in: :body, schema: tag_ref
      response(201, description: 'successful') do
        let(:submodule_id) { @submodule.id }
        let(:tag) do
          { tag: tag_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['app_id']).to eq @app.id
          expect(body['submodule_id']).to eq @submodule.id
          expect(body['name']).to eq tag_value[:name]
          expect(body['color']).to eq tag_value[:color]
        }
      end
    end
  end

  path '/serve/manage/submodules/{submodule_id}/tags/list_index' do
    parameter 'submodule_id', in: :path, type: :string

    post(summary: 'list_index tag') do
      produces 'application/json'
      consumes 'application/json'
      parameter :tag, in: :body, schema: tag_ref
      response(200, description: 'successful') do
        let(:submodule_id) { @submodule.id }
        let(:tag) do
          { tag: tag_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @serve_tag_count
        }
      end
    end
  end

  path '/serve/manage/submodules/{submodule_id}/tags/{id}' do
    parameter 'submodule_id', in: :path, type: :string
    parameter 'id', in: :path, type: :string

    get(summary: 'show tag') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:submodule_id) { @submodule.id }
        let(:id) { @serve_tags.first.id }
        it {
          body = JSON.parse(response.body)
          tag = @serve_tags.first
          expect(body['app_id']).to eq tag.app_id
          expect(body['submodule_id']).to eq tag.submodule_id
          expect(body['name']).to eq tag.name
          expect(body['color']).to eq tag.color
        }
      end
    end

    patch(summary: 'update tag') do
      produces 'application/json'
      consumes 'application/json'
      parameter :tag, in: :body, schema: tag_ref
      response(201, description: 'successful') do
        let(:submodule_id) { @submodule.id }
        let(:id) { @serve_tags.first.id }
        let(:tag) do
          { tag: tag_value }
        end
      end
    end

    delete(summary: 'delete tag') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:submodule_id) { @submodule.id }
        let(:id) { @serve_tags.first.id }
        it {
          expect(Serve::Tag.count).to eq(@serve_tag_count-1)
        }
      end
    end
  end
end
