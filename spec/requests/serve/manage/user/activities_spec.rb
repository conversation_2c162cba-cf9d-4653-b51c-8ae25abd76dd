require 'swagger_helper'

RSpec.describe 'serve/manage/user/activities', type: :request, capture_examples: true, tags: ["serve manage user"] do
  activity_ref = {
    type: :object, properties: {
      activity: {
        type: :object, properties: {
          app_id: { type: :integer, description: '' },
          submodule_id: { type: :integer, description: '' },
          source_type: { type: :string, description: '' },
          source_id: { type: :integer, description: '' },
          name: { type: :string, description: '名称' },
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          model_payload_summary: { type: :jsonb, description: 'model summary存储的字段' },
          model_detail: { type: :jsonb, description: 'model 存储的详情字段' },
          type: { type: :string, description: 'STI属性' },
          effective_at: { type: :datetime, description: '生效时间' },
          invalid_at: { type: :datetime, description: '失效时间' },
          views: { type: :jsonb, description: '' },
          view_enable: { type: :boolean, description: '是否开启permit的按钮' },
          uses: { type: :jsonb, description: '' },
          use_enable: { type: :boolean, description: '是否开启permit的按钮' },
          state: { type: :string, description: '' },
          cover_image: { type: :jsonb, description: '封面图' },
          position: { type: :integer, description: '排序' },
          content: { type: :jsonb, description: '详情，body / images / video' },
          address: { type: :text, description: '地址' },
          layout: { type: :jsonb, description: '卡片样式' },
          creator_id: { type: :integer, description: '' },
          views_count: { type: :integer, description: '浏览数量' },
          icon: { type: :string, description: 'icon' },
          create_instance_state: { type: :string, description: '创建工作流的状态' },
          manages: { type: :jsonb, description: '' },
          manage_enable: { type: :boolean, description: '是否开启permit的按钮' },
        }
      }
    }
  }
  activity_value = FactoryBot.attributes_for(:serve_activity)

  before :each do
    @user.add_role :serve_manage
  end

  path '/serve/manage/user/activities' do

    get(summary: 'list activities') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @serve_activity_count
        }
      end
    end
  end

  path '/serve/manage/user/activities/{id}' do
    parameter 'id', in: :path, type: :string

    get(summary: 'show activity') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:id) { @serve_activities.first.id }
        it {
          body = JSON.parse(response.body)
          activity = @serve_activities.first
          expect(body['app_id']).to eq activity.app_id
          expect(body['submodule_id']).to eq activity.submodule_id
          expect(body['source_type']).to eq activity.source_type
          expect(body['source_id']).to eq activity.source_id
          expect(body['name']).to eq activity.name
          expect(body['model_flag']).to eq activity.model_flag
          expect(body['model_payload']).to eq activity.model_payload
          expect(body['model_payload_summary']).to eq activity.model_payload_summary
          expect(body['model_detail']).to eq activity.model_detail
          expect(body['type']).to eq activity.type
          expect(body['effective_at']).to eq activity.effective_at
          expect(body['invalid_at']).to eq activity.invalid_at
          expect(body['views']).not_to be_nil
          expect(body['view_enable']).to eq activity.view_enable
          expect(body['uses']).not_to be_nil
          expect(body['use_enable']).to eq activity.use_enable
          expect(body['state']).to eq activity.state
          expect(body['cover_image']).to eq activity.cover_image
          expect(body['position']).to eq activity.position
          expect(body['content']).to eq activity.content
          expect(body['address']).to eq activity.address
          expect(body['layout']).to eq activity.layout
          expect(body['creator_id']).to eq activity.creator_id
          expect(body['views_count']).to eq activity.views_count
          expect(body['icon']).to eq activity.icon
          expect(body['create_instance_state']).to eq activity.create_instance_state
          expect(body['manages']).not_to be_nil
          expect(body['manage_enable']).to eq activity.manage_enable
        }
      end
    end
  end
end
