require 'swagger_helper'

RSpec.describe 'serve/manage/entries', type: :request, capture_examples: true, tags: ["serve manage"] do
  entry_ref = {
    type: :object, properties: {
      entry: {
        type: :object, properties: {
          app_id: { type: :integer, description: '' },
          submodule_id: { type: :integer, description: '' },
          activity_id: { type: :integer, description: '' },
          source_type: { type: :string, description: '' },
          source_id: { type: :integer, description: '' },
          user_id: { type: :integer, description: '' },
          name: { type: :string, description: '名称' },
          type: { type: :string, description: 'STI属性' },
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          model_payload_summary: { type: :jsonb, description: 'model summary存储的字段' },
          model_detail: { type: :jsonb, description: 'model 存储的详情字段' },
        }
      }
    }
  }
  entry_value = FactoryBot.attributes_for(:serve_entry)

  before :each do
    @user.add_role :serve_manage
  end

  path '/serve/manage/submodules/{submodule_id}/entries' do
    parameter 'submodule_id', in: :path, type: :string

    get(summary: 'list entries') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:submodule_id) { @submodule.id }
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @serve_entry_count
        }
      end
    end

    post(summary: 'create entry') do
      produces 'application/json'
      consumes 'application/json'
      parameter :entry, in: :body, schema: entry_ref
      response(201, description: 'successful') do
        let(:submodule_id) { @submodule.id }
        let(:entry) do
          { entry: entry_value.merge(user_id: @user.id, activity_id: @serve_activities.last.id) }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['app_id']).to eq @app.id
          expect(body['submodule_id']).to eq @submodule.id
          expect(body['activity_id']).to eq @serve_activities.last.id
          expect(body['source_type']).to eq entry_value[:source_type]
          expect(body['source_id']).to eq entry_value[:source_id]
          expect(body['user_id']).to eq @user.id
          expect(body['name']).to eq entry_value[:name]
          expect(body['type']).to eq entry_value[:type]
          expect(body['model_flag']).to eq entry_value[:model_flag]
          expect(body['model_payload']).to eq entry_value[:model_payload]
          expect(body['model_payload_summary']).to eq entry_value[:model_payload_summary]
          expect(body['model_detail']).to eq entry_value[:model_detail]
        }
      end
    end
  end

  path '/serve/manage/submodules/{submodule_id}/entries/list_index' do
    parameter 'submodule_id', in: :path, type: :string

    post(summary: 'list_index entry') do
      produces 'application/json'
      consumes 'application/json'
      parameter :entry, in: :body, schema: entry_ref
      response(200, description: 'successful') do
        let(:submodule_id) { @submodule.id }
        let(:entry) do
          { entry: entry_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @serve_entry_count
        }
      end
    end
  end

  path '/serve/manage/submodules/{submodule_id}/entries/{id}' do
    parameter 'submodule_id', in: :path, type: :string
    parameter 'id', in: :path, type: :string

    get(summary: 'show entry') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:submodule_id) { @submodule.id }
        let(:id) { @serve_entries.first.id }
        it {
          body = JSON.parse(response.body)
          entry = @serve_entries.first
          expect(body['app_id']).to eq entry.app_id
          expect(body['submodule_id']).to eq entry.submodule_id
          expect(body['activity_id']).to eq entry.activity_id
          expect(body['source_type']).to eq entry.source_type
          expect(body['source_id']).to eq entry.source_id
          expect(body['user_id']).to eq entry.user_id
          expect(body['name']).to eq entry.name
          expect(body['type']).to eq entry.type
          expect(body['model_flag']).to eq entry.model_flag
          expect(body['model_payload']).to eq entry.model_payload
          expect(body['model_payload_summary']).to eq entry.model_payload_summary
          expect(body['model_detail']).to eq entry.model_detail
        }
      end
    end

    patch(summary: 'update entry') do
      produces 'application/json'
      consumes 'application/json'
      parameter :entry, in: :body, schema: entry_ref
      response(201, description: 'successful') do
        let(:submodule_id) { @submodule.id }
        let(:id) { @serve_entries.first.id }
        let(:entry) do
          { entry: entry_value }
        end
      end
    end

    delete(summary: 'delete entry') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:submodule_id) { @submodule.id }
        let(:id) { @serve_entries.first.id }
        it {
          expect(Serve::Entry.count).to eq(@serve_entry_count-1)
        }
      end
    end
  end
end
