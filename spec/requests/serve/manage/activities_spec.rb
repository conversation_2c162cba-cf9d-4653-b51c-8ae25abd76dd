require 'swagger_helper'

RSpec.describe 'serve/manage/activities', type: :request, capture_examples: true, tags: ["serve manage"] do
  activity_ref = {
    type: :object, properties: {
      activity: {
        type: :object, properties: {
          app_id: { type: :integer, description: '' },
          submodule_id: { type: :integer, description: '' },
          source_type: { type: :string, description: '' },
          source_id: { type: :integer, description: '' },
          name: { type: :string, description: '名称' },
          model_flag: { type: :string, description: 'model flag，对应model_setting的flag' },
          model_payload: { type: :jsonb, description: 'model payload存储的字段' },
          model_payload_summary: { type: :jsonb, description: 'model summary存储的字段' },
          model_detail: { type: :jsonb, description: 'model 存储的详情字段' },
          type: { type: :string, description: 'STI属性' },
          effective_at: { type: :datetime, description: '生效时间' },
          invalid_at: { type: :datetime, description: '失效时间' },
          views: { type: :jsonb, description: '' },
          view_enable: { type: :boolean, description: '是否开启permit的按钮' },
          uses: { type: :jsonb, description: '' },
          use_enable: { type: :boolean, description: '是否开启permit的按钮' },
          state: { type: :string, description: '' },
          cover_image: { type: :jsonb, description: '封面图' },
          position: { type: :integer, description: '排序' },
          content: { type: :jsonb, description: '详情，body / images / video' },
          address: { type: :text, description: '地址' },
          layout: { type: :jsonb, description: '卡片样式' },
        }
      }
    }
  }
  activity_value = FactoryBot.attributes_for(:serve_activity)

  before :each do
    @user.add_role :serve_manage
  end

  path '/serve/manage/submodules/{submodule_id}/activities' do
    parameter 'submodule_id', in: :path, type: :string

    get(summary: 'list activities') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:submodule_id) { @submodule.id }
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @serve_activity_count
        }
      end
    end

    post(summary: 'create activity') do
      produces 'application/json'
      consumes 'application/json'
      parameter :activity, in: :body, schema: activity_ref
      response(201, description: 'successful') do
        let(:submodule_id) { @submodule.id }
        let(:activity) do
          { activity: activity_value }
        end
        it {
          binding.pry
          body = JSON.parse(response.body)
          expect(body['app_id']).to eq @app.id
          expect(body['submodule_id']).to eq @submodule.id
          expect(body['source_type']).to eq activity_value[:source_type]
          expect(body['source_id']).to eq activity_value[:source_id]
          expect(body['name']).to eq activity_value[:name]
          expect(body['model_flag']).to eq activity_value[:model_flag]
          expect(body['model_payload']).to eq activity_value[:model_payload]
          expect(body['model_payload_summary']).to eq activity_value[:model_payload_summary]
          expect(body['model_detail']).to eq activity_value[:model_detail]
          expect(body['type']).to eq activity_value[:type]
          expect(body['effective_at']).to eq activity_value[:effective_at]
          expect(body['invalid_at']).to eq activity_value[:invalid_at]
          expect(body['views']).not_to be_nil
          expect(body['view_enable']).to eq false
          expect(body['uses']).not_to be_nil
          expect(body['use_enable']).to eq false
          expect(body['state']).to eq activity_value[:state]
          expect(body['cover_image']).to eq activity_value[:cover_image]
          expect(body['position']).not_to be_nil
          expect(body['content']).to eq activity_value[:content]
          expect(body['address']).to eq activity_value[:address]
          expect(body['layout']).to eq activity_value[:layout]
        }
      end
    end
  end

  path '/serve/manage/submodules/{submodule_id}/activities/list_index' do
    parameter 'submodule_id', in: :path, type: :string

    post(summary: 'list_index activity') do
      produces 'application/json'
      consumes 'application/json'
      parameter :activity, in: :body, schema: activity_ref
      response(200, description: 'successful') do
        let(:submodule_id) { @submodule.id }
        let(:activity) do
          { activity: activity_value }
        end
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @serve_activity_count
        }
      end
    end
  end

  path '/serve/manage/submodules/{submodule_id}/activities/{id}' do
    parameter 'submodule_id', in: :path, type: :string
    parameter 'id', in: :path, type: :string

    get(summary: 'show activity') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:submodule_id) { @submodule.id }
        let(:id) { @serve_activities.first.id }
        it {
          body = JSON.parse(response.body)
          activity = @serve_activities.first
          expect(body['app_id']).to eq activity.app_id
          expect(body['submodule_id']).to eq activity.submodule_id
          expect(body['source_type']).to eq activity.source_type
          expect(body['source_id']).to eq activity.source_id
          expect(body['name']).to eq activity.name
          expect(body['model_flag']).to eq activity.model_flag
          expect(body['model_payload']).to eq activity.model_payload
          expect(body['model_payload_summary']).to eq activity.model_payload_summary
          expect(body['model_detail']).to eq activity.model_detail
          expect(body['type']).to eq activity.type
          expect(body['effective_at']).to eq activity.effective_at
          expect(body['invalid_at']).to eq activity.invalid_at
          expect(body['views']).not_to be_nil
          expect(body['view_enable']).to eq activity.view_enable
          expect(body['uses']).not_to be_nil
          expect(body['use_enable']).to eq activity.use_enable
          expect(body['state']).to eq activity.state
          expect(body['cover_image']).to eq activity.cover_image
          expect(body['position']).to eq activity.position
          expect(body['content']).to eq activity.content
          expect(body['address']).to eq activity.address
          expect(body['layout']).to eq activity.layout
        }
      end
    end

    patch(summary: 'update activity') do
      produces 'application/json'
      consumes 'application/json'
      parameter :activity, in: :body, schema: activity_ref
      response(201, description: 'successful') do
        let(:submodule_id) { @submodule.id }
        let(:id) { @serve_activities.first.id }
        let(:activity) do
          { activity: activity_value }
        end
      end
    end

    delete(summary: 'delete activity') do
      produces 'application/json'
      consumes 'application/json'
      response(204, description: 'successful') do
        let(:submodule_id) { @submodule.id }
        let(:id) { @serve_activities.first.id }
        it {
          expect(Serve::Activity.count).to eq(@serve_activity_count-1)
        }
      end
    end
  end
end
