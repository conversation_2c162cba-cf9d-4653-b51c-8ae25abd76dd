require 'swagger_helper'

RSpec.describe 'com/user/redis_resources', type: :request, capture_examples: true, tags: ["com user"] do
  before :each do
    @user_count = User.count
    @redis_resources = RedisResources.new(resources: User.all)
  end

  path '/com/user/redis_resources' do
    parameter 'redis_key', in: :query, type: :string

    get(summary: 'list redis_resources') do
      produces 'application/json'
      consumes 'application/json'
      response(200, description: 'successful') do
        let(:redis_key) { @redis_resources.redis_key }
        it {
          body = JSON.parse(response.body)
          expect(body['records'].count).to eq @user_count
        }
      end
    end
  end

  path "/com/user/redis_resources/{id}" do
    parameter "id", in: :path, type: :string
    parameter 'redis_key', in: :query, type: :string

    get(summary: "show redis_resources") do
      produces "application/json"
      consumes "application/json"
      response(200, description: "successful") do
        let(:id) { User.first.id }
        let(:redis_key) { @redis_resources.redis_key }
        it {
          body = response.parsed_body
          expect(body['id']).to eq(User.first.id)
        }
      end
    end

    post(summary: "add redis_resources") do
      produces "application/json"
      consumes "application/json"
      response(200, description: "successful") do
        before do
          @new_user = create(:user, app: @app)
        end
        let(:id) { @new_user.id }
        let(:redis_key) { @redis_resources.redis_key }
        it {
          expect(@redis_resources.resources.count).to eq(@user_count + 1)
        }
      end
    end

    delete(summary: "remove id from redis_resources") do
      produces "application/json"
      consumes "application/json"
      response(200, description: "successful") do
        let(:id) { User.first.id }
        let(:redis_key) { @redis_resources.redis_key }
        it {
          body = response.parsed_body
          expect(@redis_resources.resources.count).to eq(@user_count - 1)
        }
      end
    end
  end
end
